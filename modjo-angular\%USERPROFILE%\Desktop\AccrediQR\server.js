const express = require('express');
const path = require('path');
const app = express();

// Serve static files from the dist directory
app.use(express.static(path.join(__dirname, 'dist/modjo-angular/browser')));

// Send all requests to index.html
app.get('/*', function(req, res) {
  res.sendFile(path.join(__dirname, 'dist/modjo-angular/browser/index.html'));
});

// Start the app by listening on the default port
const port = process.env.PORT || 8080;
app.listen(port, () => {
  console.log(`AccrédiQR app running on port ${port}`);
  console.log(`Open your browser and navigate to http://localhost:${port}`);
});
