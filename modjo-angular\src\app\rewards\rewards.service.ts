import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Reward, RewardRedemption } from '../models/reward.model';
import { UserService } from '../user/user.service';
import { PointType } from '../models/point.model';

// These imports would normally come from Firebase, but we're mocking them for now
// import { Firestore, collection, doc, getDoc, getDocs, query, where, orderBy, addDoc, updateDoc } from '@angular/fire/firestore';

@Injectable({
  providedIn: 'root'
})
export class RewardsService {
  // Mock data for development
  private mockRewards: Reward[] = [
    {
      id: '1',
      title: 'Café gratuit',
      description: 'Un café gratuit chez Café X à Monastir',
      pointsCost: 20,
      partnerId: 'partner1',
      partnerName: 'Café X',
      imageUrl: 'https://via.placeholder.com/150',
      available: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '2',
      title: 'Réduction coiffeur',
      description: '20% de réduction sur une coupe chez Coiffeur Y',
      pointsCost: 30,
      partnerId: 'partner2',
      partnerName: 'Coiffeur Y',
      imageUrl: 'https://via.placeholder.com/150',
      available: true,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      id: '3',
      title: 'Livre gratuit',
      description: 'Un livre gratuit à la librairie Z',
      pointsCost: 50,
      partnerId: 'partner3',
      partnerName: 'Librairie Z',
      imageUrl: 'https://via.placeholder.com/150',
      available: true,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  private mockRedemptions: RewardRedemption[] = [];

  constructor(
    // private firestore: Firestore,
    private userService: UserService
  ) {}

  // Get all available rewards
  getAvailableRewards(): Observable<Reward[]> {
    // Mock implementation
    return of(this.mockRewards.filter(r => r.available));

    // Firebase implementation
    // const rewardsRef = collection(this.firestore, 'rewards');
    // const q = query(
    //   rewardsRef,
    //   where('available', '==', true),
    //   orderBy('pointsCost', 'asc')
    // );
    // 
    // return from(getDocs(q)).pipe(
    //   map(querySnapshot => {
    //     return querySnapshot.docs.map(doc => ({
    //       id: doc.id,
    //       ...doc.data()
    //     } as Reward));
    //   })
    // );
  }

  // Get reward by ID
  getRewardById(rewardId: string): Observable<Reward | null> {
    // Mock implementation
    const reward = this.mockRewards.find(r => r.id === rewardId);
    return of(reward || null);

    // Firebase implementation
    // const rewardRef = doc(this.firestore, `rewards/${rewardId}`);
    // return from(getDoc(rewardRef)).pipe(
    //   map(docSnap => {
    //     if (docSnap.exists()) {
    //       return { id: docSnap.id, ...docSnap.data() } as Reward;
    //     } else {
    //       return null;
    //     }
    //   })
    // );
  }

  // Get user's reward redemptions
  getUserRedemptions(userId: string): Observable<RewardRedemption[]> {
    // Mock implementation
    return of(this.mockRedemptions.filter(r => r.userId === userId));

    // Firebase implementation
    // const redemptionsRef = collection(this.firestore, 'redemptions');
    // const q = query(
    //   redemptionsRef,
    //   where('userId', '==', userId),
    //   orderBy('redeemedAt', 'desc')
    // );
    // 
    // return from(getDocs(q)).pipe(
    //   map(querySnapshot => {
    //     return querySnapshot.docs.map(doc => ({
    //       id: doc.id,
    //       ...doc.data()
    //     } as RewardRedemption));
    //   })
    // );
  }

  // Redeem a reward
  async redeemReward(userId: string, rewardId: string): Promise<RewardRedemption> {
    // Mock implementation
    const reward = this.mockRewards.find(r => r.id === rewardId);
    if (!reward) {
      throw new Error('Reward not found');
    }

    // Check if user has enough points
    const userPointHistory = await this.userService.getUserPointHistory(userId).toPromise();
    if (!userPointHistory || userPointHistory.totalPoints < reward.pointsCost) {
      throw new Error('Not enough points');
    }

    // Deduct points
    await this.userService.addPoints(
      userId,
      undefined,
      -reward.pointsCost,
      `Récompense: ${reward.title}`,
      PointType.REWARD_REDEMPTION
    );

    // Create redemption record
    const redemption: RewardRedemption = {
      id: Math.random().toString(36).substring(2, 15),
      userId,
      rewardId,
      reward,
      redeemedAt: new Date(),
      status: 'pending'
    };

    this.mockRedemptions.push(redemption);
    return redemption;

    // Firebase implementation
    // const reward = await this.getRewardById(rewardId).toPromise();
    // if (!reward) {
    //   throw new Error('Reward not found');
    // }
    // 
    // // Check if user has enough points
    // const userPointHistory = await this.userService.getUserPointHistory(userId).toPromise();
    // if (!userPointHistory || userPointHistory.totalPoints < reward.pointsCost) {
    //   throw new Error('Not enough points');
    // }
    // 
    // // Deduct points
    // await this.userService.addPoints(
    //   userId,
    //   undefined,
    //   -reward.pointsCost,
    //   `Récompense: ${reward.title}`,
    //   PointType.REWARD_REDEMPTION
    // );
    // 
    // // Create redemption record
    // const redemptionData: Omit<RewardRedemption, 'id'> = {
    //   userId,
    //   rewardId,
    //   reward,
    //   redeemedAt: new Date(),
    //   status: 'pending'
    // };
    // 
    // const docRef = await addDoc(collection(this.firestore, 'redemptions'), redemptionData);
    // return { id: docRef.id, ...redemptionData };
  }

  // Update redemption status
  async updateRedemptionStatus(redemptionId: string, status: 'pending' | 'completed' | 'cancelled'): Promise<void> {
    // Mock implementation
    const redemptionIndex = this.mockRedemptions.findIndex(r => r.id === redemptionId);
    if (redemptionIndex !== -1) {
      this.mockRedemptions[redemptionIndex].status = status;
    }

    // Firebase implementation
    // const redemptionRef = doc(this.firestore, `redemptions/${redemptionId}`);
    // await updateDoc(redemptionRef, { status });
  }
}
