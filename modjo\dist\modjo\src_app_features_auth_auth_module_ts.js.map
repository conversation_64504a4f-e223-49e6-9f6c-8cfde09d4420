{"version": 3, "file": "src_app_features_auth_auth_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAC+C;AACM;AACN;AAE/C;AACuD;AACW;AACT;AACE;AACJ;AACI;AACI;AACe;AACjB;AAEO;AACS;AAClC;;;AAwBrC,MAAOe,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAhBnBf,yDAAY,EACZC,+DAAmB,EACnBC,yDAAY,CAACc,QAAQ,CAACF,oDAAU,CAAC;MAEjC;MACAX,iEAAa,EACbC,4EAAkB,EAClBC,mEAAc,EACdC,sEAAe,EACfC,kEAAa,EACbC,sEAAe,EACfC,0EAAiB,EACjBC,yFAAwB,EACxBC,wEAAgB;IAAA;EAAA;;;sHAGPI,UAAU;IAAAE,YAAA,GApBnBL,6EAAc,EACdC,sFAAiB;IAAAK,OAAA,GAGjBlB,yDAAY,EACZC,+DAAmB,EAAAkB,yDAAA;IAGnB;IACAhB,iEAAa,EACbC,4EAAkB,EAClBC,mEAAc,EACdC,sEAAe,EACfC,kEAAa,EACbC,sEAAe,EACfC,0EAAiB,EACjBC,yFAAwB,EACxBC,wEAAgB;EAAA;AAAA;;;;;;;;;;;;;;;;ACtCgD;AACS;AAEtE,MAAMG,UAAU,GAAW,CAChC;EACEM,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,OAAO;EACbG,SAAS,EAAEX,6EAAcA;CAC1B,EACD;EACEQ,IAAI,EAAE,UAAU;EAChBG,SAAS,EAAEV,sFAAiBA;CAC7B,CACF;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjBmE;;;;;;;;;;;;;;;;IC2D1DY,uDAAA,sBAA0E;;;;;IAExEA,4DADF,WAAyB,eACb;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAW;IAC1BA,oDAAA,qBACF;IAAAA,0DAAA,EAAO;;;ADrDX,MAAOb,cAAc;EAMzBkB,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,YAAY;IAStB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjB,sDAAU,CAACkB,QAAQ,EAAElB,sDAAU,CAACiB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnB,sDAAU,CAACkB,QAAQ,EAAElB,sDAAU,CAACoB,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,SAAS,GAAG,IAAI,CAACJ,KAAK,CAACY,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,YAAY;IAE7E;IACA,IAAI,IAAI,CAACf,WAAW,CAACgB,eAAe,EAAE,EAAE;MACtC,IAAI,CAACf,MAAM,CAACgB,QAAQ,CAAC,CAAC,IAAI,CAACX,SAAS,CAAC,CAAC;;EAE1C;EAEMY,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,wJAAA;MACZ,IAAID,KAAI,CAACZ,SAAS,CAACc,OAAO,EAAE;QAC1BF,KAAI,CAACG,oBAAoB,EAAE;QAC3B;;MAGFH,KAAI,CAACf,SAAS,GAAG,IAAI;MACrB,MAAM;QAAEK,KAAK;QAAEE;MAAQ,CAAE,GAAGQ,KAAI,CAACZ,SAAS,CAACgB,KAAK;MAEhD,IAAI;QACF,MAAMJ,KAAI,CAACnB,WAAW,CAACwB,KAAK,CAACf,KAAK,EAAEE,QAAQ,CAAC;QAC7CQ,KAAI,CAAChB,QAAQ,CAACsB,IAAI,CAAC,oBAAoB,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACtEP,KAAI,CAAClB,MAAM,CAACgB,QAAQ,CAAC,CAACE,KAAI,CAACb,SAAS,CAAC,CAAC;OACvC,CAAC,OAAOqB,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;QACpCR,KAAI,CAAChB,QAAQ,CAACsB,IAAI,CAChBN,KAAI,CAACU,eAAe,CAACF,KAAK,CAAC,EAC3B,QAAQ,EACR;UAAED,QAAQ,EAAE;QAAI,CAAE,CACnB;OACF,SAAS;QACRP,KAAI,CAACf,SAAS,GAAG,KAAK;;IACvB;EACH;EAEQyB,eAAeA,CAACF,KAAU;IAChC,IAAIA,KAAK,CAACG,IAAI,EAAE;MACd,QAAQH,KAAK,CAACG,IAAI;QAChB,KAAK,qBAAqB;UACxB,OAAO,oDAAoD;QAC7D,KAAK,qBAAqB;UACxB,OAAO,yBAAyB;QAClC,KAAK,oBAAoB;UACvB,OAAO,yBAAyB;QAClC,KAAK,oBAAoB;UACvB,OAAO,4BAA4B;QACrC,KAAK,wBAAwB;UAC3B,OAAO,mDAAmD;QAC5D;UACE,OAAO,0CAA0C;;;IAGvD,OAAO,0CAA0C;EACnD;EAEQR,oBAAoBA,CAAA;IAC1BS,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzB,SAAS,CAAC0B,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACjD,MAAMC,OAAO,GAAG,IAAI,CAAC7B,SAAS,CAAC8B,GAAG,CAACF,GAAG,CAAC;MACvCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAACC,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAClC,SAAS,CAAC8B,GAAG,CAACG,SAAS,CAAC;IAC3C,IAAIC,KAAK,EAAEC,MAAM,IAAID,KAAK,CAACE,OAAO,EAAE;MAClC,IAAIF,KAAK,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,GAAGF,SAAS,KAAK,OAAO,GAAG,OAAO,GAAG,cAAc,SAAS;;MAErE,IAAIC,KAAK,CAACC,MAAM,CAAC,OAAO,CAAC,EAAE;QACzB,OAAO,uBAAuB;;MAEhC,IAAID,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,EAAE;QAC7B,OAAO,6CAA6C;;;IAGxD,OAAO,EAAE;EACX;EAEA;EACME,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAzB,wJAAA;MACfyB,MAAI,CAACtC,SAAS,CAACuC,UAAU,CAAC;QACxBrC,KAAK,EAAE,eAAe;QACtBE,QAAQ,EAAE;OACX,CAAC;MACF,MAAMkC,MAAI,CAAC3B,QAAQ,EAAE;IAAC;EACxB;EAEM6B,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA5B,wJAAA;MACpB4B,MAAI,CAACzC,SAAS,CAACuC,UAAU,CAAC;QACxBrC,KAAK,EAAE,oBAAoB;QAC3BE,QAAQ,EAAE;OACX,CAAC;MACF,MAAMqC,MAAI,CAAC9B,QAAQ,EAAE;IAAC;EACxB;EAEM+B,YAAYA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA9B,wJAAA;MAChB8B,MAAI,CAAC3C,SAAS,CAACuC,UAAU,CAAC;QACxBrC,KAAK,EAAE,gBAAgB;QACvBE,QAAQ,EAAE;OACX,CAAC;MACF,MAAMuC,MAAI,CAAChC,QAAQ,EAAE;IAAC;EACxB;;;uBAxHWtC,cAAc,EAAAa,+DAAA,CAAAN,uDAAA,GAAAM,+DAAA,CAAA4D,oEAAA,GAAA5D,+DAAA,CAAA8D,mDAAA,GAAA9D,+DAAA,CAAA8D,2DAAA,GAAA9D,+DAAA,CAAAiE,oEAAA;IAAA;EAAA;;;YAAd9E,cAAc;MAAAgF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTzBzE,4DAFF,aAA6B,aAEK;UAS9BA,uDARA,aAA4B,aACA,aACA,aACA,aACA,aACA,aACA,aACA,cACA;UAC9BA,0DAAA,EAAM;UAKAA,4DAHN,mBAA6B,uBACV,cACW,cACN;UAAAA,oDAAA,oBAAE;UAAAA,0DAAA,EAAM;UAC1BA,4DAAA,UAAI;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAK;UACdA,4DAAA,SAAG;UAAAA,oDAAA,0CAA6B;UAEpCA,0DAFoC,EAAI,EAChC,EACU;UAGhBA,4DADF,wBAAkB,eACyD;UAA3CA,wDAAA,sBAAA4E,kDAAA;YAAA,OAAYF,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UAGjDzB,4DADF,yBAAwD,iBAC3C;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAY;UACpCA,uDAAA,gBAGqC;UACrCA,4DAAA,mBAAoB;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAW;UACpCA,4DAAA,iBAAW;UAAAA,oDAAA,IAA4B;UACzCA,0DADyC,EAAY,EACpC;UAIfA,4DADF,yBAAwD,iBAC3C;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAY;UACnCA,uDAAA,iBAGwC;UACxCA,4DAAA,kBAK2C;UAFnCA,wDAAA,mBAAA6E,iDAAA;YAAA,OAAAH,GAAA,CAAA9D,YAAA,IAAA8D,GAAA,CAAA9D,YAAA;UAAA,EAAsC;UAG5CZ,4DAAA,gBAAU;UAAAA,oDAAA,IAAoD;UAChEA,0DADgE,EAAW,EAClE;UACTA,4DAAA,iBAAW;UAAAA,oDAAA,IAA+B;UAC5CA,0DAD4C,EAAY,EACvC;UAGjBA,4DAAA,kBAI+B;UAE7BA,wDADA,KAAA+E,sCAAA,0BAA4D,KAAAC,+BAAA,mBACnC;UAI3BhF,0DAAA,EAAS;UAIPA,4DADF,eAA2B,SACtB;UAAAA,oDAAA,8BACD;UAAAA,4DAAA,aAA4C;UAAAA,oDAAA,4BAAe;UAGjEA,0DAHiE,EAAI,EAC7D,EACA,EACD;UAGPA,4DAAA,eAA0B;UACxBA,uDAAA,mBAA2B;UAC3BA,4DAAA,UAAI;UAAAA,oDAAA,kCAAgB;UAAAA,0DAAA,EAAK;UAEvBA,4DADF,eAA0B,kBAII;UADpBA,wDAAA,mBAAAiF,iDAAA;YAAA,OAASP,GAAA,CAAAvB,WAAA,EAAa;UAAA,EAAC;UAE7BnD,4DAAA,gBAAU;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAW;UAEzBA,4DADF,WAAK,cACK;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAS;UAC5BA,4DAAA,aAAO;UAAAA,oDAAA,qBAAiB;UAE5BA,0DAF4B,EAAQ,EAC5B,EACC;UAETA,4DAAA,kBAG4B;UADpBA,wDAAA,mBAAAkF,iDAAA;YAAA,OAASR,GAAA,CAAApB,gBAAA,EAAkB;UAAA,EAAC;UAElCtD,4DAAA,gBAAU;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAW;UAE3BA,4DADF,WAAK,cACK;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAS;UAC3BA,4DAAA,aAAO;UAAAA,oDAAA,0BAAsB;UAEjCA,0DAFiC,EAAQ,EACjC,EACC;UAETA,4DAAA,kBAG4B;UADpBA,wDAAA,mBAAAmF,iDAAA;YAAA,OAAST,GAAA,CAAAlB,YAAA,EAAc;UAAA,EAAC;UAE9BxD,4DAAA,gBAAU;UAAAA,oDAAA,4BAAoB;UAAAA,0DAAA,EAAW;UAEvCA,4DADF,WAAK,cACK;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAS;UACtBA,4DAAA,aAAO;UAAAA,oDAAA,sBAAkB;UAOvCA,0DAPuC,EAAQ,EAC7B,EACC,EACL,EACF,EACW,EACV,EACP;;;UA5FMA,uDAAA,IAAuB;UAAvBA,wDAAA,cAAA0E,GAAA,CAAA5D,SAAA,CAAuB;UASdd,uDAAA,GAA4B;UAA5BA,+DAAA,CAAA0E,GAAA,CAAA5B,aAAA,UAA4B;UAOhC9C,uDAAA,GAA2C;UAA3CA,wDAAA,SAAA0E,GAAA,CAAA9D,YAAA,uBAA2C;UAO1CZ,uDAAA,EAAmC;;UAE/BA,uDAAA,GAAoD;UAApDA,+DAAA,CAAA0E,GAAA,CAAA9D,YAAA,mCAAoD;UAErDZ,uDAAA,GAA+B;UAA/BA,+DAAA,CAAA0E,GAAA,CAAA5B,aAAA,aAA+B;UAQpC9C,uDAAA,EAAsB;UAAtBA,wDAAA,aAAA0E,GAAA,CAAA/D,SAAA,CAAsB;UACeX,uDAAA,EAAe;UAAfA,wDAAA,SAAA0E,GAAA,CAAA/D,SAAA,CAAe;UACnDX,uDAAA,EAAgB;UAAhBA,wDAAA,UAAA0E,GAAA,CAAA/D,SAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC5DmC;AAIjB;;;;;;;;;;;;;;;;;;IC4CvCX,4DAAA,qBAA6D;IAC3DA,oDAAA,GACF;IAAAA,0DAAA,EAAa;;;;IAF2BA,wDAAA,UAAAwF,OAAA,CAAA1D,KAAA,CAAoB;IAC1D9B,uDAAA,EACF;IADEA,gEAAA,MAAAwF,OAAA,CAAAE,KAAA,MACF;;;;;IAWI1F,4DAFJ,qBAA4D,cACjC,eACE;IAAAA,oDAAA,GAAgB;IAAAA,0DAAA,EAAO;IAChDA,4DAAA,eAA+B;IAAAA,oDAAA,GAAsB;IAEzDA,0DAFyD,EAAO,EACxD,EACK;;;;IAL0BA,wDAAA,UAAA2F,OAAA,CAAA7D,KAAA,CAAoB;IAE9B9B,uDAAA,GAAgB;IAAhBA,+DAAA,CAAA2F,OAAA,CAAAD,KAAA,CAAgB;IACV1F,uDAAA,GAAsB;IAAtBA,+DAAA,CAAA2F,OAAA,CAAAC,WAAA,CAAsB;;;;;IA6C3D5F,4DAAA,cAAgE;IAC9DA,oDAAA,GACF;IAAAA,0DAAA,EAAM;;;;IADJA,uDAAA,EACF;IADEA,gEAAA,MAAA6F,MAAA,CAAA/C,aAAA,qBACF;;;;;IASA9C,uDAAA,sBAA2D;;;;;IAC3DA,4DAAA,WAAyB;IAAAA,oDAAA,4BAAgB;IAAAA,0DAAA,EAAO;;;AD5GpD,MAAOZ,iBAAiB;EAiB5BiB,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdE,QAAqB;IAHrB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAE,QAAQ,GAARA,QAAQ;IAnBlB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAkF,mBAAmB,GAAG,IAAI;IAE1B,KAAAC,MAAM,GAAG,CACP;MAAEjE,KAAK,EAAE,UAAU;MAAE4D,KAAK,EAAE;IAAU,CAAE,EACxC;MAAE5D,KAAK,EAAE,QAAQ;MAAE4D,KAAK,EAAE;IAAQ,CAAE,CACrC;IAED,KAAAM,KAAK,GAAG,CACN;MAAElE,KAAK,EAAEyD,kDAAQ,CAACU,IAAI;MAAEP,KAAK,EAAE,aAAa;MAAEE,WAAW,EAAE;IAAyC,CAAE,EACtG;MAAE9D,KAAK,EAAEyD,kDAAQ,CAACW,QAAQ;MAAER,KAAK,EAAE,YAAY;MAAEE,WAAW,EAAE;IAAoC,CAAE,EACpG;MAAE9D,KAAK,EAAEyD,kDAAQ,CAACY,SAAS;MAAET,KAAK,EAAE,YAAY;MAAEE,WAAW,EAAE;IAAoC,CAAE,CACtG;IAQC,IAAI,CAACQ,YAAY,GAAG,IAAI,CAAC9F,EAAE,CAACS,KAAK,CAAC;MAChCsF,IAAI,EAAE,CAAC,EAAE,EAAE,CAACtG,sDAAU,CAACkB,QAAQ,EAAElB,sDAAU,CAACoB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DH,KAAK,EAAE,CAAC,EAAE,EAAE,CAACjB,sDAAU,CAACkB,QAAQ,EAAElB,sDAAU,CAACiB,KAAK,CAAC,CAAC;MACpDsF,KAAK,EAAE,CAAC,EAAE,EAAE,CAACvG,sDAAU,CAACwG,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;MAC/CC,IAAI,EAAE,CAAC,EAAE,EAAEzG,sDAAU,CAACkB,QAAQ,CAAC;MAC/BwF,IAAI,EAAE,CAAClB,kDAAQ,CAACU,IAAI,EAAElG,sDAAU,CAACkB,QAAQ,CAAC;MAC1CC,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACnB,sDAAU,CAACkB,QAAQ,EAAElB,sDAAU,CAACoB,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DuF,eAAe,EAAE,CAAC,EAAE,EAAE3G,sDAAU,CAACkB,QAAQ,CAAC;MAC1C0F,WAAW,EAAE,CAAC,KAAK,EAAE5G,sDAAU,CAAC6G,YAAY;KAC7C,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEA1F,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAACb,WAAW,CAACgB,eAAe,EAAE,EAAE;MACtC,IAAI,CAACf,MAAM,CAACgB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEAsF,sBAAsBA,CAACC,IAAe;IACpC,MAAM7F,QAAQ,GAAG6F,IAAI,CAACnE,GAAG,CAAC,UAAU,CAAC;IACrC,MAAM8D,eAAe,GAAGK,IAAI,CAACnE,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAI1B,QAAQ,IAAIwF,eAAe,IAAIxF,QAAQ,CAACY,KAAK,KAAK4E,eAAe,CAAC5E,KAAK,EAAE;MAC3E4E,eAAe,CAACM,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;KACtD,MAAM,IAAIP,eAAe,EAAEzD,MAAM,GAAG,kBAAkB,CAAC,EAAE;MACxD,OAAOyD,eAAe,CAACzD,MAAM,CAAC,kBAAkB,CAAC;MACjD,IAAIX,MAAM,CAACC,IAAI,CAACmE,eAAe,CAACzD,MAAM,CAAC,CAACiE,MAAM,KAAK,CAAC,EAAE;QACpDR,eAAe,CAACM,SAAS,CAAC,IAAI,CAAC;;;IAGnC,OAAO,IAAI;EACb;EAEMvF,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,wJAAA;MACZ,IAAID,KAAI,CAAC0E,YAAY,CAACxE,OAAO,EAAE;QAC7BF,KAAI,CAACG,oBAAoB,EAAE;QAC3B;;MAGFH,KAAI,CAACf,SAAS,GAAG,IAAI;MACrB,MAAMwG,SAAS,GAAGzF,KAAI,CAAC0E,YAAY,CAACtE,KAAK;MAEzC,IAAI;QACF,MAAMJ,KAAI,CAACnB,WAAW,CAAC6G,QAAQ,CAAC;UAC9BpG,KAAK,EAAEmG,SAAS,CAACnG,KAAK;UACtBE,QAAQ,EAAEiG,SAAS,CAACjG,QAAQ;UAC5BmF,IAAI,EAAEc,SAAS,CAACd,IAAI;UACpBC,KAAK,EAAEa,SAAS,CAACb,KAAK;UACtBE,IAAI,EAAEW,SAAS,CAACX,IAAI;UACpBC,IAAI,EAAEU,SAAS,CAACV;SACjB,CAAC;QAEF/E,KAAI,CAAChB,QAAQ,CAACsB,IAAI,CAAC,0BAA0B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC5EP,KAAI,CAAClB,MAAM,CAACgB,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;OACrC,CAAC,OAAOU,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CR,KAAI,CAAChB,QAAQ,CAACsB,IAAI,CAChBN,KAAI,CAACU,eAAe,CAACF,KAAK,CAAC,EAC3B,QAAQ,EACR;UAAED,QAAQ,EAAE;QAAI,CAAE,CACnB;OACF,SAAS;QACRP,KAAI,CAACf,SAAS,GAAG,KAAK;;IACvB;EACH;EAEQyB,eAAeA,CAACF,KAAU;IAChC,IAAIA,KAAK,CAACG,IAAI,EAAE;MACd,QAAQH,KAAK,CAACG,IAAI;QAChB,KAAK,2BAA2B;UAC9B,OAAO,wCAAwC;QACjD,KAAK,oBAAoB;UACvB,OAAO,yBAAyB;QAClC,KAAK,oBAAoB;UACvB,OAAO,kCAAkC;QAC3C,KAAK,4BAA4B;UAC/B,OAAO,gCAAgC;QACzC;UACE,OAAO,uCAAuC;;;IAGpD,OAAO,uCAAuC;EAChD;EAEQR,oBAAoBA,CAAA;IAC1BS,MAAM,CAACC,IAAI,CAAC,IAAI,CAAC6D,YAAY,CAAC5D,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACpD,MAAMC,OAAO,GAAG,IAAI,CAACyD,YAAY,CAACxD,GAAG,CAACF,GAAG,CAAC;MAC1CC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAACC,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAACoD,YAAY,CAACxD,GAAG,CAACG,SAAS,CAAC;IAC9C,IAAIC,KAAK,EAAEC,MAAM,IAAID,KAAK,CAACE,OAAO,EAAE;MAClC,IAAIF,KAAK,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,IAAI,CAACoE,kBAAkB,CAACtE,SAAS,CAAC;;MAE3C,IAAIC,KAAK,CAACC,MAAM,CAAC,OAAO,CAAC,EAAE;QACzB,OAAO,uBAAuB;;MAEhC,IAAID,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,EAAE;QAC7B,OAAOF,SAAS,KAAK,UAAU,GAC7B,6CAA6C,GAC7C,oCAAoC;;MAExC,IAAIC,KAAK,CAACC,MAAM,CAAC,SAAS,CAAC,EAAE;QAC3B,OAAO,2CAA2C;;MAEpD,IAAID,KAAK,CAACC,MAAM,CAAC,kBAAkB,CAAC,EAAE;QACpC,OAAO,wCAAwC;;MAEjD,IAAID,KAAK,CAACC,MAAM,CAAC,cAAc,CAAC,EAAE;QAChC,OAAO,oCAAoC;;;IAG/C,OAAO,EAAE;EACX;EAEQoE,kBAAkBA,CAACtE,SAAiB;IAC1C,MAAMuE,QAAQ,GAA8B;MAC1CjB,IAAI,EAAE,YAAY;MAClBrF,KAAK,EAAE,cAAc;MACrBwF,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,aAAa;MACnBvF,QAAQ,EAAE,qBAAqB;MAC/BwF,eAAe,EAAE,sBAAsB;MACvCC,WAAW,EAAE;KACd;IACD,OAAOW,QAAQ,CAACvE,SAAS,CAAC,IAAI,cAAc;EAC9C;;;uBAzJW3D,iBAAiB,EAAAY,+DAAA,CAAAN,uDAAA,GAAAM,+DAAA,CAAA4D,oEAAA,GAAA5D,+DAAA,CAAA8D,mDAAA,GAAA9D,+DAAA,CAAAiE,oEAAA;IAAA;EAAA;;;YAAjB7E,iBAAiB;MAAA+E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAgD,2BAAA9C,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTxBzE,4DAHN,aAAgC,kBACE,sBACb,aACc;UAC3BA,uDAAA,aAAoD;UACpDA,4DAAA,SAAI;UAAAA,oDAAA,sBAAe;UAAAA,0DAAA,EAAK;UACxBA,4DAAA,QAAG;UAAAA,oDAAA,6CAAiC;UAExCA,0DAFwC,EAAI,EACpC,EACU;UAGhBA,4DADF,uBAAkB,eAC+D;UAA9CA,wDAAA,sBAAAwH,qDAAA;YAAA,OAAY9C,GAAA,CAAAjD,QAAA,EAAU;UAAA,EAAC;UAGpDzB,4DADF,yBAAwD,iBAC3C;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAY;UAClCA,uDAAA,gBAGuC;UACvCA,4DAAA,mBAAoB;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAW;UACrCA,4DAAA,iBAAW;UAAAA,oDAAA,IAA2B;UACxCA,0DADwC,EAAY,EACnC;UAIfA,4DADF,yBAAwD,iBAC3C;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAY;UACpCA,uDAAA,gBAGqC;UACrCA,4DAAA,mBAAoB;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAW;UACpCA,4DAAA,iBAAW;UAAAA,oDAAA,IAA4B;UACzCA,0DADyC,EAAY,EACpC;UAIfA,4DADF,yBAAwD,iBAC3C;UAAAA,oDAAA,uCAAqB;UAAAA,0DAAA,EAAY;UAC5CA,uDAAA,gBAG8B;UAC9BA,4DAAA,mBAAoB;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAW;UACpCA,4DAAA,iBAAW;UAAAA,oDAAA,IAA4B;UACzCA,0DADyC,EAAY,EACpC;UAIfA,4DADF,yBAAwD,iBAC3C;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAY;UAC5BA,4DAAA,sBAAmC;UACjCA,wDAAA,KAAAyH,wCAAA,yBAA6D;UAG/DzH,0DAAA,EAAa;UACbA,4DAAA,iBAAW;UAAAA,oDAAA,IAA2B;UACxCA,0DADwC,EAAY,EACnC;UAIfA,4DADF,yBAAwD,iBAC3C;UAAAA,oDAAA,sBAAc;UAAAA,0DAAA,EAAY;UACrCA,4DAAA,sBAAmC;UACjCA,wDAAA,KAAA0H,wCAAA,yBAA4D;UAM9D1H,0DAAA,EAAa;UACbA,4DAAA,iBAAW;UAAAA,oDAAA,IAA2B;UACxCA,0DADwC,EAAY,EACnC;UAIfA,4DADF,yBAAwD,iBAC3C;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAY;UACnCA,uDAAA,iBAGwC;UACxCA,4DAAA,kBAG+C;UAAvCA,wDAAA,mBAAA2H,oDAAA;YAAA,OAAAjD,GAAA,CAAA9D,YAAA,IAAA8D,GAAA,CAAA9D,YAAA;UAAA,EAAsC;UAC5CZ,4DAAA,gBAAU;UAAAA,oDAAA,IAAoD;UAChEA,0DADgE,EAAW,EAClE;UACTA,4DAAA,iBAAW;UAAAA,oDAAA,IAA+B;UAC5CA,0DAD4C,EAAY,EACvC;UAIfA,4DADF,yBAAwD,iBAC3C;UAAAA,oDAAA,iCAAyB;UAAAA,0DAAA,EAAY;UAChDA,uDAAA,iBAGkD;UAClDA,4DAAA,kBAG6D;UAArDA,wDAAA,mBAAA4H,oDAAA;YAAA,OAAAlD,GAAA,CAAAoB,mBAAA,IAAApB,GAAA,CAAAoB,mBAAA;UAAA,EAAoD;UAC1D9F,4DAAA,gBAAU;UAAAA,oDAAA,IAA2D;UACvEA,0DADuE,EAAW,EACzE;UACTA,4DAAA,iBAAW;UAAAA,oDAAA,IAAsC;UACnDA,0DADmD,EAAY,EAC9C;UAIfA,4DADF,eAA2B,wBAC0C;UACjEA,oDAAA,uBAAc;UAAAA,4DAAA,aAAyB;UAAAA,oDAAA,gCAAwB;UAAAA,0DAAA,EAAI;UACnEA,oDAAA,eAAM;UAAAA,4DAAA,aAAyB;UAAAA,oDAAA,yCAA4B;UAC7DA,0DAD6D,EAAI,EAClD;UACfA,wDAAA,KAAA6H,iCAAA,kBAAgE;UAGlE7H,0DAAA,EAAM;UAGNA,4DAAA,kBAI+B;UAE7BA,wDADA,KAAA8H,yCAAA,0BAA6C,KAAAC,kCAAA,mBACpB;UAC3B/H,0DAAA,EAAS;UAIPA,4DADF,eAAwB,SACnB;UAAAA,oDAAA,kCACD;UAAAA,4DAAA,aAAyC;UAAAA,oDAAA,oBAAY;UAMjEA,0DANiE,EAAI,EACvD,EACA,EACD,EACU,EACV,EACP;;;UAzHMA,uDAAA,IAA0B;UAA1BA,wDAAA,cAAA0E,GAAA,CAAA0B,YAAA,CAA0B;UASjBpG,uDAAA,GAA2B;UAA3BA,+DAAA,CAAA0E,GAAA,CAAA5B,aAAA,SAA2B;UAW3B9C,uDAAA,GAA4B;UAA5BA,+DAAA,CAAA0E,GAAA,CAAA5B,aAAA,UAA4B;UAW5B9C,uDAAA,GAA4B;UAA5BA,+DAAA,CAAA0E,GAAA,CAAA5B,aAAA,UAA4B;UAOR9C,uDAAA,GAAS;UAATA,wDAAA,YAAA0E,GAAA,CAAAqB,MAAA,CAAS;UAI7B/F,uDAAA,GAA2B;UAA3BA,+DAAA,CAAA0E,GAAA,CAAA5B,aAAA,SAA2B;UAOP9C,uDAAA,GAAQ;UAARA,wDAAA,YAAA0E,GAAA,CAAAsB,KAAA,CAAQ;UAO5BhG,uDAAA,GAA2B;UAA3BA,+DAAA,CAAA0E,GAAA,CAAA5B,aAAA,SAA2B;UAO/B9C,uDAAA,GAA2C;UAA3CA,wDAAA,SAAA0E,GAAA,CAAA9D,YAAA,uBAA2C;UAOtCZ,uDAAA,GAAoD;UAApDA,+DAAA,CAAA0E,GAAA,CAAA9D,YAAA,mCAAoD;UAErDZ,uDAAA,GAA+B;UAA/BA,+DAAA,CAAA0E,GAAA,CAAA5B,aAAA,aAA+B;UAOnC9C,uDAAA,GAAkD;UAAlDA,wDAAA,SAAA0E,GAAA,CAAAoB,mBAAA,uBAAkD;UAO7C9F,uDAAA,GAA2D;UAA3DA,+DAAA,CAAA0E,GAAA,CAAAoB,mBAAA,mCAA2D;UAE5D9F,uDAAA,GAAsC;UAAtCA,+DAAA,CAAA0E,GAAA,CAAA5B,aAAA,oBAAsC;UASrB9C,uDAAA,GAAkC;UAAlCA,wDAAA,SAAA0E,GAAA,CAAA5B,aAAA,gBAAkC;UAUxD9C,uDAAA,EAAsB;UAAtBA,wDAAA,aAAA0E,GAAA,CAAA/D,SAAA,CAAsB;UACAX,uDAAA,EAAe;UAAfA,wDAAA,SAAA0E,GAAA,CAAA/D,SAAA,CAAe;UACpCX,uDAAA,EAAgB;UAAhBA,wDAAA,UAAA0E,GAAA,CAAA/D,SAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACxHG;AACkO;AACzK;AACF;;AAE3F;AAAA,MAAA4I,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACA,MAAMC,4BAA4B,GAAG,IAAI1B,yDAAc,CAAC,8BAA8B,EAAE;EACpF2B,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEC;AACb,CAAC,CAAC;AACF;AACA,SAASA,oCAAoCA,CAAA,EAAG;EAC5C,OAAO;IACHC,KAAK,EAAE,QAAQ;IACfC,WAAW,EAAE;EACjB,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA,IAAIC,oBAAoB;AACxB,CAAC,UAAUA,oBAAoB,EAAE;EAC7B;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,GAAG,MAAM;EAC/D;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,SAAS,CAAC,GAAG,CAAC,CAAC,GAAG,SAAS;EACrE;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,WAAW,CAAC,GAAG,CAAC,CAAC,GAAG,WAAW;EACzE;EACAA,oBAAoB,CAACA,oBAAoB,CAAC,eAAe,CAAC,GAAG,CAAC,CAAC,GAAG,eAAe;AACrF,CAAC,EAAEA,oBAAoB,KAAKA,oBAAoB,GAAG,CAAC,CAAC,CAAC,CAAC;AACvD;AACA;AACA;AACA;AACA,MAAMC,mCAAmC,GAAG;EACxCC,OAAO,EAAEjB,6DAAiB;EAC1BkB,WAAW,EAAElC,yDAAU,CAAC,MAAMmC,WAAW,CAAC;EAC1CC,KAAK,EAAE;AACX,CAAC;AACD;AACA,MAAMC,iBAAiB,CAAC;AAExB;AACA,IAAIC,YAAY,GAAG,CAAC;AACpB;AACA,MAAMC,QAAQ,GAAGX,oCAAoC,CAAC,CAAC;AACvD,MAAMO,WAAW,CAAC;EACd;EACAK,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,aAAa,CAACC,aAAa,CAACF,KAAK,CAAC,CAAC;EAC5C;EACA;EACAG,kBAAkBA,CAACC,SAAS,EAAE;IAC1B,MAAMC,KAAK,GAAG,IAAIR,iBAAiB,CAAC,CAAC;IACrCQ,KAAK,CAACC,MAAM,GAAG,IAAI;IACnBD,KAAK,CAACE,OAAO,GAAGH,SAAS;IACzB,OAAOC,KAAK;EAChB;EACA;EACAG,0BAA0BA,CAAA,EAAG;IACzB,OAAO,IAAI,CAACP,aAAa,EAAEC,aAAa;EAC5C;EACA;EACA,IAAIO,OAAOA,CAAA,EAAG;IACV,OAAO,GAAG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACC,SAAS,QAAQ;EAC/C;EACA/K,WAAWA,CAACgL,WAAW,EAAEC,kBAAkB,EAAEC,OAAO,EAAEC,QAAQ,EAAEC,cAAc,EAAEC,QAAQ,EAAE;IACtF,IAAI,CAACL,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACE,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,QAAQ,GAAGA,QAAQ;IACxB;IACA,IAAI,CAACC,iBAAiB,GAAG;MACrBC,kBAAkB,EAAE,sCAAsC;MAC1DC,wBAAwB,EAAE,4CAA4C;MACtEC,kBAAkB,EAAE,sCAAsC;MAC1DC,sBAAsB,EAAE,0CAA0C;MAClEC,sBAAsB,EAAE,0CAA0C;MAClEC,wBAAwB,EAAE;IAC9B,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,EAAE;IACnB;AACR;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACC,aAAa,GAAG,OAAO;IAC5B;IACA,IAAI,CAAC/F,IAAI,GAAG,IAAI;IAChB;IACA,IAAI,CAACgG,MAAM,GAAG,IAAInE,uDAAY,CAAC,CAAC;IAChC;IACA,IAAI,CAACoE,mBAAmB,GAAG,IAAIpE,uDAAY,CAAC,CAAC;IAC7C;AACR;AACA;AACA;IACQ,IAAI,CAACqE,UAAU,GAAG,MAAM,CAAE,CAAC;IAC3B,IAAI,CAACC,sBAAsB,GAAG,EAAE;IAChC,IAAI,CAACC,kBAAkB,GAAGzC,oBAAoB,CAAC0C,IAAI;IACnD,IAAI,CAACC,6BAA6B,GAAG,MAAM,CAAE,CAAC;IAC9C,IAAI,CAACC,kBAAkB,GAAG,MAAM,CAAE,CAAC;IACnC,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACC,SAAS,GAAG,KAAK;IACtB,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACrB,QAAQ,GAAG,IAAI,CAACA,QAAQ,IAAIlB,QAAQ;IACzC,IAAI,CAACV,KAAK,GAAG,IAAI,CAAC4B,QAAQ,CAAC5B,KAAK,IAAIU,QAAQ,CAACV,KAAK;IAClD,IAAI,CAAC0B,QAAQ,GAAGwB,QAAQ,CAACxB,QAAQ,CAAC,IAAI,CAAC;IACvC,IAAI,CAACL,EAAE,GAAG,IAAI,CAACC,SAAS,GAAG,oBAAoB,EAAEb,YAAY,EAAE;EACnE;EACA0C,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC,UAAU,CAAC,EAAE;MACrB,IAAI,CAACN,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACAO,eAAeA,CAAA,EAAG;IACd,IAAI,CAACC,kBAAkB,CAAC,IAAI,CAACL,cAAc,CAAC;EAChD;EACA;EACA,IAAI/B,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC6B,QAAQ;EACxB;EACA,IAAI7B,OAAOA,CAAClJ,KAAK,EAAE;IACf,IAAIA,KAAK,IAAI,IAAI,CAACkJ,OAAO,EAAE;MACvB,IAAI,CAAC6B,QAAQ,GAAG/K,KAAK;MACrB,IAAI,CAACwJ,kBAAkB,CAAC+B,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACR,SAAS;EACzB;EACA,IAAIQ,QAAQA,CAACxL,KAAK,EAAE;IAChB,IAAIA,KAAK,KAAK,IAAI,CAACwL,QAAQ,EAAE;MACzB,IAAI,CAACR,SAAS,GAAGhL,KAAK;MACtB,IAAI,CAACwJ,kBAAkB,CAAC+B,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACI,IAAIE,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACR,cAAc;EAC9B;EACA,IAAIQ,aAAaA,CAACzL,KAAK,EAAE;IACrB,MAAM0L,OAAO,GAAG1L,KAAK,IAAI,IAAI,CAACiL,cAAc;IAC5C,IAAI,CAACA,cAAc,GAAGjL,KAAK;IAC3B,IAAI0L,OAAO,EAAE;MACT,IAAI,IAAI,CAACT,cAAc,EAAE;QACrB,IAAI,CAACU,qBAAqB,CAACzD,oBAAoB,CAAC0D,aAAa,CAAC;MAClE,CAAC,MACI;QACD,IAAI,CAACD,qBAAqB,CAAC,IAAI,CAACzC,OAAO,GAAGhB,oBAAoB,CAAC2D,OAAO,GAAG3D,oBAAoB,CAAC4D,SAAS,CAAC;MAC5G;MACA,IAAI,CAACtB,mBAAmB,CAACuB,IAAI,CAAC,IAAI,CAACd,cAAc,CAAC;IACtD;IACA,IAAI,CAACK,kBAAkB,CAAC,IAAI,CAACL,cAAc,CAAC;EAChD;EACAe,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACC,aAAa,IAAI,IAAI,CAACT,QAAQ;EAC9C;EACA;EACAU,kBAAkBA,CAAA,EAAG;IACjB;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC1C,kBAAkB,CAAC2C,aAAa,CAAC,CAAC;EAC3C;EACA;EACAC,UAAUA,CAACpM,KAAK,EAAE;IACd,IAAI,CAACkJ,OAAO,GAAG,CAAC,CAAClJ,KAAK;EAC1B;EACA;EACAqM,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAACzB,6BAA6B,GAAGyB,EAAE;EAC3C;EACA;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAAC7B,UAAU,GAAG6B,EAAE;EACxB;EACA;EACAE,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAACjB,QAAQ,GAAGiB,UAAU;EAC9B;EACA;EACAC,QAAQA,CAAC7L,OAAO,EAAE;IACd,OAAO,IAAI,CAAC1B,QAAQ,IAAI0B,OAAO,CAACb,KAAK,KAAK,IAAI,GAAG;MAAE,UAAU,EAAE;IAAK,CAAC,GAAG,IAAI;EAChF;EACA;EACA2M,yBAAyBA,CAACL,EAAE,EAAE;IAC1B,IAAI,CAACxB,kBAAkB,GAAGwB,EAAE;EAChC;EACAX,qBAAqBA,CAACiB,QAAQ,EAAE;IAC5B,IAAIC,QAAQ,GAAG,IAAI,CAAClC,kBAAkB;IACtC,IAAImC,OAAO,GAAG,IAAI,CAAC3D,0BAA0B,CAAC,CAAC;IAC/C,IAAI0D,QAAQ,KAAKD,QAAQ,IAAI,CAACE,OAAO,EAAE;MACnC;IACJ;IACA,IAAI,IAAI,CAACpC,sBAAsB,EAAE;MAC7BoC,OAAO,CAACC,SAAS,CAACC,MAAM,CAAC,IAAI,CAACtC,sBAAsB,CAAC;IACzD;IACA,IAAI,CAACA,sBAAsB,GAAG,IAAI,CAACuC,yCAAyC,CAACJ,QAAQ,EAAED,QAAQ,CAAC;IAChG,IAAI,CAACjC,kBAAkB,GAAGiC,QAAQ;IAClC,IAAI,IAAI,CAAClC,sBAAsB,CAACtF,MAAM,GAAG,CAAC,EAAE;MACxC0H,OAAO,CAACC,SAAS,CAACG,GAAG,CAAC,IAAI,CAACxC,sBAAsB,CAAC;MAClD;MACA,MAAMyC,cAAc,GAAG,IAAI,CAACzC,sBAAsB;MAClD,IAAI,CAACjB,OAAO,CAAC2D,iBAAiB,CAAC,MAAM;QACjCC,UAAU,CAAC,MAAM;UACbP,OAAO,CAACC,SAAS,CAACC,MAAM,CAACG,cAAc,CAAC;QAC5C,CAAC,EAAE,IAAI,CAAC;MACZ,CAAC,CAAC;IACN;EACJ;EACAG,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACzC,6BAA6B,CAAC,IAAI,CAAC3B,OAAO,CAAC;IAChD,IAAI,CAACqB,MAAM,CAACwB,IAAI,CAAC,IAAI,CAACjD,kBAAkB,CAAC,IAAI,CAACI,OAAO,CAAC,CAAC;IACvD;IACA;IACA,IAAI,IAAI,CAACN,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACC,aAAa,CAACK,OAAO,GAAG,IAAI,CAACA,OAAO;IAC3D;EACJ;EACA;EACAqE,MAAMA,CAAA,EAAG;IACL,IAAI,CAACrE,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B,IAAI,CAAC2B,6BAA6B,CAAC,IAAI,CAAC3B,OAAO,CAAC;EACpD;EACAsE,iBAAiBA,CAAA,EAAG;IAChB,MAAMvF,WAAW,GAAG,IAAI,CAAC2B,QAAQ,EAAE3B,WAAW;IAC9C;IACA,IAAI,CAAC,IAAI,CAACuD,QAAQ,IAAIvD,WAAW,KAAK,MAAM,EAAE;MAC1C;MACA,IAAI,IAAI,CAACwD,aAAa,IAAIxD,WAAW,KAAK,OAAO,EAAE;QAC/CwF,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB,IAAI,CAAC1C,cAAc,GAAG,KAAK;UAC3B,IAAI,CAACT,mBAAmB,CAACuB,IAAI,CAAC,IAAI,CAACd,cAAc,CAAC;QACtD,CAAC,CAAC;MACN;MACA,IAAI,CAACF,QAAQ,GAAG,CAAC,IAAI,CAACA,QAAQ;MAC9B,IAAI,CAACY,qBAAqB,CAAC,IAAI,CAACZ,QAAQ,GAAG7C,oBAAoB,CAAC2D,OAAO,GAAG3D,oBAAoB,CAAC4D,SAAS,CAAC;MACzG;MACA;MACA;MACA,IAAI,CAACwB,gBAAgB,CAAC,CAAC;IAC3B,CAAC,MACI,IAAI,CAAC,IAAI,CAAC9B,QAAQ,IAAIvD,WAAW,KAAK,MAAM,EAAE;MAC/C;MACA;MACA,IAAI,CAACW,aAAa,CAACC,aAAa,CAACK,OAAO,GAAG,IAAI,CAACA,OAAO;MACvD,IAAI,CAACN,aAAa,CAACC,aAAa,CAAC4C,aAAa,GAAG,IAAI,CAACA,aAAa;IACvE;EACJ;EACAmC,mBAAmBA,CAAC5E,KAAK,EAAE;IACvB;IACA;IACA;IACAA,KAAK,CAAC6E,eAAe,CAAC,CAAC;EAC3B;EACAC,OAAOA,CAAA,EAAG;IACN;IACA;IACA;IACA;IACA;IACAL,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,CAAClD,UAAU,CAAC,CAAC;MACjB,IAAI,CAACjB,kBAAkB,CAAC+B,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACA0B,yCAAyCA,CAACJ,QAAQ,EAAED,QAAQ,EAAE;IAC1D;IACA,IAAI,IAAI,CAACjD,cAAc,KAAK,gBAAgB,EAAE;MAC1C,OAAO,EAAE;IACb;IACA,QAAQkD,QAAQ;MACZ,KAAK3E,oBAAoB,CAAC0C,IAAI;QAC1B;QACA;QACA,IAAIgC,QAAQ,KAAK1E,oBAAoB,CAAC2D,OAAO,EAAE;UAC3C,OAAO,IAAI,CAAChC,iBAAiB,CAACC,kBAAkB;QACpD,CAAC,MACI,IAAI8C,QAAQ,IAAI1E,oBAAoB,CAAC0D,aAAa,EAAE;UACrD,OAAO,IAAI,CAACb,QAAQ,GACd,IAAI,CAAClB,iBAAiB,CAACI,sBAAsB,GAC7C,IAAI,CAACJ,iBAAiB,CAACE,wBAAwB;QACzD;QACA;MACJ,KAAK7B,oBAAoB,CAAC4D,SAAS;QAC/B,OAAOc,QAAQ,KAAK1E,oBAAoB,CAAC2D,OAAO,GAC1C,IAAI,CAAChC,iBAAiB,CAACC,kBAAkB,GACzC,IAAI,CAACD,iBAAiB,CAACE,wBAAwB;MACzD,KAAK7B,oBAAoB,CAAC2D,OAAO;QAC7B,OAAOe,QAAQ,KAAK1E,oBAAoB,CAAC4D,SAAS,GAC5C,IAAI,CAACjC,iBAAiB,CAACG,kBAAkB,GACzC,IAAI,CAACH,iBAAiB,CAACI,sBAAsB;MACvD,KAAK/B,oBAAoB,CAAC0D,aAAa;QACnC,OAAOgB,QAAQ,KAAK1E,oBAAoB,CAAC2D,OAAO,GAC1C,IAAI,CAAChC,iBAAiB,CAACK,sBAAsB,GAC7C,IAAI,CAACL,iBAAiB,CAACM,wBAAwB;IAC7D;IACA,OAAO,EAAE;EACb;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACImB,kBAAkBA,CAACtL,KAAK,EAAE;IACtB,MAAM+N,cAAc,GAAG,IAAI,CAACnF,aAAa;IACzC,IAAImF,cAAc,EAAE;MAChBA,cAAc,CAAClF,aAAa,CAAC4C,aAAa,GAAGzL,KAAK;IACtD;EACJ;EACAgO,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACR,iBAAiB,CAAC,CAAC;EAC5B;EACAS,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACT,iBAAiB,CAAC,CAAC;IACxB,IAAI,CAAC,IAAI,CAAChC,QAAQ,EAAE;MAChB;MACA;MACA,IAAI,CAAC5C,aAAa,CAACC,aAAa,CAACF,KAAK,CAAC,CAAC;IAC5C;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIuF,yBAAyBA,CAAClF,KAAK,EAAE;IAC7B,IAAI,CAAC,CAACA,KAAK,CAACmF,MAAM,IAAI,IAAI,CAACC,aAAa,CAACvF,aAAa,CAACwF,QAAQ,CAACrF,KAAK,CAACmF,MAAM,CAAC,EAAE;MAC3EnF,KAAK,CAAC6E,eAAe,CAAC,CAAC;IAC3B;EACJ;EACA;IAAS,IAAI,CAACS,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFlG,WAAW,EAArBpK,+DAAE,CAAqCA,qDAAa,GAApDA,+DAAE,CAA+DA,4DAAoB,GAArFA,+DAAE,CAAgGA,iDAAS,GAA3GA,+DAAE,CAAsH,UAAU,GAAlIA,+DAAE,CAA8JmI,gEAAqB,MAArLnI,+DAAE,CAAgN0J,4BAA4B;IAAA,CAA4D;EAAE;EAC5Y;IAAS,IAAI,CAACiH,IAAI,kBAD8E3Q,+DAAE;MAAA6Q,IAAA,EACJzG,WAAW;MAAAjG,SAAA;MAAA2M,SAAA,WAAAC,kBAAAtM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADTzE,yDAAE,CAAAuJ,GAAA;UAAFvJ,yDAAE,CAAAwJ,GAAA;UAAFxJ,yDAAE,CAQuJoJ,6DAAS;QAAA;QAAA,IAAA3E,EAAA;UAAA,IAAAwM,EAAA;UARlKjR,4DAAE,CAAAiR,EAAA,GAAFjR,yDAAE,QAAA0E,GAAA,CAAAgG,aAAA,GAAAuG,EAAA,CAAAG,KAAA;UAAFpR,4DAAE,CAAAiR,EAAA,GAAFjR,yDAAE,QAAA0E,GAAA,CAAAwL,aAAA,GAAAe,EAAA,CAAAG,KAAA;UAAFpR,4DAAE,CAAAiR,EAAA,GAAFjR,yDAAE,QAAA0E,GAAA,CAAA2M,MAAA,GAAAJ,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAE,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,yBAAAhN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFzE,4DAAE,OAAA0E,GAAA,CAAAyG,EACM,CAAC;UADTnL,yDAAE,aACJ,IAAI,gBAAJ,IAAI,qBAAJ,IAAI;UADFA,wDAAE,CAAA0E,GAAA,CAAAoF,KAAA,GACI,MAAM,GAAApF,GAAA,CAAAoF,KAAA,GAAW,YAAf,CAAC;UADT9J,yDAAE,4BAAA0E,GAAA,CAAA+G,cAAA,KACe,gBAAT,CAAC,2BAAA/G,GAAA,CAAA4I,QAAD,CAAC,8BAAA5I,GAAA,CAAA4I,QAAD,CAAC,6BAAA5I,GAAA,CAAAsG,OAAD,CAAC;QAAA;MAAA;MAAA8G,MAAA;QAAA5F,SAAA,GADTlM,0DAAE,CAAAgS,IAAA;QAAA7F,cAAA,GAAFnM,0DAAE,CAAAgS,IAAA;QAAAC,eAAA,GAAFjS,0DAAE,CAAAgS,IAAA;QAAA7G,EAAA;QAAAlK,QAAA,GAAFjB,0DAAE,CAAAkS,0BAAA,0BACuQ9J,2DAAgB;QAAAgE,aAAA;QAAA/F,IAAA;QAAAvE,KAAA;QAAAiM,aAAA,GADzR/N,0DAAE,CAAAkS,0BAAA,oCAC0Y9J,2DAAgB;QAAAoD,QAAA,GAD5ZxL,0DAAE,CAAAkS,0BAAA,0BACicpQ,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAGqQ,SAAS,GAAG9J,8DAAe,CAACvG,KAAK,CAAE;QAAAgI,KAAA;QAAAkB,OAAA,GADjgBhL,0DAAE,CAAAkS,0BAAA,wBACkjB9J,2DAAgB;QAAAkF,QAAA,GADpkBtN,0DAAE,CAAAkS,0BAAA,0BACwmB9J,2DAAgB;QAAAmF,aAAA,GAD1nBvN,0DAAE,CAAAkS,0BAAA,oCAC6qB9J,2DAAgB;MAAA;MAAAgK,OAAA;QAAA/F,MAAA;QAAAC,mBAAA;MAAA;MAAA+F,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAD/rBvS,gEAAE,CAC4rC,CACtxCiK,mCAAmC,EACnC;QACIC,OAAO,EAAEhB,yDAAa;QACtBiB,WAAW,EAAEC,WAAW;QACxBC,KAAK,EAAE;MACX,CAAC,CACJ,GAR2FrK,sEAAE,EAAFA,kEAAE,EAAFA,iEAAE;MAAA4S,kBAAA,EAAAnJ,GAAA;MAAArF,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAsO,qBAAApO,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAqO,GAAA,GAAF9S,8DAAE;UAAFA,6DAAE;UAAFA,4DAAE,YAQ6W,CAAC;UARhXA,wDAAE,mBAAAiT,0CAAAC,MAAA;YAAFlT,2DAAE,CAAA8S,GAAA;YAAA,OAAF9S,yDAAE,CAQ0U0E,GAAA,CAAAsL,yBAAA,CAAAkD,MAAgC,CAAC;UAAA,CAAC,CAAC;UAR/WlT,4DAAE,eAQuZ,CAAC,YAAoJ,CAAC;UAR/iBA,wDAAE,mBAAAqT,0CAAA;YAAFrT,2DAAE,CAAA8S,GAAA;YAAA,OAAF9S,yDAAE,CAQqhB0E,GAAA,CAAAqL,mBAAA,CAAoB,CAAC;UAAA,CAAC,CAAC;UAR9iB/P,0DAAE,CAQkjB,CAAC;UARrjBA,4DAAE,iBAQ+1C,CAAC;UARl2CA,wDAAE,kBAAAsT,2CAAA;YAAFtT,2DAAE,CAAA8S,GAAA;YAAA,OAAF9S,yDAAE,CAQsvC0E,GAAA,CAAAkL,OAAA,CAAQ,CAAC;UAAA,CAAC,CAAC,mBAAA2D,4CAAA;YARnwCvT,2DAAE,CAAA8S,GAAA;YAAA,OAAF9S,yDAAE,CAQwxC0E,GAAA,CAAAoL,aAAA,CAAc,CAAC;UAAA,CAAC,CAAC,oBAAA0D,6CAAAN,MAAA;YAR3yClT,2DAAE,CAAA8S,GAAA;YAAA,OAAF9S,yDAAE,CAQi0C0E,GAAA,CAAAgL,mBAAA,CAAAwD,MAA0B,CAAC;UAAA,CAAC,CAAC;UARh2ClT,0DAAE,CAQ+1C,CAAC;UARl2CA,uDAAE,YAQ+4C,CAAC;UARl5CA,4DAAE,YAQ67C,CAAC;UARh8CA,4DAAE;UAAFA,4DAAE,YAQ+kD,CAAC;UARllDA,uDAAE,cAQ0tD,CAAC;UAR7tDA,0DAAE,CAQwuD,CAAC;UAR3uDA,6DAAE;UAAFA,uDAAE,cAQ6xD,CAAC;UARhyDA,0DAAE,CAQyyD,CAAC;UAR5yDA,uDAAE,cAQmgE,CAAC;UARtgEA,0DAAE,CAQ6gE,CAAC;UARhhEA,4DAAE,mBAQ01E,CAAC;UAR71EA,0DAAE,GAQy3E,CAAC;UAR53EA,0DAAE,CAQq4E,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAyE,EAAA;UAAA,MAAAmP,WAAA,GARh5E5T,yDAAE;UAAFA,wDAAE,kBAAA0E,GAAA,CAAA0H,aAQ8T,CAAC;UARjUpM,uDAAE,EAQ6sB,CAAC;UARhtBA,yDAAE,2BAAA0E,GAAA,CAAAsG,OAQ6sB,CAAC;UARhtBhL,wDAAE,YAAA0E,GAAA,CAAAsG,OAQwhC,CAAC,kBAAAtG,GAAA,CAAA6I,aAA6C,CAAC,aAAA7I,GAAA,CAAA4I,QAAmC,CAAC,OAAA5I,GAAA,CAAAwG,OAA4B,CAAC,aAAAxG,GAAA,CAAAzD,QAAmC,CAAC,aAAAyD,GAAA,CAAA4I,QAAA,QAAA5I,GAAA,CAAA8G,QAAmD,CAAC;UARluCxL,yDAAE,eAAA0E,GAAA,CAAAwH,SAAA,6BAAAxH,GAAA,CAAAyH,cAAA,sBAAAzH,GAAA,CAAAuN,eAAA,kBAAAvN,GAAA,CAAA6I,aAAA,2BAAA7I,GAAA,CAAA2B,IAAA,WAAA3B,GAAA,CAAA5C,KAAA;UAAF9B,uDAAE,EAQ+5D,CAAC;UARl6DA,wDAAE,qBAAA4T,WAQ+5D,CAAC,sBAAAlP,GAAA,CAAAqJ,aAAA,IAAArJ,GAAA,CAAA4I,QAAwD,CAAC,0BAAmC,CAAC;UAR//DtN,uDAAE,CAQy1E,CAAC;UAR51EA,wDAAE,QAAA0E,GAAA,CAAAwG,OAQy1E,CAAC;QAAA;MAAA;MAAA4I,YAAA,GAA44nB1K,6DAAS,EAAwPC,yEAAqB;MAAA0K,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAyK;EAAE;AAC7wtB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAVoGlU,+DAAE,CAUXoK,WAAW,EAAc,CAAC;IACzGyG,IAAI,EAAEvI,oDAAS;IACf8L,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,cAAc;MAAEC,IAAI,EAAE;QAC7B,OAAO,EAAE,kBAAkB;QAC3B,iBAAiB,EAAE,MAAM;QACzB,mBAAmB,EAAE,MAAM;QAC3B,wBAAwB,EAAE,MAAM;QAChC,iCAAiC,EAAE,qCAAqC;QACxE,gCAAgC,EAAE,UAAU;QAC5C,MAAM,EAAE,IAAI;QACZ;QACA,mCAAmC,EAAE,UAAU;QAC/C,kCAAkC,EAAE,SAAS;QAC7C,SAAS,EAAE;MACf,CAAC;MAAEC,SAAS,EAAE,CACVtK,mCAAmC,EACnC;QACIC,OAAO,EAAEhB,yDAAa;QACtBiB,WAAW,EAAEC,WAAW;QACxBC,KAAK,EAAE;MACX,CAAC,CACJ;MAAEgI,QAAQ,EAAE,aAAa;MAAE2B,aAAa,EAAEzL,4DAAiB,CAACyJ,IAAI;MAAEiC,eAAe,EAAEzL,kEAAuB,CAACgM,MAAM;MAAElC,UAAU,EAAE,IAAI;MAAE7S,OAAO,EAAE,CAAC2J,6DAAS,EAAEC,yEAAqB,CAAC;MAAE9E,QAAQ,EAAE,ipEAAipE;MAAEwP,MAAM,EAAE,CAAC,6xnBAA6xnB;IAAE,CAAC;EACrosB,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAElD,IAAI,EAAE7Q,qDAAauQ;EAAC,CAAC,EAAE;IAAEM,IAAI,EAAE7Q,4DAAoBwQ;EAAC,CAAC,EAAE;IAAEK,IAAI,EAAE7Q,iDAASyQ;EAAC,CAAC,EAAE;IAAEI,IAAI,EAAEsB,SAAS;IAAEsC,UAAU,EAAE,CAAC;MAC7H5D,IAAI,EAAEpI,oDAAS;MACf2L,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAEvD,IAAI,EAAEsB,SAAS;IAAEsC,UAAU,EAAE,CAAC;MAClC5D,IAAI,EAAEnI,mDAAQA;IAClB,CAAC,EAAE;MACCmI,IAAI,EAAElI,iDAAM;MACZyL,IAAI,EAAE,CAACjM,gEAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAE0I,IAAI,EAAEsB,SAAS;IAAEsC,UAAU,EAAE,CAAC;MAClC5D,IAAI,EAAEnI,mDAAQA;IAClB,CAAC,EAAE;MACCmI,IAAI,EAAElI,iDAAM;MACZyL,IAAI,EAAE,CAAC1K,4BAA4B;IACvC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEwC,SAAS,EAAE,CAAC;MACrC2E,IAAI,EAAEjI,gDAAK;MACXwL,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEjI,cAAc,EAAE,CAAC;MACjB0E,IAAI,EAAEjI,gDAAK;MACXwL,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEnC,eAAe,EAAE,CAAC;MAClBpB,IAAI,EAAEjI,gDAAK;MACXwL,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEjJ,EAAE,EAAE,CAAC;MACL0F,IAAI,EAAEjI,gDAAKA;IACf,CAAC,CAAC;IAAE3H,QAAQ,EAAE,CAAC;MACX4P,IAAI,EAAEjI,gDAAK;MACXwL,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEtM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAEgE,aAAa,EAAE,CAAC;MAChByE,IAAI,EAAEjI,gDAAKA;IACf,CAAC,CAAC;IAAEvC,IAAI,EAAE,CAAC;MACPwK,IAAI,EAAEjI,gDAAKA;IACf,CAAC,CAAC;IAAEyD,MAAM,EAAE,CAAC;MACTwE,IAAI,EAAEhI,iDAAMA;IAChB,CAAC,CAAC;IAAEyD,mBAAmB,EAAE,CAAC;MACtBuE,IAAI,EAAEhI,iDAAMA;IAChB,CAAC,CAAC;IAAE/G,KAAK,EAAE,CAAC;MACR+O,IAAI,EAAEjI,gDAAKA;IACf,CAAC,CAAC;IAAEmF,aAAa,EAAE,CAAC;MAChB8C,IAAI,EAAEjI,gDAAK;MACXwL,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEtM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAEsC,aAAa,EAAE,CAAC;MAChBmG,IAAI,EAAE/H,oDAAS;MACfsL,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAElE,aAAa,EAAE,CAAC;MAChBW,IAAI,EAAE/H,oDAAS;MACfsL,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE5I,QAAQ,EAAE,CAAC;MACXqF,IAAI,EAAEjI,gDAAK;MACXwL,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAG5S,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAGqQ,SAAS,GAAG9J,8DAAe,CAACvG,KAAK;MAAG,CAAC;IACzF,CAAC,CAAC;IAAEgI,KAAK,EAAE,CAAC;MACR+G,IAAI,EAAEjI,gDAAKA;IACf,CAAC,CAAC;IAAEyI,MAAM,EAAE,CAAC;MACTR,IAAI,EAAE/H,oDAAS;MACfsL,IAAI,EAAE,CAAChL,6DAAS;IACpB,CAAC,CAAC;IAAE4B,OAAO,EAAE,CAAC;MACV6F,IAAI,EAAEjI,gDAAK;MACXwL,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEtM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAEkF,QAAQ,EAAE,CAAC;MACXuD,IAAI,EAAEjI,gDAAK;MACXwL,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEtM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAEmF,aAAa,EAAE,CAAC;MAChBsD,IAAI,EAAEjI,gDAAK;MACXwL,IAAI,EAAE,CAAC;QAAEM,SAAS,EAAEtM,2DAAgBA;MAAC,CAAC;IAC1C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMuM,+BAA+B,GAAG;EACpCzK,OAAO,EAAEhB,yDAAa;EACtBiB,WAAW,EAAElC,yDAAU,CAAC,MAAM2M,4BAA4B,CAAC;EAC3DvK,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuK,4BAA4B,SAASzL,qEAAyB,CAAC;EACjE;IAAS,IAAI,CAACiH,IAAI;MAAA,IAAAyE,yCAAA;MAAA,gBAAAC,qCAAAxE,CAAA;QAAA,QAAAuE,yCAAA,KAAAA,yCAAA,GAnH8E7U,mEAAE,CAmHQ4U,4BAA4B,IAAAtE,CAAA,IAA5BsE,4BAA4B;MAAA;IAAA,IAAqD;EAAE;EAC7L;IAAS,IAAI,CAACI,IAAI,kBApH8EhV,+DAAE;MAAA6Q,IAAA,EAoHJ+D,4BAA4B;MAAAzQ,SAAA;MAAAmO,UAAA;MAAAC,QAAA,GApH1BvS,gEAAE,CAoHkM,CAAC2U,+BAA+B,CAAC,GApHrO3U,wEAAE;IAAA,EAoH2Q;EAAE;AACnX;AACA;EAAA,QAAAkU,SAAA,oBAAAA,SAAA,KAtHoGlU,+DAAE,CAsHX4U,4BAA4B,EAAc,CAAC;IAC1H/D,IAAI,EAAE9H,oDAAS;IACfqL,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;AAC9B,kFAAkF;MAC9DE,SAAS,EAAE,CAACI,+BAA+B,CAAC;MAC5CrC,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAM6C,mCAAmC,CAAC;EACtC;IAAS,IAAI,CAAC/E,IAAI,YAAAgF,4CAAA9E,CAAA;MAAA,YAAAA,CAAA,IAAwF6E,mCAAmC;IAAA,CAAkD;EAAE;EACjM;IAAS,IAAI,CAACE,IAAI,kBAtI8ErV,8DAAE;MAAA6Q,IAAA,EAsISsE;IAAmC,EAAqF;EAAE;EACrO;IAAS,IAAI,CAACI,IAAI,kBAvI8EvV,8DAAE,IAuI+C;EAAE;AACvJ;AACA;EAAA,QAAAkU,SAAA,oBAAAA,SAAA,KAzIoGlU,+DAAE,CAyIXmV,mCAAmC,EAAc,CAAC;IACjItE,IAAI,EAAE7H,mDAAQ;IACdoL,IAAI,EAAE,CAAC;MACC3U,OAAO,EAAE,CAACmV,4BAA4B,CAAC;MACvCa,OAAO,EAAE,CAACb,4BAA4B;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAM5V,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAACoR,IAAI,YAAAsF,0BAAApF,CAAA;MAAA,YAAAA,CAAA,IAAwFtR,iBAAiB;IAAA,CAAkD;EAAE;EAC/K;IAAS,IAAI,CAACqW,IAAI,kBAlJ8ErV,8DAAE;MAAA6Q,IAAA,EAkJS7R;IAAiB,EAAqF;EAAE;EACnN;IAAS,IAAI,CAACuW,IAAI,kBAnJ8EvV,8DAAE;MAAAP,OAAA,GAmJsC2K,WAAW,EAAEd,mEAAe,EAAEA,mEAAe;IAAA,EAAI;EAAE;AAC/L;AACA;EAAA,QAAA4K,SAAA,oBAAAA,SAAA,KArJoGlU,+DAAE,CAqJXhB,iBAAiB,EAAc,CAAC;IAC/G6R,IAAI,EAAE7H,mDAAQ;IACdoL,IAAI,EAAE,CAAC;MACC3U,OAAO,EAAE,CAAC2K,WAAW,EAAEd,mEAAe,CAAC;MACvCmM,OAAO,EAAE,CAACrL,WAAW,EAAEd,mEAAe;IAC1C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA", "sources": ["./src/app/features/auth/auth.module.ts", "./src/app/features/auth/auth.routes.ts", "./src/app/features/auth/components/login/login.component.ts", "./src/app/features/auth/components/login/login.component.html", "./src/app/features/auth/components/register/register.component.ts", "./src/app/features/auth/components/register/register.component.html", "./node_modules/@angular/material/fesm2022/checkbox.mjs"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDividerModule } from '@angular/material/divider';\n\nimport { LoginComponent } from './components/login/login.component';\nimport { RegisterComponent } from './components/register/register.component';\nimport { authRoutes } from './auth.routes';\n\n@NgModule({\n  declarations: [\n    LoginComponent,\n    RegisterComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule.forChild(authRoutes),\n    \n    // Material modules\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSelectModule,\n    MatCheckboxModule,\n    MatProgressSpinnerModule,\n    MatDividerModule\n  ]\n})\nexport class AuthModule { }\n", "import { Routes } from '@angular/router';\nimport { LoginComponent } from './components/login/login.component';\nimport { RegisterComponent } from './components/register/register.component';\n\nexport const authRoutes: Routes = [\n  {\n    path: '',\n    redirectTo: 'login',\n    pathMatch: 'full'\n  },\n  {\n    path: 'login',\n    component: LoginComponent\n  },\n  {\n    path: 'register',\n    component: RegisterComponent\n  }\n];\n", "import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { AuthService } from '../../../../core/services/auth.service';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  isLoading = false;\n  hidePassword = true;\n  returnUrl = '/dashboard';\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  ngOnInit(): void {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    \n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate([this.returnUrl]);\n    }\n  }\n\n  async onSubmit(): Promise<void> {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.isLoading = true;\n    const { email, password } = this.loginForm.value;\n\n    try {\n      await this.authService.login(email, password);\n      this.snackBar.open('Connexion réussie!', 'Fermer', { duration: 3000 });\n      this.router.navigate([this.returnUrl]);\n    } catch (error: any) {\n      console.error('Login error:', error);\n      this.snackBar.open(\n        this.getErrorMessage(error), \n        'Fermer', \n        { duration: 5000 }\n      );\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  private getErrorMessage(error: any): string {\n    if (error.code) {\n      switch (error.code) {\n        case 'auth/user-not-found':\n          return 'Aucun utilisateur trouvé avec cette adresse email.';\n        case 'auth/wrong-password':\n          return 'Mot de passe incorrect.';\n        case 'auth/invalid-email':\n          return 'Adresse email invalide.';\n        case 'auth/user-disabled':\n          return 'Ce compte a été désactivé.';\n        case 'auth/too-many-requests':\n          return 'Trop de tentatives. Veuillez réessayer plus tard.';\n        default:\n          return 'Erreur de connexion. Veuillez réessayer.';\n      }\n    }\n    return 'Erreur de connexion. Veuillez réessayer.';\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.loginForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) {\n        return `${fieldName === 'email' ? 'Email' : 'Mot de passe'} requis`;\n      }\n      if (field.errors['email']) {\n        return 'Format email invalide';\n      }\n      if (field.errors['minlength']) {\n        return 'Mot de passe trop court (min. 6 caractères)';\n      }\n    }\n    return '';\n  }\n\n  // Demo login methods\n  async loginAsUser(): Promise<void> {\n    this.loginForm.patchValue({\n      email: '<EMAIL>',\n      password: 'password123'\n    });\n    await this.onSubmit();\n  }\n\n  async loginAsValidator(): Promise<void> {\n    this.loginForm.patchValue({\n      email: '<EMAIL>',\n      password: 'password123'\n    });\n    await this.onSubmit();\n  }\n\n  async loginAsAdmin(): Promise<void> {\n    this.loginForm.patchValue({\n      email: '<EMAIL>',\n      password: 'password123'\n    });\n    await this.onSubmit();\n  }\n}\n", "<div class=\"login-container\">\n  <!-- Floating particles -->\n  <div class=\"floating-particles\">\n    <div class=\"particle\"></div>\n    <div class=\"particle\"></div>\n    <div class=\"particle\"></div>\n    <div class=\"particle\"></div>\n    <div class=\"particle\"></div>\n    <div class=\"particle\"></div>\n    <div class=\"particle\"></div>\n    <div class=\"particle\"></div>\n    <div class=\"particle\"></div>\n  </div>\n\n  <mat-card class=\"login-card\">\n    <mat-card-header>\n      <div class=\"login-header\">\n        <div class=\"logo\">🌟</div>\n        <h1>Modjo</h1>\n        <p>Connectez-vous à votre compte</p>\n      </div>\n    </mat-card-header>\n\n    <mat-card-content>\n      <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\">\n        <!-- Email field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Adresse email</mat-label>\n          <input matInput \n                 type=\"email\" \n                 formControlName=\"email\"\n                 placeholder=\"<EMAIL>\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error>{{ getFieldError('email') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Password field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Mot de passe</mat-label>\n          <input matInput \n                 [type]=\"hidePassword ? 'password' : 'text'\"\n                 formControlName=\"password\"\n                 placeholder=\"Votre mot de passe\">\n          <button mat-icon-button \n                  matSuffix \n                  type=\"button\"\n                  (click)=\"hidePassword = !hidePassword\"\n                  [attr.aria-label]=\"'Hide password'\"\n                  [attr.aria-pressed]=\"hidePassword\">\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError('password') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Submit button -->\n        <button mat-raised-button\n                color=\"primary\"\n                type=\"submit\"\n                class=\"full-width login-button\"\n                [disabled]=\"isLoading\">\n          <mat-spinner diameter=\"24\" color=\"accent\" *ngIf=\"isLoading\"></mat-spinner>\n          <span *ngIf=\"!isLoading\">\n            <mat-icon>login</mat-icon>\n            Se connecter\n          </span>\n        </button>\n\n        <!-- Register link -->\n        <div class=\"register-link\">\n          <p>Pas encore de compte? \n            <a routerLink=\"/auth/register\" class=\"link\">Créer un compte</a>\n          </p>\n        </div>\n      </form>\n\n      <!-- Demo login buttons -->\n      <div class=\"demo-section\">\n        <mat-divider></mat-divider>\n        <h3>✨ Connexion démo</h3>\n        <div class=\"demo-buttons\">\n          <button mat-stroked-button\n                  color=\"primary\"\n                  (click)=\"loginAsUser()\"\n                  class=\"demo-button\">\n            <mat-icon>person</mat-icon>\n            <div>\n              <strong>Utilisateur</strong>\n              <small>user&#64;modjo.tn</small>\n            </div>\n          </button>\n\n          <button mat-stroked-button\n                  color=\"accent\"\n                  (click)=\"loginAsValidator()\"\n                  class=\"demo-button\">\n            <mat-icon>verified</mat-icon>\n            <div>\n              <strong>Validateur</strong>\n              <small>validator&#64;modjo.tn</small>\n            </div>\n          </button>\n\n          <button mat-stroked-button\n                  color=\"warn\"\n                  (click)=\"loginAsAdmin()\"\n                  class=\"demo-button\">\n            <mat-icon>admin_panel_settings</mat-icon>\n            <div>\n              <strong>Admin</strong>\n              <small>admin&#64;modjo.tn</small>\n            </div>\n          </button>\n        </div>\n      </div>\n    </mat-card-content>\n  </mat-card>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { UserRole } from '../../../../core/models';\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.component.html',\n  styleUrls: ['./register.component.css']\n})\nexport class RegisterComponent implements OnInit {\n  registerForm: FormGroup;\n  isLoading = false;\n  hidePassword = true;\n  hideConfirmPassword = true;\n\n  cities = [\n    { value: 'Monastir', label: 'Monastir' },\n    { value: 'Sousse', label: 'Sousse' }\n  ];\n\n  roles = [\n    { value: UserRole.USER, label: 'Utilisateur', description: 'Participer aux activités communautaires' },\n    { value: UserRole.PROVIDER, label: 'Partenaire', description: 'Offrir des récompenses et services' },\n    { value: UserRole.VALIDATOR, label: 'Validateur', description: 'Valider les actions communautaires' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) {\n    this.registerForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', [Validators.pattern(/^[0-9]{8}$/)]],\n      city: ['', Validators.required],\n      role: [UserRole.USER, Validators.required],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', Validators.required],\n      acceptTerms: [false, Validators.requiredTrue]\n    }, { validators: this.passwordMatchValidator });\n  }\n\n  ngOnInit(): void {\n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n\n  passwordMatchValidator(form: FormGroup) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    \n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({ passwordMismatch: true });\n    } else if (confirmPassword?.errors?.['passwordMismatch']) {\n      delete confirmPassword.errors['passwordMismatch'];\n      if (Object.keys(confirmPassword.errors).length === 0) {\n        confirmPassword.setErrors(null);\n      }\n    }\n    return null;\n  }\n\n  async onSubmit(): Promise<void> {\n    if (this.registerForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.isLoading = true;\n    const formValue = this.registerForm.value;\n\n    try {\n      await this.authService.register({\n        email: formValue.email,\n        password: formValue.password,\n        name: formValue.name,\n        phone: formValue.phone,\n        city: formValue.city,\n        role: formValue.role\n      });\n\n      this.snackBar.open('Compte créé avec succès!', 'Fermer', { duration: 3000 });\n      this.router.navigate(['/dashboard']);\n    } catch (error: any) {\n      console.error('Registration error:', error);\n      this.snackBar.open(\n        this.getErrorMessage(error), \n        'Fermer', \n        { duration: 5000 }\n      );\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  private getErrorMessage(error: any): string {\n    if (error.code) {\n      switch (error.code) {\n        case 'auth/email-already-in-use':\n          return 'Cette adresse email est déjà utilisée.';\n        case 'auth/invalid-email':\n          return 'Adresse email invalide.';\n        case 'auth/weak-password':\n          return 'Le mot de passe est trop faible.';\n        case 'auth/operation-not-allowed':\n          return 'Création de compte désactivée.';\n        default:\n          return 'Erreur lors de la création du compte.';\n      }\n    }\n    return 'Erreur lors de la création du compte.';\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.registerForm.controls).forEach(key => {\n      const control = this.registerForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.registerForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) {\n        return this.getRequiredMessage(fieldName);\n      }\n      if (field.errors['email']) {\n        return 'Format email invalide';\n      }\n      if (field.errors['minlength']) {\n        return fieldName === 'password' ? \n          'Mot de passe trop court (min. 6 caractères)' : \n          'Nom trop court (min. 2 caractères)';\n      }\n      if (field.errors['pattern']) {\n        return 'Numéro de téléphone invalide (8 chiffres)';\n      }\n      if (field.errors['passwordMismatch']) {\n        return 'Les mots de passe ne correspondent pas';\n      }\n      if (field.errors['requiredTrue']) {\n        return 'Vous devez accepter les conditions';\n      }\n    }\n    return '';\n  }\n\n  private getRequiredMessage(fieldName: string): string {\n    const messages: { [key: string]: string } = {\n      name: 'Nom requis',\n      email: 'Email requis',\n      city: 'Ville requise',\n      role: 'Rôle requis',\n      password: 'Mot de passe requis',\n      confirmPassword: 'Confirmation requise',\n      acceptTerms: 'Acceptation requise'\n    };\n    return messages[fieldName] || 'Champ requis';\n  }\n}\n", "<div class=\"register-container\">\n  <mat-card class=\"register-card\">\n    <mat-card-header>\n      <div class=\"register-header\">\n        <img src=\"assets/logo.png\" alt=\"Modjo\" class=\"logo\">\n        <h1><PERSON><PERSON><PERSON><PERSON>jo</h1>\n        <p>C<PERSON>ez votre compte pour commencer</p>\n      </div>\n    </mat-card-header>\n\n    <mat-card-content>\n      <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\" class=\"register-form\">\n        <!-- Name field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Nom complet</mat-label>\n          <input matInput \n                 type=\"text\" \n                 formControlName=\"name\"\n                 placeholder=\"Votre nom complet\">\n          <mat-icon matSuffix>person</mat-icon>\n          <mat-error>{{ getFieldError('name') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Email field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Adresse email</mat-label>\n          <input matInput \n                 type=\"email\" \n                 formControlName=\"email\"\n                 placeholder=\"<EMAIL>\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error>{{ getFieldError('email') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Phone field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Téléphone (optionnel)</mat-label>\n          <input matInput \n                 type=\"tel\" \n                 formControlName=\"phone\"\n                 placeholder=\"12345678\">\n          <mat-icon matSuffix>phone</mat-icon>\n          <mat-error>{{ getFieldError('phone') }}</mat-error>\n        </mat-form-field>\n\n        <!-- City field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Ville</mat-label>\n          <mat-select formControlName=\"city\">\n            <mat-option *ngFor=\"let city of cities\" [value]=\"city.value\">\n              {{ city.label }}\n            </mat-option>\n          </mat-select>\n          <mat-error>{{ getFieldError('city') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Role field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Type de compte</mat-label>\n          <mat-select formControlName=\"role\">\n            <mat-option *ngFor=\"let role of roles\" [value]=\"role.value\">\n              <div class=\"role-option\">\n                <span class=\"role-label\">{{ role.label }}</span>\n                <span class=\"role-description\">{{ role.description }}</span>\n              </div>\n            </mat-option>\n          </mat-select>\n          <mat-error>{{ getFieldError('role') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Password field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Mot de passe</mat-label>\n          <input matInput \n                 [type]=\"hidePassword ? 'password' : 'text'\"\n                 formControlName=\"password\"\n                 placeholder=\"Votre mot de passe\">\n          <button mat-icon-button \n                  matSuffix \n                  type=\"button\"\n                  (click)=\"hidePassword = !hidePassword\">\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError('password') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Confirm Password field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Confirmer le mot de passe</mat-label>\n          <input matInput \n                 [type]=\"hideConfirmPassword ? 'password' : 'text'\"\n                 formControlName=\"confirmPassword\"\n                 placeholder=\"Confirmez votre mot de passe\">\n          <button mat-icon-button \n                  matSuffix \n                  type=\"button\"\n                  (click)=\"hideConfirmPassword = !hideConfirmPassword\">\n            <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError('confirmPassword') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Terms checkbox -->\n        <div class=\"terms-section\">\n          <mat-checkbox formControlName=\"acceptTerms\" class=\"terms-checkbox\">\n            J'accepte les <a href=\"#\" class=\"link\">conditions d'utilisation</a> \n            et la <a href=\"#\" class=\"link\">politique de confidentialité</a>\n          </mat-checkbox>\n          <div class=\"error-message\" *ngIf=\"getFieldError('acceptTerms')\">\n            {{ getFieldError('acceptTerms') }}\n          </div>\n        </div>\n\n        <!-- Submit button -->\n        <button mat-raised-button \n                color=\"primary\" \n                type=\"submit\"\n                class=\"full-width register-button\"\n                [disabled]=\"isLoading\">\n          <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n          <span *ngIf=\"!isLoading\">Créer mon compte</span>\n        </button>\n\n        <!-- Login link -->\n        <div class=\"login-link\">\n          <p>Déjà un compte? \n            <a routerLink=\"/auth/login\" class=\"link\">Se connecter</a>\n          </p>\n        </div>\n      </form>\n    </mat-card-content>\n  </mat-card>\n</div>\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, ANIMATION_MODULE_TYPE, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Optional, Inject, Input, Output, ViewChild, Directive, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport { MatRipple, _MatInternalFormField, MatCommonModule } from '@angular/material/core';\n\n/** Injection token to be used to override the default options for `mat-checkbox`. */\nconst MAT_CHECKBOX_DEFAULT_OPTIONS = new InjectionToken('mat-checkbox-default-options', {\n    providedIn: 'root',\n    factory: MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY,\n});\n/** @docs-private */\nfunction MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        color: 'accent',\n        clickAction: 'check-indeterminate',\n    };\n}\n\n/**\n * Represents the different states that require custom transitions between them.\n * @docs-private\n */\nvar TransitionCheckState;\n(function (TransitionCheckState) {\n    /** The initial state of the component before any user interaction. */\n    TransitionCheckState[TransitionCheckState[\"Init\"] = 0] = \"Init\";\n    /** The state representing the component when it's becoming checked. */\n    TransitionCheckState[TransitionCheckState[\"Checked\"] = 1] = \"Checked\";\n    /** The state representing the component when it's becoming unchecked. */\n    TransitionCheckState[TransitionCheckState[\"Unchecked\"] = 2] = \"Unchecked\";\n    /** The state representing the component when it's becoming indeterminate. */\n    TransitionCheckState[TransitionCheckState[\"Indeterminate\"] = 3] = \"Indeterminate\";\n})(TransitionCheckState || (TransitionCheckState = {}));\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatCheckbox),\n    multi: true,\n};\n/** Change event object emitted by checkbox. */\nclass MatCheckboxChange {\n}\n// Increasing integer for generating unique ids for checkbox components.\nlet nextUniqueId = 0;\n// Default checkbox configuration.\nconst defaults = MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY();\nclass MatCheckbox {\n    /** Focuses the checkbox. */\n    focus() {\n        this._inputElement.nativeElement.focus();\n    }\n    /** Creates the change event that will be emitted by the checkbox. */\n    _createChangeEvent(isChecked) {\n        const event = new MatCheckboxChange();\n        event.source = this;\n        event.checked = isChecked;\n        return event;\n    }\n    /** Gets the element on which to add the animation CSS classes. */\n    _getAnimationTargetElement() {\n        return this._inputElement?.nativeElement;\n    }\n    /** Returns the unique id for the visual hidden input. */\n    get inputId() {\n        return `${this.id || this._uniqueId}-input`;\n    }\n    constructor(_elementRef, _changeDetectorRef, _ngZone, tabIndex, _animationMode, _options) {\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._ngZone = _ngZone;\n        this._animationMode = _animationMode;\n        this._options = _options;\n        /** CSS classes to add when transitioning between the different checkbox states. */\n        this._animationClasses = {\n            uncheckedToChecked: 'mdc-checkbox--anim-unchecked-checked',\n            uncheckedToIndeterminate: 'mdc-checkbox--anim-unchecked-indeterminate',\n            checkedToUnchecked: 'mdc-checkbox--anim-checked-unchecked',\n            checkedToIndeterminate: 'mdc-checkbox--anim-checked-indeterminate',\n            indeterminateToChecked: 'mdc-checkbox--anim-indeterminate-checked',\n            indeterminateToUnchecked: 'mdc-checkbox--anim-indeterminate-unchecked',\n        };\n        /**\n         * Attached to the aria-label attribute of the host element. In most cases, aria-labelledby will\n         * take precedence so this may be omitted.\n         */\n        this.ariaLabel = '';\n        /**\n         * Users can specify the `aria-labelledby` attribute which will be forwarded to the input element\n         */\n        this.ariaLabelledby = null;\n        /** Whether the label should appear after or before the checkbox. Defaults to 'after' */\n        this.labelPosition = 'after';\n        /** Name value will be applied to the input element if present */\n        this.name = null;\n        /** Event emitted when the checkbox's `checked` value changes. */\n        this.change = new EventEmitter();\n        /** Event emitted when the checkbox's `indeterminate` value changes. */\n        this.indeterminateChange = new EventEmitter();\n        /**\n         * Called when the checkbox is blurred. Needed to properly implement ControlValueAccessor.\n         * @docs-private\n         */\n        this._onTouched = () => { };\n        this._currentAnimationClass = '';\n        this._currentCheckState = TransitionCheckState.Init;\n        this._controlValueAccessorChangeFn = () => { };\n        this._validatorChangeFn = () => { };\n        this._checked = false;\n        this._disabled = false;\n        this._indeterminate = false;\n        this._options = this._options || defaults;\n        this.color = this._options.color || defaults.color;\n        this.tabIndex = parseInt(tabIndex) || 0;\n        this.id = this._uniqueId = `mat-mdc-checkbox-${++nextUniqueId}`;\n    }\n    ngOnChanges(changes) {\n        if (changes['required']) {\n            this._validatorChangeFn();\n        }\n    }\n    ngAfterViewInit() {\n        this._syncIndeterminate(this._indeterminate);\n    }\n    /** Whether the checkbox is checked. */\n    get checked() {\n        return this._checked;\n    }\n    set checked(value) {\n        if (value != this.checked) {\n            this._checked = value;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** Whether the checkbox is disabled. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        if (value !== this.disabled) {\n            this._disabled = value;\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /**\n     * Whether the checkbox is indeterminate. This is also known as \"mixed\" mode and can be used to\n     * represent a checkbox with three states, e.g. a checkbox that represents a nested list of\n     * checkable items. Note that whenever checkbox is manually clicked, indeterminate is immediately\n     * set to false.\n     */\n    get indeterminate() {\n        return this._indeterminate;\n    }\n    set indeterminate(value) {\n        const changed = value != this._indeterminate;\n        this._indeterminate = value;\n        if (changed) {\n            if (this._indeterminate) {\n                this._transitionCheckState(TransitionCheckState.Indeterminate);\n            }\n            else {\n                this._transitionCheckState(this.checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n            }\n            this.indeterminateChange.emit(this._indeterminate);\n        }\n        this._syncIndeterminate(this._indeterminate);\n    }\n    _isRippleDisabled() {\n        return this.disableRipple || this.disabled;\n    }\n    /** Method being called whenever the label text changes. */\n    _onLabelTextChange() {\n        // Since the event of the `cdkObserveContent` directive runs outside of the zone, the checkbox\n        // component will be only marked for check, but no actual change detection runs automatically.\n        // Instead of going back into the zone in order to trigger a change detection which causes\n        // *all* components to be checked (if explicitly marked or not using OnPush), we only trigger\n        // an explicit change detection for the checkbox view and its children.\n        this._changeDetectorRef.detectChanges();\n    }\n    // Implemented as part of ControlValueAccessor.\n    writeValue(value) {\n        this.checked = !!value;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnChange(fn) {\n        this._controlValueAccessorChangeFn = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    // Implemented as part of ControlValueAccessor.\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n    }\n    // Implemented as a part of Validator.\n    validate(control) {\n        return this.required && control.value !== true ? { 'required': true } : null;\n    }\n    // Implemented as a part of Validator.\n    registerOnValidatorChange(fn) {\n        this._validatorChangeFn = fn;\n    }\n    _transitionCheckState(newState) {\n        let oldState = this._currentCheckState;\n        let element = this._getAnimationTargetElement();\n        if (oldState === newState || !element) {\n            return;\n        }\n        if (this._currentAnimationClass) {\n            element.classList.remove(this._currentAnimationClass);\n        }\n        this._currentAnimationClass = this._getAnimationClassForCheckStateTransition(oldState, newState);\n        this._currentCheckState = newState;\n        if (this._currentAnimationClass.length > 0) {\n            element.classList.add(this._currentAnimationClass);\n            // Remove the animation class to avoid animation when the checkbox is moved between containers\n            const animationClass = this._currentAnimationClass;\n            this._ngZone.runOutsideAngular(() => {\n                setTimeout(() => {\n                    element.classList.remove(animationClass);\n                }, 1000);\n            });\n        }\n    }\n    _emitChangeEvent() {\n        this._controlValueAccessorChangeFn(this.checked);\n        this.change.emit(this._createChangeEvent(this.checked));\n        // Assigning the value again here is redundant, but we have to do it in case it was\n        // changed inside the `change` listener which will cause the input to be out of sync.\n        if (this._inputElement) {\n            this._inputElement.nativeElement.checked = this.checked;\n        }\n    }\n    /** Toggles the `checked` state of the checkbox. */\n    toggle() {\n        this.checked = !this.checked;\n        this._controlValueAccessorChangeFn(this.checked);\n    }\n    _handleInputClick() {\n        const clickAction = this._options?.clickAction;\n        // If resetIndeterminate is false, and the current state is indeterminate, do nothing on click\n        if (!this.disabled && clickAction !== 'noop') {\n            // When user manually click on the checkbox, `indeterminate` is set to false.\n            if (this.indeterminate && clickAction !== 'check') {\n                Promise.resolve().then(() => {\n                    this._indeterminate = false;\n                    this.indeterminateChange.emit(this._indeterminate);\n                });\n            }\n            this._checked = !this._checked;\n            this._transitionCheckState(this._checked ? TransitionCheckState.Checked : TransitionCheckState.Unchecked);\n            // Emit our custom change event if the native input emitted one.\n            // It is important to only emit it, if the native input triggered one, because\n            // we don't want to trigger a change event, when the `checked` variable changes for example.\n            this._emitChangeEvent();\n        }\n        else if (!this.disabled && clickAction === 'noop') {\n            // Reset native input when clicked with noop. The native checkbox becomes checked after\n            // click, reset it to be align with `checked` value of `mat-checkbox`.\n            this._inputElement.nativeElement.checked = this.checked;\n            this._inputElement.nativeElement.indeterminate = this.indeterminate;\n        }\n    }\n    _onInteractionEvent(event) {\n        // We always have to stop propagation on the change event.\n        // Otherwise the change event, from the input element, will bubble up and\n        // emit its event object to the `change` output.\n        event.stopPropagation();\n    }\n    _onBlur() {\n        // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n        // Angular does not expect events to be raised during change detection, so any state change\n        // (such as a form control's 'ng-touched') will cause a changed-after-checked error.\n        // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n        // telling the form control it has been touched until the next tick.\n        Promise.resolve().then(() => {\n            this._onTouched();\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    _getAnimationClassForCheckStateTransition(oldState, newState) {\n        // Don't transition if animations are disabled.\n        if (this._animationMode === 'NoopAnimations') {\n            return '';\n        }\n        switch (oldState) {\n            case TransitionCheckState.Init:\n                // Handle edge case where user interacts with checkbox that does not have [(ngModel)] or\n                // [checked] bound to it.\n                if (newState === TransitionCheckState.Checked) {\n                    return this._animationClasses.uncheckedToChecked;\n                }\n                else if (newState == TransitionCheckState.Indeterminate) {\n                    return this._checked\n                        ? this._animationClasses.checkedToIndeterminate\n                        : this._animationClasses.uncheckedToIndeterminate;\n                }\n                break;\n            case TransitionCheckState.Unchecked:\n                return newState === TransitionCheckState.Checked\n                    ? this._animationClasses.uncheckedToChecked\n                    : this._animationClasses.uncheckedToIndeterminate;\n            case TransitionCheckState.Checked:\n                return newState === TransitionCheckState.Unchecked\n                    ? this._animationClasses.checkedToUnchecked\n                    : this._animationClasses.checkedToIndeterminate;\n            case TransitionCheckState.Indeterminate:\n                return newState === TransitionCheckState.Checked\n                    ? this._animationClasses.indeterminateToChecked\n                    : this._animationClasses.indeterminateToUnchecked;\n        }\n        return '';\n    }\n    /**\n     * Syncs the indeterminate value with the checkbox DOM node.\n     *\n     * We sync `indeterminate` directly on the DOM node, because in Ivy the check for whether a\n     * property is supported on an element boils down to `if (propName in element)`. Domino's\n     * HTMLInputElement doesn't have an `indeterminate` property so Ivy will warn during\n     * server-side rendering.\n     */\n    _syncIndeterminate(value) {\n        const nativeCheckbox = this._inputElement;\n        if (nativeCheckbox) {\n            nativeCheckbox.nativeElement.indeterminate = value;\n        }\n    }\n    _onInputClick() {\n        this._handleInputClick();\n    }\n    _onTouchTargetClick() {\n        this._handleInputClick();\n        if (!this.disabled) {\n            // Normally the input should be focused already, but if the click\n            // comes from the touch target, then we might have to focus it ourselves.\n            this._inputElement.nativeElement.focus();\n        }\n    }\n    /**\n     *  Prevent click events that come from the `<label/>` element from bubbling. This prevents the\n     *  click handler on the host from triggering twice when clicking on the `<label/>` element. After\n     *  the click event on the `<label/>` propagates, the browsers dispatches click on the associated\n     *  `<input/>`. By preventing clicks on the label by bubbling, we ensure only one click event\n     *  bubbles when the label is clicked.\n     */\n    _preventBubblingFromLabel(event) {\n        if (!!event.target && this._labelElement.nativeElement.contains(event.target)) {\n            event.stopPropagation();\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckbox, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: 'tabindex', attribute: true }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_CHECKBOX_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatCheckbox, isStandalone: true, selector: \"mat-checkbox\", inputs: { ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], id: \"id\", required: [\"required\", \"required\", booleanAttribute], labelPosition: \"labelPosition\", name: \"name\", value: \"value\", disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? undefined : numberAttribute(value))], color: \"color\", checked: [\"checked\", \"checked\", booleanAttribute], disabled: [\"disabled\", \"disabled\", booleanAttribute], indeterminate: [\"indeterminate\", \"indeterminate\", booleanAttribute] }, outputs: { change: \"change\", indeterminateChange: \"indeterminateChange\" }, host: { properties: { \"attr.tabindex\": \"null\", \"attr.aria-label\": \"null\", \"attr.aria-labelledby\": \"null\", \"class._mat-animation-noopable\": \"_animationMode === 'NoopAnimations'\", \"class.mdc-checkbox--disabled\": \"disabled\", \"id\": \"id\", \"class.mat-mdc-checkbox-disabled\": \"disabled\", \"class.mat-mdc-checkbox-checked\": \"checked\", \"class\": \"color ? \\\"mat-\\\" + color : \\\"mat-accent\\\"\" }, classAttribute: \"mat-mdc-checkbox\" }, providers: [\n            MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR,\n            {\n                provide: NG_VALIDATORS,\n                useExisting: MatCheckbox,\n                multi: true,\n            },\n        ], viewQueries: [{ propertyName: \"_inputElement\", first: true, predicate: [\"input\"], descendants: true }, { propertyName: \"_labelElement\", first: true, predicate: [\"label\"], descendants: true }, { propertyName: \"ripple\", first: true, predicate: MatRipple, descendants: true }], exportAs: [\"matCheckbox\"], usesOnChanges: true, ngImport: i0, template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\"\\n         #label\\n         [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-touch-target-wrapper{display:inline}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:mdc-animation-deceleration-curve-timing-function;transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom}.mdc-checkbox[hidden]{display:none}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{border-color:CanvasText}}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{border-color:CanvasText}}@media all and (-ms-high-contrast: none){.mdc-checkbox .mdc-checkbox__focus-ring{display:none}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled{cursor:default;pointer-events:none}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--upgraded .mdc-checkbox__checkmark{opacity:1}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear 0s;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear 0s;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background{transition:border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit}.mdc-checkbox__native-control:disabled{cursor:default;pointer-events:none}.mdc-checkbox--touch{margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox--touch .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark{transition:opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__mixedmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__mixedmark{transition:none}.mdc-checkbox{padding:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:transparent;background-color:var(--mdc-checkbox-disabled-selected-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}@keyframes mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}}@keyframes mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}}.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:hover.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox .mdc-checkbox__background{top:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:focus:not(:checked):not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color);background-color:var(--mdc-checkbox-selected-focus-icon-color)}.mdc-checkbox:hover .mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox:hover .mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox .mdc-checkbox__background{-webkit-print-color-adjust:exact;color-adjust:exact}.mat-mdc-checkbox._mat-animation-noopable *,.mat-mdc-checkbox._mat-animation-noopable *::before{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color)}.mat-mdc-checkbox label:empty{display:none}.cdk-high-contrast-active .mat-mdc-checkbox.mat-mdc-checkbox-disabled{opacity:.5}.cdk-high-contrast-active .mat-mdc-checkbox .mdc-checkbox__checkmark{--mdc-checkbox-selected-checkmark-color: CanvasText;--mdc-checkbox-disabled-selected-checkmark-color: CanvasText}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display)}.mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-mdc-focus-indicator::before{content:\\\"\\\"}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"component\", type: _MatInternalFormField, selector: \"div[mat-internal-form-field]\", inputs: [\"labelPosition\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckbox, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-checkbox', host: {\n                        'class': 'mat-mdc-checkbox',\n                        '[attr.tabindex]': 'null',\n                        '[attr.aria-label]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[class._mat-animation-noopable]': `_animationMode === 'NoopAnimations'`,\n                        '[class.mdc-checkbox--disabled]': 'disabled',\n                        '[id]': 'id',\n                        // Add classes that users can use to more easily target disabled or checked checkboxes.\n                        '[class.mat-mdc-checkbox-disabled]': 'disabled',\n                        '[class.mat-mdc-checkbox-checked]': 'checked',\n                        '[class]': 'color ? \"mat-\" + color : \"mat-accent\"',\n                    }, providers: [\n                        MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR,\n                        {\n                            provide: NG_VALIDATORS,\n                            useExisting: MatCheckbox,\n                            multi: true,\n                        },\n                    ], exportAs: 'matCheckbox', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, standalone: true, imports: [MatRipple, _MatInternalFormField], template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\" (click)=\\\"_preventBubblingFromLabel($event)\\\">\\n  <div #checkbox class=\\\"mdc-checkbox\\\">\\n    <!-- Render this element first so the input is on top. -->\\n    <div class=\\\"mat-mdc-checkbox-touch-target\\\" (click)=\\\"_onTouchTargetClick()\\\"></div>\\n    <input #input\\n           type=\\\"checkbox\\\"\\n           class=\\\"mdc-checkbox__native-control\\\"\\n           [class.mdc-checkbox--selected]=\\\"checked\\\"\\n           [attr.aria-label]=\\\"ariaLabel || null\\\"\\n           [attr.aria-labelledby]=\\\"ariaLabelledby\\\"\\n           [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n           [attr.aria-checked]=\\\"indeterminate ? 'mixed' : null\\\"\\n           [attr.name]=\\\"name\\\"\\n           [attr.value]=\\\"value\\\"\\n           [checked]=\\\"checked\\\"\\n           [indeterminate]=\\\"indeterminate\\\"\\n           [disabled]=\\\"disabled\\\"\\n           [id]=\\\"inputId\\\"\\n           [required]=\\\"required\\\"\\n           [tabIndex]=\\\"disabled ? -1 : tabIndex\\\"\\n           (blur)=\\\"_onBlur()\\\"\\n           (click)=\\\"_onInputClick()\\\"\\n           (change)=\\\"_onInteractionEvent($event)\\\"/>\\n    <div class=\\\"mdc-checkbox__ripple\\\"></div>\\n    <div class=\\\"mdc-checkbox__background\\\">\\n      <svg class=\\\"mdc-checkbox__checkmark\\\"\\n           focusable=\\\"false\\\"\\n           viewBox=\\\"0 0 24 24\\\"\\n           aria-hidden=\\\"true\\\">\\n        <path class=\\\"mdc-checkbox__checkmark-path\\\"\\n              fill=\\\"none\\\"\\n              d=\\\"M1.73,12.91 8.1,19.28 22.79,4.59\\\"/>\\n      </svg>\\n      <div class=\\\"mdc-checkbox__mixedmark\\\"></div>\\n    </div>\\n    <div class=\\\"mat-mdc-checkbox-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n      [matRippleTrigger]=\\\"checkbox\\\"\\n      [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n      [matRippleCentered]=\\\"true\\\"></div>\\n  </div>\\n  <!--\\n    Avoid putting a click handler on the <label/> to fix duplicate navigation stop on Talk Back\\n    (#14385). Putting a click handler on the <label/> caused this bug because the browser produced\\n    an unnecessary accessibility tree node.\\n  -->\\n  <label class=\\\"mdc-label\\\"\\n         #label\\n         [for]=\\\"inputId\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-touch-target-wrapper{display:inline}@keyframes mdc-checkbox-unchecked-checked-checkmark-path{0%,50%{stroke-dashoffset:29.7833385}50%{animation-timing-function:cubic-bezier(0, 0, 0.2, 1)}100%{stroke-dashoffset:0}}@keyframes mdc-checkbox-unchecked-indeterminate-mixedmark{0%,68.2%{transform:scaleX(0)}68.2%{animation-timing-function:cubic-bezier(0, 0, 0, 1)}100%{transform:scaleX(1)}}@keyframes mdc-checkbox-checked-unchecked-checkmark-path{from{animation-timing-function:cubic-bezier(0.4, 0, 1, 1);opacity:1;stroke-dashoffset:0}to{opacity:0;stroke-dashoffset:-29.7833385}}@keyframes mdc-checkbox-checked-indeterminate-checkmark{from{animation-timing-function:cubic-bezier(0, 0, 0.2, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(45deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-checked-checkmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(45deg);opacity:0}to{transform:rotate(360deg);opacity:1}}@keyframes mdc-checkbox-checked-indeterminate-mixedmark{from{animation-timing-function:mdc-animation-deceleration-curve-timing-function;transform:rotate(-45deg);opacity:0}to{transform:rotate(0deg);opacity:1}}@keyframes mdc-checkbox-indeterminate-checked-mixedmark{from{animation-timing-function:cubic-bezier(0.14, 0, 0, 1);transform:rotate(0deg);opacity:1}to{transform:rotate(315deg);opacity:0}}@keyframes mdc-checkbox-indeterminate-unchecked-mixedmark{0%{animation-timing-function:linear;transform:scaleX(1);opacity:1}32.8%,100%{transform:scaleX(0);opacity:0}}.mdc-checkbox{display:inline-block;position:relative;flex:0 0 18px;box-sizing:content-box;width:18px;height:18px;line-height:0;white-space:nowrap;cursor:pointer;vertical-align:bottom}.mdc-checkbox[hidden]{display:none}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{pointer-events:none;border:2px solid rgba(0,0,0,0);border-radius:6px;box-sizing:content-box;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:100%;width:100%}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring{border-color:CanvasText}}.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{content:\\\"\\\";border:2px solid rgba(0,0,0,0);border-radius:8px;display:block;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%);height:calc(100% + 4px);width:calc(100% + 4px)}@media screen and (forced-colors: active){.mdc-checkbox.mdc-ripple-upgraded--background-focused .mdc-checkbox__focus-ring::after,.mdc-checkbox:not(.mdc-ripple-upgraded):focus .mdc-checkbox__focus-ring::after{border-color:CanvasText}}@media all and (-ms-high-contrast: none){.mdc-checkbox .mdc-checkbox__focus-ring{display:none}}@media screen and (forced-colors: active),(-ms-high-contrast: active){.mdc-checkbox__mixedmark{margin:0 1px}}.mdc-checkbox--disabled{cursor:default;pointer-events:none}.mdc-checkbox__background{display:inline-flex;position:absolute;align-items:center;justify-content:center;box-sizing:border-box;width:18px;height:18px;border:2px solid currentColor;border-radius:2px;background-color:rgba(0,0,0,0);pointer-events:none;will-change:background-color,border-color;transition:background-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),border-color 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__checkmark{position:absolute;top:0;right:0;bottom:0;left:0;width:100%;opacity:0;transition:opacity 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--upgraded .mdc-checkbox__checkmark{opacity:1}.mdc-checkbox__checkmark-path{transition:stroke-dashoffset 180ms 0ms cubic-bezier(0.4, 0, 0.6, 1);stroke:currentColor;stroke-width:3.12px;stroke-dashoffset:29.7833385;stroke-dasharray:29.7833385}.mdc-checkbox__mixedmark{width:100%;height:0;transform:scaleX(0) rotate(0deg);border-width:1px;border-style:solid;opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__background,.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__background,.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__background,.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__background{animation-duration:180ms;animation-timing-function:linear}.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-unchecked-checked-checkmark-path 180ms linear 0s;transition:none}.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-unchecked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__checkmark-path{animation:mdc-checkbox-checked-unchecked-checkmark-path 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__checkmark{animation:mdc-checkbox-checked-indeterminate-checkmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-checked-indeterminate .mdc-checkbox__mixedmark{animation:mdc-checkbox-checked-indeterminate-mixedmark 90ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__checkmark{animation:mdc-checkbox-indeterminate-checked-checkmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-checked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-checked-mixedmark 500ms linear 0s;transition:none}.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__mixedmark{animation:mdc-checkbox-indeterminate-unchecked-mixedmark 300ms linear 0s;transition:none}.mdc-checkbox__native-control:checked~.mdc-checkbox__background,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background{transition:border-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1),background-color 90ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark-path,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark-path{stroke-dashoffset:0}.mdc-checkbox__native-control{position:absolute;margin:0;padding:0;opacity:0;cursor:inherit}.mdc-checkbox__native-control:disabled{cursor:default;pointer-events:none}.mdc-checkbox--touch{margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox--touch .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__checkmark{transition:opacity 180ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 180ms 0ms cubic-bezier(0, 0, 0.2, 1);opacity:1}.mdc-checkbox__native-control:checked~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(-45deg)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__checkmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__checkmark{transform:rotate(45deg);opacity:0;transition:opacity 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1),transform 90ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-checkbox__native-control:indeterminate~.mdc-checkbox__background .mdc-checkbox__mixedmark,.mdc-checkbox__native-control[data-indeterminate=true]~.mdc-checkbox__background .mdc-checkbox__mixedmark{transform:scaleX(1) rotate(0deg);opacity:1}.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__checkmark-path,.mdc-checkbox.mdc-checkbox--upgraded .mdc-checkbox__mixedmark{transition:none}.mdc-checkbox{padding:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);margin:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2)}.mdc-checkbox .mdc-checkbox__native-control[disabled]:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-disabled-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control[disabled]:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[disabled]:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true][disabled]~.mdc-checkbox__background{border-color:transparent;background-color:var(--mdc-checkbox-disabled-selected-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__checkmark{color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:disabled~.mdc-checkbox__background .mdc-checkbox__mixedmark{border-color:var(--mdc-checkbox-disabled-selected-checkmark-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}.mdc-checkbox .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-icon-color);background-color:var(--mdc-checkbox-selected-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-icon-color);background-color:transparent}}.mdc-checkbox.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:hover .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}@keyframes mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}}@keyframes mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-hover-icon-color);background-color:var(--mdc-checkbox-selected-hover-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-hover-icon-color);background-color:transparent}}.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:hover.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:hover.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-FF212121FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:not(:checked):not(:indeterminate):not([data-indeterminate=true])~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:checked~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control:enabled:indeterminate~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active .mdc-checkbox__native-control[data-indeterminate=true]:enabled~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}@keyframes mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336{0%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}50%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}}@keyframes mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336{0%,80%{border-color:var(--mdc-checkbox-selected-pressed-icon-color);background-color:var(--mdc-checkbox-selected-pressed-icon-color)}100%{border-color:var(--mdc-checkbox-unselected-pressed-icon-color);background-color:transparent}}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-checked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-unchecked-indeterminate .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-in-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-checked-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background,.mdc-checkbox:not(:disabled):active.mdc-checkbox--anim-indeterminate-unchecked .mdc-checkbox__native-control:enabled~.mdc-checkbox__background{animation-name:mdc-checkbox-fade-out-background-8A000000FFF4433600000000FFF44336}.mdc-checkbox .mdc-checkbox__background{top:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - 18px) / 2)}.mdc-checkbox .mdc-checkbox__native-control{top:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);right:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);left:calc((var(--mdc-checkbox-state-layer-size) - var(--mdc-checkbox-state-layer-size)) / 2);width:var(--mdc-checkbox-state-layer-size);height:var(--mdc-checkbox-state-layer-size)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:focus:not(:checked):not(:indeterminate)~.mdc-checkbox__background{border-color:var(--mdc-checkbox-unselected-focus-icon-color)}.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:checked~.mdc-checkbox__background,.mdc-checkbox .mdc-checkbox__native-control:enabled:focus:indeterminate~.mdc-checkbox__background{border-color:var(--mdc-checkbox-selected-focus-icon-color);background-color:var(--mdc-checkbox-selected-focus-icon-color)}.mdc-checkbox:hover .mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox:hover .mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-unselected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-unselected-pressed-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-hover-state-layer-opacity);background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox:hover .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-hover-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-focus-state-layer-opacity);background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox .mdc-checkbox__native-control:focus:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-focus-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mdc-checkbox__ripple{opacity:var(--mdc-checkbox-selected-pressed-state-layer-opacity);background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mdc-checkbox:active .mdc-checkbox__native-control:checked~.mat-mdc-checkbox-ripple .mat-ripple-element{background-color:var(--mdc-checkbox-selected-pressed-state-layer-color)}.mat-mdc-checkbox{display:inline-block;position:relative;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mat-mdc-checkbox .mdc-checkbox__background{-webkit-print-color-adjust:exact;color-adjust:exact}.mat-mdc-checkbox._mat-animation-noopable *,.mat-mdc-checkbox._mat-animation-noopable *::before{transition:none !important;animation:none !important}.mat-mdc-checkbox label{cursor:pointer}.mat-mdc-checkbox.mat-mdc-checkbox-disabled label{cursor:default;color:var(--mat-checkbox-disabled-label-color)}.mat-mdc-checkbox label:empty{display:none}.cdk-high-contrast-active .mat-mdc-checkbox.mat-mdc-checkbox-disabled{opacity:.5}.cdk-high-contrast-active .mat-mdc-checkbox .mdc-checkbox__checkmark{--mdc-checkbox-selected-checkmark-color: CanvasText;--mdc-checkbox-disabled-selected-checkmark-color: CanvasText}.mat-mdc-checkbox .mdc-checkbox__ripple{opacity:0}.mat-mdc-checkbox-ripple,.mdc-checkbox__ripple{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-checkbox-ripple:not(:empty),.mdc-checkbox__ripple:not(:empty){transform:translateZ(0)}.mat-mdc-checkbox-ripple .mat-ripple-element{opacity:.1}.mat-mdc-checkbox-touch-target{position:absolute;top:50%;height:48px;left:50%;width:48px;transform:translate(-50%, -50%);display:var(--mat-checkbox-touch-target-display)}.mat-mdc-checkbox-ripple::before{border-radius:50%}.mdc-checkbox__native-control:focus~.mat-mdc-focus-indicator::before{content:\\\"\\\"}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_CHECKBOX_DEFAULT_OPTIONS]\n                }] }], propDecorators: { ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], id: [{\n                type: Input\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], labelPosition: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], change: [{\n                type: Output\n            }], indeterminateChange: [{\n                type: Output\n            }], value: [{\n                type: Input\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], _inputElement: [{\n                type: ViewChild,\n                args: ['input']\n            }], _labelElement: [{\n                type: ViewChild,\n                args: ['label']\n            }], tabIndex: [{\n                type: Input,\n                args: [{ transform: (value) => (value == null ? undefined : numberAttribute(value)) }]\n            }], color: [{\n                type: Input\n            }], ripple: [{\n                type: ViewChild,\n                args: [MatRipple]\n            }], checked: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], indeterminate: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }] } });\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nconst MAT_CHECKBOX_REQUIRED_VALIDATOR = {\n    provide: NG_VALIDATORS,\n    useExisting: forwardRef(() => MatCheckboxRequiredValidator),\n    multi: true,\n};\n/**\n * Validator for Material checkbox's required attribute in template-driven checkbox.\n * Current CheckboxRequiredValidator only work with `input type=checkbox` and does not\n * work with `mat-checkbox`.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass MatCheckboxRequiredValidator extends CheckboxRequiredValidator {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckboxRequiredValidator, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCheckboxRequiredValidator, isStandalone: true, selector: \"mat-checkbox[required][formControlName],\\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]\", providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckboxRequiredValidator, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-checkbox[required][formControlName],\n             mat-checkbox[required][formControl], mat-checkbox[required][ngModel]`,\n                    providers: [MAT_CHECKBOX_REQUIRED_VALIDATOR],\n                    standalone: true,\n                }]\n        }] });\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass _MatCheckboxRequiredValidatorModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule, imports: [MatCheckboxRequiredValidator], exports: [MatCheckboxRequiredValidator] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatCheckboxRequiredValidatorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCheckboxRequiredValidator],\n                    exports: [MatCheckboxRequiredValidator],\n                }]\n        }] });\nclass MatCheckboxModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckboxModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckboxModule, imports: [MatCheckbox, MatCommonModule], exports: [MatCheckbox, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckboxModule, imports: [MatCheckbox, MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCheckboxModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCheckbox, MatCommonModule],\n                    exports: [MatCheckbox, MatCommonModule],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR, MAT_CHECKBOX_DEFAULT_OPTIONS, MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY, MAT_CHECKBOX_REQUIRED_VALIDATOR, MatCheckbox, MatCheckboxChange, MatCheckboxModule, MatCheckboxRequiredValidator, TransitionCheckState, _MatCheckboxRequiredValidatorModule };\n"], "names": ["CommonModule", "ReactiveFormsModule", "RouterModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatSelectModule", "MatCheckboxModule", "MatProgressSpinnerModule", "MatDividerModule", "LoginComponent", "RegisterComponent", "authRoutes", "AuthModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1", "path", "redirectTo", "pathMatch", "component", "Validators", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "constructor", "fb", "authService", "router", "route", "snackBar", "isLoading", "hidePassword", "returnUrl", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "snapshot", "queryParams", "isAuthenticated", "navigate", "onSubmit", "_this", "_asyncToGenerator", "invalid", "markFormGroupTouched", "value", "login", "open", "duration", "error", "console", "getErrorMessage", "code", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "getFieldError", "fieldName", "field", "errors", "touched", "loginAsUser", "_this2", "patchValue", "loginAsValidator", "_this3", "loginAsAdmin", "_this4", "ɵɵdirectiveInject", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_21_listener", "LoginComponent_Template_button_click_34_listener", "ɵɵtemplate", "LoginComponent_mat_spinner_40_Template", "LoginComponent_span_41_Template", "LoginComponent_Template_button_click_52_listener", "LoginComponent_Template_button_click_60_listener", "LoginComponent_Template_button_click_68_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate", "UserRole", "city_r1", "ɵɵtextInterpolate1", "label", "role_r2", "description", "ctx_r2", "hideConfirmPassword", "cities", "roles", "USER", "PROVIDER", "VALIDATOR", "registerForm", "name", "phone", "pattern", "city", "role", "confirmPassword", "acceptTerms", "requiredTrue", "validators", "passwordMatchValidator", "form", "setErrors", "passwordMismatch", "length", "formValue", "register", "getRequiredMessage", "messages", "RegisterComponent_Template", "RegisterComponent_Template_form_ngSubmit_10_listener", "RegisterComponent_mat_option_39_Template", "RegisterComponent_mat_option_46_Template", "RegisterComponent_Template_button_click_53_listener", "RegisterComponent_Template_button_click_62_listener", "RegisterComponent_div_75_Template", "RegisterComponent_mat_spinner_77_Template", "RegisterComponent_span_78_Template", "InjectionToken", "forwardRef", "EventEmitter", "ANIMATION_MODULE_TYPE", "booleanAttribute", "numberAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Attribute", "Optional", "Inject", "Input", "Output", "ViewChild", "Directive", "NgModule", "NG_VALUE_ACCESSOR", "NG_VALIDATORS", "CheckboxRequiredValidator", "<PERSON><PERSON><PERSON><PERSON>", "_MatInternalFormField", "MatCommonModule", "_c0", "_c1", "_c2", "MAT_CHECKBOX_DEFAULT_OPTIONS", "providedIn", "factory", "MAT_CHECKBOX_DEFAULT_OPTIONS_FACTORY", "color", "clickAction", "TransitionCheckState", "MAT_CHECKBOX_CONTROL_VALUE_ACCESSOR", "provide", "useExisting", "MatCheckbox", "multi", "MatCheckboxChange", "nextUniqueId", "defaults", "focus", "_inputElement", "nativeElement", "_createChangeEvent", "isChecked", "event", "source", "checked", "_getAnimationTargetElement", "inputId", "id", "_uniqueId", "_elementRef", "_changeDetectorRef", "_ngZone", "tabIndex", "_animationMode", "_options", "_animationClasses", "uncheckedToChecked", "uncheckedToIndeterminate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>ed", "checkedToIndeterminate", "indeterminateToChecked", "indeterminate<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "labelPosition", "change", "indeterminateChange", "_onTouched", "_currentAnimationClass", "_currentCheckState", "Init", "_controlValueAccessorChangeFn", "_validatorChangeFn", "_checked", "_disabled", "_indeterminate", "parseInt", "ngOnChanges", "changes", "ngAfterViewInit", "_syncIndeterminate", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "indeterminate", "changed", "_transitionCheckState", "Indeterminate", "Checked", "Unchecked", "emit", "_isRippleDisabled", "disable<PERSON><PERSON><PERSON>", "_onLabelTextChange", "detectChanges", "writeValue", "registerOnChange", "fn", "registerOnTouched", "setDisabledState", "isDisabled", "validate", "registerOnValidatorChange", "newState", "oldState", "element", "classList", "remove", "_getAnimationClassForCheckStateTransition", "add", "animationClass", "runOutsideAngular", "setTimeout", "_emitChangeEvent", "toggle", "_handleInputClick", "Promise", "resolve", "then", "_onInteractionEvent", "stopPropagation", "_onBlur", "nativeCheckbox", "_onInputClick", "_onTouchTargetClick", "_preventBubblingFromLabel", "target", "_labelElement", "contains", "ɵfac", "MatCheckbox_Factory", "t", "ElementRef", "ChangeDetectorRef", "NgZone", "ɵɵinjectAttribute", "ɵcmp", "ɵɵdefineComponent", "type", "viewQuery", "MatCheckbox_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "ripple", "hostAttrs", "hostVars", "hostBindings", "MatCheckbox_HostBindings", "ɵɵhostProperty", "ɵɵattribute", "ɵɵclassMap", "ɵɵclassProp", "inputs", "ɵɵInputFlags", "None", "aria<PERSON><PERSON><PERSON><PERSON>", "HasDecoratorInputTransform", "undefined", "outputs", "exportAs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "MatCheckbox_Template", "_r1", "ɵɵgetCurrentView", "ɵɵprojectionDef", "MatCheckbox_Template_div_click_0_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "MatCheckbox_Template_div_click_3_listener", "MatCheckbox_Template_input_blur_4_listener", "MatCheckbox_Template_input_click_4_listener", "MatCheckbox_Template_input_change_4_listener", "ɵɵnamespaceSVG", "ɵɵnamespaceHTML", "ɵɵprojection", "checkbox_r2", "ɵɵreference", "dependencies", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "providers", "OnPush", "decorators", "transform", "MAT_CHECKBOX_REQUIRED_VALIDATOR", "MatCheckboxRequiredValidator", "ɵMatCheckboxRequiredValidator_BaseFactory", "MatCheckboxRequiredValidator_Factory", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "ɵɵInheritDefinitionFeature", "_MatCheckboxRequiredValidatorModule", "_MatCheckboxRequiredValidatorModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "MatCheckboxModule_Factory"], "sourceRoot": "webpack:///", "x_google_ignoreList": [6]}