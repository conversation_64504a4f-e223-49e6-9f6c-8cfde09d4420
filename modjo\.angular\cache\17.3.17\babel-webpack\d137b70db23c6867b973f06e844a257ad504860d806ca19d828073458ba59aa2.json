{"ast": null, "code": "import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { identity } from '../util/identity';\nimport { noop } from '../util/noop';\nimport { popResultSelector } from '../util/args';\nexport function withLatestFrom(...inputs) {\n  const project = popResultSelector(inputs);\n  return operate((source, subscriber) => {\n    const len = inputs.length;\n    const otherValues = new Array(len);\n    let hasValue = inputs.map(() => false);\n    let ready = false;\n    for (let i = 0; i < len; i++) {\n      innerFrom(inputs[i]).subscribe(createOperatorSubscriber(subscriber, value => {\n        otherValues[i] = value;\n        if (!ready && !hasValue[i]) {\n          hasValue[i] = true;\n          (ready = hasValue.every(identity)) && (hasValue = null);\n        }\n      }, noop));\n    }\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      if (ready) {\n        const values = [value, ...otherValues];\n        subscriber.next(project ? project(...values) : values);\n      }\n    }));\n  });\n}", "map": {"version": 3, "names": ["operate", "createOperatorSubscriber", "innerFrom", "identity", "noop", "popResultSelector", "withLatestFrom", "inputs", "project", "source", "subscriber", "len", "length", "otherValues", "Array", "hasValue", "map", "ready", "i", "subscribe", "value", "every", "values", "next"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/fire/node_modules/rxjs/dist/esm/internal/operators/withLatestFrom.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nimport { identity } from '../util/identity';\nimport { noop } from '../util/noop';\nimport { popResultSelector } from '../util/args';\nexport function withLatestFrom(...inputs) {\n    const project = popResultSelector(inputs);\n    return operate((source, subscriber) => {\n        const len = inputs.length;\n        const otherValues = new Array(len);\n        let hasValue = inputs.map(() => false);\n        let ready = false;\n        for (let i = 0; i < len; i++) {\n            innerFrom(inputs[i]).subscribe(createOperatorSubscriber(subscriber, (value) => {\n                otherValues[i] = value;\n                if (!ready && !hasValue[i]) {\n                    hasValue[i] = true;\n                    (ready = hasValue.every(identity)) && (hasValue = null);\n                }\n            }, noop));\n        }\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            if (ready) {\n                const values = [value, ...otherValues];\n                subscriber.next(project ? project(...values) : values);\n            }\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,QAAQ,QAAQ,kBAAkB;AAC3C,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,iBAAiB,QAAQ,cAAc;AAChD,OAAO,SAASC,cAAcA,CAAC,GAAGC,MAAM,EAAE;EACtC,MAAMC,OAAO,GAAGH,iBAAiB,CAACE,MAAM,CAAC;EACzC,OAAOP,OAAO,CAAC,CAACS,MAAM,EAAEC,UAAU,KAAK;IACnC,MAAMC,GAAG,GAAGJ,MAAM,CAACK,MAAM;IACzB,MAAMC,WAAW,GAAG,IAAIC,KAAK,CAACH,GAAG,CAAC;IAClC,IAAII,QAAQ,GAAGR,MAAM,CAACS,GAAG,CAAC,MAAM,KAAK,CAAC;IACtC,IAAIC,KAAK,GAAG,KAAK;IACjB,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGP,GAAG,EAAEO,CAAC,EAAE,EAAE;MAC1BhB,SAAS,CAACK,MAAM,CAACW,CAAC,CAAC,CAAC,CAACC,SAAS,CAAClB,wBAAwB,CAACS,UAAU,EAAGU,KAAK,IAAK;QAC3EP,WAAW,CAACK,CAAC,CAAC,GAAGE,KAAK;QACtB,IAAI,CAACH,KAAK,IAAI,CAACF,QAAQ,CAACG,CAAC,CAAC,EAAE;UACxBH,QAAQ,CAACG,CAAC,CAAC,GAAG,IAAI;UAClB,CAACD,KAAK,GAAGF,QAAQ,CAACM,KAAK,CAAClB,QAAQ,CAAC,MAAMY,QAAQ,GAAG,IAAI,CAAC;QAC3D;MACJ,CAAC,EAAEX,IAAI,CAAC,CAAC;IACb;IACAK,MAAM,CAACU,SAAS,CAAClB,wBAAwB,CAACS,UAAU,EAAGU,KAAK,IAAK;MAC7D,IAAIH,KAAK,EAAE;QACP,MAAMK,MAAM,GAAG,CAACF,KAAK,EAAE,GAAGP,WAAW,CAAC;QACtCH,UAAU,CAACa,IAAI,CAACf,OAAO,GAAGA,OAAO,CAAC,GAAGc,MAAM,CAAC,GAAGA,MAAM,CAAC;MAC1D;IACJ,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}