.rewards-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.rewards-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(103, 126, 234, 0.3);
}

.rewards-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.rewards-header h1 {
  margin: 0 0 12px 0;
  font-size: 2.5rem;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.rewards-header p {
  margin: 0 0 24px 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.user-points {
  display: inline-flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.25);
  padding: 16px 24px;
  border-radius: 32px;
  font-size: 1.2rem;
  font-weight: 700;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.user-points mat-icon {
  color: #ffd700;
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
  animation: pulse 2s infinite;
}

.filters-section {
  margin-bottom: 40px;
}

.filters-card {
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.filters-grid {
  display: grid;
  grid-template-columns: 2fr 1fr 1fr auto;
  gap: 20px;
  align-items: end;
}

.search-field {
  min-width: 300px;
}

.clear-filters-btn {
  height: 56px;
  border-radius: 16px;
  font-weight: 500;
}

.rewards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.reward-card {
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
  position: relative;
}

.reward-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.reward-image {
  position: relative;
  height: 200px;
  overflow: hidden;
}

.reward-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: transform 0.3s ease;
}

.reward-card:hover .reward-image img {
  transform: scale(1.1);
}

.reward-badges {
  position: absolute;
  top: 12px;
  left: 12px;
  right: 12px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 8px;
}

.category-chip,
.expiry-chip {
  background: rgba(255, 255, 255, 0.9) !important;
  backdrop-filter: blur(10px);
  font-weight: 500;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.reward-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 12px;
}

.reward-header h3 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
  flex: 1;
}

.points-required {
  display: flex;
  align-items: center;
  gap: 4px;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #2d3748;
  padding: 8px 12px;
  border-radius: 16px;
  font-weight: 700;
  font-size: 0.9rem;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.points-required mat-icon {
  font-size: 1.1rem;
  width: 1.1rem;
  height: 1.1rem;
}

.reward-description {
  color: #718096;
  margin: 0 0 16px 0;
  line-height: 1.5;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.reward-partner {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  color: #4a5568;
  font-weight: 500;
}

.reward-partner mat-icon {
  color: #667eea;
}

.city-chip {
  margin-left: auto;
}

.reward-availability {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 16px;
}

.expiry-date {
  font-size: 0.8rem;
  color: #a0aec0;
}

.exchange-btn {
  flex: 1;
  height: 48px;
  border-radius: 12px;
  font-weight: 600;
  margin-right: 8px;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.empty-card {
  max-width: 500px;
  text-align: center;
}

.empty-content {
  padding: 40px;
}

.empty-icon {
  font-size: 4rem;
  color: #cbd5e0;
  margin-bottom: 24px;
}

.empty-content h3 {
  margin: 0 0 12px 0;
  color: #2d3748;
  font-size: 1.5rem;
}

.empty-content p {
  margin: 0 0 24px 0;
  color: #718096;
  line-height: 1.6;
}

.floating-fab {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
  background: linear-gradient(135deg, #667eea, #764ba2);
  box-shadow: 0 8px 32px rgba(103, 126, 234, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-fab:hover {
  transform: scale(1.1) rotate(5deg);
  box-shadow: 0 12px 40px rgba(103, 126, 234, 0.6);
}

/* Mobile responsive */
@media (max-width: 768px) {
  .rewards-container {
    padding: 16px;
  }
  
  .rewards-header {
    padding: 24px;
    margin-bottom: 24px;
  }
  
  .rewards-header h1 {
    font-size: 2rem;
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .search-field {
    min-width: auto;
  }
  
  .rewards-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .reward-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .points-required {
    align-self: flex-end;
  }
}
