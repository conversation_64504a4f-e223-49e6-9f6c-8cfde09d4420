<div class="scanner-container">
  <!-- Scanner header -->
  <div class="scanner-header">
    <h1>Scanner QR Code</h1>
    <p>Pointez votre caméra vers un code QR pour gagner des points</p>
  </div>

  <!-- Camera section -->
  <div class="camera-section" *ngIf="hasCamera">
    <mat-card class="camera-card">
      <mat-card-content>
        <div class="camera-container">
          <video #video 
                 class="camera-video" 
                 [class.active]="isScanning"
                 autoplay 
                 playsinline>
          </video>
          <canvas #canvas class="camera-canvas" style="display: none;"></canvas>
          
          <!-- Scanner overlay -->
          <div class="scanner-overlay" *ngIf="isScanning">
            <div class="scanner-frame">
              <div class="scanner-corners">
                <div class="corner top-left"></div>
                <div class="corner top-right"></div>
                <div class="corner bottom-left"></div>
                <div class="corner bottom-right"></div>
              </div>
            </div>
            <p class="scanner-instruction">Placez le QR code dans le cadre</p>
          </div>
        </div>

        <!-- Camera controls -->
        <div class="camera-controls">
          <button mat-raised-button
                  color="primary"
                  (click)="startScanning()"
                  *ngIf="!isScanning"
                  class="scan-button enhanced-btn">
            <mat-icon>qr_code_scanner</mat-icon>
            Commencer le scan
          </button>

          <button mat-raised-button
                  color="warn"
                  (click)="stopScanning()"
                  *ngIf="isScanning"
                  class="stop-button enhanced-btn">
            <mat-icon>stop</mat-icon>
            Arrêter le scan
          </button>

          <button mat-stroked-button
                  color="accent"
                  (click)="toggleFlashlight()"
                  [disabled]="!isScanning"
                  class="flash-button enhanced-btn">
            <mat-icon>{{ flashlightOn ? 'flash_off' : 'flash_on' }}</mat-icon>
            {{ flashlightOn ? 'Flash Off' : 'Flash On' }}
          </button>

          <button mat-stroked-button
                  color="primary"
                  (click)="switchCamera()"
                  [disabled]="!isScanning"
                  class="camera-button enhanced-btn">
            <mat-icon>flip_camera_android</mat-icon>
            Changer caméra
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- No camera fallback -->
  <div class="no-camera-section" *ngIf="!hasCamera">
    <mat-card class="no-camera-card">
      <mat-card-content>
        <div class="no-camera-content">
          <mat-icon class="no-camera-icon">camera_alt</mat-icon>
          <h3>Caméra non disponible</h3>
          <p>Votre appareil ne dispose pas de caméra ou l'accès a été refusé.</p>
          <button mat-raised-button color="primary" (click)="manualQrInput()">
            <mat-icon>keyboard</mat-icon>
            Saisie manuelle
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Demo section -->
  <div class="demo-section fade-in">
    <mat-card class="demo-card floating-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>science</mat-icon>
          🎯 Codes QR de démonstration
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p>Testez l'application avec ces codes QR de démonstration :</p>
        <div class="demo-buttons">
          <button mat-raised-button
                  *ngFor="let demo of demoQrCodes; let i = index"
                  (click)="testQrCode(demo.data)"
                  [color]="getDemoButtonColor(i)"
                  class="demo-qr-button"
                  [style.animation-delay]="(i * 0.1) + 's'">
            <mat-icon>{{ getDemoButtonIcon(demo.label) }}</mat-icon>
            <div class="demo-button-content">
              <span class="demo-title">{{ demo.label }}</span>
              <small class="demo-points">{{ getDemoPoints(demo.label) }}</small>
            </div>
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Instructions -->
  <div class="instructions-section">
    <mat-card class="instructions-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>help_outline</mat-icon>
          Comment ça marche ?
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="instructions-list">
          <div class="instruction-item">
            <mat-icon color="primary">qr_code_scanner</mat-icon>
            <div>
              <h4>1. Scanner</h4>
              <p>Pointez votre caméra vers un code QR valide</p>
            </div>
          </div>
          
          <div class="instruction-item">
            <mat-icon color="primary">verified</mat-icon>
            <div>
              <h4>2. Validation</h4>
              <p>Le code est automatiquement vérifié et traité</p>
            </div>
          </div>
          
          <div class="instruction-item">
            <mat-icon color="primary">stars</mat-icon>
            <div>
              <h4>3. Points</h4>
              <p>Gagnez des points selon l'action effectuée</p>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
