import { <PERSON>mpo<PERSON>, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { QrService } from '../../../../core/services/qr.service';
import { UserService } from '../../../../core/services/user.service';
import { AuthService } from '../../../../core/services/auth.service';
import { PointsAssignmentComponent } from '../points-assignment/points-assignment.component';
import { QrCodeData, User } from '../../../../core/models';

@Component({
  selector: 'app-qr-scanner',
  templateUrl: './qr-scanner.component.html',
  styleUrls: ['./qr-scanner.component.css']
})
export class QrScannerComponent implements OnInit, On<PERSON><PERSON>roy {
  @ViewChild('video', { static: false }) video!: ElementRef<HTMLVideoElement>;
  @ViewChild('canvas', { static: false }) canvas!: ElementRef<HTMLCanvasElement>;

  isScanning = false;
  hasCamera = false;
  currentUser: User | null = null;
  stream: MediaStream | null = null;
  scanInterval: any;

  // Demo QR codes for testing
  demoQrCodes = [
    { label: 'Transfert utilisateur (1 pt)', data: 'user:demo123' },
    { label: 'Action validateur (10 pts)', data: 'validator:val456:community_service:10' },
    { label: 'Récompense partenaire (5 pts)', data: '{"type":"partner_reward","points":5,"timestamp":' + Date.now() + '}' },
    { label: 'Bonus système (3 pts)', data: '{"type":"system_bonus","points":3,"timestamp":' + Date.now() + '}' }
  ];

  constructor(
    private qrService: QrService,
    private userService: UserService,
    private authService: AuthService,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
    this.checkCameraSupport();
  }

  ngOnDestroy(): void {
    this.stopScanning();
  }

  async checkCameraSupport(): Promise<void> {
    try {
      const devices = await navigator.mediaDevices.enumerateDevices();
      this.hasCamera = devices.some(device => device.kind === 'videoinput');
    } catch (error) {
      console.error('Error checking camera support:', error);
      this.hasCamera = false;
    }
  }

  async startScanning(): Promise<void> {
    if (!this.hasCamera) {
      this.snackBar.open('Caméra non disponible', 'Fermer', { duration: 3000 });
      return;
    }

    try {
      this.stream = await navigator.mediaDevices.getUserMedia({
        video: { facingMode: 'environment' }
      });
      
      this.video.nativeElement.srcObject = this.stream;
      this.video.nativeElement.play();
      this.isScanning = true;

      // Start scanning for QR codes
      this.scanInterval = setInterval(() => {
        this.scanForQrCode();
      }, 500);

    } catch (error) {
      console.error('Error starting camera:', error);
      this.snackBar.open('Erreur d\'accès à la caméra', 'Fermer', { duration: 3000 });
    }
  }

  stopScanning(): void {
    if (this.stream) {
      this.stream.getTracks().forEach(track => track.stop());
      this.stream = null;
    }
    
    if (this.scanInterval) {
      clearInterval(this.scanInterval);
      this.scanInterval = null;
    }
    
    this.isScanning = false;
  }

  private scanForQrCode(): void {
    if (!this.video.nativeElement || !this.canvas.nativeElement) return;

    const video = this.video.nativeElement;
    const canvas = this.canvas.nativeElement;
    const context = canvas.getContext('2d');

    if (!context || video.readyState !== video.HAVE_ENOUGH_DATA) return;

    canvas.width = video.videoWidth;
    canvas.height = video.videoHeight;
    context.drawImage(video, 0, 0, canvas.width, canvas.height);

    // In a real implementation, you would use a QR code library like @zxing/library
    // For demo purposes, we'll simulate QR detection
    this.simulateQrDetection();
  }

  private simulateQrDetection(): void {
    // Simulate random QR code detection for demo
    if (Math.random() < 0.1) { // 10% chance per scan
      const randomQr = this.demoQrCodes[Math.floor(Math.random() * this.demoQrCodes.length)];
      this.processQrCode(randomQr.data);
    }
  }

  async processQrCode(qrData: string): Promise<void> {
    this.stopScanning();

    try {
      const parsedData = this.qrService.parseQrData(qrData);
      if (!parsedData) {
        this.snackBar.open('Code QR invalide', 'Fermer', { duration: 3000 });
        return;
      }

      const result = await this.qrService.handleQrScan(parsedData);
      this.openPointsAssignmentDialog(result, parsedData);

    } catch (error) {
      console.error('Error processing QR code:', error);
      this.snackBar.open('Erreur lors du traitement du QR code', 'Fermer', { duration: 3000 });
    }
  }

  private openPointsAssignmentDialog(scanResult: any, qrData: QrCodeData): void {
    const dialogRef = this.dialog.open(PointsAssignmentComponent, {
      width: '400px',
      data: {
        scanResult,
        qrData,
        currentUser: this.currentUser
      }
    });

    dialogRef.afterClosed().subscribe(result => {
      if (result) {
        this.snackBar.open('Points attribués avec succès!', 'Fermer', { duration: 3000 });
      }
    });
  }

  // Demo methods for testing
  testQrCode(qrData: string): void {
    this.processQrCode(qrData);
  }

  async manualQrInput(): Promise<void> {
    // In a real app, you might open a dialog for manual input
    const qrData = prompt('Entrez le code QR manuellement:');
    if (qrData) {
      await this.processQrCode(qrData);
    }
  }
}
