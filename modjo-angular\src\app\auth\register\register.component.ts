import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { Router } from '@angular/router';
import { AuthService } from '../auth.service';
import { UserRole } from '../../models/user.model';

@Component({
  selector: 'app-register',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css']
})
export class RegisterComponent {
  email: string = '';
  password: string = '';
  confirmPassword: string = '';
  firstName: string = '';
  lastName: string = '';
  city: string = '';
  role: UserRole = UserRole.USER;
  activity: string = '';
  
  errorMessage: string = '';
  isLoading: boolean = false;
  
  roles = [
    { value: UserRole.USER, label: 'Utilisateur' },
    { value: UserRole.PROVIDER, label: 'Prestataire' }
  ];

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  async onSubmit(): Promise<void> {
    this.errorMessage = '';
    
    // Validation
    if (!this.email || !this.password || !this.confirmPassword || !this.firstName || !this.lastName || !this.city) {
      this.errorMessage = 'Veuillez remplir tous les champs obligatoires';
      return;
    }
    
    if (this.password !== this.confirmPassword) {
      this.errorMessage = 'Les mots de passe ne correspondent pas';
      return;
    }
    
    if (this.role === UserRole.PROVIDER && !this.activity) {
      this.errorMessage = 'Veuillez indiquer votre activité';
      return;
    }
    
    this.isLoading = true;
    
    try {
      await this.authService.signUp(this.email, this.password, {
        firstName: this.firstName,
        lastName: this.lastName,
        displayName: `${this.firstName} ${this.lastName}`,
        city: this.city,
        role: this.role,
        activity: this.activity
      });
      
      this.router.navigate(['/profile']);
    } catch (error: any) {
      this.errorMessage = error.message || 'Une erreur est survenue lors de l\'inscription';
      console.error('Registration error:', error);
    } finally {
      this.isLoading = false;
    }
  }
}
