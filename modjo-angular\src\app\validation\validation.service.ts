import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { Action, ActionType } from '../models/action.model';
import { UserService } from '../user/user.service';
import { PointType } from '../models/point.model';

// These imports would normally come from Firebase, but we're mocking them for now
// import { Firestore, collection, doc, getDoc, getDocs, query, where, orderBy, addDoc, updateDoc } from '@angular/fire/firestore';

@Injectable({
  providedIn: 'root'
})
export class ValidationService {
  // Mock data for development
  private mockActions: Action[] = [
    {
      id: '1',
      userId: '1',
      type: ActionType.TASK_COMPLETION,
      description: 'Devoir de mathématiques',
      pointsAwarded: 10,
      status: 'pending',
      createdAt: new Date(Date.now() - 86400000) // 1 day ago
    },
    {
      id: '2',
      userId: '1',
      validatorId: '3',
      type: ActionType.EDUCATIONAL_ACHIEVEMENT,
      description: 'Projet de science',
      pointsAwarded: 15,
      status: 'validated',
      createdAt: new Date(Date.now() - 172800000), // 2 days ago
      validatedAt: new Date(Date.now() - 86400000) // 1 day ago
    }
  ];

  constructor(
    // private firestore: Firestore,
    private userService: UserService
  ) {}

  // Get pending actions for validation
  getPendingActions(): Observable<Action[]> {
    // Mock implementation
    return of(this.mockActions.filter(a => a.status === 'pending'));

    // Firebase implementation
    // const actionsRef = collection(this.firestore, 'actions');
    // const q = query(
    //   actionsRef,
    //   where('status', '==', 'pending'),
    //   orderBy('createdAt', 'desc')
    // );
    // 
    // return from(getDocs(q)).pipe(
    //   map(querySnapshot => {
    //     return querySnapshot.docs.map(doc => ({
    //       id: doc.id,
    //       ...doc.data()
    //     } as Action));
    //   })
    // );
  }

  // Get actions by validator
  getActionsByValidator(validatorId: string): Observable<Action[]> {
    // Mock implementation
    return of(this.mockActions.filter(a => a.validatorId === validatorId));

    // Firebase implementation
    // const actionsRef = collection(this.firestore, 'actions');
    // const q = query(
    //   actionsRef,
    //   where('validatorId', '==', validatorId),
    //   orderBy('validatedAt', 'desc')
    // );
    // 
    // return from(getDocs(q)).pipe(
    //   map(querySnapshot => {
    //     return querySnapshot.docs.map(doc => ({
    //       id: doc.id,
    //       ...doc.data()
    //     } as Action));
    //   })
    // );
  }

  // Get actions by user
  getActionsByUser(userId: string): Observable<Action[]> {
    // Mock implementation
    return of(this.mockActions.filter(a => a.userId === userId));

    // Firebase implementation
    // const actionsRef = collection(this.firestore, 'actions');
    // const q = query(
    //   actionsRef,
    //   where('userId', '==', userId),
    //   orderBy('createdAt', 'desc')
    // );
    // 
    // return from(getDocs(q)).pipe(
    //   map(querySnapshot => {
    //     return querySnapshot.docs.map(doc => ({
    //       id: doc.id,
    //       ...doc.data()
    //     } as Action));
    //   })
    // );
  }

  // Create a new action
  async createAction(action: Omit<Action, 'id' | 'status' | 'createdAt'>): Promise<Action> {
    // Mock implementation
    const newAction: Action = {
      id: Math.random().toString(36).substring(2, 15),
      ...action,
      status: 'pending',
      createdAt: new Date()
    };
    
    this.mockActions.push(newAction);
    return newAction;

    // Firebase implementation
    // const actionData: Omit<Action, 'id'> = {
    //   ...action,
    //   status: 'pending',
    //   createdAt: new Date()
    // };
    // 
    // const docRef = await addDoc(collection(this.firestore, 'actions'), actionData);
    // return { id: docRef.id, ...actionData };
  }

  // Validate an action
  async validateAction(actionId: string, validatorId: string, approved: boolean): Promise<void> {
    // Mock implementation
    const actionIndex = this.mockActions.findIndex(a => a.id === actionId);
    if (actionIndex === -1) {
      throw new Error('Action not found');
    }
    
    const action = this.mockActions[actionIndex];
    action.validatorId = validatorId;
    action.status = approved ? 'validated' : 'rejected';
    action.validatedAt = new Date();
    
    if (approved) {
      // Award points to the user
      await this.userService.addPoints(
        action.userId,
        undefined,
        action.pointsAwarded,
        action.description,
        PointType.VALIDATION
      );
    }

    // Firebase implementation
    // const actionRef = doc(this.firestore, `actions/${actionId}`);
    // const actionDoc = await getDoc(actionRef);
    // 
    // if (!actionDoc.exists()) {
    //   throw new Error('Action not found');
    // }
    // 
    // const action = { id: actionDoc.id, ...actionDoc.data() } as Action;
    // 
    // // Update action status
    // await updateDoc(actionRef, {
    //   validatorId,
    //   status: approved ? 'validated' : 'rejected',
    //   validatedAt: new Date()
    // });
    // 
    // if (approved) {
    //   // Award points to the user
    //   await this.userService.addPoints(
    //     action.userId,
    //     undefined,
    //     action.pointsAwarded,
    //     action.description,
    //     PointType.VALIDATION
    //   );
    // }
  }
}
