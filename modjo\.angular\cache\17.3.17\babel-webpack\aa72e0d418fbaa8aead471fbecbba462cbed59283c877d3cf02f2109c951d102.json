{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, isDevMode, NgZone, Optional, VERSION as VERSION$1, PLATFORM_ID, NgModule, Inject } from '@angular/core';\nimport firebase from 'firebase/compat/app';\nimport { VERSION } from '@angular/fire';\n\n// DEBUG quick debugger function for inline logging that typescript doesn't complain about\n//       wrote it for debugging the ɵlazySDKProxy, commenting out for now; should consider exposing a\n//       verbose mode for AngularFire in a future release that uses something like this in multiple places\n//       usage: () => log('something') || returnValue\n// const log = (...args: any[]): false => { console.log(...args); return false }\n// The problem here are things like ngOnDestroy are missing, then triggering the service\n// rather than dig too far; I'm capturing these as I go.\nconst noopFunctions = ['ngOnDestroy'];\n// INVESTIGATE should we make the Proxy revokable and do some cleanup?\n//             right now it's fairly simple but I'm sure this will grow in complexity\nconst ɵlazySDKProxy = (klass, observable, zone, options = {}) => {\n  return new Proxy(klass, {\n    get: (_, name) => zone.runOutsideAngular(() => {\n      var _a;\n      if (klass[name]) {\n        if ((_a = options === null || options === void 0 ? void 0 : options.spy) === null || _a === void 0 ? void 0 : _a.get) {\n          options.spy.get(name, klass[name]);\n        }\n        return klass[name];\n      }\n      if (noopFunctions.indexOf(name) > -1) {\n        return () => {};\n      }\n      const promise = observable.toPromise().then(mod => {\n        const ret = mod && mod[name];\n        // TODO move to proper type guards\n        if (typeof ret === 'function') {\n          return ret.bind(mod);\n        } else if (ret && ret.then) {\n          return ret.then(res => zone.run(() => res));\n        } else {\n          return zone.run(() => ret);\n        }\n      });\n      // recurse the proxy\n      return new Proxy(() => {}, {\n        get: (_, name) => promise[name],\n        // TODO handle callbacks as transparently as I can\n        apply: (self, _, args) => promise.then(it => {\n          var _a;\n          const res = it && it(...args);\n          if ((_a = options === null || options === void 0 ? void 0 : options.spy) === null || _a === void 0 ? void 0 : _a.apply) {\n            options.spy.apply(name, args, res);\n          }\n          return res;\n        })\n      });\n    })\n  });\n};\nconst ɵapplyMixins = (derivedCtor, constructors) => {\n  constructors.forEach(baseCtor => {\n    Object.getOwnPropertyNames(baseCtor.prototype || baseCtor).forEach(name => {\n      Object.defineProperty(derivedCtor.prototype, name, Object.getOwnPropertyDescriptor(baseCtor.prototype || baseCtor, name));\n    });\n  });\n};\nclass FirebaseApp {\n  constructor(app) {\n    return app;\n  }\n}\nconst FIREBASE_OPTIONS = new InjectionToken('angularfire2.app.options');\nconst FIREBASE_APP_NAME = new InjectionToken('angularfire2.app.name');\nfunction ɵfirebaseAppFactory(options, zone, nameOrConfig) {\n  const name = typeof nameOrConfig === 'string' && nameOrConfig || '[DEFAULT]';\n  const config = typeof nameOrConfig === 'object' && nameOrConfig || {};\n  config.name = config.name || name;\n  // Added any due to some inconsistency between @firebase/app and firebase types\n  const existingApp = firebase.apps.filter(app => app && app.name === config.name)[0];\n  // We support FirebaseConfig, initializeApp's public type only accepts string; need to cast as any\n  // Could be solved with https://github.com/firebase/firebase-js-sdk/pull/1206\n  const app = existingApp || zone.runOutsideAngular(() => firebase.initializeApp(options, config));\n  try {\n    if (JSON.stringify(options) !== JSON.stringify(app.options)) {\n      const hmr = !!module.hot;\n      log$1('error', `${app.name} Firebase App already initialized with different options${hmr ? ', you may need to reload as Firebase is not HMR aware.' : '.'}`);\n    }\n  } catch (e) {}\n  return new FirebaseApp(app);\n}\nconst log$1 = (level, ...args) => {\n  if (isDevMode() && typeof console !== 'undefined') {\n    console[level](...args);\n  }\n};\nconst FIREBASE_APP_PROVIDER = {\n  provide: FirebaseApp,\n  useFactory: ɵfirebaseAppFactory,\n  deps: [FIREBASE_OPTIONS, NgZone, [new Optional(), FIREBASE_APP_NAME]]\n};\nclass AngularFireModule {\n  // tslint:disable-next-line:ban-types\n  constructor(platformId) {\n    firebase.registerVersion('angularfire', VERSION.full, 'core');\n    firebase.registerVersion('angularfire', VERSION.full, 'app-compat');\n    firebase.registerVersion('angular', VERSION$1.full, platformId.toString());\n  }\n  static initializeApp(options, nameOrConfig) {\n    return {\n      ngModule: AngularFireModule,\n      providers: [{\n        provide: FIREBASE_OPTIONS,\n        useValue: options\n      }, {\n        provide: FIREBASE_APP_NAME,\n        useValue: nameOrConfig\n      }]\n    };\n  }\n}\nAngularFireModule.ɵfac = function AngularFireModule_Factory(t) {\n  return new (t || AngularFireModule)(i0.ɵɵinject(PLATFORM_ID));\n};\nAngularFireModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: AngularFireModule\n});\nAngularFireModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [FIREBASE_APP_PROVIDER]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularFireModule, [{\n    type: NgModule,\n    args: [{\n      providers: [FIREBASE_APP_PROVIDER]\n    }]\n  }], function () {\n    return [{\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }];\n  }, null);\n})();\nfunction ɵcacheInstance(cacheKey, moduleName, appName, fn, deps) {\n  const [, instance, cachedDeps] = globalThis.ɵAngularfireInstanceCache.find(it => it[0] === cacheKey) || [];\n  if (instance) {\n    if (!matchDep(deps, cachedDeps)) {\n      log('error', `${moduleName} was already initialized on the ${appName} Firebase App with different settings.${IS_HMR ? ' You may need to reload as Firebase is not HMR aware.' : ''}`);\n      log('warn', {\n        is: deps,\n        was: cachedDeps\n      });\n    }\n    return instance;\n  } else {\n    const newInstance = fn();\n    globalThis.ɵAngularfireInstanceCache.push([cacheKey, newInstance, deps]);\n    return newInstance;\n  }\n}\nfunction matchDep(a, b) {\n  try {\n    return a.toString() === b.toString();\n  } catch (_) {\n    return a === b;\n  }\n}\nconst IS_HMR = !!module.hot;\nconst log = (level, ...args) => {\n  if (isDevMode() && typeof console !== 'undefined') {\n    console[level](...args);\n  }\n};\nglobalThis.ɵAngularfireInstanceCache || (globalThis.ɵAngularfireInstanceCache = []);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularFireModule, FIREBASE_APP_NAME, FIREBASE_OPTIONS, FirebaseApp, ɵapplyMixins, ɵcacheInstance, ɵfirebaseAppFactory, ɵlazySDKProxy };", "map": {"version": 3, "names": ["i0", "InjectionToken", "isDevMode", "NgZone", "Optional", "VERSION", "VERSION$1", "PLATFORM_ID", "NgModule", "Inject", "firebase", "noopFunctions", "ɵlazySDKProxy", "klass", "observable", "zone", "options", "Proxy", "get", "_", "name", "runOutsideAngular", "_a", "spy", "indexOf", "promise", "to<PERSON>romise", "then", "mod", "ret", "bind", "res", "run", "apply", "self", "args", "it", "ɵapplyMixins", "derivedCtor", "constructors", "for<PERSON>ach", "baseCtor", "Object", "getOwnPropertyNames", "prototype", "defineProperty", "getOwnPropertyDescriptor", "FirebaseApp", "constructor", "app", "FIREBASE_OPTIONS", "FIREBASE_APP_NAME", "ɵfirebaseAppFactory", "nameOrConfig", "config", "existingApp", "apps", "filter", "initializeApp", "JSON", "stringify", "hmr", "module", "hot", "log$1", "e", "level", "console", "FIREBASE_APP_PROVIDER", "provide", "useFactory", "deps", "AngularFireModule", "platformId", "registerVersion", "full", "toString", "ngModule", "providers", "useValue", "ɵfac", "AngularFireModule_Factory", "t", "ɵɵinject", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "ngDevMode", "ɵsetClassMetadata", "decorators", "ɵcacheInstance", "cache<PERSON>ey", "moduleName", "appName", "fn", "instance", "cachedDeps", "globalThis", "ɵAngularfireInstanceCache", "find", "matchDep", "log", "IS_HMR", "is", "was", "newInstance", "push", "a", "b"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/fire/fesm2015/angular-fire-compat.js"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, isDevMode, NgZone, Optional, VERSION as VERSION$1, PLATFORM_ID, NgModule, Inject } from '@angular/core';\nimport firebase from 'firebase/compat/app';\nimport { VERSION } from '@angular/fire';\n\n// DEBUG quick debugger function for inline logging that typescript doesn't complain about\n//       wrote it for debugging the ɵlazySDKProxy, commenting out for now; should consider exposing a\n//       verbose mode for AngularFire in a future release that uses something like this in multiple places\n//       usage: () => log('something') || returnValue\n// const log = (...args: any[]): false => { console.log(...args); return false }\n// The problem here are things like ngOnDestroy are missing, then triggering the service\n// rather than dig too far; I'm capturing these as I go.\nconst noopFunctions = ['ngOnDestroy'];\n// INVESTIGATE should we make the Proxy revokable and do some cleanup?\n//             right now it's fairly simple but I'm sure this will grow in complexity\nconst ɵlazySDKProxy = (klass, observable, zone, options = {}) => {\n    return new Proxy(klass, {\n        get: (_, name) => zone.runOutsideAngular(() => {\n            var _a;\n            if (klass[name]) {\n                if ((_a = options === null || options === void 0 ? void 0 : options.spy) === null || _a === void 0 ? void 0 : _a.get) {\n                    options.spy.get(name, klass[name]);\n                }\n                return klass[name];\n            }\n            if (noopFunctions.indexOf(name) > -1) {\n                return () => {\n                };\n            }\n            const promise = observable.toPromise().then(mod => {\n                const ret = mod && mod[name];\n                // TODO move to proper type guards\n                if (typeof ret === 'function') {\n                    return ret.bind(mod);\n                }\n                else if (ret && ret.then) {\n                    return ret.then((res) => zone.run(() => res));\n                }\n                else {\n                    return zone.run(() => ret);\n                }\n            });\n            // recurse the proxy\n            return new Proxy(() => { }, {\n                get: (_, name) => promise[name],\n                // TODO handle callbacks as transparently as I can\n                apply: (self, _, args) => promise.then(it => {\n                    var _a;\n                    const res = it && it(...args);\n                    if ((_a = options === null || options === void 0 ? void 0 : options.spy) === null || _a === void 0 ? void 0 : _a.apply) {\n                        options.spy.apply(name, args, res);\n                    }\n                    return res;\n                })\n            });\n        })\n    });\n};\nconst ɵapplyMixins = (derivedCtor, constructors) => {\n    constructors.forEach((baseCtor) => {\n        Object.getOwnPropertyNames(baseCtor.prototype || baseCtor).forEach((name) => {\n            Object.defineProperty(derivedCtor.prototype, name, Object.getOwnPropertyDescriptor(baseCtor.prototype || baseCtor, name));\n        });\n    });\n};\n\nclass FirebaseApp {\n    constructor(app) {\n        return app;\n    }\n}\n\nconst FIREBASE_OPTIONS = new InjectionToken('angularfire2.app.options');\nconst FIREBASE_APP_NAME = new InjectionToken('angularfire2.app.name');\nfunction ɵfirebaseAppFactory(options, zone, nameOrConfig) {\n    const name = typeof nameOrConfig === 'string' && nameOrConfig || '[DEFAULT]';\n    const config = typeof nameOrConfig === 'object' && nameOrConfig || {};\n    config.name = config.name || name;\n    // Added any due to some inconsistency between @firebase/app and firebase types\n    const existingApp = firebase.apps.filter(app => app && app.name === config.name)[0];\n    // We support FirebaseConfig, initializeApp's public type only accepts string; need to cast as any\n    // Could be solved with https://github.com/firebase/firebase-js-sdk/pull/1206\n    const app = (existingApp || zone.runOutsideAngular(() => firebase.initializeApp(options, config)));\n    try {\n        if (JSON.stringify(options) !== JSON.stringify(app.options)) {\n            const hmr = !!module.hot;\n            log$1('error', `${app.name} Firebase App already initialized with different options${hmr ? ', you may need to reload as Firebase is not HMR aware.' : '.'}`);\n        }\n    }\n    catch (e) { }\n    return new FirebaseApp(app);\n}\nconst log$1 = (level, ...args) => {\n    if (isDevMode() && typeof console !== 'undefined') {\n        console[level](...args);\n    }\n};\nconst FIREBASE_APP_PROVIDER = {\n    provide: FirebaseApp,\n    useFactory: ɵfirebaseAppFactory,\n    deps: [\n        FIREBASE_OPTIONS,\n        NgZone,\n        [new Optional(), FIREBASE_APP_NAME]\n    ]\n};\nclass AngularFireModule {\n    // tslint:disable-next-line:ban-types\n    constructor(platformId) {\n        firebase.registerVersion('angularfire', VERSION.full, 'core');\n        firebase.registerVersion('angularfire', VERSION.full, 'app-compat');\n        firebase.registerVersion('angular', VERSION$1.full, platformId.toString());\n    }\n    static initializeApp(options, nameOrConfig) {\n        return {\n            ngModule: AngularFireModule,\n            providers: [\n                { provide: FIREBASE_OPTIONS, useValue: options },\n                { provide: FIREBASE_APP_NAME, useValue: nameOrConfig }\n            ]\n        };\n    }\n}\nAngularFireModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFireModule, deps: [{ token: PLATFORM_ID }], target: i0.ɵɵFactoryTarget.NgModule });\nAngularFireModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFireModule });\nAngularFireModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFireModule, providers: [FIREBASE_APP_PROVIDER] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFireModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [FIREBASE_APP_PROVIDER]\n                }]\n        }], ctorParameters: function () { return [{ type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }]; } });\n\nfunction ɵcacheInstance(cacheKey, moduleName, appName, fn, deps) {\n    const [, instance, cachedDeps] = globalThis.ɵAngularfireInstanceCache.find((it) => it[0] === cacheKey) || [];\n    if (instance) {\n        if (!matchDep(deps, cachedDeps)) {\n            log('error', `${moduleName} was already initialized on the ${appName} Firebase App with different settings.${IS_HMR ? ' You may need to reload as Firebase is not HMR aware.' : ''}`);\n            log('warn', { is: deps, was: cachedDeps });\n        }\n        return instance;\n    }\n    else {\n        const newInstance = fn();\n        globalThis.ɵAngularfireInstanceCache.push([cacheKey, newInstance, deps]);\n        return newInstance;\n    }\n}\nfunction matchDep(a, b) {\n    try {\n        return a.toString() === b.toString();\n    }\n    catch (_) {\n        return a === b;\n    }\n}\nconst IS_HMR = !!module.hot;\nconst log = (level, ...args) => {\n    if (isDevMode() && typeof console !== 'undefined') {\n        console[level](...args);\n    }\n};\nglobalThis.ɵAngularfireInstanceCache || (globalThis.ɵAngularfireInstanceCache = []);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularFireModule, FIREBASE_APP_NAME, FIREBASE_OPTIONS, FirebaseApp, ɵapplyMixins, ɵcacheInstance, ɵfirebaseAppFactory, ɵlazySDKProxy };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,OAAO,IAAIC,SAAS,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,eAAe;AAChI,OAAOC,QAAQ,MAAM,qBAAqB;AAC1C,SAASL,OAAO,QAAQ,eAAe;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMM,aAAa,GAAG,CAAC,aAAa,CAAC;AACrC;AACA;AACA,MAAMC,aAAa,GAAGA,CAACC,KAAK,EAAEC,UAAU,EAAEC,IAAI,EAAEC,OAAO,GAAG,CAAC,CAAC,KAAK;EAC7D,OAAO,IAAIC,KAAK,CAACJ,KAAK,EAAE;IACpBK,GAAG,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAKL,IAAI,CAACM,iBAAiB,CAAC,MAAM;MAC3C,IAAIC,EAAE;MACN,IAAIT,KAAK,CAACO,IAAI,CAAC,EAAE;QACb,IAAI,CAACE,EAAE,GAAGN,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACO,GAAG,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACJ,GAAG,EAAE;UAClHF,OAAO,CAACO,GAAG,CAACL,GAAG,CAACE,IAAI,EAAEP,KAAK,CAACO,IAAI,CAAC,CAAC;QACtC;QACA,OAAOP,KAAK,CAACO,IAAI,CAAC;MACtB;MACA,IAAIT,aAAa,CAACa,OAAO,CAACJ,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;QAClC,OAAO,MAAM,CACb,CAAC;MACL;MACA,MAAMK,OAAO,GAAGX,UAAU,CAACY,SAAS,CAAC,CAAC,CAACC,IAAI,CAACC,GAAG,IAAI;QAC/C,MAAMC,GAAG,GAAGD,GAAG,IAAIA,GAAG,CAACR,IAAI,CAAC;QAC5B;QACA,IAAI,OAAOS,GAAG,KAAK,UAAU,EAAE;UAC3B,OAAOA,GAAG,CAACC,IAAI,CAACF,GAAG,CAAC;QACxB,CAAC,MACI,IAAIC,GAAG,IAAIA,GAAG,CAACF,IAAI,EAAE;UACtB,OAAOE,GAAG,CAACF,IAAI,CAAEI,GAAG,IAAKhB,IAAI,CAACiB,GAAG,CAAC,MAAMD,GAAG,CAAC,CAAC;QACjD,CAAC,MACI;UACD,OAAOhB,IAAI,CAACiB,GAAG,CAAC,MAAMH,GAAG,CAAC;QAC9B;MACJ,CAAC,CAAC;MACF;MACA,OAAO,IAAIZ,KAAK,CAAC,MAAM,CAAE,CAAC,EAAE;QACxBC,GAAG,EAAEA,CAACC,CAAC,EAAEC,IAAI,KAAKK,OAAO,CAACL,IAAI,CAAC;QAC/B;QACAa,KAAK,EAAEA,CAACC,IAAI,EAAEf,CAAC,EAAEgB,IAAI,KAAKV,OAAO,CAACE,IAAI,CAACS,EAAE,IAAI;UACzC,IAAId,EAAE;UACN,MAAMS,GAAG,GAAGK,EAAE,IAAIA,EAAE,CAAC,GAAGD,IAAI,CAAC;UAC7B,IAAI,CAACb,EAAE,GAAGN,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACO,GAAG,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACW,KAAK,EAAE;YACpHjB,OAAO,CAACO,GAAG,CAACU,KAAK,CAACb,IAAI,EAAEe,IAAI,EAAEJ,GAAG,CAAC;UACtC;UACA,OAAOA,GAAG;QACd,CAAC;MACL,CAAC,CAAC;IACN,CAAC;EACL,CAAC,CAAC;AACN,CAAC;AACD,MAAMM,YAAY,GAAGA,CAACC,WAAW,EAAEC,YAAY,KAAK;EAChDA,YAAY,CAACC,OAAO,CAAEC,QAAQ,IAAK;IAC/BC,MAAM,CAACC,mBAAmB,CAACF,QAAQ,CAACG,SAAS,IAAIH,QAAQ,CAAC,CAACD,OAAO,CAAEpB,IAAI,IAAK;MACzEsB,MAAM,CAACG,cAAc,CAACP,WAAW,CAACM,SAAS,EAAExB,IAAI,EAAEsB,MAAM,CAACI,wBAAwB,CAACL,QAAQ,CAACG,SAAS,IAAIH,QAAQ,EAAErB,IAAI,CAAC,CAAC;IAC7H,CAAC,CAAC;EACN,CAAC,CAAC;AACN,CAAC;AAED,MAAM2B,WAAW,CAAC;EACdC,WAAWA,CAACC,GAAG,EAAE;IACb,OAAOA,GAAG;EACd;AACJ;AAEA,MAAMC,gBAAgB,GAAG,IAAIjD,cAAc,CAAC,0BAA0B,CAAC;AACvE,MAAMkD,iBAAiB,GAAG,IAAIlD,cAAc,CAAC,uBAAuB,CAAC;AACrE,SAASmD,mBAAmBA,CAACpC,OAAO,EAAED,IAAI,EAAEsC,YAAY,EAAE;EACtD,MAAMjC,IAAI,GAAG,OAAOiC,YAAY,KAAK,QAAQ,IAAIA,YAAY,IAAI,WAAW;EAC5E,MAAMC,MAAM,GAAG,OAAOD,YAAY,KAAK,QAAQ,IAAIA,YAAY,IAAI,CAAC,CAAC;EACrEC,MAAM,CAAClC,IAAI,GAAGkC,MAAM,CAAClC,IAAI,IAAIA,IAAI;EACjC;EACA,MAAMmC,WAAW,GAAG7C,QAAQ,CAAC8C,IAAI,CAACC,MAAM,CAACR,GAAG,IAAIA,GAAG,IAAIA,GAAG,CAAC7B,IAAI,KAAKkC,MAAM,CAAClC,IAAI,CAAC,CAAC,CAAC,CAAC;EACnF;EACA;EACA,MAAM6B,GAAG,GAAIM,WAAW,IAAIxC,IAAI,CAACM,iBAAiB,CAAC,MAAMX,QAAQ,CAACgD,aAAa,CAAC1C,OAAO,EAAEsC,MAAM,CAAC,CAAE;EAClG,IAAI;IACA,IAAIK,IAAI,CAACC,SAAS,CAAC5C,OAAO,CAAC,KAAK2C,IAAI,CAACC,SAAS,CAACX,GAAG,CAACjC,OAAO,CAAC,EAAE;MACzD,MAAM6C,GAAG,GAAG,CAAC,CAACC,MAAM,CAACC,GAAG;MACxBC,KAAK,CAAC,OAAO,EAAE,GAAGf,GAAG,CAAC7B,IAAI,2DAA2DyC,GAAG,GAAG,wDAAwD,GAAG,GAAG,EAAE,CAAC;IAChK;EACJ,CAAC,CACD,OAAOI,CAAC,EAAE,CAAE;EACZ,OAAO,IAAIlB,WAAW,CAACE,GAAG,CAAC;AAC/B;AACA,MAAMe,KAAK,GAAGA,CAACE,KAAK,EAAE,GAAG/B,IAAI,KAAK;EAC9B,IAAIjC,SAAS,CAAC,CAAC,IAAI,OAAOiE,OAAO,KAAK,WAAW,EAAE;IAC/CA,OAAO,CAACD,KAAK,CAAC,CAAC,GAAG/B,IAAI,CAAC;EAC3B;AACJ,CAAC;AACD,MAAMiC,qBAAqB,GAAG;EAC1BC,OAAO,EAAEtB,WAAW;EACpBuB,UAAU,EAAElB,mBAAmB;EAC/BmB,IAAI,EAAE,CACFrB,gBAAgB,EAChB/C,MAAM,EACN,CAAC,IAAIC,QAAQ,CAAC,CAAC,EAAE+C,iBAAiB,CAAC;AAE3C,CAAC;AACD,MAAMqB,iBAAiB,CAAC;EACpB;EACAxB,WAAWA,CAACyB,UAAU,EAAE;IACpB/D,QAAQ,CAACgE,eAAe,CAAC,aAAa,EAAErE,OAAO,CAACsE,IAAI,EAAE,MAAM,CAAC;IAC7DjE,QAAQ,CAACgE,eAAe,CAAC,aAAa,EAAErE,OAAO,CAACsE,IAAI,EAAE,YAAY,CAAC;IACnEjE,QAAQ,CAACgE,eAAe,CAAC,SAAS,EAAEpE,SAAS,CAACqE,IAAI,EAAEF,UAAU,CAACG,QAAQ,CAAC,CAAC,CAAC;EAC9E;EACA,OAAOlB,aAAaA,CAAC1C,OAAO,EAAEqC,YAAY,EAAE;IACxC,OAAO;MACHwB,QAAQ,EAAEL,iBAAiB;MAC3BM,SAAS,EAAE,CACP;QAAET,OAAO,EAAEnB,gBAAgB;QAAE6B,QAAQ,EAAE/D;MAAQ,CAAC,EAChD;QAAEqD,OAAO,EAAElB,iBAAiB;QAAE4B,QAAQ,EAAE1B;MAAa,CAAC;IAE9D,CAAC;EACL;AACJ;AACAmB,iBAAiB,CAACQ,IAAI,YAAAC,0BAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFV,iBAAiB,EAA3BxE,EAAE,CAAAmF,QAAA,CAA2C5E,WAAW;AAAA,CAA2C;AACvMiE,iBAAiB,CAACY,IAAI,kBAD8EpF,EAAE,CAAAqF,gBAAA;EAAAC,IAAA,EACSd;AAAiB,EAAG;AACnIA,iBAAiB,CAACe,IAAI,kBAF8EvF,EAAE,CAAAwF,gBAAA;EAAAV,SAAA,EAEuC,CAACV,qBAAqB;AAAC,EAAG;AACvK;EAAA,QAAAqB,SAAA,oBAAAA,SAAA,KAHoGzF,EAAE,CAAA0F,iBAAA,CAGXlB,iBAAiB,EAAc,CAAC;IAC/Gc,IAAI,EAAE9E,QAAQ;IACd2B,IAAI,EAAE,CAAC;MACC2C,SAAS,EAAE,CAACV,qBAAqB;IACrC,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEkB,IAAI,EAAE5C,MAAM;MAAEiD,UAAU,EAAE,CAAC;QAC3DL,IAAI,EAAE7E,MAAM;QACZ0B,IAAI,EAAE,CAAC5B,WAAW;MACtB,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,SAASqF,cAAcA,CAACC,QAAQ,EAAEC,UAAU,EAAEC,OAAO,EAAEC,EAAE,EAAEzB,IAAI,EAAE;EAC7D,MAAM,GAAG0B,QAAQ,EAAEC,UAAU,CAAC,GAAGC,UAAU,CAACC,yBAAyB,CAACC,IAAI,CAAEjE,EAAE,IAAKA,EAAE,CAAC,CAAC,CAAC,KAAKyD,QAAQ,CAAC,IAAI,EAAE;EAC5G,IAAII,QAAQ,EAAE;IACV,IAAI,CAACK,QAAQ,CAAC/B,IAAI,EAAE2B,UAAU,CAAC,EAAE;MAC7BK,GAAG,CAAC,OAAO,EAAE,GAAGT,UAAU,mCAAmCC,OAAO,yCAAyCS,MAAM,GAAG,uDAAuD,GAAG,EAAE,EAAE,CAAC;MACrLD,GAAG,CAAC,MAAM,EAAE;QAAEE,EAAE,EAAElC,IAAI;QAAEmC,GAAG,EAAER;MAAW,CAAC,CAAC;IAC9C;IACA,OAAOD,QAAQ;EACnB,CAAC,MACI;IACD,MAAMU,WAAW,GAAGX,EAAE,CAAC,CAAC;IACxBG,UAAU,CAACC,yBAAyB,CAACQ,IAAI,CAAC,CAACf,QAAQ,EAAEc,WAAW,EAAEpC,IAAI,CAAC,CAAC;IACxE,OAAOoC,WAAW;EACtB;AACJ;AACA,SAASL,QAAQA,CAACO,CAAC,EAAEC,CAAC,EAAE;EACpB,IAAI;IACA,OAAOD,CAAC,CAACjC,QAAQ,CAAC,CAAC,KAAKkC,CAAC,CAAClC,QAAQ,CAAC,CAAC;EACxC,CAAC,CACD,OAAOzD,CAAC,EAAE;IACN,OAAO0F,CAAC,KAAKC,CAAC;EAClB;AACJ;AACA,MAAMN,MAAM,GAAG,CAAC,CAAC1C,MAAM,CAACC,GAAG;AAC3B,MAAMwC,GAAG,GAAGA,CAACrC,KAAK,EAAE,GAAG/B,IAAI,KAAK;EAC5B,IAAIjC,SAAS,CAAC,CAAC,IAAI,OAAOiE,OAAO,KAAK,WAAW,EAAE;IAC/CA,OAAO,CAACD,KAAK,CAAC,CAAC,GAAG/B,IAAI,CAAC;EAC3B;AACJ,CAAC;AACDgE,UAAU,CAACC,yBAAyB,KAAKD,UAAU,CAACC,yBAAyB,GAAG,EAAE,CAAC;;AAEnF;AACA;AACA;;AAEA,SAAS5B,iBAAiB,EAAErB,iBAAiB,EAAED,gBAAgB,EAAEH,WAAW,EAAEV,YAAY,EAAEuD,cAAc,EAAExC,mBAAmB,EAAExC,aAAa", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}