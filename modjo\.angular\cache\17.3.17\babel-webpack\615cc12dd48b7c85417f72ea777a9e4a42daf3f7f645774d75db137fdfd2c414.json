{"ast": null, "code": "import { ValidatorDashboardComponent } from './components/validator-dashboard/validator-dashboard.component';\nimport { ValidationListComponent } from './components/validation-list/validation-list.component';\nimport { ValidationHistoryComponent } from './components/validation-history/validation-history.component';\nexport const validatorDashboardRoutes = [{\n  path: '',\n  component: ValidatorDashboardComponent,\n  children: [{\n    path: '',\n    redirectTo: 'pending',\n    pathMatch: 'full'\n  }, {\n    path: 'pending',\n    component: ValidationListComponent\n  }, {\n    path: 'history',\n    component: ValidationHistoryComponent\n  }]\n}];", "map": {"version": 3, "names": ["ValidatorDashboardComponent", "ValidationListComponent", "ValidationHistoryComponent", "validatorDashboardRoutes", "path", "component", "children", "redirectTo", "pathMatch"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\validator-dashboard\\validator-dashboard.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { ValidatorDashboardComponent } from './components/validator-dashboard/validator-dashboard.component';\nimport { ValidationListComponent } from './components/validation-list/validation-list.component';\nimport { ValidationHistoryComponent } from './components/validation-history/validation-history.component';\n\nexport const validatorDashboardRoutes: Routes = [\n  {\n    path: '',\n    component: ValidatorDashboardComponent,\n    children: [\n      { path: '', redirectTo: 'pending', pathMatch: 'full' },\n      { path: 'pending', component: ValidationListComponent },\n      { path: 'history', component: ValidationHistoryComponent }\n    ]\n  }\n];\n"], "mappings": "AACA,SAASA,2BAA2B,QAAQ,gEAAgE;AAC5G,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,0BAA0B,QAAQ,8DAA8D;AAEzG,OAAO,MAAMC,wBAAwB,GAAW,CAC9C;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEL,2BAA2B;EACtCM,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,SAAS;IAAEC,SAAS,EAAE;EAAM,CAAE,EACtD;IAAEJ,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEJ;EAAuB,CAAE,EACvD;IAAEG,IAAI,EAAE,SAAS;IAAEC,SAAS,EAAEH;EAA0B,CAAE;CAE7D,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}