<div class="reward-detail-container" *ngIf="reward$ | async as reward">
  <!-- Header with back button -->
  <div class="detail-header">
    <button mat-icon-button (click)="goBack()" class="back-button">
      <mat-icon>arrow_back</mat-icon>
    </button>
    <h1>Détails de la récompense</h1>
    <button mat-icon-button (click)="shareReward(reward)" class="share-button">
      <mat-icon>share</mat-icon>
    </button>
  </div>

  <!-- Reward image and basic info -->
  <div class="reward-hero slide-up">
    <div class="hero-image">
      <img [src]="reward.imageUrl || 'assets/images/reward-placeholder.jpg'" 
           [alt]="reward.title"
           onerror="this.src='assets/images/reward-placeholder.jpg'">
      
      <!-- Badges overlay -->
      <div class="hero-badges">
        <mat-chip class="category-chip" [color]="'primary'" selected>
          <mat-icon>{{ getCategoryIcon(reward.category) }}</mat-icon>
          {{ getCategoryLabel(reward.category) }}
        </mat-chip>
        
        <mat-chip *ngIf="isExpiringSoon(reward)" 
                 class="expiry-chip" 
                 color="warn" 
                 selected>
          <mat-icon>schedule</mat-icon>
          Expire bientôt
        </mat-chip>
      </div>
    </div>

    <div class="hero-content">
      <div class="reward-title-section">
        <h2>{{ reward.title }}</h2>
        <div class="points-badge">
          <mat-icon>stars</mat-icon>
          <span>{{ reward.pointsRequired }} points</span>
        </div>
      </div>
      
      <p class="reward-description">{{ reward.description }}</p>
      
      <div class="reward-meta">
        <div class="meta-item">
          <mat-icon>store</mat-icon>
          <span>{{ reward.partnerName }}</span>
        </div>
        
        <div class="meta-item">
          <mat-icon>location_on</mat-icon>
          <span>{{ reward.city }}</span>
        </div>
        
        <div class="meta-item">
          <mat-chip [color]="getAvailabilityColor(reward)" selected>
            {{ getAvailabilityText(reward) }}
          </mat-chip>
        </div>
      </div>
    </div>
  </div>

  <!-- Detailed information -->
  <div class="detail-sections fade-in">
    <!-- Terms and conditions -->
    <mat-card class="detail-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>description</mat-icon>
          Conditions d'utilisation
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <p>{{ reward.terms || 'Aucune condition particulière.' }}</p>
        
        <div class="validity-info" *ngIf="reward.validUntil">
          <mat-icon>event</mat-icon>
          <span>Valide jusqu'au {{ formatDate(reward.validUntil) }}</span>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- Partner information -->
    <mat-card class="detail-card" *ngIf="partner$ | async as partner">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>business</mat-icon>
          À propos du partenaire
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="partner-info">
          <h4>{{ partner.name }}</h4>
          <p>{{ partner.description }}</p>
          
          <div class="partner-details">
            <div class="detail-row" *ngIf="partner.address">
              <mat-icon>location_on</mat-icon>
              <span>{{ partner.address }}</span>
              <button mat-icon-button (click)="getDirections(partner)" matTooltip="Obtenir l'itinéraire">
                <mat-icon>directions</mat-icon>
              </button>
            </div>
            
            <div class="detail-row" *ngIf="partner.phone">
              <mat-icon>phone</mat-icon>
              <span>{{ partner.phone }}</span>
              <button mat-icon-button (click)="contactPartner(partner)" matTooltip="Appeler">
                <mat-icon>call</mat-icon>
              </button>
            </div>
            
            <div class="detail-row" *ngIf="partner.email">
              <mat-icon>email</mat-icon>
              <span>{{ partner.email }}</span>
              <button mat-icon-button (click)="contactPartner(partner)" matTooltip="Envoyer un email">
                <mat-icon>mail</mat-icon>
              </button>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>

    <!-- User points status -->
    <mat-card class="detail-card points-status-card" *ngIf="currentUser">
      <mat-card-content>
        <div class="points-status">
          <div class="current-points">
            <mat-icon>account_balance_wallet</mat-icon>
            <div>
              <h4>Vos points actuels</h4>
              <p class="points-value">{{ currentUser.points }} points</p>
            </div>
          </div>
          
          <div class="points-after" *ngIf="canAfford(reward)">
            <mat-icon>trending_down</mat-icon>
            <div>
              <h4>Après échange</h4>
              <p class="points-value">{{ currentUser.points - reward.pointsRequired }} points</p>
            </div>
          </div>
          
          <div class="points-needed" *ngIf="!canAfford(reward)">
            <mat-icon>add_circle</mat-icon>
            <div>
              <h4>Points nécessaires</h4>
              <p class="points-value needed">{{ reward.pointsRequired - currentUser.points }} points</p>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Action buttons -->
  <div class="action-section">
    <div class="action-buttons">
      <button mat-raised-button 
              [color]="canAfford(reward) ? 'primary' : 'basic'"
              [disabled]="!canAfford(reward) || !reward.availableQuantity || isExchanging"
              (click)="exchangeReward(reward)"
              class="exchange-button">
        <mat-spinner diameter="24" *ngIf="isExchanging"></mat-spinner>
        <mat-icon *ngIf="!isExchanging">{{ canAfford(reward) ? 'redeem' : 'lock' }}</mat-icon>
        <span *ngIf="!isExchanging">
          {{ canAfford(reward) ? 'Échanger maintenant' : 'Points insuffisants' }}
        </span>
      </button>
      
      <button mat-stroked-button 
              routerLink="/qr-scanner" 
              class="scan-button"
              *ngIf="!canAfford(reward)">
        <mat-icon>qr_code_scanner</mat-icon>
        Gagner des points
      </button>
    </div>
    
    <p class="exchange-note" *ngIf="canAfford(reward)">
      <mat-icon>info</mat-icon>
      Vous recevrez un code d'échange à présenter au partenaire.
    </p>
  </div>
</div>

<!-- Loading state -->
<div class="loading-container" *ngIf="!(reward$ | async)">
  <mat-card class="loading-card">
    <mat-card-content>
      <div class="loading-content">
        <mat-spinner diameter="48"></mat-spinner>
        <h3>Chargement des détails...</h3>
      </div>
    </mat-card-content>
  </mat-card>
</div>
