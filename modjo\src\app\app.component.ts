import { Component, OnInit } from '@angular/core';
import { Router, NavigationEnd } from '@angular/router';
import { filter, map } from 'rxjs/operators';
import { AuthService } from './core/services/auth.service';

@Component({
  selector: 'app-root',
  templateUrl: './app.component.html',
  styleUrls: ['./app.component.css']
})
export class AppComponent implements OnInit {
  title = 'Modjo';
  currentPageTitle = 'Tableau de bord';

  constructor(
    public authService: AuthService,
    private router: Router
  ) {}

  ngOnInit() {
    // Listen to route changes to update page title
    this.router.events.pipe(
      filter(event => event instanceof NavigationEnd),
      map(() => this.router.url)
    ).subscribe(url => {
      this.updatePageTitle(url);
    });
  }

  getPageTitle(): string {
    return this.currentPageTitle;
  }

  private updatePageTitle(url: string): void {
    const titleMap: { [key: string]: string } = {
      '/dashboard': 'Tableau de bord',
      '/profile': 'Profil',
      '/qr-scanner': 'Scanner QR',
      '/rewards': 'Récompenses',
      '/validation': 'Validation',
      '/admin': 'Administration'
    };

    // Find matching route
    const matchedRoute = Object.keys(titleMap).find(route => url.startsWith(route));
    this.currentPageTitle = matchedRoute ? titleMap[matchedRoute] : 'Modjo';
  }

  async logout(): Promise<void> {
    try {
      await this.authService.logout();
      this.router.navigate(['/auth/login']);
    } catch (error) {
      console.error('Logout error:', error);
    }
  }
}
