export enum PointType {
  SCAN = 'scan',
  VALIDATION = 'validation',
  REWARD_REDEMPTION = 'reward_redemption'
}

export interface PointTransaction {
  id: string;
  userId: string;
  providerId?: string;
  validatorId?: string;
  type: PointType;
  amount: number;
  description: string;
  createdAt: Date;
}

export interface PointHistory {
  userId: string;
  transactions: PointTransaction[];
  totalPoints: number;
}
