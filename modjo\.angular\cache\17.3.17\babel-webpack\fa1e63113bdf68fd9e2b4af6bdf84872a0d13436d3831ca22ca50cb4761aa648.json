{"ast": null, "code": "import { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { RouterModule } from '@angular/router';\nimport { ServiceWorkerModule } from '@angular/service-worker';\n// Firebase - temporarily disabled for initial setup\n// import { AngularFireModule } from '@angular/fire/compat';\n// import { AngularFireAuthModule } from '@angular/fire/compat/auth';\n// import { AngularFirestoreModule } from '@angular/fire/compat/firestore';\n// import { AngularFireStorageModule } from '@angular/fire/compat/storage';\n// Material\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { AppComponent } from './app.component';\nimport { appRoutes } from './app.routes';\nimport { environment } from '../environments/environment';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/service-worker\";\nexport class AppModule {\n  static {\n    this.ɵfac = function AppModule_Factory(t) {\n      return new (t || AppModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AppModule,\n      bootstrap: [AppComponent]\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [BrowserModule, BrowserAnimationsModule, RouterModule.forRoot(appRoutes), ServiceWorkerModule.register('ngsw-worker.js', {\n        enabled: environment.production,\n        registrationStrategy: 'registerWhenStable:30000'\n      }),\n      // Firebase - temporarily disabled for initial setup\n      // AngularFireModule.initializeApp(environment.firebase),\n      // AngularFireAuthModule,\n      // AngularFirestoreModule,\n      // AngularFireStorageModule,\n      // Material modules\n      MatToolbarModule, MatButtonModule, MatIconModule, MatSidenavModule, MatListModule, MatCardModule, MatSnackBarModule, MatMenuModule, MatDividerModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AppModule, {\n    declarations: [AppComponent],\n    imports: [BrowserModule, BrowserAnimationsModule, i1.RouterModule, i2.ServiceWorkerModule,\n    // Firebase - temporarily disabled for initial setup\n    // AngularFireModule.initializeApp(environment.firebase),\n    // AngularFireAuthModule,\n    // AngularFirestoreModule,\n    // AngularFireStorageModule,\n    // Material modules\n    MatToolbarModule, MatButtonModule, MatIconModule, MatSidenavModule, MatListModule, MatCardModule, MatSnackBarModule, MatMenuModule, MatDividerModule]\n  });\n})();", "map": {"version": 3, "names": ["BrowserModule", "BrowserAnimationsModule", "RouterModule", "ServiceWorkerModule", "MatToolbarModule", "MatButtonModule", "MatIconModule", "MatSidenavModule", "MatListModule", "MatCardModule", "MatSnackBarModule", "MatMenuModule", "MatDividerModule", "AppComponent", "appRoutes", "environment", "AppModule", "bootstrap", "forRoot", "register", "enabled", "production", "registrationStrategy", "declarations", "imports", "i1", "i2"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\app.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { RouterModule } from '@angular/router';\nimport { ServiceWorkerModule } from '@angular/service-worker';\n\n// Firebase - temporarily disabled for initial setup\n// import { AngularFireModule } from '@angular/fire/compat';\n// import { AngularFireAuthModule } from '@angular/fire/compat/auth';\n// import { AngularFirestoreModule } from '@angular/fire/compat/firestore';\n// import { AngularFireStorageModule } from '@angular/fire/compat/storage';\n\n// Material\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDividerModule } from '@angular/material/divider';\n\nimport { AppComponent } from './app.component';\nimport { appRoutes } from './app.routes';\nimport { environment } from '../environments/environment';\n\n@NgModule({\n  declarations: [\n    AppComponent\n  ],\n  imports: [\n    BrowserModule,\n    BrowserAnimationsModule,\n    RouterModule.forRoot(appRoutes),\n    ServiceWorkerModule.register('ngsw-worker.js', {\n      enabled: environment.production,\n      registrationStrategy: 'registerWhenStable:30000'\n    }),\n\n    // Firebase - temporarily disabled for initial setup\n    // AngularFireModule.initializeApp(environment.firebase),\n    // AngularFireAuthModule,\n    // AngularFirestoreModule,\n    // AngularFireStorageModule,\n\n    // Material modules\n    MatToolbarModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSidenavModule,\n    MatListModule,\n    MatCardModule,\n    MatSnackBarModule,\n    MatMenuModule,\n    MatDividerModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n"], "mappings": "AACA,SAASA,aAAa,QAAQ,2BAA2B;AACzD,SAASC,uBAAuB,QAAQ,sCAAsC;AAC9E,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,yBAAyB;AAE7D;AACA;AACA;AACA;AACA;AAEA;AACA,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,SAAS,QAAQ,cAAc;AACxC,SAASC,WAAW,QAAQ,6BAA6B;;;;AAmCzD,OAAM,MAAOC,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRJ,YAAY;IAAA;EAAA;;;gBA1BtBb,aAAa,EACbC,uBAAuB,EACvBC,YAAY,CAACgB,OAAO,CAACJ,SAAS,CAAC,EAC/BX,mBAAmB,CAACgB,QAAQ,CAAC,gBAAgB,EAAE;QAC7CC,OAAO,EAAEL,WAAW,CAACM,UAAU;QAC/BC,oBAAoB,EAAE;OACvB,CAAC;MAEF;MACA;MACA;MACA;MACA;MAEA;MACAlB,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB;IAAA;EAAA;;;2EAKPI,SAAS;IAAAO,YAAA,GA/BlBV,YAAY;IAAAW,OAAA,GAGZxB,aAAa,EACbC,uBAAuB,EAAAwB,EAAA,CAAAvB,YAAA,EAAAwB,EAAA,CAAAvB,mBAAA;IAOvB;IACA;IACA;IACA;IACA;IAEA;IACAC,gBAAgB,EAChBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,iBAAiB,EACjBC,aAAa,EACbC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}