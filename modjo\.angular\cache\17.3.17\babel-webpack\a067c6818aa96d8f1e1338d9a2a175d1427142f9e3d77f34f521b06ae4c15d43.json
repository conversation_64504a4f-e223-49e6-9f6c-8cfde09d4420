{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\nimport { ProviderDashboardComponent } from './components/provider-dashboard/provider-dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: ProviderDashboardComponent\n}];\nexport class ProviderDashboardModule {\n  static {\n    this.ɵfac = function ProviderDashboardModule_Factory(t) {\n      return new (t || ProviderDashboardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProviderDashboardModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProviderDashboardModule, {\n    declarations: [ProviderDashboardComponent],\n    imports: [SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SharedModule", "ProviderDashboardComponent", "routes", "path", "component", "ProviderDashboardModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\provider-dashboard\\provider-dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\nimport { SharedModule } from '../../shared/shared.module';\nimport { ProviderDashboardComponent } from './components/provider-dashboard/provider-dashboard.component';\n\nconst routes = [\n  { path: '', component: ProviderDashboardComponent }\n];\n\n@NgModule({\n  declarations: [\n    ProviderDashboardComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class ProviderDashboardModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,0BAA0B,QAAQ,8DAA8D;;;AAEzG,MAAMC,MAAM,GAAG,CACb;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAA0B,CAAE,CACpD;AAWD,OAAM,MAAOI,uBAAuB;;;uBAAvBA,uBAAuB;IAAA;EAAA;;;YAAvBA;IAAuB;EAAA;;;gBAJhCL,YAAY,EACZD,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC;IAAA;EAAA;;;2EAGpBG,uBAAuB;IAAAE,YAAA,GAPhCN,0BAA0B;IAAAO,OAAA,GAG1BR,YAAY,EAAAS,EAAA,CAAAV,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}