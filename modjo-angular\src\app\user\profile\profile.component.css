.profile-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 20px;
}

.loading, .error {
  text-align: center;
  padding: 30px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.error {
  color: #d32f2f;
}

.profile-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.profile-header {
  display: flex;
  padding: 30px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee;
}

.profile-image {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 30px;
  flex-shrink: 0;
}

.profile-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info {
  flex: 1;
}

h1 {
  margin: 0 0 10px 0;
  font-size: 24px;
  color: #333;
}

.role {
  font-size: 16px;
  color: #4a90e2;
  margin: 0 0 5px 0;
}

.location, .activity {
  font-size: 14px;
  color: #666;
  margin: 0 0 5px 0;
}

.points-badge {
  display: inline-block;
  background-color: #4a90e2;
  color: white;
  border-radius: 20px;
  padding: 5px 15px;
  margin-top: 10px;
}

.points-count {
  font-size: 18px;
  font-weight: 700;
  margin-right: 5px;
}

.points-label {
  font-size: 14px;
}

.profile-actions {
  display: flex;
  padding: 20px;
  gap: 20px;
  border-bottom: 1px solid #eee;
}

.action-button {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 15px;
  background-color: #f5f5f5;
  color: #333;
  text-decoration: none;
  border-radius: 8px;
  transition: background-color 0.3s;
}

.action-button:hover {
  background-color: #e0e0e0;
}

.icon {
  font-size: 20px;
  margin-right: 10px;
}

.points-history {
  padding: 20px;
}

h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  color: #333;
}

.transactions {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.transaction-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.transaction-description {
  margin: 0 0 5px 0;
  font-weight: 500;
  color: #333;
}

.transaction-date {
  margin: 0;
  font-size: 12px;
  color: #888;
}

.transaction-points {
  font-weight: 700;
  font-size: 16px;
}

.positive {
  color: #4caf50;
}

.negative {
  color: #f44336;
}

.no-transactions {
  text-align: center;
  padding: 30px;
  color: #666;
}

.start-scanning {
  display: inline-block;
  margin-top: 15px;
  padding: 10px 20px;
  background-color: #4a90e2;
  color: white;
  text-decoration: none;
  border-radius: 4px;
  transition: background-color 0.3s;
}

.start-scanning:hover {
  background-color: #3a7bc8;
}

@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }
  
  .profile-image {
    margin-right: 0;
    margin-bottom: 20px;
  }
  
  .profile-actions {
    flex-direction: column;
  }
}
