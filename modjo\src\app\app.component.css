.content {
  display: flex;
  margin: 82px auto 32px;
  padding: 0 16px;
  max-width: 960px;
  flex-direction: column;
  align-items: center;
}

h1 {
  color: #3f51b5;
  margin-bottom: 8px;
}

.card-container {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  margin-top: 16px;
}

.card {
  all: unset;
  border-radius: 4px;
  border: 1px solid #eee;
  background-color: #fafafa;
  height: 200px;
  width: 200px;
  margin: 0 8px 16px;
  padding: 16px;
  display: flex;
  flex-direction: column;
  transition: all 0.2s ease-in-out;
  line-height: 24px;
}

.card:hover {
  transform: translateY(-3px);
  box-shadow: 0 4px 17px rgba(0, 0, 0, 0.35);
}

.card h2 {
  margin: 8px 0;
  color: #3f51b5;
}

.card ul {
  list-style: none;
  padding-left: 0;
}

.card li {
  margin-bottom: 8px;
}

.card a {
  color: #3f51b5;
  text-decoration: none;
}

.card a:hover {
  text-decoration: underline;
}
