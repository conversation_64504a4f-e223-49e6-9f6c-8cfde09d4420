.app-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
}

.sidenav-container {
  flex: 1;
}

.sidenav {
  width: 280px;
}

.sidenav-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 0 16px;
}

.logo {
  width: 32px;
  height: 32px;
}

.app-name {
  font-size: 1.2rem;
  font-weight: 500;
}

.main-toolbar {
  position: sticky;
  top: 0;
  z-index: 1000;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.menu-button {
  margin-right: 16px;
}

.toolbar-title {
  font-size: 1.1rem;
  font-weight: 500;
}

.spacer {
  flex: 1 1 auto;
}

.points-display {
  display: flex;
  align-items: center;
  gap: 4px;
  background: rgba(255, 255, 255, 0.1);
  padding: 4px 12px;
  border-radius: 16px;
  margin-right: 16px;
}

.points-display mat-icon {
  color: #ffd700;
  font-size: 18px;
  width: 18px;
  height: 18px;
}

.user-avatar {
  margin-left: 8px;
}

.main-content {
  padding: 16px;
  min-height: calc(100vh - 64px);
  background-color: #f5f5f5;
}

.auth-layout {
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

/* Navigation active state */
.mat-mdc-list-item.active {
  background-color: rgba(103, 126, 234, 0.1);
  color: #667eea;
}

.mat-mdc-list-item.active mat-icon {
  color: #667eea;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .main-content {
    padding: 8px;
  }

  .toolbar-title {
    font-size: 1rem;
  }

  .points-display {
    padding: 2px 8px;
    font-size: 0.9rem;
  }
}
