export interface AdminStats {
  totalUsers: number;
  activeUsers: number;
  newUsersThisMonth: number;
  totalPoints: number;
  pointsDistributedThisMonth: number;
  pointsRedeemedThisMonth: number;
  totalValidations: number;
  pendingValidations: number;
  totalPartners: number;
  activePartners: number;
  totalProviders: number;
  activeProviders: number;
  totalValidators: number;
  activeValidators: number;
}

export interface UserManagement {
  id: string;
  email: string;
  name: string;
  role: UserRole;
  city: 'Monastir' | 'Sousse';
  points: number;
  isActive: boolean;
  lastLoginAt?: Date;
  createdAt: Date;
  totalValidations?: number;
  totalRedemptions?: number;
  organizationName?: string; // Pour providers/partners
}

export interface SystemConfiguration {
  id: string;
  key: string;
  value: string | number | boolean;
  description: string;
  category: ConfigCategory;
  updatedAt: Date;
  updatedBy: string;
}

export enum ConfigCategory {
  POINTS = 'points',
  VALIDATION = 'validation',
  REWARDS = 'rewards',
  SECURITY = 'security',
  NOTIFICATIONS = 'notifications',
  GENERAL = 'general'
}

export interface PointsConfiguration {
  maxPointsPerValidation: number;
  maxValidationsPerDay: number;
  maxQrScansPerDay: number;
  pointsExpirationDays: number;
  minRedemptionPoints: number;
  bonusMultiplier: number;
}

export interface ValidationConfiguration {
  autoApprovalThreshold: number;
  maxPendingDays: number;
  requireEvidence: boolean;
  maxValidationsPerValidator: number;
  validationCooldown: number; // en heures
}

export interface GlobalStats {
  totalPointsInCirculation: number;
  totalPointsDistributed: number;
  totalPointsRedeemed: number;
  averageUserPoints: number;
  mostActiveCity: 'Monastir' | 'Sousse';
  topValidators: TopValidator[];
  topPartners: TopPartner[];
  topProviders: TopProvider[];
  monthlyGrowth: MonthlyGrowth[];
}

export interface TopValidator {
  id: string;
  name: string;
  organizationName: string;
  totalValidations: number;
  averageResponseTime: number; // en heures
  approvalRate: number; // pourcentage
}

export interface TopPartner {
  id: string;
  businessName: string;
  totalRedemptions: number;
  totalPointsGenerated: number;
  customerSatisfaction: number; // pourcentage
  activeRewards: number;
}

export interface TopProvider {
  id: string;
  organizationName: string;
  totalScans: number;
  totalPointsDistributed: number;
  uniqueUsers: number;
  engagementRate: number; // pourcentage
}

export interface MonthlyGrowth {
  month: string; // YYYY-MM
  newUsers: number;
  pointsDistributed: number;
  pointsRedeemed: number;
  validations: number;
  redemptions: number;
}

export interface AuditLog {
  id: string;
  userId: string;
  userName: string;
  action: AuditAction;
  entityType: EntityType;
  entityId: string;
  oldValue?: any;
  newValue?: any;
  timestamp: Date;
  ipAddress?: string;
  userAgent?: string;
}

export enum AuditAction {
  CREATE = 'create',
  UPDATE = 'update',
  DELETE = 'delete',
  LOGIN = 'login',
  LOGOUT = 'logout',
  VALIDATE = 'validate',
  REDEEM = 'redeem',
  SCAN = 'scan',
  APPROVE = 'approve',
  REJECT = 'reject',
  SUSPEND = 'suspend',
  ACTIVATE = 'activate'
}

export enum EntityType {
  USER = 'user',
  VALIDATION = 'validation',
  REWARD = 'reward',
  QR_CODE = 'qr_code',
  PARTNER = 'partner',
  PROVIDER = 'provider',
  CONFIGURATION = 'configuration'
}

export interface CreateAdminUserRequest {
  email: string;
  name: string;
  role: UserRole;
  city: 'Monastir' | 'Sousse';
  phone?: string;
  organizationName?: string; // Pour providers/partners
  organizationType?: string;
}

export interface UpdateAdminUserRequest {
  name?: string;
  role?: UserRole;
  city?: 'Monastir' | 'Sousse';
  phone?: string;
  isActive?: boolean;
  points?: number;
}

import { UserRole } from './user.model';
