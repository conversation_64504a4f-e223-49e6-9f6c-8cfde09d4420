{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { QrScannerComponent } from './components/qr-scanner/qr-scanner.component';\nimport { PointsAssignmentComponent } from './components/points-assignment/points-assignment.component';\nimport { qrScannerRoutes } from './qr-scanner.routes';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class QrScannerModule {\n  static {\n    this.ɵfac = function QrScannerModule_Factory(t) {\n      return new (t || QrScannerModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: QrScannerModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, RouterModule.forChild(qrScannerRoutes),\n      // Material modules\n      MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatDialogModule, MatSnackBarModule, MatProgressSpinnerModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(QrScannerModule, {\n    declarations: [QrScannerComponent, PointsAssignmentComponent],\n    imports: [CommonModule, ReactiveFormsModule, i1.RouterModule,\n    // Material modules\n    MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatDialogModule, MatSnackBarModule, MatProgressSpinnerModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatDialogModule", "MatSnackBarModule", "MatProgressSpinnerModule", "QrScannerComponent", "PointsAssignmentComponent", "qrScannerRoutes", "QrScannerModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\qr-scanner\\qr-scanner.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\n\nimport { QrScannerComponent } from './components/qr-scanner/qr-scanner.component';\nimport { PointsAssignmentComponent } from './components/points-assignment/points-assignment.component';\nimport { qrScannerRoutes } from './qr-scanner.routes';\n\n@NgModule({\n  declarations: [\n    QrScannerComponent,\n    PointsAssignmentComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule.forChild(qrScannerRoutes),\n    \n    // Material modules\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule\n  ]\n})\nexport class QrScannerModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,wBAAwB,QAAQ,oCAAoC;AAE7E,SAASC,kBAAkB,QAAQ,8CAA8C;AACjF,SAASC,yBAAyB,QAAQ,4DAA4D;AACtG,SAASC,eAAe,QAAQ,qBAAqB;;;AAwBrD,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAhBxBf,YAAY,EACZC,mBAAmB,EACnBC,YAAY,CAACc,QAAQ,CAACF,eAAe,CAAC;MAEtC;MACAX,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB;IAAA;EAAA;;;2EAGfI,eAAe;IAAAE,YAAA,GApBxBL,kBAAkB,EAClBC,yBAAyB;IAAAK,OAAA,GAGzBlB,YAAY,EACZC,mBAAmB,EAAAkB,EAAA,CAAAjB,YAAA;IAGnB;IACAC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}