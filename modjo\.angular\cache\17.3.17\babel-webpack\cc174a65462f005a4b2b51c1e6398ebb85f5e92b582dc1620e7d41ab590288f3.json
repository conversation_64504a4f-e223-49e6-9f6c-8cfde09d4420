{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { UserRole } from '../../../../core/models';\nlet DashboardComponent = class DashboardComponent {\n  constructor(authService, router, dashboardRouter) {\n    this.authService = authService;\n    this.router = router;\n    this.dashboardRouter = dashboardRouter;\n    this.user = null;\n    // Community data\n    this.communityFeed = [];\n    this.topContributors = [];\n    this.localEvents = [];\n    this.featuredPartners = [];\n    this.activeChallenges = [];\n    // City comparison data\n    this.cityComparison = {\n      monastir: {\n        score: 0,\n        participants: 0\n      },\n      sousse: {\n        score: 0,\n        participants: 0\n      }\n    };\n    this.comparisonStats = [{\n      name: 'Actions validées',\n      monastir: 0,\n      sousse: 0,\n      monastirValue: 0,\n      sousseValue: 0\n    }, {\n      name: 'Événements organisés',\n      monastir: 0,\n      sousse: 0,\n      monastirValue: 0,\n      sousseValue: 0\n    }, {\n      name: 'Partenaires actifs',\n      monastir: 0,\n      sousse: 0,\n      monastirValue: 0,\n      sousseValue: 0\n    }];\n    this.actionCategories = [{\n      title: 'Environnement',\n      actions: [{\n        title: 'Nettoyage plage',\n        icon: 'waves',\n        color: 'primary',\n        points: 15\n      }, {\n        title: 'Plantation arbres',\n        icon: 'park',\n        color: 'primary',\n        points: 20\n      }, {\n        title: 'Recyclage',\n        icon: 'recycling',\n        color: 'primary',\n        points: 10\n      }]\n    }, {\n      title: 'Social',\n      actions: [{\n        title: 'Aide personnes âgées',\n        icon: 'elderly',\n        color: 'accent',\n        points: 25\n      }, {\n        title: 'Cours bénévoles',\n        icon: 'school',\n        color: 'accent',\n        points: 30\n      }, {\n        title: 'Distribution repas',\n        icon: 'restaurant',\n        color: 'accent',\n        points: 20\n      }]\n    }, {\n      title: 'Culture',\n      actions: [{\n        title: 'Guide touristique',\n        icon: 'tour',\n        color: 'warn',\n        points: 15\n      }, {\n        title: 'Animation enfants',\n        icon: 'child_care',\n        color: 'warn',\n        points: 18\n      }, {\n        title: 'Événement culturel',\n        icon: 'theater_comedy',\n        color: 'warn',\n        points: 25\n      }]\n    }];\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        // Rediriger vers le dashboard approprié selon le rôle\n        if (user.role !== UserRole.USER) {\n          this.dashboardRouter.navigateToUserDashboard(user);\n          return;\n        }\n        this.loadDashboardData();\n      }\n    });\n  }\n  loadDashboardData() {\n    this.loadCommunityFeed();\n    this.loadTopContributors();\n    this.loadLocalEvents();\n    this.loadFeaturedPartners();\n    this.loadActiveChallenges();\n    this.loadCityComparison();\n  }\n  loadCommunityFeed() {\n    // Simulate real community activity feed\n    this.communityFeed = [{\n      id: '1',\n      description: 'Ahmed a nettoyé la plage de Monastir',\n      points: 15,\n      type: 'environment',\n      timestamp: new Date(Date.now() - 1000 * 60 * 30),\n      userId: 'user1',\n      userName: 'Ahmed'\n    }, {\n      id: '2',\n      description: 'Fatma a aidé des personnes âgées à Sousse',\n      points: 25,\n      type: 'social',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60),\n      userId: 'user2',\n      userName: 'Fatma'\n    }, {\n      id: '3',\n      description: 'Mohamed a organisé un cours de français',\n      points: 30,\n      type: 'education',\n      timestamp: new Date(Date.now() - 1000 * 60 * 90),\n      userId: 'user3',\n      userName: 'Mohamed'\n    }, {\n      id: '4',\n      description: 'Leila a guidé des touristes au Ribat',\n      points: 15,\n      type: 'culture',\n      timestamp: new Date(Date.now() - 1000 * 60 * 120),\n      userId: 'user4',\n      userName: 'Leila'\n    }];\n  }\n  loadTopContributors() {\n    this.topContributors = [{\n      uid: 'top1',\n      name: 'Ahmed Ben Ali',\n      city: 'Monastir',\n      points: 1250,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }, {\n      uid: 'top2',\n      name: 'Fatma Trabelsi',\n      city: 'Sousse',\n      points: 1180,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }, {\n      uid: 'top3',\n      name: 'Mohamed Gharbi',\n      city: 'Monastir',\n      points: 1050,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }, {\n      uid: 'top4',\n      name: 'Leila Mansouri',\n      city: 'Sousse',\n      points: 980,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }, {\n      uid: 'top5',\n      name: 'Karim Bouazizi',\n      city: 'Monastir',\n      points: 920,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }];\n  }\n  loadLocalEvents() {\n    this.localEvents = [{\n      id: 'event1',\n      title: 'Nettoyage de la Plage de Monastir',\n      description: 'Rejoignez-nous pour nettoyer notre belle plage',\n      date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2),\n      location: 'Plage de Monastir',\n      category: 'Environnement',\n      points: 20,\n      image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=200&fit=crop',\n      participants: 45,\n      maxParticipants: 100\n    }, {\n      id: 'event2',\n      title: 'Festival Culturel de Sousse',\n      description: 'Célébrons notre patrimoine culturel ensemble',\n      date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5),\n      location: 'Médina de Sousse',\n      category: 'Culture',\n      points: 15,\n      image: 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=400&h=200&fit=crop',\n      participants: 120,\n      maxParticipants: 200\n    }, {\n      id: 'event3',\n      title: 'Cours de Français pour Réfugiés',\n      description: 'Aidez à enseigner le français aux nouveaux arrivants',\n      date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7),\n      location: 'Centre Communautaire',\n      category: 'Éducation',\n      points: 30,\n      image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=200&fit=crop',\n      participants: 15,\n      maxParticipants: 30\n    }];\n  }\n  loadFeaturedPartners() {\n    this.featuredPartners = [{\n      id: 'partner1',\n      name: 'Café des Nattes',\n      category: 'Restaurant',\n      logo: 'https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=100&h=100&fit=crop',\n      rating: 4.5,\n      reviews: 127,\n      currentOffer: '10% de réduction avec 50 points',\n      location: 'Médina de Monastir',\n      qrCode: 'PARTNER_CAFE_NATTES'\n    }, {\n      id: 'partner2',\n      name: 'Boutique Artisanat Sousse',\n      category: 'Artisanat',\n      logo: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop',\n      rating: 4.8,\n      reviews: 89,\n      currentOffer: 'Produit gratuit avec 100 points',\n      location: 'Médina de Sousse',\n      qrCode: 'PARTNER_ARTISANAT_SOUSSE'\n    }, {\n      id: 'partner3',\n      name: 'Hammam Traditionnel',\n      category: 'Bien-être',\n      logo: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=100&h=100&fit=crop',\n      rating: 4.3,\n      reviews: 156,\n      currentOffer: 'Séance gratuite avec 75 points',\n      location: 'Centre-ville Monastir',\n      qrCode: 'PARTNER_HAMMAM'\n    }];\n  }\n  loadActiveChallenges() {\n    this.activeChallenges = [{\n      id: 'challenge1',\n      title: 'Éco-Warrior',\n      description: 'Participez à 5 actions environnementales ce mois-ci',\n      icon: 'eco',\n      gradient: 'linear-gradient(135deg, #4CAF50, #8BC34A)',\n      progress: 60,\n      current: 3,\n      target: 5,\n      reward: 'Badge Éco-Warrior + 100 points bonus',\n      endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 15) // 15 days\n    }, {\n      id: 'challenge2',\n      title: 'Ambassadeur Culturel',\n      description: 'Guidez 10 touristes dans votre ville',\n      icon: 'tour',\n      gradient: 'linear-gradient(135deg, #FF9800, #FFC107)',\n      progress: 30,\n      current: 3,\n      target: 10,\n      reward: 'Badge Ambassadeur + Visite gratuite musée',\n      endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 20) // 20 days\n    }, {\n      id: 'challenge3',\n      title: 'Solidarité Communautaire',\n      description: 'Aidez 15 personnes dans le besoin',\n      icon: 'volunteer_activism',\n      gradient: 'linear-gradient(135deg, #E91E63, #F06292)',\n      progress: 80,\n      current: 12,\n      target: 15,\n      reward: 'Badge Solidarité + 200 points bonus',\n      endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 10) // 10 days\n    }];\n  }\n  loadCityComparison() {\n    // Simulate city competition data\n    this.cityComparison = {\n      monastir: {\n        score: 15420,\n        participants: 342\n      },\n      sousse: {\n        score: 16180,\n        participants: 389\n      }\n    };\n    this.comparisonStats = [{\n      name: 'Actions validées',\n      monastir: 65,\n      sousse: 78,\n      monastirValue: 1250,\n      sousseValue: 1520\n    }, {\n      name: 'Événements organisés',\n      monastir: 45,\n      sousse: 52,\n      monastirValue: 23,\n      sousseValue: 27\n    }, {\n      name: 'Partenaires actifs',\n      monastir: 80,\n      sousse: 70,\n      monastirValue: 16,\n      sousseValue: 14\n    }];\n  }\n  // UI Helper Methods\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n  getCityIcon() {\n    if (!this.user) return 'location_city';\n    return this.user.city === 'Monastir' ? 'account_balance' : 'museum';\n  }\n  getCommunityRank() {\n    if (!this.user) return 0;\n    const userIndex = this.topContributors.findIndex(u => u.uid === this.user.uid);\n    return userIndex >= 0 ? userIndex + 1 : this.topContributors.length + 1;\n  }\n  getImpactScore() {\n    if (!this.user) return 0;\n    // Calculate impact based on points and community actions\n    return Math.floor(this.user.points * 1.2 + (this.user.history?.length || 0) * 5);\n  }\n  getCityProgress() {\n    if (!this.user) return 0;\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    const monthlyTarget = 20000;\n    return Math.min(cityData.score / monthlyTarget * 314, 314); // 314 = 2π * 50 (circle circumference)\n  }\n  getCityProgressPercent() {\n    if (!this.user) return 0;\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    const monthlyTarget = 20000;\n    return Math.min(Math.round(cityData.score / monthlyTarget * 100), 100);\n  }\n  getCityStats() {\n    if (!this.user) return {\n      validatedActions: 0,\n      activeUsers: 0\n    };\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    return {\n      validatedActions: Math.floor(cityData.score / 15),\n      activeUsers: cityData.participants\n    };\n  }\n  getActivityColor(type) {\n    const colorMap = {\n      'environment': '#4CAF50',\n      'social': '#E91E63',\n      'education': '#2196F3',\n      'culture': '#FF9800',\n      'health': '#9C27B0'\n    };\n    return colorMap[type] || '#667eea';\n  }\n  getActivityIcon(type) {\n    const iconMap = {\n      'environment': 'eco',\n      'social': 'volunteer_activism',\n      'education': 'school',\n      'culture': 'theater_comedy',\n      'health': 'health_and_safety'\n    };\n    return iconMap[type] || 'circle';\n  }\n  getTrophyColor(index) {\n    const colors = ['#FFD700', '#C0C0C0', '#CD7F32']; // Gold, Silver, Bronze\n    return colors[index] || '#667eea';\n  }\n  getTrophyIcon(index) {\n    return index < 3 ? 'emoji_events' : 'star';\n  }\n  getStars(rating) {\n    return Array(Math.floor(rating)).fill(0);\n  }\n  // Action Methods\n  viewEvent(event) {\n    // Navigate to event details or open modal\n    console.log('Viewing event:', event);\n  }\n  viewPartner(partner) {\n    // Navigate to partner details or open modal\n    console.log('Viewing partner:', partner);\n  }\n  executeAction(action) {\n    // Execute the selected action\n    console.log('Executing action:', action);\n    // This would typically open a form or navigate to action details\n  }\n  viewAllActivity() {\n    this.router.navigate(['/community/activity']);\n  }\n};\nDashboardComponent = __decorate([Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})], DashboardComponent);\nexport { DashboardComponent };", "map": {"version": 3, "names": ["Component", "UserRole", "DashboardComponent", "constructor", "authService", "router", "dashboardRouter", "user", "communityFeed", "topContributors", "localEvents", "featuredPartners", "activeChallenges", "cityComparison", "monastir", "score", "participants", "sousse", "comparisonStats", "name", "monastirValue", "sousseValue", "actionCategories", "title", "actions", "icon", "color", "points", "ngOnInit", "currentUser$", "subscribe", "role", "USER", "navigateToUserDashboard", "loadDashboardData", "loadCommunityFeed", "loadTopContributors", "loadLocalEvents", "loadFeaturedPartners", "loadActiveChallenges", "loadCityComparison", "id", "description", "type", "timestamp", "Date", "now", "userId", "userName", "uid", "city", "email", "createdAt", "updatedAt", "isActive", "history", "date", "location", "category", "image", "maxParticipants", "logo", "rating", "reviews", "currentOffer", "qrCode", "gradient", "progress", "current", "target", "reward", "endDate", "getGreeting", "hour", "getHours", "getCityIcon", "getCommunityRank", "userIndex", "findIndex", "u", "length", "getImpactScore", "Math", "floor", "getCityProgress", "cityData", "monthlyTarget", "min", "getCityProgressPercent", "round", "getCityStats", "validatedActions", "activeUsers", "getActivityColor", "colorMap", "getActivityIcon", "iconMap", "getTrophyColor", "index", "colors", "getTrophyIcon", "getStars", "Array", "fill", "viewEvent", "event", "console", "log", "viewP<PERSON>ner", "partner", "executeAction", "action", "viewAllActivity", "navigate", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\components\\dashboard\\dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { DashboardRouterService } from '../../../../core/services/dashboard-router.service';\nimport { User, UserRole } from '../../../../core/models';\n\ninterface CommunityActivity {\n  id: string;\n  description: string;\n  points: number;\n  type: string;\n  timestamp: Date;\n  userId: string;\n  userName: string;\n}\n\ninterface LocalEvent {\n  id: string;\n  title: string;\n  description: string;\n  date: Date;\n  location: string;\n  category: string;\n  points: number;\n  image: string;\n  participants: number;\n  maxParticipants: number;\n}\n\ninterface Partner {\n  id: string;\n  name: string;\n  category: string;\n  logo: string;\n  rating: number;\n  reviews: number;\n  currentOffer: string;\n  location: string;\n  qrCode: string;\n}\n\ninterface Challenge {\n  id: string;\n  title: string;\n  description: string;\n  icon: string;\n  gradient: string;\n  progress: number;\n  current: number;\n  target: number;\n  reward: string;\n  endDate: Date;\n}\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit {\n  user: User | null = null;\n\n  // Community data\n  communityFeed: CommunityActivity[] = [];\n  topContributors: User[] = [];\n  localEvents: LocalEvent[] = [];\n  featuredPartners: Partner[] = [];\n  activeChallenges: Challenge[] = [];\n\n  // City comparison data\n  cityComparison = {\n    monastir: { score: 0, participants: 0 },\n    sousse: { score: 0, participants: 0 }\n  };\n\n  comparisonStats = [\n    { name: 'Actions validées', monastir: 0, sousse: 0, monastirValue: 0, sousseValue: 0 },\n    { name: 'Événements organisés', monastir: 0, sousse: 0, monastirValue: 0, sousseValue: 0 },\n    { name: 'Partenaires actifs', monastir: 0, sousse: 0, monastirValue: 0, sousseValue: 0 }\n  ];\n\n  actionCategories = [\n    {\n      title: 'Environnement',\n      actions: [\n        { title: 'Nettoyage plage', icon: 'waves', color: 'primary', points: 15 },\n        { title: 'Plantation arbres', icon: 'park', color: 'primary', points: 20 },\n        { title: 'Recyclage', icon: 'recycling', color: 'primary', points: 10 }\n      ]\n    },\n    {\n      title: 'Social',\n      actions: [\n        { title: 'Aide personnes âgées', icon: 'elderly', color: 'accent', points: 25 },\n        { title: 'Cours bénévoles', icon: 'school', color: 'accent', points: 30 },\n        { title: 'Distribution repas', icon: 'restaurant', color: 'accent', points: 20 }\n      ]\n    },\n    {\n      title: 'Culture',\n      actions: [\n        { title: 'Guide touristique', icon: 'tour', color: 'warn', points: 15 },\n        { title: 'Animation enfants', icon: 'child_care', color: 'warn', points: 18 },\n        { title: 'Événement culturel', icon: 'theater_comedy', color: 'warn', points: 25 }\n      ]\n    }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private dashboardRouter: DashboardRouterService\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        // Rediriger vers le dashboard approprié selon le rôle\n        if (user.role !== UserRole.USER) {\n          this.dashboardRouter.navigateToUserDashboard(user);\n          return;\n        }\n        this.loadDashboardData();\n      }\n    });\n  }\n\n  private loadDashboardData(): void {\n    this.loadCommunityFeed();\n    this.loadTopContributors();\n    this.loadLocalEvents();\n    this.loadFeaturedPartners();\n    this.loadActiveChallenges();\n    this.loadCityComparison();\n  }\n\n  private loadCommunityFeed(): void {\n    // Simulate real community activity feed\n    this.communityFeed = [\n      {\n        id: '1',\n        description: 'Ahmed a nettoyé la plage de Monastir',\n        points: 15,\n        type: 'environment',\n        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 min ago\n        userId: 'user1',\n        userName: 'Ahmed'\n      },\n      {\n        id: '2',\n        description: 'Fatma a aidé des personnes âgées à Sousse',\n        points: 25,\n        type: 'social',\n        timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago\n        userId: 'user2',\n        userName: 'Fatma'\n      },\n      {\n        id: '3',\n        description: 'Mohamed a organisé un cours de français',\n        points: 30,\n        type: 'education',\n        timestamp: new Date(Date.now() - 1000 * 60 * 90), // 1.5 hours ago\n        userId: 'user3',\n        userName: 'Mohamed'\n      },\n      {\n        id: '4',\n        description: 'Leila a guidé des touristes au Ribat',\n        points: 15,\n        type: 'culture',\n        timestamp: new Date(Date.now() - 1000 * 60 * 120), // 2 hours ago\n        userId: 'user4',\n        userName: 'Leila'\n      }\n    ];\n  }\n\n  private loadTopContributors(): void {\n    this.topContributors = [\n      { uid: 'top1', name: 'Ahmed Ben Ali', city: 'Monastir', points: 1250, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },\n      { uid: 'top2', name: 'Fatma Trabelsi', city: 'Sousse', points: 1180, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },\n      { uid: 'top3', name: 'Mohamed Gharbi', city: 'Monastir', points: 1050, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },\n      { uid: 'top4', name: 'Leila Mansouri', city: 'Sousse', points: 980, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },\n      { uid: 'top5', name: 'Karim Bouazizi', city: 'Monastir', points: 920, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] }\n    ];\n  }\n\n  private loadLocalEvents(): void {\n    this.localEvents = [\n      {\n        id: 'event1',\n        title: 'Nettoyage de la Plage de Monastir',\n        description: 'Rejoignez-nous pour nettoyer notre belle plage',\n        date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2), // In 2 days\n        location: 'Plage de Monastir',\n        category: 'Environnement',\n        points: 20,\n        image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=200&fit=crop',\n        participants: 45,\n        maxParticipants: 100\n      },\n      {\n        id: 'event2',\n        title: 'Festival Culturel de Sousse',\n        description: 'Célébrons notre patrimoine culturel ensemble',\n        date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5), // In 5 days\n        location: 'Médina de Sousse',\n        category: 'Culture',\n        points: 15,\n        image: 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=400&h=200&fit=crop',\n        participants: 120,\n        maxParticipants: 200\n      },\n      {\n        id: 'event3',\n        title: 'Cours de Français pour Réfugiés',\n        description: 'Aidez à enseigner le français aux nouveaux arrivants',\n        date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7), // In 1 week\n        location: 'Centre Communautaire',\n        category: 'Éducation',\n        points: 30,\n        image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=200&fit=crop',\n        participants: 15,\n        maxParticipants: 30\n      }\n    ];\n  }\n\n  private loadFeaturedPartners(): void {\n    this.featuredPartners = [\n      {\n        id: 'partner1',\n        name: 'Café des Nattes',\n        category: 'Restaurant',\n        logo: 'https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=100&h=100&fit=crop',\n        rating: 4.5,\n        reviews: 127,\n        currentOffer: '10% de réduction avec 50 points',\n        location: 'Médina de Monastir',\n        qrCode: 'PARTNER_CAFE_NATTES'\n      },\n      {\n        id: 'partner2',\n        name: 'Boutique Artisanat Sousse',\n        category: 'Artisanat',\n        logo: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop',\n        rating: 4.8,\n        reviews: 89,\n        currentOffer: 'Produit gratuit avec 100 points',\n        location: 'Médina de Sousse',\n        qrCode: 'PARTNER_ARTISANAT_SOUSSE'\n      },\n      {\n        id: 'partner3',\n        name: 'Hammam Traditionnel',\n        category: 'Bien-être',\n        logo: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=100&h=100&fit=crop',\n        rating: 4.3,\n        reviews: 156,\n        currentOffer: 'Séance gratuite avec 75 points',\n        location: 'Centre-ville Monastir',\n        qrCode: 'PARTNER_HAMMAM'\n      }\n    ];\n  }\n\n  private loadActiveChallenges(): void {\n    this.activeChallenges = [\n      {\n        id: 'challenge1',\n        title: 'Éco-Warrior',\n        description: 'Participez à 5 actions environnementales ce mois-ci',\n        icon: 'eco',\n        gradient: 'linear-gradient(135deg, #4CAF50, #8BC34A)',\n        progress: 60,\n        current: 3,\n        target: 5,\n        reward: 'Badge Éco-Warrior + 100 points bonus',\n        endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 15) // 15 days\n      },\n      {\n        id: 'challenge2',\n        title: 'Ambassadeur Culturel',\n        description: 'Guidez 10 touristes dans votre ville',\n        icon: 'tour',\n        gradient: 'linear-gradient(135deg, #FF9800, #FFC107)',\n        progress: 30,\n        current: 3,\n        target: 10,\n        reward: 'Badge Ambassadeur + Visite gratuite musée',\n        endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 20) // 20 days\n      },\n      {\n        id: 'challenge3',\n        title: 'Solidarité Communautaire',\n        description: 'Aidez 15 personnes dans le besoin',\n        icon: 'volunteer_activism',\n        gradient: 'linear-gradient(135deg, #E91E63, #F06292)',\n        progress: 80,\n        current: 12,\n        target: 15,\n        reward: 'Badge Solidarité + 200 points bonus',\n        endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 10) // 10 days\n      }\n    ];\n  }\n\n  private loadCityComparison(): void {\n    // Simulate city competition data\n    this.cityComparison = {\n      monastir: { score: 15420, participants: 342 },\n      sousse: { score: 16180, participants: 389 }\n    };\n\n    this.comparisonStats = [\n      {\n        name: 'Actions validées',\n        monastir: 65,\n        sousse: 78,\n        monastirValue: 1250,\n        sousseValue: 1520\n      },\n      {\n        name: 'Événements organisés',\n        monastir: 45,\n        sousse: 52,\n        monastirValue: 23,\n        sousseValue: 27\n      },\n      {\n        name: 'Partenaires actifs',\n        monastir: 80,\n        sousse: 70,\n        monastirValue: 16,\n        sousseValue: 14\n      }\n    ];\n  }\n\n  // UI Helper Methods\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n\n  getCityIcon(): string {\n    if (!this.user) return 'location_city';\n    return this.user.city === 'Monastir' ? 'account_balance' : 'museum';\n  }\n\n  getCommunityRank(): number {\n    if (!this.user) return 0;\n    const userIndex = this.topContributors.findIndex(u => u.uid === this.user!.uid);\n    return userIndex >= 0 ? userIndex + 1 : this.topContributors.length + 1;\n  }\n\n  getImpactScore(): number {\n    if (!this.user) return 0;\n    // Calculate impact based on points and community actions\n    return Math.floor(this.user.points * 1.2 + (this.user.history?.length || 0) * 5);\n  }\n\n  getCityProgress(): number {\n    if (!this.user) return 0;\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    const monthlyTarget = 20000;\n    return Math.min((cityData.score / monthlyTarget) * 314, 314); // 314 = 2π * 50 (circle circumference)\n  }\n\n  getCityProgressPercent(): number {\n    if (!this.user) return 0;\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    const monthlyTarget = 20000;\n    return Math.min(Math.round((cityData.score / monthlyTarget) * 100), 100);\n  }\n\n  getCityStats() {\n    if (!this.user) return { validatedActions: 0, activeUsers: 0 };\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    return {\n      validatedActions: Math.floor(cityData.score / 15), // Estimate based on average points per action\n      activeUsers: cityData.participants\n    };\n  }\n\n  getActivityColor(type: string): string {\n    const colorMap: { [key: string]: string } = {\n      'environment': '#4CAF50',\n      'social': '#E91E63',\n      'education': '#2196F3',\n      'culture': '#FF9800',\n      'health': '#9C27B0'\n    };\n    return colorMap[type] || '#667eea';\n  }\n\n  getActivityIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'environment': 'eco',\n      'social': 'volunteer_activism',\n      'education': 'school',\n      'culture': 'theater_comedy',\n      'health': 'health_and_safety'\n    };\n    return iconMap[type] || 'circle';\n  }\n\n  getTrophyColor(index: number): string {\n    const colors = ['#FFD700', '#C0C0C0', '#CD7F32']; // Gold, Silver, Bronze\n    return colors[index] || '#667eea';\n  }\n\n  getTrophyIcon(index: number): string {\n    return index < 3 ? 'emoji_events' : 'star';\n  }\n\n  getStars(rating: number): number[] {\n    return Array(Math.floor(rating)).fill(0);\n  }\n\n  // Action Methods\n  viewEvent(event: LocalEvent): void {\n    // Navigate to event details or open modal\n    console.log('Viewing event:', event);\n  }\n\n  viewPartner(partner: Partner): void {\n    // Navigate to partner details or open modal\n    console.log('Viewing partner:', partner);\n  }\n\n  executeAction(action: any): void {\n    // Execute the selected action\n    console.log('Executing action:', action);\n    // This would typically open a form or navigate to action details\n  }\n\n  viewAllActivity(): void {\n    this.router.navigate(['/community/activity']);\n  }\n}\n"], "mappings": ";AAAA,SAASA,SAAS,QAAgB,eAAe;AAIjD,SAAeC,QAAQ,QAAQ,yBAAyB;AAuDjD,IAAMC,kBAAkB,GAAxB,MAAMA,kBAAkB;EAiD7BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,eAAuC;IAFvC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IAnDzB,KAAAC,IAAI,GAAgB,IAAI;IAExB;IACA,KAAAC,aAAa,GAAwB,EAAE;IACvC,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAC,gBAAgB,GAAgB,EAAE;IAElC;IACA,KAAAC,cAAc,GAAG;MACfC,QAAQ,EAAE;QAAEC,KAAK,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAC,CAAE;MACvCC,MAAM,EAAE;QAAEF,KAAK,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAC;KACpC;IAED,KAAAE,eAAe,GAAG,CAChB;MAAEC,IAAI,EAAE,kBAAkB;MAAEL,QAAQ,EAAE,CAAC;MAAEG,MAAM,EAAE,CAAC;MAAEG,aAAa,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAC,CAAE,EACtF;MAAEF,IAAI,EAAE,sBAAsB;MAAEL,QAAQ,EAAE,CAAC;MAAEG,MAAM,EAAE,CAAC;MAAEG,aAAa,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAC,CAAE,EAC1F;MAAEF,IAAI,EAAE,oBAAoB;MAAEL,QAAQ,EAAE,CAAC;MAAEG,MAAM,EAAE,CAAC;MAAEG,aAAa,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAC,CAAE,CACzF;IAED,KAAAC,gBAAgB,GAAG,CACjB;MACEC,KAAK,EAAE,eAAe;MACtBC,OAAO,EAAE,CACP;QAAED,KAAK,EAAE,iBAAiB;QAAEE,IAAI,EAAE,OAAO;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAE,CAAE,EACzE;QAAEJ,KAAK,EAAE,mBAAmB;QAAEE,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAE,CAAE,EAC1E;QAAEJ,KAAK,EAAE,WAAW;QAAEE,IAAI,EAAE,WAAW;QAAEC,KAAK,EAAE,SAAS;QAAEC,MAAM,EAAE;MAAE,CAAE;KAE1E,EACD;MACEJ,KAAK,EAAE,QAAQ;MACfC,OAAO,EAAE,CACP;QAAED,KAAK,EAAE,sBAAsB;QAAEE,IAAI,EAAE,SAAS;QAAEC,KAAK,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAE,CAAE,EAC/E;QAAEJ,KAAK,EAAE,iBAAiB;QAAEE,IAAI,EAAE,QAAQ;QAAEC,KAAK,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAE,CAAE,EACzE;QAAEJ,KAAK,EAAE,oBAAoB;QAAEE,IAAI,EAAE,YAAY;QAAEC,KAAK,EAAE,QAAQ;QAAEC,MAAM,EAAE;MAAE,CAAE;KAEnF,EACD;MACEJ,KAAK,EAAE,SAAS;MAChBC,OAAO,EAAE,CACP;QAAED,KAAK,EAAE,mBAAmB;QAAEE,IAAI,EAAE,MAAM;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAE,CAAE,EACvE;QAAEJ,KAAK,EAAE,mBAAmB;QAAEE,IAAI,EAAE,YAAY;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAE,CAAE,EAC7E;QAAEJ,KAAK,EAAE,oBAAoB;QAAEE,IAAI,EAAE,gBAAgB;QAAEC,KAAK,EAAE,MAAM;QAAEC,MAAM,EAAE;MAAE,CAAE;KAErF,CACF;EAME;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACxB,WAAW,CAACyB,YAAY,CAACC,SAAS,CAACvB,IAAI,IAAG;MAC7C,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAIA,IAAI,EAAE;QACR;QACA,IAAIA,IAAI,CAACwB,IAAI,KAAK9B,QAAQ,CAAC+B,IAAI,EAAE;UAC/B,IAAI,CAAC1B,eAAe,CAAC2B,uBAAuB,CAAC1B,IAAI,CAAC;UAClD;;QAEF,IAAI,CAAC2B,iBAAiB,EAAE;;IAE5B,CAAC,CAAC;EACJ;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQL,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAAC3B,aAAa,GAAG,CACnB;MACEiC,EAAE,EAAE,GAAG;MACPC,WAAW,EAAE,sCAAsC;MACnDf,MAAM,EAAE,EAAE;MACVgB,IAAI,EAAE,aAAa;MACnBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE;KACX,EACD;MACEP,EAAE,EAAE,GAAG;MACPC,WAAW,EAAE,2CAA2C;MACxDf,MAAM,EAAE,EAAE;MACVgB,IAAI,EAAE,QAAQ;MACdC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE;KACX,EACD;MACEP,EAAE,EAAE,GAAG;MACPC,WAAW,EAAE,yCAAyC;MACtDf,MAAM,EAAE,EAAE;MACVgB,IAAI,EAAE,WAAW;MACjBC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE;KACX,EACD;MACEP,EAAE,EAAE,GAAG;MACPC,WAAW,EAAE,sCAAsC;MACnDf,MAAM,EAAE,EAAE;MACVgB,IAAI,EAAE,SAAS;MACfC,SAAS,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC;MACjDC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE;KACX,CACF;EACH;EAEQZ,mBAAmBA,CAAA;IACzB,IAAI,CAAC3B,eAAe,GAAG,CACrB;MAAEwC,GAAG,EAAE,MAAM;MAAE9B,IAAI,EAAE,eAAe;MAAE+B,IAAI,EAAE,UAAU;MAAEvB,MAAM,EAAE,IAAI;MAAEwB,KAAK,EAAE,EAAE;MAAEpB,IAAI,EAAE9B,QAAQ,CAAC+B,IAAI;MAAEoB,SAAS,EAAE,IAAIP,IAAI,EAAE;MAAEQ,SAAS,EAAE,IAAIR,IAAI,EAAE;MAAES,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,EACjL;MAAEN,GAAG,EAAE,MAAM;MAAE9B,IAAI,EAAE,gBAAgB;MAAE+B,IAAI,EAAE,QAAQ;MAAEvB,MAAM,EAAE,IAAI;MAAEwB,KAAK,EAAE,EAAE;MAAEpB,IAAI,EAAE9B,QAAQ,CAAC+B,IAAI;MAAEoB,SAAS,EAAE,IAAIP,IAAI,EAAE;MAAEQ,SAAS,EAAE,IAAIR,IAAI,EAAE;MAAES,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,EAChL;MAAEN,GAAG,EAAE,MAAM;MAAE9B,IAAI,EAAE,gBAAgB;MAAE+B,IAAI,EAAE,UAAU;MAAEvB,MAAM,EAAE,IAAI;MAAEwB,KAAK,EAAE,EAAE;MAAEpB,IAAI,EAAE9B,QAAQ,CAAC+B,IAAI;MAAEoB,SAAS,EAAE,IAAIP,IAAI,EAAE;MAAEQ,SAAS,EAAE,IAAIR,IAAI,EAAE;MAAES,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,EAClL;MAAEN,GAAG,EAAE,MAAM;MAAE9B,IAAI,EAAE,gBAAgB;MAAE+B,IAAI,EAAE,QAAQ;MAAEvB,MAAM,EAAE,GAAG;MAAEwB,KAAK,EAAE,EAAE;MAAEpB,IAAI,EAAE9B,QAAQ,CAAC+B,IAAI;MAAEoB,SAAS,EAAE,IAAIP,IAAI,EAAE;MAAEQ,SAAS,EAAE,IAAIR,IAAI,EAAE;MAAES,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,EAC/K;MAAEN,GAAG,EAAE,MAAM;MAAE9B,IAAI,EAAE,gBAAgB;MAAE+B,IAAI,EAAE,UAAU;MAAEvB,MAAM,EAAE,GAAG;MAAEwB,KAAK,EAAE,EAAE;MAAEpB,IAAI,EAAE9B,QAAQ,CAAC+B,IAAI;MAAEoB,SAAS,EAAE,IAAIP,IAAI,EAAE;MAAEQ,SAAS,EAAE,IAAIR,IAAI,EAAE;MAAES,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,CAClL;EACH;EAEQlB,eAAeA,CAAA;IACrB,IAAI,CAAC3B,WAAW,GAAG,CACjB;MACE+B,EAAE,EAAE,QAAQ;MACZlB,KAAK,EAAE,mCAAmC;MAC1CmB,WAAW,EAAE,gDAAgD;MAC7Dc,IAAI,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACpDW,QAAQ,EAAE,mBAAmB;MAC7BC,QAAQ,EAAE,eAAe;MACzB/B,MAAM,EAAE,EAAE;MACVgC,KAAK,EAAE,gFAAgF;MACvF3C,YAAY,EAAE,EAAE;MAChB4C,eAAe,EAAE;KAClB,EACD;MACEnB,EAAE,EAAE,QAAQ;MACZlB,KAAK,EAAE,6BAA6B;MACpCmB,WAAW,EAAE,8CAA8C;MAC3Dc,IAAI,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACpDW,QAAQ,EAAE,kBAAkB;MAC5BC,QAAQ,EAAE,SAAS;MACnB/B,MAAM,EAAE,EAAE;MACVgC,KAAK,EAAE,mFAAmF;MAC1F3C,YAAY,EAAE,GAAG;MACjB4C,eAAe,EAAE;KAClB,EACD;MACEnB,EAAE,EAAE,QAAQ;MACZlB,KAAK,EAAE,iCAAiC;MACxCmB,WAAW,EAAE,sDAAsD;MACnEc,IAAI,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACpDW,QAAQ,EAAE,sBAAsB;MAChCC,QAAQ,EAAE,WAAW;MACrB/B,MAAM,EAAE,EAAE;MACVgC,KAAK,EAAE,mFAAmF;MAC1F3C,YAAY,EAAE,EAAE;MAChB4C,eAAe,EAAE;KAClB,CACF;EACH;EAEQtB,oBAAoBA,CAAA;IAC1B,IAAI,CAAC3B,gBAAgB,GAAG,CACtB;MACE8B,EAAE,EAAE,UAAU;MACdtB,IAAI,EAAE,iBAAiB;MACvBuC,QAAQ,EAAE,YAAY;MACtBG,IAAI,EAAE,gFAAgF;MACtFC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,GAAG;MACZC,YAAY,EAAE,iCAAiC;MAC/CP,QAAQ,EAAE,oBAAoB;MAC9BQ,MAAM,EAAE;KACT,EACD;MACExB,EAAE,EAAE,UAAU;MACdtB,IAAI,EAAE,2BAA2B;MACjCuC,QAAQ,EAAE,WAAW;MACrBG,IAAI,EAAE,mFAAmF;MACzFC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,iCAAiC;MAC/CP,QAAQ,EAAE,kBAAkB;MAC5BQ,MAAM,EAAE;KACT,EACD;MACExB,EAAE,EAAE,UAAU;MACdtB,IAAI,EAAE,qBAAqB;MAC3BuC,QAAQ,EAAE,WAAW;MACrBG,IAAI,EAAE,mFAAmF;MACzFC,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,GAAG;MACZC,YAAY,EAAE,gCAAgC;MAC9CP,QAAQ,EAAE,uBAAuB;MACjCQ,MAAM,EAAE;KACT,CACF;EACH;EAEQ1B,oBAAoBA,CAAA;IAC1B,IAAI,CAAC3B,gBAAgB,GAAG,CACtB;MACE6B,EAAE,EAAE,YAAY;MAChBlB,KAAK,EAAE,aAAa;MACpBmB,WAAW,EAAE,qDAAqD;MAClEjB,IAAI,EAAE,KAAK;MACXyC,QAAQ,EAAE,2CAA2C;MACrDC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,sCAAsC;MAC9CC,OAAO,EAAE,IAAI1B,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;KAC1D,EACD;MACEL,EAAE,EAAE,YAAY;MAChBlB,KAAK,EAAE,sBAAsB;MAC7BmB,WAAW,EAAE,sCAAsC;MACnDjB,IAAI,EAAE,MAAM;MACZyC,QAAQ,EAAE,2CAA2C;MACrDC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,2CAA2C;MACnDC,OAAO,EAAE,IAAI1B,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;KAC1D,EACD;MACEL,EAAE,EAAE,YAAY;MAChBlB,KAAK,EAAE,0BAA0B;MACjCmB,WAAW,EAAE,mCAAmC;MAChDjB,IAAI,EAAE,oBAAoB;MAC1ByC,QAAQ,EAAE,2CAA2C;MACrDC,QAAQ,EAAE,EAAE;MACZC,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,qCAAqC;MAC7CC,OAAO,EAAE,IAAI1B,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;KAC1D,CACF;EACH;EAEQN,kBAAkBA,CAAA;IACxB;IACA,IAAI,CAAC3B,cAAc,GAAG;MACpBC,QAAQ,EAAE;QAAEC,KAAK,EAAE,KAAK;QAAEC,YAAY,EAAE;MAAG,CAAE;MAC7CC,MAAM,EAAE;QAAEF,KAAK,EAAE,KAAK;QAAEC,YAAY,EAAE;MAAG;KAC1C;IAED,IAAI,CAACE,eAAe,GAAG,CACrB;MACEC,IAAI,EAAE,kBAAkB;MACxBL,QAAQ,EAAE,EAAE;MACZG,MAAM,EAAE,EAAE;MACVG,aAAa,EAAE,IAAI;MACnBC,WAAW,EAAE;KACd,EACD;MACEF,IAAI,EAAE,sBAAsB;MAC5BL,QAAQ,EAAE,EAAE;MACZG,MAAM,EAAE,EAAE;MACVG,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE;KACd,EACD;MACEF,IAAI,EAAE,oBAAoB;MAC1BL,QAAQ,EAAE,EAAE;MACZG,MAAM,EAAE,EAAE;MACVG,aAAa,EAAE,EAAE;MACjBC,WAAW,EAAE;KACd,CACF;EACH;EAEA;EACAmD,WAAWA,CAAA;IACT,MAAMC,IAAI,GAAG,IAAI5B,IAAI,EAAE,CAAC6B,QAAQ,EAAE;IAClC,IAAID,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,SAAS;EAClB;EAEAE,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACpE,IAAI,EAAE,OAAO,eAAe;IACtC,OAAO,IAAI,CAACA,IAAI,CAAC2C,IAAI,KAAK,UAAU,GAAG,iBAAiB,GAAG,QAAQ;EACrE;EAEA0B,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACrE,IAAI,EAAE,OAAO,CAAC;IACxB,MAAMsE,SAAS,GAAG,IAAI,CAACpE,eAAe,CAACqE,SAAS,CAACC,CAAC,IAAIA,CAAC,CAAC9B,GAAG,KAAK,IAAI,CAAC1C,IAAK,CAAC0C,GAAG,CAAC;IAC/E,OAAO4B,SAAS,IAAI,CAAC,GAAGA,SAAS,GAAG,CAAC,GAAG,IAAI,CAACpE,eAAe,CAACuE,MAAM,GAAG,CAAC;EACzE;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC1E,IAAI,EAAE,OAAO,CAAC;IACxB;IACA,OAAO2E,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC5E,IAAI,CAACoB,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAACpB,IAAI,CAACgD,OAAO,EAAEyB,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;EAClF;EAEAI,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAC7E,IAAI,EAAE,OAAO,CAAC;IACxB,MAAM8E,QAAQ,GAAG,IAAI,CAAC9E,IAAI,CAAC2C,IAAI,KAAK,UAAU,GAAG,IAAI,CAACrC,cAAc,CAACC,QAAQ,GAAG,IAAI,CAACD,cAAc,CAACI,MAAM;IAC1G,MAAMqE,aAAa,GAAG,KAAK;IAC3B,OAAOJ,IAAI,CAACK,GAAG,CAAEF,QAAQ,CAACtE,KAAK,GAAGuE,aAAa,GAAI,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;EAChE;EAEAE,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACjF,IAAI,EAAE,OAAO,CAAC;IACxB,MAAM8E,QAAQ,GAAG,IAAI,CAAC9E,IAAI,CAAC2C,IAAI,KAAK,UAAU,GAAG,IAAI,CAACrC,cAAc,CAACC,QAAQ,GAAG,IAAI,CAACD,cAAc,CAACI,MAAM;IAC1G,MAAMqE,aAAa,GAAG,KAAK;IAC3B,OAAOJ,IAAI,CAACK,GAAG,CAACL,IAAI,CAACO,KAAK,CAAEJ,QAAQ,CAACtE,KAAK,GAAGuE,aAAa,GAAI,GAAG,CAAC,EAAE,GAAG,CAAC;EAC1E;EAEAI,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACnF,IAAI,EAAE,OAAO;MAAEoF,gBAAgB,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAC,CAAE;IAC9D,MAAMP,QAAQ,GAAG,IAAI,CAAC9E,IAAI,CAAC2C,IAAI,KAAK,UAAU,GAAG,IAAI,CAACrC,cAAc,CAACC,QAAQ,GAAG,IAAI,CAACD,cAAc,CAACI,MAAM;IAC1G,OAAO;MACL0E,gBAAgB,EAAET,IAAI,CAACC,KAAK,CAACE,QAAQ,CAACtE,KAAK,GAAG,EAAE,CAAC;MACjD6E,WAAW,EAAEP,QAAQ,CAACrE;KACvB;EACH;EAEA6E,gBAAgBA,CAAClD,IAAY;IAC3B,MAAMmD,QAAQ,GAA8B;MAC1C,aAAa,EAAE,SAAS;MACxB,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,SAAS;MACtB,SAAS,EAAE,SAAS;MACpB,QAAQ,EAAE;KACX;IACD,OAAOA,QAAQ,CAACnD,IAAI,CAAC,IAAI,SAAS;EACpC;EAEAoD,eAAeA,CAACpD,IAAY;IAC1B,MAAMqD,OAAO,GAA8B;MACzC,aAAa,EAAE,KAAK;MACpB,QAAQ,EAAE,oBAAoB;MAC9B,WAAW,EAAE,QAAQ;MACrB,SAAS,EAAE,gBAAgB;MAC3B,QAAQ,EAAE;KACX;IACD,OAAOA,OAAO,CAACrD,IAAI,CAAC,IAAI,QAAQ;EAClC;EAEAsD,cAAcA,CAACC,KAAa;IAC1B,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;IAClD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC;EAEAE,aAAaA,CAACF,KAAa;IACzB,OAAOA,KAAK,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM;EAC5C;EAEAG,QAAQA,CAACvC,MAAc;IACrB,OAAOwC,KAAK,CAACpB,IAAI,CAACC,KAAK,CAACrB,MAAM,CAAC,CAAC,CAACyC,IAAI,CAAC,CAAC,CAAC;EAC1C;EAEA;EACAC,SAASA,CAACC,KAAiB;IACzB;IACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEF,KAAK,CAAC;EACtC;EAEAG,WAAWA,CAACC,OAAgB;IAC1B;IACAH,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEE,OAAO,CAAC;EAC1C;EAEAC,aAAaA,CAACC,MAAW;IACvB;IACAL,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEI,MAAM,CAAC;IACxC;EACF;EAEAC,eAAeA,CAAA;IACb,IAAI,CAAC3G,MAAM,CAAC4G,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC;EAC/C;CACD;AAjYY/G,kBAAkB,GAAAgH,UAAA,EAL9BlH,SAAS,CAAC;EACTmH,QAAQ,EAAE,eAAe;EACzBC,WAAW,EAAE,4BAA4B;EACzCC,SAAS,EAAE,CAAC,2BAA2B;CACxC,CAAC,C,EACWnH,kBAAkB,CAiY9B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}