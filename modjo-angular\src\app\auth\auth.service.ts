import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { Observable, of } from 'rxjs';
import { switchMap, map, tap } from 'rxjs/operators';
import { User, UserRole } from '../models/user.model';

// These imports would normally come from Firebase, but we're mocking them for now
// import { Auth, authState, createUserWithEmailAndPassword, signInWithEmailAndPassword, signOut, updateProfile, UserCredential } from '@angular/fire/auth';
// import { Firestore, doc, setDoc, getDoc, updateDoc } from '@angular/fire/firestore';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  // Mock user for development
  private mockUser: User | null = null;
  
  // This would normally be from Firebase
  // user$: Observable<User | null>;

  constructor(
    // private auth: Auth,
    // private firestore: Firestore,
    private router: Router
  ) {
    // This would be the Firebase implementation
    // this.user$ = authState(this.auth).pipe(
    //   switchMap(user => {
    //     if (user) {
    //       return this.getUserData(user.uid);
    //     } else {
    //       return of(null);
    //     }
    //   })
    // );
  }

  // Get user data from Firestore
  getUserData(uid: string): Observable<User | null> {
    // Mock implementation
    if (this.mockUser && this.mockUser.uid === uid) {
      return of(this.mockUser);
    }
    return of(null);

    // Firebase implementation
    // const userRef = doc(this.firestore, `users/${uid}`);
    // return from(getDoc(userRef)).pipe(
    //   map(docSnap => {
    //     if (docSnap.exists()) {
    //       return { uid, ...docSnap.data() } as User;
    //     } else {
    //       return null;
    //     }
    //   })
    // );
  }

  // Sign in with email and password
  async signIn(email: string, password: string): Promise<User> {
    // Mock implementation
    if (email === '<EMAIL>' && password === 'password') {
      this.mockUser = {
        uid: '1',
        email: email,
        displayName: 'Test User',
        firstName: 'Test',
        lastName: 'User',
        city: 'Monastir',
        role: UserRole.USER,
        points: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      return this.mockUser;
    } else if (email === '<EMAIL>' && password === 'password') {
      this.mockUser = {
        uid: '2',
        email: email,
        displayName: 'Test Provider',
        firstName: 'Test',
        lastName: 'Provider',
        city: 'Sousse',
        role: UserRole.PROVIDER,
        activity: 'Coiffeur',
        points: 100,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      return this.mockUser;
    } else if (email === '<EMAIL>' && password === 'password') {
      this.mockUser = {
        uid: '3',
        email: email,
        displayName: 'Test Validator',
        firstName: 'Test',
        lastName: 'Validator',
        city: 'Monastir',
        role: UserRole.VALIDATOR,
        activity: 'École',
        points: 0,
        createdAt: new Date(),
        updatedAt: new Date()
      };
      return this.mockUser;
    } else {
      throw new Error('Invalid email or password');
    }

    // Firebase implementation
    // try {
    //   const userCredential = await signInWithEmailAndPassword(this.auth, email, password);
    //   return this.getUserData(userCredential.user.uid).toPromise();
    // } catch (error) {
    //   console.error('Error signing in:', error);
    //   throw error;
    // }
  }

  // Sign up with email and password
  async signUp(email: string, password: string, userData: Partial<User>): Promise<User> {
    // Mock implementation
    this.mockUser = {
      uid: Math.random().toString(36).substring(2, 15),
      email: email,
      displayName: userData.displayName || '',
      firstName: userData.firstName || '',
      lastName: userData.lastName || '',
      city: userData.city || '',
      role: userData.role || UserRole.USER,
      activity: userData.activity || '',
      points: 0,
      createdAt: new Date(),
      updatedAt: new Date()
    };
    return this.mockUser;

    // Firebase implementation
    // try {
    //   const userCredential = await createUserWithEmailAndPassword(this.auth, email, password);
    //   const user = userCredential.user;
    //   
    //   if (userData.displayName) {
    //     await updateProfile(user, { displayName: userData.displayName });
    //   }
    //   
    //   const newUser: User = {
    //     uid: user.uid,
    //     email: user.email!,
    //     displayName: userData.displayName || '',
    //     firstName: userData.firstName || '',
    //     lastName: userData.lastName || '',
    //     city: userData.city || '',
    //     role: userData.role || UserRole.USER,
    //     activity: userData.activity || '',
    //     points: 0,
    //     createdAt: new Date(),
    //     updatedAt: new Date()
    //   };
    //   
    //   await setDoc(doc(this.firestore, `users/${user.uid}`), newUser);
    //   return newUser;
    // } catch (error) {
    //   console.error('Error signing up:', error);
    //   throw error;
    // }
  }

  // Sign out
  async signOut(): Promise<void> {
    // Mock implementation
    this.mockUser = null;
    this.router.navigate(['/login']);

    // Firebase implementation
    // await signOut(this.auth);
    // this.router.navigate(['/login']);
  }

  // Check if user is authenticated
  isAuthenticated(): boolean {
    return !!this.mockUser;
    
    // Firebase implementation
    // return this.auth.currentUser !== null;
  }

  // Get current user
  getCurrentUser(): User | null {
    return this.mockUser;
    
    // Firebase implementation
    // return this.auth.currentUser ? this.getUserData(this.auth.currentUser.uid) : null;
  }

  // Update user profile
  async updateUserProfile(uid: string, data: Partial<User>): Promise<void> {
    // Mock implementation
    if (this.mockUser && this.mockUser.uid === uid) {
      this.mockUser = { ...this.mockUser, ...data, updatedAt: new Date() };
    }

    // Firebase implementation
    // const userRef = doc(this.firestore, `users/${uid}`);
    // await updateDoc(userRef, { ...data, updatedAt: new Date() });
  }
}
