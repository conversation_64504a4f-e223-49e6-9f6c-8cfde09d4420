// User models
export * from './user.model';

// Reward models
export * from './reward.model';

// Validation models
export * from './validation.model';

// Common interfaces
export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  hasNext: boolean;
  hasPrevious: boolean;
}

export interface FilterOptions {
  search?: string;
  category?: string;
  city?: 'Monastir' | 'Sousse';
  dateFrom?: Date;
  dateTo?: Date;
  status?: string;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}

export interface NotificationData {
  id: string;
  userId: string;
  title: string;
  message: string;
  type: NotificationType;
  isRead: boolean;
  createdAt: Date;
  data?: any;
}

export enum NotificationType {
  POINTS_EARNED = 'points_earned',
  POINTS_SPENT = 'points_spent',
  VALIDATION_APPROVED = 'validation_approved',
  VALIDATION_REJECTED = 'validation_rejected',
  REWARD_AVAILABLE = 'reward_available',
  SYSTEM_ANNOUNCEMENT = 'system_announcement'
}

export interface AppConfig {
  firebase: {
    apiKey: string;
    authDomain: string;
    projectId: string;
    storageBucket: string;
    messagingSenderId: string;
    appId: string;
  };
  app: {
    name: string;
    version: string;
    environment: 'development' | 'staging' | 'production';
  };
  features: {
    pushNotifications: boolean;
    geolocation: boolean;
    darkMode: boolean;
    multiLanguage: boolean;
  };
}
