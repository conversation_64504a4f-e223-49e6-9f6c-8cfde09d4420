import { GeoLocation } from './user.model';

export interface Validation {
  id: string;
  validatorId: string;
  validatorName: string;
  userId: string;
  userName: string;
  action: ValidationAction;
  points: number;
  description: string;
  status: ValidationStatus;
  timestamp: Date;
  location?: GeoLocation;
  evidence?: ValidationEvidence;
  qrCode?: string;
}

export enum ValidationAction {
  COMMUNITY_SERVICE = 'community_service',
  ENVIRONMENTAL_ACTION = 'environmental_action',
  CULTURAL_PARTICIPATION = 'cultural_participation',
  SPORTS_ACTIVITY = 'sports_activity',
  EDUCATIONAL_ACTIVITY = 'educational_activity',
  VOLUNTEER_WORK = 'volunteer_work',
  LOCAL_BUSINESS_SUPPORT = 'local_business_support',
  OTHER = 'other'
}

export enum ValidationStatus {
  PENDING = 'pending',
  APPROVED = 'approved',
  REJECTED = 'rejected',
  EXPIRED = 'expired'
}

export interface ValidationEvidence {
  photos?: string[];
  description: string;
  witnesses?: string[];
  documents?: string[];
}

export interface QrScanResult {
  data: string;
  format: string;
  timestamp: Date;
  location?: GeoLocation;
}

export interface QrCodeData {
  type: QrCodeType;
  userId?: string;
  validatorId?: string;
  points?: number;
  action?: string;
  timestamp: number;
  signature?: string;
}

export enum QrCodeType {
  USER_TRANSFER = 'user_transfer',
  VALIDATOR_ACTION = 'validator_action',
  PARTNER_REWARD = 'partner_reward',
  SYSTEM_BONUS = 'system_bonus'
}

export interface CreateValidationRequest {
  userId: string;
  action: ValidationAction;
  points: number;
  description: string;
  evidence?: ValidationEvidence;
  location?: GeoLocation;
}

export interface UpdateValidationRequest {
  status: ValidationStatus;
  adminNotes?: string;
  pointsAdjustment?: number;
}

export interface ValidationStats {
  totalValidations: number;
  pendingValidations: number;
  approvedValidations: number;
  rejectedValidations: number;
  totalPointsAwarded: number;
  averagePointsPerValidation: number;
  validationsByAction: Record<ValidationAction, number>;
  validationsByMonth: Record<string, number>;
}

// Nouvelles interfaces pour le système Modjo
export interface ValidationRule {
  id: string;
  actionType: ValidationAction;
  maxPointsPerAction: number;
  maxValidationsPerUserPerDay: number;
  maxValidationsPerValidatorPerDay: number;
  requiredEvidence: boolean;
  autoApprovalThreshold?: number;
}

export interface ValidatorStats {
  validatorId: string;
  totalValidations: number;
  validationsToday: number;
  averageValidationTime: number; // en minutes
  approvalRate: number; // pourcentage
  lastValidationAt?: Date;
}

export interface UserValidationHistory {
  userId: string;
  validationId: string;
  validatedAt: Date;
  points: number;
  actionType: ValidationAction;
  validatorId: string;
  isUnique: boolean; // Pour s'assurer qu'une action n'est validée qu'une fois
}

// GeoLocation interface is defined in user.model.ts
