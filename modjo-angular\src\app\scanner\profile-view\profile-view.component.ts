import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { ActivatedRoute, Router } from '@angular/router';
import { FormsModule } from '@angular/forms';
import { ScannerService } from '../scanner.service';
import { AuthService } from '../../auth/auth.service';
import { UserProfile } from '../../models/user.model';

@Component({
  selector: 'app-profile-view',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './profile-view.component.html',
  styleUrls: ['./profile-view.component.css']
})
export class ProfileViewComponent implements OnInit {
  knowMeId: string = '';
  profile: any = null;
  userProfile: UserProfile | null = null;
  isLoading: boolean = true;
  error: string = '';

  // Point attribution
  selectedPoints: number = 1;
  pointOptions: number[] = [1, 3, 5];
  description: string = '';
  isAwarding: boolean = false;
  awardSuccess: boolean = false;
  awardError: string = '';

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private scannerService: ScannerService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/login'], { queryParams: { returnUrl: this.router.url } });
      return;
    }

    // Get KnowMe ID from route params
    this.knowMeId = this.route.snapshot.paramMap.get('id') || '';
    if (!this.knowMeId) {
      this.error = 'ID de profil manquant';
      this.isLoading = false;
      return;
    }

    // Load profile data
    this.loadProfileData();
  }

  private async loadProfileData(): Promise<void> {
    try {
      // Get KnowMe profile data
      this.profile = await this.scannerService.getKnowMeProfileData(this.knowMeId).toPromise();

      // Get user profile from our database
      this.userProfile = await this.scannerService.getUserFromKnowMeId(this.knowMeId).toPromise() || null;

      this.isLoading = false;
    } catch (error) {
      console.error('Error loading profile:', error);
      this.error = 'Erreur lors du chargement du profil';
      this.isLoading = false;
    }
  }

  async awardPoints(): Promise<void> {
    if (!this.description) {
      this.awardError = 'Veuillez entrer une description';
      return;
    }

    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      this.awardError = 'Vous devez être connecté pour attribuer des points';
      return;
    }

    if (!this.userProfile) {
      this.awardError = 'Profil utilisateur non trouvé';
      return;
    }

    this.isAwarding = true;
    this.awardError = '';

    try {
      await this.scannerService.awardPoints(
        currentUser.uid,
        this.userProfile.uid,
        this.selectedPoints,
        this.description
      );

      this.awardSuccess = true;
      this.description = '';
    } catch (error: any) {
      console.error('Error awarding points:', error);
      this.awardError = error.message || 'Erreur lors de l\'attribution des points';
    } finally {
      this.isAwarding = false;
    }
  }

  goBack(): void {
    this.router.navigate(['/scanner']);
  }
}
