export interface Provider {
  id: string;
  userId: string; // Lié au compte utilisateur
  organizationName: string;
  organizationType: ProviderType;
  description: string;
  address: string;
  city: 'Monastir' | 'Sousse';
  phone: string;
  email: string;
  logo?: string;
  website?: string;
  contactPerson: string;
  isActive: boolean;
  verificationStatus: VerificationStatus;
  joinedAt: Date;
  lastActiveAt: Date;
}

export enum ProviderType {
  SCHOOL = 'school',
  UNIVERSITY = 'university',
  ASSOCIATION = 'association',
  NGO = 'ngo',
  SPORTS_CLUB = 'sports_club',
  CULTURAL_CENTER = 'cultural_center',
  LIBRARY = 'library',
  COMMUNITY_CENTER = 'community_center',
  TRAINING_CENTER = 'training_center',
  COACHING = 'coaching',
  OTHER = 'other'
}

export enum VerificationStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  REJECTED = 'rejected',
  SUSPENDED = 'suspended'
}

export interface QrCodeGeneration {
  id: string;
  providerId: string;
  providerName: string;
  qrCodeData: string;
  pointsValue: number; // 1, 3, ou 5 points
  activityType: ActivityType;
  description: string;
  maxScansPerUser: number;
  maxScansPerDay: number;
  validFrom: Date;
  validUntil: Date;
  isActive: boolean;
  createdAt: Date;
  totalScans: number;
  uniqueUsers: number;
}

export enum ActivityType {
  LESSON = 'lesson',
  WORKSHOP = 'workshop',
  TRAINING = 'training',
  CONFERENCE = 'conference',
  SPORTS_SESSION = 'sports_session',
  CULTURAL_EVENT = 'cultural_event',
  VOLUNTEER_WORK = 'volunteer_work',
  TUTORING = 'tutoring',
  LIBRARY_VISIT = 'library_visit',
  COMMUNITY_SERVICE = 'community_service',
  OTHER = 'other'
}

export interface QrCodeScan {
  id: string;
  qrCodeId: string;
  userId: string;
  userName: string;
  providerId: string;
  providerName: string;
  pointsAwarded: number;
  scannedAt: Date;
  location?: string;
  isValid: boolean;
  dailyLimitReached: boolean;
}

export interface ProviderStats {
  providerId: string;
  totalQrCodes: number;
  activeQrCodes: number;
  totalScans: number;
  totalPointsDistributed: number;
  uniqueUsers: number;
  averageScansPerQr: number;
  popularActivities: PopularActivity[];
  monthlyStats: MonthlyProviderStats[];
  userEngagement: number; // pourcentage de retour
}

export interface PopularActivity {
  activityType: ActivityType;
  description: string;
  scanCount: number;
  pointsDistributed: number;
}

export interface MonthlyProviderStats {
  month: string; // YYYY-MM
  scans: number;
  pointsDistributed: number;
  uniqueUsers: number;
  newUsers: number;
}

export interface CreateQrCodeRequest {
  pointsValue: number; // 1, 3, ou 5
  activityType: ActivityType;
  description: string;
  maxScansPerUser: number;
  maxScansPerDay: number;
  validUntil: Date;
}

export interface UpdateQrCodeRequest {
  description?: string;
  maxScansPerUser?: number;
  maxScansPerDay?: number;
  validUntil?: Date;
  isActive?: boolean;
}

export interface DailyLimit {
  userId: string;
  providerId: string;
  date: string; // YYYY-MM-DD
  scansCount: number;
  pointsEarned: number;
  lastScanAt: Date;
}

export interface QrCodeValidationRule {
  maxPointsPerDay: number;
  maxScansPerUserPerProvider: number;
  maxScansPerUserPerDay: number;
  cooldownPeriod: number; // en minutes
  requireLocation: boolean;
}
