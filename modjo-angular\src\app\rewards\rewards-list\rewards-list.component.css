.rewards-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  margin-bottom: 30px;
  color: #333;
}

.loading, .error, .no-data {
  text-align: center;
  padding: 30px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 20px;
}

.error {
  color: #d32f2f;
}

.points-summary {
  display: flex;
  justify-content: center;
  margin-bottom: 30px;
}

.points-badge {
  display: inline-flex;
  align-items: center;
  background-color: #4a90e2;
  color: white;
  border-radius: 30px;
  padding: 10px 25px;
}

.points-count {
  font-size: 24px;
  font-weight: 700;
  margin-right: 10px;
}

.points-label {
  font-size: 16px;
}

.success-message {
  padding: 15px;
  background-color: #e8f5e9;
  color: #2e7d32;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

.error-message {
  padding: 15px;
  background-color: #ffebee;
  color: #d32f2f;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
}

.rewards-section, .redemptions-section {
  margin-bottom: 40px;
}

h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 20px;
  color: #333;
}

.rewards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
  gap: 30px;
}

.reward-card {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  transition: transform 0.3s ease;
}

.reward-card:hover {
  transform: translateY(-5px);
}

.reward-card.disabled {
  opacity: 0.7;
}

.reward-image {
  height: 150px;
  overflow: hidden;
}

.reward-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.reward-content {
  padding: 20px;
}

.reward-content h3 {
  margin-top: 0;
  margin-bottom: 10px;
  font-size: 18px;
  color: #333;
}

.reward-description {
  margin-bottom: 10px;
  color: #666;
  font-size: 14px;
}

.reward-partner {
  margin-bottom: 15px;
  color: #888;
  font-size: 12px;
}

.reward-cost {
  display: inline-block;
  background-color: #f5f5f5;
  padding: 5px 15px;
  border-radius: 20px;
  margin-bottom: 15px;
}

.cost-value {
  font-weight: 700;
  color: #4a90e2;
  margin-right: 5px;
}

.cost-label {
  font-size: 14px;
  color: #666;
}

.redeem-button {
  width: 100%;
  padding: 15px;
  background-color: #4a90e2;
  color: white;
  border: none;
  font-size: 16px;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.3s;
}

.redeem-button:hover {
  background-color: #3a7bc8;
}

.redeem-button:disabled {
  background-color: #a0c3e8;
  cursor: not-allowed;
}

.redemptions-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.redemption-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.redemption-info h3 {
  margin-top: 0;
  margin-bottom: 5px;
  font-size: 18px;
  color: #333;
}

.redemption-date, .redemption-partner {
  margin: 0 0 5px 0;
  font-size: 14px;
  color: #666;
}

.redemption-status {
  padding: 5px 15px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

.redemption-status.pending {
  background-color: #fff8e1;
  color: #f57f17;
}

.redemption-status.completed {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.redemption-status.cancelled {
  background-color: #ffebee;
  color: #d32f2f;
}

@media (max-width: 768px) {
  .rewards-grid {
    grid-template-columns: 1fr;
  }
  
  .redemption-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .redemption-status {
    margin-top: 15px;
    align-self: flex-end;
  }
}
