{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/fire/compat/auth\";\nimport * as i2 from \"@angular/fire/compat/firestore\";\nexport class AuthService {\n  constructor(auth, firestore) {\n    var _this = this;\n    this.auth = auth;\n    this.firestore = firestore;\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    // Listen to auth state changes\n    this.auth.authState.subscribe(/*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (firebaseUser) {\n        if (firebaseUser) {\n          const userData = yield _this.getUserData(firebaseUser.uid);\n          _this.currentUserSubject.next(userData);\n        } else {\n          _this.currentUserSubject.next(null);\n        }\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n  register(userData) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Create Firebase Auth user\n        const credential = yield _this2.auth.createUserWithEmailAndPassword(userData.email, userData.password);\n        if (!credential.user) {\n          throw new Error('Failed to create user');\n        }\n        // Create user document in Firestore\n        const user = {\n          uid: credential.user.uid,\n          email: userData.email,\n          name: userData.name,\n          phone: userData.phone,\n          city: userData.city,\n          role: userData.role,\n          points: 0,\n          createdAt: new Date(),\n          updatedAt: new Date(),\n          isActive: true,\n          history: []\n        };\n        yield _this2.firestore.collection('users').doc(user.uid).set(user);\n        _this2.currentUserSubject.next(user);\n        return user;\n      } catch (error) {\n        console.error('Registration error:', error);\n        throw error;\n      }\n    })();\n  }\n  login(email, password) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const credential = yield _this3.auth.signInWithEmailAndPassword(email, password);\n        if (!credential.user) {\n          throw new Error('Login failed');\n        }\n        const userData = yield _this3.getUserData(credential.user.uid);\n        _this3.currentUserSubject.next(userData);\n        return userData;\n      } catch (error) {\n        console.error('Login error:', error);\n        throw error;\n      }\n    })();\n  }\n  logout() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this4.auth.signOut();\n        _this4.currentUserSubject.next(null);\n      } catch (error) {\n        console.error('Logout error:', error);\n        throw error;\n      }\n    })();\n  }\n  getUserData(uid) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      const userDoc = yield _this5.firestore.collection('users').doc(uid).get().toPromise();\n      if (userDoc && userDoc.exists) {\n        return userDoc.data();\n      }\n      throw new Error('User data not found');\n    })();\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  isAuthenticated() {\n    return this.currentUserSubject.value !== null;\n  }\n  hasRole(role) {\n    const user = this.getCurrentUser();\n    return user?.role === role;\n  }\n  hasAnyRole(roles) {\n    const user = this.getCurrentUser();\n    return user ? roles.includes(user.role) : false;\n  }\n  updateUserProfile(updates) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      const user = _this6.getCurrentUser();\n      if (!user) throw new Error('No authenticated user');\n      const updatedUser = {\n        ...user,\n        ...updates,\n        updatedAt: new Date()\n      };\n      yield _this6.firestore.collection('users').doc(user.uid).update(updatedUser);\n      _this6.currentUserSubject.next(updatedUser);\n    })();\n  }\n  // Password reset\n  resetPassword(email) {\n    return _asyncToGenerator(function* () {\n      // Implementation would use Firebase Auth sendPasswordResetEmail\n      // For now, just a placeholder\n      console.log('Password reset requested for:', email);\n    })();\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)(i0.ɵɵinject(i1.AngularFireAuth), i0.ɵɵinject(i2.AngularFirestore));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "AuthService", "constructor", "auth", "firestore", "_this", "currentUserSubject", "currentUser$", "asObservable", "authState", "subscribe", "_ref", "_asyncToGenerator", "firebaseUser", "userData", "getUserData", "uid", "next", "_x", "apply", "arguments", "register", "_this2", "credential", "createUserWithEmailAndPassword", "email", "password", "user", "Error", "name", "phone", "city", "role", "points", "createdAt", "Date", "updatedAt", "isActive", "history", "collection", "doc", "set", "error", "console", "login", "_this3", "signInWithEmailAndPassword", "logout", "_this4", "signOut", "_this5", "userDoc", "get", "to<PERSON>romise", "exists", "data", "getCurrentUser", "value", "isAuthenticated", "hasRole", "hasAnyRole", "roles", "includes", "updateUserProfile", "updates", "_this6", "updatedUser", "update", "resetPassword", "log", "i0", "ɵɵinject", "i1", "AngularFireAuth", "i2", "AngularFirestore", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { AngularFireAuth } from '@angular/fire/compat/auth';\nimport { AngularFirestore } from '@angular/fire/compat/firestore';\nimport { BehaviorSubject, Observable, from, map, switchMap, of } from 'rxjs';\nimport { User, CreateUserRequest, UserRole } from '../models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  public currentUser$ = this.currentUserSubject.asObservable();\n\n  constructor(\n    private auth: AngularFireAuth,\n    private firestore: AngularFirestore\n  ) {\n    // Listen to auth state changes\n    this.auth.authState.subscribe(async (firebaseUser) => {\n      if (firebaseUser) {\n        const userData = await this.getUserData(firebaseUser.uid);\n        this.currentUserSubject.next(userData);\n      } else {\n        this.currentUserSubject.next(null);\n      }\n    });\n  }\n\n  async register(userData: CreateUserRequest): Promise<User> {\n    try {\n      // Create Firebase Auth user\n      const credential = await this.auth.createUserWithEmailAndPassword(\n        userData.email,\n        userData.password\n      );\n\n      if (!credential.user) {\n        throw new Error('Failed to create user');\n      }\n\n      // Create user document in Firestore\n      const user: User = {\n        uid: credential.user.uid,\n        email: userData.email,\n        name: userData.name,\n        phone: userData.phone,\n        city: userData.city,\n        role: userData.role,\n        points: 0,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n        isActive: true,\n        history: []\n      };\n\n      await this.firestore.collection('users').doc(user.uid).set(user);\n      this.currentUserSubject.next(user);\n\n      return user;\n    } catch (error) {\n      console.error('Registration error:', error);\n      throw error;\n    }\n  }\n\n  async login(email: string, password: string): Promise<User> {\n    try {\n      const credential = await this.auth.signInWithEmailAndPassword(email, password);\n      if (!credential.user) {\n        throw new Error('Login failed');\n      }\n      const userData = await this.getUserData(credential.user.uid);\n      this.currentUserSubject.next(userData);\n      return userData;\n    } catch (error) {\n      console.error('Login error:', error);\n      throw error;\n    }\n  }\n\n  async logout(): Promise<void> {\n    try {\n      await this.auth.signOut();\n      this.currentUserSubject.next(null);\n    } catch (error) {\n      console.error('Logout error:', error);\n      throw error;\n    }\n  }\n\n  private async getUserData(uid: string): Promise<User> {\n    const userDoc = await this.firestore.collection('users').doc(uid).get().toPromise();\n    if (userDoc && userDoc.exists) {\n      return userDoc.data() as User;\n    }\n    throw new Error('User data not found');\n  }\n\n  getCurrentUser(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  isAuthenticated(): boolean {\n    return this.currentUserSubject.value !== null;\n  }\n\n  hasRole(role: UserRole): boolean {\n    const user = this.getCurrentUser();\n    return user?.role === role;\n  }\n\n  hasAnyRole(roles: UserRole[]): boolean {\n    const user = this.getCurrentUser();\n    return user ? roles.includes(user.role) : false;\n  }\n\n  async updateUserProfile(updates: Partial<User>): Promise<void> {\n    const user = this.getCurrentUser();\n    if (!user) throw new Error('No authenticated user');\n\n    const updatedUser = { ...user, ...updates, updatedAt: new Date() };\n    await this.firestore.collection('users').doc(user.uid).update(updatedUser);\n    this.currentUserSubject.next(updatedUser);\n  }\n\n  // Password reset\n  async resetPassword(email: string): Promise<void> {\n    // Implementation would use Firebase Auth sendPasswordResetEmail\n    // For now, just a placeholder\n    console.log('Password reset requested for:', email);\n  }\n}\n"], "mappings": ";AAGA,SAASA,eAAe,QAA8C,MAAM;;;;AAM5E,OAAM,MAAOC,WAAW;EAItBC,YACUC,IAAqB,EACrBC,SAA2B;IAAA,IAAAC,KAAA;IAD3B,KAAAF,IAAI,GAAJA,IAAI;IACJ,KAAAC,SAAS,GAATA,SAAS;IALX,KAAAE,kBAAkB,GAAG,IAAIN,eAAe,CAAc,IAAI,CAAC;IAC5D,KAAAO,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IAM1D;IACA,IAAI,CAACL,IAAI,CAACM,SAAS,CAACC,SAAS;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAC,WAAOC,YAAY,EAAI;QACnD,IAAIA,YAAY,EAAE;UAChB,MAAMC,QAAQ,SAAST,KAAI,CAACU,WAAW,CAACF,YAAY,CAACG,GAAG,CAAC;UACzDX,KAAI,CAACC,kBAAkB,CAACW,IAAI,CAACH,QAAQ,CAAC;SACvC,MAAM;UACLT,KAAI,CAACC,kBAAkB,CAACW,IAAI,CAAC,IAAI,CAAC;;MAEtC,CAAC;MAAA,iBAAAC,EAAA;QAAA,OAAAP,IAAA,CAAAQ,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC;EACJ;EAEMC,QAAQA,CAACP,QAA2B;IAAA,IAAAQ,MAAA;IAAA,OAAAV,iBAAA;MACxC,IAAI;QACF;QACA,MAAMW,UAAU,SAASD,MAAI,CAACnB,IAAI,CAACqB,8BAA8B,CAC/DV,QAAQ,CAACW,KAAK,EACdX,QAAQ,CAACY,QAAQ,CAClB;QAED,IAAI,CAACH,UAAU,CAACI,IAAI,EAAE;UACpB,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;;QAG1C;QACA,MAAMD,IAAI,GAAS;UACjBX,GAAG,EAAEO,UAAU,CAACI,IAAI,CAACX,GAAG;UACxBS,KAAK,EAAEX,QAAQ,CAACW,KAAK;UACrBI,IAAI,EAAEf,QAAQ,CAACe,IAAI;UACnBC,KAAK,EAAEhB,QAAQ,CAACgB,KAAK;UACrBC,IAAI,EAAEjB,QAAQ,CAACiB,IAAI;UACnBC,IAAI,EAAElB,QAAQ,CAACkB,IAAI;UACnBC,MAAM,EAAE,CAAC;UACTC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,SAAS,EAAE,IAAID,IAAI,EAAE;UACrBE,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE;SACV;QAED,MAAMhB,MAAI,CAAClB,SAAS,CAACmC,UAAU,CAAC,OAAO,CAAC,CAACC,GAAG,CAACb,IAAI,CAACX,GAAG,CAAC,CAACyB,GAAG,CAACd,IAAI,CAAC;QAChEL,MAAI,CAAChB,kBAAkB,CAACW,IAAI,CAACU,IAAI,CAAC;QAElC,OAAOA,IAAI;OACZ,CAAC,OAAOe,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3C,MAAMA,KAAK;;IACZ;EACH;EAEME,KAAKA,CAACnB,KAAa,EAAEC,QAAgB;IAAA,IAAAmB,MAAA;IAAA,OAAAjC,iBAAA;MACzC,IAAI;QACF,MAAMW,UAAU,SAASsB,MAAI,CAAC1C,IAAI,CAAC2C,0BAA0B,CAACrB,KAAK,EAAEC,QAAQ,CAAC;QAC9E,IAAI,CAACH,UAAU,CAACI,IAAI,EAAE;UACpB,MAAM,IAAIC,KAAK,CAAC,cAAc,CAAC;;QAEjC,MAAMd,QAAQ,SAAS+B,MAAI,CAAC9B,WAAW,CAACQ,UAAU,CAACI,IAAI,CAACX,GAAG,CAAC;QAC5D6B,MAAI,CAACvC,kBAAkB,CAACW,IAAI,CAACH,QAAQ,CAAC;QACtC,OAAOA,QAAQ;OAChB,CAAC,OAAO4B,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;QACpC,MAAMA,KAAK;;IACZ;EACH;EAEMK,MAAMA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAApC,iBAAA;MACV,IAAI;QACF,MAAMoC,MAAI,CAAC7C,IAAI,CAAC8C,OAAO,EAAE;QACzBD,MAAI,CAAC1C,kBAAkB,CAACW,IAAI,CAAC,IAAI,CAAC;OACnC,CAAC,OAAOyB,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;QACrC,MAAMA,KAAK;;IACZ;EACH;EAEc3B,WAAWA,CAACC,GAAW;IAAA,IAAAkC,MAAA;IAAA,OAAAtC,iBAAA;MACnC,MAAMuC,OAAO,SAASD,MAAI,CAAC9C,SAAS,CAACmC,UAAU,CAAC,OAAO,CAAC,CAACC,GAAG,CAACxB,GAAG,CAAC,CAACoC,GAAG,EAAE,CAACC,SAAS,EAAE;MACnF,IAAIF,OAAO,IAAIA,OAAO,CAACG,MAAM,EAAE;QAC7B,OAAOH,OAAO,CAACI,IAAI,EAAU;;MAE/B,MAAM,IAAI3B,KAAK,CAAC,qBAAqB,CAAC;IAAC;EACzC;EAEA4B,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAClD,kBAAkB,CAACmD,KAAK;EACtC;EAEAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACpD,kBAAkB,CAACmD,KAAK,KAAK,IAAI;EAC/C;EAEAE,OAAOA,CAAC3B,IAAc;IACpB,MAAML,IAAI,GAAG,IAAI,CAAC6B,cAAc,EAAE;IAClC,OAAO7B,IAAI,EAAEK,IAAI,KAAKA,IAAI;EAC5B;EAEA4B,UAAUA,CAACC,KAAiB;IAC1B,MAAMlC,IAAI,GAAG,IAAI,CAAC6B,cAAc,EAAE;IAClC,OAAO7B,IAAI,GAAGkC,KAAK,CAACC,QAAQ,CAACnC,IAAI,CAACK,IAAI,CAAC,GAAG,KAAK;EACjD;EAEM+B,iBAAiBA,CAACC,OAAsB;IAAA,IAAAC,MAAA;IAAA,OAAArD,iBAAA;MAC5C,MAAMe,IAAI,GAAGsC,MAAI,CAACT,cAAc,EAAE;MAClC,IAAI,CAAC7B,IAAI,EAAE,MAAM,IAAIC,KAAK,CAAC,uBAAuB,CAAC;MAEnD,MAAMsC,WAAW,GAAG;QAAE,GAAGvC,IAAI;QAAE,GAAGqC,OAAO;QAAE5B,SAAS,EAAE,IAAID,IAAI;MAAE,CAAE;MAClE,MAAM8B,MAAI,CAAC7D,SAAS,CAACmC,UAAU,CAAC,OAAO,CAAC,CAACC,GAAG,CAACb,IAAI,CAACX,GAAG,CAAC,CAACmD,MAAM,CAACD,WAAW,CAAC;MAC1ED,MAAI,CAAC3D,kBAAkB,CAACW,IAAI,CAACiD,WAAW,CAAC;IAAC;EAC5C;EAEA;EACME,aAAaA,CAAC3C,KAAa;IAAA,OAAAb,iBAAA;MAC/B;MACA;MACA+B,OAAO,CAAC0B,GAAG,CAAC,+BAA+B,EAAE5C,KAAK,CAAC;IAAC;EACtD;;;uBAzHWxB,WAAW,EAAAqE,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,eAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;aAAX1E,WAAW;MAAA2E,OAAA,EAAX3E,WAAW,CAAA4E,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}