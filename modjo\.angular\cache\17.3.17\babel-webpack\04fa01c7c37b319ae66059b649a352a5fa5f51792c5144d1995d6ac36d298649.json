{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class AuditLogComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function AuditLogComponent_Factory(t) {\n      return new (t || AuditLogComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AuditLogComponent,\n      selectors: [[\"app-audit-log\"]],\n      decls: 5,\n      vars: 0,\n      template: function AuditLogComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"h2\");\n          i0.ɵɵtext(2, \"\\uD83D\\uDCCB Journal d'Audit\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"Historique complet des actions syst\\u00E8me\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\"div[_ngcontent-%COMP%] { padding: 20px; }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImF1ZGl0LWxvZy5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsTUFBTSxhQUFhLEVBQUUiLCJmaWxlIjoiYXVkaXQtbG9nLmNvbXBvbmVudC50cyIsInNvdXJjZXNDb250ZW50IjpbImRpdiB7IHBhZGRpbmc6IDIwcHg7IH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvYWRtaW4tZGFzaGJvYXJkL2NvbXBvbmVudHMvYXVkaXQtbG9nL2F1ZGl0LWxvZy5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsTUFBTSxhQUFhLEVBQUU7QUFDckIsd1JBQXdSIiwic291cmNlc0NvbnRlbnQiOlsiZGl2IHsgcGFkZGluZzogMjBweDsgfSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["AuditLogComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "template", "AuditLogComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\admin-dashboard\\components\\audit-log\\audit-log.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-audit-log',\n  template: '<div><h2>📋 Journal d\\'Audit</h2><p>Historique complet des actions système</p></div>',\n  styles: ['div { padding: 20px; }']\n})\nexport class AuditLogComponent implements OnInit {\n  constructor() { }\n  ngOnInit(): void { }\n}\n"], "mappings": ";AAOA,OAAM,MAAOA,iBAAiB;EAC5BC,YAAA,GAAgB;EAChBC,QAAQA,CAAA,GAAW;;;uBAFRF,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAHZE,EAAL,CAAAC,cAAA,UAAK,SAAI;UAAAD,EAAA,CAAAE,MAAA,mCAAmB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAAAH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,kDAAsC;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}