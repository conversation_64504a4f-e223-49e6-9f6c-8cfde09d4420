{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { _getProvider, getApp, _registerComponent, registerVersion } from '@firebase/app';\nimport { Component } from '@firebase/component';\nimport { Deferred, ErrorFactory, isIndexedDBAvailable, uuidv4, getGlobal, base64, issuedAtTime, calculateBackoffMillis, getModularInstance } from '@firebase/util';\nimport { Logger } from '@firebase/logger';\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst APP_CHECK_STATES = new Map();\nconst DEFAULT_STATE = {\n  activated: false,\n  tokenObservers: []\n};\nconst DEBUG_STATE = {\n  initialized: false,\n  enabled: false\n};\n/**\r\n * Gets a reference to the state object.\r\n */\nfunction getStateReference(app) {\n  return APP_CHECK_STATES.get(app) || Object.assign({}, DEFAULT_STATE);\n}\n/**\r\n * Set once on initialization. The map should hold the same reference to the\r\n * same object until this entry is deleted.\r\n */\nfunction setInitialState(app, state) {\n  APP_CHECK_STATES.set(app, state);\n  return APP_CHECK_STATES.get(app);\n}\nfunction getDebugState() {\n  return DEBUG_STATE;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst BASE_ENDPOINT = 'https://content-firebaseappcheck.googleapis.com/v1';\nconst EXCHANGE_RECAPTCHA_TOKEN_METHOD = 'exchangeRecaptchaV3Token';\nconst EXCHANGE_RECAPTCHA_ENTERPRISE_TOKEN_METHOD = 'exchangeRecaptchaEnterpriseToken';\nconst EXCHANGE_DEBUG_TOKEN_METHOD = 'exchangeDebugToken';\nconst TOKEN_REFRESH_TIME = {\n  /**\r\n   * The offset time before token natural expiration to run the refresh.\r\n   * This is currently 5 minutes.\r\n   */\n  OFFSET_DURATION: 5 * 60 * 1000,\n  /**\r\n   * This is the first retrial wait after an error. This is currently\r\n   * 30 seconds.\r\n   */\n  RETRIAL_MIN_WAIT: 30 * 1000,\n  /**\r\n   * This is the maximum retrial wait, currently 16 minutes.\r\n   */\n  RETRIAL_MAX_WAIT: 16 * 60 * 1000\n};\n/**\r\n * One day in millis, for certain error code backoffs.\r\n */\nconst ONE_DAY = 24 * 60 * 60 * 1000;\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Port from auth proactiverefresh.js\r\n *\r\n */\n// TODO: move it to @firebase/util?\n// TODO: allow to config whether refresh should happen in the background\nclass Refresher {\n  constructor(operation, retryPolicy, getWaitDuration, lowerBound, upperBound) {\n    this.operation = operation;\n    this.retryPolicy = retryPolicy;\n    this.getWaitDuration = getWaitDuration;\n    this.lowerBound = lowerBound;\n    this.upperBound = upperBound;\n    this.pending = null;\n    this.nextErrorWaitInterval = lowerBound;\n    if (lowerBound > upperBound) {\n      throw new Error('Proactive refresh lower bound greater than upper bound!');\n    }\n  }\n  start() {\n    this.nextErrorWaitInterval = this.lowerBound;\n    this.process(true).catch(() => {\n      /* we don't care about the result */\n    });\n  }\n  stop() {\n    if (this.pending) {\n      this.pending.reject('cancelled');\n      this.pending = null;\n    }\n  }\n  isRunning() {\n    return !!this.pending;\n  }\n  process(hasSucceeded) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      _this.stop();\n      try {\n        _this.pending = new Deferred();\n        yield sleep(_this.getNextRun(hasSucceeded));\n        // Why do we resolve a promise, then immediate wait for it?\n        // We do it to make the promise chain cancellable.\n        // We can call stop() which rejects the promise before the following line execute, which makes\n        // the code jump to the catch block.\n        // TODO: unit test this\n        _this.pending.resolve();\n        yield _this.pending.promise;\n        _this.pending = new Deferred();\n        yield _this.operation();\n        _this.pending.resolve();\n        yield _this.pending.promise;\n        _this.process(true).catch(() => {\n          /* we don't care about the result */\n        });\n      } catch (error) {\n        if (_this.retryPolicy(error)) {\n          _this.process(false).catch(() => {\n            /* we don't care about the result */\n          });\n        } else {\n          _this.stop();\n        }\n      }\n    })();\n  }\n  getNextRun(hasSucceeded) {\n    if (hasSucceeded) {\n      // If last operation succeeded, reset next error wait interval and return\n      // the default wait duration.\n      this.nextErrorWaitInterval = this.lowerBound;\n      // Return typical wait duration interval after a successful operation.\n      return this.getWaitDuration();\n    } else {\n      // Get next error wait interval.\n      const currentErrorWaitInterval = this.nextErrorWaitInterval;\n      // Double interval for next consecutive error.\n      this.nextErrorWaitInterval *= 2;\n      // Make sure next wait interval does not exceed the maximum upper bound.\n      if (this.nextErrorWaitInterval > this.upperBound) {\n        this.nextErrorWaitInterval = this.upperBound;\n      }\n      return currentErrorWaitInterval;\n    }\n  }\n}\nfunction sleep(ms) {\n  return new Promise(resolve => {\n    setTimeout(resolve, ms);\n  });\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst ERRORS = {\n  [\"already-initialized\" /* AppCheckError.ALREADY_INITIALIZED */]: 'You have already called initializeAppCheck() for FirebaseApp {$appName} with ' + 'different options. To avoid this error, call initializeAppCheck() with the ' + 'same options as when it was originally called. This will return the ' + 'already initialized instance.',\n  [\"use-before-activation\" /* AppCheckError.USE_BEFORE_ACTIVATION */]: 'App Check is being used before initializeAppCheck() is called for FirebaseApp {$appName}. ' + 'Call initializeAppCheck() before instantiating other Firebase services.',\n  [\"fetch-network-error\" /* AppCheckError.FETCH_NETWORK_ERROR */]: 'Fetch failed to connect to a network. Check Internet connection. ' + 'Original error: {$originalErrorMessage}.',\n  [\"fetch-parse-error\" /* AppCheckError.FETCH_PARSE_ERROR */]: 'Fetch client could not parse response.' + ' Original error: {$originalErrorMessage}.',\n  [\"fetch-status-error\" /* AppCheckError.FETCH_STATUS_ERROR */]: 'Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.',\n  [\"storage-open\" /* AppCheckError.STORAGE_OPEN */]: 'Error thrown when opening storage. Original error: {$originalErrorMessage}.',\n  [\"storage-get\" /* AppCheckError.STORAGE_GET */]: 'Error thrown when reading from storage. Original error: {$originalErrorMessage}.',\n  [\"storage-set\" /* AppCheckError.STORAGE_WRITE */]: 'Error thrown when writing to storage. Original error: {$originalErrorMessage}.',\n  [\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */]: 'ReCAPTCHA error.',\n  [\"throttled\" /* AppCheckError.THROTTLED */]: `Requests throttled due to {$httpStatus} error. Attempts allowed again after {$time}`\n};\nconst ERROR_FACTORY = new ErrorFactory('appCheck', 'AppCheck', ERRORS);\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction getRecaptcha(isEnterprise = false) {\n  var _a;\n  if (isEnterprise) {\n    return (_a = self.grecaptcha) === null || _a === void 0 ? void 0 : _a.enterprise;\n  }\n  return self.grecaptcha;\n}\nfunction ensureActivated(app) {\n  if (!getStateReference(app).activated) {\n    throw ERROR_FACTORY.create(\"use-before-activation\" /* AppCheckError.USE_BEFORE_ACTIVATION */, {\n      appName: app.name\n    });\n  }\n}\nfunction getDurationString(durationInMillis) {\n  const totalSeconds = Math.round(durationInMillis / 1000);\n  const days = Math.floor(totalSeconds / (3600 * 24));\n  const hours = Math.floor((totalSeconds - days * 3600 * 24) / 3600);\n  const minutes = Math.floor((totalSeconds - days * 3600 * 24 - hours * 3600) / 60);\n  const seconds = totalSeconds - days * 3600 * 24 - hours * 3600 - minutes * 60;\n  let result = '';\n  if (days) {\n    result += pad(days) + 'd:';\n  }\n  if (hours) {\n    result += pad(hours) + 'h:';\n  }\n  result += pad(minutes) + 'm:' + pad(seconds) + 's';\n  return result;\n}\nfunction pad(value) {\n  if (value === 0) {\n    return '00';\n  }\n  return value >= 10 ? value.toString() : '0' + value;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction exchangeToken(_x, _x2) {\n  return _exchangeToken.apply(this, arguments);\n}\nfunction _exchangeToken() {\n  _exchangeToken = _asyncToGenerator(function* ({\n    url,\n    body\n  }, heartbeatServiceProvider) {\n    const headers = {\n      'Content-Type': 'application/json'\n    };\n    // If heartbeat service exists, add heartbeat header string to the header.\n    const heartbeatService = heartbeatServiceProvider.getImmediate({\n      optional: true\n    });\n    if (heartbeatService) {\n      const heartbeatsHeader = yield heartbeatService.getHeartbeatsHeader();\n      if (heartbeatsHeader) {\n        headers['X-Firebase-Client'] = heartbeatsHeader;\n      }\n    }\n    const options = {\n      method: 'POST',\n      body: JSON.stringify(body),\n      headers\n    };\n    let response;\n    try {\n      response = yield fetch(url, options);\n    } catch (originalError) {\n      throw ERROR_FACTORY.create(\"fetch-network-error\" /* AppCheckError.FETCH_NETWORK_ERROR */, {\n        originalErrorMessage: originalError === null || originalError === void 0 ? void 0 : originalError.message\n      });\n    }\n    if (response.status !== 200) {\n      throw ERROR_FACTORY.create(\"fetch-status-error\" /* AppCheckError.FETCH_STATUS_ERROR */, {\n        httpStatus: response.status\n      });\n    }\n    let responseBody;\n    try {\n      // JSON parsing throws SyntaxError if the response body isn't a JSON string.\n      responseBody = yield response.json();\n    } catch (originalError) {\n      throw ERROR_FACTORY.create(\"fetch-parse-error\" /* AppCheckError.FETCH_PARSE_ERROR */, {\n        originalErrorMessage: originalError === null || originalError === void 0 ? void 0 : originalError.message\n      });\n    }\n    // Protobuf duration format.\n    // https://developers.google.com/protocol-buffers/docs/reference/java/com/google/protobuf/Duration\n    const match = responseBody.ttl.match(/^([\\d.]+)(s)$/);\n    if (!match || !match[2] || isNaN(Number(match[1]))) {\n      throw ERROR_FACTORY.create(\"fetch-parse-error\" /* AppCheckError.FETCH_PARSE_ERROR */, {\n        originalErrorMessage: `ttl field (timeToLive) is not in standard Protobuf Duration ` + `format: ${responseBody.ttl}`\n      });\n    }\n    const timeToLiveAsNumber = Number(match[1]) * 1000;\n    const now = Date.now();\n    return {\n      token: responseBody.token,\n      expireTimeMillis: now + timeToLiveAsNumber,\n      issuedAtTimeMillis: now\n    };\n  });\n  return _exchangeToken.apply(this, arguments);\n}\nfunction getExchangeRecaptchaV3TokenRequest(app, reCAPTCHAToken) {\n  const {\n    projectId,\n    appId,\n    apiKey\n  } = app.options;\n  return {\n    url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_RECAPTCHA_TOKEN_METHOD}?key=${apiKey}`,\n    body: {\n      'recaptcha_v3_token': reCAPTCHAToken\n    }\n  };\n}\nfunction getExchangeRecaptchaEnterpriseTokenRequest(app, reCAPTCHAToken) {\n  const {\n    projectId,\n    appId,\n    apiKey\n  } = app.options;\n  return {\n    url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_RECAPTCHA_ENTERPRISE_TOKEN_METHOD}?key=${apiKey}`,\n    body: {\n      'recaptcha_enterprise_token': reCAPTCHAToken\n    }\n  };\n}\nfunction getExchangeDebugTokenRequest(app, debugToken) {\n  const {\n    projectId,\n    appId,\n    apiKey\n  } = app.options;\n  return {\n    url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_DEBUG_TOKEN_METHOD}?key=${apiKey}`,\n    body: {\n      // eslint-disable-next-line\n      debug_token: debugToken\n    }\n  };\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst DB_NAME = 'firebase-app-check-database';\nconst DB_VERSION = 1;\nconst STORE_NAME = 'firebase-app-check-store';\nconst DEBUG_TOKEN_KEY = 'debug-token';\nlet dbPromise = null;\nfunction getDBPromise() {\n  if (dbPromise) {\n    return dbPromise;\n  }\n  dbPromise = new Promise((resolve, reject) => {\n    try {\n      const request = indexedDB.open(DB_NAME, DB_VERSION);\n      request.onsuccess = event => {\n        resolve(event.target.result);\n      };\n      request.onerror = event => {\n        var _a;\n        reject(ERROR_FACTORY.create(\"storage-open\" /* AppCheckError.STORAGE_OPEN */, {\n          originalErrorMessage: (_a = event.target.error) === null || _a === void 0 ? void 0 : _a.message\n        }));\n      };\n      request.onupgradeneeded = event => {\n        const db = event.target.result;\n        // We don't use 'break' in this switch statement, the fall-through\n        // behavior is what we want, because if there are multiple versions between\n        // the old version and the current version, we want ALL the migrations\n        // that correspond to those versions to run, not only the last one.\n        // eslint-disable-next-line default-case\n        switch (event.oldVersion) {\n          case 0:\n            db.createObjectStore(STORE_NAME, {\n              keyPath: 'compositeKey'\n            });\n        }\n      };\n    } catch (e) {\n      reject(ERROR_FACTORY.create(\"storage-open\" /* AppCheckError.STORAGE_OPEN */, {\n        originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\n      }));\n    }\n  });\n  return dbPromise;\n}\nfunction readTokenFromIndexedDB(app) {\n  return read(computeKey(app));\n}\nfunction writeTokenToIndexedDB(app, token) {\n  return write(computeKey(app), token);\n}\nfunction writeDebugTokenToIndexedDB(token) {\n  return write(DEBUG_TOKEN_KEY, token);\n}\nfunction readDebugTokenFromIndexedDB() {\n  return read(DEBUG_TOKEN_KEY);\n}\nfunction write(_x3, _x4) {\n  return _write.apply(this, arguments);\n}\nfunction _write() {\n  _write = _asyncToGenerator(function* (key, value) {\n    const db = yield getDBPromise();\n    const transaction = db.transaction(STORE_NAME, 'readwrite');\n    const store = transaction.objectStore(STORE_NAME);\n    const request = store.put({\n      compositeKey: key,\n      value\n    });\n    return new Promise((resolve, reject) => {\n      request.onsuccess = _event => {\n        resolve();\n      };\n      transaction.onerror = event => {\n        var _a;\n        reject(ERROR_FACTORY.create(\"storage-set\" /* AppCheckError.STORAGE_WRITE */, {\n          originalErrorMessage: (_a = event.target.error) === null || _a === void 0 ? void 0 : _a.message\n        }));\n      };\n    });\n  });\n  return _write.apply(this, arguments);\n}\nfunction read(_x5) {\n  return _read.apply(this, arguments);\n}\nfunction _read() {\n  _read = _asyncToGenerator(function* (key) {\n    const db = yield getDBPromise();\n    const transaction = db.transaction(STORE_NAME, 'readonly');\n    const store = transaction.objectStore(STORE_NAME);\n    const request = store.get(key);\n    return new Promise((resolve, reject) => {\n      request.onsuccess = event => {\n        const result = event.target.result;\n        if (result) {\n          resolve(result.value);\n        } else {\n          resolve(undefined);\n        }\n      };\n      transaction.onerror = event => {\n        var _a;\n        reject(ERROR_FACTORY.create(\"storage-get\" /* AppCheckError.STORAGE_GET */, {\n          originalErrorMessage: (_a = event.target.error) === null || _a === void 0 ? void 0 : _a.message\n        }));\n      };\n    });\n  });\n  return _read.apply(this, arguments);\n}\nfunction computeKey(app) {\n  return `${app.options.appId}-${app.name}`;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst logger = new Logger('@firebase/app-check');\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Always resolves. In case of an error reading from indexeddb, resolve with undefined\r\n */\nfunction readTokenFromStorage(_x6) {\n  return _readTokenFromStorage.apply(this, arguments);\n}\n/**\r\n * Always resolves. In case of an error writing to indexeddb, print a warning and resolve the promise\r\n */\nfunction _readTokenFromStorage() {\n  _readTokenFromStorage = _asyncToGenerator(function* (app) {\n    if (isIndexedDBAvailable()) {\n      let token = undefined;\n      try {\n        token = yield readTokenFromIndexedDB(app);\n      } catch (e) {\n        // swallow the error and return undefined\n        logger.warn(`Failed to read token from IndexedDB. Error: ${e}`);\n      }\n      return token;\n    }\n    return undefined;\n  });\n  return _readTokenFromStorage.apply(this, arguments);\n}\nfunction writeTokenToStorage(app, token) {\n  if (isIndexedDBAvailable()) {\n    return writeTokenToIndexedDB(app, token).catch(e => {\n      // swallow the error and resolve the promise\n      logger.warn(`Failed to write token to IndexedDB. Error: ${e}`);\n    });\n  }\n  return Promise.resolve();\n}\nfunction readOrCreateDebugTokenFromStorage() {\n  return _readOrCreateDebugTokenFromStorage.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction _readOrCreateDebugTokenFromStorage() {\n  _readOrCreateDebugTokenFromStorage = _asyncToGenerator(function* () {\n    /**\r\n     * Theoretically race condition can happen if we read, then write in 2 separate transactions.\r\n     * But it won't happen here, because this function will be called exactly once.\r\n     */\n    let existingDebugToken = undefined;\n    try {\n      existingDebugToken = yield readDebugTokenFromIndexedDB();\n    } catch (_e) {\n      // failed to read from indexeddb. We assume there is no existing debug token, and generate a new one.\n    }\n    if (!existingDebugToken) {\n      // create a new debug token\n      const newToken = uuidv4();\n      // We don't need to block on writing to indexeddb\n      // In case persistence failed, a new debug token will be generated everytime the page is refreshed.\n      // It renders the debug token useless because you have to manually register(whitelist) the new token in the firebase console again and again.\n      // If you see this error trying to use debug token, it probably means you are using a browser that doesn't support indexeddb.\n      // You should switch to a different browser that supports indexeddb\n      writeDebugTokenToIndexedDB(newToken).catch(e => logger.warn(`Failed to persist debug token to IndexedDB. Error: ${e}`));\n      return newToken;\n    } else {\n      return existingDebugToken;\n    }\n  });\n  return _readOrCreateDebugTokenFromStorage.apply(this, arguments);\n}\nfunction isDebugMode() {\n  const debugState = getDebugState();\n  return debugState.enabled;\n}\nfunction getDebugToken() {\n  return _getDebugToken.apply(this, arguments);\n}\nfunction _getDebugToken() {\n  _getDebugToken = _asyncToGenerator(function* () {\n    const state = getDebugState();\n    if (state.enabled && state.token) {\n      return state.token.promise;\n    } else {\n      // should not happen!\n      throw Error(`\n            Can't get debug token in production mode.\n        `);\n    }\n  });\n  return _getDebugToken.apply(this, arguments);\n}\nfunction initializeDebugMode() {\n  const globals = getGlobal();\n  const debugState = getDebugState();\n  // Set to true if this function has been called, whether or not\n  // it enabled debug mode.\n  debugState.initialized = true;\n  if (typeof globals.FIREBASE_APPCHECK_DEBUG_TOKEN !== 'string' && globals.FIREBASE_APPCHECK_DEBUG_TOKEN !== true) {\n    return;\n  }\n  debugState.enabled = true;\n  const deferredToken = new Deferred();\n  debugState.token = deferredToken;\n  if (typeof globals.FIREBASE_APPCHECK_DEBUG_TOKEN === 'string') {\n    deferredToken.resolve(globals.FIREBASE_APPCHECK_DEBUG_TOKEN);\n  } else {\n    deferredToken.resolve(readOrCreateDebugTokenFromStorage());\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// Initial hardcoded value agreed upon across platforms for initial launch.\n// Format left open for possible dynamic error values and other fields in the future.\nconst defaultTokenErrorData = {\n  error: 'UNKNOWN_ERROR'\n};\n/**\r\n * Stringify and base64 encode token error data.\r\n *\r\n * @param tokenError Error data, currently hardcoded.\r\n */\nfunction formatDummyToken(tokenErrorData) {\n  return base64.encodeString(JSON.stringify(tokenErrorData), /* webSafe= */false);\n}\n/**\r\n * This function always resolves.\r\n * The result will contain an error field if there is any error.\r\n * In case there is an error, the token field in the result will be populated with a dummy value\r\n */\nfunction getToken$2(_x7) {\n  return _getToken$.apply(this, arguments);\n}\n/**\r\n * Internal API for limited use tokens. Skips all FAC state and simply calls\r\n * the underlying provider.\r\n */\nfunction _getToken$() {\n  _getToken$ = _asyncToGenerator(function* (appCheck, forceRefresh = false) {\n    const app = appCheck.app;\n    ensureActivated(app);\n    const state = getStateReference(app);\n    /**\r\n     * First check if there is a token in memory from a previous `getToken()` call.\r\n     */\n    let token = state.token;\n    let error = undefined;\n    /**\r\n     * If an invalid token was found in memory, clear token from\r\n     * memory and unset the local variable `token`.\r\n     */\n    if (token && !isValid(token)) {\n      state.token = undefined;\n      token = undefined;\n    }\n    /**\r\n     * If there is no valid token in memory, try to load token from indexedDB.\r\n     */\n    if (!token) {\n      // cachedTokenPromise contains the token found in IndexedDB or undefined if not found.\n      const cachedToken = yield state.cachedTokenPromise;\n      if (cachedToken) {\n        if (isValid(cachedToken)) {\n          token = cachedToken;\n        } else {\n          // If there was an invalid token in the indexedDB cache, clear it.\n          yield writeTokenToStorage(app, undefined);\n        }\n      }\n    }\n    // Return the cached token (from either memory or indexedDB) if it's valid\n    if (!forceRefresh && token && isValid(token)) {\n      return {\n        token: token.token\n      };\n    }\n    // Only set to true if this `getToken()` call is making the actual\n    // REST call to the exchange endpoint, versus waiting for an already\n    // in-flight call (see debug and regular exchange endpoint paths below)\n    let shouldCallListeners = false;\n    /**\r\n     * DEBUG MODE\r\n     * If debug mode is set, and there is no cached token, fetch a new App\r\n     * Check token using the debug token, and return it directly.\r\n     */\n    if (isDebugMode()) {\n      // Avoid making another call to the exchange endpoint if one is in flight.\n      if (!state.exchangeTokenPromise) {\n        state.exchangeTokenPromise = exchangeToken(getExchangeDebugTokenRequest(app, yield getDebugToken()), appCheck.heartbeatServiceProvider).finally(() => {\n          // Clear promise when settled - either resolved or rejected.\n          state.exchangeTokenPromise = undefined;\n        });\n        shouldCallListeners = true;\n      }\n      const tokenFromDebugExchange = yield state.exchangeTokenPromise;\n      // Write debug token to indexedDB.\n      yield writeTokenToStorage(app, tokenFromDebugExchange);\n      // Write debug token to state.\n      state.token = tokenFromDebugExchange;\n      return {\n        token: tokenFromDebugExchange.token\n      };\n    }\n    /**\r\n     * There are no valid tokens in memory or indexedDB and we are not in\r\n     * debug mode.\r\n     * Request a new token from the exchange endpoint.\r\n     */\n    try {\n      // Avoid making another call to the exchange endpoint if one is in flight.\n      if (!state.exchangeTokenPromise) {\n        // state.provider is populated in initializeAppCheck()\n        // ensureActivated() at the top of this function checks that\n        // initializeAppCheck() has been called.\n        state.exchangeTokenPromise = state.provider.getToken().finally(() => {\n          // Clear promise when settled - either resolved or rejected.\n          state.exchangeTokenPromise = undefined;\n        });\n        shouldCallListeners = true;\n      }\n      token = yield getStateReference(app).exchangeTokenPromise;\n    } catch (e) {\n      if (e.code === `appCheck/${\"throttled\" /* AppCheckError.THROTTLED */}`) {\n        // Warn if throttled, but do not treat it as an error.\n        logger.warn(e.message);\n      } else {\n        // `getToken()` should never throw, but logging error text to console will aid debugging.\n        logger.error(e);\n      }\n      // Always save error to be added to dummy token.\n      error = e;\n    }\n    let interopTokenResult;\n    if (!token) {\n      // If token is undefined, there must be an error.\n      // Return a dummy token along with the error.\n      interopTokenResult = makeDummyTokenResult(error);\n    } else if (error) {\n      if (isValid(token)) {\n        // It's also possible a valid token exists, but there's also an error.\n        // (Such as if the token is almost expired, tries to refresh, and\n        // the exchange request fails.)\n        // We add a special error property here so that the refresher will\n        // count this as a failed attempt and use the backoff instead of\n        // retrying repeatedly with no delay, but any 3P listeners will not\n        // be hindered in getting the still-valid token.\n        interopTokenResult = {\n          token: token.token,\n          internalError: error\n        };\n      } else {\n        // No invalid tokens should make it to this step. Memory and cached tokens\n        // are checked. Other tokens are from fresh exchanges. But just in case.\n        interopTokenResult = makeDummyTokenResult(error);\n      }\n    } else {\n      interopTokenResult = {\n        token: token.token\n      };\n      // write the new token to the memory state as well as the persistent storage.\n      // Only do it if we got a valid new token\n      state.token = token;\n      yield writeTokenToStorage(app, token);\n    }\n    if (shouldCallListeners) {\n      notifyTokenListeners(app, interopTokenResult);\n    }\n    return interopTokenResult;\n  });\n  return _getToken$.apply(this, arguments);\n}\nfunction getLimitedUseToken$1(_x8) {\n  return _getLimitedUseToken$.apply(this, arguments);\n}\nfunction _getLimitedUseToken$() {\n  _getLimitedUseToken$ = _asyncToGenerator(function* (appCheck) {\n    const app = appCheck.app;\n    ensureActivated(app);\n    const {\n      provider\n    } = getStateReference(app);\n    if (isDebugMode()) {\n      const debugToken = yield getDebugToken();\n      const {\n        token\n      } = yield exchangeToken(getExchangeDebugTokenRequest(app, debugToken), appCheck.heartbeatServiceProvider);\n      return {\n        token\n      };\n    } else {\n      // provider is definitely valid since we ensure AppCheck was activated\n      const {\n        token\n      } = yield provider.getToken();\n      return {\n        token\n      };\n    }\n  });\n  return _getLimitedUseToken$.apply(this, arguments);\n}\nfunction addTokenListener(appCheck, type, listener, onError) {\n  const {\n    app\n  } = appCheck;\n  const state = getStateReference(app);\n  const tokenObserver = {\n    next: listener,\n    error: onError,\n    type\n  };\n  state.tokenObservers = [...state.tokenObservers, tokenObserver];\n  // Invoke the listener async immediately if there is a valid token\n  // in memory.\n  if (state.token && isValid(state.token)) {\n    const validToken = state.token;\n    Promise.resolve().then(() => {\n      listener({\n        token: validToken.token\n      });\n      initTokenRefresher(appCheck);\n    }).catch(() => {\n      /* we don't care about exceptions thrown in listeners */\n    });\n  }\n  /**\r\n   * Wait for any cached token promise to resolve before starting the token\r\n   * refresher. The refresher checks to see if there is an existing token\r\n   * in state and calls the exchange endpoint if not. We should first let the\r\n   * IndexedDB check have a chance to populate state if it can.\r\n   *\r\n   * Listener call isn't needed here because cachedTokenPromise will call any\r\n   * listeners that exist when it resolves.\r\n   */\n  // state.cachedTokenPromise is always populated in `activate()`.\n  void state.cachedTokenPromise.then(() => initTokenRefresher(appCheck));\n}\nfunction removeTokenListener(app, listener) {\n  const state = getStateReference(app);\n  const newObservers = state.tokenObservers.filter(tokenObserver => tokenObserver.next !== listener);\n  if (newObservers.length === 0 && state.tokenRefresher && state.tokenRefresher.isRunning()) {\n    state.tokenRefresher.stop();\n  }\n  state.tokenObservers = newObservers;\n}\n/**\r\n * Logic to create and start refresher as needed.\r\n */\nfunction initTokenRefresher(appCheck) {\n  const {\n    app\n  } = appCheck;\n  const state = getStateReference(app);\n  // Create the refresher but don't start it if `isTokenAutoRefreshEnabled`\n  // is not true.\n  let refresher = state.tokenRefresher;\n  if (!refresher) {\n    refresher = createTokenRefresher(appCheck);\n    state.tokenRefresher = refresher;\n  }\n  if (!refresher.isRunning() && state.isTokenAutoRefreshEnabled) {\n    refresher.start();\n  }\n}\nfunction createTokenRefresher(appCheck) {\n  const {\n    app\n  } = appCheck;\n  return new Refresher(\n  /*#__PURE__*/\n  // Keep in mind when this fails for any reason other than the ones\n  // for which we should retry, it will effectively stop the proactive refresh.\n  _asyncToGenerator(function* () {\n    const state = getStateReference(app);\n    // If there is no token, we will try to load it from storage and use it\n    // If there is a token, we force refresh it because we know it's going to expire soon\n    let result;\n    if (!state.token) {\n      result = yield getToken$2(appCheck);\n    } else {\n      result = yield getToken$2(appCheck, true);\n    }\n    /**\r\n     * getToken() always resolves. In case the result has an error field defined, it means\r\n     * the operation failed, and we should retry.\r\n     */\n    if (result.error) {\n      throw result.error;\n    }\n    /**\r\n     * A special `internalError` field reflects that there was an error\r\n     * getting a new token from the exchange endpoint, but there's still a\r\n     * previous token that's valid for now and this should be passed to 2P/3P\r\n     * requests for a token. But we want this callback (`this.operation` in\r\n     * `Refresher`) to throw in order to kick off the Refresher's retry\r\n     * backoff. (Setting `hasSucceeded` to false.)\r\n     */\n    if (result.internalError) {\n      throw result.internalError;\n    }\n  }), () => {\n    return true;\n  }, () => {\n    const state = getStateReference(app);\n    if (state.token) {\n      // issuedAtTime + (50% * total TTL) + 5 minutes\n      let nextRefreshTimeMillis = state.token.issuedAtTimeMillis + (state.token.expireTimeMillis - state.token.issuedAtTimeMillis) * 0.5 + 5 * 60 * 1000;\n      // Do not allow refresh time to be past (expireTime - 5 minutes)\n      const latestAllowableRefresh = state.token.expireTimeMillis - 5 * 60 * 1000;\n      nextRefreshTimeMillis = Math.min(nextRefreshTimeMillis, latestAllowableRefresh);\n      return Math.max(0, nextRefreshTimeMillis - Date.now());\n    } else {\n      return 0;\n    }\n  }, TOKEN_REFRESH_TIME.RETRIAL_MIN_WAIT, TOKEN_REFRESH_TIME.RETRIAL_MAX_WAIT);\n}\nfunction notifyTokenListeners(app, token) {\n  const observers = getStateReference(app).tokenObservers;\n  for (const observer of observers) {\n    try {\n      if (observer.type === \"EXTERNAL\" /* ListenerType.EXTERNAL */ && token.error != null) {\n        // If this listener was added by a 3P call, send any token error to\n        // the supplied error handler. A 3P observer always has an error\n        // handler.\n        observer.error(token.error);\n      } else {\n        // If the token has no error field, always return the token.\n        // If this is a 2P listener, return the token, whether or not it\n        // has an error field.\n        observer.next(token);\n      }\n    } catch (e) {\n      // Errors in the listener function itself are always ignored.\n    }\n  }\n}\nfunction isValid(token) {\n  return token.expireTimeMillis - Date.now() > 0;\n}\nfunction makeDummyTokenResult(error) {\n  return {\n    token: formatDummyToken(defaultTokenErrorData),\n    error\n  };\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * AppCheck Service class.\r\n */\nclass AppCheckService {\n  constructor(app, heartbeatServiceProvider) {\n    this.app = app;\n    this.heartbeatServiceProvider = heartbeatServiceProvider;\n  }\n  _delete() {\n    const {\n      tokenObservers\n    } = getStateReference(this.app);\n    for (const tokenObserver of tokenObservers) {\n      removeTokenListener(this.app, tokenObserver.next);\n    }\n    return Promise.resolve();\n  }\n}\nfunction factory(app, heartbeatServiceProvider) {\n  return new AppCheckService(app, heartbeatServiceProvider);\n}\nfunction internalFactory(appCheck) {\n  return {\n    getToken: forceRefresh => getToken$2(appCheck, forceRefresh),\n    getLimitedUseToken: () => getLimitedUseToken$1(appCheck),\n    addTokenListener: listener => addTokenListener(appCheck, \"INTERNAL\" /* ListenerType.INTERNAL */, listener),\n    removeTokenListener: listener => removeTokenListener(appCheck.app, listener)\n  };\n}\nconst name = \"@firebase/app-check\";\nconst version = \"0.8.0\";\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst RECAPTCHA_URL = 'https://www.google.com/recaptcha/api.js';\nconst RECAPTCHA_ENTERPRISE_URL = 'https://www.google.com/recaptcha/enterprise.js';\nfunction initializeV3(app, siteKey) {\n  const initialized = new Deferred();\n  const state = getStateReference(app);\n  state.reCAPTCHAState = {\n    initialized\n  };\n  const divId = makeDiv(app);\n  const grecaptcha = getRecaptcha(false);\n  if (!grecaptcha) {\n    loadReCAPTCHAV3Script(() => {\n      const grecaptcha = getRecaptcha(false);\n      if (!grecaptcha) {\n        // it shouldn't happen.\n        throw new Error('no recaptcha');\n      }\n      queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n    });\n  } else {\n    queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n  }\n  return initialized.promise;\n}\nfunction initializeEnterprise(app, siteKey) {\n  const initialized = new Deferred();\n  const state = getStateReference(app);\n  state.reCAPTCHAState = {\n    initialized\n  };\n  const divId = makeDiv(app);\n  const grecaptcha = getRecaptcha(true);\n  if (!grecaptcha) {\n    loadReCAPTCHAEnterpriseScript(() => {\n      const grecaptcha = getRecaptcha(true);\n      if (!grecaptcha) {\n        // it shouldn't happen.\n        throw new Error('no recaptcha');\n      }\n      queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n    });\n  } else {\n    queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\n  }\n  return initialized.promise;\n}\n/**\r\n * Add listener to render the widget and resolve the promise when\r\n * the grecaptcha.ready() event fires.\r\n */\nfunction queueWidgetRender(app, siteKey, grecaptcha, container, initialized) {\n  grecaptcha.ready(() => {\n    // Invisible widgets allow us to set a different siteKey for each widget,\n    // so we use them to support multiple apps\n    renderInvisibleWidget(app, siteKey, grecaptcha, container);\n    initialized.resolve(grecaptcha);\n  });\n}\n/**\r\n * Add invisible div to page.\r\n */\nfunction makeDiv(app) {\n  const divId = `fire_app_check_${app.name}`;\n  const invisibleDiv = document.createElement('div');\n  invisibleDiv.id = divId;\n  invisibleDiv.style.display = 'none';\n  document.body.appendChild(invisibleDiv);\n  return divId;\n}\nfunction getToken$1(_x9) {\n  return _getToken$2.apply(this, arguments);\n}\n/**\r\n *\r\n * @param app\r\n * @param container - Id of a HTML element.\r\n */\nfunction _getToken$2() {\n  _getToken$2 = _asyncToGenerator(function* (app) {\n    ensureActivated(app);\n    // ensureActivated() guarantees that reCAPTCHAState is set\n    const reCAPTCHAState = getStateReference(app).reCAPTCHAState;\n    const recaptcha = yield reCAPTCHAState.initialized.promise;\n    return new Promise((resolve, _reject) => {\n      // Updated after initialization is complete.\n      const reCAPTCHAState = getStateReference(app).reCAPTCHAState;\n      recaptcha.ready(() => {\n        resolve(\n        // widgetId is guaranteed to be available if reCAPTCHAState.initialized.promise resolved.\n        recaptcha.execute(reCAPTCHAState.widgetId, {\n          action: 'fire_app_check'\n        }));\n      });\n    });\n  });\n  return _getToken$2.apply(this, arguments);\n}\nfunction renderInvisibleWidget(app, siteKey, grecaptcha, container) {\n  const widgetId = grecaptcha.render(container, {\n    sitekey: siteKey,\n    size: 'invisible',\n    // Success callback - set state\n    callback: () => {\n      getStateReference(app).reCAPTCHAState.succeeded = true;\n    },\n    // Failure callback - set state\n    'error-callback': () => {\n      getStateReference(app).reCAPTCHAState.succeeded = false;\n    }\n  });\n  const state = getStateReference(app);\n  state.reCAPTCHAState = Object.assign(Object.assign({}, state.reCAPTCHAState), {\n    // state.reCAPTCHAState is set in the initialize()\n    widgetId\n  });\n}\nfunction loadReCAPTCHAV3Script(onload) {\n  const script = document.createElement('script');\n  script.src = RECAPTCHA_URL;\n  script.onload = onload;\n  document.head.appendChild(script);\n}\nfunction loadReCAPTCHAEnterpriseScript(onload) {\n  const script = document.createElement('script');\n  script.src = RECAPTCHA_ENTERPRISE_URL;\n  script.onload = onload;\n  document.head.appendChild(script);\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * App Check provider that can obtain a reCAPTCHA V3 token and exchange it\r\n * for an App Check token.\r\n *\r\n * @public\r\n */\nclass ReCaptchaV3Provider {\n  /**\r\n   * Create a ReCaptchaV3Provider instance.\r\n   * @param siteKey - ReCAPTCHA V3 siteKey.\r\n   */\n  constructor(_siteKey) {\n    this._siteKey = _siteKey;\n    /**\r\n     * Throttle requests on certain error codes to prevent too many retries\r\n     * in a short time.\r\n     */\n    this._throttleData = null;\n  }\n  /**\r\n   * Returns an App Check token.\r\n   * @internal\r\n   */\n  getToken() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c;\n      throwIfThrottled(_this2._throttleData);\n      // Top-level `getToken()` has already checked that App Check is initialized\n      // and therefore this._app and this._heartbeatServiceProvider are available.\n      const attestedClaimsToken = yield getToken$1(_this2._app).catch(_e => {\n        // reCaptcha.execute() throws null which is not very descriptive.\n        throw ERROR_FACTORY.create(\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */);\n      });\n      // Check if a failure state was set by the recaptcha \"error-callback\".\n      if (!((_a = getStateReference(_this2._app).reCAPTCHAState) === null || _a === void 0 ? void 0 : _a.succeeded)) {\n        throw ERROR_FACTORY.create(\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */);\n      }\n      let result;\n      try {\n        result = yield exchangeToken(getExchangeRecaptchaV3TokenRequest(_this2._app, attestedClaimsToken), _this2._heartbeatServiceProvider);\n      } catch (e) {\n        if ((_b = e.code) === null || _b === void 0 ? void 0 : _b.includes(\"fetch-status-error\" /* AppCheckError.FETCH_STATUS_ERROR */)) {\n          _this2._throttleData = setBackoff(Number((_c = e.customData) === null || _c === void 0 ? void 0 : _c.httpStatus), _this2._throttleData);\n          throw ERROR_FACTORY.create(\"throttled\" /* AppCheckError.THROTTLED */, {\n            time: getDurationString(_this2._throttleData.allowRequestsAfter - Date.now()),\n            httpStatus: _this2._throttleData.httpStatus\n          });\n        } else {\n          throw e;\n        }\n      }\n      // If successful, clear throttle data.\n      _this2._throttleData = null;\n      return result;\n    })();\n  }\n  /**\r\n   * @internal\r\n   */\n  initialize(app) {\n    this._app = app;\n    this._heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n    initializeV3(app, this._siteKey).catch(() => {\n      /* we don't care about the initialization result */\n    });\n  }\n  /**\r\n   * @internal\r\n   */\n  isEqual(otherProvider) {\n    if (otherProvider instanceof ReCaptchaV3Provider) {\n      return this._siteKey === otherProvider._siteKey;\n    } else {\n      return false;\n    }\n  }\n}\n/**\r\n * App Check provider that can obtain a reCAPTCHA Enterprise token and exchange it\r\n * for an App Check token.\r\n *\r\n * @public\r\n */\nclass ReCaptchaEnterpriseProvider {\n  /**\r\n   * Create a ReCaptchaEnterpriseProvider instance.\r\n   * @param siteKey - reCAPTCHA Enterprise score-based site key.\r\n   */\n  constructor(_siteKey) {\n    this._siteKey = _siteKey;\n    /**\r\n     * Throttle requests on certain error codes to prevent too many retries\r\n     * in a short time.\r\n     */\n    this._throttleData = null;\n  }\n  /**\r\n   * Returns an App Check token.\r\n   * @internal\r\n   */\n  getToken() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      var _a, _b, _c;\n      throwIfThrottled(_this3._throttleData);\n      // Top-level `getToken()` has already checked that App Check is initialized\n      // and therefore this._app and this._heartbeatServiceProvider are available.\n      const attestedClaimsToken = yield getToken$1(_this3._app).catch(_e => {\n        // reCaptcha.execute() throws null which is not very descriptive.\n        throw ERROR_FACTORY.create(\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */);\n      });\n      // Check if a failure state was set by the recaptcha \"error-callback\".\n      if (!((_a = getStateReference(_this3._app).reCAPTCHAState) === null || _a === void 0 ? void 0 : _a.succeeded)) {\n        throw ERROR_FACTORY.create(\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */);\n      }\n      let result;\n      try {\n        result = yield exchangeToken(getExchangeRecaptchaEnterpriseTokenRequest(_this3._app, attestedClaimsToken), _this3._heartbeatServiceProvider);\n      } catch (e) {\n        if ((_b = e.code) === null || _b === void 0 ? void 0 : _b.includes(\"fetch-status-error\" /* AppCheckError.FETCH_STATUS_ERROR */)) {\n          _this3._throttleData = setBackoff(Number((_c = e.customData) === null || _c === void 0 ? void 0 : _c.httpStatus), _this3._throttleData);\n          throw ERROR_FACTORY.create(\"throttled\" /* AppCheckError.THROTTLED */, {\n            time: getDurationString(_this3._throttleData.allowRequestsAfter - Date.now()),\n            httpStatus: _this3._throttleData.httpStatus\n          });\n        } else {\n          throw e;\n        }\n      }\n      // If successful, clear throttle data.\n      _this3._throttleData = null;\n      return result;\n    })();\n  }\n  /**\r\n   * @internal\r\n   */\n  initialize(app) {\n    this._app = app;\n    this._heartbeatServiceProvider = _getProvider(app, 'heartbeat');\n    initializeEnterprise(app, this._siteKey).catch(() => {\n      /* we don't care about the initialization result */\n    });\n  }\n  /**\r\n   * @internal\r\n   */\n  isEqual(otherProvider) {\n    if (otherProvider instanceof ReCaptchaEnterpriseProvider) {\n      return this._siteKey === otherProvider._siteKey;\n    } else {\n      return false;\n    }\n  }\n}\n/**\r\n * Custom provider class.\r\n * @public\r\n */\nclass CustomProvider {\n  constructor(_customProviderOptions) {\n    this._customProviderOptions = _customProviderOptions;\n  }\n  /**\r\n   * @internal\r\n   */\n  getToken() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      // custom provider\n      const customToken = yield _this4._customProviderOptions.getToken();\n      // Try to extract IAT from custom token, in case this token is not\n      // being newly issued. JWT timestamps are in seconds since epoch.\n      const issuedAtTimeSeconds = issuedAtTime(customToken.token);\n      // Very basic validation, use current timestamp as IAT if JWT\n      // has no `iat` field or value is out of bounds.\n      const issuedAtTimeMillis = issuedAtTimeSeconds !== null && issuedAtTimeSeconds < Date.now() && issuedAtTimeSeconds > 0 ? issuedAtTimeSeconds * 1000 : Date.now();\n      return Object.assign(Object.assign({}, customToken), {\n        issuedAtTimeMillis\n      });\n    })();\n  }\n  /**\r\n   * @internal\r\n   */\n  initialize(app) {\n    this._app = app;\n  }\n  /**\r\n   * @internal\r\n   */\n  isEqual(otherProvider) {\n    if (otherProvider instanceof CustomProvider) {\n      return this._customProviderOptions.getToken.toString() === otherProvider._customProviderOptions.getToken.toString();\n    } else {\n      return false;\n    }\n  }\n}\n/**\r\n * Set throttle data to block requests until after a certain time\r\n * depending on the failed request's status code.\r\n * @param httpStatus - Status code of failed request.\r\n * @param throttleData - `ThrottleData` object containing previous throttle\r\n * data state.\r\n * @returns Data about current throttle state and expiration time.\r\n */\nfunction setBackoff(httpStatus, throttleData) {\n  /**\r\n   * Block retries for 1 day for the following error codes:\r\n   *\r\n   * 404: Likely malformed URL.\r\n   *\r\n   * 403:\r\n   * - Attestation failed\r\n   * - Wrong API key\r\n   * - Project deleted\r\n   */\n  if (httpStatus === 404 || httpStatus === 403) {\n    return {\n      backoffCount: 1,\n      allowRequestsAfter: Date.now() + ONE_DAY,\n      httpStatus\n    };\n  } else {\n    /**\r\n     * For all other error codes, the time when it is ok to retry again\r\n     * is based on exponential backoff.\r\n     */\n    const backoffCount = throttleData ? throttleData.backoffCount : 0;\n    const backoffMillis = calculateBackoffMillis(backoffCount, 1000, 2);\n    return {\n      backoffCount: backoffCount + 1,\n      allowRequestsAfter: Date.now() + backoffMillis,\n      httpStatus\n    };\n  }\n}\nfunction throwIfThrottled(throttleData) {\n  if (throttleData) {\n    if (Date.now() - throttleData.allowRequestsAfter <= 0) {\n      // If before, throw.\n      throw ERROR_FACTORY.create(\"throttled\" /* AppCheckError.THROTTLED */, {\n        time: getDurationString(throttleData.allowRequestsAfter - Date.now()),\n        httpStatus: throttleData.httpStatus\n      });\n    }\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Activate App Check for the given app. Can be called only once per app.\r\n * @param app - the {@link @firebase/app#FirebaseApp} to activate App Check for\r\n * @param options - App Check initialization options\r\n * @public\r\n */\nfunction initializeAppCheck(app = getApp(), options) {\n  app = getModularInstance(app);\n  const provider = _getProvider(app, 'app-check');\n  // Ensure initializeDebugMode() is only called once.\n  if (!getDebugState().initialized) {\n    initializeDebugMode();\n  }\n  // Log a message containing the debug token when `initializeAppCheck()`\n  // is called in debug mode.\n  if (isDebugMode()) {\n    // Do not block initialization to get the token for the message.\n    void getDebugToken().then(token =>\n    // Not using logger because I don't think we ever want this accidentally hidden.\n    console.log(`App Check debug token: ${token}. You will need to add it to your app's App Check settings in the Firebase console for it to work.`));\n  }\n  if (provider.isInitialized()) {\n    const existingInstance = provider.getImmediate();\n    const initialOptions = provider.getOptions();\n    if (initialOptions.isTokenAutoRefreshEnabled === options.isTokenAutoRefreshEnabled && initialOptions.provider.isEqual(options.provider)) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(\"already-initialized\" /* AppCheckError.ALREADY_INITIALIZED */, {\n        appName: app.name\n      });\n    }\n  }\n  const appCheck = provider.initialize({\n    options\n  });\n  _activate(app, options.provider, options.isTokenAutoRefreshEnabled);\n  // If isTokenAutoRefreshEnabled is false, do not send any requests to the\n  // exchange endpoint without an explicit call from the user either directly\n  // or through another Firebase library (storage, functions, etc.)\n  if (getStateReference(app).isTokenAutoRefreshEnabled) {\n    // Adding a listener will start the refresher and fetch a token if needed.\n    // This gets a token ready and prevents a delay when an internal library\n    // requests the token.\n    // Listener function does not need to do anything, its base functionality\n    // of calling getToken() already fetches token and writes it to memory/storage.\n    addTokenListener(appCheck, \"INTERNAL\" /* ListenerType.INTERNAL */, () => {});\n  }\n  return appCheck;\n}\n/**\r\n * Activate App Check\r\n * @param app - Firebase app to activate App Check for.\r\n * @param provider - reCAPTCHA v3 provider or\r\n * custom token provider.\r\n * @param isTokenAutoRefreshEnabled - If true, the SDK automatically\r\n * refreshes App Check tokens as needed. If undefined, defaults to the\r\n * value of `app.automaticDataCollectionEnabled`, which defaults to\r\n * false and can be set in the app config.\r\n */\nfunction _activate(app, provider, isTokenAutoRefreshEnabled) {\n  // Create an entry in the APP_CHECK_STATES map. Further changes should\n  // directly mutate this object.\n  const state = setInitialState(app, Object.assign({}, DEFAULT_STATE));\n  state.activated = true;\n  state.provider = provider; // Read cached token from storage if it exists and store it in memory.\n  state.cachedTokenPromise = readTokenFromStorage(app).then(cachedToken => {\n    if (cachedToken && isValid(cachedToken)) {\n      state.token = cachedToken;\n      // notify all listeners with the cached token\n      notifyTokenListeners(app, {\n        token: cachedToken.token\n      });\n    }\n    return cachedToken;\n  });\n  // Use value of global `automaticDataCollectionEnabled` (which\n  // itself defaults to false if not specified in config) if\n  // `isTokenAutoRefreshEnabled` param was not provided by user.\n  state.isTokenAutoRefreshEnabled = isTokenAutoRefreshEnabled === undefined ? app.automaticDataCollectionEnabled : isTokenAutoRefreshEnabled;\n  state.provider.initialize(app);\n}\n/**\r\n * Set whether App Check will automatically refresh tokens as needed.\r\n *\r\n * @param appCheckInstance - The App Check service instance.\r\n * @param isTokenAutoRefreshEnabled - If true, the SDK automatically\r\n * refreshes App Check tokens as needed. This overrides any value set\r\n * during `initializeAppCheck()`.\r\n * @public\r\n */\nfunction setTokenAutoRefreshEnabled(appCheckInstance, isTokenAutoRefreshEnabled) {\n  const app = appCheckInstance.app;\n  const state = getStateReference(app);\n  // This will exist if any product libraries have called\n  // `addTokenListener()`\n  if (state.tokenRefresher) {\n    if (isTokenAutoRefreshEnabled === true) {\n      state.tokenRefresher.start();\n    } else {\n      state.tokenRefresher.stop();\n    }\n  }\n  state.isTokenAutoRefreshEnabled = isTokenAutoRefreshEnabled;\n}\n/**\r\n * Get the current App Check token. Attaches to the most recent\r\n * in-flight request if one is present. Returns null if no token\r\n * is present and no token requests are in-flight.\r\n *\r\n * @param appCheckInstance - The App Check service instance.\r\n * @param forceRefresh - If true, will always try to fetch a fresh token.\r\n * If false, will use a cached token if found in storage.\r\n * @public\r\n */\nfunction getToken(_x0, _x1) {\n  return _getToken.apply(this, arguments);\n}\n/**\r\n * Requests a Firebase App Check token. This method should be used\r\n * only if you need to authorize requests to a non-Firebase backend.\r\n *\r\n * Returns limited-use tokens that are intended for use with your\r\n * non-Firebase backend endpoints that are protected with\r\n * <a href=\"https://firebase.google.com/docs/app-check/custom-resource-backend#replay-protection\">\r\n * Replay Protection</a>. This method\r\n * does not affect the token generation behavior of the\r\n * #getAppCheckToken() method.\r\n *\r\n * @param appCheckInstance - The App Check service instance.\r\n * @returns The limited use token.\r\n * @public\r\n */\nfunction _getToken() {\n  _getToken = _asyncToGenerator(function* (appCheckInstance, forceRefresh) {\n    const result = yield getToken$2(appCheckInstance, forceRefresh);\n    if (result.error) {\n      throw result.error;\n    }\n    return {\n      token: result.token\n    };\n  });\n  return _getToken.apply(this, arguments);\n}\nfunction getLimitedUseToken(appCheckInstance) {\n  return getLimitedUseToken$1(appCheckInstance);\n}\n/**\r\n * Wraps `addTokenListener`/`removeTokenListener` methods in an `Observer`\r\n * pattern for public use.\r\n */\nfunction onTokenChanged(appCheckInstance, onNextOrObserver, onError,\n/**\r\n * NOTE: Although an `onCompletion` callback can be provided, it will\r\n * never be called because the token stream is never-ending.\r\n * It is added only for API consistency with the observer pattern, which\r\n * we follow in JS APIs.\r\n */\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\nonCompletion) {\n  let nextFn = () => {};\n  let errorFn = () => {};\n  if (onNextOrObserver.next != null) {\n    nextFn = onNextOrObserver.next.bind(onNextOrObserver);\n  } else {\n    nextFn = onNextOrObserver;\n  }\n  if (onNextOrObserver.error != null) {\n    errorFn = onNextOrObserver.error.bind(onNextOrObserver);\n  } else if (onError) {\n    errorFn = onError;\n  }\n  addTokenListener(appCheckInstance, \"EXTERNAL\" /* ListenerType.EXTERNAL */, nextFn, errorFn);\n  return () => removeTokenListener(appCheckInstance.app, nextFn);\n}\n\n/**\r\n * Firebase App Check\r\n *\r\n * @packageDocumentation\r\n */\nconst APP_CHECK_NAME = 'app-check';\nconst APP_CHECK_NAME_INTERNAL = 'app-check-internal';\nfunction registerAppCheck() {\n  // The public interface\n  _registerComponent(new Component(APP_CHECK_NAME, container => {\n    // getImmediate for FirebaseApp will always succeed\n    const app = container.getProvider('app').getImmediate();\n    const heartbeatServiceProvider = container.getProvider('heartbeat');\n    return factory(app, heartbeatServiceProvider);\n  }, \"PUBLIC\" /* ComponentType.PUBLIC */).setInstantiationMode(\"EXPLICIT\" /* InstantiationMode.EXPLICIT */)\n  /**\r\n   * Initialize app-check-internal after app-check is initialized to make AppCheck available to\r\n   * other Firebase SDKs\r\n   */.setInstanceCreatedCallback((container, _identifier, _appcheckService) => {\n    container.getProvider(APP_CHECK_NAME_INTERNAL).initialize();\n  }));\n  // The internal interface used by other Firebase products\n  _registerComponent(new Component(APP_CHECK_NAME_INTERNAL, container => {\n    const appCheck = container.getProvider('app-check').getImmediate();\n    return internalFactory(appCheck);\n  }, \"PUBLIC\" /* ComponentType.PUBLIC */).setInstantiationMode(\"EXPLICIT\" /* InstantiationMode.EXPLICIT */));\n  registerVersion(name, version);\n}\nregisterAppCheck();\nexport { CustomProvider, ReCaptchaEnterpriseProvider, ReCaptchaV3Provider, getLimitedUseToken, getToken, initializeAppCheck, onTokenChanged, setTokenAutoRefreshEnabled };", "map": {"version": 3, "names": ["_get<PERSON><PERSON><PERSON>", "getApp", "_registerComponent", "registerVersion", "Component", "Deferred", "ErrorFactory", "isIndexedDBAvailable", "uuidv4", "getGlobal", "base64", "issuedAtTime", "calculateBackoffMillis", "getModularInstance", "<PERSON><PERSON>", "APP_CHECK_STATES", "Map", "DEFAULT_STATE", "activated", "tokenObservers", "DEBUG_STATE", "initialized", "enabled", "getStateReference", "app", "get", "Object", "assign", "setInitialState", "state", "set", "getDebugState", "BASE_ENDPOINT", "EXCHANGE_RECAPTCHA_TOKEN_METHOD", "EXCHANGE_RECAPTCHA_ENTERPRISE_TOKEN_METHOD", "EXCHANGE_DEBUG_TOKEN_METHOD", "TOKEN_REFRESH_TIME", "OFFSET_DURATION", "RETRIAL_MIN_WAIT", "RETRIAL_MAX_WAIT", "ONE_DAY", "Refresher", "constructor", "operation", "retryPolicy", "getWaitDuration", "lowerBound", "upperBound", "pending", "nextErrorWaitInterval", "Error", "start", "process", "catch", "stop", "reject", "isRunning", "has<PERSON>ucceeded", "_this", "_asyncToGenerator", "sleep", "getNextRun", "resolve", "promise", "error", "currentErrorWaitInterval", "ms", "Promise", "setTimeout", "ERRORS", "ERROR_FACTORY", "getRecaptcha", "isEnterprise", "_a", "self", "gre<PERSON><PERSON>a", "enterprise", "ensureActivated", "create", "appName", "name", "getDurationString", "durationInMillis", "totalSeconds", "Math", "round", "days", "floor", "hours", "minutes", "seconds", "result", "pad", "value", "toString", "exchangeToken", "_x", "_x2", "_exchangeToken", "apply", "arguments", "url", "body", "heartbeatServiceProvider", "headers", "heartbeatService", "getImmediate", "optional", "heartbeatsHeader", "getHeartbeatsHeader", "options", "method", "JSON", "stringify", "response", "fetch", "originalError", "originalErrorMessage", "message", "status", "httpStatus", "responseBody", "json", "match", "ttl", "isNaN", "Number", "timeToLiveAsNumber", "now", "Date", "token", "expire<PERSON>ime<PERSON><PERSON><PERSON>", "issuedAtTimeMillis", "getExchangeRecaptchaV3TokenRequest", "reCAPTCHAToken", "projectId", "appId", "<PERSON><PERSON><PERSON><PERSON>", "getExchangeRecaptchaEnterpriseTokenRequest", "getExchangeDebugTokenRequest", "debugToken", "debug_token", "DB_NAME", "DB_VERSION", "STORE_NAME", "DEBUG_TOKEN_KEY", "db<PERSON><PERSON><PERSON>", "getDBPromise", "request", "indexedDB", "open", "onsuccess", "event", "target", "onerror", "onupgradeneeded", "db", "oldVersion", "createObjectStore", "keyP<PERSON>", "e", "readTokenFromIndexedDB", "read", "computeKey", "writeTokenToIndexedDB", "write", "writeDebugTokenToIndexedDB", "readDebugTokenFromIndexedDB", "_x3", "_x4", "_write", "key", "transaction", "store", "objectStore", "put", "compositeKey", "_event", "_x5", "_read", "undefined", "logger", "readTokenFromStorage", "_x6", "_readTokenFromStorage", "warn", "writeTokenToStorage", "readOrCreateDebugTokenFromStorage", "_readOrCreateDebugTokenFromStorage", "existingDebugToken", "_e", "newToken", "isDebugMode", "debugState", "getDebugToken", "_getDebugToken", "initializeDebugMode", "globals", "FIREBASE_APPCHECK_DEBUG_TOKEN", "deferredToken", "defaultTokenErrorData", "formatDummyToken", "tokenErrorData", "encodeString", "getToken$2", "_x7", "_getToken$", "appCheck", "forceRefresh", "<PERSON><PERSON><PERSON><PERSON>", "cachedToken", "cachedTokenPromise", "shouldCallListeners", "exchangeTokenPromise", "finally", "tokenFromDebugExchange", "provider", "getToken", "code", "interopTokenResult", "makeDummyTokenResult", "internalError", "notifyTokenListeners", "getLimitedUseToken$1", "_x8", "_getLimitedUseToken$", "addTokenListener", "type", "listener", "onError", "tokenObserver", "next", "validToken", "then", "initTokenRefresher", "removeTokenListener", "newObservers", "filter", "length", "tokenRefresher", "refresher", "createTokenRefresher", "isTokenAutoRefreshEnabled", "nextRefreshTimeMillis", "latestAllowableRefresh", "min", "max", "observers", "observer", "AppCheckService", "_delete", "factory", "internalFactory", "getLimitedUseToken", "version", "RECAPTCHA_URL", "RECAPTCHA_ENTERPRISE_URL", "initializeV3", "siteKey", "reCAPTCHAState", "divId", "makeDiv", "loadReCAPTCHAV3Script", "queueWidgetRender", "initializeEnterprise", "loadReCAPTCHAEnterpriseScript", "container", "ready", "renderInvisibleWidget", "invisibleDiv", "document", "createElement", "id", "style", "display", "append<PERSON><PERSON><PERSON>", "getToken$1", "_x9", "_getToken$2", "recaptcha", "_reject", "execute", "widgetId", "action", "render", "sitekey", "size", "callback", "succeeded", "error-callback", "onload", "script", "src", "head", "ReCaptchaV3Provider", "_siteKey", "_throttleData", "_this2", "_b", "_c", "throwIfThrottled", "attestedClaimsToken", "_app", "_heartbeatServiceProvider", "includes", "<PERSON><PERSON><PERSON><PERSON>", "customData", "time", "allowRequestsAfter", "initialize", "isEqual", "otherProvider", "ReCaptchaEnterpriseProvider", "_this3", "CustomProvider", "_customProviderOptions", "_this4", "customToken", "issuedAtTimeSeconds", "throttle<PERSON><PERSON>", "backoffCount", "backoff<PERSON><PERSON><PERSON>", "initializeAppCheck", "console", "log", "isInitialized", "existingInstance", "initialOptions", "getOptions", "_activate", "automaticDataCollectionEnabled", "setTokenAutoRefreshEnabled", "appCheckInstance", "_x0", "_x1", "_getToken", "onTokenChanged", "onNextOrObserver", "onCompletion", "nextFn", "errorFn", "bind", "APP_CHECK_NAME", "APP_CHECK_NAME_INTERNAL", "registerAppCheck", "get<PERSON><PERSON><PERSON>", "setInstantiationMode", "setInstanceCreatedCallback", "_identifier", "_appcheckService"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/fire/node_modules/@firebase/app-check/dist/esm/index.esm2017.js"], "sourcesContent": ["import { _getProvider, getApp, _registerComponent, registerVersion } from '@firebase/app';\nimport { Component } from '@firebase/component';\nimport { Deferred, ErrorFactory, isIndexedDBAvailable, uuidv4, getGlobal, base64, issuedAtTime, calculateBackoffMillis, getModularInstance } from '@firebase/util';\nimport { Logger } from '@firebase/logger';\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst APP_CHECK_STATES = new Map();\r\nconst DEFAULT_STATE = {\r\n    activated: false,\r\n    tokenObservers: []\r\n};\r\nconst DEBUG_STATE = {\r\n    initialized: false,\r\n    enabled: false\r\n};\r\n/**\r\n * Gets a reference to the state object.\r\n */\r\nfunction getStateReference(app) {\r\n    return APP_CHECK_STATES.get(app) || Object.assign({}, DEFAULT_STATE);\r\n}\r\n/**\r\n * Set once on initialization. The map should hold the same reference to the\r\n * same object until this entry is deleted.\r\n */\r\nfunction setInitialState(app, state) {\r\n    APP_CHECK_STATES.set(app, state);\r\n    return APP_CHECK_STATES.get(app);\r\n}\r\nfunction getDebugState() {\r\n    return DEBUG_STATE;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst BASE_ENDPOINT = 'https://content-firebaseappcheck.googleapis.com/v1';\r\nconst EXCHANGE_RECAPTCHA_TOKEN_METHOD = 'exchangeRecaptchaV3Token';\r\nconst EXCHANGE_RECAPTCHA_ENTERPRISE_TOKEN_METHOD = 'exchangeRecaptchaEnterpriseToken';\r\nconst EXCHANGE_DEBUG_TOKEN_METHOD = 'exchangeDebugToken';\r\nconst TOKEN_REFRESH_TIME = {\r\n    /**\r\n     * The offset time before token natural expiration to run the refresh.\r\n     * This is currently 5 minutes.\r\n     */\r\n    OFFSET_DURATION: 5 * 60 * 1000,\r\n    /**\r\n     * This is the first retrial wait after an error. This is currently\r\n     * 30 seconds.\r\n     */\r\n    RETRIAL_MIN_WAIT: 30 * 1000,\r\n    /**\r\n     * This is the maximum retrial wait, currently 16 minutes.\r\n     */\r\n    RETRIAL_MAX_WAIT: 16 * 60 * 1000\r\n};\r\n/**\r\n * One day in millis, for certain error code backoffs.\r\n */\r\nconst ONE_DAY = 24 * 60 * 60 * 1000;\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Port from auth proactiverefresh.js\r\n *\r\n */\r\n// TODO: move it to @firebase/util?\r\n// TODO: allow to config whether refresh should happen in the background\r\nclass Refresher {\r\n    constructor(operation, retryPolicy, getWaitDuration, lowerBound, upperBound) {\r\n        this.operation = operation;\r\n        this.retryPolicy = retryPolicy;\r\n        this.getWaitDuration = getWaitDuration;\r\n        this.lowerBound = lowerBound;\r\n        this.upperBound = upperBound;\r\n        this.pending = null;\r\n        this.nextErrorWaitInterval = lowerBound;\r\n        if (lowerBound > upperBound) {\r\n            throw new Error('Proactive refresh lower bound greater than upper bound!');\r\n        }\r\n    }\r\n    start() {\r\n        this.nextErrorWaitInterval = this.lowerBound;\r\n        this.process(true).catch(() => {\r\n            /* we don't care about the result */\r\n        });\r\n    }\r\n    stop() {\r\n        if (this.pending) {\r\n            this.pending.reject('cancelled');\r\n            this.pending = null;\r\n        }\r\n    }\r\n    isRunning() {\r\n        return !!this.pending;\r\n    }\r\n    async process(hasSucceeded) {\r\n        this.stop();\r\n        try {\r\n            this.pending = new Deferred();\r\n            await sleep(this.getNextRun(hasSucceeded));\r\n            // Why do we resolve a promise, then immediate wait for it?\r\n            // We do it to make the promise chain cancellable.\r\n            // We can call stop() which rejects the promise before the following line execute, which makes\r\n            // the code jump to the catch block.\r\n            // TODO: unit test this\r\n            this.pending.resolve();\r\n            await this.pending.promise;\r\n            this.pending = new Deferred();\r\n            await this.operation();\r\n            this.pending.resolve();\r\n            await this.pending.promise;\r\n            this.process(true).catch(() => {\r\n                /* we don't care about the result */\r\n            });\r\n        }\r\n        catch (error) {\r\n            if (this.retryPolicy(error)) {\r\n                this.process(false).catch(() => {\r\n                    /* we don't care about the result */\r\n                });\r\n            }\r\n            else {\r\n                this.stop();\r\n            }\r\n        }\r\n    }\r\n    getNextRun(hasSucceeded) {\r\n        if (hasSucceeded) {\r\n            // If last operation succeeded, reset next error wait interval and return\r\n            // the default wait duration.\r\n            this.nextErrorWaitInterval = this.lowerBound;\r\n            // Return typical wait duration interval after a successful operation.\r\n            return this.getWaitDuration();\r\n        }\r\n        else {\r\n            // Get next error wait interval.\r\n            const currentErrorWaitInterval = this.nextErrorWaitInterval;\r\n            // Double interval for next consecutive error.\r\n            this.nextErrorWaitInterval *= 2;\r\n            // Make sure next wait interval does not exceed the maximum upper bound.\r\n            if (this.nextErrorWaitInterval > this.upperBound) {\r\n                this.nextErrorWaitInterval = this.upperBound;\r\n            }\r\n            return currentErrorWaitInterval;\r\n        }\r\n    }\r\n}\r\nfunction sleep(ms) {\r\n    return new Promise(resolve => {\r\n        setTimeout(resolve, ms);\r\n    });\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst ERRORS = {\r\n    [\"already-initialized\" /* AppCheckError.ALREADY_INITIALIZED */]: 'You have already called initializeAppCheck() for FirebaseApp {$appName} with ' +\r\n        'different options. To avoid this error, call initializeAppCheck() with the ' +\r\n        'same options as when it was originally called. This will return the ' +\r\n        'already initialized instance.',\r\n    [\"use-before-activation\" /* AppCheckError.USE_BEFORE_ACTIVATION */]: 'App Check is being used before initializeAppCheck() is called for FirebaseApp {$appName}. ' +\r\n        'Call initializeAppCheck() before instantiating other Firebase services.',\r\n    [\"fetch-network-error\" /* AppCheckError.FETCH_NETWORK_ERROR */]: 'Fetch failed to connect to a network. Check Internet connection. ' +\r\n        'Original error: {$originalErrorMessage}.',\r\n    [\"fetch-parse-error\" /* AppCheckError.FETCH_PARSE_ERROR */]: 'Fetch client could not parse response.' +\r\n        ' Original error: {$originalErrorMessage}.',\r\n    [\"fetch-status-error\" /* AppCheckError.FETCH_STATUS_ERROR */]: 'Fetch server returned an HTTP error status. HTTP status: {$httpStatus}.',\r\n    [\"storage-open\" /* AppCheckError.STORAGE_OPEN */]: 'Error thrown when opening storage. Original error: {$originalErrorMessage}.',\r\n    [\"storage-get\" /* AppCheckError.STORAGE_GET */]: 'Error thrown when reading from storage. Original error: {$originalErrorMessage}.',\r\n    [\"storage-set\" /* AppCheckError.STORAGE_WRITE */]: 'Error thrown when writing to storage. Original error: {$originalErrorMessage}.',\r\n    [\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */]: 'ReCAPTCHA error.',\r\n    [\"throttled\" /* AppCheckError.THROTTLED */]: `Requests throttled due to {$httpStatus} error. Attempts allowed again after {$time}`\r\n};\r\nconst ERROR_FACTORY = new ErrorFactory('appCheck', 'AppCheck', ERRORS);\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction getRecaptcha(isEnterprise = false) {\r\n    var _a;\r\n    if (isEnterprise) {\r\n        return (_a = self.grecaptcha) === null || _a === void 0 ? void 0 : _a.enterprise;\r\n    }\r\n    return self.grecaptcha;\r\n}\r\nfunction ensureActivated(app) {\r\n    if (!getStateReference(app).activated) {\r\n        throw ERROR_FACTORY.create(\"use-before-activation\" /* AppCheckError.USE_BEFORE_ACTIVATION */, {\r\n            appName: app.name\r\n        });\r\n    }\r\n}\r\nfunction getDurationString(durationInMillis) {\r\n    const totalSeconds = Math.round(durationInMillis / 1000);\r\n    const days = Math.floor(totalSeconds / (3600 * 24));\r\n    const hours = Math.floor((totalSeconds - days * 3600 * 24) / 3600);\r\n    const minutes = Math.floor((totalSeconds - days * 3600 * 24 - hours * 3600) / 60);\r\n    const seconds = totalSeconds - days * 3600 * 24 - hours * 3600 - minutes * 60;\r\n    let result = '';\r\n    if (days) {\r\n        result += pad(days) + 'd:';\r\n    }\r\n    if (hours) {\r\n        result += pad(hours) + 'h:';\r\n    }\r\n    result += pad(minutes) + 'm:' + pad(seconds) + 's';\r\n    return result;\r\n}\r\nfunction pad(value) {\r\n    if (value === 0) {\r\n        return '00';\r\n    }\r\n    return value >= 10 ? value.toString() : '0' + value;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nasync function exchangeToken({ url, body }, heartbeatServiceProvider) {\r\n    const headers = {\r\n        'Content-Type': 'application/json'\r\n    };\r\n    // If heartbeat service exists, add heartbeat header string to the header.\r\n    const heartbeatService = heartbeatServiceProvider.getImmediate({\r\n        optional: true\r\n    });\r\n    if (heartbeatService) {\r\n        const heartbeatsHeader = await heartbeatService.getHeartbeatsHeader();\r\n        if (heartbeatsHeader) {\r\n            headers['X-Firebase-Client'] = heartbeatsHeader;\r\n        }\r\n    }\r\n    const options = {\r\n        method: 'POST',\r\n        body: JSON.stringify(body),\r\n        headers\r\n    };\r\n    let response;\r\n    try {\r\n        response = await fetch(url, options);\r\n    }\r\n    catch (originalError) {\r\n        throw ERROR_FACTORY.create(\"fetch-network-error\" /* AppCheckError.FETCH_NETWORK_ERROR */, {\r\n            originalErrorMessage: originalError === null || originalError === void 0 ? void 0 : originalError.message\r\n        });\r\n    }\r\n    if (response.status !== 200) {\r\n        throw ERROR_FACTORY.create(\"fetch-status-error\" /* AppCheckError.FETCH_STATUS_ERROR */, {\r\n            httpStatus: response.status\r\n        });\r\n    }\r\n    let responseBody;\r\n    try {\r\n        // JSON parsing throws SyntaxError if the response body isn't a JSON string.\r\n        responseBody = await response.json();\r\n    }\r\n    catch (originalError) {\r\n        throw ERROR_FACTORY.create(\"fetch-parse-error\" /* AppCheckError.FETCH_PARSE_ERROR */, {\r\n            originalErrorMessage: originalError === null || originalError === void 0 ? void 0 : originalError.message\r\n        });\r\n    }\r\n    // Protobuf duration format.\r\n    // https://developers.google.com/protocol-buffers/docs/reference/java/com/google/protobuf/Duration\r\n    const match = responseBody.ttl.match(/^([\\d.]+)(s)$/);\r\n    if (!match || !match[2] || isNaN(Number(match[1]))) {\r\n        throw ERROR_FACTORY.create(\"fetch-parse-error\" /* AppCheckError.FETCH_PARSE_ERROR */, {\r\n            originalErrorMessage: `ttl field (timeToLive) is not in standard Protobuf Duration ` +\r\n                `format: ${responseBody.ttl}`\r\n        });\r\n    }\r\n    const timeToLiveAsNumber = Number(match[1]) * 1000;\r\n    const now = Date.now();\r\n    return {\r\n        token: responseBody.token,\r\n        expireTimeMillis: now + timeToLiveAsNumber,\r\n        issuedAtTimeMillis: now\r\n    };\r\n}\r\nfunction getExchangeRecaptchaV3TokenRequest(app, reCAPTCHAToken) {\r\n    const { projectId, appId, apiKey } = app.options;\r\n    return {\r\n        url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_RECAPTCHA_TOKEN_METHOD}?key=${apiKey}`,\r\n        body: {\r\n            'recaptcha_v3_token': reCAPTCHAToken\r\n        }\r\n    };\r\n}\r\nfunction getExchangeRecaptchaEnterpriseTokenRequest(app, reCAPTCHAToken) {\r\n    const { projectId, appId, apiKey } = app.options;\r\n    return {\r\n        url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_RECAPTCHA_ENTERPRISE_TOKEN_METHOD}?key=${apiKey}`,\r\n        body: {\r\n            'recaptcha_enterprise_token': reCAPTCHAToken\r\n        }\r\n    };\r\n}\r\nfunction getExchangeDebugTokenRequest(app, debugToken) {\r\n    const { projectId, appId, apiKey } = app.options;\r\n    return {\r\n        url: `${BASE_ENDPOINT}/projects/${projectId}/apps/${appId}:${EXCHANGE_DEBUG_TOKEN_METHOD}?key=${apiKey}`,\r\n        body: {\r\n            // eslint-disable-next-line\r\n            debug_token: debugToken\r\n        }\r\n    };\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst DB_NAME = 'firebase-app-check-database';\r\nconst DB_VERSION = 1;\r\nconst STORE_NAME = 'firebase-app-check-store';\r\nconst DEBUG_TOKEN_KEY = 'debug-token';\r\nlet dbPromise = null;\r\nfunction getDBPromise() {\r\n    if (dbPromise) {\r\n        return dbPromise;\r\n    }\r\n    dbPromise = new Promise((resolve, reject) => {\r\n        try {\r\n            const request = indexedDB.open(DB_NAME, DB_VERSION);\r\n            request.onsuccess = event => {\r\n                resolve(event.target.result);\r\n            };\r\n            request.onerror = event => {\r\n                var _a;\r\n                reject(ERROR_FACTORY.create(\"storage-open\" /* AppCheckError.STORAGE_OPEN */, {\r\n                    originalErrorMessage: (_a = event.target.error) === null || _a === void 0 ? void 0 : _a.message\r\n                }));\r\n            };\r\n            request.onupgradeneeded = event => {\r\n                const db = event.target.result;\r\n                // We don't use 'break' in this switch statement, the fall-through\r\n                // behavior is what we want, because if there are multiple versions between\r\n                // the old version and the current version, we want ALL the migrations\r\n                // that correspond to those versions to run, not only the last one.\r\n                // eslint-disable-next-line default-case\r\n                switch (event.oldVersion) {\r\n                    case 0:\r\n                        db.createObjectStore(STORE_NAME, {\r\n                            keyPath: 'compositeKey'\r\n                        });\r\n                }\r\n            };\r\n        }\r\n        catch (e) {\r\n            reject(ERROR_FACTORY.create(\"storage-open\" /* AppCheckError.STORAGE_OPEN */, {\r\n                originalErrorMessage: e === null || e === void 0 ? void 0 : e.message\r\n            }));\r\n        }\r\n    });\r\n    return dbPromise;\r\n}\r\nfunction readTokenFromIndexedDB(app) {\r\n    return read(computeKey(app));\r\n}\r\nfunction writeTokenToIndexedDB(app, token) {\r\n    return write(computeKey(app), token);\r\n}\r\nfunction writeDebugTokenToIndexedDB(token) {\r\n    return write(DEBUG_TOKEN_KEY, token);\r\n}\r\nfunction readDebugTokenFromIndexedDB() {\r\n    return read(DEBUG_TOKEN_KEY);\r\n}\r\nasync function write(key, value) {\r\n    const db = await getDBPromise();\r\n    const transaction = db.transaction(STORE_NAME, 'readwrite');\r\n    const store = transaction.objectStore(STORE_NAME);\r\n    const request = store.put({\r\n        compositeKey: key,\r\n        value\r\n    });\r\n    return new Promise((resolve, reject) => {\r\n        request.onsuccess = _event => {\r\n            resolve();\r\n        };\r\n        transaction.onerror = event => {\r\n            var _a;\r\n            reject(ERROR_FACTORY.create(\"storage-set\" /* AppCheckError.STORAGE_WRITE */, {\r\n                originalErrorMessage: (_a = event.target.error) === null || _a === void 0 ? void 0 : _a.message\r\n            }));\r\n        };\r\n    });\r\n}\r\nasync function read(key) {\r\n    const db = await getDBPromise();\r\n    const transaction = db.transaction(STORE_NAME, 'readonly');\r\n    const store = transaction.objectStore(STORE_NAME);\r\n    const request = store.get(key);\r\n    return new Promise((resolve, reject) => {\r\n        request.onsuccess = event => {\r\n            const result = event.target.result;\r\n            if (result) {\r\n                resolve(result.value);\r\n            }\r\n            else {\r\n                resolve(undefined);\r\n            }\r\n        };\r\n        transaction.onerror = event => {\r\n            var _a;\r\n            reject(ERROR_FACTORY.create(\"storage-get\" /* AppCheckError.STORAGE_GET */, {\r\n                originalErrorMessage: (_a = event.target.error) === null || _a === void 0 ? void 0 : _a.message\r\n            }));\r\n        };\r\n    });\r\n}\r\nfunction computeKey(app) {\r\n    return `${app.options.appId}-${app.name}`;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst logger = new Logger('@firebase/app-check');\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Always resolves. In case of an error reading from indexeddb, resolve with undefined\r\n */\r\nasync function readTokenFromStorage(app) {\r\n    if (isIndexedDBAvailable()) {\r\n        let token = undefined;\r\n        try {\r\n            token = await readTokenFromIndexedDB(app);\r\n        }\r\n        catch (e) {\r\n            // swallow the error and return undefined\r\n            logger.warn(`Failed to read token from IndexedDB. Error: ${e}`);\r\n        }\r\n        return token;\r\n    }\r\n    return undefined;\r\n}\r\n/**\r\n * Always resolves. In case of an error writing to indexeddb, print a warning and resolve the promise\r\n */\r\nfunction writeTokenToStorage(app, token) {\r\n    if (isIndexedDBAvailable()) {\r\n        return writeTokenToIndexedDB(app, token).catch(e => {\r\n            // swallow the error and resolve the promise\r\n            logger.warn(`Failed to write token to IndexedDB. Error: ${e}`);\r\n        });\r\n    }\r\n    return Promise.resolve();\r\n}\r\nasync function readOrCreateDebugTokenFromStorage() {\r\n    /**\r\n     * Theoretically race condition can happen if we read, then write in 2 separate transactions.\r\n     * But it won't happen here, because this function will be called exactly once.\r\n     */\r\n    let existingDebugToken = undefined;\r\n    try {\r\n        existingDebugToken = await readDebugTokenFromIndexedDB();\r\n    }\r\n    catch (_e) {\r\n        // failed to read from indexeddb. We assume there is no existing debug token, and generate a new one.\r\n    }\r\n    if (!existingDebugToken) {\r\n        // create a new debug token\r\n        const newToken = uuidv4();\r\n        // We don't need to block on writing to indexeddb\r\n        // In case persistence failed, a new debug token will be generated everytime the page is refreshed.\r\n        // It renders the debug token useless because you have to manually register(whitelist) the new token in the firebase console again and again.\r\n        // If you see this error trying to use debug token, it probably means you are using a browser that doesn't support indexeddb.\r\n        // You should switch to a different browser that supports indexeddb\r\n        writeDebugTokenToIndexedDB(newToken).catch(e => logger.warn(`Failed to persist debug token to IndexedDB. Error: ${e}`));\r\n        return newToken;\r\n    }\r\n    else {\r\n        return existingDebugToken;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction isDebugMode() {\r\n    const debugState = getDebugState();\r\n    return debugState.enabled;\r\n}\r\nasync function getDebugToken() {\r\n    const state = getDebugState();\r\n    if (state.enabled && state.token) {\r\n        return state.token.promise;\r\n    }\r\n    else {\r\n        // should not happen!\r\n        throw Error(`\n            Can't get debug token in production mode.\n        `);\r\n    }\r\n}\r\nfunction initializeDebugMode() {\r\n    const globals = getGlobal();\r\n    const debugState = getDebugState();\r\n    // Set to true if this function has been called, whether or not\r\n    // it enabled debug mode.\r\n    debugState.initialized = true;\r\n    if (typeof globals.FIREBASE_APPCHECK_DEBUG_TOKEN !== 'string' &&\r\n        globals.FIREBASE_APPCHECK_DEBUG_TOKEN !== true) {\r\n        return;\r\n    }\r\n    debugState.enabled = true;\r\n    const deferredToken = new Deferred();\r\n    debugState.token = deferredToken;\r\n    if (typeof globals.FIREBASE_APPCHECK_DEBUG_TOKEN === 'string') {\r\n        deferredToken.resolve(globals.FIREBASE_APPCHECK_DEBUG_TOKEN);\r\n    }\r\n    else {\r\n        deferredToken.resolve(readOrCreateDebugTokenFromStorage());\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n// Initial hardcoded value agreed upon across platforms for initial launch.\r\n// Format left open for possible dynamic error values and other fields in the future.\r\nconst defaultTokenErrorData = { error: 'UNKNOWN_ERROR' };\r\n/**\r\n * Stringify and base64 encode token error data.\r\n *\r\n * @param tokenError Error data, currently hardcoded.\r\n */\r\nfunction formatDummyToken(tokenErrorData) {\r\n    return base64.encodeString(JSON.stringify(tokenErrorData), \r\n    /* webSafe= */ false);\r\n}\r\n/**\r\n * This function always resolves.\r\n * The result will contain an error field if there is any error.\r\n * In case there is an error, the token field in the result will be populated with a dummy value\r\n */\r\nasync function getToken$2(appCheck, forceRefresh = false) {\r\n    const app = appCheck.app;\r\n    ensureActivated(app);\r\n    const state = getStateReference(app);\r\n    /**\r\n     * First check if there is a token in memory from a previous `getToken()` call.\r\n     */\r\n    let token = state.token;\r\n    let error = undefined;\r\n    /**\r\n     * If an invalid token was found in memory, clear token from\r\n     * memory and unset the local variable `token`.\r\n     */\r\n    if (token && !isValid(token)) {\r\n        state.token = undefined;\r\n        token = undefined;\r\n    }\r\n    /**\r\n     * If there is no valid token in memory, try to load token from indexedDB.\r\n     */\r\n    if (!token) {\r\n        // cachedTokenPromise contains the token found in IndexedDB or undefined if not found.\r\n        const cachedToken = await state.cachedTokenPromise;\r\n        if (cachedToken) {\r\n            if (isValid(cachedToken)) {\r\n                token = cachedToken;\r\n            }\r\n            else {\r\n                // If there was an invalid token in the indexedDB cache, clear it.\r\n                await writeTokenToStorage(app, undefined);\r\n            }\r\n        }\r\n    }\r\n    // Return the cached token (from either memory or indexedDB) if it's valid\r\n    if (!forceRefresh && token && isValid(token)) {\r\n        return {\r\n            token: token.token\r\n        };\r\n    }\r\n    // Only set to true if this `getToken()` call is making the actual\r\n    // REST call to the exchange endpoint, versus waiting for an already\r\n    // in-flight call (see debug and regular exchange endpoint paths below)\r\n    let shouldCallListeners = false;\r\n    /**\r\n     * DEBUG MODE\r\n     * If debug mode is set, and there is no cached token, fetch a new App\r\n     * Check token using the debug token, and return it directly.\r\n     */\r\n    if (isDebugMode()) {\r\n        // Avoid making another call to the exchange endpoint if one is in flight.\r\n        if (!state.exchangeTokenPromise) {\r\n            state.exchangeTokenPromise = exchangeToken(getExchangeDebugTokenRequest(app, await getDebugToken()), appCheck.heartbeatServiceProvider).finally(() => {\r\n                // Clear promise when settled - either resolved or rejected.\r\n                state.exchangeTokenPromise = undefined;\r\n            });\r\n            shouldCallListeners = true;\r\n        }\r\n        const tokenFromDebugExchange = await state.exchangeTokenPromise;\r\n        // Write debug token to indexedDB.\r\n        await writeTokenToStorage(app, tokenFromDebugExchange);\r\n        // Write debug token to state.\r\n        state.token = tokenFromDebugExchange;\r\n        return { token: tokenFromDebugExchange.token };\r\n    }\r\n    /**\r\n     * There are no valid tokens in memory or indexedDB and we are not in\r\n     * debug mode.\r\n     * Request a new token from the exchange endpoint.\r\n     */\r\n    try {\r\n        // Avoid making another call to the exchange endpoint if one is in flight.\r\n        if (!state.exchangeTokenPromise) {\r\n            // state.provider is populated in initializeAppCheck()\r\n            // ensureActivated() at the top of this function checks that\r\n            // initializeAppCheck() has been called.\r\n            state.exchangeTokenPromise = state.provider.getToken().finally(() => {\r\n                // Clear promise when settled - either resolved or rejected.\r\n                state.exchangeTokenPromise = undefined;\r\n            });\r\n            shouldCallListeners = true;\r\n        }\r\n        token = await getStateReference(app).exchangeTokenPromise;\r\n    }\r\n    catch (e) {\r\n        if (e.code === `appCheck/${\"throttled\" /* AppCheckError.THROTTLED */}`) {\r\n            // Warn if throttled, but do not treat it as an error.\r\n            logger.warn(e.message);\r\n        }\r\n        else {\r\n            // `getToken()` should never throw, but logging error text to console will aid debugging.\r\n            logger.error(e);\r\n        }\r\n        // Always save error to be added to dummy token.\r\n        error = e;\r\n    }\r\n    let interopTokenResult;\r\n    if (!token) {\r\n        // If token is undefined, there must be an error.\r\n        // Return a dummy token along with the error.\r\n        interopTokenResult = makeDummyTokenResult(error);\r\n    }\r\n    else if (error) {\r\n        if (isValid(token)) {\r\n            // It's also possible a valid token exists, but there's also an error.\r\n            // (Such as if the token is almost expired, tries to refresh, and\r\n            // the exchange request fails.)\r\n            // We add a special error property here so that the refresher will\r\n            // count this as a failed attempt and use the backoff instead of\r\n            // retrying repeatedly with no delay, but any 3P listeners will not\r\n            // be hindered in getting the still-valid token.\r\n            interopTokenResult = {\r\n                token: token.token,\r\n                internalError: error\r\n            };\r\n        }\r\n        else {\r\n            // No invalid tokens should make it to this step. Memory and cached tokens\r\n            // are checked. Other tokens are from fresh exchanges. But just in case.\r\n            interopTokenResult = makeDummyTokenResult(error);\r\n        }\r\n    }\r\n    else {\r\n        interopTokenResult = {\r\n            token: token.token\r\n        };\r\n        // write the new token to the memory state as well as the persistent storage.\r\n        // Only do it if we got a valid new token\r\n        state.token = token;\r\n        await writeTokenToStorage(app, token);\r\n    }\r\n    if (shouldCallListeners) {\r\n        notifyTokenListeners(app, interopTokenResult);\r\n    }\r\n    return interopTokenResult;\r\n}\r\n/**\r\n * Internal API for limited use tokens. Skips all FAC state and simply calls\r\n * the underlying provider.\r\n */\r\nasync function getLimitedUseToken$1(appCheck) {\r\n    const app = appCheck.app;\r\n    ensureActivated(app);\r\n    const { provider } = getStateReference(app);\r\n    if (isDebugMode()) {\r\n        const debugToken = await getDebugToken();\r\n        const { token } = await exchangeToken(getExchangeDebugTokenRequest(app, debugToken), appCheck.heartbeatServiceProvider);\r\n        return { token };\r\n    }\r\n    else {\r\n        // provider is definitely valid since we ensure AppCheck was activated\r\n        const { token } = await provider.getToken();\r\n        return { token };\r\n    }\r\n}\r\nfunction addTokenListener(appCheck, type, listener, onError) {\r\n    const { app } = appCheck;\r\n    const state = getStateReference(app);\r\n    const tokenObserver = {\r\n        next: listener,\r\n        error: onError,\r\n        type\r\n    };\r\n    state.tokenObservers = [...state.tokenObservers, tokenObserver];\r\n    // Invoke the listener async immediately if there is a valid token\r\n    // in memory.\r\n    if (state.token && isValid(state.token)) {\r\n        const validToken = state.token;\r\n        Promise.resolve()\r\n            .then(() => {\r\n            listener({ token: validToken.token });\r\n            initTokenRefresher(appCheck);\r\n        })\r\n            .catch(() => {\r\n            /* we don't care about exceptions thrown in listeners */\r\n        });\r\n    }\r\n    /**\r\n     * Wait for any cached token promise to resolve before starting the token\r\n     * refresher. The refresher checks to see if there is an existing token\r\n     * in state and calls the exchange endpoint if not. We should first let the\r\n     * IndexedDB check have a chance to populate state if it can.\r\n     *\r\n     * Listener call isn't needed here because cachedTokenPromise will call any\r\n     * listeners that exist when it resolves.\r\n     */\r\n    // state.cachedTokenPromise is always populated in `activate()`.\r\n    void state.cachedTokenPromise.then(() => initTokenRefresher(appCheck));\r\n}\r\nfunction removeTokenListener(app, listener) {\r\n    const state = getStateReference(app);\r\n    const newObservers = state.tokenObservers.filter(tokenObserver => tokenObserver.next !== listener);\r\n    if (newObservers.length === 0 &&\r\n        state.tokenRefresher &&\r\n        state.tokenRefresher.isRunning()) {\r\n        state.tokenRefresher.stop();\r\n    }\r\n    state.tokenObservers = newObservers;\r\n}\r\n/**\r\n * Logic to create and start refresher as needed.\r\n */\r\nfunction initTokenRefresher(appCheck) {\r\n    const { app } = appCheck;\r\n    const state = getStateReference(app);\r\n    // Create the refresher but don't start it if `isTokenAutoRefreshEnabled`\r\n    // is not true.\r\n    let refresher = state.tokenRefresher;\r\n    if (!refresher) {\r\n        refresher = createTokenRefresher(appCheck);\r\n        state.tokenRefresher = refresher;\r\n    }\r\n    if (!refresher.isRunning() && state.isTokenAutoRefreshEnabled) {\r\n        refresher.start();\r\n    }\r\n}\r\nfunction createTokenRefresher(appCheck) {\r\n    const { app } = appCheck;\r\n    return new Refresher(\r\n    // Keep in mind when this fails for any reason other than the ones\r\n    // for which we should retry, it will effectively stop the proactive refresh.\r\n    async () => {\r\n        const state = getStateReference(app);\r\n        // If there is no token, we will try to load it from storage and use it\r\n        // If there is a token, we force refresh it because we know it's going to expire soon\r\n        let result;\r\n        if (!state.token) {\r\n            result = await getToken$2(appCheck);\r\n        }\r\n        else {\r\n            result = await getToken$2(appCheck, true);\r\n        }\r\n        /**\r\n         * getToken() always resolves. In case the result has an error field defined, it means\r\n         * the operation failed, and we should retry.\r\n         */\r\n        if (result.error) {\r\n            throw result.error;\r\n        }\r\n        /**\r\n         * A special `internalError` field reflects that there was an error\r\n         * getting a new token from the exchange endpoint, but there's still a\r\n         * previous token that's valid for now and this should be passed to 2P/3P\r\n         * requests for a token. But we want this callback (`this.operation` in\r\n         * `Refresher`) to throw in order to kick off the Refresher's retry\r\n         * backoff. (Setting `hasSucceeded` to false.)\r\n         */\r\n        if (result.internalError) {\r\n            throw result.internalError;\r\n        }\r\n    }, () => {\r\n        return true;\r\n    }, () => {\r\n        const state = getStateReference(app);\r\n        if (state.token) {\r\n            // issuedAtTime + (50% * total TTL) + 5 minutes\r\n            let nextRefreshTimeMillis = state.token.issuedAtTimeMillis +\r\n                (state.token.expireTimeMillis - state.token.issuedAtTimeMillis) *\r\n                    0.5 +\r\n                5 * 60 * 1000;\r\n            // Do not allow refresh time to be past (expireTime - 5 minutes)\r\n            const latestAllowableRefresh = state.token.expireTimeMillis - 5 * 60 * 1000;\r\n            nextRefreshTimeMillis = Math.min(nextRefreshTimeMillis, latestAllowableRefresh);\r\n            return Math.max(0, nextRefreshTimeMillis - Date.now());\r\n        }\r\n        else {\r\n            return 0;\r\n        }\r\n    }, TOKEN_REFRESH_TIME.RETRIAL_MIN_WAIT, TOKEN_REFRESH_TIME.RETRIAL_MAX_WAIT);\r\n}\r\nfunction notifyTokenListeners(app, token) {\r\n    const observers = getStateReference(app).tokenObservers;\r\n    for (const observer of observers) {\r\n        try {\r\n            if (observer.type === \"EXTERNAL\" /* ListenerType.EXTERNAL */ && token.error != null) {\r\n                // If this listener was added by a 3P call, send any token error to\r\n                // the supplied error handler. A 3P observer always has an error\r\n                // handler.\r\n                observer.error(token.error);\r\n            }\r\n            else {\r\n                // If the token has no error field, always return the token.\r\n                // If this is a 2P listener, return the token, whether or not it\r\n                // has an error field.\r\n                observer.next(token);\r\n            }\r\n        }\r\n        catch (e) {\r\n            // Errors in the listener function itself are always ignored.\r\n        }\r\n    }\r\n}\r\nfunction isValid(token) {\r\n    return token.expireTimeMillis - Date.now() > 0;\r\n}\r\nfunction makeDummyTokenResult(error) {\r\n    return {\r\n        token: formatDummyToken(defaultTokenErrorData),\r\n        error\r\n    };\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * AppCheck Service class.\r\n */\r\nclass AppCheckService {\r\n    constructor(app, heartbeatServiceProvider) {\r\n        this.app = app;\r\n        this.heartbeatServiceProvider = heartbeatServiceProvider;\r\n    }\r\n    _delete() {\r\n        const { tokenObservers } = getStateReference(this.app);\r\n        for (const tokenObserver of tokenObservers) {\r\n            removeTokenListener(this.app, tokenObserver.next);\r\n        }\r\n        return Promise.resolve();\r\n    }\r\n}\r\nfunction factory(app, heartbeatServiceProvider) {\r\n    return new AppCheckService(app, heartbeatServiceProvider);\r\n}\r\nfunction internalFactory(appCheck) {\r\n    return {\r\n        getToken: forceRefresh => getToken$2(appCheck, forceRefresh),\r\n        getLimitedUseToken: () => getLimitedUseToken$1(appCheck),\r\n        addTokenListener: listener => addTokenListener(appCheck, \"INTERNAL\" /* ListenerType.INTERNAL */, listener),\r\n        removeTokenListener: listener => removeTokenListener(appCheck.app, listener)\r\n    };\r\n}\n\nconst name = \"@firebase/app-check\";\nconst version = \"0.8.0\";\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst RECAPTCHA_URL = 'https://www.google.com/recaptcha/api.js';\r\nconst RECAPTCHA_ENTERPRISE_URL = 'https://www.google.com/recaptcha/enterprise.js';\r\nfunction initializeV3(app, siteKey) {\r\n    const initialized = new Deferred();\r\n    const state = getStateReference(app);\r\n    state.reCAPTCHAState = { initialized };\r\n    const divId = makeDiv(app);\r\n    const grecaptcha = getRecaptcha(false);\r\n    if (!grecaptcha) {\r\n        loadReCAPTCHAV3Script(() => {\r\n            const grecaptcha = getRecaptcha(false);\r\n            if (!grecaptcha) {\r\n                // it shouldn't happen.\r\n                throw new Error('no recaptcha');\r\n            }\r\n            queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\r\n        });\r\n    }\r\n    else {\r\n        queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\r\n    }\r\n    return initialized.promise;\r\n}\r\nfunction initializeEnterprise(app, siteKey) {\r\n    const initialized = new Deferred();\r\n    const state = getStateReference(app);\r\n    state.reCAPTCHAState = { initialized };\r\n    const divId = makeDiv(app);\r\n    const grecaptcha = getRecaptcha(true);\r\n    if (!grecaptcha) {\r\n        loadReCAPTCHAEnterpriseScript(() => {\r\n            const grecaptcha = getRecaptcha(true);\r\n            if (!grecaptcha) {\r\n                // it shouldn't happen.\r\n                throw new Error('no recaptcha');\r\n            }\r\n            queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\r\n        });\r\n    }\r\n    else {\r\n        queueWidgetRender(app, siteKey, grecaptcha, divId, initialized);\r\n    }\r\n    return initialized.promise;\r\n}\r\n/**\r\n * Add listener to render the widget and resolve the promise when\r\n * the grecaptcha.ready() event fires.\r\n */\r\nfunction queueWidgetRender(app, siteKey, grecaptcha, container, initialized) {\r\n    grecaptcha.ready(() => {\r\n        // Invisible widgets allow us to set a different siteKey for each widget,\r\n        // so we use them to support multiple apps\r\n        renderInvisibleWidget(app, siteKey, grecaptcha, container);\r\n        initialized.resolve(grecaptcha);\r\n    });\r\n}\r\n/**\r\n * Add invisible div to page.\r\n */\r\nfunction makeDiv(app) {\r\n    const divId = `fire_app_check_${app.name}`;\r\n    const invisibleDiv = document.createElement('div');\r\n    invisibleDiv.id = divId;\r\n    invisibleDiv.style.display = 'none';\r\n    document.body.appendChild(invisibleDiv);\r\n    return divId;\r\n}\r\nasync function getToken$1(app) {\r\n    ensureActivated(app);\r\n    // ensureActivated() guarantees that reCAPTCHAState is set\r\n    const reCAPTCHAState = getStateReference(app).reCAPTCHAState;\r\n    const recaptcha = await reCAPTCHAState.initialized.promise;\r\n    return new Promise((resolve, _reject) => {\r\n        // Updated after initialization is complete.\r\n        const reCAPTCHAState = getStateReference(app).reCAPTCHAState;\r\n        recaptcha.ready(() => {\r\n            resolve(\r\n            // widgetId is guaranteed to be available if reCAPTCHAState.initialized.promise resolved.\r\n            recaptcha.execute(reCAPTCHAState.widgetId, {\r\n                action: 'fire_app_check'\r\n            }));\r\n        });\r\n    });\r\n}\r\n/**\r\n *\r\n * @param app\r\n * @param container - Id of a HTML element.\r\n */\r\nfunction renderInvisibleWidget(app, siteKey, grecaptcha, container) {\r\n    const widgetId = grecaptcha.render(container, {\r\n        sitekey: siteKey,\r\n        size: 'invisible',\r\n        // Success callback - set state\r\n        callback: () => {\r\n            getStateReference(app).reCAPTCHAState.succeeded = true;\r\n        },\r\n        // Failure callback - set state\r\n        'error-callback': () => {\r\n            getStateReference(app).reCAPTCHAState.succeeded = false;\r\n        }\r\n    });\r\n    const state = getStateReference(app);\r\n    state.reCAPTCHAState = Object.assign(Object.assign({}, state.reCAPTCHAState), { // state.reCAPTCHAState is set in the initialize()\r\n        widgetId });\r\n}\r\nfunction loadReCAPTCHAV3Script(onload) {\r\n    const script = document.createElement('script');\r\n    script.src = RECAPTCHA_URL;\r\n    script.onload = onload;\r\n    document.head.appendChild(script);\r\n}\r\nfunction loadReCAPTCHAEnterpriseScript(onload) {\r\n    const script = document.createElement('script');\r\n    script.src = RECAPTCHA_ENTERPRISE_URL;\r\n    script.onload = onload;\r\n    document.head.appendChild(script);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * App Check provider that can obtain a reCAPTCHA V3 token and exchange it\r\n * for an App Check token.\r\n *\r\n * @public\r\n */\r\nclass ReCaptchaV3Provider {\r\n    /**\r\n     * Create a ReCaptchaV3Provider instance.\r\n     * @param siteKey - ReCAPTCHA V3 siteKey.\r\n     */\r\n    constructor(_siteKey) {\r\n        this._siteKey = _siteKey;\r\n        /**\r\n         * Throttle requests on certain error codes to prevent too many retries\r\n         * in a short time.\r\n         */\r\n        this._throttleData = null;\r\n    }\r\n    /**\r\n     * Returns an App Check token.\r\n     * @internal\r\n     */\r\n    async getToken() {\r\n        var _a, _b, _c;\r\n        throwIfThrottled(this._throttleData);\r\n        // Top-level `getToken()` has already checked that App Check is initialized\r\n        // and therefore this._app and this._heartbeatServiceProvider are available.\r\n        const attestedClaimsToken = await getToken$1(this._app).catch(_e => {\r\n            // reCaptcha.execute() throws null which is not very descriptive.\r\n            throw ERROR_FACTORY.create(\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */);\r\n        });\r\n        // Check if a failure state was set by the recaptcha \"error-callback\".\r\n        if (!((_a = getStateReference(this._app).reCAPTCHAState) === null || _a === void 0 ? void 0 : _a.succeeded)) {\r\n            throw ERROR_FACTORY.create(\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */);\r\n        }\r\n        let result;\r\n        try {\r\n            result = await exchangeToken(getExchangeRecaptchaV3TokenRequest(this._app, attestedClaimsToken), this._heartbeatServiceProvider);\r\n        }\r\n        catch (e) {\r\n            if ((_b = e.code) === null || _b === void 0 ? void 0 : _b.includes(\"fetch-status-error\" /* AppCheckError.FETCH_STATUS_ERROR */)) {\r\n                this._throttleData = setBackoff(Number((_c = e.customData) === null || _c === void 0 ? void 0 : _c.httpStatus), this._throttleData);\r\n                throw ERROR_FACTORY.create(\"throttled\" /* AppCheckError.THROTTLED */, {\r\n                    time: getDurationString(this._throttleData.allowRequestsAfter - Date.now()),\r\n                    httpStatus: this._throttleData.httpStatus\r\n                });\r\n            }\r\n            else {\r\n                throw e;\r\n            }\r\n        }\r\n        // If successful, clear throttle data.\r\n        this._throttleData = null;\r\n        return result;\r\n    }\r\n    /**\r\n     * @internal\r\n     */\r\n    initialize(app) {\r\n        this._app = app;\r\n        this._heartbeatServiceProvider = _getProvider(app, 'heartbeat');\r\n        initializeV3(app, this._siteKey).catch(() => {\r\n            /* we don't care about the initialization result */\r\n        });\r\n    }\r\n    /**\r\n     * @internal\r\n     */\r\n    isEqual(otherProvider) {\r\n        if (otherProvider instanceof ReCaptchaV3Provider) {\r\n            return this._siteKey === otherProvider._siteKey;\r\n        }\r\n        else {\r\n            return false;\r\n        }\r\n    }\r\n}\r\n/**\r\n * App Check provider that can obtain a reCAPTCHA Enterprise token and exchange it\r\n * for an App Check token.\r\n *\r\n * @public\r\n */\r\nclass ReCaptchaEnterpriseProvider {\r\n    /**\r\n     * Create a ReCaptchaEnterpriseProvider instance.\r\n     * @param siteKey - reCAPTCHA Enterprise score-based site key.\r\n     */\r\n    constructor(_siteKey) {\r\n        this._siteKey = _siteKey;\r\n        /**\r\n         * Throttle requests on certain error codes to prevent too many retries\r\n         * in a short time.\r\n         */\r\n        this._throttleData = null;\r\n    }\r\n    /**\r\n     * Returns an App Check token.\r\n     * @internal\r\n     */\r\n    async getToken() {\r\n        var _a, _b, _c;\r\n        throwIfThrottled(this._throttleData);\r\n        // Top-level `getToken()` has already checked that App Check is initialized\r\n        // and therefore this._app and this._heartbeatServiceProvider are available.\r\n        const attestedClaimsToken = await getToken$1(this._app).catch(_e => {\r\n            // reCaptcha.execute() throws null which is not very descriptive.\r\n            throw ERROR_FACTORY.create(\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */);\r\n        });\r\n        // Check if a failure state was set by the recaptcha \"error-callback\".\r\n        if (!((_a = getStateReference(this._app).reCAPTCHAState) === null || _a === void 0 ? void 0 : _a.succeeded)) {\r\n            throw ERROR_FACTORY.create(\"recaptcha-error\" /* AppCheckError.RECAPTCHA_ERROR */);\r\n        }\r\n        let result;\r\n        try {\r\n            result = await exchangeToken(getExchangeRecaptchaEnterpriseTokenRequest(this._app, attestedClaimsToken), this._heartbeatServiceProvider);\r\n        }\r\n        catch (e) {\r\n            if ((_b = e.code) === null || _b === void 0 ? void 0 : _b.includes(\"fetch-status-error\" /* AppCheckError.FETCH_STATUS_ERROR */)) {\r\n                this._throttleData = setBackoff(Number((_c = e.customData) === null || _c === void 0 ? void 0 : _c.httpStatus), this._throttleData);\r\n                throw ERROR_FACTORY.create(\"throttled\" /* AppCheckError.THROTTLED */, {\r\n                    time: getDurationString(this._throttleData.allowRequestsAfter - Date.now()),\r\n                    httpStatus: this._throttleData.httpStatus\r\n                });\r\n            }\r\n            else {\r\n                throw e;\r\n            }\r\n        }\r\n        // If successful, clear throttle data.\r\n        this._throttleData = null;\r\n        return result;\r\n    }\r\n    /**\r\n     * @internal\r\n     */\r\n    initialize(app) {\r\n        this._app = app;\r\n        this._heartbeatServiceProvider = _getProvider(app, 'heartbeat');\r\n        initializeEnterprise(app, this._siteKey).catch(() => {\r\n            /* we don't care about the initialization result */\r\n        });\r\n    }\r\n    /**\r\n     * @internal\r\n     */\r\n    isEqual(otherProvider) {\r\n        if (otherProvider instanceof ReCaptchaEnterpriseProvider) {\r\n            return this._siteKey === otherProvider._siteKey;\r\n        }\r\n        else {\r\n            return false;\r\n        }\r\n    }\r\n}\r\n/**\r\n * Custom provider class.\r\n * @public\r\n */\r\nclass CustomProvider {\r\n    constructor(_customProviderOptions) {\r\n        this._customProviderOptions = _customProviderOptions;\r\n    }\r\n    /**\r\n     * @internal\r\n     */\r\n    async getToken() {\r\n        // custom provider\r\n        const customToken = await this._customProviderOptions.getToken();\r\n        // Try to extract IAT from custom token, in case this token is not\r\n        // being newly issued. JWT timestamps are in seconds since epoch.\r\n        const issuedAtTimeSeconds = issuedAtTime(customToken.token);\r\n        // Very basic validation, use current timestamp as IAT if JWT\r\n        // has no `iat` field or value is out of bounds.\r\n        const issuedAtTimeMillis = issuedAtTimeSeconds !== null &&\r\n            issuedAtTimeSeconds < Date.now() &&\r\n            issuedAtTimeSeconds > 0\r\n            ? issuedAtTimeSeconds * 1000\r\n            : Date.now();\r\n        return Object.assign(Object.assign({}, customToken), { issuedAtTimeMillis });\r\n    }\r\n    /**\r\n     * @internal\r\n     */\r\n    initialize(app) {\r\n        this._app = app;\r\n    }\r\n    /**\r\n     * @internal\r\n     */\r\n    isEqual(otherProvider) {\r\n        if (otherProvider instanceof CustomProvider) {\r\n            return (this._customProviderOptions.getToken.toString() ===\r\n                otherProvider._customProviderOptions.getToken.toString());\r\n        }\r\n        else {\r\n            return false;\r\n        }\r\n    }\r\n}\r\n/**\r\n * Set throttle data to block requests until after a certain time\r\n * depending on the failed request's status code.\r\n * @param httpStatus - Status code of failed request.\r\n * @param throttleData - `ThrottleData` object containing previous throttle\r\n * data state.\r\n * @returns Data about current throttle state and expiration time.\r\n */\r\nfunction setBackoff(httpStatus, throttleData) {\r\n    /**\r\n     * Block retries for 1 day for the following error codes:\r\n     *\r\n     * 404: Likely malformed URL.\r\n     *\r\n     * 403:\r\n     * - Attestation failed\r\n     * - Wrong API key\r\n     * - Project deleted\r\n     */\r\n    if (httpStatus === 404 || httpStatus === 403) {\r\n        return {\r\n            backoffCount: 1,\r\n            allowRequestsAfter: Date.now() + ONE_DAY,\r\n            httpStatus\r\n        };\r\n    }\r\n    else {\r\n        /**\r\n         * For all other error codes, the time when it is ok to retry again\r\n         * is based on exponential backoff.\r\n         */\r\n        const backoffCount = throttleData ? throttleData.backoffCount : 0;\r\n        const backoffMillis = calculateBackoffMillis(backoffCount, 1000, 2);\r\n        return {\r\n            backoffCount: backoffCount + 1,\r\n            allowRequestsAfter: Date.now() + backoffMillis,\r\n            httpStatus\r\n        };\r\n    }\r\n}\r\nfunction throwIfThrottled(throttleData) {\r\n    if (throttleData) {\r\n        if (Date.now() - throttleData.allowRequestsAfter <= 0) {\r\n            // If before, throw.\r\n            throw ERROR_FACTORY.create(\"throttled\" /* AppCheckError.THROTTLED */, {\r\n                time: getDurationString(throttleData.allowRequestsAfter - Date.now()),\r\n                httpStatus: throttleData.httpStatus\r\n            });\r\n        }\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Activate App Check for the given app. Can be called only once per app.\r\n * @param app - the {@link @firebase/app#FirebaseApp} to activate App Check for\r\n * @param options - App Check initialization options\r\n * @public\r\n */\r\nfunction initializeAppCheck(app = getApp(), options) {\r\n    app = getModularInstance(app);\r\n    const provider = _getProvider(app, 'app-check');\r\n    // Ensure initializeDebugMode() is only called once.\r\n    if (!getDebugState().initialized) {\r\n        initializeDebugMode();\r\n    }\r\n    // Log a message containing the debug token when `initializeAppCheck()`\r\n    // is called in debug mode.\r\n    if (isDebugMode()) {\r\n        // Do not block initialization to get the token for the message.\r\n        void getDebugToken().then(token => \r\n        // Not using logger because I don't think we ever want this accidentally hidden.\r\n        console.log(`App Check debug token: ${token}. You will need to add it to your app's App Check settings in the Firebase console for it to work.`));\r\n    }\r\n    if (provider.isInitialized()) {\r\n        const existingInstance = provider.getImmediate();\r\n        const initialOptions = provider.getOptions();\r\n        if (initialOptions.isTokenAutoRefreshEnabled ===\r\n            options.isTokenAutoRefreshEnabled &&\r\n            initialOptions.provider.isEqual(options.provider)) {\r\n            return existingInstance;\r\n        }\r\n        else {\r\n            throw ERROR_FACTORY.create(\"already-initialized\" /* AppCheckError.ALREADY_INITIALIZED */, {\r\n                appName: app.name\r\n            });\r\n        }\r\n    }\r\n    const appCheck = provider.initialize({ options });\r\n    _activate(app, options.provider, options.isTokenAutoRefreshEnabled);\r\n    // If isTokenAutoRefreshEnabled is false, do not send any requests to the\r\n    // exchange endpoint without an explicit call from the user either directly\r\n    // or through another Firebase library (storage, functions, etc.)\r\n    if (getStateReference(app).isTokenAutoRefreshEnabled) {\r\n        // Adding a listener will start the refresher and fetch a token if needed.\r\n        // This gets a token ready and prevents a delay when an internal library\r\n        // requests the token.\r\n        // Listener function does not need to do anything, its base functionality\r\n        // of calling getToken() already fetches token and writes it to memory/storage.\r\n        addTokenListener(appCheck, \"INTERNAL\" /* ListenerType.INTERNAL */, () => { });\r\n    }\r\n    return appCheck;\r\n}\r\n/**\r\n * Activate App Check\r\n * @param app - Firebase app to activate App Check for.\r\n * @param provider - reCAPTCHA v3 provider or\r\n * custom token provider.\r\n * @param isTokenAutoRefreshEnabled - If true, the SDK automatically\r\n * refreshes App Check tokens as needed. If undefined, defaults to the\r\n * value of `app.automaticDataCollectionEnabled`, which defaults to\r\n * false and can be set in the app config.\r\n */\r\nfunction _activate(app, provider, isTokenAutoRefreshEnabled) {\r\n    // Create an entry in the APP_CHECK_STATES map. Further changes should\r\n    // directly mutate this object.\r\n    const state = setInitialState(app, Object.assign({}, DEFAULT_STATE));\r\n    state.activated = true;\r\n    state.provider = provider; // Read cached token from storage if it exists and store it in memory.\r\n    state.cachedTokenPromise = readTokenFromStorage(app).then(cachedToken => {\r\n        if (cachedToken && isValid(cachedToken)) {\r\n            state.token = cachedToken;\r\n            // notify all listeners with the cached token\r\n            notifyTokenListeners(app, { token: cachedToken.token });\r\n        }\r\n        return cachedToken;\r\n    });\r\n    // Use value of global `automaticDataCollectionEnabled` (which\r\n    // itself defaults to false if not specified in config) if\r\n    // `isTokenAutoRefreshEnabled` param was not provided by user.\r\n    state.isTokenAutoRefreshEnabled =\r\n        isTokenAutoRefreshEnabled === undefined\r\n            ? app.automaticDataCollectionEnabled\r\n            : isTokenAutoRefreshEnabled;\r\n    state.provider.initialize(app);\r\n}\r\n/**\r\n * Set whether App Check will automatically refresh tokens as needed.\r\n *\r\n * @param appCheckInstance - The App Check service instance.\r\n * @param isTokenAutoRefreshEnabled - If true, the SDK automatically\r\n * refreshes App Check tokens as needed. This overrides any value set\r\n * during `initializeAppCheck()`.\r\n * @public\r\n */\r\nfunction setTokenAutoRefreshEnabled(appCheckInstance, isTokenAutoRefreshEnabled) {\r\n    const app = appCheckInstance.app;\r\n    const state = getStateReference(app);\r\n    // This will exist if any product libraries have called\r\n    // `addTokenListener()`\r\n    if (state.tokenRefresher) {\r\n        if (isTokenAutoRefreshEnabled === true) {\r\n            state.tokenRefresher.start();\r\n        }\r\n        else {\r\n            state.tokenRefresher.stop();\r\n        }\r\n    }\r\n    state.isTokenAutoRefreshEnabled = isTokenAutoRefreshEnabled;\r\n}\r\n/**\r\n * Get the current App Check token. Attaches to the most recent\r\n * in-flight request if one is present. Returns null if no token\r\n * is present and no token requests are in-flight.\r\n *\r\n * @param appCheckInstance - The App Check service instance.\r\n * @param forceRefresh - If true, will always try to fetch a fresh token.\r\n * If false, will use a cached token if found in storage.\r\n * @public\r\n */\r\nasync function getToken(appCheckInstance, forceRefresh) {\r\n    const result = await getToken$2(appCheckInstance, forceRefresh);\r\n    if (result.error) {\r\n        throw result.error;\r\n    }\r\n    return { token: result.token };\r\n}\r\n/**\r\n * Requests a Firebase App Check token. This method should be used\r\n * only if you need to authorize requests to a non-Firebase backend.\r\n *\r\n * Returns limited-use tokens that are intended for use with your\r\n * non-Firebase backend endpoints that are protected with\r\n * <a href=\"https://firebase.google.com/docs/app-check/custom-resource-backend#replay-protection\">\r\n * Replay Protection</a>. This method\r\n * does not affect the token generation behavior of the\r\n * #getAppCheckToken() method.\r\n *\r\n * @param appCheckInstance - The App Check service instance.\r\n * @returns The limited use token.\r\n * @public\r\n */\r\nfunction getLimitedUseToken(appCheckInstance) {\r\n    return getLimitedUseToken$1(appCheckInstance);\r\n}\r\n/**\r\n * Wraps `addTokenListener`/`removeTokenListener` methods in an `Observer`\r\n * pattern for public use.\r\n */\r\nfunction onTokenChanged(appCheckInstance, onNextOrObserver, onError, \r\n/**\r\n * NOTE: Although an `onCompletion` callback can be provided, it will\r\n * never be called because the token stream is never-ending.\r\n * It is added only for API consistency with the observer pattern, which\r\n * we follow in JS APIs.\r\n */\r\n// eslint-disable-next-line @typescript-eslint/no-unused-vars\r\nonCompletion) {\r\n    let nextFn = () => { };\r\n    let errorFn = () => { };\r\n    if (onNextOrObserver.next != null) {\r\n        nextFn = onNextOrObserver.next.bind(onNextOrObserver);\r\n    }\r\n    else {\r\n        nextFn = onNextOrObserver;\r\n    }\r\n    if (onNextOrObserver.error != null) {\r\n        errorFn = onNextOrObserver.error.bind(onNextOrObserver);\r\n    }\r\n    else if (onError) {\r\n        errorFn = onError;\r\n    }\r\n    addTokenListener(appCheckInstance, \"EXTERNAL\" /* ListenerType.EXTERNAL */, nextFn, errorFn);\r\n    return () => removeTokenListener(appCheckInstance.app, nextFn);\r\n}\n\n/**\r\n * Firebase App Check\r\n *\r\n * @packageDocumentation\r\n */\r\nconst APP_CHECK_NAME = 'app-check';\r\nconst APP_CHECK_NAME_INTERNAL = 'app-check-internal';\r\nfunction registerAppCheck() {\r\n    // The public interface\r\n    _registerComponent(new Component(APP_CHECK_NAME, container => {\r\n        // getImmediate for FirebaseApp will always succeed\r\n        const app = container.getProvider('app').getImmediate();\r\n        const heartbeatServiceProvider = container.getProvider('heartbeat');\r\n        return factory(app, heartbeatServiceProvider);\r\n    }, \"PUBLIC\" /* ComponentType.PUBLIC */)\r\n        .setInstantiationMode(\"EXPLICIT\" /* InstantiationMode.EXPLICIT */)\r\n        /**\r\n         * Initialize app-check-internal after app-check is initialized to make AppCheck available to\r\n         * other Firebase SDKs\r\n         */\r\n        .setInstanceCreatedCallback((container, _identifier, _appcheckService) => {\r\n        container.getProvider(APP_CHECK_NAME_INTERNAL).initialize();\r\n    }));\r\n    // The internal interface used by other Firebase products\r\n    _registerComponent(new Component(APP_CHECK_NAME_INTERNAL, container => {\r\n        const appCheck = container.getProvider('app-check').getImmediate();\r\n        return internalFactory(appCheck);\r\n    }, \"PUBLIC\" /* ComponentType.PUBLIC */).setInstantiationMode(\"EXPLICIT\" /* InstantiationMode.EXPLICIT */));\r\n    registerVersion(name, version);\r\n}\r\nregisterAppCheck();\n\nexport { CustomProvider, ReCaptchaEnterpriseProvider, ReCaptchaV3Provider, getLimitedUseToken, getToken, initializeAppCheck, onTokenChanged, setTokenAutoRefreshEnabled };\n"], "mappings": ";AAAA,SAASA,YAAY,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,eAAe,QAAQ,eAAe;AACzF,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,QAAQ,EAAEC,YAAY,EAAEC,oBAAoB,EAAEC,MAAM,EAAEC,SAAS,EAAEC,MAAM,EAAEC,YAAY,EAAEC,sBAAsB,EAAEC,kBAAkB,QAAQ,gBAAgB;AAClK,SAASC,MAAM,QAAQ,kBAAkB;;AAEzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;AAClC,MAAMC,aAAa,GAAG;EAClBC,SAAS,EAAE,KAAK;EAChBC,cAAc,EAAE;AACpB,CAAC;AACD,MAAMC,WAAW,GAAG;EAChBC,WAAW,EAAE,KAAK;EAClBC,OAAO,EAAE;AACb,CAAC;AACD;AACA;AACA;AACA,SAASC,iBAAiBA,CAACC,GAAG,EAAE;EAC5B,OAAOT,gBAAgB,CAACU,GAAG,CAACD,GAAG,CAAC,IAAIE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,aAAa,CAAC;AACxE;AACA;AACA;AACA;AACA;AACA,SAASW,eAAeA,CAACJ,GAAG,EAAEK,KAAK,EAAE;EACjCd,gBAAgB,CAACe,GAAG,CAACN,GAAG,EAAEK,KAAK,CAAC;EAChC,OAAOd,gBAAgB,CAACU,GAAG,CAACD,GAAG,CAAC;AACpC;AACA,SAASO,aAAaA,CAAA,EAAG;EACrB,OAAOX,WAAW;AACtB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMY,aAAa,GAAG,oDAAoD;AAC1E,MAAMC,+BAA+B,GAAG,0BAA0B;AAClE,MAAMC,0CAA0C,GAAG,kCAAkC;AACrF,MAAMC,2BAA2B,GAAG,oBAAoB;AACxD,MAAMC,kBAAkB,GAAG;EACvB;AACJ;AACA;AACA;EACIC,eAAe,EAAE,CAAC,GAAG,EAAE,GAAG,IAAI;EAC9B;AACJ;AACA;AACA;EACIC,gBAAgB,EAAE,EAAE,GAAG,IAAI;EAC3B;AACJ;AACA;EACIC,gBAAgB,EAAE,EAAE,GAAG,EAAE,GAAG;AAChC,CAAC;AACD;AACA;AACA;AACA,MAAMC,OAAO,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI;;AAEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZC,WAAWA,CAACC,SAAS,EAAEC,WAAW,EAAEC,eAAe,EAAEC,UAAU,EAAEC,UAAU,EAAE;IACzE,IAAI,CAACJ,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,OAAO,GAAG,IAAI;IACnB,IAAI,CAACC,qBAAqB,GAAGH,UAAU;IACvC,IAAIA,UAAU,GAAGC,UAAU,EAAE;MACzB,MAAM,IAAIG,KAAK,CAAC,yDAAyD,CAAC;IAC9E;EACJ;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACF,qBAAqB,GAAG,IAAI,CAACH,UAAU;IAC5C,IAAI,CAACM,OAAO,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,MAAM;MAC3B;IAAA,CACH,CAAC;EACN;EACAC,IAAIA,CAAA,EAAG;IACH,IAAI,IAAI,CAACN,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,CAACO,MAAM,CAAC,WAAW,CAAC;MAChC,IAAI,CAACP,OAAO,GAAG,IAAI;IACvB;EACJ;EACAQ,SAASA,CAAA,EAAG;IACR,OAAO,CAAC,CAAC,IAAI,CAACR,OAAO;EACzB;EACMI,OAAOA,CAACK,YAAY,EAAE;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACxBD,KAAI,CAACJ,IAAI,CAAC,CAAC;MACX,IAAI;QACAI,KAAI,CAACV,OAAO,GAAG,IAAI3C,QAAQ,CAAC,CAAC;QAC7B,MAAMuD,KAAK,CAACF,KAAI,CAACG,UAAU,CAACJ,YAAY,CAAC,CAAC;QAC1C;QACA;QACA;QACA;QACA;QACAC,KAAI,CAACV,OAAO,CAACc,OAAO,CAAC,CAAC;QACtB,MAAMJ,KAAI,CAACV,OAAO,CAACe,OAAO;QAC1BL,KAAI,CAACV,OAAO,GAAG,IAAI3C,QAAQ,CAAC,CAAC;QAC7B,MAAMqD,KAAI,CAACf,SAAS,CAAC,CAAC;QACtBe,KAAI,CAACV,OAAO,CAACc,OAAO,CAAC,CAAC;QACtB,MAAMJ,KAAI,CAACV,OAAO,CAACe,OAAO;QAC1BL,KAAI,CAACN,OAAO,CAAC,IAAI,CAAC,CAACC,KAAK,CAAC,MAAM;UAC3B;QAAA,CACH,CAAC;MACN,CAAC,CACD,OAAOW,KAAK,EAAE;QACV,IAAIN,KAAI,CAACd,WAAW,CAACoB,KAAK,CAAC,EAAE;UACzBN,KAAI,CAACN,OAAO,CAAC,KAAK,CAAC,CAACC,KAAK,CAAC,MAAM;YAC5B;UAAA,CACH,CAAC;QACN,CAAC,MACI;UACDK,KAAI,CAACJ,IAAI,CAAC,CAAC;QACf;MACJ;IAAC;EACL;EACAO,UAAUA,CAACJ,YAAY,EAAE;IACrB,IAAIA,YAAY,EAAE;MACd;MACA;MACA,IAAI,CAACR,qBAAqB,GAAG,IAAI,CAACH,UAAU;MAC5C;MACA,OAAO,IAAI,CAACD,eAAe,CAAC,CAAC;IACjC,CAAC,MACI;MACD;MACA,MAAMoB,wBAAwB,GAAG,IAAI,CAAChB,qBAAqB;MAC3D;MACA,IAAI,CAACA,qBAAqB,IAAI,CAAC;MAC/B;MACA,IAAI,IAAI,CAACA,qBAAqB,GAAG,IAAI,CAACF,UAAU,EAAE;QAC9C,IAAI,CAACE,qBAAqB,GAAG,IAAI,CAACF,UAAU;MAChD;MACA,OAAOkB,wBAAwB;IACnC;EACJ;AACJ;AACA,SAASL,KAAKA,CAACM,EAAE,EAAE;EACf,OAAO,IAAIC,OAAO,CAACL,OAAO,IAAI;IAC1BM,UAAU,CAACN,OAAO,EAAEI,EAAE,CAAC;EAC3B,CAAC,CAAC;AACN;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,MAAM,GAAG;EACX,CAAC,qBAAqB,CAAC,0CAA0C,+EAA+E,GAC5I,6EAA6E,GAC7E,sEAAsE,GACtE,+BAA+B;EACnC,CAAC,uBAAuB,CAAC,4CAA4C,4FAA4F,GAC7J,yEAAyE;EAC7E,CAAC,qBAAqB,CAAC,0CAA0C,mEAAmE,GAChI,0CAA0C;EAC9C,CAAC,mBAAmB,CAAC,wCAAwC,wCAAwC,GACjG,2CAA2C;EAC/C,CAAC,oBAAoB,CAAC,yCAAyC,yEAAyE;EACxI,CAAC,cAAc,CAAC,mCAAmC,6EAA6E;EAChI,CAAC,aAAa,CAAC,kCAAkC,kFAAkF;EACnI,CAAC,aAAa,CAAC,oCAAoC,gFAAgF;EACnI,CAAC,iBAAiB,CAAC,sCAAsC,kBAAkB;EAC3E,CAAC,WAAW,CAAC,gCAAgC;AACjD,CAAC;AACD,MAAMC,aAAa,GAAG,IAAIhE,YAAY,CAAC,UAAU,EAAE,UAAU,EAAE+D,MAAM,CAAC;;AAEtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,YAAYA,CAACC,YAAY,GAAG,KAAK,EAAE;EACxC,IAAIC,EAAE;EACN,IAAID,YAAY,EAAE;IACd,OAAO,CAACC,EAAE,GAAGC,IAAI,CAACC,UAAU,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,UAAU;EACpF;EACA,OAAOF,IAAI,CAACC,UAAU;AAC1B;AACA,SAASE,eAAeA,CAACrD,GAAG,EAAE;EAC1B,IAAI,CAACD,iBAAiB,CAACC,GAAG,CAAC,CAACN,SAAS,EAAE;IACnC,MAAMoD,aAAa,CAACQ,MAAM,CAAC,uBAAuB,CAAC,2CAA2C;MAC1FC,OAAO,EAAEvD,GAAG,CAACwD;IACjB,CAAC,CAAC;EACN;AACJ;AACA,SAASC,iBAAiBA,CAACC,gBAAgB,EAAE;EACzC,MAAMC,YAAY,GAAGC,IAAI,CAACC,KAAK,CAACH,gBAAgB,GAAG,IAAI,CAAC;EACxD,MAAMI,IAAI,GAAGF,IAAI,CAACG,KAAK,CAACJ,YAAY,IAAI,IAAI,GAAG,EAAE,CAAC,CAAC;EACnD,MAAMK,KAAK,GAAGJ,IAAI,CAACG,KAAK,CAAC,CAACJ,YAAY,GAAGG,IAAI,GAAG,IAAI,GAAG,EAAE,IAAI,IAAI,CAAC;EAClE,MAAMG,OAAO,GAAGL,IAAI,CAACG,KAAK,CAAC,CAACJ,YAAY,GAAGG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAGE,KAAK,GAAG,IAAI,IAAI,EAAE,CAAC;EACjF,MAAME,OAAO,GAAGP,YAAY,GAAGG,IAAI,GAAG,IAAI,GAAG,EAAE,GAAGE,KAAK,GAAG,IAAI,GAAGC,OAAO,GAAG,EAAE;EAC7E,IAAIE,MAAM,GAAG,EAAE;EACf,IAAIL,IAAI,EAAE;IACNK,MAAM,IAAIC,GAAG,CAACN,IAAI,CAAC,GAAG,IAAI;EAC9B;EACA,IAAIE,KAAK,EAAE;IACPG,MAAM,IAAIC,GAAG,CAACJ,KAAK,CAAC,GAAG,IAAI;EAC/B;EACAG,MAAM,IAAIC,GAAG,CAACH,OAAO,CAAC,GAAG,IAAI,GAAGG,GAAG,CAACF,OAAO,CAAC,GAAG,GAAG;EAClD,OAAOC,MAAM;AACjB;AACA,SAASC,GAAGA,CAACC,KAAK,EAAE;EAChB,IAAIA,KAAK,KAAK,CAAC,EAAE;IACb,OAAO,IAAI;EACf;EACA,OAAOA,KAAK,IAAI,EAAE,GAAGA,KAAK,CAACC,QAAQ,CAAC,CAAC,GAAG,GAAG,GAAGD,KAAK;AACvD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBeE,aAAaA,CAAAC,EAAA,EAAAC,GAAA;EAAA,OAAAC,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,eAAA;EAAAA,cAAA,GAAAvC,iBAAA,CAA5B,WAA6B;IAAE0C,GAAG;IAAEC;EAAK,CAAC,EAAEC,wBAAwB,EAAE;IAClE,MAAMC,OAAO,GAAG;MACZ,cAAc,EAAE;IACpB,CAAC;IACD;IACA,MAAMC,gBAAgB,GAAGF,wBAAwB,CAACG,YAAY,CAAC;MAC3DC,QAAQ,EAAE;IACd,CAAC,CAAC;IACF,IAAIF,gBAAgB,EAAE;MAClB,MAAMG,gBAAgB,SAASH,gBAAgB,CAACI,mBAAmB,CAAC,CAAC;MACrE,IAAID,gBAAgB,EAAE;QAClBJ,OAAO,CAAC,mBAAmB,CAAC,GAAGI,gBAAgB;MACnD;IACJ;IACA,MAAME,OAAO,GAAG;MACZC,MAAM,EAAE,MAAM;MACdT,IAAI,EAAEU,IAAI,CAACC,SAAS,CAACX,IAAI,CAAC;MAC1BE;IACJ,CAAC;IACD,IAAIU,QAAQ;IACZ,IAAI;MACAA,QAAQ,SAASC,KAAK,CAACd,GAAG,EAAES,OAAO,CAAC;IACxC,CAAC,CACD,OAAOM,aAAa,EAAE;MAClB,MAAM9C,aAAa,CAACQ,MAAM,CAAC,qBAAqB,CAAC,yCAAyC;QACtFuC,oBAAoB,EAAED,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACE;MACtG,CAAC,CAAC;IACN;IACA,IAAIJ,QAAQ,CAACK,MAAM,KAAK,GAAG,EAAE;MACzB,MAAMjD,aAAa,CAACQ,MAAM,CAAC,oBAAoB,CAAC,wCAAwC;QACpF0C,UAAU,EAAEN,QAAQ,CAACK;MACzB,CAAC,CAAC;IACN;IACA,IAAIE,YAAY;IAChB,IAAI;MACA;MACAA,YAAY,SAASP,QAAQ,CAACQ,IAAI,CAAC,CAAC;IACxC,CAAC,CACD,OAAON,aAAa,EAAE;MAClB,MAAM9C,aAAa,CAACQ,MAAM,CAAC,mBAAmB,CAAC,uCAAuC;QAClFuC,oBAAoB,EAAED,aAAa,KAAK,IAAI,IAAIA,aAAa,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,aAAa,CAACE;MACtG,CAAC,CAAC;IACN;IACA;IACA;IACA,MAAMK,KAAK,GAAGF,YAAY,CAACG,GAAG,CAACD,KAAK,CAAC,eAAe,CAAC;IACrD,IAAI,CAACA,KAAK,IAAI,CAACA,KAAK,CAAC,CAAC,CAAC,IAAIE,KAAK,CAACC,MAAM,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,CAAC,EAAE;MAChD,MAAMrD,aAAa,CAACQ,MAAM,CAAC,mBAAmB,CAAC,uCAAuC;QAClFuC,oBAAoB,EAAE,8DAA8D,GAChF,WAAWI,YAAY,CAACG,GAAG;MACnC,CAAC,CAAC;IACN;IACA,MAAMG,kBAAkB,GAAGD,MAAM,CAACH,KAAK,CAAC,CAAC,CAAC,CAAC,GAAG,IAAI;IAClD,MAAMK,GAAG,GAAGC,IAAI,CAACD,GAAG,CAAC,CAAC;IACtB,OAAO;MACHE,KAAK,EAAET,YAAY,CAACS,KAAK;MACzBC,gBAAgB,EAAEH,GAAG,GAAGD,kBAAkB;MAC1CK,kBAAkB,EAAEJ;IACxB,CAAC;EACL,CAAC;EAAA,OAAA9B,cAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,SAASiC,kCAAkCA,CAAC7G,GAAG,EAAE8G,cAAc,EAAE;EAC7D,MAAM;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAGjH,GAAG,CAACsF,OAAO;EAChD,OAAO;IACHT,GAAG,EAAE,GAAGrE,aAAa,aAAauG,SAAS,SAASC,KAAK,IAAIvG,+BAA+B,QAAQwG,MAAM,EAAE;IAC5GnC,IAAI,EAAE;MACF,oBAAoB,EAAEgC;IAC1B;EACJ,CAAC;AACL;AACA,SAASI,0CAA0CA,CAAClH,GAAG,EAAE8G,cAAc,EAAE;EACrE,MAAM;IAAEC,SAAS;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAGjH,GAAG,CAACsF,OAAO;EAChD,OAAO;IACHT,GAAG,EAAE,GAAGrE,aAAa,aAAauG,SAAS,SAASC,KAAK,IAAItG,0CAA0C,QAAQuG,MAAM,EAAE;IACvHnC,IAAI,EAAE;MACF,4BAA4B,EAAEgC;IAClC;EACJ,CAAC;AACL;AACA,SAASK,4BAA4BA,CAACnH,GAAG,EAAEoH,UAAU,EAAE;EACnD,MAAM;IAAEL,SAAS;IAAEC,KAAK;IAAEC;EAAO,CAAC,GAAGjH,GAAG,CAACsF,OAAO;EAChD,OAAO;IACHT,GAAG,EAAE,GAAGrE,aAAa,aAAauG,SAAS,SAASC,KAAK,IAAIrG,2BAA2B,QAAQsG,MAAM,EAAE;IACxGnC,IAAI,EAAE;MACF;MACAuC,WAAW,EAAED;IACjB;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,OAAO,GAAG,6BAA6B;AAC7C,MAAMC,UAAU,GAAG,CAAC;AACpB,MAAMC,UAAU,GAAG,0BAA0B;AAC7C,MAAMC,eAAe,GAAG,aAAa;AACrC,IAAIC,SAAS,GAAG,IAAI;AACpB,SAASC,YAAYA,CAAA,EAAG;EACpB,IAAID,SAAS,EAAE;IACX,OAAOA,SAAS;EACpB;EACAA,SAAS,GAAG,IAAI/E,OAAO,CAAC,CAACL,OAAO,EAAEP,MAAM,KAAK;IACzC,IAAI;MACA,MAAM6F,OAAO,GAAGC,SAAS,CAACC,IAAI,CAACR,OAAO,EAAEC,UAAU,CAAC;MACnDK,OAAO,CAACG,SAAS,GAAGC,KAAK,IAAI;QACzB1F,OAAO,CAAC0F,KAAK,CAACC,MAAM,CAAC9D,MAAM,CAAC;MAChC,CAAC;MACDyD,OAAO,CAACM,OAAO,GAAGF,KAAK,IAAI;QACvB,IAAI/E,EAAE;QACNlB,MAAM,CAACe,aAAa,CAACQ,MAAM,CAAC,cAAc,CAAC,kCAAkC;UACzEuC,oBAAoB,EAAE,CAAC5C,EAAE,GAAG+E,KAAK,CAACC,MAAM,CAACzF,KAAK,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6C;QAC5F,CAAC,CAAC,CAAC;MACP,CAAC;MACD8B,OAAO,CAACO,eAAe,GAAGH,KAAK,IAAI;QAC/B,MAAMI,EAAE,GAAGJ,KAAK,CAACC,MAAM,CAAC9D,MAAM;QAC9B;QACA;QACA;QACA;QACA;QACA,QAAQ6D,KAAK,CAACK,UAAU;UACpB,KAAK,CAAC;YACFD,EAAE,CAACE,iBAAiB,CAACd,UAAU,EAAE;cAC7Be,OAAO,EAAE;YACb,CAAC,CAAC;QACV;MACJ,CAAC;IACL,CAAC,CACD,OAAOC,CAAC,EAAE;MACNzG,MAAM,CAACe,aAAa,CAACQ,MAAM,CAAC,cAAc,CAAC,kCAAkC;QACzEuC,oBAAoB,EAAE2C,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC1C;MAClE,CAAC,CAAC,CAAC;IACP;EACJ,CAAC,CAAC;EACF,OAAO4B,SAAS;AACpB;AACA,SAASe,sBAAsBA,CAACzI,GAAG,EAAE;EACjC,OAAO0I,IAAI,CAACC,UAAU,CAAC3I,GAAG,CAAC,CAAC;AAChC;AACA,SAAS4I,qBAAqBA,CAAC5I,GAAG,EAAE0G,KAAK,EAAE;EACvC,OAAOmC,KAAK,CAACF,UAAU,CAAC3I,GAAG,CAAC,EAAE0G,KAAK,CAAC;AACxC;AACA,SAASoC,0BAA0BA,CAACpC,KAAK,EAAE;EACvC,OAAOmC,KAAK,CAACpB,eAAe,EAAEf,KAAK,CAAC;AACxC;AACA,SAASqC,2BAA2BA,CAAA,EAAG;EACnC,OAAOL,IAAI,CAACjB,eAAe,CAAC;AAChC;AAAC,SACcoB,KAAKA,CAAAG,GAAA,EAAAC,GAAA;EAAA,OAAAC,MAAA,CAAAvE,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAsE,OAAA;EAAAA,MAAA,GAAA/G,iBAAA,CAApB,WAAqBgH,GAAG,EAAE9E,KAAK,EAAE;IAC7B,MAAM+D,EAAE,SAAST,YAAY,CAAC,CAAC;IAC/B,MAAMyB,WAAW,GAAGhB,EAAE,CAACgB,WAAW,CAAC5B,UAAU,EAAE,WAAW,CAAC;IAC3D,MAAM6B,KAAK,GAAGD,WAAW,CAACE,WAAW,CAAC9B,UAAU,CAAC;IACjD,MAAMI,OAAO,GAAGyB,KAAK,CAACE,GAAG,CAAC;MACtBC,YAAY,EAAEL,GAAG;MACjB9E;IACJ,CAAC,CAAC;IACF,OAAO,IAAI1B,OAAO,CAAC,CAACL,OAAO,EAAEP,MAAM,KAAK;MACpC6F,OAAO,CAACG,SAAS,GAAG0B,MAAM,IAAI;QAC1BnH,OAAO,CAAC,CAAC;MACb,CAAC;MACD8G,WAAW,CAAClB,OAAO,GAAGF,KAAK,IAAI;QAC3B,IAAI/E,EAAE;QACNlB,MAAM,CAACe,aAAa,CAACQ,MAAM,CAAC,aAAa,CAAC,mCAAmC;UACzEuC,oBAAoB,EAAE,CAAC5C,EAAE,GAAG+E,KAAK,CAACC,MAAM,CAACzF,KAAK,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6C;QAC5F,CAAC,CAAC,CAAC;MACP,CAAC;IACL,CAAC,CAAC;EACN,CAAC;EAAA,OAAAoD,MAAA,CAAAvE,KAAA,OAAAC,SAAA;AAAA;AAAA,SACc8D,IAAIA,CAAAgB,GAAA;EAAA,OAAAC,KAAA,CAAAhF,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA+E,MAAA;EAAAA,KAAA,GAAAxH,iBAAA,CAAnB,WAAoBgH,GAAG,EAAE;IACrB,MAAMf,EAAE,SAAST,YAAY,CAAC,CAAC;IAC/B,MAAMyB,WAAW,GAAGhB,EAAE,CAACgB,WAAW,CAAC5B,UAAU,EAAE,UAAU,CAAC;IAC1D,MAAM6B,KAAK,GAAGD,WAAW,CAACE,WAAW,CAAC9B,UAAU,CAAC;IACjD,MAAMI,OAAO,GAAGyB,KAAK,CAACpJ,GAAG,CAACkJ,GAAG,CAAC;IAC9B,OAAO,IAAIxG,OAAO,CAAC,CAACL,OAAO,EAAEP,MAAM,KAAK;MACpC6F,OAAO,CAACG,SAAS,GAAGC,KAAK,IAAI;QACzB,MAAM7D,MAAM,GAAG6D,KAAK,CAACC,MAAM,CAAC9D,MAAM;QAClC,IAAIA,MAAM,EAAE;UACR7B,OAAO,CAAC6B,MAAM,CAACE,KAAK,CAAC;QACzB,CAAC,MACI;UACD/B,OAAO,CAACsH,SAAS,CAAC;QACtB;MACJ,CAAC;MACDR,WAAW,CAAClB,OAAO,GAAGF,KAAK,IAAI;QAC3B,IAAI/E,EAAE;QACNlB,MAAM,CAACe,aAAa,CAACQ,MAAM,CAAC,aAAa,CAAC,iCAAiC;UACvEuC,oBAAoB,EAAE,CAAC5C,EAAE,GAAG+E,KAAK,CAACC,MAAM,CAACzF,KAAK,MAAM,IAAI,IAAIS,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6C;QAC5F,CAAC,CAAC,CAAC;MACP,CAAC;IACL,CAAC,CAAC;EACN,CAAC;EAAA,OAAA6D,KAAA,CAAAhF,KAAA,OAAAC,SAAA;AAAA;AACD,SAAS+D,UAAUA,CAAC3I,GAAG,EAAE;EACrB,OAAO,GAAGA,GAAG,CAACsF,OAAO,CAAC0B,KAAK,IAAIhH,GAAG,CAACwD,IAAI,EAAE;AAC7C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqG,MAAM,GAAG,IAAIvK,MAAM,CAAC,qBAAqB,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA,SAGewK,oBAAoBA,CAAAC,GAAA;EAAA,OAAAC,qBAAA,CAAArF,KAAA,OAAAC,SAAA;AAAA;AAcnC;AACA;AACA;AAFA,SAAAoF,sBAAA;EAAAA,qBAAA,GAAA7H,iBAAA,CAdA,WAAoCnC,GAAG,EAAE;IACrC,IAAIjB,oBAAoB,CAAC,CAAC,EAAE;MACxB,IAAI2H,KAAK,GAAGkD,SAAS;MACrB,IAAI;QACAlD,KAAK,SAAS+B,sBAAsB,CAACzI,GAAG,CAAC;MAC7C,CAAC,CACD,OAAOwI,CAAC,EAAE;QACN;QACAqB,MAAM,CAACI,IAAI,CAAC,+CAA+CzB,CAAC,EAAE,CAAC;MACnE;MACA,OAAO9B,KAAK;IAChB;IACA,OAAOkD,SAAS;EACpB,CAAC;EAAA,OAAAI,qBAAA,CAAArF,KAAA,OAAAC,SAAA;AAAA;AAID,SAASsF,mBAAmBA,CAAClK,GAAG,EAAE0G,KAAK,EAAE;EACrC,IAAI3H,oBAAoB,CAAC,CAAC,EAAE;IACxB,OAAO6J,qBAAqB,CAAC5I,GAAG,EAAE0G,KAAK,CAAC,CAAC7E,KAAK,CAAC2G,CAAC,IAAI;MAChD;MACAqB,MAAM,CAACI,IAAI,CAAC,8CAA8CzB,CAAC,EAAE,CAAC;IAClE,CAAC,CAAC;EACN;EACA,OAAO7F,OAAO,CAACL,OAAO,CAAC,CAAC;AAC5B;AAAC,SACc6H,iCAAiCA,CAAA;EAAA,OAAAC,kCAAA,CAAAzF,KAAA,OAAAC,SAAA;AAAA;AA4BhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAAAwF,mCAAA;EAAAA,kCAAA,GAAAjI,iBAAA,CA5BA,aAAmD;IAC/C;AACJ;AACA;AACA;IACI,IAAIkI,kBAAkB,GAAGT,SAAS;IAClC,IAAI;MACAS,kBAAkB,SAAStB,2BAA2B,CAAC,CAAC;IAC5D,CAAC,CACD,OAAOuB,EAAE,EAAE;MACP;IAAA;IAEJ,IAAI,CAACD,kBAAkB,EAAE;MACrB;MACA,MAAME,QAAQ,GAAGvL,MAAM,CAAC,CAAC;MACzB;MACA;MACA;MACA;MACA;MACA8J,0BAA0B,CAACyB,QAAQ,CAAC,CAAC1I,KAAK,CAAC2G,CAAC,IAAIqB,MAAM,CAACI,IAAI,CAAC,sDAAsDzB,CAAC,EAAE,CAAC,CAAC;MACvH,OAAO+B,QAAQ;IACnB,CAAC,MACI;MACD,OAAOF,kBAAkB;IAC7B;EACJ,CAAC;EAAA,OAAAD,kCAAA,CAAAzF,KAAA,OAAAC,SAAA;AAAA;AAkBD,SAAS4F,WAAWA,CAAA,EAAG;EACnB,MAAMC,UAAU,GAAGlK,aAAa,CAAC,CAAC;EAClC,OAAOkK,UAAU,CAAC3K,OAAO;AAC7B;AAAC,SACc4K,aAAaA,CAAA;EAAA,OAAAC,cAAA,CAAAhG,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA+F,eAAA;EAAAA,cAAA,GAAAxI,iBAAA,CAA5B,aAA+B;IAC3B,MAAM9B,KAAK,GAAGE,aAAa,CAAC,CAAC;IAC7B,IAAIF,KAAK,CAACP,OAAO,IAAIO,KAAK,CAACqG,KAAK,EAAE;MAC9B,OAAOrG,KAAK,CAACqG,KAAK,CAACnE,OAAO;IAC9B,CAAC,MACI;MACD;MACA,MAAMb,KAAK,CAAC;AACpB;AACA,SAAS,CAAC;IACN;EACJ,CAAC;EAAA,OAAAiJ,cAAA,CAAAhG,KAAA,OAAAC,SAAA;AAAA;AACD,SAASgG,mBAAmBA,CAAA,EAAG;EAC3B,MAAMC,OAAO,GAAG5L,SAAS,CAAC,CAAC;EAC3B,MAAMwL,UAAU,GAAGlK,aAAa,CAAC,CAAC;EAClC;EACA;EACAkK,UAAU,CAAC5K,WAAW,GAAG,IAAI;EAC7B,IAAI,OAAOgL,OAAO,CAACC,6BAA6B,KAAK,QAAQ,IACzDD,OAAO,CAACC,6BAA6B,KAAK,IAAI,EAAE;IAChD;EACJ;EACAL,UAAU,CAAC3K,OAAO,GAAG,IAAI;EACzB,MAAMiL,aAAa,GAAG,IAAIlM,QAAQ,CAAC,CAAC;EACpC4L,UAAU,CAAC/D,KAAK,GAAGqE,aAAa;EAChC,IAAI,OAAOF,OAAO,CAACC,6BAA6B,KAAK,QAAQ,EAAE;IAC3DC,aAAa,CAACzI,OAAO,CAACuI,OAAO,CAACC,6BAA6B,CAAC;EAChE,CAAC,MACI;IACDC,aAAa,CAACzI,OAAO,CAAC6H,iCAAiC,CAAC,CAAC,CAAC;EAC9D;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMa,qBAAqB,GAAG;EAAExI,KAAK,EAAE;AAAgB,CAAC;AACxD;AACA;AACA;AACA;AACA;AACA,SAASyI,gBAAgBA,CAACC,cAAc,EAAE;EACtC,OAAOhM,MAAM,CAACiM,YAAY,CAAC3F,IAAI,CAACC,SAAS,CAACyF,cAAc,CAAC,EACzD,cAAe,KAAK,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AAJA,SAKeE,UAAUA,CAAAC,GAAA;EAAA,OAAAC,UAAA,CAAA3G,KAAA,OAAAC,SAAA;AAAA;AAuIzB;AACA;AACA;AACA;AAHA,SAAA0G,WAAA;EAAAA,UAAA,GAAAnJ,iBAAA,CAvIA,WAA0BoJ,QAAQ,EAAEC,YAAY,GAAG,KAAK,EAAE;IACtD,MAAMxL,GAAG,GAAGuL,QAAQ,CAACvL,GAAG;IACxBqD,eAAe,CAACrD,GAAG,CAAC;IACpB,MAAMK,KAAK,GAAGN,iBAAiB,CAACC,GAAG,CAAC;IACpC;AACJ;AACA;IACI,IAAI0G,KAAK,GAAGrG,KAAK,CAACqG,KAAK;IACvB,IAAIlE,KAAK,GAAGoH,SAAS;IACrB;AACJ;AACA;AACA;IACI,IAAIlD,KAAK,IAAI,CAAC+E,OAAO,CAAC/E,KAAK,CAAC,EAAE;MAC1BrG,KAAK,CAACqG,KAAK,GAAGkD,SAAS;MACvBlD,KAAK,GAAGkD,SAAS;IACrB;IACA;AACJ;AACA;IACI,IAAI,CAAClD,KAAK,EAAE;MACR;MACA,MAAMgF,WAAW,SAASrL,KAAK,CAACsL,kBAAkB;MAClD,IAAID,WAAW,EAAE;QACb,IAAID,OAAO,CAACC,WAAW,CAAC,EAAE;UACtBhF,KAAK,GAAGgF,WAAW;QACvB,CAAC,MACI;UACD;UACA,MAAMxB,mBAAmB,CAAClK,GAAG,EAAE4J,SAAS,CAAC;QAC7C;MACJ;IACJ;IACA;IACA,IAAI,CAAC4B,YAAY,IAAI9E,KAAK,IAAI+E,OAAO,CAAC/E,KAAK,CAAC,EAAE;MAC1C,OAAO;QACHA,KAAK,EAAEA,KAAK,CAACA;MACjB,CAAC;IACL;IACA;IACA;IACA;IACA,IAAIkF,mBAAmB,GAAG,KAAK;IAC/B;AACJ;AACA;AACA;AACA;IACI,IAAIpB,WAAW,CAAC,CAAC,EAAE;MACf;MACA,IAAI,CAACnK,KAAK,CAACwL,oBAAoB,EAAE;QAC7BxL,KAAK,CAACwL,oBAAoB,GAAGtH,aAAa,CAAC4C,4BAA4B,CAACnH,GAAG,QAAQ0K,aAAa,CAAC,CAAC,CAAC,EAAEa,QAAQ,CAACxG,wBAAwB,CAAC,CAAC+G,OAAO,CAAC,MAAM;UAClJ;UACAzL,KAAK,CAACwL,oBAAoB,GAAGjC,SAAS;QAC1C,CAAC,CAAC;QACFgC,mBAAmB,GAAG,IAAI;MAC9B;MACA,MAAMG,sBAAsB,SAAS1L,KAAK,CAACwL,oBAAoB;MAC/D;MACA,MAAM3B,mBAAmB,CAAClK,GAAG,EAAE+L,sBAAsB,CAAC;MACtD;MACA1L,KAAK,CAACqG,KAAK,GAAGqF,sBAAsB;MACpC,OAAO;QAAErF,KAAK,EAAEqF,sBAAsB,CAACrF;MAAM,CAAC;IAClD;IACA;AACJ;AACA;AACA;AACA;IACI,IAAI;MACA;MACA,IAAI,CAACrG,KAAK,CAACwL,oBAAoB,EAAE;QAC7B;QACA;QACA;QACAxL,KAAK,CAACwL,oBAAoB,GAAGxL,KAAK,CAAC2L,QAAQ,CAACC,QAAQ,CAAC,CAAC,CAACH,OAAO,CAAC,MAAM;UACjE;UACAzL,KAAK,CAACwL,oBAAoB,GAAGjC,SAAS;QAC1C,CAAC,CAAC;QACFgC,mBAAmB,GAAG,IAAI;MAC9B;MACAlF,KAAK,SAAS3G,iBAAiB,CAACC,GAAG,CAAC,CAAC6L,oBAAoB;IAC7D,CAAC,CACD,OAAOrD,CAAC,EAAE;MACN,IAAIA,CAAC,CAAC0D,IAAI,KAAK,YAAY,WAAW,CAAC,+BAA+B,EAAE;QACpE;QACArC,MAAM,CAACI,IAAI,CAACzB,CAAC,CAAC1C,OAAO,CAAC;MAC1B,CAAC,MACI;QACD;QACA+D,MAAM,CAACrH,KAAK,CAACgG,CAAC,CAAC;MACnB;MACA;MACAhG,KAAK,GAAGgG,CAAC;IACb;IACA,IAAI2D,kBAAkB;IACtB,IAAI,CAACzF,KAAK,EAAE;MACR;MACA;MACAyF,kBAAkB,GAAGC,oBAAoB,CAAC5J,KAAK,CAAC;IACpD,CAAC,MACI,IAAIA,KAAK,EAAE;MACZ,IAAIiJ,OAAO,CAAC/E,KAAK,CAAC,EAAE;QAChB;QACA;QACA;QACA;QACA;QACA;QACA;QACAyF,kBAAkB,GAAG;UACjBzF,KAAK,EAAEA,KAAK,CAACA,KAAK;UAClB2F,aAAa,EAAE7J;QACnB,CAAC;MACL,CAAC,MACI;QACD;QACA;QACA2J,kBAAkB,GAAGC,oBAAoB,CAAC5J,KAAK,CAAC;MACpD;IACJ,CAAC,MACI;MACD2J,kBAAkB,GAAG;QACjBzF,KAAK,EAAEA,KAAK,CAACA;MACjB,CAAC;MACD;MACA;MACArG,KAAK,CAACqG,KAAK,GAAGA,KAAK;MACnB,MAAMwD,mBAAmB,CAAClK,GAAG,EAAE0G,KAAK,CAAC;IACzC;IACA,IAAIkF,mBAAmB,EAAE;MACrBU,oBAAoB,CAACtM,GAAG,EAAEmM,kBAAkB,CAAC;IACjD;IACA,OAAOA,kBAAkB;EAC7B,CAAC;EAAA,OAAAb,UAAA,CAAA3G,KAAA,OAAAC,SAAA;AAAA;AAAA,SAKc2H,oBAAoBA,CAAAC,GAAA;EAAA,OAAAC,oBAAA,CAAA9H,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA6H,qBAAA;EAAAA,oBAAA,GAAAtK,iBAAA,CAAnC,WAAoCoJ,QAAQ,EAAE;IAC1C,MAAMvL,GAAG,GAAGuL,QAAQ,CAACvL,GAAG;IACxBqD,eAAe,CAACrD,GAAG,CAAC;IACpB,MAAM;MAAEgM;IAAS,CAAC,GAAGjM,iBAAiB,CAACC,GAAG,CAAC;IAC3C,IAAIwK,WAAW,CAAC,CAAC,EAAE;MACf,MAAMpD,UAAU,SAASsD,aAAa,CAAC,CAAC;MACxC,MAAM;QAAEhE;MAAM,CAAC,SAASnC,aAAa,CAAC4C,4BAA4B,CAACnH,GAAG,EAAEoH,UAAU,CAAC,EAAEmE,QAAQ,CAACxG,wBAAwB,CAAC;MACvH,OAAO;QAAE2B;MAAM,CAAC;IACpB,CAAC,MACI;MACD;MACA,MAAM;QAAEA;MAAM,CAAC,SAASsF,QAAQ,CAACC,QAAQ,CAAC,CAAC;MAC3C,OAAO;QAAEvF;MAAM,CAAC;IACpB;EACJ,CAAC;EAAA,OAAA+F,oBAAA,CAAA9H,KAAA,OAAAC,SAAA;AAAA;AACD,SAAS8H,gBAAgBA,CAACnB,QAAQ,EAAEoB,IAAI,EAAEC,QAAQ,EAAEC,OAAO,EAAE;EACzD,MAAM;IAAE7M;EAAI,CAAC,GAAGuL,QAAQ;EACxB,MAAMlL,KAAK,GAAGN,iBAAiB,CAACC,GAAG,CAAC;EACpC,MAAM8M,aAAa,GAAG;IAClBC,IAAI,EAAEH,QAAQ;IACdpK,KAAK,EAAEqK,OAAO;IACdF;EACJ,CAAC;EACDtM,KAAK,CAACV,cAAc,GAAG,CAAC,GAAGU,KAAK,CAACV,cAAc,EAAEmN,aAAa,CAAC;EAC/D;EACA;EACA,IAAIzM,KAAK,CAACqG,KAAK,IAAI+E,OAAO,CAACpL,KAAK,CAACqG,KAAK,CAAC,EAAE;IACrC,MAAMsG,UAAU,GAAG3M,KAAK,CAACqG,KAAK;IAC9B/D,OAAO,CAACL,OAAO,CAAC,CAAC,CACZ2K,IAAI,CAAC,MAAM;MACZL,QAAQ,CAAC;QAAElG,KAAK,EAAEsG,UAAU,CAACtG;MAAM,CAAC,CAAC;MACrCwG,kBAAkB,CAAC3B,QAAQ,CAAC;IAChC,CAAC,CAAC,CACG1J,KAAK,CAAC,MAAM;MACb;IAAA,CACH,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI;EACA,KAAKxB,KAAK,CAACsL,kBAAkB,CAACsB,IAAI,CAAC,MAAMC,kBAAkB,CAAC3B,QAAQ,CAAC,CAAC;AAC1E;AACA,SAAS4B,mBAAmBA,CAACnN,GAAG,EAAE4M,QAAQ,EAAE;EACxC,MAAMvM,KAAK,GAAGN,iBAAiB,CAACC,GAAG,CAAC;EACpC,MAAMoN,YAAY,GAAG/M,KAAK,CAACV,cAAc,CAAC0N,MAAM,CAACP,aAAa,IAAIA,aAAa,CAACC,IAAI,KAAKH,QAAQ,CAAC;EAClG,IAAIQ,YAAY,CAACE,MAAM,KAAK,CAAC,IACzBjN,KAAK,CAACkN,cAAc,IACpBlN,KAAK,CAACkN,cAAc,CAACvL,SAAS,CAAC,CAAC,EAAE;IAClC3B,KAAK,CAACkN,cAAc,CAACzL,IAAI,CAAC,CAAC;EAC/B;EACAzB,KAAK,CAACV,cAAc,GAAGyN,YAAY;AACvC;AACA;AACA;AACA;AACA,SAASF,kBAAkBA,CAAC3B,QAAQ,EAAE;EAClC,MAAM;IAAEvL;EAAI,CAAC,GAAGuL,QAAQ;EACxB,MAAMlL,KAAK,GAAGN,iBAAiB,CAACC,GAAG,CAAC;EACpC;EACA;EACA,IAAIwN,SAAS,GAAGnN,KAAK,CAACkN,cAAc;EACpC,IAAI,CAACC,SAAS,EAAE;IACZA,SAAS,GAAGC,oBAAoB,CAAClC,QAAQ,CAAC;IAC1ClL,KAAK,CAACkN,cAAc,GAAGC,SAAS;EACpC;EACA,IAAI,CAACA,SAAS,CAACxL,SAAS,CAAC,CAAC,IAAI3B,KAAK,CAACqN,yBAAyB,EAAE;IAC3DF,SAAS,CAAC7L,KAAK,CAAC,CAAC;EACrB;AACJ;AACA,SAAS8L,oBAAoBA,CAAClC,QAAQ,EAAE;EACpC,MAAM;IAAEvL;EAAI,CAAC,GAAGuL,QAAQ;EACxB,OAAO,IAAItK,SAAS;EAAA;EACpB;EACA;EAAAkB,iBAAA,CACA,aAAY;IACR,MAAM9B,KAAK,GAAGN,iBAAiB,CAACC,GAAG,CAAC;IACpC;IACA;IACA,IAAImE,MAAM;IACV,IAAI,CAAC9D,KAAK,CAACqG,KAAK,EAAE;MACdvC,MAAM,SAASiH,UAAU,CAACG,QAAQ,CAAC;IACvC,CAAC,MACI;MACDpH,MAAM,SAASiH,UAAU,CAACG,QAAQ,EAAE,IAAI,CAAC;IAC7C;IACA;AACR;AACA;AACA;IACQ,IAAIpH,MAAM,CAAC3B,KAAK,EAAE;MACd,MAAM2B,MAAM,CAAC3B,KAAK;IACtB;IACA;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI2B,MAAM,CAACkI,aAAa,EAAE;MACtB,MAAMlI,MAAM,CAACkI,aAAa;IAC9B;EACJ,CAAC,GAAE,MAAM;IACL,OAAO,IAAI;EACf,CAAC,EAAE,MAAM;IACL,MAAMhM,KAAK,GAAGN,iBAAiB,CAACC,GAAG,CAAC;IACpC,IAAIK,KAAK,CAACqG,KAAK,EAAE;MACb;MACA,IAAIiH,qBAAqB,GAAGtN,KAAK,CAACqG,KAAK,CAACE,kBAAkB,GACtD,CAACvG,KAAK,CAACqG,KAAK,CAACC,gBAAgB,GAAGtG,KAAK,CAACqG,KAAK,CAACE,kBAAkB,IAC1D,GAAG,GACP,CAAC,GAAG,EAAE,GAAG,IAAI;MACjB;MACA,MAAMgH,sBAAsB,GAAGvN,KAAK,CAACqG,KAAK,CAACC,gBAAgB,GAAG,CAAC,GAAG,EAAE,GAAG,IAAI;MAC3EgH,qBAAqB,GAAG/J,IAAI,CAACiK,GAAG,CAACF,qBAAqB,EAAEC,sBAAsB,CAAC;MAC/E,OAAOhK,IAAI,CAACkK,GAAG,CAAC,CAAC,EAAEH,qBAAqB,GAAGlH,IAAI,CAACD,GAAG,CAAC,CAAC,CAAC;IAC1D,CAAC,MACI;MACD,OAAO,CAAC;IACZ;EACJ,CAAC,EAAE5F,kBAAkB,CAACE,gBAAgB,EAAEF,kBAAkB,CAACG,gBAAgB,CAAC;AAChF;AACA,SAASuL,oBAAoBA,CAACtM,GAAG,EAAE0G,KAAK,EAAE;EACtC,MAAMqH,SAAS,GAAGhO,iBAAiB,CAACC,GAAG,CAAC,CAACL,cAAc;EACvD,KAAK,MAAMqO,QAAQ,IAAID,SAAS,EAAE;IAC9B,IAAI;MACA,IAAIC,QAAQ,CAACrB,IAAI,KAAK,UAAU,CAAC,+BAA+BjG,KAAK,CAAClE,KAAK,IAAI,IAAI,EAAE;QACjF;QACA;QACA;QACAwL,QAAQ,CAACxL,KAAK,CAACkE,KAAK,CAAClE,KAAK,CAAC;MAC/B,CAAC,MACI;QACD;QACA;QACA;QACAwL,QAAQ,CAACjB,IAAI,CAACrG,KAAK,CAAC;MACxB;IACJ,CAAC,CACD,OAAO8B,CAAC,EAAE;MACN;IAAA;EAER;AACJ;AACA,SAASiD,OAAOA,CAAC/E,KAAK,EAAE;EACpB,OAAOA,KAAK,CAACC,gBAAgB,GAAGF,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG,CAAC;AAClD;AACA,SAAS4F,oBAAoBA,CAAC5J,KAAK,EAAE;EACjC,OAAO;IACHkE,KAAK,EAAEuE,gBAAgB,CAACD,qBAAqB,CAAC;IAC9CxI;EACJ,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyL,eAAe,CAAC;EAClB/M,WAAWA,CAAClB,GAAG,EAAE+E,wBAAwB,EAAE;IACvC,IAAI,CAAC/E,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC+E,wBAAwB,GAAGA,wBAAwB;EAC5D;EACAmJ,OAAOA,CAAA,EAAG;IACN,MAAM;MAAEvO;IAAe,CAAC,GAAGI,iBAAiB,CAAC,IAAI,CAACC,GAAG,CAAC;IACtD,KAAK,MAAM8M,aAAa,IAAInN,cAAc,EAAE;MACxCwN,mBAAmB,CAAC,IAAI,CAACnN,GAAG,EAAE8M,aAAa,CAACC,IAAI,CAAC;IACrD;IACA,OAAOpK,OAAO,CAACL,OAAO,CAAC,CAAC;EAC5B;AACJ;AACA,SAAS6L,OAAOA,CAACnO,GAAG,EAAE+E,wBAAwB,EAAE;EAC5C,OAAO,IAAIkJ,eAAe,CAACjO,GAAG,EAAE+E,wBAAwB,CAAC;AAC7D;AACA,SAASqJ,eAAeA,CAAC7C,QAAQ,EAAE;EAC/B,OAAO;IACHU,QAAQ,EAAET,YAAY,IAAIJ,UAAU,CAACG,QAAQ,EAAEC,YAAY,CAAC;IAC5D6C,kBAAkB,EAAEA,CAAA,KAAM9B,oBAAoB,CAAChB,QAAQ,CAAC;IACxDmB,gBAAgB,EAAEE,QAAQ,IAAIF,gBAAgB,CAACnB,QAAQ,EAAE,UAAU,CAAC,6BAA6BqB,QAAQ,CAAC;IAC1GO,mBAAmB,EAAEP,QAAQ,IAAIO,mBAAmB,CAAC5B,QAAQ,CAACvL,GAAG,EAAE4M,QAAQ;EAC/E,CAAC;AACL;AAEA,MAAMpJ,IAAI,GAAG,qBAAqB;AAClC,MAAM8K,OAAO,GAAG,OAAO;;AAEvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,yCAAyC;AAC/D,MAAMC,wBAAwB,GAAG,gDAAgD;AACjF,SAASC,YAAYA,CAACzO,GAAG,EAAE0O,OAAO,EAAE;EAChC,MAAM7O,WAAW,GAAG,IAAIhB,QAAQ,CAAC,CAAC;EAClC,MAAMwB,KAAK,GAAGN,iBAAiB,CAACC,GAAG,CAAC;EACpCK,KAAK,CAACsO,cAAc,GAAG;IAAE9O;EAAY,CAAC;EACtC,MAAM+O,KAAK,GAAGC,OAAO,CAAC7O,GAAG,CAAC;EAC1B,MAAMmD,UAAU,GAAGJ,YAAY,CAAC,KAAK,CAAC;EACtC,IAAI,CAACI,UAAU,EAAE;IACb2L,qBAAqB,CAAC,MAAM;MACxB,MAAM3L,UAAU,GAAGJ,YAAY,CAAC,KAAK,CAAC;MACtC,IAAI,CAACI,UAAU,EAAE;QACb;QACA,MAAM,IAAIzB,KAAK,CAAC,cAAc,CAAC;MACnC;MACAqN,iBAAiB,CAAC/O,GAAG,EAAE0O,OAAO,EAAEvL,UAAU,EAAEyL,KAAK,EAAE/O,WAAW,CAAC;IACnE,CAAC,CAAC;EACN,CAAC,MACI;IACDkP,iBAAiB,CAAC/O,GAAG,EAAE0O,OAAO,EAAEvL,UAAU,EAAEyL,KAAK,EAAE/O,WAAW,CAAC;EACnE;EACA,OAAOA,WAAW,CAAC0C,OAAO;AAC9B;AACA,SAASyM,oBAAoBA,CAAChP,GAAG,EAAE0O,OAAO,EAAE;EACxC,MAAM7O,WAAW,GAAG,IAAIhB,QAAQ,CAAC,CAAC;EAClC,MAAMwB,KAAK,GAAGN,iBAAiB,CAACC,GAAG,CAAC;EACpCK,KAAK,CAACsO,cAAc,GAAG;IAAE9O;EAAY,CAAC;EACtC,MAAM+O,KAAK,GAAGC,OAAO,CAAC7O,GAAG,CAAC;EAC1B,MAAMmD,UAAU,GAAGJ,YAAY,CAAC,IAAI,CAAC;EACrC,IAAI,CAACI,UAAU,EAAE;IACb8L,6BAA6B,CAAC,MAAM;MAChC,MAAM9L,UAAU,GAAGJ,YAAY,CAAC,IAAI,CAAC;MACrC,IAAI,CAACI,UAAU,EAAE;QACb;QACA,MAAM,IAAIzB,KAAK,CAAC,cAAc,CAAC;MACnC;MACAqN,iBAAiB,CAAC/O,GAAG,EAAE0O,OAAO,EAAEvL,UAAU,EAAEyL,KAAK,EAAE/O,WAAW,CAAC;IACnE,CAAC,CAAC;EACN,CAAC,MACI;IACDkP,iBAAiB,CAAC/O,GAAG,EAAE0O,OAAO,EAAEvL,UAAU,EAAEyL,KAAK,EAAE/O,WAAW,CAAC;EACnE;EACA,OAAOA,WAAW,CAAC0C,OAAO;AAC9B;AACA;AACA;AACA;AACA;AACA,SAASwM,iBAAiBA,CAAC/O,GAAG,EAAE0O,OAAO,EAAEvL,UAAU,EAAE+L,SAAS,EAAErP,WAAW,EAAE;EACzEsD,UAAU,CAACgM,KAAK,CAAC,MAAM;IACnB;IACA;IACAC,qBAAqB,CAACpP,GAAG,EAAE0O,OAAO,EAAEvL,UAAU,EAAE+L,SAAS,CAAC;IAC1DrP,WAAW,CAACyC,OAAO,CAACa,UAAU,CAAC;EACnC,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA,SAAS0L,OAAOA,CAAC7O,GAAG,EAAE;EAClB,MAAM4O,KAAK,GAAG,kBAAkB5O,GAAG,CAACwD,IAAI,EAAE;EAC1C,MAAM6L,YAAY,GAAGC,QAAQ,CAACC,aAAa,CAAC,KAAK,CAAC;EAClDF,YAAY,CAACG,EAAE,GAAGZ,KAAK;EACvBS,YAAY,CAACI,KAAK,CAACC,OAAO,GAAG,MAAM;EACnCJ,QAAQ,CAACxK,IAAI,CAAC6K,WAAW,CAACN,YAAY,CAAC;EACvC,OAAOT,KAAK;AAChB;AAAC,SACcgB,UAAUA,CAAAC,GAAA;EAAA,OAAAC,WAAA,CAAAnL,KAAA,OAAAC,SAAA;AAAA;AAiBzB;AACA;AACA;AACA;AACA;AAJA,SAAAkL,YAAA;EAAAA,WAAA,GAAA3N,iBAAA,CAjBA,WAA0BnC,GAAG,EAAE;IAC3BqD,eAAe,CAACrD,GAAG,CAAC;IACpB;IACA,MAAM2O,cAAc,GAAG5O,iBAAiB,CAACC,GAAG,CAAC,CAAC2O,cAAc;IAC5D,MAAMoB,SAAS,SAASpB,cAAc,CAAC9O,WAAW,CAAC0C,OAAO;IAC1D,OAAO,IAAII,OAAO,CAAC,CAACL,OAAO,EAAE0N,OAAO,KAAK;MACrC;MACA,MAAMrB,cAAc,GAAG5O,iBAAiB,CAACC,GAAG,CAAC,CAAC2O,cAAc;MAC5DoB,SAAS,CAACZ,KAAK,CAAC,MAAM;QAClB7M,OAAO;QACP;QACAyN,SAAS,CAACE,OAAO,CAACtB,cAAc,CAACuB,QAAQ,EAAE;UACvCC,MAAM,EAAE;QACZ,CAAC,CAAC,CAAC;MACP,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EAAA,OAAAL,WAAA,CAAAnL,KAAA,OAAAC,SAAA;AAAA;AAMD,SAASwK,qBAAqBA,CAACpP,GAAG,EAAE0O,OAAO,EAAEvL,UAAU,EAAE+L,SAAS,EAAE;EAChE,MAAMgB,QAAQ,GAAG/M,UAAU,CAACiN,MAAM,CAAClB,SAAS,EAAE;IAC1CmB,OAAO,EAAE3B,OAAO;IAChB4B,IAAI,EAAE,WAAW;IACjB;IACAC,QAAQ,EAAEA,CAAA,KAAM;MACZxQ,iBAAiB,CAACC,GAAG,CAAC,CAAC2O,cAAc,CAAC6B,SAAS,GAAG,IAAI;IAC1D,CAAC;IACD;IACA,gBAAgB,EAAEC,CAAA,KAAM;MACpB1Q,iBAAiB,CAACC,GAAG,CAAC,CAAC2O,cAAc,CAAC6B,SAAS,GAAG,KAAK;IAC3D;EACJ,CAAC,CAAC;EACF,MAAMnQ,KAAK,GAAGN,iBAAiB,CAACC,GAAG,CAAC;EACpCK,KAAK,CAACsO,cAAc,GAAGzO,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEE,KAAK,CAACsO,cAAc,CAAC,EAAE;IAAE;IAC5EuB;EAAS,CAAC,CAAC;AACnB;AACA,SAASpB,qBAAqBA,CAAC4B,MAAM,EAAE;EACnC,MAAMC,MAAM,GAAGrB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/CoB,MAAM,CAACC,GAAG,GAAGrC,aAAa;EAC1BoC,MAAM,CAACD,MAAM,GAAGA,MAAM;EACtBpB,QAAQ,CAACuB,IAAI,CAAClB,WAAW,CAACgB,MAAM,CAAC;AACrC;AACA,SAAS1B,6BAA6BA,CAACyB,MAAM,EAAE;EAC3C,MAAMC,MAAM,GAAGrB,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/CoB,MAAM,CAACC,GAAG,GAAGpC,wBAAwB;EACrCmC,MAAM,CAACD,MAAM,GAAGA,MAAM;EACtBpB,QAAQ,CAACuB,IAAI,CAAClB,WAAW,CAACgB,MAAM,CAAC;AACrC;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,mBAAmB,CAAC;EACtB;AACJ;AACA;AACA;EACI5P,WAAWA,CAAC6P,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB;AACR;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;EAC7B;EACA;AACJ;AACA;AACA;EACU/E,QAAQA,CAAA,EAAG;IAAA,IAAAgF,MAAA;IAAA,OAAA9O,iBAAA;MACb,IAAIc,EAAE,EAAEiO,EAAE,EAAEC,EAAE;MACdC,gBAAgB,CAACH,MAAI,CAACD,aAAa,CAAC;MACpC;MACA;MACA,MAAMK,mBAAmB,SAASzB,UAAU,CAACqB,MAAI,CAACK,IAAI,CAAC,CAACzP,KAAK,CAACyI,EAAE,IAAI;QAChE;QACA,MAAMxH,aAAa,CAACQ,MAAM,CAAC,iBAAiB,CAAC,mCAAmC,CAAC;MACrF,CAAC,CAAC;MACF;MACA,IAAI,EAAE,CAACL,EAAE,GAAGlD,iBAAiB,CAACkR,MAAI,CAACK,IAAI,CAAC,CAAC3C,cAAc,MAAM,IAAI,IAAI1L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuN,SAAS,CAAC,EAAE;QACzG,MAAM1N,aAAa,CAACQ,MAAM,CAAC,iBAAiB,CAAC,mCAAmC,CAAC;MACrF;MACA,IAAIa,MAAM;MACV,IAAI;QACAA,MAAM,SAASI,aAAa,CAACsC,kCAAkC,CAACoK,MAAI,CAACK,IAAI,EAAED,mBAAmB,CAAC,EAAEJ,MAAI,CAACM,yBAAyB,CAAC;MACpI,CAAC,CACD,OAAO/I,CAAC,EAAE;QACN,IAAI,CAAC0I,EAAE,GAAG1I,CAAC,CAAC0D,IAAI,MAAM,IAAI,IAAIgF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,QAAQ,CAAC,oBAAoB,CAAC,sCAAsC,CAAC,EAAE;UAC7HP,MAAI,CAACD,aAAa,GAAGS,UAAU,CAACnL,MAAM,CAAC,CAAC6K,EAAE,GAAG3I,CAAC,CAACkJ,UAAU,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACnL,UAAU,CAAC,EAAEiL,MAAI,CAACD,aAAa,CAAC;UACnI,MAAMlO,aAAa,CAACQ,MAAM,CAAC,WAAW,CAAC,+BAA+B;YAClEqO,IAAI,EAAElO,iBAAiB,CAACwN,MAAI,CAACD,aAAa,CAACY,kBAAkB,GAAGnL,IAAI,CAACD,GAAG,CAAC,CAAC,CAAC;YAC3ER,UAAU,EAAEiL,MAAI,CAACD,aAAa,CAAChL;UACnC,CAAC,CAAC;QACN,CAAC,MACI;UACD,MAAMwC,CAAC;QACX;MACJ;MACA;MACAyI,MAAI,CAACD,aAAa,GAAG,IAAI;MACzB,OAAO7M,MAAM;IAAC;EAClB;EACA;AACJ;AACA;EACI0N,UAAUA,CAAC7R,GAAG,EAAE;IACZ,IAAI,CAACsR,IAAI,GAAGtR,GAAG;IACf,IAAI,CAACuR,yBAAyB,GAAG/S,YAAY,CAACwB,GAAG,EAAE,WAAW,CAAC;IAC/DyO,YAAY,CAACzO,GAAG,EAAE,IAAI,CAAC+Q,QAAQ,CAAC,CAAClP,KAAK,CAAC,MAAM;MACzC;IAAA,CACH,CAAC;EACN;EACA;AACJ;AACA;EACIiQ,OAAOA,CAACC,aAAa,EAAE;IACnB,IAAIA,aAAa,YAAYjB,mBAAmB,EAAE;MAC9C,OAAO,IAAI,CAACC,QAAQ,KAAKgB,aAAa,CAAChB,QAAQ;IACnD,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiB,2BAA2B,CAAC;EAC9B;AACJ;AACA;AACA;EACI9Q,WAAWA,CAAC6P,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB;AACR;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,IAAI;EAC7B;EACA;AACJ;AACA;AACA;EACU/E,QAAQA,CAAA,EAAG;IAAA,IAAAgG,MAAA;IAAA,OAAA9P,iBAAA;MACb,IAAIc,EAAE,EAAEiO,EAAE,EAAEC,EAAE;MACdC,gBAAgB,CAACa,MAAI,CAACjB,aAAa,CAAC;MACpC;MACA;MACA,MAAMK,mBAAmB,SAASzB,UAAU,CAACqC,MAAI,CAACX,IAAI,CAAC,CAACzP,KAAK,CAACyI,EAAE,IAAI;QAChE;QACA,MAAMxH,aAAa,CAACQ,MAAM,CAAC,iBAAiB,CAAC,mCAAmC,CAAC;MACrF,CAAC,CAAC;MACF;MACA,IAAI,EAAE,CAACL,EAAE,GAAGlD,iBAAiB,CAACkS,MAAI,CAACX,IAAI,CAAC,CAAC3C,cAAc,MAAM,IAAI,IAAI1L,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACuN,SAAS,CAAC,EAAE;QACzG,MAAM1N,aAAa,CAACQ,MAAM,CAAC,iBAAiB,CAAC,mCAAmC,CAAC;MACrF;MACA,IAAIa,MAAM;MACV,IAAI;QACAA,MAAM,SAASI,aAAa,CAAC2C,0CAA0C,CAAC+K,MAAI,CAACX,IAAI,EAAED,mBAAmB,CAAC,EAAEY,MAAI,CAACV,yBAAyB,CAAC;MAC5I,CAAC,CACD,OAAO/I,CAAC,EAAE;QACN,IAAI,CAAC0I,EAAE,GAAG1I,CAAC,CAAC0D,IAAI,MAAM,IAAI,IAAIgF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,QAAQ,CAAC,oBAAoB,CAAC,sCAAsC,CAAC,EAAE;UAC7HS,MAAI,CAACjB,aAAa,GAAGS,UAAU,CAACnL,MAAM,CAAC,CAAC6K,EAAE,GAAG3I,CAAC,CAACkJ,UAAU,MAAM,IAAI,IAAIP,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACnL,UAAU,CAAC,EAAEiM,MAAI,CAACjB,aAAa,CAAC;UACnI,MAAMlO,aAAa,CAACQ,MAAM,CAAC,WAAW,CAAC,+BAA+B;YAClEqO,IAAI,EAAElO,iBAAiB,CAACwO,MAAI,CAACjB,aAAa,CAACY,kBAAkB,GAAGnL,IAAI,CAACD,GAAG,CAAC,CAAC,CAAC;YAC3ER,UAAU,EAAEiM,MAAI,CAACjB,aAAa,CAAChL;UACnC,CAAC,CAAC;QACN,CAAC,MACI;UACD,MAAMwC,CAAC;QACX;MACJ;MACA;MACAyJ,MAAI,CAACjB,aAAa,GAAG,IAAI;MACzB,OAAO7M,MAAM;IAAC;EAClB;EACA;AACJ;AACA;EACI0N,UAAUA,CAAC7R,GAAG,EAAE;IACZ,IAAI,CAACsR,IAAI,GAAGtR,GAAG;IACf,IAAI,CAACuR,yBAAyB,GAAG/S,YAAY,CAACwB,GAAG,EAAE,WAAW,CAAC;IAC/DgP,oBAAoB,CAAChP,GAAG,EAAE,IAAI,CAAC+Q,QAAQ,CAAC,CAAClP,KAAK,CAAC,MAAM;MACjD;IAAA,CACH,CAAC;EACN;EACA;AACJ;AACA;EACIiQ,OAAOA,CAACC,aAAa,EAAE;IACnB,IAAIA,aAAa,YAAYC,2BAA2B,EAAE;MACtD,OAAO,IAAI,CAACjB,QAAQ,KAAKgB,aAAa,CAAChB,QAAQ;IACnD,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMmB,cAAc,CAAC;EACjBhR,WAAWA,CAACiR,sBAAsB,EAAE;IAChC,IAAI,CAACA,sBAAsB,GAAGA,sBAAsB;EACxD;EACA;AACJ;AACA;EACUlG,QAAQA,CAAA,EAAG;IAAA,IAAAmG,MAAA;IAAA,OAAAjQ,iBAAA;MACb;MACA,MAAMkQ,WAAW,SAASD,MAAI,CAACD,sBAAsB,CAAClG,QAAQ,CAAC,CAAC;MAChE;MACA;MACA,MAAMqG,mBAAmB,GAAGnT,YAAY,CAACkT,WAAW,CAAC3L,KAAK,CAAC;MAC3D;MACA;MACA,MAAME,kBAAkB,GAAG0L,mBAAmB,KAAK,IAAI,IACnDA,mBAAmB,GAAG7L,IAAI,CAACD,GAAG,CAAC,CAAC,IAChC8L,mBAAmB,GAAG,CAAC,GACrBA,mBAAmB,GAAG,IAAI,GAC1B7L,IAAI,CAACD,GAAG,CAAC,CAAC;MAChB,OAAOtG,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEkS,WAAW,CAAC,EAAE;QAAEzL;MAAmB,CAAC,CAAC;IAAC;EACjF;EACA;AACJ;AACA;EACIiL,UAAUA,CAAC7R,GAAG,EAAE;IACZ,IAAI,CAACsR,IAAI,GAAGtR,GAAG;EACnB;EACA;AACJ;AACA;EACI8R,OAAOA,CAACC,aAAa,EAAE;IACnB,IAAIA,aAAa,YAAYG,cAAc,EAAE;MACzC,OAAQ,IAAI,CAACC,sBAAsB,CAAClG,QAAQ,CAAC3H,QAAQ,CAAC,CAAC,KACnDyN,aAAa,CAACI,sBAAsB,CAAClG,QAAQ,CAAC3H,QAAQ,CAAC,CAAC;IAChE,CAAC,MACI;MACD,OAAO,KAAK;IAChB;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmN,UAAUA,CAACzL,UAAU,EAAEuM,YAAY,EAAE;EAC1C;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI,IAAIvM,UAAU,KAAK,GAAG,IAAIA,UAAU,KAAK,GAAG,EAAE;IAC1C,OAAO;MACHwM,YAAY,EAAE,CAAC;MACfZ,kBAAkB,EAAEnL,IAAI,CAACD,GAAG,CAAC,CAAC,GAAGxF,OAAO;MACxCgF;IACJ,CAAC;EACL,CAAC,MACI;IACD;AACR;AACA;AACA;IACQ,MAAMwM,YAAY,GAAGD,YAAY,GAAGA,YAAY,CAACC,YAAY,GAAG,CAAC;IACjE,MAAMC,aAAa,GAAGrT,sBAAsB,CAACoT,YAAY,EAAE,IAAI,EAAE,CAAC,CAAC;IACnE,OAAO;MACHA,YAAY,EAAEA,YAAY,GAAG,CAAC;MAC9BZ,kBAAkB,EAAEnL,IAAI,CAACD,GAAG,CAAC,CAAC,GAAGiM,aAAa;MAC9CzM;IACJ,CAAC;EACL;AACJ;AACA,SAASoL,gBAAgBA,CAACmB,YAAY,EAAE;EACpC,IAAIA,YAAY,EAAE;IACd,IAAI9L,IAAI,CAACD,GAAG,CAAC,CAAC,GAAG+L,YAAY,CAACX,kBAAkB,IAAI,CAAC,EAAE;MACnD;MACA,MAAM9O,aAAa,CAACQ,MAAM,CAAC,WAAW,CAAC,+BAA+B;QAClEqO,IAAI,EAAElO,iBAAiB,CAAC8O,YAAY,CAACX,kBAAkB,GAAGnL,IAAI,CAACD,GAAG,CAAC,CAAC,CAAC;QACrER,UAAU,EAAEuM,YAAY,CAACvM;MAC7B,CAAC,CAAC;IACN;EACJ;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0M,kBAAkBA,CAAC1S,GAAG,GAAGvB,MAAM,CAAC,CAAC,EAAE6G,OAAO,EAAE;EACjDtF,GAAG,GAAGX,kBAAkB,CAACW,GAAG,CAAC;EAC7B,MAAMgM,QAAQ,GAAGxN,YAAY,CAACwB,GAAG,EAAE,WAAW,CAAC;EAC/C;EACA,IAAI,CAACO,aAAa,CAAC,CAAC,CAACV,WAAW,EAAE;IAC9B+K,mBAAmB,CAAC,CAAC;EACzB;EACA;EACA;EACA,IAAIJ,WAAW,CAAC,CAAC,EAAE;IACf;IACA,KAAKE,aAAa,CAAC,CAAC,CAACuC,IAAI,CAACvG,KAAK;IAC/B;IACAiM,OAAO,CAACC,GAAG,CAAC,0BAA0BlM,KAAK,oGAAoG,CAAC,CAAC;EACrJ;EACA,IAAIsF,QAAQ,CAAC6G,aAAa,CAAC,CAAC,EAAE;IAC1B,MAAMC,gBAAgB,GAAG9G,QAAQ,CAAC9G,YAAY,CAAC,CAAC;IAChD,MAAM6N,cAAc,GAAG/G,QAAQ,CAACgH,UAAU,CAAC,CAAC;IAC5C,IAAID,cAAc,CAACrF,yBAAyB,KACxCpI,OAAO,CAACoI,yBAAyB,IACjCqF,cAAc,CAAC/G,QAAQ,CAAC8F,OAAO,CAACxM,OAAO,CAAC0G,QAAQ,CAAC,EAAE;MACnD,OAAO8G,gBAAgB;IAC3B,CAAC,MACI;MACD,MAAMhQ,aAAa,CAACQ,MAAM,CAAC,qBAAqB,CAAC,yCAAyC;QACtFC,OAAO,EAAEvD,GAAG,CAACwD;MACjB,CAAC,CAAC;IACN;EACJ;EACA,MAAM+H,QAAQ,GAAGS,QAAQ,CAAC6F,UAAU,CAAC;IAAEvM;EAAQ,CAAC,CAAC;EACjD2N,SAAS,CAACjT,GAAG,EAAEsF,OAAO,CAAC0G,QAAQ,EAAE1G,OAAO,CAACoI,yBAAyB,CAAC;EACnE;EACA;EACA;EACA,IAAI3N,iBAAiB,CAACC,GAAG,CAAC,CAAC0N,yBAAyB,EAAE;IAClD;IACA;IACA;IACA;IACA;IACAhB,gBAAgB,CAACnB,QAAQ,EAAE,UAAU,CAAC,6BAA6B,MAAM,CAAE,CAAC,CAAC;EACjF;EACA,OAAOA,QAAQ;AACnB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS0H,SAASA,CAACjT,GAAG,EAAEgM,QAAQ,EAAE0B,yBAAyB,EAAE;EACzD;EACA;EACA,MAAMrN,KAAK,GAAGD,eAAe,CAACJ,GAAG,EAAEE,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEV,aAAa,CAAC,CAAC;EACpEY,KAAK,CAACX,SAAS,GAAG,IAAI;EACtBW,KAAK,CAAC2L,QAAQ,GAAGA,QAAQ,CAAC,CAAC;EAC3B3L,KAAK,CAACsL,kBAAkB,GAAG7B,oBAAoB,CAAC9J,GAAG,CAAC,CAACiN,IAAI,CAACvB,WAAW,IAAI;IACrE,IAAIA,WAAW,IAAID,OAAO,CAACC,WAAW,CAAC,EAAE;MACrCrL,KAAK,CAACqG,KAAK,GAAGgF,WAAW;MACzB;MACAY,oBAAoB,CAACtM,GAAG,EAAE;QAAE0G,KAAK,EAAEgF,WAAW,CAAChF;MAAM,CAAC,CAAC;IAC3D;IACA,OAAOgF,WAAW;EACtB,CAAC,CAAC;EACF;EACA;EACA;EACArL,KAAK,CAACqN,yBAAyB,GAC3BA,yBAAyB,KAAK9D,SAAS,GACjC5J,GAAG,CAACkT,8BAA8B,GAClCxF,yBAAyB;EACnCrN,KAAK,CAAC2L,QAAQ,CAAC6F,UAAU,CAAC7R,GAAG,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASmT,0BAA0BA,CAACC,gBAAgB,EAAE1F,yBAAyB,EAAE;EAC7E,MAAM1N,GAAG,GAAGoT,gBAAgB,CAACpT,GAAG;EAChC,MAAMK,KAAK,GAAGN,iBAAiB,CAACC,GAAG,CAAC;EACpC;EACA;EACA,IAAIK,KAAK,CAACkN,cAAc,EAAE;IACtB,IAAIG,yBAAyB,KAAK,IAAI,EAAE;MACpCrN,KAAK,CAACkN,cAAc,CAAC5L,KAAK,CAAC,CAAC;IAChC,CAAC,MACI;MACDtB,KAAK,CAACkN,cAAc,CAACzL,IAAI,CAAC,CAAC;IAC/B;EACJ;EACAzB,KAAK,CAACqN,yBAAyB,GAAGA,yBAAyB;AAC/D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAUezB,QAAQA,CAAAoH,GAAA,EAAAC,GAAA;EAAA,OAAAC,SAAA,CAAA5O,KAAA,OAAAC,SAAA;AAAA;AAOvB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAdA,SAAA2O,UAAA;EAAAA,SAAA,GAAApR,iBAAA,CAPA,WAAwBiR,gBAAgB,EAAE5H,YAAY,EAAE;IACpD,MAAMrH,MAAM,SAASiH,UAAU,CAACgI,gBAAgB,EAAE5H,YAAY,CAAC;IAC/D,IAAIrH,MAAM,CAAC3B,KAAK,EAAE;MACd,MAAM2B,MAAM,CAAC3B,KAAK;IACtB;IACA,OAAO;MAAEkE,KAAK,EAAEvC,MAAM,CAACuC;IAAM,CAAC;EAClC,CAAC;EAAA,OAAA6M,SAAA,CAAA5O,KAAA,OAAAC,SAAA;AAAA;AAgBD,SAASyJ,kBAAkBA,CAAC+E,gBAAgB,EAAE;EAC1C,OAAO7G,oBAAoB,CAAC6G,gBAAgB,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA,SAASI,cAAcA,CAACJ,gBAAgB,EAAEK,gBAAgB,EAAE5G,OAAO;AACnE;AACA;AACA;AACA;AACA;AACA;AACA;AACA6G,YAAY,EAAE;EACV,IAAIC,MAAM,GAAGA,CAAA,KAAM,CAAE,CAAC;EACtB,IAAIC,OAAO,GAAGA,CAAA,KAAM,CAAE,CAAC;EACvB,IAAIH,gBAAgB,CAAC1G,IAAI,IAAI,IAAI,EAAE;IAC/B4G,MAAM,GAAGF,gBAAgB,CAAC1G,IAAI,CAAC8G,IAAI,CAACJ,gBAAgB,CAAC;EACzD,CAAC,MACI;IACDE,MAAM,GAAGF,gBAAgB;EAC7B;EACA,IAAIA,gBAAgB,CAACjR,KAAK,IAAI,IAAI,EAAE;IAChCoR,OAAO,GAAGH,gBAAgB,CAACjR,KAAK,CAACqR,IAAI,CAACJ,gBAAgB,CAAC;EAC3D,CAAC,MACI,IAAI5G,OAAO,EAAE;IACd+G,OAAO,GAAG/G,OAAO;EACrB;EACAH,gBAAgB,CAAC0G,gBAAgB,EAAE,UAAU,CAAC,6BAA6BO,MAAM,EAAEC,OAAO,CAAC;EAC3F,OAAO,MAAMzG,mBAAmB,CAACiG,gBAAgB,CAACpT,GAAG,EAAE2T,MAAM,CAAC;AAClE;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,cAAc,GAAG,WAAW;AAClC,MAAMC,uBAAuB,GAAG,oBAAoB;AACpD,SAASC,gBAAgBA,CAAA,EAAG;EACxB;EACAtV,kBAAkB,CAAC,IAAIE,SAAS,CAACkV,cAAc,EAAE5E,SAAS,IAAI;IAC1D;IACA,MAAMlP,GAAG,GAAGkP,SAAS,CAAC+E,WAAW,CAAC,KAAK,CAAC,CAAC/O,YAAY,CAAC,CAAC;IACvD,MAAMH,wBAAwB,GAAGmK,SAAS,CAAC+E,WAAW,CAAC,WAAW,CAAC;IACnE,OAAO9F,OAAO,CAACnO,GAAG,EAAE+E,wBAAwB,CAAC;EACjD,CAAC,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAClCmP,oBAAoB,CAAC,UAAU,CAAC,gCAAgC;EACjE;AACR;AACA;AACA,KAHQ,CAICC,0BAA0B,CAAC,CAACjF,SAAS,EAAEkF,WAAW,EAAEC,gBAAgB,KAAK;IAC1EnF,SAAS,CAAC+E,WAAW,CAACF,uBAAuB,CAAC,CAAClC,UAAU,CAAC,CAAC;EAC/D,CAAC,CAAC,CAAC;EACH;EACAnT,kBAAkB,CAAC,IAAIE,SAAS,CAACmV,uBAAuB,EAAE7E,SAAS,IAAI;IACnE,MAAM3D,QAAQ,GAAG2D,SAAS,CAAC+E,WAAW,CAAC,WAAW,CAAC,CAAC/O,YAAY,CAAC,CAAC;IAClE,OAAOkJ,eAAe,CAAC7C,QAAQ,CAAC;EACpC,CAAC,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAAC2I,oBAAoB,CAAC,UAAU,CAAC,gCAAgC,CAAC,CAAC;EAC1GvV,eAAe,CAAC6E,IAAI,EAAE8K,OAAO,CAAC;AAClC;AACA0F,gBAAgB,CAAC,CAAC;AAElB,SAAS9B,cAAc,EAAEF,2BAA2B,EAAElB,mBAAmB,EAAEzC,kBAAkB,EAAEpC,QAAQ,EAAEyG,kBAAkB,EAAEc,cAAc,EAAEL,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}