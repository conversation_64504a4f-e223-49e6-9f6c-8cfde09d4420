{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { RewardsListComponent } from './components/rewards-list/rewards-list.component';\nimport { RewardDetailComponent } from './components/reward-detail/reward-detail.component';\nimport { ExchangeHistoryComponent } from './components/exchange-history/exchange-history.component';\nimport { rewardsRoutes } from './rewards.routes';\nlet RewardsModule = class RewardsModule {};\nRewardsModule = __decorate([NgModule({\n  declarations: [RewardsListComponent, RewardDetailComponent, ExchangeHistoryComponent],\n  imports: [CommonModule, RouterModule.forChild(rewardsRoutes),\n  // Material modules\n  MatCardModule, MatButtonModule, MatIconModule, MatChipsModule, MatTabsModule, MatDialogModule, MatSnackBarModule, MatProgressSpinnerModule, MatBadgeModule]\n})], RewardsModule);\nexport { RewardsModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatChipsModule", "MatTabsModule", "MatDialogModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatBadgeModule", "RewardsListComponent", "RewardDetailComponent", "ExchangeHistoryComponent", "rewardsRoutes", "RewardsModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\rewards\\rewards.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { ReactiveFormsModule } from '@angular/forms';\n\nimport { RewardsListComponent } from './components/rewards-list/rewards-list.component';\nimport { RewardDetailComponent } from './components/reward-detail/reward-detail.component';\nimport { ExchangeHistoryComponent } from './components/exchange-history/exchange-history.component';\nimport { rewardsRoutes } from './rewards.routes';\n\n@NgModule({\n  declarations: [\n    RewardsListComponent,\n    RewardDetailComponent,\n    ExchangeHistoryComponent\n  ],\n  imports: [\n    CommonModule,\n    RouterModule.forChild(rewardsRoutes),\n    \n    // Material modules\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatChipsModule,\n    MatTabsModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule,\n    MatBadgeModule\n  ]\n})\nexport class RewardsModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AAKxD,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,wBAAwB,QAAQ,0DAA0D;AACnG,SAASC,aAAa,QAAQ,kBAAkB;AAwBzC,IAAMC,aAAa,GAAnB,MAAMA,aAAa,GAAI;AAAjBA,aAAa,GAAAC,UAAA,EAtBzBjB,QAAQ,CAAC;EACRkB,YAAY,EAAE,CACZN,oBAAoB,EACpBC,qBAAqB,EACrBC,wBAAwB,CACzB;EACDK,OAAO,EAAE,CACPlB,YAAY,EACZC,YAAY,CAACkB,QAAQ,CAACL,aAAa,CAAC;EAEpC;EACAZ,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,EACxBC,cAAc;CAEjB,CAAC,C,EACWK,aAAa,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}