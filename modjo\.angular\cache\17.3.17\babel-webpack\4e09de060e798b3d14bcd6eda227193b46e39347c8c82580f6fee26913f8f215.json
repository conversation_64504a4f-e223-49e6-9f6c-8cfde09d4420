{"ast": null, "code": "import { UserRole } from '../models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class DashboardRouterService {\n  constructor(router) {\n    this.router = router;\n  }\n  /**\n   * Redirige l'utilisateur vers le dashboard approprié selon son rôle\n   */\n  navigateToUserDashboard(user) {\n    const route = this.getDashboardRoute(user.role);\n    this.router.navigate([route]);\n  }\n  /**\n   * Retourne la route du dashboard selon le rôle utilisateur\n   */\n  getDashboardRoute(role) {\n    const dashboardRoutes = {\n      [UserRole.USER]: '/dashboard',\n      [UserRole.ADMIN]: '/admin',\n      [UserRole.VALIDATOR]: '/validator',\n      [UserRole.PARTNER]: '/partner',\n      [UserRole.PROVIDER]: '/provider' // 🧑‍🔧 Prestataire\n    };\n    return dashboardRoutes[role] || '/dashboard';\n  }\n  /**\n   * Vérifie si l'utilisateur a accès à une route spécifique\n   */\n  canAccessRoute(userRole, route) {\n    const roleRoutes = {\n      [UserRole.USER]: ['/dashboard', '/profile', '/qr-scanner', '/rewards'],\n      [UserRole.ADMIN]: ['/admin', '/profile'],\n      [UserRole.VALIDATOR]: ['/validator', '/profile'],\n      [UserRole.PARTNER]: ['/partner', '/profile'],\n      [UserRole.PROVIDER]: ['/provider', '/profile', '/qr-scanner']\n    };\n    const allowedRoutes = roleRoutes[userRole] || [];\n    return allowedRoutes.some(allowedRoute => route.startsWith(allowedRoute));\n  }\n  /**\n   * Retourne le nom d'affichage du dashboard selon le rôle\n   */\n  getDashboardDisplayName(role) {\n    const dashboardNames = {\n      [UserRole.USER]: 'Dashboard Utilisateur',\n      [UserRole.ADMIN]: 'Dashboard Administrateur',\n      [UserRole.VALIDATOR]: 'Dashboard Validateur',\n      [UserRole.PARTNER]: 'Dashboard Partenaire',\n      [UserRole.PROVIDER]: 'Dashboard Prestataire'\n    };\n    return dashboardNames[role] || 'Dashboard';\n  }\n  /**\n   * Retourne l'icône du dashboard selon le rôle\n   */\n  getDashboardIcon(role) {\n    const dashboardIcons = {\n      [UserRole.USER]: 'person',\n      [UserRole.ADMIN]: 'admin_panel_settings',\n      [UserRole.VALIDATOR]: 'verified_user',\n      [UserRole.PARTNER]: 'store',\n      [UserRole.PROVIDER]: 'business'\n    };\n    return dashboardIcons[role] || 'dashboard';\n  }\n  /**\n   * Retourne la description du dashboard selon le rôle\n   */\n  getDashboardDescription(role) {\n    const dashboardDescriptions = {\n      [UserRole.USER]: 'Gérez vos points, scannez des QR codes et échangez des récompenses',\n      [UserRole.ADMIN]: 'Supervision complète de l\\'application et gestion des utilisateurs',\n      [UserRole.VALIDATOR]: 'Validez les actions communautaires et attribuez des points',\n      [UserRole.PARTNER]: 'Gérez vos récompenses et suivez vos performances commerciales',\n      [UserRole.PROVIDER]: 'Générez des QR codes et suivez l\\'engagement de vos activités'\n    };\n    return dashboardDescriptions[role] || 'Tableau de bord';\n  }\n  /**\n   * Retourne les fonctionnalités principales selon le rôle\n   */\n  getDashboardFeatures(role) {\n    const dashboardFeatures = {\n      [UserRole.USER]: ['Scanner des QR codes', 'Voir ses points disponibles', 'Échanger des récompenses', 'Consulter l\\'historique', 'Modifier son profil'],\n      [UserRole.ADMIN]: ['Gestion des utilisateurs', 'Suivi des points globaux', 'Gestion des partenaires', 'Configuration système', 'Statistiques globales'],\n      [UserRole.VALIDATOR]: ['Valider les actions', 'Attribuer des points', 'Consulter l\\'historique', 'Voir les statistiques'],\n      [UserRole.PARTNER]: ['Ajouter des récompenses', 'Suivre les échanges', 'Modifier les offres', 'Voir les statistiques'],\n      [UserRole.PROVIDER]: ['Générer des QR codes', 'Voir les statistiques d\\'usage', 'Consulter l\\'historique', 'Gérer son profil']\n    };\n    return dashboardFeatures[role] || [];\n  }\n  static {\n    this.ɵfac = function DashboardRouterService_Factory(t) {\n      return new (t || DashboardRouterService)(i0.ɵɵinject(i1.Router));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: DashboardRouterService,\n      factory: DashboardRouterService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["UserRole", "DashboardRouterService", "constructor", "router", "navigateToUserDashboard", "user", "route", "getDashboardRoute", "role", "navigate", "dashboardRoutes", "USER", "ADMIN", "VALIDATOR", "PARTNER", "PROVIDER", "canAccessRoute", "userRole", "roleRoutes", "allowedRoutes", "some", "allowed<PERSON><PERSON>e", "startsWith", "getDashboardDisplayName", "dashboardNames", "getDashboardIcon", "dashboardIcons", "getDashboardDescription", "dashboardDescriptions", "getDashboardFeatures", "dashboardFeatures", "i0", "ɵɵinject", "i1", "Router", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\services\\dashboard-router.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { UserRole, User } from '../models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class DashboardRouterService {\n\n  constructor(private router: Router) {}\n\n  /**\n   * Redirige l'utilisateur vers le dashboard approprié selon son rôle\n   */\n  navigateToUserDashboard(user: User): void {\n    const route = this.getDashboardRoute(user.role);\n    this.router.navigate([route]);\n  }\n\n  /**\n   * Retourne la route du dashboard selon le rôle utilisateur\n   */\n  getDashboardRoute(role: UserRole): string {\n    const dashboardRoutes = {\n      [UserRole.USER]: '/dashboard',           // 🙋‍♂️ Utilisateur standard\n      [UserRole.ADMIN]: '/admin',              // 🧑‍💼 Administrateur\n      [UserRole.VALIDATOR]: '/validator',      // 🧑‍🏫 Validateur\n      [UserRole.PARTNER]: '/partner',          // 🧑‍🍳 Partenaire\n      [UserRole.PROVIDER]: '/provider'         // 🧑‍🔧 Prestataire\n    };\n\n    return dashboardRoutes[role] || '/dashboard';\n  }\n\n  /**\n   * Vérifie si l'utilisateur a accès à une route spécifique\n   */\n  canAccessRoute(userRole: UserRole, route: string): boolean {\n    const roleRoutes = {\n      [UserRole.USER]: ['/dashboard', '/profile', '/qr-scanner', '/rewards'],\n      [UserRole.ADMIN]: ['/admin', '/profile'],\n      [UserRole.VALIDATOR]: ['/validator', '/profile'],\n      [UserRole.PARTNER]: ['/partner', '/profile'],\n      [UserRole.PROVIDER]: ['/provider', '/profile', '/qr-scanner']\n    };\n\n    const allowedRoutes = roleRoutes[userRole] || [];\n    return allowedRoutes.some(allowedRoute => route.startsWith(allowedRoute));\n  }\n\n  /**\n   * Retourne le nom d'affichage du dashboard selon le rôle\n   */\n  getDashboardDisplayName(role: UserRole): string {\n    const dashboardNames = {\n      [UserRole.USER]: 'Dashboard Utilisateur',\n      [UserRole.ADMIN]: 'Dashboard Administrateur',\n      [UserRole.VALIDATOR]: 'Dashboard Validateur',\n      [UserRole.PARTNER]: 'Dashboard Partenaire',\n      [UserRole.PROVIDER]: 'Dashboard Prestataire'\n    };\n\n    return dashboardNames[role] || 'Dashboard';\n  }\n\n  /**\n   * Retourne l'icône du dashboard selon le rôle\n   */\n  getDashboardIcon(role: UserRole): string {\n    const dashboardIcons = {\n      [UserRole.USER]: 'person',\n      [UserRole.ADMIN]: 'admin_panel_settings',\n      [UserRole.VALIDATOR]: 'verified_user',\n      [UserRole.PARTNER]: 'store',\n      [UserRole.PROVIDER]: 'business'\n    };\n\n    return dashboardIcons[role] || 'dashboard';\n  }\n\n  /**\n   * Retourne la description du dashboard selon le rôle\n   */\n  getDashboardDescription(role: UserRole): string {\n    const dashboardDescriptions = {\n      [UserRole.USER]: 'Gérez vos points, scannez des QR codes et échangez des récompenses',\n      [UserRole.ADMIN]: 'Supervision complète de l\\'application et gestion des utilisateurs',\n      [UserRole.VALIDATOR]: 'Validez les actions communautaires et attribuez des points',\n      [UserRole.PARTNER]: 'Gérez vos récompenses et suivez vos performances commerciales',\n      [UserRole.PROVIDER]: 'Générez des QR codes et suivez l\\'engagement de vos activités'\n    };\n\n    return dashboardDescriptions[role] || 'Tableau de bord';\n  }\n\n  /**\n   * Retourne les fonctionnalités principales selon le rôle\n   */\n  getDashboardFeatures(role: UserRole): string[] {\n    const dashboardFeatures = {\n      [UserRole.USER]: [\n        'Scanner des QR codes',\n        'Voir ses points disponibles',\n        'Échanger des récompenses',\n        'Consulter l\\'historique',\n        'Modifier son profil'\n      ],\n      [UserRole.ADMIN]: [\n        'Gestion des utilisateurs',\n        'Suivi des points globaux',\n        'Gestion des partenaires',\n        'Configuration système',\n        'Statistiques globales'\n      ],\n      [UserRole.VALIDATOR]: [\n        'Valider les actions',\n        'Attribuer des points',\n        'Consulter l\\'historique',\n        'Voir les statistiques'\n      ],\n      [UserRole.PARTNER]: [\n        'Ajouter des récompenses',\n        'Suivre les échanges',\n        'Modifier les offres',\n        'Voir les statistiques'\n      ],\n      [UserRole.PROVIDER]: [\n        'Générer des QR codes',\n        'Voir les statistiques d\\'usage',\n        'Consulter l\\'historique',\n        'Gérer son profil'\n      ]\n    };\n\n    return dashboardFeatures[role] || [];\n  }\n}\n"], "mappings": "AAEA,SAASA,QAAQ,QAAc,WAAW;;;AAK1C,OAAM,MAAOC,sBAAsB;EAEjCC,YAAoBC,MAAc;IAAd,KAAAA,MAAM,GAANA,MAAM;EAAW;EAErC;;;EAGAC,uBAAuBA,CAACC,IAAU;IAChC,MAAMC,KAAK,GAAG,IAAI,CAACC,iBAAiB,CAACF,IAAI,CAACG,IAAI,CAAC;IAC/C,IAAI,CAACL,MAAM,CAACM,QAAQ,CAAC,CAACH,KAAK,CAAC,CAAC;EAC/B;EAEA;;;EAGAC,iBAAiBA,CAACC,IAAc;IAC9B,MAAME,eAAe,GAAG;MACtB,CAACV,QAAQ,CAACW,IAAI,GAAG,YAAY;MAC7B,CAACX,QAAQ,CAACY,KAAK,GAAG,QAAQ;MAC1B,CAACZ,QAAQ,CAACa,SAAS,GAAG,YAAY;MAClC,CAACb,QAAQ,CAACc,OAAO,GAAG,UAAU;MAC9B,CAACd,QAAQ,CAACe,QAAQ,GAAG,WAAW,CAAS;KAC1C;IAED,OAAOL,eAAe,CAACF,IAAI,CAAC,IAAI,YAAY;EAC9C;EAEA;;;EAGAQ,cAAcA,CAACC,QAAkB,EAAEX,KAAa;IAC9C,MAAMY,UAAU,GAAG;MACjB,CAAClB,QAAQ,CAACW,IAAI,GAAG,CAAC,YAAY,EAAE,UAAU,EAAE,aAAa,EAAE,UAAU,CAAC;MACtE,CAACX,QAAQ,CAACY,KAAK,GAAG,CAAC,QAAQ,EAAE,UAAU,CAAC;MACxC,CAACZ,QAAQ,CAACa,SAAS,GAAG,CAAC,YAAY,EAAE,UAAU,CAAC;MAChD,CAACb,QAAQ,CAACc,OAAO,GAAG,CAAC,UAAU,EAAE,UAAU,CAAC;MAC5C,CAACd,QAAQ,CAACe,QAAQ,GAAG,CAAC,WAAW,EAAE,UAAU,EAAE,aAAa;KAC7D;IAED,MAAMI,aAAa,GAAGD,UAAU,CAACD,QAAQ,CAAC,IAAI,EAAE;IAChD,OAAOE,aAAa,CAACC,IAAI,CAACC,YAAY,IAAIf,KAAK,CAACgB,UAAU,CAACD,YAAY,CAAC,CAAC;EAC3E;EAEA;;;EAGAE,uBAAuBA,CAACf,IAAc;IACpC,MAAMgB,cAAc,GAAG;MACrB,CAACxB,QAAQ,CAACW,IAAI,GAAG,uBAAuB;MACxC,CAACX,QAAQ,CAACY,KAAK,GAAG,0BAA0B;MAC5C,CAACZ,QAAQ,CAACa,SAAS,GAAG,sBAAsB;MAC5C,CAACb,QAAQ,CAACc,OAAO,GAAG,sBAAsB;MAC1C,CAACd,QAAQ,CAACe,QAAQ,GAAG;KACtB;IAED,OAAOS,cAAc,CAAChB,IAAI,CAAC,IAAI,WAAW;EAC5C;EAEA;;;EAGAiB,gBAAgBA,CAACjB,IAAc;IAC7B,MAAMkB,cAAc,GAAG;MACrB,CAAC1B,QAAQ,CAACW,IAAI,GAAG,QAAQ;MACzB,CAACX,QAAQ,CAACY,KAAK,GAAG,sBAAsB;MACxC,CAACZ,QAAQ,CAACa,SAAS,GAAG,eAAe;MACrC,CAACb,QAAQ,CAACc,OAAO,GAAG,OAAO;MAC3B,CAACd,QAAQ,CAACe,QAAQ,GAAG;KACtB;IAED,OAAOW,cAAc,CAAClB,IAAI,CAAC,IAAI,WAAW;EAC5C;EAEA;;;EAGAmB,uBAAuBA,CAACnB,IAAc;IACpC,MAAMoB,qBAAqB,GAAG;MAC5B,CAAC5B,QAAQ,CAACW,IAAI,GAAG,oEAAoE;MACrF,CAACX,QAAQ,CAACY,KAAK,GAAG,oEAAoE;MACtF,CAACZ,QAAQ,CAACa,SAAS,GAAG,4DAA4D;MAClF,CAACb,QAAQ,CAACc,OAAO,GAAG,+DAA+D;MACnF,CAACd,QAAQ,CAACe,QAAQ,GAAG;KACtB;IAED,OAAOa,qBAAqB,CAACpB,IAAI,CAAC,IAAI,iBAAiB;EACzD;EAEA;;;EAGAqB,oBAAoBA,CAACrB,IAAc;IACjC,MAAMsB,iBAAiB,GAAG;MACxB,CAAC9B,QAAQ,CAACW,IAAI,GAAG,CACf,sBAAsB,EACtB,6BAA6B,EAC7B,0BAA0B,EAC1B,yBAAyB,EACzB,qBAAqB,CACtB;MACD,CAACX,QAAQ,CAACY,KAAK,GAAG,CAChB,0BAA0B,EAC1B,0BAA0B,EAC1B,yBAAyB,EACzB,uBAAuB,EACvB,uBAAuB,CACxB;MACD,CAACZ,QAAQ,CAACa,SAAS,GAAG,CACpB,qBAAqB,EACrB,sBAAsB,EACtB,yBAAyB,EACzB,uBAAuB,CACxB;MACD,CAACb,QAAQ,CAACc,OAAO,GAAG,CAClB,yBAAyB,EACzB,qBAAqB,EACrB,qBAAqB,EACrB,uBAAuB,CACxB;MACD,CAACd,QAAQ,CAACe,QAAQ,GAAG,CACnB,sBAAsB,EACtB,gCAAgC,EAChC,yBAAyB,EACzB,kBAAkB;KAErB;IAED,OAAOe,iBAAiB,CAACtB,IAAI,CAAC,IAAI,EAAE;EACtC;;;uBAhIWP,sBAAsB,EAAA8B,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,MAAA;IAAA;EAAA;;;aAAtBjC,sBAAsB;MAAAkC,OAAA,EAAtBlC,sBAAsB,CAAAmC,IAAA;MAAAC,UAAA,EAFrB;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}