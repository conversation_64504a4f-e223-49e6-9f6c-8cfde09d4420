import { Injectable } from '@angular/core';
import { Firestore, collection, doc, getDoc, getDocs, updateDoc, query, where, orderBy, limit } from '@angular/fire/firestore';
import { Observable, from, map } from 'rxjs';
import { User, PointsTransaction, TransactionType, UpdateUserRequest, PaginatedResponse } from '../models';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  private usersCollection = collection(this.firestore, 'users');
  private transactionsCollection = collection(this.firestore, 'transactions');

  constructor(private firestore: Firestore) {}

  getUserById(uid: string): Observable<User | null> {
    return from(getDoc(doc(this.firestore, 'users', uid))).pipe(
      map(docSnap => docSnap.exists() ? docSnap.data() as User : null)
    );
  }

  updateUser(uid: string, updates: UpdateUserRequest): Observable<void> {
    const updateData = {
      ...updates,
      updatedAt: new Date()
    };
    return from(updateDoc(doc(this.firestore, 'users', uid), updateData));
  }

  getUsersByCity(city: 'Monastir' | 'Sousse'): Observable<User[]> {
    const q = query(
      this.usersCollection,
      where('city', '==', city),
      where('isActive', '==', true),
      orderBy('points', 'desc')
    );
    
    return from(getDocs(q)).pipe(
      map(snapshot => snapshot.docs.map(doc => doc.data() as User))
    );
  }

  getTopUsers(limitCount: number = 10): Observable<User[]> {
    const q = query(
      this.usersCollection,
      where('isActive', '==', true),
      orderBy('points', 'desc'),
      limit(limitCount)
    );
    
    return from(getDocs(q)).pipe(
      map(snapshot => snapshot.docs.map(doc => doc.data() as User))
    );
  }

  getUserTransactions(uid: string, limitCount: number = 50): Observable<PointsTransaction[]> {
    const q = query(
      this.transactionsCollection,
      where('toUserId', '==', uid),
      orderBy('timestamp', 'desc'),
      limit(limitCount)
    );
    
    return from(getDocs(q)).pipe(
      map(snapshot => snapshot.docs.map(doc => doc.data() as PointsTransaction))
    );
  }

  async addPointsToUser(uid: string, points: number, type: TransactionType, description: string, fromUserId?: string): Promise<void> {
    try {
      // Get current user data
      const userDoc = await getDoc(doc(this.firestore, 'users', uid));
      if (!userDoc.exists()) {
        throw new Error('User not found');
      }

      const userData = userDoc.data() as User;
      const newPoints = userData.points + points;

      // Create transaction record
      const transaction: PointsTransaction = {
        id: this.generateTransactionId(),
        fromUserId,
        toUserId: uid,
        points,
        type,
        description,
        timestamp: new Date()
      };

      // Update user points and add transaction to history
      const updatedHistory = [...(userData.history || []), transaction];
      
      await updateDoc(doc(this.firestore, 'users', uid), {
        points: newPoints,
        history: updatedHistory,
        updatedAt: new Date()
      });

      // Also store transaction in separate collection for better querying
      await updateDoc(doc(this.firestore, 'transactions', transaction.id), transaction);
    } catch (error) {
      console.error('Error adding points:', error);
      throw error;
    }
  }

  async transferPoints(fromUid: string, toUid: string, points: number, description: string): Promise<void> {
    try {
      // Get both users
      const [fromUserDoc, toUserDoc] = await Promise.all([
        getDoc(doc(this.firestore, 'users', fromUid)),
        getDoc(doc(this.firestore, 'users', toUid))
      ]);

      if (!fromUserDoc.exists() || !toUserDoc.exists()) {
        throw new Error('One or both users not found');
      }

      const fromUser = fromUserDoc.data() as User;
      const toUser = toUserDoc.data() as User;

      if (fromUser.points < points) {
        throw new Error('Insufficient points');
      }

      // Create transactions
      const transactionId = this.generateTransactionId();
      const timestamp = new Date();

      const fromTransaction: PointsTransaction = {
        id: transactionId + '_from',
        fromUserId: fromUid,
        toUserId: toUid,
        points: -points,
        type: TransactionType.TRANSFERRED,
        description: `Transferred to ${toUser.name}: ${description}`,
        timestamp
      };

      const toTransaction: PointsTransaction = {
        id: transactionId + '_to',
        fromUserId: fromUid,
        toUserId: toUid,
        points: points,
        type: TransactionType.TRANSFERRED,
        description: `Received from ${fromUser.name}: ${description}`,
        timestamp
      };

      // Update both users
      await Promise.all([
        updateDoc(doc(this.firestore, 'users', fromUid), {
          points: fromUser.points - points,
          history: [...(fromUser.history || []), fromTransaction],
          updatedAt: timestamp
        }),
        updateDoc(doc(this.firestore, 'users', toUid), {
          points: toUser.points + points,
          history: [...(toUser.history || []), toTransaction],
          updatedAt: timestamp
        })
      ]);
    } catch (error) {
      console.error('Error transferring points:', error);
      throw error;
    }
  }

  private generateTransactionId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  // Get user statistics
  getUserStats(uid: string): Observable<any> {
    return this.getUserById(uid).pipe(
      map(user => {
        if (!user) return null;
        
        const history = user.history || [];
        const earned = history.filter(t => t.points > 0).reduce((sum, t) => sum + t.points, 0);
        const spent = history.filter(t => t.points < 0).reduce((sum, t) => sum + Math.abs(t.points), 0);
        
        return {
          totalPoints: user.points,
          totalEarned: earned,
          totalSpent: spent,
          transactionCount: history.length,
          joinDate: user.createdAt,
          lastActivity: user.updatedAt
        };
      })
    );
  }
}
