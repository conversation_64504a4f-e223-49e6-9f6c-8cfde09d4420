import { Injectable } from '@angular/core';
import { AngularFirestore } from '@angular/fire/compat/firestore';
import { Observable, from, map } from 'rxjs';
import { User, PointsTransaction, TransactionType, UpdateUserRequest, PaginatedResponse } from '../models';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  constructor(private firestore: AngularFirestore) {}

  getUserById(uid: string): Observable<User | null> {
    // Temporarily disabled for initial setup
    return new Observable(observer => {
      observer.next(null);
      observer.complete();
    });
  }

  updateUser(uid: string, updates: UpdateUserRequest): Observable<void> {
    // Temporarily disabled for initial setup
    return new Observable(observer => {
      observer.next();
      observer.complete();
    });
  }

  getUsersByCity(city: 'Monastir' | 'Sousse'): Observable<User[]> {
    // Temporarily disabled for initial setup
    return new Observable(observer => {
      observer.next([]);
      observer.complete();
    });
  }

  getTopUsers(limitCount: number = 10): Observable<User[]> {
    // Temporarily disabled for initial setup
    return new Observable(observer => {
      observer.next([]);
      observer.complete();
    });
  }

  getUserTransactions(uid: string, limitCount: number = 50): Observable<PointsTransaction[]> {
    // Temporarily disabled for initial setup
    return new Observable(observer => {
      observer.next([]);
      observer.complete();
    });
  }

  async addPointsToUser(uid: string, points: number, type: TransactionType, description: string, fromUserId?: string): Promise<void> {
    // Temporarily disabled for initial setup
    console.log('Mock: Adding points', { uid, points, type, description });
    return Promise.resolve();
  }

  async transferPoints(fromUid: string, toUid: string, points: number, description: string): Promise<void> {
    // Temporarily disabled for initial setup
    console.log('Mock: Transferring points', { fromUid, toUid, points, description });
    return Promise.resolve();
  }

  private generateTransactionId(): string {
    return Date.now().toString() + Math.random().toString(36).substr(2, 9);
  }

  // Get user statistics
  getUserStats(uid: string): Observable<any> {
    // Temporarily disabled for initial setup
    return new Observable(observer => {
      observer.next({
        totalPoints: 0,
        totalEarned: 0,
        totalSpent: 0,
        transactionCount: 0,
        joinDate: new Date(),
        lastActivity: new Date()
      });
      observer.complete();
    });
  }
}
