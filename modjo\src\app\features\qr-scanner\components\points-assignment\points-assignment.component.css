.assignment-dialog {
  min-width: 400px;
  max-width: 500px;
}

.dialog-header {
  text-align: center;
  margin-bottom: 16px;
}

.dialog-header h2 {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  margin: 0 0 8px 0;
  color: #333;
}

.scan-type {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.assignment-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.full-width {
  width: 100%;
}

.points-option {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
}

.points-value {
  font-weight: 500;
  color: #333;
}

.points-description {
  font-size: 0.8rem;
  color: #666;
  margin-top: 2px;
}

.scan-details {
  background: #f5f5f5;
  border-radius: 8px;
  padding: 16px;
  margin: 16px 0;
}

.scan-details h4 {
  margin: 0 0 12px 0;
  color: #333;
  font-size: 1rem;
  font-weight: 500;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  color: #666;
  font-size: 0.9rem;
}

.detail-item:last-child {
  margin-bottom: 0;
}

.detail-item mat-icon {
  font-size: 1.2rem;
  width: 1.2rem;
  height: 1.2rem;
  color: #999;
}

.points-preview {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 12px;
  padding: 16px;
  color: white;
  margin: 16px 0;
}

.preview-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.preview-icon {
  font-size: 2.5rem;
  color: #ffd700;
}

.preview-text h3 {
  margin: 0 0 4px 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.preview-text p {
  margin: 0;
  opacity: 0.9;
  font-size: 0.9rem;
}

mat-dialog-actions {
  padding: 16px 0 0 0;
  margin: 0;
}

mat-dialog-actions button {
  margin-left: 8px;
}

/* Mobile responsive */
@media (max-width: 480px) {
  .assignment-dialog {
    min-width: 300px;
    max-width: 350px;
  }
  
  .preview-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .preview-icon {
    font-size: 2rem;
  }
  
  .preview-text h3 {
    font-size: 1.3rem;
  }
}
