{"ast": null, "code": "import { ProfileComponent } from './components/profile/profile.component';\n// import { ProfileEditComponent } from './components/profile-edit/profile-edit.component';\nexport const profileRoutes = [{\n  path: '',\n  component: ProfileComponent\n}\n// {\n//   path: 'edit',\n//   component: ProfileEditComponent\n// }\n];", "map": {"version": 3, "names": ["ProfileComponent", "profileRoutes", "path", "component"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\profile\\profile.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { ProfileComponent } from './components/profile/profile.component';\n// import { ProfileEditComponent } from './components/profile-edit/profile-edit.component';\n\nexport const profileRoutes: Routes = [\n  {\n    path: '',\n    component: ProfileComponent\n  },\n  // {\n  //   path: 'edit',\n  //   component: ProfileEditComponent\n  // }\n];\n"], "mappings": "AACA,SAASA,gBAAgB,QAAQ,wCAAwC;AACzE;AAEA,OAAO,MAAMC,aAAa,GAAW,CACnC;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;;AAEb;AACA;AACA;AACA;AAAA,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}