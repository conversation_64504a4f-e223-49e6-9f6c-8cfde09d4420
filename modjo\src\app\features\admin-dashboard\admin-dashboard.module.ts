import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { SharedModule } from '../../shared/shared.module';
import { AdminDashboardComponent } from './components/admin-dashboard/admin-dashboard.component';
import { UserManagementComponent } from './components/user-management/user-management.component';
import { SystemStatsComponent } from './components/system-stats/system-stats.component';
import { PartnerManagementComponent } from './components/partner-management/partner-management.component';
import { ProviderManagementComponent } from './components/provider-management/provider-management.component';
import { ValidatorManagementComponent } from './components/validator-management/validator-management.component';
import { SystemConfigComponent } from './components/system-config/system-config.component';
import { AuditLogComponent } from './components/audit-log/audit-log.component';
import { adminDashboardRoutes } from './admin-dashboard.routes';

@NgModule({
  declarations: [
    AdminDashboardComponent,
    UserManagementComponent,
    SystemStatsComponent,
    PartnerManagementComponent,
    ProviderManagementComponent,
    ValidatorManagementComponent,
    SystemConfigComponent,
    AuditLogComponent
  ],
  imports: [
    SharedModule,
    RouterModule.forChild(adminDashboardRoutes)
  ]
})
export class AdminDashboardModule { }
