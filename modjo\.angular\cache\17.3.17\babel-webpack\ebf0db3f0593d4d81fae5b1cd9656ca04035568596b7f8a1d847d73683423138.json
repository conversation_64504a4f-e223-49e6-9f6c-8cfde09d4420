{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport firebase from '@firebase/app-compat';\nimport * as exp from '@firebase/auth/internal';\nimport { Component } from '@firebase/component';\nimport { isBrowserExtension, getUA, isReactNative, isNode, isIndexedDBAvailable, isIE, FirebaseError } from '@firebase/util';\nvar name = \"@firebase/auth-compat\";\nvar version = \"0.4.2\";\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst CORDOVA_ONDEVICEREADY_TIMEOUT_MS = 1000;\nfunction _getCurrentScheme() {\n  var _a;\n  return ((_a = self === null || self === void 0 ? void 0 : self.location) === null || _a === void 0 ? void 0 : _a.protocol) || null;\n}\n/**\r\n * @return {boolean} Whether the current environment is http or https.\r\n */\nfunction _isHttpOrHttps() {\n  return _getCurrentScheme() === 'http:' || _getCurrentScheme() === 'https:';\n}\n/**\r\n * @param {?string=} ua The user agent.\r\n * @return {boolean} Whether the app is rendered in a mobile iOS or Android\r\n *     Cordova environment.\r\n */\nfunction _isAndroidOrIosCordovaScheme(ua = getUA()) {\n  return !!((_getCurrentScheme() === 'file:' || _getCurrentScheme() === 'ionic:' || _getCurrentScheme() === 'capacitor:') && ua.toLowerCase().match(/iphone|ipad|ipod|android/));\n}\n/**\r\n * @return {boolean} Whether the environment is a native environment, where\r\n *     CORS checks do not apply.\r\n */\nfunction _isNativeEnvironment() {\n  return isReactNative() || isNode();\n}\n/**\r\n * Checks whether the user agent is IE11.\r\n * @return {boolean} True if it is IE11.\r\n */\nfunction _isIe11() {\n  return isIE() && (document === null || document === void 0 ? void 0 : document.documentMode) === 11;\n}\n/**\r\n * Checks whether the user agent is Edge.\r\n * @param {string} userAgent The browser user agent string.\r\n * @return {boolean} True if it is Edge.\r\n */\nfunction _isEdge(ua = getUA()) {\n  return /Edge\\/\\d+/.test(ua);\n}\n/**\r\n * @param {?string=} opt_userAgent The navigator user agent.\r\n * @return {boolean} Whether local storage is not synchronized between an iframe\r\n *     and a popup of the same domain.\r\n */\nfunction _isLocalStorageNotSynchronized(ua = getUA()) {\n  return _isIe11() || _isEdge(ua);\n}\n/** @return {boolean} Whether web storage is supported. */\nfunction _isWebStorageSupported() {\n  try {\n    const storage = self.localStorage;\n    const key = exp._generateEventId();\n    if (storage) {\n      // setItem will throw an exception if we cannot access WebStorage (e.g.,\n      // Safari in private mode).\n      storage['setItem'](key, '1');\n      storage['removeItem'](key);\n      // For browsers where iframe web storage does not synchronize with a popup\n      // of the same domain, indexedDB is used for persistent storage. These\n      // browsers include IE11 and Edge.\n      // Make sure it is supported (IE11 and Edge private mode does not support\n      // that).\n      if (_isLocalStorageNotSynchronized()) {\n        // In such browsers, if indexedDB is not supported, an iframe cannot be\n        // notified of the popup sign in result.\n        return isIndexedDBAvailable();\n      }\n      return true;\n    }\n  } catch (e) {\n    // localStorage is not available from a worker. Test availability of\n    // indexedDB.\n    return _isWorker() && isIndexedDBAvailable();\n  }\n  return false;\n}\n/**\r\n * @param {?Object=} global The optional global scope.\r\n * @return {boolean} Whether current environment is a worker.\r\n */\nfunction _isWorker() {\n  // WorkerGlobalScope only defined in worker environment.\n  return typeof global !== 'undefined' && 'WorkerGlobalScope' in global && 'importScripts' in global;\n}\nfunction _isPopupRedirectSupported() {\n  return (_isHttpOrHttps() || isBrowserExtension() || _isAndroidOrIosCordovaScheme()) &&\n  // React Native with remote debugging reports its location.protocol as\n  // http.\n  !_isNativeEnvironment() &&\n  // Local storage has to be supported for browser popup and redirect\n  // operations to work.\n  _isWebStorageSupported() &&\n  // DOM, popups and redirects are not supported within a worker.\n  !_isWorker();\n}\n/** Quick check that indicates the platform *may* be Cordova */\nfunction _isLikelyCordova() {\n  return _isAndroidOrIosCordovaScheme() && typeof document !== 'undefined';\n}\nfunction _isCordova() {\n  return _isCordova2.apply(this, arguments);\n}\nfunction _isCordova2() {\n  _isCordova2 = _asyncToGenerator(function* () {\n    if (!_isLikelyCordova()) {\n      return false;\n    }\n    return new Promise(resolve => {\n      const timeoutId = setTimeout(() => {\n        // We've waited long enough; the telltale Cordova event didn't happen\n        resolve(false);\n      }, CORDOVA_ONDEVICEREADY_TIMEOUT_MS);\n      document.addEventListener('deviceready', () => {\n        clearTimeout(timeoutId);\n        resolve(true);\n      });\n    });\n  });\n  return _isCordova2.apply(this, arguments);\n}\nfunction _getSelfWindow() {\n  return typeof window !== 'undefined' ? window : null;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst Persistence = {\n  LOCAL: 'local',\n  NONE: 'none',\n  SESSION: 'session'\n};\nconst _assert$3 = exp._assert;\nconst PERSISTENCE_KEY = 'persistence';\n/**\r\n * Validates that an argument is a valid persistence value. If an invalid type\r\n * is specified, an error is thrown synchronously.\r\n */\nfunction _validatePersistenceArgument(auth, persistence) {\n  _assert$3(Object.values(Persistence).includes(persistence), auth, \"invalid-persistence-type\" /* exp.AuthErrorCode.INVALID_PERSISTENCE */);\n  // Validate if the specified type is supported in the current environment.\n  if (isReactNative()) {\n    // This is only supported in a browser.\n    _assert$3(persistence !== Persistence.SESSION, auth, \"unsupported-persistence-type\" /* exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE */);\n    return;\n  }\n  if (isNode()) {\n    // Only none is supported in Node.js.\n    _assert$3(persistence === Persistence.NONE, auth, \"unsupported-persistence-type\" /* exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE */);\n    return;\n  }\n  if (_isWorker()) {\n    // In a worker environment, either LOCAL or NONE are supported.\n    // If indexedDB not supported and LOCAL provided, throw an error\n    _assert$3(persistence === Persistence.NONE || persistence === Persistence.LOCAL && isIndexedDBAvailable(), auth, \"unsupported-persistence-type\" /* exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE */);\n    return;\n  }\n  // This is restricted by what the browser supports.\n  _assert$3(persistence === Persistence.NONE || _isWebStorageSupported(), auth, \"unsupported-persistence-type\" /* exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE */);\n}\nfunction _savePersistenceForRedirect(_x) {\n  return _savePersistenceForRedirect2.apply(this, arguments);\n}\nfunction _savePersistenceForRedirect2() {\n  _savePersistenceForRedirect2 = _asyncToGenerator(function* (auth) {\n    yield auth._initializationPromise;\n    const session = getSessionStorageIfAvailable();\n    const key = exp._persistenceKeyName(PERSISTENCE_KEY, auth.config.apiKey, auth.name);\n    if (session) {\n      session.setItem(key, auth._getPersistence());\n    }\n  });\n  return _savePersistenceForRedirect2.apply(this, arguments);\n}\nfunction _getPersistencesFromRedirect(apiKey, appName) {\n  const session = getSessionStorageIfAvailable();\n  if (!session) {\n    return [];\n  }\n  const key = exp._persistenceKeyName(PERSISTENCE_KEY, apiKey, appName);\n  const persistence = session.getItem(key);\n  switch (persistence) {\n    case Persistence.NONE:\n      return [exp.inMemoryPersistence];\n    case Persistence.LOCAL:\n      return [exp.indexedDBLocalPersistence, exp.browserSessionPersistence];\n    case Persistence.SESSION:\n      return [exp.browserSessionPersistence];\n    default:\n      return [];\n  }\n}\n/** Returns session storage, or null if the property access errors */\nfunction getSessionStorageIfAvailable() {\n  var _a;\n  try {\n    return ((_a = _getSelfWindow()) === null || _a === void 0 ? void 0 : _a.sessionStorage) || null;\n  } catch (e) {\n    return null;\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst _assert$2 = exp._assert;\n/** Platform-agnostic popup-redirect resolver */\nclass CompatPopupRedirectResolver {\n  constructor() {\n    // Create both resolvers for dynamic resolution later\n    this.browserResolver = exp._getInstance(exp.browserPopupRedirectResolver);\n    this.cordovaResolver = exp._getInstance(exp.cordovaPopupRedirectResolver);\n    // The actual resolver in use: either browserResolver or cordovaResolver.\n    this.underlyingResolver = null;\n    this._redirectPersistence = exp.browserSessionPersistence;\n    this._completeRedirectFn = exp._getRedirectResult;\n    this._overrideRedirectResult = exp._overrideRedirectResult;\n  }\n  _initialize(auth) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.selectUnderlyingResolver();\n      return _this.assertedUnderlyingResolver._initialize(auth);\n    })();\n  }\n  _openPopup(auth, provider, authType, eventId) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      yield _this2.selectUnderlyingResolver();\n      return _this2.assertedUnderlyingResolver._openPopup(auth, provider, authType, eventId);\n    })();\n  }\n  _openRedirect(auth, provider, authType, eventId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      yield _this3.selectUnderlyingResolver();\n      return _this3.assertedUnderlyingResolver._openRedirect(auth, provider, authType, eventId);\n    })();\n  }\n  _isIframeWebStorageSupported(auth, cb) {\n    this.assertedUnderlyingResolver._isIframeWebStorageSupported(auth, cb);\n  }\n  _originValidation(auth) {\n    return this.assertedUnderlyingResolver._originValidation(auth);\n  }\n  get _shouldInitProactively() {\n    return _isLikelyCordova() || this.browserResolver._shouldInitProactively;\n  }\n  get assertedUnderlyingResolver() {\n    _assert$2(this.underlyingResolver, \"internal-error\" /* exp.AuthErrorCode.INTERNAL_ERROR */);\n    return this.underlyingResolver;\n  }\n  selectUnderlyingResolver() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      if (_this4.underlyingResolver) {\n        return;\n      }\n      // We haven't yet determined whether or not we're in Cordova; go ahead\n      // and determine that state now.\n      const isCordova = yield _isCordova();\n      _this4.underlyingResolver = isCordova ? _this4.cordovaResolver : _this4.browserResolver;\n    })();\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction unwrap(object) {\n  return object.unwrap();\n}\nfunction wrapped(object) {\n  return object.wrapped();\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction credentialFromResponse(userCredential) {\n  return credentialFromObject(userCredential);\n}\nfunction attachExtraErrorFields(auth, e) {\n  var _a;\n  // The response contains all fields from the server which may or may not\n  // actually match the underlying type\n  const response = (_a = e.customData) === null || _a === void 0 ? void 0 : _a._tokenResponse;\n  if ((e === null || e === void 0 ? void 0 : e.code) === 'auth/multi-factor-auth-required') {\n    const mfaErr = e;\n    mfaErr.resolver = new MultiFactorResolver(auth, exp.getMultiFactorResolver(auth, e));\n  } else if (response) {\n    const credential = credentialFromObject(e);\n    const credErr = e;\n    if (credential) {\n      credErr.credential = credential;\n      credErr.tenantId = response.tenantId || undefined;\n      credErr.email = response.email || undefined;\n      credErr.phoneNumber = response.phoneNumber || undefined;\n    }\n  }\n}\nfunction credentialFromObject(object) {\n  const {\n    _tokenResponse\n  } = object instanceof FirebaseError ? object.customData : object;\n  if (!_tokenResponse) {\n    return null;\n  }\n  // Handle phone Auth credential responses, as they have a different format\n  // from other backend responses (i.e. no providerId). This is also only the\n  // case for user credentials (does not work for errors).\n  if (!(object instanceof FirebaseError)) {\n    if ('temporaryProof' in _tokenResponse && 'phoneNumber' in _tokenResponse) {\n      return exp.PhoneAuthProvider.credentialFromResult(object);\n    }\n  }\n  const providerId = _tokenResponse.providerId;\n  // Email and password is not supported as there is no situation where the\n  // server would return the password to the client.\n  if (!providerId || providerId === exp.ProviderId.PASSWORD) {\n    return null;\n  }\n  let provider;\n  switch (providerId) {\n    case exp.ProviderId.GOOGLE:\n      provider = exp.GoogleAuthProvider;\n      break;\n    case exp.ProviderId.FACEBOOK:\n      provider = exp.FacebookAuthProvider;\n      break;\n    case exp.ProviderId.GITHUB:\n      provider = exp.GithubAuthProvider;\n      break;\n    case exp.ProviderId.TWITTER:\n      provider = exp.TwitterAuthProvider;\n      break;\n    default:\n      const {\n        oauthIdToken,\n        oauthAccessToken,\n        oauthTokenSecret,\n        pendingToken,\n        nonce\n      } = _tokenResponse;\n      if (!oauthAccessToken && !oauthTokenSecret && !oauthIdToken && !pendingToken) {\n        return null;\n      }\n      // TODO(avolkovi): uncomment this and get it working with SAML & OIDC\n      if (pendingToken) {\n        if (providerId.startsWith('saml.')) {\n          return exp.SAMLAuthCredential._create(providerId, pendingToken);\n        } else {\n          // OIDC and non-default providers excluding Twitter.\n          return exp.OAuthCredential._fromParams({\n            providerId,\n            signInMethod: providerId,\n            pendingToken,\n            idToken: oauthIdToken,\n            accessToken: oauthAccessToken\n          });\n        }\n      }\n      return new exp.OAuthProvider(providerId).credential({\n        idToken: oauthIdToken,\n        accessToken: oauthAccessToken,\n        rawNonce: nonce\n      });\n  }\n  return object instanceof FirebaseError ? provider.credentialFromError(object) : provider.credentialFromResult(object);\n}\nfunction convertCredential(auth, credentialPromise) {\n  return credentialPromise.catch(e => {\n    if (e instanceof FirebaseError) {\n      attachExtraErrorFields(auth, e);\n    }\n    throw e;\n  }).then(credential => {\n    const operationType = credential.operationType;\n    const user = credential.user;\n    return {\n      operationType,\n      credential: credentialFromResponse(credential),\n      additionalUserInfo: exp.getAdditionalUserInfo(credential),\n      user: User.getOrCreate(user)\n    };\n  });\n}\nfunction convertConfirmationResult(_x2, _x3) {\n  return _convertConfirmationResult.apply(this, arguments);\n}\nfunction _convertConfirmationResult() {\n  _convertConfirmationResult = _asyncToGenerator(function* (auth, confirmationResultPromise) {\n    const confirmationResultExp = yield confirmationResultPromise;\n    return {\n      verificationId: confirmationResultExp.verificationId,\n      confirm: verificationCode => convertCredential(auth, confirmationResultExp.confirm(verificationCode))\n    };\n  });\n  return _convertConfirmationResult.apply(this, arguments);\n}\nclass MultiFactorResolver {\n  constructor(auth, resolver) {\n    this.resolver = resolver;\n    this.auth = wrapped(auth);\n  }\n  get session() {\n    return this.resolver.session;\n  }\n  get hints() {\n    return this.resolver.hints;\n  }\n  resolveSignIn(assertion) {\n    return convertCredential(unwrap(this.auth), this.resolver.resolveSignIn(assertion));\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass User {\n  constructor(_delegate) {\n    this._delegate = _delegate;\n    this.multiFactor = exp.multiFactor(_delegate);\n  }\n  static getOrCreate(user) {\n    if (!User.USER_MAP.has(user)) {\n      User.USER_MAP.set(user, new User(user));\n    }\n    return User.USER_MAP.get(user);\n  }\n  delete() {\n    return this._delegate.delete();\n  }\n  reload() {\n    return this._delegate.reload();\n  }\n  toJSON() {\n    return this._delegate.toJSON();\n  }\n  getIdTokenResult(forceRefresh) {\n    return this._delegate.getIdTokenResult(forceRefresh);\n  }\n  getIdToken(forceRefresh) {\n    return this._delegate.getIdToken(forceRefresh);\n  }\n  linkAndRetrieveDataWithCredential(credential) {\n    return this.linkWithCredential(credential);\n  }\n  linkWithCredential(credential) {\n    var _this5 = this;\n    return _asyncToGenerator(function* () {\n      return convertCredential(_this5.auth, exp.linkWithCredential(_this5._delegate, credential));\n    })();\n  }\n  linkWithPhoneNumber(phoneNumber, applicationVerifier) {\n    var _this6 = this;\n    return _asyncToGenerator(function* () {\n      return convertConfirmationResult(_this6.auth, exp.linkWithPhoneNumber(_this6._delegate, phoneNumber, applicationVerifier));\n    })();\n  }\n  linkWithPopup(provider) {\n    var _this7 = this;\n    return _asyncToGenerator(function* () {\n      return convertCredential(_this7.auth, exp.linkWithPopup(_this7._delegate, provider, CompatPopupRedirectResolver));\n    })();\n  }\n  linkWithRedirect(provider) {\n    var _this8 = this;\n    return _asyncToGenerator(function* () {\n      yield _savePersistenceForRedirect(exp._castAuth(_this8.auth));\n      return exp.linkWithRedirect(_this8._delegate, provider, CompatPopupRedirectResolver);\n    })();\n  }\n  reauthenticateAndRetrieveDataWithCredential(credential) {\n    return this.reauthenticateWithCredential(credential);\n  }\n  reauthenticateWithCredential(credential) {\n    var _this9 = this;\n    return _asyncToGenerator(function* () {\n      return convertCredential(_this9.auth, exp.reauthenticateWithCredential(_this9._delegate, credential));\n    })();\n  }\n  reauthenticateWithPhoneNumber(phoneNumber, applicationVerifier) {\n    return convertConfirmationResult(this.auth, exp.reauthenticateWithPhoneNumber(this._delegate, phoneNumber, applicationVerifier));\n  }\n  reauthenticateWithPopup(provider) {\n    return convertCredential(this.auth, exp.reauthenticateWithPopup(this._delegate, provider, CompatPopupRedirectResolver));\n  }\n  reauthenticateWithRedirect(provider) {\n    var _this0 = this;\n    return _asyncToGenerator(function* () {\n      yield _savePersistenceForRedirect(exp._castAuth(_this0.auth));\n      return exp.reauthenticateWithRedirect(_this0._delegate, provider, CompatPopupRedirectResolver);\n    })();\n  }\n  sendEmailVerification(actionCodeSettings) {\n    return exp.sendEmailVerification(this._delegate, actionCodeSettings);\n  }\n  unlink(providerId) {\n    var _this1 = this;\n    return _asyncToGenerator(function* () {\n      yield exp.unlink(_this1._delegate, providerId);\n      return _this1;\n    })();\n  }\n  updateEmail(newEmail) {\n    return exp.updateEmail(this._delegate, newEmail);\n  }\n  updatePassword(newPassword) {\n    return exp.updatePassword(this._delegate, newPassword);\n  }\n  updatePhoneNumber(phoneCredential) {\n    return exp.updatePhoneNumber(this._delegate, phoneCredential);\n  }\n  updateProfile(profile) {\n    return exp.updateProfile(this._delegate, profile);\n  }\n  verifyBeforeUpdateEmail(newEmail, actionCodeSettings) {\n    return exp.verifyBeforeUpdateEmail(this._delegate, newEmail, actionCodeSettings);\n  }\n  get emailVerified() {\n    return this._delegate.emailVerified;\n  }\n  get isAnonymous() {\n    return this._delegate.isAnonymous;\n  }\n  get metadata() {\n    return this._delegate.metadata;\n  }\n  get phoneNumber() {\n    return this._delegate.phoneNumber;\n  }\n  get providerData() {\n    return this._delegate.providerData;\n  }\n  get refreshToken() {\n    return this._delegate.refreshToken;\n  }\n  get tenantId() {\n    return this._delegate.tenantId;\n  }\n  get displayName() {\n    return this._delegate.displayName;\n  }\n  get email() {\n    return this._delegate.email;\n  }\n  get photoURL() {\n    return this._delegate.photoURL;\n  }\n  get providerId() {\n    return this._delegate.providerId;\n  }\n  get uid() {\n    return this._delegate.uid;\n  }\n  get auth() {\n    return this._delegate.auth;\n  }\n}\n// Maintain a map so that there's always a 1:1 mapping between new User and\n// legacy compat users\nUser.USER_MAP = new WeakMap();\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst _assert$1 = exp._assert;\nclass Auth {\n  constructor(app, provider) {\n    this.app = app;\n    if (provider.isInitialized()) {\n      this._delegate = provider.getImmediate();\n      this.linkUnderlyingAuth();\n      return;\n    }\n    const {\n      apiKey\n    } = app.options;\n    // TODO: platform needs to be determined using heuristics\n    _assert$1(apiKey, \"invalid-api-key\" /* exp.AuthErrorCode.INVALID_API_KEY */, {\n      appName: app.name\n    });\n    // TODO: platform needs to be determined using heuristics\n    _assert$1(apiKey, \"invalid-api-key\" /* exp.AuthErrorCode.INVALID_API_KEY */, {\n      appName: app.name\n    });\n    // Only use a popup/redirect resolver in browser environments\n    const resolver = typeof window !== 'undefined' ? CompatPopupRedirectResolver : undefined;\n    this._delegate = provider.initialize({\n      options: {\n        persistence: buildPersistenceHierarchy(apiKey, app.name),\n        popupRedirectResolver: resolver\n      }\n    });\n    this._delegate._updateErrorMap(exp.debugErrorMap);\n    this.linkUnderlyingAuth();\n  }\n  get emulatorConfig() {\n    return this._delegate.emulatorConfig;\n  }\n  get currentUser() {\n    if (!this._delegate.currentUser) {\n      return null;\n    }\n    return User.getOrCreate(this._delegate.currentUser);\n  }\n  get languageCode() {\n    return this._delegate.languageCode;\n  }\n  set languageCode(languageCode) {\n    this._delegate.languageCode = languageCode;\n  }\n  get settings() {\n    return this._delegate.settings;\n  }\n  get tenantId() {\n    return this._delegate.tenantId;\n  }\n  set tenantId(tid) {\n    this._delegate.tenantId = tid;\n  }\n  useDeviceLanguage() {\n    this._delegate.useDeviceLanguage();\n  }\n  signOut() {\n    return this._delegate.signOut();\n  }\n  useEmulator(url, options) {\n    exp.connectAuthEmulator(this._delegate, url, options);\n  }\n  applyActionCode(code) {\n    return exp.applyActionCode(this._delegate, code);\n  }\n  checkActionCode(code) {\n    return exp.checkActionCode(this._delegate, code);\n  }\n  confirmPasswordReset(code, newPassword) {\n    return exp.confirmPasswordReset(this._delegate, code, newPassword);\n  }\n  createUserWithEmailAndPassword(email, password) {\n    var _this10 = this;\n    return _asyncToGenerator(function* () {\n      return convertCredential(_this10._delegate, exp.createUserWithEmailAndPassword(_this10._delegate, email, password));\n    })();\n  }\n  fetchProvidersForEmail(email) {\n    return this.fetchSignInMethodsForEmail(email);\n  }\n  fetchSignInMethodsForEmail(email) {\n    return exp.fetchSignInMethodsForEmail(this._delegate, email);\n  }\n  isSignInWithEmailLink(emailLink) {\n    return exp.isSignInWithEmailLink(this._delegate, emailLink);\n  }\n  getRedirectResult() {\n    var _this11 = this;\n    return _asyncToGenerator(function* () {\n      _assert$1(_isPopupRedirectSupported(), _this11._delegate, \"operation-not-supported-in-this-environment\" /* exp.AuthErrorCode.OPERATION_NOT_SUPPORTED */);\n      const credential = yield exp.getRedirectResult(_this11._delegate, CompatPopupRedirectResolver);\n      if (!credential) {\n        return {\n          credential: null,\n          user: null\n        };\n      }\n      return convertCredential(_this11._delegate, Promise.resolve(credential));\n    })();\n  }\n  // This function should only be called by frameworks (e.g. FirebaseUI-web) to log their usage.\n  // It is not intended for direct use by developer apps. NO jsdoc here to intentionally leave it\n  // out of autogenerated documentation pages to reduce accidental misuse.\n  addFrameworkForLogging(framework) {\n    exp.addFrameworkForLogging(this._delegate, framework);\n  }\n  onAuthStateChanged(nextOrObserver, errorFn, completed) {\n    const {\n      next,\n      error,\n      complete\n    } = wrapObservers(nextOrObserver, errorFn, completed);\n    return this._delegate.onAuthStateChanged(next, error, complete);\n  }\n  onIdTokenChanged(nextOrObserver, errorFn, completed) {\n    const {\n      next,\n      error,\n      complete\n    } = wrapObservers(nextOrObserver, errorFn, completed);\n    return this._delegate.onIdTokenChanged(next, error, complete);\n  }\n  sendSignInLinkToEmail(email, actionCodeSettings) {\n    return exp.sendSignInLinkToEmail(this._delegate, email, actionCodeSettings);\n  }\n  sendPasswordResetEmail(email, actionCodeSettings) {\n    return exp.sendPasswordResetEmail(this._delegate, email, actionCodeSettings || undefined);\n  }\n  setPersistence(persistence) {\n    var _this12 = this;\n    return _asyncToGenerator(function* () {\n      _validatePersistenceArgument(_this12._delegate, persistence);\n      let converted;\n      switch (persistence) {\n        case Persistence.SESSION:\n          converted = exp.browserSessionPersistence;\n          break;\n        case Persistence.LOCAL:\n          // Not using isIndexedDBAvailable() since it only checks if indexedDB is defined.\n          const isIndexedDBFullySupported = yield exp._getInstance(exp.indexedDBLocalPersistence)._isAvailable();\n          converted = isIndexedDBFullySupported ? exp.indexedDBLocalPersistence : exp.browserLocalPersistence;\n          break;\n        case Persistence.NONE:\n          converted = exp.inMemoryPersistence;\n          break;\n        default:\n          return exp._fail(\"argument-error\" /* exp.AuthErrorCode.ARGUMENT_ERROR */, {\n            appName: _this12._delegate.name\n          });\n      }\n      return _this12._delegate.setPersistence(converted);\n    })();\n  }\n  signInAndRetrieveDataWithCredential(credential) {\n    return this.signInWithCredential(credential);\n  }\n  signInAnonymously() {\n    return convertCredential(this._delegate, exp.signInAnonymously(this._delegate));\n  }\n  signInWithCredential(credential) {\n    return convertCredential(this._delegate, exp.signInWithCredential(this._delegate, credential));\n  }\n  signInWithCustomToken(token) {\n    return convertCredential(this._delegate, exp.signInWithCustomToken(this._delegate, token));\n  }\n  signInWithEmailAndPassword(email, password) {\n    return convertCredential(this._delegate, exp.signInWithEmailAndPassword(this._delegate, email, password));\n  }\n  signInWithEmailLink(email, emailLink) {\n    return convertCredential(this._delegate, exp.signInWithEmailLink(this._delegate, email, emailLink));\n  }\n  signInWithPhoneNumber(phoneNumber, applicationVerifier) {\n    return convertConfirmationResult(this._delegate, exp.signInWithPhoneNumber(this._delegate, phoneNumber, applicationVerifier));\n  }\n  signInWithPopup(provider) {\n    var _this13 = this;\n    return _asyncToGenerator(function* () {\n      _assert$1(_isPopupRedirectSupported(), _this13._delegate, \"operation-not-supported-in-this-environment\" /* exp.AuthErrorCode.OPERATION_NOT_SUPPORTED */);\n      return convertCredential(_this13._delegate, exp.signInWithPopup(_this13._delegate, provider, CompatPopupRedirectResolver));\n    })();\n  }\n  signInWithRedirect(provider) {\n    var _this14 = this;\n    return _asyncToGenerator(function* () {\n      _assert$1(_isPopupRedirectSupported(), _this14._delegate, \"operation-not-supported-in-this-environment\" /* exp.AuthErrorCode.OPERATION_NOT_SUPPORTED */);\n      yield _savePersistenceForRedirect(_this14._delegate);\n      return exp.signInWithRedirect(_this14._delegate, provider, CompatPopupRedirectResolver);\n    })();\n  }\n  updateCurrentUser(user) {\n    // remove ts-ignore once overloads are defined for exp functions to accept compat objects\n    // @ts-ignore\n    return this._delegate.updateCurrentUser(user);\n  }\n  verifyPasswordResetCode(code) {\n    return exp.verifyPasswordResetCode(this._delegate, code);\n  }\n  unwrap() {\n    return this._delegate;\n  }\n  _delete() {\n    return this._delegate._delete();\n  }\n  linkUnderlyingAuth() {\n    this._delegate.wrapped = () => this;\n  }\n}\nAuth.Persistence = Persistence;\nfunction wrapObservers(nextOrObserver, error, complete) {\n  let next = nextOrObserver;\n  if (typeof nextOrObserver !== 'function') {\n    ({\n      next,\n      error,\n      complete\n    } = nextOrObserver);\n  }\n  // We know 'next' is now a function\n  const oldNext = next;\n  const newNext = user => oldNext(user && User.getOrCreate(user));\n  return {\n    next: newNext,\n    error: error,\n    complete\n  };\n}\nfunction buildPersistenceHierarchy(apiKey, appName) {\n  // Note this is slightly different behavior: in this case, the stored\n  // persistence is checked *first* rather than last. This is because we want\n  // to prefer stored persistence type in the hierarchy. This is an empty\n  // array if window is not available or there is no pending redirect\n  const persistences = _getPersistencesFromRedirect(apiKey, appName);\n  // If \"self\" is available, add indexedDB\n  if (typeof self !== 'undefined' && !persistences.includes(exp.indexedDBLocalPersistence)) {\n    persistences.push(exp.indexedDBLocalPersistence);\n  }\n  // If \"window\" is available, add HTML Storage persistences\n  if (typeof window !== 'undefined') {\n    for (const persistence of [exp.browserLocalPersistence, exp.browserSessionPersistence]) {\n      if (!persistences.includes(persistence)) {\n        persistences.push(persistence);\n      }\n    }\n  }\n  // Add in-memory as a final fallback\n  if (!persistences.includes(exp.inMemoryPersistence)) {\n    persistences.push(exp.inMemoryPersistence);\n  }\n  return persistences;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass PhoneAuthProvider {\n  constructor() {\n    this.providerId = 'phone';\n    // TODO: remove ts-ignore when moving types from auth-types to auth-compat\n    // @ts-ignore\n    this._delegate = new exp.PhoneAuthProvider(unwrap(firebase.auth()));\n  }\n  static credential(verificationId, verificationCode) {\n    return exp.PhoneAuthProvider.credential(verificationId, verificationCode);\n  }\n  verifyPhoneNumber(phoneInfoOptions, applicationVerifier) {\n    return this._delegate.verifyPhoneNumber(\n    // The implementation matches but the types are subtly incompatible\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    phoneInfoOptions, applicationVerifier);\n  }\n  unwrap() {\n    return this._delegate;\n  }\n}\nPhoneAuthProvider.PHONE_SIGN_IN_METHOD = exp.PhoneAuthProvider.PHONE_SIGN_IN_METHOD;\nPhoneAuthProvider.PROVIDER_ID = exp.PhoneAuthProvider.PROVIDER_ID;\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst _assert = exp._assert;\nclass RecaptchaVerifier {\n  constructor(container, parameters, app = firebase.app()) {\n    var _a;\n    // API key is required for web client RPC calls.\n    _assert((_a = app.options) === null || _a === void 0 ? void 0 : _a.apiKey, \"invalid-api-key\" /* exp.AuthErrorCode.INVALID_API_KEY */, {\n      appName: app.name\n    });\n    this._delegate = new exp.RecaptchaVerifier(container,\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    parameters,\n    // TODO: remove ts-ignore when moving types from auth-types to auth-compat\n    // @ts-ignore\n    app.auth());\n    this.type = this._delegate.type;\n  }\n  clear() {\n    this._delegate.clear();\n  }\n  render() {\n    return this._delegate.render();\n  }\n  verify() {\n    return this._delegate.verify();\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst AUTH_TYPE = 'auth-compat';\n// Create auth components to register with firebase.\n// Provides Auth public APIs.\nfunction registerAuthCompat(instance) {\n  instance.INTERNAL.registerComponent(new Component(AUTH_TYPE, container => {\n    // getImmediate for FirebaseApp will always succeed\n    const app = container.getProvider('app-compat').getImmediate();\n    const authProvider = container.getProvider('auth');\n    return new Auth(app, authProvider);\n  }, \"PUBLIC\" /* ComponentType.PUBLIC */).setServiceProps({\n    ActionCodeInfo: {\n      Operation: {\n        EMAIL_SIGNIN: exp.ActionCodeOperation.EMAIL_SIGNIN,\n        PASSWORD_RESET: exp.ActionCodeOperation.PASSWORD_RESET,\n        RECOVER_EMAIL: exp.ActionCodeOperation.RECOVER_EMAIL,\n        REVERT_SECOND_FACTOR_ADDITION: exp.ActionCodeOperation.REVERT_SECOND_FACTOR_ADDITION,\n        VERIFY_AND_CHANGE_EMAIL: exp.ActionCodeOperation.VERIFY_AND_CHANGE_EMAIL,\n        VERIFY_EMAIL: exp.ActionCodeOperation.VERIFY_EMAIL\n      }\n    },\n    EmailAuthProvider: exp.EmailAuthProvider,\n    FacebookAuthProvider: exp.FacebookAuthProvider,\n    GithubAuthProvider: exp.GithubAuthProvider,\n    GoogleAuthProvider: exp.GoogleAuthProvider,\n    OAuthProvider: exp.OAuthProvider,\n    SAMLAuthProvider: exp.SAMLAuthProvider,\n    PhoneAuthProvider: PhoneAuthProvider,\n    PhoneMultiFactorGenerator: exp.PhoneMultiFactorGenerator,\n    RecaptchaVerifier: RecaptchaVerifier,\n    TwitterAuthProvider: exp.TwitterAuthProvider,\n    Auth,\n    AuthCredential: exp.AuthCredential,\n    Error: FirebaseError\n  }).setInstantiationMode(\"LAZY\" /* InstantiationMode.LAZY */).setMultipleInstances(false));\n  instance.registerVersion(name, version);\n}\nregisterAuthCompat(firebase);", "map": {"version": 3, "names": ["firebase", "exp", "Component", "isBrowserExtension", "getUA", "isReactNative", "isNode", "isIndexedDBAvailable", "isIE", "FirebaseError", "name", "version", "CORDOVA_ONDEVICEREADY_TIMEOUT_MS", "_getCurrentScheme", "_a", "self", "location", "protocol", "_isHttpOrHttps", "_isAndroidOrIosCordovaScheme", "ua", "toLowerCase", "match", "_isNativeEnvironment", "_isIe11", "document", "documentMode", "_isEdge", "test", "_isLocalStorageNotSynchronized", "_isWebStorageSupported", "storage", "localStorage", "key", "_generateEventId", "e", "_isWorker", "global", "_isPopupRedirectSupported", "_is<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_is<PERSON><PERSON><PERSON>", "_isCordova2", "apply", "arguments", "_asyncToGenerator", "Promise", "resolve", "timeoutId", "setTimeout", "addEventListener", "clearTimeout", "_getSelfWindow", "window", "Persistence", "LOCAL", "NONE", "SESSION", "_assert$3", "_assert", "PERSISTENCE_KEY", "_validatePersistenceArgument", "auth", "persistence", "Object", "values", "includes", "_savePersistenceForRedirect", "_x", "_savePersistenceForRedirect2", "_initializationPromise", "session", "getSessionStorageIfAvailable", "_persistenceKeyName", "config", "<PERSON><PERSON><PERSON><PERSON>", "setItem", "_getPersistence", "_getPersistencesFromRedirect", "appName", "getItem", "inMemoryPersistence", "indexedDBLocalPersistence", "browserSessionPersistence", "sessionStorage", "_assert$2", "CompatPopupRedirectResolver", "constructor", "browserResolver", "_getInstance", "browserPopupRedirectResolver", "cordovaResolver", "cordovaPopupRedirectResolver", "underlyingResolver", "_redirectPersistence", "_completeRedirectFn", "_getRedirectResult", "_overrideRedirectResult", "_initialize", "_this", "selectUnderlyingResolver", "assertedUnderlyingResolver", "_openPopup", "provider", "authType", "eventId", "_this2", "_openRedirect", "_this3", "_isIframeWebStorageSupported", "cb", "_originValidation", "_shouldInitProactively", "_this4", "<PERSON><PERSON><PERSON><PERSON>", "unwrap", "object", "wrapped", "credentialFromResponse", "userCredential", "credentialFromObject", "attachExtraErrorFields", "response", "customData", "_tokenResponse", "code", "mfaErr", "resolver", "MultiFactorResolver", "getMultiFactorResolver", "credential", "credErr", "tenantId", "undefined", "email", "phoneNumber", "PhoneAuthProvider", "credentialFromResult", "providerId", "ProviderId", "PASSWORD", "GOOGLE", "GoogleAuthProvider", "FACEBOOK", "FacebookAuthProvider", "GITHUB", "GithubAuth<PERSON>rovider", "TWITTER", "TwitterAuthProvider", "oauthIdToken", "oauthAccessToken", "oauthTokenSecret", "pendingToken", "nonce", "startsWith", "SAMLAuthCredential", "_create", "OAuthCredential", "_fromParams", "signInMethod", "idToken", "accessToken", "OAuth<PERSON><PERSON><PERSON>", "rawNonce", "credentialFromError", "convertCredential", "credentialPromise", "catch", "then", "operationType", "user", "additionalUserInfo", "getAdditionalUserInfo", "User", "getOrCreate", "convertConfirmationResult", "_x2", "_x3", "_convertConfirmationResult", "confirmationResultPromise", "confirmationResultExp", "verificationId", "confirm", "verificationCode", "hints", "resolveSignIn", "assertion", "_delegate", "multiFactor", "USER_MAP", "has", "set", "get", "delete", "reload", "toJSON", "getIdTokenResult", "forceRefresh", "getIdToken", "linkAndRetrieveDataWithCredential", "linkWithCredential", "_this5", "linkWithPhoneNumber", "applicationVerifier", "_this6", "linkWithPopup", "_this7", "linkWithRedirect", "_this8", "_castAuth", "reauthenticateAndRetrieveDataWithCredential", "reauthenticateWithCredential", "_this9", "reauthenticateWithPhoneNumber", "reauthenticateWithPopup", "reauthenticateWithRedirect", "_this0", "sendEmailVerification", "actionCodeSettings", "unlink", "_this1", "updateEmail", "newEmail", "updatePassword", "newPassword", "updatePhoneNumber", "phoneCredential", "updateProfile", "profile", "verifyBeforeUpdateEmail", "emailVerified", "isAnonymous", "metadata", "providerData", "refreshToken", "displayName", "photoURL", "uid", "WeakMap", "_assert$1", "<PERSON><PERSON>", "app", "isInitialized", "getImmediate", "linkUnderlyingAuth", "options", "initialize", "buildPersistenceHierarchy", "popupRedirectResolver", "_updateErrorMap", "debugErrorMap", "emulatorConfig", "currentUser", "languageCode", "settings", "tid", "useDeviceLanguage", "signOut", "useEmulator", "url", "connectAuthEmulator", "applyActionCode", "checkActionCode", "confirmPasswordReset", "createUserWithEmailAndPassword", "password", "_this10", "fetchProvidersForEmail", "fetchSignInMethodsForEmail", "isSignInWithEmailLink", "emailLink", "getRedirectResult", "_this11", "addFrameworkForLogging", "framework", "onAuthStateChanged", "nextOrObserver", "errorFn", "completed", "next", "error", "complete", "wrapObservers", "onIdTokenChanged", "sendSignInLinkToEmail", "sendPasswordResetEmail", "setPersistence", "_this12", "converted", "isIndexedDBFullySupported", "_isAvailable", "browserLocalPersistence", "_fail", "signInAndRetrieveDataWithCredential", "signInWithCredential", "signInAnonymously", "signInWithCustomToken", "token", "signInWithEmailAndPassword", "signInWithEmailLink", "signInWithPhoneNumber", "signInWithPopup", "_this13", "signInWithRedirect", "_this14", "updateCurrentUser", "verifyPasswordResetCode", "_delete", "oldNext", "newNext", "persistences", "push", "verifyPhoneNumber", "phoneInfoOptions", "PHONE_SIGN_IN_METHOD", "PROVIDER_ID", "RecaptchaVerifier", "container", "parameters", "type", "clear", "render", "verify", "AUTH_TYPE", "registerAuthCompat", "instance", "INTERNAL", "registerComponent", "get<PERSON><PERSON><PERSON>", "authProvider", "setServiceProps", "ActionCodeInfo", "Operation", "EMAIL_SIGNIN", "ActionCodeOperation", "PASSWORD_RESET", "RECOVER_EMAIL", "REVERT_SECOND_FACTOR_ADDITION", "VERIFY_AND_CHANGE_EMAIL", "VERIFY_EMAIL", "EmailAuthProvider", "SAMLAuthProvider", "PhoneMultiFactorGenerator", "AuthCredential", "Error", "setInstantiationMode", "setMultipleInstances", "registerVersion"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/fire/node_modules/@firebase/auth-compat/dist/index.esm2017.js"], "sourcesContent": ["import firebase from '@firebase/app-compat';\nimport * as exp from '@firebase/auth/internal';\nimport { Component } from '@firebase/component';\nimport { isBrowserExtension, getUA, isReactNative, isNode, isIndexedDBAvailable, isIE, FirebaseError } from '@firebase/util';\n\nvar name = \"@firebase/auth-compat\";\nvar version = \"0.4.2\";\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst CORDOVA_ONDEVICEREADY_TIMEOUT_MS = 1000;\r\nfunction _getCurrentScheme() {\r\n    var _a;\r\n    return ((_a = self === null || self === void 0 ? void 0 : self.location) === null || _a === void 0 ? void 0 : _a.protocol) || null;\r\n}\r\n/**\r\n * @return {boolean} Whether the current environment is http or https.\r\n */\r\nfunction _isHttpOrHttps() {\r\n    return _getCurrentScheme() === 'http:' || _getCurrentScheme() === 'https:';\r\n}\r\n/**\r\n * @param {?string=} ua The user agent.\r\n * @return {boolean} Whether the app is rendered in a mobile iOS or Android\r\n *     Cordova environment.\r\n */\r\nfunction _isAndroidOrIosCordovaScheme(ua = getUA()) {\r\n    return !!((_getCurrentScheme() === 'file:' ||\r\n        _getCurrentScheme() === 'ionic:' ||\r\n        _getCurrentScheme() === 'capacitor:') &&\r\n        ua.toLowerCase().match(/iphone|ipad|ipod|android/));\r\n}\r\n/**\r\n * @return {boolean} Whether the environment is a native environment, where\r\n *     CORS checks do not apply.\r\n */\r\nfunction _isNativeEnvironment() {\r\n    return isReactNative() || isNode();\r\n}\r\n/**\r\n * Checks whether the user agent is IE11.\r\n * @return {boolean} True if it is IE11.\r\n */\r\nfunction _isIe11() {\r\n    return isIE() && (document === null || document === void 0 ? void 0 : document.documentMode) === 11;\r\n}\r\n/**\r\n * Checks whether the user agent is Edge.\r\n * @param {string} userAgent The browser user agent string.\r\n * @return {boolean} True if it is Edge.\r\n */\r\nfunction _isEdge(ua = getUA()) {\r\n    return /Edge\\/\\d+/.test(ua);\r\n}\r\n/**\r\n * @param {?string=} opt_userAgent The navigator user agent.\r\n * @return {boolean} Whether local storage is not synchronized between an iframe\r\n *     and a popup of the same domain.\r\n */\r\nfunction _isLocalStorageNotSynchronized(ua = getUA()) {\r\n    return _isIe11() || _isEdge(ua);\r\n}\r\n/** @return {boolean} Whether web storage is supported. */\r\nfunction _isWebStorageSupported() {\r\n    try {\r\n        const storage = self.localStorage;\r\n        const key = exp._generateEventId();\r\n        if (storage) {\r\n            // setItem will throw an exception if we cannot access WebStorage (e.g.,\r\n            // Safari in private mode).\r\n            storage['setItem'](key, '1');\r\n            storage['removeItem'](key);\r\n            // For browsers where iframe web storage does not synchronize with a popup\r\n            // of the same domain, indexedDB is used for persistent storage. These\r\n            // browsers include IE11 and Edge.\r\n            // Make sure it is supported (IE11 and Edge private mode does not support\r\n            // that).\r\n            if (_isLocalStorageNotSynchronized()) {\r\n                // In such browsers, if indexedDB is not supported, an iframe cannot be\r\n                // notified of the popup sign in result.\r\n                return isIndexedDBAvailable();\r\n            }\r\n            return true;\r\n        }\r\n    }\r\n    catch (e) {\r\n        // localStorage is not available from a worker. Test availability of\r\n        // indexedDB.\r\n        return _isWorker() && isIndexedDBAvailable();\r\n    }\r\n    return false;\r\n}\r\n/**\r\n * @param {?Object=} global The optional global scope.\r\n * @return {boolean} Whether current environment is a worker.\r\n */\r\nfunction _isWorker() {\r\n    // WorkerGlobalScope only defined in worker environment.\r\n    return (typeof global !== 'undefined' &&\r\n        'WorkerGlobalScope' in global &&\r\n        'importScripts' in global);\r\n}\r\nfunction _isPopupRedirectSupported() {\r\n    return ((_isHttpOrHttps() ||\r\n        isBrowserExtension() ||\r\n        _isAndroidOrIosCordovaScheme()) &&\r\n        // React Native with remote debugging reports its location.protocol as\r\n        // http.\r\n        !_isNativeEnvironment() &&\r\n        // Local storage has to be supported for browser popup and redirect\r\n        // operations to work.\r\n        _isWebStorageSupported() &&\r\n        // DOM, popups and redirects are not supported within a worker.\r\n        !_isWorker());\r\n}\r\n/** Quick check that indicates the platform *may* be Cordova */\r\nfunction _isLikelyCordova() {\r\n    return _isAndroidOrIosCordovaScheme() && typeof document !== 'undefined';\r\n}\r\nasync function _isCordova() {\r\n    if (!_isLikelyCordova()) {\r\n        return false;\r\n    }\r\n    return new Promise(resolve => {\r\n        const timeoutId = setTimeout(() => {\r\n            // We've waited long enough; the telltale Cordova event didn't happen\r\n            resolve(false);\r\n        }, CORDOVA_ONDEVICEREADY_TIMEOUT_MS);\r\n        document.addEventListener('deviceready', () => {\r\n            clearTimeout(timeoutId);\r\n            resolve(true);\r\n        });\r\n    });\r\n}\r\nfunction _getSelfWindow() {\r\n    return typeof window !== 'undefined' ? window : null;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst Persistence = {\r\n    LOCAL: 'local',\r\n    NONE: 'none',\r\n    SESSION: 'session'\r\n};\r\nconst _assert$3 = exp._assert;\r\nconst PERSISTENCE_KEY = 'persistence';\r\n/**\r\n * Validates that an argument is a valid persistence value. If an invalid type\r\n * is specified, an error is thrown synchronously.\r\n */\r\nfunction _validatePersistenceArgument(auth, persistence) {\r\n    _assert$3(Object.values(Persistence).includes(persistence), auth, \"invalid-persistence-type\" /* exp.AuthErrorCode.INVALID_PERSISTENCE */);\r\n    // Validate if the specified type is supported in the current environment.\r\n    if (isReactNative()) {\r\n        // This is only supported in a browser.\r\n        _assert$3(persistence !== Persistence.SESSION, auth, \"unsupported-persistence-type\" /* exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE */);\r\n        return;\r\n    }\r\n    if (isNode()) {\r\n        // Only none is supported in Node.js.\r\n        _assert$3(persistence === Persistence.NONE, auth, \"unsupported-persistence-type\" /* exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE */);\r\n        return;\r\n    }\r\n    if (_isWorker()) {\r\n        // In a worker environment, either LOCAL or NONE are supported.\r\n        // If indexedDB not supported and LOCAL provided, throw an error\r\n        _assert$3(persistence === Persistence.NONE ||\r\n            (persistence === Persistence.LOCAL && isIndexedDBAvailable()), auth, \"unsupported-persistence-type\" /* exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE */);\r\n        return;\r\n    }\r\n    // This is restricted by what the browser supports.\r\n    _assert$3(persistence === Persistence.NONE || _isWebStorageSupported(), auth, \"unsupported-persistence-type\" /* exp.AuthErrorCode.UNSUPPORTED_PERSISTENCE */);\r\n}\r\nasync function _savePersistenceForRedirect(auth) {\r\n    await auth._initializationPromise;\r\n    const session = getSessionStorageIfAvailable();\r\n    const key = exp._persistenceKeyName(PERSISTENCE_KEY, auth.config.apiKey, auth.name);\r\n    if (session) {\r\n        session.setItem(key, auth._getPersistence());\r\n    }\r\n}\r\nfunction _getPersistencesFromRedirect(apiKey, appName) {\r\n    const session = getSessionStorageIfAvailable();\r\n    if (!session) {\r\n        return [];\r\n    }\r\n    const key = exp._persistenceKeyName(PERSISTENCE_KEY, apiKey, appName);\r\n    const persistence = session.getItem(key);\r\n    switch (persistence) {\r\n        case Persistence.NONE:\r\n            return [exp.inMemoryPersistence];\r\n        case Persistence.LOCAL:\r\n            return [exp.indexedDBLocalPersistence, exp.browserSessionPersistence];\r\n        case Persistence.SESSION:\r\n            return [exp.browserSessionPersistence];\r\n        default:\r\n            return [];\r\n    }\r\n}\r\n/** Returns session storage, or null if the property access errors */\r\nfunction getSessionStorageIfAvailable() {\r\n    var _a;\r\n    try {\r\n        return ((_a = _getSelfWindow()) === null || _a === void 0 ? void 0 : _a.sessionStorage) || null;\r\n    }\r\n    catch (e) {\r\n        return null;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst _assert$2 = exp._assert;\r\n/** Platform-agnostic popup-redirect resolver */\r\nclass CompatPopupRedirectResolver {\r\n    constructor() {\r\n        // Create both resolvers for dynamic resolution later\r\n        this.browserResolver = exp._getInstance(exp.browserPopupRedirectResolver);\r\n        this.cordovaResolver = exp._getInstance(exp.cordovaPopupRedirectResolver);\r\n        // The actual resolver in use: either browserResolver or cordovaResolver.\r\n        this.underlyingResolver = null;\r\n        this._redirectPersistence = exp.browserSessionPersistence;\r\n        this._completeRedirectFn = exp._getRedirectResult;\r\n        this._overrideRedirectResult = exp._overrideRedirectResult;\r\n    }\r\n    async _initialize(auth) {\r\n        await this.selectUnderlyingResolver();\r\n        return this.assertedUnderlyingResolver._initialize(auth);\r\n    }\r\n    async _openPopup(auth, provider, authType, eventId) {\r\n        await this.selectUnderlyingResolver();\r\n        return this.assertedUnderlyingResolver._openPopup(auth, provider, authType, eventId);\r\n    }\r\n    async _openRedirect(auth, provider, authType, eventId) {\r\n        await this.selectUnderlyingResolver();\r\n        return this.assertedUnderlyingResolver._openRedirect(auth, provider, authType, eventId);\r\n    }\r\n    _isIframeWebStorageSupported(auth, cb) {\r\n        this.assertedUnderlyingResolver._isIframeWebStorageSupported(auth, cb);\r\n    }\r\n    _originValidation(auth) {\r\n        return this.assertedUnderlyingResolver._originValidation(auth);\r\n    }\r\n    get _shouldInitProactively() {\r\n        return _isLikelyCordova() || this.browserResolver._shouldInitProactively;\r\n    }\r\n    get assertedUnderlyingResolver() {\r\n        _assert$2(this.underlyingResolver, \"internal-error\" /* exp.AuthErrorCode.INTERNAL_ERROR */);\r\n        return this.underlyingResolver;\r\n    }\r\n    async selectUnderlyingResolver() {\r\n        if (this.underlyingResolver) {\r\n            return;\r\n        }\r\n        // We haven't yet determined whether or not we're in Cordova; go ahead\r\n        // and determine that state now.\r\n        const isCordova = await _isCordova();\r\n        this.underlyingResolver = isCordova\r\n            ? this.cordovaResolver\r\n            : this.browserResolver;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction unwrap(object) {\r\n    return object.unwrap();\r\n}\r\nfunction wrapped(object) {\r\n    return object.wrapped();\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction credentialFromResponse(userCredential) {\r\n    return credentialFromObject(userCredential);\r\n}\r\nfunction attachExtraErrorFields(auth, e) {\r\n    var _a;\r\n    // The response contains all fields from the server which may or may not\r\n    // actually match the underlying type\r\n    const response = (_a = e.customData) === null || _a === void 0 ? void 0 : _a._tokenResponse;\r\n    if ((e === null || e === void 0 ? void 0 : e.code) === 'auth/multi-factor-auth-required') {\r\n        const mfaErr = e;\r\n        mfaErr.resolver = new MultiFactorResolver(auth, exp.getMultiFactorResolver(auth, e));\r\n    }\r\n    else if (response) {\r\n        const credential = credentialFromObject(e);\r\n        const credErr = e;\r\n        if (credential) {\r\n            credErr.credential = credential;\r\n            credErr.tenantId = response.tenantId || undefined;\r\n            credErr.email = response.email || undefined;\r\n            credErr.phoneNumber = response.phoneNumber || undefined;\r\n        }\r\n    }\r\n}\r\nfunction credentialFromObject(object) {\r\n    const { _tokenResponse } = (object instanceof FirebaseError ? object.customData : object);\r\n    if (!_tokenResponse) {\r\n        return null;\r\n    }\r\n    // Handle phone Auth credential responses, as they have a different format\r\n    // from other backend responses (i.e. no providerId). This is also only the\r\n    // case for user credentials (does not work for errors).\r\n    if (!(object instanceof FirebaseError)) {\r\n        if ('temporaryProof' in _tokenResponse && 'phoneNumber' in _tokenResponse) {\r\n            return exp.PhoneAuthProvider.credentialFromResult(object);\r\n        }\r\n    }\r\n    const providerId = _tokenResponse.providerId;\r\n    // Email and password is not supported as there is no situation where the\r\n    // server would return the password to the client.\r\n    if (!providerId || providerId === exp.ProviderId.PASSWORD) {\r\n        return null;\r\n    }\r\n    let provider;\r\n    switch (providerId) {\r\n        case exp.ProviderId.GOOGLE:\r\n            provider = exp.GoogleAuthProvider;\r\n            break;\r\n        case exp.ProviderId.FACEBOOK:\r\n            provider = exp.FacebookAuthProvider;\r\n            break;\r\n        case exp.ProviderId.GITHUB:\r\n            provider = exp.GithubAuthProvider;\r\n            break;\r\n        case exp.ProviderId.TWITTER:\r\n            provider = exp.TwitterAuthProvider;\r\n            break;\r\n        default:\r\n            const { oauthIdToken, oauthAccessToken, oauthTokenSecret, pendingToken, nonce } = _tokenResponse;\r\n            if (!oauthAccessToken &&\r\n                !oauthTokenSecret &&\r\n                !oauthIdToken &&\r\n                !pendingToken) {\r\n                return null;\r\n            }\r\n            // TODO(avolkovi): uncomment this and get it working with SAML & OIDC\r\n            if (pendingToken) {\r\n                if (providerId.startsWith('saml.')) {\r\n                    return exp.SAMLAuthCredential._create(providerId, pendingToken);\r\n                }\r\n                else {\r\n                    // OIDC and non-default providers excluding Twitter.\r\n                    return exp.OAuthCredential._fromParams({\r\n                        providerId,\r\n                        signInMethod: providerId,\r\n                        pendingToken,\r\n                        idToken: oauthIdToken,\r\n                        accessToken: oauthAccessToken\r\n                    });\r\n                }\r\n            }\r\n            return new exp.OAuthProvider(providerId).credential({\r\n                idToken: oauthIdToken,\r\n                accessToken: oauthAccessToken,\r\n                rawNonce: nonce\r\n            });\r\n    }\r\n    return object instanceof FirebaseError\r\n        ? provider.credentialFromError(object)\r\n        : provider.credentialFromResult(object);\r\n}\r\nfunction convertCredential(auth, credentialPromise) {\r\n    return credentialPromise\r\n        .catch(e => {\r\n        if (e instanceof FirebaseError) {\r\n            attachExtraErrorFields(auth, e);\r\n        }\r\n        throw e;\r\n    })\r\n        .then(credential => {\r\n        const operationType = credential.operationType;\r\n        const user = credential.user;\r\n        return {\r\n            operationType,\r\n            credential: credentialFromResponse(credential),\r\n            additionalUserInfo: exp.getAdditionalUserInfo(credential),\r\n            user: User.getOrCreate(user)\r\n        };\r\n    });\r\n}\r\nasync function convertConfirmationResult(auth, confirmationResultPromise) {\r\n    const confirmationResultExp = await confirmationResultPromise;\r\n    return {\r\n        verificationId: confirmationResultExp.verificationId,\r\n        confirm: (verificationCode) => convertCredential(auth, confirmationResultExp.confirm(verificationCode))\r\n    };\r\n}\r\nclass MultiFactorResolver {\r\n    constructor(auth, resolver) {\r\n        this.resolver = resolver;\r\n        this.auth = wrapped(auth);\r\n    }\r\n    get session() {\r\n        return this.resolver.session;\r\n    }\r\n    get hints() {\r\n        return this.resolver.hints;\r\n    }\r\n    resolveSignIn(assertion) {\r\n        return convertCredential(unwrap(this.auth), this.resolver.resolveSignIn(assertion));\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass User {\r\n    constructor(_delegate) {\r\n        this._delegate = _delegate;\r\n        this.multiFactor = exp.multiFactor(_delegate);\r\n    }\r\n    static getOrCreate(user) {\r\n        if (!User.USER_MAP.has(user)) {\r\n            User.USER_MAP.set(user, new User(user));\r\n        }\r\n        return User.USER_MAP.get(user);\r\n    }\r\n    delete() {\r\n        return this._delegate.delete();\r\n    }\r\n    reload() {\r\n        return this._delegate.reload();\r\n    }\r\n    toJSON() {\r\n        return this._delegate.toJSON();\r\n    }\r\n    getIdTokenResult(forceRefresh) {\r\n        return this._delegate.getIdTokenResult(forceRefresh);\r\n    }\r\n    getIdToken(forceRefresh) {\r\n        return this._delegate.getIdToken(forceRefresh);\r\n    }\r\n    linkAndRetrieveDataWithCredential(credential) {\r\n        return this.linkWithCredential(credential);\r\n    }\r\n    async linkWithCredential(credential) {\r\n        return convertCredential(this.auth, exp.linkWithCredential(this._delegate, credential));\r\n    }\r\n    async linkWithPhoneNumber(phoneNumber, applicationVerifier) {\r\n        return convertConfirmationResult(this.auth, exp.linkWithPhoneNumber(this._delegate, phoneNumber, applicationVerifier));\r\n    }\r\n    async linkWithPopup(provider) {\r\n        return convertCredential(this.auth, exp.linkWithPopup(this._delegate, provider, CompatPopupRedirectResolver));\r\n    }\r\n    async linkWithRedirect(provider) {\r\n        await _savePersistenceForRedirect(exp._castAuth(this.auth));\r\n        return exp.linkWithRedirect(this._delegate, provider, CompatPopupRedirectResolver);\r\n    }\r\n    reauthenticateAndRetrieveDataWithCredential(credential) {\r\n        return this.reauthenticateWithCredential(credential);\r\n    }\r\n    async reauthenticateWithCredential(credential) {\r\n        return convertCredential(this.auth, exp.reauthenticateWithCredential(this._delegate, credential));\r\n    }\r\n    reauthenticateWithPhoneNumber(phoneNumber, applicationVerifier) {\r\n        return convertConfirmationResult(this.auth, exp.reauthenticateWithPhoneNumber(this._delegate, phoneNumber, applicationVerifier));\r\n    }\r\n    reauthenticateWithPopup(provider) {\r\n        return convertCredential(this.auth, exp.reauthenticateWithPopup(this._delegate, provider, CompatPopupRedirectResolver));\r\n    }\r\n    async reauthenticateWithRedirect(provider) {\r\n        await _savePersistenceForRedirect(exp._castAuth(this.auth));\r\n        return exp.reauthenticateWithRedirect(this._delegate, provider, CompatPopupRedirectResolver);\r\n    }\r\n    sendEmailVerification(actionCodeSettings) {\r\n        return exp.sendEmailVerification(this._delegate, actionCodeSettings);\r\n    }\r\n    async unlink(providerId) {\r\n        await exp.unlink(this._delegate, providerId);\r\n        return this;\r\n    }\r\n    updateEmail(newEmail) {\r\n        return exp.updateEmail(this._delegate, newEmail);\r\n    }\r\n    updatePassword(newPassword) {\r\n        return exp.updatePassword(this._delegate, newPassword);\r\n    }\r\n    updatePhoneNumber(phoneCredential) {\r\n        return exp.updatePhoneNumber(this._delegate, phoneCredential);\r\n    }\r\n    updateProfile(profile) {\r\n        return exp.updateProfile(this._delegate, profile);\r\n    }\r\n    verifyBeforeUpdateEmail(newEmail, actionCodeSettings) {\r\n        return exp.verifyBeforeUpdateEmail(this._delegate, newEmail, actionCodeSettings);\r\n    }\r\n    get emailVerified() {\r\n        return this._delegate.emailVerified;\r\n    }\r\n    get isAnonymous() {\r\n        return this._delegate.isAnonymous;\r\n    }\r\n    get metadata() {\r\n        return this._delegate.metadata;\r\n    }\r\n    get phoneNumber() {\r\n        return this._delegate.phoneNumber;\r\n    }\r\n    get providerData() {\r\n        return this._delegate.providerData;\r\n    }\r\n    get refreshToken() {\r\n        return this._delegate.refreshToken;\r\n    }\r\n    get tenantId() {\r\n        return this._delegate.tenantId;\r\n    }\r\n    get displayName() {\r\n        return this._delegate.displayName;\r\n    }\r\n    get email() {\r\n        return this._delegate.email;\r\n    }\r\n    get photoURL() {\r\n        return this._delegate.photoURL;\r\n    }\r\n    get providerId() {\r\n        return this._delegate.providerId;\r\n    }\r\n    get uid() {\r\n        return this._delegate.uid;\r\n    }\r\n    get auth() {\r\n        return this._delegate.auth;\r\n    }\r\n}\r\n// Maintain a map so that there's always a 1:1 mapping between new User and\r\n// legacy compat users\r\nUser.USER_MAP = new WeakMap();\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst _assert$1 = exp._assert;\r\nclass Auth {\r\n    constructor(app, provider) {\r\n        this.app = app;\r\n        if (provider.isInitialized()) {\r\n            this._delegate = provider.getImmediate();\r\n            this.linkUnderlyingAuth();\r\n            return;\r\n        }\r\n        const { apiKey } = app.options;\r\n        // TODO: platform needs to be determined using heuristics\r\n        _assert$1(apiKey, \"invalid-api-key\" /* exp.AuthErrorCode.INVALID_API_KEY */, {\r\n            appName: app.name\r\n        });\r\n        // TODO: platform needs to be determined using heuristics\r\n        _assert$1(apiKey, \"invalid-api-key\" /* exp.AuthErrorCode.INVALID_API_KEY */, {\r\n            appName: app.name\r\n        });\r\n        // Only use a popup/redirect resolver in browser environments\r\n        const resolver = typeof window !== 'undefined' ? CompatPopupRedirectResolver : undefined;\r\n        this._delegate = provider.initialize({\r\n            options: {\r\n                persistence: buildPersistenceHierarchy(apiKey, app.name),\r\n                popupRedirectResolver: resolver\r\n            }\r\n        });\r\n        this._delegate._updateErrorMap(exp.debugErrorMap);\r\n        this.linkUnderlyingAuth();\r\n    }\r\n    get emulatorConfig() {\r\n        return this._delegate.emulatorConfig;\r\n    }\r\n    get currentUser() {\r\n        if (!this._delegate.currentUser) {\r\n            return null;\r\n        }\r\n        return User.getOrCreate(this._delegate.currentUser);\r\n    }\r\n    get languageCode() {\r\n        return this._delegate.languageCode;\r\n    }\r\n    set languageCode(languageCode) {\r\n        this._delegate.languageCode = languageCode;\r\n    }\r\n    get settings() {\r\n        return this._delegate.settings;\r\n    }\r\n    get tenantId() {\r\n        return this._delegate.tenantId;\r\n    }\r\n    set tenantId(tid) {\r\n        this._delegate.tenantId = tid;\r\n    }\r\n    useDeviceLanguage() {\r\n        this._delegate.useDeviceLanguage();\r\n    }\r\n    signOut() {\r\n        return this._delegate.signOut();\r\n    }\r\n    useEmulator(url, options) {\r\n        exp.connectAuthEmulator(this._delegate, url, options);\r\n    }\r\n    applyActionCode(code) {\r\n        return exp.applyActionCode(this._delegate, code);\r\n    }\r\n    checkActionCode(code) {\r\n        return exp.checkActionCode(this._delegate, code);\r\n    }\r\n    confirmPasswordReset(code, newPassword) {\r\n        return exp.confirmPasswordReset(this._delegate, code, newPassword);\r\n    }\r\n    async createUserWithEmailAndPassword(email, password) {\r\n        return convertCredential(this._delegate, exp.createUserWithEmailAndPassword(this._delegate, email, password));\r\n    }\r\n    fetchProvidersForEmail(email) {\r\n        return this.fetchSignInMethodsForEmail(email);\r\n    }\r\n    fetchSignInMethodsForEmail(email) {\r\n        return exp.fetchSignInMethodsForEmail(this._delegate, email);\r\n    }\r\n    isSignInWithEmailLink(emailLink) {\r\n        return exp.isSignInWithEmailLink(this._delegate, emailLink);\r\n    }\r\n    async getRedirectResult() {\r\n        _assert$1(_isPopupRedirectSupported(), this._delegate, \"operation-not-supported-in-this-environment\" /* exp.AuthErrorCode.OPERATION_NOT_SUPPORTED */);\r\n        const credential = await exp.getRedirectResult(this._delegate, CompatPopupRedirectResolver);\r\n        if (!credential) {\r\n            return {\r\n                credential: null,\r\n                user: null\r\n            };\r\n        }\r\n        return convertCredential(this._delegate, Promise.resolve(credential));\r\n    }\r\n    // This function should only be called by frameworks (e.g. FirebaseUI-web) to log their usage.\r\n    // It is not intended for direct use by developer apps. NO jsdoc here to intentionally leave it\r\n    // out of autogenerated documentation pages to reduce accidental misuse.\r\n    addFrameworkForLogging(framework) {\r\n        exp.addFrameworkForLogging(this._delegate, framework);\r\n    }\r\n    onAuthStateChanged(nextOrObserver, errorFn, completed) {\r\n        const { next, error, complete } = wrapObservers(nextOrObserver, errorFn, completed);\r\n        return this._delegate.onAuthStateChanged(next, error, complete);\r\n    }\r\n    onIdTokenChanged(nextOrObserver, errorFn, completed) {\r\n        const { next, error, complete } = wrapObservers(nextOrObserver, errorFn, completed);\r\n        return this._delegate.onIdTokenChanged(next, error, complete);\r\n    }\r\n    sendSignInLinkToEmail(email, actionCodeSettings) {\r\n        return exp.sendSignInLinkToEmail(this._delegate, email, actionCodeSettings);\r\n    }\r\n    sendPasswordResetEmail(email, actionCodeSettings) {\r\n        return exp.sendPasswordResetEmail(this._delegate, email, actionCodeSettings || undefined);\r\n    }\r\n    async setPersistence(persistence) {\r\n        _validatePersistenceArgument(this._delegate, persistence);\r\n        let converted;\r\n        switch (persistence) {\r\n            case Persistence.SESSION:\r\n                converted = exp.browserSessionPersistence;\r\n                break;\r\n            case Persistence.LOCAL:\r\n                // Not using isIndexedDBAvailable() since it only checks if indexedDB is defined.\r\n                const isIndexedDBFullySupported = await exp\r\n                    ._getInstance(exp.indexedDBLocalPersistence)\r\n                    ._isAvailable();\r\n                converted = isIndexedDBFullySupported\r\n                    ? exp.indexedDBLocalPersistence\r\n                    : exp.browserLocalPersistence;\r\n                break;\r\n            case Persistence.NONE:\r\n                converted = exp.inMemoryPersistence;\r\n                break;\r\n            default:\r\n                return exp._fail(\"argument-error\" /* exp.AuthErrorCode.ARGUMENT_ERROR */, {\r\n                    appName: this._delegate.name\r\n                });\r\n        }\r\n        return this._delegate.setPersistence(converted);\r\n    }\r\n    signInAndRetrieveDataWithCredential(credential) {\r\n        return this.signInWithCredential(credential);\r\n    }\r\n    signInAnonymously() {\r\n        return convertCredential(this._delegate, exp.signInAnonymously(this._delegate));\r\n    }\r\n    signInWithCredential(credential) {\r\n        return convertCredential(this._delegate, exp.signInWithCredential(this._delegate, credential));\r\n    }\r\n    signInWithCustomToken(token) {\r\n        return convertCredential(this._delegate, exp.signInWithCustomToken(this._delegate, token));\r\n    }\r\n    signInWithEmailAndPassword(email, password) {\r\n        return convertCredential(this._delegate, exp.signInWithEmailAndPassword(this._delegate, email, password));\r\n    }\r\n    signInWithEmailLink(email, emailLink) {\r\n        return convertCredential(this._delegate, exp.signInWithEmailLink(this._delegate, email, emailLink));\r\n    }\r\n    signInWithPhoneNumber(phoneNumber, applicationVerifier) {\r\n        return convertConfirmationResult(this._delegate, exp.signInWithPhoneNumber(this._delegate, phoneNumber, applicationVerifier));\r\n    }\r\n    async signInWithPopup(provider) {\r\n        _assert$1(_isPopupRedirectSupported(), this._delegate, \"operation-not-supported-in-this-environment\" /* exp.AuthErrorCode.OPERATION_NOT_SUPPORTED */);\r\n        return convertCredential(this._delegate, exp.signInWithPopup(this._delegate, provider, CompatPopupRedirectResolver));\r\n    }\r\n    async signInWithRedirect(provider) {\r\n        _assert$1(_isPopupRedirectSupported(), this._delegate, \"operation-not-supported-in-this-environment\" /* exp.AuthErrorCode.OPERATION_NOT_SUPPORTED */);\r\n        await _savePersistenceForRedirect(this._delegate);\r\n        return exp.signInWithRedirect(this._delegate, provider, CompatPopupRedirectResolver);\r\n    }\r\n    updateCurrentUser(user) {\r\n        // remove ts-ignore once overloads are defined for exp functions to accept compat objects\r\n        // @ts-ignore\r\n        return this._delegate.updateCurrentUser(user);\r\n    }\r\n    verifyPasswordResetCode(code) {\r\n        return exp.verifyPasswordResetCode(this._delegate, code);\r\n    }\r\n    unwrap() {\r\n        return this._delegate;\r\n    }\r\n    _delete() {\r\n        return this._delegate._delete();\r\n    }\r\n    linkUnderlyingAuth() {\r\n        this._delegate.wrapped = () => this;\r\n    }\r\n}\r\nAuth.Persistence = Persistence;\r\nfunction wrapObservers(nextOrObserver, error, complete) {\r\n    let next = nextOrObserver;\r\n    if (typeof nextOrObserver !== 'function') {\r\n        ({ next, error, complete } = nextOrObserver);\r\n    }\r\n    // We know 'next' is now a function\r\n    const oldNext = next;\r\n    const newNext = (user) => oldNext(user && User.getOrCreate(user));\r\n    return {\r\n        next: newNext,\r\n        error: error,\r\n        complete\r\n    };\r\n}\r\nfunction buildPersistenceHierarchy(apiKey, appName) {\r\n    // Note this is slightly different behavior: in this case, the stored\r\n    // persistence is checked *first* rather than last. This is because we want\r\n    // to prefer stored persistence type in the hierarchy. This is an empty\r\n    // array if window is not available or there is no pending redirect\r\n    const persistences = _getPersistencesFromRedirect(apiKey, appName);\r\n    // If \"self\" is available, add indexedDB\r\n    if (typeof self !== 'undefined' &&\r\n        !persistences.includes(exp.indexedDBLocalPersistence)) {\r\n        persistences.push(exp.indexedDBLocalPersistence);\r\n    }\r\n    // If \"window\" is available, add HTML Storage persistences\r\n    if (typeof window !== 'undefined') {\r\n        for (const persistence of [\r\n            exp.browserLocalPersistence,\r\n            exp.browserSessionPersistence\r\n        ]) {\r\n            if (!persistences.includes(persistence)) {\r\n                persistences.push(persistence);\r\n            }\r\n        }\r\n    }\r\n    // Add in-memory as a final fallback\r\n    if (!persistences.includes(exp.inMemoryPersistence)) {\r\n        persistences.push(exp.inMemoryPersistence);\r\n    }\r\n    return persistences;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass PhoneAuthProvider {\r\n    constructor() {\r\n        this.providerId = 'phone';\r\n        // TODO: remove ts-ignore when moving types from auth-types to auth-compat\r\n        // @ts-ignore\r\n        this._delegate = new exp.PhoneAuthProvider(unwrap(firebase.auth()));\r\n    }\r\n    static credential(verificationId, verificationCode) {\r\n        return exp.PhoneAuthProvider.credential(verificationId, verificationCode);\r\n    }\r\n    verifyPhoneNumber(phoneInfoOptions, applicationVerifier) {\r\n        return this._delegate.verifyPhoneNumber(\r\n        // The implementation matches but the types are subtly incompatible\r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        phoneInfoOptions, applicationVerifier);\r\n    }\r\n    unwrap() {\r\n        return this._delegate;\r\n    }\r\n}\r\nPhoneAuthProvider.PHONE_SIGN_IN_METHOD = exp.PhoneAuthProvider.PHONE_SIGN_IN_METHOD;\r\nPhoneAuthProvider.PROVIDER_ID = exp.PhoneAuthProvider.PROVIDER_ID;\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst _assert = exp._assert;\r\nclass RecaptchaVerifier {\r\n    constructor(container, parameters, app = firebase.app()) {\r\n        var _a;\r\n        // API key is required for web client RPC calls.\r\n        _assert((_a = app.options) === null || _a === void 0 ? void 0 : _a.apiKey, \"invalid-api-key\" /* exp.AuthErrorCode.INVALID_API_KEY */, {\r\n            appName: app.name\r\n        });\r\n        this._delegate = new exp.RecaptchaVerifier(container, \r\n        // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n        parameters, \r\n        // TODO: remove ts-ignore when moving types from auth-types to auth-compat\r\n        // @ts-ignore\r\n        app.auth());\r\n        this.type = this._delegate.type;\r\n    }\r\n    clear() {\r\n        this._delegate.clear();\r\n    }\r\n    render() {\r\n        return this._delegate.render();\r\n    }\r\n    verify() {\r\n        return this._delegate.verify();\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst AUTH_TYPE = 'auth-compat';\r\n// Create auth components to register with firebase.\r\n// Provides Auth public APIs.\r\nfunction registerAuthCompat(instance) {\r\n    instance.INTERNAL.registerComponent(new Component(AUTH_TYPE, container => {\r\n        // getImmediate for FirebaseApp will always succeed\r\n        const app = container.getProvider('app-compat').getImmediate();\r\n        const authProvider = container.getProvider('auth');\r\n        return new Auth(app, authProvider);\r\n    }, \"PUBLIC\" /* ComponentType.PUBLIC */)\r\n        .setServiceProps({\r\n        ActionCodeInfo: {\r\n            Operation: {\r\n                EMAIL_SIGNIN: exp.ActionCodeOperation.EMAIL_SIGNIN,\r\n                PASSWORD_RESET: exp.ActionCodeOperation.PASSWORD_RESET,\r\n                RECOVER_EMAIL: exp.ActionCodeOperation.RECOVER_EMAIL,\r\n                REVERT_SECOND_FACTOR_ADDITION: exp.ActionCodeOperation.REVERT_SECOND_FACTOR_ADDITION,\r\n                VERIFY_AND_CHANGE_EMAIL: exp.ActionCodeOperation.VERIFY_AND_CHANGE_EMAIL,\r\n                VERIFY_EMAIL: exp.ActionCodeOperation.VERIFY_EMAIL\r\n            }\r\n        },\r\n        EmailAuthProvider: exp.EmailAuthProvider,\r\n        FacebookAuthProvider: exp.FacebookAuthProvider,\r\n        GithubAuthProvider: exp.GithubAuthProvider,\r\n        GoogleAuthProvider: exp.GoogleAuthProvider,\r\n        OAuthProvider: exp.OAuthProvider,\r\n        SAMLAuthProvider: exp.SAMLAuthProvider,\r\n        PhoneAuthProvider: PhoneAuthProvider,\r\n        PhoneMultiFactorGenerator: exp.PhoneMultiFactorGenerator,\r\n        RecaptchaVerifier: RecaptchaVerifier,\r\n        TwitterAuthProvider: exp.TwitterAuthProvider,\r\n        Auth,\r\n        AuthCredential: exp.AuthCredential,\r\n        Error: FirebaseError\r\n    })\r\n        .setInstantiationMode(\"LAZY\" /* InstantiationMode.LAZY */)\r\n        .setMultipleInstances(false));\r\n    instance.registerVersion(name, version);\r\n}\r\nregisterAuthCompat(firebase);\n"], "mappings": ";AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C,OAAO,KAAKC,GAAG,MAAM,yBAAyB;AAC9C,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,SAASC,kBAAkB,EAAEC,KAAK,EAAEC,aAAa,EAAEC,MAAM,EAAEC,oBAAoB,EAAEC,IAAI,EAAEC,aAAa,QAAQ,gBAAgB;AAE5H,IAAIC,IAAI,GAAG,uBAAuB;AAClC,IAAIC,OAAO,GAAG,OAAO;;AAErB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,gCAAgC,GAAG,IAAI;AAC7C,SAASC,iBAAiBA,CAAA,EAAG;EACzB,IAAIC,EAAE;EACN,OAAO,CAAC,CAACA,EAAE,GAAGC,IAAI,KAAK,IAAI,IAAIA,IAAI,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,IAAI,CAACC,QAAQ,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACG,QAAQ,KAAK,IAAI;AACtI;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAA,EAAG;EACtB,OAAOL,iBAAiB,CAAC,CAAC,KAAK,OAAO,IAAIA,iBAAiB,CAAC,CAAC,KAAK,QAAQ;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA,SAASM,4BAA4BA,CAACC,EAAE,GAAGhB,KAAK,CAAC,CAAC,EAAE;EAChD,OAAO,CAAC,EAAE,CAACS,iBAAiB,CAAC,CAAC,KAAK,OAAO,IACtCA,iBAAiB,CAAC,CAAC,KAAK,QAAQ,IAChCA,iBAAiB,CAAC,CAAC,KAAK,YAAY,KACpCO,EAAE,CAACC,WAAW,CAAC,CAAC,CAACC,KAAK,CAAC,0BAA0B,CAAC,CAAC;AAC3D;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAA,EAAG;EAC5B,OAAOlB,aAAa,CAAC,CAAC,IAAIC,MAAM,CAAC,CAAC;AACtC;AACA;AACA;AACA;AACA;AACA,SAASkB,OAAOA,CAAA,EAAG;EACf,OAAOhB,IAAI,CAAC,CAAC,IAAI,CAACiB,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,YAAY,MAAM,EAAE;AACvG;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,OAAOA,CAACP,EAAE,GAAGhB,KAAK,CAAC,CAAC,EAAE;EAC3B,OAAO,WAAW,CAACwB,IAAI,CAACR,EAAE,CAAC;AAC/B;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,8BAA8BA,CAACT,EAAE,GAAGhB,KAAK,CAAC,CAAC,EAAE;EAClD,OAAOoB,OAAO,CAAC,CAAC,IAAIG,OAAO,CAACP,EAAE,CAAC;AACnC;AACA;AACA,SAASU,sBAAsBA,CAAA,EAAG;EAC9B,IAAI;IACA,MAAMC,OAAO,GAAGhB,IAAI,CAACiB,YAAY;IACjC,MAAMC,GAAG,GAAGhC,GAAG,CAACiC,gBAAgB,CAAC,CAAC;IAClC,IAAIH,OAAO,EAAE;MACT;MACA;MACAA,OAAO,CAAC,SAAS,CAAC,CAACE,GAAG,EAAE,GAAG,CAAC;MAC5BF,OAAO,CAAC,YAAY,CAAC,CAACE,GAAG,CAAC;MAC1B;MACA;MACA;MACA;MACA;MACA,IAAIJ,8BAA8B,CAAC,CAAC,EAAE;QAClC;QACA;QACA,OAAOtB,oBAAoB,CAAC,CAAC;MACjC;MACA,OAAO,IAAI;IACf;EACJ,CAAC,CACD,OAAO4B,CAAC,EAAE;IACN;IACA;IACA,OAAOC,SAAS,CAAC,CAAC,IAAI7B,oBAAoB,CAAC,CAAC;EAChD;EACA,OAAO,KAAK;AAChB;AACA;AACA;AACA;AACA;AACA,SAAS6B,SAASA,CAAA,EAAG;EACjB;EACA,OAAQ,OAAOC,MAAM,KAAK,WAAW,IACjC,mBAAmB,IAAIA,MAAM,IAC7B,eAAe,IAAIA,MAAM;AACjC;AACA,SAASC,yBAAyBA,CAAA,EAAG;EACjC,OAAQ,CAACpB,cAAc,CAAC,CAAC,IACrBf,kBAAkB,CAAC,CAAC,IACpBgB,4BAA4B,CAAC,CAAC;EAC9B;EACA;EACA,CAACI,oBAAoB,CAAC,CAAC;EACvB;EACA;EACAO,sBAAsB,CAAC,CAAC;EACxB;EACA,CAACM,SAAS,CAAC,CAAC;AACpB;AACA;AACA,SAASG,gBAAgBA,CAAA,EAAG;EACxB,OAAOpB,4BAA4B,CAAC,CAAC,IAAI,OAAOM,QAAQ,KAAK,WAAW;AAC5E;AAAC,SACce,UAAUA,CAAA;EAAA,OAAAC,WAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAF,YAAA;EAAAA,WAAA,GAAAG,iBAAA,CAAzB,aAA4B;IACxB,IAAI,CAACL,gBAAgB,CAAC,CAAC,EAAE;MACrB,OAAO,KAAK;IAChB;IACA,OAAO,IAAIM,OAAO,CAACC,OAAO,IAAI;MAC1B,MAAMC,SAAS,GAAGC,UAAU,CAAC,MAAM;QAC/B;QACAF,OAAO,CAAC,KAAK,CAAC;MAClB,CAAC,EAAElC,gCAAgC,CAAC;MACpCa,QAAQ,CAACwB,gBAAgB,CAAC,aAAa,EAAE,MAAM;QAC3CC,YAAY,CAACH,SAAS,CAAC;QACvBD,OAAO,CAAC,IAAI,CAAC;MACjB,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EAAA,OAAAL,WAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AACD,SAASQ,cAAcA,CAAA,EAAG;EACtB,OAAO,OAAOC,MAAM,KAAK,WAAW,GAAGA,MAAM,GAAG,IAAI;AACxD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,WAAW,GAAG;EAChBC,KAAK,EAAE,OAAO;EACdC,IAAI,EAAE,MAAM;EACZC,OAAO,EAAE;AACb,CAAC;AACD,MAAMC,SAAS,GAAGxD,GAAG,CAACyD,OAAO;AAC7B,MAAMC,eAAe,GAAG,aAAa;AACrC;AACA;AACA;AACA;AACA,SAASC,4BAA4BA,CAACC,IAAI,EAAEC,WAAW,EAAE;EACrDL,SAAS,CAACM,MAAM,CAACC,MAAM,CAACX,WAAW,CAAC,CAACY,QAAQ,CAACH,WAAW,CAAC,EAAED,IAAI,EAAE,0BAA0B,CAAC,2CAA2C,CAAC;EACzI;EACA,IAAIxD,aAAa,CAAC,CAAC,EAAE;IACjB;IACAoD,SAAS,CAACK,WAAW,KAAKT,WAAW,CAACG,OAAO,EAAEK,IAAI,EAAE,8BAA8B,CAAC,+CAA+C,CAAC;IACpI;EACJ;EACA,IAAIvD,MAAM,CAAC,CAAC,EAAE;IACV;IACAmD,SAAS,CAACK,WAAW,KAAKT,WAAW,CAACE,IAAI,EAAEM,IAAI,EAAE,8BAA8B,CAAC,+CAA+C,CAAC;IACjI;EACJ;EACA,IAAIzB,SAAS,CAAC,CAAC,EAAE;IACb;IACA;IACAqB,SAAS,CAACK,WAAW,KAAKT,WAAW,CAACE,IAAI,IACrCO,WAAW,KAAKT,WAAW,CAACC,KAAK,IAAI/C,oBAAoB,CAAC,CAAE,EAAEsD,IAAI,EAAE,8BAA8B,CAAC,+CAA+C,CAAC;IACxJ;EACJ;EACA;EACAJ,SAAS,CAACK,WAAW,KAAKT,WAAW,CAACE,IAAI,IAAIzB,sBAAsB,CAAC,CAAC,EAAE+B,IAAI,EAAE,8BAA8B,CAAC,+CAA+C,CAAC;AACjK;AAAC,SACcK,2BAA2BA,CAAAC,EAAA;EAAA,OAAAC,4BAAA,CAAA1B,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAyB,6BAAA;EAAAA,4BAAA,GAAAxB,iBAAA,CAA1C,WAA2CiB,IAAI,EAAE;IAC7C,MAAMA,IAAI,CAACQ,sBAAsB;IACjC,MAAMC,OAAO,GAAGC,4BAA4B,CAAC,CAAC;IAC9C,MAAMtC,GAAG,GAAGhC,GAAG,CAACuE,mBAAmB,CAACb,eAAe,EAAEE,IAAI,CAACY,MAAM,CAACC,MAAM,EAAEb,IAAI,CAACnD,IAAI,CAAC;IACnF,IAAI4D,OAAO,EAAE;MACTA,OAAO,CAACK,OAAO,CAAC1C,GAAG,EAAE4B,IAAI,CAACe,eAAe,CAAC,CAAC,CAAC;IAChD;EACJ,CAAC;EAAA,OAAAR,4BAAA,CAAA1B,KAAA,OAAAC,SAAA;AAAA;AACD,SAASkC,4BAA4BA,CAACH,MAAM,EAAEI,OAAO,EAAE;EACnD,MAAMR,OAAO,GAAGC,4BAA4B,CAAC,CAAC;EAC9C,IAAI,CAACD,OAAO,EAAE;IACV,OAAO,EAAE;EACb;EACA,MAAMrC,GAAG,GAAGhC,GAAG,CAACuE,mBAAmB,CAACb,eAAe,EAAEe,MAAM,EAAEI,OAAO,CAAC;EACrE,MAAMhB,WAAW,GAAGQ,OAAO,CAACS,OAAO,CAAC9C,GAAG,CAAC;EACxC,QAAQ6B,WAAW;IACf,KAAKT,WAAW,CAACE,IAAI;MACjB,OAAO,CAACtD,GAAG,CAAC+E,mBAAmB,CAAC;IACpC,KAAK3B,WAAW,CAACC,KAAK;MAClB,OAAO,CAACrD,GAAG,CAACgF,yBAAyB,EAAEhF,GAAG,CAACiF,yBAAyB,CAAC;IACzE,KAAK7B,WAAW,CAACG,OAAO;MACpB,OAAO,CAACvD,GAAG,CAACiF,yBAAyB,CAAC;IAC1C;MACI,OAAO,EAAE;EACjB;AACJ;AACA;AACA,SAASX,4BAA4BA,CAAA,EAAG;EACpC,IAAIzD,EAAE;EACN,IAAI;IACA,OAAO,CAAC,CAACA,EAAE,GAAGqC,cAAc,CAAC,CAAC,MAAM,IAAI,IAAIrC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACqE,cAAc,KAAK,IAAI;EACnG,CAAC,CACD,OAAOhD,CAAC,EAAE;IACN,OAAO,IAAI;EACf;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiD,SAAS,GAAGnF,GAAG,CAACyD,OAAO;AAC7B;AACA,MAAM2B,2BAA2B,CAAC;EAC9BC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,eAAe,GAAGtF,GAAG,CAACuF,YAAY,CAACvF,GAAG,CAACwF,4BAA4B,CAAC;IACzE,IAAI,CAACC,eAAe,GAAGzF,GAAG,CAACuF,YAAY,CAACvF,GAAG,CAAC0F,4BAA4B,CAAC;IACzE;IACA,IAAI,CAACC,kBAAkB,GAAG,IAAI;IAC9B,IAAI,CAACC,oBAAoB,GAAG5F,GAAG,CAACiF,yBAAyB;IACzD,IAAI,CAACY,mBAAmB,GAAG7F,GAAG,CAAC8F,kBAAkB;IACjD,IAAI,CAACC,uBAAuB,GAAG/F,GAAG,CAAC+F,uBAAuB;EAC9D;EACMC,WAAWA,CAACpC,IAAI,EAAE;IAAA,IAAAqC,KAAA;IAAA,OAAAtD,iBAAA;MACpB,MAAMsD,KAAI,CAACC,wBAAwB,CAAC,CAAC;MACrC,OAAOD,KAAI,CAACE,0BAA0B,CAACH,WAAW,CAACpC,IAAI,CAAC;IAAC;EAC7D;EACMwC,UAAUA,CAACxC,IAAI,EAAEyC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA7D,iBAAA;MAChD,MAAM6D,MAAI,CAACN,wBAAwB,CAAC,CAAC;MACrC,OAAOM,MAAI,CAACL,0BAA0B,CAACC,UAAU,CAACxC,IAAI,EAAEyC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,CAAC;IAAC;EACzF;EACME,aAAaA,CAAC7C,IAAI,EAAEyC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,EAAE;IAAA,IAAAG,MAAA;IAAA,OAAA/D,iBAAA;MACnD,MAAM+D,MAAI,CAACR,wBAAwB,CAAC,CAAC;MACrC,OAAOQ,MAAI,CAACP,0BAA0B,CAACM,aAAa,CAAC7C,IAAI,EAAEyC,QAAQ,EAAEC,QAAQ,EAAEC,OAAO,CAAC;IAAC;EAC5F;EACAI,4BAA4BA,CAAC/C,IAAI,EAAEgD,EAAE,EAAE;IACnC,IAAI,CAACT,0BAA0B,CAACQ,4BAA4B,CAAC/C,IAAI,EAAEgD,EAAE,CAAC;EAC1E;EACAC,iBAAiBA,CAACjD,IAAI,EAAE;IACpB,OAAO,IAAI,CAACuC,0BAA0B,CAACU,iBAAiB,CAACjD,IAAI,CAAC;EAClE;EACA,IAAIkD,sBAAsBA,CAAA,EAAG;IACzB,OAAOxE,gBAAgB,CAAC,CAAC,IAAI,IAAI,CAACgD,eAAe,CAACwB,sBAAsB;EAC5E;EACA,IAAIX,0BAA0BA,CAAA,EAAG;IAC7BhB,SAAS,CAAC,IAAI,CAACQ,kBAAkB,EAAE,gBAAgB,CAAC,sCAAsC,CAAC;IAC3F,OAAO,IAAI,CAACA,kBAAkB;EAClC;EACMO,wBAAwBA,CAAA,EAAG;IAAA,IAAAa,MAAA;IAAA,OAAApE,iBAAA;MAC7B,IAAIoE,MAAI,CAACpB,kBAAkB,EAAE;QACzB;MACJ;MACA;MACA;MACA,MAAMqB,SAAS,SAASzE,UAAU,CAAC,CAAC;MACpCwE,MAAI,CAACpB,kBAAkB,GAAGqB,SAAS,GAC7BD,MAAI,CAACtB,eAAe,GACpBsB,MAAI,CAACzB,eAAe;IAAC;EAC/B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2B,MAAMA,CAACC,MAAM,EAAE;EACpB,OAAOA,MAAM,CAACD,MAAM,CAAC,CAAC;AAC1B;AACA,SAASE,OAAOA,CAACD,MAAM,EAAE;EACrB,OAAOA,MAAM,CAACC,OAAO,CAAC,CAAC;AAC3B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,sBAAsBA,CAACC,cAAc,EAAE;EAC5C,OAAOC,oBAAoB,CAACD,cAAc,CAAC;AAC/C;AACA,SAASE,sBAAsBA,CAAC3D,IAAI,EAAE1B,CAAC,EAAE;EACrC,IAAIrB,EAAE;EACN;EACA;EACA,MAAM2G,QAAQ,GAAG,CAAC3G,EAAE,GAAGqB,CAAC,CAACuF,UAAU,MAAM,IAAI,IAAI5G,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC6G,cAAc;EAC3F,IAAI,CAACxF,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACyF,IAAI,MAAM,iCAAiC,EAAE;IACtF,MAAMC,MAAM,GAAG1F,CAAC;IAChB0F,MAAM,CAACC,QAAQ,GAAG,IAAIC,mBAAmB,CAAClE,IAAI,EAAE5D,GAAG,CAAC+H,sBAAsB,CAACnE,IAAI,EAAE1B,CAAC,CAAC,CAAC;EACxF,CAAC,MACI,IAAIsF,QAAQ,EAAE;IACf,MAAMQ,UAAU,GAAGV,oBAAoB,CAACpF,CAAC,CAAC;IAC1C,MAAM+F,OAAO,GAAG/F,CAAC;IACjB,IAAI8F,UAAU,EAAE;MACZC,OAAO,CAACD,UAAU,GAAGA,UAAU;MAC/BC,OAAO,CAACC,QAAQ,GAAGV,QAAQ,CAACU,QAAQ,IAAIC,SAAS;MACjDF,OAAO,CAACG,KAAK,GAAGZ,QAAQ,CAACY,KAAK,IAAID,SAAS;MAC3CF,OAAO,CAACI,WAAW,GAAGb,QAAQ,CAACa,WAAW,IAAIF,SAAS;IAC3D;EACJ;AACJ;AACA,SAASb,oBAAoBA,CAACJ,MAAM,EAAE;EAClC,MAAM;IAAEQ;EAAe,CAAC,GAAIR,MAAM,YAAY1G,aAAa,GAAG0G,MAAM,CAACO,UAAU,GAAGP,MAAO;EACzF,IAAI,CAACQ,cAAc,EAAE;IACjB,OAAO,IAAI;EACf;EACA;EACA;EACA;EACA,IAAI,EAAER,MAAM,YAAY1G,aAAa,CAAC,EAAE;IACpC,IAAI,gBAAgB,IAAIkH,cAAc,IAAI,aAAa,IAAIA,cAAc,EAAE;MACvE,OAAO1H,GAAG,CAACsI,iBAAiB,CAACC,oBAAoB,CAACrB,MAAM,CAAC;IAC7D;EACJ;EACA,MAAMsB,UAAU,GAAGd,cAAc,CAACc,UAAU;EAC5C;EACA;EACA,IAAI,CAACA,UAAU,IAAIA,UAAU,KAAKxI,GAAG,CAACyI,UAAU,CAACC,QAAQ,EAAE;IACvD,OAAO,IAAI;EACf;EACA,IAAIrC,QAAQ;EACZ,QAAQmC,UAAU;IACd,KAAKxI,GAAG,CAACyI,UAAU,CAACE,MAAM;MACtBtC,QAAQ,GAAGrG,GAAG,CAAC4I,kBAAkB;MACjC;IACJ,KAAK5I,GAAG,CAACyI,UAAU,CAACI,QAAQ;MACxBxC,QAAQ,GAAGrG,GAAG,CAAC8I,oBAAoB;MACnC;IACJ,KAAK9I,GAAG,CAACyI,UAAU,CAACM,MAAM;MACtB1C,QAAQ,GAAGrG,GAAG,CAACgJ,kBAAkB;MACjC;IACJ,KAAKhJ,GAAG,CAACyI,UAAU,CAACQ,OAAO;MACvB5C,QAAQ,GAAGrG,GAAG,CAACkJ,mBAAmB;MAClC;IACJ;MACI,MAAM;QAAEC,YAAY;QAAEC,gBAAgB;QAAEC,gBAAgB;QAAEC,YAAY;QAAEC;MAAM,CAAC,GAAG7B,cAAc;MAChG,IAAI,CAAC0B,gBAAgB,IACjB,CAACC,gBAAgB,IACjB,CAACF,YAAY,IACb,CAACG,YAAY,EAAE;QACf,OAAO,IAAI;MACf;MACA;MACA,IAAIA,YAAY,EAAE;QACd,IAAId,UAAU,CAACgB,UAAU,CAAC,OAAO,CAAC,EAAE;UAChC,OAAOxJ,GAAG,CAACyJ,kBAAkB,CAACC,OAAO,CAAClB,UAAU,EAAEc,YAAY,CAAC;QACnE,CAAC,MACI;UACD;UACA,OAAOtJ,GAAG,CAAC2J,eAAe,CAACC,WAAW,CAAC;YACnCpB,UAAU;YACVqB,YAAY,EAAErB,UAAU;YACxBc,YAAY;YACZQ,OAAO,EAAEX,YAAY;YACrBY,WAAW,EAAEX;UACjB,CAAC,CAAC;QACN;MACJ;MACA,OAAO,IAAIpJ,GAAG,CAACgK,aAAa,CAACxB,UAAU,CAAC,CAACR,UAAU,CAAC;QAChD8B,OAAO,EAAEX,YAAY;QACrBY,WAAW,EAAEX,gBAAgB;QAC7Ba,QAAQ,EAAEV;MACd,CAAC,CAAC;EACV;EACA,OAAOrC,MAAM,YAAY1G,aAAa,GAChC6F,QAAQ,CAAC6D,mBAAmB,CAAChD,MAAM,CAAC,GACpCb,QAAQ,CAACkC,oBAAoB,CAACrB,MAAM,CAAC;AAC/C;AACA,SAASiD,iBAAiBA,CAACvG,IAAI,EAAEwG,iBAAiB,EAAE;EAChD,OAAOA,iBAAiB,CACnBC,KAAK,CAACnI,CAAC,IAAI;IACZ,IAAIA,CAAC,YAAY1B,aAAa,EAAE;MAC5B+G,sBAAsB,CAAC3D,IAAI,EAAE1B,CAAC,CAAC;IACnC;IACA,MAAMA,CAAC;EACX,CAAC,CAAC,CACGoI,IAAI,CAACtC,UAAU,IAAI;IACpB,MAAMuC,aAAa,GAAGvC,UAAU,CAACuC,aAAa;IAC9C,MAAMC,IAAI,GAAGxC,UAAU,CAACwC,IAAI;IAC5B,OAAO;MACHD,aAAa;MACbvC,UAAU,EAAEZ,sBAAsB,CAACY,UAAU,CAAC;MAC9CyC,kBAAkB,EAAEzK,GAAG,CAAC0K,qBAAqB,CAAC1C,UAAU,CAAC;MACzDwC,IAAI,EAAEG,IAAI,CAACC,WAAW,CAACJ,IAAI;IAC/B,CAAC;EACL,CAAC,CAAC;AACN;AAAC,SACcK,yBAAyBA,CAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,0BAAA,CAAAvI,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAsI,2BAAA;EAAAA,0BAAA,GAAArI,iBAAA,CAAxC,WAAyCiB,IAAI,EAAEqH,yBAAyB,EAAE;IACtE,MAAMC,qBAAqB,SAASD,yBAAyB;IAC7D,OAAO;MACHE,cAAc,EAAED,qBAAqB,CAACC,cAAc;MACpDC,OAAO,EAAGC,gBAAgB,IAAKlB,iBAAiB,CAACvG,IAAI,EAAEsH,qBAAqB,CAACE,OAAO,CAACC,gBAAgB,CAAC;IAC1G,CAAC;EACL,CAAC;EAAA,OAAAL,0BAAA,CAAAvI,KAAA,OAAAC,SAAA;AAAA;AACD,MAAMoF,mBAAmB,CAAC;EACtBzC,WAAWA,CAACzB,IAAI,EAAEiE,QAAQ,EAAE;IACxB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACjE,IAAI,GAAGuD,OAAO,CAACvD,IAAI,CAAC;EAC7B;EACA,IAAIS,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACwD,QAAQ,CAACxD,OAAO;EAChC;EACA,IAAIiH,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACzD,QAAQ,CAACyD,KAAK;EAC9B;EACAC,aAAaA,CAACC,SAAS,EAAE;IACrB,OAAOrB,iBAAiB,CAAClD,MAAM,CAAC,IAAI,CAACrD,IAAI,CAAC,EAAE,IAAI,CAACiE,QAAQ,CAAC0D,aAAa,CAACC,SAAS,CAAC,CAAC;EACvF;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMb,IAAI,CAAC;EACPtF,WAAWA,CAACoG,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,WAAW,GAAG1L,GAAG,CAAC0L,WAAW,CAACD,SAAS,CAAC;EACjD;EACA,OAAOb,WAAWA,CAACJ,IAAI,EAAE;IACrB,IAAI,CAACG,IAAI,CAACgB,QAAQ,CAACC,GAAG,CAACpB,IAAI,CAAC,EAAE;MAC1BG,IAAI,CAACgB,QAAQ,CAACE,GAAG,CAACrB,IAAI,EAAE,IAAIG,IAAI,CAACH,IAAI,CAAC,CAAC;IAC3C;IACA,OAAOG,IAAI,CAACgB,QAAQ,CAACG,GAAG,CAACtB,IAAI,CAAC;EAClC;EACAuB,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACN,SAAS,CAACM,MAAM,CAAC,CAAC;EAClC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACP,SAAS,CAACO,MAAM,CAAC,CAAC;EAClC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACR,SAAS,CAACQ,MAAM,CAAC,CAAC;EAClC;EACAC,gBAAgBA,CAACC,YAAY,EAAE;IAC3B,OAAO,IAAI,CAACV,SAAS,CAACS,gBAAgB,CAACC,YAAY,CAAC;EACxD;EACAC,UAAUA,CAACD,YAAY,EAAE;IACrB,OAAO,IAAI,CAACV,SAAS,CAACW,UAAU,CAACD,YAAY,CAAC;EAClD;EACAE,iCAAiCA,CAACrE,UAAU,EAAE;IAC1C,OAAO,IAAI,CAACsE,kBAAkB,CAACtE,UAAU,CAAC;EAC9C;EACMsE,kBAAkBA,CAACtE,UAAU,EAAE;IAAA,IAAAuE,MAAA;IAAA,OAAA5J,iBAAA;MACjC,OAAOwH,iBAAiB,CAACoC,MAAI,CAAC3I,IAAI,EAAE5D,GAAG,CAACsM,kBAAkB,CAACC,MAAI,CAACd,SAAS,EAAEzD,UAAU,CAAC,CAAC;IAAC;EAC5F;EACMwE,mBAAmBA,CAACnE,WAAW,EAAEoE,mBAAmB,EAAE;IAAA,IAAAC,MAAA;IAAA,OAAA/J,iBAAA;MACxD,OAAOkI,yBAAyB,CAAC6B,MAAI,CAAC9I,IAAI,EAAE5D,GAAG,CAACwM,mBAAmB,CAACE,MAAI,CAACjB,SAAS,EAAEpD,WAAW,EAAEoE,mBAAmB,CAAC,CAAC;IAAC;EAC3H;EACME,aAAaA,CAACtG,QAAQ,EAAE;IAAA,IAAAuG,MAAA;IAAA,OAAAjK,iBAAA;MAC1B,OAAOwH,iBAAiB,CAACyC,MAAI,CAAChJ,IAAI,EAAE5D,GAAG,CAAC2M,aAAa,CAACC,MAAI,CAACnB,SAAS,EAAEpF,QAAQ,EAAEjB,2BAA2B,CAAC,CAAC;IAAC;EAClH;EACMyH,gBAAgBA,CAACxG,QAAQ,EAAE;IAAA,IAAAyG,MAAA;IAAA,OAAAnK,iBAAA;MAC7B,MAAMsB,2BAA2B,CAACjE,GAAG,CAAC+M,SAAS,CAACD,MAAI,CAAClJ,IAAI,CAAC,CAAC;MAC3D,OAAO5D,GAAG,CAAC6M,gBAAgB,CAACC,MAAI,CAACrB,SAAS,EAAEpF,QAAQ,EAAEjB,2BAA2B,CAAC;IAAC;EACvF;EACA4H,2CAA2CA,CAAChF,UAAU,EAAE;IACpD,OAAO,IAAI,CAACiF,4BAA4B,CAACjF,UAAU,CAAC;EACxD;EACMiF,4BAA4BA,CAACjF,UAAU,EAAE;IAAA,IAAAkF,MAAA;IAAA,OAAAvK,iBAAA;MAC3C,OAAOwH,iBAAiB,CAAC+C,MAAI,CAACtJ,IAAI,EAAE5D,GAAG,CAACiN,4BAA4B,CAACC,MAAI,CAACzB,SAAS,EAAEzD,UAAU,CAAC,CAAC;IAAC;EACtG;EACAmF,6BAA6BA,CAAC9E,WAAW,EAAEoE,mBAAmB,EAAE;IAC5D,OAAO5B,yBAAyB,CAAC,IAAI,CAACjH,IAAI,EAAE5D,GAAG,CAACmN,6BAA6B,CAAC,IAAI,CAAC1B,SAAS,EAAEpD,WAAW,EAAEoE,mBAAmB,CAAC,CAAC;EACpI;EACAW,uBAAuBA,CAAC/G,QAAQ,EAAE;IAC9B,OAAO8D,iBAAiB,CAAC,IAAI,CAACvG,IAAI,EAAE5D,GAAG,CAACoN,uBAAuB,CAAC,IAAI,CAAC3B,SAAS,EAAEpF,QAAQ,EAAEjB,2BAA2B,CAAC,CAAC;EAC3H;EACMiI,0BAA0BA,CAAChH,QAAQ,EAAE;IAAA,IAAAiH,MAAA;IAAA,OAAA3K,iBAAA;MACvC,MAAMsB,2BAA2B,CAACjE,GAAG,CAAC+M,SAAS,CAACO,MAAI,CAAC1J,IAAI,CAAC,CAAC;MAC3D,OAAO5D,GAAG,CAACqN,0BAA0B,CAACC,MAAI,CAAC7B,SAAS,EAAEpF,QAAQ,EAAEjB,2BAA2B,CAAC;IAAC;EACjG;EACAmI,qBAAqBA,CAACC,kBAAkB,EAAE;IACtC,OAAOxN,GAAG,CAACuN,qBAAqB,CAAC,IAAI,CAAC9B,SAAS,EAAE+B,kBAAkB,CAAC;EACxE;EACMC,MAAMA,CAACjF,UAAU,EAAE;IAAA,IAAAkF,MAAA;IAAA,OAAA/K,iBAAA;MACrB,MAAM3C,GAAG,CAACyN,MAAM,CAACC,MAAI,CAACjC,SAAS,EAAEjD,UAAU,CAAC;MAC5C,OAAOkF,MAAI;IAAC;EAChB;EACAC,WAAWA,CAACC,QAAQ,EAAE;IAClB,OAAO5N,GAAG,CAAC2N,WAAW,CAAC,IAAI,CAAClC,SAAS,EAAEmC,QAAQ,CAAC;EACpD;EACAC,cAAcA,CAACC,WAAW,EAAE;IACxB,OAAO9N,GAAG,CAAC6N,cAAc,CAAC,IAAI,CAACpC,SAAS,EAAEqC,WAAW,CAAC;EAC1D;EACAC,iBAAiBA,CAACC,eAAe,EAAE;IAC/B,OAAOhO,GAAG,CAAC+N,iBAAiB,CAAC,IAAI,CAACtC,SAAS,EAAEuC,eAAe,CAAC;EACjE;EACAC,aAAaA,CAACC,OAAO,EAAE;IACnB,OAAOlO,GAAG,CAACiO,aAAa,CAAC,IAAI,CAACxC,SAAS,EAAEyC,OAAO,CAAC;EACrD;EACAC,uBAAuBA,CAACP,QAAQ,EAAEJ,kBAAkB,EAAE;IAClD,OAAOxN,GAAG,CAACmO,uBAAuB,CAAC,IAAI,CAAC1C,SAAS,EAAEmC,QAAQ,EAAEJ,kBAAkB,CAAC;EACpF;EACA,IAAIY,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAAC3C,SAAS,CAAC2C,aAAa;EACvC;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC5C,SAAS,CAAC4C,WAAW;EACrC;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC7C,SAAS,CAAC6C,QAAQ;EAClC;EACA,IAAIjG,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACoD,SAAS,CAACpD,WAAW;EACrC;EACA,IAAIkG,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC9C,SAAS,CAAC8C,YAAY;EACtC;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAC/C,SAAS,CAAC+C,YAAY;EACtC;EACA,IAAItG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACuD,SAAS,CAACvD,QAAQ;EAClC;EACA,IAAIuG,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAChD,SAAS,CAACgD,WAAW;EACrC;EACA,IAAIrG,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACqD,SAAS,CAACrD,KAAK;EAC/B;EACA,IAAIsG,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACjD,SAAS,CAACiD,QAAQ;EAClC;EACA,IAAIlG,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACiD,SAAS,CAACjD,UAAU;EACpC;EACA,IAAImG,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI,CAAClD,SAAS,CAACkD,GAAG;EAC7B;EACA,IAAI/K,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC6H,SAAS,CAAC7H,IAAI;EAC9B;AACJ;AACA;AACA;AACA+G,IAAI,CAACgB,QAAQ,GAAG,IAAIiD,OAAO,CAAC,CAAC;;AAE7B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG7O,GAAG,CAACyD,OAAO;AAC7B,MAAMqL,IAAI,CAAC;EACPzJ,WAAWA,CAAC0J,GAAG,EAAE1I,QAAQ,EAAE;IACvB,IAAI,CAAC0I,GAAG,GAAGA,GAAG;IACd,IAAI1I,QAAQ,CAAC2I,aAAa,CAAC,CAAC,EAAE;MAC1B,IAAI,CAACvD,SAAS,GAAGpF,QAAQ,CAAC4I,YAAY,CAAC,CAAC;MACxC,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB;IACJ;IACA,MAAM;MAAEzK;IAAO,CAAC,GAAGsK,GAAG,CAACI,OAAO;IAC9B;IACAN,SAAS,CAACpK,MAAM,EAAE,iBAAiB,CAAC,yCAAyC;MACzEI,OAAO,EAAEkK,GAAG,CAACtO;IACjB,CAAC,CAAC;IACF;IACAoO,SAAS,CAACpK,MAAM,EAAE,iBAAiB,CAAC,yCAAyC;MACzEI,OAAO,EAAEkK,GAAG,CAACtO;IACjB,CAAC,CAAC;IACF;IACA,MAAMoH,QAAQ,GAAG,OAAO1E,MAAM,KAAK,WAAW,GAAGiC,2BAA2B,GAAG+C,SAAS;IACxF,IAAI,CAACsD,SAAS,GAAGpF,QAAQ,CAAC+I,UAAU,CAAC;MACjCD,OAAO,EAAE;QACLtL,WAAW,EAAEwL,yBAAyB,CAAC5K,MAAM,EAAEsK,GAAG,CAACtO,IAAI,CAAC;QACxD6O,qBAAqB,EAAEzH;MAC3B;IACJ,CAAC,CAAC;IACF,IAAI,CAAC4D,SAAS,CAAC8D,eAAe,CAACvP,GAAG,CAACwP,aAAa,CAAC;IACjD,IAAI,CAACN,kBAAkB,CAAC,CAAC;EAC7B;EACA,IAAIO,cAAcA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAChE,SAAS,CAACgE,cAAc;EACxC;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACjE,SAAS,CAACiE,WAAW,EAAE;MAC7B,OAAO,IAAI;IACf;IACA,OAAO/E,IAAI,CAACC,WAAW,CAAC,IAAI,CAACa,SAAS,CAACiE,WAAW,CAAC;EACvD;EACA,IAAIC,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAAClE,SAAS,CAACkE,YAAY;EACtC;EACA,IAAIA,YAAYA,CAACA,YAAY,EAAE;IAC3B,IAAI,CAAClE,SAAS,CAACkE,YAAY,GAAGA,YAAY;EAC9C;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACnE,SAAS,CAACmE,QAAQ;EAClC;EACA,IAAI1H,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACuD,SAAS,CAACvD,QAAQ;EAClC;EACA,IAAIA,QAAQA,CAAC2H,GAAG,EAAE;IACd,IAAI,CAACpE,SAAS,CAACvD,QAAQ,GAAG2H,GAAG;EACjC;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAACrE,SAAS,CAACqE,iBAAiB,CAAC,CAAC;EACtC;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACtE,SAAS,CAACsE,OAAO,CAAC,CAAC;EACnC;EACAC,WAAWA,CAACC,GAAG,EAAEd,OAAO,EAAE;IACtBnP,GAAG,CAACkQ,mBAAmB,CAAC,IAAI,CAACzE,SAAS,EAAEwE,GAAG,EAAEd,OAAO,CAAC;EACzD;EACAgB,eAAeA,CAACxI,IAAI,EAAE;IAClB,OAAO3H,GAAG,CAACmQ,eAAe,CAAC,IAAI,CAAC1E,SAAS,EAAE9D,IAAI,CAAC;EACpD;EACAyI,eAAeA,CAACzI,IAAI,EAAE;IAClB,OAAO3H,GAAG,CAACoQ,eAAe,CAAC,IAAI,CAAC3E,SAAS,EAAE9D,IAAI,CAAC;EACpD;EACA0I,oBAAoBA,CAAC1I,IAAI,EAAEmG,WAAW,EAAE;IACpC,OAAO9N,GAAG,CAACqQ,oBAAoB,CAAC,IAAI,CAAC5E,SAAS,EAAE9D,IAAI,EAAEmG,WAAW,CAAC;EACtE;EACMwC,8BAA8BA,CAAClI,KAAK,EAAEmI,QAAQ,EAAE;IAAA,IAAAC,OAAA;IAAA,OAAA7N,iBAAA;MAClD,OAAOwH,iBAAiB,CAACqG,OAAI,CAAC/E,SAAS,EAAEzL,GAAG,CAACsQ,8BAA8B,CAACE,OAAI,CAAC/E,SAAS,EAAErD,KAAK,EAAEmI,QAAQ,CAAC,CAAC;IAAC;EAClH;EACAE,sBAAsBA,CAACrI,KAAK,EAAE;IAC1B,OAAO,IAAI,CAACsI,0BAA0B,CAACtI,KAAK,CAAC;EACjD;EACAsI,0BAA0BA,CAACtI,KAAK,EAAE;IAC9B,OAAOpI,GAAG,CAAC0Q,0BAA0B,CAAC,IAAI,CAACjF,SAAS,EAAErD,KAAK,CAAC;EAChE;EACAuI,qBAAqBA,CAACC,SAAS,EAAE;IAC7B,OAAO5Q,GAAG,CAAC2Q,qBAAqB,CAAC,IAAI,CAAClF,SAAS,EAAEmF,SAAS,CAAC;EAC/D;EACMC,iBAAiBA,CAAA,EAAG;IAAA,IAAAC,OAAA;IAAA,OAAAnO,iBAAA;MACtBkM,SAAS,CAACxM,yBAAyB,CAAC,CAAC,EAAEyO,OAAI,CAACrF,SAAS,EAAE,6CAA6C,CAAC,+CAA+C,CAAC;MACrJ,MAAMzD,UAAU,SAAShI,GAAG,CAAC6Q,iBAAiB,CAACC,OAAI,CAACrF,SAAS,EAAErG,2BAA2B,CAAC;MAC3F,IAAI,CAAC4C,UAAU,EAAE;QACb,OAAO;UACHA,UAAU,EAAE,IAAI;UAChBwC,IAAI,EAAE;QACV,CAAC;MACL;MACA,OAAOL,iBAAiB,CAAC2G,OAAI,CAACrF,SAAS,EAAE7I,OAAO,CAACC,OAAO,CAACmF,UAAU,CAAC,CAAC;IAAC;EAC1E;EACA;EACA;EACA;EACA+I,sBAAsBA,CAACC,SAAS,EAAE;IAC9BhR,GAAG,CAAC+Q,sBAAsB,CAAC,IAAI,CAACtF,SAAS,EAAEuF,SAAS,CAAC;EACzD;EACAC,kBAAkBA,CAACC,cAAc,EAAEC,OAAO,EAAEC,SAAS,EAAE;IACnD,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC;IAAS,CAAC,GAAGC,aAAa,CAACN,cAAc,EAAEC,OAAO,EAAEC,SAAS,CAAC;IACnF,OAAO,IAAI,CAAC3F,SAAS,CAACwF,kBAAkB,CAACI,IAAI,EAAEC,KAAK,EAAEC,QAAQ,CAAC;EACnE;EACAE,gBAAgBA,CAACP,cAAc,EAAEC,OAAO,EAAEC,SAAS,EAAE;IACjD,MAAM;MAAEC,IAAI;MAAEC,KAAK;MAAEC;IAAS,CAAC,GAAGC,aAAa,CAACN,cAAc,EAAEC,OAAO,EAAEC,SAAS,CAAC;IACnF,OAAO,IAAI,CAAC3F,SAAS,CAACgG,gBAAgB,CAACJ,IAAI,EAAEC,KAAK,EAAEC,QAAQ,CAAC;EACjE;EACAG,qBAAqBA,CAACtJ,KAAK,EAAEoF,kBAAkB,EAAE;IAC7C,OAAOxN,GAAG,CAAC0R,qBAAqB,CAAC,IAAI,CAACjG,SAAS,EAAErD,KAAK,EAAEoF,kBAAkB,CAAC;EAC/E;EACAmE,sBAAsBA,CAACvJ,KAAK,EAAEoF,kBAAkB,EAAE;IAC9C,OAAOxN,GAAG,CAAC2R,sBAAsB,CAAC,IAAI,CAAClG,SAAS,EAAErD,KAAK,EAAEoF,kBAAkB,IAAIrF,SAAS,CAAC;EAC7F;EACMyJ,cAAcA,CAAC/N,WAAW,EAAE;IAAA,IAAAgO,OAAA;IAAA,OAAAlP,iBAAA;MAC9BgB,4BAA4B,CAACkO,OAAI,CAACpG,SAAS,EAAE5H,WAAW,CAAC;MACzD,IAAIiO,SAAS;MACb,QAAQjO,WAAW;QACf,KAAKT,WAAW,CAACG,OAAO;UACpBuO,SAAS,GAAG9R,GAAG,CAACiF,yBAAyB;UACzC;QACJ,KAAK7B,WAAW,CAACC,KAAK;UAClB;UACA,MAAM0O,yBAAyB,SAAS/R,GAAG,CACtCuF,YAAY,CAACvF,GAAG,CAACgF,yBAAyB,CAAC,CAC3CgN,YAAY,CAAC,CAAC;UACnBF,SAAS,GAAGC,yBAAyB,GAC/B/R,GAAG,CAACgF,yBAAyB,GAC7BhF,GAAG,CAACiS,uBAAuB;UACjC;QACJ,KAAK7O,WAAW,CAACE,IAAI;UACjBwO,SAAS,GAAG9R,GAAG,CAAC+E,mBAAmB;UACnC;QACJ;UACI,OAAO/E,GAAG,CAACkS,KAAK,CAAC,gBAAgB,CAAC,wCAAwC;YACtErN,OAAO,EAAEgN,OAAI,CAACpG,SAAS,CAAChL;UAC5B,CAAC,CAAC;MACV;MACA,OAAOoR,OAAI,CAACpG,SAAS,CAACmG,cAAc,CAACE,SAAS,CAAC;IAAC;EACpD;EACAK,mCAAmCA,CAACnK,UAAU,EAAE;IAC5C,OAAO,IAAI,CAACoK,oBAAoB,CAACpK,UAAU,CAAC;EAChD;EACAqK,iBAAiBA,CAAA,EAAG;IAChB,OAAOlI,iBAAiB,CAAC,IAAI,CAACsB,SAAS,EAAEzL,GAAG,CAACqS,iBAAiB,CAAC,IAAI,CAAC5G,SAAS,CAAC,CAAC;EACnF;EACA2G,oBAAoBA,CAACpK,UAAU,EAAE;IAC7B,OAAOmC,iBAAiB,CAAC,IAAI,CAACsB,SAAS,EAAEzL,GAAG,CAACoS,oBAAoB,CAAC,IAAI,CAAC3G,SAAS,EAAEzD,UAAU,CAAC,CAAC;EAClG;EACAsK,qBAAqBA,CAACC,KAAK,EAAE;IACzB,OAAOpI,iBAAiB,CAAC,IAAI,CAACsB,SAAS,EAAEzL,GAAG,CAACsS,qBAAqB,CAAC,IAAI,CAAC7G,SAAS,EAAE8G,KAAK,CAAC,CAAC;EAC9F;EACAC,0BAA0BA,CAACpK,KAAK,EAAEmI,QAAQ,EAAE;IACxC,OAAOpG,iBAAiB,CAAC,IAAI,CAACsB,SAAS,EAAEzL,GAAG,CAACwS,0BAA0B,CAAC,IAAI,CAAC/G,SAAS,EAAErD,KAAK,EAAEmI,QAAQ,CAAC,CAAC;EAC7G;EACAkC,mBAAmBA,CAACrK,KAAK,EAAEwI,SAAS,EAAE;IAClC,OAAOzG,iBAAiB,CAAC,IAAI,CAACsB,SAAS,EAAEzL,GAAG,CAACyS,mBAAmB,CAAC,IAAI,CAAChH,SAAS,EAAErD,KAAK,EAAEwI,SAAS,CAAC,CAAC;EACvG;EACA8B,qBAAqBA,CAACrK,WAAW,EAAEoE,mBAAmB,EAAE;IACpD,OAAO5B,yBAAyB,CAAC,IAAI,CAACY,SAAS,EAAEzL,GAAG,CAAC0S,qBAAqB,CAAC,IAAI,CAACjH,SAAS,EAAEpD,WAAW,EAAEoE,mBAAmB,CAAC,CAAC;EACjI;EACMkG,eAAeA,CAACtM,QAAQ,EAAE;IAAA,IAAAuM,OAAA;IAAA,OAAAjQ,iBAAA;MAC5BkM,SAAS,CAACxM,yBAAyB,CAAC,CAAC,EAAEuQ,OAAI,CAACnH,SAAS,EAAE,6CAA6C,CAAC,+CAA+C,CAAC;MACrJ,OAAOtB,iBAAiB,CAACyI,OAAI,CAACnH,SAAS,EAAEzL,GAAG,CAAC2S,eAAe,CAACC,OAAI,CAACnH,SAAS,EAAEpF,QAAQ,EAAEjB,2BAA2B,CAAC,CAAC;IAAC;EACzH;EACMyN,kBAAkBA,CAACxM,QAAQ,EAAE;IAAA,IAAAyM,OAAA;IAAA,OAAAnQ,iBAAA;MAC/BkM,SAAS,CAACxM,yBAAyB,CAAC,CAAC,EAAEyQ,OAAI,CAACrH,SAAS,EAAE,6CAA6C,CAAC,+CAA+C,CAAC;MACrJ,MAAMxH,2BAA2B,CAAC6O,OAAI,CAACrH,SAAS,CAAC;MACjD,OAAOzL,GAAG,CAAC6S,kBAAkB,CAACC,OAAI,CAACrH,SAAS,EAAEpF,QAAQ,EAAEjB,2BAA2B,CAAC;IAAC;EACzF;EACA2N,iBAAiBA,CAACvI,IAAI,EAAE;IACpB;IACA;IACA,OAAO,IAAI,CAACiB,SAAS,CAACsH,iBAAiB,CAACvI,IAAI,CAAC;EACjD;EACAwI,uBAAuBA,CAACrL,IAAI,EAAE;IAC1B,OAAO3H,GAAG,CAACgT,uBAAuB,CAAC,IAAI,CAACvH,SAAS,EAAE9D,IAAI,CAAC;EAC5D;EACAV,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACwE,SAAS;EACzB;EACAwH,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACxH,SAAS,CAACwH,OAAO,CAAC,CAAC;EACnC;EACA/D,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACzD,SAAS,CAACtE,OAAO,GAAG,MAAM,IAAI;EACvC;AACJ;AACA2H,IAAI,CAAC1L,WAAW,GAAGA,WAAW;AAC9B,SAASoO,aAAaA,CAACN,cAAc,EAAEI,KAAK,EAAEC,QAAQ,EAAE;EACpD,IAAIF,IAAI,GAAGH,cAAc;EACzB,IAAI,OAAOA,cAAc,KAAK,UAAU,EAAE;IACtC,CAAC;MAAEG,IAAI;MAAEC,KAAK;MAAEC;IAAS,CAAC,GAAGL,cAAc;EAC/C;EACA;EACA,MAAMgC,OAAO,GAAG7B,IAAI;EACpB,MAAM8B,OAAO,GAAI3I,IAAI,IAAK0I,OAAO,CAAC1I,IAAI,IAAIG,IAAI,CAACC,WAAW,CAACJ,IAAI,CAAC,CAAC;EACjE,OAAO;IACH6G,IAAI,EAAE8B,OAAO;IACb7B,KAAK,EAAEA,KAAK;IACZC;EACJ,CAAC;AACL;AACA,SAASlC,yBAAyBA,CAAC5K,MAAM,EAAEI,OAAO,EAAE;EAChD;EACA;EACA;EACA;EACA,MAAMuO,YAAY,GAAGxO,4BAA4B,CAACH,MAAM,EAAEI,OAAO,CAAC;EAClE;EACA,IAAI,OAAO/D,IAAI,KAAK,WAAW,IAC3B,CAACsS,YAAY,CAACpP,QAAQ,CAAChE,GAAG,CAACgF,yBAAyB,CAAC,EAAE;IACvDoO,YAAY,CAACC,IAAI,CAACrT,GAAG,CAACgF,yBAAyB,CAAC;EACpD;EACA;EACA,IAAI,OAAO7B,MAAM,KAAK,WAAW,EAAE;IAC/B,KAAK,MAAMU,WAAW,IAAI,CACtB7D,GAAG,CAACiS,uBAAuB,EAC3BjS,GAAG,CAACiF,yBAAyB,CAChC,EAAE;MACC,IAAI,CAACmO,YAAY,CAACpP,QAAQ,CAACH,WAAW,CAAC,EAAE;QACrCuP,YAAY,CAACC,IAAI,CAACxP,WAAW,CAAC;MAClC;IACJ;EACJ;EACA;EACA,IAAI,CAACuP,YAAY,CAACpP,QAAQ,CAAChE,GAAG,CAAC+E,mBAAmB,CAAC,EAAE;IACjDqO,YAAY,CAACC,IAAI,CAACrT,GAAG,CAAC+E,mBAAmB,CAAC;EAC9C;EACA,OAAOqO,YAAY;AACvB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM9K,iBAAiB,CAAC;EACpBjD,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmD,UAAU,GAAG,OAAO;IACzB;IACA;IACA,IAAI,CAACiD,SAAS,GAAG,IAAIzL,GAAG,CAACsI,iBAAiB,CAACrB,MAAM,CAAClH,QAAQ,CAAC6D,IAAI,CAAC,CAAC,CAAC,CAAC;EACvE;EACA,OAAOoE,UAAUA,CAACmD,cAAc,EAAEE,gBAAgB,EAAE;IAChD,OAAOrL,GAAG,CAACsI,iBAAiB,CAACN,UAAU,CAACmD,cAAc,EAAEE,gBAAgB,CAAC;EAC7E;EACAiI,iBAAiBA,CAACC,gBAAgB,EAAE9G,mBAAmB,EAAE;IACrD,OAAO,IAAI,CAAChB,SAAS,CAAC6H,iBAAiB;IACvC;IACA;IACAC,gBAAgB,EAAE9G,mBAAmB,CAAC;EAC1C;EACAxF,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACwE,SAAS;EACzB;AACJ;AACAnD,iBAAiB,CAACkL,oBAAoB,GAAGxT,GAAG,CAACsI,iBAAiB,CAACkL,oBAAoB;AACnFlL,iBAAiB,CAACmL,WAAW,GAAGzT,GAAG,CAACsI,iBAAiB,CAACmL,WAAW;;AAEjE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMhQ,OAAO,GAAGzD,GAAG,CAACyD,OAAO;AAC3B,MAAMiQ,iBAAiB,CAAC;EACpBrO,WAAWA,CAACsO,SAAS,EAAEC,UAAU,EAAE7E,GAAG,GAAGhP,QAAQ,CAACgP,GAAG,CAAC,CAAC,EAAE;IACrD,IAAIlO,EAAE;IACN;IACA4C,OAAO,CAAC,CAAC5C,EAAE,GAAGkO,GAAG,CAACI,OAAO,MAAM,IAAI,IAAItO,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC4D,MAAM,EAAE,iBAAiB,CAAC,yCAAyC;MAClII,OAAO,EAAEkK,GAAG,CAACtO;IACjB,CAAC,CAAC;IACF,IAAI,CAACgL,SAAS,GAAG,IAAIzL,GAAG,CAAC0T,iBAAiB,CAACC,SAAS;IACpD;IACAC,UAAU;IACV;IACA;IACA7E,GAAG,CAACnL,IAAI,CAAC,CAAC,CAAC;IACX,IAAI,CAACiQ,IAAI,GAAG,IAAI,CAACpI,SAAS,CAACoI,IAAI;EACnC;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACrI,SAAS,CAACqI,KAAK,CAAC,CAAC;EAC1B;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACtI,SAAS,CAACsI,MAAM,CAAC,CAAC;EAClC;EACAC,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACvI,SAAS,CAACuI,MAAM,CAAC,CAAC;EAClC;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG,aAAa;AAC/B;AACA;AACA,SAASC,kBAAkBA,CAACC,QAAQ,EAAE;EAClCA,QAAQ,CAACC,QAAQ,CAACC,iBAAiB,CAAC,IAAIpU,SAAS,CAACgU,SAAS,EAAEN,SAAS,IAAI;IACtE;IACA,MAAM5E,GAAG,GAAG4E,SAAS,CAACW,WAAW,CAAC,YAAY,CAAC,CAACrF,YAAY,CAAC,CAAC;IAC9D,MAAMsF,YAAY,GAAGZ,SAAS,CAACW,WAAW,CAAC,MAAM,CAAC;IAClD,OAAO,IAAIxF,IAAI,CAACC,GAAG,EAAEwF,YAAY,CAAC;EACtC,CAAC,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAClCC,eAAe,CAAC;IACjBC,cAAc,EAAE;MACZC,SAAS,EAAE;QACPC,YAAY,EAAE3U,GAAG,CAAC4U,mBAAmB,CAACD,YAAY;QAClDE,cAAc,EAAE7U,GAAG,CAAC4U,mBAAmB,CAACC,cAAc;QACtDC,aAAa,EAAE9U,GAAG,CAAC4U,mBAAmB,CAACE,aAAa;QACpDC,6BAA6B,EAAE/U,GAAG,CAAC4U,mBAAmB,CAACG,6BAA6B;QACpFC,uBAAuB,EAAEhV,GAAG,CAAC4U,mBAAmB,CAACI,uBAAuB;QACxEC,YAAY,EAAEjV,GAAG,CAAC4U,mBAAmB,CAACK;MAC1C;IACJ,CAAC;IACDC,iBAAiB,EAAElV,GAAG,CAACkV,iBAAiB;IACxCpM,oBAAoB,EAAE9I,GAAG,CAAC8I,oBAAoB;IAC9CE,kBAAkB,EAAEhJ,GAAG,CAACgJ,kBAAkB;IAC1CJ,kBAAkB,EAAE5I,GAAG,CAAC4I,kBAAkB;IAC1CoB,aAAa,EAAEhK,GAAG,CAACgK,aAAa;IAChCmL,gBAAgB,EAAEnV,GAAG,CAACmV,gBAAgB;IACtC7M,iBAAiB,EAAEA,iBAAiB;IACpC8M,yBAAyB,EAAEpV,GAAG,CAACoV,yBAAyB;IACxD1B,iBAAiB,EAAEA,iBAAiB;IACpCxK,mBAAmB,EAAElJ,GAAG,CAACkJ,mBAAmB;IAC5C4F,IAAI;IACJuG,cAAc,EAAErV,GAAG,CAACqV,cAAc;IAClCC,KAAK,EAAE9U;EACX,CAAC,CAAC,CACG+U,oBAAoB,CAAC,MAAM,CAAC,4BAA4B,CAAC,CACzDC,oBAAoB,CAAC,KAAK,CAAC,CAAC;EACjCrB,QAAQ,CAACsB,eAAe,CAAChV,IAAI,EAAEC,OAAO,CAAC;AAC3C;AACAwT,kBAAkB,CAACnU,QAAQ,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}