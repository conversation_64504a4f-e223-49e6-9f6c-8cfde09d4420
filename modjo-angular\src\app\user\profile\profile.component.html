<div class="profile-container">
  <div *ngIf="isLoading" class="loading">
    <p>Chargement du profil...</p>
  </div>

  <div *ngIf="error" class="error">
    <p>{{ error }}</p>
  </div>

  <div *ngIf="!isLoading && !error && user" class="profile-content">
    <div class="profile-header">
      <div class="profile-image">
        <img [src]="user.photoURL || 'assets/default-avatar.png'" alt="Photo de profil">
      </div>
      <div class="profile-info">
        <h1>{{ user.displayName }}</h1>
        <p class="role">{{ user.role === 'user' ? 'Utilisateur' : user.role === 'provider' ? 'Prestataire' : user.role === 'validator' ? 'Validateur' : 'Administrateur' }}</p>
        <p class="location">{{ user.city }}</p>
        <p *ngIf="user.activity" class="activity">{{ user.activity }}</p>
        
        <div class="points-badge">
          <span class="points-count">{{ user.points }}</span>
          <span class="points-label">points</span>
        </div>
      </div>
    </div>

    <div class="profile-actions">
      <a routerLink="/scanner" class="action-button">
        <span class="icon">📷</span>
        Scanner un QR code
      </a>
      <a routerLink="/rewards" class="action-button">
        <span class="icon">🎁</span>
        Voir les récompenses
      </a>
    </div>

    <div class="points-history">
      <h2>Historique des points</h2>
      
      <div *ngIf="pointHistory && pointHistory.transactions.length > 0" class="transactions">
        <div *ngFor="let transaction of pointHistory.transactions" class="transaction-item">
          <div class="transaction-info">
            <p class="transaction-description">{{ transaction.description }}</p>
            <p class="transaction-date">{{ transaction.createdAt | date:'dd/MM/yyyy HH:mm' }}</p>
          </div>
          <div class="transaction-points" [ngClass]="{'positive': transaction.amount > 0, 'negative': transaction.amount < 0}">
            {{ transaction.amount > 0 ? '+' : '' }}{{ transaction.amount }} pts
          </div>
        </div>
      </div>
      
      <div *ngIf="!pointHistory || pointHistory.transactions.length === 0" class="no-transactions">
        <p>Aucune transaction pour le moment</p>
        <a routerLink="/scanner" class="start-scanning">Commencer à scanner des QR codes</a>
      </div>
    </div>
  </div>
</div>
