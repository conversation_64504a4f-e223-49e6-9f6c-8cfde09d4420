import { Injectable } from '@angular/core';
import { BehaviorSubject, Observable } from 'rxjs';
import { QrScanResult, QrCodeData, QrCodeType, User } from '../models';
import { UserService } from './user.service';
import { AuthService } from './auth.service';

@Injectable({
  providedIn: 'root'
})
export class QrService {
  private scanResultSubject = new BehaviorSubject<QrScanResult | null>(null);
  public scanResult$ = this.scanResultSubject.asObservable();

  constructor(
    private userService: UserService,
    private authService: AuthService
  ) {}

  // Process scanned QR code
  async processScanResult(rawData: string, location?: any): Promise<QrScanResult> {
    const scanResult: QrScanResult = {
      data: rawData,
      format: 'QR_CODE',
      timestamp: new Date(),
      location
    };

    this.scanResultSubject.next(scanResult);
    return scanResult;
  }

  // Parse QR code data
  parseQrData(rawData: string): QrCodeData | null {
    try {
      // Try to parse as JSON first
      const parsed = JSON.parse(rawData);
      if (this.isValidQrCodeData(parsed)) {
        return parsed as QrCodeData;
      }
    } catch {
      // If not JSON, try to parse as simple formats
      return this.parseSimpleQrData(rawData);
    }
    return null;
  }

  // Generate QR code data for user transfers
  generateUserTransferQr(userId: string, points?: number): string {
    const qrData: QrCodeData = {
      type: QrCodeType.USER_TRANSFER,
      userId,
      points,
      timestamp: Date.now(),
      signature: this.generateSignature(userId, points)
    };
    return JSON.stringify(qrData);
  }

  // Generate QR code for validator actions
  generateValidatorQr(validatorId: string, action: string, points: number): string {
    const qrData: QrCodeData = {
      type: QrCodeType.VALIDATOR_ACTION,
      validatorId,
      action,
      points,
      timestamp: Date.now(),
      signature: this.generateSignature(validatorId, points, action)
    };
    return JSON.stringify(qrData);
  }

  // Handle different types of QR scans
  async handleQrScan(qrData: QrCodeData): Promise<any> {
    const currentUser = this.authService.getCurrentUser();
    if (!currentUser) {
      throw new Error('User not authenticated');
    }

    switch (qrData.type) {
      case QrCodeType.USER_TRANSFER:
        return this.handleUserTransfer(qrData, currentUser);
      
      case QrCodeType.VALIDATOR_ACTION:
        return this.handleValidatorAction(qrData, currentUser);
      
      case QrCodeType.PARTNER_REWARD:
        return this.handlePartnerReward(qrData, currentUser);
      
      case QrCodeType.SYSTEM_BONUS:
        return this.handleSystemBonus(qrData, currentUser);
      
      default:
        throw new Error('Unknown QR code type');
    }
  }

  private async handleUserTransfer(qrData: QrCodeData, currentUser: User): Promise<any> {
    if (!qrData.userId) {
      throw new Error('Invalid user transfer QR code');
    }

    // For demo purposes, we'll simulate a simple point transfer
    const points = qrData.points || 1; // Default 1 point if not specified
    
    return {
      type: 'user_transfer',
      targetUserId: qrData.userId,
      points,
      message: `Ready to transfer ${points} point(s) to user`
    };
  }

  private async handleValidatorAction(qrData: QrCodeData, currentUser: User): Promise<any> {
    if (currentUser.role !== 'validator') {
      throw new Error('Only validators can process validator QR codes');
    }

    return {
      type: 'validator_action',
      action: qrData.action,
      points: qrData.points || 10,
      message: `Validation action: ${qrData.action}`
    };
  }

  private async handlePartnerReward(qrData: QrCodeData, currentUser: User): Promise<any> {
    return {
      type: 'partner_reward',
      points: qrData.points || 5,
      message: 'Partner reward scanned'
    };
  }

  private async handleSystemBonus(qrData: QrCodeData, currentUser: User): Promise<any> {
    return {
      type: 'system_bonus',
      points: qrData.points || 3,
      message: 'System bonus activated'
    };
  }

  // Validate QR code data structure
  private isValidQrCodeData(data: any): boolean {
    return data &&
           typeof data.type === 'string' &&
           Object.values(QrCodeType).includes(data.type) &&
           typeof data.timestamp === 'number';
  }

  // Parse simple QR data formats (for demo/testing)
  private parseSimpleQrData(rawData: string): QrCodeData | null {
    // Handle simple formats like "user:123" or "points:5"
    if (rawData.startsWith('user:')) {
      const userId = rawData.substring(5);
      return {
        type: QrCodeType.USER_TRANSFER,
        userId,
        timestamp: Date.now()
      };
    }

    if (rawData.startsWith('validator:')) {
      const parts = rawData.substring(10).split(':');
      return {
        type: QrCodeType.VALIDATOR_ACTION,
        validatorId: parts[0],
        action: parts[1] || 'community_service',
        points: parseInt(parts[2]) || 10,
        timestamp: Date.now()
      };
    }

    // Default: treat as user ID
    return {
      type: QrCodeType.USER_TRANSFER,
      userId: rawData,
      timestamp: Date.now()
    };
  }

  // Generate simple signature for QR codes (for demo purposes)
  private generateSignature(userId: string, points?: number, action?: string): string {
    const data = `${userId}-${points || 0}-${action || ''}-${Date.now()}`;
    return btoa(data).substring(0, 16);
  }

  // Generate sample QR codes for testing
  generateSampleQrCodes(): string[] {
    return [
      this.generateUserTransferQr('user123', 1),
      this.generateUserTransferQr('user456', 3),
      this.generateValidatorQr('validator789', 'community_service', 10),
      JSON.stringify({
        type: QrCodeType.PARTNER_REWARD,
        points: 5,
        timestamp: Date.now()
      }),
      JSON.stringify({
        type: QrCodeType.SYSTEM_BONUS,
        points: 3,
        timestamp: Date.now()
      })
    ];
  }

  // Clear scan result
  clearScanResult(): void {
    this.scanResultSubject.next(null);
  }
}
