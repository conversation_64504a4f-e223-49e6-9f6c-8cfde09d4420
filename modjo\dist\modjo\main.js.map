{"version": 3, "file": "main.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;AACwD;AACX;;;;;;;;;;;ICwBrCG,4DADF,cAA6E,eACjE;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,WAAM;IAAAA,oDAAA,GAAqB;IAC7BA,0DAD6B,EAAO,EAC9B;;;;IADEA,uDAAA,GAAqB;IAArBA,gEAAA,KAAAM,OAAA,CAAAC,MAAA,SAAqB;;;;;IAQ3BP,4DAJF,iBAGiD,eACrC;IAAAA,oDAAA,qBAAc;IAC1BA,0DAD0B,EAAW,EAC5B;;;;;IAJDA,wDAAA,sBAAAS,WAAA,CAA8B;;;ADtBtC,MAAOC,YAAY;EAIvBC,YACSC,WAAwB,EACvBC,MAAc;IADf,KAAAD,WAAW,GAAXA,WAAW;IACV,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAC,KAAK,GAAG,OAAO;IACf,KAAAC,gBAAgB,GAAG,iBAAiB;EAKjC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,MAAM,CAACI,MAAM,CAACC,IAAI,CACrBpB,sDAAM,CAACqB,KAAK,IAAIA,KAAK,YAAYtB,0DAAa,CAAC,EAC/CE,mDAAG,CAAC,MAAM,IAAI,CAACc,MAAM,CAACO,GAAG,CAAC,CAC3B,CAACC,SAAS,CAACD,GAAG,IAAG;MAChB,IAAI,CAACE,eAAe,CAACF,GAAG,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEAG,YAAYA,CAAA;IACV,OAAO,IAAI,CAACR,gBAAgB;EAC9B;EAEQO,eAAeA,CAACF,GAAW;IACjC,MAAMI,QAAQ,GAA8B;MAC1C,YAAY,EAAE,iBAAiB;MAC/B,UAAU,EAAE,QAAQ;MACpB,aAAa,EAAE,YAAY;MAC3B,UAAU,EAAE,aAAa;MACzB,aAAa,EAAE,YAAY;MAC3B,QAAQ,EAAE;KACX;IAED;IACA,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,IAAI,CAACC,KAAK,IAAIT,GAAG,CAACU,UAAU,CAACD,KAAK,CAAC,CAAC;IAC/E,IAAI,CAACd,gBAAgB,GAAGU,YAAY,GAAGD,QAAQ,CAACC,YAAY,CAAC,GAAG,OAAO;EACzE;EAEMM,MAAMA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,wJAAA;MACV,IAAI;QACF,MAAMD,KAAI,CAACpB,WAAW,CAACmB,MAAM,EAAE;QAC/BC,KAAI,CAACnB,MAAM,CAACqB,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;OACtC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;;IACtC;EACH;;;uBA7CWzB,YAAY,EAAAV,+DAAA,CAAAsC,oEAAA,GAAAtC,+DAAA,CAAAwC,mDAAA;IAAA;EAAA;;;YAAZ9B,YAAY;MAAAgC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCNnBhD,4DAJN,aAA2B,aAEc,qBACa,aAC9B;UAAAA,oDAAA,mBAAE;UAAAA,0DAAA,EAAM;UAC1BA,4DAAA,cAA4B;UAAAA,oDAAA,gBAAS;UAAAA,0DAAA,EAAO;UAC5CA,uDAAA,cAA4B;UAO1BA,4DAJF,gBAG2B,eACf;UAAAA,oDAAA,iBAAS;UAAAA,0DAAA,EAAW;UAC9BA,oDAAA,mBACF;UAAAA,0DAAA,EAAS;UAKPA,4DAHF,iBAE2B,gBACf;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAW;UAC1BA,oDAAA,cACF;UAAAA,0DAAA,EAAS;UAGTA,wDAAA,KAAAoD,4BAAA,iBAA6E;;UAM7EpD,wDAAA,KAAAqD,+BAAA,qBAGiD;;UAM7CrD,4DAFJ,uBAAgD,kBACF,gBAChC;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAW;UAC3BA,4DAAA,YAAM;UAAAA,oDAAA,cAAM;UACdA,0DADc,EAAO,EACZ;UACTA,4DAAA,kBAAyC;UAAnBA,wDAAA,mBAAAuD,+CAAA;YAAAvD,2DAAA,CAAAyD,GAAA;YAAA,OAAAzD,yDAAA,CAASiD,GAAA,CAAAlB,MAAA,EAAQ;UAAA,EAAC;UACtC/B,4DAAA,gBAAU;UAAAA,oDAAA,cAAM;UAAAA,0DAAA,EAAW;UAC3BA,4DAAA,YAAM;UAAAA,oDAAA,wBAAW;UAGvBA,0DAHuB,EAAO,EACjB,EACA,EACC;UAGZA,4DADF,gBAA2B,eACa;UACpCA,uDAAA,qBAA+B;UAIvCA,0DAHM,EAAM,EACD,EACH,EACF;;;UA/B6BA,uDAAA,IAAuC;UAAvCA,wDAAA,SAAAA,yDAAA,QAAAiD,GAAA,CAAArC,WAAA,CAAAgD,YAAA,EAAuC;UAS3D5D,uDAAA,GAAsC;UAAtCA,wDAAA,SAAAA,yDAAA,QAAAiD,GAAA,CAAArC,WAAA,CAAAgD,YAAA,EAAsC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACjCK;AACqB;AAChC;AACe;AAE9D;AACA;AACA;AACA;AACA;AAEA;AAC6D;AACF;AACJ;AACM;AACN;AACA;AACS;AACT;AACM;AAEd;AACN;AACiB;;;;AAmCpD,MAAOgB,SAAS;;;uBAATA,SAAS;IAAA;EAAA;;;YAATA,SAAS;MAAAC,SAAA,GAFRnE,wDAAY;IAAA;EAAA;;;gBA1BtBmD,oEAAa,EACbC,yFAAuB,EACvBC,yDAAY,CAACe,OAAO,CAACJ,kDAAS,CAAC,EAC/BV,wEAAmB,CAACe,QAAQ,CAAC,gBAAgB,EAAE;QAC7CC,OAAO,EAAEL,kEAAW,CAACM,UAAU;QAC/BC,oBAAoB,EAAE;OACvB,CAAC;MAEF;MACA;MACA;MACA;MACA;MAEA;MACAjB,uEAAgB,EAChBC,qEAAe,EACfC,kEAAa,EACbC,wEAAgB,EAChBC,kEAAa,EACbC,kEAAa,EACbC,2EAAiB,EACjBC,kEAAa,EACbC,wEAAgB;IAAA;EAAA;;;sHAKPG,SAAS;IAAAO,YAAA,GA/BlBzE,wDAAY;IAAA0E,OAAA,GAGZvB,oEAAa,EACbC,yFAAuB,EAAAxB,yDAAA,EAAAE,wEAAA;IAOvB;IACA;IACA;IACA;IACA;IAEA;IACAyB,uEAAgB,EAChBC,qEAAe,EACfC,kEAAa,EACbC,wEAAgB,EAChBC,kEAAa,EACbC,kEAAa,EACbC,2EAAiB,EACjBC,kEAAa,EACbC,wEAAgB;EAAA;AAAA;;;;;;;;;;;;;;ACnDb,MAAMC,SAAS,GAAW,CAC/B;EACEW,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,oZAAqC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU;CACjF,EACD;EACEN,IAAI,EAAE,WAAW;EACjBG,YAAY,EAAEA,CAAA,KAAM,8LAA+C,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,eAAe;EAC/F;CACD,EACD;EACEP,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,ygBAA2C,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,aAAa;EACzF;CACD,EACD;EACER,IAAI,EAAE,YAAY;EAClBG,YAAY,EAAEA,CAAA,KAAM,ykBAAiD,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,eAAe;EACjG;CACD,EACD;EACET,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,onBAA2C,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,aAAa;EACzF;CACD;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEV,IAAI,EAAE,cAAc;EACpBW,aAAa,EAAEA,CAAA,KAAM,kOAAiE,CAACP,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAACC,qBAAqB;CACzH,EACD;EACEb,IAAI,EAAE,IAAI;EACVW,aAAa,EAAEA,CAAA,KAAM,oNAA2D,CAACP,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAACE,iBAAiB;CAC/G,CACF;;;;;;;;;;;;;;;;;;;;;;;;;ACrDD;AAC6B;AAE7B;AAC+B;AAE/B;AACmC;AAyC5B,IAAKC,gBAOX;AAPD,WAAYA,gBAAgB;EAC1BA,gBAAA,mCAA+B;EAC/BA,gBAAA,iCAA6B;EAC7BA,gBAAA,+CAA2C;EAC3CA,gBAAA,+CAA2C;EAC3CA,gBAAA,yCAAqC;EACrCA,gBAAA,+CAA2C;AAC7C,CAAC,EAPWA,gBAAgB,KAAhBA,gBAAgB;;;;;;;;;;;;;;;;AC5BrB,IAAKC,cASX;AATD,WAAYA,cAAc;EACxBA,cAAA,iBAAa;EACbA,cAAA,yBAAqB;EACrBA,cAAA,mCAA+B;EAC/BA,cAAA,yBAAqB;EACrBA,cAAA,qBAAiB;EACjBA,cAAA,2BAAuB;EACvBA,cAAA,2BAAuB;EACvBA,cAAA,mBAAe;AACjB,CAAC,EATWA,cAAc,KAAdA,cAAc;AA6BnB,IAAKC,eASX;AATD,WAAYA,eAAe;EACzBA,eAAA,6BAAyB;EACzBA,eAAA,iBAAa;EACbA,eAAA,qBAAiB;EACjBA,eAAA,uBAAmB;EACnBA,eAAA,mCAA+B;EAC/BA,eAAA,qBAAiB;EACjBA,eAAA,2BAAuB;EACvBA,eAAA,mBAAe;AACjB,CAAC,EATWA,eAAe,KAAfA,eAAe;AAwBpB,IAAKC,cAMX;AAND,WAAYA,cAAc;EACxBA,cAAA,uBAAmB;EACnBA,cAAA,2BAAuB;EACvBA,cAAA,iBAAa;EACbA,cAAA,uBAAmB;EACnBA,cAAA,2BAAuB;AACzB,CAAC,EANWA,cAAc,KAAdA,cAAc;AAiC1B;;;;;;;;;;;;;;;ACxEO,IAAKC,QAKX;AALD,WAAYA,QAAQ;EAClBA,QAAA,iBAAa;EACbA,QAAA,yBAAqB;EACrBA,QAAA,2BAAuB;EACvBA,QAAA,mBAAe;AACjB,CAAC,EALWA,QAAQ,KAARA,QAAQ;AAoBb,IAAKC,eAMX;AAND,WAAYA,eAAe;EACzBA,eAAA,qBAAiB;EACjBA,eAAA,mBAAe;EACfA,eAAA,+BAA2B;EAC3BA,eAAA,2BAAuB;EACvBA,eAAA,mBAAe;AACjB,CAAC,EANWA,eAAe,KAAfA,eAAe;;;;;;;;;;;;;;;;ACpCpB,IAAKC,gBASX;AATD,WAAYA,gBAAgB;EAC1BA,gBAAA,2CAAuC;EACvCA,gBAAA,iDAA6C;EAC7CA,gBAAA,qDAAiD;EACjDA,gBAAA,uCAAmC;EACnCA,gBAAA,iDAA6C;EAC7CA,gBAAA,qCAAiC;EACjCA,gBAAA,qDAAiD;EACjDA,gBAAA,mBAAe;AACjB,CAAC,EATWA,gBAAgB,KAAhBA,gBAAgB;AAWrB,IAAKC,gBAKX;AALD,WAAYA,gBAAgB;EAC1BA,gBAAA,uBAAmB;EACnBA,gBAAA,yBAAqB;EACrBA,gBAAA,yBAAqB;EACrBA,gBAAA,uBAAmB;AACrB,CAAC,EALWA,gBAAgB,KAAhBA,gBAAgB;AA+BrB,IAAKC,UAKX;AALD,WAAYA,UAAU;EACpBA,UAAA,mCAA+B;EAC/BA,UAAA,yCAAqC;EACrCA,UAAA,qCAAiC;EACjCA,UAAA,iCAA6B;AAC/B,CAAC,EALWA,UAAU,KAAVA,UAAU;AAiCtB;;;;;;;;;;;;;;;;;;;AC5FA;AACA;AAC6E;AACf;;AAKxD,MAAOrE,WAAW;EAItB5B,YAAA;IAHQ,KAAAmG,kBAAkB,GAAG,IAAID,iDAAe,CAAc,IAAI,CAAC;IAC5D,KAAAjD,YAAY,GAAG,IAAI,CAACkD,kBAAkB,CAACC,YAAY,EAAE;IAM1D;IACA;IACA,MAAMC,QAAQ,GAAS;MACrBC,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAEb,6CAAQ,CAACc,IAAI;MACnB/G,MAAM,EAAE,GAAG;MACXgH,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,SAAS,EAAE,IAAID,IAAI,EAAE;MACrBE,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE;KACV;IACD,IAAI,CAACb,kBAAkB,CAACc,IAAI,CAACZ,QAAQ,CAAC;EACxC;EAEMjC,QAAQA,CAAC8C,QAA2B;IAAA,IAAA7F,KAAA;IAAA,OAAAC,wJAAA;MACxC;MACA,MAAM6F,IAAI,GAAS;QACjBb,GAAG,EAAE,YAAY,GAAGO,IAAI,CAACO,GAAG,EAAE;QAC9Bb,KAAK,EAAEW,QAAQ,CAACX,KAAK;QACrBC,IAAI,EAAEU,QAAQ,CAACV,IAAI;QACnBa,KAAK,EAAEH,QAAQ,CAACG,KAAK;QACrBZ,IAAI,EAAES,QAAQ,CAACT,IAAI;QACnBC,IAAI,EAAEQ,QAAQ,CAACR,IAAI;QACnB9G,MAAM,EAAE,CAAC;QACTgH,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,SAAS,EAAE,IAAID,IAAI,EAAE;QACrBE,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE;OACV;MAED3F,KAAI,CAAC8E,kBAAkB,CAACc,IAAI,CAACE,IAAI,CAAC;MAClC,OAAOG,OAAO,CAACC,OAAO,CAACJ,IAAI,CAAC;IAAC;EAC/B;EAEMK,KAAKA,CAACjB,KAAa,EAAEkB,QAAgB;IAAA,IAAAC,MAAA;IAAA,OAAApG,wJAAA;MACzC;MACA,MAAMqG,SAAS,GAA4B;QACzC,eAAe,EAAE;UACfrB,GAAG,EAAE,UAAU;UACfC,KAAK,EAAE,eAAe;UACtBC,IAAI,EAAE,kBAAkB;UACxBC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAEb,6CAAQ,CAACc,IAAI;UACnB/G,MAAM,EAAE,GAAG;UACXgH,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,SAAS,EAAE,IAAID,IAAI,EAAE;UACrBE,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE;SACV;QACD,gBAAgB,EAAE;UAChBV,GAAG,EAAE,WAAW;UAChBC,KAAK,EAAE,gBAAgB;UACvBC,IAAI,EAAE,YAAY;UAClBC,IAAI,EAAE,QAAQ;UACdC,IAAI,EAAEb,6CAAQ,CAAC+B,KAAK;UACpBhI,MAAM,EAAE,GAAG;UACXgH,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,SAAS,EAAE,IAAID,IAAI,EAAE;UACrBE,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE;SACV;QACD,oBAAoB,EAAE;UACpBV,GAAG,EAAE,eAAe;UACpBC,KAAK,EAAE,oBAAoB;UAC3BC,IAAI,EAAE,iBAAiB;UACvBC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAEb,6CAAQ,CAACgC,SAAS;UACxBjI,MAAM,EAAE,GAAG;UACXgH,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,SAAS,EAAE,IAAID,IAAI,EAAE;UACrBE,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE;;OAEZ;MAED,MAAMG,IAAI,GAAGQ,SAAS,CAACpB,KAAK,CAAC;MAC7B,IAAIY,IAAI,IAAIM,QAAQ,KAAK,aAAa,EAAE;QACtCC,MAAI,CAACvB,kBAAkB,CAACc,IAAI,CAACE,IAAI,CAAC;QAClC,OAAOG,OAAO,CAACC,OAAO,CAACJ,IAAI,CAAC;;MAG9B,MAAM,IAAIW,KAAK,CAAC,qBAAqB,CAAC;IAAC;EACzC;EAEM1G,MAAMA,CAAA;IAAA,IAAA2G,MAAA;IAAA,OAAAzG,wJAAA;MACVyG,MAAI,CAAC5B,kBAAkB,CAACc,IAAI,CAAC,IAAI,CAAC;MAClC,OAAOK,OAAO,CAACC,OAAO,EAAE;IAAC;EAC3B;EAEcS,WAAWA,CAAC1B,GAAW;IAAA,OAAAhF,wJAAA;MACnC;MACA,MAAM,IAAIwG,KAAK,CAAC,qBAAqB,CAAC;IAAC;EACzC;EAEAG,cAAcA,CAAA;IACZ,OAAO,IAAI,CAAC9B,kBAAkB,CAAC+B,KAAK;EACtC;EAEAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAAChC,kBAAkB,CAAC+B,KAAK,KAAK,IAAI;EAC/C;EAEAE,OAAOA,CAAC1B,IAAc;IACpB,MAAMS,IAAI,GAAG,IAAI,CAACc,cAAc,EAAE;IAClC,OAAOd,IAAI,EAAET,IAAI,KAAKA,IAAI;EAC5B;EAEA2B,UAAUA,CAACC,KAAiB;IAC1B,MAAMnB,IAAI,GAAG,IAAI,CAACc,cAAc,EAAE;IAClC,OAAOd,IAAI,GAAGmB,KAAK,CAACC,QAAQ,CAACpB,IAAI,CAACT,IAAI,CAAC,GAAG,KAAK;EACjD;EAEM8B,iBAAiBA,CAACC,OAAsB;IAAA,IAAAC,MAAA;IAAA,OAAApH,wJAAA;MAC5C,MAAM6F,IAAI,GAAGuB,MAAI,CAACT,cAAc,EAAE;MAClC,IAAI,CAACd,IAAI,EAAE,MAAM,IAAIW,KAAK,CAAC,uBAAuB,CAAC;MAEnD,MAAMa,WAAW,GAAG;QAAE,GAAGxB,IAAI;QAAE,GAAGsB,OAAO;QAAE3B,SAAS,EAAE,IAAID,IAAI;MAAE,CAAE;MAClE6B,MAAI,CAACvC,kBAAkB,CAACc,IAAI,CAAC0B,WAAW,CAAC;MACzC,OAAOrB,OAAO,CAACC,OAAO,EAAE;IAAC;EAC3B;EAEA;EACMqB,aAAaA,CAACrC,KAAa;IAAA,OAAAjF,wJAAA;MAC/B;MACA;MACAG,OAAO,CAACoH,GAAG,CAAC,+BAA+B,EAAEtC,KAAK,CAAC;IAAC;EACtD;;;uBAzIW3E,WAAW;IAAA;EAAA;;;aAAXA,WAAW;MAAAkH,OAAA,EAAXlH,WAAW,CAAAmH,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA;;;;;;;;;;;;;;;ACPb,MAAMhF,WAAW,GAAG;EACzBM,UAAU,EAAE,KAAK;EACjB2E,QAAQ,EAAE;IACRC,MAAM,EAAE,cAAc;IACtBC,UAAU,EAAE,2BAA2B;IACvCC,SAAS,EAAE,WAAW;IACtBC,aAAa,EAAE,uBAAuB;IACtCC,iBAAiB,EAAE,WAAW;IAC9BC,KAAK,EAAE;GACR;EACDC,GAAG,EAAE;IACHhD,IAAI,EAAE,OAAO;IACbiD,OAAO,EAAE,OAAO;IAChBzF,WAAW,EAAE;GACd;EACD0F,QAAQ,EAAE;IACRC,iBAAiB,EAAE,IAAI;IACvBC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,IAAI;IACdC,aAAa,EAAE;;CAElB;;;;;;;;;;;;;;ACnB4C;AAE7CC,sEAAA,EAAwB,CAACE,eAAe,CAAChG,sDAAS,CAAC,CAChDiG,KAAK,CAACC,GAAG,IAAI1I,OAAO,CAACD,KAAK,CAAC2I,GAAG,CAAC,CAAC", "sources": ["./src/app/app.component.ts", "./src/app/app.component.html", "./src/app/app.module.ts", "./src/app/app.routes.ts", "./src/app/core/models/index.ts", "./src/app/core/models/reward.model.ts", "./src/app/core/models/user.model.ts", "./src/app/core/models/validation.model.ts", "./src/app/core/services/auth.service.ts", "./src/environments/environment.ts", "./src/main.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { filter, map } from 'rxjs/operators';\nimport { AuthService } from './core/services/auth.service';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent implements OnInit {\n  title = 'Modjo';\n  currentPageTitle = 'Tableau de bord';\n\n  constructor(\n    public authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    // Listen to route changes to update page title\n    this.router.events.pipe(\n      filter(event => event instanceof NavigationEnd),\n      map(() => this.router.url)\n    ).subscribe(url => {\n      this.updatePageTitle(url);\n    });\n  }\n\n  getPageTitle(): string {\n    return this.currentPageTitle;\n  }\n\n  private updatePageTitle(url: string): void {\n    const titleMap: { [key: string]: string } = {\n      '/dashboard': 'Tableau de bord',\n      '/profile': 'Profil',\n      '/qr-scanner': 'Scanner QR',\n      '/rewards': 'Récompenses',\n      '/validation': 'Validation',\n      '/admin': 'Administration'\n    };\n\n    // Find matching route\n    const matchedRoute = Object.keys(titleMap).find(route => url.startsWith(route));\n    this.currentPageTitle = matchedRoute ? titleMap[matchedRoute] : 'Modjo';\n  }\n\n  async logout(): Promise<void> {\n    try {\n      await this.authService.logout();\n      this.router.navigate(['/auth/login']);\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  }\n}\n", "<div class=\"app-container\">\n  <!-- Enhanced main layout -->\n  <div class=\"welcome-container fade-in\">\n    <mat-toolbar color=\"primary\" class=\"main-toolbar\">\n      <div class=\"logo\">🌟</div>\n      <span class=\"toolbar-title\">Modjo <PERSON></span>\n      <span class=\"spacer\"></span>\n\n      <!-- Navigation buttons -->\n      <button mat-raised-button\n              color=\"accent\"\n              routerLink=\"/dashboard\"\n              class=\"nav-button\">\n        <mat-icon>dashboard</mat-icon>\n        Dashboard\n      </button>\n\n      <button mat-stroked-button\n              routerLink=\"/auth\"\n              class=\"nav-button\">\n        <mat-icon>login</mat-icon>\n        Auth\n      </button>\n\n      <!-- Points display (when user is logged in) -->\n      <div class=\"points-display\" *ngIf=\"authService.currentUser$ | async as user\">\n        <mat-icon>stars</mat-icon>\n        <span>{{ user.points }} pts</span>\n      </div>\n\n      <!-- User menu -->\n      <button mat-icon-button\n              [matMenuTriggerFor]=\"userMenu\"\n              class=\"user-avatar\"\n              *ngIf=\"authService.currentUser$ | async\">\n        <mat-icon>account_circle</mat-icon>\n      </button>\n\n      <mat-menu #userMenu=\"matMenu\" class=\"user-menu\">\n        <button mat-menu-item routerLink=\"/profile\">\n          <mat-icon>person</mat-icon>\n          <span>Profil</span>\n        </button>\n        <button mat-menu-item (click)=\"logout()\">\n          <mat-icon>logout</mat-icon>\n          <span>Déconnexion</span>\n        </button>\n      </mat-menu>\n    </mat-toolbar>\n\n    <main class=\"main-content\">\n      <div class=\"content-wrapper slide-up\">\n        <router-outlet></router-outlet>\n      </div>\n    </main>\n  </div>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { BrowserModule } from '@angular/platform-browser';\nimport { BrowserAnimationsModule } from '@angular/platform-browser/animations';\nimport { RouterModule } from '@angular/router';\nimport { ServiceWorkerModule } from '@angular/service-worker';\n\n// Firebase - temporarily disabled for initial setup\n// import { AngularFireModule } from '@angular/fire/compat';\n// import { AngularFireAuthModule } from '@angular/fire/compat/auth';\n// import { AngularFirestoreModule } from '@angular/fire/compat/firestore';\n// import { AngularFireStorageModule } from '@angular/fire/compat/storage';\n\n// Material\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatDividerModule } from '@angular/material/divider';\n\nimport { AppComponent } from './app.component';\nimport { appRoutes } from './app.routes';\nimport { environment } from '../environments/environment';\n\n@NgModule({\n  declarations: [\n    AppComponent\n  ],\n  imports: [\n    BrowserModule,\n    BrowserAnimationsModule,\n    RouterModule.forRoot(appRoutes),\n    ServiceWorkerModule.register('ngsw-worker.js', {\n      enabled: environment.production,\n      registrationStrategy: 'registerWhenStable:30000'\n    }),\n\n    // Firebase - temporarily disabled for initial setup\n    // AngularFireModule.initializeApp(environment.firebase),\n    // AngularFireAuthModule,\n    // AngularFirestoreModule,\n    // AngularFireStorageModule,\n\n    // Material modules\n    MatToolbarModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSidenavModule,\n    MatListModule,\n    MatCardModule,\n    MatSnackBarModule,\n    MatMenuModule,\n    MatDividerModule\n  ],\n  providers: [],\n  bootstrap: [AppComponent]\n})\nexport class AppModule { }\n", "import { Routes } from '@angular/router';\nimport { AuthGuard, AdminGuard, ValidatorGuard } from './core/guards/auth.guard';\nimport { UserRole } from './core/models';\n\nexport const appRoutes: Routes = [\n  {\n    path: '',\n    redirectTo: '/dashboard',\n    pathMatch: 'full'\n  },\n  {\n    path: 'auth',\n    loadChildren: () => import('./features/auth/auth.module').then(m => m.AuthModule)\n  },\n  {\n    path: 'dashboard',\n    loadChildren: () => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule)\n    // canActivate: [AuthGuard] // Temporarily disabled\n  },\n  {\n    path: 'profile',\n    loadChildren: () => import('./features/profile/profile.module').then(m => m.ProfileModule)\n    // canActivate: [AuthGuard] // Temporarily disabled\n  },\n  {\n    path: 'qr-scanner',\n    loadChildren: () => import('./features/qr-scanner/qr-scanner.module').then(m => m.QrScannerModule)\n    // canActivate: [AuthGuard] // Temporarily disabled\n  },\n  {\n    path: 'rewards',\n    loadChildren: () => import('./features/rewards/rewards.module').then(m => m.RewardsModule)\n    // canActivate: [AuthGuard] // Temporarily disabled\n  },\n  // {\n  //   path: 'validation',\n  //   loadChildren: () => import('./features/validation/validation.module').then(m => m.ValidationModule),\n  //   canActivate: [ValidatorGuard]\n  // },\n  // {\n  //   path: 'admin',\n  //   loadChildren: () => import('./features/admin/admin.module').then(m => m.AdminModule),\n  //   canActivate: [AdminGuard],\n  //   data: { roles: [UserRole.ADMIN] }\n  // },\n  {\n    path: 'unauthorized',\n    loadComponent: () => import('./shared/components/unauthorized/unauthorized.component').then(c => c.UnauthorizedComponent)\n  },\n  {\n    path: '**',\n    loadComponent: () => import('./shared/components/not-found/not-found.component').then(c => c.NotFoundComponent)\n  }\n];\n", "// User models\nexport * from './user.model';\n\n// Reward models\nexport * from './reward.model';\n\n// Validation models\nexport * from './validation.model';\n\n// Common interfaces\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  items: T[];\n  total: number;\n  page: number;\n  limit: number;\n  hasNext: boolean;\n  hasPrevious: boolean;\n}\n\nexport interface FilterOptions {\n  search?: string;\n  category?: string;\n  city?: 'Monastir' | 'Sousse';\n  dateFrom?: Date;\n  dateTo?: Date;\n  status?: string;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport interface NotificationData {\n  id: string;\n  userId: string;\n  title: string;\n  message: string;\n  type: NotificationType;\n  isRead: boolean;\n  createdAt: Date;\n  data?: any;\n}\n\nexport enum NotificationType {\n  POINTS_EARNED = 'points_earned',\n  POINTS_SPENT = 'points_spent',\n  VALIDATION_APPROVED = 'validation_approved',\n  VALIDATION_REJECTED = 'validation_rejected',\n  REWARD_AVAILABLE = 'reward_available',\n  SYSTEM_ANNOUNCEMENT = 'system_announcement'\n}\n\nexport interface AppConfig {\n  firebase: {\n    apiKey: string;\n    authDomain: string;\n    projectId: string;\n    storageBucket: string;\n    messagingSenderId: string;\n    appId: string;\n  };\n  app: {\n    name: string;\n    version: string;\n    environment: 'development' | 'staging' | 'production';\n  };\n  features: {\n    pushNotifications: boolean;\n    geolocation: boolean;\n    darkMode: boolean;\n    multiLanguage: boolean;\n  };\n}\n", "import { GeoLocation } from './user.model';\n\nexport interface Reward {\n  id: string;\n  title: string;\n  description: string;\n  pointsRequired: number;\n  partnerId: string;\n  partnerName: string;\n  category: RewardCategory;\n  imageUrl?: string;\n  isActive: boolean;\n  availableQuantity?: number;\n  validUntil?: Date;\n  terms?: string;\n  createdAt: Date;\n  updatedAt: Date;\n  city: 'Monastir' | 'Sousse' | 'Both';\n}\n\nexport enum RewardCategory {\n  FOOD = 'food',\n  SHOPPING = 'shopping',\n  ENTERTAINMENT = 'entertainment',\n  SERVICES = 'services',\n  HEALTH = 'health',\n  EDUCATION = 'education',\n  TRANSPORT = 'transport',\n  OTHER = 'other'\n}\n\nexport interface Partner {\n  id: string;\n  name: string;\n  description: string;\n  logo?: string;\n  category: PartnerCategory;\n  address: string;\n  city: 'Monastir' | 'Sousse';\n  phone?: string;\n  email?: string;\n  website?: string;\n  location: GeoLocation;\n  isActive: boolean;\n  rewards: string[]; // Array of reward IDs\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport enum PartnerCategory {\n  RESTAURANT = 'restaurant',\n  CAFE = 'cafe',\n  RETAIL = 'retail',\n  SERVICE = 'service',\n  ENTERTAINMENT = 'entertainment',\n  HEALTH = 'health',\n  EDUCATION = 'education',\n  OTHER = 'other'\n}\n\nexport interface RewardExchange {\n  id: string;\n  userId: string;\n  rewardId: string;\n  pointsSpent: number;\n  status: ExchangeStatus;\n  exchangeCode: string;\n  exchangedAt: Date;\n  usedAt?: Date;\n  validUntil: Date;\n  partnerConfirmation?: string;\n}\n\nexport enum ExchangeStatus {\n  PENDING = 'pending',\n  CONFIRMED = 'confirmed',\n  USED = 'used',\n  EXPIRED = 'expired',\n  CANCELLED = 'cancelled'\n}\n\nexport interface CreateRewardRequest {\n  title: string;\n  description: string;\n  pointsRequired: number;\n  partnerId: string;\n  category: RewardCategory;\n  imageUrl?: string;\n  availableQuantity?: number;\n  validUntil?: Date;\n  terms?: string;\n  city: 'Monastir' | 'Sousse' | 'Both';\n}\n\nexport interface UpdateRewardRequest {\n  title?: string;\n  description?: string;\n  pointsRequired?: number;\n  category?: RewardCategory;\n  imageUrl?: string;\n  availableQuantity?: number;\n  validUntil?: Date;\n  terms?: string;\n  isActive?: boolean;\n}\n\n// GeoLocation interface is defined in user.model.ts\n", "export interface User {\n  uid: string;\n  email: string;\n  name: string;\n  phone?: string;\n  city: 'Monastir' | 'Sousse';\n  role: UserRole;\n  points: number;\n  avatar?: string;\n  createdAt: Date;\n  updatedAt: Date;\n  isActive: boolean;\n  history: PointsTransaction[];\n}\n\nexport interface UserProfile {\n  uid: string;\n  name: string;\n  email: string;\n  phone?: string;\n  city: 'Monastir' | 'Sousse';\n  avatar?: string;\n  bio?: string;\n  dateOfBirth?: Date;\n  preferences: UserPreferences;\n}\n\nexport interface UserPreferences {\n  notifications: boolean;\n  darkMode: boolean;\n  language: 'fr' | 'ar' | 'en';\n  location: boolean;\n}\n\nexport enum UserRole {\n  USER = 'user',\n  PROVIDER = 'provider',\n  VALIDATOR = 'validator',\n  ADMIN = 'admin'\n}\n\nexport interface PointsTransaction {\n  id: string;\n  fromUserId?: string;\n  toUserId: string;\n  points: number;\n  type: TransactionType;\n  description: string;\n  timestamp: Date;\n  validatedBy?: string;\n  qrCode?: string;\n  location?: GeoLocation;\n}\n\nexport enum TransactionType {\n  EARNED = 'earned',\n  SPENT = 'spent',\n  TRANSFERRED = 'transferred',\n  VALIDATED = 'validated',\n  BONUS = 'bonus'\n}\n\nexport interface GeoLocation {\n  latitude: number;\n  longitude: number;\n  address?: string;\n}\n\nexport interface CreateUserRequest {\n  email: string;\n  password: string;\n  name: string;\n  phone?: string;\n  city: 'Monastir' | 'Sousse';\n  role: UserRole;\n}\n\nexport interface UpdateUserRequest {\n  name?: string;\n  phone?: string;\n  city?: 'Monastir' | 'Sousse';\n  avatar?: string;\n  bio?: string;\n  preferences?: Partial<UserPreferences>;\n}\n", "import { GeoLocation } from './user.model';\n\nexport interface Validation {\n  id: string;\n  validatorId: string;\n  validatorName: string;\n  userId: string;\n  userName: string;\n  action: ValidationAction;\n  points: number;\n  description: string;\n  status: ValidationStatus;\n  timestamp: Date;\n  location?: GeoLocation;\n  evidence?: ValidationEvidence;\n  qrCode?: string;\n}\n\nexport enum ValidationAction {\n  COMMUNITY_SERVICE = 'community_service',\n  ENVIRONMENTAL_ACTION = 'environmental_action',\n  CULTURAL_PARTICIPATION = 'cultural_participation',\n  SPORTS_ACTIVITY = 'sports_activity',\n  EDUCATIONAL_ACTIVITY = 'educational_activity',\n  VOLUNTEER_WORK = 'volunteer_work',\n  LOCAL_BUSINESS_SUPPORT = 'local_business_support',\n  OTHER = 'other'\n}\n\nexport enum ValidationStatus {\n  PENDING = 'pending',\n  APPROVED = 'approved',\n  REJECTED = 'rejected',\n  EXPIRED = 'expired'\n}\n\nexport interface ValidationEvidence {\n  photos?: string[];\n  description: string;\n  witnesses?: string[];\n  documents?: string[];\n}\n\nexport interface QrScanResult {\n  data: string;\n  format: string;\n  timestamp: Date;\n  location?: GeoLocation;\n}\n\nexport interface QrCodeData {\n  type: QrCodeType;\n  userId?: string;\n  validatorId?: string;\n  points?: number;\n  action?: string;\n  timestamp: number;\n  signature?: string;\n}\n\nexport enum QrCodeType {\n  USER_TRANSFER = 'user_transfer',\n  VALIDATOR_ACTION = 'validator_action',\n  PARTNER_REWARD = 'partner_reward',\n  SYSTEM_BONUS = 'system_bonus'\n}\n\nexport interface CreateValidationRequest {\n  userId: string;\n  action: ValidationAction;\n  points: number;\n  description: string;\n  evidence?: ValidationEvidence;\n  location?: GeoLocation;\n}\n\nexport interface UpdateValidationRequest {\n  status: ValidationStatus;\n  adminNotes?: string;\n  pointsAdjustment?: number;\n}\n\nexport interface ValidationStats {\n  totalValidations: number;\n  pendingValidations: number;\n  approvedValidations: number;\n  rejectedValidations: number;\n  totalPointsAwarded: number;\n  averagePointsPerValidation: number;\n  validationsByAction: Record<ValidationAction, number>;\n  validationsByMonth: Record<string, number>;\n}\n\n// GeoLocation interface is defined in user.model.ts\n", "import { Injectable } from '@angular/core';\n// import { AngularFireAuth } from '@angular/fire/compat/auth';\n// import { AngularFirestore } from '@angular/fire/compat/firestore';\nimport { BehaviorSubject, Observable, from, map, switchMap, of } from 'rxjs';\nimport { User, CreateUserRequest, UserRole } from '../models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  public currentUser$ = this.currentUserSubject.asObservable();\n\n  constructor(\n    // private auth: AngularFireAuth,\n    // private firestore: AngularFirestore\n  ) {\n    // Temporarily disabled for initial setup\n    // Create a mock user for testing\n    const mockUser: User = {\n      uid: 'mock-user-123',\n      email: '<EMAIL>',\n      name: 'Utilisateur Test',\n      city: 'Monastir',\n      role: UserRole.USER,\n      points: 150,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    };\n    this.currentUserSubject.next(mockUser);\n  }\n\n  async register(userData: CreateUserRequest): Promise<User> {\n    // Mock registration for initial setup\n    const user: User = {\n      uid: 'mock-user-' + Date.now(),\n      email: userData.email,\n      name: userData.name,\n      phone: userData.phone,\n      city: userData.city,\n      role: userData.role,\n      points: 0,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    };\n\n    this.currentUserSubject.next(user);\n    return Promise.resolve(user);\n  }\n\n  async login(email: string, password: string): Promise<User> {\n    // Mock login for initial setup\n    const mockUsers: { [key: string]: User } = {\n      '<EMAIL>': {\n        uid: 'user-123',\n        email: '<EMAIL>',\n        name: 'Utilisateur Test',\n        city: 'Monastir',\n        role: UserRole.USER,\n        points: 150,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n        isActive: true,\n        history: []\n      },\n      '<EMAIL>': {\n        uid: 'admin-123',\n        email: '<EMAIL>',\n        name: 'Admin Test',\n        city: 'Sousse',\n        role: UserRole.ADMIN,\n        points: 500,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n        isActive: true,\n        history: []\n      },\n      '<EMAIL>': {\n        uid: 'validator-123',\n        email: '<EMAIL>',\n        name: 'Validateur Test',\n        city: 'Monastir',\n        role: UserRole.VALIDATOR,\n        points: 300,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n        isActive: true,\n        history: []\n      }\n    };\n\n    const user = mockUsers[email];\n    if (user && password === 'password123') {\n      this.currentUserSubject.next(user);\n      return Promise.resolve(user);\n    }\n\n    throw new Error('Invalid credentials');\n  }\n\n  async logout(): Promise<void> {\n    this.currentUserSubject.next(null);\n    return Promise.resolve();\n  }\n\n  private async getUserData(uid: string): Promise<User> {\n    // Mock implementation\n    throw new Error('User data not found');\n  }\n\n  getCurrentUser(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  isAuthenticated(): boolean {\n    return this.currentUserSubject.value !== null;\n  }\n\n  hasRole(role: UserRole): boolean {\n    const user = this.getCurrentUser();\n    return user?.role === role;\n  }\n\n  hasAnyRole(roles: UserRole[]): boolean {\n    const user = this.getCurrentUser();\n    return user ? roles.includes(user.role) : false;\n  }\n\n  async updateUserProfile(updates: Partial<User>): Promise<void> {\n    const user = this.getCurrentUser();\n    if (!user) throw new Error('No authenticated user');\n\n    const updatedUser = { ...user, ...updates, updatedAt: new Date() };\n    this.currentUserSubject.next(updatedUser);\n    return Promise.resolve();\n  }\n\n  // Password reset\n  async resetPassword(email: string): Promise<void> {\n    // Implementation would use Firebase Auth sendPasswordResetEmail\n    // For now, just a placeholder\n    console.log('Password reset requested for:', email);\n  }\n}\n", "export const environment = {\n  production: false,\n  firebase: {\n    apiKey: \"your-api-key\",\n    authDomain: \"modjo-app.firebaseapp.com\",\n    projectId: \"modjo-app\",\n    storageBucket: \"modjo-app.appspot.com\",\n    messagingSenderId: \"123456789\",\n    appId: \"1:123456789:web:abcdef123456\"\n  },\n  app: {\n    name: 'Modjo',\n    version: '1.0.0',\n    environment: 'development' as const\n  },\n  features: {\n    pushNotifications: true,\n    geolocation: true,\n    darkMode: true,\n    multiLanguage: true\n  }\n};\n", "import { platformBrowserDynamic } from '@angular/platform-browser-dynamic';\n\nimport { AppModule } from './app/app.module';\n\nplatformBrowserDynamic().bootstrapModule(AppModule)\n  .catch(err => console.error(err));\n"], "names": ["NavigationEnd", "filter", "map", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "user_r2", "points", "ɵɵproperty", "userMenu_r3", "AppComponent", "constructor", "authService", "router", "title", "currentPageTitle", "ngOnInit", "events", "pipe", "event", "url", "subscribe", "updatePageTitle", "getPageTitle", "titleMap", "matchedRoute", "Object", "keys", "find", "route", "startsWith", "logout", "_this", "_asyncToGenerator", "navigate", "error", "console", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement", "ɵɵtemplate", "AppComponent_div_16_Template", "AppComponent_button_18_Template", "ɵɵlistener", "AppComponent_Template_button_click_27_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "ɵɵpipeBind1", "currentUser$", "BrowserModule", "BrowserAnimationsModule", "RouterModule", "ServiceWorkerModule", "MatToolbarModule", "MatButtonModule", "MatIconModule", "MatSidenavModule", "MatListModule", "MatCardModule", "MatSnackBarModule", "MatMenuModule", "MatDividerModule", "appRoutes", "environment", "AppModule", "bootstrap", "forRoot", "register", "enabled", "production", "registrationStrategy", "declarations", "imports", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "AuthModule", "DashboardModule", "ProfileModule", "QrScannerModule", "RewardsModule", "loadComponent", "c", "UnauthorizedComponent", "NotFoundComponent", "NotificationType", "RewardCategory", "PartnerCategory", "ExchangeStatus", "UserRole", "TransactionType", "ValidationAction", "ValidationStatus", "QrCodeType", "BehaviorSubject", "currentUserSubject", "asObservable", "mockUser", "uid", "email", "name", "city", "role", "USER", "createdAt", "Date", "updatedAt", "isActive", "history", "next", "userData", "user", "now", "phone", "Promise", "resolve", "login", "password", "_this2", "mockUsers", "ADMIN", "VALIDATOR", "Error", "_this3", "getUserData", "getCurrentUser", "value", "isAuthenticated", "hasRole", "hasAnyRole", "roles", "includes", "updateUserProfile", "updates", "_this4", "updatedUser", "resetPassword", "log", "factory", "ɵfac", "providedIn", "firebase", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "app", "version", "features", "pushNotifications", "geolocation", "darkMode", "multiLanguage", "__Ng<PERSON>li_bootstrap_1", "platformBrowser", "bootstrapModule", "catch", "err"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}