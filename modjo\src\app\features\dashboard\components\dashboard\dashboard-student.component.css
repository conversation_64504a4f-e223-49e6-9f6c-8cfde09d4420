/* Dashboard Élève - ModJob */
.dashboard-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
}

/* En-tête */
.dashboard-header {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-welcome {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  width: 60px;
  height: 60px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.user-avatar mat-icon {
  font-size: 2rem;
}

.user-info h2 {
  margin: 0 0 4px 0;
  color: #2d3748;
  font-weight: 600;
}

.user-role {
  color: #718096;
  margin: 0;
  font-size: 0.9rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.points-display {
  display: flex;
  align-items: center;
}

.points-card {
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: white;
  padding: 12px 20px;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.points-icon {
  font-size: 1.5rem !important;
}

.points-value {
  font-size: 1.5rem;
  font-weight: 700;
}

.points-label {
  font-size: 0.9rem;
  opacity: 0.9;
}

.notifications-btn {
  background: #f7fafc;
  color: #4a5568;
}

/* Section principale */
.main-section {
  margin-bottom: 32px;
}

/* Scanner QR */
.qr-scan-section {
  margin-bottom: 24px;
}

.qr-scan-card {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border-radius: 16px;
  overflow: hidden;
}

.qr-scan-content {
  display: flex;
  align-items: center;
  gap: 24px;
  padding: 8px;
}

.qr-scan-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.qr-scan-icon mat-icon {
  font-size: 2.5rem !important;
}

.qr-scan-text {
  flex: 1;
}

.qr-scan-text h3 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.qr-scan-text p {
  margin: 0;
  opacity: 0.9;
}

.qr-scan-btn {
  background: white;
  color: #4CAF50;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 12px;
}

.qr-scan-btn:hover {
  background: #f8f9fa;
}

/* Progression */
.progress-section {
  margin-bottom: 24px;
}

.progress-card {
  background: white;
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.progress-card h3 {
  margin: 0 0 20px 0;
  color: #2d3748;
  font-weight: 600;
}

.reward-progress {
  padding: 8px 0;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.next-reward {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reward-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  object-fit: cover;
}

.reward-details h4 {
  margin: 0 0 4px 0;
  color: #2d3748;
  font-weight: 600;
}

.reward-details p {
  margin: 0;
  color: #718096;
  font-size: 0.9rem;
}

.points-needed {
  text-align: right;
}

.points-remaining {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

.points-label {
  font-size: 0.8rem;
  color: #718096;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 6px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 0.9rem;
  color: #718096;
  margin-bottom: 12px;
}

.motivation-message {
  text-align: center;
  padding: 12px;
  background: #f7fafc;
  border-radius: 8px;
}

.motivation-message p {
  margin: 0;
  color: #4a5568;
  font-weight: 500;
}

/* Récompenses */
.rewards-section {
  margin-bottom: 32px;
}

.section-subtitle {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.rewards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.reward-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.reward-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.reward-card.affordable {
  border: 2px solid #4CAF50;
}

.reward-image {
  position: relative;
  height: 150px;
  overflow: hidden;
}

.reward-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.reward-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #4CAF50;
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reward-card h4 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-weight: 600;
}

.reward-description {
  color: #718096;
  font-size: 0.9rem;
  margin-bottom: 12px;
}

.reward-partner,
.reward-points {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: #4a5568;
}

.exchange-btn {
  width: 100%;
  margin-top: 12px;
}

/* Floating QR Button */
.floating-qr-btn {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  
  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .qr-scan-content {
    flex-direction: column;
    text-align: center;
  }
  
  .progress-info {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .rewards-grid {
    grid-template-columns: 1fr;
  }
}
