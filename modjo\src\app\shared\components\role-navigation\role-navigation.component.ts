import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../core/services/auth.service';
import { DashboardRouterService } from '../../../core/services/dashboard-router.service';
import { User, UserRole } from '../../../core/models';

@Component({
  selector: 'app-role-navigation',
  template: `
    <div class="role-navigation" *ngIf="user">
      <div class="nav-header">
        <div class="user-info">
          <h3>{{ user.name }}</h3>
          <span class="role-badge" [style.background-color]="getRoleColor(user.role) + '20'" 
                [style.color]="getRoleColor(user.role)">
            {{ getRoleDisplayName(user.role) }}
          </span>
        </div>
        <div class="user-points" *ngIf="user.role === 'user'">
          <mat-icon>stars</mat-icon>
          <span>{{ user.points }} points</span>
        </div>
      </div>
      
      <nav class="nav-menu">
        <a *ngFor="let item of navigationItems" 
           [routerLink]="item.route" 
           routerLinkActive="active"
           class="nav-item">
          <mat-icon>{{ item.icon }}</mat-icon>
          <span>{{ item.title }}</span>
        </a>
      </nav>
      
      <div class="nav-footer">
        <button mat-button (click)="logout()" class="logout-btn">
          <mat-icon>logout</mat-icon>
          Déconnexion
        </button>
      </div>
    </div>
  `,
  styles: [`
    .role-navigation {
      background: white;
      border-radius: 16px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      padding: 24px;
      margin-bottom: 24px;
    }
    
    .nav-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 24px;
      padding-bottom: 16px;
      border-bottom: 1px solid #e2e8f0;
    }
    
    .user-info h3 {
      margin: 0 0 8px 0;
      color: #2d3748;
      font-weight: 600;
    }
    
    .role-badge {
      padding: 4px 12px;
      border-radius: 12px;
      font-size: 0.8rem;
      font-weight: 600;
      text-transform: uppercase;
    }
    
    .user-points {
      display: flex;
      align-items: center;
      gap: 8px;
      background: linear-gradient(135deg, #FFD700, #FFA500);
      color: white;
      padding: 8px 16px;
      border-radius: 20px;
      font-weight: 600;
    }
    
    .nav-menu {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 24px;
    }
    
    .nav-item {
      display: flex;
      align-items: center;
      gap: 12px;
      padding: 12px 16px;
      border-radius: 12px;
      color: #4a5568;
      text-decoration: none;
      transition: all 0.2s ease;
    }
    
    .nav-item:hover {
      background: #f7fafc;
      color: #2d3748;
    }
    
    .nav-item.active {
      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
      color: #667eea;
      font-weight: 600;
    }
    
    .nav-footer {
      padding-top: 16px;
      border-top: 1px solid #e2e8f0;
    }
    
    .logout-btn {
      width: 100%;
      color: #F44336;
    }
  `]
})
export class RoleNavigationComponent implements OnInit {
  user: User | null = null;
  navigationItems: any[] = [];

  constructor(
    private authService: AuthService,
    private dashboardRouter: DashboardRouterService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.user = user;
      if (user) {
        this.loadNavigationItems(user.role);
      }
    });
  }

  private loadNavigationItems(role: UserRole): void {
    const navigationMap = {
      [UserRole.USER]: [
        { title: 'Dashboard', icon: 'dashboard', route: '/dashboard' },
        { title: 'Scanner QR', icon: 'qr_code_scanner', route: '/qr-scanner' },
        { title: 'Récompenses', icon: 'card_giftcard', route: '/rewards' },
        { title: 'Mon Profil', icon: 'person', route: '/profile' }
      ],
      [UserRole.ADMIN]: [
        { title: 'Dashboard Admin', icon: 'admin_panel_settings', route: '/admin' },
        { title: 'Gestion Utilisateurs', icon: 'people', route: '/admin/users' },
        { title: 'Gestion Partenaires', icon: 'store', route: '/admin/partners' },
        { title: 'Configuration', icon: 'settings', route: '/admin/config' },
        { title: 'Mon Profil', icon: 'person', route: '/profile' }
      ],
      [UserRole.VALIDATOR]: [
        { title: 'Dashboard Validateur', icon: 'verified_user', route: '/validator' },
        { title: 'Actions à Valider', icon: 'pending_actions', route: '/validator/pending' },
        { title: 'Historique', icon: 'history', route: '/validator/history' },
        { title: 'Mon Profil', icon: 'person', route: '/profile' }
      ],
      [UserRole.PARTNER]: [
        { title: 'Dashboard Partenaire', icon: 'store', route: '/partner' },
        { title: 'Mes Récompenses', icon: 'card_giftcard', route: '/partner/rewards' },
        { title: 'Statistiques', icon: 'analytics', route: '/partner/stats' },
        { title: 'Mon Profil', icon: 'person', route: '/profile' }
      ],
      [UserRole.PROVIDER]: [
        { title: 'Dashboard Prestataire', icon: 'business', route: '/provider' },
        { title: 'Générer QR', icon: 'qr_code', route: '/provider/qr-generator' },
        { title: 'Statistiques', icon: 'analytics', route: '/provider/stats' },
        { title: 'Mon Profil', icon: 'person', route: '/profile' }
      ]
    };

    this.navigationItems = navigationMap[role] || [];
  }

  getRoleDisplayName(role: UserRole): string {
    const roleNames = {
      [UserRole.USER]: 'Utilisateur',
      [UserRole.ADMIN]: 'Administrateur',
      [UserRole.VALIDATOR]: 'Validateur',
      [UserRole.PARTNER]: 'Partenaire',
      [UserRole.PROVIDER]: 'Prestataire'
    };
    return roleNames[role];
  }

  getRoleColor(role: UserRole): string {
    const colors = {
      [UserRole.USER]: '#4CAF50',
      [UserRole.ADMIN]: '#F44336',
      [UserRole.VALIDATOR]: '#2196F3',
      [UserRole.PARTNER]: '#FF9800',
      [UserRole.PROVIDER]: '#9C27B0'
    };
    return colors[role];
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/auth/login']);
  }
}
