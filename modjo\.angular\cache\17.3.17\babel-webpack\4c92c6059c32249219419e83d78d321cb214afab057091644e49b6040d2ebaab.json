{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ValidationHistoryComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function ValidationHistoryComponent_Factory(t) {\n      return new (t || ValidationHistoryComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ValidationHistoryComponent,\n      selectors: [[\"app-validation-history\"]],\n      decls: 5,\n      vars: 0,\n      template: function ValidationHistoryComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"h2\");\n          i0.ɵɵtext(2, \"\\uD83D\\uDCDA Historique des Validations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"Historique complet de vos validations\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\"div[_ngcontent-%COMP%] { padding: 20px; }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInZhbGlkYXRpb24taGlzdG9yeS5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsTUFBTSxhQUFhLEVBQUUiLCJmaWxlIjoidmFsaWRhdGlvbi1oaXN0b3J5LmNvbXBvbmVudC50cyIsInNvdXJjZXNDb250ZW50IjpbImRpdiB7IHBhZGRpbmc6IDIwcHg7IH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvdmFsaWRhdG9yLWRhc2hib2FyZC9jb21wb25lbnRzL3ZhbGlkYXRpb24taGlzdG9yeS92YWxpZGF0aW9uLWhpc3RvcnkuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLE1BQU0sYUFBYSxFQUFFO0FBQ3JCLGdUQUFnVCIsInNvdXJjZXNDb250ZW50IjpbImRpdiB7IHBhZGRpbmc6IDIwcHg7IH0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ValidationHistoryComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "template", "ValidationHistoryComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\validator-dashboard\\components\\validation-history\\validation-history.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-validation-history',\n  template: '<div><h2>📚 Historique des Validations</h2><p>Historique complet de vos validations</p></div>',\n  styles: ['div { padding: 20px; }']\n})\nexport class ValidationHistoryComponent implements OnInit {\n  constructor() { }\n  ngOnInit(): void { }\n}\n"], "mappings": ";AAOA,OAAM,MAAOA,0BAA0B;EACrCC,YAAA,GAAgB;EAChBC,QAAQA,CAAA,GAAW;;;uBAFRF,0BAA0B;IAAA;EAAA;;;YAA1BA,0BAA0B;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAHrBE,EAAL,CAAAC,cAAA,UAAK,SAAI;UAAAD,EAAA,CAAAE,MAAA,8CAA6B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAAAH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,4CAAqC;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}