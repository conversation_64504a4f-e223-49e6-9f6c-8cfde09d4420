{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { _getProvider, getApp, _registerComponent, registerVersion } from '@firebase/app';\nimport { Logger } from '@firebase/logger';\nimport { ErrorFactory, calculateBackoffMillis, FirebaseError, isIndexedDBAvailable, validateIndexedDBOpenable, isBrowserExtension, areCookiesEnabled, getModularInstance, deepEqual } from '@firebase/util';\nimport { Component } from '@firebase/component';\nimport '@firebase/installations';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Type constant for Firebase Analytics.\r\n */\nconst ANALYTICS_TYPE = 'analytics';\n// Key to attach FID to in gtag params.\nconst GA_FID_KEY = 'firebase_id';\nconst ORIGIN_KEY = 'origin';\nconst FETCH_TIMEOUT_MILLIS = 60 * 1000;\nconst DYNAMIC_CONFIG_URL = 'https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig';\nconst GTAG_URL = 'https://www.googletagmanager.com/gtag/js';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst logger = new Logger('@firebase/analytics');\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst ERRORS = {\n  [\"already-exists\" /* AnalyticsError.ALREADY_EXISTS */]: 'A Firebase Analytics instance with the appId {$id} ' + ' already exists. ' + 'Only one Firebase Analytics instance can be created for each appId.',\n  [\"already-initialized\" /* AnalyticsError.ALREADY_INITIALIZED */]: 'initializeAnalytics() cannot be called again with different options than those ' + 'it was initially called with. It can be called again with the same options to ' + 'return the existing instance, or getAnalytics() can be used ' + 'to get a reference to the already-intialized instance.',\n  [\"already-initialized-settings\" /* AnalyticsError.ALREADY_INITIALIZED_SETTINGS */]: 'Firebase Analytics has already been initialized.' + 'settings() must be called before initializing any Analytics instance' + 'or it will have no effect.',\n  [\"interop-component-reg-failed\" /* AnalyticsError.INTEROP_COMPONENT_REG_FAILED */]: 'Firebase Analytics Interop Component failed to instantiate: {$reason}',\n  [\"invalid-analytics-context\" /* AnalyticsError.INVALID_ANALYTICS_CONTEXT */]: 'Firebase Analytics is not supported in this environment. ' + 'Wrap initialization of analytics in analytics.isSupported() ' + 'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [\"indexeddb-unavailable\" /* AnalyticsError.INDEXEDDB_UNAVAILABLE */]: 'IndexedDB unavailable or restricted in this environment. ' + 'Wrap initialization of analytics in analytics.isSupported() ' + 'to prevent initialization in unsupported environments. Details: {$errorInfo}',\n  [\"fetch-throttle\" /* AnalyticsError.FETCH_THROTTLE */]: 'The config fetch request timed out while in an exponential backoff state.' + ' Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',\n  [\"config-fetch-failed\" /* AnalyticsError.CONFIG_FETCH_FAILED */]: 'Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}',\n  [\"no-api-key\" /* AnalyticsError.NO_API_KEY */]: 'The \"apiKey\" field is empty in the local Firebase config. Firebase Analytics requires this field to' + 'contain a valid API key.',\n  [\"no-app-id\" /* AnalyticsError.NO_APP_ID */]: 'The \"appId\" field is empty in the local Firebase config. Firebase Analytics requires this field to' + 'contain a valid app ID.',\n  [\"no-client-id\" /* AnalyticsError.NO_CLIENT_ID */]: 'The \"client_id\" field is empty.',\n  [\"invalid-gtag-resource\" /* AnalyticsError.INVALID_GTAG_RESOURCE */]: 'Trusted Types detected an invalid gtag resource: {$gtagURL}.'\n};\nconst ERROR_FACTORY = new ErrorFactory('analytics', 'Analytics', ERRORS);\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Verifies and creates a TrustedScriptURL.\r\n */\nfunction createGtagTrustedTypesScriptURL(url) {\n  if (!url.startsWith(GTAG_URL)) {\n    const err = ERROR_FACTORY.create(\"invalid-gtag-resource\" /* AnalyticsError.INVALID_GTAG_RESOURCE */, {\n      gtagURL: url\n    });\n    logger.warn(err.message);\n    return '';\n  }\n  return url;\n}\n/**\r\n * Makeshift polyfill for Promise.allSettled(). Resolves when all promises\r\n * have either resolved or rejected.\r\n *\r\n * @param promises Array of promises to wait for.\r\n */\nfunction promiseAllSettled(promises) {\n  return Promise.all(promises.map(promise => promise.catch(e => e)));\n}\n/**\r\n * Creates a TrustedTypePolicy object that implements the rules passed as policyOptions.\r\n *\r\n * @param policyName A string containing the name of the policy\r\n * @param policyOptions Object containing implementations of instance methods for TrustedTypesPolicy, see {@link https://developer.mozilla.org/en-US/docs/Web/API/TrustedTypePolicy#instance_methods\r\n * | the TrustedTypePolicy reference documentation}.\r\n */\nfunction createTrustedTypesPolicy(policyName, policyOptions) {\n  // Create a TrustedTypes policy that we can use for updating src\n  // properties\n  let trustedTypesPolicy;\n  if (window.trustedTypes) {\n    trustedTypesPolicy = window.trustedTypes.createPolicy(policyName, policyOptions);\n  }\n  return trustedTypesPolicy;\n}\n/**\r\n * Inserts gtag script tag into the page to asynchronously download gtag.\r\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\r\n */\nfunction insertScriptTag(dataLayerName, measurementId) {\n  const trustedTypesPolicy = createTrustedTypesPolicy('firebase-js-sdk-policy', {\n    createScriptURL: createGtagTrustedTypesScriptURL\n  });\n  const script = document.createElement('script');\n  // We are not providing an analyticsId in the URL because it would trigger a `page_view`\n  // without fid. We will initialize ga-id using gtag (config) command together with fid.\n  const gtagScriptURL = `${GTAG_URL}?l=${dataLayerName}&id=${measurementId}`;\n  script.src = trustedTypesPolicy ? trustedTypesPolicy === null || trustedTypesPolicy === void 0 ? void 0 : trustedTypesPolicy.createScriptURL(gtagScriptURL) : gtagScriptURL;\n  script.async = true;\n  document.head.appendChild(script);\n}\n/**\r\n * Get reference to, or create, global datalayer.\r\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\r\n */\nfunction getOrCreateDataLayer(dataLayerName) {\n  // Check for existing dataLayer and create if needed.\n  let dataLayer = [];\n  if (Array.isArray(window[dataLayerName])) {\n    dataLayer = window[dataLayerName];\n  } else {\n    window[dataLayerName] = dataLayer;\n  }\n  return dataLayer;\n}\n/**\r\n * Wrapped gtag logic when gtag is called with 'config' command.\r\n *\r\n * @param gtagCore Basic gtag function that just appends to dataLayer.\r\n * @param initializationPromisesMap Map of appIds to their initialization promises.\r\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\r\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\r\n * @param measurementId GA Measurement ID to set config for.\r\n * @param gtagParams Gtag config params to set.\r\n */\nfunction gtagOnConfig(_x, _x2, _x3, _x4, _x5, _x6) {\n  return _gtagOnConfig.apply(this, arguments);\n}\n/**\r\n * Wrapped gtag logic when gtag is called with 'event' command.\r\n *\r\n * @param gtagCore Basic gtag function that just appends to dataLayer.\r\n * @param initializationPromisesMap Map of appIds to their initialization promises.\r\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\r\n * @param measurementId GA Measurement ID to log event to.\r\n * @param gtagParams Params to log with this event.\r\n */\nfunction _gtagOnConfig() {\n  _gtagOnConfig = _asyncToGenerator(function* (gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId, measurementId, gtagParams) {\n    // If config is already fetched, we know the appId and can use it to look up what FID promise we\n    /// are waiting for, and wait only on that one.\n    const correspondingAppId = measurementIdToAppId[measurementId];\n    try {\n      if (correspondingAppId) {\n        yield initializationPromisesMap[correspondingAppId];\n      } else {\n        // If config is not fetched yet, wait for all configs (we don't know which one we need) and\n        // find the appId (if any) corresponding to this measurementId. If there is one, wait on\n        // that appId's initialization promise. If there is none, promise resolves and gtag\n        // call goes through.\n        const dynamicConfigResults = yield promiseAllSettled(dynamicConfigPromisesList);\n        const foundConfig = dynamicConfigResults.find(config => config.measurementId === measurementId);\n        if (foundConfig) {\n          yield initializationPromisesMap[foundConfig.appId];\n        }\n      }\n    } catch (e) {\n      logger.error(e);\n    }\n    gtagCore(\"config\" /* GtagCommand.CONFIG */, measurementId, gtagParams);\n  });\n  return _gtagOnConfig.apply(this, arguments);\n}\nfunction gtagOnEvent(_x7, _x8, _x9, _x0, _x1) {\n  return _gtagOnEvent.apply(this, arguments);\n}\n/**\r\n * Wraps a standard gtag function with extra code to wait for completion of\r\n * relevant initialization promises before sending requests.\r\n *\r\n * @param gtagCore Basic gtag function that just appends to dataLayer.\r\n * @param initializationPromisesMap Map of appIds to their initialization promises.\r\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\r\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\r\n */\nfunction _gtagOnEvent() {\n  _gtagOnEvent = _asyncToGenerator(function* (gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementId, gtagParams) {\n    try {\n      let initializationPromisesToWaitFor = [];\n      // If there's a 'send_to' param, check if any ID specified matches\n      // an initializeIds() promise we are waiting for.\n      if (gtagParams && gtagParams['send_to']) {\n        let gaSendToList = gtagParams['send_to'];\n        // Make it an array if is isn't, so it can be dealt with the same way.\n        if (!Array.isArray(gaSendToList)) {\n          gaSendToList = [gaSendToList];\n        }\n        // Checking 'send_to' fields requires having all measurement ID results back from\n        // the dynamic config fetch.\n        const dynamicConfigResults = yield promiseAllSettled(dynamicConfigPromisesList);\n        for (const sendToId of gaSendToList) {\n          // Any fetched dynamic measurement ID that matches this 'send_to' ID\n          const foundConfig = dynamicConfigResults.find(config => config.measurementId === sendToId);\n          const initializationPromise = foundConfig && initializationPromisesMap[foundConfig.appId];\n          if (initializationPromise) {\n            initializationPromisesToWaitFor.push(initializationPromise);\n          } else {\n            // Found an item in 'send_to' that is not associated\n            // directly with an FID, possibly a group.  Empty this array,\n            // exit the loop early, and let it get populated below.\n            initializationPromisesToWaitFor = [];\n            break;\n          }\n        }\n      }\n      // This will be unpopulated if there was no 'send_to' field , or\n      // if not all entries in the 'send_to' field could be mapped to\n      // a FID. In these cases, wait on all pending initialization promises.\n      if (initializationPromisesToWaitFor.length === 0) {\n        initializationPromisesToWaitFor = Object.values(initializationPromisesMap);\n      }\n      // Run core gtag function with args after all relevant initialization\n      // promises have been resolved.\n      yield Promise.all(initializationPromisesToWaitFor);\n      // Workaround for http://b/141370449 - third argument cannot be undefined.\n      gtagCore(\"event\" /* GtagCommand.EVENT */, measurementId, gtagParams || {});\n    } catch (e) {\n      logger.error(e);\n    }\n  });\n  return _gtagOnEvent.apply(this, arguments);\n}\nfunction wrapGtag(gtagCore,\n/**\r\n * Allows wrapped gtag calls to wait on whichever intialization promises are required,\r\n * depending on the contents of the gtag params' `send_to` field, if any.\r\n */\ninitializationPromisesMap,\n/**\r\n * Wrapped gtag calls sometimes require all dynamic config fetches to have returned\r\n * before determining what initialization promises (which include FIDs) to wait for.\r\n */\ndynamicConfigPromisesList,\n/**\r\n * Wrapped gtag config calls can narrow down which initialization promise (with FID)\r\n * to wait for if the measurementId is already fetched, by getting the corresponding appId,\r\n * which is the key for the initialization promises map.\r\n */\nmeasurementIdToAppId) {\n  /**\r\n   * Wrapper around gtag that ensures FID is sent with gtag calls.\r\n   * @param command Gtag command type.\r\n   * @param idOrNameOrParams Measurement ID if command is EVENT/CONFIG, params if command is SET.\r\n   * @param gtagParams Params if event is EVENT/CONFIG.\r\n   */\n  function gtagWrapper(_x10) {\n    return _gtagWrapper.apply(this, arguments);\n  }\n  function _gtagWrapper() {\n    _gtagWrapper = _asyncToGenerator(function* (command, ...args) {\n      try {\n        // If event, check that relevant initialization promises have completed.\n        if (command === \"event\" /* GtagCommand.EVENT */) {\n          const [measurementId, gtagParams] = args;\n          // If EVENT, second arg must be measurementId.\n          yield gtagOnEvent(gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementId, gtagParams);\n        } else if (command === \"config\" /* GtagCommand.CONFIG */) {\n          const [measurementId, gtagParams] = args;\n          // If CONFIG, second arg must be measurementId.\n          yield gtagOnConfig(gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId, measurementId, gtagParams);\n        } else if (command === \"consent\" /* GtagCommand.CONSENT */) {\n          const [gtagParams] = args;\n          gtagCore(\"consent\" /* GtagCommand.CONSENT */, 'update', gtagParams);\n        } else if (command === \"get\" /* GtagCommand.GET */) {\n          const [measurementId, fieldName, callback] = args;\n          gtagCore(\"get\" /* GtagCommand.GET */, measurementId, fieldName, callback);\n        } else if (command === \"set\" /* GtagCommand.SET */) {\n          const [customParams] = args;\n          // If SET, second arg must be params.\n          gtagCore(\"set\" /* GtagCommand.SET */, customParams);\n        } else {\n          gtagCore(command, ...args);\n        }\n      } catch (e) {\n        logger.error(e);\n      }\n    });\n    return _gtagWrapper.apply(this, arguments);\n  }\n  return gtagWrapper;\n}\n/**\r\n * Creates global gtag function or wraps existing one if found.\r\n * This wrapped function attaches Firebase instance ID (FID) to gtag 'config' and\r\n * 'event' calls that belong to the GAID associated with this Firebase instance.\r\n *\r\n * @param initializationPromisesMap Map of appIds to their initialization promises.\r\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\r\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\r\n * @param dataLayerName Name of global GA datalayer array.\r\n * @param gtagFunctionName Name of global gtag function (\"gtag\" if not user-specified).\r\n */\nfunction wrapOrCreateGtag(initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId, dataLayerName, gtagFunctionName) {\n  // Create a basic core gtag function\n  let gtagCore = function (..._args) {\n    // Must push IArguments object, not an array.\n    window[dataLayerName].push(arguments);\n  };\n  // Replace it with existing one if found\n  if (window[gtagFunctionName] && typeof window[gtagFunctionName] === 'function') {\n    // @ts-ignore\n    gtagCore = window[gtagFunctionName];\n  }\n  window[gtagFunctionName] = wrapGtag(gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId);\n  return {\n    gtagCore,\n    wrappedGtag: window[gtagFunctionName]\n  };\n}\n/**\r\n * Returns the script tag in the DOM matching both the gtag url pattern\r\n * and the provided data layer name.\r\n */\nfunction findGtagScriptOnPage(dataLayerName) {\n  const scriptTags = window.document.getElementsByTagName('script');\n  for (const tag of Object.values(scriptTags)) {\n    if (tag.src && tag.src.includes(GTAG_URL) && tag.src.includes(dataLayerName)) {\n      return tag;\n    }\n  }\n  return null;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Backoff factor for 503 errors, which we want to be conservative about\r\n * to avoid overloading servers. Each retry interval will be\r\n * BASE_INTERVAL_MILLIS * LONG_RETRY_FACTOR ^ retryCount, so the second one\r\n * will be ~30 seconds (with fuzzing).\r\n */\nconst LONG_RETRY_FACTOR = 30;\n/**\r\n * Base wait interval to multiplied by backoffFactor^backoffCount.\r\n */\nconst BASE_INTERVAL_MILLIS = 1000;\n/**\r\n * Stubbable retry data storage class.\r\n */\nclass RetryData {\n  constructor(throttleMetadata = {}, intervalMillis = BASE_INTERVAL_MILLIS) {\n    this.throttleMetadata = throttleMetadata;\n    this.intervalMillis = intervalMillis;\n  }\n  getThrottleMetadata(appId) {\n    return this.throttleMetadata[appId];\n  }\n  setThrottleMetadata(appId, metadata) {\n    this.throttleMetadata[appId] = metadata;\n  }\n  deleteThrottleMetadata(appId) {\n    delete this.throttleMetadata[appId];\n  }\n}\nconst defaultRetryData = new RetryData();\n/**\r\n * Set GET request headers.\r\n * @param apiKey App API key.\r\n */\nfunction getHeaders(apiKey) {\n  return new Headers({\n    Accept: 'application/json',\n    'x-goog-api-key': apiKey\n  });\n}\n/**\r\n * Fetches dynamic config from backend.\r\n * @param app Firebase app to fetch config for.\r\n */\nfunction fetchDynamicConfig(_x11) {\n  return _fetchDynamicConfig.apply(this, arguments);\n}\n/**\r\n * Fetches dynamic config from backend, retrying if failed.\r\n * @param app Firebase app to fetch config for.\r\n */\nfunction _fetchDynamicConfig() {\n  _fetchDynamicConfig = _asyncToGenerator(function* (appFields) {\n    var _a;\n    const {\n      appId,\n      apiKey\n    } = appFields;\n    const request = {\n      method: 'GET',\n      headers: getHeaders(apiKey)\n    };\n    const appUrl = DYNAMIC_CONFIG_URL.replace('{app-id}', appId);\n    const response = yield fetch(appUrl, request);\n    if (response.status !== 200 && response.status !== 304) {\n      let errorMessage = '';\n      try {\n        // Try to get any error message text from server response.\n        const jsonResponse = yield response.json();\n        if ((_a = jsonResponse.error) === null || _a === void 0 ? void 0 : _a.message) {\n          errorMessage = jsonResponse.error.message;\n        }\n      } catch (_ignored) {}\n      throw ERROR_FACTORY.create(\"config-fetch-failed\" /* AnalyticsError.CONFIG_FETCH_FAILED */, {\n        httpStatus: response.status,\n        responseMessage: errorMessage\n      });\n    }\n    return response.json();\n  });\n  return _fetchDynamicConfig.apply(this, arguments);\n}\nfunction fetchDynamicConfigWithRetry(_x12) {\n  return _fetchDynamicConfigWithRetry.apply(this, arguments);\n}\n/**\r\n * Runs one retry attempt.\r\n * @param appFields Necessary app config fields.\r\n * @param throttleMetadata Ongoing metadata to determine throttling times.\r\n * @param signal Abort signal.\r\n */\nfunction _fetchDynamicConfigWithRetry() {\n  _fetchDynamicConfigWithRetry = _asyncToGenerator(function* (app,\n  // retryData and timeoutMillis are parameterized to allow passing a different value for testing.\n  retryData = defaultRetryData, timeoutMillis) {\n    const {\n      appId,\n      apiKey,\n      measurementId\n    } = app.options;\n    if (!appId) {\n      throw ERROR_FACTORY.create(\"no-app-id\" /* AnalyticsError.NO_APP_ID */);\n    }\n    if (!apiKey) {\n      if (measurementId) {\n        return {\n          measurementId,\n          appId\n        };\n      }\n      throw ERROR_FACTORY.create(\"no-api-key\" /* AnalyticsError.NO_API_KEY */);\n    }\n    const throttleMetadata = retryData.getThrottleMetadata(appId) || {\n      backoffCount: 0,\n      throttleEndTimeMillis: Date.now()\n    };\n    const signal = new AnalyticsAbortSignal();\n    setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n      // Note a very low delay, eg < 10ms, can elapse before listeners are initialized.\n      signal.abort();\n    }), timeoutMillis !== undefined ? timeoutMillis : FETCH_TIMEOUT_MILLIS);\n    return attemptFetchDynamicConfigWithRetry({\n      appId,\n      apiKey,\n      measurementId\n    }, throttleMetadata, signal, retryData);\n  });\n  return _fetchDynamicConfigWithRetry.apply(this, arguments);\n}\nfunction attemptFetchDynamicConfigWithRetry(_x13, _x14, _x15) {\n  return _attemptFetchDynamicConfigWithRetry.apply(this, arguments);\n}\n/**\r\n * Supports waiting on a backoff by:\r\n *\r\n * <ul>\r\n *   <li>Promisifying setTimeout, so we can set a timeout in our Promise chain</li>\r\n *   <li>Listening on a signal bus for abort events, just like the Fetch API</li>\r\n *   <li>Failing in the same way the Fetch API fails, so timing out a live request and a throttled\r\n *       request appear the same.</li>\r\n * </ul>\r\n *\r\n * <p>Visible for testing.\r\n */\nfunction _attemptFetchDynamicConfigWithRetry() {\n  _attemptFetchDynamicConfigWithRetry = _asyncToGenerator(function* (appFields, {\n    throttleEndTimeMillis,\n    backoffCount\n  }, signal, retryData = defaultRetryData // for testing\n  ) {\n    var _a;\n    const {\n      appId,\n      measurementId\n    } = appFields;\n    // Starts with a (potentially zero) timeout to support resumption from stored state.\n    // Ensures the throttle end time is honored if the last attempt timed out.\n    // Note the SDK will never make a request if the fetch timeout expires at this point.\n    try {\n      yield setAbortableTimeout(signal, throttleEndTimeMillis);\n    } catch (e) {\n      if (measurementId) {\n        logger.warn(`Timed out fetching this Firebase app's measurement ID from the server.` + ` Falling back to the measurement ID ${measurementId}` + ` provided in the \"measurementId\" field in the local Firebase config. [${e === null || e === void 0 ? void 0 : e.message}]`);\n        return {\n          appId,\n          measurementId\n        };\n      }\n      throw e;\n    }\n    try {\n      const response = yield fetchDynamicConfig(appFields);\n      // Note the SDK only clears throttle state if response is success or non-retriable.\n      retryData.deleteThrottleMetadata(appId);\n      return response;\n    } catch (e) {\n      const error = e;\n      if (!isRetriableError(error)) {\n        retryData.deleteThrottleMetadata(appId);\n        if (measurementId) {\n          logger.warn(`Failed to fetch this Firebase app's measurement ID from the server.` + ` Falling back to the measurement ID ${measurementId}` + ` provided in the \"measurementId\" field in the local Firebase config. [${error === null || error === void 0 ? void 0 : error.message}]`);\n          return {\n            appId,\n            measurementId\n          };\n        } else {\n          throw e;\n        }\n      }\n      const backoffMillis = Number((_a = error === null || error === void 0 ? void 0 : error.customData) === null || _a === void 0 ? void 0 : _a.httpStatus) === 503 ? calculateBackoffMillis(backoffCount, retryData.intervalMillis, LONG_RETRY_FACTOR) : calculateBackoffMillis(backoffCount, retryData.intervalMillis);\n      // Increments backoff state.\n      const throttleMetadata = {\n        throttleEndTimeMillis: Date.now() + backoffMillis,\n        backoffCount: backoffCount + 1\n      };\n      // Persists state.\n      retryData.setThrottleMetadata(appId, throttleMetadata);\n      logger.debug(`Calling attemptFetch again in ${backoffMillis} millis`);\n      return attemptFetchDynamicConfigWithRetry(appFields, throttleMetadata, signal, retryData);\n    }\n  });\n  return _attemptFetchDynamicConfigWithRetry.apply(this, arguments);\n}\nfunction setAbortableTimeout(signal, throttleEndTimeMillis) {\n  return new Promise((resolve, reject) => {\n    // Derives backoff from given end time, normalizing negative numbers to zero.\n    const backoffMillis = Math.max(throttleEndTimeMillis - Date.now(), 0);\n    const timeout = setTimeout(resolve, backoffMillis);\n    // Adds listener, rather than sets onabort, because signal is a shared object.\n    signal.addEventListener(() => {\n      clearTimeout(timeout);\n      // If the request completes before this timeout, the rejection has no effect.\n      reject(ERROR_FACTORY.create(\"fetch-throttle\" /* AnalyticsError.FETCH_THROTTLE */, {\n        throttleEndTimeMillis\n      }));\n    });\n  });\n}\n/**\r\n * Returns true if the {@link Error} indicates a fetch request may succeed later.\r\n */\nfunction isRetriableError(e) {\n  if (!(e instanceof FirebaseError) || !e.customData) {\n    return false;\n  }\n  // Uses string index defined by ErrorData, which FirebaseError implements.\n  const httpStatus = Number(e.customData['httpStatus']);\n  return httpStatus === 429 || httpStatus === 500 || httpStatus === 503 || httpStatus === 504;\n}\n/**\r\n * Shims a minimal AbortSignal (copied from Remote Config).\r\n *\r\n * <p>AbortController's AbortSignal conveniently decouples fetch timeout logic from other aspects\r\n * of networking, such as retries. Firebase doesn't use AbortController enough to justify a\r\n * polyfill recommendation, like we do with the Fetch API, but this minimal shim can easily be\r\n * swapped out if/when we do.\r\n */\nclass AnalyticsAbortSignal {\n  constructor() {\n    this.listeners = [];\n  }\n  addEventListener(listener) {\n    this.listeners.push(listener);\n  }\n  abort() {\n    this.listeners.forEach(listener => listener());\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Event parameters to set on 'gtag' during initialization.\r\n */\nlet defaultEventParametersForInit;\n/**\r\n * Logs an analytics event through the Firebase SDK.\r\n *\r\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\r\n * @param eventName Google Analytics event name, choose from standard list or use a custom string.\r\n * @param eventParams Analytics event parameters.\r\n */\nfunction logEvent$1(_x16, _x17, _x18, _x19, _x20) {\n  return _logEvent$.apply(this, arguments);\n}\n/**\r\n * Set screen_name parameter for this Google Analytics ID.\r\n *\r\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\r\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\r\n *\r\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\r\n * @param screenName Screen name string to set.\r\n */\nfunction _logEvent$() {\n  _logEvent$ = _asyncToGenerator(function* (gtagFunction, initializationPromise, eventName, eventParams, options) {\n    if (options && options.global) {\n      gtagFunction(\"event\" /* GtagCommand.EVENT */, eventName, eventParams);\n      return;\n    } else {\n      const measurementId = yield initializationPromise;\n      const params = Object.assign(Object.assign({}, eventParams), {\n        'send_to': measurementId\n      });\n      gtagFunction(\"event\" /* GtagCommand.EVENT */, eventName, params);\n    }\n  });\n  return _logEvent$.apply(this, arguments);\n}\nfunction setCurrentScreen$1(_x21, _x22, _x23, _x24) {\n  return _setCurrentScreen$.apply(this, arguments);\n}\n/**\r\n * Set user_id parameter for this Google Analytics ID.\r\n *\r\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\r\n * @param id User ID string to set\r\n */\nfunction _setCurrentScreen$() {\n  _setCurrentScreen$ = _asyncToGenerator(function* (gtagFunction, initializationPromise, screenName, options) {\n    if (options && options.global) {\n      gtagFunction(\"set\" /* GtagCommand.SET */, {\n        'screen_name': screenName\n      });\n      return Promise.resolve();\n    } else {\n      const measurementId = yield initializationPromise;\n      gtagFunction(\"config\" /* GtagCommand.CONFIG */, measurementId, {\n        update: true,\n        'screen_name': screenName\n      });\n    }\n  });\n  return _setCurrentScreen$.apply(this, arguments);\n}\nfunction setUserId$1(_x25, _x26, _x27, _x28) {\n  return _setUserId$.apply(this, arguments);\n}\n/**\r\n * Set all other user properties other than user_id and screen_name.\r\n *\r\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\r\n * @param properties Map of user properties to set\r\n */\nfunction _setUserId$() {\n  _setUserId$ = _asyncToGenerator(function* (gtagFunction, initializationPromise, id, options) {\n    if (options && options.global) {\n      gtagFunction(\"set\" /* GtagCommand.SET */, {\n        'user_id': id\n      });\n      return Promise.resolve();\n    } else {\n      const measurementId = yield initializationPromise;\n      gtagFunction(\"config\" /* GtagCommand.CONFIG */, measurementId, {\n        update: true,\n        'user_id': id\n      });\n    }\n  });\n  return _setUserId$.apply(this, arguments);\n}\nfunction setUserProperties$1(_x29, _x30, _x31, _x32) {\n  return _setUserProperties$.apply(this, arguments);\n}\n/**\r\n * Retrieves a unique Google Analytics identifier for the web client.\r\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\r\n *\r\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\r\n */\nfunction _setUserProperties$() {\n  _setUserProperties$ = _asyncToGenerator(function* (gtagFunction, initializationPromise, properties, options) {\n    if (options && options.global) {\n      const flatProperties = {};\n      for (const key of Object.keys(properties)) {\n        // use dot notation for merge behavior in gtag.js\n        flatProperties[`user_properties.${key}`] = properties[key];\n      }\n      gtagFunction(\"set\" /* GtagCommand.SET */, flatProperties);\n      return Promise.resolve();\n    } else {\n      const measurementId = yield initializationPromise;\n      gtagFunction(\"config\" /* GtagCommand.CONFIG */, measurementId, {\n        update: true,\n        'user_properties': properties\n      });\n    }\n  });\n  return _setUserProperties$.apply(this, arguments);\n}\nfunction internalGetGoogleAnalyticsClientId(_x33, _x34) {\n  return _internalGetGoogleAnalyticsClientId.apply(this, arguments);\n}\n/**\r\n * Set whether collection is enabled for this ID.\r\n *\r\n * @param enabled If true, collection is enabled for this ID.\r\n */\nfunction _internalGetGoogleAnalyticsClientId() {\n  _internalGetGoogleAnalyticsClientId = _asyncToGenerator(function* (gtagFunction, initializationPromise) {\n    const measurementId = yield initializationPromise;\n    return new Promise((resolve, reject) => {\n      gtagFunction(\"get\" /* GtagCommand.GET */, measurementId, 'client_id', clientId => {\n        if (!clientId) {\n          reject(ERROR_FACTORY.create(\"no-client-id\" /* AnalyticsError.NO_CLIENT_ID */));\n        }\n        resolve(clientId);\n      });\n    });\n  });\n  return _internalGetGoogleAnalyticsClientId.apply(this, arguments);\n}\nfunction setAnalyticsCollectionEnabled$1(_x35, _x36) {\n  return _setAnalyticsCollectionEnabled$.apply(this, arguments);\n}\n/**\r\n * Consent parameters to default to during 'gtag' initialization.\r\n */\nfunction _setAnalyticsCollectionEnabled$() {\n  _setAnalyticsCollectionEnabled$ = _asyncToGenerator(function* (initializationPromise, enabled) {\n    const measurementId = yield initializationPromise;\n    window[`ga-disable-${measurementId}`] = !enabled;\n  });\n  return _setAnalyticsCollectionEnabled$.apply(this, arguments);\n}\nlet defaultConsentSettingsForInit;\n/**\r\n * Sets the variable {@link defaultConsentSettingsForInit} for use in the initialization of\r\n * analytics.\r\n *\r\n * @param consentSettings Maps the applicable end user consent state for gtag.js.\r\n */\nfunction _setConsentDefaultForInit(consentSettings) {\n  defaultConsentSettingsForInit = consentSettings;\n}\n/**\r\n * Sets the variable `defaultEventParametersForInit` for use in the initialization of\r\n * analytics.\r\n *\r\n * @param customParams Any custom params the user may pass to gtag.js.\r\n */\nfunction _setDefaultEventParametersForInit(customParams) {\n  defaultEventParametersForInit = customParams;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction validateIndexedDB() {\n  return _validateIndexedDB.apply(this, arguments);\n}\n/**\r\n * Initialize the analytics instance in gtag.js by calling config command with fid.\r\n *\r\n * NOTE: We combine analytics initialization and setting fid together because we want fid to be\r\n * part of the `page_view` event that's sent during the initialization\r\n * @param app Firebase app\r\n * @param gtagCore The gtag function that's not wrapped.\r\n * @param dynamicConfigPromisesList Array of all dynamic config promises.\r\n * @param measurementIdToAppId Maps measurementID to appID.\r\n * @param installations _FirebaseInstallationsInternal instance.\r\n *\r\n * @returns Measurement ID.\r\n */\nfunction _validateIndexedDB() {\n  _validateIndexedDB = _asyncToGenerator(function* () {\n    if (!isIndexedDBAvailable()) {\n      logger.warn(ERROR_FACTORY.create(\"indexeddb-unavailable\" /* AnalyticsError.INDEXEDDB_UNAVAILABLE */, {\n        errorInfo: 'IndexedDB is not available in this environment.'\n      }).message);\n      return false;\n    } else {\n      try {\n        yield validateIndexedDBOpenable();\n      } catch (e) {\n        logger.warn(ERROR_FACTORY.create(\"indexeddb-unavailable\" /* AnalyticsError.INDEXEDDB_UNAVAILABLE */, {\n          errorInfo: e === null || e === void 0 ? void 0 : e.toString()\n        }).message);\n        return false;\n      }\n    }\n    return true;\n  });\n  return _validateIndexedDB.apply(this, arguments);\n}\nfunction _initializeAnalytics(_x37, _x38, _x39, _x40, _x41, _x42, _x43) {\n  return _initializeAnalytics2.apply(this, arguments);\n}\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Analytics Service class.\r\n */\nfunction _initializeAnalytics2() {\n  _initializeAnalytics2 = _asyncToGenerator(function* (app, dynamicConfigPromisesList, measurementIdToAppId, installations, gtagCore, dataLayerName, options) {\n    var _a;\n    const dynamicConfigPromise = fetchDynamicConfigWithRetry(app);\n    // Once fetched, map measurementIds to appId, for ease of lookup in wrapped gtag function.\n    dynamicConfigPromise.then(config => {\n      measurementIdToAppId[config.measurementId] = config.appId;\n      if (app.options.measurementId && config.measurementId !== app.options.measurementId) {\n        logger.warn(`The measurement ID in the local Firebase config (${app.options.measurementId})` + ` does not match the measurement ID fetched from the server (${config.measurementId}).` + ` To ensure analytics events are always sent to the correct Analytics property,` + ` update the` + ` measurement ID field in the local config or remove it from the local config.`);\n      }\n    }).catch(e => logger.error(e));\n    // Add to list to track state of all dynamic config promises.\n    dynamicConfigPromisesList.push(dynamicConfigPromise);\n    const fidPromise = validateIndexedDB().then(envIsValid => {\n      if (envIsValid) {\n        return installations.getId();\n      } else {\n        return undefined;\n      }\n    });\n    const [dynamicConfig, fid] = yield Promise.all([dynamicConfigPromise, fidPromise]);\n    // Detect if user has already put the gtag <script> tag on this page with the passed in\n    // data layer name.\n    if (!findGtagScriptOnPage(dataLayerName)) {\n      insertScriptTag(dataLayerName, dynamicConfig.measurementId);\n    }\n    // Detects if there are consent settings that need to be configured.\n    if (defaultConsentSettingsForInit) {\n      gtagCore(\"consent\" /* GtagCommand.CONSENT */, 'default', defaultConsentSettingsForInit);\n      _setConsentDefaultForInit(undefined);\n    }\n    // This command initializes gtag.js and only needs to be called once for the entire web app,\n    // but since it is idempotent, we can call it multiple times.\n    // We keep it together with other initialization logic for better code structure.\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\n    gtagCore('js', new Date());\n    // User config added first. We don't want users to accidentally overwrite\n    // base Firebase config properties.\n    const configProperties = (_a = options === null || options === void 0 ? void 0 : options.config) !== null && _a !== void 0 ? _a : {};\n    // guard against developers accidentally setting properties with prefix `firebase_`\n    configProperties[ORIGIN_KEY] = 'firebase';\n    configProperties.update = true;\n    if (fid != null) {\n      configProperties[GA_FID_KEY] = fid;\n    }\n    // It should be the first config command called on this GA-ID\n    // Initialize this GA-ID and set FID on it using the gtag config API.\n    // Note: This will trigger a page_view event unless 'send_page_view' is set to false in\n    // `configProperties`.\n    gtagCore(\"config\" /* GtagCommand.CONFIG */, dynamicConfig.measurementId, configProperties);\n    // Detects if there is data that will be set on every event logged from the SDK.\n    if (defaultEventParametersForInit) {\n      gtagCore(\"set\" /* GtagCommand.SET */, defaultEventParametersForInit);\n      _setDefaultEventParametersForInit(undefined);\n    }\n    return dynamicConfig.measurementId;\n  });\n  return _initializeAnalytics2.apply(this, arguments);\n}\nclass AnalyticsService {\n  constructor(app) {\n    this.app = app;\n  }\n  _delete() {\n    delete initializationPromisesMap[this.app.options.appId];\n    return Promise.resolve();\n  }\n}\n/**\r\n * Maps appId to full initialization promise. Wrapped gtag calls must wait on\r\n * all or some of these, depending on the call's `send_to` param and the status\r\n * of the dynamic config fetches (see below).\r\n */\nlet initializationPromisesMap = {};\n/**\r\n * List of dynamic config fetch promises. In certain cases, wrapped gtag calls\r\n * wait on all these to be complete in order to determine if it can selectively\r\n * wait for only certain initialization (FID) promises or if it must wait for all.\r\n */\nlet dynamicConfigPromisesList = [];\n/**\r\n * Maps fetched measurementIds to appId. Populated when the app's dynamic config\r\n * fetch completes. If already populated, gtag config calls can use this to\r\n * selectively wait for only this app's initialization promise (FID) instead of all\r\n * initialization promises.\r\n */\nconst measurementIdToAppId = {};\n/**\r\n * Name for window global data layer array used by GA: defaults to 'dataLayer'.\r\n */\nlet dataLayerName = 'dataLayer';\n/**\r\n * Name for window global gtag function used by GA: defaults to 'gtag'.\r\n */\nlet gtagName = 'gtag';\n/**\r\n * Reproduction of standard gtag function or reference to existing\r\n * gtag function on window object.\r\n */\nlet gtagCoreFunction;\n/**\r\n * Wrapper around gtag function that ensures FID is sent with all\r\n * relevant event and config calls.\r\n */\nlet wrappedGtagFunction;\n/**\r\n * Flag to ensure page initialization steps (creation or wrapping of\r\n * dataLayer and gtag script) are only run once per page load.\r\n */\nlet globalInitDone = false;\n/**\r\n * Configures Firebase Analytics to use custom `gtag` or `dataLayer` names.\r\n * Intended to be used if `gtag.js` script has been installed on\r\n * this page independently of Firebase Analytics, and is using non-default\r\n * names for either the `gtag` function or for `dataLayer`.\r\n * Must be called before calling `getAnalytics()` or it won't\r\n * have any effect.\r\n *\r\n * @public\r\n *\r\n * @param options - Custom gtag and dataLayer names.\r\n */\nfunction settings(options) {\n  if (globalInitDone) {\n    throw ERROR_FACTORY.create(\"already-initialized\" /* AnalyticsError.ALREADY_INITIALIZED */);\n  }\n  if (options.dataLayerName) {\n    dataLayerName = options.dataLayerName;\n  }\n  if (options.gtagName) {\n    gtagName = options.gtagName;\n  }\n}\n/**\r\n * Returns true if no environment mismatch is found.\r\n * If environment mismatches are found, throws an INVALID_ANALYTICS_CONTEXT\r\n * error that also lists details for each mismatch found.\r\n */\nfunction warnOnBrowserContextMismatch() {\n  const mismatchedEnvMessages = [];\n  if (isBrowserExtension()) {\n    mismatchedEnvMessages.push('This is a browser extension environment.');\n  }\n  if (!areCookiesEnabled()) {\n    mismatchedEnvMessages.push('Cookies are not available.');\n  }\n  if (mismatchedEnvMessages.length > 0) {\n    const details = mismatchedEnvMessages.map((message, index) => `(${index + 1}) ${message}`).join(' ');\n    const err = ERROR_FACTORY.create(\"invalid-analytics-context\" /* AnalyticsError.INVALID_ANALYTICS_CONTEXT */, {\n      errorInfo: details\n    });\n    logger.warn(err.message);\n  }\n}\n/**\r\n * Analytics instance factory.\r\n * @internal\r\n */\nfunction factory(app, installations, options) {\n  warnOnBrowserContextMismatch();\n  const appId = app.options.appId;\n  if (!appId) {\n    throw ERROR_FACTORY.create(\"no-app-id\" /* AnalyticsError.NO_APP_ID */);\n  }\n  if (!app.options.apiKey) {\n    if (app.options.measurementId) {\n      logger.warn(`The \"apiKey\" field is empty in the local Firebase config. This is needed to fetch the latest` + ` measurement ID for this Firebase app. Falling back to the measurement ID ${app.options.measurementId}` + ` provided in the \"measurementId\" field in the local Firebase config.`);\n    } else {\n      throw ERROR_FACTORY.create(\"no-api-key\" /* AnalyticsError.NO_API_KEY */);\n    }\n  }\n  if (initializationPromisesMap[appId] != null) {\n    throw ERROR_FACTORY.create(\"already-exists\" /* AnalyticsError.ALREADY_EXISTS */, {\n      id: appId\n    });\n  }\n  if (!globalInitDone) {\n    // Steps here should only be done once per page: creation or wrapping\n    // of dataLayer and global gtag function.\n    getOrCreateDataLayer(dataLayerName);\n    const {\n      wrappedGtag,\n      gtagCore\n    } = wrapOrCreateGtag(initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId, dataLayerName, gtagName);\n    wrappedGtagFunction = wrappedGtag;\n    gtagCoreFunction = gtagCore;\n    globalInitDone = true;\n  }\n  // Async but non-blocking.\n  // This map reflects the completion state of all promises for each appId.\n  initializationPromisesMap[appId] = _initializeAnalytics(app, dynamicConfigPromisesList, measurementIdToAppId, installations, gtagCoreFunction, dataLayerName, options);\n  const analyticsInstance = new AnalyticsService(app);\n  return analyticsInstance;\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\n/**\r\n * Returns an {@link Analytics} instance for the given app.\r\n *\r\n * @public\r\n *\r\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\r\n */\nfunction getAnalytics(app = getApp()) {\n  app = getModularInstance(app);\n  // Dependencies\n  const analyticsProvider = _getProvider(app, ANALYTICS_TYPE);\n  if (analyticsProvider.isInitialized()) {\n    return analyticsProvider.getImmediate();\n  }\n  return initializeAnalytics(app);\n}\n/**\r\n * Returns an {@link Analytics} instance for the given app.\r\n *\r\n * @public\r\n *\r\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\r\n */\nfunction initializeAnalytics(app, options = {}) {\n  // Dependencies\n  const analyticsProvider = _getProvider(app, ANALYTICS_TYPE);\n  if (analyticsProvider.isInitialized()) {\n    const existingInstance = analyticsProvider.getImmediate();\n    if (deepEqual(options, analyticsProvider.getOptions())) {\n      return existingInstance;\n    } else {\n      throw ERROR_FACTORY.create(\"already-initialized\" /* AnalyticsError.ALREADY_INITIALIZED */);\n    }\n  }\n  const analyticsInstance = analyticsProvider.initialize({\n    options\n  });\n  return analyticsInstance;\n}\n/**\r\n * This is a public static method provided to users that wraps four different checks:\r\n *\r\n * 1. Check if it's not a browser extension environment.\r\n * 2. Check if cookies are enabled in current browser.\r\n * 3. Check if IndexedDB is supported by the browser environment.\r\n * 4. Check if the current browser context is valid for using `IndexedDB.open()`.\r\n *\r\n * @public\r\n *\r\n */\nfunction isSupported() {\n  return _isSupported.apply(this, arguments);\n}\n/**\r\n * Use gtag `config` command to set `screen_name`.\r\n *\r\n * @public\r\n *\r\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\r\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\r\n *\r\n * @param analyticsInstance - The {@link Analytics} instance.\r\n * @param screenName - Screen name to set.\r\n */\nfunction _isSupported() {\n  _isSupported = _asyncToGenerator(function* () {\n    if (isBrowserExtension()) {\n      return false;\n    }\n    if (!areCookiesEnabled()) {\n      return false;\n    }\n    if (!isIndexedDBAvailable()) {\n      return false;\n    }\n    try {\n      const isDBOpenable = yield validateIndexedDBOpenable();\n      return isDBOpenable;\n    } catch (error) {\n      return false;\n    }\n  });\n  return _isSupported.apply(this, arguments);\n}\nfunction setCurrentScreen(analyticsInstance, screenName, options) {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  setCurrentScreen$1(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId], screenName, options).catch(e => logger.error(e));\n}\n/**\r\n * Retrieves a unique Google Analytics identifier for the web client.\r\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\r\n *\r\n * @public\r\n *\r\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\r\n */\nfunction getGoogleAnalyticsClientId(_x44) {\n  return _getGoogleAnalyticsClientId.apply(this, arguments);\n}\n/**\r\n * Use gtag `config` command to set `user_id`.\r\n *\r\n * @public\r\n *\r\n * @param analyticsInstance - The {@link Analytics} instance.\r\n * @param id - User ID to set.\r\n */\nfunction _getGoogleAnalyticsClientId() {\n  _getGoogleAnalyticsClientId = _asyncToGenerator(function* (analyticsInstance) {\n    analyticsInstance = getModularInstance(analyticsInstance);\n    return internalGetGoogleAnalyticsClientId(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId]);\n  });\n  return _getGoogleAnalyticsClientId.apply(this, arguments);\n}\nfunction setUserId(analyticsInstance, id, options) {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  setUserId$1(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId], id, options).catch(e => logger.error(e));\n}\n/**\r\n * Use gtag `config` command to set all params specified.\r\n *\r\n * @public\r\n */\nfunction setUserProperties(analyticsInstance, properties, options) {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  setUserProperties$1(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId], properties, options).catch(e => logger.error(e));\n}\n/**\r\n * Sets whether Google Analytics collection is enabled for this app on this device.\r\n * Sets global `window['ga-disable-analyticsId'] = true;`\r\n *\r\n * @public\r\n *\r\n * @param analyticsInstance - The {@link Analytics} instance.\r\n * @param enabled - If true, enables collection, if false, disables it.\r\n */\nfunction setAnalyticsCollectionEnabled(analyticsInstance, enabled) {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  setAnalyticsCollectionEnabled$1(initializationPromisesMap[analyticsInstance.app.options.appId], enabled).catch(e => logger.error(e));\n}\n/**\r\n * Adds data that will be set on every event logged from the SDK, including automatic ones.\r\n * With gtag's \"set\" command, the values passed persist on the current page and are passed with\r\n * all subsequent events.\r\n * @public\r\n * @param customParams - Any custom params the user may pass to gtag.js.\r\n */\nfunction setDefaultEventParameters(customParams) {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(\"set\" /* GtagCommand.SET */, customParams);\n  } else {\n    _setDefaultEventParametersForInit(customParams);\n  }\n}\n/**\r\n * Sends a Google Analytics event with given `eventParams`. This method\r\n * automatically associates this logged event with this Firebase web\r\n * app instance on this device.\r\n * List of official event parameters can be found in the gtag.js\r\n * reference documentation:\r\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\r\n * | the GA4 reference documentation}.\r\n *\r\n * @public\r\n */\nfunction logEvent(analyticsInstance, eventName, eventParams, options) {\n  analyticsInstance = getModularInstance(analyticsInstance);\n  logEvent$1(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId], eventName, eventParams, options).catch(e => logger.error(e));\n}\n/**\r\n * Sets the applicable end user consent state for this web app across all gtag references once\r\n * Firebase Analytics is initialized.\r\n *\r\n * Use the {@link ConsentSettings} to specify individual consent type values. By default consent\r\n * types are set to \"granted\".\r\n * @public\r\n * @param consentSettings - Maps the applicable end user consent state for gtag.js.\r\n */\nfunction setConsent(consentSettings) {\n  // Check if reference to existing gtag function on window object exists\n  if (wrappedGtagFunction) {\n    wrappedGtagFunction(\"consent\" /* GtagCommand.CONSENT */, 'update', consentSettings);\n  } else {\n    _setConsentDefaultForInit(consentSettings);\n  }\n}\nconst name = \"@firebase/analytics\";\nconst version = \"0.10.0\";\n\n/**\r\n * Firebase Analytics\r\n *\r\n * @packageDocumentation\r\n */\nfunction registerAnalytics() {\n  _registerComponent(new Component(ANALYTICS_TYPE, (container, {\n    options: analyticsOptions\n  }) => {\n    // getImmediate for FirebaseApp will always succeed\n    const app = container.getProvider('app').getImmediate();\n    const installations = container.getProvider('installations-internal').getImmediate();\n    return factory(app, installations, analyticsOptions);\n  }, \"PUBLIC\" /* ComponentType.PUBLIC */));\n  _registerComponent(new Component('analytics-internal', internalFactory, \"PRIVATE\" /* ComponentType.PRIVATE */));\n  registerVersion(name, version);\n  // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\n  registerVersion(name, version, 'esm2017');\n  function internalFactory(container) {\n    try {\n      const analytics = container.getProvider(ANALYTICS_TYPE).getImmediate();\n      return {\n        logEvent: (eventName, eventParams, options) => logEvent(analytics, eventName, eventParams, options)\n      };\n    } catch (e) {\n      throw ERROR_FACTORY.create(\"interop-component-reg-failed\" /* AnalyticsError.INTEROP_COMPONENT_REG_FAILED */, {\n        reason: e\n      });\n    }\n  }\n}\nregisterAnalytics();\nexport { getAnalytics, getGoogleAnalyticsClientId, initializeAnalytics, isSupported, logEvent, setAnalyticsCollectionEnabled, setConsent, setCurrentScreen, setDefaultEventParameters, setUserId, setUserProperties, settings };", "map": {"version": 3, "names": ["_get<PERSON><PERSON><PERSON>", "getApp", "_registerComponent", "registerVersion", "<PERSON><PERSON>", "ErrorFactory", "calculateBackoffMillis", "FirebaseError", "isIndexedDBAvailable", "validateIndexedDBOpenable", "isBrowserExtension", "areCookiesEnabled", "getModularInstance", "deepEqual", "Component", "ANALYTICS_TYPE", "GA_FID_KEY", "ORIGIN_KEY", "FETCH_TIMEOUT_MILLIS", "DYNAMIC_CONFIG_URL", "GTAG_URL", "logger", "ERRORS", "ERROR_FACTORY", "createGtagTrustedTypesScriptURL", "url", "startsWith", "err", "create", "gtagURL", "warn", "message", "promiseAllSettled", "promises", "Promise", "all", "map", "promise", "catch", "e", "createTrustedTypesPolicy", "policyName", "policyOptions", "trustedTypesPolicy", "window", "trustedTypes", "createPolicy", "insertScriptTag", "dataLayerName", "measurementId", "createScriptURL", "script", "document", "createElement", "gtagScriptURL", "src", "async", "head", "append<PERSON><PERSON><PERSON>", "getOrCreateDataLayer", "dataLayer", "Array", "isArray", "gtagOnConfig", "_x", "_x2", "_x3", "_x4", "_x5", "_x6", "_gtagOnConfig", "apply", "arguments", "_asyncToGenerator", "gtagCore", "initializationPromisesMap", "dynamicConfigPromisesList", "measurementIdToAppId", "gtagParams", "correspondingAppId", "dynamicConfigResults", "foundConfig", "find", "config", "appId", "error", "gtagOnEvent", "_x7", "_x8", "_x9", "_x0", "_x1", "_gtagOnEvent", "initializationPromisesToWaitFor", "gaSendToList", "sendToId", "initializationPromise", "push", "length", "Object", "values", "wrapGtag", "gtagWrapper", "_x10", "_gtagWrapper", "command", "args", "fieldName", "callback", "customParams", "wrapOrCreateGtag", "gtagFunctionName", "_args", "wrappedGtag", "findGtagScriptOnPage", "scriptTags", "getElementsByTagName", "tag", "includes", "LONG_RETRY_FACTOR", "BASE_INTERVAL_MILLIS", "RetryData", "constructor", "throttleMetadata", "<PERSON><PERSON><PERSON><PERSON>", "getThrottleMetadata", "setThrottleMetadata", "metadata", "deleteThrottleMetadata", "defaultRetryData", "getHeaders", "<PERSON><PERSON><PERSON><PERSON>", "Headers", "Accept", "fetchDynamicConfig", "_x11", "_fetchDynamicConfig", "appFields", "_a", "request", "method", "headers", "appUrl", "replace", "response", "fetch", "status", "errorMessage", "jsonResponse", "json", "_ignored", "httpStatus", "responseMessage", "fetchDynamicConfigWithRetry", "_x12", "_fetchDynamicConfigWithRetry", "app", "retryData", "timeout<PERSON><PERSON><PERSON>", "options", "backoffCount", "throttleEnd<PERSON>imeMill<PERSON>", "Date", "now", "signal", "AnalyticsAbortSignal", "setTimeout", "abort", "undefined", "attemptFetchDynamicConfigWithRetry", "_x13", "_x14", "_x15", "_attemptFetchDynamicConfigWithRetry", "setAbortableTimeout", "isRetriableError", "backoff<PERSON><PERSON><PERSON>", "Number", "customData", "debug", "resolve", "reject", "Math", "max", "timeout", "addEventListener", "clearTimeout", "listeners", "listener", "for<PERSON>ach", "defaultEventParametersForInit", "logEvent$1", "_x16", "_x17", "_x18", "_x19", "_x20", "_logEvent$", "gtagFunction", "eventName", "eventParams", "global", "params", "assign", "setCurrentScreen$1", "_x21", "_x22", "_x23", "_x24", "_setCurrentScreen$", "screenName", "update", "setUserId$1", "_x25", "_x26", "_x27", "_x28", "_setUserId$", "id", "setUserProperties$1", "_x29", "_x30", "_x31", "_x32", "_setUserProperties$", "properties", "flatProperties", "key", "keys", "internalGetGoogleAnalyticsClientId", "_x33", "_x34", "_internalGetGoogleAnalyticsClientId", "clientId", "setAnalyticsCollectionEnabled$1", "_x35", "_x36", "_setAnalyticsCollectionEnabled$", "enabled", "defaultConsentSettingsForInit", "_setConsentDefaultForInit", "consentSettings", "_setDefaultEventParametersForInit", "validateIndexedDB", "_validateIndexedDB", "errorInfo", "toString", "_initializeAnalytics", "_x37", "_x38", "_x39", "_x40", "_x41", "_x42", "_x43", "_initializeAnalytics2", "installations", "dynamicConfigPromise", "then", "fidPromise", "envIsValid", "getId", "dynamicConfig", "fid", "configProperties", "AnalyticsService", "_delete", "gtagName", "gtagCoreFunction", "wrappedGtagFunction", "globalInitDone", "settings", "warnOnBrowserContextMismatch", "mismatchedEnvMessages", "details", "index", "join", "factory", "analyticsInstance", "getAnalytics", "analyticsProvider", "isInitialized", "getImmediate", "initializeAnalytics", "existingInstance", "getOptions", "initialize", "isSupported", "_isSupported", "isDBOpenable", "setCurrentScreen", "getGoogleAnalyticsClientId", "_x44", "_getGoogleAnalyticsClientId", "setUserId", "setUserProperties", "setAnalyticsCollectionEnabled", "setDefaultEventParameters", "logEvent", "setConsent", "name", "version", "registerAnalytics", "container", "analyticsOptions", "get<PERSON><PERSON><PERSON>", "internalFactory", "analytics", "reason"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/fire/node_modules/@firebase/analytics/dist/esm/index.esm2017.js"], "sourcesContent": ["import { _getProvider, getApp, _registerComponent, registerVersion } from '@firebase/app';\nimport { Logger } from '@firebase/logger';\nimport { ErrorFactory, calculateBackoffMillis, FirebaseError, isIndexedDBAvailable, validateIndexedDBOpenable, isBrowserExtension, areCookiesEnabled, getModularInstance, deepEqual } from '@firebase/util';\nimport { Component } from '@firebase/component';\nimport '@firebase/installations';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Type constant for Firebase Analytics.\r\n */\r\nconst ANALYTICS_TYPE = 'analytics';\r\n// Key to attach FID to in gtag params.\r\nconst GA_FID_KEY = 'firebase_id';\r\nconst ORIGIN_KEY = 'origin';\r\nconst FETCH_TIMEOUT_MILLIS = 60 * 1000;\r\nconst DYNAMIC_CONFIG_URL = 'https://firebase.googleapis.com/v1alpha/projects/-/apps/{app-id}/webConfig';\r\nconst GTAG_URL = 'https://www.googletagmanager.com/gtag/js';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst logger = new Logger('@firebase/analytics');\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst ERRORS = {\r\n    [\"already-exists\" /* AnalyticsError.ALREADY_EXISTS */]: 'A Firebase Analytics instance with the appId {$id} ' +\r\n        ' already exists. ' +\r\n        'Only one Firebase Analytics instance can be created for each appId.',\r\n    [\"already-initialized\" /* AnalyticsError.ALREADY_INITIALIZED */]: 'initializeAnalytics() cannot be called again with different options than those ' +\r\n        'it was initially called with. It can be called again with the same options to ' +\r\n        'return the existing instance, or getAnalytics() can be used ' +\r\n        'to get a reference to the already-intialized instance.',\r\n    [\"already-initialized-settings\" /* AnalyticsError.ALREADY_INITIALIZED_SETTINGS */]: 'Firebase Analytics has already been initialized.' +\r\n        'settings() must be called before initializing any Analytics instance' +\r\n        'or it will have no effect.',\r\n    [\"interop-component-reg-failed\" /* AnalyticsError.INTEROP_COMPONENT_REG_FAILED */]: 'Firebase Analytics Interop Component failed to instantiate: {$reason}',\r\n    [\"invalid-analytics-context\" /* AnalyticsError.INVALID_ANALYTICS_CONTEXT */]: 'Firebase Analytics is not supported in this environment. ' +\r\n        'Wrap initialization of analytics in analytics.isSupported() ' +\r\n        'to prevent initialization in unsupported environments. Details: {$errorInfo}',\r\n    [\"indexeddb-unavailable\" /* AnalyticsError.INDEXEDDB_UNAVAILABLE */]: 'IndexedDB unavailable or restricted in this environment. ' +\r\n        'Wrap initialization of analytics in analytics.isSupported() ' +\r\n        'to prevent initialization in unsupported environments. Details: {$errorInfo}',\r\n    [\"fetch-throttle\" /* AnalyticsError.FETCH_THROTTLE */]: 'The config fetch request timed out while in an exponential backoff state.' +\r\n        ' Unix timestamp in milliseconds when fetch request throttling ends: {$throttleEndTimeMillis}.',\r\n    [\"config-fetch-failed\" /* AnalyticsError.CONFIG_FETCH_FAILED */]: 'Dynamic config fetch failed: [{$httpStatus}] {$responseMessage}',\r\n    [\"no-api-key\" /* AnalyticsError.NO_API_KEY */]: 'The \"apiKey\" field is empty in the local Firebase config. Firebase Analytics requires this field to' +\r\n        'contain a valid API key.',\r\n    [\"no-app-id\" /* AnalyticsError.NO_APP_ID */]: 'The \"appId\" field is empty in the local Firebase config. Firebase Analytics requires this field to' +\r\n        'contain a valid app ID.',\r\n    [\"no-client-id\" /* AnalyticsError.NO_CLIENT_ID */]: 'The \"client_id\" field is empty.',\r\n    [\"invalid-gtag-resource\" /* AnalyticsError.INVALID_GTAG_RESOURCE */]: 'Trusted Types detected an invalid gtag resource: {$gtagURL}.'\r\n};\r\nconst ERROR_FACTORY = new ErrorFactory('analytics', 'Analytics', ERRORS);\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Verifies and creates a TrustedScriptURL.\r\n */\r\nfunction createGtagTrustedTypesScriptURL(url) {\r\n    if (!url.startsWith(GTAG_URL)) {\r\n        const err = ERROR_FACTORY.create(\"invalid-gtag-resource\" /* AnalyticsError.INVALID_GTAG_RESOURCE */, {\r\n            gtagURL: url\r\n        });\r\n        logger.warn(err.message);\r\n        return '';\r\n    }\r\n    return url;\r\n}\r\n/**\r\n * Makeshift polyfill for Promise.allSettled(). Resolves when all promises\r\n * have either resolved or rejected.\r\n *\r\n * @param promises Array of promises to wait for.\r\n */\r\nfunction promiseAllSettled(promises) {\r\n    return Promise.all(promises.map(promise => promise.catch(e => e)));\r\n}\r\n/**\r\n * Creates a TrustedTypePolicy object that implements the rules passed as policyOptions.\r\n *\r\n * @param policyName A string containing the name of the policy\r\n * @param policyOptions Object containing implementations of instance methods for TrustedTypesPolicy, see {@link https://developer.mozilla.org/en-US/docs/Web/API/TrustedTypePolicy#instance_methods\r\n * | the TrustedTypePolicy reference documentation}.\r\n */\r\nfunction createTrustedTypesPolicy(policyName, policyOptions) {\r\n    // Create a TrustedTypes policy that we can use for updating src\r\n    // properties\r\n    let trustedTypesPolicy;\r\n    if (window.trustedTypes) {\r\n        trustedTypesPolicy = window.trustedTypes.createPolicy(policyName, policyOptions);\r\n    }\r\n    return trustedTypesPolicy;\r\n}\r\n/**\r\n * Inserts gtag script tag into the page to asynchronously download gtag.\r\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\r\n */\r\nfunction insertScriptTag(dataLayerName, measurementId) {\r\n    const trustedTypesPolicy = createTrustedTypesPolicy('firebase-js-sdk-policy', {\r\n        createScriptURL: createGtagTrustedTypesScriptURL\r\n    });\r\n    const script = document.createElement('script');\r\n    // We are not providing an analyticsId in the URL because it would trigger a `page_view`\r\n    // without fid. We will initialize ga-id using gtag (config) command together with fid.\r\n    const gtagScriptURL = `${GTAG_URL}?l=${dataLayerName}&id=${measurementId}`;\r\n    script.src = trustedTypesPolicy\r\n        ? trustedTypesPolicy === null || trustedTypesPolicy === void 0 ? void 0 : trustedTypesPolicy.createScriptURL(gtagScriptURL)\r\n        : gtagScriptURL;\r\n    script.async = true;\r\n    document.head.appendChild(script);\r\n}\r\n/**\r\n * Get reference to, or create, global datalayer.\r\n * @param dataLayerName Name of datalayer (most often the default, \"_dataLayer\").\r\n */\r\nfunction getOrCreateDataLayer(dataLayerName) {\r\n    // Check for existing dataLayer and create if needed.\r\n    let dataLayer = [];\r\n    if (Array.isArray(window[dataLayerName])) {\r\n        dataLayer = window[dataLayerName];\r\n    }\r\n    else {\r\n        window[dataLayerName] = dataLayer;\r\n    }\r\n    return dataLayer;\r\n}\r\n/**\r\n * Wrapped gtag logic when gtag is called with 'config' command.\r\n *\r\n * @param gtagCore Basic gtag function that just appends to dataLayer.\r\n * @param initializationPromisesMap Map of appIds to their initialization promises.\r\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\r\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\r\n * @param measurementId GA Measurement ID to set config for.\r\n * @param gtagParams Gtag config params to set.\r\n */\r\nasync function gtagOnConfig(gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId, measurementId, gtagParams) {\r\n    // If config is already fetched, we know the appId and can use it to look up what FID promise we\r\n    /// are waiting for, and wait only on that one.\r\n    const correspondingAppId = measurementIdToAppId[measurementId];\r\n    try {\r\n        if (correspondingAppId) {\r\n            await initializationPromisesMap[correspondingAppId];\r\n        }\r\n        else {\r\n            // If config is not fetched yet, wait for all configs (we don't know which one we need) and\r\n            // find the appId (if any) corresponding to this measurementId. If there is one, wait on\r\n            // that appId's initialization promise. If there is none, promise resolves and gtag\r\n            // call goes through.\r\n            const dynamicConfigResults = await promiseAllSettled(dynamicConfigPromisesList);\r\n            const foundConfig = dynamicConfigResults.find(config => config.measurementId === measurementId);\r\n            if (foundConfig) {\r\n                await initializationPromisesMap[foundConfig.appId];\r\n            }\r\n        }\r\n    }\r\n    catch (e) {\r\n        logger.error(e);\r\n    }\r\n    gtagCore(\"config\" /* GtagCommand.CONFIG */, measurementId, gtagParams);\r\n}\r\n/**\r\n * Wrapped gtag logic when gtag is called with 'event' command.\r\n *\r\n * @param gtagCore Basic gtag function that just appends to dataLayer.\r\n * @param initializationPromisesMap Map of appIds to their initialization promises.\r\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\r\n * @param measurementId GA Measurement ID to log event to.\r\n * @param gtagParams Params to log with this event.\r\n */\r\nasync function gtagOnEvent(gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementId, gtagParams) {\r\n    try {\r\n        let initializationPromisesToWaitFor = [];\r\n        // If there's a 'send_to' param, check if any ID specified matches\r\n        // an initializeIds() promise we are waiting for.\r\n        if (gtagParams && gtagParams['send_to']) {\r\n            let gaSendToList = gtagParams['send_to'];\r\n            // Make it an array if is isn't, so it can be dealt with the same way.\r\n            if (!Array.isArray(gaSendToList)) {\r\n                gaSendToList = [gaSendToList];\r\n            }\r\n            // Checking 'send_to' fields requires having all measurement ID results back from\r\n            // the dynamic config fetch.\r\n            const dynamicConfigResults = await promiseAllSettled(dynamicConfigPromisesList);\r\n            for (const sendToId of gaSendToList) {\r\n                // Any fetched dynamic measurement ID that matches this 'send_to' ID\r\n                const foundConfig = dynamicConfigResults.find(config => config.measurementId === sendToId);\r\n                const initializationPromise = foundConfig && initializationPromisesMap[foundConfig.appId];\r\n                if (initializationPromise) {\r\n                    initializationPromisesToWaitFor.push(initializationPromise);\r\n                }\r\n                else {\r\n                    // Found an item in 'send_to' that is not associated\r\n                    // directly with an FID, possibly a group.  Empty this array,\r\n                    // exit the loop early, and let it get populated below.\r\n                    initializationPromisesToWaitFor = [];\r\n                    break;\r\n                }\r\n            }\r\n        }\r\n        // This will be unpopulated if there was no 'send_to' field , or\r\n        // if not all entries in the 'send_to' field could be mapped to\r\n        // a FID. In these cases, wait on all pending initialization promises.\r\n        if (initializationPromisesToWaitFor.length === 0) {\r\n            initializationPromisesToWaitFor = Object.values(initializationPromisesMap);\r\n        }\r\n        // Run core gtag function with args after all relevant initialization\r\n        // promises have been resolved.\r\n        await Promise.all(initializationPromisesToWaitFor);\r\n        // Workaround for http://b/141370449 - third argument cannot be undefined.\r\n        gtagCore(\"event\" /* GtagCommand.EVENT */, measurementId, gtagParams || {});\r\n    }\r\n    catch (e) {\r\n        logger.error(e);\r\n    }\r\n}\r\n/**\r\n * Wraps a standard gtag function with extra code to wait for completion of\r\n * relevant initialization promises before sending requests.\r\n *\r\n * @param gtagCore Basic gtag function that just appends to dataLayer.\r\n * @param initializationPromisesMap Map of appIds to their initialization promises.\r\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\r\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\r\n */\r\nfunction wrapGtag(gtagCore, \r\n/**\r\n * Allows wrapped gtag calls to wait on whichever intialization promises are required,\r\n * depending on the contents of the gtag params' `send_to` field, if any.\r\n */\r\ninitializationPromisesMap, \r\n/**\r\n * Wrapped gtag calls sometimes require all dynamic config fetches to have returned\r\n * before determining what initialization promises (which include FIDs) to wait for.\r\n */\r\ndynamicConfigPromisesList, \r\n/**\r\n * Wrapped gtag config calls can narrow down which initialization promise (with FID)\r\n * to wait for if the measurementId is already fetched, by getting the corresponding appId,\r\n * which is the key for the initialization promises map.\r\n */\r\nmeasurementIdToAppId) {\r\n    /**\r\n     * Wrapper around gtag that ensures FID is sent with gtag calls.\r\n     * @param command Gtag command type.\r\n     * @param idOrNameOrParams Measurement ID if command is EVENT/CONFIG, params if command is SET.\r\n     * @param gtagParams Params if event is EVENT/CONFIG.\r\n     */\r\n    async function gtagWrapper(command, ...args) {\r\n        try {\r\n            // If event, check that relevant initialization promises have completed.\r\n            if (command === \"event\" /* GtagCommand.EVENT */) {\r\n                const [measurementId, gtagParams] = args;\r\n                // If EVENT, second arg must be measurementId.\r\n                await gtagOnEvent(gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementId, gtagParams);\r\n            }\r\n            else if (command === \"config\" /* GtagCommand.CONFIG */) {\r\n                const [measurementId, gtagParams] = args;\r\n                // If CONFIG, second arg must be measurementId.\r\n                await gtagOnConfig(gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId, measurementId, gtagParams);\r\n            }\r\n            else if (command === \"consent\" /* GtagCommand.CONSENT */) {\r\n                const [gtagParams] = args;\r\n                gtagCore(\"consent\" /* GtagCommand.CONSENT */, 'update', gtagParams);\r\n            }\r\n            else if (command === \"get\" /* GtagCommand.GET */) {\r\n                const [measurementId, fieldName, callback] = args;\r\n                gtagCore(\"get\" /* GtagCommand.GET */, measurementId, fieldName, callback);\r\n            }\r\n            else if (command === \"set\" /* GtagCommand.SET */) {\r\n                const [customParams] = args;\r\n                // If SET, second arg must be params.\r\n                gtagCore(\"set\" /* GtagCommand.SET */, customParams);\r\n            }\r\n            else {\r\n                gtagCore(command, ...args);\r\n            }\r\n        }\r\n        catch (e) {\r\n            logger.error(e);\r\n        }\r\n    }\r\n    return gtagWrapper;\r\n}\r\n/**\r\n * Creates global gtag function or wraps existing one if found.\r\n * This wrapped function attaches Firebase instance ID (FID) to gtag 'config' and\r\n * 'event' calls that belong to the GAID associated with this Firebase instance.\r\n *\r\n * @param initializationPromisesMap Map of appIds to their initialization promises.\r\n * @param dynamicConfigPromisesList Array of dynamic config fetch promises.\r\n * @param measurementIdToAppId Map of GA measurementIDs to corresponding Firebase appId.\r\n * @param dataLayerName Name of global GA datalayer array.\r\n * @param gtagFunctionName Name of global gtag function (\"gtag\" if not user-specified).\r\n */\r\nfunction wrapOrCreateGtag(initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId, dataLayerName, gtagFunctionName) {\r\n    // Create a basic core gtag function\r\n    let gtagCore = function (..._args) {\r\n        // Must push IArguments object, not an array.\r\n        window[dataLayerName].push(arguments);\r\n    };\r\n    // Replace it with existing one if found\r\n    if (window[gtagFunctionName] &&\r\n        typeof window[gtagFunctionName] === 'function') {\r\n        // @ts-ignore\r\n        gtagCore = window[gtagFunctionName];\r\n    }\r\n    window[gtagFunctionName] = wrapGtag(gtagCore, initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId);\r\n    return {\r\n        gtagCore,\r\n        wrappedGtag: window[gtagFunctionName]\r\n    };\r\n}\r\n/**\r\n * Returns the script tag in the DOM matching both the gtag url pattern\r\n * and the provided data layer name.\r\n */\r\nfunction findGtagScriptOnPage(dataLayerName) {\r\n    const scriptTags = window.document.getElementsByTagName('script');\r\n    for (const tag of Object.values(scriptTags)) {\r\n        if (tag.src &&\r\n            tag.src.includes(GTAG_URL) &&\r\n            tag.src.includes(dataLayerName)) {\r\n            return tag;\r\n        }\r\n    }\r\n    return null;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Backoff factor for 503 errors, which we want to be conservative about\r\n * to avoid overloading servers. Each retry interval will be\r\n * BASE_INTERVAL_MILLIS * LONG_RETRY_FACTOR ^ retryCount, so the second one\r\n * will be ~30 seconds (with fuzzing).\r\n */\r\nconst LONG_RETRY_FACTOR = 30;\r\n/**\r\n * Base wait interval to multiplied by backoffFactor^backoffCount.\r\n */\r\nconst BASE_INTERVAL_MILLIS = 1000;\r\n/**\r\n * Stubbable retry data storage class.\r\n */\r\nclass RetryData {\r\n    constructor(throttleMetadata = {}, intervalMillis = BASE_INTERVAL_MILLIS) {\r\n        this.throttleMetadata = throttleMetadata;\r\n        this.intervalMillis = intervalMillis;\r\n    }\r\n    getThrottleMetadata(appId) {\r\n        return this.throttleMetadata[appId];\r\n    }\r\n    setThrottleMetadata(appId, metadata) {\r\n        this.throttleMetadata[appId] = metadata;\r\n    }\r\n    deleteThrottleMetadata(appId) {\r\n        delete this.throttleMetadata[appId];\r\n    }\r\n}\r\nconst defaultRetryData = new RetryData();\r\n/**\r\n * Set GET request headers.\r\n * @param apiKey App API key.\r\n */\r\nfunction getHeaders(apiKey) {\r\n    return new Headers({\r\n        Accept: 'application/json',\r\n        'x-goog-api-key': apiKey\r\n    });\r\n}\r\n/**\r\n * Fetches dynamic config from backend.\r\n * @param app Firebase app to fetch config for.\r\n */\r\nasync function fetchDynamicConfig(appFields) {\r\n    var _a;\r\n    const { appId, apiKey } = appFields;\r\n    const request = {\r\n        method: 'GET',\r\n        headers: getHeaders(apiKey)\r\n    };\r\n    const appUrl = DYNAMIC_CONFIG_URL.replace('{app-id}', appId);\r\n    const response = await fetch(appUrl, request);\r\n    if (response.status !== 200 && response.status !== 304) {\r\n        let errorMessage = '';\r\n        try {\r\n            // Try to get any error message text from server response.\r\n            const jsonResponse = (await response.json());\r\n            if ((_a = jsonResponse.error) === null || _a === void 0 ? void 0 : _a.message) {\r\n                errorMessage = jsonResponse.error.message;\r\n            }\r\n        }\r\n        catch (_ignored) { }\r\n        throw ERROR_FACTORY.create(\"config-fetch-failed\" /* AnalyticsError.CONFIG_FETCH_FAILED */, {\r\n            httpStatus: response.status,\r\n            responseMessage: errorMessage\r\n        });\r\n    }\r\n    return response.json();\r\n}\r\n/**\r\n * Fetches dynamic config from backend, retrying if failed.\r\n * @param app Firebase app to fetch config for.\r\n */\r\nasync function fetchDynamicConfigWithRetry(app, \r\n// retryData and timeoutMillis are parameterized to allow passing a different value for testing.\r\nretryData = defaultRetryData, timeoutMillis) {\r\n    const { appId, apiKey, measurementId } = app.options;\r\n    if (!appId) {\r\n        throw ERROR_FACTORY.create(\"no-app-id\" /* AnalyticsError.NO_APP_ID */);\r\n    }\r\n    if (!apiKey) {\r\n        if (measurementId) {\r\n            return {\r\n                measurementId,\r\n                appId\r\n            };\r\n        }\r\n        throw ERROR_FACTORY.create(\"no-api-key\" /* AnalyticsError.NO_API_KEY */);\r\n    }\r\n    const throttleMetadata = retryData.getThrottleMetadata(appId) || {\r\n        backoffCount: 0,\r\n        throttleEndTimeMillis: Date.now()\r\n    };\r\n    const signal = new AnalyticsAbortSignal();\r\n    setTimeout(async () => {\r\n        // Note a very low delay, eg < 10ms, can elapse before listeners are initialized.\r\n        signal.abort();\r\n    }, timeoutMillis !== undefined ? timeoutMillis : FETCH_TIMEOUT_MILLIS);\r\n    return attemptFetchDynamicConfigWithRetry({ appId, apiKey, measurementId }, throttleMetadata, signal, retryData);\r\n}\r\n/**\r\n * Runs one retry attempt.\r\n * @param appFields Necessary app config fields.\r\n * @param throttleMetadata Ongoing metadata to determine throttling times.\r\n * @param signal Abort signal.\r\n */\r\nasync function attemptFetchDynamicConfigWithRetry(appFields, { throttleEndTimeMillis, backoffCount }, signal, retryData = defaultRetryData // for testing\r\n) {\r\n    var _a;\r\n    const { appId, measurementId } = appFields;\r\n    // Starts with a (potentially zero) timeout to support resumption from stored state.\r\n    // Ensures the throttle end time is honored if the last attempt timed out.\r\n    // Note the SDK will never make a request if the fetch timeout expires at this point.\r\n    try {\r\n        await setAbortableTimeout(signal, throttleEndTimeMillis);\r\n    }\r\n    catch (e) {\r\n        if (measurementId) {\r\n            logger.warn(`Timed out fetching this Firebase app's measurement ID from the server.` +\r\n                ` Falling back to the measurement ID ${measurementId}` +\r\n                ` provided in the \"measurementId\" field in the local Firebase config. [${e === null || e === void 0 ? void 0 : e.message}]`);\r\n            return { appId, measurementId };\r\n        }\r\n        throw e;\r\n    }\r\n    try {\r\n        const response = await fetchDynamicConfig(appFields);\r\n        // Note the SDK only clears throttle state if response is success or non-retriable.\r\n        retryData.deleteThrottleMetadata(appId);\r\n        return response;\r\n    }\r\n    catch (e) {\r\n        const error = e;\r\n        if (!isRetriableError(error)) {\r\n            retryData.deleteThrottleMetadata(appId);\r\n            if (measurementId) {\r\n                logger.warn(`Failed to fetch this Firebase app's measurement ID from the server.` +\r\n                    ` Falling back to the measurement ID ${measurementId}` +\r\n                    ` provided in the \"measurementId\" field in the local Firebase config. [${error === null || error === void 0 ? void 0 : error.message}]`);\r\n                return { appId, measurementId };\r\n            }\r\n            else {\r\n                throw e;\r\n            }\r\n        }\r\n        const backoffMillis = Number((_a = error === null || error === void 0 ? void 0 : error.customData) === null || _a === void 0 ? void 0 : _a.httpStatus) === 503\r\n            ? calculateBackoffMillis(backoffCount, retryData.intervalMillis, LONG_RETRY_FACTOR)\r\n            : calculateBackoffMillis(backoffCount, retryData.intervalMillis);\r\n        // Increments backoff state.\r\n        const throttleMetadata = {\r\n            throttleEndTimeMillis: Date.now() + backoffMillis,\r\n            backoffCount: backoffCount + 1\r\n        };\r\n        // Persists state.\r\n        retryData.setThrottleMetadata(appId, throttleMetadata);\r\n        logger.debug(`Calling attemptFetch again in ${backoffMillis} millis`);\r\n        return attemptFetchDynamicConfigWithRetry(appFields, throttleMetadata, signal, retryData);\r\n    }\r\n}\r\n/**\r\n * Supports waiting on a backoff by:\r\n *\r\n * <ul>\r\n *   <li>Promisifying setTimeout, so we can set a timeout in our Promise chain</li>\r\n *   <li>Listening on a signal bus for abort events, just like the Fetch API</li>\r\n *   <li>Failing in the same way the Fetch API fails, so timing out a live request and a throttled\r\n *       request appear the same.</li>\r\n * </ul>\r\n *\r\n * <p>Visible for testing.\r\n */\r\nfunction setAbortableTimeout(signal, throttleEndTimeMillis) {\r\n    return new Promise((resolve, reject) => {\r\n        // Derives backoff from given end time, normalizing negative numbers to zero.\r\n        const backoffMillis = Math.max(throttleEndTimeMillis - Date.now(), 0);\r\n        const timeout = setTimeout(resolve, backoffMillis);\r\n        // Adds listener, rather than sets onabort, because signal is a shared object.\r\n        signal.addEventListener(() => {\r\n            clearTimeout(timeout);\r\n            // If the request completes before this timeout, the rejection has no effect.\r\n            reject(ERROR_FACTORY.create(\"fetch-throttle\" /* AnalyticsError.FETCH_THROTTLE */, {\r\n                throttleEndTimeMillis\r\n            }));\r\n        });\r\n    });\r\n}\r\n/**\r\n * Returns true if the {@link Error} indicates a fetch request may succeed later.\r\n */\r\nfunction isRetriableError(e) {\r\n    if (!(e instanceof FirebaseError) || !e.customData) {\r\n        return false;\r\n    }\r\n    // Uses string index defined by ErrorData, which FirebaseError implements.\r\n    const httpStatus = Number(e.customData['httpStatus']);\r\n    return (httpStatus === 429 ||\r\n        httpStatus === 500 ||\r\n        httpStatus === 503 ||\r\n        httpStatus === 504);\r\n}\r\n/**\r\n * Shims a minimal AbortSignal (copied from Remote Config).\r\n *\r\n * <p>AbortController's AbortSignal conveniently decouples fetch timeout logic from other aspects\r\n * of networking, such as retries. Firebase doesn't use AbortController enough to justify a\r\n * polyfill recommendation, like we do with the Fetch API, but this minimal shim can easily be\r\n * swapped out if/when we do.\r\n */\r\nclass AnalyticsAbortSignal {\r\n    constructor() {\r\n        this.listeners = [];\r\n    }\r\n    addEventListener(listener) {\r\n        this.listeners.push(listener);\r\n    }\r\n    abort() {\r\n        this.listeners.forEach(listener => listener());\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Event parameters to set on 'gtag' during initialization.\r\n */\r\nlet defaultEventParametersForInit;\r\n/**\r\n * Logs an analytics event through the Firebase SDK.\r\n *\r\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\r\n * @param eventName Google Analytics event name, choose from standard list or use a custom string.\r\n * @param eventParams Analytics event parameters.\r\n */\r\nasync function logEvent$1(gtagFunction, initializationPromise, eventName, eventParams, options) {\r\n    if (options && options.global) {\r\n        gtagFunction(\"event\" /* GtagCommand.EVENT */, eventName, eventParams);\r\n        return;\r\n    }\r\n    else {\r\n        const measurementId = await initializationPromise;\r\n        const params = Object.assign(Object.assign({}, eventParams), { 'send_to': measurementId });\r\n        gtagFunction(\"event\" /* GtagCommand.EVENT */, eventName, params);\r\n    }\r\n}\r\n/**\r\n * Set screen_name parameter for this Google Analytics ID.\r\n *\r\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\r\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\r\n *\r\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\r\n * @param screenName Screen name string to set.\r\n */\r\nasync function setCurrentScreen$1(gtagFunction, initializationPromise, screenName, options) {\r\n    if (options && options.global) {\r\n        gtagFunction(\"set\" /* GtagCommand.SET */, { 'screen_name': screenName });\r\n        return Promise.resolve();\r\n    }\r\n    else {\r\n        const measurementId = await initializationPromise;\r\n        gtagFunction(\"config\" /* GtagCommand.CONFIG */, measurementId, {\r\n            update: true,\r\n            'screen_name': screenName\r\n        });\r\n    }\r\n}\r\n/**\r\n * Set user_id parameter for this Google Analytics ID.\r\n *\r\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\r\n * @param id User ID string to set\r\n */\r\nasync function setUserId$1(gtagFunction, initializationPromise, id, options) {\r\n    if (options && options.global) {\r\n        gtagFunction(\"set\" /* GtagCommand.SET */, { 'user_id': id });\r\n        return Promise.resolve();\r\n    }\r\n    else {\r\n        const measurementId = await initializationPromise;\r\n        gtagFunction(\"config\" /* GtagCommand.CONFIG */, measurementId, {\r\n            update: true,\r\n            'user_id': id\r\n        });\r\n    }\r\n}\r\n/**\r\n * Set all other user properties other than user_id and screen_name.\r\n *\r\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\r\n * @param properties Map of user properties to set\r\n */\r\nasync function setUserProperties$1(gtagFunction, initializationPromise, properties, options) {\r\n    if (options && options.global) {\r\n        const flatProperties = {};\r\n        for (const key of Object.keys(properties)) {\r\n            // use dot notation for merge behavior in gtag.js\r\n            flatProperties[`user_properties.${key}`] = properties[key];\r\n        }\r\n        gtagFunction(\"set\" /* GtagCommand.SET */, flatProperties);\r\n        return Promise.resolve();\r\n    }\r\n    else {\r\n        const measurementId = await initializationPromise;\r\n        gtagFunction(\"config\" /* GtagCommand.CONFIG */, measurementId, {\r\n            update: true,\r\n            'user_properties': properties\r\n        });\r\n    }\r\n}\r\n/**\r\n * Retrieves a unique Google Analytics identifier for the web client.\r\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\r\n *\r\n * @param gtagFunction Wrapped gtag function that waits for fid to be set before sending an event\r\n */\r\nasync function internalGetGoogleAnalyticsClientId(gtagFunction, initializationPromise) {\r\n    const measurementId = await initializationPromise;\r\n    return new Promise((resolve, reject) => {\r\n        gtagFunction(\"get\" /* GtagCommand.GET */, measurementId, 'client_id', (clientId) => {\r\n            if (!clientId) {\r\n                reject(ERROR_FACTORY.create(\"no-client-id\" /* AnalyticsError.NO_CLIENT_ID */));\r\n            }\r\n            resolve(clientId);\r\n        });\r\n    });\r\n}\r\n/**\r\n * Set whether collection is enabled for this ID.\r\n *\r\n * @param enabled If true, collection is enabled for this ID.\r\n */\r\nasync function setAnalyticsCollectionEnabled$1(initializationPromise, enabled) {\r\n    const measurementId = await initializationPromise;\r\n    window[`ga-disable-${measurementId}`] = !enabled;\r\n}\r\n/**\r\n * Consent parameters to default to during 'gtag' initialization.\r\n */\r\nlet defaultConsentSettingsForInit;\r\n/**\r\n * Sets the variable {@link defaultConsentSettingsForInit} for use in the initialization of\r\n * analytics.\r\n *\r\n * @param consentSettings Maps the applicable end user consent state for gtag.js.\r\n */\r\nfunction _setConsentDefaultForInit(consentSettings) {\r\n    defaultConsentSettingsForInit = consentSettings;\r\n}\r\n/**\r\n * Sets the variable `defaultEventParametersForInit` for use in the initialization of\r\n * analytics.\r\n *\r\n * @param customParams Any custom params the user may pass to gtag.js.\r\n */\r\nfunction _setDefaultEventParametersForInit(customParams) {\r\n    defaultEventParametersForInit = customParams;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nasync function validateIndexedDB() {\r\n    if (!isIndexedDBAvailable()) {\r\n        logger.warn(ERROR_FACTORY.create(\"indexeddb-unavailable\" /* AnalyticsError.INDEXEDDB_UNAVAILABLE */, {\r\n            errorInfo: 'IndexedDB is not available in this environment.'\r\n        }).message);\r\n        return false;\r\n    }\r\n    else {\r\n        try {\r\n            await validateIndexedDBOpenable();\r\n        }\r\n        catch (e) {\r\n            logger.warn(ERROR_FACTORY.create(\"indexeddb-unavailable\" /* AnalyticsError.INDEXEDDB_UNAVAILABLE */, {\r\n                errorInfo: e === null || e === void 0 ? void 0 : e.toString()\r\n            }).message);\r\n            return false;\r\n        }\r\n    }\r\n    return true;\r\n}\r\n/**\r\n * Initialize the analytics instance in gtag.js by calling config command with fid.\r\n *\r\n * NOTE: We combine analytics initialization and setting fid together because we want fid to be\r\n * part of the `page_view` event that's sent during the initialization\r\n * @param app Firebase app\r\n * @param gtagCore The gtag function that's not wrapped.\r\n * @param dynamicConfigPromisesList Array of all dynamic config promises.\r\n * @param measurementIdToAppId Maps measurementID to appID.\r\n * @param installations _FirebaseInstallationsInternal instance.\r\n *\r\n * @returns Measurement ID.\r\n */\r\nasync function _initializeAnalytics(app, dynamicConfigPromisesList, measurementIdToAppId, installations, gtagCore, dataLayerName, options) {\r\n    var _a;\r\n    const dynamicConfigPromise = fetchDynamicConfigWithRetry(app);\r\n    // Once fetched, map measurementIds to appId, for ease of lookup in wrapped gtag function.\r\n    dynamicConfigPromise\r\n        .then(config => {\r\n        measurementIdToAppId[config.measurementId] = config.appId;\r\n        if (app.options.measurementId &&\r\n            config.measurementId !== app.options.measurementId) {\r\n            logger.warn(`The measurement ID in the local Firebase config (${app.options.measurementId})` +\r\n                ` does not match the measurement ID fetched from the server (${config.measurementId}).` +\r\n                ` To ensure analytics events are always sent to the correct Analytics property,` +\r\n                ` update the` +\r\n                ` measurement ID field in the local config or remove it from the local config.`);\r\n        }\r\n    })\r\n        .catch(e => logger.error(e));\r\n    // Add to list to track state of all dynamic config promises.\r\n    dynamicConfigPromisesList.push(dynamicConfigPromise);\r\n    const fidPromise = validateIndexedDB().then(envIsValid => {\r\n        if (envIsValid) {\r\n            return installations.getId();\r\n        }\r\n        else {\r\n            return undefined;\r\n        }\r\n    });\r\n    const [dynamicConfig, fid] = await Promise.all([\r\n        dynamicConfigPromise,\r\n        fidPromise\r\n    ]);\r\n    // Detect if user has already put the gtag <script> tag on this page with the passed in\r\n    // data layer name.\r\n    if (!findGtagScriptOnPage(dataLayerName)) {\r\n        insertScriptTag(dataLayerName, dynamicConfig.measurementId);\r\n    }\r\n    // Detects if there are consent settings that need to be configured.\r\n    if (defaultConsentSettingsForInit) {\r\n        gtagCore(\"consent\" /* GtagCommand.CONSENT */, 'default', defaultConsentSettingsForInit);\r\n        _setConsentDefaultForInit(undefined);\r\n    }\r\n    // This command initializes gtag.js and only needs to be called once for the entire web app,\r\n    // but since it is idempotent, we can call it multiple times.\r\n    // We keep it together with other initialization logic for better code structure.\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    gtagCore('js', new Date());\r\n    // User config added first. We don't want users to accidentally overwrite\r\n    // base Firebase config properties.\r\n    const configProperties = (_a = options === null || options === void 0 ? void 0 : options.config) !== null && _a !== void 0 ? _a : {};\r\n    // guard against developers accidentally setting properties with prefix `firebase_`\r\n    configProperties[ORIGIN_KEY] = 'firebase';\r\n    configProperties.update = true;\r\n    if (fid != null) {\r\n        configProperties[GA_FID_KEY] = fid;\r\n    }\r\n    // It should be the first config command called on this GA-ID\r\n    // Initialize this GA-ID and set FID on it using the gtag config API.\r\n    // Note: This will trigger a page_view event unless 'send_page_view' is set to false in\r\n    // `configProperties`.\r\n    gtagCore(\"config\" /* GtagCommand.CONFIG */, dynamicConfig.measurementId, configProperties);\r\n    // Detects if there is data that will be set on every event logged from the SDK.\r\n    if (defaultEventParametersForInit) {\r\n        gtagCore(\"set\" /* GtagCommand.SET */, defaultEventParametersForInit);\r\n        _setDefaultEventParametersForInit(undefined);\r\n    }\r\n    return dynamicConfig.measurementId;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Analytics Service class.\r\n */\r\nclass AnalyticsService {\r\n    constructor(app) {\r\n        this.app = app;\r\n    }\r\n    _delete() {\r\n        delete initializationPromisesMap[this.app.options.appId];\r\n        return Promise.resolve();\r\n    }\r\n}\r\n/**\r\n * Maps appId to full initialization promise. Wrapped gtag calls must wait on\r\n * all or some of these, depending on the call's `send_to` param and the status\r\n * of the dynamic config fetches (see below).\r\n */\r\nlet initializationPromisesMap = {};\r\n/**\r\n * List of dynamic config fetch promises. In certain cases, wrapped gtag calls\r\n * wait on all these to be complete in order to determine if it can selectively\r\n * wait for only certain initialization (FID) promises or if it must wait for all.\r\n */\r\nlet dynamicConfigPromisesList = [];\r\n/**\r\n * Maps fetched measurementIds to appId. Populated when the app's dynamic config\r\n * fetch completes. If already populated, gtag config calls can use this to\r\n * selectively wait for only this app's initialization promise (FID) instead of all\r\n * initialization promises.\r\n */\r\nconst measurementIdToAppId = {};\r\n/**\r\n * Name for window global data layer array used by GA: defaults to 'dataLayer'.\r\n */\r\nlet dataLayerName = 'dataLayer';\r\n/**\r\n * Name for window global gtag function used by GA: defaults to 'gtag'.\r\n */\r\nlet gtagName = 'gtag';\r\n/**\r\n * Reproduction of standard gtag function or reference to existing\r\n * gtag function on window object.\r\n */\r\nlet gtagCoreFunction;\r\n/**\r\n * Wrapper around gtag function that ensures FID is sent with all\r\n * relevant event and config calls.\r\n */\r\nlet wrappedGtagFunction;\r\n/**\r\n * Flag to ensure page initialization steps (creation or wrapping of\r\n * dataLayer and gtag script) are only run once per page load.\r\n */\r\nlet globalInitDone = false;\r\n/**\r\n * Configures Firebase Analytics to use custom `gtag` or `dataLayer` names.\r\n * Intended to be used if `gtag.js` script has been installed on\r\n * this page independently of Firebase Analytics, and is using non-default\r\n * names for either the `gtag` function or for `dataLayer`.\r\n * Must be called before calling `getAnalytics()` or it won't\r\n * have any effect.\r\n *\r\n * @public\r\n *\r\n * @param options - Custom gtag and dataLayer names.\r\n */\r\nfunction settings(options) {\r\n    if (globalInitDone) {\r\n        throw ERROR_FACTORY.create(\"already-initialized\" /* AnalyticsError.ALREADY_INITIALIZED */);\r\n    }\r\n    if (options.dataLayerName) {\r\n        dataLayerName = options.dataLayerName;\r\n    }\r\n    if (options.gtagName) {\r\n        gtagName = options.gtagName;\r\n    }\r\n}\r\n/**\r\n * Returns true if no environment mismatch is found.\r\n * If environment mismatches are found, throws an INVALID_ANALYTICS_CONTEXT\r\n * error that also lists details for each mismatch found.\r\n */\r\nfunction warnOnBrowserContextMismatch() {\r\n    const mismatchedEnvMessages = [];\r\n    if (isBrowserExtension()) {\r\n        mismatchedEnvMessages.push('This is a browser extension environment.');\r\n    }\r\n    if (!areCookiesEnabled()) {\r\n        mismatchedEnvMessages.push('Cookies are not available.');\r\n    }\r\n    if (mismatchedEnvMessages.length > 0) {\r\n        const details = mismatchedEnvMessages\r\n            .map((message, index) => `(${index + 1}) ${message}`)\r\n            .join(' ');\r\n        const err = ERROR_FACTORY.create(\"invalid-analytics-context\" /* AnalyticsError.INVALID_ANALYTICS_CONTEXT */, {\r\n            errorInfo: details\r\n        });\r\n        logger.warn(err.message);\r\n    }\r\n}\r\n/**\r\n * Analytics instance factory.\r\n * @internal\r\n */\r\nfunction factory(app, installations, options) {\r\n    warnOnBrowserContextMismatch();\r\n    const appId = app.options.appId;\r\n    if (!appId) {\r\n        throw ERROR_FACTORY.create(\"no-app-id\" /* AnalyticsError.NO_APP_ID */);\r\n    }\r\n    if (!app.options.apiKey) {\r\n        if (app.options.measurementId) {\r\n            logger.warn(`The \"apiKey\" field is empty in the local Firebase config. This is needed to fetch the latest` +\r\n                ` measurement ID for this Firebase app. Falling back to the measurement ID ${app.options.measurementId}` +\r\n                ` provided in the \"measurementId\" field in the local Firebase config.`);\r\n        }\r\n        else {\r\n            throw ERROR_FACTORY.create(\"no-api-key\" /* AnalyticsError.NO_API_KEY */);\r\n        }\r\n    }\r\n    if (initializationPromisesMap[appId] != null) {\r\n        throw ERROR_FACTORY.create(\"already-exists\" /* AnalyticsError.ALREADY_EXISTS */, {\r\n            id: appId\r\n        });\r\n    }\r\n    if (!globalInitDone) {\r\n        // Steps here should only be done once per page: creation or wrapping\r\n        // of dataLayer and global gtag function.\r\n        getOrCreateDataLayer(dataLayerName);\r\n        const { wrappedGtag, gtagCore } = wrapOrCreateGtag(initializationPromisesMap, dynamicConfigPromisesList, measurementIdToAppId, dataLayerName, gtagName);\r\n        wrappedGtagFunction = wrappedGtag;\r\n        gtagCoreFunction = gtagCore;\r\n        globalInitDone = true;\r\n    }\r\n    // Async but non-blocking.\r\n    // This map reflects the completion state of all promises for each appId.\r\n    initializationPromisesMap[appId] = _initializeAnalytics(app, dynamicConfigPromisesList, measurementIdToAppId, installations, gtagCoreFunction, dataLayerName, options);\r\n    const analyticsInstance = new AnalyticsService(app);\r\n    return analyticsInstance;\r\n}\n\n/* eslint-disable @typescript-eslint/no-explicit-any */\r\n/**\r\n * Returns an {@link Analytics} instance for the given app.\r\n *\r\n * @public\r\n *\r\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\r\n */\r\nfunction getAnalytics(app = getApp()) {\r\n    app = getModularInstance(app);\r\n    // Dependencies\r\n    const analyticsProvider = _getProvider(app, ANALYTICS_TYPE);\r\n    if (analyticsProvider.isInitialized()) {\r\n        return analyticsProvider.getImmediate();\r\n    }\r\n    return initializeAnalytics(app);\r\n}\r\n/**\r\n * Returns an {@link Analytics} instance for the given app.\r\n *\r\n * @public\r\n *\r\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\r\n */\r\nfunction initializeAnalytics(app, options = {}) {\r\n    // Dependencies\r\n    const analyticsProvider = _getProvider(app, ANALYTICS_TYPE);\r\n    if (analyticsProvider.isInitialized()) {\r\n        const existingInstance = analyticsProvider.getImmediate();\r\n        if (deepEqual(options, analyticsProvider.getOptions())) {\r\n            return existingInstance;\r\n        }\r\n        else {\r\n            throw ERROR_FACTORY.create(\"already-initialized\" /* AnalyticsError.ALREADY_INITIALIZED */);\r\n        }\r\n    }\r\n    const analyticsInstance = analyticsProvider.initialize({ options });\r\n    return analyticsInstance;\r\n}\r\n/**\r\n * This is a public static method provided to users that wraps four different checks:\r\n *\r\n * 1. Check if it's not a browser extension environment.\r\n * 2. Check if cookies are enabled in current browser.\r\n * 3. Check if IndexedDB is supported by the browser environment.\r\n * 4. Check if the current browser context is valid for using `IndexedDB.open()`.\r\n *\r\n * @public\r\n *\r\n */\r\nasync function isSupported() {\r\n    if (isBrowserExtension()) {\r\n        return false;\r\n    }\r\n    if (!areCookiesEnabled()) {\r\n        return false;\r\n    }\r\n    if (!isIndexedDBAvailable()) {\r\n        return false;\r\n    }\r\n    try {\r\n        const isDBOpenable = await validateIndexedDBOpenable();\r\n        return isDBOpenable;\r\n    }\r\n    catch (error) {\r\n        return false;\r\n    }\r\n}\r\n/**\r\n * Use gtag `config` command to set `screen_name`.\r\n *\r\n * @public\r\n *\r\n * @deprecated Use {@link logEvent} with `eventName` as 'screen_view' and add relevant `eventParams`.\r\n * See {@link https://firebase.google.com/docs/analytics/screenviews | Track Screenviews}.\r\n *\r\n * @param analyticsInstance - The {@link Analytics} instance.\r\n * @param screenName - Screen name to set.\r\n */\r\nfunction setCurrentScreen(analyticsInstance, screenName, options) {\r\n    analyticsInstance = getModularInstance(analyticsInstance);\r\n    setCurrentScreen$1(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId], screenName, options).catch(e => logger.error(e));\r\n}\r\n/**\r\n * Retrieves a unique Google Analytics identifier for the web client.\r\n * See {@link https://developers.google.com/analytics/devguides/collection/ga4/reference/config#client_id | client_id}.\r\n *\r\n * @public\r\n *\r\n * @param app - The {@link @firebase/app#FirebaseApp} to use.\r\n */\r\nasync function getGoogleAnalyticsClientId(analyticsInstance) {\r\n    analyticsInstance = getModularInstance(analyticsInstance);\r\n    return internalGetGoogleAnalyticsClientId(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId]);\r\n}\r\n/**\r\n * Use gtag `config` command to set `user_id`.\r\n *\r\n * @public\r\n *\r\n * @param analyticsInstance - The {@link Analytics} instance.\r\n * @param id - User ID to set.\r\n */\r\nfunction setUserId(analyticsInstance, id, options) {\r\n    analyticsInstance = getModularInstance(analyticsInstance);\r\n    setUserId$1(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId], id, options).catch(e => logger.error(e));\r\n}\r\n/**\r\n * Use gtag `config` command to set all params specified.\r\n *\r\n * @public\r\n */\r\nfunction setUserProperties(analyticsInstance, properties, options) {\r\n    analyticsInstance = getModularInstance(analyticsInstance);\r\n    setUserProperties$1(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId], properties, options).catch(e => logger.error(e));\r\n}\r\n/**\r\n * Sets whether Google Analytics collection is enabled for this app on this device.\r\n * Sets global `window['ga-disable-analyticsId'] = true;`\r\n *\r\n * @public\r\n *\r\n * @param analyticsInstance - The {@link Analytics} instance.\r\n * @param enabled - If true, enables collection, if false, disables it.\r\n */\r\nfunction setAnalyticsCollectionEnabled(analyticsInstance, enabled) {\r\n    analyticsInstance = getModularInstance(analyticsInstance);\r\n    setAnalyticsCollectionEnabled$1(initializationPromisesMap[analyticsInstance.app.options.appId], enabled).catch(e => logger.error(e));\r\n}\r\n/**\r\n * Adds data that will be set on every event logged from the SDK, including automatic ones.\r\n * With gtag's \"set\" command, the values passed persist on the current page and are passed with\r\n * all subsequent events.\r\n * @public\r\n * @param customParams - Any custom params the user may pass to gtag.js.\r\n */\r\nfunction setDefaultEventParameters(customParams) {\r\n    // Check if reference to existing gtag function on window object exists\r\n    if (wrappedGtagFunction) {\r\n        wrappedGtagFunction(\"set\" /* GtagCommand.SET */, customParams);\r\n    }\r\n    else {\r\n        _setDefaultEventParametersForInit(customParams);\r\n    }\r\n}\r\n/**\r\n * Sends a Google Analytics event with given `eventParams`. This method\r\n * automatically associates this logged event with this Firebase web\r\n * app instance on this device.\r\n * List of official event parameters can be found in the gtag.js\r\n * reference documentation:\r\n * {@link https://developers.google.com/gtagjs/reference/ga4-events\r\n * | the GA4 reference documentation}.\r\n *\r\n * @public\r\n */\r\nfunction logEvent(analyticsInstance, eventName, eventParams, options) {\r\n    analyticsInstance = getModularInstance(analyticsInstance);\r\n    logEvent$1(wrappedGtagFunction, initializationPromisesMap[analyticsInstance.app.options.appId], eventName, eventParams, options).catch(e => logger.error(e));\r\n}\r\n/**\r\n * Sets the applicable end user consent state for this web app across all gtag references once\r\n * Firebase Analytics is initialized.\r\n *\r\n * Use the {@link ConsentSettings} to specify individual consent type values. By default consent\r\n * types are set to \"granted\".\r\n * @public\r\n * @param consentSettings - Maps the applicable end user consent state for gtag.js.\r\n */\r\nfunction setConsent(consentSettings) {\r\n    // Check if reference to existing gtag function on window object exists\r\n    if (wrappedGtagFunction) {\r\n        wrappedGtagFunction(\"consent\" /* GtagCommand.CONSENT */, 'update', consentSettings);\r\n    }\r\n    else {\r\n        _setConsentDefaultForInit(consentSettings);\r\n    }\r\n}\n\nconst name = \"@firebase/analytics\";\nconst version = \"0.10.0\";\n\n/**\r\n * Firebase Analytics\r\n *\r\n * @packageDocumentation\r\n */\r\nfunction registerAnalytics() {\r\n    _registerComponent(new Component(ANALYTICS_TYPE, (container, { options: analyticsOptions }) => {\r\n        // getImmediate for FirebaseApp will always succeed\r\n        const app = container.getProvider('app').getImmediate();\r\n        const installations = container\r\n            .getProvider('installations-internal')\r\n            .getImmediate();\r\n        return factory(app, installations, analyticsOptions);\r\n    }, \"PUBLIC\" /* ComponentType.PUBLIC */));\r\n    _registerComponent(new Component('analytics-internal', internalFactory, \"PRIVATE\" /* ComponentType.PRIVATE */));\r\n    registerVersion(name, version);\r\n    // BUILD_TARGET will be replaced by values like esm5, esm2017, cjs5, etc during the compilation\r\n    registerVersion(name, version, 'esm2017');\r\n    function internalFactory(container) {\r\n        try {\r\n            const analytics = container.getProvider(ANALYTICS_TYPE).getImmediate();\r\n            return {\r\n                logEvent: (eventName, eventParams, options) => logEvent(analytics, eventName, eventParams, options)\r\n            };\r\n        }\r\n        catch (e) {\r\n            throw ERROR_FACTORY.create(\"interop-component-reg-failed\" /* AnalyticsError.INTEROP_COMPONENT_REG_FAILED */, {\r\n                reason: e\r\n            });\r\n        }\r\n    }\r\n}\r\nregisterAnalytics();\n\nexport { getAnalytics, getGoogleAnalyticsClientId, initializeAnalytics, isSupported, logEvent, setAnalyticsCollectionEnabled, setConsent, setCurrentScreen, setDefaultEventParameters, setUserId, setUserProperties, settings };\n"], "mappings": ";AAAA,SAASA,YAAY,EAAEC,MAAM,EAAEC,kBAAkB,EAAEC,eAAe,QAAQ,eAAe;AACzF,SAASC,MAAM,QAAQ,kBAAkB;AACzC,SAASC,YAAY,EAAEC,sBAAsB,EAAEC,aAAa,EAAEC,oBAAoB,EAAEC,yBAAyB,EAAEC,kBAAkB,EAAEC,iBAAiB,EAAEC,kBAAkB,EAAEC,SAAS,QAAQ,gBAAgB;AAC3M,SAASC,SAAS,QAAQ,qBAAqB;AAC/C,OAAO,yBAAyB;;AAEhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,cAAc,GAAG,WAAW;AAClC;AACA,MAAMC,UAAU,GAAG,aAAa;AAChC,MAAMC,UAAU,GAAG,QAAQ;AAC3B,MAAMC,oBAAoB,GAAG,EAAE,GAAG,IAAI;AACtC,MAAMC,kBAAkB,GAAG,4EAA4E;AACvG,MAAMC,QAAQ,GAAG,0CAA0C;;AAE3D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,MAAM,GAAG,IAAIjB,MAAM,CAAC,qBAAqB,CAAC;;AAEhD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkB,MAAM,GAAG;EACX,CAAC,gBAAgB,CAAC,sCAAsC,qDAAqD,GACzG,mBAAmB,GACnB,qEAAqE;EACzE,CAAC,qBAAqB,CAAC,2CAA2C,iFAAiF,GAC/I,gFAAgF,GAChF,8DAA8D,GAC9D,wDAAwD;EAC5D,CAAC,8BAA8B,CAAC,oDAAoD,kDAAkD,GAClI,sEAAsE,GACtE,4BAA4B;EAChC,CAAC,8BAA8B,CAAC,oDAAoD,uEAAuE;EAC3J,CAAC,2BAA2B,CAAC,iDAAiD,2DAA2D,GACrI,8DAA8D,GAC9D,8EAA8E;EAClF,CAAC,uBAAuB,CAAC,6CAA6C,2DAA2D,GAC7H,8DAA8D,GAC9D,8EAA8E;EAClF,CAAC,gBAAgB,CAAC,sCAAsC,2EAA2E,GAC/H,+FAA+F;EACnG,CAAC,qBAAqB,CAAC,2CAA2C,iEAAiE;EACnI,CAAC,YAAY,CAAC,kCAAkC,qGAAqG,GACjJ,0BAA0B;EAC9B,CAAC,WAAW,CAAC,iCAAiC,oGAAoG,GAC9I,yBAAyB;EAC7B,CAAC,cAAc,CAAC,oCAAoC,iCAAiC;EACrF,CAAC,uBAAuB,CAAC,6CAA6C;AAC1E,CAAC;AACD,MAAMC,aAAa,GAAG,IAAIlB,YAAY,CAAC,WAAW,EAAE,WAAW,EAAEiB,MAAM,CAAC;;AAExE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,+BAA+BA,CAACC,GAAG,EAAE;EAC1C,IAAI,CAACA,GAAG,CAACC,UAAU,CAACN,QAAQ,CAAC,EAAE;IAC3B,MAAMO,GAAG,GAAGJ,aAAa,CAACK,MAAM,CAAC,uBAAuB,CAAC,4CAA4C;MACjGC,OAAO,EAAEJ;IACb,CAAC,CAAC;IACFJ,MAAM,CAACS,IAAI,CAACH,GAAG,CAACI,OAAO,CAAC;IACxB,OAAO,EAAE;EACb;EACA,OAAON,GAAG;AACd;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASO,iBAAiBA,CAACC,QAAQ,EAAE;EACjC,OAAOC,OAAO,CAACC,GAAG,CAACF,QAAQ,CAACG,GAAG,CAACC,OAAO,IAAIA,OAAO,CAACC,KAAK,CAACC,CAAC,IAAIA,CAAC,CAAC,CAAC,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,wBAAwBA,CAACC,UAAU,EAAEC,aAAa,EAAE;EACzD;EACA;EACA,IAAIC,kBAAkB;EACtB,IAAIC,MAAM,CAACC,YAAY,EAAE;IACrBF,kBAAkB,GAAGC,MAAM,CAACC,YAAY,CAACC,YAAY,CAACL,UAAU,EAAEC,aAAa,CAAC;EACpF;EACA,OAAOC,kBAAkB;AAC7B;AACA;AACA;AACA;AACA;AACA,SAASI,eAAeA,CAACC,aAAa,EAAEC,aAAa,EAAE;EACnD,MAAMN,kBAAkB,GAAGH,wBAAwB,CAAC,wBAAwB,EAAE;IAC1EU,eAAe,EAAE1B;EACrB,CAAC,CAAC;EACF,MAAM2B,MAAM,GAAGC,QAAQ,CAACC,aAAa,CAAC,QAAQ,CAAC;EAC/C;EACA;EACA,MAAMC,aAAa,GAAG,GAAGlC,QAAQ,MAAM4B,aAAa,OAAOC,aAAa,EAAE;EAC1EE,MAAM,CAACI,GAAG,GAAGZ,kBAAkB,GACzBA,kBAAkB,KAAK,IAAI,IAAIA,kBAAkB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,kBAAkB,CAACO,eAAe,CAACI,aAAa,CAAC,GACzHA,aAAa;EACnBH,MAAM,CAACK,KAAK,GAAG,IAAI;EACnBJ,QAAQ,CAACK,IAAI,CAACC,WAAW,CAACP,MAAM,CAAC;AACrC;AACA;AACA;AACA;AACA;AACA,SAASQ,oBAAoBA,CAACX,aAAa,EAAE;EACzC;EACA,IAAIY,SAAS,GAAG,EAAE;EAClB,IAAIC,KAAK,CAACC,OAAO,CAAClB,MAAM,CAACI,aAAa,CAAC,CAAC,EAAE;IACtCY,SAAS,GAAGhB,MAAM,CAACI,aAAa,CAAC;EACrC,CAAC,MACI;IACDJ,MAAM,CAACI,aAAa,CAAC,GAAGY,SAAS;EACrC;EACA,OAAOA,SAAS;AACpB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AATA,SAUeG,YAAYA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,aAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAyB3B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAF,cAAA;EAAAA,aAAA,GAAAG,iBAAA,CAzBA,WAA4BC,QAAQ,EAAEC,yBAAyB,EAAEC,yBAAyB,EAAEC,oBAAoB,EAAE5B,aAAa,EAAE6B,UAAU,EAAE;IACzI;IACA;IACA,MAAMC,kBAAkB,GAAGF,oBAAoB,CAAC5B,aAAa,CAAC;IAC9D,IAAI;MACA,IAAI8B,kBAAkB,EAAE;QACpB,MAAMJ,yBAAyB,CAACI,kBAAkB,CAAC;MACvD,CAAC,MACI;QACD;QACA;QACA;QACA;QACA,MAAMC,oBAAoB,SAAShD,iBAAiB,CAAC4C,yBAAyB,CAAC;QAC/E,MAAMK,WAAW,GAAGD,oBAAoB,CAACE,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAClC,aAAa,KAAKA,aAAa,CAAC;QAC/F,IAAIgC,WAAW,EAAE;UACb,MAAMN,yBAAyB,CAACM,WAAW,CAACG,KAAK,CAAC;QACtD;MACJ;IACJ,CAAC,CACD,OAAO7C,CAAC,EAAE;MACNlB,MAAM,CAACgE,KAAK,CAAC9C,CAAC,CAAC;IACnB;IACAmC,QAAQ,CAAC,QAAQ,CAAC,0BAA0BzB,aAAa,EAAE6B,UAAU,CAAC;EAC1E,CAAC;EAAA,OAAAR,aAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAUcc,WAAWA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,YAAA,CAAArB,KAAA,OAAAC,SAAA;AAAA;AA8C1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAAoB,aAAA;EAAAA,YAAA,GAAAnB,iBAAA,CA9CA,WAA2BC,QAAQ,EAAEC,yBAAyB,EAAEC,yBAAyB,EAAE3B,aAAa,EAAE6B,UAAU,EAAE;IAClH,IAAI;MACA,IAAIe,+BAA+B,GAAG,EAAE;MACxC;MACA;MACA,IAAIf,UAAU,IAAIA,UAAU,CAAC,SAAS,CAAC,EAAE;QACrC,IAAIgB,YAAY,GAAGhB,UAAU,CAAC,SAAS,CAAC;QACxC;QACA,IAAI,CAACjB,KAAK,CAACC,OAAO,CAACgC,YAAY,CAAC,EAAE;UAC9BA,YAAY,GAAG,CAACA,YAAY,CAAC;QACjC;QACA;QACA;QACA,MAAMd,oBAAoB,SAAShD,iBAAiB,CAAC4C,yBAAyB,CAAC;QAC/E,KAAK,MAAMmB,QAAQ,IAAID,YAAY,EAAE;UACjC;UACA,MAAMb,WAAW,GAAGD,oBAAoB,CAACE,IAAI,CAACC,MAAM,IAAIA,MAAM,CAAClC,aAAa,KAAK8C,QAAQ,CAAC;UAC1F,MAAMC,qBAAqB,GAAGf,WAAW,IAAIN,yBAAyB,CAACM,WAAW,CAACG,KAAK,CAAC;UACzF,IAAIY,qBAAqB,EAAE;YACvBH,+BAA+B,CAACI,IAAI,CAACD,qBAAqB,CAAC;UAC/D,CAAC,MACI;YACD;YACA;YACA;YACAH,+BAA+B,GAAG,EAAE;YACpC;UACJ;QACJ;MACJ;MACA;MACA;MACA;MACA,IAAIA,+BAA+B,CAACK,MAAM,KAAK,CAAC,EAAE;QAC9CL,+BAA+B,GAAGM,MAAM,CAACC,MAAM,CAACzB,yBAAyB,CAAC;MAC9E;MACA;MACA;MACA,MAAMzC,OAAO,CAACC,GAAG,CAAC0D,+BAA+B,CAAC;MAClD;MACAnB,QAAQ,CAAC,OAAO,CAAC,yBAAyBzB,aAAa,EAAE6B,UAAU,IAAI,CAAC,CAAC,CAAC;IAC9E,CAAC,CACD,OAAOvC,CAAC,EAAE;MACNlB,MAAM,CAACgE,KAAK,CAAC9C,CAAC,CAAC;IACnB;EACJ,CAAC;EAAA,OAAAqD,YAAA,CAAArB,KAAA,OAAAC,SAAA;AAAA;AAUD,SAAS6B,QAAQA,CAAC3B,QAAQ;AAC1B;AACA;AACA;AACA;AACAC,yBAAyB;AACzB;AACA;AACA;AACA;AACAC,yBAAyB;AACzB;AACA;AACA;AACA;AACA;AACAC,oBAAoB,EAAE;EAClB;AACJ;AACA;AACA;AACA;AACA;EALI,SAMeyB,WAAWA,CAAAC,IAAA;IAAA,OAAAC,YAAA,CAAAjC,KAAA,OAAAC,SAAA;EAAA;EAAA,SAAAgC,aAAA;IAAAA,YAAA,GAAA/B,iBAAA,CAA1B,WAA2BgC,OAAO,EAAE,GAAGC,IAAI,EAAE;MACzC,IAAI;QACA;QACA,IAAID,OAAO,KAAK,OAAO,CAAC,yBAAyB;UAC7C,MAAM,CAACxD,aAAa,EAAE6B,UAAU,CAAC,GAAG4B,IAAI;UACxC;UACA,MAAMpB,WAAW,CAACZ,QAAQ,EAAEC,yBAAyB,EAAEC,yBAAyB,EAAE3B,aAAa,EAAE6B,UAAU,CAAC;QAChH,CAAC,MACI,IAAI2B,OAAO,KAAK,QAAQ,CAAC,0BAA0B;UACpD,MAAM,CAACxD,aAAa,EAAE6B,UAAU,CAAC,GAAG4B,IAAI;UACxC;UACA,MAAM3C,YAAY,CAACW,QAAQ,EAAEC,yBAAyB,EAAEC,yBAAyB,EAAEC,oBAAoB,EAAE5B,aAAa,EAAE6B,UAAU,CAAC;QACvI,CAAC,MACI,IAAI2B,OAAO,KAAK,SAAS,CAAC,2BAA2B;UACtD,MAAM,CAAC3B,UAAU,CAAC,GAAG4B,IAAI;UACzBhC,QAAQ,CAAC,SAAS,CAAC,2BAA2B,QAAQ,EAAEI,UAAU,CAAC;QACvE,CAAC,MACI,IAAI2B,OAAO,KAAK,KAAK,CAAC,uBAAuB;UAC9C,MAAM,CAACxD,aAAa,EAAE0D,SAAS,EAAEC,QAAQ,CAAC,GAAGF,IAAI;UACjDhC,QAAQ,CAAC,KAAK,CAAC,uBAAuBzB,aAAa,EAAE0D,SAAS,EAAEC,QAAQ,CAAC;QAC7E,CAAC,MACI,IAAIH,OAAO,KAAK,KAAK,CAAC,uBAAuB;UAC9C,MAAM,CAACI,YAAY,CAAC,GAAGH,IAAI;UAC3B;UACAhC,QAAQ,CAAC,KAAK,CAAC,uBAAuBmC,YAAY,CAAC;QACvD,CAAC,MACI;UACDnC,QAAQ,CAAC+B,OAAO,EAAE,GAAGC,IAAI,CAAC;QAC9B;MACJ,CAAC,CACD,OAAOnE,CAAC,EAAE;QACNlB,MAAM,CAACgE,KAAK,CAAC9C,CAAC,CAAC;MACnB;IACJ,CAAC;IAAA,OAAAiE,YAAA,CAAAjC,KAAA,OAAAC,SAAA;EAAA;EACD,OAAO8B,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASQ,gBAAgBA,CAACnC,yBAAyB,EAAEC,yBAAyB,EAAEC,oBAAoB,EAAE7B,aAAa,EAAE+D,gBAAgB,EAAE;EACnI;EACA,IAAIrC,QAAQ,GAAG,SAAAA,CAAU,GAAGsC,KAAK,EAAE;IAC/B;IACApE,MAAM,CAACI,aAAa,CAAC,CAACiD,IAAI,CAACzB,SAAS,CAAC;EACzC,CAAC;EACD;EACA,IAAI5B,MAAM,CAACmE,gBAAgB,CAAC,IACxB,OAAOnE,MAAM,CAACmE,gBAAgB,CAAC,KAAK,UAAU,EAAE;IAChD;IACArC,QAAQ,GAAG9B,MAAM,CAACmE,gBAAgB,CAAC;EACvC;EACAnE,MAAM,CAACmE,gBAAgB,CAAC,GAAGV,QAAQ,CAAC3B,QAAQ,EAAEC,yBAAyB,EAAEC,yBAAyB,EAAEC,oBAAoB,CAAC;EACzH,OAAO;IACHH,QAAQ;IACRuC,WAAW,EAAErE,MAAM,CAACmE,gBAAgB;EACxC,CAAC;AACL;AACA;AACA;AACA;AACA;AACA,SAASG,oBAAoBA,CAAClE,aAAa,EAAE;EACzC,MAAMmE,UAAU,GAAGvE,MAAM,CAACQ,QAAQ,CAACgE,oBAAoB,CAAC,QAAQ,CAAC;EACjE,KAAK,MAAMC,GAAG,IAAIlB,MAAM,CAACC,MAAM,CAACe,UAAU,CAAC,EAAE;IACzC,IAAIE,GAAG,CAAC9D,GAAG,IACP8D,GAAG,CAAC9D,GAAG,CAAC+D,QAAQ,CAAClG,QAAQ,CAAC,IAC1BiG,GAAG,CAAC9D,GAAG,CAAC+D,QAAQ,CAACtE,aAAa,CAAC,EAAE;MACjC,OAAOqE,GAAG;IACd;EACJ;EACA,OAAO,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,iBAAiB,GAAG,EAAE;AAC5B;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG,IAAI;AACjC;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZC,WAAWA,CAACC,gBAAgB,GAAG,CAAC,CAAC,EAAEC,cAAc,GAAGJ,oBAAoB,EAAE;IACtE,IAAI,CAACG,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACC,cAAc,GAAGA,cAAc;EACxC;EACAC,mBAAmBA,CAACzC,KAAK,EAAE;IACvB,OAAO,IAAI,CAACuC,gBAAgB,CAACvC,KAAK,CAAC;EACvC;EACA0C,mBAAmBA,CAAC1C,KAAK,EAAE2C,QAAQ,EAAE;IACjC,IAAI,CAACJ,gBAAgB,CAACvC,KAAK,CAAC,GAAG2C,QAAQ;EAC3C;EACAC,sBAAsBA,CAAC5C,KAAK,EAAE;IAC1B,OAAO,IAAI,CAACuC,gBAAgB,CAACvC,KAAK,CAAC;EACvC;AACJ;AACA,MAAM6C,gBAAgB,GAAG,IAAIR,SAAS,CAAC,CAAC;AACxC;AACA;AACA;AACA;AACA,SAASS,UAAUA,CAACC,MAAM,EAAE;EACxB,OAAO,IAAIC,OAAO,CAAC;IACfC,MAAM,EAAE,kBAAkB;IAC1B,gBAAgB,EAAEF;EACtB,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AAHA,SAIeG,kBAAkBA,CAAAC,IAAA;EAAA,OAAAC,mBAAA,CAAAjE,KAAA,OAAAC,SAAA;AAAA;AA0BjC;AACA;AACA;AACA;AAHA,SAAAgE,oBAAA;EAAAA,mBAAA,GAAA/D,iBAAA,CA1BA,WAAkCgE,SAAS,EAAE;IACzC,IAAIC,EAAE;IACN,MAAM;MAAEtD,KAAK;MAAE+C;IAAO,CAAC,GAAGM,SAAS;IACnC,MAAME,OAAO,GAAG;MACZC,MAAM,EAAE,KAAK;MACbC,OAAO,EAAEX,UAAU,CAACC,MAAM;IAC9B,CAAC;IACD,MAAMW,MAAM,GAAG3H,kBAAkB,CAAC4H,OAAO,CAAC,UAAU,EAAE3D,KAAK,CAAC;IAC5D,MAAM4D,QAAQ,SAASC,KAAK,CAACH,MAAM,EAAEH,OAAO,CAAC;IAC7C,IAAIK,QAAQ,CAACE,MAAM,KAAK,GAAG,IAAIF,QAAQ,CAACE,MAAM,KAAK,GAAG,EAAE;MACpD,IAAIC,YAAY,GAAG,EAAE;MACrB,IAAI;QACA;QACA,MAAMC,YAAY,SAAUJ,QAAQ,CAACK,IAAI,CAAC,CAAE;QAC5C,IAAI,CAACX,EAAE,GAAGU,YAAY,CAAC/D,KAAK,MAAM,IAAI,IAAIqD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC3G,OAAO,EAAE;UAC3EoH,YAAY,GAAGC,YAAY,CAAC/D,KAAK,CAACtD,OAAO;QAC7C;MACJ,CAAC,CACD,OAAOuH,QAAQ,EAAE,CAAE;MACnB,MAAM/H,aAAa,CAACK,MAAM,CAAC,qBAAqB,CAAC,0CAA0C;QACvF2H,UAAU,EAAEP,QAAQ,CAACE,MAAM;QAC3BM,eAAe,EAAEL;MACrB,CAAC,CAAC;IACN;IACA,OAAOH,QAAQ,CAACK,IAAI,CAAC,CAAC;EAC1B,CAAC;EAAA,OAAAb,mBAAA,CAAAjE,KAAA,OAAAC,SAAA;AAAA;AAAA,SAKciF,2BAA2BA,CAAAC,IAAA;EAAA,OAAAC,4BAAA,CAAApF,KAAA,OAAAC,SAAA;AAAA;AA2B1C;AACA;AACA;AACA;AACA;AACA;AALA,SAAAmF,6BAAA;EAAAA,4BAAA,GAAAlF,iBAAA,CA3BA,WAA2CmF,GAAG;EAC9C;EACAC,SAAS,GAAG5B,gBAAgB,EAAE6B,aAAa,EAAE;IACzC,MAAM;MAAE1E,KAAK;MAAE+C,MAAM;MAAElF;IAAc,CAAC,GAAG2G,GAAG,CAACG,OAAO;IACpD,IAAI,CAAC3E,KAAK,EAAE;MACR,MAAM7D,aAAa,CAACK,MAAM,CAAC,WAAW,CAAC,8BAA8B,CAAC;IAC1E;IACA,IAAI,CAACuG,MAAM,EAAE;MACT,IAAIlF,aAAa,EAAE;QACf,OAAO;UACHA,aAAa;UACbmC;QACJ,CAAC;MACL;MACA,MAAM7D,aAAa,CAACK,MAAM,CAAC,YAAY,CAAC,+BAA+B,CAAC;IAC5E;IACA,MAAM+F,gBAAgB,GAAGkC,SAAS,CAAChC,mBAAmB,CAACzC,KAAK,CAAC,IAAI;MAC7D4E,YAAY,EAAE,CAAC;MACfC,qBAAqB,EAAEC,IAAI,CAACC,GAAG,CAAC;IACpC,CAAC;IACD,MAAMC,MAAM,GAAG,IAAIC,oBAAoB,CAAC,CAAC;IACzCC,UAAU,cAAA7F,iBAAA,CAAC,aAAY;MACnB;MACA2F,MAAM,CAACG,KAAK,CAAC,CAAC;IAClB,CAAC,GAAET,aAAa,KAAKU,SAAS,GAAGV,aAAa,GAAG5I,oBAAoB,CAAC;IACtE,OAAOuJ,kCAAkC,CAAC;MAAErF,KAAK;MAAE+C,MAAM;MAAElF;IAAc,CAAC,EAAE0E,gBAAgB,EAAEyC,MAAM,EAAEP,SAAS,CAAC;EACpH,CAAC;EAAA,OAAAF,4BAAA,CAAApF,KAAA,OAAAC,SAAA;AAAA;AAAA,SAOciG,kCAAkCA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,mCAAA,CAAAtG,KAAA,OAAAC,SAAA;AAAA;AAqDjD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAXA,SAAAqG,oCAAA;EAAAA,mCAAA,GAAApG,iBAAA,CArDA,WAAkDgE,SAAS,EAAE;IAAEwB,qBAAqB;IAAED;EAAa,CAAC,EAAEI,MAAM,EAAEP,SAAS,GAAG5B,gBAAgB,CAAC;EAAA,EACzI;IACE,IAAIS,EAAE;IACN,MAAM;MAAEtD,KAAK;MAAEnC;IAAc,CAAC,GAAGwF,SAAS;IAC1C;IACA;IACA;IACA,IAAI;MACA,MAAMqC,mBAAmB,CAACV,MAAM,EAAEH,qBAAqB,CAAC;IAC5D,CAAC,CACD,OAAO1H,CAAC,EAAE;MACN,IAAIU,aAAa,EAAE;QACf5B,MAAM,CAACS,IAAI,CAAC,wEAAwE,GAChF,uCAAuCmB,aAAa,EAAE,GACtD,yEAAyEV,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAACR,OAAO,GAAG,CAAC;QAChI,OAAO;UAAEqD,KAAK;UAAEnC;QAAc,CAAC;MACnC;MACA,MAAMV,CAAC;IACX;IACA,IAAI;MACA,MAAMyG,QAAQ,SAASV,kBAAkB,CAACG,SAAS,CAAC;MACpD;MACAoB,SAAS,CAAC7B,sBAAsB,CAAC5C,KAAK,CAAC;MACvC,OAAO4D,QAAQ;IACnB,CAAC,CACD,OAAOzG,CAAC,EAAE;MACN,MAAM8C,KAAK,GAAG9C,CAAC;MACf,IAAI,CAACwI,gBAAgB,CAAC1F,KAAK,CAAC,EAAE;QAC1BwE,SAAS,CAAC7B,sBAAsB,CAAC5C,KAAK,CAAC;QACvC,IAAInC,aAAa,EAAE;UACf5B,MAAM,CAACS,IAAI,CAAC,qEAAqE,GAC7E,uCAAuCmB,aAAa,EAAE,GACtD,yEAAyEoC,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAACtD,OAAO,GAAG,CAAC;UAC5I,OAAO;YAAEqD,KAAK;YAAEnC;UAAc,CAAC;QACnC,CAAC,MACI;UACD,MAAMV,CAAC;QACX;MACJ;MACA,MAAMyI,aAAa,GAAGC,MAAM,CAAC,CAACvC,EAAE,GAAGrD,KAAK,KAAK,IAAI,IAAIA,KAAK,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,KAAK,CAAC6F,UAAU,MAAM,IAAI,IAAIxC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACa,UAAU,CAAC,KAAK,GAAG,GACxJjJ,sBAAsB,CAAC0J,YAAY,EAAEH,SAAS,CAACjC,cAAc,EAAEL,iBAAiB,CAAC,GACjFjH,sBAAsB,CAAC0J,YAAY,EAAEH,SAAS,CAACjC,cAAc,CAAC;MACpE;MACA,MAAMD,gBAAgB,GAAG;QACrBsC,qBAAqB,EAAEC,IAAI,CAACC,GAAG,CAAC,CAAC,GAAGa,aAAa;QACjDhB,YAAY,EAAEA,YAAY,GAAG;MACjC,CAAC;MACD;MACAH,SAAS,CAAC/B,mBAAmB,CAAC1C,KAAK,EAAEuC,gBAAgB,CAAC;MACtDtG,MAAM,CAAC8J,KAAK,CAAC,iCAAiCH,aAAa,SAAS,CAAC;MACrE,OAAOP,kCAAkC,CAAChC,SAAS,EAAEd,gBAAgB,EAAEyC,MAAM,EAAEP,SAAS,CAAC;IAC7F;EACJ,CAAC;EAAA,OAAAgB,mCAAA,CAAAtG,KAAA,OAAAC,SAAA;AAAA;AAaD,SAASsG,mBAAmBA,CAACV,MAAM,EAAEH,qBAAqB,EAAE;EACxD,OAAO,IAAI/H,OAAO,CAAC,CAACkJ,OAAO,EAAEC,MAAM,KAAK;IACpC;IACA,MAAML,aAAa,GAAGM,IAAI,CAACC,GAAG,CAACtB,qBAAqB,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,CAAC,CAAC;IACrE,MAAMqB,OAAO,GAAGlB,UAAU,CAACc,OAAO,EAAEJ,aAAa,CAAC;IAClD;IACAZ,MAAM,CAACqB,gBAAgB,CAAC,MAAM;MAC1BC,YAAY,CAACF,OAAO,CAAC;MACrB;MACAH,MAAM,CAAC9J,aAAa,CAACK,MAAM,CAAC,gBAAgB,CAAC,qCAAqC;QAC9EqI;MACJ,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA,SAASc,gBAAgBA,CAACxI,CAAC,EAAE;EACzB,IAAI,EAAEA,CAAC,YAAYhC,aAAa,CAAC,IAAI,CAACgC,CAAC,CAAC2I,UAAU,EAAE;IAChD,OAAO,KAAK;EAChB;EACA;EACA,MAAM3B,UAAU,GAAG0B,MAAM,CAAC1I,CAAC,CAAC2I,UAAU,CAAC,YAAY,CAAC,CAAC;EACrD,OAAQ3B,UAAU,KAAK,GAAG,IACtBA,UAAU,KAAK,GAAG,IAClBA,UAAU,KAAK,GAAG,IAClBA,UAAU,KAAK,GAAG;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMc,oBAAoB,CAAC;EACvB3C,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiE,SAAS,GAAG,EAAE;EACvB;EACAF,gBAAgBA,CAACG,QAAQ,EAAE;IACvB,IAAI,CAACD,SAAS,CAAC1F,IAAI,CAAC2F,QAAQ,CAAC;EACjC;EACArB,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACoB,SAAS,CAACE,OAAO,CAACD,QAAQ,IAAIA,QAAQ,CAAC,CAAC,CAAC;EAClD;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,IAAIE,6BAA6B;AACjC;AACA;AACA;AACA;AACA;AACA;AACA;AANA,SAOeC,UAAUA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,UAAA,CAAA9H,KAAA,OAAAC,SAAA;AAAA;AAWzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AARA,SAAA6H,WAAA;EAAAA,UAAA,GAAA5H,iBAAA,CAXA,WAA0B6H,YAAY,EAAEtG,qBAAqB,EAAEuG,SAAS,EAAEC,WAAW,EAAEzC,OAAO,EAAE;IAC5F,IAAIA,OAAO,IAAIA,OAAO,CAAC0C,MAAM,EAAE;MAC3BH,YAAY,CAAC,OAAO,CAAC,yBAAyBC,SAAS,EAAEC,WAAW,CAAC;MACrE;IACJ,CAAC,MACI;MACD,MAAMvJ,aAAa,SAAS+C,qBAAqB;MACjD,MAAM0G,MAAM,GAAGvG,MAAM,CAACwG,MAAM,CAACxG,MAAM,CAACwG,MAAM,CAAC,CAAC,CAAC,EAAEH,WAAW,CAAC,EAAE;QAAE,SAAS,EAAEvJ;MAAc,CAAC,CAAC;MAC1FqJ,YAAY,CAAC,OAAO,CAAC,yBAAyBC,SAAS,EAAEG,MAAM,CAAC;IACpE;EACJ,CAAC;EAAA,OAAAL,UAAA,CAAA9H,KAAA,OAAAC,SAAA;AAAA;AAAA,SAUcoI,kBAAkBA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,kBAAA,CAAA1I,KAAA,OAAAC,SAAA;AAAA;AAajC;AACA;AACA;AACA;AACA;AACA;AALA,SAAAyI,mBAAA;EAAAA,kBAAA,GAAAxI,iBAAA,CAbA,WAAkC6H,YAAY,EAAEtG,qBAAqB,EAAEkH,UAAU,EAAEnD,OAAO,EAAE;IACxF,IAAIA,OAAO,IAAIA,OAAO,CAAC0C,MAAM,EAAE;MAC3BH,YAAY,CAAC,KAAK,CAAC,uBAAuB;QAAE,aAAa,EAAEY;MAAW,CAAC,CAAC;MACxE,OAAOhL,OAAO,CAACkJ,OAAO,CAAC,CAAC;IAC5B,CAAC,MACI;MACD,MAAMnI,aAAa,SAAS+C,qBAAqB;MACjDsG,YAAY,CAAC,QAAQ,CAAC,0BAA0BrJ,aAAa,EAAE;QAC3DkK,MAAM,EAAE,IAAI;QACZ,aAAa,EAAED;MACnB,CAAC,CAAC;IACN;EACJ,CAAC;EAAA,OAAAD,kBAAA,CAAA1I,KAAA,OAAAC,SAAA;AAAA;AAAA,SAOc4I,WAAWA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,WAAA,CAAAlJ,KAAA,OAAAC,SAAA;AAAA;AAa1B;AACA;AACA;AACA;AACA;AACA;AALA,SAAAiJ,YAAA;EAAAA,WAAA,GAAAhJ,iBAAA,CAbA,WAA2B6H,YAAY,EAAEtG,qBAAqB,EAAE0H,EAAE,EAAE3D,OAAO,EAAE;IACzE,IAAIA,OAAO,IAAIA,OAAO,CAAC0C,MAAM,EAAE;MAC3BH,YAAY,CAAC,KAAK,CAAC,uBAAuB;QAAE,SAAS,EAAEoB;MAAG,CAAC,CAAC;MAC5D,OAAOxL,OAAO,CAACkJ,OAAO,CAAC,CAAC;IAC5B,CAAC,MACI;MACD,MAAMnI,aAAa,SAAS+C,qBAAqB;MACjDsG,YAAY,CAAC,QAAQ,CAAC,0BAA0BrJ,aAAa,EAAE;QAC3DkK,MAAM,EAAE,IAAI;QACZ,SAAS,EAAEO;MACf,CAAC,CAAC;IACN;EACJ,CAAC;EAAA,OAAAD,WAAA,CAAAlJ,KAAA,OAAAC,SAAA;AAAA;AAAA,SAOcmJ,mBAAmBA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,mBAAA,CAAAzJ,KAAA,OAAAC,SAAA;AAAA;AAkBlC;AACA;AACA;AACA;AACA;AACA;AALA,SAAAwJ,oBAAA;EAAAA,mBAAA,GAAAvJ,iBAAA,CAlBA,WAAmC6H,YAAY,EAAEtG,qBAAqB,EAAEiI,UAAU,EAAElE,OAAO,EAAE;IACzF,IAAIA,OAAO,IAAIA,OAAO,CAAC0C,MAAM,EAAE;MAC3B,MAAMyB,cAAc,GAAG,CAAC,CAAC;MACzB,KAAK,MAAMC,GAAG,IAAIhI,MAAM,CAACiI,IAAI,CAACH,UAAU,CAAC,EAAE;QACvC;QACAC,cAAc,CAAC,mBAAmBC,GAAG,EAAE,CAAC,GAAGF,UAAU,CAACE,GAAG,CAAC;MAC9D;MACA7B,YAAY,CAAC,KAAK,CAAC,uBAAuB4B,cAAc,CAAC;MACzD,OAAOhM,OAAO,CAACkJ,OAAO,CAAC,CAAC;IAC5B,CAAC,MACI;MACD,MAAMnI,aAAa,SAAS+C,qBAAqB;MACjDsG,YAAY,CAAC,QAAQ,CAAC,0BAA0BrJ,aAAa,EAAE;QAC3DkK,MAAM,EAAE,IAAI;QACZ,iBAAiB,EAAEc;MACvB,CAAC,CAAC;IACN;EACJ,CAAC;EAAA,OAAAD,mBAAA,CAAAzJ,KAAA,OAAAC,SAAA;AAAA;AAAA,SAOc6J,kCAAkCA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,mCAAA,CAAAjK,KAAA,OAAAC,SAAA;AAAA;AAWjD;AACA;AACA;AACA;AACA;AAJA,SAAAgK,oCAAA;EAAAA,mCAAA,GAAA/J,iBAAA,CAXA,WAAkD6H,YAAY,EAAEtG,qBAAqB,EAAE;IACnF,MAAM/C,aAAa,SAAS+C,qBAAqB;IACjD,OAAO,IAAI9D,OAAO,CAAC,CAACkJ,OAAO,EAAEC,MAAM,KAAK;MACpCiB,YAAY,CAAC,KAAK,CAAC,uBAAuBrJ,aAAa,EAAE,WAAW,EAAGwL,QAAQ,IAAK;QAChF,IAAI,CAACA,QAAQ,EAAE;UACXpD,MAAM,CAAC9J,aAAa,CAACK,MAAM,CAAC,cAAc,CAAC,iCAAiC,CAAC,CAAC;QAClF;QACAwJ,OAAO,CAACqD,QAAQ,CAAC;MACrB,CAAC,CAAC;IACN,CAAC,CAAC;EACN,CAAC;EAAA,OAAAD,mCAAA,CAAAjK,KAAA,OAAAC,SAAA;AAAA;AAAA,SAMckK,+BAA+BA,CAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,+BAAA,CAAAtK,KAAA,OAAAC,SAAA;AAAA;AAI9C;AACA;AACA;AAFA,SAAAqK,gCAAA;EAAAA,+BAAA,GAAApK,iBAAA,CAJA,WAA+CuB,qBAAqB,EAAE8I,OAAO,EAAE;IAC3E,MAAM7L,aAAa,SAAS+C,qBAAqB;IACjDpD,MAAM,CAAC,cAAcK,aAAa,EAAE,CAAC,GAAG,CAAC6L,OAAO;EACpD,CAAC;EAAA,OAAAD,+BAAA,CAAAtK,KAAA,OAAAC,SAAA;AAAA;AAID,IAAIuK,6BAA6B;AACjC;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,yBAAyBA,CAACC,eAAe,EAAE;EAChDF,6BAA6B,GAAGE,eAAe;AACnD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,iCAAiCA,CAACrI,YAAY,EAAE;EACrDiF,6BAA6B,GAAGjF,YAAY;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAfA,SAgBesI,iBAAiBA,CAAA;EAAA,OAAAC,kBAAA,CAAA7K,KAAA,OAAAC,SAAA;AAAA;AAoBhC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAZA,SAAA4K,mBAAA;EAAAA,kBAAA,GAAA3K,iBAAA,CApBA,aAAmC;IAC/B,IAAI,CAACjE,oBAAoB,CAAC,CAAC,EAAE;MACzBa,MAAM,CAACS,IAAI,CAACP,aAAa,CAACK,MAAM,CAAC,uBAAuB,CAAC,4CAA4C;QACjGyN,SAAS,EAAE;MACf,CAAC,CAAC,CAACtN,OAAO,CAAC;MACX,OAAO,KAAK;IAChB,CAAC,MACI;MACD,IAAI;QACA,MAAMtB,yBAAyB,CAAC,CAAC;MACrC,CAAC,CACD,OAAO8B,CAAC,EAAE;QACNlB,MAAM,CAACS,IAAI,CAACP,aAAa,CAACK,MAAM,CAAC,uBAAuB,CAAC,4CAA4C;UACjGyN,SAAS,EAAE9M,CAAC,KAAK,IAAI,IAAIA,CAAC,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,CAAC,CAAC+M,QAAQ,CAAC;QAChE,CAAC,CAAC,CAACvN,OAAO,CAAC;QACX,OAAO,KAAK;MAChB;IACJ;IACA,OAAO,IAAI;EACf,CAAC;EAAA,OAAAqN,kBAAA,CAAA7K,KAAA,OAAAC,SAAA;AAAA;AAAA,SAcc+K,oBAAoBA,CAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA,EAAAC,IAAA;EAAA,OAAAC,qBAAA,CAAAxL,KAAA,OAAAC,SAAA;AAAA;AAoEnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAFA,SAAAuL,sBAAA;EAAAA,qBAAA,GAAAtL,iBAAA,CApFA,WAAoCmF,GAAG,EAAEhF,yBAAyB,EAAEC,oBAAoB,EAAEmL,aAAa,EAAEtL,QAAQ,EAAE1B,aAAa,EAAE+G,OAAO,EAAE;IACvI,IAAIrB,EAAE;IACN,MAAMuH,oBAAoB,GAAGxG,2BAA2B,CAACG,GAAG,CAAC;IAC7D;IACAqG,oBAAoB,CACfC,IAAI,CAAC/K,MAAM,IAAI;MAChBN,oBAAoB,CAACM,MAAM,CAAClC,aAAa,CAAC,GAAGkC,MAAM,CAACC,KAAK;MACzD,IAAIwE,GAAG,CAACG,OAAO,CAAC9G,aAAa,IACzBkC,MAAM,CAAClC,aAAa,KAAK2G,GAAG,CAACG,OAAO,CAAC9G,aAAa,EAAE;QACpD5B,MAAM,CAACS,IAAI,CAAC,oDAAoD8H,GAAG,CAACG,OAAO,CAAC9G,aAAa,GAAG,GACxF,+DAA+DkC,MAAM,CAAClC,aAAa,IAAI,GACvF,gFAAgF,GAChF,aAAa,GACb,+EAA+E,CAAC;MACxF;IACJ,CAAC,CAAC,CACGX,KAAK,CAACC,CAAC,IAAIlB,MAAM,CAACgE,KAAK,CAAC9C,CAAC,CAAC,CAAC;IAChC;IACAqC,yBAAyB,CAACqB,IAAI,CAACgK,oBAAoB,CAAC;IACpD,MAAME,UAAU,GAAGhB,iBAAiB,CAAC,CAAC,CAACe,IAAI,CAACE,UAAU,IAAI;MACtD,IAAIA,UAAU,EAAE;QACZ,OAAOJ,aAAa,CAACK,KAAK,CAAC,CAAC;MAChC,CAAC,MACI;QACD,OAAO7F,SAAS;MACpB;IACJ,CAAC,CAAC;IACF,MAAM,CAAC8F,aAAa,EAAEC,GAAG,CAAC,SAASrO,OAAO,CAACC,GAAG,CAAC,CAC3C8N,oBAAoB,EACpBE,UAAU,CACb,CAAC;IACF;IACA;IACA,IAAI,CAACjJ,oBAAoB,CAAClE,aAAa,CAAC,EAAE;MACtCD,eAAe,CAACC,aAAa,EAAEsN,aAAa,CAACrN,aAAa,CAAC;IAC/D;IACA;IACA,IAAI8L,6BAA6B,EAAE;MAC/BrK,QAAQ,CAAC,SAAS,CAAC,2BAA2B,SAAS,EAAEqK,6BAA6B,CAAC;MACvFC,yBAAyB,CAACxE,SAAS,CAAC;IACxC;IACA;IACA;IACA;IACA;IACA9F,QAAQ,CAAC,IAAI,EAAE,IAAIwF,IAAI,CAAC,CAAC,CAAC;IAC1B;IACA;IACA,MAAMsG,gBAAgB,GAAG,CAAC9H,EAAE,GAAGqB,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAAC5E,MAAM,MAAM,IAAI,IAAIuD,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,CAAC,CAAC;IACpI;IACA8H,gBAAgB,CAACvP,UAAU,CAAC,GAAG,UAAU;IACzCuP,gBAAgB,CAACrD,MAAM,GAAG,IAAI;IAC9B,IAAIoD,GAAG,IAAI,IAAI,EAAE;MACbC,gBAAgB,CAACxP,UAAU,CAAC,GAAGuP,GAAG;IACtC;IACA;IACA;IACA;IACA;IACA7L,QAAQ,CAAC,QAAQ,CAAC,0BAA0B4L,aAAa,CAACrN,aAAa,EAAEuN,gBAAgB,CAAC;IAC1F;IACA,IAAI1E,6BAA6B,EAAE;MAC/BpH,QAAQ,CAAC,KAAK,CAAC,uBAAuBoH,6BAA6B,CAAC;MACpEoD,iCAAiC,CAAC1E,SAAS,CAAC;IAChD;IACA,OAAO8F,aAAa,CAACrN,aAAa;EACtC,CAAC;EAAA,OAAA8M,qBAAA,CAAAxL,KAAA,OAAAC,SAAA;AAAA;AAqBD,MAAMiM,gBAAgB,CAAC;EACnB/I,WAAWA,CAACkC,GAAG,EAAE;IACb,IAAI,CAACA,GAAG,GAAGA,GAAG;EAClB;EACA8G,OAAOA,CAAA,EAAG;IACN,OAAO/L,yBAAyB,CAAC,IAAI,CAACiF,GAAG,CAACG,OAAO,CAAC3E,KAAK,CAAC;IACxD,OAAOlD,OAAO,CAACkJ,OAAO,CAAC,CAAC;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,IAAIzG,yBAAyB,GAAG,CAAC,CAAC;AAClC;AACA;AACA;AACA;AACA;AACA,IAAIC,yBAAyB,GAAG,EAAE;AAClC;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG,CAAC,CAAC;AAC/B;AACA;AACA;AACA,IAAI7B,aAAa,GAAG,WAAW;AAC/B;AACA;AACA;AACA,IAAI2N,QAAQ,GAAG,MAAM;AACrB;AACA;AACA;AACA;AACA,IAAIC,gBAAgB;AACpB;AACA;AACA;AACA;AACA,IAAIC,mBAAmB;AACvB;AACA;AACA;AACA;AACA,IAAIC,cAAc,GAAG,KAAK;AAC1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,QAAQA,CAAChH,OAAO,EAAE;EACvB,IAAI+G,cAAc,EAAE;IAChB,MAAMvP,aAAa,CAACK,MAAM,CAAC,qBAAqB,CAAC,wCAAwC,CAAC;EAC9F;EACA,IAAImI,OAAO,CAAC/G,aAAa,EAAE;IACvBA,aAAa,GAAG+G,OAAO,CAAC/G,aAAa;EACzC;EACA,IAAI+G,OAAO,CAAC4G,QAAQ,EAAE;IAClBA,QAAQ,GAAG5G,OAAO,CAAC4G,QAAQ;EAC/B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,4BAA4BA,CAAA,EAAG;EACpC,MAAMC,qBAAqB,GAAG,EAAE;EAChC,IAAIvQ,kBAAkB,CAAC,CAAC,EAAE;IACtBuQ,qBAAqB,CAAChL,IAAI,CAAC,0CAA0C,CAAC;EAC1E;EACA,IAAI,CAACtF,iBAAiB,CAAC,CAAC,EAAE;IACtBsQ,qBAAqB,CAAChL,IAAI,CAAC,4BAA4B,CAAC;EAC5D;EACA,IAAIgL,qBAAqB,CAAC/K,MAAM,GAAG,CAAC,EAAE;IAClC,MAAMgL,OAAO,GAAGD,qBAAqB,CAChC7O,GAAG,CAAC,CAACL,OAAO,EAAEoP,KAAK,KAAK,IAAIA,KAAK,GAAG,CAAC,KAAKpP,OAAO,EAAE,CAAC,CACpDqP,IAAI,CAAC,GAAG,CAAC;IACd,MAAMzP,GAAG,GAAGJ,aAAa,CAACK,MAAM,CAAC,2BAA2B,CAAC,gDAAgD;MACzGyN,SAAS,EAAE6B;IACf,CAAC,CAAC;IACF7P,MAAM,CAACS,IAAI,CAACH,GAAG,CAACI,OAAO,CAAC;EAC5B;AACJ;AACA;AACA;AACA;AACA;AACA,SAASsP,OAAOA,CAACzH,GAAG,EAAEoG,aAAa,EAAEjG,OAAO,EAAE;EAC1CiH,4BAA4B,CAAC,CAAC;EAC9B,MAAM5L,KAAK,GAAGwE,GAAG,CAACG,OAAO,CAAC3E,KAAK;EAC/B,IAAI,CAACA,KAAK,EAAE;IACR,MAAM7D,aAAa,CAACK,MAAM,CAAC,WAAW,CAAC,8BAA8B,CAAC;EAC1E;EACA,IAAI,CAACgI,GAAG,CAACG,OAAO,CAAC5B,MAAM,EAAE;IACrB,IAAIyB,GAAG,CAACG,OAAO,CAAC9G,aAAa,EAAE;MAC3B5B,MAAM,CAACS,IAAI,CAAC,8FAA8F,GACtG,6EAA6E8H,GAAG,CAACG,OAAO,CAAC9G,aAAa,EAAE,GACxG,sEAAsE,CAAC;IAC/E,CAAC,MACI;MACD,MAAM1B,aAAa,CAACK,MAAM,CAAC,YAAY,CAAC,+BAA+B,CAAC;IAC5E;EACJ;EACA,IAAI+C,yBAAyB,CAACS,KAAK,CAAC,IAAI,IAAI,EAAE;IAC1C,MAAM7D,aAAa,CAACK,MAAM,CAAC,gBAAgB,CAAC,qCAAqC;MAC7E8L,EAAE,EAAEtI;IACR,CAAC,CAAC;EACN;EACA,IAAI,CAAC0L,cAAc,EAAE;IACjB;IACA;IACAnN,oBAAoB,CAACX,aAAa,CAAC;IACnC,MAAM;MAAEiE,WAAW;MAAEvC;IAAS,CAAC,GAAGoC,gBAAgB,CAACnC,yBAAyB,EAAEC,yBAAyB,EAAEC,oBAAoB,EAAE7B,aAAa,EAAE2N,QAAQ,CAAC;IACvJE,mBAAmB,GAAG5J,WAAW;IACjC2J,gBAAgB,GAAGlM,QAAQ;IAC3BoM,cAAc,GAAG,IAAI;EACzB;EACA;EACA;EACAnM,yBAAyB,CAACS,KAAK,CAAC,GAAGmK,oBAAoB,CAAC3F,GAAG,EAAEhF,yBAAyB,EAAEC,oBAAoB,EAAEmL,aAAa,EAAEY,gBAAgB,EAAE5N,aAAa,EAAE+G,OAAO,CAAC;EACtK,MAAMuH,iBAAiB,GAAG,IAAIb,gBAAgB,CAAC7G,GAAG,CAAC;EACnD,OAAO0H,iBAAiB;AAC5B;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,YAAYA,CAAC3H,GAAG,GAAG3J,MAAM,CAAC,CAAC,EAAE;EAClC2J,GAAG,GAAGhJ,kBAAkB,CAACgJ,GAAG,CAAC;EAC7B;EACA,MAAM4H,iBAAiB,GAAGxR,YAAY,CAAC4J,GAAG,EAAE7I,cAAc,CAAC;EAC3D,IAAIyQ,iBAAiB,CAACC,aAAa,CAAC,CAAC,EAAE;IACnC,OAAOD,iBAAiB,CAACE,YAAY,CAAC,CAAC;EAC3C;EACA,OAAOC,mBAAmB,CAAC/H,GAAG,CAAC;AACnC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS+H,mBAAmBA,CAAC/H,GAAG,EAAEG,OAAO,GAAG,CAAC,CAAC,EAAE;EAC5C;EACA,MAAMyH,iBAAiB,GAAGxR,YAAY,CAAC4J,GAAG,EAAE7I,cAAc,CAAC;EAC3D,IAAIyQ,iBAAiB,CAACC,aAAa,CAAC,CAAC,EAAE;IACnC,MAAMG,gBAAgB,GAAGJ,iBAAiB,CAACE,YAAY,CAAC,CAAC;IACzD,IAAI7Q,SAAS,CAACkJ,OAAO,EAAEyH,iBAAiB,CAACK,UAAU,CAAC,CAAC,CAAC,EAAE;MACpD,OAAOD,gBAAgB;IAC3B,CAAC,MACI;MACD,MAAMrQ,aAAa,CAACK,MAAM,CAAC,qBAAqB,CAAC,wCAAwC,CAAC;IAC9F;EACJ;EACA,MAAM0P,iBAAiB,GAAGE,iBAAiB,CAACM,UAAU,CAAC;IAAE/H;EAAQ,CAAC,CAAC;EACnE,OAAOuH,iBAAiB;AAC5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAWeS,WAAWA,CAAA;EAAA,OAAAC,YAAA,CAAAzN,KAAA,OAAAC,SAAA;AAAA;AAkB1B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAVA,SAAAwN,aAAA;EAAAA,YAAA,GAAAvN,iBAAA,CAlBA,aAA6B;IACzB,IAAI/D,kBAAkB,CAAC,CAAC,EAAE;MACtB,OAAO,KAAK;IAChB;IACA,IAAI,CAACC,iBAAiB,CAAC,CAAC,EAAE;MACtB,OAAO,KAAK;IAChB;IACA,IAAI,CAACH,oBAAoB,CAAC,CAAC,EAAE;MACzB,OAAO,KAAK;IAChB;IACA,IAAI;MACA,MAAMyR,YAAY,SAASxR,yBAAyB,CAAC,CAAC;MACtD,OAAOwR,YAAY;IACvB,CAAC,CACD,OAAO5M,KAAK,EAAE;MACV,OAAO,KAAK;IAChB;EACJ,CAAC;EAAA,OAAA2M,YAAA,CAAAzN,KAAA,OAAAC,SAAA;AAAA;AAYD,SAAS0N,gBAAgBA,CAACZ,iBAAiB,EAAEpE,UAAU,EAAEnD,OAAO,EAAE;EAC9DuH,iBAAiB,GAAG1Q,kBAAkB,CAAC0Q,iBAAiB,CAAC;EACzD1E,kBAAkB,CAACiE,mBAAmB,EAAElM,yBAAyB,CAAC2M,iBAAiB,CAAC1H,GAAG,CAACG,OAAO,CAAC3E,KAAK,CAAC,EAAE8H,UAAU,EAAEnD,OAAO,CAAC,CAACzH,KAAK,CAACC,CAAC,IAAIlB,MAAM,CAACgE,KAAK,CAAC9C,CAAC,CAAC,CAAC;AAC5J;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAQe4P,0BAA0BA,CAAAC,IAAA;EAAA,OAAAC,2BAAA,CAAA9N,KAAA,OAAAC,SAAA;AAAA;AAIzC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAPA,SAAA6N,4BAAA;EAAAA,2BAAA,GAAA5N,iBAAA,CAJA,WAA0C6M,iBAAiB,EAAE;IACzDA,iBAAiB,GAAG1Q,kBAAkB,CAAC0Q,iBAAiB,CAAC;IACzD,OAAOjD,kCAAkC,CAACwC,mBAAmB,EAAElM,yBAAyB,CAAC2M,iBAAiB,CAAC1H,GAAG,CAACG,OAAO,CAAC3E,KAAK,CAAC,CAAC;EAClI,CAAC;EAAA,OAAAiN,2BAAA,CAAA9N,KAAA,OAAAC,SAAA;AAAA;AASD,SAAS8N,SAASA,CAAChB,iBAAiB,EAAE5D,EAAE,EAAE3D,OAAO,EAAE;EAC/CuH,iBAAiB,GAAG1Q,kBAAkB,CAAC0Q,iBAAiB,CAAC;EACzDlE,WAAW,CAACyD,mBAAmB,EAAElM,yBAAyB,CAAC2M,iBAAiB,CAAC1H,GAAG,CAACG,OAAO,CAAC3E,KAAK,CAAC,EAAEsI,EAAE,EAAE3D,OAAO,CAAC,CAACzH,KAAK,CAACC,CAAC,IAAIlB,MAAM,CAACgE,KAAK,CAAC9C,CAAC,CAAC,CAAC;AAC7I;AACA;AACA;AACA;AACA;AACA;AACA,SAASgQ,iBAAiBA,CAACjB,iBAAiB,EAAErD,UAAU,EAAElE,OAAO,EAAE;EAC/DuH,iBAAiB,GAAG1Q,kBAAkB,CAAC0Q,iBAAiB,CAAC;EACzD3D,mBAAmB,CAACkD,mBAAmB,EAAElM,yBAAyB,CAAC2M,iBAAiB,CAAC1H,GAAG,CAACG,OAAO,CAAC3E,KAAK,CAAC,EAAE6I,UAAU,EAAElE,OAAO,CAAC,CAACzH,KAAK,CAACC,CAAC,IAAIlB,MAAM,CAACgE,KAAK,CAAC9C,CAAC,CAAC,CAAC;AAC7J;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiQ,6BAA6BA,CAAClB,iBAAiB,EAAExC,OAAO,EAAE;EAC/DwC,iBAAiB,GAAG1Q,kBAAkB,CAAC0Q,iBAAiB,CAAC;EACzD5C,+BAA+B,CAAC/J,yBAAyB,CAAC2M,iBAAiB,CAAC1H,GAAG,CAACG,OAAO,CAAC3E,KAAK,CAAC,EAAE0J,OAAO,CAAC,CAACxM,KAAK,CAACC,CAAC,IAAIlB,MAAM,CAACgE,KAAK,CAAC9C,CAAC,CAAC,CAAC;AACxI;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASkQ,yBAAyBA,CAAC5L,YAAY,EAAE;EAC7C;EACA,IAAIgK,mBAAmB,EAAE;IACrBA,mBAAmB,CAAC,KAAK,CAAC,uBAAuBhK,YAAY,CAAC;EAClE,CAAC,MACI;IACDqI,iCAAiC,CAACrI,YAAY,CAAC;EACnD;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6L,QAAQA,CAACpB,iBAAiB,EAAE/E,SAAS,EAAEC,WAAW,EAAEzC,OAAO,EAAE;EAClEuH,iBAAiB,GAAG1Q,kBAAkB,CAAC0Q,iBAAiB,CAAC;EACzDvF,UAAU,CAAC8E,mBAAmB,EAAElM,yBAAyB,CAAC2M,iBAAiB,CAAC1H,GAAG,CAACG,OAAO,CAAC3E,KAAK,CAAC,EAAEmH,SAAS,EAAEC,WAAW,EAAEzC,OAAO,CAAC,CAACzH,KAAK,CAACC,CAAC,IAAIlB,MAAM,CAACgE,KAAK,CAAC9C,CAAC,CAAC,CAAC;AAChK;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoQ,UAAUA,CAAC1D,eAAe,EAAE;EACjC;EACA,IAAI4B,mBAAmB,EAAE;IACrBA,mBAAmB,CAAC,SAAS,CAAC,2BAA2B,QAAQ,EAAE5B,eAAe,CAAC;EACvF,CAAC,MACI;IACDD,yBAAyB,CAACC,eAAe,CAAC;EAC9C;AACJ;AAEA,MAAM2D,IAAI,GAAG,qBAAqB;AAClC,MAAMC,OAAO,GAAG,QAAQ;;AAExB;AACA;AACA;AACA;AACA;AACA,SAASC,iBAAiBA,CAAA,EAAG;EACzB5S,kBAAkB,CAAC,IAAIY,SAAS,CAACC,cAAc,EAAE,CAACgS,SAAS,EAAE;IAAEhJ,OAAO,EAAEiJ;EAAiB,CAAC,KAAK;IAC3F;IACA,MAAMpJ,GAAG,GAAGmJ,SAAS,CAACE,WAAW,CAAC,KAAK,CAAC,CAACvB,YAAY,CAAC,CAAC;IACvD,MAAM1B,aAAa,GAAG+C,SAAS,CAC1BE,WAAW,CAAC,wBAAwB,CAAC,CACrCvB,YAAY,CAAC,CAAC;IACnB,OAAOL,OAAO,CAACzH,GAAG,EAAEoG,aAAa,EAAEgD,gBAAgB,CAAC;EACxD,CAAC,EAAE,QAAQ,CAAC,0BAA0B,CAAC,CAAC;EACxC9S,kBAAkB,CAAC,IAAIY,SAAS,CAAC,oBAAoB,EAAEoS,eAAe,EAAE,SAAS,CAAC,2BAA2B,CAAC,CAAC;EAC/G/S,eAAe,CAACyS,IAAI,EAAEC,OAAO,CAAC;EAC9B;EACA1S,eAAe,CAACyS,IAAI,EAAEC,OAAO,EAAE,SAAS,CAAC;EACzC,SAASK,eAAeA,CAACH,SAAS,EAAE;IAChC,IAAI;MACA,MAAMI,SAAS,GAAGJ,SAAS,CAACE,WAAW,CAAClS,cAAc,CAAC,CAAC2Q,YAAY,CAAC,CAAC;MACtE,OAAO;QACHgB,QAAQ,EAAEA,CAACnG,SAAS,EAAEC,WAAW,EAAEzC,OAAO,KAAK2I,QAAQ,CAACS,SAAS,EAAE5G,SAAS,EAAEC,WAAW,EAAEzC,OAAO;MACtG,CAAC;IACL,CAAC,CACD,OAAOxH,CAAC,EAAE;MACN,MAAMhB,aAAa,CAACK,MAAM,CAAC,8BAA8B,CAAC,mDAAmD;QACzGwR,MAAM,EAAE7Q;MACZ,CAAC,CAAC;IACN;EACJ;AACJ;AACAuQ,iBAAiB,CAAC,CAAC;AAEnB,SAASvB,YAAY,EAAEY,0BAA0B,EAAER,mBAAmB,EAAEI,WAAW,EAAEW,QAAQ,EAAEF,6BAA6B,EAAEG,UAAU,EAAET,gBAAgB,EAAEO,yBAAyB,EAAEH,SAAS,EAAEC,iBAAiB,EAAExB,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}