/* Validator Dashboard Container */
.validator-dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* Header Section */
.validator-header {
  margin-bottom: 40px;
  padding: 40px;
  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);
  border-radius: 24px;
  color: white;
  box-shadow: 0 20px 40px rgba(33, 150, 243, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.validator-welcome h1 {
  margin: 0 0 8px 0;
  font-size: 2.5rem;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
}

.validator-subtitle {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.validator-badge {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.2);
  padding: 16px 24px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.validator-badge mat-icon {
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
}

.validator-badge span {
  font-weight: 600;
  font-size: 1.1rem;
}

/* Section Titles */
.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 24px 0;
  color: #2d3748;
  font-size: 1.8rem;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
  position: relative;
}

.pending-count {
  background: #FF9800;
  color: white;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-left: 12px;
}

/* Quick Stats */
.quick-stats-section {
  margin-bottom: 48px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.stat-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-header {
  margin-bottom: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon mat-icon {
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
}

.stat-value {
  margin: 0 0 4px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
}

.stat-title {
  margin: 0 0 4px 0;
  color: #4a5568;
  font-size: 1rem;
  font-weight: 600;
}

.stat-description {
  color: #718096;
  font-size: 0.8rem;
}

/* Pending Validations */
.pending-section {
  margin-bottom: 48px;
}

.empty-state {
  text-align: center;
}

.empty-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  padding: 40px;
}

.empty-icon {
  font-size: 4rem !important;
  width: 4rem !important;
  height: 4rem !important;
  color: #4CAF50;
  margin-bottom: 16px;
}

.empty-card h3 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-size: 1.5rem;
}

.empty-card p {
  margin: 0;
  color: #718096;
}

.validations-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 24px;
}

.validation-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.validation-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.validation-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.user-info h4 {
  margin: 0 0 4px 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
}

.user-email {
  color: #718096;
  font-size: 0.9rem;
}

.type-badge {
  background: linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(25, 118, 210, 0.1));
  color: #1976D2;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
}

.description {
  margin: 16px 0;
  color: #2d3748;
  font-size: 1rem;
  line-height: 1.5;
}

.validation-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
  margin-bottom: 16px;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 4px;
  color: #4a5568;
  font-size: 0.9rem;
}

.meta-item mat-icon {
  font-size: 1rem !important;
  width: 1rem !important;
  height: 1rem !important;
  color: #718096;
}

.meta-item.points {
  color: #FF9800;
  font-weight: 600;
}

.meta-item.points mat-icon {
  color: #FF9800;
}

.evidence-section {
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.evidence-section h5 {
  margin: 0 0 12px 0;
  color: #4a5568;
  font-size: 0.9rem;
  font-weight: 600;
}

.evidence-list {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.evidence-btn {
  font-size: 0.8rem;
}

.validation-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  padding: 16px;
  border-top: 1px solid #e2e8f0;
}

.approve-btn, .reject-btn {
  font-weight: 600;
}

/* Recent Validations */
.recent-section {
  margin-bottom: 48px;
}

.recent-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.recent-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.recent-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f7fafc;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.recent-item:hover {
  background: #edf2f7;
  transform: translateX(4px);
}

.recent-icon {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.recent-icon mat-icon {
  font-size: 1.2rem;
  width: 1.2rem;
  height: 1.2rem;
}

.recent-content {
  flex: 1;
}

.recent-description {
  margin: 0 0 4px 0;
  color: #2d3748;
  font-size: 0.9rem;
}

.recent-meta {
  display: flex;
  gap: 16px;
  align-items: center;
  font-size: 0.8rem;
}

.recent-time {
  color: #718096;
}

.recent-status {
  font-weight: 600;
}

.recent-points {
  color: #FF9800;
  font-weight: 600;
}

.view-all-btn {
  width: 100%;
  height: 44px;
  font-weight: 600;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .validator-dashboard-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .validator-welcome h1 {
    font-size: 2rem;
  }
  
  .stats-grid,
  .validations-grid {
    grid-template-columns: 1fr;
  }
  
  .validation-actions {
    flex-direction: column;
  }
  
  .recent-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
}
