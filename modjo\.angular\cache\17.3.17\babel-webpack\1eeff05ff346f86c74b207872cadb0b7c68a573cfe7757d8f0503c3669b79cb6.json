{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ValidationListComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function ValidationListComponent_Factory(t) {\n      return new (t || ValidationListComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ValidationListComponent,\n      selectors: [[\"app-validation-list\"]],\n      decls: 5,\n      vars: 0,\n      template: function ValidationListComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"h2\");\n          i0.ɵɵtext(2, \"\\uD83D\\uDCCB Liste des Validations\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"Liste compl\\u00E8te des actions \\u00E0 valider\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\"div[_ngcontent-%COMP%] { padding: 20px; }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInZhbGlkYXRpb24tbGlzdC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsTUFBTSxhQUFhLEVBQUUiLCJmaWxlIjoidmFsaWRhdGlvbi1saXN0LmNvbXBvbmVudC50cyIsInNvdXJjZXNDb250ZW50IjpbImRpdiB7IHBhZGRpbmc6IDIwcHg7IH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvdmFsaWRhdG9yLWRhc2hib2FyZC9jb21wb25lbnRzL3ZhbGlkYXRpb24tbGlzdC92YWxpZGF0aW9uLWxpc3QuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLE1BQU0sYUFBYSxFQUFFO0FBQ3JCLHdTQUF3UyIsInNvdXJjZXNDb250ZW50IjpbImRpdiB7IHBhZGRpbmc6IDIwcHg7IH0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ValidationListComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "template", "ValidationListComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\validator-dashboard\\components\\validation-list\\validation-list.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-validation-list',\n  template: '<div><h2>📋 Liste des Validations</h2><p>Liste complète des actions à valider</p></div>',\n  styles: ['div { padding: 20px; }']\n})\nexport class ValidationListComponent implements OnInit {\n  constructor() { }\n  ngOnInit(): void { }\n}\n"], "mappings": ";AAOA,OAAM,MAAOA,uBAAuB;EAClCC,YAAA,GAAgB;EAChBC,QAAQA,CAAA,GAAW;;;uBAFRF,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAHlBE,EAAL,CAAAC,cAAA,UAAK,SAAI;UAAAD,EAAA,CAAAE,MAAA,yCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAAAH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,qDAAoC;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}