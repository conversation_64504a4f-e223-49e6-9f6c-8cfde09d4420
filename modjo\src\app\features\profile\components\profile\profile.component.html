<div class="profile-container" *ngIf="!isLoading && user">
  <!-- Profile header -->
  <div class="profile-header">
    <div class="profile-avatar">
      <mat-icon class="avatar-icon">account_circle</mat-icon>
    </div>
    <div class="profile-info">
      <h1>{{ user.name }}</h1>
      <p class="user-email">{{ user.email }}</p>
      <div class="user-badges">
        <mat-chip-set>
          <mat-chip [color]="getRoleBadgeColor(user.role)" selected>
            {{ getRoleDisplayName(user.role) }}
          </mat-chip>
          <mat-chip [style.background-color]="getActivityLevelColor()" selected>
            {{ getActivityLevel() }}
          </mat-chip>
        </mat-chip-set>
      </div>
    </div>
    <div class="profile-actions">
      <button mat-raised-button color="primary" (click)="editProfile()" class="action-btn">
        <mat-icon>edit</mat-icon>
        Modifier
      </button>

      <button mat-stroked-button color="accent" (click)="shareProfile()" class="action-btn">
        <mat-icon>share</mat-icon>
        Partager
      </button>

      <button mat-stroked-button color="primary" (click)="downloadQRCode()" class="action-btn">
        <mat-icon>qr_code</mat-icon>
        Mon QR Code
      </button>
    </div>
  </div>

  <!-- Profile tabs -->
  <mat-tab-group class="profile-tabs">
    <!-- Overview tab -->
    <mat-tab label="Aperçu">
      <div class="tab-content">
        <!-- Stats cards -->
        <div class="stats-section">
          <div class="stats-grid">
            <mat-card class="stat-card">
              <mat-card-content>
                <div class="stat-content">
                  <mat-icon class="stat-icon" style="color: #ffd700;">stars</mat-icon>
                  <div class="stat-info">
                    <h3>{{ user.points }}</h3>
                    <p>Points totaux</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="stat-card" *ngIf="userStats">
              <mat-card-content>
                <div class="stat-content">
                  <mat-icon class="stat-icon" style="color: #4caf50;">trending_up</mat-icon>
                  <div class="stat-info">
                    <h3>{{ userStats.totalEarned || 0 }}</h3>
                    <p>Points gagnés</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="stat-card" *ngIf="userStats">
              <mat-card-content>
                <div class="stat-content">
                  <mat-icon class="stat-icon" style="color: #ff9800;">shopping_cart</mat-icon>
                  <div class="stat-info">
                    <h3>{{ userStats.totalSpent || 0 }}</h3>
                    <p>Points dépensés</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>

            <mat-card class="stat-card" *ngIf="userStats">
              <mat-card-content>
                <div class="stat-content">
                  <mat-icon class="stat-icon" style="color: #2196f3;">timeline</mat-icon>
                  <div class="stat-info">
                    <h3>{{ userStats.transactionCount || 0 }}</h3>
                    <p>Transactions</p>
                  </div>
                </div>
              </mat-card-content>
            </mat-card>
          </div>
        </div>

        <!-- User details -->
        <div class="details-section">
          <mat-card class="details-card">
            <mat-card-header>
              <mat-card-title>
                <mat-icon>info</mat-icon>
                Informations personnelles
              </mat-card-title>
            </mat-card-header>
            <mat-card-content>
              <div class="details-list">
                <div class="detail-item">
                  <mat-icon>location_on</mat-icon>
                  <span class="detail-label">Ville:</span>
                  <span class="detail-value">{{ user.city }}</span>
                </div>
                <div class="detail-item" *ngIf="user.phone">
                  <mat-icon>phone</mat-icon>
                  <span class="detail-label">Téléphone:</span>
                  <span class="detail-value">{{ user.phone }}</span>
                </div>
                <div class="detail-item">
                  <mat-icon>calendar_today</mat-icon>
                  <span class="detail-label">Membre depuis:</span>
                  <span class="detail-value">{{ getMemberSince() }}</span>
                </div>
                <div class="detail-item">
                  <mat-icon>update</mat-icon>
                  <span class="detail-label">Dernière activité:</span>
                  <span class="detail-value">{{ formatDate(user.updatedAt) }}</span>
                </div>
              </div>
            </mat-card-content>
          </mat-card>
        </div>
      </div>
    </mat-tab>

    <!-- Recent activity tab -->
    <mat-tab label="Activité récente">
      <div class="tab-content">
        <mat-card class="activity-card">
          <mat-card-header>
            <mat-card-title>
              <mat-icon>history</mat-icon>
              Dernières transactions
            </mat-card-title>
            <button mat-button color="primary" (click)="viewAllTransactions()">
              Voir tout
            </button>
          </mat-card-header>
          <mat-card-content>
            <div class="activity-list" *ngIf="recentTransactions.length > 0; else noActivity">
              <div *ngFor="let transaction of recentTransactions" class="activity-item">
                <div class="activity-icon">
                  <mat-icon [class]="'activity-' + transaction.type">
                    {{ getTransactionIcon(transaction.type) }}
                  </mat-icon>
                </div>
                <div class="activity-info">
                  <p class="activity-description">{{ transaction.description }}</p>
                  <p class="activity-date">{{ formatDate(transaction.timestamp) }}</p>
                </div>
                <div class="activity-points" [class]="transaction.points > 0 ? 'positive' : 'negative'">
                  {{ transaction.points > 0 ? '+' : '' }}{{ transaction.points }} pts
                </div>
              </div>
            </div>
            
            <ng-template #noActivity>
              <div class="no-activity">
                <mat-icon class="no-activity-icon">inbox</mat-icon>
                <h3>Aucune activité récente</h3>
                <p>Commencez à scanner des QR codes pour voir votre activité ici.</p>
                <button mat-raised-button color="primary" routerLink="/qr-scanner">
                  <mat-icon>qr_code_scanner</mat-icon>
                  Scanner un QR Code
                </button>
              </div>
            </ng-template>
          </mat-card-content>
        </mat-card>
      </div>
    </mat-tab>
  </mat-tab-group>
</div>

<!-- Loading state -->
<div class="loading-container" *ngIf="isLoading">
  <mat-card class="loading-card">
    <mat-card-content>
      <div class="loading-content">
        <mat-icon class="loading-icon">account_circle</mat-icon>
        <h3>Chargement du profil...</h3>
      </div>
    </mat-card-content>
  </mat-card>
</div>
