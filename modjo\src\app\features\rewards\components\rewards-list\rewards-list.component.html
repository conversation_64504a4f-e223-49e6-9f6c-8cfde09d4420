<div class="rewards-container">
  <!-- Header -->
  <div class="rewards-header slide-up">
    <h1>🎁 Récompenses</h1>
    <p>Échangez vos points contre des récompenses exclusives</p>
    
    <div class="user-points" *ngIf="currentUser">
      <mat-icon>stars</mat-icon>
      <span>{{ currentUser.points }} points disponibles</span>
    </div>
  </div>

  <!-- Filters -->
  <div class="filters-section fade-in">
    <mat-card class="filters-card">
      <mat-card-content>
        <div class="filters-grid">
          <!-- Search -->
          <mat-form-field appearance="outline" class="search-field">
            <mat-label>Rechercher</mat-label>
            <input matInput [formControl]="searchFilter" placeholder="Nom, description, partenaire...">
            <mat-icon matSuffix>search</mat-icon>
          </mat-form-field>

          <!-- Category filter -->
          <mat-form-field appearance="outline">
            <mat-label>Catégorie</mat-label>
            <mat-select [formControl]="categoryFilter">
              <mat-option *ngFor="let category of categories" [value]="category.value">
                <mat-icon>{{ category.icon }}</mat-icon>
                {{ category.label }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- City filter -->
          <mat-form-field appearance="outline">
            <mat-label>Ville</mat-label>
            <mat-select [formControl]="cityFilter">
              <mat-option *ngFor="let city of cities" [value]="city.value">
                {{ city.label }}
              </mat-option>
            </mat-select>
          </mat-form-field>

          <!-- Clear filters -->
          <button mat-stroked-button (click)="clearFilters()" class="clear-filters-btn">
            <mat-icon>clear</mat-icon>
            Effacer
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Rewards grid -->
  <div class="rewards-grid" *ngIf="filteredRewards$ | async as rewards">
    <mat-card *ngFor="let reward of rewards; let i = index" 
              class="reward-card floating-card"
              [style.animation-delay]="(i * 0.1) + 's'"
              (click)="viewRewardDetails(reward)">
      
      <!-- Reward image -->
      <div class="reward-image">
        <img [src]="reward.imageUrl || 'assets/images/reward-placeholder.jpg'" 
             [alt]="reward.title"
             onerror="this.src='assets/images/reward-placeholder.jpg'">
        
        <!-- Badges -->
        <div class="reward-badges">
          <mat-chip class="category-chip" [color]="'primary'" selected>
            <mat-icon>{{ getCategoryIcon(reward.category) }}</mat-icon>
            {{ getCategoryLabel(reward.category) }}
          </mat-chip>
          
          <mat-chip *ngIf="isExpiringSoon(reward)" 
                   class="expiry-chip" 
                   color="warn" 
                   selected>
            <mat-icon>schedule</mat-icon>
            Expire bientôt
          </mat-chip>
        </div>
      </div>

      <!-- Reward content -->
      <mat-card-content>
        <div class="reward-header">
          <h3>{{ reward.title }}</h3>
          <div class="points-required">
            <mat-icon>stars</mat-icon>
            <span>{{ reward.pointsRequired }}</span>
          </div>
        </div>

        <p class="reward-description">{{ reward.description }}</p>
        
        <div class="reward-partner">
          <mat-icon>store</mat-icon>
          <span>{{ reward.partnerName }}</span>
          <mat-chip class="city-chip" [color]="'accent'" selected>
            {{ reward.city }}
          </mat-chip>
        </div>

        <div class="reward-availability">
          <mat-chip [color]="getAvailabilityColor(reward)" selected>
            {{ getAvailabilityText(reward) }}
          </mat-chip>
          
          <span *ngIf="reward.validUntil" class="expiry-date">
            Valide jusqu'au {{ formatExpiryDate(reward.validUntil) }}
          </span>
        </div>
      </mat-card-content>

      <!-- Reward actions -->
      <mat-card-actions>
        <button mat-raised-button 
                [color]="canAfford(reward) ? 'primary' : 'basic'"
                [disabled]="!canAfford(reward) || !reward.availableQuantity"
                (click)="exchangeReward(reward); $event.stopPropagation()"
                class="exchange-btn">
          <mat-icon>{{ canAfford(reward) ? 'redeem' : 'lock' }}</mat-icon>
          {{ canAfford(reward) ? 'Échanger' : 'Points insuffisants' }}
        </button>
        
        <button mat-button (click)="viewRewardDetails(reward); $event.stopPropagation()">
          <mat-icon>info</mat-icon>
          Détails
        </button>
      </mat-card-actions>
    </mat-card>
  </div>

  <!-- Empty state -->
  <div class="empty-state" *ngIf="(filteredRewards$ | async)?.length === 0">
    <mat-card class="empty-card">
      <mat-card-content>
        <div class="empty-content">
          <mat-icon class="empty-icon">card_giftcard</mat-icon>
          <h3>Aucune récompense trouvée</h3>
          <p>Essayez de modifier vos filtres ou revenez plus tard pour découvrir de nouvelles récompenses.</p>
          <button mat-raised-button color="primary" (click)="clearFilters()">
            <mat-icon>refresh</mat-icon>
            Réinitialiser les filtres
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Floating action button -->
  <button mat-fab 
          class="floating-fab" 
          color="primary"
          routerLink="/qr-scanner"
          matTooltip="Scanner pour gagner des points">
    <mat-icon>qr_code_scanner</mat-icon>
  </button>
</div>
