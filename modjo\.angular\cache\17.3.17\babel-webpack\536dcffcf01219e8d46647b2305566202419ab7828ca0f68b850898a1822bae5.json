{"ast": null, "code": "export var ValidationAction;\n(function (ValidationAction) {\n  ValidationAction[\"COMMUNITY_SERVICE\"] = \"community_service\";\n  ValidationAction[\"ENVIRONMENTAL_ACTION\"] = \"environmental_action\";\n  ValidationAction[\"CULTURAL_PARTICIPATION\"] = \"cultural_participation\";\n  ValidationAction[\"SPORTS_ACTIVITY\"] = \"sports_activity\";\n  ValidationAction[\"EDUCATIONAL_ACTIVITY\"] = \"educational_activity\";\n  ValidationAction[\"VOLUNTEER_WORK\"] = \"volunteer_work\";\n  ValidationAction[\"LOCAL_BUSINESS_SUPPORT\"] = \"local_business_support\";\n  ValidationAction[\"OTHER\"] = \"other\";\n})(ValidationAction || (ValidationAction = {}));\nexport var ValidationStatus;\n(function (ValidationStatus) {\n  ValidationStatus[\"PENDING\"] = \"pending\";\n  ValidationStatus[\"APPROVED\"] = \"approved\";\n  ValidationStatus[\"REJECTED\"] = \"rejected\";\n  ValidationStatus[\"EXPIRED\"] = \"expired\";\n})(ValidationStatus || (ValidationStatus = {}));\nexport var QrCodeType;\n(function (QrCodeType) {\n  QrCodeType[\"USER_TRANSFER\"] = \"user_transfer\";\n  QrCodeType[\"VALIDATOR_ACTION\"] = \"validator_action\";\n  QrCodeType[\"PARTNER_REWARD\"] = \"partner_reward\";\n  QrCodeType[\"SYSTEM_BONUS\"] = \"system_bonus\";\n})(QrCodeType || (QrCodeType = {}));\n// GeoLocation interface is defined in user.model.ts", "map": {"version": 3, "names": ["ValidationAction", "ValidationStatus", "QrCodeType"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\models\\validation.model.ts"], "sourcesContent": ["export interface Validation {\n  id: string;\n  validatorId: string;\n  validatorName: string;\n  userId: string;\n  userName: string;\n  action: ValidationAction;\n  points: number;\n  description: string;\n  status: ValidationStatus;\n  timestamp: Date;\n  location?: GeoLocation;\n  evidence?: ValidationEvidence;\n  qrCode?: string;\n}\n\nexport enum ValidationAction {\n  COMMUNITY_SERVICE = 'community_service',\n  ENVIRONMENTAL_ACTION = 'environmental_action',\n  CULTURAL_PARTICIPATION = 'cultural_participation',\n  SPORTS_ACTIVITY = 'sports_activity',\n  EDUCATIONAL_ACTIVITY = 'educational_activity',\n  VOLUNTEER_WORK = 'volunteer_work',\n  LOCAL_BUSINESS_SUPPORT = 'local_business_support',\n  OTHER = 'other'\n}\n\nexport enum ValidationStatus {\n  PENDING = 'pending',\n  APPROVED = 'approved',\n  REJECTED = 'rejected',\n  EXPIRED = 'expired'\n}\n\nexport interface ValidationEvidence {\n  photos?: string[];\n  description: string;\n  witnesses?: string[];\n  documents?: string[];\n}\n\nexport interface QrScanResult {\n  data: string;\n  format: string;\n  timestamp: Date;\n  location?: GeoLocation;\n}\n\nexport interface QrCodeData {\n  type: QrCodeType;\n  userId?: string;\n  validatorId?: string;\n  points?: number;\n  action?: string;\n  timestamp: number;\n  signature?: string;\n}\n\nexport enum QrCodeType {\n  USER_TRANSFER = 'user_transfer',\n  VALIDATOR_ACTION = 'validator_action',\n  PARTNER_REWARD = 'partner_reward',\n  SYSTEM_BONUS = 'system_bonus'\n}\n\nexport interface CreateValidationRequest {\n  userId: string;\n  action: ValidationAction;\n  points: number;\n  description: string;\n  evidence?: ValidationEvidence;\n  location?: GeoLocation;\n}\n\nexport interface UpdateValidationRequest {\n  status: ValidationStatus;\n  adminNotes?: string;\n  pointsAdjustment?: number;\n}\n\nexport interface ValidationStats {\n  totalValidations: number;\n  pendingValidations: number;\n  approvedValidations: number;\n  rejectedValidations: number;\n  totalPointsAwarded: number;\n  averagePointsPerValidation: number;\n  validationsByAction: Record<ValidationAction, number>;\n  validationsByMonth: Record<string, number>;\n}\n\n// GeoLocation interface is defined in user.model.ts\n"], "mappings": "AAgBA,WAAYA,gBASX;AATD,WAAYA,gBAAgB;EAC1BA,gBAAA,2CAAuC;EACvCA,gBAAA,iDAA6C;EAC7CA,gBAAA,qDAAiD;EACjDA,gBAAA,uCAAmC;EACnCA,gBAAA,iDAA6C;EAC7CA,gBAAA,qCAAiC;EACjCA,gBAAA,qDAAiD;EACjDA,gBAAA,mBAAe;AACjB,CAAC,EATWA,gBAAgB,KAAhBA,gBAAgB;AAW5B,WAAYC,gBAKX;AALD,WAAYA,gBAAgB;EAC1BA,gBAAA,uBAAmB;EACnBA,gBAAA,yBAAqB;EACrBA,gBAAA,yBAAqB;EACrBA,gBAAA,uBAAmB;AACrB,CAAC,EALWA,gBAAgB,KAAhBA,gBAAgB;AA+B5B,WAAYC,UAKX;AALD,WAAYA,UAAU;EACpBA,UAAA,mCAA+B;EAC/BA,UAAA,yCAAqC;EACrCA,UAAA,qCAAiC;EACjCA,UAAA,iCAA6B;AAC/B,CAAC,EALWA,UAAU,KAAVA,UAAU;AAiCtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}