.reward-detail-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.detail-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
  padding: 0 16px;
}

.detail-header h1 {
  margin: 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: #2d3748;
  flex: 1;
  text-align: center;
}

.back-button,
.share-button {
  background: rgba(255, 255, 255, 0.9);
  backdrop-filter: blur(10px);
  border-radius: 12px;
  width: 48px;
  height: 48px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.reward-hero {
  margin-bottom: 40px;
}

.hero-image {
  position: relative;
  height: 300px;
  border-radius: 24px;
  overflow: hidden;
  margin-bottom: 24px;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.hero-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.hero-badges {
  position: absolute;
  top: 20px;
  left: 20px;
  right: 20px;
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  flex-wrap: wrap;
  gap: 12px;
}

.category-chip,
.expiry-chip {
  background: rgba(255, 255, 255, 0.95) !important;
  backdrop-filter: blur(10px);
  font-weight: 600;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.hero-content {
  padding: 0 16px;
}

.reward-title-section {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.reward-title-section h2 {
  margin: 0;
  font-size: 2.2rem;
  font-weight: 700;
  color: #2d3748;
  font-family: 'Poppins', sans-serif;
  flex: 1;
}

.points-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #2d3748;
  padding: 12px 20px;
  border-radius: 24px;
  font-weight: 700;
  font-size: 1.1rem;
  box-shadow: 0 8px 25px rgba(255, 215, 0, 0.4);
}

.points-badge mat-icon {
  font-size: 1.3rem;
  width: 1.3rem;
  height: 1.3rem;
}

.reward-description {
  font-size: 1.1rem;
  line-height: 1.6;
  color: #4a5568;
  margin-bottom: 24px;
}

.reward-meta {
  display: flex;
  flex-wrap: wrap;
  gap: 20px;
  align-items: center;
}

.meta-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #718096;
  font-weight: 500;
}

.meta-item mat-icon {
  color: #667eea;
}

.detail-sections {
  margin-bottom: 40px;
}

.detail-card {
  margin-bottom: 24px;
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.detail-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
}

.validity-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 16px;
  padding: 12px;
  background: rgba(103, 126, 234, 0.1);
  border-radius: 12px;
  color: #667eea;
  font-weight: 500;
}

.partner-info h4 {
  margin: 0 0 8px 0;
  font-size: 1.2rem;
  font-weight: 600;
  color: #2d3748;
}

.partner-info p {
  margin: 0 0 16px 0;
  color: #718096;
  line-height: 1.5;
}

.partner-details {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.detail-row {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.detail-row mat-icon {
  color: #667eea;
  flex-shrink: 0;
}

.detail-row span {
  flex: 1;
  color: #4a5568;
}

.points-status-card {
  background: linear-gradient(135deg, rgba(103, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
}

.points-status {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 24px;
}

.current-points,
.points-after,
.points-needed {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: rgba(255, 255, 255, 0.8);
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
}

.current-points mat-icon {
  color: #4caf50;
}

.points-after mat-icon {
  color: #2196f3;
}

.points-needed mat-icon {
  color: #ff9800;
}

.points-status h4 {
  margin: 0 0 4px 0;
  font-size: 0.9rem;
  font-weight: 500;
  color: #718096;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.points-value {
  margin: 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
}

.points-value.needed {
  color: #f56565;
}

.action-section {
  position: sticky;
  bottom: 0;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border-top: 1px solid rgba(255, 255, 255, 0.2);
  padding: 24px;
  border-radius: 24px 24px 0 0;
  box-shadow: 0 -10px 30px rgba(0, 0, 0, 0.1);
}

.action-buttons {
  display: flex;
  gap: 16px;
  margin-bottom: 16px;
}

.exchange-button {
  flex: 2;
  height: 56px;
  border-radius: 16px;
  font-size: 1.1rem;
  font-weight: 600;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.scan-button {
  flex: 1;
  height: 56px;
  border-radius: 16px;
  font-weight: 600;
}

.exchange-note {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  color: #718096;
  font-size: 0.9rem;
  text-align: center;
  justify-content: center;
}

.exchange-note mat-icon {
  color: #667eea;
  font-size: 1.1rem;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-card {
  max-width: 400px;
  text-align: center;
}

.loading-content {
  padding: 40px;
}

.loading-content h3 {
  margin: 24px 0 0 0;
  color: #2d3748;
  font-size: 1.3rem;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .reward-detail-container {
    padding: 16px;
  }
  
  .detail-header {
    padding: 0;
  }
  
  .detail-header h1 {
    font-size: 1.5rem;
  }
  
  .hero-image {
    height: 250px;
  }
  
  .reward-title-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .reward-title-section h2 {
    font-size: 1.8rem;
  }
  
  .reward-meta {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .points-status {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    flex-direction: column;
  }
  
  .exchange-button,
  .scan-button {
    width: 100%;
  }
}
