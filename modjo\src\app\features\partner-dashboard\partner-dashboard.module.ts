import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { SharedModule } from '../../shared/shared.module';
import { PartnerDashboardComponent } from './components/partner-dashboard/partner-dashboard.component';

const routes = [
  { path: '', component: PartnerDashboardComponent }
];

@NgModule({
  declarations: [
    PartnerDashboardComponent
  ],
  imports: [
    SharedModule,
    RouterModule.forChild(routes)
  ]
})
export class PartnerDashboardModule { }
