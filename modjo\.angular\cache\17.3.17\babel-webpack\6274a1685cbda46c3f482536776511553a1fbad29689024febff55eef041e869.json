{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"@angular/material/button\";\nimport * as i3 from \"@angular/material/card\";\nimport * as i4 from \"@angular/material/icon\";\nexport class NotFoundComponent {\n  static {\n    this.ɵfac = function NotFoundComponent_Factory(t) {\n      return new (t || NotFoundComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: NotFoundComponent,\n      selectors: [[\"app-not-found\"]],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 14,\n      vars: 0,\n      consts: [[1, \"error-container\"], [1, \"error-card\"], [1, \"error-content\"], [1, \"error-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/dashboard\"]],\n      template: function NotFoundComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-content\")(3, \"div\", 2)(4, \"mat-icon\", 3);\n          i0.ɵɵtext(5, \"search_off\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"h1\");\n          i0.ɵɵtext(7, \"Page non trouv\\u00E9e\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\");\n          i0.ɵɵtext(9, \"La page que vous recherchez n'existe pas ou a \\u00E9t\\u00E9 d\\u00E9plac\\u00E9e.\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"button\", 4)(11, \"mat-icon\");\n          i0.ɵɵtext(12, \"home\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(13, \" Retour au tableau de bord \");\n          i0.ɵɵelementEnd()()()()();\n        }\n      },\n      dependencies: [i1.RouterLink, i2.MatButton, i3.MatCard, i3.MatCardContent, i4.MatIcon],\n      styles: [\".error-container[_ngcontent-%COMP%] {\\n      display: flex;\\n      justify-content: center;\\n      align-items: center;\\n      min-height: 100vh;\\n      padding: 16px;\\n      background-color: #f5f5f5;\\n    }\\n\\n    .error-card[_ngcontent-%COMP%] {\\n      max-width: 400px;\\n      text-align: center;\\n    }\\n\\n    .error-content[_ngcontent-%COMP%] {\\n      padding: 32px;\\n    }\\n\\n    .error-icon[_ngcontent-%COMP%] {\\n      font-size: 4rem;\\n      color: #ff9800;\\n      margin-bottom: 16px;\\n    }\\n\\n    h1[_ngcontent-%COMP%] {\\n      margin: 0 0 16px 0;\\n      color: #333;\\n    }\\n\\n    p[_ngcontent-%COMP%] {\\n      margin: 0 0 24px 0;\\n      color: #666;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIm5vdC1mb3VuZC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJO01BQ0UsYUFBYTtNQUNiLHVCQUF1QjtNQUN2QixtQkFBbUI7TUFDbkIsaUJBQWlCO01BQ2pCLGFBQWE7TUFDYix5QkFBeUI7SUFDM0I7O0lBRUE7TUFDRSxnQkFBZ0I7TUFDaEIsa0JBQWtCO0lBQ3BCOztJQUVBO01BQ0UsYUFBYTtJQUNmOztJQUVBO01BQ0UsZUFBZTtNQUNmLGNBQWM7TUFDZCxtQkFBbUI7SUFDckI7O0lBRUE7TUFDRSxrQkFBa0I7TUFDbEIsV0FBVztJQUNiOztJQUVBO01BQ0Usa0JBQWtCO01BQ2xCLFdBQVc7SUFDYiIsImZpbGUiOiJub3QtZm91bmQuY29tcG9uZW50LnRzIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLmVycm9yLWNvbnRhaW5lciB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgICBhbGlnbi1pdGVtczogY2VudGVyO1xuICAgICAgbWluLWhlaWdodDogMTAwdmg7XG4gICAgICBwYWRkaW5nOiAxNnB4O1xuICAgICAgYmFja2dyb3VuZC1jb2xvcjogI2Y1ZjVmNTtcbiAgICB9XG5cbiAgICAuZXJyb3ItY2FyZCB7XG4gICAgICBtYXgtd2lkdGg6IDQwMHB4O1xuICAgICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgIH1cblxuICAgIC5lcnJvci1jb250ZW50IHtcbiAgICAgIHBhZGRpbmc6IDMycHg7XG4gICAgfVxuXG4gICAgLmVycm9yLWljb24ge1xuICAgICAgZm9udC1zaXplOiA0cmVtO1xuICAgICAgY29sb3I6ICNmZjk4MDA7XG4gICAgICBtYXJnaW4tYm90dG9tOiAxNnB4O1xuICAgIH1cblxuICAgIGgxIHtcbiAgICAgIG1hcmdpbjogMCAwIDE2cHggMDtcbiAgICAgIGNvbG9yOiAjMzMzO1xuICAgIH1cblxuICAgIHAge1xuICAgICAgbWFyZ2luOiAwIDAgMjRweCAwO1xuICAgICAgY29sb3I6ICM2NjY7XG4gICAgfVxuICAiXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvbm90LWZvdW5kL25vdC1mb3VuZC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJO01BQ0UsYUFBYTtNQUNiLHVCQUF1QjtNQUN2QixtQkFBbUI7TUFDbkIsaUJBQWlCO01BQ2pCLGFBQWE7TUFDYix5QkFBeUI7SUFDM0I7O0lBRUE7TUFDRSxnQkFBZ0I7TUFDaEIsa0JBQWtCO0lBQ3BCOztJQUVBO01BQ0UsYUFBYTtJQUNmOztJQUVBO01BQ0UsZUFBZTtNQUNmLGNBQWM7TUFDZCxtQkFBbUI7SUFDckI7O0lBRUE7TUFDRSxrQkFBa0I7TUFDbEIsV0FBVztJQUNiOztJQUVBO01BQ0Usa0JBQWtCO01BQ2xCLFdBQVc7SUFDYjs7QUFFSixveUNBQW95QyIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5lcnJvci1jb250YWluZXIge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIG1pbi1oZWlnaHQ6IDEwMHZoO1xuICAgICAgcGFkZGluZzogMTZweDtcbiAgICAgIGJhY2tncm91bmQtY29sb3I6ICNmNWY1ZjU7XG4gICAgfVxuXG4gICAgLmVycm9yLWNhcmQge1xuICAgICAgbWF4LXdpZHRoOiA0MDBweDtcbiAgICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgICB9XG5cbiAgICAuZXJyb3ItY29udGVudCB7XG4gICAgICBwYWRkaW5nOiAzMnB4O1xuICAgIH1cblxuICAgIC5lcnJvci1pY29uIHtcbiAgICAgIGZvbnQtc2l6ZTogNHJlbTtcbiAgICAgIGNvbG9yOiAjZmY5ODAwO1xuICAgICAgbWFyZ2luLWJvdHRvbTogMTZweDtcbiAgICB9XG5cbiAgICBoMSB7XG4gICAgICBtYXJnaW46IDAgMCAxNnB4IDA7XG4gICAgICBjb2xvcjogIzMzMztcbiAgICB9XG5cbiAgICBwIHtcbiAgICAgIG1hcmdpbjogMCAwIDI0cHggMDtcbiAgICAgIGNvbG9yOiAjNjY2O1xuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NotFoundComponent", "selectors", "standalone", "features", "i0", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "NotFoundComponent_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\shared\\components\\not-found\\not-found.component.ts"], "sourcesContent": ["import { Component } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\n\n@Component({\n  selector: 'app-not-found',\n  standalone: true,\n  imports: [CommonModule, RouterModule, MatCardModule, MatButtonModule, MatIconModule],\n  template: `\n    <div class=\"error-container\">\n      <mat-card class=\"error-card\">\n        <mat-card-content>\n          <div class=\"error-content\">\n            <mat-icon class=\"error-icon\">search_off</mat-icon>\n            <h1>Page non trouvée</h1>\n            <p>La page que vous recherchez n'existe pas ou a été déplacée.</p>\n            <button mat-raised-button color=\"primary\" routerLink=\"/dashboard\">\n              <mat-icon>home</mat-icon>\n              Retour au tableau de bord\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  `,\n  styles: [`\n    .error-container {\n      display: flex;\n      justify-content: center;\n      align-items: center;\n      min-height: 100vh;\n      padding: 16px;\n      background-color: #f5f5f5;\n    }\n\n    .error-card {\n      max-width: 400px;\n      text-align: center;\n    }\n\n    .error-content {\n      padding: 32px;\n    }\n\n    .error-icon {\n      font-size: 4rem;\n      color: #ff9800;\n      margin-bottom: 16px;\n    }\n\n    h1 {\n      margin: 0 0 16px 0;\n      color: #333;\n    }\n\n    p {\n      margin: 0 0 24px 0;\n      color: #666;\n    }\n  `]\n})\nexport class NotFoundComponent { }\n"], "mappings": ";;;;;AAgEA,OAAM,MAAOA,iBAAiB;;;uBAAjBA,iBAAiB;IAAA;EAAA;;;YAAjBA,iBAAiB;MAAAC,SAAA;MAAAC,UAAA;MAAAC,QAAA,GAAAC,EAAA,CAAAC,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhDlBP,EAJR,CAAAS,cAAA,aAA6B,kBACE,uBACT,aACW,kBACI;UAAAT,EAAA,CAAAU,MAAA,iBAAU;UAAAV,EAAA,CAAAW,YAAA,EAAW;UAClDX,EAAA,CAAAS,cAAA,SAAI;UAAAT,EAAA,CAAAU,MAAA,4BAAgB;UAAAV,EAAA,CAAAW,YAAA,EAAK;UACzBX,EAAA,CAAAS,cAAA,QAAG;UAAAT,EAAA,CAAAU,MAAA,sFAA2D;UAAAV,EAAA,CAAAW,YAAA,EAAI;UAEhEX,EADF,CAAAS,cAAA,iBAAkE,gBACtD;UAAAT,EAAA,CAAAU,MAAA,YAAI;UAAAV,EAAA,CAAAW,YAAA,EAAW;UACzBX,EAAA,CAAAU,MAAA,mCACF;UAIRV,EAJQ,CAAAW,YAAA,EAAS,EACL,EACW,EACV,EACP", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}