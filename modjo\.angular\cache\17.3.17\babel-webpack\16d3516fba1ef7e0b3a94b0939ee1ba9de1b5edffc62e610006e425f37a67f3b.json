{"ast": null, "code": "import { UserRole } from '../../../../core/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/common\";\nimport * as i2 from \"@angular/forms\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/icon\";\nimport * as i6 from \"@angular/material/input\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/menu\";\nimport * as i9 from \"@angular/material/select\";\nimport * as i10 from \"@angular/material/core\";\nimport * as i11 from \"@angular/material/table\";\nimport * as i12 from \"../../../../shared/pipes/time-ago.pipe\";\nconst _c0 = () => [\"name\", \"role\", \"city\", \"points\", \"status\", \"lastLogin\", \"actions\"];\nfunction UserManagementComponent_mat_option_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const role_r1 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵproperty(\"value\", role_r1);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getRoleDisplayName(role_r1), \" \");\n  }\n}\nfunction UserManagementComponent_mat_option_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 28);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const city_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", city_r3);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(city_r3);\n  }\n}\nfunction UserManagementComponent_th_35_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 29);\n    i0.ɵɵtext(1, \"Nom\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_td_36_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(user_r4.organizationName);\n  }\n}\nfunction UserManagementComponent_td_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 30)(1, \"div\", 31)(2, \"strong\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 32);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(6, UserManagementComponent_td_36_div_6_Template, 2, 1, \"div\", 33);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r4 = ctx.$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r4.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(user_r4.email);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", user_r4.organizationName);\n  }\n}\nfunction UserManagementComponent_th_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 29);\n    i0.ɵɵtext(1, \"R\\u00F4le\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_td_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 30)(1, \"span\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.getRoleColor(user_r5.role) + \"20\")(\"color\", ctx_r1.getRoleColor(user_r5.role));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getRoleDisplayName(user_r5.role), \" \");\n  }\n}\nfunction UserManagementComponent_th_41_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 29);\n    i0.ɵɵtext(1, \"Ville\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_td_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 30);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r6 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(user_r6.city);\n  }\n}\nfunction UserManagementComponent_th_44_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 29);\n    i0.ɵɵtext(1, \"Points\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_td_45_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r7 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(user_r7.points);\n  }\n}\nfunction UserManagementComponent_td_45_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"-\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_td_45_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 30);\n    i0.ɵɵtemplate(1, UserManagementComponent_td_45_span_1_Template, 2, 1, \"span\", 36)(2, UserManagementComponent_td_45_span_2_Template, 2, 0, \"span\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", user_r7.role === \"user\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", user_r7.role !== \"user\");\n  }\n}\nfunction UserManagementComponent_th_47_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 29);\n    i0.ɵɵtext(1, \"Statut\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_td_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 30)(1, \"span\", 37);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const user_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.getStatusColor(user_r8.isActive) + \"20\")(\"color\", ctx_r1.getStatusColor(user_r8.isActive));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusText(user_r8.isActive), \" \");\n  }\n}\nfunction UserManagementComponent_th_50_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 29);\n    i0.ɵɵtext(1, \"Derni\\u00E8re connexion\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_td_51_span_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵpipe(2, \"timeAgo\");\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r9 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(2, 1, user_r9.lastLoginAt));\n  }\n}\nfunction UserManagementComponent_td_51_span_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Jamais\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_td_51_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"td\", 30);\n    i0.ɵɵtemplate(1, UserManagementComponent_td_51_span_1_Template, 3, 3, \"span\", 36)(2, UserManagementComponent_td_51_span_2_Template, 2, 0, \"span\", 36);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r9 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", user_r9.lastLoginAt);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !user_r9.lastLoginAt);\n  }\n}\nfunction UserManagementComponent_th_53_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"th\", 29);\n    i0.ɵɵtext(1, \"Actions\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction UserManagementComponent_td_54_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"td\", 30)(1, \"button\", 38)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"more_vert\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-menu\", null, 0)(6, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_td_54_Template_button_click_6_listener() {\n      const user_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editUser(user_r11));\n    });\n    i0.ɵɵelementStart(7, \"mat-icon\");\n    i0.ɵɵtext(8, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(9, \" Modifier \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_td_54_Template_button_click_10_listener() {\n      const user_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleUserStatus(user_r11));\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function UserManagementComponent_td_54_Template_button_click_14_listener() {\n      const user_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.deleteUser(user_r11));\n    });\n    i0.ɵɵelementStart(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"delete\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Supprimer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const user_r11 = ctx.$implicit;\n    const userMenu_r12 = i0.ɵɵreference(5);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r12);\n    i0.ɵɵadvance(11);\n    i0.ɵɵtextInterpolate(user_r11.isActive ? \"block\" : \"check_circle\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", user_r11.isActive ? \"D\\u00E9sactiver\" : \"Activer\", \" \");\n  }\n}\nfunction UserManagementComponent_tr_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 41);\n  }\n}\nfunction UserManagementComponent_tr_56_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"tr\", 42);\n  }\n}\nexport class UserManagementComponent {\n  constructor() {\n    this.users = [];\n    this.filteredUsers = [];\n    this.searchTerm = '';\n    this.selectedRole = 'all';\n    this.selectedCity = 'all';\n    this.userRoles = Object.values(UserRole);\n    this.cities = ['Monastir', 'Sousse'];\n    // Pagination\n    this.pageSize = 10;\n    this.currentPage = 0;\n    this.totalUsers = 0;\n  }\n  ngOnInit() {\n    this.loadUsers();\n  }\n  loadUsers() {\n    // Simulate user data\n    this.users = [{\n      id: '1',\n      email: '<EMAIL>',\n      name: 'Ahmed Ben Ali',\n      role: UserRole.USER,\n      city: 'Monastir',\n      points: 245,\n      isActive: true,\n      lastLoginAt: new Date(Date.now() - 1000 * 60 * 30),\n      createdAt: new Date('2024-01-15'),\n      totalRedemptions: 5\n    }, {\n      id: '2',\n      email: '<EMAIL>',\n      name: 'Fatma Trabelsi',\n      role: UserRole.VALIDATOR,\n      city: 'Sousse',\n      points: 0,\n      isActive: true,\n      lastLoginAt: new Date(Date.now() - 1000 * 60 * 60 * 2),\n      createdAt: new Date('2024-01-10'),\n      totalValidations: 89,\n      organizationName: 'École Primaire Sousse'\n    }, {\n      id: '3',\n      email: '<EMAIL>',\n      name: 'Mohamed Gharbi',\n      role: UserRole.PARTNER,\n      city: 'Monastir',\n      points: 0,\n      isActive: true,\n      lastLoginAt: new Date(Date.now() - 1000 * 60 * 60 * 5),\n      createdAt: new Date('2024-01-08'),\n      organizationName: 'Café des Nattes'\n    }, {\n      id: '4',\n      email: '<EMAIL>',\n      name: 'Leila Mansouri',\n      role: UserRole.PROVIDER,\n      city: 'Sousse',\n      points: 0,\n      isActive: true,\n      lastLoginAt: new Date(Date.now() - 1000 * 60 * 60 * 24),\n      createdAt: new Date('2024-01-05'),\n      organizationName: 'Club de Football Sousse'\n    }, {\n      id: '5',\n      email: '<EMAIL>',\n      name: 'Karim Bouazizi',\n      role: UserRole.USER,\n      city: 'Monastir',\n      points: 156,\n      isActive: false,\n      lastLoginAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7),\n      createdAt: new Date('2024-01-03'),\n      totalRedemptions: 2\n    }];\n    this.totalUsers = this.users.length;\n    this.applyFilters();\n  }\n  applyFilters() {\n    this.filteredUsers = this.users.filter(user => {\n      const matchesSearch = user.name.toLowerCase().includes(this.searchTerm.toLowerCase()) || user.email.toLowerCase().includes(this.searchTerm.toLowerCase()) || user.organizationName && user.organizationName.toLowerCase().includes(this.searchTerm.toLowerCase());\n      const matchesRole = this.selectedRole === 'all' || user.role === this.selectedRole;\n      const matchesCity = this.selectedCity === 'all' || user.city === this.selectedCity;\n      return matchesSearch && matchesRole && matchesCity;\n    });\n  }\n  onSearchChange() {\n    this.currentPage = 0;\n    this.applyFilters();\n  }\n  onRoleChange() {\n    this.currentPage = 0;\n    this.applyFilters();\n  }\n  onCityChange() {\n    this.currentPage = 0;\n    this.applyFilters();\n  }\n  getRoleDisplayName(role) {\n    const roleNames = {\n      [UserRole.USER]: 'Utilisateur',\n      [UserRole.VALIDATOR]: 'Validateur',\n      [UserRole.PARTNER]: 'Partenaire',\n      [UserRole.PROVIDER]: 'Prestataire',\n      [UserRole.ADMIN]: 'Administrateur'\n    };\n    return roleNames[role];\n  }\n  getRoleColor(role) {\n    const colors = {\n      [UserRole.USER]: '#4CAF50',\n      [UserRole.VALIDATOR]: '#2196F3',\n      [UserRole.PARTNER]: '#FF9800',\n      [UserRole.PROVIDER]: '#9C27B0',\n      [UserRole.ADMIN]: '#F44336'\n    };\n    return colors[role];\n  }\n  getStatusColor(isActive) {\n    return isActive ? '#4CAF50' : '#F44336';\n  }\n  getStatusText(isActive) {\n    return isActive ? 'Actif' : 'Inactif';\n  }\n  editUser(user) {\n    console.log('Edit user:', user);\n    // TODO: Open edit dialog\n  }\n  toggleUserStatus(user) {\n    user.isActive = !user.isActive;\n    console.log('Toggle user status:', user);\n    // TODO: Update user status in backend\n  }\n  deleteUser(user) {\n    console.log('Delete user:', user);\n    // TODO: Show confirmation dialog and delete user\n  }\n  addUser() {\n    console.log('Add new user');\n    // TODO: Open add user dialog\n  }\n  exportUsers() {\n    console.log('Export users');\n    // TODO: Export users to CSV/Excel\n  }\n  getPaginatedUsers() {\n    const startIndex = this.currentPage * this.pageSize;\n    return this.filteredUsers.slice(startIndex, startIndex + this.pageSize);\n  }\n  getTotalPages() {\n    return Math.ceil(this.filteredUsers.length / this.pageSize);\n  }\n  nextPage() {\n    if (this.currentPage < this.getTotalPages() - 1) {\n      this.currentPage++;\n    }\n  }\n  previousPage() {\n    if (this.currentPage > 0) {\n      this.currentPage--;\n    }\n  }\n  goToPage(page) {\n    this.currentPage = page;\n  }\n  getDisplayEnd() {\n    return Math.min((this.currentPage + 1) * this.pageSize, this.filteredUsers.length);\n  }\n  static {\n    this.ɵfac = function UserManagementComponent_Factory(t) {\n      return new (t || UserManagementComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: UserManagementComponent,\n      selectors: [[\"app-user-management\"]],\n      decls: 69,\n      vars: 17,\n      consts: [[\"userMenu\", \"matMenu\"], [1, \"user-management-container\"], [1, \"filters-section\"], [\"matInput\", \"\", \"placeholder\", \"Nom, email, organisation...\", 3, \"ngModelChange\", \"input\", \"ngModel\"], [\"matSuffix\", \"\"], [3, \"valueChange\", \"selectionChange\", \"value\"], [\"value\", \"all\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"users-table-card\"], [1, \"table-container\"], [\"mat-table\", \"\", 1, \"users-table\", 3, \"dataSource\"], [\"matColumnDef\", \"name\"], [\"mat-header-cell\", \"\", 4, \"matHeaderCellDef\"], [\"mat-cell\", \"\", 4, \"matCellDef\"], [\"matColumnDef\", \"role\"], [\"matColumnDef\", \"city\"], [\"matColumnDef\", \"points\"], [\"matColumnDef\", \"status\"], [\"matColumnDef\", \"lastLogin\"], [\"matColumnDef\", \"actions\"], [\"mat-header-row\", \"\", 4, \"matHeaderRowDef\"], [\"mat-row\", \"\", 4, \"matRowDef\", \"matRowDefColumns\"], [1, \"pagination-container\"], [1, \"pagination-info\"], [1, \"pagination-controls\"], [\"mat-icon-button\", \"\", 3, \"click\", \"disabled\"], [1, \"page-info\"], [3, \"value\"], [\"mat-header-cell\", \"\"], [\"mat-cell\", \"\"], [1, \"user-info\"], [1, \"user-email\"], [\"class\", \"user-org\", 4, \"ngIf\"], [1, \"user-org\"], [1, \"role-badge\"], [4, \"ngIf\"], [1, \"status-badge\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [\"mat-menu-item\", \"\", 3, \"click\"], [\"mat-menu-item\", \"\", 1, \"delete-action\", 3, \"click\"], [\"mat-header-row\", \"\"], [\"mat-row\", \"\"]],\n      template: function UserManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"h2\");\n          i0.ɵɵtext(2, \"\\uD83E\\uDDD1\\u200D\\uD83D\\uDCBC Gestion des Utilisateurs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"Ajouter, modifier, supprimer des utilisateurs et g\\u00E9rer leurs r\\u00F4les\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 2)(6, \"mat-form-field\")(7, \"mat-label\");\n          i0.ɵɵtext(8, \"Rechercher\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(9, \"input\", 3);\n          i0.ɵɵtwoWayListener(\"ngModelChange\", function UserManagementComponent_Template_input_ngModelChange_9_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.searchTerm, $event) || (ctx.searchTerm = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"input\", function UserManagementComponent_Template_input_input_9_listener() {\n            return ctx.onSearchChange();\n          });\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(10, \"mat-icon\", 4);\n          i0.ɵɵtext(11, \"search\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(12, \"mat-form-field\")(13, \"mat-label\");\n          i0.ɵɵtext(14, \"R\\u00F4le\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"mat-select\", 5);\n          i0.ɵɵtwoWayListener(\"valueChange\", function UserManagementComponent_Template_mat_select_valueChange_15_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedRole, $event) || (ctx.selectedRole = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function UserManagementComponent_Template_mat_select_selectionChange_15_listener() {\n            return ctx.onRoleChange();\n          });\n          i0.ɵɵelementStart(16, \"mat-option\", 6);\n          i0.ɵɵtext(17, \"Tous les r\\u00F4les\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(18, UserManagementComponent_mat_option_18_Template, 2, 2, \"mat-option\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"mat-form-field\")(20, \"mat-label\");\n          i0.ɵɵtext(21, \"Ville\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-select\", 5);\n          i0.ɵɵtwoWayListener(\"valueChange\", function UserManagementComponent_Template_mat_select_valueChange_22_listener($event) {\n            i0.ɵɵtwoWayBindingSet(ctx.selectedCity, $event) || (ctx.selectedCity = $event);\n            return $event;\n          });\n          i0.ɵɵlistener(\"selectionChange\", function UserManagementComponent_Template_mat_select_selectionChange_22_listener() {\n            return ctx.onCityChange();\n          });\n          i0.ɵɵelementStart(23, \"mat-option\", 6);\n          i0.ɵɵtext(24, \"Toutes les villes\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(25, UserManagementComponent_mat_option_25_Template, 2, 2, \"mat-option\", 7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"button\", 8);\n          i0.ɵɵlistener(\"click\", function UserManagementComponent_Template_button_click_26_listener() {\n            return ctx.addUser();\n          });\n          i0.ɵɵelementStart(27, \"mat-icon\");\n          i0.ɵɵtext(28, \"person_add\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(29, \" Ajouter Utilisateur \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(30, \"mat-card\", 9)(31, \"mat-card-content\")(32, \"div\", 10)(33, \"table\", 11);\n          i0.ɵɵelementContainerStart(34, 12);\n          i0.ɵɵtemplate(35, UserManagementComponent_th_35_Template, 2, 0, \"th\", 13)(36, UserManagementComponent_td_36_Template, 7, 3, \"td\", 14);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(37, 15);\n          i0.ɵɵtemplate(38, UserManagementComponent_th_38_Template, 2, 0, \"th\", 13)(39, UserManagementComponent_td_39_Template, 3, 5, \"td\", 14);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(40, 16);\n          i0.ɵɵtemplate(41, UserManagementComponent_th_41_Template, 2, 0, \"th\", 13)(42, UserManagementComponent_td_42_Template, 2, 1, \"td\", 14);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(43, 17);\n          i0.ɵɵtemplate(44, UserManagementComponent_th_44_Template, 2, 0, \"th\", 13)(45, UserManagementComponent_td_45_Template, 3, 2, \"td\", 14);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(46, 18);\n          i0.ɵɵtemplate(47, UserManagementComponent_th_47_Template, 2, 0, \"th\", 13)(48, UserManagementComponent_td_48_Template, 3, 5, \"td\", 14);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(49, 19);\n          i0.ɵɵtemplate(50, UserManagementComponent_th_50_Template, 2, 0, \"th\", 13)(51, UserManagementComponent_td_51_Template, 3, 2, \"td\", 14);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵelementContainerStart(52, 20);\n          i0.ɵɵtemplate(53, UserManagementComponent_th_53_Template, 2, 0, \"th\", 13)(54, UserManagementComponent_td_54_Template, 18, 3, \"td\", 14);\n          i0.ɵɵelementContainerEnd();\n          i0.ɵɵtemplate(55, UserManagementComponent_tr_55_Template, 1, 0, \"tr\", 21)(56, UserManagementComponent_tr_56_Template, 1, 0, \"tr\", 22);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(57, \"div\", 23)(58, \"div\", 24);\n          i0.ɵɵtext(59);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(60, \"div\", 25)(61, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function UserManagementComponent_Template_button_click_61_listener() {\n            return ctx.previousPage();\n          });\n          i0.ɵɵelementStart(62, \"mat-icon\");\n          i0.ɵɵtext(63, \"chevron_left\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(64, \"span\", 27);\n          i0.ɵɵtext(65);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(66, \"button\", 26);\n          i0.ɵɵlistener(\"click\", function UserManagementComponent_Template_button_click_66_listener() {\n            return ctx.nextPage();\n          });\n          i0.ɵɵelementStart(67, \"mat-icon\");\n          i0.ɵɵtext(68, \"chevron_right\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(9);\n          i0.ɵɵtwoWayProperty(\"ngModel\", ctx.searchTerm);\n          i0.ɵɵadvance(6);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectedRole);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.userRoles);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtwoWayProperty(\"value\", ctx.selectedCity);\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"ngForOf\", ctx.cities);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"dataSource\", ctx.getPaginatedUsers());\n          i0.ɵɵadvance(22);\n          i0.ɵɵproperty(\"matHeaderRowDef\", i0.ɵɵpureFunction0(15, _c0));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"matRowDefColumns\", i0.ɵɵpureFunction0(16, _c0));\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate3(\" Affichage \", ctx.currentPage * ctx.pageSize + 1, \" - \", ctx.getDisplayEnd(), \" sur \", ctx.filteredUsers.length, \" utilisateurs \");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage === 0);\n          i0.ɵɵadvance(4);\n          i0.ɵɵtextInterpolate2(\"\", ctx.currentPage + 1, \" / \", ctx.getTotalPages(), \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.currentPage >= ctx.getTotalPages() - 1);\n        }\n      },\n      dependencies: [i1.NgForOf, i1.NgIf, i2.DefaultValueAccessor, i2.NgControlStatus, i2.NgModel, i3.MatButton, i3.MatIconButton, i4.MatCard, i4.MatCardContent, i5.MatIcon, i6.MatInput, i7.MatFormField, i7.MatLabel, i7.MatSuffix, i8.MatMenu, i8.MatMenuItem, i8.MatMenuTrigger, i9.MatSelect, i10.MatOption, i11.MatTable, i11.MatHeaderCellDef, i11.MatHeaderRowDef, i11.MatColumnDef, i11.MatCellDef, i11.MatRowDef, i11.MatHeaderCell, i11.MatCell, i11.MatHeaderRow, i11.MatRow, i12.TimeAgoPipe],\n      styles: [\".user-management-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n}\\n\\n.user-management-container[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #2d3748;\\n  font-size: 2rem;\\n  font-weight: 700;\\n}\\n\\n.user-management-container[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 32px 0;\\n  color: #718096;\\n  font-size: 1.1rem;\\n}\\n\\n.filters-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  margin-bottom: 24px;\\n  align-items: center;\\n  flex-wrap: wrap;\\n}\\n\\n.filters-section[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n  min-width: 200px;\\n}\\n\\n.users-table-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.table-container[_ngcontent-%COMP%] {\\n  overflow-x: auto;\\n}\\n\\n.users-table[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.user-info[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n  display: block;\\n  color: #2d3748;\\n  font-weight: 600;\\n}\\n\\n.user-email[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: 0.9rem;\\n  margin-top: 2px;\\n}\\n\\n.user-org[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  font-size: 0.8rem;\\n  font-style: italic;\\n  margin-top: 2px;\\n}\\n\\n.role-badge[_ngcontent-%COMP%], .status-badge[_ngcontent-%COMP%] {\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n.delete-action[_ngcontent-%COMP%] {\\n  color: #F44336 !important;\\n}\\n\\n.pagination-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-top: 16px;\\n  padding-top: 16px;\\n  border-top: 1px solid #e2e8f0;\\n}\\n\\n.pagination-info[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n\\n.pagination-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n}\\n\\n.page-info[_ngcontent-%COMP%] {\\n  color: #4a5568;\\n  font-weight: 500;\\n  min-width: 60px;\\n  text-align: center;\\n}\\n\\n@media (max-width: 768px) {\\n  .filters-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: stretch;\\n  }\\n  \\n  .filters-section[_ngcontent-%COMP%]   mat-form-field[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n  \\n  .pagination-container[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 12px;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInVzZXItbWFuYWdlbWVudC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsYUFBYTtBQUNmOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLGNBQWM7RUFDZCxlQUFlO0VBQ2YsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLGNBQWM7RUFDZCxpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsU0FBUztFQUNULG1CQUFtQjtFQUNuQixtQkFBbUI7RUFDbkIsZUFBZTtBQUNqQjs7QUFFQTtFQUNFLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQix5Q0FBeUM7QUFDM0M7O0FBRUE7RUFDRSxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxXQUFXO0FBQ2I7O0FBRUE7RUFDRSxjQUFjO0VBQ2QsY0FBYztFQUNkLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGNBQWM7RUFDZCxpQkFBaUI7RUFDakIsZUFBZTtBQUNqQjs7QUFFQTtFQUNFLGNBQWM7RUFDZCxpQkFBaUI7RUFDakIsa0JBQWtCO0VBQ2xCLGVBQWU7QUFDakI7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsbUJBQW1CO0VBQ25CLGlCQUFpQjtFQUNqQixnQkFBZ0I7RUFDaEIseUJBQXlCO0VBQ3pCLHFCQUFxQjtBQUN2Qjs7QUFFQTtFQUNFLHlCQUF5QjtBQUMzQjs7QUFFQTtFQUNFLGFBQWE7RUFDYiw4QkFBOEI7RUFDOUIsbUJBQW1CO0VBQ25CLGdCQUFnQjtFQUNoQixpQkFBaUI7RUFDakIsNkJBQTZCO0FBQy9COztBQUVBO0VBQ0UsY0FBYztFQUNkLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsUUFBUTtBQUNWOztBQUVBO0VBQ0UsY0FBYztFQUNkLGdCQUFnQjtFQUNoQixlQUFlO0VBQ2Ysa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0U7SUFDRSxzQkFBc0I7SUFDdEIsb0JBQW9CO0VBQ3RCOztFQUVBO0lBQ0UsZUFBZTtFQUNqQjs7RUFFQTtJQUNFLHNCQUFzQjtJQUN0QixTQUFTO0VBQ1g7QUFDRiIsImZpbGUiOiJ1c2VyLW1hbmFnZW1lbnQuY29tcG9uZW50LmNzcyIsInNvdXJjZXNDb250ZW50IjpbIi51c2VyLW1hbmFnZW1lbnQtY29udGFpbmVyIHtcbiAgcGFkZGluZzogMjBweDtcbn1cblxuLnVzZXItbWFuYWdlbWVudC1jb250YWluZXIgaDIge1xuICBtYXJnaW46IDAgMCA4cHggMDtcbiAgY29sb3I6ICMyZDM3NDg7XG4gIGZvbnQtc2l6ZTogMnJlbTtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbn1cblxuLnVzZXItbWFuYWdlbWVudC1jb250YWluZXIgcCB7XG4gIG1hcmdpbjogMCAwIDMycHggMDtcbiAgY29sb3I6ICM3MTgwOTY7XG4gIGZvbnQtc2l6ZTogMS4xcmVtO1xufVxuXG4uZmlsdGVycy1zZWN0aW9uIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZ2FwOiAxNnB4O1xuICBtYXJnaW4tYm90dG9tOiAyNHB4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBmbGV4LXdyYXA6IHdyYXA7XG59XG5cbi5maWx0ZXJzLXNlY3Rpb24gbWF0LWZvcm0tZmllbGQge1xuICBtaW4td2lkdGg6IDIwMHB4O1xufVxuXG4udXNlcnMtdGFibGUtY2FyZCB7XG4gIGJvcmRlci1yYWRpdXM6IDE2cHg7XG4gIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xufVxuXG4udGFibGUtY29udGFpbmVyIHtcbiAgb3ZlcmZsb3cteDogYXV0bztcbn1cblxuLnVzZXJzLXRhYmxlIHtcbiAgd2lkdGg6IDEwMCU7XG59XG5cbi51c2VyLWluZm8gc3Ryb25nIHtcbiAgZGlzcGxheTogYmxvY2s7XG4gIGNvbG9yOiAjMmQzNzQ4O1xuICBmb250LXdlaWdodDogNjAwO1xufVxuXG4udXNlci1lbWFpbCB7XG4gIGNvbG9yOiAjNzE4MDk2O1xuICBmb250LXNpemU6IDAuOXJlbTtcbiAgbWFyZ2luLXRvcDogMnB4O1xufVxuXG4udXNlci1vcmcge1xuICBjb2xvcjogIzRhNTU2ODtcbiAgZm9udC1zaXplOiAwLjhyZW07XG4gIGZvbnQtc3R5bGU6IGl0YWxpYztcbiAgbWFyZ2luLXRvcDogMnB4O1xufVxuXG4ucm9sZS1iYWRnZSwgLnN0YXR1cy1iYWRnZSB7XG4gIHBhZGRpbmc6IDRweCAxMnB4O1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBmb250LXNpemU6IDAuOHJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcbiAgbGV0dGVyLXNwYWNpbmc6IDAuNXB4O1xufVxuXG4uZGVsZXRlLWFjdGlvbiB7XG4gIGNvbG9yOiAjRjQ0MzM2ICFpbXBvcnRhbnQ7XG59XG5cbi5wYWdpbmF0aW9uLWNvbnRhaW5lciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgbWFyZ2luLXRvcDogMTZweDtcbiAgcGFkZGluZy10b3A6IDE2cHg7XG4gIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTJlOGYwO1xufVxuXG4ucGFnaW5hdGlvbi1pbmZvIHtcbiAgY29sb3I6ICM3MTgwOTY7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xufVxuXG4ucGFnaW5hdGlvbi1jb250cm9scyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogOHB4O1xufVxuXG4ucGFnZS1pbmZvIHtcbiAgY29sb3I6ICM0YTU1Njg7XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIG1pbi13aWR0aDogNjBweDtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xufVxuXG5AbWVkaWEgKG1heC13aWR0aDogNzY4cHgpIHtcbiAgLmZpbHRlcnMtc2VjdGlvbiB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBhbGlnbi1pdGVtczogc3RyZXRjaDtcbiAgfVxuICBcbiAgLmZpbHRlcnMtc2VjdGlvbiBtYXQtZm9ybS1maWVsZCB7XG4gICAgbWluLXdpZHRoOiBhdXRvO1xuICB9XG4gIFxuICAucGFnaW5hdGlvbi1jb250YWluZXIge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgZ2FwOiAxMnB4O1xuICB9XG59XG4iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["UserRole", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "role_r1", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "getRoleDisplayName", "city_r3", "ɵɵtextInterpolate", "user_r4", "organizationName", "ɵɵtemplate", "UserManagementComponent_td_36_div_6_Template", "name", "email", "ɵɵstyleProp", "getRoleColor", "user_r5", "role", "user_r6", "city", "user_r7", "points", "UserManagementComponent_td_45_span_1_Template", "UserManagementComponent_td_45_span_2_Template", "getStatusColor", "user_r8", "isActive", "getStatusText", "ɵɵpipeBind1", "user_r9", "lastLoginAt", "UserManagementComponent_td_51_span_1_Template", "UserManagementComponent_td_51_span_2_Template", "ɵɵlistener", "UserManagementComponent_td_54_Template_button_click_6_listener", "user_r11", "ɵɵrestoreView", "_r10", "$implicit", "ɵɵnextContext", "ɵɵresetView", "editUser", "UserManagementComponent_td_54_Template_button_click_10_listener", "toggleUserStatus", "UserManagementComponent_td_54_Template_button_click_14_listener", "deleteUser", "userMenu_r12", "ɵɵelement", "UserManagementComponent", "constructor", "users", "filteredUsers", "searchTerm", "selectedR<PERSON>", "selectedCity", "userRoles", "Object", "values", "cities", "pageSize", "currentPage", "totalUsers", "ngOnInit", "loadUsers", "id", "USER", "Date", "now", "createdAt", "totalRedemptions", "VALIDATOR", "totalValidations", "PARTNER", "PROVIDER", "length", "applyFilters", "filter", "user", "matchesSearch", "toLowerCase", "includes", "matchesRole", "matchesCity", "onSearchChange", "onRoleChange", "onCityChange", "roleNames", "ADMIN", "colors", "console", "log", "addUser", "exportUsers", "getPaginatedUsers", "startIndex", "slice", "getTotalPages", "Math", "ceil", "nextPage", "previousPage", "goToPage", "page", "getDisplayEnd", "min", "selectors", "decls", "vars", "consts", "template", "UserManagementComponent_Template", "rf", "ctx", "ɵɵtwoWayListener", "UserManagementComponent_Template_input_ngModelChange_9_listener", "$event", "ɵɵtwoWayBindingSet", "UserManagementComponent_Template_input_input_9_listener", "UserManagementComponent_Template_mat_select_valueChange_15_listener", "UserManagementComponent_Template_mat_select_selectionChange_15_listener", "UserManagementComponent_mat_option_18_Template", "UserManagementComponent_Template_mat_select_valueChange_22_listener", "UserManagementComponent_Template_mat_select_selectionChange_22_listener", "UserManagementComponent_mat_option_25_Template", "UserManagementComponent_Template_button_click_26_listener", "ɵɵelementContainerStart", "UserManagementComponent_th_35_Template", "UserManagementComponent_td_36_Template", "UserManagementComponent_th_38_Template", "UserManagementComponent_td_39_Template", "UserManagementComponent_th_41_Template", "UserManagementComponent_td_42_Template", "UserManagementComponent_th_44_Template", "UserManagementComponent_td_45_Template", "UserManagementComponent_th_47_Template", "UserManagementComponent_td_48_Template", "UserManagementComponent_th_50_Template", "UserManagementComponent_td_51_Template", "UserManagementComponent_th_53_Template", "UserManagementComponent_td_54_Template", "UserManagementComponent_tr_55_Template", "UserManagementComponent_tr_56_Template", "UserManagementComponent_Template_button_click_61_listener", "UserManagementComponent_Template_button_click_66_listener", "ɵɵtwoWayProperty", "ɵɵpureFunction0", "_c0", "ɵɵtextInterpolate3", "ɵɵtextInterpolate2"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\admin-dashboard\\components\\user-management\\user-management.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\admin-dashboard\\components\\user-management\\user-management.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { UserRole, UserManagement, CreateUserRequest } from '../../../../core/models';\n\n@Component({\n  selector: 'app-user-management',\n  templateUrl: './user-management.component.html',\n  styleUrls: ['./user-management.component.css']\n})\nexport class UserManagementComponent implements OnInit {\n  users: UserManagement[] = [];\n  filteredUsers: UserManagement[] = [];\n  searchTerm = '';\n  selectedRole: UserRole | 'all' = 'all';\n  selectedCity: 'Monastir' | 'Sousse' | 'all' = 'all';\n  \n  userRoles = Object.values(UserRole);\n  cities = ['Monastir', 'Sousse'];\n  \n  // Pagination\n  pageSize = 10;\n  currentPage = 0;\n  totalUsers = 0;\n\n  constructor() {}\n\n  ngOnInit(): void {\n    this.loadUsers();\n  }\n\n  private loadUsers(): void {\n    // Simulate user data\n    this.users = [\n      {\n        id: '1',\n        email: '<EMAIL>',\n        name: '<PERSON>',\n        role: UserRole.USER,\n        city: 'Monastir',\n        points: 245,\n        isActive: true,\n        lastLoginAt: new Date(Date.now() - 1000 * 60 * 30),\n        createdAt: new Date('2024-01-15'),\n        totalRedemptions: 5\n      },\n      {\n        id: '2',\n        email: '<EMAIL>',\n        name: 'Fatma Trabelsi',\n        role: UserRole.VALIDATOR,\n        city: 'Sousse',\n        points: 0,\n        isActive: true,\n        lastLoginAt: new Date(Date.now() - 1000 * 60 * 60 * 2),\n        createdAt: new Date('2024-01-10'),\n        totalValidations: 89,\n        organizationName: 'École Primaire Sousse'\n      },\n      {\n        id: '3',\n        email: '<EMAIL>',\n        name: 'Mohamed Gharbi',\n        role: UserRole.PARTNER,\n        city: 'Monastir',\n        points: 0,\n        isActive: true,\n        lastLoginAt: new Date(Date.now() - 1000 * 60 * 60 * 5),\n        createdAt: new Date('2024-01-08'),\n        organizationName: 'Café des Nattes'\n      },\n      {\n        id: '4',\n        email: '<EMAIL>',\n        name: 'Leila Mansouri',\n        role: UserRole.PROVIDER,\n        city: 'Sousse',\n        points: 0,\n        isActive: true,\n        lastLoginAt: new Date(Date.now() - 1000 * 60 * 60 * 24),\n        createdAt: new Date('2024-01-05'),\n        organizationName: 'Club de Football Sousse'\n      },\n      {\n        id: '5',\n        email: '<EMAIL>',\n        name: 'Karim Bouazizi',\n        role: UserRole.USER,\n        city: 'Monastir',\n        points: 156,\n        isActive: false,\n        lastLoginAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7),\n        createdAt: new Date('2024-01-03'),\n        totalRedemptions: 2\n      }\n    ];\n    \n    this.totalUsers = this.users.length;\n    this.applyFilters();\n  }\n\n  applyFilters(): void {\n    this.filteredUsers = this.users.filter(user => {\n      const matchesSearch = user.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\n                           user.email.toLowerCase().includes(this.searchTerm.toLowerCase()) ||\n                           (user.organizationName && user.organizationName.toLowerCase().includes(this.searchTerm.toLowerCase()));\n      \n      const matchesRole = this.selectedRole === 'all' || user.role === this.selectedRole;\n      const matchesCity = this.selectedCity === 'all' || user.city === this.selectedCity;\n      \n      return matchesSearch && matchesRole && matchesCity;\n    });\n  }\n\n  onSearchChange(): void {\n    this.currentPage = 0;\n    this.applyFilters();\n  }\n\n  onRoleChange(): void {\n    this.currentPage = 0;\n    this.applyFilters();\n  }\n\n  onCityChange(): void {\n    this.currentPage = 0;\n    this.applyFilters();\n  }\n\n  getRoleDisplayName(role: UserRole): string {\n    const roleNames = {\n      [UserRole.USER]: 'Utilisateur',\n      [UserRole.VALIDATOR]: 'Validateur',\n      [UserRole.PARTNER]: 'Partenaire',\n      [UserRole.PROVIDER]: 'Prestataire',\n      [UserRole.ADMIN]: 'Administrateur'\n    };\n    return roleNames[role];\n  }\n\n  getRoleColor(role: UserRole): string {\n    const colors = {\n      [UserRole.USER]: '#4CAF50',\n      [UserRole.VALIDATOR]: '#2196F3',\n      [UserRole.PARTNER]: '#FF9800',\n      [UserRole.PROVIDER]: '#9C27B0',\n      [UserRole.ADMIN]: '#F44336'\n    };\n    return colors[role];\n  }\n\n  getStatusColor(isActive: boolean): string {\n    return isActive ? '#4CAF50' : '#F44336';\n  }\n\n  getStatusText(isActive: boolean): string {\n    return isActive ? 'Actif' : 'Inactif';\n  }\n\n  editUser(user: UserManagement): void {\n    console.log('Edit user:', user);\n    // TODO: Open edit dialog\n  }\n\n  toggleUserStatus(user: UserManagement): void {\n    user.isActive = !user.isActive;\n    console.log('Toggle user status:', user);\n    // TODO: Update user status in backend\n  }\n\n  deleteUser(user: UserManagement): void {\n    console.log('Delete user:', user);\n    // TODO: Show confirmation dialog and delete user\n  }\n\n  addUser(): void {\n    console.log('Add new user');\n    // TODO: Open add user dialog\n  }\n\n  exportUsers(): void {\n    console.log('Export users');\n    // TODO: Export users to CSV/Excel\n  }\n\n  getPaginatedUsers(): UserManagement[] {\n    const startIndex = this.currentPage * this.pageSize;\n    return this.filteredUsers.slice(startIndex, startIndex + this.pageSize);\n  }\n\n  getTotalPages(): number {\n    return Math.ceil(this.filteredUsers.length / this.pageSize);\n  }\n\n  nextPage(): void {\n    if (this.currentPage < this.getTotalPages() - 1) {\n      this.currentPage++;\n    }\n  }\n\n  previousPage(): void {\n    if (this.currentPage > 0) {\n      this.currentPage--;\n    }\n  }\n\n  goToPage(page: number): void {\n    this.currentPage = page;\n  }\n\n  getDisplayEnd(): number {\n    return Math.min((this.currentPage + 1) * this.pageSize, this.filteredUsers.length);\n  }\n}\n", "<div class=\"user-management-container\">\n  <h2>🧑‍💼 Gestion des Utilisateurs</h2>\n  <p>Ajou<PERSON>, modifier, supprimer des utilisateurs et gérer leurs rôles</p>\n  \n  <!-- Filters and Search -->\n  <div class=\"filters-section\">\n    <mat-form-field>\n      <mat-label>Rechercher</mat-label>\n      <input matInput [(ngModel)]=\"searchTerm\" (input)=\"onSearchChange()\" placeholder=\"Nom, email, organisation...\">\n      <mat-icon matSuffix>search</mat-icon>\n    </mat-form-field>\n    \n    <mat-form-field>\n      <mat-label>Rôle</mat-label>\n      <mat-select [(value)]=\"selectedRole\" (selectionChange)=\"onRoleChange()\">\n        <mat-option value=\"all\">Tous les rôles</mat-option>\n        <mat-option *ngFor=\"let role of userRoles\" [value]=\"role\">\n          {{ getRoleDisplayName(role) }}\n        </mat-option>\n      </mat-select>\n    </mat-form-field>\n    \n    <mat-form-field>\n      <mat-label>Ville</mat-label>\n      <mat-select [(value)]=\"selectedCity\" (selectionChange)=\"onCityChange()\">\n        <mat-option value=\"all\">Toutes les villes</mat-option>\n        <mat-option *ngFor=\"let city of cities\" [value]=\"city\">{{ city }}</mat-option>\n      </mat-select>\n    </mat-form-field>\n    \n    <button mat-raised-button color=\"primary\" (click)=\"addUser()\">\n      <mat-icon>person_add</mat-icon>\n      Ajouter Utilisateur\n    </button>\n  </div>\n\n  <!-- Users Table -->\n  <mat-card class=\"users-table-card\">\n    <mat-card-content>\n      <div class=\"table-container\">\n        <table mat-table [dataSource]=\"getPaginatedUsers()\" class=\"users-table\">\n          <!-- Name Column -->\n          <ng-container matColumnDef=\"name\">\n            <th mat-header-cell *matHeaderCellDef>Nom</th>\n            <td mat-cell *matCellDef=\"let user\">\n              <div class=\"user-info\">\n                <strong>{{ user.name }}</strong>\n                <div class=\"user-email\">{{ user.email }}</div>\n                <div *ngIf=\"user.organizationName\" class=\"user-org\">{{ user.organizationName }}</div>\n              </div>\n            </td>\n          </ng-container>\n\n          <!-- Role Column -->\n          <ng-container matColumnDef=\"role\">\n            <th mat-header-cell *matHeaderCellDef>Rôle</th>\n            <td mat-cell *matCellDef=\"let user\">\n              <span class=\"role-badge\" [style.background-color]=\"getRoleColor(user.role) + '20'\" \n                    [style.color]=\"getRoleColor(user.role)\">\n                {{ getRoleDisplayName(user.role) }}\n              </span>\n            </td>\n          </ng-container>\n\n          <!-- City Column -->\n          <ng-container matColumnDef=\"city\">\n            <th mat-header-cell *matHeaderCellDef>Ville</th>\n            <td mat-cell *matCellDef=\"let user\">{{ user.city }}</td>\n          </ng-container>\n\n          <!-- Points Column -->\n          <ng-container matColumnDef=\"points\">\n            <th mat-header-cell *matHeaderCellDef>Points</th>\n            <td mat-cell *matCellDef=\"let user\">\n              <span *ngIf=\"user.role === 'user'\">{{ user.points }}</span>\n              <span *ngIf=\"user.role !== 'user'\">-</span>\n            </td>\n          </ng-container>\n\n          <!-- Status Column -->\n          <ng-container matColumnDef=\"status\">\n            <th mat-header-cell *matHeaderCellDef>Statut</th>\n            <td mat-cell *matCellDef=\"let user\">\n              <span class=\"status-badge\" [style.background-color]=\"getStatusColor(user.isActive) + '20'\" \n                    [style.color]=\"getStatusColor(user.isActive)\">\n                {{ getStatusText(user.isActive) }}\n              </span>\n            </td>\n          </ng-container>\n\n          <!-- Last Login Column -->\n          <ng-container matColumnDef=\"lastLogin\">\n            <th mat-header-cell *matHeaderCellDef>Dernière connexion</th>\n            <td mat-cell *matCellDef=\"let user\">\n              <span *ngIf=\"user.lastLoginAt\">{{ user.lastLoginAt | timeAgo }}</span>\n              <span *ngIf=\"!user.lastLoginAt\">Jamais</span>\n            </td>\n          </ng-container>\n\n          <!-- Actions Column -->\n          <ng-container matColumnDef=\"actions\">\n            <th mat-header-cell *matHeaderCellDef>Actions</th>\n            <td mat-cell *matCellDef=\"let user\">\n              <button mat-icon-button [matMenuTriggerFor]=\"userMenu\">\n                <mat-icon>more_vert</mat-icon>\n              </button>\n              <mat-menu #userMenu=\"matMenu\">\n                <button mat-menu-item (click)=\"editUser(user)\">\n                  <mat-icon>edit</mat-icon>\n                  Modifier\n                </button>\n                <button mat-menu-item (click)=\"toggleUserStatus(user)\">\n                  <mat-icon>{{ user.isActive ? 'block' : 'check_circle' }}</mat-icon>\n                  {{ user.isActive ? 'Désactiver' : 'Activer' }}\n                </button>\n                <button mat-menu-item (click)=\"deleteUser(user)\" class=\"delete-action\">\n                  <mat-icon>delete</mat-icon>\n                  Supprimer\n                </button>\n              </mat-menu>\n            </td>\n          </ng-container>\n\n          <tr mat-header-row *matHeaderRowDef=\"['name', 'role', 'city', 'points', 'status', 'lastLogin', 'actions']\"></tr>\n          <tr mat-row *matRowDef=\"let row; columns: ['name', 'role', 'city', 'points', 'status', 'lastLogin', 'actions'];\"></tr>\n        </table>\n      </div>\n\n      <!-- Pagination -->\n      <div class=\"pagination-container\">\n        <div class=\"pagination-info\">\n          Affichage {{ currentPage * pageSize + 1 }} - {{ getDisplayEnd() }}\n          sur {{ filteredUsers.length }} utilisateurs\n        </div>\n        <div class=\"pagination-controls\">\n          <button mat-icon-button [disabled]=\"currentPage === 0\" (click)=\"previousPage()\">\n            <mat-icon>chevron_left</mat-icon>\n          </button>\n          <span class=\"page-info\">{{ currentPage + 1 }} / {{ getTotalPages() }}</span>\n          <button mat-icon-button [disabled]=\"currentPage >= getTotalPages() - 1\" (click)=\"nextPage()\">\n            <mat-icon>chevron_right</mat-icon>\n          </button>\n        </div>\n      </div>\n    </mat-card-content>\n  </mat-card>\n</div>\n"], "mappings": "AACA,SAASA,QAAQ,QAA2C,yBAAyB;;;;;;;;;;;;;;;;;ICe7EC,EAAA,CAAAC,cAAA,qBAA0D;IACxDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;;IAF8BH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAc;IACvDL,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAAJ,OAAA,OACF;;;;;IAQAL,EAAA,CAAAC,cAAA,qBAAuD;IAAAD,EAAA,CAAAE,MAAA,GAAU;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAAtCH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAc;IAACV,EAAA,CAAAM,SAAA,EAAU;IAAVN,EAAA,CAAAW,iBAAA,CAAAD,OAAA,CAAU;;;;;IAiB7DV,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,UAAG;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAK1CH,EAAA,CAAAC,cAAA,cAAoD;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IAAjCH,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAW,iBAAA,CAAAC,OAAA,CAAAC,gBAAA,CAA2B;;;;;IAF/Eb,EAFJ,CAAAC,cAAA,aAAoC,cACX,aACb;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAChCH,EAAA,CAAAC,cAAA,cAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC9CH,EAAA,CAAAc,UAAA,IAAAC,4CAAA,kBAAoD;IAExDf,EADE,CAAAG,YAAA,EAAM,EACH;;;;IAJOH,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAW,iBAAA,CAAAC,OAAA,CAAAI,IAAA,CAAe;IACChB,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAW,iBAAA,CAAAC,OAAA,CAAAK,KAAA,CAAgB;IAClCjB,EAAA,CAAAM,SAAA,EAA2B;IAA3BN,EAAA,CAAAI,UAAA,SAAAQ,OAAA,CAAAC,gBAAA,CAA2B;;;;;IAOrCb,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,gBAAI;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE7CH,EADF,CAAAC,cAAA,aAAoC,eAEY;IAC5CD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;;;;;IAJsBH,EAAA,CAAAM,SAAA,EAAyD;IAC5EN,EADmB,CAAAkB,WAAA,qBAAAV,MAAA,CAAAW,YAAA,CAAAC,OAAA,CAAAC,IAAA,SAAyD,UAAAb,MAAA,CAAAW,YAAA,CAAAC,OAAA,CAAAC,IAAA,EACrC;IAC3CrB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAC,kBAAA,CAAAW,OAAA,CAAAC,IAAA,OACF;;;;;IAMFrB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAChDH,EAAA,CAAAC,cAAA,aAAoC;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;IAApBH,EAAA,CAAAM,SAAA,EAAe;IAAfN,EAAA,CAAAW,iBAAA,CAAAW,OAAA,CAAAC,IAAA,CAAe;;;;;IAKnDvB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/CH,EAAA,CAAAC,cAAA,WAAmC;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAxBH,EAAA,CAAAM,SAAA,EAAiB;IAAjBN,EAAA,CAAAW,iBAAA,CAAAa,OAAA,CAAAC,MAAA,CAAiB;;;;;IACpDzB,EAAA,CAAAC,cAAA,WAAmC;IAAAD,EAAA,CAAAE,MAAA,QAAC;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF7CH,EAAA,CAAAC,cAAA,aAAoC;IAElCD,EADA,CAAAc,UAAA,IAAAY,6CAAA,mBAAmC,IAAAC,6CAAA,mBACA;IACrC3B,EAAA,CAAAG,YAAA,EAAK;;;;IAFIH,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,SAAAoB,OAAA,CAAAH,IAAA,YAA0B;IAC1BrB,EAAA,CAAAM,SAAA,EAA0B;IAA1BN,EAAA,CAAAI,UAAA,SAAAoB,OAAA,CAAAH,IAAA,YAA0B;;;;;IAMnCrB,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE/CH,EADF,CAAAC,cAAA,aAAoC,eAEkB;IAClDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACJ;;;;;IAJwBH,EAAA,CAAAM,SAAA,EAA+D;IACpFN,EADqB,CAAAkB,WAAA,qBAAAV,MAAA,CAAAoB,cAAA,CAAAC,OAAA,CAAAC,QAAA,SAA+D,UAAAtB,MAAA,CAAAoB,cAAA,CAAAC,OAAA,CAAAC,QAAA,EACvC;IACjD9B,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAC,MAAA,CAAAuB,aAAA,CAAAF,OAAA,CAAAC,QAAA,OACF;;;;;IAMF9B,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,8BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;IAE3DH,EAAA,CAAAC,cAAA,WAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAgC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAvCH,EAAA,CAAAM,SAAA,EAAgC;IAAhCN,EAAA,CAAAW,iBAAA,CAAAX,EAAA,CAAAgC,WAAA,OAAAC,OAAA,CAAAC,WAAA,EAAgC;;;;;IAC/DlC,EAAA,CAAAC,cAAA,WAAgC;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IAF/CH,EAAA,CAAAC,cAAA,aAAoC;IAElCD,EADA,CAAAc,UAAA,IAAAqB,6CAAA,mBAA+B,IAAAC,6CAAA,mBACC;IAClCpC,EAAA,CAAAG,YAAA,EAAK;;;;IAFIH,EAAA,CAAAM,SAAA,EAAsB;IAAtBN,EAAA,CAAAI,UAAA,SAAA6B,OAAA,CAAAC,WAAA,CAAsB;IACtBlC,EAAA,CAAAM,SAAA,EAAuB;IAAvBN,EAAA,CAAAI,UAAA,UAAA6B,OAAA,CAAAC,WAAA,CAAuB;;;;;IAMhClC,EAAA,CAAAC,cAAA,aAAsC;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAK;;;;;;IAG9CH,EAFJ,CAAAC,cAAA,aAAoC,iBACqB,eAC3C;IAAAD,EAAA,CAAAE,MAAA,gBAAS;IACrBF,EADqB,CAAAG,YAAA,EAAW,EACvB;IAEPH,EADF,CAAAC,cAAA,wBAA8B,iBACmB;IAAzBD,EAAA,CAAAqC,UAAA,mBAAAC,+DAAA;MAAA,MAAAC,QAAA,GAAAvC,EAAA,CAAAwC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAlC,MAAA,GAAAR,EAAA,CAAA2C,aAAA;MAAA,OAAA3C,EAAA,CAAA4C,WAAA,CAASpC,MAAA,CAAAqC,QAAA,CAAAN,QAAA,CAAc;IAAA,EAAC;IAC5CvC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAuD;IAAjCD,EAAA,CAAAqC,UAAA,mBAAAS,gEAAA;MAAA,MAAAP,QAAA,GAAAvC,EAAA,CAAAwC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAlC,MAAA,GAAAR,EAAA,CAAA2C,aAAA;MAAA,OAAA3C,EAAA,CAAA4C,WAAA,CAASpC,MAAA,CAAAuC,gBAAA,CAAAR,QAAA,CAAsB;IAAA,EAAC;IACpDvC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,IAA8C;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnEH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAAuE;IAAjDD,EAAA,CAAAqC,UAAA,mBAAAW,gEAAA;MAAA,MAAAT,QAAA,GAAAvC,EAAA,CAAAwC,aAAA,CAAAC,IAAA,EAAAC,SAAA;MAAA,MAAAlC,MAAA,GAAAR,EAAA,CAAA2C,aAAA;MAAA,OAAA3C,EAAA,CAAA4C,WAAA,CAASpC,MAAA,CAAAyC,UAAA,CAAAV,QAAA,CAAgB;IAAA,EAAC;IAC9CvC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,mBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACA,EACR;;;;;IAjBqBH,EAAA,CAAAM,SAAA,EAA8B;IAA9BN,EAAA,CAAAI,UAAA,sBAAA8C,YAAA,CAA8B;IASxClD,EAAA,CAAAM,SAAA,IAA8C;IAA9CN,EAAA,CAAAW,iBAAA,CAAA4B,QAAA,CAAAT,QAAA,4BAA8C;IACxD9B,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAO,kBAAA,MAAAgC,QAAA,CAAAT,QAAA,sCACF;;;;;IASN9B,EAAA,CAAAmD,SAAA,aAAgH;;;;;IAChHnD,EAAA,CAAAmD,SAAA,aAAsH;;;ADpHhI,OAAM,MAAOC,uBAAuB;EAelCC,YAAA;IAdA,KAAAC,KAAK,GAAqB,EAAE;IAC5B,KAAAC,aAAa,GAAqB,EAAE;IACpC,KAAAC,UAAU,GAAG,EAAE;IACf,KAAAC,YAAY,GAAqB,KAAK;IACtC,KAAAC,YAAY,GAAkC,KAAK;IAEnD,KAAAC,SAAS,GAAGC,MAAM,CAACC,MAAM,CAAC9D,QAAQ,CAAC;IACnC,KAAA+D,MAAM,GAAG,CAAC,UAAU,EAAE,QAAQ,CAAC;IAE/B;IACA,KAAAC,QAAQ,GAAG,EAAE;IACb,KAAAC,WAAW,GAAG,CAAC;IACf,KAAAC,UAAU,GAAG,CAAC;EAEC;EAEfC,QAAQA,CAAA;IACN,IAAI,CAACC,SAAS,EAAE;EAClB;EAEQA,SAASA,CAAA;IACf;IACA,IAAI,CAACb,KAAK,GAAG,CACX;MACEc,EAAE,EAAE,GAAG;MACPnD,KAAK,EAAE,wBAAwB;MAC/BD,IAAI,EAAE,eAAe;MACrBK,IAAI,EAAEtB,QAAQ,CAACsE,IAAI;MACnB9C,IAAI,EAAE,UAAU;MAChBE,MAAM,EAAE,GAAG;MACXK,QAAQ,EAAE,IAAI;MACdI,WAAW,EAAE,IAAIoC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAClDC,SAAS,EAAE,IAAIF,IAAI,CAAC,YAAY,CAAC;MACjCG,gBAAgB,EAAE;KACnB,EACD;MACEL,EAAE,EAAE,GAAG;MACPnD,KAAK,EAAE,0BAA0B;MACjCD,IAAI,EAAE,gBAAgB;MACtBK,IAAI,EAAEtB,QAAQ,CAAC2E,SAAS;MACxBnD,IAAI,EAAE,QAAQ;MACdE,MAAM,EAAE,CAAC;MACTK,QAAQ,EAAE,IAAI;MACdI,WAAW,EAAE,IAAIoC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACtDC,SAAS,EAAE,IAAIF,IAAI,CAAC,YAAY,CAAC;MACjCK,gBAAgB,EAAE,EAAE;MACpB9D,gBAAgB,EAAE;KACnB,EACD;MACEuD,EAAE,EAAE,GAAG;MACPnD,KAAK,EAAE,0BAA0B;MACjCD,IAAI,EAAE,gBAAgB;MACtBK,IAAI,EAAEtB,QAAQ,CAAC6E,OAAO;MACtBrD,IAAI,EAAE,UAAU;MAChBE,MAAM,EAAE,CAAC;MACTK,QAAQ,EAAE,IAAI;MACdI,WAAW,EAAE,IAAIoC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACtDC,SAAS,EAAE,IAAIF,IAAI,CAAC,YAAY,CAAC;MACjCzD,gBAAgB,EAAE;KACnB,EACD;MACEuD,EAAE,EAAE,GAAG;MACPnD,KAAK,EAAE,0BAA0B;MACjCD,IAAI,EAAE,gBAAgB;MACtBK,IAAI,EAAEtB,QAAQ,CAAC8E,QAAQ;MACvBtD,IAAI,EAAE,QAAQ;MACdE,MAAM,EAAE,CAAC;MACTK,QAAQ,EAAE,IAAI;MACdI,WAAW,EAAE,IAAIoC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;MACvDC,SAAS,EAAE,IAAIF,IAAI,CAAC,YAAY,CAAC;MACjCzD,gBAAgB,EAAE;KACnB,EACD;MACEuD,EAAE,EAAE,GAAG;MACPnD,KAAK,EAAE,0BAA0B;MACjCD,IAAI,EAAE,gBAAgB;MACtBK,IAAI,EAAEtB,QAAQ,CAACsE,IAAI;MACnB9C,IAAI,EAAE,UAAU;MAChBE,MAAM,EAAE,GAAG;MACXK,QAAQ,EAAE,KAAK;MACfI,WAAW,EAAE,IAAIoC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MAC3DC,SAAS,EAAE,IAAIF,IAAI,CAAC,YAAY,CAAC;MACjCG,gBAAgB,EAAE;KACnB,CACF;IAED,IAAI,CAACR,UAAU,GAAG,IAAI,CAACX,KAAK,CAACwB,MAAM;IACnC,IAAI,CAACC,YAAY,EAAE;EACrB;EAEAA,YAAYA,CAAA;IACV,IAAI,CAACxB,aAAa,GAAG,IAAI,CAACD,KAAK,CAAC0B,MAAM,CAACC,IAAI,IAAG;MAC5C,MAAMC,aAAa,GAAGD,IAAI,CAACjE,IAAI,CAACmE,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC5B,UAAU,CAAC2B,WAAW,EAAE,CAAC,IAChEF,IAAI,CAAChE,KAAK,CAACkE,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC5B,UAAU,CAAC2B,WAAW,EAAE,CAAC,IAC/DF,IAAI,CAACpE,gBAAgB,IAAIoE,IAAI,CAACpE,gBAAgB,CAACsE,WAAW,EAAE,CAACC,QAAQ,CAAC,IAAI,CAAC5B,UAAU,CAAC2B,WAAW,EAAE,CAAE;MAE3H,MAAME,WAAW,GAAG,IAAI,CAAC5B,YAAY,KAAK,KAAK,IAAIwB,IAAI,CAAC5D,IAAI,KAAK,IAAI,CAACoC,YAAY;MAClF,MAAM6B,WAAW,GAAG,IAAI,CAAC5B,YAAY,KAAK,KAAK,IAAIuB,IAAI,CAAC1D,IAAI,KAAK,IAAI,CAACmC,YAAY;MAElF,OAAOwB,aAAa,IAAIG,WAAW,IAAIC,WAAW;IACpD,CAAC,CAAC;EACJ;EAEAC,cAAcA,CAAA;IACZ,IAAI,CAACvB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACe,YAAY,EAAE;EACrB;EAEAS,YAAYA,CAAA;IACV,IAAI,CAACxB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACe,YAAY,EAAE;EACrB;EAEAU,YAAYA,CAAA;IACV,IAAI,CAACzB,WAAW,GAAG,CAAC;IACpB,IAAI,CAACe,YAAY,EAAE;EACrB;EAEAtE,kBAAkBA,CAACY,IAAc;IAC/B,MAAMqE,SAAS,GAAG;MAChB,CAAC3F,QAAQ,CAACsE,IAAI,GAAG,aAAa;MAC9B,CAACtE,QAAQ,CAAC2E,SAAS,GAAG,YAAY;MAClC,CAAC3E,QAAQ,CAAC6E,OAAO,GAAG,YAAY;MAChC,CAAC7E,QAAQ,CAAC8E,QAAQ,GAAG,aAAa;MAClC,CAAC9E,QAAQ,CAAC4F,KAAK,GAAG;KACnB;IACD,OAAOD,SAAS,CAACrE,IAAI,CAAC;EACxB;EAEAF,YAAYA,CAACE,IAAc;IACzB,MAAMuE,MAAM,GAAG;MACb,CAAC7F,QAAQ,CAACsE,IAAI,GAAG,SAAS;MAC1B,CAACtE,QAAQ,CAAC2E,SAAS,GAAG,SAAS;MAC/B,CAAC3E,QAAQ,CAAC6E,OAAO,GAAG,SAAS;MAC7B,CAAC7E,QAAQ,CAAC8E,QAAQ,GAAG,SAAS;MAC9B,CAAC9E,QAAQ,CAAC4F,KAAK,GAAG;KACnB;IACD,OAAOC,MAAM,CAACvE,IAAI,CAAC;EACrB;EAEAO,cAAcA,CAACE,QAAiB;IAC9B,OAAOA,QAAQ,GAAG,SAAS,GAAG,SAAS;EACzC;EAEAC,aAAaA,CAACD,QAAiB;IAC7B,OAAOA,QAAQ,GAAG,OAAO,GAAG,SAAS;EACvC;EAEAe,QAAQA,CAACoC,IAAoB;IAC3BY,OAAO,CAACC,GAAG,CAAC,YAAY,EAAEb,IAAI,CAAC;IAC/B;EACF;EAEAlC,gBAAgBA,CAACkC,IAAoB;IACnCA,IAAI,CAACnD,QAAQ,GAAG,CAACmD,IAAI,CAACnD,QAAQ;IAC9B+D,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAEb,IAAI,CAAC;IACxC;EACF;EAEAhC,UAAUA,CAACgC,IAAoB;IAC7BY,OAAO,CAACC,GAAG,CAAC,cAAc,EAAEb,IAAI,CAAC;IACjC;EACF;EAEAc,OAAOA,CAAA;IACLF,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B;EACF;EAEAE,WAAWA,CAAA;IACTH,OAAO,CAACC,GAAG,CAAC,cAAc,CAAC;IAC3B;EACF;EAEAG,iBAAiBA,CAAA;IACf,MAAMC,UAAU,GAAG,IAAI,CAAClC,WAAW,GAAG,IAAI,CAACD,QAAQ;IACnD,OAAO,IAAI,CAACR,aAAa,CAAC4C,KAAK,CAACD,UAAU,EAAEA,UAAU,GAAG,IAAI,CAACnC,QAAQ,CAAC;EACzE;EAEAqC,aAAaA,CAAA;IACX,OAAOC,IAAI,CAACC,IAAI,CAAC,IAAI,CAAC/C,aAAa,CAACuB,MAAM,GAAG,IAAI,CAACf,QAAQ,CAAC;EAC7D;EAEAwC,QAAQA,CAAA;IACN,IAAI,IAAI,CAACvC,WAAW,GAAG,IAAI,CAACoC,aAAa,EAAE,GAAG,CAAC,EAAE;MAC/C,IAAI,CAACpC,WAAW,EAAE;;EAEtB;EAEAwC,YAAYA,CAAA;IACV,IAAI,IAAI,CAACxC,WAAW,GAAG,CAAC,EAAE;MACxB,IAAI,CAACA,WAAW,EAAE;;EAEtB;EAEAyC,QAAQA,CAACC,IAAY;IACnB,IAAI,CAAC1C,WAAW,GAAG0C,IAAI;EACzB;EAEAC,aAAaA,CAAA;IACX,OAAON,IAAI,CAACO,GAAG,CAAC,CAAC,IAAI,CAAC5C,WAAW,GAAG,CAAC,IAAI,IAAI,CAACD,QAAQ,EAAE,IAAI,CAACR,aAAa,CAACuB,MAAM,CAAC;EACpF;;;uBA1MW1B,uBAAuB;IAAA;EAAA;;;YAAvBA,uBAAuB;MAAAyD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPlCnH,EADF,CAAAC,cAAA,aAAuC,SACjC;UAAAD,EAAA,CAAAE,MAAA,8DAA8B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACvCH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,mFAAkE;UAAAF,EAAA,CAAAG,YAAA,EAAI;UAKrEH,EAFJ,CAAAC,cAAA,aAA6B,qBACX,gBACH;UAAAD,EAAA,CAAAE,MAAA,iBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACjCH,EAAA,CAAAC,cAAA,eAA8G;UAA9FD,EAAA,CAAAqH,gBAAA,2BAAAC,gEAAAC,MAAA;YAAAvH,EAAA,CAAAwH,kBAAA,CAAAJ,GAAA,CAAA5D,UAAA,EAAA+D,MAAA,MAAAH,GAAA,CAAA5D,UAAA,GAAA+D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAACvH,EAAA,CAAAqC,UAAA,mBAAAoF,wDAAA;YAAA,OAASL,GAAA,CAAA7B,cAAA,EAAgB;UAAA,EAAC;UAAnEvF,EAAA,CAAAG,YAAA,EAA8G;UAC9GH,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACtB;UAGfH,EADF,CAAAC,cAAA,sBAAgB,iBACH;UAAAD,EAAA,CAAAE,MAAA,iBAAI;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC3BH,EAAA,CAAAC,cAAA,qBAAwE;UAA5DD,EAAA,CAAAqH,gBAAA,yBAAAK,oEAAAH,MAAA;YAAAvH,EAAA,CAAAwH,kBAAA,CAAAJ,GAAA,CAAA3D,YAAA,EAAA8D,MAAA,MAAAH,GAAA,CAAA3D,YAAA,GAAA8D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAACvH,EAAA,CAAAqC,UAAA,6BAAAsF,wEAAA;YAAA,OAAmBP,GAAA,CAAA5B,YAAA,EAAc;UAAA,EAAC;UACrExF,EAAA,CAAAC,cAAA,qBAAwB;UAAAD,EAAA,CAAAE,MAAA,2BAAc;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACnDH,EAAA,CAAAc,UAAA,KAAA8G,8CAAA,wBAA0D;UAI9D5H,EADE,CAAAG,YAAA,EAAa,EACE;UAGfH,EADF,CAAAC,cAAA,sBAAgB,iBACH;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAC,cAAA,qBAAwE;UAA5DD,EAAA,CAAAqH,gBAAA,yBAAAQ,oEAAAN,MAAA;YAAAvH,EAAA,CAAAwH,kBAAA,CAAAJ,GAAA,CAAA1D,YAAA,EAAA6D,MAAA,MAAAH,GAAA,CAAA1D,YAAA,GAAA6D,MAAA;YAAA,OAAAA,MAAA;UAAA,EAAwB;UAACvH,EAAA,CAAAqC,UAAA,6BAAAyF,wEAAA;YAAA,OAAmBV,GAAA,CAAA3B,YAAA,EAAc;UAAA,EAAC;UACrEzF,EAAA,CAAAC,cAAA,qBAAwB;UAAAD,EAAA,CAAAE,MAAA,yBAAiB;UAAAF,EAAA,CAAAG,YAAA,EAAa;UACtDH,EAAA,CAAAc,UAAA,KAAAiH,8CAAA,wBAAuD;UAE3D/H,EADE,CAAAG,YAAA,EAAa,EACE;UAEjBH,EAAA,CAAAC,cAAA,iBAA8D;UAApBD,EAAA,CAAAqC,UAAA,mBAAA2F,0DAAA;YAAA,OAASZ,GAAA,CAAArB,OAAA,EAAS;UAAA,EAAC;UAC3D/F,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,kBAAU;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC/BH,EAAA,CAAAE,MAAA,6BACF;UACFF,EADE,CAAAG,YAAA,EAAS,EACL;UAMAH,EAHN,CAAAC,cAAA,mBAAmC,wBACf,eACa,iBAC6C;UAEtED,EAAA,CAAAiI,uBAAA,QAAkC;UAEhCjI,EADA,CAAAc,UAAA,KAAAoH,sCAAA,iBAAsC,KAAAC,sCAAA,iBACF;;UAUtCnI,EAAA,CAAAiI,uBAAA,QAAkC;UAEhCjI,EADA,CAAAc,UAAA,KAAAsH,sCAAA,iBAAsC,KAAAC,sCAAA,iBACF;;UAStCrI,EAAA,CAAAiI,uBAAA,QAAkC;UAEhCjI,EADA,CAAAc,UAAA,KAAAwH,sCAAA,iBAAsC,KAAAC,sCAAA,iBACF;;UAItCvI,EAAA,CAAAiI,uBAAA,QAAoC;UAElCjI,EADA,CAAAc,UAAA,KAAA0H,sCAAA,iBAAsC,KAAAC,sCAAA,iBACF;;UAOtCzI,EAAA,CAAAiI,uBAAA,QAAoC;UAElCjI,EADA,CAAAc,UAAA,KAAA4H,sCAAA,iBAAsC,KAAAC,sCAAA,iBACF;;UAStC3I,EAAA,CAAAiI,uBAAA,QAAuC;UAErCjI,EADA,CAAAc,UAAA,KAAA8H,sCAAA,iBAAsC,KAAAC,sCAAA,iBACF;;UAOtC7I,EAAA,CAAAiI,uBAAA,QAAqC;UAEnCjI,EADA,CAAAc,UAAA,KAAAgI,sCAAA,iBAAsC,KAAAC,sCAAA,kBACF;;UAsBtC/I,EADA,CAAAc,UAAA,KAAAkI,sCAAA,iBAA2G,KAAAC,sCAAA,iBACM;UAErHjJ,EADE,CAAAG,YAAA,EAAQ,EACJ;UAIJH,EADF,CAAAC,cAAA,eAAkC,eACH;UAC3BD,EAAA,CAAAE,MAAA,IAEF;UAAAF,EAAA,CAAAG,YAAA,EAAM;UAEJH,EADF,CAAAC,cAAA,eAAiC,kBACiD;UAAzBD,EAAA,CAAAqC,UAAA,mBAAA6G,0DAAA;YAAA,OAAS9B,GAAA,CAAAZ,YAAA,EAAc;UAAA,EAAC;UAC7ExG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UACxBF,EADwB,CAAAG,YAAA,EAAW,EAC1B;UACTH,EAAA,CAAAC,cAAA,gBAAwB;UAAAD,EAAA,CAAAE,MAAA,IAA6C;UAAAF,EAAA,CAAAG,YAAA,EAAO;UAC5EH,EAAA,CAAAC,cAAA,kBAA6F;UAArBD,EAAA,CAAAqC,UAAA,mBAAA8G,0DAAA;YAAA,OAAS/B,GAAA,CAAAb,QAAA,EAAU;UAAA,EAAC;UAC1FvG,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAMnCF,EANmC,CAAAG,YAAA,EAAW,EAC3B,EACL,EACF,EACW,EACV,EACP;;;UA1IgBH,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAoJ,gBAAA,YAAAhC,GAAA,CAAA5D,UAAA,CAAwB;UAM5BxD,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAoJ,gBAAA,UAAAhC,GAAA,CAAA3D,YAAA,CAAwB;UAELzD,EAAA,CAAAM,SAAA,GAAY;UAAZN,EAAA,CAAAI,UAAA,YAAAgH,GAAA,CAAAzD,SAAA,CAAY;UAQ/B3D,EAAA,CAAAM,SAAA,GAAwB;UAAxBN,EAAA,CAAAoJ,gBAAA,UAAAhC,GAAA,CAAA1D,YAAA,CAAwB;UAEL1D,EAAA,CAAAM,SAAA,GAAS;UAATN,EAAA,CAAAI,UAAA,YAAAgH,GAAA,CAAAtD,MAAA,CAAS;UAcrB9D,EAAA,CAAAM,SAAA,GAAkC;UAAlCN,EAAA,CAAAI,UAAA,eAAAgH,GAAA,CAAAnB,iBAAA,GAAkC;UAmF7BjG,EAAA,CAAAM,SAAA,IAAqF;UAArFN,EAAA,CAAAI,UAAA,oBAAAJ,EAAA,CAAAqJ,eAAA,KAAAC,GAAA,EAAqF;UACxEtJ,EAAA,CAAAM,SAAA,EAA8E;UAA9EN,EAAA,CAAAI,UAAA,qBAAAJ,EAAA,CAAAqJ,eAAA,KAAAC,GAAA,EAA8E;UAO/GtJ,EAAA,CAAAM,SAAA,GAEF;UAFEN,EAAA,CAAAuJ,kBAAA,gBAAAnC,GAAA,CAAApD,WAAA,GAAAoD,GAAA,CAAArD,QAAA,aAAAqD,GAAA,CAAAT,aAAA,aAAAS,GAAA,CAAA7D,aAAA,CAAAuB,MAAA,mBAEF;UAE0B9E,EAAA,CAAAM,SAAA,GAA8B;UAA9BN,EAAA,CAAAI,UAAA,aAAAgH,GAAA,CAAApD,WAAA,OAA8B;UAG9BhE,EAAA,CAAAM,SAAA,GAA6C;UAA7CN,EAAA,CAAAwJ,kBAAA,KAAApC,GAAA,CAAApD,WAAA,aAAAoD,GAAA,CAAAhB,aAAA,OAA6C;UAC7CpG,EAAA,CAAAM,SAAA,EAA+C;UAA/CN,EAAA,CAAAI,UAAA,aAAAgH,GAAA,CAAApD,WAAA,IAAAoD,GAAA,CAAAhB,aAAA,OAA+C", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}