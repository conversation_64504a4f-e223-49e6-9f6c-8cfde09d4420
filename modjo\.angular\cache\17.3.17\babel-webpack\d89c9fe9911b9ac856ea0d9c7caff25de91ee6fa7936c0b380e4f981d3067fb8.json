{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class SystemStatsComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function SystemStatsComponent_Factory(t) {\n      return new (t || SystemStatsComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SystemStatsComponent,\n      selectors: [[\"app-system-stats\"]],\n      decls: 5,\n      vars: 0,\n      template: function SystemStatsComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"h2\");\n          i0.ɵɵtext(2, \"\\uD83D\\uDCCA Statistiques Syst\\u00E8me\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"Statistiques d\\u00E9taill\\u00E9es et analytics\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\"div[_ngcontent-%COMP%] { padding: 20px; }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInN5c3RlbS1zdGF0cy5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsTUFBTSxhQUFhLEVBQUUiLCJmaWxlIjoic3lzdGVtLXN0YXRzLmNvbXBvbmVudC50cyIsInNvdXJjZXNDb250ZW50IjpbImRpdiB7IHBhZGRpbmc6IDIwcHg7IH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvYWRtaW4tZGFzaGJvYXJkL2NvbXBvbmVudHMvc3lzdGVtLXN0YXRzL3N5c3RlbS1zdGF0cy5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsTUFBTSxhQUFhLEVBQUU7QUFDckIsZ1NBQWdTIiwic291cmNlc0NvbnRlbnQiOlsiZGl2IHsgcGFkZGluZzogMjBweDsgfSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SystemStatsComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "template", "SystemStatsComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\admin-dashboard\\components\\system-stats\\system-stats.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-system-stats',\n  template: '<div><h2>📊 Statistiques Système</h2><p>Statistiques détaillées et analytics</p></div>',\n  styles: ['div { padding: 20px; }']\n})\nexport class SystemStatsComponent implements OnInit {\n  constructor() { }\n  ngOnInit(): void { }\n}\n"], "mappings": ";AAOA,OAAM,MAAOA,oBAAoB;EAC/BC,YAAA,GAAgB;EAChBC,QAAQA,CAAA,GAAW;;;uBAFRF,oBAAoB;IAAA;EAAA;;;YAApBA,oBAAoB;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,8BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAHfE,EAAL,CAAAC,cAAA,UAAK,SAAI;UAAAD,EAAA,CAAAE,MAAA,6CAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAAAH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,qDAAoC;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}