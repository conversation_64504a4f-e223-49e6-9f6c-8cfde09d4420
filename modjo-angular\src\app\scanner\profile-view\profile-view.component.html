<div class="profile-container">
  <button class="back-button" (click)="goBack()">
    <span>←</span> Retour
  </button>

  <div *ngIf="isLoading" class="loading">
    <p>Chargement du profil...</p>
  </div>

  <div *ngIf="error" class="error">
    <p>{{ error }}</p>
    <button (click)="goBack()">Retour au scanner</button>
  </div>

  <div *ngIf="!isLoading && !error && profile" class="profile-content">
    <div class="profile-header">
      <div class="profile-image">
        <img [src]="profile.photoURL || 'assets/default-avatar.png'" alt="Photo de profil">
      </div>
      <div class="profile-info">
        <h2>{{ profile.name }}</h2>
        <p class="profession">{{ profile.profession }}</p>
        <p class="location">{{ profile.location }}</p>
        
        <div class="rating">
          <span class="stars">
            <span *ngFor="let i of [1, 2, 3, 4, 5]">
              <span *ngIf="i <= profile.rating">★</span>
              <span *ngIf="i > profile.rating">☆</span>
            </span>
          </span>
          <span class="reviews">({{ profile.reviews }} avis)</span>
        </div>
      </div>
    </div>

    <div class="profile-details">
      <h3>Compétences</h3>
      <div class="skills">
        <span *ngFor="let skill of profile.skills" class="skill-tag">{{ skill }}</span>
      </div>
    </div>

    <div class="knowme-link">
      <a [href]="'https://knowme.com/profile/' + knowMeId" target="_blank">
        Voir le profil complet sur KnowMe
      </a>
    </div>

    <div class="award-points-section">
      <h3>Attribuer des points</h3>
      
      <div *ngIf="awardSuccess" class="success-message">
        <p>Points attribués avec succès!</p>
      </div>
      
      <div *ngIf="awardError" class="error-message">
        <p>{{ awardError }}</p>
      </div>
      
      <form (ngSubmit)="awardPoints()" *ngIf="!awardSuccess">
        <div class="form-group">
          <label>Nombre de points</label>
          <div class="points-selector">
            <button 
              *ngFor="let points of pointOptions" 
              type="button"
              [class.selected]="selectedPoints === points"
              (click)="selectedPoints = points"
            >
              {{ points }} point{{ points > 1 ? 's' : '' }}
            </button>
          </div>
        </div>
        
        <div class="form-group">
          <label for="description">Description</label>
          <input 
            type="text" 
            id="description" 
            name="description" 
            [(ngModel)]="description" 
            required 
            placeholder="Ex: Coupe de cheveux, consultation, etc."
          >
        </div>
        
        <button type="submit" [disabled]="isAwarding">
          <span *ngIf="isAwarding">Attribution en cours...</span>
          <span *ngIf="!isAwarding">Attribuer {{ selectedPoints }} point{{ selectedPoints > 1 ? 's' : '' }}</span>
        </button>
      </form>
    </div>
  </div>
</div>
