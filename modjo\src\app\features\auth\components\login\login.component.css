.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-card {
  width: 100%;
  max-width: 400px;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
}

.login-header {
  text-align: center;
  margin-bottom: 24px;
}

.logo {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

.login-header h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 2rem;
  font-weight: 600;
}

.login-header p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.full-width {
  width: 100%;
}

.login-button {
  height: 48px;
  font-size: 1rem;
  font-weight: 500;
  margin-top: 8px;
}

.register-link {
  text-align: center;
  margin-top: 16px;
}

.register-link p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
}

.demo-section {
  margin-top: 32px;
  text-align: center;
}

.demo-section h3 {
  margin: 16px 0;
  color: #666;
  font-size: 0.9rem;
  font-weight: 500;
}

.demo-buttons {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.demo-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  height: 40px;
  font-size: 0.9rem;
}

/* Mobile responsive */
@media (max-width: 480px) {
  .login-container {
    padding: 8px;
  }
  
  .login-card {
    padding: 16px;
  }
  
  .logo {
    width: 48px;
    height: 48px;
  }
  
  .login-header h1 {
    font-size: 1.5rem;
  }
}
