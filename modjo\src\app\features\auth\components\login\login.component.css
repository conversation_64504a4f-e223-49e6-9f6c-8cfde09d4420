.login-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 20px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  position: relative;
  overflow: hidden;
}

.login-container::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);
  pointer-events: none;
}

.login-card {
  width: 100%;
  max-width: 450px;
  padding: 40px;
  border-radius: 24px;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  position: relative;
  z-index: 1;
  animation: slideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);
}

.login-header {
  text-align: center;
  margin-bottom: 40px;
  animation: fadeIn 1s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;
}

.logo {
  width: 80px;
  height: 80px;
  margin-bottom: 24px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2rem;
  color: white;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 10px 30px rgba(103, 126, 234, 0.3);
  animation: bounceIn 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.5s both;
}

.login-header h1 {
  margin: 0 0 12px 0;
  color: #2d3748;
  font-size: 2.5rem;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.login-header p {
  margin: 0;
  color: #718096;
  font-size: 1.1rem;
  font-weight: 500;
}

.login-form {
  display: flex;
  flex-direction: column;
  gap: 24px;
  animation: slideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
}

.full-width {
  width: 100%;
}

.login-button {
  height: 56px;
  font-size: 1.1rem;
  font-weight: 600;
  margin-top: 16px;
  border-radius: 16px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  box-shadow: 0 8px 25px rgba(103, 126, 234, 0.4);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  text-transform: none;
  letter-spacing: 0.5px;
}

.login-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 12px 35px rgba(103, 126, 234, 0.6);
}

.login-button:disabled {
  opacity: 0.7;
  transform: none;
  box-shadow: 0 4px 15px rgba(103, 126, 234, 0.2);
}

.register-link {
  text-align: center;
  margin-top: 16px;
}

.register-link p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
}

.demo-section {
  margin-top: 40px;
  text-align: center;
  animation: fadeIn 1s cubic-bezier(0.4, 0, 0.2, 1) 0.8s both;
}

.demo-section h3 {
  margin: 24px 0;
  color: #4a5568;
  font-size: 1.1rem;
  font-weight: 600;
  font-family: 'Poppins', sans-serif;
  position: relative;
}

.demo-section h3::before,
.demo-section h3::after {
  content: '';
  position: absolute;
  top: 50%;
  width: 60px;
  height: 1px;
  background: linear-gradient(90deg, transparent, #cbd5e0, transparent);
}

.demo-section h3::before {
  left: -80px;
}

.demo-section h3::after {
  right: -80px;
}

.demo-buttons {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.demo-button {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  height: 48px;
  font-size: 1rem;
  font-weight: 500;
  border-radius: 12px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: rgba(255, 255, 255, 0.8);
  border: 2px solid rgba(103, 126, 234, 0.2);
}

.demo-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: rgba(103, 126, 234, 0.4);
  background: rgba(255, 255, 255, 1);
}

.demo-button div {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.demo-button strong {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 2px;
}

.demo-button small {
  font-size: 0.8rem;
  opacity: 0.7;
  font-weight: 400;
}

/* Mobile responsive */
@media (max-width: 480px) {
  .login-container {
    padding: 8px;
  }
  
  .login-card {
    padding: 16px;
  }
  
  .logo {
    width: 48px;
    height: 48px;
  }
  
  .login-header h1 {
    font-size: 1.5rem;
  }
}
