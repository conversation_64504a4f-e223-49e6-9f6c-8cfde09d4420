import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { MatDialog } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { RewardsService } from '../../../../core/services/rewards.service';
import { AuthService } from '../../../../core/services/auth.service';
import { Reward, RewardCategory, User } from '../../../../core/models';
import { Observable, combineLatest } from 'rxjs';
import { map, startWith } from 'rxjs/operators';
import { FormControl } from '@angular/forms';

@Component({
  selector: 'app-rewards-list',
  templateUrl: './rewards-list.component.html',
  styleUrls: ['./rewards-list.component.css']
})
export class RewardsListComponent implements OnInit {
  rewards$: Observable<Reward[]>;
  filteredRewards$!: Observable<Reward[]>;
  currentUser: User | null = null;
  
  // Filters
  categoryFilter = new FormControl('all');
  cityFilter = new FormControl('all');
  searchFilter = new FormControl('');
  
  categories = [
    { value: 'all', label: 'Toutes les catégories', icon: 'category' },
    { value: RewardCategory.FOOD, label: 'Restauration', icon: 'restaurant' },
    { value: RewardCategory.SHOPPING, label: 'Shopping', icon: 'shopping_bag' },
    { value: RewardCategory.ENTERTAINMENT, label: 'Divertissement', icon: 'movie' },
    { value: RewardCategory.SERVICES, label: 'Services', icon: 'build' },
    { value: RewardCategory.HEALTH, label: 'Santé', icon: 'local_hospital' },
    { value: RewardCategory.EDUCATION, label: 'Éducation', icon: 'school' },
    { value: RewardCategory.TRANSPORT, label: 'Transport', icon: 'directions_car' },
    { value: RewardCategory.OTHER, label: 'Autre', icon: 'more_horiz' }
  ];

  cities = [
    { value: 'all', label: 'Toutes les villes' },
    { value: 'Monastir', label: 'Monastir' },
    { value: 'Sousse', label: 'Sousse' }
  ];

  constructor(
    private rewardsService: RewardsService,
    private authService: AuthService,
    private router: Router,
    private dialog: MatDialog,
    private snackBar: MatSnackBar
  ) {
    this.rewards$ = this.rewardsService.getRewards();
  }

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
      if (user) {
        this.cityFilter.setValue(user.city);
      }
    });

    this.setupFilters();
  }

  private setupFilters(): void {
    this.filteredRewards$ = combineLatest([
      this.rewards$,
      this.categoryFilter.valueChanges.pipe(startWith('all')),
      this.cityFilter.valueChanges.pipe(startWith('all')),
      this.searchFilter.valueChanges.pipe(startWith(''))
    ]).pipe(
      map(([rewards, category, city, search]) => {
        return rewards.filter(reward => {
          // Category filter
          if (category !== 'all' && reward.category !== category) {
            return false;
          }
          
          // City filter
          if (city !== 'all' && reward.city !== city && reward.city !== 'Both') {
            return false;
          }
          
          // Search filter
          if (search && !reward.title.toLowerCase().includes(search.toLowerCase()) &&
              !reward.description.toLowerCase().includes(search.toLowerCase()) &&
              !reward.partnerName.toLowerCase().includes(search.toLowerCase())) {
            return false;
          }
          
          return reward.isActive;
        });
      })
    );
  }

  getCategoryIcon(category: RewardCategory): string {
    const categoryData = this.categories.find(c => c.value === category);
    return categoryData?.icon || 'category';
  }

  getCategoryLabel(category: RewardCategory): string {
    const categoryData = this.categories.find(c => c.value === category);
    return categoryData?.label || category;
  }

  canAfford(reward: Reward): boolean {
    return this.currentUser ? this.currentUser.points >= reward.pointsRequired : false;
  }

  async exchangeReward(reward: Reward): Promise<void> {
    if (!this.currentUser) {
      this.snackBar.open('Vous devez être connecté pour échanger une récompense', 'Fermer', { duration: 3000 });
      return;
    }

    if (!this.canAfford(reward)) {
      this.snackBar.open('Points insuffisants pour cette récompense', 'Fermer', { duration: 3000 });
      return;
    }

    try {
      const exchange = await this.rewardsService.exchangeReward(reward.id, this.currentUser.uid);
      this.snackBar.open(
        `Récompense échangée avec succès! Code: ${exchange.exchangeCode}`, 
        'Fermer', 
        { duration: 5000 }
      );
      
      // Note: In a real app, you would refresh user data here
      
    } catch (error: any) {
      console.error('Exchange error:', error);
      this.snackBar.open(error.message || 'Erreur lors de l\'échange', 'Fermer', { duration: 3000 });
    }
  }

  viewRewardDetails(reward: Reward): void {
    this.router.navigate(['/rewards', reward.id]);
  }

  getAvailabilityText(reward: Reward): string {
    if (!reward.availableQuantity) {
      return 'Disponible';
    }
    
    if (reward.availableQuantity <= 0) {
      return 'Épuisé';
    }
    
    if (reward.availableQuantity <= 5) {
      return `Plus que ${reward.availableQuantity} disponible(s)`;
    }
    
    return 'Disponible';
  }

  getAvailabilityColor(reward: Reward): string {
    if (!reward.availableQuantity || reward.availableQuantity > 5) {
      return 'primary';
    }
    
    if (reward.availableQuantity <= 0) {
      return 'warn';
    }
    
    return 'accent';
  }

  isExpiringSoon(reward: Reward): boolean {
    if (!reward.validUntil) return false;
    
    const now = new Date();
    const validUntil = new Date(reward.validUntil);
    const daysUntilExpiry = Math.ceil((validUntil.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    return daysUntilExpiry <= 7 && daysUntilExpiry > 0;
  }

  formatExpiryDate(date: Date): string {
    return new Date(date).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric'
    });
  }

  clearFilters(): void {
    this.categoryFilter.setValue('all');
    this.cityFilter.setValue(this.currentUser?.city || 'all');
    this.searchFilter.setValue('');
  }
}
