export enum UserRole {
  USER = 'user',
  PROVIDER = 'provider',
  VALIDATOR = 'validator',
  ADMIN = 'admin'
}

export interface User {
  uid: string;
  email: string;
  displayName?: string;
  firstName?: string;
  lastName?: string;
  city?: string;
  role: UserRole;
  activity?: string;
  points: number;
  photoURL?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface UserProfile extends User {
  phoneNumber?: string;
  knowMeId?: string;
}
