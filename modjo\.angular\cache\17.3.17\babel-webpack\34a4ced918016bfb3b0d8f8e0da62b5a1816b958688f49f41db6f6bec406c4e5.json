{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { switchMap } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nimport * as i2 from \"../../../../core/services/rewards.service\";\nimport * as i3 from \"../../../../core/services/auth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nfunction RewardDetailComponent_div_0_mat_chip_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-chip\", 31)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Expire bient\\u00F4t \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RewardDetailComponent_div_0_div_54_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 32)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reward_r3 = i0.ɵɵnextContext().ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Valide jusqu'au \", ctx_r1.formatDate(reward_r3.validUntil), \"\");\n  }\n}\nfunction RewardDetailComponent_div_0_mat_card_55_div_13_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 37);\n    i0.ɵɵlistener(\"click\", function RewardDetailComponent_div_0_mat_card_55_div_13_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const partner_r5 = i0.ɵɵnextContext().ngIf;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.getDirections(partner_r5));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"directions\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const partner_r5 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(partner_r5.address);\n  }\n}\nfunction RewardDetailComponent_div_0_mat_card_55_div_14_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function RewardDetailComponent_div_0_mat_card_55_div_14_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const partner_r5 = i0.ɵɵnextContext().ngIf;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.contactPartner(partner_r5));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"call\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const partner_r5 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(partner_r5.phone);\n  }\n}\nfunction RewardDetailComponent_div_0_mat_card_55_div_15_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"email\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function RewardDetailComponent_div_0_mat_card_55_div_15_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const partner_r5 = i0.ɵɵnextContext().ngIf;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.contactPartner(partner_r5));\n    });\n    i0.ɵɵelementStart(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"mail\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const partner_r5 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(partner_r5.email);\n  }\n}\nfunction RewardDetailComponent_div_0_mat_card_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 20)(1, \"mat-card-header\")(2, \"mat-card-title\")(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(5, \" \\u00C0 propos du partenaire \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"div\", 33)(8, \"h4\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 34);\n    i0.ɵɵtemplate(13, RewardDetailComponent_div_0_mat_card_55_div_13_Template, 8, 1, \"div\", 35)(14, RewardDetailComponent_div_0_mat_card_55_div_14_Template, 8, 1, \"div\", 35)(15, RewardDetailComponent_div_0_mat_card_55_div_15_Template, 8, 1, \"div\", 35);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const partner_r5 = ctx.ngIf;\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(partner_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(partner_r5.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", partner_r5.address);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", partner_r5.phone);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", partner_r5.email);\n  }\n}\nfunction RewardDetailComponent_div_0_mat_card_57_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"trending_down\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"h4\");\n    i0.ɵɵtext(5, \"Apr\\u00E8s \\u00E9change\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 43);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const reward_r3 = i0.ɵɵnextContext(2).ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.currentUser.points - reward_r3.pointsRequired, \" points\");\n  }\n}\nfunction RewardDetailComponent_div_0_mat_card_57_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 47)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"add_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\")(4, \"h4\");\n    i0.ɵɵtext(5, \"Points n\\u00E9cessaires\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 48);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const reward_r3 = i0.ɵɵnextContext(2).ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\"\", reward_r3.pointsRequired - ctx_r1.currentUser.points, \" points\");\n  }\n}\nfunction RewardDetailComponent_div_0_mat_card_57_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 40)(1, \"mat-card-content\")(2, \"div\", 41)(3, \"div\", 42)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"account_balance_wallet\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"div\")(7, \"h4\");\n    i0.ɵɵtext(8, \"Vos points actuels\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 43);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(11, RewardDetailComponent_div_0_mat_card_57_div_11_Template, 8, 1, \"div\", 44)(12, RewardDetailComponent_div_0_mat_card_57_div_12_Template, 8, 1, \"div\", 45);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const reward_r3 = i0.ɵɵnextContext().ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.currentUser.points, \" points\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.canAfford(reward_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.canAfford(reward_r3));\n  }\n}\nfunction RewardDetailComponent_div_0_mat_spinner_61_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 49);\n  }\n}\nfunction RewardDetailComponent_div_0_mat_icon_62_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const reward_r3 = i0.ɵɵnextContext().ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.canAfford(reward_r3) ? \"redeem\" : \"lock\");\n  }\n}\nfunction RewardDetailComponent_div_0_span_63_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const reward_r3 = i0.ɵɵnextContext().ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.canAfford(reward_r3) ? \"\\u00C9changer maintenant\" : \"Points insuffisants\", \" \");\n  }\n}\nfunction RewardDetailComponent_div_0_button_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 50)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"qr_code_scanner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Gagner des points \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RewardDetailComponent_div_0_p_65_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"p\", 51)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Vous recevrez un code d'\\u00E9change \\u00E0 pr\\u00E9senter au partenaire. \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction RewardDetailComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"button\", 4);\n    i0.ɵɵlistener(\"click\", function RewardDetailComponent_div_0_Template_button_click_2_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.goBack());\n    });\n    i0.ɵɵelementStart(3, \"mat-icon\");\n    i0.ɵɵtext(4, \"arrow_back\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h1\");\n    i0.ɵɵtext(6, \"D\\u00E9tails de la r\\u00E9compense\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function RewardDetailComponent_div_0_Template_button_click_7_listener() {\n      const reward_r3 = i0.ɵɵrestoreView(_r1).ngIf;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.shareReward(reward_r3));\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"share\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"div\", 6)(11, \"div\", 7);\n    i0.ɵɵelement(12, \"img\", 8);\n    i0.ɵɵelementStart(13, \"div\", 9)(14, \"mat-chip\", 10)(15, \"mat-icon\");\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(18, RewardDetailComponent_div_0_mat_chip_18_Template, 4, 0, \"mat-chip\", 11);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 12)(20, \"div\", 13)(21, \"h2\");\n    i0.ɵɵtext(22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"div\", 14)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(28, \"p\", 15);\n    i0.ɵɵtext(29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(30, \"div\", 16)(31, \"div\", 17)(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"store\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"span\");\n    i0.ɵɵtext(35);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(36, \"div\", 17)(37, \"mat-icon\");\n    i0.ɵɵtext(38, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(39, \"span\");\n    i0.ɵɵtext(40);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(41, \"div\", 17)(42, \"mat-chip\", 18);\n    i0.ɵɵtext(43);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(44, \"div\", 19)(45, \"mat-card\", 20)(46, \"mat-card-header\")(47, \"mat-card-title\")(48, \"mat-icon\");\n    i0.ɵɵtext(49, \"description\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(50, \" Conditions d'utilisation \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(51, \"mat-card-content\")(52, \"p\");\n    i0.ɵɵtext(53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(54, RewardDetailComponent_div_0_div_54_Template, 5, 1, \"div\", 21);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(55, RewardDetailComponent_div_0_mat_card_55_Template, 16, 5, \"mat-card\", 22);\n    i0.ɵɵpipe(56, \"async\");\n    i0.ɵɵtemplate(57, RewardDetailComponent_div_0_mat_card_57_Template, 13, 3, \"mat-card\", 23);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(58, \"div\", 24)(59, \"div\", 25)(60, \"button\", 26);\n    i0.ɵɵlistener(\"click\", function RewardDetailComponent_div_0_Template_button_click_60_listener() {\n      const reward_r3 = i0.ɵɵrestoreView(_r1).ngIf;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.exchangeReward(reward_r3));\n    });\n    i0.ɵɵtemplate(61, RewardDetailComponent_div_0_mat_spinner_61_Template, 1, 0, \"mat-spinner\", 27)(62, RewardDetailComponent_div_0_mat_icon_62_Template, 2, 1, \"mat-icon\", 28)(63, RewardDetailComponent_div_0_span_63_Template, 2, 1, \"span\", 28);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(64, RewardDetailComponent_div_0_button_64_Template, 4, 0, \"button\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(65, RewardDetailComponent_div_0_p_65_Template, 4, 0, \"p\", 30);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const reward_r3 = ctx.ngIf;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"src\", reward_r3.imageUrl || \"assets/images/reward-placeholder.jpg\", i0.ɵɵsanitizeUrl)(\"alt\", reward_r3.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", \"primary\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getCategoryIcon(reward_r3.category));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getCategoryLabel(reward_r3.category), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isExpiringSoon(reward_r3));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(reward_r3.title);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", reward_r3.pointsRequired, \" points\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reward_r3.description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(reward_r3.partnerName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(reward_r3.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"color\", ctx_r1.getAvailabilityColor(reward_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getAvailabilityText(reward_r3), \" \");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(reward_r3.terms || \"Aucune condition particuli\\u00E8re.\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", reward_r3.validUntil);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(56, 24, ctx_r1.partner$));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.currentUser);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"color\", ctx_r1.canAfford(reward_r3) ? \"primary\" : \"basic\")(\"disabled\", !ctx_r1.canAfford(reward_r3) || !reward_r3.availableQuantity || ctx_r1.isExchanging);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isExchanging);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isExchanging);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isExchanging);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.canAfford(reward_r3));\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.canAfford(reward_r3));\n  }\n}\nfunction RewardDetailComponent_div_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 52)(1, \"mat-card\", 53)(2, \"mat-card-content\")(3, \"div\", 54);\n    i0.ɵɵelement(4, \"mat-spinner\", 55);\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6, \"Chargement des d\\u00E9tails...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nexport class RewardDetailComponent {\n  constructor(route, router, rewardsService, authService, snackBar) {\n    this.route = route;\n    this.router = router;\n    this.rewardsService = rewardsService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.currentUser = null;\n    this.isExchanging = false;\n    this.reward$ = this.route.params.pipe(switchMap(params => this.rewardsService.getRewardById(params['id'])));\n    this.partner$ = this.reward$.pipe(switchMap(reward => reward ? this.rewardsService.getPartnerById(reward.partnerId) : [null]));\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n  canAfford(reward) {\n    return this.currentUser ? this.currentUser.points >= reward.pointsRequired : false;\n  }\n  exchangeReward(reward) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (!_this.currentUser) {\n        _this.snackBar.open('Vous devez être connecté pour échanger une récompense', 'Fermer', {\n          duration: 3000\n        });\n        return;\n      }\n      if (!_this.canAfford(reward)) {\n        _this.snackBar.open('Points insuffisants pour cette récompense', 'Fermer', {\n          duration: 3000\n        });\n        return;\n      }\n      if (reward.availableQuantity !== undefined && reward.availableQuantity <= 0) {\n        _this.snackBar.open('Cette récompense n\\'est plus disponible', 'Fermer', {\n          duration: 3000\n        });\n        return;\n      }\n      _this.isExchanging = true;\n      try {\n        const exchange = yield _this.rewardsService.exchangeReward(reward.id, _this.currentUser.uid);\n        _this.snackBar.open(`Récompense échangée avec succès! Code d'échange: ${exchange.exchangeCode}`, 'Fermer', {\n          duration: 8000\n        });\n        // Refresh user data to update points\n        _this.authService.refreshCurrentUser();\n        // Navigate to exchange history or profile\n        _this.router.navigate(['/profile'], {\n          fragment: 'exchanges'\n        });\n      } catch (error) {\n        console.error('Exchange error:', error);\n        _this.snackBar.open(error.message || 'Erreur lors de l\\'échange', 'Fermer', {\n          duration: 3000\n        });\n      } finally {\n        _this.isExchanging = false;\n      }\n    })();\n  }\n  goBack() {\n    this.router.navigate(['/rewards']);\n  }\n  getCategoryIcon(category) {\n    const iconMap = {\n      'FOOD': 'restaurant',\n      'SHOPPING': 'shopping_bag',\n      'ENTERTAINMENT': 'movie',\n      'SERVICES': 'build',\n      'HEALTH': 'local_hospital',\n      'EDUCATION': 'school',\n      'TRANSPORT': 'directions_car',\n      'OTHER': 'more_horiz'\n    };\n    return iconMap[category] || 'category';\n  }\n  getCategoryLabel(category) {\n    const labelMap = {\n      'FOOD': 'Restauration',\n      'SHOPPING': 'Shopping',\n      'ENTERTAINMENT': 'Divertissement',\n      'SERVICES': 'Services',\n      'HEALTH': 'Santé',\n      'EDUCATION': 'Éducation',\n      'TRANSPORT': 'Transport',\n      'OTHER': 'Autre'\n    };\n    return labelMap[category] || category;\n  }\n  formatDate(date) {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      day: '2-digit',\n      month: 'long',\n      year: 'numeric'\n    });\n  }\n  isExpiringSoon(reward) {\n    if (!reward.validUntil) return false;\n    const now = new Date();\n    const validUntil = new Date(reward.validUntil);\n    const daysUntilExpiry = Math.ceil((validUntil.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));\n    return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n  }\n  getAvailabilityText(reward) {\n    if (!reward.availableQuantity) {\n      return 'Disponible';\n    }\n    if (reward.availableQuantity <= 0) {\n      return 'Épuisé';\n    }\n    if (reward.availableQuantity <= 5) {\n      return `Plus que ${reward.availableQuantity} disponible(s)`;\n    }\n    return `${reward.availableQuantity} disponible(s)`;\n  }\n  getAvailabilityColor(reward) {\n    if (!reward.availableQuantity || reward.availableQuantity > 5) {\n      return 'primary';\n    }\n    if (reward.availableQuantity <= 0) {\n      return 'warn';\n    }\n    return 'accent';\n  }\n  shareReward(reward) {\n    if (navigator.share) {\n      navigator.share({\n        title: `Récompense Modjo: ${reward.title}`,\n        text: `Découvrez cette récompense sur Modjo: ${reward.description}`,\n        url: window.location.href\n      }).catch(console.error);\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href).then(() => {\n        this.snackBar.open('Lien copié dans le presse-papiers', 'Fermer', {\n          duration: 2000\n        });\n      });\n    }\n  }\n  contactPartner(partner) {\n    if (partner.phone) {\n      window.open(`tel:${partner.phone}`, '_blank');\n    } else if (partner.email) {\n      window.open(`mailto:${partner.email}`, '_blank');\n    }\n  }\n  getDirections(partner) {\n    if (partner.location) {\n      const url = `https://www.google.com/maps/dir/?api=1&destination=${partner.location.latitude},${partner.location.longitude}`;\n      window.open(url, '_blank');\n    } else if (partner.address) {\n      const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(partner.address)}`;\n      window.open(url, '_blank');\n    }\n  }\n  static {\n    this.ɵfac = function RewardDetailComponent_Factory(t) {\n      return new (t || RewardDetailComponent)(i0.ɵɵdirectiveInject(i1.ActivatedRoute), i0.ɵɵdirectiveInject(i1.Router), i0.ɵɵdirectiveInject(i2.RewardsService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RewardDetailComponent,\n      selectors: [[\"app-reward-detail\"]],\n      decls: 4,\n      vars: 6,\n      consts: [[\"class\", \"reward-detail-container\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"reward-detail-container\"], [1, \"detail-header\"], [\"mat-icon-button\", \"\", 1, \"back-button\", 3, \"click\"], [\"mat-icon-button\", \"\", 1, \"share-button\", 3, \"click\"], [1, \"reward-hero\", \"slide-up\"], [1, \"hero-image\"], [\"onerror\", \"this.src='assets/images/reward-placeholder.jpg'\", 3, \"src\", \"alt\"], [1, \"hero-badges\"], [\"selected\", \"\", 1, \"category-chip\", 3, \"color\"], [\"class\", \"expiry-chip\", \"color\", \"warn\", \"selected\", \"\", 4, \"ngIf\"], [1, \"hero-content\"], [1, \"reward-title-section\"], [1, \"points-badge\"], [1, \"reward-description\"], [1, \"reward-meta\"], [1, \"meta-item\"], [\"selected\", \"\", 3, \"color\"], [1, \"detail-sections\", \"fade-in\"], [1, \"detail-card\"], [\"class\", \"validity-info\", 4, \"ngIf\"], [\"class\", \"detail-card\", 4, \"ngIf\"], [\"class\", \"detail-card points-status-card\", 4, \"ngIf\"], [1, \"action-section\"], [1, \"action-buttons\"], [\"mat-raised-button\", \"\", 1, \"exchange-button\", 3, \"click\", \"color\", \"disabled\"], [\"diameter\", \"24\", 4, \"ngIf\"], [4, \"ngIf\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/qr-scanner\", \"class\", \"scan-button\", 4, \"ngIf\"], [\"class\", \"exchange-note\", 4, \"ngIf\"], [\"color\", \"warn\", \"selected\", \"\", 1, \"expiry-chip\"], [1, \"validity-info\"], [1, \"partner-info\"], [1, \"partner-details\"], [\"class\", \"detail-row\", 4, \"ngIf\"], [1, \"detail-row\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Obtenir l'itin\\u00E9raire\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Appeler\", 3, \"click\"], [\"mat-icon-button\", \"\", \"matTooltip\", \"Envoyer un email\", 3, \"click\"], [1, \"detail-card\", \"points-status-card\"], [1, \"points-status\"], [1, \"current-points\"], [1, \"points-value\"], [\"class\", \"points-after\", 4, \"ngIf\"], [\"class\", \"points-needed\", 4, \"ngIf\"], [1, \"points-after\"], [1, \"points-needed\"], [1, \"points-value\", \"needed\"], [\"diameter\", \"24\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/qr-scanner\", 1, \"scan-button\"], [1, \"exchange-note\"], [1, \"loading-container\"], [1, \"loading-card\"], [1, \"loading-content\"], [\"diameter\", \"48\"]],\n      template: function RewardDetailComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, RewardDetailComponent_div_0_Template, 66, 26, \"div\", 0);\n          i0.ɵɵpipe(1, \"async\");\n          i0.ɵɵtemplate(2, RewardDetailComponent_div_2_Template, 7, 0, \"div\", 1);\n          i0.ɵɵpipe(3, \"async\");\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(1, 2, ctx.reward$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", !i0.ɵɵpipeBind1(3, 4, ctx.reward$));\n        }\n      },\n      styles: [\"\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\t\\\"use strict\\\";\\n\\n \\t\\n\\n \\t\\n\\n })()[_ngcontent-%COMP%]\\n;\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["switchMap", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "formatDate", "reward_r3", "validUntil", "ɵɵlistener", "RewardDetailComponent_div_0_mat_card_55_div_13_Template_button_click_5_listener", "ɵɵrestoreView", "_r4", "partner_r5", "ɵɵnextContext", "ngIf", "ɵɵresetView", "getDirections", "ɵɵtextInterpolate", "address", "RewardDetailComponent_div_0_mat_card_55_div_14_Template_button_click_5_listener", "_r6", "<PERSON><PERSON><PERSON><PERSON>", "phone", "RewardDetailComponent_div_0_mat_card_55_div_15_Template_button_click_5_listener", "_r7", "email", "ɵɵtemplate", "RewardDetailComponent_div_0_mat_card_55_div_13_Template", "RewardDetailComponent_div_0_mat_card_55_div_14_Template", "RewardDetailComponent_div_0_mat_card_55_div_15_Template", "name", "description", "ɵɵproperty", "currentUser", "points", "pointsRequired", "RewardDetailComponent_div_0_mat_card_57_div_11_Template", "RewardDetailComponent_div_0_mat_card_57_div_12_Template", "can<PERSON>fford", "ɵɵelement", "RewardDetailComponent_div_0_Template_button_click_2_listener", "_r1", "goBack", "RewardDetailComponent_div_0_Template_button_click_7_listener", "shareReward", "RewardDetailComponent_div_0_mat_chip_18_Template", "RewardDetailComponent_div_0_div_54_Template", "RewardDetailComponent_div_0_mat_card_55_Template", "RewardDetailComponent_div_0_mat_card_57_Template", "RewardDetailComponent_div_0_Template_button_click_60_listener", "exchangeReward", "RewardDetailComponent_div_0_mat_spinner_61_Template", "RewardDetailComponent_div_0_mat_icon_62_Template", "RewardDetailComponent_div_0_span_63_Template", "RewardDetailComponent_div_0_button_64_Template", "RewardDetailComponent_div_0_p_65_Template", "imageUrl", "ɵɵsanitizeUrl", "title", "getCategoryIcon", "category", "getCategoryLabel", "isExpiringSoon", "partner<PERSON>ame", "city", "getAvailabilityColor", "getAvailabilityText", "terms", "ɵɵpipeBind1", "partner$", "availableQuantity", "isExchanging", "RewardDetailComponent", "constructor", "route", "router", "rewardsService", "authService", "snackBar", "reward$", "params", "pipe", "getRewardById", "reward", "getPartnerById", "partnerId", "ngOnInit", "currentUser$", "subscribe", "user", "_this", "_asyncToGenerator", "open", "duration", "undefined", "exchange", "id", "uid", "exchangeCode", "refreshCurrentUser", "navigate", "fragment", "error", "console", "message", "iconMap", "labelMap", "date", "Date", "toLocaleDateString", "day", "month", "year", "now", "daysUntilExpiry", "Math", "ceil", "getTime", "navigator", "share", "text", "url", "window", "location", "href", "catch", "clipboard", "writeText", "then", "partner", "latitude", "longitude", "encodeURIComponent", "ɵɵdirectiveInject", "i1", "ActivatedRoute", "Router", "i2", "RewardsService", "i3", "AuthService", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "RewardDetailComponent_Template", "rf", "ctx", "RewardDetailComponent_div_0_Template", "RewardDetailComponent_div_2_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\rewards\\components\\reward-detail\\reward-detail.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\rewards\\components\\reward-detail\\reward-detail.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { RewardsService } from '../../../../core/services/rewards.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { Reward, Partner, User } from '../../../../core/models';\nimport { Observable, combineLatest } from 'rxjs';\nimport { map, switchMap } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-reward-detail',\n  templateUrl: './reward-detail.component.html',\n  styleUrls: ['./reward-detail.component.css']\n})\nexport class RewardDetailComponent implements OnInit {\n  reward$: Observable<Reward | null>;\n  partner$: Observable<Partner | null>;\n  currentUser: User | null = null;\n  isExchanging = false;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private rewardsService: RewardsService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar\n  ) {\n    this.reward$ = this.route.params.pipe(\n      switchMap(params => this.rewardsService.getRewardById(params['id']))\n    );\n\n    this.partner$ = this.reward$.pipe(\n      switchMap(reward => \n        reward ? this.rewardsService.getPartnerById(reward.partnerId) : [null]\n      )\n    );\n  }\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  canAfford(reward: Reward): boolean {\n    return this.currentUser ? this.currentUser.points >= reward.pointsRequired : false;\n  }\n\n  async exchangeReward(reward: Reward): Promise<void> {\n    if (!this.currentUser) {\n      this.snackBar.open('Vous devez être connecté pour échanger une récompense', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    if (!this.canAfford(reward)) {\n      this.snackBar.open('Points insuffisants pour cette récompense', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    if (reward.availableQuantity !== undefined && reward.availableQuantity <= 0) {\n      this.snackBar.open('Cette récompense n\\'est plus disponible', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    this.isExchanging = true;\n\n    try {\n      const exchange = await this.rewardsService.exchangeReward(reward.id, this.currentUser.uid);\n      \n      this.snackBar.open(\n        `Récompense échangée avec succès! Code d'échange: ${exchange.exchangeCode}`, \n        'Fermer', \n        { duration: 8000 }\n      );\n      \n      // Refresh user data to update points\n      this.authService.refreshCurrentUser();\n      \n      // Navigate to exchange history or profile\n      this.router.navigate(['/profile'], { fragment: 'exchanges' });\n      \n    } catch (error: any) {\n      console.error('Exchange error:', error);\n      this.snackBar.open(error.message || 'Erreur lors de l\\'échange', 'Fermer', { duration: 3000 });\n    } finally {\n      this.isExchanging = false;\n    }\n  }\n\n  goBack(): void {\n    this.router.navigate(['/rewards']);\n  }\n\n  getCategoryIcon(category: string): string {\n    const iconMap: { [key: string]: string } = {\n      'FOOD': 'restaurant',\n      'SHOPPING': 'shopping_bag',\n      'ENTERTAINMENT': 'movie',\n      'SERVICES': 'build',\n      'HEALTH': 'local_hospital',\n      'EDUCATION': 'school',\n      'TRANSPORT': 'directions_car',\n      'OTHER': 'more_horiz'\n    };\n    return iconMap[category] || 'category';\n  }\n\n  getCategoryLabel(category: string): string {\n    const labelMap: { [key: string]: string } = {\n      'FOOD': 'Restauration',\n      'SHOPPING': 'Shopping',\n      'ENTERTAINMENT': 'Divertissement',\n      'SERVICES': 'Services',\n      'HEALTH': 'Santé',\n      'EDUCATION': 'Éducation',\n      'TRANSPORT': 'Transport',\n      'OTHER': 'Autre'\n    };\n    return labelMap[category] || category;\n  }\n\n  formatDate(date: Date): string {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      day: '2-digit',\n      month: 'long',\n      year: 'numeric'\n    });\n  }\n\n  isExpiringSoon(reward: Reward): boolean {\n    if (!reward.validUntil) return false;\n    \n    const now = new Date();\n    const validUntil = new Date(reward.validUntil);\n    const daysUntilExpiry = Math.ceil((validUntil.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));\n    \n    return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n  }\n\n  getAvailabilityText(reward: Reward): string {\n    if (!reward.availableQuantity) {\n      return 'Disponible';\n    }\n    \n    if (reward.availableQuantity <= 0) {\n      return 'Épuisé';\n    }\n    \n    if (reward.availableQuantity <= 5) {\n      return `Plus que ${reward.availableQuantity} disponible(s)`;\n    }\n    \n    return `${reward.availableQuantity} disponible(s)`;\n  }\n\n  getAvailabilityColor(reward: Reward): string {\n    if (!reward.availableQuantity || reward.availableQuantity > 5) {\n      return 'primary';\n    }\n    \n    if (reward.availableQuantity <= 0) {\n      return 'warn';\n    }\n    \n    return 'accent';\n  }\n\n  shareReward(reward: Reward): void {\n    if (navigator.share) {\n      navigator.share({\n        title: `Récompense Modjo: ${reward.title}`,\n        text: `Découvrez cette récompense sur Modjo: ${reward.description}`,\n        url: window.location.href\n      }).catch(console.error);\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href).then(() => {\n        this.snackBar.open('Lien copié dans le presse-papiers', 'Fermer', { duration: 2000 });\n      });\n    }\n  }\n\n  contactPartner(partner: Partner): void {\n    if (partner.phone) {\n      window.open(`tel:${partner.phone}`, '_blank');\n    } else if (partner.email) {\n      window.open(`mailto:${partner.email}`, '_blank');\n    }\n  }\n\n  getDirections(partner: Partner): void {\n    if (partner.location) {\n      const url = `https://www.google.com/maps/dir/?api=1&destination=${partner.location.latitude},${partner.location.longitude}`;\n      window.open(url, '_blank');\n    } else if (partner.address) {\n      const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(partner.address)}`;\n      window.open(url, '_blank');\n    }\n  }\n}\n", "<div class=\"reward-detail-container\" *ngIf=\"reward$ | async as reward\">\n  <!-- Header with back button -->\n  <div class=\"detail-header\">\n    <button mat-icon-button (click)=\"goBack()\" class=\"back-button\">\n      <mat-icon>arrow_back</mat-icon>\n    </button>\n    <h1>Détails de la récompense</h1>\n    <button mat-icon-button (click)=\"shareReward(reward)\" class=\"share-button\">\n      <mat-icon>share</mat-icon>\n    </button>\n  </div>\n\n  <!-- Reward image and basic info -->\n  <div class=\"reward-hero slide-up\">\n    <div class=\"hero-image\">\n      <img [src]=\"reward.imageUrl || 'assets/images/reward-placeholder.jpg'\" \n           [alt]=\"reward.title\"\n           onerror=\"this.src='assets/images/reward-placeholder.jpg'\">\n      \n      <!-- Badges overlay -->\n      <div class=\"hero-badges\">\n        <mat-chip class=\"category-chip\" [color]=\"'primary'\" selected>\n          <mat-icon>{{ getCategoryIcon(reward.category) }}</mat-icon>\n          {{ getCategoryLabel(reward.category) }}\n        </mat-chip>\n        \n        <mat-chip *ngIf=\"isExpiringSoon(reward)\" \n                 class=\"expiry-chip\" \n                 color=\"warn\" \n                 selected>\n          <mat-icon>schedule</mat-icon>\n          Expire bientôt\n        </mat-chip>\n      </div>\n    </div>\n\n    <div class=\"hero-content\">\n      <div class=\"reward-title-section\">\n        <h2>{{ reward.title }}</h2>\n        <div class=\"points-badge\">\n          <mat-icon>stars</mat-icon>\n          <span>{{ reward.pointsRequired }} points</span>\n        </div>\n      </div>\n      \n      <p class=\"reward-description\">{{ reward.description }}</p>\n      \n      <div class=\"reward-meta\">\n        <div class=\"meta-item\">\n          <mat-icon>store</mat-icon>\n          <span>{{ reward.partnerName }}</span>\n        </div>\n        \n        <div class=\"meta-item\">\n          <mat-icon>location_on</mat-icon>\n          <span>{{ reward.city }}</span>\n        </div>\n        \n        <div class=\"meta-item\">\n          <mat-chip [color]=\"getAvailabilityColor(reward)\" selected>\n            {{ getAvailabilityText(reward) }}\n          </mat-chip>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Detailed information -->\n  <div class=\"detail-sections fade-in\">\n    <!-- Terms and conditions -->\n    <mat-card class=\"detail-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>description</mat-icon>\n          Conditions d'utilisation\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <p>{{ reward.terms || 'Aucune condition particulière.' }}</p>\n        \n        <div class=\"validity-info\" *ngIf=\"reward.validUntil\">\n          <mat-icon>event</mat-icon>\n          <span>Valide jusqu'au {{ formatDate(reward.validUntil) }}</span>\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- Partner information -->\n    <mat-card class=\"detail-card\" *ngIf=\"partner$ | async as partner\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>business</mat-icon>\n          À propos du partenaire\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"partner-info\">\n          <h4>{{ partner.name }}</h4>\n          <p>{{ partner.description }}</p>\n          \n          <div class=\"partner-details\">\n            <div class=\"detail-row\" *ngIf=\"partner.address\">\n              <mat-icon>location_on</mat-icon>\n              <span>{{ partner.address }}</span>\n              <button mat-icon-button (click)=\"getDirections(partner)\" matTooltip=\"Obtenir l'itinéraire\">\n                <mat-icon>directions</mat-icon>\n              </button>\n            </div>\n            \n            <div class=\"detail-row\" *ngIf=\"partner.phone\">\n              <mat-icon>phone</mat-icon>\n              <span>{{ partner.phone }}</span>\n              <button mat-icon-button (click)=\"contactPartner(partner)\" matTooltip=\"Appeler\">\n                <mat-icon>call</mat-icon>\n              </button>\n            </div>\n            \n            <div class=\"detail-row\" *ngIf=\"partner.email\">\n              <mat-icon>email</mat-icon>\n              <span>{{ partner.email }}</span>\n              <button mat-icon-button (click)=\"contactPartner(partner)\" matTooltip=\"Envoyer un email\">\n                <mat-icon>mail</mat-icon>\n              </button>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- User points status -->\n    <mat-card class=\"detail-card points-status-card\" *ngIf=\"currentUser\">\n      <mat-card-content>\n        <div class=\"points-status\">\n          <div class=\"current-points\">\n            <mat-icon>account_balance_wallet</mat-icon>\n            <div>\n              <h4>Vos points actuels</h4>\n              <p class=\"points-value\">{{ currentUser.points }} points</p>\n            </div>\n          </div>\n          \n          <div class=\"points-after\" *ngIf=\"canAfford(reward)\">\n            <mat-icon>trending_down</mat-icon>\n            <div>\n              <h4>Après échange</h4>\n              <p class=\"points-value\">{{ currentUser.points - reward.pointsRequired }} points</p>\n            </div>\n          </div>\n          \n          <div class=\"points-needed\" *ngIf=\"!canAfford(reward)\">\n            <mat-icon>add_circle</mat-icon>\n            <div>\n              <h4>Points nécessaires</h4>\n              <p class=\"points-value needed\">{{ reward.pointsRequired - currentUser.points }} points</p>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Action buttons -->\n  <div class=\"action-section\">\n    <div class=\"action-buttons\">\n      <button mat-raised-button \n              [color]=\"canAfford(reward) ? 'primary' : 'basic'\"\n              [disabled]=\"!canAfford(reward) || !reward.availableQuantity || isExchanging\"\n              (click)=\"exchangeReward(reward)\"\n              class=\"exchange-button\">\n        <mat-spinner diameter=\"24\" *ngIf=\"isExchanging\"></mat-spinner>\n        <mat-icon *ngIf=\"!isExchanging\">{{ canAfford(reward) ? 'redeem' : 'lock' }}</mat-icon>\n        <span *ngIf=\"!isExchanging\">\n          {{ canAfford(reward) ? 'Échanger maintenant' : 'Points insuffisants' }}\n        </span>\n      </button>\n      \n      <button mat-stroked-button \n              routerLink=\"/qr-scanner\" \n              class=\"scan-button\"\n              *ngIf=\"!canAfford(reward)\">\n        <mat-icon>qr_code_scanner</mat-icon>\n        Gagner des points\n      </button>\n    </div>\n    \n    <p class=\"exchange-note\" *ngIf=\"canAfford(reward)\">\n      <mat-icon>info</mat-icon>\n      Vous recevrez un code d'échange à présenter au partenaire.\n    </p>\n  </div>\n</div>\n\n<!-- Loading state -->\n<div class=\"loading-container\" *ngIf=\"!(reward$ | async)\">\n  <mat-card class=\"loading-card\">\n    <mat-card-content>\n      <div class=\"loading-content\">\n        <mat-spinner diameter=\"48\"></mat-spinner>\n        <h3>Chargement des détails...</h3>\n      </div>\n    </mat-card-content>\n  </mat-card>\n</div>\n"], "mappings": ";AAOA,SAAcA,SAAS,QAAQ,gBAAgB;;;;;;;;ICuBrCC,EAJF,CAAAC,cAAA,mBAGkB,eACN;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAiDTH,EADF,CAAAC,cAAA,cAAqD,eACzC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAmD;IAC3DF,EAD2D,CAAAG,YAAA,EAAO,EAC5D;;;;;IADEH,EAAA,CAAAI,SAAA,GAAmD;IAAnDJ,EAAA,CAAAK,kBAAA,qBAAAC,MAAA,CAAAC,UAAA,CAAAC,SAAA,CAAAC,UAAA,MAAmD;;;;;;IAoBrDT,EADF,CAAAC,cAAA,cAAgD,eACpC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClCH,EAAA,CAAAC,cAAA,iBAA2F;IAAnED,EAAA,CAAAU,UAAA,mBAAAC,gFAAA;MAAAX,EAAA,CAAAY,aAAA,CAAAC,GAAA;MAAA,MAAAC,UAAA,GAAAd,EAAA,CAAAe,aAAA,GAAAC,IAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAiB,WAAA,CAASX,MAAA,CAAAY,aAAA,CAAAJ,UAAA,CAAsB;IAAA,EAAC;IACtDd,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAExBF,EAFwB,CAAAG,YAAA,EAAW,EACxB,EACL;;;;IAJEH,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAmB,iBAAA,CAAAL,UAAA,CAAAM,OAAA,CAAqB;;;;;;IAO3BpB,EADF,CAAAC,cAAA,cAA8C,eAClC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChCH,EAAA,CAAAC,cAAA,iBAA+E;IAAvDD,EAAA,CAAAU,UAAA,mBAAAW,gFAAA;MAAArB,EAAA,CAAAY,aAAA,CAAAU,GAAA;MAAA,MAAAR,UAAA,GAAAd,EAAA,CAAAe,aAAA,GAAAC,IAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAiB,WAAA,CAASX,MAAA,CAAAiB,cAAA,CAAAT,UAAA,CAAuB;IAAA,EAAC;IACvDd,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAElBF,EAFkB,CAAAG,YAAA,EAAW,EAClB,EACL;;;;IAJEH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAmB,iBAAA,CAAAL,UAAA,CAAAU,KAAA,CAAmB;;;;;;IAOzBxB,EADF,CAAAC,cAAA,cAA8C,eAClC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChCH,EAAA,CAAAC,cAAA,iBAAwF;IAAhED,EAAA,CAAAU,UAAA,mBAAAe,gFAAA;MAAAzB,EAAA,CAAAY,aAAA,CAAAc,GAAA;MAAA,MAAAZ,UAAA,GAAAd,EAAA,CAAAe,aAAA,GAAAC,IAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAiB,WAAA,CAASX,MAAA,CAAAiB,cAAA,CAAAT,UAAA,CAAuB;IAAA,EAAC;IACvDd,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAElBF,EAFkB,CAAAG,YAAA,EAAW,EAClB,EACL;;;;IAJEH,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAmB,iBAAA,CAAAL,UAAA,CAAAa,KAAA,CAAmB;;;;;IA5B7B3B,EAHN,CAAAC,cAAA,mBAAkE,sBAC/C,qBACC,eACJ;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,oCACF;IACFF,EADE,CAAAG,YAAA,EAAiB,EACD;IAGdH,EAFJ,CAAAC,cAAA,uBAAkB,cACU,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEhCH,EAAA,CAAAC,cAAA,eAA6B;IAiB3BD,EAhBA,CAAA4B,UAAA,KAAAC,uDAAA,kBAAgD,KAAAC,uDAAA,kBAQF,KAAAC,uDAAA,kBAQA;IAUtD/B,EAHM,CAAAG,YAAA,EAAM,EACF,EACW,EACV;;;;IA9BDH,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAmB,iBAAA,CAAAL,UAAA,CAAAkB,IAAA,CAAkB;IACnBhC,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAmB,iBAAA,CAAAL,UAAA,CAAAmB,WAAA,CAAyB;IAGDjC,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAkC,UAAA,SAAApB,UAAA,CAAAM,OAAA,CAAqB;IAQrBpB,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAkC,UAAA,SAAApB,UAAA,CAAAU,KAAA,CAAmB;IAQnBxB,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAkC,UAAA,SAAApB,UAAA,CAAAa,KAAA,CAAmB;;;;;IAyB5C3B,EADF,CAAAC,cAAA,cAAoD,eACxC;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEhCH,EADF,CAAAC,cAAA,UAAK,SACC;IAAAD,EAAA,CAAAE,MAAA,8BAAa;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACtBH,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAuD;IAEnFF,EAFmF,CAAAG,YAAA,EAAI,EAC/E,EACF;;;;;IAFsBH,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAA6B,WAAA,CAAAC,MAAA,GAAA5B,SAAA,CAAA6B,cAAA,YAAuD;;;;;IAKjFrC,EADF,CAAAC,cAAA,cAAsD,eAC1C;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE7BH,EADF,CAAAC,cAAA,UAAK,SACC;IAAAD,EAAA,CAAAE,MAAA,8BAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAuD;IAE1FF,EAF0F,CAAAG,YAAA,EAAI,EACtF,EACF;;;;;IAF6BH,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAK,kBAAA,KAAAG,SAAA,CAAA6B,cAAA,GAAA/B,MAAA,CAAA6B,WAAA,CAAAC,MAAA,YAAuD;;;;;IAnBxFpC,EAJR,CAAAC,cAAA,mBAAqE,uBACjD,cACW,cACG,eAChB;IAAAD,EAAA,CAAAE,MAAA,6BAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEzCH,EADF,CAAAC,cAAA,UAAK,SACC;IAAAD,EAAA,CAAAE,MAAA,yBAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,YAAwB;IAAAD,EAAA,CAAAE,MAAA,IAA+B;IAE3DF,EAF2D,CAAAG,YAAA,EAAI,EACvD,EACF;IAUNH,EARA,CAAA4B,UAAA,KAAAU,uDAAA,kBAAoD,KAAAC,uDAAA,kBAQE;IAS5DvC,EAFI,CAAAG,YAAA,EAAM,EACW,EACV;;;;;IArBuBH,EAAA,CAAAI,SAAA,IAA+B;IAA/BJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAA6B,WAAA,CAAAC,MAAA,YAA+B;IAIhCpC,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAkC,SAAA,CAAAhC,SAAA,EAAuB;IAQtBR,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAkC,UAAA,UAAA5B,MAAA,CAAAkC,SAAA,CAAAhC,SAAA,EAAwB;;;;;IAoBtDR,EAAA,CAAAyC,SAAA,sBAA8D;;;;;IAC9DzC,EAAA,CAAAC,cAAA,eAAgC;IAAAD,EAAA,CAAAE,MAAA,GAA2C;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAAtDH,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAAmB,iBAAA,CAAAb,MAAA,CAAAkC,SAAA,CAAAhC,SAAA,sBAA2C;;;;;IAC3ER,EAAA,CAAAC,cAAA,WAA4B;IAC1BD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;;IADLH,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAkC,SAAA,CAAAhC,SAAA,4DACF;;;;;IAOAR,EAJF,CAAAC,cAAA,iBAGmC,eACvB;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IAITH,EADF,CAAAC,cAAA,YAAmD,eACvC;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,kFACF;IAAAF,EAAA,CAAAG,YAAA,EAAI;;;;;;IAzLJH,EAHJ,CAAAC,cAAA,aAAuE,aAE1C,gBACsC;IAAvCD,EAAA,CAAAU,UAAA,mBAAAgC,6DAAA;MAAA1C,EAAA,CAAAY,aAAA,CAAA+B,GAAA;MAAA,MAAArC,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAiB,WAAA,CAASX,MAAA,CAAAsC,MAAA,EAAQ;IAAA,EAAC;IACxC5C,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,iBAAU;IACtBF,EADsB,CAAAG,YAAA,EAAW,EACxB;IACTH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,yCAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,gBAA2E;IAAnDD,EAAA,CAAAU,UAAA,mBAAAmC,6DAAA;MAAA,MAAArC,SAAA,GAAAR,EAAA,CAAAY,aAAA,CAAA+B,GAAA,EAAA3B,IAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAiB,WAAA,CAASX,MAAA,CAAAwC,WAAA,CAAAtC,SAAA,CAAmB;IAAA,EAAC;IACnDR,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAEnBF,EAFmB,CAAAG,YAAA,EAAW,EACnB,EACL;IAIJH,EADF,CAAAC,cAAA,cAAkC,cACR;IACtBD,EAAA,CAAAyC,SAAA,cAE+D;IAK3DzC,EAFJ,CAAAC,cAAA,cAAyB,oBACsC,gBACjD;IAAAD,EAAA,CAAAE,MAAA,IAAsC;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3DH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAEXH,EAAA,CAAA4B,UAAA,KAAAmB,gDAAA,uBAGkB;IAKtB/C,EADE,CAAAG,YAAA,EAAM,EACF;IAIFH,EAFJ,CAAAC,cAAA,eAA0B,eACU,UAC5B;IAAAD,EAAA,CAAAE,MAAA,IAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEzBH,EADF,CAAAC,cAAA,eAA0B,gBACd;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAE5CF,EAF4C,CAAAG,YAAA,EAAO,EAC3C,EACF;IAENH,EAAA,CAAAC,cAAA,aAA8B;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAItDH,EAFJ,CAAAC,cAAA,eAAyB,eACA,gBACX;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAChCF,EADgC,CAAAG,YAAA,EAAO,EACjC;IAGJH,EADF,CAAAC,cAAA,eAAuB,gBACX;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IACzBF,EADyB,CAAAG,YAAA,EAAO,EAC1B;IAGJH,EADF,CAAAC,cAAA,eAAuB,oBACqC;IACxDD,EAAA,CAAAE,MAAA,IACF;IAIRF,EAJQ,CAAAG,YAAA,EAAW,EACP,EACF,EACF,EACF;IAQEH,EALR,CAAAC,cAAA,eAAqC,oBAEL,uBACX,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,kCACF;IACFF,EADE,CAAAG,YAAA,EAAiB,EACD;IAEhBH,EADF,CAAAC,cAAA,wBAAkB,SACb;IAAAD,EAAA,CAAAE,MAAA,IAAsD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE7DH,EAAA,CAAA4B,UAAA,KAAAoB,2CAAA,kBAAqD;IAKzDhD,EADE,CAAAG,YAAA,EAAmB,EACV;IAGXH,EAAA,CAAA4B,UAAA,KAAAqB,gDAAA,wBAAkE;;IA0ClEjD,EAAA,CAAA4B,UAAA,KAAAsB,gDAAA,wBAAqE;IA6BvElD,EAAA,CAAAG,YAAA,EAAM;IAKFH,EAFJ,CAAAC,cAAA,eAA4B,eACE,kBAKM;IADxBD,EAAA,CAAAU,UAAA,mBAAAyC,8DAAA;MAAA,MAAA3C,SAAA,GAAAR,EAAA,CAAAY,aAAA,CAAA+B,GAAA,EAAA3B,IAAA;MAAA,MAAAV,MAAA,GAAAN,EAAA,CAAAe,aAAA;MAAA,OAAAf,EAAA,CAAAiB,WAAA,CAASX,MAAA,CAAA8C,cAAA,CAAA5C,SAAA,CAAsB;IAAA,EAAC;IAItCR,EAFA,CAAA4B,UAAA,KAAAyB,mDAAA,0BAAgD,KAAAC,gDAAA,uBAChB,KAAAC,4CAAA,mBACJ;IAG9BvD,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAA4B,UAAA,KAAA4B,8CAAA,qBAGmC;IAIrCxD,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAA4B,UAAA,KAAA6B,yCAAA,gBAAmD;IAKvDzD,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IA/KKH,EAAA,CAAAI,SAAA,IAAiE;IACjEJ,EADA,CAAAkC,UAAA,QAAA1B,SAAA,CAAAkD,QAAA,4CAAA1D,EAAA,CAAA2D,aAAA,CAAiE,QAAAnD,SAAA,CAAAoD,KAAA,CAC7C;IAKS5D,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAkC,UAAA,oBAAmB;IACvClC,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAmB,iBAAA,CAAAb,MAAA,CAAAuD,eAAA,CAAArD,SAAA,CAAAsD,QAAA,EAAsC;IAChD9D,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAyD,gBAAA,CAAAvD,SAAA,CAAAsD,QAAA,OACF;IAEW9D,EAAA,CAAAI,SAAA,EAA4B;IAA5BJ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA0D,cAAA,CAAAxD,SAAA,EAA4B;IAYnCR,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAmB,iBAAA,CAAAX,SAAA,CAAAoD,KAAA,CAAkB;IAGd5D,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAK,kBAAA,KAAAG,SAAA,CAAA6B,cAAA,YAAkC;IAIdrC,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmB,iBAAA,CAAAX,SAAA,CAAAyB,WAAA,CAAwB;IAK5CjC,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAmB,iBAAA,CAAAX,SAAA,CAAAyD,WAAA,CAAwB;IAKxBjE,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAmB,iBAAA,CAAAX,SAAA,CAAA0D,IAAA,CAAiB;IAIblE,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAkC,UAAA,UAAA5B,MAAA,CAAA6D,oBAAA,CAAA3D,SAAA,EAAsC;IAC9CR,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAA8D,mBAAA,CAAA5D,SAAA,OACF;IAiBCR,EAAA,CAAAI,SAAA,IAAsD;IAAtDJ,EAAA,CAAAmB,iBAAA,CAAAX,SAAA,CAAA6D,KAAA,0CAAsD;IAE7BrE,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAkC,UAAA,SAAA1B,SAAA,CAAAC,UAAA,CAAuB;IAQxBT,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAkC,UAAA,SAAAlC,EAAA,CAAAsE,WAAA,SAAAhE,MAAA,CAAAiE,QAAA,EAAuB;IA0CJvE,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAA6B,WAAA,CAAiB;IAmCzDnC,EAAA,CAAAI,SAAA,GAAiD;IACjDJ,EADA,CAAAkC,UAAA,UAAA5B,MAAA,CAAAkC,SAAA,CAAAhC,SAAA,wBAAiD,cAAAF,MAAA,CAAAkC,SAAA,CAAAhC,SAAA,MAAAA,SAAA,CAAAgE,iBAAA,IAAAlE,MAAA,CAAAmE,YAAA,CAC2B;IAGtDzE,EAAA,CAAAI,SAAA,EAAkB;IAAlBJ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAmE,YAAA,CAAkB;IACnCzE,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAkC,UAAA,UAAA5B,MAAA,CAAAmE,YAAA,CAAmB;IACvBzE,EAAA,CAAAI,SAAA,EAAmB;IAAnBJ,EAAA,CAAAkC,UAAA,UAAA5B,MAAA,CAAAmE,YAAA,CAAmB;IAQnBzE,EAAA,CAAAI,SAAA,EAAwB;IAAxBJ,EAAA,CAAAkC,UAAA,UAAA5B,MAAA,CAAAkC,SAAA,CAAAhC,SAAA,EAAwB;IAMTR,EAAA,CAAAI,SAAA,EAAuB;IAAvBJ,EAAA,CAAAkC,UAAA,SAAA5B,MAAA,CAAAkC,SAAA,CAAAhC,SAAA,EAAuB;;;;;IAW/CR,EAHN,CAAAC,cAAA,cAA0D,mBACzB,uBACX,cACa;IAC3BD,EAAA,CAAAyC,SAAA,sBAAyC;IACzCzC,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,qCAAyB;IAIrCF,EAJqC,CAAAG,YAAA,EAAK,EAC9B,EACW,EACV,EACP;;;AD5LN,OAAM,MAAOuE,qBAAqB;EAMhCC,YACUC,KAAqB,EACrBC,MAAc,EACdC,cAA8B,EAC9BC,WAAwB,EACxBC,QAAqB;IAJrB,KAAAJ,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IARlB,KAAA7C,WAAW,GAAgB,IAAI;IAC/B,KAAAsC,YAAY,GAAG,KAAK;IASlB,IAAI,CAACQ,OAAO,GAAG,IAAI,CAACL,KAAK,CAACM,MAAM,CAACC,IAAI,CACnCpF,SAAS,CAACmF,MAAM,IAAI,IAAI,CAACJ,cAAc,CAACM,aAAa,CAACF,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CACrE;IAED,IAAI,CAACX,QAAQ,GAAG,IAAI,CAACU,OAAO,CAACE,IAAI,CAC/BpF,SAAS,CAACsF,MAAM,IACdA,MAAM,GAAG,IAAI,CAACP,cAAc,CAACQ,cAAc,CAACD,MAAM,CAACE,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CACvE,CACF;EACH;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACT,WAAW,CAACU,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACxD,WAAW,GAAGwD,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAnD,SAASA,CAAC6C,MAAc;IACtB,OAAO,IAAI,CAAClD,WAAW,GAAG,IAAI,CAACA,WAAW,CAACC,MAAM,IAAIiD,MAAM,CAAChD,cAAc,GAAG,KAAK;EACpF;EAEMe,cAAcA,CAACiC,MAAc;IAAA,IAAAO,KAAA;IAAA,OAAAC,iBAAA;MACjC,IAAI,CAACD,KAAI,CAACzD,WAAW,EAAE;QACrByD,KAAI,CAACZ,QAAQ,CAACc,IAAI,CAAC,uDAAuD,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACzG;;MAGF,IAAI,CAACH,KAAI,CAACpD,SAAS,CAAC6C,MAAM,CAAC,EAAE;QAC3BO,KAAI,CAACZ,QAAQ,CAACc,IAAI,CAAC,2CAA2C,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC7F;;MAGF,IAAIV,MAAM,CAACb,iBAAiB,KAAKwB,SAAS,IAAIX,MAAM,CAACb,iBAAiB,IAAI,CAAC,EAAE;QAC3EoB,KAAI,CAACZ,QAAQ,CAACc,IAAI,CAAC,yCAAyC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC3F;;MAGFH,KAAI,CAACnB,YAAY,GAAG,IAAI;MAExB,IAAI;QACF,MAAMwB,QAAQ,SAASL,KAAI,CAACd,cAAc,CAAC1B,cAAc,CAACiC,MAAM,CAACa,EAAE,EAAEN,KAAI,CAACzD,WAAW,CAACgE,GAAG,CAAC;QAE1FP,KAAI,CAACZ,QAAQ,CAACc,IAAI,CAChB,oDAAoDG,QAAQ,CAACG,YAAY,EAAE,EAC3E,QAAQ,EACR;UAAEL,QAAQ,EAAE;QAAI,CAAE,CACnB;QAED;QACAH,KAAI,CAACb,WAAW,CAACsB,kBAAkB,EAAE;QAErC;QACAT,KAAI,CAACf,MAAM,CAACyB,QAAQ,CAAC,CAAC,UAAU,CAAC,EAAE;UAAEC,QAAQ,EAAE;QAAW,CAAE,CAAC;OAE9D,CAAC,OAAOC,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;QACvCZ,KAAI,CAACZ,QAAQ,CAACc,IAAI,CAACU,KAAK,CAACE,OAAO,IAAI,2BAA2B,EAAE,QAAQ,EAAE;UAAEX,QAAQ,EAAE;QAAI,CAAE,CAAC;OAC/F,SAAS;QACRH,KAAI,CAACnB,YAAY,GAAG,KAAK;;IAC1B;EACH;EAEA7B,MAAMA,CAAA;IACJ,IAAI,CAACiC,MAAM,CAACyB,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEAzC,eAAeA,CAACC,QAAgB;IAC9B,MAAM6C,OAAO,GAA8B;MACzC,MAAM,EAAE,YAAY;MACpB,UAAU,EAAE,cAAc;MAC1B,eAAe,EAAE,OAAO;MACxB,UAAU,EAAE,OAAO;MACnB,QAAQ,EAAE,gBAAgB;MAC1B,WAAW,EAAE,QAAQ;MACrB,WAAW,EAAE,gBAAgB;MAC7B,OAAO,EAAE;KACV;IACD,OAAOA,OAAO,CAAC7C,QAAQ,CAAC,IAAI,UAAU;EACxC;EAEAC,gBAAgBA,CAACD,QAAgB;IAC/B,MAAM8C,QAAQ,GAA8B;MAC1C,MAAM,EAAE,cAAc;MACtB,UAAU,EAAE,UAAU;MACtB,eAAe,EAAE,gBAAgB;MACjC,UAAU,EAAE,UAAU;MACtB,QAAQ,EAAE,OAAO;MACjB,WAAW,EAAE,WAAW;MACxB,WAAW,EAAE,WAAW;MACxB,OAAO,EAAE;KACV;IACD,OAAOA,QAAQ,CAAC9C,QAAQ,CAAC,IAAIA,QAAQ;EACvC;EAEAvD,UAAUA,CAACsG,IAAU;IACnB,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAChDC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE;KACP,CAAC;EACJ;EAEAlD,cAAcA,CAACqB,MAAc;IAC3B,IAAI,CAACA,MAAM,CAAC5E,UAAU,EAAE,OAAO,KAAK;IAEpC,MAAM0G,GAAG,GAAG,IAAIL,IAAI,EAAE;IACtB,MAAMrG,UAAU,GAAG,IAAIqG,IAAI,CAACzB,MAAM,CAAC5E,UAAU,CAAC;IAC9C,MAAM2G,eAAe,GAAGC,IAAI,CAACC,IAAI,CAAC,CAAC7G,UAAU,CAAC8G,OAAO,EAAE,GAAGJ,GAAG,CAACI,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEjG,OAAOH,eAAe,IAAI,CAAC,IAAIA,eAAe,GAAG,CAAC;EACpD;EAEAhD,mBAAmBA,CAACiB,MAAc;IAChC,IAAI,CAACA,MAAM,CAACb,iBAAiB,EAAE;MAC7B,OAAO,YAAY;;IAGrB,IAAIa,MAAM,CAACb,iBAAiB,IAAI,CAAC,EAAE;MACjC,OAAO,QAAQ;;IAGjB,IAAIa,MAAM,CAACb,iBAAiB,IAAI,CAAC,EAAE;MACjC,OAAO,YAAYa,MAAM,CAACb,iBAAiB,gBAAgB;;IAG7D,OAAO,GAAGa,MAAM,CAACb,iBAAiB,gBAAgB;EACpD;EAEAL,oBAAoBA,CAACkB,MAAc;IACjC,IAAI,CAACA,MAAM,CAACb,iBAAiB,IAAIa,MAAM,CAACb,iBAAiB,GAAG,CAAC,EAAE;MAC7D,OAAO,SAAS;;IAGlB,IAAIa,MAAM,CAACb,iBAAiB,IAAI,CAAC,EAAE;MACjC,OAAO,MAAM;;IAGf,OAAO,QAAQ;EACjB;EAEA1B,WAAWA,CAACuC,MAAc;IACxB,IAAImC,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAAC;QACd7D,KAAK,EAAE,qBAAqByB,MAAM,CAACzB,KAAK,EAAE;QAC1C8D,IAAI,EAAE,yCAAyCrC,MAAM,CAACpD,WAAW,EAAE;QACnE0F,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC;OACtB,CAAC,CAACC,KAAK,CAACtB,OAAO,CAACD,KAAK,CAAC;KACxB,MAAM;MACL;MACAgB,SAAS,CAACQ,SAAS,CAACC,SAAS,CAACL,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAACI,IAAI,CAAC,MAAK;QAC5D,IAAI,CAAClD,QAAQ,CAACc,IAAI,CAAC,mCAAmC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACvF,CAAC,CAAC;;EAEN;EAEAxE,cAAcA,CAAC4G,OAAgB;IAC7B,IAAIA,OAAO,CAAC3G,KAAK,EAAE;MACjBoG,MAAM,CAAC9B,IAAI,CAAC,OAAOqC,OAAO,CAAC3G,KAAK,EAAE,EAAE,QAAQ,CAAC;KAC9C,MAAM,IAAI2G,OAAO,CAACxG,KAAK,EAAE;MACxBiG,MAAM,CAAC9B,IAAI,CAAC,UAAUqC,OAAO,CAACxG,KAAK,EAAE,EAAE,QAAQ,CAAC;;EAEpD;EAEAT,aAAaA,CAACiH,OAAgB;IAC5B,IAAIA,OAAO,CAACN,QAAQ,EAAE;MACpB,MAAMF,GAAG,GAAG,sDAAsDQ,OAAO,CAACN,QAAQ,CAACO,QAAQ,IAAID,OAAO,CAACN,QAAQ,CAACQ,SAAS,EAAE;MAC3HT,MAAM,CAAC9B,IAAI,CAAC6B,GAAG,EAAE,QAAQ,CAAC;KAC3B,MAAM,IAAIQ,OAAO,CAAC/G,OAAO,EAAE;MAC1B,MAAMuG,GAAG,GAAG,mDAAmDW,kBAAkB,CAACH,OAAO,CAAC/G,OAAO,CAAC,EAAE;MACpGwG,MAAM,CAAC9B,IAAI,CAAC6B,GAAG,EAAE,QAAQ,CAAC;;EAE9B;;;uBAxLWjD,qBAAqB,EAAA1E,EAAA,CAAAuI,iBAAA,CAAAC,EAAA,CAAAC,cAAA,GAAAzI,EAAA,CAAAuI,iBAAA,CAAAC,EAAA,CAAAE,MAAA,GAAA1I,EAAA,CAAAuI,iBAAA,CAAAI,EAAA,CAAAC,cAAA,GAAA5I,EAAA,CAAAuI,iBAAA,CAAAM,EAAA,CAAAC,WAAA,GAAA9I,EAAA,CAAAuI,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAArBtE,qBAAqB;MAAAuE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdlCvJ,EAAA,CAAA4B,UAAA,IAAA6H,oCAAA,mBAAuE;;UAiMvEzJ,EAAA,CAAA4B,UAAA,IAAA8H,oCAAA,iBAA0D;;;;UAjMpB1J,EAAA,CAAAkC,UAAA,SAAAlC,EAAA,CAAAsE,WAAA,OAAAkF,GAAA,CAAAvE,OAAA,EAAsB;UAiM5BjF,EAAA,CAAAI,SAAA,GAAwB;UAAxBJ,EAAA,CAAAkC,UAAA,UAAAlC,EAAA,CAAAsE,WAAA,OAAAkF,GAAA,CAAAvE,OAAA,EAAwB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}