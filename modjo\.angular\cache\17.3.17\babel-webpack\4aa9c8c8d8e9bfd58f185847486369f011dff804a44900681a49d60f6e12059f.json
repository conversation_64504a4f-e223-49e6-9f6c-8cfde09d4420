{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PointsAssignmentComponent } from '../points-assignment/points-assignment.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/qr.service\";\nimport * as i2 from \"../../../../core/services/user.service\";\nimport * as i3 from \"../../../../core/services/auth.service\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nconst _c0 = [\"video\"];\nconst _c1 = [\"canvas\"];\nfunction QrScannerComponent_div_6_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"div\", 25)(2, \"div\", 26);\n    i0.ɵɵelement(3, \"div\", 27)(4, \"div\", 28)(5, \"div\", 29)(6, \"div\", 30);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 31);\n    i0.ɵɵtext(8, \"Placez le QR code dans le cadre\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QrScannerComponent_div_6_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 32);\n    i0.ɵɵlistener(\"click\", function QrScannerComponent_div_6_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.startScanning());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"qr_code_scanner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Commencer le scan \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QrScannerComponent_div_6_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function QrScannerComponent_div_6_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r3);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.stopScanning());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"stop\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Arr\\u00EAter le scan \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QrScannerComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-card\", 16)(2, \"mat-card-content\")(3, \"div\", 17);\n    i0.ɵɵelement(4, \"video\", 18, 0)(6, \"canvas\", 19, 1);\n    i0.ɵɵtemplate(8, QrScannerComponent_div_6_div_8_Template, 9, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 21);\n    i0.ɵɵtemplate(10, QrScannerComponent_div_6_button_10_Template, 4, 0, \"button\", 22)(11, QrScannerComponent_div_6_button_11_Template, 4, 0, \"button\", 23);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"active\", ctx_r1.isScanning);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isScanning);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r1.isScanning);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.isScanning);\n  }\n}\nfunction QrScannerComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-card\", 35)(2, \"mat-card-content\")(3, \"div\", 36)(4, \"mat-icon\", 37);\n    i0.ɵɵtext(5, \"camera_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"Cam\\u00E9ra non disponible\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Votre appareil ne dispose pas de cam\\u00E9ra ou l'acc\\u00E8s a \\u00E9t\\u00E9 refus\\u00E9.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 38);\n    i0.ɵɵlistener(\"click\", function QrScannerComponent_div_7_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.manualQrInput());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"keyboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Saisie manuelle \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction QrScannerComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 39);\n    i0.ɵɵlistener(\"click\", function QrScannerComponent_button_19_Template_button_click_0_listener() {\n      const demo_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.testQrCode(demo_r6.data));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 40)(4, \"span\", 41);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"small\", 42);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const demo_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"animation-delay\", i_r7 * 0.1 + \"s\");\n    i0.ɵɵproperty(\"color\", ctx_r1.getDemoButtonColor(i_r7));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getDemoButtonIcon(demo_r6.label));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(demo_r6.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.getDemoPoints(demo_r6.label));\n  }\n}\nexport class QrScannerComponent {\n  constructor(qrService, userService, authService, dialog, snackBar) {\n    this.qrService = qrService;\n    this.userService = userService;\n    this.authService = authService;\n    this.dialog = dialog;\n    this.snackBar = snackBar;\n    this.isScanning = false;\n    this.hasCamera = false;\n    this.currentUser = null;\n    this.stream = null;\n    // Demo QR codes for testing\n    this.demoQrCodes = [{\n      label: 'Transfert utilisateur (1 pt)',\n      data: 'user:demo123'\n    }, {\n      label: 'Action validateur (10 pts)',\n      data: 'validator:val456:community_service:10'\n    }, {\n      label: 'Récompense partenaire (5 pts)',\n      data: '{\"type\":\"partner_reward\",\"points\":5,\"timestamp\":' + Date.now() + '}'\n    }, {\n      label: 'Bonus système (3 pts)',\n      data: '{\"type\":\"system_bonus\",\"points\":3,\"timestamp\":' + Date.now() + '}'\n    }];\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.checkCameraSupport();\n  }\n  ngOnDestroy() {\n    this.stopScanning();\n  }\n  checkCameraSupport() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const devices = yield navigator.mediaDevices.enumerateDevices();\n        _this.hasCamera = devices.some(device => device.kind === 'videoinput');\n      } catch (error) {\n        console.error('Error checking camera support:', error);\n        _this.hasCamera = false;\n      }\n    })();\n  }\n  startScanning() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.hasCamera) {\n        _this2.snackBar.open('Caméra non disponible', 'Fermer', {\n          duration: 3000\n        });\n        return;\n      }\n      try {\n        _this2.stream = yield navigator.mediaDevices.getUserMedia({\n          video: {\n            facingMode: 'environment'\n          }\n        });\n        _this2.video.nativeElement.srcObject = _this2.stream;\n        _this2.video.nativeElement.play();\n        _this2.isScanning = true;\n        // Start scanning for QR codes\n        _this2.scanInterval = setInterval(() => {\n          _this2.scanForQrCode();\n        }, 500);\n      } catch (error) {\n        console.error('Error starting camera:', error);\n        _this2.snackBar.open('Erreur d\\'accès à la caméra', 'Fermer', {\n          duration: 3000\n        });\n      }\n    })();\n  }\n  stopScanning() {\n    if (this.stream) {\n      this.stream.getTracks().forEach(track => track.stop());\n      this.stream = null;\n    }\n    if (this.scanInterval) {\n      clearInterval(this.scanInterval);\n      this.scanInterval = null;\n    }\n    this.isScanning = false;\n  }\n  scanForQrCode() {\n    if (!this.video.nativeElement || !this.canvas.nativeElement) return;\n    const video = this.video.nativeElement;\n    const canvas = this.canvas.nativeElement;\n    const context = canvas.getContext('2d');\n    if (!context || video.readyState !== video.HAVE_ENOUGH_DATA) return;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    context.drawImage(video, 0, 0, canvas.width, canvas.height);\n    // In a real implementation, you would use a QR code library like @zxing/library\n    // For demo purposes, we'll simulate QR detection\n    this.simulateQrDetection();\n  }\n  simulateQrDetection() {\n    // Simulate random QR code detection for demo\n    if (Math.random() < 0.1) {\n      // 10% chance per scan\n      const randomQr = this.demoQrCodes[Math.floor(Math.random() * this.demoQrCodes.length)];\n      this.processQrCode(randomQr.data);\n    }\n  }\n  processQrCode(qrData) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.stopScanning();\n      try {\n        const parsedData = _this3.qrService.parseQrData(qrData);\n        if (!parsedData) {\n          _this3.snackBar.open('Code QR invalide', 'Fermer', {\n            duration: 3000\n          });\n          return;\n        }\n        const result = yield _this3.qrService.handleQrScan(parsedData);\n        _this3.openPointsAssignmentDialog(result, parsedData);\n      } catch (error) {\n        console.error('Error processing QR code:', error);\n        _this3.snackBar.open('Erreur lors du traitement du QR code', 'Fermer', {\n          duration: 3000\n        });\n      }\n    })();\n  }\n  openPointsAssignmentDialog(scanResult, qrData) {\n    const dialogRef = this.dialog.open(PointsAssignmentComponent, {\n      width: '400px',\n      data: {\n        scanResult,\n        qrData,\n        currentUser: this.currentUser\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.snackBar.open('Points attribués avec succès!', 'Fermer', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Demo methods for testing\n  testQrCode(qrData) {\n    this.processQrCode(qrData);\n  }\n  manualQrInput() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      // In a real app, you might open a dialog for manual input\n      const qrData = prompt('Entrez le code QR manuellement:');\n      if (qrData) {\n        yield _this4.processQrCode(qrData);\n      }\n    })();\n  }\n  static {\n    this.ɵfac = function QrScannerComponent_Factory(t) {\n      return new (t || QrScannerComponent)(i0.ɵɵdirectiveInject(i1.QrService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QrScannerComponent,\n      selectors: [[\"app-qr-scanner\"]],\n      viewQuery: function QrScannerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.video = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.canvas = _t.first);\n        }\n      },\n      decls: 53,\n      vars: 3,\n      consts: [[\"video\", \"\"], [\"canvas\", \"\"], [1, \"scanner-container\"], [1, \"scanner-header\"], [\"class\", \"camera-section\", 4, \"ngIf\"], [\"class\", \"no-camera-section\", 4, \"ngIf\"], [1, \"demo-section\", \"fade-in\"], [1, \"demo-card\", \"floating-card\"], [1, \"demo-buttons\"], [\"mat-raised-button\", \"\", \"class\", \"demo-qr-button\", 3, \"color\", \"animation-delay\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"instructions-section\"], [1, \"instructions-card\"], [1, \"instructions-list\"], [1, \"instruction-item\"], [\"color\", \"primary\"], [1, \"camera-section\"], [1, \"camera-card\"], [1, \"camera-container\"], [\"autoplay\", \"\", \"playsinline\", \"\", 1, \"camera-video\"], [1, \"camera-canvas\", 2, \"display\", \"none\"], [\"class\", \"scanner-overlay\", 4, \"ngIf\"], [1, \"camera-controls\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"class\", \"scan-button\", 3, \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", \"class\", \"stop-button\", 3, \"click\", 4, \"ngIf\"], [1, \"scanner-overlay\"], [1, \"scanner-frame\"], [1, \"scanner-corners\"], [1, \"corner\", \"top-left\"], [1, \"corner\", \"top-right\"], [1, \"corner\", \"bottom-left\"], [1, \"corner\", \"bottom-right\"], [1, \"scanner-instruction\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"scan-button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 1, \"stop-button\", 3, \"click\"], [1, \"no-camera-section\"], [1, \"no-camera-card\"], [1, \"no-camera-content\"], [1, \"no-camera-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-raised-button\", \"\", 1, \"demo-qr-button\", 3, \"click\", \"color\"], [1, \"demo-button-content\"], [1, \"demo-title\"], [1, \"demo-points\"]],\n      template: function QrScannerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"h1\");\n          i0.ɵɵtext(3, \"Scanner QR Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\");\n          i0.ɵɵtext(5, \"Pointez votre cam\\u00E9ra vers un code QR pour gagner des points\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, QrScannerComponent_div_6_Template, 12, 5, \"div\", 4)(7, QrScannerComponent_div_7_Template, 14, 0, \"div\", 5);\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"mat-card\", 7)(10, \"mat-card-header\")(11, \"mat-card-title\")(12, \"mat-icon\");\n          i0.ɵɵtext(13, \"science\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" \\uD83C\\uDFAF Codes QR de d\\u00E9monstration \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"mat-card-content\")(16, \"p\");\n          i0.ɵɵtext(17, \"Testez l'application avec ces codes QR de d\\u00E9monstration :\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 8);\n          i0.ɵɵtemplate(19, QrScannerComponent_button_19_Template, 8, 6, \"button\", 9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(20, \"div\", 10)(21, \"mat-card\", 11)(22, \"mat-card-header\")(23, \"mat-card-title\")(24, \"mat-icon\");\n          i0.ɵɵtext(25, \"help_outline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26, \" Comment \\u00E7a marche ? \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"mat-card-content\")(28, \"div\", 12)(29, \"div\", 13)(30, \"mat-icon\", 14);\n          i0.ɵɵtext(31, \"qr_code_scanner\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\")(33, \"h4\");\n          i0.ɵɵtext(34, \"1. Scanner\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"p\");\n          i0.ɵɵtext(36, \"Pointez votre cam\\u00E9ra vers un code QR valide\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"div\", 13)(38, \"mat-icon\", 14);\n          i0.ɵɵtext(39, \"verified\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\")(41, \"h4\");\n          i0.ɵɵtext(42, \"2. Validation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\");\n          i0.ɵɵtext(44, \"Le code est automatiquement v\\u00E9rifi\\u00E9 et trait\\u00E9\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 13)(46, \"mat-icon\", 14);\n          i0.ɵɵtext(47, \"stars\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\")(49, \"h4\");\n          i0.ɵɵtext(50, \"3. Points\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"p\");\n          i0.ɵɵtext(52, \"Gagnez des points selon l'action effectu\\u00E9e\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasCamera);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasCamera);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.demoQrCodes);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatButton, i9.MatIcon],\n      styles: [\".scanner-container[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n  padding: 16px;\\n}\\n\\n.scanner-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 24px;\\n}\\n\\n.scanner-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n  font-size: 2rem;\\n  font-weight: 600;\\n}\\n\\n.scanner-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 1rem;\\n}\\n\\n.camera-section[_ngcontent-%COMP%], .no-camera-section[_ngcontent-%COMP%], .demo-section[_ngcontent-%COMP%], .instructions-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.camera-card[_ngcontent-%COMP%], .no-camera-card[_ngcontent-%COMP%], .demo-card[_ngcontent-%COMP%], .instructions-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.camera-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  max-width: 400px;\\n  margin: 0 auto;\\n  background: #000;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.camera-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 300px;\\n  object-fit: cover;\\n  display: block;\\n}\\n\\n.camera-video.active[_ngcontent-%COMP%] {\\n  height: 400px;\\n}\\n\\n.scanner-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  background: rgba(0, 0, 0, 0.3);\\n}\\n\\n.scanner-frame[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  position: relative;\\n}\\n\\n.scanner-corners[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.corner[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 20px;\\n  height: 20px;\\n  border: 3px solid #fff;\\n}\\n\\n.corner.top-left[_ngcontent-%COMP%] {\\n  top: 0;\\n  left: 0;\\n  border-right: none;\\n  border-bottom: none;\\n}\\n\\n.corner.top-right[_ngcontent-%COMP%] {\\n  top: 0;\\n  right: 0;\\n  border-left: none;\\n  border-bottom: none;\\n}\\n\\n.corner.bottom-left[_ngcontent-%COMP%] {\\n  bottom: 0;\\n  left: 0;\\n  border-right: none;\\n  border-top: none;\\n}\\n\\n.corner.bottom-right[_ngcontent-%COMP%] {\\n  bottom: 0;\\n  right: 0;\\n  border-left: none;\\n  border-top: none;\\n}\\n\\n.scanner-instruction[_ngcontent-%COMP%] {\\n  color: white;\\n  margin-top: 16px;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n\\n.camera-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 16px;\\n}\\n\\n.scan-button[_ngcontent-%COMP%], .stop-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 24px;\\n  font-size: 1rem;\\n}\\n\\n.no-camera-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 32px;\\n}\\n\\n.no-camera-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  color: #ccc;\\n  margin-bottom: 16px;\\n}\\n\\n.no-camera-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n\\n.no-camera-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: #666;\\n}\\n\\n.demo-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  margin-top: 16px;\\n}\\n\\n.demo-qr-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: 8px;\\n  padding: 12px 16px;\\n  text-align: left;\\n}\\n\\n.instructions-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  margin-top: 16px;\\n}\\n\\n.instruction-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n}\\n\\n.instruction-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n  flex-shrink: 0;\\n}\\n\\n.instruction-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #333;\\n  font-size: 1.1rem;\\n}\\n\\n.instruction-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .scanner-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  \\n  .scanner-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  \\n  .camera-video[_ngcontent-%COMP%] {\\n    height: 250px;\\n  }\\n  \\n  .camera-video.active[_ngcontent-%COMP%] {\\n    height: 300px;\\n  }\\n  \\n  .scanner-frame[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 150px;\\n  }\\n  \\n  .demo-buttons[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  \\n  .demo-qr-button[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 0.9rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["PointsAssignmentComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "QrScannerComponent_div_6_button_10_Template_button_click_0_listener", "ɵɵrestoreView", "_r1", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "startScanning", "QrScannerComponent_div_6_button_11_Template_button_click_0_listener", "_r3", "stopScanning", "ɵɵtemplate", "QrScannerComponent_div_6_div_8_Template", "QrScannerComponent_div_6_button_10_Template", "QrScannerComponent_div_6_button_11_Template", "ɵɵadvance", "ɵɵclassProp", "isScanning", "ɵɵproperty", "QrScannerComponent_div_7_Template_button_click_10_listener", "_r4", "manualQrInput", "QrScannerComponent_button_19_Template_button_click_0_listener", "demo_r6", "_r5", "$implicit", "testQrCode", "data", "ɵɵstyleProp", "i_r7", "getDemoButtonColor", "ɵɵtextInterpolate", "getDemoButtonIcon", "label", "getDemoPoints", "QrScannerComponent", "constructor", "qrService", "userService", "authService", "dialog", "snackBar", "hasCamera", "currentUser", "stream", "demoQrCodes", "Date", "now", "ngOnInit", "currentUser$", "subscribe", "user", "checkCameraSupport", "ngOnDestroy", "_this", "_asyncToGenerator", "devices", "navigator", "mediaDevices", "enumerateDevices", "some", "device", "kind", "error", "console", "_this2", "open", "duration", "getUserMedia", "video", "facingMode", "nativeElement", "srcObject", "play", "scanInterval", "setInterval", "scanForQrCode", "getTracks", "for<PERSON>ach", "track", "stop", "clearInterval", "canvas", "context", "getContext", "readyState", "HAVE_ENOUGH_DATA", "width", "videoWidth", "height", "videoHeight", "drawImage", "simulateQrDetection", "Math", "random", "randomQr", "floor", "length", "processQrCode", "qrData", "_this3", "parsedData", "parseQrData", "result", "handleQrScan", "openPointsAssignmentDialog", "scanResult", "dialogRef", "afterClosed", "_this4", "prompt", "ɵɵdirectiveInject", "i1", "QrService", "i2", "UserService", "i3", "AuthService", "i4", "MatDialog", "i5", "MatSnackBar", "selectors", "viewQuery", "QrScannerComponent_Query", "rf", "ctx", "QrScannerComponent_div_6_Template", "QrScannerComponent_div_7_Template", "QrScannerComponent_button_19_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\qr-scanner\\components\\qr-scanner\\qr-scanner.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\qr-scanner\\components\\qr-scanner\\qr-scanner.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON>roy, ViewChild, ElementRef } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { QrService } from '../../../../core/services/qr.service';\nimport { UserService } from '../../../../core/services/user.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { PointsAssignmentComponent } from '../points-assignment/points-assignment.component';\nimport { QrCodeData, User } from '../../../../core/models';\n\n@Component({\n  selector: 'app-qr-scanner',\n  templateUrl: './qr-scanner.component.html',\n  styleUrls: ['./qr-scanner.component.css']\n})\nexport class QrScannerComponent implements OnInit, On<PERSON><PERSON>roy {\n  @ViewChild('video', { static: false }) video!: ElementRef<HTMLVideoElement>;\n  @ViewChild('canvas', { static: false }) canvas!: ElementRef<HTMLCanvasElement>;\n\n  isScanning = false;\n  hasCamera = false;\n  currentUser: User | null = null;\n  stream: MediaStream | null = null;\n  scanInterval: any;\n\n  // Demo QR codes for testing\n  demoQrCodes = [\n    { label: 'Transfert utilisateur (1 pt)', data: 'user:demo123' },\n    { label: 'Action validateur (10 pts)', data: 'validator:val456:community_service:10' },\n    { label: 'Récompense partenaire (5 pts)', data: '{\"type\":\"partner_reward\",\"points\":5,\"timestamp\":' + Date.now() + '}' },\n    { label: 'Bonus système (3 pts)', data: '{\"type\":\"system_bonus\",\"points\":3,\"timestamp\":' + Date.now() + '}' }\n  ];\n\n  constructor(\n    private qrService: QrService,\n    private userService: UserService,\n    private authService: AuthService,\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.checkCameraSupport();\n  }\n\n  ngOnDestroy(): void {\n    this.stopScanning();\n  }\n\n  async checkCameraSupport(): Promise<void> {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      this.hasCamera = devices.some(device => device.kind === 'videoinput');\n    } catch (error) {\n      console.error('Error checking camera support:', error);\n      this.hasCamera = false;\n    }\n  }\n\n  async startScanning(): Promise<void> {\n    if (!this.hasCamera) {\n      this.snackBar.open('Caméra non disponible', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    try {\n      this.stream = await navigator.mediaDevices.getUserMedia({\n        video: { facingMode: 'environment' }\n      });\n      \n      this.video.nativeElement.srcObject = this.stream;\n      this.video.nativeElement.play();\n      this.isScanning = true;\n\n      // Start scanning for QR codes\n      this.scanInterval = setInterval(() => {\n        this.scanForQrCode();\n      }, 500);\n\n    } catch (error) {\n      console.error('Error starting camera:', error);\n      this.snackBar.open('Erreur d\\'accès à la caméra', 'Fermer', { duration: 3000 });\n    }\n  }\n\n  stopScanning(): void {\n    if (this.stream) {\n      this.stream.getTracks().forEach(track => track.stop());\n      this.stream = null;\n    }\n    \n    if (this.scanInterval) {\n      clearInterval(this.scanInterval);\n      this.scanInterval = null;\n    }\n    \n    this.isScanning = false;\n  }\n\n  private scanForQrCode(): void {\n    if (!this.video.nativeElement || !this.canvas.nativeElement) return;\n\n    const video = this.video.nativeElement;\n    const canvas = this.canvas.nativeElement;\n    const context = canvas.getContext('2d');\n\n    if (!context || video.readyState !== video.HAVE_ENOUGH_DATA) return;\n\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    context.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // In a real implementation, you would use a QR code library like @zxing/library\n    // For demo purposes, we'll simulate QR detection\n    this.simulateQrDetection();\n  }\n\n  private simulateQrDetection(): void {\n    // Simulate random QR code detection for demo\n    if (Math.random() < 0.1) { // 10% chance per scan\n      const randomQr = this.demoQrCodes[Math.floor(Math.random() * this.demoQrCodes.length)];\n      this.processQrCode(randomQr.data);\n    }\n  }\n\n  async processQrCode(qrData: string): Promise<void> {\n    this.stopScanning();\n\n    try {\n      const parsedData = this.qrService.parseQrData(qrData);\n      if (!parsedData) {\n        this.snackBar.open('Code QR invalide', 'Fermer', { duration: 3000 });\n        return;\n      }\n\n      const result = await this.qrService.handleQrScan(parsedData);\n      this.openPointsAssignmentDialog(result, parsedData);\n\n    } catch (error) {\n      console.error('Error processing QR code:', error);\n      this.snackBar.open('Erreur lors du traitement du QR code', 'Fermer', { duration: 3000 });\n    }\n  }\n\n  private openPointsAssignmentDialog(scanResult: any, qrData: QrCodeData): void {\n    const dialogRef = this.dialog.open(PointsAssignmentComponent, {\n      width: '400px',\n      data: {\n        scanResult,\n        qrData,\n        currentUser: this.currentUser\n      }\n    });\n\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.snackBar.open('Points attribués avec succès!', 'Fermer', { duration: 3000 });\n      }\n    });\n  }\n\n  // Demo methods for testing\n  testQrCode(qrData: string): void {\n    this.processQrCode(qrData);\n  }\n\n  async manualQrInput(): Promise<void> {\n    // In a real app, you might open a dialog for manual input\n    const qrData = prompt('Entrez le code QR manuellement:');\n    if (qrData) {\n      await this.processQrCode(qrData);\n    }\n  }\n}\n", "<div class=\"scanner-container\">\n  <!-- Scanner header -->\n  <div class=\"scanner-header\">\n    <h1>Scanner QR Code</h1>\n    <p>Pointez votre caméra vers un code QR pour gagner des points</p>\n  </div>\n\n  <!-- Camera section -->\n  <div class=\"camera-section\" *ngIf=\"hasCamera\">\n    <mat-card class=\"camera-card\">\n      <mat-card-content>\n        <div class=\"camera-container\">\n          <video #video \n                 class=\"camera-video\" \n                 [class.active]=\"isScanning\"\n                 autoplay \n                 playsinline>\n          </video>\n          <canvas #canvas class=\"camera-canvas\" style=\"display: none;\"></canvas>\n          \n          <!-- Scanner overlay -->\n          <div class=\"scanner-overlay\" *ngIf=\"isScanning\">\n            <div class=\"scanner-frame\">\n              <div class=\"scanner-corners\">\n                <div class=\"corner top-left\"></div>\n                <div class=\"corner top-right\"></div>\n                <div class=\"corner bottom-left\"></div>\n                <div class=\"corner bottom-right\"></div>\n              </div>\n            </div>\n            <p class=\"scanner-instruction\">Placez le QR code dans le cadre</p>\n          </div>\n        </div>\n\n        <!-- Camera controls -->\n        <div class=\"camera-controls\">\n          <button mat-raised-button \n                  color=\"primary\" \n                  (click)=\"startScanning()\" \n                  *ngIf=\"!isScanning\"\n                  class=\"scan-button\">\n            <mat-icon>qr_code_scanner</mat-icon>\n            Commencer le scan\n          </button>\n          \n          <button mat-raised-button \n                  color=\"warn\" \n                  (click)=\"stopScanning()\" \n                  *ngIf=\"isScanning\"\n                  class=\"stop-button\">\n            <mat-icon>stop</mat-icon>\n            Arrêter le scan\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- No camera fallback -->\n  <div class=\"no-camera-section\" *ngIf=\"!hasCamera\">\n    <mat-card class=\"no-camera-card\">\n      <mat-card-content>\n        <div class=\"no-camera-content\">\n          <mat-icon class=\"no-camera-icon\">camera_alt</mat-icon>\n          <h3>Caméra non disponible</h3>\n          <p>Votre appareil ne dispose pas de caméra ou l'accès a été refusé.</p>\n          <button mat-raised-button color=\"primary\" (click)=\"manualQrInput()\">\n            <mat-icon>keyboard</mat-icon>\n            Saisie manuelle\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Demo section -->\n  <div class=\"demo-section fade-in\">\n    <mat-card class=\"demo-card floating-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>science</mat-icon>\n          🎯 Codes QR de démonstration\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <p>Testez l'application avec ces codes QR de démonstration :</p>\n        <div class=\"demo-buttons\">\n          <button mat-raised-button\n                  *ngFor=\"let demo of demoQrCodes; let i = index\"\n                  (click)=\"testQrCode(demo.data)\"\n                  [color]=\"getDemoButtonColor(i)\"\n                  class=\"demo-qr-button\"\n                  [style.animation-delay]=\"(i * 0.1) + 's'\">\n            <mat-icon>{{ getDemoButtonIcon(demo.label) }}</mat-icon>\n            <div class=\"demo-button-content\">\n              <span class=\"demo-title\">{{ demo.label }}</span>\n              <small class=\"demo-points\">{{ getDemoPoints(demo.label) }}</small>\n            </div>\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Instructions -->\n  <div class=\"instructions-section\">\n    <mat-card class=\"instructions-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>help_outline</mat-icon>\n          Comment ça marche ?\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"instructions-list\">\n          <div class=\"instruction-item\">\n            <mat-icon color=\"primary\">qr_code_scanner</mat-icon>\n            <div>\n              <h4>1. Scanner</h4>\n              <p>Pointez votre caméra vers un code QR valide</p>\n            </div>\n          </div>\n          \n          <div class=\"instruction-item\">\n            <mat-icon color=\"primary\">verified</mat-icon>\n            <div>\n              <h4>2. Validation</h4>\n              <p>Le code est automatiquement vérifié et traité</p>\n            </div>\n          </div>\n          \n          <div class=\"instruction-item\">\n            <mat-icon color=\"primary\">stars</mat-icon>\n            <div>\n              <h4>3. Points</h4>\n              <p>Gagnez des points selon l'action effectuée</p>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": ";AAMA,SAASA,yBAAyB,QAAQ,kDAAkD;;;;;;;;;;;;;;;ICiB9EC,EAFJ,CAAAC,cAAA,cAAgD,cACnB,cACI;IAI3BD,EAHA,CAAAE,SAAA,cAAmC,cACC,cACE,cACC;IAE3CF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAI,MAAA,sCAA+B;IAChEJ,EADgE,CAAAG,YAAA,EAAI,EAC9D;;;;;;IAKNH,EAAA,CAAAC,cAAA,iBAI4B;IAFpBD,EAAA,CAAAK,UAAA,mBAAAC,oEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IAG/BZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,sBAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAI,MAAA,0BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAETH,EAAA,CAAAC,cAAA,iBAI4B;IAFpBD,EAAA,CAAAK,UAAA,mBAAAQ,oEAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAO,GAAA;MAAA,MAAAL,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAM,YAAA,EAAc;IAAA,EAAC;IAG9Bf,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,WAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAI,MAAA,6BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;IAzCXH,EAHN,CAAAC,cAAA,cAA8C,mBACd,uBACV,cACc;IAO5BD,EANA,CAAAE,SAAA,mBAKQ,oBAC8D;IAGtEF,EAAA,CAAAgB,UAAA,IAAAC,uCAAA,kBAAgD;IAWlDjB,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAA6B;IAU3BD,EATA,CAAAgB,UAAA,KAAAE,2CAAA,qBAI4B,KAAAC,2CAAA,qBASA;IAOpCnB,EAHM,CAAAG,YAAA,EAAM,EACW,EACV,EACP;;;;IA1CSH,EAAA,CAAAoB,SAAA,GAA2B;IAA3BpB,EAAA,CAAAqB,WAAA,WAAAZ,MAAA,CAAAa,UAAA,CAA2B;IAOJtB,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAuB,UAAA,SAAAd,MAAA,CAAAa,UAAA,CAAgB;IAkBrCtB,EAAA,CAAAoB,SAAA,GAAiB;IAAjBpB,EAAA,CAAAuB,UAAA,UAAAd,MAAA,CAAAa,UAAA,CAAiB;IASjBtB,EAAA,CAAAoB,SAAA,EAAgB;IAAhBpB,EAAA,CAAAuB,UAAA,SAAAd,MAAA,CAAAa,UAAA,CAAgB;;;;;;IAezBtB,EAJR,CAAAC,cAAA,cAAkD,mBACf,uBACb,cACe,mBACI;IAAAD,EAAA,CAAAI,MAAA,iBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACtDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,iCAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,gGAAgE;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACvEH,EAAA,CAAAC,cAAA,kBAAoE;IAA1BD,EAAA,CAAAK,UAAA,mBAAAmB,2DAAA;MAAAxB,EAAA,CAAAO,aAAA,CAAAkB,GAAA;MAAA,MAAAhB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAiB,aAAA,EAAe;IAAA,EAAC;IACjE1B,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAI,MAAA,yBACF;IAIRJ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;;;;IAcEH,EAAA,CAAAC,cAAA,iBAKkD;IAH1CD,EAAA,CAAAK,UAAA,mBAAAsB,8DAAA;MAAA,MAAAC,OAAA,GAAA5B,EAAA,CAAAO,aAAA,CAAAsB,GAAA,EAAAC,SAAA;MAAA,MAAArB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAsB,UAAA,CAAAH,OAAA,CAAAI,IAAA,CAAqB;IAAA,EAAC;IAIrChC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,GAAmC;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAEtDH,EADF,CAAAC,cAAA,cAAiC,eACN;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,GAA+B;IAE9DJ,EAF8D,CAAAG,YAAA,EAAQ,EAC9D,EACC;;;;;;IANDH,EAAA,CAAAiC,WAAA,oBAAAC,IAAA,aAAyC;IAFzClC,EAAA,CAAAuB,UAAA,UAAAd,MAAA,CAAA0B,kBAAA,CAAAD,IAAA,EAA+B;IAG3BlC,EAAA,CAAAoB,SAAA,GAAmC;IAAnCpB,EAAA,CAAAoC,iBAAA,CAAA3B,MAAA,CAAA4B,iBAAA,CAAAT,OAAA,CAAAU,KAAA,EAAmC;IAElBtC,EAAA,CAAAoB,SAAA,GAAgB;IAAhBpB,EAAA,CAAAoC,iBAAA,CAAAR,OAAA,CAAAU,KAAA,CAAgB;IACdtC,EAAA,CAAAoB,SAAA,GAA+B;IAA/BpB,EAAA,CAAAoC,iBAAA,CAAA3B,MAAA,CAAA8B,aAAA,CAAAX,OAAA,CAAAU,KAAA,EAA+B;;;ADlFxE,OAAM,MAAOE,kBAAkB;EAkB7BC,YACUC,SAAoB,EACpBC,WAAwB,EACxBC,WAAwB,EACxBC,MAAiB,EACjBC,QAAqB;IAJrB,KAAAJ,SAAS,GAATA,SAAS;IACT,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAnBlB,KAAAxB,UAAU,GAAG,KAAK;IAClB,KAAAyB,SAAS,GAAG,KAAK;IACjB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,MAAM,GAAuB,IAAI;IAGjC;IACA,KAAAC,WAAW,GAAG,CACZ;MAAEZ,KAAK,EAAE,8BAA8B;MAAEN,IAAI,EAAE;IAAc,CAAE,EAC/D;MAAEM,KAAK,EAAE,4BAA4B;MAAEN,IAAI,EAAE;IAAuC,CAAE,EACtF;MAAEM,KAAK,EAAE,+BAA+B;MAAEN,IAAI,EAAE,kDAAkD,GAAGmB,IAAI,CAACC,GAAG,EAAE,GAAG;IAAG,CAAE,EACvH;MAAEd,KAAK,EAAE,uBAAuB;MAAEN,IAAI,EAAE,gDAAgD,GAAGmB,IAAI,CAACC,GAAG,EAAE,GAAG;IAAG,CAAE,CAC9G;EAQE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACT,WAAW,CAACU,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACR,WAAW,GAAGQ,IAAI;IACzB,CAAC,CAAC;IACF,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAC3C,YAAY,EAAE;EACrB;EAEM0C,kBAAkBA,CAAA;IAAA,IAAAE,KAAA;IAAA,OAAAC,iBAAA;MACtB,IAAI;QACF,MAAMC,OAAO,SAASC,SAAS,CAACC,YAAY,CAACC,gBAAgB,EAAE;QAC/DL,KAAI,CAACZ,SAAS,GAAGc,OAAO,CAACI,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,CAAC;OACtE,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDT,KAAI,CAACZ,SAAS,GAAG,KAAK;;IACvB;EACH;EAEMnC,aAAaA,CAAA;IAAA,IAAA0D,MAAA;IAAA,OAAAV,iBAAA;MACjB,IAAI,CAACU,MAAI,CAACvB,SAAS,EAAE;QACnBuB,MAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,uBAAuB,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACzE;;MAGF,IAAI;QACFF,MAAI,CAACrB,MAAM,SAASa,SAAS,CAACC,YAAY,CAACU,YAAY,CAAC;UACtDC,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAa;SACnC,CAAC;QAEFL,MAAI,CAACI,KAAK,CAACE,aAAa,CAACC,SAAS,GAAGP,MAAI,CAACrB,MAAM;QAChDqB,MAAI,CAACI,KAAK,CAACE,aAAa,CAACE,IAAI,EAAE;QAC/BR,MAAI,CAAChD,UAAU,GAAG,IAAI;QAEtB;QACAgD,MAAI,CAACS,YAAY,GAAGC,WAAW,CAAC,MAAK;UACnCV,MAAI,CAACW,aAAa,EAAE;QACtB,CAAC,EAAE,GAAG,CAAC;OAER,CAAC,OAAOb,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CE,MAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,6BAA6B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;;IAChF;EACH;EAEAzD,YAAYA,CAAA;IACV,IAAI,IAAI,CAACkC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACiC,SAAS,EAAE,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,EAAE,CAAC;MACtD,IAAI,CAACpC,MAAM,GAAG,IAAI;;IAGpB,IAAI,IAAI,CAAC8B,YAAY,EAAE;MACrBO,aAAa,CAAC,IAAI,CAACP,YAAY,CAAC;MAChC,IAAI,CAACA,YAAY,GAAG,IAAI;;IAG1B,IAAI,CAACzD,UAAU,GAAG,KAAK;EACzB;EAEQ2D,aAAaA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACP,KAAK,CAACE,aAAa,IAAI,CAAC,IAAI,CAACW,MAAM,CAACX,aAAa,EAAE;IAE7D,MAAMF,KAAK,GAAG,IAAI,CAACA,KAAK,CAACE,aAAa;IACtC,MAAMW,MAAM,GAAG,IAAI,CAACA,MAAM,CAACX,aAAa;IACxC,MAAMY,OAAO,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IAEvC,IAAI,CAACD,OAAO,IAAId,KAAK,CAACgB,UAAU,KAAKhB,KAAK,CAACiB,gBAAgB,EAAE;IAE7DJ,MAAM,CAACK,KAAK,GAAGlB,KAAK,CAACmB,UAAU;IAC/BN,MAAM,CAACO,MAAM,GAAGpB,KAAK,CAACqB,WAAW;IACjCP,OAAO,CAACQ,SAAS,CAACtB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEa,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACO,MAAM,CAAC;IAE3D;IACA;IACA,IAAI,CAACG,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB;IACA,IAAIC,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG,EAAE;MAAE;MACzB,MAAMC,QAAQ,GAAG,IAAI,CAAClD,WAAW,CAACgD,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,MAAM,EAAE,GAAG,IAAI,CAACjD,WAAW,CAACoD,MAAM,CAAC,CAAC;MACtF,IAAI,CAACC,aAAa,CAACH,QAAQ,CAACpE,IAAI,CAAC;;EAErC;EAEMuE,aAAaA,CAACC,MAAc;IAAA,IAAAC,MAAA;IAAA,OAAA7C,iBAAA;MAChC6C,MAAI,CAAC1F,YAAY,EAAE;MAEnB,IAAI;QACF,MAAM2F,UAAU,GAAGD,MAAI,CAAC/D,SAAS,CAACiE,WAAW,CAACH,MAAM,CAAC;QACrD,IAAI,CAACE,UAAU,EAAE;UACfD,MAAI,CAAC3D,QAAQ,CAACyB,IAAI,CAAC,kBAAkB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACpE;;QAGF,MAAMoC,MAAM,SAASH,MAAI,CAAC/D,SAAS,CAACmE,YAAY,CAACH,UAAU,CAAC;QAC5DD,MAAI,CAACK,0BAA0B,CAACF,MAAM,EAAEF,UAAU,CAAC;OAEpD,CAAC,OAAOtC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDqC,MAAI,CAAC3D,QAAQ,CAACyB,IAAI,CAAC,sCAAsC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;;IACzF;EACH;EAEQsC,0BAA0BA,CAACC,UAAe,EAAEP,MAAkB;IACpE,MAAMQ,SAAS,GAAG,IAAI,CAACnE,MAAM,CAAC0B,IAAI,CAACxE,yBAAyB,EAAE;MAC5D6F,KAAK,EAAE,OAAO;MACd5D,IAAI,EAAE;QACJ+E,UAAU;QACVP,MAAM;QACNxD,WAAW,EAAE,IAAI,CAACA;;KAErB,CAAC;IAEFgE,SAAS,CAACC,WAAW,EAAE,CAAC1D,SAAS,CAACqD,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAE;QACV,IAAI,CAAC9D,QAAQ,CAACyB,IAAI,CAAC,+BAA+B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;;IAErF,CAAC,CAAC;EACJ;EAEA;EACAzC,UAAUA,CAACyE,MAAc;IACvB,IAAI,CAACD,aAAa,CAACC,MAAM,CAAC;EAC5B;EAEM9E,aAAaA,CAAA;IAAA,IAAAwF,MAAA;IAAA,OAAAtD,iBAAA;MACjB;MACA,MAAM4C,MAAM,GAAGW,MAAM,CAAC,iCAAiC,CAAC;MACxD,IAAIX,MAAM,EAAE;QACV,MAAMU,MAAI,CAACX,aAAa,CAACC,MAAM,CAAC;;IACjC;EACH;;;uBAhKWhE,kBAAkB,EAAAxC,EAAA,CAAAoH,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAAtH,EAAA,CAAAoH,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAxH,EAAA,CAAAoH,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA1H,EAAA,CAAAoH,iBAAA,CAAAO,EAAA,CAAAC,SAAA,GAAA5H,EAAA,CAAAoH,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAlBtF,kBAAkB;MAAAuF,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCX3BlI,EAHJ,CAAAC,cAAA,aAA+B,aAED,SACtB;UAAAD,EAAA,CAAAI,MAAA,sBAAe;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAI,MAAA,uEAA2D;UAChEJ,EADgE,CAAAG,YAAA,EAAI,EAC9D;UAsDNH,EAnDA,CAAAgB,UAAA,IAAAoH,iCAAA,kBAA8C,IAAAC,iCAAA,kBAmDI;UAqB1CrI,EAJR,CAAAC,cAAA,aAAkC,kBACU,uBACvB,sBACC,gBACJ;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAI,MAAA,qDACF;UACFJ,EADE,CAAAG,YAAA,EAAiB,EACD;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,SACb;UAAAD,EAAA,CAAAI,MAAA,sEAAyD;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAChEH,EAAA,CAAAC,cAAA,cAA0B;UACxBD,EAAA,CAAAgB,UAAA,KAAAsH,qCAAA,oBAKkD;UAU1DtI,EAHM,CAAAG,YAAA,EAAM,EACW,EACV,EACP;UAOEH,EAJR,CAAAC,cAAA,eAAkC,oBACI,uBACjB,sBACC,gBACJ;UAAAD,EAAA,CAAAI,MAAA,oBAAY;UAAAJ,EAAA,CAAAG,YAAA,EAAW;UACjCH,EAAA,CAAAI,MAAA,kCACF;UACFJ,EADE,CAAAG,YAAA,EAAiB,EACD;UAIZH,EAHN,CAAAC,cAAA,wBAAkB,eACe,eACC,oBACF;UAAAD,EAAA,CAAAI,MAAA,uBAAe;UAAAJ,EAAA,CAAAG,YAAA,EAAW;UAElDH,EADF,CAAAC,cAAA,WAAK,UACC;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,wDAA2C;UAElDJ,EAFkD,CAAAG,YAAA,EAAI,EAC9C,EACF;UAGJH,EADF,CAAAC,cAAA,eAA8B,oBACF;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAW;UAE3CH,EADF,CAAAC,cAAA,WAAK,UACC;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,oEAA6C;UAEpDJ,EAFoD,CAAAG,YAAA,EAAI,EAChD,EACF;UAGJH,EADF,CAAAC,cAAA,eAA8B,oBACF;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAW;UAExCH,EADF,CAAAC,cAAA,WAAK,UACC;UAAAD,EAAA,CAAAI,MAAA,iBAAS;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,uDAA0C;UAO3DJ,EAP2D,CAAAG,YAAA,EAAI,EAC7C,EACF,EACF,EACW,EACV,EACP,EACF;;;UAtIyBH,EAAA,CAAAoB,SAAA,GAAe;UAAfpB,EAAA,CAAAuB,UAAA,SAAA4G,GAAA,CAAApF,SAAA,CAAe;UAmDZ/C,EAAA,CAAAoB,SAAA,EAAgB;UAAhBpB,EAAA,CAAAuB,UAAA,UAAA4G,GAAA,CAAApF,SAAA,CAAgB;UA6Bf/C,EAAA,CAAAoB,SAAA,IAAgB;UAAhBpB,EAAA,CAAAuB,UAAA,YAAA4G,GAAA,CAAAjF,WAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}