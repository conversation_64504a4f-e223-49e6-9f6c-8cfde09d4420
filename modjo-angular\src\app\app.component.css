/* Global Styles */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  line-height: 1.6;
  color: #333;
  background-color: #f8f9fa;
}

.app-container {
  min-height: calc(100vh - 60px);
  padding-top: 20px;
  padding-bottom: 40px;
}

h1, h2, h3, h4, h5, h6 {
  font-weight: 600;
  line-height: 1.3;
}

a {
  color: #4a90e2;
  text-decoration: none;
  transition: color 0.3s;
}

a:hover {
  color: #3a7bc8;
}

button {
  cursor: pointer;
}

.loading {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}