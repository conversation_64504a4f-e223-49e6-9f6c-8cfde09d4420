{"ast": null, "code": "import { UserRole } from '../../../../core/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../core/services/dashboard-router.service\";\nimport * as i4 from \"../../../../core/services/mock-data.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/menu\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nimport * as i13 from \"@angular/material/badge\";\nimport * as i14 from \"@angular/material/tooltip\";\nimport * as i15 from \"../../../../shared/pipes/time-ago.pipe\";\nfunction DashboardComponent_div_0_mat_card_89_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 87)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_0_mat_card_89_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 79)(1, \"div\", 80);\n    i0.ɵɵelement(2, \"img\", 81);\n    i0.ɵɵtemplate(3, DashboardComponent_div_0_mat_card_89_div_3_Template, 3, 0, \"div\", 82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 83);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 84)(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"store\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 85)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"button\", 86);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_mat_card_89_Template_button_click_19_listener() {\n      const reward_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.exchangeReward(reward_r4));\n    });\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const reward_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"affordable\", ctx_r1.user.points >= reward_r4.pointsRequired);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", reward_r4.imageUrl, i0.ɵɵsanitizeUrl)(\"alt\", reward_r4.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.user.points >= reward_r4.pointsRequired);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(reward_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reward_r4.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(reward_r4.partnerName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", reward_r4.pointsRequired, \" points\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r1.user.points >= reward_r4.pointsRequired ? \"primary\" : \"\")(\"disabled\", ctx_r1.user.points < reward_r4.pointsRequired);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.user.points >= reward_r4.pointsRequired ? \"\\u00C9changer\" : \"Pas assez de points\", \" \");\n  }\n}\nfunction DashboardComponent_div_0_div_112_div_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 99)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", action_r5.location, \" \");\n  }\n}\nfunction DashboardComponent_div_0_div_112_div_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 100)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const action_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(action_r5.providerName);\n  }\n}\nfunction DashboardComponent_div_0_div_112_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 90)(1, \"div\", 91)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 92)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 93)(8, \"span\", 94);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardComponent_div_0_div_112_div_1_span_11_Template, 4, 1, \"span\", 95);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, DashboardComponent_div_0_div_112_div_1_div_12_Template, 5, 1, \"div\", 96);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 97)(14, \"span\", 98);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 17);\n    i0.ɵɵtext(17, \"pts\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const action_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getActionColor(action_r5.type));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getActionIcon(action_r5.type), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(action_r5.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 8, action_r5.timestamp, \"short\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", action_r5.location);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", action_r5.providerName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"+\", action_r5.points, \"\");\n  }\n}\nfunction DashboardComponent_div_0_div_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 88);\n    i0.ɵɵtemplate(1, DashboardComponent_div_0_div_112_div_1_Template, 18, 11, \"div\", 89);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredHistory);\n  }\n}\nfunction DashboardComponent_div_0_ng_template_113_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 101)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"sentiment_neutral\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Aucune action pour cette p\\u00E9riode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 102);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_ng_template_113_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openQRScanner());\n    });\n    i0.ɵɵtext(6, \" Commencer maintenant \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_0_div_165_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 103);\n    i0.ɵɵelement(1, \"div\", 104);\n    i0.ɵɵelementStart(2, \"span\", 105);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const week_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"height\", week_r7.points / ctx_r1.maxWeeklyPoints * 100 + \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"S\", week_r7.week, \"\");\n  }\n}\nfunction DashboardComponent_div_0_div_166_div_11_div_1_button_12_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r9 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_div_166_div_11_div_1_button_12_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r9);\n      const notification_r10 = i0.ɵɵnextContext().$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(4);\n      return i0.ɵɵresetView(ctx_r1.markAsRead(notification_r10));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"check\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_0_div_166_div_11_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 112)(1, \"div\", 113)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 114)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\", 115);\n    i0.ɵɵtext(10);\n    i0.ɵɵpipe(11, \"timeAgo\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(12, DashboardComponent_div_0_div_166_div_11_div_1_button_12_Template, 3, 0, \"button\", 116);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const notification_r10 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(4);\n    i0.ɵɵclassProp(\"unread\", !notification_r10.read);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getNotificationColor(notification_r10.type));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getNotificationIcon(notification_r10.type), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(notification_r10.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(notification_r10.message);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(11, 9, notification_r10.timestamp));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !notification_r10.read);\n  }\n}\nfunction DashboardComponent_div_0_div_166_div_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 110);\n    i0.ɵɵtemplate(1, DashboardComponent_div_0_div_166_div_11_div_1_Template, 13, 11, \"div\", 111);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.notifications);\n  }\n}\nfunction DashboardComponent_div_0_div_166_ng_template_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 117)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"notifications_off\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Aucune notification\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_0_div_166_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"mat-card\", 107)(2, \"mat-card-header\")(3, \"mat-card-title\")(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"notifications\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(6, \" Notifications \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_div_166_Template_button_click_7_listener() {\n      i0.ɵɵrestoreView(_r8);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.toggleNotifications());\n    });\n    i0.ɵɵelementStart(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"close\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(10, \"mat-card-content\");\n    i0.ɵɵtemplate(11, DashboardComponent_div_0_div_166_div_11_Template, 2, 1, \"div\", 109)(12, DashboardComponent_div_0_div_166_ng_template_12_Template, 5, 0, \"ng-template\", null, 2, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const noNotifications_r11 = i0.ɵɵreference(13);\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.notifications.length > 0)(\"ngIfElse\", noNotifications_r11);\n  }\n}\nfunction DashboardComponent_div_0_div_176_div_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 122)(1, \"div\", 37);\n    i0.ɵɵelement(2, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const badge_r12 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", badge_r12.progress + \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", badge_r12.current, \"/\", badge_r12.target, \"\");\n  }\n}\nfunction DashboardComponent_div_0_div_176_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 118)(1, \"div\", 119)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 120)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(9, DashboardComponent_div_0_div_176_div_9_Template, 5, 4, \"div\", 121);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const badge_r12 = ctx.$implicit;\n    i0.ɵɵclassProp(\"earned\", badge_r12.earned)(\"locked\", !badge_r12.earned);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", badge_r12.earned ? badge_r12.color : \"#ccc\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", badge_r12.icon, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(badge_r12.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(badge_r12.description);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !badge_r12.earned);\n  }\n}\nfunction DashboardComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 5)(1, \"div\", 6)(2, \"div\", 7)(3, \"div\", 8)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"school\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 9)(7, \"h2\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 10);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 11)(12, \"div\", 12)(13, \"div\", 13)(14, \"mat-icon\", 14);\n    i0.ɵɵtext(15, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 15)(17, \"span\", 16);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 17);\n    i0.ɵɵtext(20, \"Points\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(21, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleNotifications());\n    });\n    i0.ɵɵelementStart(22, \"mat-icon\");\n    i0.ɵɵtext(23, \"notifications\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"button\", 19)(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"account_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"mat-menu\", null, 0)(29, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openProfile());\n    });\n    i0.ɵɵelementStart(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\");\n    i0.ɵɵtext(33, \"Mon Profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"button\", 20);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelementStart(35, \"mat-icon\");\n    i0.ɵɵtext(36, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(39, \"div\", 21)(40, \"div\", 22)(41, \"mat-card\", 23)(42, \"mat-card-content\")(43, \"div\", 24)(44, \"div\", 25)(45, \"mat-icon\");\n    i0.ɵɵtext(46, \"qr_code_scanner\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 26)(48, \"h3\");\n    i0.ɵɵtext(49, \"Scanner un QR Code KnowMe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"p\");\n    i0.ɵɵtext(51, \"D\\u00E9couvre un prestataire et gagne des points !\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"button\", 27);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_Template_button_click_52_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openQRScanner());\n    });\n    i0.ɵɵelementStart(53, \"mat-icon\");\n    i0.ɵɵtext(54, \"camera_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(55, \" Scanner \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(56, \"div\", 28)(57, \"mat-card\", 29)(58, \"mat-card-content\")(59, \"h3\");\n    i0.ɵɵtext(60, \"Progression vers ta prochaine r\\u00E9compense\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"div\", 30)(62, \"div\", 31)(63, \"div\", 32);\n    i0.ɵɵelement(64, \"img\", 33);\n    i0.ɵɵelementStart(65, \"div\", 34)(66, \"h4\");\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"p\");\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(70, \"div\", 35)(71, \"span\", 36);\n    i0.ɵɵtext(72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"span\", 17);\n    i0.ɵɵtext(74, \"points restants\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(75, \"div\", 37);\n    i0.ɵɵelement(76, \"div\", 38);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"div\", 39)(78, \"span\");\n    i0.ɵɵtext(79);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(80, \"div\", 40)(81, \"p\");\n    i0.ɵɵtext(82);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(83, \"div\", 41)(84, \"h3\", 42)(85, \"mat-icon\");\n    i0.ɵɵtext(86, \"card_giftcard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(87, \" R\\u00E9compenses disponibles \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"div\", 43);\n    i0.ɵɵtemplate(89, DashboardComponent_div_0_mat_card_89_Template, 21, 12, \"mat-card\", 44);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(90, \"div\", 45)(91, \"div\", 46)(92, \"mat-card\", 47)(93, \"mat-card-header\")(94, \"mat-card-title\")(95, \"mat-icon\");\n    i0.ɵɵtext(96, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(97, \" Historique de mes actions \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"div\", 48)(99, \"mat-form-field\", 49)(100, \"mat-label\");\n    i0.ɵɵtext(101, \"P\\u00E9riode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(102, \"mat-select\", 50);\n    i0.ɵɵtwoWayListener(\"valueChange\", function DashboardComponent_div_0_Template_mat_select_valueChange_102_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedPeriod, $event) || (ctx_r1.selectedPeriod = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectionChange\", function DashboardComponent_div_0_Template_mat_select_selectionChange_102_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.filterHistory());\n    });\n    i0.ɵɵelementStart(103, \"mat-option\", 51);\n    i0.ɵɵtext(104, \"Cette semaine\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(105, \"mat-option\", 52);\n    i0.ɵɵtext(106, \"Ce mois\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(107, \"mat-option\", 53);\n    i0.ɵɵtext(108, \"Cette ann\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(109, \"mat-option\", 54);\n    i0.ɵɵtext(110, \"Tout\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(111, \"mat-card-content\");\n    i0.ɵɵtemplate(112, DashboardComponent_div_0_div_112_Template, 2, 1, \"div\", 55)(113, DashboardComponent_div_0_ng_template_113_Template, 7, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(115, \"div\", 56)(116, \"mat-card\", 57)(117, \"mat-card-header\")(118, \"mat-card-title\")(119, \"mat-icon\");\n    i0.ɵɵtext(120, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(121, \" Mes statistiques ce mois \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(122, \"mat-card-content\")(123, \"div\", 58)(124, \"div\", 59)(125, \"div\", 60)(126, \"mat-icon\");\n    i0.ɵɵtext(127, \"trending_up\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(128, \"div\", 61)(129, \"span\", 62);\n    i0.ɵɵtext(130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(131, \"span\", 63);\n    i0.ɵɵtext(132, \"Points gagn\\u00E9s\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(133, \"div\", 59)(134, \"div\", 60)(135, \"mat-icon\");\n    i0.ɵɵtext(136, \"trending_down\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(137, \"div\", 61)(138, \"span\", 62);\n    i0.ɵɵtext(139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(140, \"span\", 63);\n    i0.ɵɵtext(141, \"Points d\\u00E9pens\\u00E9s\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(142, \"div\", 59)(143, \"div\", 60)(144, \"mat-icon\");\n    i0.ɵɵtext(145, \"qr_code_scanner\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(146, \"div\", 61)(147, \"span\", 62);\n    i0.ɵɵtext(148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(149, \"span\", 63);\n    i0.ɵɵtext(150, \"QR scann\\u00E9s\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(151, \"div\", 59)(152, \"div\", 60)(153, \"mat-icon\");\n    i0.ɵɵtext(154, \"verified\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(155, \"div\", 61)(156, \"span\", 62);\n    i0.ɵɵtext(157);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(158, \"span\", 63);\n    i0.ɵɵtext(159, \"Actions valid\\u00E9es\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(160, \"div\", 64)(161, \"h4\");\n    i0.ɵɵtext(162, \"\\u00C9volution des points\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(163, \"div\", 65)(164, \"div\", 66);\n    i0.ɵɵtemplate(165, DashboardComponent_div_0_div_165_Template, 4, 3, \"div\", 67);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵtemplate(166, DashboardComponent_div_0_div_166_Template, 14, 2, \"div\", 68);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(167, \"div\", 69)(168, \"mat-card\", 70)(169, \"mat-card-header\")(170, \"mat-card-title\")(171, \"mat-icon\");\n    i0.ɵɵtext(172, \"emoji_events\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(173, \" Mes achievements \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(174, \"mat-card-content\")(175, \"div\", 71);\n    i0.ɵɵtemplate(176, DashboardComponent_div_0_div_176_Template, 10, 10, \"div\", 72);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(177, \"div\", 73)(178, \"h3\", 42)(179, \"mat-icon\");\n    i0.ɵɵtext(180, \"flash_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(181, \" Actions rapides \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(182, \"div\", 74)(183, \"button\", 75);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_Template_button_click_183_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openQRScanner());\n    });\n    i0.ɵɵelementStart(184, \"mat-icon\");\n    i0.ɵɵtext(185, \"qr_code_scanner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(186, \"span\");\n    i0.ɵɵtext(187, \"Scanner QR\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(188, \"button\", 76)(189, \"mat-icon\");\n    i0.ɵɵtext(190, \"card_giftcard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(191, \"span\");\n    i0.ɵɵtext(192, \"Mes r\\u00E9compenses\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(193, \"button\", 77);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_Template_button_click_193_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openProfile());\n    });\n    i0.ɵɵelementStart(194, \"mat-icon\");\n    i0.ɵɵtext(195, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(196, \"span\");\n    i0.ɵɵtext(197, \"Mon profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(198, \"button\", 78)(199, \"mat-icon\");\n    i0.ɵɵtext(200, \"leaderboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(201, \"span\");\n    i0.ɵɵtext(202, \"Classement\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const profileMenu_r13 = i0.ɵɵreference(28);\n    const noHistory_r14 = i0.ɵɵreference(114);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"Bonjour \", ctx_r1.user.name, \" ! \\uD83D\\uDC4B\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u00C9l\\u00E8ve \\u2022 \", ctx_r1.user.city, \"\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.user.points);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matBadge\", ctx_r1.unreadNotifications)(\"matBadgeHidden\", ctx_r1.unreadNotifications === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", profileMenu_r13);\n    i0.ɵɵadvance(40);\n    i0.ɵɵproperty(\"src\", ctx_r1.nextReward.image, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.nextReward.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.nextReward.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.nextReward.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getPointsToNextReward());\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressPercentage() + \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.user.points, \" / \", ctx_r1.nextReward.pointsRequired, \" points\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getMotivationMessage());\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.availableRewards);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtwoWayProperty(\"value\", ctx_r1.selectedPeriod);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filteredHistory.length > 0)(\"ngIfElse\", noHistory_r14);\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate(ctx_r1.monthlyStats.pointsEarned);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.monthlyStats.pointsSpent);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.monthlyStats.scansCount);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.monthlyStats.validationsCount);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.weeklyPointsData);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.showNotifications);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.achievements);\n  }\n}\nexport class DashboardComponent {\n  constructor(authService, router, dashboardRouter, mockDataService) {\n    this.authService = authService;\n    this.router = router;\n    this.dashboardRouter = dashboardRouter;\n    this.mockDataService = mockDataService;\n    this.user = null;\n    // Données pour le dashboard élève\n    this.availableRewards = [];\n    this.nextReward = null;\n    this.filteredHistory = [];\n    this.monthlyStats = {\n      pointsEarned: 0,\n      pointsSpent: 0,\n      scansCount: 0,\n      validationsCount: 0\n    };\n    this.achievements = [];\n    this.notifications = [];\n    this.weeklyPointsData = [];\n    this.maxWeeklyPoints = 100;\n    // État de l'interface\n    this.selectedPeriod = 'month';\n    this.showNotifications = false;\n    this.unreadNotifications = 0;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        // Rediriger vers le dashboard approprié selon le rôle\n        if (user.role !== UserRole.USER) {\n          this.dashboardRouter.navigateToUserDashboard(user);\n          return;\n        }\n        this.loadStudentDashboardData();\n      }\n    });\n  }\n  loadStudentDashboardData() {\n    this.loadAvailableRewards();\n    this.loadUserHistory();\n    this.loadMonthlyStats();\n    this.loadAchievements();\n    this.loadNotifications();\n    this.loadWeeklyPointsData();\n    this.setNextReward();\n  }\n  loadAvailableRewards() {\n    const mockData = this.mockDataService.getMockDataForRole(UserRole.USER);\n    this.availableRewards = mockData.availableRewards || [{\n      id: 'reward1',\n      title: 'Café gratuit',\n      description: 'Un café offert au Café des Nattes',\n      pointsRequired: 50,\n      partnerName: 'Café des Nattes',\n      imageUrl: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',\n      image: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',\n      category: 'FOOD',\n      isActive: true\n    }, {\n      id: 'reward2',\n      title: '10% de réduction',\n      description: 'Réduction sur tous les produits artisanaux',\n      pointsRequired: 75,\n      partnerName: 'Boutique Artisanat',\n      imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',\n      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',\n      category: 'SHOPPING',\n      isActive: true\n    }, {\n      id: 'reward3',\n      title: 'Entrée gratuite',\n      description: 'Visite gratuite du musée de Sousse',\n      pointsRequired: 100,\n      partnerName: 'Musée de Sousse',\n      imageUrl: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300',\n      image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300',\n      category: 'CULTURE',\n      isActive: true\n    }];\n  }\n  loadUserHistory() {\n    if (!this.user) return;\n    // Charger l'historique depuis les données utilisateur\n    this.filteredHistory = this.user.history?.map(h => ({\n      id: h.id,\n      type: h.type,\n      description: h.description,\n      points: h.points,\n      timestamp: h.timestamp,\n      location: 'Monastir',\n      providerName: 'Prestataire local' // Exemple\n    })) || [];\n    this.applyHistoryFilter();\n  }\n  loadMonthlyStats() {\n    if (!this.user) return;\n    // Calculer les statistiques du mois en cours\n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n    const monthlyActions = this.user.history?.filter(h => {\n      const actionDate = new Date(h.timestamp);\n      return actionDate.getMonth() === currentMonth && actionDate.getFullYear() === currentYear;\n    }) || [];\n    this.monthlyStats = {\n      pointsEarned: monthlyActions.filter(a => a.points > 0).reduce((sum, a) => sum + a.points, 0),\n      pointsSpent: Math.abs(monthlyActions.filter(a => a.points < 0).reduce((sum, a) => sum + a.points, 0)),\n      scansCount: monthlyActions.filter(a => a.description.includes('Scan QR')).length,\n      validationsCount: monthlyActions.filter(a => !a.description.includes('Scan QR')).length\n    };\n  }\n  loadAchievements() {\n    this.achievements = [{\n      id: 'first_scan',\n      title: 'Premier Scan',\n      description: 'Scanner votre premier QR Code',\n      icon: 'qr_code_scanner',\n      color: '#4CAF50',\n      earned: (this.user?.history?.length || 0) > 0,\n      progress: Math.min((this.user?.history?.length || 0) * 100, 100),\n      current: this.user?.history?.length || 0,\n      target: 1\n    }, {\n      id: 'point_collector',\n      title: 'Collecteur de Points',\n      description: 'Atteindre 100 points',\n      icon: 'stars',\n      color: '#FFD700',\n      earned: (this.user?.points || 0) >= 100,\n      progress: Math.min(this.user?.points || 0, 100),\n      current: this.user?.points || 0,\n      target: 100\n    }, {\n      id: 'community_helper',\n      title: 'Aide Communautaire',\n      description: 'Effectuer 5 bonnes actions',\n      icon: 'volunteer_activism',\n      color: '#E91E63',\n      earned: (this.user?.history?.length || 0) >= 5,\n      progress: Math.min((this.user?.history?.length || 0) * 20, 100),\n      current: this.user?.history?.length || 0,\n      target: 5\n    }, {\n      id: 'reward_redeemer',\n      title: 'Échangeur de Récompenses',\n      description: 'Échanger votre première récompense',\n      icon: 'card_giftcard',\n      color: '#FF9800',\n      earned: false,\n      progress: 0,\n      current: 0,\n      target: 1\n    }];\n  }\n  loadNotifications() {\n    this.notifications = [{\n      id: 'notif1',\n      type: 'points_earned',\n      title: 'Points gagnés !',\n      message: 'Vous avez gagné 15 points pour votre aide à la bibliothèque',\n      timestamp: new Date(Date.now() - 1000 * 60 * 30),\n      read: false\n    }, {\n      id: 'notif2',\n      type: 'new_reward',\n      title: 'Nouvelle récompense !',\n      message: 'Une nouvelle récompense est disponible au Café des Nattes',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),\n      read: false\n    }, {\n      id: 'notif3',\n      type: 'achievement',\n      title: 'Achievement débloqué !',\n      message: 'Félicitations ! Vous avez débloqué le badge \"Premier Scan\"',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),\n      read: true\n    }];\n    this.unreadNotifications = this.notifications.filter(n => !n.read).length;\n  }\n  loadWeeklyPointsData() {\n    // Simuler les données de points par semaine pour le graphique\n    this.weeklyPointsData = [{\n      week: 1,\n      points: 45\n    }, {\n      week: 2,\n      points: 62\n    }, {\n      week: 3,\n      points: 38\n    }, {\n      week: 4,\n      points: 75\n    }];\n    this.maxWeeklyPoints = Math.max(...this.weeklyPointsData.map(w => w.points));\n  }\n  setNextReward() {\n    if (!this.user || this.availableRewards.length === 0) return;\n    // Trouver la prochaine récompense atteignable\n    const affordableRewards = this.availableRewards.filter(r => r.pointsRequired > this.user.points).sort((a, b) => a.pointsRequired - b.pointsRequired);\n    this.nextReward = affordableRewards[0] || this.availableRewards[0];\n  }\n  // Méthodes pour le dashboard élève\n  // Progression vers la prochaine récompense\n  getPointsToNextReward() {\n    if (!this.user || !this.nextReward) return 0;\n    return Math.max(0, this.nextReward.pointsRequired - this.user.points);\n  }\n  getProgressPercentage() {\n    if (!this.user || !this.nextReward) return 0;\n    return Math.min(this.user.points / this.nextReward.pointsRequired * 100, 100);\n  }\n  getMotivationMessage() {\n    const pointsNeeded = this.getPointsToNextReward();\n    if (pointsNeeded === 0) {\n      return \"🎉 Félicitations ! Vous pouvez échanger cette récompense !\";\n    } else if (pointsNeeded <= 10) {\n      return `🔥 Plus que ${pointsNeeded} points ! Vous y êtes presque !`;\n    } else if (pointsNeeded <= 25) {\n      return `💪 Encore ${pointsNeeded} points et c'est à vous !`;\n    } else {\n      return `🎯 Objectif : ${pointsNeeded} points pour votre prochaine récompense !`;\n    }\n  }\n  // Filtrage de l'historique\n  filterHistory() {\n    this.applyHistoryFilter();\n  }\n  applyHistoryFilter() {\n    if (!this.user?.history) {\n      this.filteredHistory = [];\n      return;\n    }\n    const now = new Date();\n    let startDate;\n    switch (this.selectedPeriod) {\n      case 'week':\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        break;\n      case 'month':\n        startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n        break;\n      case 'year':\n        startDate = new Date(now.getFullYear(), 0, 1);\n        break;\n      default:\n        this.filteredHistory = this.user.history.map(h => ({\n          id: h.id,\n          type: h.type,\n          description: h.description,\n          points: h.points,\n          timestamp: h.timestamp,\n          location: 'Monastir',\n          providerName: 'Prestataire local'\n        }));\n        return;\n    }\n    this.filteredHistory = this.user.history.filter(h => new Date(h.timestamp) >= startDate).map(h => ({\n      id: h.id,\n      type: h.type,\n      description: h.description,\n      points: h.points,\n      timestamp: h.timestamp,\n      location: 'Monastir',\n      providerName: 'Prestataire local'\n    }));\n  }\n  // Méthodes helper pour l'interface\n  getActionColor(type) {\n    const colorMap = {\n      'earned': '#4CAF50',\n      'spent': '#F44336',\n      'qr_scan': '#2196F3',\n      'validation': '#FF9800'\n    };\n    return colorMap[type] || '#667eea';\n  }\n  getActionIcon(type) {\n    const iconMap = {\n      'earned': 'trending_up',\n      'spent': 'trending_down',\n      'qr_scan': 'qr_code_scanner',\n      'validation': 'verified'\n    };\n    return iconMap[type] || 'circle';\n  }\n  getNotificationColor(type) {\n    const colorMap = {\n      'points_earned': '#4CAF50',\n      'new_reward': '#FF9800',\n      'achievement': '#9C27B0',\n      'reminder': '#2196F3'\n    };\n    return colorMap[type] || '#667eea';\n  }\n  getNotificationIcon(type) {\n    const iconMap = {\n      'points_earned': 'stars',\n      'new_reward': 'card_giftcard',\n      'achievement': 'emoji_events',\n      'reminder': 'notifications'\n    };\n    return iconMap[type] || 'info';\n  }\n  // Méthodes d'action\n  openQRScanner() {\n    console.log('Ouverture du scanner QR...');\n    this.router.navigate(['/qr-scanner']);\n  }\n  exchangeReward(reward) {\n    if (!this.user || this.user.points < reward.pointsRequired) {\n      console.log('Pas assez de points pour cette récompense');\n      return;\n    }\n    console.log('Échange de récompense:', reward);\n    // TODO: Implémenter l'échange de récompense\n    // - Déduire les points\n    // - Générer un code d'échange\n    // - Mettre à jour l'historique\n  }\n  toggleNotifications() {\n    this.showNotifications = !this.showNotifications;\n  }\n  markAsRead(notification) {\n    notification.read = true;\n    this.unreadNotifications = this.notifications.filter(n => !n.read).length;\n  }\n  openProfile() {\n    this.router.navigate(['/profile']);\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/auth/login']);\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.DashboardRouterService), i0.ɵɵdirectiveInject(i4.MockDataService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 4,\n      vars: 1,\n      consts: [[\"profileMenu\", \"matMenu\"], [\"noHistory\", \"\"], [\"noNotifications\", \"\"], [\"class\", \"dashboard-container\", 4, \"ngIf\"], [\"mat-fab\", \"\", \"color\", \"primary\", \"matTooltip\", \"Scanner un QR Code\", 1, \"floating-qr-btn\", 3, \"click\"], [1, \"dashboard-container\"], [1, \"dashboard-header\", \"fade-in\"], [1, \"user-welcome\"], [1, \"user-avatar\"], [1, \"user-info\"], [1, \"user-role\"], [1, \"header-actions\"], [1, \"points-display\"], [1, \"points-card\"], [1, \"points-icon\"], [1, \"points-info\"], [1, \"points-value\"], [1, \"points-label\"], [\"mat-icon-button\", \"\", \"matBadgeColor\", \"warn\", 1, \"notifications-btn\", 3, \"click\", \"matBadge\", \"matBadgeHidden\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"main-section\"], [1, \"qr-scan-section\"], [1, \"qr-scan-card\"], [1, \"qr-scan-content\"], [1, \"qr-scan-icon\"], [1, \"qr-scan-text\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"qr-scan-btn\", 3, \"click\"], [1, \"progress-section\"], [1, \"progress-card\"], [1, \"reward-progress\"], [1, \"progress-info\"], [1, \"next-reward\"], [1, \"reward-icon\", 3, \"src\", \"alt\"], [1, \"reward-details\"], [1, \"points-needed\"], [1, \"points-remaining\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"motivation-message\"], [1, \"rewards-section\"], [1, \"section-subtitle\"], [1, \"rewards-grid\"], [\"class\", \"reward-card\", 3, \"affordable\", 4, \"ngFor\", \"ngForOf\"], [1, \"secondary-section\"], [1, \"history-section\"], [1, \"history-card\"], [1, \"history-filters\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [3, \"valueChange\", \"selectionChange\", \"value\"], [\"value\", \"week\"], [\"value\", \"month\"], [\"value\", \"year\"], [\"value\", \"all\"], [\"class\", \"history-list\", 4, \"ngIf\", \"ngIfElse\"], [1, \"stats-section\"], [1, \"stats-card\"], [1, \"stats-grid\"], [1, \"stat-item\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"points-chart\"], [1, \"chart-container\"], [1, \"chart-bars\"], [\"class\", \"chart-bar\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"notifications-section\", 4, \"ngIf\"], [1, \"motivation-section\"], [1, \"motivation-card\"], [1, \"badges-grid\"], [\"class\", \"badge-item\", 3, \"earned\", \"locked\", 4, \"ngFor\", \"ngForOf\"], [1, \"quick-actions\"], [1, \"quick-actions-grid\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"quick-action-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/rewards\", 1, \"quick-action-btn\"], [\"mat-raised-button\", \"\", 1, \"quick-action-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"routerLink\", \"/leaderboard\", 1, \"quick-action-btn\"], [1, \"reward-card\"], [1, \"reward-image\"], [3, \"src\", \"alt\"], [\"class\", \"reward-badge\", 4, \"ngIf\"], [1, \"reward-description\"], [1, \"reward-partner\"], [1, \"reward-points\"], [\"mat-raised-button\", \"\", 1, \"exchange-btn\", 3, \"click\", \"color\", \"disabled\"], [1, \"reward-badge\"], [1, \"history-list\"], [\"class\", \"history-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"history-item\"], [1, \"action-icon\"], [1, \"action-details\"], [1, \"action-meta\"], [1, \"action-date\"], [\"class\", \"action-location\", 4, \"ngIf\"], [\"class\", \"action-provider\", 4, \"ngIf\"], [1, \"action-points\"], [1, \"points-earned\"], [1, \"action-location\"], [1, \"action-provider\"], [1, \"no-history\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"chart-bar\"], [1, \"bar\"], [1, \"bar-label\"], [1, \"notifications-section\"], [1, \"notifications-card\"], [\"mat-icon-button\", \"\", 3, \"click\"], [\"class\", \"notifications-list\", 4, \"ngIf\", \"ngIfElse\"], [1, \"notifications-list\"], [\"class\", \"notification-item\", 3, \"unread\", 4, \"ngFor\", \"ngForOf\"], [1, \"notification-item\"], [1, \"notification-icon\"], [1, \"notification-content\"], [1, \"notification-time\"], [\"mat-icon-button\", \"\", 3, \"click\", 4, \"ngIf\"], [1, \"no-notifications\"], [1, \"badge-item\"], [1, \"badge-icon\"], [1, \"badge-info\"], [\"class\", \"badge-progress\", 4, \"ngIf\"], [1, \"badge-progress\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, DashboardComponent_div_0_Template, 203, 27, \"div\", 3);\n          i0.ɵɵelementStart(1, \"button\", 4);\n          i0.ɵɵlistener(\"click\", function DashboardComponent_Template_button_click_1_listener() {\n            return ctx.openQRScanner();\n          });\n          i0.ɵɵelementStart(2, \"mat-icon\");\n          i0.ɵɵtext(3, \"qr_code_scanner\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i2.RouterLink, i6.MatButton, i6.MatIconButton, i6.MatFabButton, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatIcon, i9.MatFormField, i9.MatLabel, i10.MatMenu, i10.MatMenuItem, i10.MatMenuTrigger, i11.MatSelect, i12.MatOption, i13.MatBadge, i14.MatTooltip, i5.DatePipe, i15.TimeAgoPipe],\n      styles: [\"\\n\\n.dashboard-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  min-height: 100vh;\\n}\\n\\n\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  margin-bottom: 24px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n\\n.user-welcome[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem;\\n}\\n\\n.user-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #2d3748;\\n  font-weight: 600;\\n}\\n\\n.user-role[_ngcontent-%COMP%] {\\n  color: #718096;\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.points-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n}\\n\\n.points-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  background: linear-gradient(135deg, #FFD700, #FFA500);\\n  color: white;\\n  padding: 12px 20px;\\n  border-radius: 20px;\\n  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);\\n}\\n\\n.points-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem !important;\\n}\\n\\n.points-value[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n}\\n\\n.points-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  opacity: 0.9;\\n}\\n\\n.notifications-btn[_ngcontent-%COMP%] {\\n  background: #f7fafc;\\n  color: #4a5568;\\n}\\n\\n\\n\\n.main-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n\\n\\n.qr-scan-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.qr-scan-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4CAF50, #45a049);\\n  color: white;\\n  border-radius: 16px;\\n  overflow: hidden;\\n}\\n\\n.qr-scan-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 24px;\\n  padding: 8px;\\n}\\n\\n.qr-scan-icon[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.qr-scan-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem !important;\\n}\\n\\n.qr-scan-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.qr-scan-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n}\\n\\n.qr-scan-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  opacity: 0.9;\\n}\\n\\n.qr-scan-btn[_ngcontent-%COMP%] {\\n  background: white;\\n  color: #4CAF50;\\n  font-weight: 600;\\n  padding: 12px 24px;\\n  border-radius: 12px;\\n}\\n\\n.qr-scan-btn[_ngcontent-%COMP%]:hover {\\n  background: #f8f9fa;\\n}\\n\\n\\n\\n.floating-qr-btn[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 24px;\\n  right: 24px;\\n  z-index: 1000;\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .dashboard-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    text-align: center;\\n  }\\n\\n  .qr-scan-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n  }\\n}\\n\\n.hero-section.city-monastir[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.hero-section.city-sousse[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n}\\n\\n.hero-section[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n.city-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.hero-title[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 3rem;\\n  font-weight: 800;\\n  font-family: 'Poppins', sans-serif;\\n  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\\n  background: linear-gradient(45deg, #ffffff, #f0f0f0);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.hero-subtitle[_ngcontent-%COMP%] {\\n  margin: 0 0 32px 0;\\n  font-size: 1.2rem;\\n  opacity: 0.9;\\n  font-weight: 400;\\n}\\n\\n.hero-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 32px;\\n}\\n\\n.hero-stat[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  line-height: 1;\\n  margin-bottom: 4px;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  opacity: 0.8;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n}\\n\\n.hero-visual[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.city-illustration[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 120px;\\n  background: rgba(255, 255, 255, 0.15);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.city-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem !important;\\n  width: 4rem !important;\\n  height: 4rem !important;\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n\\n\\n\\n.impact-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  margin: 0 0 32px 0;\\n  color: #2d3748;\\n  font-size: 2rem;\\n  font-weight: 700;\\n  font-family: 'Poppins', sans-serif;\\n  text-align: center;\\n  position: relative;\\n  padding-bottom: 16px;\\n}\\n\\n.section-title[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 80px;\\n  height: 4px;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  border-radius: 2px;\\n}\\n\\n.impact-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 24px;\\n}\\n\\n.impact-card[_ngcontent-%COMP%] {\\n  border-radius: 20px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.impact-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.impact-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #2d3748;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n\\n\\n\\n.city-progress[_ngcontent-%COMP%]   .progress-ring[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 auto 24px;\\n  width: 120px;\\n  height: 120px;\\n}\\n\\n.progress-svg[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n\\n.progress-bg[_ngcontent-%COMP%] {\\n  fill: none;\\n  stroke: #e2e8f0;\\n  stroke-width: 8;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  fill: none;\\n  stroke: #667eea;\\n  stroke-width: 8;\\n  stroke-linecap: round;\\n  transition: stroke-dasharray 1s ease-in-out;\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n}\\n\\n.progress-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #2d3748;\\n}\\n\\n.progress-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.progress-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  padding: 8px 0;\\n  border-bottom: 1px solid #e2e8f0;\\n}\\n\\n.detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n\\n\\n.community-feed[_ngcontent-%COMP%]   .feed-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n\\n.feed-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background: #f7fafc;\\n  border-radius: 12px;\\n  transition: all 0.2s ease;\\n}\\n\\n.feed-item[_ngcontent-%COMP%]:hover {\\n  background: #edf2f7;\\n  transform: translateX(4px);\\n}\\n\\n.feed-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  flex-shrink: 0;\\n}\\n\\n.feed-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.feed-text[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 0.9rem;\\n  color: #2d3748;\\n  font-weight: 500;\\n}\\n\\n.feed-time[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.feed-points[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78, #38a169);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n}\\n\\n.view-all-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-top: 8px;\\n}\\n\\n\\n\\n.leaderboard[_ngcontent-%COMP%]   .leaderboard-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.leader-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  border-radius: 12px;\\n  transition: all 0.2s ease;\\n}\\n\\n.leader-item[_ngcontent-%COMP%]:hover {\\n  background: #f7fafc;\\n}\\n\\n.leader-item.current-user[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\\n  border: 2px solid rgba(102, 126, 234, 0.3);\\n}\\n\\n.leader-rank[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 700;\\n  border-radius: 50%;\\n  background: #e2e8f0;\\n  color: #4a5568;\\n}\\n\\n.trophy-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem !important;\\n  width: 1.5rem !important;\\n  height: 1.5rem !important;\\n}\\n\\n.rank-number[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.leader-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.leader-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  color: #2d3748;\\n  font-size: 0.9rem;\\n}\\n\\n.leader-city[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.leader-points[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #667eea;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.events-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.events-carousel[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 24px;\\n  overflow-x: auto;\\n  padding-bottom: 16px;\\n  scroll-behavior: smooth;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 8px;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #667eea;\\n  border-radius: 4px;\\n}\\n\\n.event-card[_ngcontent-%COMP%] {\\n  min-width: 320px;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.event-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.event-image[_ngcontent-%COMP%] {\\n  height: 160px;\\n  background-size: cover;\\n  background-position: center;\\n  position: relative;\\n  background-color: #e2e8f0;\\n}\\n\\n.event-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  color: white;\\n  text-transform: uppercase;\\n}\\n\\n.badge-environnement[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78, #38a169);\\n}\\n\\n.badge-culture[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ed8936, #dd6b20);\\n}\\n\\n.badge-\\u00E9ducation[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4299e1, #3182ce);\\n}\\n\\n.event-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #2d3748;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.event-description[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n}\\n\\n.event-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.event-detail[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 0.8rem;\\n  color: #4a5568;\\n}\\n\\n.event-detail[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  width: 1rem !important;\\n  height: 1rem !important;\\n  color: #667eea;\\n}\\n\\n.event-join-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 36px;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.partners-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.partners-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 24px;\\n}\\n\\n.partner-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.partner-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.partner-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 20px 20px 0;\\n}\\n\\n.partner-logo[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 12px;\\n  object-fit: cover;\\n  background: #f7fafc;\\n}\\n\\n.partner-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #2d3748;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.partner-category[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n\\n.partner-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.star-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  width: 1rem !important;\\n  height: 1rem !important;\\n  color: #ffd700;\\n}\\n\\n.partner-offer[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  color: #2d3748;\\n  font-weight: 500;\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\\n  padding: 12px;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n}\\n\\n.partner-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.partner-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 36px;\\n  font-size: 0.8rem;\\n}\\n\\n\\n\\n.actions-hub[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 32px;\\n}\\n\\n.action-category[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.category-title[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #2d3748;\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  text-align: center;\\n  padding-bottom: 12px;\\n  border-bottom: 2px solid #e2e8f0;\\n}\\n\\n.category-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n  border-radius: 12px;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: left;\\n  margin-left: 12px;\\n  font-weight: 500;\\n}\\n\\n.action-points[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #2d3748;\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.challenges-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.challenges-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n  gap: 24px;\\n}\\n\\n.challenge-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n}\\n\\n.challenge-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.challenge-header[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  text-align: center;\\n  color: white;\\n  position: relative;\\n}\\n\\n.challenge-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem !important;\\n  width: 3rem !important;\\n  height: 3rem !important;\\n  margin-bottom: 12px;\\n}\\n\\n.challenge-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n}\\n\\n.challenge-description[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #4a5568;\\n  line-height: 1.5;\\n}\\n\\n.challenge-progress[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 8px;\\n  background: #e2e8f0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  margin-bottom: 8px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  border-radius: 4px;\\n  transition: width 1s ease-in-out;\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 0.9rem;\\n  color: #4a5568;\\n  font-weight: 600;\\n}\\n\\n.challenge-reward[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 20px;\\n  padding: 12px;\\n  background: #f7fafc;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  color: #2d3748;\\n}\\n\\n\\n\\n.comparison-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.comparison-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.comparison-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 32px;\\n  padding: 0 20px;\\n}\\n\\n.city-score[_ngcontent-%COMP%] {\\n  text-align: center;\\n  flex: 1;\\n}\\n\\n.city-score.monastir[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n\\n.city-score.sousse[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #f093fb;\\n}\\n\\n.city-score[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n}\\n\\n.city-score[_ngcontent-%COMP%]   .score[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  font-weight: 800;\\n  margin-bottom: 8px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.city-score.sousse[_ngcontent-%COMP%]   .score[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.city-score[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n\\n.vs-divider[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 0 20px;\\n}\\n\\n.vs-divider[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem !important;\\n  width: 2rem !important;\\n  height: 2rem !important;\\n  color: #667eea;\\n}\\n\\n.vs-divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #2d3748;\\n  font-size: 1.2rem;\\n}\\n\\n.comparison-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n\\n.stat-comparison[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.stat-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2d3748;\\n  font-size: 0.9rem;\\n  text-align: center;\\n}\\n\\n.stat-bars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  align-items: center;\\n}\\n\\n.stat-bar[_ngcontent-%COMP%] {\\n  height: 24px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  min-width: 60px;\\n  transition: all 0.3s ease;\\n}\\n\\n.stat-bar.monastir[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n}\\n\\n.stat-bar.sousse[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .hero-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 24px;\\n    text-align: center;\\n    padding: 40px 24px;\\n  }\\n\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .hero-stats[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    gap: 20px;\\n  }\\n\\n  .impact-grid[_ngcontent-%COMP%], .partners-grid[_ngcontent-%COMP%], .actions-grid[_ngcontent-%COMP%], .challenges-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .events-scroll[_ngcontent-%COMP%] {\\n    padding-left: 16px;\\n  }\\n\\n  .comparison-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n  }\\n\\n  .vs-divider[_ngcontent-%COMP%] {\\n    order: -1;\\n  }\\n\\n  .stat-bars[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n\\n  .stat-bar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n    padding: 0 12px;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .hero-stats[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .action-btn[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n    text-align: center;\\n  }\\n\\n  .action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["UserRole", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "DashboardComponent_div_0_mat_card_89_div_3_Template", "ɵɵlistener", "DashboardComponent_div_0_mat_card_89_Template_button_click_19_listener", "reward_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "exchangeReward", "ɵɵclassProp", "user", "points", "pointsRequired", "ɵɵadvance", "ɵɵproperty", "imageUrl", "ɵɵsanitizeUrl", "title", "ɵɵtextInterpolate", "description", "partner<PERSON>ame", "ɵɵtextInterpolate1", "action_r5", "location", "providerName", "DashboardComponent_div_0_div_112_div_1_span_11_Template", "DashboardComponent_div_0_div_112_div_1_div_12_Template", "ɵɵstyleProp", "getActionColor", "type", "getActionIcon", "ɵɵpipeBind2", "timestamp", "DashboardComponent_div_0_div_112_div_1_Template", "filteredHistory", "DashboardComponent_div_0_ng_template_113_Template_button_click_5_listener", "_r6", "openQRScanner", "week_r7", "maxWeeklyPoints", "week", "DashboardComponent_div_0_div_166_div_11_div_1_button_12_Template_button_click_0_listener", "_r9", "notification_r10", "mark<PERSON><PERSON><PERSON>", "DashboardComponent_div_0_div_166_div_11_div_1_button_12_Template", "read", "getNotificationColor", "getNotificationIcon", "message", "ɵɵpipeBind1", "DashboardComponent_div_0_div_166_div_11_div_1_Template", "notifications", "DashboardComponent_div_0_div_166_Template_button_click_7_listener", "_r8", "toggleNotifications", "DashboardComponent_div_0_div_166_div_11_Template", "DashboardComponent_div_0_div_166_ng_template_12_Template", "ɵɵtemplateRefExtractor", "length", "noNotifications_r11", "badge_r12", "progress", "ɵɵtextInterpolate2", "current", "target", "DashboardComponent_div_0_div_176_div_9_Template", "earned", "color", "icon", "DashboardComponent_div_0_Template_button_click_21_listener", "_r1", "DashboardComponent_div_0_Template_button_click_29_listener", "openProfile", "DashboardComponent_div_0_Template_button_click_34_listener", "logout", "DashboardComponent_div_0_Template_button_click_52_listener", "DashboardComponent_div_0_mat_card_89_Template", "ɵɵtwoWayListener", "DashboardComponent_div_0_Template_mat_select_valueChange_102_listener", "$event", "ɵɵtwoWayBindingSet", "<PERSON><PERSON><PERSON><PERSON>", "DashboardComponent_div_0_Template_mat_select_selectionChange_102_listener", "filterHistory", "DashboardComponent_div_0_div_112_Template", "DashboardComponent_div_0_ng_template_113_Template", "DashboardComponent_div_0_div_165_Template", "DashboardComponent_div_0_div_166_Template", "DashboardComponent_div_0_div_176_Template", "DashboardComponent_div_0_Template_button_click_183_listener", "DashboardComponent_div_0_Template_button_click_193_listener", "name", "city", "unreadNotifications", "profileMenu_r13", "nextReward", "image", "getPointsToNextReward", "getProgressPercentage", "getMotivationMessage", "availableRewards", "ɵɵtwoWayProperty", "noHistory_r14", "monthlyStats", "pointsEarned", "pointsSpent", "scansCount", "validationsCount", "weeklyPointsData", "showNotifications", "achievements", "DashboardComponent", "constructor", "authService", "router", "dashboardRouter", "mockDataService", "ngOnInit", "currentUser$", "subscribe", "role", "USER", "navigateToUserDashboard", "loadStudentDashboardData", "loadAvailableRewards", "loadUserHistory", "loadMonthlyStats", "loadAchievements", "loadNotifications", "loadWeeklyPointsData", "setNextReward", "mockData", "getMockDataForRole", "id", "category", "isActive", "history", "map", "h", "apply<PERSON>istoryFilter", "currentMonth", "Date", "getMonth", "currentYear", "getFullYear", "monthlyActions", "filter", "actionDate", "a", "reduce", "sum", "Math", "abs", "includes", "min", "now", "n", "max", "w", "affordableRewards", "r", "sort", "b", "pointsNeeded", "startDate", "getTime", "colorMap", "iconMap", "console", "log", "navigate", "reward", "notification", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "DashboardRouterService", "i4", "MockDataService", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_div_0_Template", "DashboardComponent_Template_button_click_1_listener"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { DashboardRouterService } from '../../../../core/services/dashboard-router.service';\nimport { MockDataService } from '../../../../core/services/mock-data.service';\nimport { User, UserRole } from '../../../../core/models';\n\n// Interfaces pour le dashboard élève\ninterface Reward {\n  id: string;\n  title: string;\n  description: string;\n  pointsRequired: number;\n  partnerName: string;\n  imageUrl: string;\n  image?: string; // Alias pour imageUrl\n  category: string;\n  isActive: boolean;\n}\n\ninterface HistoryAction {\n  id: string;\n  type: string;\n  description: string;\n  points: number;\n  timestamp: Date;\n  location?: string;\n  providerName?: string;\n}\n\ninterface MonthlyStats {\n  pointsEarned: number;\n  pointsSpent: number;\n  scansCount: number;\n  validationsCount: number;\n}\n\ninterface Achievement {\n  id: string;\n  title: string;\n  description: string;\n  icon: string;\n  color: string;\n  earned: boolean;\n  progress: number;\n  current: number;\n  target: number;\n}\n\ninterface Notification {\n  id: string;\n  type: string;\n  title: string;\n  message: string;\n  timestamp: Date;\n  read: boolean;\n}\n\ninterface CommunityActivity {\n  id: string;\n  description: string;\n  points: number;\n  type: string;\n  timestamp: Date;\n  userId: string;\n  userName: string;\n}\n\ninterface LocalEvent {\n  id: string;\n  title: string;\n  description: string;\n  date: Date;\n  location: string;\n  category: string;\n  points: number;\n  image: string;\n  participants: number;\n  maxParticipants: number;\n}\n\ninterface Partner {\n  id: string;\n  name: string;\n  category: string;\n  logo: string;\n  rating: number;\n  reviews: number;\n  currentOffer: string;\n  location: string;\n  qrCode: string;\n}\n\ninterface Challenge {\n  id: string;\n  title: string;\n  description: string;\n  icon: string;\n  gradient: string;\n  progress: number;\n  current: number;\n  target: number;\n  reward: string;\n  endDate: Date;\n}\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit {\n  user: User | null = null;\n\n  // Données pour le dashboard élève\n  availableRewards: Reward[] = [];\n  nextReward: Reward | null = null;\n  filteredHistory: HistoryAction[] = [];\n  monthlyStats: MonthlyStats = {\n    pointsEarned: 0,\n    pointsSpent: 0,\n    scansCount: 0,\n    validationsCount: 0\n  };\n  achievements: Achievement[] = [];\n  notifications: Notification[] = [];\n  weeklyPointsData: any[] = [];\n  maxWeeklyPoints = 100;\n\n  // État de l'interface\n  selectedPeriod = 'month';\n  showNotifications = false;\n  unreadNotifications = 0;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private dashboardRouter: DashboardRouterService,\n    private mockDataService: MockDataService\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        // Rediriger vers le dashboard approprié selon le rôle\n        if (user.role !== UserRole.USER) {\n          this.dashboardRouter.navigateToUserDashboard(user);\n          return;\n        }\n        this.loadStudentDashboardData();\n      }\n    });\n  }\n\n  private loadStudentDashboardData(): void {\n    this.loadAvailableRewards();\n    this.loadUserHistory();\n    this.loadMonthlyStats();\n    this.loadAchievements();\n    this.loadNotifications();\n    this.loadWeeklyPointsData();\n    this.setNextReward();\n  }\n\n  private loadAvailableRewards(): void {\n    const mockData = this.mockDataService.getMockDataForRole(UserRole.USER);\n    this.availableRewards = mockData.availableRewards || [\n      {\n        id: 'reward1',\n        title: 'Café gratuit',\n        description: 'Un café offert au Café des Nattes',\n        pointsRequired: 50,\n        partnerName: 'Café des Nattes',\n        imageUrl: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',\n        image: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',\n        category: 'FOOD',\n        isActive: true\n      },\n      {\n        id: 'reward2',\n        title: '10% de réduction',\n        description: 'Réduction sur tous les produits artisanaux',\n        pointsRequired: 75,\n        partnerName: 'Boutique Artisanat',\n        imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',\n        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',\n        category: 'SHOPPING',\n        isActive: true\n      },\n      {\n        id: 'reward3',\n        title: 'Entrée gratuite',\n        description: 'Visite gratuite du musée de Sousse',\n        pointsRequired: 100,\n        partnerName: 'Musée de Sousse',\n        imageUrl: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300',\n        image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300',\n        category: 'CULTURE',\n        isActive: true\n      }\n    ];\n  }\n\n  private loadUserHistory(): void {\n    if (!this.user) return;\n\n    // Charger l'historique depuis les données utilisateur\n    this.filteredHistory = this.user.history?.map(h => ({\n      id: h.id,\n      type: h.type,\n      description: h.description,\n      points: h.points,\n      timestamp: h.timestamp,\n      location: 'Monastir', // Exemple\n      providerName: 'Prestataire local' // Exemple\n    })) || [];\n\n    this.applyHistoryFilter();\n  }\n\n  private loadMonthlyStats(): void {\n    if (!this.user) return;\n\n    // Calculer les statistiques du mois en cours\n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n\n    const monthlyActions = this.user.history?.filter(h => {\n      const actionDate = new Date(h.timestamp);\n      return actionDate.getMonth() === currentMonth && actionDate.getFullYear() === currentYear;\n    }) || [];\n\n    this.monthlyStats = {\n      pointsEarned: monthlyActions.filter(a => a.points > 0).reduce((sum, a) => sum + a.points, 0),\n      pointsSpent: Math.abs(monthlyActions.filter(a => a.points < 0).reduce((sum, a) => sum + a.points, 0)),\n      scansCount: monthlyActions.filter(a => a.description.includes('Scan QR')).length,\n      validationsCount: monthlyActions.filter(a => !a.description.includes('Scan QR')).length\n    };\n  }\n\n  private loadAchievements(): void {\n    this.achievements = [\n      {\n        id: 'first_scan',\n        title: 'Premier Scan',\n        description: 'Scanner votre premier QR Code',\n        icon: 'qr_code_scanner',\n        color: '#4CAF50',\n        earned: (this.user?.history?.length || 0) > 0,\n        progress: Math.min((this.user?.history?.length || 0) * 100, 100),\n        current: this.user?.history?.length || 0,\n        target: 1\n      },\n      {\n        id: 'point_collector',\n        title: 'Collecteur de Points',\n        description: 'Atteindre 100 points',\n        icon: 'stars',\n        color: '#FFD700',\n        earned: (this.user?.points || 0) >= 100,\n        progress: Math.min((this.user?.points || 0), 100),\n        current: this.user?.points || 0,\n        target: 100\n      },\n      {\n        id: 'community_helper',\n        title: 'Aide Communautaire',\n        description: 'Effectuer 5 bonnes actions',\n        icon: 'volunteer_activism',\n        color: '#E91E63',\n        earned: (this.user?.history?.length || 0) >= 5,\n        progress: Math.min((this.user?.history?.length || 0) * 20, 100),\n        current: this.user?.history?.length || 0,\n        target: 5\n      },\n      {\n        id: 'reward_redeemer',\n        title: 'Échangeur de Récompenses',\n        description: 'Échanger votre première récompense',\n        icon: 'card_giftcard',\n        color: '#FF9800',\n        earned: false, // À implémenter avec l'historique des échanges\n        progress: 0,\n        current: 0,\n        target: 1\n      }\n    ];\n  }\n\n  private loadNotifications(): void {\n    this.notifications = [\n      {\n        id: 'notif1',\n        type: 'points_earned',\n        title: 'Points gagnés !',\n        message: 'Vous avez gagné 15 points pour votre aide à la bibliothèque',\n        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 min ago\n        read: false\n      },\n      {\n        id: 'notif2',\n        type: 'new_reward',\n        title: 'Nouvelle récompense !',\n        message: 'Une nouvelle récompense est disponible au Café des Nattes',\n        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago\n        read: false\n      },\n      {\n        id: 'notif3',\n        type: 'achievement',\n        title: 'Achievement débloqué !',\n        message: 'Félicitations ! Vous avez débloqué le badge \"Premier Scan\"',\n        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago\n        read: true\n      }\n    ];\n\n    this.unreadNotifications = this.notifications.filter(n => !n.read).length;\n  }\n\n  private loadWeeklyPointsData(): void {\n    // Simuler les données de points par semaine pour le graphique\n    this.weeklyPointsData = [\n      { week: 1, points: 45 },\n      { week: 2, points: 62 },\n      { week: 3, points: 38 },\n      { week: 4, points: 75 }\n    ];\n\n    this.maxWeeklyPoints = Math.max(...this.weeklyPointsData.map(w => w.points));\n  }\n\n  private setNextReward(): void {\n    if (!this.user || this.availableRewards.length === 0) return;\n\n    // Trouver la prochaine récompense atteignable\n    const affordableRewards = this.availableRewards\n      .filter(r => r.pointsRequired > this.user!.points)\n      .sort((a, b) => a.pointsRequired - b.pointsRequired);\n\n    this.nextReward = affordableRewards[0] || this.availableRewards[0];\n  }\n\n  // Méthodes pour le dashboard élève\n\n  // Progression vers la prochaine récompense\n  getPointsToNextReward(): number {\n    if (!this.user || !this.nextReward) return 0;\n    return Math.max(0, this.nextReward.pointsRequired - this.user.points);\n  }\n\n  getProgressPercentage(): number {\n    if (!this.user || !this.nextReward) return 0;\n    return Math.min((this.user.points / this.nextReward.pointsRequired) * 100, 100);\n  }\n\n  getMotivationMessage(): string {\n    const pointsNeeded = this.getPointsToNextReward();\n    if (pointsNeeded === 0) {\n      return \"🎉 Félicitations ! Vous pouvez échanger cette récompense !\";\n    } else if (pointsNeeded <= 10) {\n      return `🔥 Plus que ${pointsNeeded} points ! Vous y êtes presque !`;\n    } else if (pointsNeeded <= 25) {\n      return `💪 Encore ${pointsNeeded} points et c'est à vous !`;\n    } else {\n      return `🎯 Objectif : ${pointsNeeded} points pour votre prochaine récompense !`;\n    }\n  }\n\n  // Filtrage de l'historique\n  filterHistory(): void {\n    this.applyHistoryFilter();\n  }\n\n  applyHistoryFilter(): void {\n    if (!this.user?.history) {\n      this.filteredHistory = [];\n      return;\n    }\n\n    const now = new Date();\n    let startDate: Date;\n\n    switch (this.selectedPeriod) {\n      case 'week':\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        break;\n      case 'month':\n        startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n        break;\n      case 'year':\n        startDate = new Date(now.getFullYear(), 0, 1);\n        break;\n      default:\n        this.filteredHistory = this.user.history.map(h => ({\n          id: h.id,\n          type: h.type,\n          description: h.description,\n          points: h.points,\n          timestamp: h.timestamp,\n          location: 'Monastir',\n          providerName: 'Prestataire local'\n        }));\n        return;\n    }\n\n    this.filteredHistory = this.user.history\n      .filter(h => new Date(h.timestamp) >= startDate)\n      .map(h => ({\n        id: h.id,\n        type: h.type,\n        description: h.description,\n        points: h.points,\n        timestamp: h.timestamp,\n        location: 'Monastir',\n        providerName: 'Prestataire local'\n      }));\n  }\n\n  // Méthodes helper pour l'interface\n  getActionColor(type: string): string {\n    const colorMap: { [key: string]: string } = {\n      'earned': '#4CAF50',\n      'spent': '#F44336',\n      'qr_scan': '#2196F3',\n      'validation': '#FF9800'\n    };\n    return colorMap[type] || '#667eea';\n  }\n\n  getActionIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'earned': 'trending_up',\n      'spent': 'trending_down',\n      'qr_scan': 'qr_code_scanner',\n      'validation': 'verified'\n    };\n    return iconMap[type] || 'circle';\n  }\n\n  getNotificationColor(type: string): string {\n    const colorMap: { [key: string]: string } = {\n      'points_earned': '#4CAF50',\n      'new_reward': '#FF9800',\n      'achievement': '#9C27B0',\n      'reminder': '#2196F3'\n    };\n    return colorMap[type] || '#667eea';\n  }\n\n  getNotificationIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'points_earned': 'stars',\n      'new_reward': 'card_giftcard',\n      'achievement': 'emoji_events',\n      'reminder': 'notifications'\n    };\n    return iconMap[type] || 'info';\n  }\n\n  // Méthodes d'action\n  openQRScanner(): void {\n    console.log('Ouverture du scanner QR...');\n    this.router.navigate(['/qr-scanner']);\n  }\n\n  exchangeReward(reward: Reward): void {\n    if (!this.user || this.user.points < reward.pointsRequired) {\n      console.log('Pas assez de points pour cette récompense');\n      return;\n    }\n\n    console.log('Échange de récompense:', reward);\n    // TODO: Implémenter l'échange de récompense\n    // - Déduire les points\n    // - Générer un code d'échange\n    // - Mettre à jour l'historique\n  }\n\n  toggleNotifications(): void {\n    this.showNotifications = !this.showNotifications;\n  }\n\n  markAsRead(notification: Notification): void {\n    notification.read = true;\n    this.unreadNotifications = this.notifications.filter(n => !n.read).length;\n  }\n\n  openProfile(): void {\n    this.router.navigate(['/profile']);\n  }\n\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/auth/login']);\n  }\n}\n", "<div class=\"dashboard-container\" *ngIf=\"user\">\n  <!-- En-tête avec informations utilisateur -->\n  <div class=\"dashboard-header fade-in\">\n    <div class=\"user-welcome\">\n      <div class=\"user-avatar\">\n        <mat-icon>school</mat-icon>\n      </div>\n      <div class=\"user-info\">\n        <h2>Bonjour {{ user.name }} ! 👋</h2>\n        <p class=\"user-role\">Élève • {{ user.city }}</p>\n      </div>\n    </div>\n\n    <div class=\"header-actions\">\n      <div class=\"points-display\">\n        <div class=\"points-card\">\n          <mat-icon class=\"points-icon\">stars</mat-icon>\n          <div class=\"points-info\">\n            <span class=\"points-value\">{{ user.points }}</span>\n            <span class=\"points-label\">Points</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Notifications -->\n      <button mat-icon-button class=\"notifications-btn\"\n              [matBadge]=\"unreadNotifications\"\n              matBadgeColor=\"warn\"\n              [matBadgeHidden]=\"unreadNotifications === 0\"\n              (click)=\"toggleNotifications()\">\n        <mat-icon>notifications</mat-icon>\n      </button>\n\n      <!-- Profil -->\n      <button mat-icon-button [matMenuTriggerFor]=\"profileMenu\">\n        <mat-icon>account_circle</mat-icon>\n      </button>\n      <mat-menu #profileMenu=\"matMenu\">\n        <button mat-menu-item (click)=\"openProfile()\">\n          <mat-icon>person</mat-icon>\n          <span>Mon Profil</span>\n        </button>\n        <button mat-menu-item (click)=\"logout()\">\n          <mat-icon>logout</mat-icon>\n          <span>Déconnexion</span>\n        </button>\n      </mat-menu>\n    </div>\n  </div>\n\n  <!-- Section principale -->\n  <div class=\"main-section\">\n    <!-- Bouton Scanner QR - Bien visible -->\n    <div class=\"qr-scan-section\">\n      <mat-card class=\"qr-scan-card\">\n        <mat-card-content>\n          <div class=\"qr-scan-content\">\n            <div class=\"qr-scan-icon\">\n              <mat-icon>qr_code_scanner</mat-icon>\n            </div>\n            <div class=\"qr-scan-text\">\n              <h3>Scanner un QR Code KnowMe</h3>\n              <p>Découvre un prestataire et gagne des points !</p>\n            </div>\n            <button mat-raised-button color=\"primary\" class=\"qr-scan-btn\" (click)=\"openQRScanner()\">\n              <mat-icon>camera_alt</mat-icon>\n              Scanner\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Jauge d'avancement vers la prochaine récompense -->\n    <div class=\"progress-section\">\n      <mat-card class=\"progress-card\">\n        <mat-card-content>\n          <h3>Progression vers ta prochaine récompense</h3>\n          <div class=\"reward-progress\">\n            <div class=\"progress-info\">\n              <div class=\"next-reward\">\n                <img [src]=\"nextReward.image\" [alt]=\"nextReward.title\" class=\"reward-icon\">\n                <div class=\"reward-details\">\n                  <h4>{{ nextReward.title }}</h4>\n                  <p>{{ nextReward.description }}</p>\n                </div>\n              </div>\n              <div class=\"points-needed\">\n                <span class=\"points-remaining\">{{ getPointsToNextReward() }}</span>\n                <span class=\"points-label\">points restants</span>\n              </div>\n            </div>\n            <div class=\"progress-bar\">\n              <div class=\"progress-fill\" [style.width]=\"getProgressPercentage() + '%'\"></div>\n            </div>\n            <div class=\"progress-text\">\n              <span>{{ user.points }} / {{ nextReward.pointsRequired }} points</span>\n            </div>\n            <div class=\"motivation-message\">\n              <p>{{ getMotivationMessage() }}</p>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Offres/Récompenses disponibles -->\n    <div class=\"rewards-section\">\n      <h3 class=\"section-subtitle\">\n        <mat-icon>card_giftcard</mat-icon>\n        Récompenses disponibles\n      </h3>\n      <div class=\"rewards-grid\">\n        <mat-card *ngFor=\"let reward of availableRewards\" class=\"reward-card\"\n                  [class.affordable]=\"user.points >= reward.pointsRequired\">\n          <div class=\"reward-image\">\n            <img [src]=\"reward.imageUrl\" [alt]=\"reward.title\">\n            <div class=\"reward-badge\" *ngIf=\"user.points >= reward.pointsRequired\">\n              <mat-icon>check_circle</mat-icon>\n            </div>\n          </div>\n          <mat-card-content>\n            <h4>{{ reward.title }}</h4>\n            <p class=\"reward-description\">{{ reward.description }}</p>\n            <div class=\"reward-partner\">\n              <mat-icon>store</mat-icon>\n              <span>{{ reward.partnerName }}</span>\n            </div>\n            <div class=\"reward-points\">\n              <mat-icon>stars</mat-icon>\n              <span>{{ reward.pointsRequired }} points</span>\n            </div>\n            <button mat-raised-button\n                    [color]=\"user.points >= reward.pointsRequired ? 'primary' : ''\"\n                    [disabled]=\"user.points < reward.pointsRequired\"\n                    class=\"exchange-btn\"\n                    (click)=\"exchangeReward(reward)\">\n              {{ user.points >= reward.pointsRequired ? 'Échanger' : 'Pas assez de points' }}\n            </button>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  </div>\n\n  <!-- Section secondaire -->\n  <div class=\"secondary-section\">\n    <!-- Historique des bonnes actions -->\n    <div class=\"history-section\">\n      <mat-card class=\"history-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>history</mat-icon>\n            Historique de mes actions\n          </mat-card-title>\n          <div class=\"history-filters\">\n            <mat-form-field appearance=\"outline\" class=\"filter-field\">\n              <mat-label>Période</mat-label>\n              <mat-select [(value)]=\"selectedPeriod\" (selectionChange)=\"filterHistory()\">\n                <mat-option value=\"week\">Cette semaine</mat-option>\n                <mat-option value=\"month\">Ce mois</mat-option>\n                <mat-option value=\"year\">Cette année</mat-option>\n                <mat-option value=\"all\">Tout</mat-option>\n              </mat-select>\n            </mat-form-field>\n          </div>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"history-list\" *ngIf=\"filteredHistory.length > 0; else noHistory\">\n            <div *ngFor=\"let action of filteredHistory\" class=\"history-item\">\n              <div class=\"action-icon\">\n                <mat-icon [style.color]=\"getActionColor(action.type)\">\n                  {{ getActionIcon(action.type) }}\n                </mat-icon>\n              </div>\n              <div class=\"action-details\">\n                <h4>{{ action.description }}</h4>\n                <div class=\"action-meta\">\n                  <span class=\"action-date\">{{ action.timestamp | date:'short' }}</span>\n                  <span class=\"action-location\" *ngIf=\"action.location\">\n                    <mat-icon>location_on</mat-icon>\n                    {{ action.location }}\n                  </span>\n                </div>\n                <div class=\"action-provider\" *ngIf=\"action.providerName\">\n                  <mat-icon>business</mat-icon>\n                  <span>{{ action.providerName }}</span>\n                </div>\n              </div>\n              <div class=\"action-points\">\n                <span class=\"points-earned\">+{{ action.points }}</span>\n                <span class=\"points-label\">pts</span>\n              </div>\n            </div>\n          </div>\n          <ng-template #noHistory>\n            <div class=\"no-history\">\n              <mat-icon>sentiment_neutral</mat-icon>\n              <p>Aucune action pour cette période</p>\n              <button mat-raised-button color=\"primary\" (click)=\"openQRScanner()\">\n                Commencer maintenant\n              </button>\n            </div>\n          </ng-template>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Statistiques mensuelles -->\n    <div class=\"stats-section\">\n      <mat-card class=\"stats-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>analytics</mat-icon>\n            Mes statistiques ce mois\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"stats-grid\">\n            <div class=\"stat-item\">\n              <div class=\"stat-icon\">\n                <mat-icon>trending_up</mat-icon>\n              </div>\n              <div class=\"stat-content\">\n                <span class=\"stat-value\">{{ monthlyStats.pointsEarned }}</span>\n                <span class=\"stat-label\">Points gagnés</span>\n              </div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-icon\">\n                <mat-icon>trending_down</mat-icon>\n              </div>\n              <div class=\"stat-content\">\n                <span class=\"stat-value\">{{ monthlyStats.pointsSpent }}</span>\n                <span class=\"stat-label\">Points dépensés</span>\n              </div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-icon\">\n                <mat-icon>qr_code_scanner</mat-icon>\n              </div>\n              <div class=\"stat-content\">\n                <span class=\"stat-value\">{{ monthlyStats.scansCount }}</span>\n                <span class=\"stat-label\">QR scannés</span>\n              </div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-icon\">\n                <mat-icon>verified</mat-icon>\n              </div>\n              <div class=\"stat-content\">\n                <span class=\"stat-value\">{{ monthlyStats.validationsCount }}</span>\n                <span class=\"stat-label\">Actions validées</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- Graphique simple des points -->\n          <div class=\"points-chart\">\n            <h4>Évolution des points</h4>\n            <div class=\"chart-container\">\n              <div class=\"chart-bars\">\n                <div *ngFor=\"let week of weeklyPointsData\" class=\"chart-bar\">\n                  <div class=\"bar\" [style.height]=\"(week.points / maxWeeklyPoints * 100) + '%'\"></div>\n                  <span class=\"bar-label\">S{{ week.week }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Notifications -->\n    <div class=\"notifications-section\" *ngIf=\"showNotifications\">\n      <mat-card class=\"notifications-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>notifications</mat-icon>\n            Notifications\n          </mat-card-title>\n          <button mat-icon-button (click)=\"toggleNotifications()\">\n            <mat-icon>close</mat-icon>\n          </button>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"notifications-list\" *ngIf=\"notifications.length > 0; else noNotifications\">\n            <div *ngFor=\"let notification of notifications\"\n                 class=\"notification-item\"\n                 [class.unread]=\"!notification.read\">\n              <div class=\"notification-icon\">\n                <mat-icon [style.color]=\"getNotificationColor(notification.type)\">\n                  {{ getNotificationIcon(notification.type) }}\n                </mat-icon>\n              </div>\n              <div class=\"notification-content\">\n                <h4>{{ notification.title }}</h4>\n                <p>{{ notification.message }}</p>\n                <span class=\"notification-time\">{{ notification.timestamp | timeAgo }}</span>\n              </div>\n              <button mat-icon-button *ngIf=\"!notification.read\" (click)=\"markAsRead(notification)\">\n                <mat-icon>check</mat-icon>\n              </button>\n            </div>\n          </div>\n          <ng-template #noNotifications>\n            <div class=\"no-notifications\">\n              <mat-icon>notifications_off</mat-icon>\n              <p>Aucune notification</p>\n            </div>\n          </ng-template>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Tableau de motivation et badges -->\n  <div class=\"motivation-section\">\n    <mat-card class=\"motivation-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>emoji_events</mat-icon>\n          Mes achievements\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"badges-grid\">\n          <div *ngFor=\"let badge of achievements\"\n               class=\"badge-item\"\n               [class.earned]=\"badge.earned\"\n               [class.locked]=\"!badge.earned\">\n            <div class=\"badge-icon\">\n              <mat-icon [style.color]=\"badge.earned ? badge.color : '#ccc'\">\n                {{ badge.icon }}\n              </mat-icon>\n            </div>\n            <div class=\"badge-info\">\n              <h4>{{ badge.title }}</h4>\n              <p>{{ badge.description }}</p>\n              <div class=\"badge-progress\" *ngIf=\"!badge.earned\">\n                <div class=\"progress-bar\">\n                  <div class=\"progress-fill\" [style.width]=\"badge.progress + '%'\"></div>\n                </div>\n                <span>{{ badge.current }}/{{ badge.target }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Accès rapide aux actions -->\n  <div class=\"quick-actions\">\n    <h3 class=\"section-subtitle\">\n      <mat-icon>flash_on</mat-icon>\n      Actions rapides\n    </h3>\n    <div class=\"quick-actions-grid\">\n      <button mat-raised-button color=\"primary\" class=\"quick-action-btn\" (click)=\"openQRScanner()\">\n        <mat-icon>qr_code_scanner</mat-icon>\n        <span>Scanner QR</span>\n      </button>\n      <button mat-raised-button class=\"quick-action-btn\" routerLink=\"/rewards\">\n        <mat-icon>card_giftcard</mat-icon>\n        <span>Mes récompenses</span>\n      </button>\n      <button mat-raised-button class=\"quick-action-btn\" (click)=\"openProfile()\">\n        <mat-icon>person</mat-icon>\n        <span>Mon profil</span>\n      </button>\n      <button mat-raised-button class=\"quick-action-btn\" routerLink=\"/leaderboard\">\n        <mat-icon>leaderboard</mat-icon>\n        <span>Classement</span>\n      </button>\n    </div>\n  </div>\n</div>\n\n<!-- Floating QR Scanner Button -->\n<button mat-fab\n        class=\"floating-qr-btn\"\n        color=\"primary\"\n        (click)=\"openQRScanner()\"\n        matTooltip=\"Scanner un QR Code\">\n  <mat-icon>qr_code_scanner</mat-icon>\n</button>\n"], "mappings": "AAKA,SAAeA,QAAQ,QAAQ,yBAAyB;;;;;;;;;;;;;;;;;;;ICiH1CC,EADF,CAAAC,cAAA,cAAuE,eAC3D;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IACxBF,EADwB,CAAAG,YAAA,EAAW,EAC7B;;;;;;IAJRH,EAFF,CAAAC,cAAA,mBACoE,cACxC;IACxBD,EAAA,CAAAI,SAAA,cAAkD;IAClDJ,EAAA,CAAAK,UAAA,IAAAC,mDAAA,kBAAuE;IAGzEN,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,uBAAkB,SACZ;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAExDH,EADF,CAAAC,cAAA,cAA4B,gBAChB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAChCF,EADgC,CAAAG,YAAA,EAAO,EACjC;IAEJH,EADF,CAAAC,cAAA,eAA2B,gBACf;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,kBAIyC;IAAjCD,EAAA,CAAAO,UAAA,mBAAAC,uEAAA;MAAA,MAAAC,SAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAG,cAAA,CAAAP,SAAA,CAAsB;IAAA,EAAC;IACtCT,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;;;;;IA1BDH,EAAA,CAAAiB,WAAA,eAAAJ,MAAA,CAAAK,IAAA,CAAAC,MAAA,IAAAV,SAAA,CAAAW,cAAA,CAAyD;IAE1DpB,EAAA,CAAAqB,SAAA,GAAuB;IAACrB,EAAxB,CAAAsB,UAAA,QAAAb,SAAA,CAAAc,QAAA,EAAAvB,EAAA,CAAAwB,aAAA,CAAuB,QAAAf,SAAA,CAAAgB,KAAA,CAAqB;IACtBzB,EAAA,CAAAqB,SAAA,EAA0C;IAA1CrB,EAAA,CAAAsB,UAAA,SAAAT,MAAA,CAAAK,IAAA,CAAAC,MAAA,IAAAV,SAAA,CAAAW,cAAA,CAA0C;IAKjEpB,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAA0B,iBAAA,CAAAjB,SAAA,CAAAgB,KAAA,CAAkB;IACQzB,EAAA,CAAAqB,SAAA,GAAwB;IAAxBrB,EAAA,CAAA0B,iBAAA,CAAAjB,SAAA,CAAAkB,WAAA,CAAwB;IAG9C3B,EAAA,CAAAqB,SAAA,GAAwB;IAAxBrB,EAAA,CAAA0B,iBAAA,CAAAjB,SAAA,CAAAmB,WAAA,CAAwB;IAIxB5B,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAA6B,kBAAA,KAAApB,SAAA,CAAAW,cAAA,YAAkC;IAGlCpB,EAAA,CAAAqB,SAAA,EAA+D;IAC/DrB,EADA,CAAAsB,UAAA,UAAAT,MAAA,CAAAK,IAAA,CAAAC,MAAA,IAAAV,SAAA,CAAAW,cAAA,kBAA+D,aAAAP,MAAA,CAAAK,IAAA,CAAAC,MAAA,GAAAV,SAAA,CAAAW,cAAA,CACf;IAGtDpB,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAA6B,kBAAA,MAAAhB,MAAA,CAAAK,IAAA,CAAAC,MAAA,IAAAV,SAAA,CAAAW,cAAA,gDACF;;;;;IA0CQpB,EADF,CAAAC,cAAA,eAAsD,eAC1C;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAA6B,kBAAA,MAAAC,SAAA,CAAAC,QAAA,MACF;;;;;IAGA/B,EADF,CAAAC,cAAA,eAAyD,eAC7C;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IACjCF,EADiC,CAAAG,YAAA,EAAO,EAClC;;;;IADEH,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAA0B,iBAAA,CAAAI,SAAA,CAAAE,YAAA,CAAyB;;;;;IAfjChC,EAFJ,CAAAC,cAAA,cAAiE,cACtC,eAC+B;IACpDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,cAA4B,SACtB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE/BH,EADF,CAAAC,cAAA,cAAyB,eACG;IAAAD,EAAA,CAAAE,MAAA,GAAqC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtEH,EAAA,CAAAK,UAAA,KAAA4B,uDAAA,mBAAsD;IAIxDjC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,UAAA,KAAA6B,sDAAA,kBAAyD;IAI3DlC,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA2B,gBACG;IAAAD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvDH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAElCF,EAFkC,CAAAG,YAAA,EAAO,EACjC,EACF;;;;;IAtBQH,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAmC,WAAA,UAAAtB,MAAA,CAAAuB,cAAA,CAAAN,SAAA,CAAAO,IAAA,EAA2C;IACnDrC,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAA6B,kBAAA,MAAAhB,MAAA,CAAAyB,aAAA,CAAAR,SAAA,CAAAO,IAAA,OACF;IAGIrC,EAAA,CAAAqB,SAAA,GAAwB;IAAxBrB,EAAA,CAAA0B,iBAAA,CAAAI,SAAA,CAAAH,WAAA,CAAwB;IAEA3B,EAAA,CAAAqB,SAAA,GAAqC;IAArCrB,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAAuC,WAAA,QAAAT,SAAA,CAAAU,SAAA,WAAqC;IAChCxC,EAAA,CAAAqB,SAAA,GAAqB;IAArBrB,EAAA,CAAAsB,UAAA,SAAAQ,SAAA,CAAAC,QAAA,CAAqB;IAKxB/B,EAAA,CAAAqB,SAAA,EAAyB;IAAzBrB,EAAA,CAAAsB,UAAA,SAAAQ,SAAA,CAAAE,YAAA,CAAyB;IAM3BhC,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAA6B,kBAAA,MAAAC,SAAA,CAAAX,MAAA,KAAoB;;;;;IAtBtDnB,EAAA,CAAAC,cAAA,cAA6E;IAC3ED,EAAA,CAAAK,UAAA,IAAAoC,+CAAA,oBAAiE;IAyBnEzC,EAAA,CAAAG,YAAA,EAAM;;;;IAzBoBH,EAAA,CAAAqB,SAAA,EAAkB;IAAlBrB,EAAA,CAAAsB,UAAA,YAAAT,MAAA,CAAA6B,eAAA,CAAkB;;;;;;IA4BxC1C,EADF,CAAAC,cAAA,eAAwB,eACZ;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,4CAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvCH,EAAA,CAAAC,cAAA,kBAAoE;IAA1BD,EAAA,CAAAO,UAAA,mBAAAoC,0EAAA;MAAA3C,EAAA,CAAAU,aAAA,CAAAkC,GAAA;MAAA,MAAA/B,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAgC,aAAA,EAAe;IAAA,EAAC;IACjE7C,EAAA,CAAAE,MAAA,6BACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IA4DFH,EAAA,CAAAC,cAAA,eAA6D;IAC3DD,EAAA,CAAAI,SAAA,eAAoF;IACpFJ,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;;;;;IAFaH,EAAA,CAAAqB,SAAA,EAA4D;IAA5DrB,EAAA,CAAAmC,WAAA,WAAAW,OAAA,CAAA3B,MAAA,GAAAN,MAAA,CAAAkC,eAAA,aAA4D;IACrD/C,EAAA,CAAAqB,SAAA,GAAgB;IAAhBrB,EAAA,CAAA6B,kBAAA,MAAAiB,OAAA,CAAAE,IAAA,KAAgB;;;;;;IAoC5ChD,EAAA,CAAAC,cAAA,kBAAsF;IAAnCD,EAAA,CAAAO,UAAA,mBAAA0C,yFAAA;MAAAjD,EAAA,CAAAU,aAAA,CAAAwC,GAAA;MAAA,MAAAC,gBAAA,GAAAnD,EAAA,CAAAc,aAAA,GAAAF,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAuC,UAAA,CAAAD,gBAAA,CAAwB;IAAA,EAAC;IACnFnD,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IACjBF,EADiB,CAAAG,YAAA,EAAW,EACnB;;;;;IAXPH,EAJJ,CAAAC,cAAA,eAEyC,eACR,eACqC;IAChED,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,eAAkC,SAC5B;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjCH,EAAA,CAAAC,cAAA,gBAAgC;IAAAD,EAAA,CAAAE,MAAA,IAAsC;;IACxEF,EADwE,CAAAG,YAAA,EAAO,EACzE;IACNH,EAAA,CAAAK,UAAA,KAAAgD,gEAAA,sBAAsF;IAGxFrD,EAAA,CAAAG,YAAA,EAAM;;;;;IAdDH,EAAA,CAAAiB,WAAA,YAAAkC,gBAAA,CAAAG,IAAA,CAAmC;IAE1BtD,EAAA,CAAAqB,SAAA,GAAuD;IAAvDrB,EAAA,CAAAmC,WAAA,UAAAtB,MAAA,CAAA0C,oBAAA,CAAAJ,gBAAA,CAAAd,IAAA,EAAuD;IAC/DrC,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAA6B,kBAAA,MAAAhB,MAAA,CAAA2C,mBAAA,CAAAL,gBAAA,CAAAd,IAAA,OACF;IAGIrC,EAAA,CAAAqB,SAAA,GAAwB;IAAxBrB,EAAA,CAAA0B,iBAAA,CAAAyB,gBAAA,CAAA1B,KAAA,CAAwB;IACzBzB,EAAA,CAAAqB,SAAA,GAA0B;IAA1BrB,EAAA,CAAA0B,iBAAA,CAAAyB,gBAAA,CAAAM,OAAA,CAA0B;IACGzD,EAAA,CAAAqB,SAAA,GAAsC;IAAtCrB,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAA0D,WAAA,QAAAP,gBAAA,CAAAX,SAAA,EAAsC;IAE/CxC,EAAA,CAAAqB,SAAA,GAAwB;IAAxBrB,EAAA,CAAAsB,UAAA,UAAA6B,gBAAA,CAAAG,IAAA,CAAwB;;;;;IAdrDtD,EAAA,CAAAC,cAAA,eAAuF;IACrFD,EAAA,CAAAK,UAAA,IAAAsD,sDAAA,qBAEyC;IAe3C3D,EAAA,CAAAG,YAAA,EAAM;;;;IAjB0BH,EAAA,CAAAqB,SAAA,EAAgB;IAAhBrB,EAAA,CAAAsB,UAAA,YAAAT,MAAA,CAAA+C,aAAA,CAAgB;;;;;IAoB5C5D,EADF,CAAAC,cAAA,eAA8B,eAClB;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,0BAAmB;IACxBF,EADwB,CAAAG,YAAA,EAAI,EACtB;;;;;;IA/BNH,EAJR,CAAAC,cAAA,eAA6D,oBACtB,sBAClB,qBACC,eACJ;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAE,MAAA,sBACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,kBAAwD;IAAhCD,EAAA,CAAAO,UAAA,mBAAAsD,kEAAA;MAAA7D,EAAA,CAAAU,aAAA,CAAAoD,GAAA;MAAA,MAAAjD,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAkD,mBAAA,EAAqB;IAAA,EAAC;IACrD/D,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAEnBF,EAFmB,CAAAG,YAAA,EAAW,EACnB,EACO;IAClBH,EAAA,CAAAC,cAAA,wBAAkB;IAoBhBD,EAnBA,CAAAK,UAAA,KAAA2D,gDAAA,mBAAuF,KAAAC,wDAAA,gCAAAjE,EAAA,CAAAkE,sBAAA,CAmBzD;IAQpClE,EAFI,CAAAG,YAAA,EAAmB,EACV,EACP;;;;;IA3BiCH,EAAA,CAAAqB,SAAA,IAAgC;IAAArB,EAAhC,CAAAsB,UAAA,SAAAT,MAAA,CAAA+C,aAAA,CAAAO,MAAA,KAAgC,aAAAC,mBAAA,CAAoB;;;;;IAsD/EpE,EADF,CAAAC,cAAA,eAAkD,cACtB;IACxBD,EAAA,CAAAI,SAAA,cAAsE;IACxEJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAsC;IAC9CF,EAD8C,CAAAG,YAAA,EAAO,EAC/C;;;;IAHyBH,EAAA,CAAAqB,SAAA,GAAoC;IAApCrB,EAAA,CAAAmC,WAAA,UAAAkC,SAAA,CAAAC,QAAA,OAAoC;IAE3DtE,EAAA,CAAAqB,SAAA,GAAsC;IAAtCrB,EAAA,CAAAuE,kBAAA,KAAAF,SAAA,CAAAG,OAAA,OAAAH,SAAA,CAAAI,MAAA,KAAsC;;;;;IAX9CzE,EALJ,CAAAC,cAAA,eAGoC,eACV,eACwC;IAC5DD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,eAAwB,SAClB;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC9BH,EAAA,CAAAK,UAAA,IAAAqE,+CAAA,mBAAkD;IAOtD1E,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAhBDH,EADA,CAAAiB,WAAA,WAAAoD,SAAA,CAAAM,MAAA,CAA6B,YAAAN,SAAA,CAAAM,MAAA,CACC;IAErB3E,EAAA,CAAAqB,SAAA,GAAmD;IAAnDrB,EAAA,CAAAmC,WAAA,UAAAkC,SAAA,CAAAM,MAAA,GAAAN,SAAA,CAAAO,KAAA,UAAmD;IAC3D5E,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAA6B,kBAAA,MAAAwC,SAAA,CAAAQ,IAAA,MACF;IAGI7E,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAA0B,iBAAA,CAAA2C,SAAA,CAAA5C,KAAA,CAAiB;IAClBzB,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAA0B,iBAAA,CAAA2C,SAAA,CAAA1C,WAAA,CAAuB;IACG3B,EAAA,CAAAqB,SAAA,EAAmB;IAAnBrB,EAAA,CAAAsB,UAAA,UAAA+C,SAAA,CAAAM,MAAA,CAAmB;;;;;;IA9UtD3E,EALR,CAAAC,cAAA,aAA8C,aAEN,aACV,aACC,eACb;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAClBF,EADkB,CAAAG,YAAA,EAAW,EACvB;IAEJH,EADF,CAAAC,cAAA,aAAuB,SACjB;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,YAAqB;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAEhDF,EAFgD,CAAAG,YAAA,EAAI,EAC5C,EACF;IAKAH,EAHN,CAAAC,cAAA,eAA4B,eACE,eACD,oBACO;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE5CH,EADF,CAAAC,cAAA,eAAyB,gBACI;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAGvCF,EAHuC,CAAAG,YAAA,EAAO,EACpC,EACF,EACF;IAGNH,EAAA,CAAAC,cAAA,kBAIwC;IAAhCD,EAAA,CAAAO,UAAA,mBAAAuE,2DAAA;MAAA9E,EAAA,CAAAU,aAAA,CAAAqE,GAAA;MAAA,MAAAlE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAkD,mBAAA,EAAqB;IAAA,EAAC;IACrC/D,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACzBF,EADyB,CAAAG,YAAA,EAAW,EAC3B;IAIPH,EADF,CAAAC,cAAA,kBAA0D,gBAC9C;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAC1BF,EAD0B,CAAAG,YAAA,EAAW,EAC5B;IAEPH,EADF,CAAAC,cAAA,yBAAiC,kBACe;IAAxBD,EAAA,CAAAO,UAAA,mBAAAyE,2DAAA;MAAAhF,EAAA,CAAAU,aAAA,CAAAqE,GAAA;MAAA,MAAAlE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAoE,WAAA,EAAa;IAAA,EAAC;IAC3CjF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAClBF,EADkB,CAAAG,YAAA,EAAO,EAChB;IACTH,EAAA,CAAAC,cAAA,kBAAyC;IAAnBD,EAAA,CAAAO,UAAA,mBAAA2E,2DAAA;MAAAlF,EAAA,CAAAU,aAAA,CAAAqE,GAAA;MAAA,MAAAlE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAsE,MAAA,EAAQ;IAAA,EAAC;IACtCnF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,wBAAW;IAIzBF,EAJyB,CAAAG,YAAA,EAAO,EACjB,EACA,EACP,EACF;IAUMH,EAPZ,CAAAC,cAAA,eAA0B,eAEK,oBACI,wBACX,eACa,eACD,gBACd;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAChC;IAEJH,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAE,MAAA,iCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,0DAA6C;IAClDF,EADkD,CAAAG,YAAA,EAAI,EAChD;IACNH,EAAA,CAAAC,cAAA,kBAAwF;IAA1BD,EAAA,CAAAO,UAAA,mBAAA6E,2DAAA;MAAApF,EAAA,CAAAU,aAAA,CAAAqE,GAAA;MAAA,MAAAlE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAgC,aAAA,EAAe;IAAA,EAAC;IACrF7C,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,iBACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;IAMAH,EAHN,CAAAC,cAAA,eAA8B,oBACI,wBACZ,UACZ;IAAAD,EAAA,CAAAE,MAAA,qDAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG7CH,EAFJ,CAAAC,cAAA,eAA6B,eACA,eACA;IACvBD,EAAA,CAAAI,SAAA,eAA2E;IAEzEJ,EADF,CAAAC,cAAA,eAA4B,UACtB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAEnCF,EAFmC,CAAAG,YAAA,EAAI,EAC/B,EACF;IAEJH,EADF,CAAAC,cAAA,eAA2B,gBACM;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAE9CF,EAF8C,CAAAG,YAAA,EAAO,EAC7C,EACF;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAI,SAAA,eAA+E;IACjFJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA2B,YACnB;IAAAD,EAAA,CAAAE,MAAA,IAA0D;IAClEF,EADkE,CAAAG,YAAA,EAAO,EACnE;IAEJH,EADF,CAAAC,cAAA,eAAgC,SAC3B;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAKzCF,EALyC,CAAAG,YAAA,EAAI,EAC/B,EACF,EACW,EACV,EACP;IAKFH,EAFJ,CAAAC,cAAA,eAA6B,cACE,gBACjB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAK,UAAA,KAAAgF,6CAAA,yBACoE;IA6B1ErF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IASIH,EANV,CAAAC,cAAA,eAA+B,eAEA,oBACI,uBACZ,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,eAA6B,0BAC+B,kBAC7C;IAAAD,EAAA,CAAAE,MAAA,qBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC9BH,EAAA,CAAAC,cAAA,uBAA2E;IAA/DD,EAAA,CAAAsF,gBAAA,yBAAAC,sEAAAC,MAAA;MAAAxF,EAAA,CAAAU,aAAA,CAAAqE,GAAA;MAAA,MAAAlE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAAyF,kBAAA,CAAA5E,MAAA,CAAA6E,cAAA,EAAAF,MAAA,MAAA3E,MAAA,CAAA6E,cAAA,GAAAF,MAAA;MAAA,OAAAxF,EAAA,CAAAe,WAAA,CAAAyE,MAAA;IAAA,EAA0B;IAACxF,EAAA,CAAAO,UAAA,6BAAAoF,0EAAA;MAAA3F,EAAA,CAAAU,aAAA,CAAAqE,GAAA;MAAA,MAAAlE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAmBF,MAAA,CAAA+E,aAAA,EAAe;IAAA,EAAC;IACxE5F,EAAA,CAAAC,cAAA,uBAAyB;IAAAD,EAAA,CAAAE,MAAA,sBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACnDH,EAAA,CAAAC,cAAA,uBAA0B;IAAAD,EAAA,CAAAE,MAAA,gBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC9CH,EAAA,CAAAC,cAAA,uBAAyB;IAAAD,EAAA,CAAAE,MAAA,yBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACjDH,EAAA,CAAAC,cAAA,uBAAwB;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAIpCF,EAJoC,CAAAG,YAAA,EAAa,EAC9B,EACE,EACb,EACU;IAClBH,EAAA,CAAAC,cAAA,yBAAkB;IA4BhBD,EA3BA,CAAAK,UAAA,MAAAwF,yCAAA,kBAA6E,MAAAC,iDAAA,gCAAA9F,EAAA,CAAAkE,sBAAA,CA2BrD;IAW9BlE,EAFI,CAAAG,YAAA,EAAmB,EACV,EACP;IAOEH,EAJR,CAAAC,cAAA,gBAA2B,qBACI,wBACV,uBACC,iBACJ;IAAAD,EAAA,CAAAE,MAAA,kBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAE,MAAA,mCACF;IACFF,EADE,CAAAG,YAAA,EAAiB,EACD;IAKVH,EAJR,CAAAC,cAAA,yBAAkB,gBACQ,gBACC,gBACE,iBACX;IAAAD,EAAA,CAAAE,MAAA,oBAAW;IACvBF,EADuB,CAAAG,YAAA,EAAW,EAC5B;IAEJH,EADF,CAAAC,cAAA,gBAA0B,iBACC;IAAAD,EAAA,CAAAE,MAAA,KAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/DH,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,2BAAa;IAE1CF,EAF0C,CAAAG,YAAA,EAAO,EACzC,EACF;IAGFH,EAFJ,CAAAC,cAAA,gBAAuB,gBACE,iBACX;IAAAD,EAAA,CAAAE,MAAA,sBAAa;IACzBF,EADyB,CAAAG,YAAA,EAAW,EAC9B;IAEJH,EADF,CAAAC,cAAA,gBAA0B,iBACC;IAAAD,EAAA,CAAAE,MAAA,KAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,kCAAe;IAE5CF,EAF4C,CAAAG,YAAA,EAAO,EAC3C,EACF;IAGFH,EAFJ,CAAAC,cAAA,gBAAuB,gBACE,iBACX;IAAAD,EAAA,CAAAE,MAAA,wBAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAChC;IAEJH,EADF,CAAAC,cAAA,gBAA0B,iBACC;IAAAD,EAAA,CAAAE,MAAA,KAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7DH,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAU;IAEvCF,EAFuC,CAAAG,YAAA,EAAO,EACtC,EACF;IAGFH,EAFJ,CAAAC,cAAA,gBAAuB,gBACE,iBACX;IAAAD,EAAA,CAAAE,MAAA,iBAAQ;IACpBF,EADoB,CAAAG,YAAA,EAAW,EACzB;IAEJH,EADF,CAAAC,cAAA,gBAA0B,iBACC;IAAAD,EAAA,CAAAE,MAAA,KAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,8BAAgB;IAG/CF,EAH+C,CAAAG,YAAA,EAAO,EAC5C,EACF,EACF;IAIJH,EADF,CAAAC,cAAA,gBAA0B,WACpB;IAAAD,EAAA,CAAAE,MAAA,kCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE3BH,EADF,CAAAC,cAAA,gBAA6B,gBACH;IACtBD,EAAA,CAAAK,UAAA,MAAA0F,yCAAA,kBAA6D;IASzE/F,EALU,CAAAG,YAAA,EAAM,EACF,EACF,EACW,EACV,EACP;IAGNH,EAAA,CAAAK,UAAA,MAAA2F,yCAAA,mBAA6D;IAwC/DhG,EAAA,CAAAG,YAAA,EAAM;IAOEH,EAJR,CAAAC,cAAA,gBAAgC,qBACI,wBACf,uBACC,iBACJ;IAAAD,EAAA,CAAAE,MAAA,qBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAE,MAAA,2BACF;IACFF,EADE,CAAAG,YAAA,EAAiB,EACD;IAEhBH,EADF,CAAAC,cAAA,yBAAkB,gBACS;IACvBD,EAAA,CAAAK,UAAA,MAAA4F,yCAAA,oBAGoC;IAoB5CjG,EAHM,CAAAG,YAAA,EAAM,EACW,EACV,EACP;IAKFH,EAFJ,CAAAC,cAAA,gBAA2B,eACI,iBACjB;IAAAD,EAAA,CAAAE,MAAA,iBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,gBAAgC,mBAC+D;IAA1BD,EAAA,CAAAO,UAAA,mBAAA2F,4DAAA;MAAAlG,EAAA,CAAAU,aAAA,CAAAqE,GAAA;MAAA,MAAAlE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAgC,aAAA,EAAe;IAAA,EAAC;IAC1F7C,EAAA,CAAAC,cAAA,iBAAU;IAAAD,EAAA,CAAAE,MAAA,wBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAE,MAAA,mBAAU;IAClBF,EADkB,CAAAG,YAAA,EAAO,EAChB;IAEPH,EADF,CAAAC,cAAA,mBAAyE,iBAC7D;IAAAD,EAAA,CAAAE,MAAA,sBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAE,MAAA,6BAAe;IACvBF,EADuB,CAAAG,YAAA,EAAO,EACrB;IACTH,EAAA,CAAAC,cAAA,mBAA2E;IAAxBD,EAAA,CAAAO,UAAA,mBAAA4F,4DAAA;MAAAnG,EAAA,CAAAU,aAAA,CAAAqE,GAAA;MAAA,MAAAlE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAoE,WAAA,EAAa;IAAA,EAAC;IACxEjF,EAAA,CAAAC,cAAA,iBAAU;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAE,MAAA,mBAAU;IAClBF,EADkB,CAAAG,YAAA,EAAO,EAChB;IAEPH,EADF,CAAAC,cAAA,mBAA6E,iBACjE;IAAAD,EAAA,CAAAE,MAAA,oBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAE,MAAA,mBAAU;IAIxBF,EAJwB,CAAAG,YAAA,EAAO,EAChB,EACL,EACF,EACF;;;;;;IAjXMH,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAA6B,kBAAA,aAAAhB,MAAA,CAAAK,IAAA,CAAAkF,IAAA,oBAA4B;IACXpG,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAA6B,kBAAA,4BAAAhB,MAAA,CAAAK,IAAA,CAAAmF,IAAA,KAAuB;IASbrG,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAK,IAAA,CAAAC,MAAA,CAAiB;IAQ1CnB,EAAA,CAAAqB,SAAA,GAAgC;IAEhCrB,EAFA,CAAAsB,UAAA,aAAAT,MAAA,CAAAyF,mBAAA,CAAgC,mBAAAzF,MAAA,CAAAyF,mBAAA,OAEY;IAM5BtG,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAsB,UAAA,sBAAAiF,eAAA,CAAiC;IA+C1CvG,EAAA,CAAAqB,SAAA,IAAwB;IAACrB,EAAzB,CAAAsB,UAAA,QAAAT,MAAA,CAAA2F,UAAA,CAAAC,KAAA,EAAAzG,EAAA,CAAAwB,aAAA,CAAwB,QAAAX,MAAA,CAAA2F,UAAA,CAAA/E,KAAA,CAAyB;IAEhDzB,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAA2F,UAAA,CAAA/E,KAAA,CAAsB;IACvBzB,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAA2F,UAAA,CAAA7E,WAAA,CAA4B;IAIF3B,EAAA,CAAAqB,SAAA,GAA6B;IAA7BrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAA6F,qBAAA,GAA6B;IAKnC1G,EAAA,CAAAqB,SAAA,GAA6C;IAA7CrB,EAAA,CAAAmC,WAAA,UAAAtB,MAAA,CAAA8F,qBAAA,SAA6C;IAGlE3G,EAAA,CAAAqB,SAAA,GAA0D;IAA1DrB,EAAA,CAAAuE,kBAAA,KAAA1D,MAAA,CAAAK,IAAA,CAAAC,MAAA,SAAAN,MAAA,CAAA2F,UAAA,CAAApF,cAAA,YAA0D;IAG7DpB,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAA+F,oBAAA,GAA4B;IAcR5G,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAsB,UAAA,YAAAT,MAAA,CAAAgG,gBAAA,CAAmB;IA6C9B7G,EAAA,CAAAqB,SAAA,IAA0B;IAA1BrB,EAAA,CAAA8G,gBAAA,UAAAjG,MAAA,CAAA6E,cAAA,CAA0B;IAUf1F,EAAA,CAAAqB,SAAA,IAAkC;IAAArB,EAAlC,CAAAsB,UAAA,SAAAT,MAAA,CAAA6B,eAAA,CAAAyB,MAAA,KAAkC,aAAA4C,aAAA,CAAc;IAwD5C/G,EAAA,CAAAqB,SAAA,IAA+B;IAA/BrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAmG,YAAA,CAAAC,YAAA,CAA+B;IAS/BjH,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAmG,YAAA,CAAAE,WAAA,CAA8B;IAS9BlH,EAAA,CAAAqB,SAAA,GAA6B;IAA7BrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAmG,YAAA,CAAAG,UAAA,CAA6B;IAS7BnH,EAAA,CAAAqB,SAAA,GAAmC;IAAnCrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAmG,YAAA,CAAAI,gBAAA,CAAmC;IAWtCpH,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAsB,UAAA,YAAAT,MAAA,CAAAwG,gBAAA,CAAmB;IAYjBrH,EAAA,CAAAqB,SAAA,EAAuB;IAAvBrB,EAAA,CAAAsB,UAAA,SAAAT,MAAA,CAAAyG,iBAAA,CAAuB;IAqD9BtH,EAAA,CAAAqB,SAAA,IAAe;IAAfrB,EAAA,CAAAsB,UAAA,YAAAT,MAAA,CAAA0G,YAAA,CAAe;;;ADxNhD,OAAM,MAAOC,kBAAkB;EAuB7BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,eAAuC,EACvCC,eAAgC;IAHhC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IA1BzB,KAAA3G,IAAI,GAAgB,IAAI;IAExB;IACA,KAAA2F,gBAAgB,GAAa,EAAE;IAC/B,KAAAL,UAAU,GAAkB,IAAI;IAChC,KAAA9D,eAAe,GAAoB,EAAE;IACrC,KAAAsE,YAAY,GAAiB;MAC3BC,YAAY,EAAE,CAAC;MACfC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC;MACbC,gBAAgB,EAAE;KACnB;IACD,KAAAG,YAAY,GAAkB,EAAE;IAChC,KAAA3D,aAAa,GAAmB,EAAE;IAClC,KAAAyD,gBAAgB,GAAU,EAAE;IAC5B,KAAAtE,eAAe,GAAG,GAAG;IAErB;IACA,KAAA2C,cAAc,GAAG,OAAO;IACxB,KAAA4B,iBAAiB,GAAG,KAAK;IACzB,KAAAhB,mBAAmB,GAAG,CAAC;EAOpB;EAEHwB,QAAQA,CAAA;IACN,IAAI,CAACJ,WAAW,CAACK,YAAY,CAACC,SAAS,CAAC9G,IAAI,IAAG;MAC7C,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAIA,IAAI,EAAE;QACR;QACA,IAAIA,IAAI,CAAC+G,IAAI,KAAKlI,QAAQ,CAACmI,IAAI,EAAE;UAC/B,IAAI,CAACN,eAAe,CAACO,uBAAuB,CAACjH,IAAI,CAAC;UAClD;;QAEF,IAAI,CAACkH,wBAAwB,EAAE;;IAEnC,CAAC,CAAC;EACJ;EAEQA,wBAAwBA,CAAA;IAC9B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEQN,oBAAoBA,CAAA;IAC1B,MAAMO,QAAQ,GAAG,IAAI,CAACf,eAAe,CAACgB,kBAAkB,CAAC9I,QAAQ,CAACmI,IAAI,CAAC;IACvE,IAAI,CAACrB,gBAAgB,GAAG+B,QAAQ,CAAC/B,gBAAgB,IAAI,CACnD;MACEiC,EAAE,EAAE,SAAS;MACbrH,KAAK,EAAE,cAAc;MACrBE,WAAW,EAAE,mCAAmC;MAChDP,cAAc,EAAE,EAAE;MAClBQ,WAAW,EAAE,iBAAiB;MAC9BL,QAAQ,EAAE,oEAAoE;MAC9EkF,KAAK,EAAE,oEAAoE;MAC3EsC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE;KACX,EACD;MACEF,EAAE,EAAE,SAAS;MACbrH,KAAK,EAAE,kBAAkB;MACzBE,WAAW,EAAE,4CAA4C;MACzDP,cAAc,EAAE,EAAE;MAClBQ,WAAW,EAAE,oBAAoB;MACjCL,QAAQ,EAAE,oEAAoE;MAC9EkF,KAAK,EAAE,oEAAoE;MAC3EsC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;KACX,EACD;MACEF,EAAE,EAAE,SAAS;MACbrH,KAAK,EAAE,iBAAiB;MACxBE,WAAW,EAAE,oCAAoC;MACjDP,cAAc,EAAE,GAAG;MACnBQ,WAAW,EAAE,iBAAiB;MAC9BL,QAAQ,EAAE,oEAAoE;MAC9EkF,KAAK,EAAE,oEAAoE;MAC3EsC,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE;KACX,CACF;EACH;EAEQV,eAAeA,CAAA;IACrB,IAAI,CAAC,IAAI,CAACpH,IAAI,EAAE;IAEhB;IACA,IAAI,CAACwB,eAAe,GAAG,IAAI,CAACxB,IAAI,CAAC+H,OAAO,EAAEC,GAAG,CAACC,CAAC,KAAK;MAClDL,EAAE,EAAEK,CAAC,CAACL,EAAE;MACRzG,IAAI,EAAE8G,CAAC,CAAC9G,IAAI;MACZV,WAAW,EAAEwH,CAAC,CAACxH,WAAW;MAC1BR,MAAM,EAAEgI,CAAC,CAAChI,MAAM;MAChBqB,SAAS,EAAE2G,CAAC,CAAC3G,SAAS;MACtBT,QAAQ,EAAE,UAAU;MACpBC,YAAY,EAAE,mBAAmB,CAAC;KACnC,CAAC,CAAC,IAAI,EAAE;IAET,IAAI,CAACoH,kBAAkB,EAAE;EAC3B;EAEQb,gBAAgBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAACrH,IAAI,EAAE;IAEhB;IACA,MAAMmI,YAAY,GAAG,IAAIC,IAAI,EAAE,CAACC,QAAQ,EAAE;IAC1C,MAAMC,WAAW,GAAG,IAAIF,IAAI,EAAE,CAACG,WAAW,EAAE;IAE5C,MAAMC,cAAc,GAAG,IAAI,CAACxI,IAAI,CAAC+H,OAAO,EAAEU,MAAM,CAACR,CAAC,IAAG;MACnD,MAAMS,UAAU,GAAG,IAAIN,IAAI,CAACH,CAAC,CAAC3G,SAAS,CAAC;MACxC,OAAOoH,UAAU,CAACL,QAAQ,EAAE,KAAKF,YAAY,IAAIO,UAAU,CAACH,WAAW,EAAE,KAAKD,WAAW;IAC3F,CAAC,CAAC,IAAI,EAAE;IAER,IAAI,CAACxC,YAAY,GAAG;MAClBC,YAAY,EAAEyC,cAAc,CAACC,MAAM,CAACE,CAAC,IAAIA,CAAC,CAAC1I,MAAM,GAAG,CAAC,CAAC,CAAC2I,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGF,CAAC,CAAC1I,MAAM,EAAE,CAAC,CAAC;MAC5F+F,WAAW,EAAE8C,IAAI,CAACC,GAAG,CAACP,cAAc,CAACC,MAAM,CAACE,CAAC,IAAIA,CAAC,CAAC1I,MAAM,GAAG,CAAC,CAAC,CAAC2I,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGF,CAAC,CAAC1I,MAAM,EAAE,CAAC,CAAC,CAAC;MACrGgG,UAAU,EAAEuC,cAAc,CAACC,MAAM,CAACE,CAAC,IAAIA,CAAC,CAAClI,WAAW,CAACuI,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC/F,MAAM;MAChFiD,gBAAgB,EAAEsC,cAAc,CAACC,MAAM,CAACE,CAAC,IAAI,CAACA,CAAC,CAAClI,WAAW,CAACuI,QAAQ,CAAC,SAAS,CAAC,CAAC,CAAC/F;KAClF;EACH;EAEQqE,gBAAgBA,CAAA;IACtB,IAAI,CAACjB,YAAY,GAAG,CAClB;MACEuB,EAAE,EAAE,YAAY;MAChBrH,KAAK,EAAE,cAAc;MACrBE,WAAW,EAAE,+BAA+B;MAC5CkD,IAAI,EAAE,iBAAiB;MACvBD,KAAK,EAAE,SAAS;MAChBD,MAAM,EAAE,CAAC,IAAI,CAACzD,IAAI,EAAE+H,OAAO,EAAE9E,MAAM,IAAI,CAAC,IAAI,CAAC;MAC7CG,QAAQ,EAAE0F,IAAI,CAACG,GAAG,CAAC,CAAC,IAAI,CAACjJ,IAAI,EAAE+H,OAAO,EAAE9E,MAAM,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC;MAChEK,OAAO,EAAE,IAAI,CAACtD,IAAI,EAAE+H,OAAO,EAAE9E,MAAM,IAAI,CAAC;MACxCM,MAAM,EAAE;KACT,EACD;MACEqE,EAAE,EAAE,iBAAiB;MACrBrH,KAAK,EAAE,sBAAsB;MAC7BE,WAAW,EAAE,sBAAsB;MACnCkD,IAAI,EAAE,OAAO;MACbD,KAAK,EAAE,SAAS;MAChBD,MAAM,EAAE,CAAC,IAAI,CAACzD,IAAI,EAAEC,MAAM,IAAI,CAAC,KAAK,GAAG;MACvCmD,QAAQ,EAAE0F,IAAI,CAACG,GAAG,CAAE,IAAI,CAACjJ,IAAI,EAAEC,MAAM,IAAI,CAAC,EAAG,GAAG,CAAC;MACjDqD,OAAO,EAAE,IAAI,CAACtD,IAAI,EAAEC,MAAM,IAAI,CAAC;MAC/BsD,MAAM,EAAE;KACT,EACD;MACEqE,EAAE,EAAE,kBAAkB;MACtBrH,KAAK,EAAE,oBAAoB;MAC3BE,WAAW,EAAE,4BAA4B;MACzCkD,IAAI,EAAE,oBAAoB;MAC1BD,KAAK,EAAE,SAAS;MAChBD,MAAM,EAAE,CAAC,IAAI,CAACzD,IAAI,EAAE+H,OAAO,EAAE9E,MAAM,IAAI,CAAC,KAAK,CAAC;MAC9CG,QAAQ,EAAE0F,IAAI,CAACG,GAAG,CAAC,CAAC,IAAI,CAACjJ,IAAI,EAAE+H,OAAO,EAAE9E,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC;MAC/DK,OAAO,EAAE,IAAI,CAACtD,IAAI,EAAE+H,OAAO,EAAE9E,MAAM,IAAI,CAAC;MACxCM,MAAM,EAAE;KACT,EACD;MACEqE,EAAE,EAAE,iBAAiB;MACrBrH,KAAK,EAAE,0BAA0B;MACjCE,WAAW,EAAE,oCAAoC;MACjDkD,IAAI,EAAE,eAAe;MACrBD,KAAK,EAAE,SAAS;MAChBD,MAAM,EAAE,KAAK;MACbL,QAAQ,EAAE,CAAC;MACXE,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE;KACT,CACF;EACH;EAEQgE,iBAAiBA,CAAA;IACvB,IAAI,CAAC7E,aAAa,GAAG,CACnB;MACEkF,EAAE,EAAE,QAAQ;MACZzG,IAAI,EAAE,eAAe;MACrBZ,KAAK,EAAE,iBAAiB;MACxBgC,OAAO,EAAE,6DAA6D;MACtEjB,SAAS,EAAE,IAAI8G,IAAI,CAACA,IAAI,CAACc,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChD9G,IAAI,EAAE;KACP,EACD;MACEwF,EAAE,EAAE,QAAQ;MACZzG,IAAI,EAAE,YAAY;MAClBZ,KAAK,EAAE,uBAAuB;MAC9BgC,OAAO,EAAE,2DAA2D;MACpEjB,SAAS,EAAE,IAAI8G,IAAI,CAACA,IAAI,CAACc,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACpD9G,IAAI,EAAE;KACP,EACD;MACEwF,EAAE,EAAE,QAAQ;MACZzG,IAAI,EAAE,aAAa;MACnBZ,KAAK,EAAE,wBAAwB;MAC/BgC,OAAO,EAAE,4DAA4D;MACrEjB,SAAS,EAAE,IAAI8G,IAAI,CAACA,IAAI,CAACc,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;MACrD9G,IAAI,EAAE;KACP,CACF;IAED,IAAI,CAACgD,mBAAmB,GAAG,IAAI,CAAC1C,aAAa,CAAC+F,MAAM,CAACU,CAAC,IAAI,CAACA,CAAC,CAAC/G,IAAI,CAAC,CAACa,MAAM;EAC3E;EAEQuE,oBAAoBA,CAAA;IAC1B;IACA,IAAI,CAACrB,gBAAgB,GAAG,CACtB;MAAErE,IAAI,EAAE,CAAC;MAAE7B,MAAM,EAAE;IAAE,CAAE,EACvB;MAAE6B,IAAI,EAAE,CAAC;MAAE7B,MAAM,EAAE;IAAE,CAAE,EACvB;MAAE6B,IAAI,EAAE,CAAC;MAAE7B,MAAM,EAAE;IAAE,CAAE,EACvB;MAAE6B,IAAI,EAAE,CAAC;MAAE7B,MAAM,EAAE;IAAE,CAAE,CACxB;IAED,IAAI,CAAC4B,eAAe,GAAGiH,IAAI,CAACM,GAAG,CAAC,GAAG,IAAI,CAACjD,gBAAgB,CAAC6B,GAAG,CAACqB,CAAC,IAAIA,CAAC,CAACpJ,MAAM,CAAC,CAAC;EAC9E;EAEQwH,aAAaA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACzH,IAAI,IAAI,IAAI,CAAC2F,gBAAgB,CAAC1C,MAAM,KAAK,CAAC,EAAE;IAEtD;IACA,MAAMqG,iBAAiB,GAAG,IAAI,CAAC3D,gBAAgB,CAC5C8C,MAAM,CAACc,CAAC,IAAIA,CAAC,CAACrJ,cAAc,GAAG,IAAI,CAACF,IAAK,CAACC,MAAM,CAAC,CACjDuJ,IAAI,CAAC,CAACb,CAAC,EAAEc,CAAC,KAAKd,CAAC,CAACzI,cAAc,GAAGuJ,CAAC,CAACvJ,cAAc,CAAC;IAEtD,IAAI,CAACoF,UAAU,GAAGgE,iBAAiB,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC3D,gBAAgB,CAAC,CAAC,CAAC;EACpE;EAEA;EAEA;EACAH,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACxF,IAAI,IAAI,CAAC,IAAI,CAACsF,UAAU,EAAE,OAAO,CAAC;IAC5C,OAAOwD,IAAI,CAACM,GAAG,CAAC,CAAC,EAAE,IAAI,CAAC9D,UAAU,CAACpF,cAAc,GAAG,IAAI,CAACF,IAAI,CAACC,MAAM,CAAC;EACvE;EAEAwF,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACzF,IAAI,IAAI,CAAC,IAAI,CAACsF,UAAU,EAAE,OAAO,CAAC;IAC5C,OAAOwD,IAAI,CAACG,GAAG,CAAE,IAAI,CAACjJ,IAAI,CAACC,MAAM,GAAG,IAAI,CAACqF,UAAU,CAACpF,cAAc,GAAI,GAAG,EAAE,GAAG,CAAC;EACjF;EAEAwF,oBAAoBA,CAAA;IAClB,MAAMgE,YAAY,GAAG,IAAI,CAAClE,qBAAqB,EAAE;IACjD,IAAIkE,YAAY,KAAK,CAAC,EAAE;MACtB,OAAO,4DAA4D;KACpE,MAAM,IAAIA,YAAY,IAAI,EAAE,EAAE;MAC7B,OAAO,eAAeA,YAAY,iCAAiC;KACpE,MAAM,IAAIA,YAAY,IAAI,EAAE,EAAE;MAC7B,OAAO,aAAaA,YAAY,2BAA2B;KAC5D,MAAM;MACL,OAAO,iBAAiBA,YAAY,2CAA2C;;EAEnF;EAEA;EACAhF,aAAaA,CAAA;IACX,IAAI,CAACwD,kBAAkB,EAAE;EAC3B;EAEAA,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAClI,IAAI,EAAE+H,OAAO,EAAE;MACvB,IAAI,CAACvG,eAAe,GAAG,EAAE;MACzB;;IAGF,MAAM0H,GAAG,GAAG,IAAId,IAAI,EAAE;IACtB,IAAIuB,SAAe;IAEnB,QAAQ,IAAI,CAACnF,cAAc;MACzB,KAAK,MAAM;QACTmF,SAAS,GAAG,IAAIvB,IAAI,CAACc,GAAG,CAACU,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC7D;MACF,KAAK,OAAO;QACVD,SAAS,GAAG,IAAIvB,IAAI,CAACc,GAAG,CAACX,WAAW,EAAE,EAAEW,GAAG,CAACb,QAAQ,EAAE,EAAE,CAAC,CAAC;QAC1D;MACF,KAAK,MAAM;QACTsB,SAAS,GAAG,IAAIvB,IAAI,CAACc,GAAG,CAACX,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAC7C;MACF;QACE,IAAI,CAAC/G,eAAe,GAAG,IAAI,CAACxB,IAAI,CAAC+H,OAAO,CAACC,GAAG,CAACC,CAAC,KAAK;UACjDL,EAAE,EAAEK,CAAC,CAACL,EAAE;UACRzG,IAAI,EAAE8G,CAAC,CAAC9G,IAAI;UACZV,WAAW,EAAEwH,CAAC,CAACxH,WAAW;UAC1BR,MAAM,EAAEgI,CAAC,CAAChI,MAAM;UAChBqB,SAAS,EAAE2G,CAAC,CAAC3G,SAAS;UACtBT,QAAQ,EAAE,UAAU;UACpBC,YAAY,EAAE;SACf,CAAC,CAAC;QACH;;IAGJ,IAAI,CAACU,eAAe,GAAG,IAAI,CAACxB,IAAI,CAAC+H,OAAO,CACrCU,MAAM,CAACR,CAAC,IAAI,IAAIG,IAAI,CAACH,CAAC,CAAC3G,SAAS,CAAC,IAAIqI,SAAS,CAAC,CAC/C3B,GAAG,CAACC,CAAC,KAAK;MACTL,EAAE,EAAEK,CAAC,CAACL,EAAE;MACRzG,IAAI,EAAE8G,CAAC,CAAC9G,IAAI;MACZV,WAAW,EAAEwH,CAAC,CAACxH,WAAW;MAC1BR,MAAM,EAAEgI,CAAC,CAAChI,MAAM;MAChBqB,SAAS,EAAE2G,CAAC,CAAC3G,SAAS;MACtBT,QAAQ,EAAE,UAAU;MACpBC,YAAY,EAAE;KACf,CAAC,CAAC;EACP;EAEA;EACAI,cAAcA,CAACC,IAAY;IACzB,MAAM0I,QAAQ,GAA8B;MAC1C,QAAQ,EAAE,SAAS;MACnB,OAAO,EAAE,SAAS;MAClB,SAAS,EAAE,SAAS;MACpB,YAAY,EAAE;KACf;IACD,OAAOA,QAAQ,CAAC1I,IAAI,CAAC,IAAI,SAAS;EACpC;EAEAC,aAAaA,CAACD,IAAY;IACxB,MAAM2I,OAAO,GAA8B;MACzC,QAAQ,EAAE,aAAa;MACvB,OAAO,EAAE,eAAe;MACxB,SAAS,EAAE,iBAAiB;MAC5B,YAAY,EAAE;KACf;IACD,OAAOA,OAAO,CAAC3I,IAAI,CAAC,IAAI,QAAQ;EAClC;EAEAkB,oBAAoBA,CAAClB,IAAY;IAC/B,MAAM0I,QAAQ,GAA8B;MAC1C,eAAe,EAAE,SAAS;MAC1B,YAAY,EAAE,SAAS;MACvB,aAAa,EAAE,SAAS;MACxB,UAAU,EAAE;KACb;IACD,OAAOA,QAAQ,CAAC1I,IAAI,CAAC,IAAI,SAAS;EACpC;EAEAmB,mBAAmBA,CAACnB,IAAY;IAC9B,MAAM2I,OAAO,GAA8B;MACzC,eAAe,EAAE,OAAO;MACxB,YAAY,EAAE,eAAe;MAC7B,aAAa,EAAE,cAAc;MAC7B,UAAU,EAAE;KACb;IACD,OAAOA,OAAO,CAAC3I,IAAI,CAAC,IAAI,MAAM;EAChC;EAEA;EACAQ,aAAaA,CAAA;IACXoI,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzC,IAAI,CAACvD,MAAM,CAACwD,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAnK,cAAcA,CAACoK,MAAc;IAC3B,IAAI,CAAC,IAAI,CAAClK,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,GAAGiK,MAAM,CAAChK,cAAc,EAAE;MAC1D6J,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD;;IAGFD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEE,MAAM,CAAC;IAC7C;IACA;IACA;IACA;EACF;EAEArH,mBAAmBA,CAAA;IACjB,IAAI,CAACuD,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;EAClD;EAEAlE,UAAUA,CAACiI,YAA0B;IACnCA,YAAY,CAAC/H,IAAI,GAAG,IAAI;IACxB,IAAI,CAACgD,mBAAmB,GAAG,IAAI,CAAC1C,aAAa,CAAC+F,MAAM,CAACU,CAAC,IAAI,CAACA,CAAC,CAAC/G,IAAI,CAAC,CAACa,MAAM;EAC3E;EAEAc,WAAWA,CAAA;IACT,IAAI,CAAC0C,MAAM,CAACwD,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEAhG,MAAMA,CAAA;IACJ,IAAI,CAACuC,WAAW,CAACvC,MAAM,EAAE;IACzB,IAAI,CAACwC,MAAM,CAACwD,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;;;uBAjYW3D,kBAAkB,EAAAxH,EAAA,CAAAsL,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxL,EAAA,CAAAsL,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA1L,EAAA,CAAAsL,iBAAA,CAAAK,EAAA,CAAAC,sBAAA,GAAA5L,EAAA,CAAAsL,iBAAA,CAAAO,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAlBtE,kBAAkB;MAAAuE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/G/BrM,EAAA,CAAAK,UAAA,IAAAkM,iCAAA,oBAA8C;UA4X9CvM,EAAA,CAAAC,cAAA,gBAIwC;UADhCD,EAAA,CAAAO,UAAA,mBAAAiM,oDAAA;YAAA,OAASF,GAAA,CAAAzJ,aAAA,EAAe;UAAA,EAAC;UAE/B7C,EAAA,CAAAC,cAAA,eAAU;UAAAD,EAAA,CAAAE,MAAA,sBAAe;UAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAC7B;;;UAlYyBH,EAAA,CAAAsB,UAAA,SAAAgL,GAAA,CAAApL,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}