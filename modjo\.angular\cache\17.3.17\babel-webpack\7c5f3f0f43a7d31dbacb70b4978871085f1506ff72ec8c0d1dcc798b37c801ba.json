{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, ANIMATION_MODULE_TYPE, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Inject, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Injection token to be used to override the default options for `mat-progress-bar`. */\nfunction MatProgressBar_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"div\", 2);\n  }\n}\nconst MAT_PROGRESS_BAR_DEFAULT_OPTIONS = new InjectionToken('MAT_PROGRESS_BAR_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatProgressBar`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_PROGRESS_BAR_LOCATION = new InjectionToken('mat-progress-bar-location', {\n  providedIn: 'root',\n  factory: MAT_PROGRESS_BAR_LOCATION_FACTORY\n});\n/** @docs-private */\nfunction MAT_PROGRESS_BAR_LOCATION_FACTORY() {\n  const _document = inject(DOCUMENT);\n  const _location = _document ? _document.location : null;\n  return {\n    // Note that this needs to be a function, rather than a property, because Angular\n    // will only resolve it once, but we want the current path on each call.\n    getPathname: () => _location ? _location.pathname + _location.search : ''\n  };\n}\nclass MatProgressBar {\n  constructor(_elementRef, _ngZone, _changeDetectorRef, _animationMode, defaults) {\n    this._elementRef = _elementRef;\n    this._ngZone = _ngZone;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._animationMode = _animationMode;\n    /** Flag that indicates whether NoopAnimations mode is set to true. */\n    this._isNoopAnimation = false;\n    this._defaultColor = 'primary';\n    this._value = 0;\n    this._bufferValue = 0;\n    /**\n     * Event emitted when animation of the primary progress bar completes. This event will not\n     * be emitted when animations are disabled, nor will it be emitted for modes with continuous\n     * animations (indeterminate and query).\n     */\n    this.animationEnd = new EventEmitter();\n    this._mode = 'determinate';\n    /** Event handler for `transitionend` events. */\n    this._transitionendHandler = event => {\n      if (this.animationEnd.observers.length === 0 || !event.target || !event.target.classList.contains('mdc-linear-progress__primary-bar')) {\n        return;\n      }\n      if (this.mode === 'determinate' || this.mode === 'buffer') {\n        this._ngZone.run(() => this.animationEnd.next({\n          value: this.value\n        }));\n      }\n    };\n    this._isNoopAnimation = _animationMode === 'NoopAnimations';\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n      this.mode = defaults.mode || this.mode;\n    }\n  }\n  // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n  /** Theme palette color of the progress bar. */\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value) {\n    this._color = value;\n  }\n  /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n  get value() {\n    return this._value;\n  }\n  set value(v) {\n    this._value = clamp(v || 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Buffer value of the progress bar. Defaults to zero. */\n  get bufferValue() {\n    return this._bufferValue || 0;\n  }\n  set bufferValue(v) {\n    this._bufferValue = clamp(v || 0);\n    this._changeDetectorRef.markForCheck();\n  }\n  /**\n   * Mode of the progress bar.\n   *\n   * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n   * 'determinate'.\n   * Mirrored to mode attribute.\n   */\n  get mode() {\n    return this._mode;\n  }\n  set mode(value) {\n    // Note that we don't technically need a getter and a setter here,\n    // but we use it to match the behavior of the existing mat-progress-bar.\n    this._mode = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  ngAfterViewInit() {\n    // Run outside angular so change detection didn't get triggered on every transition end\n    // instead only on the animation that we care about (primary value bar's transitionend)\n    this._ngZone.runOutsideAngular(() => {\n      this._elementRef.nativeElement.addEventListener('transitionend', this._transitionendHandler);\n    });\n  }\n  ngOnDestroy() {\n    this._elementRef.nativeElement.removeEventListener('transitionend', this._transitionendHandler);\n  }\n  /** Gets the transform style that should be applied to the primary bar. */\n  _getPrimaryBarTransform() {\n    return `scaleX(${this._isIndeterminate() ? 1 : this.value / 100})`;\n  }\n  /** Gets the `flex-basis` value that should be applied to the buffer bar. */\n  _getBufferBarFlexBasis() {\n    return `${this.mode === 'buffer' ? this.bufferValue : 100}%`;\n  }\n  /** Returns whether the progress bar is indeterminate. */\n  _isIndeterminate() {\n    return this.mode === 'indeterminate' || this.mode === 'query';\n  }\n  static {\n    this.ɵfac = function MatProgressBar_Factory(t) {\n      return new (t || MatProgressBar)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8), i0.ɵɵdirectiveInject(MAT_PROGRESS_BAR_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatProgressBar,\n      selectors: [[\"mat-progress-bar\"]],\n      hostAttrs: [\"role\", \"progressbar\", \"aria-valuemin\", \"0\", \"aria-valuemax\", \"100\", \"tabindex\", \"-1\", 1, \"mat-mdc-progress-bar\", \"mdc-linear-progress\"],\n      hostVars: 10,\n      hostBindings: function MatProgressBar_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"aria-valuenow\", ctx._isIndeterminate() ? null : ctx.value)(\"mode\", ctx.mode);\n          i0.ɵɵclassMap(\"mat-\" + ctx.color);\n          i0.ɵɵclassProp(\"_mat-animation-noopable\", ctx._isNoopAnimation)(\"mdc-linear-progress--animation-ready\", !ctx._isNoopAnimation)(\"mdc-linear-progress--indeterminate\", ctx._isIndeterminate());\n        }\n      },\n      inputs: {\n        color: \"color\",\n        value: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"value\", \"value\", numberAttribute],\n        bufferValue: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"bufferValue\", \"bufferValue\", numberAttribute],\n        mode: \"mode\"\n      },\n      outputs: {\n        animationEnd: \"animationEnd\"\n      },\n      exportAs: [\"matProgressBar\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      decls: 7,\n      vars: 5,\n      consts: [[\"aria-hidden\", \"true\", 1, \"mdc-linear-progress__buffer\"], [1, \"mdc-linear-progress__buffer-bar\"], [1, \"mdc-linear-progress__buffer-dots\"], [\"aria-hidden\", \"true\", 1, \"mdc-linear-progress__bar\", \"mdc-linear-progress__primary-bar\"], [1, \"mdc-linear-progress__bar-inner\"], [\"aria-hidden\", \"true\", 1, \"mdc-linear-progress__bar\", \"mdc-linear-progress__secondary-bar\"]],\n      template: function MatProgressBar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵelement(1, \"div\", 1);\n          i0.ɵɵtemplate(2, MatProgressBar_Conditional_2_Template, 1, 0, \"div\", 2);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"div\", 3);\n          i0.ɵɵelement(4, \"span\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"div\", 5);\n          i0.ɵɵelement(6, \"span\", 4);\n          i0.ɵɵelementEnd();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"flex-basis\", ctx._getBufferBarFlexBasis());\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(2, ctx.mode === \"buffer\" ? 2 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵstyleProp(\"transform\", ctx._getPrimaryBarTransform());\n        }\n      },\n      styles: [\"@keyframes mdc-linear-progress-primary-indeterminate-translate{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half))}100%{transform:translateX(var(--mdc-linear-progress-primary-full))}}@keyframes mdc-linear-progress-primary-indeterminate-scale{0%{transform:scaleX(0.08)}36.65%{animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);transform:scaleX(0.08)}69.15%{animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);transform:scaleX(0.661479)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full))}}@keyframes mdc-linear-progress-secondary-indeterminate-scale{0%{animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);transform:scaleX(0.08)}19.15%{animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);transform:scaleX(0.457104)}44.15%{animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);transform:scaleX(0.72796)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-primary-indeterminate-translate-reverse{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-primary-full-neg))}}@keyframes mdc-linear-progress-secondary-indeterminate-translate-reverse{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter-neg))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full-neg))}}@keyframes mdc-linear-progress-buffering-reverse{from{transform:translateX(-10px)}}.mdc-linear-progress{position:relative;width:100%;transform:translateZ(0);outline:1px solid rgba(0,0,0,0);overflow-x:hidden;transition:opacity 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}@media screen and (forced-colors: active){.mdc-linear-progress{outline-color:CanvasText}}.mdc-linear-progress__bar{position:absolute;top:0;bottom:0;margin:auto 0;width:100%;animation:none;transform-origin:top left;transition:transform 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__bar-inner{display:inline-block;position:absolute;width:100%;animation:none;border-top-style:solid}.mdc-linear-progress__buffer{display:flex;position:absolute;top:0;bottom:0;margin:auto 0;width:100%;overflow:hidden}.mdc-linear-progress__buffer-dots{background-repeat:repeat-x;flex:auto;transform:rotate(180deg);-webkit-mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");animation:mdc-linear-progress-buffering 250ms infinite linear}.mdc-linear-progress__buffer-bar{flex:0 1 100%;transition:flex-basis 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__primary-bar{transform:scaleX(0)}.mdc-linear-progress__secondary-bar{display:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__bar{transition:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{left:-145.166611%}.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{left:-54.888891%;display:block}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation:mdc-linear-progress-primary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-primary-indeterminate-scale 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation:mdc-linear-progress-secondary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-secondary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__bar{right:0;-webkit-transform-origin:center right;transform-origin:center right}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation-name:mdc-linear-progress-primary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation-name:mdc-linear-progress-secondary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__buffer-dots,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__buffer-dots{animation:mdc-linear-progress-buffering-reverse 250ms infinite linear;transform:rotate(0)}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{right:-145.166611%;left:auto}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{right:-54.888891%;left:auto}.mdc-linear-progress--closed{opacity:0}.mdc-linear-progress--closed-animation-off .mdc-linear-progress__buffer-dots{animation:none}.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar,.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar .mdc-linear-progress__bar-inner{animation:none}@keyframes mdc-linear-progress-buffering{from{transform:rotate(180deg) translateX(calc(var(--mdc-linear-progress-track-height) * -2.5))}}.mdc-linear-progress__bar-inner{border-color:var(--mdc-linear-progress-active-indicator-color)}.mdc-linear-progress__buffer-dots{background-color:var(--mdc-linear-progress-track-color)}@media(forced-colors: active){.mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill='rgba(244, 67, 54, 0.25)'/%3E%3C/svg%3E\\\")}}.mdc-linear-progress__buffer-bar{background-color:var(--mdc-linear-progress-track-color)}.mdc-linear-progress{height:max(var(--mdc-linear-progress-track-height), var(--mdc-linear-progress-active-indicator-height))}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress{height:4px}}.mdc-linear-progress__bar{height:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__bar-inner{border-top-width:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__buffer{height:var(--mdc-linear-progress-track-height)}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-size:10px var(--mdc-linear-progress-track-height)}}.mdc-linear-progress__buffer{border-radius:var(--mdc-linear-progress-track-shape)}.mat-mdc-progress-bar{display:block;text-align:start;--mdc-linear-progress-primary-half: 83.67142%;--mdc-linear-progress-primary-full: 200.611057%;--mdc-linear-progress-secondary-quarter: 37.651913%;--mdc-linear-progress-secondary-half: 84.386165%;--mdc-linear-progress-secondary-full: 160.277782%;--mdc-linear-progress-primary-half-neg: -83.67142%;--mdc-linear-progress-primary-full-neg: -200.611057%;--mdc-linear-progress-secondary-quarter-neg: -37.651913%;--mdc-linear-progress-secondary-half-neg: -84.386165%;--mdc-linear-progress-secondary-full-neg: -160.277782%}.mat-mdc-progress-bar[mode=query]{transform:scaleX(-1)}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-dots,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__secondary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__bar-inner.mdc-linear-progress__bar-inner{animation:none}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-bar{transition:transform 1ms}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressBar, [{\n    type: Component,\n    args: [{\n      selector: 'mat-progress-bar',\n      exportAs: 'matProgressBar',\n      host: {\n        'role': 'progressbar',\n        'aria-valuemin': '0',\n        'aria-valuemax': '100',\n        // set tab index to -1 so screen readers will read the aria-label\n        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n        'tabindex': '-1',\n        '[attr.aria-valuenow]': '_isIndeterminate() ? null : value',\n        '[attr.mode]': 'mode',\n        'class': 'mat-mdc-progress-bar mdc-linear-progress',\n        '[class]': '\"mat-\" + color',\n        '[class._mat-animation-noopable]': '_isNoopAnimation',\n        '[class.mdc-linear-progress--animation-ready]': '!_isNoopAnimation',\n        '[class.mdc-linear-progress--indeterminate]': '_isIndeterminate()'\n      },\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      template: \"<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-linear-progress__buffer\\\" aria-hidden=\\\"true\\\">\\n  <div\\n    class=\\\"mdc-linear-progress__buffer-bar\\\"\\n    [style.flex-basis]=\\\"_getBufferBarFlexBasis()\\\"></div>\\n  <!-- Remove the dots outside of buffer mode since they can cause CSP issues (see #28938) -->\\n  @if (mode === 'buffer') {\\n    <div class=\\\"mdc-linear-progress__buffer-dots\\\"></div>\\n  }\\n</div>\\n<div\\n  class=\\\"mdc-linear-progress__bar mdc-linear-progress__primary-bar\\\"\\n  aria-hidden=\\\"true\\\"\\n  [style.transform]=\\\"_getPrimaryBarTransform()\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n<div class=\\\"mdc-linear-progress__bar mdc-linear-progress__secondary-bar\\\" aria-hidden=\\\"true\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n\",\n      styles: [\"@keyframes mdc-linear-progress-primary-indeterminate-translate{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half))}100%{transform:translateX(var(--mdc-linear-progress-primary-full))}}@keyframes mdc-linear-progress-primary-indeterminate-scale{0%{transform:scaleX(0.08)}36.65%{animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);transform:scaleX(0.08)}69.15%{animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);transform:scaleX(0.661479)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full))}}@keyframes mdc-linear-progress-secondary-indeterminate-scale{0%{animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);transform:scaleX(0.08)}19.15%{animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);transform:scaleX(0.457104)}44.15%{animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);transform:scaleX(0.72796)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-primary-indeterminate-translate-reverse{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-primary-full-neg))}}@keyframes mdc-linear-progress-secondary-indeterminate-translate-reverse{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter-neg))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full-neg))}}@keyframes mdc-linear-progress-buffering-reverse{from{transform:translateX(-10px)}}.mdc-linear-progress{position:relative;width:100%;transform:translateZ(0);outline:1px solid rgba(0,0,0,0);overflow-x:hidden;transition:opacity 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}@media screen and (forced-colors: active){.mdc-linear-progress{outline-color:CanvasText}}.mdc-linear-progress__bar{position:absolute;top:0;bottom:0;margin:auto 0;width:100%;animation:none;transform-origin:top left;transition:transform 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__bar-inner{display:inline-block;position:absolute;width:100%;animation:none;border-top-style:solid}.mdc-linear-progress__buffer{display:flex;position:absolute;top:0;bottom:0;margin:auto 0;width:100%;overflow:hidden}.mdc-linear-progress__buffer-dots{background-repeat:repeat-x;flex:auto;transform:rotate(180deg);-webkit-mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");animation:mdc-linear-progress-buffering 250ms infinite linear}.mdc-linear-progress__buffer-bar{flex:0 1 100%;transition:flex-basis 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__primary-bar{transform:scaleX(0)}.mdc-linear-progress__secondary-bar{display:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__bar{transition:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{left:-145.166611%}.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{left:-54.888891%;display:block}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation:mdc-linear-progress-primary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-primary-indeterminate-scale 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation:mdc-linear-progress-secondary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-secondary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__bar{right:0;-webkit-transform-origin:center right;transform-origin:center right}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation-name:mdc-linear-progress-primary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation-name:mdc-linear-progress-secondary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__buffer-dots,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__buffer-dots{animation:mdc-linear-progress-buffering-reverse 250ms infinite linear;transform:rotate(0)}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{right:-145.166611%;left:auto}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{right:-54.888891%;left:auto}.mdc-linear-progress--closed{opacity:0}.mdc-linear-progress--closed-animation-off .mdc-linear-progress__buffer-dots{animation:none}.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar,.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar .mdc-linear-progress__bar-inner{animation:none}@keyframes mdc-linear-progress-buffering{from{transform:rotate(180deg) translateX(calc(var(--mdc-linear-progress-track-height) * -2.5))}}.mdc-linear-progress__bar-inner{border-color:var(--mdc-linear-progress-active-indicator-color)}.mdc-linear-progress__buffer-dots{background-color:var(--mdc-linear-progress-track-color)}@media(forced-colors: active){.mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill='rgba(244, 67, 54, 0.25)'/%3E%3C/svg%3E\\\")}}.mdc-linear-progress__buffer-bar{background-color:var(--mdc-linear-progress-track-color)}.mdc-linear-progress{height:max(var(--mdc-linear-progress-track-height), var(--mdc-linear-progress-active-indicator-height))}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress{height:4px}}.mdc-linear-progress__bar{height:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__bar-inner{border-top-width:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__buffer{height:var(--mdc-linear-progress-track-height)}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-size:10px var(--mdc-linear-progress-track-height)}}.mdc-linear-progress__buffer{border-radius:var(--mdc-linear-progress-track-shape)}.mat-mdc-progress-bar{display:block;text-align:start;--mdc-linear-progress-primary-half: 83.67142%;--mdc-linear-progress-primary-full: 200.611057%;--mdc-linear-progress-secondary-quarter: 37.651913%;--mdc-linear-progress-secondary-half: 84.386165%;--mdc-linear-progress-secondary-full: 160.277782%;--mdc-linear-progress-primary-half-neg: -83.67142%;--mdc-linear-progress-primary-full-neg: -200.611057%;--mdc-linear-progress-secondary-quarter-neg: -37.651913%;--mdc-linear-progress-secondary-half-neg: -84.386165%;--mdc-linear-progress-secondary-full-neg: -160.277782%}.mat-mdc-progress-bar[mode=query]{transform:scaleX(-1)}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-dots,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__secondary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__bar-inner.mdc-linear-progress__bar-inner{animation:none}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-bar{transition:transform 1ms}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_PROGRESS_BAR_DEFAULT_OPTIONS]\n    }]\n  }], {\n    color: [{\n      type: Input\n    }],\n    value: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    bufferValue: [{\n      type: Input,\n      args: [{\n        transform: numberAttribute\n      }]\n    }],\n    animationEnd: [{\n      type: Output\n    }],\n    mode: [{\n      type: Input\n    }]\n  });\n})();\n/** Clamps a value to be between two numbers, by default 0 and 100. */\nfunction clamp(v, min = 0, max = 100) {\n  return Math.max(min, Math.min(max, v));\n}\nclass MatProgressBarModule {\n  static {\n    this.ɵfac = function MatProgressBarModule_Factory(t) {\n      return new (t || MatProgressBarModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatProgressBarModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatProgressBarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatProgressBar],\n      exports: [MatProgressBar, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PROGRESS_BAR_DEFAULT_OPTIONS, MAT_PROGRESS_BAR_LOCATION, MAT_PROGRESS_BAR_LOCATION_FACTORY, MatProgressBar, MatProgressBarModule };", "map": {"version": 3, "names": ["i0", "InjectionToken", "inject", "EventEmitter", "ANIMATION_MODULE_TYPE", "numberAttribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "Optional", "Inject", "Input", "Output", "NgModule", "DOCUMENT", "MatCommonModule", "MatProgressBar_Conditional_2_Template", "rf", "ctx", "ɵɵelement", "MAT_PROGRESS_BAR_DEFAULT_OPTIONS", "MAT_PROGRESS_BAR_LOCATION", "providedIn", "factory", "MAT_PROGRESS_BAR_LOCATION_FACTORY", "_document", "_location", "location", "getPathname", "pathname", "search", "MatProgressBar", "constructor", "_elementRef", "_ngZone", "_changeDetectorRef", "_animationMode", "defaults", "_isNoopAnimation", "_defaultColor", "_value", "_bufferValue", "animationEnd", "_mode", "_<PERSON><PERSON><PERSON><PERSON><PERSON>", "event", "observers", "length", "target", "classList", "contains", "mode", "run", "next", "value", "color", "_color", "v", "clamp", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "bufferValue", "ngAfterViewInit", "runOutsideAngular", "nativeElement", "addEventListener", "ngOnDestroy", "removeEventListener", "_getPrimaryBarTransform", "_isIndeterminate", "_getBufferBarFlexBasis", "ɵfac", "MatProgressBar_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "NgZone", "ChangeDetectorRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatProgressBar_HostBindings", "ɵɵattribute", "ɵɵclassMap", "ɵɵclassProp", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "outputs", "exportAs", "standalone", "features", "ɵɵInputTransformsFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "MatProgressBar_Template", "ɵɵelementStart", "ɵɵtemplate", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "ɵɵconditional", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "OnPush", "None", "undefined", "decorators", "transform", "min", "max", "Math", "MatProgressBarModule", "MatProgressBarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/material/fesm2022/progress-bar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, ANIMATION_MODULE_TYPE, numberAttribute, Component, ChangeDetectionStrategy, ViewEncapsulation, Optional, Inject, Input, Output, NgModule } from '@angular/core';\nimport { DOCUMENT } from '@angular/common';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Injection token to be used to override the default options for `mat-progress-bar`. */\nconst MAT_PROGRESS_BAR_DEFAULT_OPTIONS = new InjectionToken('MAT_PROGRESS_BAR_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatProgressBar`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_PROGRESS_BAR_LOCATION = new InjectionToken('mat-progress-bar-location', { providedIn: 'root', factory: MAT_PROGRESS_BAR_LOCATION_FACTORY });\n/** @docs-private */\nfunction MAT_PROGRESS_BAR_LOCATION_FACTORY() {\n    const _document = inject(DOCUMENT);\n    const _location = _document ? _document.location : null;\n    return {\n        // Note that this needs to be a function, rather than a property, because Angular\n        // will only resolve it once, but we want the current path on each call.\n        getPathname: () => (_location ? _location.pathname + _location.search : ''),\n    };\n}\nclass MatProgressBar {\n    constructor(_elementRef, _ngZone, _changeDetectorRef, _animationMode, defaults) {\n        this._elementRef = _elementRef;\n        this._ngZone = _ngZone;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._animationMode = _animationMode;\n        /** Flag that indicates whether NoopAnimations mode is set to true. */\n        this._isNoopAnimation = false;\n        this._defaultColor = 'primary';\n        this._value = 0;\n        this._bufferValue = 0;\n        /**\n         * Event emitted when animation of the primary progress bar completes. This event will not\n         * be emitted when animations are disabled, nor will it be emitted for modes with continuous\n         * animations (indeterminate and query).\n         */\n        this.animationEnd = new EventEmitter();\n        this._mode = 'determinate';\n        /** Event handler for `transitionend` events. */\n        this._transitionendHandler = (event) => {\n            if (this.animationEnd.observers.length === 0 ||\n                !event.target ||\n                !event.target.classList.contains('mdc-linear-progress__primary-bar')) {\n                return;\n            }\n            if (this.mode === 'determinate' || this.mode === 'buffer') {\n                this._ngZone.run(() => this.animationEnd.next({ value: this.value }));\n            }\n        };\n        this._isNoopAnimation = _animationMode === 'NoopAnimations';\n        if (defaults) {\n            if (defaults.color) {\n                this.color = this._defaultColor = defaults.color;\n            }\n            this.mode = defaults.mode || this.mode;\n        }\n    }\n    // TODO: should be typed as `ThemePalette` but internal apps pass in arbitrary strings.\n    /** Theme palette color of the progress bar. */\n    get color() {\n        return this._color || this._defaultColor;\n    }\n    set color(value) {\n        this._color = value;\n    }\n    /** Value of the progress bar. Defaults to zero. Mirrored to aria-valuenow. */\n    get value() {\n        return this._value;\n    }\n    set value(v) {\n        this._value = clamp(v || 0);\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Buffer value of the progress bar. Defaults to zero. */\n    get bufferValue() {\n        return this._bufferValue || 0;\n    }\n    set bufferValue(v) {\n        this._bufferValue = clamp(v || 0);\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Mode of the progress bar.\n     *\n     * Input must be one of these values: determinate, indeterminate, buffer, query, defaults to\n     * 'determinate'.\n     * Mirrored to mode attribute.\n     */\n    get mode() {\n        return this._mode;\n    }\n    set mode(value) {\n        // Note that we don't technically need a getter and a setter here,\n        // but we use it to match the behavior of the existing mat-progress-bar.\n        this._mode = value;\n        this._changeDetectorRef.markForCheck();\n    }\n    ngAfterViewInit() {\n        // Run outside angular so change detection didn't get triggered on every transition end\n        // instead only on the animation that we care about (primary value bar's transitionend)\n        this._ngZone.runOutsideAngular(() => {\n            this._elementRef.nativeElement.addEventListener('transitionend', this._transitionendHandler);\n        });\n    }\n    ngOnDestroy() {\n        this._elementRef.nativeElement.removeEventListener('transitionend', this._transitionendHandler);\n    }\n    /** Gets the transform style that should be applied to the primary bar. */\n    _getPrimaryBarTransform() {\n        return `scaleX(${this._isIndeterminate() ? 1 : this.value / 100})`;\n    }\n    /** Gets the `flex-basis` value that should be applied to the buffer bar. */\n    _getBufferBarFlexBasis() {\n        return `${this.mode === 'buffer' ? this.bufferValue : 100}%`;\n    }\n    /** Returns whether the progress bar is indeterminate. */\n    _isIndeterminate() {\n        return this.mode === 'indeterminate' || this.mode === 'query';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatProgressBar, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }, { token: i0.ChangeDetectorRef }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: MAT_PROGRESS_BAR_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatProgressBar, isStandalone: true, selector: \"mat-progress-bar\", inputs: { color: \"color\", value: [\"value\", \"value\", numberAttribute], bufferValue: [\"bufferValue\", \"bufferValue\", numberAttribute], mode: \"mode\" }, outputs: { animationEnd: \"animationEnd\" }, host: { attributes: { \"role\": \"progressbar\", \"aria-valuemin\": \"0\", \"aria-valuemax\": \"100\", \"tabindex\": \"-1\" }, properties: { \"attr.aria-valuenow\": \"_isIndeterminate() ? null : value\", \"attr.mode\": \"mode\", \"class\": \"\\\"mat-\\\" + color\", \"class._mat-animation-noopable\": \"_isNoopAnimation\", \"class.mdc-linear-progress--animation-ready\": \"!_isNoopAnimation\", \"class.mdc-linear-progress--indeterminate\": \"_isIndeterminate()\" }, classAttribute: \"mat-mdc-progress-bar mdc-linear-progress\" }, exportAs: [\"matProgressBar\"], ngImport: i0, template: \"<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-linear-progress__buffer\\\" aria-hidden=\\\"true\\\">\\n  <div\\n    class=\\\"mdc-linear-progress__buffer-bar\\\"\\n    [style.flex-basis]=\\\"_getBufferBarFlexBasis()\\\"></div>\\n  <!-- Remove the dots outside of buffer mode since they can cause CSP issues (see #28938) -->\\n  @if (mode === 'buffer') {\\n    <div class=\\\"mdc-linear-progress__buffer-dots\\\"></div>\\n  }\\n</div>\\n<div\\n  class=\\\"mdc-linear-progress__bar mdc-linear-progress__primary-bar\\\"\\n  aria-hidden=\\\"true\\\"\\n  [style.transform]=\\\"_getPrimaryBarTransform()\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n<div class=\\\"mdc-linear-progress__bar mdc-linear-progress__secondary-bar\\\" aria-hidden=\\\"true\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n\", styles: [\"@keyframes mdc-linear-progress-primary-indeterminate-translate{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half))}100%{transform:translateX(var(--mdc-linear-progress-primary-full))}}@keyframes mdc-linear-progress-primary-indeterminate-scale{0%{transform:scaleX(0.08)}36.65%{animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);transform:scaleX(0.08)}69.15%{animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);transform:scaleX(0.661479)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full))}}@keyframes mdc-linear-progress-secondary-indeterminate-scale{0%{animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);transform:scaleX(0.08)}19.15%{animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);transform:scaleX(0.457104)}44.15%{animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);transform:scaleX(0.72796)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-primary-indeterminate-translate-reverse{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-primary-full-neg))}}@keyframes mdc-linear-progress-secondary-indeterminate-translate-reverse{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter-neg))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full-neg))}}@keyframes mdc-linear-progress-buffering-reverse{from{transform:translateX(-10px)}}.mdc-linear-progress{position:relative;width:100%;transform:translateZ(0);outline:1px solid rgba(0,0,0,0);overflow-x:hidden;transition:opacity 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}@media screen and (forced-colors: active){.mdc-linear-progress{outline-color:CanvasText}}.mdc-linear-progress__bar{position:absolute;top:0;bottom:0;margin:auto 0;width:100%;animation:none;transform-origin:top left;transition:transform 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__bar-inner{display:inline-block;position:absolute;width:100%;animation:none;border-top-style:solid}.mdc-linear-progress__buffer{display:flex;position:absolute;top:0;bottom:0;margin:auto 0;width:100%;overflow:hidden}.mdc-linear-progress__buffer-dots{background-repeat:repeat-x;flex:auto;transform:rotate(180deg);-webkit-mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");animation:mdc-linear-progress-buffering 250ms infinite linear}.mdc-linear-progress__buffer-bar{flex:0 1 100%;transition:flex-basis 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__primary-bar{transform:scaleX(0)}.mdc-linear-progress__secondary-bar{display:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__bar{transition:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{left:-145.166611%}.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{left:-54.888891%;display:block}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation:mdc-linear-progress-primary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-primary-indeterminate-scale 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation:mdc-linear-progress-secondary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-secondary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__bar{right:0;-webkit-transform-origin:center right;transform-origin:center right}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation-name:mdc-linear-progress-primary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation-name:mdc-linear-progress-secondary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__buffer-dots,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__buffer-dots{animation:mdc-linear-progress-buffering-reverse 250ms infinite linear;transform:rotate(0)}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{right:-145.166611%;left:auto}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{right:-54.888891%;left:auto}.mdc-linear-progress--closed{opacity:0}.mdc-linear-progress--closed-animation-off .mdc-linear-progress__buffer-dots{animation:none}.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar,.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar .mdc-linear-progress__bar-inner{animation:none}@keyframes mdc-linear-progress-buffering{from{transform:rotate(180deg) translateX(calc(var(--mdc-linear-progress-track-height) * -2.5))}}.mdc-linear-progress__bar-inner{border-color:var(--mdc-linear-progress-active-indicator-color)}.mdc-linear-progress__buffer-dots{background-color:var(--mdc-linear-progress-track-color)}@media(forced-colors: active){.mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill='rgba(244, 67, 54, 0.25)'/%3E%3C/svg%3E\\\")}}.mdc-linear-progress__buffer-bar{background-color:var(--mdc-linear-progress-track-color)}.mdc-linear-progress{height:max(var(--mdc-linear-progress-track-height), var(--mdc-linear-progress-active-indicator-height))}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress{height:4px}}.mdc-linear-progress__bar{height:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__bar-inner{border-top-width:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__buffer{height:var(--mdc-linear-progress-track-height)}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-size:10px var(--mdc-linear-progress-track-height)}}.mdc-linear-progress__buffer{border-radius:var(--mdc-linear-progress-track-shape)}.mat-mdc-progress-bar{display:block;text-align:start;--mdc-linear-progress-primary-half: 83.67142%;--mdc-linear-progress-primary-full: 200.611057%;--mdc-linear-progress-secondary-quarter: 37.651913%;--mdc-linear-progress-secondary-half: 84.386165%;--mdc-linear-progress-secondary-full: 160.277782%;--mdc-linear-progress-primary-half-neg: -83.67142%;--mdc-linear-progress-primary-full-neg: -200.611057%;--mdc-linear-progress-secondary-quarter-neg: -37.651913%;--mdc-linear-progress-secondary-half-neg: -84.386165%;--mdc-linear-progress-secondary-full-neg: -160.277782%}.mat-mdc-progress-bar[mode=query]{transform:scaleX(-1)}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-dots,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__secondary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__bar-inner.mdc-linear-progress__bar-inner{animation:none}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-bar{transition:transform 1ms}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatProgressBar, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-progress-bar', exportAs: 'matProgressBar', host: {\n                        'role': 'progressbar',\n                        'aria-valuemin': '0',\n                        'aria-valuemax': '100',\n                        // set tab index to -1 so screen readers will read the aria-label\n                        // Note: there is a known issue with JAWS that does not read progressbar aria labels on FireFox\n                        'tabindex': '-1',\n                        '[attr.aria-valuenow]': '_isIndeterminate() ? null : value',\n                        '[attr.mode]': 'mode',\n                        'class': 'mat-mdc-progress-bar mdc-linear-progress',\n                        '[class]': '\"mat-\" + color',\n                        '[class._mat-animation-noopable]': '_isNoopAnimation',\n                        '[class.mdc-linear-progress--animation-ready]': '!_isNoopAnimation',\n                        '[class.mdc-linear-progress--indeterminate]': '_isIndeterminate()',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, standalone: true, template: \"<!--\\n  All children need to be hidden for screen readers in order to support ChromeVox.\\n  More context in the issue: https://github.com/angular/components/issues/22165.\\n-->\\n<div class=\\\"mdc-linear-progress__buffer\\\" aria-hidden=\\\"true\\\">\\n  <div\\n    class=\\\"mdc-linear-progress__buffer-bar\\\"\\n    [style.flex-basis]=\\\"_getBufferBarFlexBasis()\\\"></div>\\n  <!-- Remove the dots outside of buffer mode since they can cause CSP issues (see #28938) -->\\n  @if (mode === 'buffer') {\\n    <div class=\\\"mdc-linear-progress__buffer-dots\\\"></div>\\n  }\\n</div>\\n<div\\n  class=\\\"mdc-linear-progress__bar mdc-linear-progress__primary-bar\\\"\\n  aria-hidden=\\\"true\\\"\\n  [style.transform]=\\\"_getPrimaryBarTransform()\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n<div class=\\\"mdc-linear-progress__bar mdc-linear-progress__secondary-bar\\\" aria-hidden=\\\"true\\\">\\n  <span class=\\\"mdc-linear-progress__bar-inner\\\"></span>\\n</div>\\n\", styles: [\"@keyframes mdc-linear-progress-primary-indeterminate-translate{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half))}100%{transform:translateX(var(--mdc-linear-progress-primary-full))}}@keyframes mdc-linear-progress-primary-indeterminate-scale{0%{transform:scaleX(0.08)}36.65%{animation-timing-function:cubic-bezier(0.334731, 0.12482, 0.785844, 1);transform:scaleX(0.08)}69.15%{animation-timing-function:cubic-bezier(0.06, 0.11, 0.6, 1);transform:scaleX(0.661479)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-secondary-indeterminate-translate{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full))}}@keyframes mdc-linear-progress-secondary-indeterminate-scale{0%{animation-timing-function:cubic-bezier(0.205028, 0.057051, 0.57661, 0.453971);transform:scaleX(0.08)}19.15%{animation-timing-function:cubic-bezier(0.152313, 0.196432, 0.648374, 1.004315);transform:scaleX(0.457104)}44.15%{animation-timing-function:cubic-bezier(0.257759, -0.003163, 0.211762, 1.38179);transform:scaleX(0.72796)}100%{transform:scaleX(0.08)}}@keyframes mdc-linear-progress-primary-indeterminate-translate-reverse{0%{transform:translateX(0)}20%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(0)}59.15%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(var(--mdc-linear-progress-primary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-primary-full-neg))}}@keyframes mdc-linear-progress-secondary-indeterminate-translate-reverse{0%{animation-timing-function:cubic-bezier(0.15, 0, 0.515058, 0.409685);transform:translateX(0)}25%{animation-timing-function:cubic-bezier(0.31033, 0.284058, 0.8, 0.733712);transform:translateX(var(--mdc-linear-progress-secondary-quarter-neg))}48.35%{animation-timing-function:cubic-bezier(0.4, 0.627035, 0.6, 0.902026);transform:translateX(var(--mdc-linear-progress-secondary-half-neg))}100%{transform:translateX(var(--mdc-linear-progress-secondary-full-neg))}}@keyframes mdc-linear-progress-buffering-reverse{from{transform:translateX(-10px)}}.mdc-linear-progress{position:relative;width:100%;transform:translateZ(0);outline:1px solid rgba(0,0,0,0);overflow-x:hidden;transition:opacity 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}@media screen and (forced-colors: active){.mdc-linear-progress{outline-color:CanvasText}}.mdc-linear-progress__bar{position:absolute;top:0;bottom:0;margin:auto 0;width:100%;animation:none;transform-origin:top left;transition:transform 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__bar-inner{display:inline-block;position:absolute;width:100%;animation:none;border-top-style:solid}.mdc-linear-progress__buffer{display:flex;position:absolute;top:0;bottom:0;margin:auto 0;width:100%;overflow:hidden}.mdc-linear-progress__buffer-dots{background-repeat:repeat-x;flex:auto;transform:rotate(180deg);-webkit-mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");mask-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='xMinYMin slice'%3E%3Ccircle cx='1' cy='1' r='1'/%3E%3C/svg%3E\\\");animation:mdc-linear-progress-buffering 250ms infinite linear}.mdc-linear-progress__buffer-bar{flex:0 1 100%;transition:flex-basis 250ms 0ms cubic-bezier(0.4, 0, 0.6, 1)}.mdc-linear-progress__primary-bar{transform:scaleX(0)}.mdc-linear-progress__secondary-bar{display:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__bar{transition:none}.mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{left:-145.166611%}.mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{left:-54.888891%;display:block}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation:mdc-linear-progress-primary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-primary-indeterminate-scale 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation:mdc-linear-progress-secondary-indeterminate-translate 2s infinite linear}.mdc-linear-progress--indeterminate.mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar>.mdc-linear-progress__bar-inner{animation:mdc-linear-progress-secondary-indeterminate-scale 2s infinite linear}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__bar{right:0;-webkit-transform-origin:center right;transform-origin:center right}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__primary-bar{animation-name:mdc-linear-progress-primary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--animation-ready .mdc-linear-progress__secondary-bar{animation-name:mdc-linear-progress-secondary-indeterminate-translate-reverse}[dir=rtl] .mdc-linear-progress:not([dir=ltr]) .mdc-linear-progress__buffer-dots,.mdc-linear-progress[dir=rtl]:not([dir=ltr]) .mdc-linear-progress__buffer-dots{animation:mdc-linear-progress-buffering-reverse 250ms infinite linear;transform:rotate(0)}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__primary-bar{right:-145.166611%;left:auto}[dir=rtl] .mdc-linear-progress:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar,.mdc-linear-progress[dir=rtl]:not([dir=ltr]).mdc-linear-progress--indeterminate .mdc-linear-progress__secondary-bar{right:-54.888891%;left:auto}.mdc-linear-progress--closed{opacity:0}.mdc-linear-progress--closed-animation-off .mdc-linear-progress__buffer-dots{animation:none}.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar,.mdc-linear-progress--closed-animation-off.mdc-linear-progress--indeterminate .mdc-linear-progress__bar .mdc-linear-progress__bar-inner{animation:none}@keyframes mdc-linear-progress-buffering{from{transform:rotate(180deg) translateX(calc(var(--mdc-linear-progress-track-height) * -2.5))}}.mdc-linear-progress__bar-inner{border-color:var(--mdc-linear-progress-active-indicator-color)}.mdc-linear-progress__buffer-dots{background-color:var(--mdc-linear-progress-track-color)}@media(forced-colors: active){.mdc-linear-progress__buffer-dots{background-color:ButtonBorder}}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-color:rgba(0,0,0,0);background-image:url(\\\"data:image/svg+xml,%3Csvg version='1.1' xmlns='http://www.w3.org/2000/svg' xmlns:xlink='http://www.w3.org/1999/xlink' x='0px' y='0px' enable-background='new 0 0 5 2' xml:space='preserve' viewBox='0 0 5 2' preserveAspectRatio='none slice'%3E%3Ccircle cx='1' cy='1' r='1' fill='rgba(244, 67, 54, 0.25)'/%3E%3C/svg%3E\\\")}}.mdc-linear-progress__buffer-bar{background-color:var(--mdc-linear-progress-track-color)}.mdc-linear-progress{height:max(var(--mdc-linear-progress-track-height), var(--mdc-linear-progress-active-indicator-height))}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress{height:4px}}.mdc-linear-progress__bar{height:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__bar-inner{border-top-width:var(--mdc-linear-progress-active-indicator-height)}.mdc-linear-progress__buffer{height:var(--mdc-linear-progress-track-height)}@media all and (-ms-high-contrast: none),(-ms-high-contrast: active){.mdc-linear-progress__buffer-dots{background-size:10px var(--mdc-linear-progress-track-height)}}.mdc-linear-progress__buffer{border-radius:var(--mdc-linear-progress-track-shape)}.mat-mdc-progress-bar{display:block;text-align:start;--mdc-linear-progress-primary-half: 83.67142%;--mdc-linear-progress-primary-full: 200.611057%;--mdc-linear-progress-secondary-quarter: 37.651913%;--mdc-linear-progress-secondary-half: 84.386165%;--mdc-linear-progress-secondary-full: 160.277782%;--mdc-linear-progress-primary-half-neg: -83.67142%;--mdc-linear-progress-primary-full-neg: -200.611057%;--mdc-linear-progress-secondary-quarter-neg: -37.651913%;--mdc-linear-progress-secondary-half-neg: -84.386165%;--mdc-linear-progress-secondary-full-neg: -160.277782%}.mat-mdc-progress-bar[mode=query]{transform:scaleX(-1)}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-dots,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__secondary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__bar-inner.mdc-linear-progress__bar-inner{animation:none}.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__primary-bar,.mat-mdc-progress-bar._mat-animation-noopable .mdc-linear-progress__buffer-bar{transition:transform 1ms}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.NgZone }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_PROGRESS_BAR_DEFAULT_OPTIONS]\n                }] }], propDecorators: { color: [{\n                type: Input\n            }], value: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], bufferValue: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], animationEnd: [{\n                type: Output\n            }], mode: [{\n                type: Input\n            }] } });\n/** Clamps a value to be between two numbers, by default 0 and 100. */\nfunction clamp(v, min = 0, max = 100) {\n    return Math.max(min, Math.min(max, v));\n}\n\nclass MatProgressBarModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatProgressBarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatProgressBarModule, imports: [MatProgressBar], exports: [MatProgressBar, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatProgressBarModule, imports: [MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatProgressBarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatProgressBar],\n                    exports: [MatProgressBar, MatCommonModule],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_PROGRESS_BAR_DEFAULT_OPTIONS, MAT_PROGRESS_BAR_LOCATION, MAT_PROGRESS_BAR_LOCATION_FACTORY, MatProgressBar, MatProgressBarModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,MAAM,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,uBAAuB,EAAEC,iBAAiB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC9M,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,eAAe,QAAQ,wBAAwB;;AAExD;AAAA,SAAAC,sCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAqHoGjB,EAAE,CAAAmB,SAAA,YACozC,CAAC;EAAA;AAAA;AArH35C,MAAMC,gCAAgC,GAAG,IAAInB,cAAc,CAAC,kCAAkC,CAAC;AAC/F;AACA;AACA;AACA;AACA;AACA,MAAMoB,yBAAyB,GAAG,IAAIpB,cAAc,CAAC,2BAA2B,EAAE;EAAEqB,UAAU,EAAE,MAAM;EAAEC,OAAO,EAAEC;AAAkC,CAAC,CAAC;AACrJ;AACA,SAASA,iCAAiCA,CAAA,EAAG;EACzC,MAAMC,SAAS,GAAGvB,MAAM,CAACY,QAAQ,CAAC;EAClC,MAAMY,SAAS,GAAGD,SAAS,GAAGA,SAAS,CAACE,QAAQ,GAAG,IAAI;EACvD,OAAO;IACH;IACA;IACAC,WAAW,EAAEA,CAAA,KAAOF,SAAS,GAAGA,SAAS,CAACG,QAAQ,GAAGH,SAAS,CAACI,MAAM,GAAG;EAC5E,CAAC;AACL;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAACC,WAAW,EAAEC,OAAO,EAAEC,kBAAkB,EAAEC,cAAc,EAAEC,QAAQ,EAAE;IAC5E,IAAI,CAACJ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC;IACA,IAAI,CAACE,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,aAAa,GAAG,SAAS;IAC9B,IAAI,CAACC,MAAM,GAAG,CAAC;IACf,IAAI,CAACC,YAAY,GAAG,CAAC;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAIvC,YAAY,CAAC,CAAC;IACtC,IAAI,CAACwC,KAAK,GAAG,aAAa;IAC1B;IACA,IAAI,CAACC,qBAAqB,GAAIC,KAAK,IAAK;MACpC,IAAI,IAAI,CAACH,YAAY,CAACI,SAAS,CAACC,MAAM,KAAK,CAAC,IACxC,CAACF,KAAK,CAACG,MAAM,IACb,CAACH,KAAK,CAACG,MAAM,CAACC,SAAS,CAACC,QAAQ,CAAC,kCAAkC,CAAC,EAAE;QACtE;MACJ;MACA,IAAI,IAAI,CAACC,IAAI,KAAK,aAAa,IAAI,IAAI,CAACA,IAAI,KAAK,QAAQ,EAAE;QACvD,IAAI,CAACjB,OAAO,CAACkB,GAAG,CAAC,MAAM,IAAI,CAACV,YAAY,CAACW,IAAI,CAAC;UAAEC,KAAK,EAAE,IAAI,CAACA;QAAM,CAAC,CAAC,CAAC;MACzE;IACJ,CAAC;IACD,IAAI,CAAChB,gBAAgB,GAAGF,cAAc,KAAK,gBAAgB;IAC3D,IAAIC,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACkB,KAAK,EAAE;QAChB,IAAI,CAACA,KAAK,GAAG,IAAI,CAAChB,aAAa,GAAGF,QAAQ,CAACkB,KAAK;MACpD;MACA,IAAI,CAACJ,IAAI,GAAGd,QAAQ,CAACc,IAAI,IAAI,IAAI,CAACA,IAAI;IAC1C;EACJ;EACA;EACA;EACA,IAAII,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM,IAAI,IAAI,CAACjB,aAAa;EAC5C;EACA,IAAIgB,KAAKA,CAACD,KAAK,EAAE;IACb,IAAI,CAACE,MAAM,GAAGF,KAAK;EACvB;EACA;EACA,IAAIA,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACd,MAAM;EACtB;EACA,IAAIc,KAAKA,CAACG,CAAC,EAAE;IACT,IAAI,CAACjB,MAAM,GAAGkB,KAAK,CAACD,CAAC,IAAI,CAAC,CAAC;IAC3B,IAAI,CAACtB,kBAAkB,CAACwB,YAAY,CAAC,CAAC;EAC1C;EACA;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACnB,YAAY,IAAI,CAAC;EACjC;EACA,IAAImB,WAAWA,CAACH,CAAC,EAAE;IACf,IAAI,CAAChB,YAAY,GAAGiB,KAAK,CAACD,CAAC,IAAI,CAAC,CAAC;IACjC,IAAI,CAACtB,kBAAkB,CAACwB,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI,IAAIR,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACR,KAAK;EACrB;EACA,IAAIQ,IAAIA,CAACG,KAAK,EAAE;IACZ;IACA;IACA,IAAI,CAACX,KAAK,GAAGW,KAAK;IAClB,IAAI,CAACnB,kBAAkB,CAACwB,YAAY,CAAC,CAAC;EAC1C;EACAE,eAAeA,CAAA,EAAG;IACd;IACA;IACA,IAAI,CAAC3B,OAAO,CAAC4B,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC7B,WAAW,CAAC8B,aAAa,CAACC,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAACpB,qBAAqB,CAAC;IAChG,CAAC,CAAC;EACN;EACAqB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAChC,WAAW,CAAC8B,aAAa,CAACG,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAACtB,qBAAqB,CAAC;EACnG;EACA;EACAuB,uBAAuBA,CAAA,EAAG;IACtB,OAAO,UAAU,IAAI,CAACC,gBAAgB,CAAC,CAAC,GAAG,CAAC,GAAG,IAAI,CAACd,KAAK,GAAG,GAAG,GAAG;EACtE;EACA;EACAe,sBAAsBA,CAAA,EAAG;IACrB,OAAO,GAAG,IAAI,CAAClB,IAAI,KAAK,QAAQ,GAAG,IAAI,CAACS,WAAW,GAAG,GAAG,GAAG;EAChE;EACA;EACAQ,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACjB,IAAI,KAAK,eAAe,IAAI,IAAI,CAACA,IAAI,KAAK,OAAO;EACjE;EACA;IAAS,IAAI,CAACmB,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFzC,cAAc,EAAxB/B,EAAE,CAAAyE,iBAAA,CAAwCzE,EAAE,CAAC0E,UAAU,GAAvD1E,EAAE,CAAAyE,iBAAA,CAAkEzE,EAAE,CAAC2E,MAAM,GAA7E3E,EAAE,CAAAyE,iBAAA,CAAwFzE,EAAE,CAAC4E,iBAAiB,GAA9G5E,EAAE,CAAAyE,iBAAA,CAAyHrE,qBAAqB,MAAhJJ,EAAE,CAAAyE,iBAAA,CAA2KrD,gCAAgC;IAAA,CAA4D;EAAE;EAC3W;IAAS,IAAI,CAACyD,IAAI,kBAD8E7E,EAAE,CAAA8E,iBAAA;MAAAC,IAAA,EACJhD,cAAc;MAAAiD,SAAA;MAAAC,SAAA,WAAiR,aAAa,mBAAmB,GAAG,mBAAmB,KAAK,cAAc,IAAI;MAAAC,QAAA;MAAAC,YAAA,WAAAC,4BAAAnE,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD1WjB,EAAE,CAAAqF,WAAA,kBACJnE,GAAA,CAAAkD,gBAAA,CAAiB,CAAC,GAAG,IAAI,GAAAlD,GAAA,CAAAoC,KAAA,UAAApC,GAAA,CAAAiC,IAAA;UADvBnD,EAAE,CAAAsF,UAAA,CACJ,MAAM,GAAApE,GAAA,CAAAqC,KAAO,CAAC;UADZvD,EAAE,CAAAuF,WAAA,4BAAArE,GAAA,CAAAoB,gBACS,CAAC,0CAAApB,GAAA,CAAAoB,gBAAD,CAAC,uCAAdpB,GAAA,CAAAkD,gBAAA,CAAiB,CAAJ,CAAC;QAAA;MAAA;MAAAoB,MAAA;QAAAjC,KAAA;QAAAD,KAAA,GADZtD,EAAE,CAAAyF,YAAA,CAAAC,0BAAA,oBACkHrF,eAAe;QAAAuD,WAAA,GADnI5D,EAAE,CAAAyF,YAAA,CAAAC,0BAAA,gCACgLrF,eAAe;QAAA8C,IAAA;MAAA;MAAAwC,OAAA;QAAAjD,YAAA;MAAA;MAAAkD,QAAA;MAAAC,UAAA;MAAAC,QAAA,GADjM9F,EAAE,CAAA+F,wBAAA,EAAF/F,EAAE,CAAAgG,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAApF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFjB,EAAE,CAAAsG,cAAA,YACwgC,CAAC;UAD3gCtG,EAAE,CAAAmB,SAAA,YAC2nC,CAAC;UAD9nCnB,EAAE,CAAAuG,UAAA,IAAAvF,qCAAA,gBACwvC,CAAC;UAD3vChB,EAAE,CAAAwG,YAAA,CACi0C,CAAC;UADp0CxG,EAAE,CAAAsG,cAAA,YAC09C,CAAC;UAD79CtG,EAAE,CAAAmB,SAAA,aACohD,CAAC;UADvhDnB,EAAE,CAAAwG,YAAA,CAC4hD,CAAC;UAD/hDxG,EAAE,CAAAsG,cAAA,YAC8nD,CAAC;UADjoDtG,EAAE,CAAAmB,SAAA,aACwrD,CAAC;UAD3rDnB,EAAE,CAAAwG,YAAA,CACgsD,CAAC;QAAA;QAAA,IAAAvF,EAAA;UADnsDjB,EAAE,CAAAyG,SAAA,CAConC,CAAC;UADvnCzG,EAAE,CAAA0G,WAAA,eAAAxF,GAAA,CAAAmD,sBAAA,EAConC,CAAC;UADvnCrE,EAAE,CAAAyG,SAAA,CACyzC,CAAC;UAD5zCzG,EAAE,CAAA2G,aAAA,IAAAzF,GAAA,CAAAiC,IAAA,sBACyzC,CAAC;UAD5zCnD,EAAE,CAAAyG,SAAA,CACy9C,CAAC;UAD59CzG,EAAE,CAAA0G,WAAA,cAAAxF,GAAA,CAAAiD,uBAAA,EACy9C,CAAC;QAAA;MAAA;MAAAyC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAA26U;EAAE;AAC7+X;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG/G,EAAE,CAAAgH,iBAAA,CAGXjF,cAAc,EAAc,CAAC;IAC5GgD,IAAI,EAAEzE,SAAS;IACf2G,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,kBAAkB;MAAEtB,QAAQ,EAAE,gBAAgB;MAAEuB,IAAI,EAAE;QAC7D,MAAM,EAAE,aAAa;QACrB,eAAe,EAAE,GAAG;QACpB,eAAe,EAAE,KAAK;QACtB;QACA;QACA,UAAU,EAAE,IAAI;QAChB,sBAAsB,EAAE,mCAAmC;QAC3D,aAAa,EAAE,MAAM;QACrB,OAAO,EAAE,0CAA0C;QACnD,SAAS,EAAE,gBAAgB;QAC3B,iCAAiC,EAAE,kBAAkB;QACrD,8CAA8C,EAAE,mBAAmB;QACnE,4CAA4C,EAAE;MAClD,CAAC;MAAEL,eAAe,EAAEvG,uBAAuB,CAAC6G,MAAM;MAAEP,aAAa,EAAErG,iBAAiB,CAAC6G,IAAI;MAAExB,UAAU,EAAE,IAAI;MAAEO,QAAQ,EAAE,66BAA66B;MAAEQ,MAAM,EAAE,CAAC,olUAAolU;IAAE,CAAC;EAClpW,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE7B,IAAI,EAAE/E,EAAE,CAAC0E;EAAW,CAAC,EAAE;IAAEK,IAAI,EAAE/E,EAAE,CAAC2E;EAAO,CAAC,EAAE;IAAEI,IAAI,EAAE/E,EAAE,CAAC4E;EAAkB,CAAC,EAAE;IAAEG,IAAI,EAAEuC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC7HxC,IAAI,EAAEtE;IACV,CAAC,EAAE;MACCsE,IAAI,EAAErE,MAAM;MACZuG,IAAI,EAAE,CAAC7G,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAE2E,IAAI,EAAEuC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCxC,IAAI,EAAEtE;IACV,CAAC,EAAE;MACCsE,IAAI,EAAErE,MAAM;MACZuG,IAAI,EAAE,CAAC7F,gCAAgC;IAC3C,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEmC,KAAK,EAAE,CAAC;MACjCwB,IAAI,EAAEpE;IACV,CAAC,CAAC;IAAE2C,KAAK,EAAE,CAAC;MACRyB,IAAI,EAAEpE,KAAK;MACXsG,IAAI,EAAE,CAAC;QAAEO,SAAS,EAAEnH;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEuD,WAAW,EAAE,CAAC;MACdmB,IAAI,EAAEpE,KAAK;MACXsG,IAAI,EAAE,CAAC;QAAEO,SAAS,EAAEnH;MAAgB,CAAC;IACzC,CAAC,CAAC;IAAEqC,YAAY,EAAE,CAAC;MACfqC,IAAI,EAAEnE;IACV,CAAC,CAAC;IAAEuC,IAAI,EAAE,CAAC;MACP4B,IAAI,EAAEpE;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA,SAAS+C,KAAKA,CAACD,CAAC,EAAEgE,GAAG,GAAG,CAAC,EAAEC,GAAG,GAAG,GAAG,EAAE;EAClC,OAAOC,IAAI,CAACD,GAAG,CAACD,GAAG,EAAEE,IAAI,CAACF,GAAG,CAACC,GAAG,EAAEjE,CAAC,CAAC,CAAC;AAC1C;AAEA,MAAMmE,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAACtD,IAAI,YAAAuD,6BAAArD,CAAA;MAAA,YAAAA,CAAA,IAAwFoD,oBAAoB;IAAA,CAAkD;EAAE;EAClL;IAAS,IAAI,CAACE,IAAI,kBAlD8E9H,EAAE,CAAA+H,gBAAA;MAAAhD,IAAA,EAkDS6C;IAAoB,EAA0E;EAAE;EAC3M;IAAS,IAAI,CAACI,IAAI,kBAnD8EhI,EAAE,CAAAiI,gBAAA;MAAAC,OAAA,GAmDyCnH,eAAe;IAAA,EAAI;EAAE;AACpK;AACA;EAAA,QAAAgG,SAAA,oBAAAA,SAAA,KArDoG/G,EAAE,CAAAgH,iBAAA,CAqDXY,oBAAoB,EAAc,CAAC;IAClH7C,IAAI,EAAElE,QAAQ;IACdoG,IAAI,EAAE,CAAC;MACCiB,OAAO,EAAE,CAACnG,cAAc,CAAC;MACzBoG,OAAO,EAAE,CAACpG,cAAc,EAAEhB,eAAe;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASK,gCAAgC,EAAEC,yBAAyB,EAAEG,iCAAiC,EAAEO,cAAc,EAAE6F,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}