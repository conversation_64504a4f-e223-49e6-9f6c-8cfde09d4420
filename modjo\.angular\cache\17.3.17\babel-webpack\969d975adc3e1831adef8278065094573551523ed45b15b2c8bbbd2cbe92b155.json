{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nfunction DashboardComponent_div_0_mat_card_18_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 16)(1, \"mat-card-content\")(2, \"div\", 17)(3, \"div\", 18)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 19)(7, \"h3\", 20);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const stat_r1 = ctx.$implicit;\n    const i_r2 = ctx.index;\n    i0.ɵɵstyleProp(\"animation-delay\", i_r2 * 0.1 + \"s\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"color\", stat_r1.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r1.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.title);\n  }\n}\nfunction DashboardComponent_div_0_mat_card_23_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 21)(1, \"mat-card-content\")(2, \"div\", 22)(3, \"mat-icon\", 23);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const action_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", action_r3.route);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"color\", action_r3.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", action_r3.icon, \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r3.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r3.description);\n  }\n}\nfunction DashboardComponent_div_0_div_24_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 30)(5, \"p\", 31);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 32);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 33);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const transaction_r4 = ctx.$implicit;\n    const ctx_r4 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"activity-\" + transaction_r4.type);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r4.getActivityIcon(transaction_r4.type), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(transaction_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(9, 9, transaction_r4.timestamp, \"short\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(transaction_r4.points > 0 ? \"positive\" : \"negative\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", transaction_r4.points > 0 ? \"+\" : \"\", \"\", transaction_r4.points, \" pts \");\n  }\n}\nfunction DashboardComponent_div_0_div_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"h2\");\n    i0.ɵɵtext(2, \"Activit\\u00E9 r\\u00E9cente\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"mat-card\", 25)(4, \"mat-card-content\")(5, \"div\", 26);\n    i0.ɵɵtemplate(6, DashboardComponent_div_0_div_24_div_6_Template, 12, 12, \"div\", 27);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.user.history.slice(-5));\n  }\n}\nfunction DashboardComponent_div_0_div_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"mat-card\", 35)(2, \"mat-card-content\")(3, \"div\", 36)(4, \"mat-icon\", 37);\n    i0.ɵɵtext(5, \"explore\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"Commencez votre aventure!\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Scannez votre premier code QR ou participez \\u00E0 une activit\\u00E9 communautaire pour gagner vos premiers points.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 38)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"qr_code_scanner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Scanner un QR Code \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction DashboardComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"h1\", 4);\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\", 5)(6, \"mat-icon\");\n    i0.ɵɵtext(7, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 6)(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 7)(15, \"h2\", 8);\n    i0.ɵɵtext(16, \"\\uD83D\\uDCCA Mes statistiques\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"div\", 9);\n    i0.ɵɵtemplate(18, DashboardComponent_div_0_mat_card_18_Template, 11, 7, \"mat-card\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 11)(20, \"h2\");\n    i0.ɵɵtext(21, \"Actions rapides\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(22, \"div\", 12);\n    i0.ɵɵtemplate(23, DashboardComponent_div_0_mat_card_23_Template, 9, 5, \"mat-card\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(24, DashboardComponent_div_0_div_24_Template, 7, 1, \"div\", 14)(25, DashboardComponent_div_0_div_25_Template, 14, 0, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.getGreeting(), \", \", ctx_r4.user.name, \"!\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r4.user.city, \" \\u2022 \", ctx_r4.getRoleDisplayName(ctx_r4.user.role), \" \");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r4.user.points, \" points\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.stats);\n    i0.ɵɵadvance(5);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.quickActions);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r4.user.history && ctx_r4.user.history.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", !ctx_r4.user.history || ctx_r4.user.history.length === 0);\n  }\n}\nexport class DashboardComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.user = null;\n    this.quickActions = [{\n      title: 'Scanner QR',\n      description: 'Scanner un code QR pour gagner des points',\n      icon: 'qr_code_scanner',\n      route: '/qr-scanner',\n      color: 'primary'\n    }, {\n      title: 'Récompenses',\n      description: 'Découvrir les récompenses disponibles',\n      icon: 'card_giftcard',\n      route: '/rewards',\n      color: 'accent'\n    }, {\n      title: 'Mon Profil',\n      description: 'Voir et modifier mon profil',\n      icon: 'person',\n      route: '/profile',\n      color: 'primary'\n    }];\n    this.stats = [{\n      title: 'Points totaux',\n      value: '0',\n      icon: 'stars',\n      color: '#ffd700'\n    }, {\n      title: 'Actions validées',\n      value: '0',\n      icon: 'verified',\n      color: '#4caf50'\n    }, {\n      title: 'Récompenses obtenues',\n      value: '0',\n      icon: 'card_giftcard',\n      color: '#ff9800'\n    }];\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        this.updateStats(user);\n      }\n    });\n  }\n  updateStats(user) {\n    this.stats[0].value = user.points.toString();\n    // Count validated actions from history\n    const validatedActions = user.history?.filter(h => h.type === 'validated').length || 0;\n    this.stats[1].value = validatedActions.toString();\n    // Count spent points (rewards obtained)\n    const rewardsObtained = user.history?.filter(h => h.type === 'spent').length || 0;\n    this.stats[2].value = rewardsObtained.toString();\n  }\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n  getRoleDisplayName(role) {\n    const roleNames = {\n      'user': 'Utilisateur',\n      'provider': 'Partenaire',\n      'validator': 'Validateur',\n      'admin': 'Administrateur'\n    };\n    return roleNames[role] || role;\n  }\n  getActivityIcon(type) {\n    const iconMap = {\n      'earned': 'add_circle',\n      'spent': 'remove_circle',\n      'transferred': 'swap_horiz',\n      'validated': 'verified',\n      'bonus': 'star'\n    };\n    return iconMap[type] || 'circle';\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"dashboard-container\", 4, \"ngIf\"], [1, \"dashboard-container\"], [1, \"welcome-section\", \"slide-up\"], [1, \"welcome-content\"], [1, \"bounce-in\"], [1, \"user-info\", \"fade-in\"], [1, \"points-badge\", \"glow\"], [1, \"stats-section\"], [1, \"section-title\", \"slide-in-left\"], [1, \"stats-grid\"], [\"class\", \"stat-card floating-card\", 3, \"animation-delay\", 4, \"ngFor\", \"ngForOf\"], [1, \"actions-section\"], [1, \"actions-grid\"], [\"class\", \"action-card\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"activity-section\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [1, \"stat-card\", \"floating-card\"], [1, \"stat-content\"], [1, \"stat-icon\"], [1, \"stat-info\"], [1, \"counter-animation\"], [1, \"action-card\", 3, \"routerLink\"], [1, \"action-content\"], [1, \"action-icon\", 3, \"color\"], [1, \"activity-section\"], [1, \"activity-card\"], [1, \"activity-list\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"activity-item\"], [1, \"activity-icon\"], [1, \"activity-info\"], [1, \"activity-description\"], [1, \"activity-date\"], [1, \"activity-points\"], [1, \"empty-state\"], [1, \"empty-card\"], [1, \"empty-content\"], [1, \"empty-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/qr-scanner\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, DashboardComponent_div_0_Template, 26, 9, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n        }\n      },\n      dependencies: [i2.NgForOf, i2.NgIf, i3.RouterLink, i4.MatCard, i4.MatCardContent, i5.MatButton, i6.MatIcon, i2.DatePipe],\n      styles: [\".dashboard-container[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  padding: 0;\\n  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.welcome-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 40px;\\n  padding: 40px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 24px;\\n  color: white;\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: 0 20px 40px rgba(103, 126, 234, 0.3);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.welcome-section[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n.welcome-content[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.welcome-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  font-family: 'Poppins', sans-serif;\\n  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\\n  background: linear-gradient(45deg, #ffffff, #f0f0f0);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.user-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin: 0;\\n  opacity: 0.95;\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n}\\n\\n.user-info[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));\\n}\\n\\n.points-badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  background: rgba(255, 255, 255, 0.25);\\n  padding: 16px 24px;\\n  border-radius: 32px;\\n  font-size: 1.2rem;\\n  font-weight: 700;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.points-badge[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.05);\\n  background: rgba(255, 255, 255, 0.35);\\n}\\n\\n.points-badge[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n  color: #ffd700;\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));\\n  animation: pulse 2s infinite;\\n}\\n\\n.stats-section[_ngcontent-%COMP%], .actions-section[_ngcontent-%COMP%], .activity-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.stats-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .actions-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%], .activity-section[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  color: #333;\\n  font-size: 1.5rem;\\n  font-weight: 500;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 24px;\\n  margin-bottom: 40px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  border-radius: 20px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  height: 4px;\\n  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);\\n  opacity: 0.8;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px) scale(1.02);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:nth-child(1)::before {\\n  background: linear-gradient(90deg, #ffd700, #ffed4e);\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:nth-child(2)::before {\\n  background: linear-gradient(90deg, #4facfe, #00f2fe);\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:nth-child(3)::before {\\n  background: linear-gradient(90deg, #43e97b, #38f9d7);\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:nth-child(4)::before {\\n  background: linear-gradient(90deg, #fa709a, #fee140);\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 20px;\\n  padding: 24px;\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  background: linear-gradient(135deg, rgba(103, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\\n  border-radius: 16px;\\n  padding: 16px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);\\n}\\n\\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));\\n}\\n\\n.stat-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 2.2rem;\\n  font-weight: 700;\\n  color: #2d3748;\\n  font-family: 'Poppins', sans-serif;\\n  background: linear-gradient(135deg, #2d3748, #4a5568);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #718096;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  text-transform: uppercase;\\n  letter-spacing: 0.5px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n}\\n\\n.action-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n  cursor: pointer;\\n  transition: transform 0.2s ease, box-shadow 0.2s ease;\\n}\\n\\n.action-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);\\n}\\n\\n.action-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 16px;\\n}\\n\\n.action-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  margin-bottom: 16px;\\n}\\n\\n.action-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  width: 3rem;\\n  height: 3rem;\\n}\\n\\n.action-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n  font-size: 1.2rem;\\n  font-weight: 500;\\n}\\n\\n.action-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.activity-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.activity-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 12px 0;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n.activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.activity-icon[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n.activity-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.activity-earned[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] { color: #4caf50; }\\n.activity-spent[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] { color: #f44336; }\\n.activity-transferred[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] { color: #2196f3; }\\n.activity-validated[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] { color: #ff9800; }\\n.activity-bonus[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] { color: #9c27b0; }\\n\\n.activity-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.activity-description[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.activity-date[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.8rem;\\n}\\n\\n.activity-points[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 1rem;\\n}\\n\\n.activity-points.positive[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n.activity-points.negative[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n}\\n\\n.empty-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.empty-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 32px;\\n}\\n\\n.empty-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  color: #ccc;\\n  margin-bottom: 16px;\\n}\\n\\n.empty-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n  font-size: 1.5rem;\\n}\\n\\n.empty-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: #666;\\n  max-width: 400px;\\n  margin-left: auto;\\n  margin-right: auto;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .welcome-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n    text-align: center;\\n  }\\n  \\n  .welcome-content[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  \\n  .stats-grid[_ngcontent-%COMP%], .actions-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .activity-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n  }\\n  \\n  .activity-points[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvZGFzaGJvYXJkL2NvbXBvbmVudHMvZGFzaGJvYXJkL2Rhc2hib2FyZC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBO0VBQ0UsaUJBQWlCO0VBQ2pCLGNBQWM7RUFDZCxVQUFVO0VBQ1YsbURBQW1EO0FBQ3JEOztBQUVBO0VBQ0UsYUFBYTtFQUNiLDhCQUE4QjtFQUM5QixtQkFBbUI7RUFDbkIsbUJBQW1CO0VBQ25CLGFBQWE7RUFDYiw2REFBNkQ7RUFDN0QsbUJBQW1CO0VBQ25CLFlBQVk7RUFDWixrQkFBa0I7RUFDbEIsZ0JBQWdCO0VBQ2hCLGdEQUFnRDtFQUNoRCxtQ0FBMkI7VUFBM0IsMkJBQTJCO0VBQzNCLDBDQUEwQztBQUM1Qzs7QUFFQTtFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsTUFBTTtFQUNOLE9BQU87RUFDUCxRQUFRO0VBQ1IsU0FBUztFQUNUOztvRkFFa0Y7RUFDbEYsb0JBQW9CO0FBQ3RCOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLFVBQVU7QUFDWjs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixpQkFBaUI7RUFDakIsZ0JBQWdCO0VBQ2hCLGtDQUFrQztFQUNsQyx5Q0FBeUM7RUFDekMsb0RBQW9EO0VBQ3BELDZCQUE2QjtFQUM3QixvQ0FBb0M7RUFDcEMscUJBQXFCO0FBQ3ZCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQixTQUFTO0VBQ1QsU0FBUztFQUNULGFBQWE7RUFDYixpQkFBaUI7RUFDakIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsaURBQWlEO0FBQ25EOztBQUVBO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQixTQUFTO0VBQ1QscUNBQXFDO0VBQ3JDLGtCQUFrQjtFQUNsQixtQkFBbUI7RUFDbkIsaUJBQWlCO0VBQ2pCLGdCQUFnQjtFQUNoQixtQ0FBMkI7VUFBM0IsMkJBQTJCO0VBQzNCLDBDQUEwQztFQUMxQyx5Q0FBeUM7RUFDekMsaURBQWlEO0VBQ2pELGtCQUFrQjtFQUNsQixVQUFVO0FBQ1o7O0FBRUE7RUFDRSxzQkFBc0I7RUFDdEIscUNBQXFDO0FBQ3ZDOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLGFBQWE7RUFDYixjQUFjO0VBQ2QsY0FBYztFQUNkLGlEQUFpRDtFQUNqRCw0QkFBNEI7QUFDOUI7O0FBRUE7OztFQUdFLG1CQUFtQjtBQUNyQjs7QUFFQTs7O0VBR0Usa0JBQWtCO0VBQ2xCLFdBQVc7RUFDWCxpQkFBaUI7RUFDakIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLDJEQUEyRDtFQUMzRCxTQUFTO0VBQ1QsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsbUJBQW1CO0VBQ25CLDBDQUEwQztFQUMxQyxxQ0FBcUM7RUFDckMsbUNBQTJCO1VBQTNCLDJCQUEyQjtFQUMzQiwwQ0FBMEM7RUFDMUMsaURBQWlEO0VBQ2pELGdCQUFnQjtFQUNoQixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsa0JBQWtCO0VBQ2xCLE1BQU07RUFDTixPQUFPO0VBQ1AsUUFBUTtFQUNSLFdBQVc7RUFDWCw2REFBNkQ7RUFDN0QsWUFBWTtBQUNkOztBQUVBO0VBQ0UsdUNBQXVDO0VBQ3ZDLDJDQUEyQztBQUM3Qzs7QUFFQTtFQUNFLG9EQUFvRDtBQUN0RDs7QUFFQTtFQUNFLG9EQUFvRDtBQUN0RDs7QUFFQTtFQUNFLG9EQUFvRDtBQUN0RDs7QUFFQTtFQUNFLG9EQUFvRDtBQUN0RDs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsU0FBUztFQUNULGFBQWE7RUFDYixrQkFBa0I7RUFDbEIsVUFBVTtBQUNaOztBQUVBO0VBQ0UsZUFBZTtFQUNmLHNGQUFzRjtFQUN0RixtQkFBbUI7RUFDbkIsYUFBYTtFQUNiLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLHlDQUF5QztBQUMzQzs7QUFFQTtFQUNFLGVBQWU7RUFDZixXQUFXO0VBQ1gsWUFBWTtFQUNaLGlEQUFpRDtBQUNuRDs7QUFFQTtFQUNFLE9BQU87QUFDVDs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixpQkFBaUI7RUFDakIsZ0JBQWdCO0VBQ2hCLGNBQWM7RUFDZCxrQ0FBa0M7RUFDbEMscURBQXFEO0VBQ3JELDZCQUE2QjtFQUM3QixvQ0FBb0M7RUFDcEMscUJBQXFCO0FBQ3ZCOztBQUVBO0VBQ0UsU0FBUztFQUNULGNBQWM7RUFDZCxlQUFlO0VBQ2YsZ0JBQWdCO0VBQ2hCLHlCQUF5QjtFQUN6QixxQkFBcUI7QUFDdkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsMkRBQTJEO0VBQzNELFNBQVM7QUFDWDs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQix3Q0FBd0M7RUFDeEMsZUFBZTtFQUNmLHFEQUFxRDtBQUN2RDs7QUFFQTtFQUNFLDJCQUEyQjtFQUMzQiwwQ0FBMEM7QUFDNUM7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsYUFBYTtBQUNmOztBQUVBO0VBQ0UsZUFBZTtFQUNmLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGVBQWU7RUFDZixXQUFXO0VBQ1gsWUFBWTtBQUNkOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLFdBQVc7RUFDWCxpQkFBaUI7RUFDakIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsU0FBUztFQUNULFdBQVc7RUFDWCxpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsd0NBQXdDO0FBQzFDOztBQUVBO0VBQ0UsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixTQUFTO0FBQ1g7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFNBQVM7RUFDVCxlQUFlO0VBQ2YsNkJBQTZCO0FBQy9COztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsY0FBYztBQUNoQjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixhQUFhO0VBQ2IsY0FBYztBQUNoQjs7QUFFQSw0QkFBNEIsY0FBYyxFQUFFO0FBQzVDLDJCQUEyQixjQUFjLEVBQUU7QUFDM0MsaUNBQWlDLGNBQWMsRUFBRTtBQUNqRCwrQkFBK0IsY0FBYyxFQUFFO0FBQy9DLDJCQUEyQixjQUFjLEVBQUU7O0FBRTNDO0VBQ0UsT0FBTztBQUNUOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLFdBQVc7RUFDWCxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxTQUFTO0VBQ1QsV0FBVztFQUNYLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLGdCQUFnQjtFQUNoQixlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsY0FBYztBQUNoQjs7QUFFQTtFQUNFLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsd0NBQXdDO0FBQzFDOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLGFBQWE7QUFDZjs7QUFFQTtFQUNFLGVBQWU7RUFDZixXQUFXO0VBQ1gsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLFdBQVc7RUFDWCxpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsV0FBVztFQUNYLGdCQUFnQjtFQUNoQixpQkFBaUI7RUFDakIsa0JBQWtCO0FBQ3BCOztBQUVBLHNCQUFzQjtBQUN0QjtFQUNFO0lBQ0Usc0JBQXNCO0lBQ3RCLFNBQVM7SUFDVCxrQkFBa0I7RUFDcEI7O0VBRUE7SUFDRSxpQkFBaUI7RUFDbkI7O0VBRUE7O0lBRUUsMEJBQTBCO0VBQzVCOztFQUVBO0lBQ0Usc0JBQXNCO0lBQ3RCLHVCQUF1QjtJQUN2QixRQUFRO0VBQ1Y7O0VBRUE7SUFDRSxvQkFBb0I7RUFDdEI7QUFDRjs7QUFLQSw0d2NBQTR3YyIsInNvdXJjZXNDb250ZW50IjpbIi5kYXNoYm9hcmQtY29udGFpbmVyIHtcbiAgbWF4LXdpZHRoOiAxNDAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xuICBwYWRkaW5nOiAwO1xuICBhbmltYXRpb246IGZhZGVJbiAwLjZzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XG59XG5cbi53ZWxjb21lLXNlY3Rpb24ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIG1hcmdpbi1ib3R0b206IDQwcHg7XG4gIHBhZGRpbmc6IDQwcHg7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7XG4gIGJvcmRlci1yYWRpdXM6IDI0cHg7XG4gIGNvbG9yOiB3aGl0ZTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBib3gtc2hhZG93OiAwIDIwcHggNDBweCByZ2JhKDEwMywgMTI2LCAyMzQsIDAuMyk7XG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigyMHB4KTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjEpO1xufVxuXG4ud2VsY29tZS1zZWN0aW9uOjpiZWZvcmUge1xuICBjb250ZW50OiAnJztcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHJpZ2h0OiAwO1xuICBib3R0b206IDA7XG4gIGJhY2tncm91bmQ6XG4gICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAyMCUgODAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgMCUsIHRyYW5zcGFyZW50IDUwJSksXG4gICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA4MCUgMjAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgMCUsIHRyYW5zcGFyZW50IDUwJSk7XG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xufVxuXG4ud2VsY29tZS1jb250ZW50IHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB6LWluZGV4OiAxO1xufVxuXG4ud2VsY29tZS1jb250ZW50IGgxIHtcbiAgbWFyZ2luOiAwIDAgMTJweCAwO1xuICBmb250LXNpemU6IDIuNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgZm9udC1mYW1pbHk6ICdQb3BwaW5zJywgc2Fucy1zZXJpZjtcbiAgdGV4dC1zaGFkb3c6IDAgNHB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMik7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg0NWRlZywgI2ZmZmZmZiwgI2YwZjBmMCk7XG4gIC13ZWJraXQtYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xuICAtd2Via2l0LXRleHQtZmlsbC1jb2xvcjogdHJhbnNwYXJlbnQ7XG4gIGJhY2tncm91bmQtY2xpcDogdGV4dDtcbn1cblxuLnVzZXItaW5mbyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMTJweDtcbiAgbWFyZ2luOiAwO1xuICBvcGFjaXR5OiAwLjk1O1xuICBmb250LXNpemU6IDEuMXJlbTtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbn1cblxuLnVzZXItaW5mbyBtYXQtaWNvbiB7XG4gIGZpbHRlcjogZHJvcC1zaGFkb3coMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4yKSk7XG59XG5cbi5wb2ludHMtYmFkZ2Uge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDEycHg7XG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yNSk7XG4gIHBhZGRpbmc6IDE2cHggMjRweDtcbiAgYm9yZGVyLXJhZGl1czogMzJweDtcbiAgZm9udC1zaXplOiAxLjJyZW07XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcbiAgYm9yZGVyOiAxcHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjMpO1xuICBib3gtc2hhZG93OiAwIDhweCAzMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB6LWluZGV4OiAxO1xufVxuXG4ucG9pbnRzLWJhZGdlOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiBzY2FsZSgxLjA1KTtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjM1KTtcbn1cblxuLnBvaW50cy1iYWRnZSBtYXQtaWNvbiB7XG4gIGZvbnQtc2l6ZTogMS41cmVtO1xuICB3aWR0aDogMS41cmVtO1xuICBoZWlnaHQ6IDEuNXJlbTtcbiAgY29sb3I6ICNmZmQ3MDA7XG4gIGZpbHRlcjogZHJvcC1zaGFkb3coMCAycHggNHB4IHJnYmEoMCwgMCwgMCwgMC4zKSk7XG4gIGFuaW1hdGlvbjogcHVsc2UgMnMgaW5maW5pdGU7XG59XG5cbi5zdGF0cy1zZWN0aW9uLFxuLmFjdGlvbnMtc2VjdGlvbixcbi5hY3Rpdml0eS1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogMzJweDtcbn1cblxuLnN0YXRzLXNlY3Rpb24gaDIsXG4uYWN0aW9ucy1zZWN0aW9uIGgyLFxuLmFjdGl2aXR5LXNlY3Rpb24gaDIge1xuICBtYXJnaW46IDAgMCAxNnB4IDA7XG4gIGNvbG9yOiAjMzMzO1xuICBmb250LXNpemU6IDEuNXJlbTtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbn1cblxuLnN0YXRzLWdyaWQge1xuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDI4MHB4LCAxZnIpKTtcbiAgZ2FwOiAyNHB4O1xuICBtYXJnaW4tYm90dG9tOiA0MHB4O1xufVxuXG4uc3RhdC1jYXJkIHtcbiAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgYm94LXNoYWRvdzogMCAxMHB4IDMwcHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICBiYWNrZ3JvdW5kOiByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuOTUpO1xuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMjBweCk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xufVxuXG4uc3RhdC1jYXJkOjpiZWZvcmUge1xuICBjb250ZW50OiAnJztcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHJpZ2h0OiAwO1xuICBoZWlnaHQ6IDRweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjNjY3ZWVhLCAjNzY0YmEyLCAjZjA5M2ZiKTtcbiAgb3BhY2l0eTogMC44O1xufVxuXG4uc3RhdC1jYXJkOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC04cHgpIHNjYWxlKDEuMDIpO1xuICBib3gtc2hhZG93OiAwIDIwcHggNDBweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xufVxuXG4uc3RhdC1jYXJkOm50aC1jaGlsZCgxKTo6YmVmb3JlIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjZmZkNzAwLCAjZmZlZDRlKTtcbn1cblxuLnN0YXQtY2FyZDpudGgtY2hpbGQoMik6OmJlZm9yZSB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg5MGRlZywgIzRmYWNmZSwgIzAwZjJmZSk7XG59XG5cbi5zdGF0LWNhcmQ6bnRoLWNoaWxkKDMpOjpiZWZvcmUge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICM0M2U5N2IsICMzOGY5ZDcpO1xufVxuXG4uc3RhdC1jYXJkOm50aC1jaGlsZCg0KTo6YmVmb3JlIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDkwZGVnLCAjZmE3MDlhLCAjZmVlMTQwKTtcbn1cblxuLnN0YXQtY29udGVudCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMjBweDtcbiAgcGFkZGluZzogMjRweDtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB6LWluZGV4OiAxO1xufVxuXG4uc3RhdC1pY29uIHtcbiAgZm9udC1zaXplOiAzcmVtO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDEwMywgMTI2LCAyMzQsIDAuMSksIHJnYmEoMTE4LCA3NSwgMTYyLCAwLjEpKTtcbiAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgcGFkZGluZzogMTZweDtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGJveC1zaGFkb3c6IDAgOHB4IDE2cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xufVxuXG4uc3RhdC1pY29uIG1hdC1pY29uIHtcbiAgZm9udC1zaXplOiAzcmVtO1xuICB3aWR0aDogM3JlbTtcbiAgaGVpZ2h0OiAzcmVtO1xuICBmaWx0ZXI6IGRyb3Atc2hhZG93KDAgMnB4IDRweCByZ2JhKDAsIDAsIDAsIDAuMSkpO1xufVxuXG4uc3RhdC1pbmZvIHtcbiAgZmxleDogMTtcbn1cblxuLnN0YXQtaW5mbyBoMyB7XG4gIG1hcmdpbjogMCAwIDhweCAwO1xuICBmb250LXNpemU6IDIuMnJlbTtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgY29sb3I6ICMyZDM3NDg7XG4gIGZvbnQtZmFtaWx5OiAnUG9wcGlucycsIHNhbnMtc2VyaWY7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICMyZDM3NDgsICM0YTU1NjgpO1xuICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcbiAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xuICBiYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XG59XG5cbi5zdGF0LWluZm8gcCB7XG4gIG1hcmdpbjogMDtcbiAgY29sb3I6ICM3MTgwOTY7XG4gIGZvbnQtc2l6ZTogMXJlbTtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbiAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcbiAgbGV0dGVyLXNwYWNpbmc6IDAuNXB4O1xufVxuXG4uYWN0aW9ucy1ncmlkIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyNTBweCwgMWZyKSk7XG4gIGdhcDogMTZweDtcbn1cblxuLmFjdGlvbi1jYXJkIHtcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiB0cmFuc2Zvcm0gMC4ycyBlYXNlLCBib3gtc2hhZG93IDAuMnMgZWFzZTtcbn1cblxuLmFjdGlvbi1jYXJkOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICBib3gtc2hhZG93OiAwIDRweCAxNnB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XG59XG5cbi5hY3Rpb24tY29udGVudCB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgcGFkZGluZzogMTZweDtcbn1cblxuLmFjdGlvbi1pY29uIHtcbiAgZm9udC1zaXplOiAzcmVtO1xuICBtYXJnaW4tYm90dG9tOiAxNnB4O1xufVxuXG4uYWN0aW9uLWljb24gbWF0LWljb24ge1xuICBmb250LXNpemU6IDNyZW07XG4gIHdpZHRoOiAzcmVtO1xuICBoZWlnaHQ6IDNyZW07XG59XG5cbi5hY3Rpb24tY29udGVudCBoMyB7XG4gIG1hcmdpbjogMCAwIDhweCAwO1xuICBjb2xvcjogIzMzMztcbiAgZm9udC1zaXplOiAxLjJyZW07XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG59XG5cbi5hY3Rpb24tY29udGVudCBwIHtcbiAgbWFyZ2luOiAwO1xuICBjb2xvcjogIzY2NjtcbiAgZm9udC1zaXplOiAwLjlyZW07XG59XG5cbi5hY3Rpdml0eS1jYXJkIHtcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgYm94LXNoYWRvdzogMCAycHggOHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbn1cblxuLmFjdGl2aXR5LWxpc3Qge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBnYXA6IDE2cHg7XG59XG5cbi5hY3Rpdml0eS1pdGVtIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiAxNnB4O1xuICBwYWRkaW5nOiAxMnB4IDA7XG4gIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZWVlO1xufVxuXG4uYWN0aXZpdHktaXRlbTpsYXN0LWNoaWxkIHtcbiAgYm9yZGVyLWJvdHRvbTogbm9uZTtcbn1cblxuLmFjdGl2aXR5LWljb24ge1xuICBmbGV4LXNocmluazogMDtcbn1cblxuLmFjdGl2aXR5LWljb24gbWF0LWljb24ge1xuICBmb250LXNpemU6IDEuNXJlbTtcbiAgd2lkdGg6IDEuNXJlbTtcbiAgaGVpZ2h0OiAxLjVyZW07XG59XG5cbi5hY3Rpdml0eS1lYXJuZWQgbWF0LWljb24geyBjb2xvcjogIzRjYWY1MDsgfVxuLmFjdGl2aXR5LXNwZW50IG1hdC1pY29uIHsgY29sb3I6ICNmNDQzMzY7IH1cbi5hY3Rpdml0eS10cmFuc2ZlcnJlZCBtYXQtaWNvbiB7IGNvbG9yOiAjMjE5NmYzOyB9XG4uYWN0aXZpdHktdmFsaWRhdGVkIG1hdC1pY29uIHsgY29sb3I6ICNmZjk4MDA7IH1cbi5hY3Rpdml0eS1ib251cyBtYXQtaWNvbiB7IGNvbG9yOiAjOWMyN2IwOyB9XG5cbi5hY3Rpdml0eS1pbmZvIHtcbiAgZmxleDogMTtcbn1cblxuLmFjdGl2aXR5LWRlc2NyaXB0aW9uIHtcbiAgbWFyZ2luOiAwIDAgNHB4IDA7XG4gIGNvbG9yOiAjMzMzO1xuICBmb250LXdlaWdodDogNTAwO1xufVxuXG4uYWN0aXZpdHktZGF0ZSB7XG4gIG1hcmdpbjogMDtcbiAgY29sb3I6ICM2NjY7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xufVxuXG4uYWN0aXZpdHktcG9pbnRzIHtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgZm9udC1zaXplOiAxcmVtO1xufVxuXG4uYWN0aXZpdHktcG9pbnRzLnBvc2l0aXZlIHtcbiAgY29sb3I6ICM0Y2FmNTA7XG59XG5cbi5hY3Rpdml0eS1wb2ludHMubmVnYXRpdmUge1xuICBjb2xvcjogI2Y0NDMzNjtcbn1cblxuLmVtcHR5LXN0YXRlIHtcbiAgbWFyZ2luLXRvcDogMzJweDtcbn1cblxuLmVtcHR5LWNhcmQge1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBib3gtc2hhZG93OiAwIDJweCA4cHggcmdiYSgwLCAwLCAwLCAwLjEpO1xufVxuXG4uZW1wdHktY29udGVudCB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgcGFkZGluZzogMzJweDtcbn1cblxuLmVtcHR5LWljb24ge1xuICBmb250LXNpemU6IDRyZW07XG4gIGNvbG9yOiAjY2NjO1xuICBtYXJnaW4tYm90dG9tOiAxNnB4O1xufVxuXG4uZW1wdHktY29udGVudCBoMyB7XG4gIG1hcmdpbjogMCAwIDhweCAwO1xuICBjb2xvcjogIzMzMztcbiAgZm9udC1zaXplOiAxLjVyZW07XG59XG5cbi5lbXB0eS1jb250ZW50IHAge1xuICBtYXJnaW46IDAgMCAyNHB4IDA7XG4gIGNvbG9yOiAjNjY2O1xuICBtYXgtd2lkdGg6IDQwMHB4O1xuICBtYXJnaW4tbGVmdDogYXV0bztcbiAgbWFyZ2luLXJpZ2h0OiBhdXRvO1xufVxuXG4vKiBNb2JpbGUgcmVzcG9uc2l2ZSAqL1xuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC53ZWxjb21lLXNlY3Rpb24ge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgZ2FwOiAxNnB4O1xuICAgIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgfVxuICBcbiAgLndlbGNvbWUtY29udGVudCBoMSB7XG4gICAgZm9udC1zaXplOiAxLjVyZW07XG4gIH1cbiAgXG4gIC5zdGF0cy1ncmlkLFxuICAuYWN0aW9ucy1ncmlkIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgfVxuICBcbiAgLmFjdGl2aXR5LWl0ZW0ge1xuICAgIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gICAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG4gICAgZ2FwOiA4cHg7XG4gIH1cbiAgXG4gIC5hY3Rpdml0eS1wb2ludHMge1xuICAgIGFsaWduLXNlbGY6IGZsZXgtZW5kO1xuICB9XG59XG4iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵstyleProp", "i_r2", "ɵɵadvance", "stat_r1", "color", "ɵɵtextInterpolate", "icon", "value", "title", "ɵɵproperty", "action_r3", "route", "ɵɵtextInterpolate1", "description", "ɵɵclassMap", "transaction_r4", "type", "ctx_r4", "getActivityIcon", "ɵɵpipeBind2", "timestamp", "points", "ɵɵtextInterpolate2", "ɵɵtemplate", "DashboardComponent_div_0_div_24_div_6_Template", "user", "history", "slice", "DashboardComponent_div_0_mat_card_18_Template", "DashboardComponent_div_0_mat_card_23_Template", "DashboardComponent_div_0_div_24_Template", "DashboardComponent_div_0_div_25_Template", "getGreeting", "name", "city", "getRoleDisplayName", "role", "stats", "quickActions", "length", "DashboardComponent", "constructor", "authService", "ngOnInit", "currentUser$", "subscribe", "updateStats", "toString", "validatedActions", "filter", "h", "rewardsObtained", "hour", "Date", "getHours", "roleNames", "iconMap", "ɵɵdirectiveInject", "i1", "AuthService", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { User } from '../../../../core/models';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit {\n  user: User | null = null;\n\n  quickActions = [\n    {\n      title: 'Scanner QR',\n      description: 'Scanner un code QR pour gagner des points',\n      icon: 'qr_code_scanner',\n      route: '/qr-scanner',\n      color: 'primary'\n    },\n    {\n      title: 'Récompenses',\n      description: 'Découvrir les récompenses disponibles',\n      icon: 'card_giftcard',\n      route: '/rewards',\n      color: 'accent'\n    },\n    {\n      title: 'Mon Profil',\n      description: 'Voir et modifier mon profil',\n      icon: 'person',\n      route: '/profile',\n      color: 'primary'\n    }\n  ];\n\n  stats = [\n    {\n      title: 'Points totaux',\n      value: '0',\n      icon: 'stars',\n      color: '#ffd700'\n    },\n    {\n      title: 'Actions validées',\n      value: '0',\n      icon: 'verified',\n      color: '#4caf50'\n    },\n    {\n      title: 'Récompenses obtenues',\n      value: '0',\n      icon: 'card_giftcard',\n      color: '#ff9800'\n    }\n  ];\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        this.updateStats(user);\n      }\n    });\n  }\n\n  private updateStats(user: User): void {\n    this.stats[0].value = user.points.toString();\n    \n    // Count validated actions from history\n    const validatedActions = user.history?.filter(h => h.type === 'validated').length || 0;\n    this.stats[1].value = validatedActions.toString();\n    \n    // Count spent points (rewards obtained)\n    const rewardsObtained = user.history?.filter(h => h.type === 'spent').length || 0;\n    this.stats[2].value = rewardsObtained.toString();\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n\n  getRoleDisplayName(role: string): string {\n    const roleNames: { [key: string]: string } = {\n      'user': 'Utilisateur',\n      'provider': 'Partenaire',\n      'validator': 'Validateur',\n      'admin': 'Administrateur'\n    };\n    return roleNames[role] || role;\n  }\n\n  getActivityIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'earned': 'add_circle',\n      'spent': 'remove_circle',\n      'transferred': 'swap_horiz',\n      'validated': 'verified',\n      'bonus': 'star'\n    };\n    return iconMap[type] || 'circle';\n  }\n}\n", "<div class=\"dashboard-container\" *ngIf=\"user\">\n  <!-- Welcome section -->\n  <div class=\"welcome-section slide-up\">\n    <div class=\"welcome-content\">\n      <h1 class=\"bounce-in\">{{ getGreeting() }}, {{ user.name }}!</h1>\n      <p class=\"user-info fade-in\">\n        <mat-icon>location_on</mat-icon>\n        {{ user.city }} • {{ getRoleDisplayName(user.role) }}\n      </p>\n    </div>\n    <div class=\"points-badge glow\">\n      <mat-icon>stars</mat-icon>\n      <span>{{ user.points }} points</span>\n    </div>\n  </div>\n\n  <!-- Stats cards -->\n  <div class=\"stats-section\">\n    <h2 class=\"section-title slide-in-left\">📊 Mes statistiques</h2>\n    <div class=\"stats-grid\">\n      <mat-card *ngFor=\"let stat of stats; let i = index\"\n                class=\"stat-card floating-card\"\n                [style.animation-delay]=\"(i * 0.1) + 's'\">\n        <mat-card-content>\n          <div class=\"stat-content\">\n            <div class=\"stat-icon\" [style.color]=\"stat.color\">\n              <mat-icon>{{ stat.icon }}</mat-icon>\n            </div>\n            <div class=\"stat-info\">\n              <h3 class=\"counter-animation\">{{ stat.value }}</h3>\n              <p>{{ stat.title }}</p>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Quick actions -->\n  <div class=\"actions-section\">\n    <h2>Actions rapides</h2>\n    <div class=\"actions-grid\">\n      <mat-card *ngFor=\"let action of quickActions\" \n                class=\"action-card\" \n                [routerLink]=\"action.route\">\n        <mat-card-content>\n          <div class=\"action-content\">\n            <mat-icon [color]=\"action.color\" class=\"action-icon\">\n              {{ action.icon }}\n            </mat-icon>\n            <h3>{{ action.title }}</h3>\n            <p>{{ action.description }}</p>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Recent activity -->\n  <div class=\"activity-section\" *ngIf=\"user.history && user.history.length > 0\">\n    <h2>Activité récente</h2>\n    <mat-card class=\"activity-card\">\n      <mat-card-content>\n        <div class=\"activity-list\">\n          <div *ngFor=\"let transaction of user.history.slice(-5)\" class=\"activity-item\">\n            <div class=\"activity-icon\">\n              <mat-icon [class]=\"'activity-' + transaction.type\">\n                {{ getActivityIcon(transaction.type) }}\n              </mat-icon>\n            </div>\n            <div class=\"activity-info\">\n              <p class=\"activity-description\">{{ transaction.description }}</p>\n              <p class=\"activity-date\">{{ transaction.timestamp | date:'short' }}</p>\n            </div>\n            <div class=\"activity-points\" [class]=\"transaction.points > 0 ? 'positive' : 'negative'\">\n              {{ transaction.points > 0 ? '+' : '' }}{{ transaction.points }} pts\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Empty state for new users -->\n  <div class=\"empty-state\" *ngIf=\"!user.history || user.history.length === 0\">\n    <mat-card class=\"empty-card\">\n      <mat-card-content>\n        <div class=\"empty-content\">\n          <mat-icon class=\"empty-icon\">explore</mat-icon>\n          <h3>Commencez votre aventure!</h3>\n          <p>Scannez votre premier code QR ou participez à une activité communautaire pour gagner vos premiers points.</p>\n          <button mat-raised-button color=\"primary\" routerLink=\"/qr-scanner\">\n            <mat-icon>qr_code_scanner</mat-icon>\n            Scanner un QR Code\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": ";;;;;;;;;IC0BcA,EANR,CAAAC,cAAA,mBAEoD,uBAChC,cACU,cAC0B,eACtC;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAChC;IAEJH,EADF,CAAAC,cAAA,cAAuB,aACS;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnDH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAI3BF,EAJ2B,CAAAG,YAAA,EAAI,EACnB,EACF,EACW,EACV;;;;;IAZDH,EAAA,CAAAI,WAAA,oBAAAC,IAAA,aAAyC;IAGtBL,EAAA,CAAAM,SAAA,GAA0B;IAA1BN,EAAA,CAAAI,WAAA,UAAAG,OAAA,CAAAC,KAAA,CAA0B;IACrCR,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAS,iBAAA,CAAAF,OAAA,CAAAG,IAAA,CAAe;IAGKV,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAS,iBAAA,CAAAF,OAAA,CAAAI,KAAA,CAAgB;IAC3CX,EAAA,CAAAM,SAAA,GAAgB;IAAhBN,EAAA,CAAAS,iBAAA,CAAAF,OAAA,CAAAK,KAAA,CAAgB;;;;;IAiBrBZ,EALN,CAAAC,cAAA,mBAEsC,uBAClB,cACY,mBAC2B;IACnDD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAGjCF,EAHiC,CAAAG,YAAA,EAAI,EAC3B,EACW,EACV;;;;IAVDH,EAAA,CAAAa,UAAA,eAAAC,SAAA,CAAAC,KAAA,CAA2B;IAGrBf,EAAA,CAAAM,SAAA,GAAsB;IAAtBN,EAAA,CAAAa,UAAA,UAAAC,SAAA,CAAAN,KAAA,CAAsB;IAC9BR,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAgB,kBAAA,MAAAF,SAAA,CAAAJ,IAAA,MACF;IACIV,EAAA,CAAAM,SAAA,GAAkB;IAAlBN,EAAA,CAAAS,iBAAA,CAAAK,SAAA,CAAAF,KAAA,CAAkB;IACnBZ,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAS,iBAAA,CAAAK,SAAA,CAAAG,WAAA,CAAwB;;;;;IAezBjB,EAFJ,CAAAC,cAAA,cAA8E,cACjD,eAC0B;IACjDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,cAA2B,YACO;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjEH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAE,MAAA,GAA0C;;IACrEF,EADqE,CAAAG,YAAA,EAAI,EACnE;IACNH,EAAA,CAAAC,cAAA,eAAwF;IACtFD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAXQH,EAAA,CAAAM,SAAA,GAAwC;IAAxCN,EAAA,CAAAkB,UAAA,eAAAC,cAAA,CAAAC,IAAA,CAAwC;IAChDpB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAAgB,kBAAA,MAAAK,MAAA,CAAAC,eAAA,CAAAH,cAAA,CAAAC,IAAA,OACF;IAGgCpB,EAAA,CAAAM,SAAA,GAA6B;IAA7BN,EAAA,CAAAS,iBAAA,CAAAU,cAAA,CAAAF,WAAA,CAA6B;IACpCjB,EAAA,CAAAM,SAAA,GAA0C;IAA1CN,EAAA,CAAAS,iBAAA,CAAAT,EAAA,CAAAuB,WAAA,OAAAJ,cAAA,CAAAK,SAAA,WAA0C;IAExCxB,EAAA,CAAAM,SAAA,GAA0D;IAA1DN,EAAA,CAAAkB,UAAA,CAAAC,cAAA,CAAAM,MAAA,+BAA0D;IACrFzB,EAAA,CAAAM,SAAA,EACF;IADEN,EAAA,CAAA0B,kBAAA,MAAAP,cAAA,CAAAM,MAAA,qBAAAN,cAAA,CAAAM,MAAA,UACF;;;;;IAhBRzB,EADF,CAAAC,cAAA,cAA8E,SACxE;IAAAD,EAAA,CAAAE,MAAA,iCAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGrBH,EAFJ,CAAAC,cAAA,mBAAgC,uBACZ,cACW;IACzBD,EAAA,CAAA2B,UAAA,IAAAC,8CAAA,oBAA8E;IAiBtF5B,EAHM,CAAAG,YAAA,EAAM,EACW,EACV,EACP;;;;IAjB+BH,EAAA,CAAAM,SAAA,GAAyB;IAAzBN,EAAA,CAAAa,UAAA,YAAAQ,MAAA,CAAAQ,IAAA,CAAAC,OAAA,CAAAC,KAAA,KAAyB;;;;;IAwBtD/B,EAJR,CAAAC,cAAA,cAA4E,mBAC7C,uBACT,cACW,mBACI;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/CH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,gCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,0HAAyG;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE9GH,EADF,CAAAC,cAAA,kBAAmE,gBACvD;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAE,MAAA,4BACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;;;IA9FFH,EAJN,CAAAC,cAAA,aAA8C,aAEN,aACP,YACL;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE9DH,EADF,CAAAC,cAAA,WAA6B,eACjB;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAI,EACA;IAEJH,EADF,CAAAC,cAAA,aAA+B,gBACnB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAElCF,EAFkC,CAAAG,YAAA,EAAO,EACjC,EACF;IAIJH,EADF,CAAAC,cAAA,cAA2B,aACe;IAAAD,EAAA,CAAAE,MAAA,qCAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChEH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAA2B,UAAA,KAAAK,6CAAA,wBAEoD;IAcxDhC,EADE,CAAAG,YAAA,EAAM,EACF;IAIJH,EADF,CAAAC,cAAA,eAA6B,UACvB;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAA2B,UAAA,KAAAM,6CAAA,uBAEsC;IAY1CjC,EADE,CAAAG,YAAA,EAAM,EACF;IA4BNH,EAzBA,CAAA2B,UAAA,KAAAO,wCAAA,kBAA8E,KAAAC,wCAAA,mBAyBF;IAe9EnC,EAAA,CAAAG,YAAA,EAAM;;;;IA/FsBH,EAAA,CAAAM,SAAA,GAAqC;IAArCN,EAAA,CAAA0B,kBAAA,KAAAL,MAAA,CAAAe,WAAA,UAAAf,MAAA,CAAAQ,IAAA,CAAAQ,IAAA,MAAqC;IAGzDrC,EAAA,CAAAM,SAAA,GACF;IADEN,EAAA,CAAA0B,kBAAA,MAAAL,MAAA,CAAAQ,IAAA,CAAAS,IAAA,cAAAjB,MAAA,CAAAkB,kBAAA,CAAAlB,MAAA,CAAAQ,IAAA,CAAAW,IAAA,OACF;IAIMxC,EAAA,CAAAM,SAAA,GAAwB;IAAxBN,EAAA,CAAAgB,kBAAA,KAAAK,MAAA,CAAAQ,IAAA,CAAAJ,MAAA,YAAwB;IAQHzB,EAAA,CAAAM,SAAA,GAAU;IAAVN,EAAA,CAAAa,UAAA,YAAAQ,MAAA,CAAAoB,KAAA,CAAU;IAsBRzC,EAAA,CAAAM,SAAA,GAAe;IAAfN,EAAA,CAAAa,UAAA,YAAAQ,MAAA,CAAAqB,YAAA,CAAe;IAiBjB1C,EAAA,CAAAM,SAAA,EAA6C;IAA7CN,EAAA,CAAAa,UAAA,SAAAQ,MAAA,CAAAQ,IAAA,CAAAC,OAAA,IAAAT,MAAA,CAAAQ,IAAA,CAAAC,OAAA,CAAAa,MAAA,KAA6C;IAyBlD3C,EAAA,CAAAM,SAAA,EAAgD;IAAhDN,EAAA,CAAAa,UAAA,UAAAQ,MAAA,CAAAQ,IAAA,CAAAC,OAAA,IAAAT,MAAA,CAAAQ,IAAA,CAAAC,OAAA,CAAAa,MAAA,OAAgD;;;AD3E5E,OAAM,MAAOC,kBAAkB;EAgD7BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IA/C/B,KAAAjB,IAAI,GAAgB,IAAI;IAExB,KAAAa,YAAY,GAAG,CACb;MACE9B,KAAK,EAAE,YAAY;MACnBK,WAAW,EAAE,2CAA2C;MACxDP,IAAI,EAAE,iBAAiB;MACvBK,KAAK,EAAE,aAAa;MACpBP,KAAK,EAAE;KACR,EACD;MACEI,KAAK,EAAE,aAAa;MACpBK,WAAW,EAAE,uCAAuC;MACpDP,IAAI,EAAE,eAAe;MACrBK,KAAK,EAAE,UAAU;MACjBP,KAAK,EAAE;KACR,EACD;MACEI,KAAK,EAAE,YAAY;MACnBK,WAAW,EAAE,6BAA6B;MAC1CP,IAAI,EAAE,QAAQ;MACdK,KAAK,EAAE,UAAU;MACjBP,KAAK,EAAE;KACR,CACF;IAED,KAAAiC,KAAK,GAAG,CACN;MACE7B,KAAK,EAAE,eAAe;MACtBD,KAAK,EAAE,GAAG;MACVD,IAAI,EAAE,OAAO;MACbF,KAAK,EAAE;KACR,EACD;MACEI,KAAK,EAAE,kBAAkB;MACzBD,KAAK,EAAE,GAAG;MACVD,IAAI,EAAE,UAAU;MAChBF,KAAK,EAAE;KACR,EACD;MACEI,KAAK,EAAE,sBAAsB;MAC7BD,KAAK,EAAE,GAAG;MACVD,IAAI,EAAE,eAAe;MACrBF,KAAK,EAAE;KACR,CACF;EAE8C;EAE/CuC,QAAQA,CAAA;IACN,IAAI,CAACD,WAAW,CAACE,YAAY,CAACC,SAAS,CAACpB,IAAI,IAAG;MAC7C,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACqB,WAAW,CAACrB,IAAI,CAAC;;IAE1B,CAAC,CAAC;EACJ;EAEQqB,WAAWA,CAACrB,IAAU;IAC5B,IAAI,CAACY,KAAK,CAAC,CAAC,CAAC,CAAC9B,KAAK,GAAGkB,IAAI,CAACJ,MAAM,CAAC0B,QAAQ,EAAE;IAE5C;IACA,MAAMC,gBAAgB,GAAGvB,IAAI,CAACC,OAAO,EAAEuB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClC,IAAI,KAAK,WAAW,CAAC,CAACuB,MAAM,IAAI,CAAC;IACtF,IAAI,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC9B,KAAK,GAAGyC,gBAAgB,CAACD,QAAQ,EAAE;IAEjD;IACA,MAAMI,eAAe,GAAG1B,IAAI,CAACC,OAAO,EAAEuB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAClC,IAAI,KAAK,OAAO,CAAC,CAACuB,MAAM,IAAI,CAAC;IACjF,IAAI,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC9B,KAAK,GAAG4C,eAAe,CAACJ,QAAQ,EAAE;EAClD;EAEAf,WAAWA,CAAA;IACT,MAAMoB,IAAI,GAAG,IAAIC,IAAI,EAAE,CAACC,QAAQ,EAAE;IAClC,IAAIF,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,SAAS;EAClB;EAEAjB,kBAAkBA,CAACC,IAAY;IAC7B,MAAMmB,SAAS,GAA8B;MAC3C,MAAM,EAAE,aAAa;MACrB,UAAU,EAAE,YAAY;MACxB,WAAW,EAAE,YAAY;MACzB,OAAO,EAAE;KACV;IACD,OAAOA,SAAS,CAACnB,IAAI,CAAC,IAAIA,IAAI;EAChC;EAEAlB,eAAeA,CAACF,IAAY;IAC1B,MAAMwC,OAAO,GAA8B;MACzC,QAAQ,EAAE,YAAY;MACtB,OAAO,EAAE,eAAe;MACxB,aAAa,EAAE,YAAY;MAC3B,WAAW,EAAE,UAAU;MACvB,OAAO,EAAE;KACV;IACD,OAAOA,OAAO,CAACxC,IAAI,CAAC,IAAI,QAAQ;EAClC;;;uBAjGWwB,kBAAkB,EAAA5C,EAAA,CAAA6D,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAlBnB,kBAAkB;MAAAoB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT/BtE,EAAA,CAAA2B,UAAA,IAAA6C,iCAAA,kBAA8C;;;UAAZxE,EAAA,CAAAa,UAAA,SAAA0D,GAAA,CAAA1C,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}