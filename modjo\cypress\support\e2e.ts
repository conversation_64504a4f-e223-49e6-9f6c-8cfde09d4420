// ***********************************************************
// This example support/e2e.ts is processed and
// loaded automatically before your test files.
//
// This is a great place to put global configuration and
// behavior that modifies Cypress.
//
// You can change the location of this file or turn off
// automatically serving support files with the
// 'supportFile' configuration option.
//
// You can read more here:
// https://on.cypress.io/configuration
// ***********************************************************

// Import commands.js using ES2015 syntax:
import './commands';

// Alternatively you can use CommonJS syntax:
// require('./commands')

// Add custom commands
declare global {
  namespace Cypress {
    interface Chainable {
      login(email: string, password: string): Chainable<void>;
      loginAsUser(): Chainable<void>;
      loginAsAdmin(): Chainable<void>;
      loginAsValidator(): Chainable<void>;
    }
  }
}

// Custom commands for authentication
Cypress.Commands.add('login', (email: string, password: string) => {
  cy.visit('/auth/login');
  cy.get('input[formControlName="email"]').type(email);
  cy.get('input[formControlName="password"]').type(password);
  cy.get('button[type="submit"]').click();
  cy.url().should('not.include', '/auth');
});

Cypress.Commands.add('loginAsUser', () => {
  cy.login('<EMAIL>', 'password123');
});

Cypress.Commands.add('loginAsAdmin', () => {
  cy.login('<EMAIL>', 'password123');
});

Cypress.Commands.add('loginAsValidator', () => {
  cy.login('<EMAIL>', 'password123');
});

// Handle uncaught exceptions
Cypress.on('uncaught:exception', (err, runnable) => {
  // returning false here prevents Cypress from failing the test
  if (err.message.includes('Firebase')) {
    return false;
  }
  return true;
});
