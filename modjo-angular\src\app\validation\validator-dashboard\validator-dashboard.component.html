<div class="dashboard-container">
  <h1>Tableau de bord validateur</h1>
  
  <div *ngIf="isLoading" class="loading">
    <p>Chargement des actions...</p>
  </div>

  <div *ngIf="error" class="error">
    <p>{{ error }}</p>
  </div>

  <div *ngIf="!isLoading && !error">
    <div class="success-message" *ngIf="successMessage">
      {{ successMessage }}
    </div>
    
    <div class="error-message" *ngIf="errorMessage">
      {{ errorMessage }}
    </div>
    
    <div class="dashboard-section">
      <h2>Créer une nouvelle action</h2>
      
      <div class="action-form">
        <div class="form-group">
          <label for="userId">ID Utilisateur</label>
          <input 
            type="text" 
            id="userId" 
            name="userId" 
            [(ngModel)]="newActionUserId" 
            required 
            placeholder="ID de l'utilisateur"
          >
        </div>
        
        <div class="form-group">
          <label for="description">Description</label>
          <input 
            type="text" 
            id="description" 
            name="description" 
            [(ngModel)]="newActionDescription" 
            required 
            placeholder="Description de l'action (ex: Devoir complété)"
          >
        </div>
        
        <div class="form-group">
          <label for="points">Points</label>
          <input 
            type="number" 
            id="points" 
            name="points" 
            [(ngModel)]="newActionPoints" 
            required 
            min="1"
          >
        </div>
        
        <button (click)="createAction()">Créer l'action</button>
      </div>
    </div>
    
    <div class="dashboard-section">
      <h2>Actions en attente de validation</h2>
      
      <div *ngIf="pendingActions.length === 0" class="no-data">
        <p>Aucune action en attente de validation</p>
      </div>
      
      <div *ngIf="pendingActions.length > 0" class="actions-list">
        <div *ngFor="let action of pendingActions" class="action-item">
          <div class="action-info">
            <p class="action-description">{{ action.description }}</p>
            <p class="action-details">
              <span>Utilisateur: {{ action.userId }}</span>
              <span>Points: {{ action.pointsAwarded }}</span>
              <span>Date: {{ action.createdAt | date:'dd/MM/yyyy' }}</span>
            </p>
          </div>
          <div class="action-buttons">
            <button class="approve-button" (click)="validateAction(action.id, true)">Valider</button>
            <button class="reject-button" (click)="validateAction(action.id, false)">Rejeter</button>
          </div>
        </div>
      </div>
    </div>
    
    <div class="dashboard-section">
      <h2>Actions validées récemment</h2>
      
      <div *ngIf="validatedActions.length === 0" class="no-data">
        <p>Aucune action validée récemment</p>
      </div>
      
      <div *ngIf="validatedActions.length > 0" class="actions-list">
        <div *ngFor="let action of validatedActions" class="action-item validated">
          <div class="action-info">
            <p class="action-description">{{ action.description }}</p>
            <p class="action-details">
              <span>Utilisateur: {{ action.userId }}</span>
              <span>Points: {{ action.pointsAwarded }}</span>
              <span>Date de validation: {{ action.validatedAt | date:'dd/MM/yyyy' }}</span>
            </p>
          </div>
          <div class="action-status" [ngClass]="{'approved': action.status === 'validated', 'rejected': action.status === 'rejected'}">
            {{ action.status === 'validated' ? 'Validée' : 'Rejetée' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
