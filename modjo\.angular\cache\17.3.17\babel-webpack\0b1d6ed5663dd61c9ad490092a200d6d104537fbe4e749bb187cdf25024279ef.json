{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, ANIMATION_MODULE_TYPE, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Inject, Optional, ViewChild, Input, Output, Directive, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport * as i1 from '@angular/cdk/a11y';\nimport { MatRipple, _MatInternalFormField, MatCommonModule } from '@angular/material/core';\n\n/** Injection token to be used to override the default options for `mat-slide-toggle`. */\nconst _c0 = [\"switch\"];\nconst _c1 = [\"*\"];\nfunction MatSlideToggle_Conditional_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(1, \"svg\", 12);\n    i0.ɵɵelement(2, \"path\", 13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"svg\", 14);\n    i0.ɵɵelement(4, \"path\", 15);\n    i0.ɵɵelementEnd()();\n  }\n}\nconst MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS = new InjectionToken('mat-slide-toggle-default-options', {\n  providedIn: 'root',\n  factory: () => ({\n    disableToggleValue: false,\n    hideIcon: false\n  })\n});\n\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nconst MAT_SLIDE_TOGGLE_VALUE_ACCESSOR = {\n  provide: NG_VALUE_ACCESSOR,\n  useExisting: forwardRef(() => MatSlideToggle),\n  multi: true\n};\n/** Change event object emitted by a slide toggle. */\nclass MatSlideToggleChange {\n  constructor(/** The source slide toggle of the event. */\n  source, /** The new `checked` value of the slide toggle. */\n  checked) {\n    this.source = source;\n    this.checked = checked;\n  }\n}\n// Increasing integer for generating unique ids for slide-toggle components.\nlet nextUniqueId = 0;\nclass MatSlideToggle {\n  _createChangeEvent(isChecked) {\n    return new MatSlideToggleChange(this, isChecked);\n  }\n  /** Returns the unique id for the visual hidden button. */\n  get buttonId() {\n    return `${this.id || this._uniqueId}-button`;\n  }\n  /** Focuses the slide-toggle. */\n  focus() {\n    this._switchElement.nativeElement.focus();\n  }\n  /** Whether the slide-toggle element is checked or not. */\n  get checked() {\n    return this._checked;\n  }\n  set checked(value) {\n    this._checked = value;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Returns the unique id for the visual hidden input. */\n  get inputId() {\n    return `${this.id || this._uniqueId}-input`;\n  }\n  constructor(_elementRef, _focusMonitor, _changeDetectorRef, tabIndex, defaults, animationMode) {\n    this._elementRef = _elementRef;\n    this._focusMonitor = _focusMonitor;\n    this._changeDetectorRef = _changeDetectorRef;\n    this.defaults = defaults;\n    this._onChange = _ => {};\n    this._onTouched = () => {};\n    this._validatorOnChange = () => {};\n    this._checked = false;\n    /** Name value will be applied to the input element if present. */\n    this.name = null;\n    /** Whether the label should appear after or before the slide-toggle. Defaults to 'after'. */\n    this.labelPosition = 'after';\n    /** Used to set the aria-label attribute on the underlying input element. */\n    this.ariaLabel = null;\n    /** Used to set the aria-labelledby attribute on the underlying input element. */\n    this.ariaLabelledby = null;\n    /** Whether the slide toggle is disabled. */\n    this.disabled = false;\n    /** Whether the slide toggle has a ripple. */\n    this.disableRipple = false;\n    /** Tabindex of slide toggle. */\n    this.tabIndex = 0;\n    /** An event will be dispatched each time the slide-toggle changes its value. */\n    this.change = new EventEmitter();\n    /**\n     * An event will be dispatched each time the slide-toggle input is toggled.\n     * This event is always emitted when the user toggles the slide toggle, but this does not mean\n     * the slide toggle's value has changed.\n     */\n    this.toggleChange = new EventEmitter();\n    this.tabIndex = parseInt(tabIndex) || 0;\n    this.color = defaults.color || 'accent';\n    this._noopAnimations = animationMode === 'NoopAnimations';\n    this.id = this._uniqueId = `mat-mdc-slide-toggle-${++nextUniqueId}`;\n    this.hideIcon = defaults.hideIcon ?? false;\n    this._labelId = this._uniqueId + '-label';\n  }\n  ngAfterContentInit() {\n    this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n      if (focusOrigin === 'keyboard' || focusOrigin === 'program') {\n        this._focused = true;\n        this._changeDetectorRef.markForCheck();\n      } else if (!focusOrigin) {\n        // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n        // Angular does not expect events to be raised during change detection, so any state\n        // change (such as a form control's ng-touched) will cause a changed-after-checked error.\n        // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n        // telling the form control it has been touched until the next tick.\n        Promise.resolve().then(() => {\n          this._focused = false;\n          this._onTouched();\n          this._changeDetectorRef.markForCheck();\n        });\n      }\n    });\n  }\n  ngOnChanges(changes) {\n    if (changes['required']) {\n      this._validatorOnChange();\n    }\n  }\n  ngOnDestroy() {\n    this._focusMonitor.stopMonitoring(this._elementRef);\n  }\n  /** Implemented as part of ControlValueAccessor. */\n  writeValue(value) {\n    this.checked = !!value;\n  }\n  /** Implemented as part of ControlValueAccessor. */\n  registerOnChange(fn) {\n    this._onChange = fn;\n  }\n  /** Implemented as part of ControlValueAccessor. */\n  registerOnTouched(fn) {\n    this._onTouched = fn;\n  }\n  /** Implemented as a part of Validator. */\n  validate(control) {\n    return this.required && control.value !== true ? {\n      'required': true\n    } : null;\n  }\n  /** Implemented as a part of Validator. */\n  registerOnValidatorChange(fn) {\n    this._validatorOnChange = fn;\n  }\n  /** Implemented as a part of ControlValueAccessor. */\n  setDisabledState(isDisabled) {\n    this.disabled = isDisabled;\n    this._changeDetectorRef.markForCheck();\n  }\n  /** Toggles the checked state of the slide-toggle. */\n  toggle() {\n    this.checked = !this.checked;\n    this._onChange(this.checked);\n  }\n  /**\n   * Emits a change event on the `change` output. Also notifies the FormControl about the change.\n   */\n  _emitChangeEvent() {\n    this._onChange(this.checked);\n    this.change.emit(this._createChangeEvent(this.checked));\n  }\n  /** Method being called whenever the underlying button is clicked. */\n  _handleClick() {\n    this.toggleChange.emit();\n    if (!this.defaults.disableToggleValue) {\n      this.checked = !this.checked;\n      this._onChange(this.checked);\n      this.change.emit(new MatSlideToggleChange(this, this.checked));\n    }\n  }\n  _getAriaLabelledBy() {\n    if (this.ariaLabelledby) {\n      return this.ariaLabelledby;\n    }\n    // Even though we have a `label` element with a `for` pointing to the button, we need the\n    // `aria-labelledby`, because the button gets flagged as not having a label by tools like axe.\n    return this.ariaLabel ? null : this._labelId;\n  }\n  static {\n    this.ɵfac = function MatSlideToggle_Factory(t) {\n      return new (t || MatSlideToggle)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusMonitor), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵinjectAttribute('tabindex'), i0.ɵɵdirectiveInject(MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS), i0.ɵɵdirectiveInject(ANIMATION_MODULE_TYPE, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSlideToggle,\n      selectors: [[\"mat-slide-toggle\"]],\n      viewQuery: function MatSlideToggle_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._switchElement = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mat-mdc-slide-toggle\"],\n      hostVars: 13,\n      hostBindings: function MatSlideToggle_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵhostProperty(\"id\", ctx.id);\n          i0.ɵɵattribute(\"tabindex\", null)(\"aria-label\", null)(\"name\", null)(\"aria-labelledby\", null);\n          i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n          i0.ɵɵclassProp(\"mat-mdc-slide-toggle-focused\", ctx._focused)(\"mat-mdc-slide-toggle-checked\", ctx.checked)(\"_mat-animation-noopable\", ctx._noopAnimations);\n        }\n      },\n      inputs: {\n        name: \"name\",\n        id: \"id\",\n        labelPosition: \"labelPosition\",\n        ariaLabel: [i0.ɵɵInputFlags.None, \"aria-label\", \"ariaLabel\"],\n        ariaLabelledby: [i0.ɵɵInputFlags.None, \"aria-labelledby\", \"ariaLabelledby\"],\n        ariaDescribedby: [i0.ɵɵInputFlags.None, \"aria-describedby\", \"ariaDescribedby\"],\n        required: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"required\", \"required\", booleanAttribute],\n        color: \"color\",\n        disabled: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disabled\", \"disabled\", booleanAttribute],\n        disableRipple: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"disableRipple\", \"disableRipple\", booleanAttribute],\n        tabIndex: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"tabIndex\", \"tabIndex\", value => value == null ? 0 : numberAttribute(value)],\n        checked: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"checked\", \"checked\", booleanAttribute],\n        hideIcon: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"hideIcon\", \"hideIcon\", booleanAttribute]\n      },\n      outputs: {\n        change: \"change\",\n        toggleChange: \"toggleChange\"\n      },\n      exportAs: [\"matSlideToggle\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MAT_SLIDE_TOGGLE_VALUE_ACCESSOR, {\n        provide: NG_VALIDATORS,\n        useExisting: MatSlideToggle,\n        multi: true\n      }]), i0.ɵɵInputTransformsFeature, i0.ɵɵNgOnChangesFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c1,\n      decls: 13,\n      vars: 24,\n      consts: [[\"switch\", \"\"], [\"mat-internal-form-field\", \"\", 3, \"labelPosition\"], [\"role\", \"switch\", \"type\", \"button\", 1, \"mdc-switch\", 3, \"click\", \"tabIndex\", \"disabled\"], [1, \"mdc-switch__track\"], [1, \"mdc-switch__handle-track\"], [1, \"mdc-switch__handle\"], [1, \"mdc-switch__shadow\"], [1, \"mdc-elevation-overlay\"], [1, \"mdc-switch__ripple\"], [\"mat-ripple\", \"\", 1, \"mat-mdc-slide-toggle-ripple\", \"mat-mdc-focus-indicator\", 3, \"matRippleTrigger\", \"matRippleDisabled\", \"matRippleCentered\"], [1, \"mdc-switch__icons\"], [1, \"mdc-label\", 3, \"click\", \"for\"], [\"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-switch__icon\", \"mdc-switch__icon--on\"], [\"d\", \"M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z\"], [\"viewBox\", \"0 0 24 24\", \"aria-hidden\", \"true\", 1, \"mdc-switch__icon\", \"mdc-switch__icon--off\"], [\"d\", \"M20 13H4v-2h16v2z\"]],\n      template: function MatSlideToggle_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"button\", 2, 0);\n          i0.ɵɵlistener(\"click\", function MatSlideToggle_Template_button_click_1_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx._handleClick());\n          });\n          i0.ɵɵelement(3, \"div\", 3);\n          i0.ɵɵelementStart(4, \"div\", 4)(5, \"div\", 5)(6, \"div\", 6);\n          i0.ɵɵelement(7, \"div\", 7);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"div\", 8);\n          i0.ɵɵelement(9, \"div\", 9);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(10, MatSlideToggle_Conditional_10_Template, 5, 0, \"div\", 10);\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(11, \"label\", 11);\n          i0.ɵɵlistener(\"click\", function MatSlideToggle_Template_label_click_11_listener($event) {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView($event.stopPropagation());\n          });\n          i0.ɵɵprojection(12);\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const switch_r2 = i0.ɵɵreference(2);\n          i0.ɵɵproperty(\"labelPosition\", ctx.labelPosition);\n          i0.ɵɵadvance();\n          i0.ɵɵclassProp(\"mdc-switch--selected\", ctx.checked)(\"mdc-switch--unselected\", !ctx.checked)(\"mdc-switch--checked\", ctx.checked)(\"mdc-switch--disabled\", ctx.disabled);\n          i0.ɵɵproperty(\"tabIndex\", ctx.disabled ? -1 : ctx.tabIndex)(\"disabled\", ctx.disabled);\n          i0.ɵɵattribute(\"id\", ctx.buttonId)(\"name\", ctx.name)(\"aria-label\", ctx.ariaLabel)(\"aria-labelledby\", ctx._getAriaLabelledBy())(\"aria-describedby\", ctx.ariaDescribedby)(\"aria-required\", ctx.required || null)(\"aria-checked\", ctx.checked);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"matRippleTrigger\", switch_r2)(\"matRippleDisabled\", ctx.disableRipple || ctx.disabled)(\"matRippleCentered\", true);\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(10, !ctx.hideIcon ? 10 : -1);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"for\", ctx.buttonId);\n          i0.ɵɵattribute(\"id\", ctx._labelId);\n        }\n      },\n      dependencies: [MatRipple, _MatInternalFormField],\n      styles: [\".mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);background-color:var(--mdc-elevation-overlay-color)}.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative}.mdc-switch[hidden]{display:none}.mdc-switch:disabled{cursor:default;pointer-events:none}.mdc-switch__track{overflow:hidden;position:relative;width:100%}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;width:100%}@media screen and (forced-colors: active){.mdc-switch__track::before,.mdc-switch__track::after{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(-100%)}[dir=rtl] .mdc-switch__track::after,.mdc-switch__track[dir=rtl]::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track[dir=rtl]::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0)}[dir=rtl] .mdc-switch__handle-track,.mdc-switch__handle-track[dir=rtl]{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track,.mdc-switch--selected .mdc-switch__handle-track[dir=rtl]{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto}[dir=rtl] .mdc-switch__handle,.mdc-switch__handle[dir=rtl]{left:auto;right:0}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media screen and (forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-elevation-overlay{bottom:0;left:0;right:0;top:0}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1}.mdc-switch:disabled .mdc-switch__ripple{display:none}.mdc-switch__icons{height:100%;position:relative;width:100%;z-index:1}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:var(--mdc-switch-disabled-label-text-color)}.mdc-switch{width:var(--mdc-switch-track-width)}.mdc-switch.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color)}.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color)}.mdc-switch.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color)}.mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color)}.mdc-switch.mdc-switch--selected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color)}.mdc-switch.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color)}.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color)}.mdc-switch.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color)}.mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color)}.mdc-switch.mdc-switch--unselected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color)}.mdc-switch .mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color)}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation)}.mdc-switch:disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation)}.mdc-switch .mdc-switch__focus-ring-wrapper,.mdc-switch .mdc-switch__handle{height:var(--mdc-switch-handle-height)}.mdc-switch .mdc-switch__handle{border-radius:var(--mdc-switch-handle-shape)}.mdc-switch .mdc-switch__handle{width:var(--mdc-switch-handle-width)}.mdc-switch .mdc-switch__handle-track{width:calc(100% - var(--mdc-switch-handle-width))}.mdc-switch.mdc-switch--selected:enabled .mdc-switch__icon{fill:var(--mdc-switch-selected-icon-color)}.mdc-switch.mdc-switch--selected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color)}.mdc-switch.mdc-switch--unselected:enabled .mdc-switch__icon{fill:var(--mdc-switch-unselected-icon-color)}.mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color)}.mdc-switch.mdc-switch--selected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity)}.mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity)}.mdc-switch.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size);height:var(--mdc-switch-selected-icon-size)}.mdc-switch.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size);height:var(--mdc-switch-unselected-icon-size)}.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-hover-state-layer-color)}.mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-focus-state-layer-color)}.mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-pressed-state-layer-color)}.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-hover-state-layer-color)}.mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-focus-state-layer-color)}.mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-pressed-state-layer-color)}.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-selected-hover-state-layer-opacity)}.mdc-switch.mdc-switch--selected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-selected-focus-state-layer-opacity)}.mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-selected-pressed-state-layer-opacity)}.mdc-switch.mdc-switch--selected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-selected-pressed-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-unselected-hover-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-unselected-focus-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity)}.mdc-switch .mdc-switch__ripple{height:var(--mdc-switch-state-layer-size);width:var(--mdc-switch-state-layer-size)}.mdc-switch .mdc-switch__track{height:var(--mdc-switch-track-height)}.mdc-switch:disabled .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity)}.mdc-switch:enabled .mdc-switch__track::after{background:var(--mdc-switch-selected-track-color)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color)}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color)}.mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color)}.mdc-switch:disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color)}.mdc-switch:enabled .mdc-switch__track::before{background:var(--mdc-switch-unselected-track-color)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color)}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color)}.mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color)}.mdc-switch:disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color)}.mdc-switch .mdc-switch__track{border-radius:var(--mdc-switch-track-shape)}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation-shadow)}.mdc-switch:disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation-shadow)}.mat-mdc-slide-toggle{display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle .mdc-switch__ripple::after{content:\\\"\\\";opacity:0}.mat-mdc-slide-toggle .mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:opacity 75ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-mdc-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-elevation-overlay,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}.mat-mdc-slide-toggle .mdc-switch:enabled+.mdc-label{cursor:pointer}.mdc-switch__handle{transition:width 75ms cubic-bezier(0.4, 0, 0.2, 1),height 75ms cubic-bezier(0.4, 0, 0.2, 1),margin 75ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-switch--selected .mdc-switch__track::before{opacity:var(--mat-switch-hidden-track-opacity);transition:var(--mat-switch-hidden-track-transition)}.mdc-switch--selected .mdc-switch__track::after{opacity:var(--mat-switch-visible-track-opacity);transition:var(--mat-switch-visible-track-transition)}.mdc-switch--unselected .mdc-switch__track::before{opacity:var(--mat-switch-visible-track-opacity);transition:var(--mat-switch-visible-track-transition)}.mdc-switch--unselected .mdc-switch__track::after{opacity:var(--mat-switch-hidden-track-opacity);transition:var(--mat-switch-hidden-track-transition)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{width:var(--mat-switch-unselected-handle-size);height:var(--mat-switch-unselected-handle-size)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{width:var(--mat-switch-selected-handle-size);height:var(--mat-switch-selected-handle-size)}.mat-mdc-slide-toggle .mdc-switch__handle:has(.mdc-switch__icons){width:var(--mat-switch-with-icon-handle-size);height:var(--mat-switch-with-icon-handle-size)}.mat-mdc-slide-toggle:active .mdc-switch:not(.mdc-switch--disabled) .mdc-switch__handle{width:var(--mat-switch-pressed-handle-size);height:var(--mat-switch-pressed-handle-size)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{margin:var(--mat-switch-selected-handle-horizontal-margin)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-selected-with-icon-handle-horizontal-margin)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{margin:var(--mat-switch-unselected-handle-horizontal-margin)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-unselected-with-icon-handle-horizontal-margin)}.mat-mdc-slide-toggle:active .mdc-switch--selected:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-selected-pressed-handle-horizontal-margin)}.mat-mdc-slide-toggle:active .mdc-switch--unselected:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-unselected-pressed-handle-horizontal-margin)}.mdc-switch__track::after,.mdc-switch__track::before{border-width:var(--mat-switch-track-outline-width);border-color:var(--mat-switch-track-outline-color)}.mdc-switch--selected .mdc-switch__track::after,.mdc-switch--selected .mdc-switch__track::before{border-width:var(--mat-switch-selected-track-outline-width)}.mdc-switch--disabled .mdc-switch__track::after,.mdc-switch--disabled .mdc-switch__track::before{border-width:var(--mat-switch-disabled-unselected-track-outline-width);border-color:var(--mat-switch-disabled-unselected-track-outline-color)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-selected-handle-opacity)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-unselected-handle-opacity)}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSlideToggle, [{\n    type: Component,\n    args: [{\n      selector: 'mat-slide-toggle',\n      host: {\n        'class': 'mat-mdc-slide-toggle',\n        '[id]': 'id',\n        // Needs to be removed since it causes some a11y issues (see #21266).\n        '[attr.tabindex]': 'null',\n        '[attr.aria-label]': 'null',\n        '[attr.name]': 'null',\n        '[attr.aria-labelledby]': 'null',\n        '[class.mat-mdc-slide-toggle-focused]': '_focused',\n        '[class.mat-mdc-slide-toggle-checked]': 'checked',\n        '[class._mat-animation-noopable]': '_noopAnimations',\n        '[class]': 'color ? \"mat-\" + color : \"\"'\n      },\n      exportAs: 'matSlideToggle',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      providers: [MAT_SLIDE_TOGGLE_VALUE_ACCESSOR, {\n        provide: NG_VALIDATORS,\n        useExisting: MatSlideToggle,\n        multi: true\n      }],\n      standalone: true,\n      imports: [MatRipple, _MatInternalFormField],\n      template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\">\\n  <button\\n    class=\\\"mdc-switch\\\"\\n    role=\\\"switch\\\"\\n    type=\\\"button\\\"\\n    [class.mdc-switch--selected]=\\\"checked\\\"\\n    [class.mdc-switch--unselected]=\\\"!checked\\\"\\n    [class.mdc-switch--checked]=\\\"checked\\\"\\n    [class.mdc-switch--disabled]=\\\"disabled\\\"\\n    [tabIndex]=\\\"disabled ? -1 : tabIndex\\\"\\n    [disabled]=\\\"disabled\\\"\\n    [attr.id]=\\\"buttonId\\\"\\n    [attr.name]=\\\"name\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-labelledby]=\\\"_getAriaLabelledBy()\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n    [attr.aria-required]=\\\"required || null\\\"\\n    [attr.aria-checked]=\\\"checked\\\"\\n    (click)=\\\"_handleClick()\\\"\\n    #switch>\\n    <div class=\\\"mdc-switch__track\\\"></div>\\n    <div class=\\\"mdc-switch__handle-track\\\">\\n      <div class=\\\"mdc-switch__handle\\\">\\n        <div class=\\\"mdc-switch__shadow\\\">\\n          <div class=\\\"mdc-elevation-overlay\\\"></div>\\n        </div>\\n        <div class=\\\"mdc-switch__ripple\\\">\\n          <div class=\\\"mat-mdc-slide-toggle-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n            [matRippleTrigger]=\\\"switch\\\"\\n            [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n            [matRippleCentered]=\\\"true\\\"></div>\\n        </div>\\n        @if (!hideIcon) {\\n          <div class=\\\"mdc-switch__icons\\\">\\n            <svg\\n              class=\\\"mdc-switch__icon mdc-switch__icon--on\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              aria-hidden=\\\"true\\\">\\n              <path d=\\\"M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z\\\" />\\n            </svg>\\n            <svg\\n              class=\\\"mdc-switch__icon mdc-switch__icon--off\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              aria-hidden=\\\"true\\\">\\n              <path d=\\\"M20 13H4v-2h16v2z\\\" />\\n            </svg>\\n          </div>\\n        }\\n      </div>\\n    </div>\\n  </button>\\n\\n  <!--\\n    Clicking on the label will trigger another click event from the button.\\n    Stop propagation here so other listeners further up in the DOM don't execute twice.\\n  -->\\n  <label class=\\\"mdc-label\\\" [for]=\\\"buttonId\\\" [attr.id]=\\\"_labelId\\\" (click)=\\\"$event.stopPropagation()\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\",\n      styles: [\".mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);background-color:var(--mdc-elevation-overlay-color)}.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative}.mdc-switch[hidden]{display:none}.mdc-switch:disabled{cursor:default;pointer-events:none}.mdc-switch__track{overflow:hidden;position:relative;width:100%}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;width:100%}@media screen and (forced-colors: active){.mdc-switch__track::before,.mdc-switch__track::after{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(-100%)}[dir=rtl] .mdc-switch__track::after,.mdc-switch__track[dir=rtl]::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track[dir=rtl]::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0)}[dir=rtl] .mdc-switch__handle-track,.mdc-switch__handle-track[dir=rtl]{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track,.mdc-switch--selected .mdc-switch__handle-track[dir=rtl]{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto}[dir=rtl] .mdc-switch__handle,.mdc-switch__handle[dir=rtl]{left:auto;right:0}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media screen and (forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-elevation-overlay{bottom:0;left:0;right:0;top:0}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1}.mdc-switch:disabled .mdc-switch__ripple{display:none}.mdc-switch__icons{height:100%;position:relative;width:100%;z-index:1}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:var(--mdc-switch-disabled-label-text-color)}.mdc-switch{width:var(--mdc-switch-track-width)}.mdc-switch.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color)}.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color)}.mdc-switch.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color)}.mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color)}.mdc-switch.mdc-switch--selected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color)}.mdc-switch.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color)}.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color)}.mdc-switch.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color)}.mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color)}.mdc-switch.mdc-switch--unselected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color)}.mdc-switch .mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color)}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation)}.mdc-switch:disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation)}.mdc-switch .mdc-switch__focus-ring-wrapper,.mdc-switch .mdc-switch__handle{height:var(--mdc-switch-handle-height)}.mdc-switch .mdc-switch__handle{border-radius:var(--mdc-switch-handle-shape)}.mdc-switch .mdc-switch__handle{width:var(--mdc-switch-handle-width)}.mdc-switch .mdc-switch__handle-track{width:calc(100% - var(--mdc-switch-handle-width))}.mdc-switch.mdc-switch--selected:enabled .mdc-switch__icon{fill:var(--mdc-switch-selected-icon-color)}.mdc-switch.mdc-switch--selected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color)}.mdc-switch.mdc-switch--unselected:enabled .mdc-switch__icon{fill:var(--mdc-switch-unselected-icon-color)}.mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color)}.mdc-switch.mdc-switch--selected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity)}.mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity)}.mdc-switch.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size);height:var(--mdc-switch-selected-icon-size)}.mdc-switch.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size);height:var(--mdc-switch-unselected-icon-size)}.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-hover-state-layer-color)}.mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-focus-state-layer-color)}.mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-pressed-state-layer-color)}.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-hover-state-layer-color)}.mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-focus-state-layer-color)}.mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-pressed-state-layer-color)}.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-selected-hover-state-layer-opacity)}.mdc-switch.mdc-switch--selected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-selected-focus-state-layer-opacity)}.mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-selected-pressed-state-layer-opacity)}.mdc-switch.mdc-switch--selected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-selected-pressed-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-unselected-hover-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-unselected-focus-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity)}.mdc-switch .mdc-switch__ripple{height:var(--mdc-switch-state-layer-size);width:var(--mdc-switch-state-layer-size)}.mdc-switch .mdc-switch__track{height:var(--mdc-switch-track-height)}.mdc-switch:disabled .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity)}.mdc-switch:enabled .mdc-switch__track::after{background:var(--mdc-switch-selected-track-color)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color)}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color)}.mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color)}.mdc-switch:disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color)}.mdc-switch:enabled .mdc-switch__track::before{background:var(--mdc-switch-unselected-track-color)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color)}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color)}.mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color)}.mdc-switch:disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color)}.mdc-switch .mdc-switch__track{border-radius:var(--mdc-switch-track-shape)}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation-shadow)}.mdc-switch:disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation-shadow)}.mat-mdc-slide-toggle{display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle .mdc-switch__ripple::after{content:\\\"\\\";opacity:0}.mat-mdc-slide-toggle .mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:opacity 75ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-mdc-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-elevation-overlay,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}.mat-mdc-slide-toggle .mdc-switch:enabled+.mdc-label{cursor:pointer}.mdc-switch__handle{transition:width 75ms cubic-bezier(0.4, 0, 0.2, 1),height 75ms cubic-bezier(0.4, 0, 0.2, 1),margin 75ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-switch--selected .mdc-switch__track::before{opacity:var(--mat-switch-hidden-track-opacity);transition:var(--mat-switch-hidden-track-transition)}.mdc-switch--selected .mdc-switch__track::after{opacity:var(--mat-switch-visible-track-opacity);transition:var(--mat-switch-visible-track-transition)}.mdc-switch--unselected .mdc-switch__track::before{opacity:var(--mat-switch-visible-track-opacity);transition:var(--mat-switch-visible-track-transition)}.mdc-switch--unselected .mdc-switch__track::after{opacity:var(--mat-switch-hidden-track-opacity);transition:var(--mat-switch-hidden-track-transition)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{width:var(--mat-switch-unselected-handle-size);height:var(--mat-switch-unselected-handle-size)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{width:var(--mat-switch-selected-handle-size);height:var(--mat-switch-selected-handle-size)}.mat-mdc-slide-toggle .mdc-switch__handle:has(.mdc-switch__icons){width:var(--mat-switch-with-icon-handle-size);height:var(--mat-switch-with-icon-handle-size)}.mat-mdc-slide-toggle:active .mdc-switch:not(.mdc-switch--disabled) .mdc-switch__handle{width:var(--mat-switch-pressed-handle-size);height:var(--mat-switch-pressed-handle-size)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{margin:var(--mat-switch-selected-handle-horizontal-margin)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-selected-with-icon-handle-horizontal-margin)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{margin:var(--mat-switch-unselected-handle-horizontal-margin)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-unselected-with-icon-handle-horizontal-margin)}.mat-mdc-slide-toggle:active .mdc-switch--selected:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-selected-pressed-handle-horizontal-margin)}.mat-mdc-slide-toggle:active .mdc-switch--unselected:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-unselected-pressed-handle-horizontal-margin)}.mdc-switch__track::after,.mdc-switch__track::before{border-width:var(--mat-switch-track-outline-width);border-color:var(--mat-switch-track-outline-color)}.mdc-switch--selected .mdc-switch__track::after,.mdc-switch--selected .mdc-switch__track::before{border-width:var(--mat-switch-selected-track-outline-width)}.mdc-switch--disabled .mdc-switch__track::after,.mdc-switch--disabled .mdc-switch__track::before{border-width:var(--mat-switch-disabled-unselected-track-outline-width);border-color:var(--mat-switch-disabled-unselected-track-outline-color)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-selected-handle-opacity)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-unselected-handle-opacity)}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1.FocusMonitor\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['tabindex']\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [ANIMATION_MODULE_TYPE]\n    }]\n  }], {\n    _switchElement: [{\n      type: ViewChild,\n      args: ['switch']\n    }],\n    name: [{\n      type: Input\n    }],\n    id: [{\n      type: Input\n    }],\n    labelPosition: [{\n      type: Input\n    }],\n    ariaLabel: [{\n      type: Input,\n      args: ['aria-label']\n    }],\n    ariaLabelledby: [{\n      type: Input,\n      args: ['aria-labelledby']\n    }],\n    ariaDescribedby: [{\n      type: Input,\n      args: ['aria-describedby']\n    }],\n    required: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    color: [{\n      type: Input\n    }],\n    disabled: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    disableRipple: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    tabIndex: [{\n      type: Input,\n      args: [{\n        transform: value => value == null ? 0 : numberAttribute(value)\n      }]\n    }],\n    checked: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    hideIcon: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    change: [{\n      type: Output\n    }],\n    toggleChange: [{\n      type: Output\n    }]\n  });\n})();\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nconst MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR = {\n  provide: NG_VALIDATORS,\n  useExisting: forwardRef(() => MatSlideToggleRequiredValidator),\n  multi: true\n};\n/**\n * Validator for Material slide-toggle components with the required attribute in a\n * template-driven form. The default validator for required form controls asserts\n * that the control value is not undefined but that is not appropriate for a slide-toggle\n * where the value is always defined.\n *\n * Required slide-toggle form controls are valid when checked.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass MatSlideToggleRequiredValidator extends CheckboxRequiredValidator {\n  static {\n    this.ɵfac = /* @__PURE__ */(() => {\n      let ɵMatSlideToggleRequiredValidator_BaseFactory;\n      return function MatSlideToggleRequiredValidator_Factory(t) {\n        return (ɵMatSlideToggleRequiredValidator_BaseFactory || (ɵMatSlideToggleRequiredValidator_BaseFactory = i0.ɵɵgetInheritedFactory(MatSlideToggleRequiredValidator)))(t || MatSlideToggleRequiredValidator);\n      };\n    })();\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSlideToggleRequiredValidator,\n      selectors: [[\"mat-slide-toggle\", \"required\", \"\", \"formControlName\", \"\"], [\"mat-slide-toggle\", \"required\", \"\", \"formControl\", \"\"], [\"mat-slide-toggle\", \"required\", \"\", \"ngModel\", \"\"]],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR]), i0.ɵɵInheritDefinitionFeature]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSlideToggleRequiredValidator, [{\n    type: Directive,\n    args: [{\n      selector: `mat-slide-toggle[required][formControlName],\n             mat-slide-toggle[required][formControl], mat-slide-toggle[required][ngModel]`,\n      providers: [MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR],\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * @deprecated No longer used, `MatSlideToggle` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass _MatSlideToggleRequiredValidatorModule {\n  static {\n    this.ɵfac = function _MatSlideToggleRequiredValidatorModule_Factory(t) {\n      return new (t || _MatSlideToggleRequiredValidatorModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: _MatSlideToggleRequiredValidatorModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({});\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(_MatSlideToggleRequiredValidatorModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatSlideToggleRequiredValidator],\n      exports: [MatSlideToggleRequiredValidator]\n    }]\n  }], null, null);\n})();\nclass MatSlideToggleModule {\n  static {\n    this.ɵfac = function MatSlideToggleModule_Factory(t) {\n      return new (t || MatSlideToggleModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSlideToggleModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatSlideToggle, MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSlideToggleModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatSlideToggle, MatCommonModule],\n      exports: [MatSlideToggle, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS, MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR, MAT_SLIDE_TOGGLE_VALUE_ACCESSOR, MatSlideToggle, MatSlideToggleChange, MatSlideToggleModule, MatSlideToggleRequiredValidator, _MatSlideToggleRequiredValidatorModule };", "map": {"version": 3, "names": ["i0", "InjectionToken", "forwardRef", "EventEmitter", "ANIMATION_MODULE_TYPE", "booleanAttribute", "numberAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Attribute", "Inject", "Optional", "ViewChild", "Input", "Output", "Directive", "NgModule", "NG_VALUE_ACCESSOR", "NG_VALIDATORS", "CheckboxRequiredValidator", "i1", "<PERSON><PERSON><PERSON><PERSON>", "_MatInternalFormField", "MatCommonModule", "_c0", "_c1", "MatSlideToggle_Conditional_10_Template", "rf", "ctx", "ɵɵelementStart", "ɵɵnamespaceSVG", "ɵɵelement", "ɵɵelementEnd", "MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS", "providedIn", "factory", "disable<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "hideIcon", "MAT_SLIDE_TOGGLE_VALUE_ACCESSOR", "provide", "useExisting", "MatSlideToggle", "multi", "MatSlideToggleChange", "constructor", "source", "checked", "nextUniqueId", "_createChangeEvent", "isChecked", "buttonId", "id", "_uniqueId", "focus", "_switchElement", "nativeElement", "_checked", "value", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "inputId", "_elementRef", "_focusMonitor", "tabIndex", "defaults", "animationMode", "_onChange", "_", "_onTouched", "_validatorOnChange", "name", "labelPosition", "aria<PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "disabled", "disable<PERSON><PERSON><PERSON>", "change", "toggle<PERSON><PERSON>e", "parseInt", "color", "_noopAnimations", "_labelId", "ngAfterContentInit", "monitor", "subscribe", "<PERSON><PERSON><PERSON><PERSON>", "_focused", "Promise", "resolve", "then", "ngOnChanges", "changes", "ngOnDestroy", "stopMonitoring", "writeValue", "registerOnChange", "fn", "registerOnTouched", "validate", "control", "required", "registerOnValidatorChange", "setDisabledState", "isDisabled", "toggle", "_emitChangeEvent", "emit", "_handleClick", "_getAriaLabelledBy", "ɵfac", "MatSlideToggle_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "FocusMonitor", "ChangeDetectorRef", "ɵɵinjectAttribute", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "MatSlideToggle_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "MatSlideToggle_HostBindings", "ɵɵhostProperty", "ɵɵattribute", "ɵɵclassMap", "ɵɵclassProp", "inputs", "ɵɵInputFlags", "None", "aria<PERSON><PERSON><PERSON><PERSON>", "HasDecoratorInputTransform", "outputs", "exportAs", "standalone", "features", "ɵɵProvidersFeature", "ɵɵInputTransformsFeature", "ɵɵNgOnChangesFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatSlideToggle_Template", "_r1", "ɵɵgetCurrentView", "ɵɵprojectionDef", "ɵɵlistener", "MatSlideToggle_Template_button_click_1_listener", "ɵɵrestoreView", "ɵɵresetView", "ɵɵtemplate", "MatSlideToggle_Template_label_click_11_listener", "$event", "stopPropagation", "ɵɵprojection", "switch_r2", "ɵɵreference", "ɵɵproperty", "ɵɵadvance", "ɵɵconditional", "dependencies", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "OnPush", "providers", "imports", "undefined", "decorators", "transform", "MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR", "MatSlideToggleRequiredValidator", "ɵMatSlideToggleRequiredValidator_BaseFactory", "MatSlideToggleRequiredValidator_Factory", "ɵɵgetInheritedFactory", "ɵdir", "ɵɵdefineDirective", "ɵɵInheritDefinitionFeature", "_MatSlideToggleRequiredValidatorModule", "_MatSlideToggleRequiredValidatorModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "MatSlideToggleModule", "MatSlideToggleModule_Factory"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/material/fesm2022/slide-toggle.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, forwardRef, EventEmitter, ANIMATION_MODULE_TYPE, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Inject, Optional, ViewChild, Input, Output, Directive, NgModule } from '@angular/core';\nimport { NG_VALUE_ACCESSOR, NG_VALIDATORS, CheckboxRequiredValidator } from '@angular/forms';\nimport * as i1 from '@angular/cdk/a11y';\nimport { MatRipple, _MatInternalFormField, MatCommonModule } from '@angular/material/core';\n\n/** Injection token to be used to override the default options for `mat-slide-toggle`. */\nconst MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS = new InjectionToken('mat-slide-toggle-default-options', {\n    providedIn: 'root',\n    factory: () => ({ disableToggleValue: false, hideIcon: false }),\n});\n\n/**\n * @deprecated Will stop being exported.\n * @breaking-change 19.0.0\n */\nconst MAT_SLIDE_TOGGLE_VALUE_ACCESSOR = {\n    provide: NG_VALUE_ACCESSOR,\n    useExisting: forwardRef(() => MatSlideToggle),\n    multi: true,\n};\n/** Change event object emitted by a slide toggle. */\nclass MatSlideToggleChange {\n    constructor(\n    /** The source slide toggle of the event. */\n    source, \n    /** The new `checked` value of the slide toggle. */\n    checked) {\n        this.source = source;\n        this.checked = checked;\n    }\n}\n// Increasing integer for generating unique ids for slide-toggle components.\nlet nextUniqueId = 0;\nclass MatSlideToggle {\n    _createChangeEvent(isChecked) {\n        return new MatSlideToggleChange(this, isChecked);\n    }\n    /** Returns the unique id for the visual hidden button. */\n    get buttonId() {\n        return `${this.id || this._uniqueId}-button`;\n    }\n    /** Focuses the slide-toggle. */\n    focus() {\n        this._switchElement.nativeElement.focus();\n    }\n    /** Whether the slide-toggle element is checked or not. */\n    get checked() {\n        return this._checked;\n    }\n    set checked(value) {\n        this._checked = value;\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Returns the unique id for the visual hidden input. */\n    get inputId() {\n        return `${this.id || this._uniqueId}-input`;\n    }\n    constructor(_elementRef, _focusMonitor, _changeDetectorRef, tabIndex, defaults, animationMode) {\n        this._elementRef = _elementRef;\n        this._focusMonitor = _focusMonitor;\n        this._changeDetectorRef = _changeDetectorRef;\n        this.defaults = defaults;\n        this._onChange = (_) => { };\n        this._onTouched = () => { };\n        this._validatorOnChange = () => { };\n        this._checked = false;\n        /** Name value will be applied to the input element if present. */\n        this.name = null;\n        /** Whether the label should appear after or before the slide-toggle. Defaults to 'after'. */\n        this.labelPosition = 'after';\n        /** Used to set the aria-label attribute on the underlying input element. */\n        this.ariaLabel = null;\n        /** Used to set the aria-labelledby attribute on the underlying input element. */\n        this.ariaLabelledby = null;\n        /** Whether the slide toggle is disabled. */\n        this.disabled = false;\n        /** Whether the slide toggle has a ripple. */\n        this.disableRipple = false;\n        /** Tabindex of slide toggle. */\n        this.tabIndex = 0;\n        /** An event will be dispatched each time the slide-toggle changes its value. */\n        this.change = new EventEmitter();\n        /**\n         * An event will be dispatched each time the slide-toggle input is toggled.\n         * This event is always emitted when the user toggles the slide toggle, but this does not mean\n         * the slide toggle's value has changed.\n         */\n        this.toggleChange = new EventEmitter();\n        this.tabIndex = parseInt(tabIndex) || 0;\n        this.color = defaults.color || 'accent';\n        this._noopAnimations = animationMode === 'NoopAnimations';\n        this.id = this._uniqueId = `mat-mdc-slide-toggle-${++nextUniqueId}`;\n        this.hideIcon = defaults.hideIcon ?? false;\n        this._labelId = this._uniqueId + '-label';\n    }\n    ngAfterContentInit() {\n        this._focusMonitor.monitor(this._elementRef, true).subscribe(focusOrigin => {\n            if (focusOrigin === 'keyboard' || focusOrigin === 'program') {\n                this._focused = true;\n                this._changeDetectorRef.markForCheck();\n            }\n            else if (!focusOrigin) {\n                // When a focused element becomes disabled, the browser *immediately* fires a blur event.\n                // Angular does not expect events to be raised during change detection, so any state\n                // change (such as a form control's ng-touched) will cause a changed-after-checked error.\n                // See https://github.com/angular/angular/issues/17793. To work around this, we defer\n                // telling the form control it has been touched until the next tick.\n                Promise.resolve().then(() => {\n                    this._focused = false;\n                    this._onTouched();\n                    this._changeDetectorRef.markForCheck();\n                });\n            }\n        });\n    }\n    ngOnChanges(changes) {\n        if (changes['required']) {\n            this._validatorOnChange();\n        }\n    }\n    ngOnDestroy() {\n        this._focusMonitor.stopMonitoring(this._elementRef);\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    writeValue(value) {\n        this.checked = !!value;\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /** Implemented as part of ControlValueAccessor. */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /** Implemented as a part of Validator. */\n    validate(control) {\n        return this.required && control.value !== true ? { 'required': true } : null;\n    }\n    /** Implemented as a part of Validator. */\n    registerOnValidatorChange(fn) {\n        this._validatorOnChange = fn;\n    }\n    /** Implemented as a part of ControlValueAccessor. */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._changeDetectorRef.markForCheck();\n    }\n    /** Toggles the checked state of the slide-toggle. */\n    toggle() {\n        this.checked = !this.checked;\n        this._onChange(this.checked);\n    }\n    /**\n     * Emits a change event on the `change` output. Also notifies the FormControl about the change.\n     */\n    _emitChangeEvent() {\n        this._onChange(this.checked);\n        this.change.emit(this._createChangeEvent(this.checked));\n    }\n    /** Method being called whenever the underlying button is clicked. */\n    _handleClick() {\n        this.toggleChange.emit();\n        if (!this.defaults.disableToggleValue) {\n            this.checked = !this.checked;\n            this._onChange(this.checked);\n            this.change.emit(new MatSlideToggleChange(this, this.checked));\n        }\n    }\n    _getAriaLabelledBy() {\n        if (this.ariaLabelledby) {\n            return this.ariaLabelledby;\n        }\n        // Even though we have a `label` element with a `for` pointing to the button, we need the\n        // `aria-labelledby`, because the button gets flagged as not having a label by tools like axe.\n        return this.ariaLabel ? null : this._labelId;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSlideToggle, deps: [{ token: i0.ElementRef }, { token: i1.FocusMonitor }, { token: i0.ChangeDetectorRef }, { token: 'tabindex', attribute: true }, { token: MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatSlideToggle, isStandalone: true, selector: \"mat-slide-toggle\", inputs: { name: \"name\", id: \"id\", labelPosition: \"labelPosition\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], ariaDescribedby: [\"aria-describedby\", \"ariaDescribedby\"], required: [\"required\", \"required\", booleanAttribute], color: \"color\", disabled: [\"disabled\", \"disabled\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))], checked: [\"checked\", \"checked\", booleanAttribute], hideIcon: [\"hideIcon\", \"hideIcon\", booleanAttribute] }, outputs: { change: \"change\", toggleChange: \"toggleChange\" }, host: { properties: { \"id\": \"id\", \"attr.tabindex\": \"null\", \"attr.aria-label\": \"null\", \"attr.name\": \"null\", \"attr.aria-labelledby\": \"null\", \"class.mat-mdc-slide-toggle-focused\": \"_focused\", \"class.mat-mdc-slide-toggle-checked\": \"checked\", \"class._mat-animation-noopable\": \"_noopAnimations\", \"class\": \"color ? \\\"mat-\\\" + color : \\\"\\\"\" }, classAttribute: \"mat-mdc-slide-toggle\" }, providers: [\n            MAT_SLIDE_TOGGLE_VALUE_ACCESSOR,\n            {\n                provide: NG_VALIDATORS,\n                useExisting: MatSlideToggle,\n                multi: true,\n            },\n        ], viewQueries: [{ propertyName: \"_switchElement\", first: true, predicate: [\"switch\"], descendants: true }], exportAs: [\"matSlideToggle\"], usesOnChanges: true, ngImport: i0, template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\">\\n  <button\\n    class=\\\"mdc-switch\\\"\\n    role=\\\"switch\\\"\\n    type=\\\"button\\\"\\n    [class.mdc-switch--selected]=\\\"checked\\\"\\n    [class.mdc-switch--unselected]=\\\"!checked\\\"\\n    [class.mdc-switch--checked]=\\\"checked\\\"\\n    [class.mdc-switch--disabled]=\\\"disabled\\\"\\n    [tabIndex]=\\\"disabled ? -1 : tabIndex\\\"\\n    [disabled]=\\\"disabled\\\"\\n    [attr.id]=\\\"buttonId\\\"\\n    [attr.name]=\\\"name\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-labelledby]=\\\"_getAriaLabelledBy()\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n    [attr.aria-required]=\\\"required || null\\\"\\n    [attr.aria-checked]=\\\"checked\\\"\\n    (click)=\\\"_handleClick()\\\"\\n    #switch>\\n    <div class=\\\"mdc-switch__track\\\"></div>\\n    <div class=\\\"mdc-switch__handle-track\\\">\\n      <div class=\\\"mdc-switch__handle\\\">\\n        <div class=\\\"mdc-switch__shadow\\\">\\n          <div class=\\\"mdc-elevation-overlay\\\"></div>\\n        </div>\\n        <div class=\\\"mdc-switch__ripple\\\">\\n          <div class=\\\"mat-mdc-slide-toggle-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n            [matRippleTrigger]=\\\"switch\\\"\\n            [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n            [matRippleCentered]=\\\"true\\\"></div>\\n        </div>\\n        @if (!hideIcon) {\\n          <div class=\\\"mdc-switch__icons\\\">\\n            <svg\\n              class=\\\"mdc-switch__icon mdc-switch__icon--on\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              aria-hidden=\\\"true\\\">\\n              <path d=\\\"M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z\\\" />\\n            </svg>\\n            <svg\\n              class=\\\"mdc-switch__icon mdc-switch__icon--off\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              aria-hidden=\\\"true\\\">\\n              <path d=\\\"M20 13H4v-2h16v2z\\\" />\\n            </svg>\\n          </div>\\n        }\\n      </div>\\n    </div>\\n  </button>\\n\\n  <!--\\n    Clicking on the label will trigger another click event from the button.\\n    Stop propagation here so other listeners further up in the DOM don't execute twice.\\n  -->\\n  <label class=\\\"mdc-label\\\" [for]=\\\"buttonId\\\" [attr.id]=\\\"_labelId\\\" (click)=\\\"$event.stopPropagation()\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);background-color:var(--mdc-elevation-overlay-color)}.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative}.mdc-switch[hidden]{display:none}.mdc-switch:disabled{cursor:default;pointer-events:none}.mdc-switch__track{overflow:hidden;position:relative;width:100%}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;width:100%}@media screen and (forced-colors: active){.mdc-switch__track::before,.mdc-switch__track::after{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(-100%)}[dir=rtl] .mdc-switch__track::after,.mdc-switch__track[dir=rtl]::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track[dir=rtl]::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0)}[dir=rtl] .mdc-switch__handle-track,.mdc-switch__handle-track[dir=rtl]{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track,.mdc-switch--selected .mdc-switch__handle-track[dir=rtl]{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto}[dir=rtl] .mdc-switch__handle,.mdc-switch__handle[dir=rtl]{left:auto;right:0}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media screen and (forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-elevation-overlay{bottom:0;left:0;right:0;top:0}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1}.mdc-switch:disabled .mdc-switch__ripple{display:none}.mdc-switch__icons{height:100%;position:relative;width:100%;z-index:1}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:var(--mdc-switch-disabled-label-text-color)}.mdc-switch{width:var(--mdc-switch-track-width)}.mdc-switch.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color)}.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color)}.mdc-switch.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color)}.mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color)}.mdc-switch.mdc-switch--selected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color)}.mdc-switch.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color)}.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color)}.mdc-switch.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color)}.mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color)}.mdc-switch.mdc-switch--unselected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color)}.mdc-switch .mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color)}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation)}.mdc-switch:disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation)}.mdc-switch .mdc-switch__focus-ring-wrapper,.mdc-switch .mdc-switch__handle{height:var(--mdc-switch-handle-height)}.mdc-switch .mdc-switch__handle{border-radius:var(--mdc-switch-handle-shape)}.mdc-switch .mdc-switch__handle{width:var(--mdc-switch-handle-width)}.mdc-switch .mdc-switch__handle-track{width:calc(100% - var(--mdc-switch-handle-width))}.mdc-switch.mdc-switch--selected:enabled .mdc-switch__icon{fill:var(--mdc-switch-selected-icon-color)}.mdc-switch.mdc-switch--selected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color)}.mdc-switch.mdc-switch--unselected:enabled .mdc-switch__icon{fill:var(--mdc-switch-unselected-icon-color)}.mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color)}.mdc-switch.mdc-switch--selected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity)}.mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity)}.mdc-switch.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size);height:var(--mdc-switch-selected-icon-size)}.mdc-switch.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size);height:var(--mdc-switch-unselected-icon-size)}.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-hover-state-layer-color)}.mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-focus-state-layer-color)}.mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-pressed-state-layer-color)}.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-hover-state-layer-color)}.mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-focus-state-layer-color)}.mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-pressed-state-layer-color)}.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-selected-hover-state-layer-opacity)}.mdc-switch.mdc-switch--selected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-selected-focus-state-layer-opacity)}.mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-selected-pressed-state-layer-opacity)}.mdc-switch.mdc-switch--selected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-selected-pressed-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-unselected-hover-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-unselected-focus-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity)}.mdc-switch .mdc-switch__ripple{height:var(--mdc-switch-state-layer-size);width:var(--mdc-switch-state-layer-size)}.mdc-switch .mdc-switch__track{height:var(--mdc-switch-track-height)}.mdc-switch:disabled .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity)}.mdc-switch:enabled .mdc-switch__track::after{background:var(--mdc-switch-selected-track-color)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color)}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color)}.mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color)}.mdc-switch:disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color)}.mdc-switch:enabled .mdc-switch__track::before{background:var(--mdc-switch-unselected-track-color)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color)}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color)}.mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color)}.mdc-switch:disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color)}.mdc-switch .mdc-switch__track{border-radius:var(--mdc-switch-track-shape)}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation-shadow)}.mdc-switch:disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation-shadow)}.mat-mdc-slide-toggle{display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle .mdc-switch__ripple::after{content:\\\"\\\";opacity:0}.mat-mdc-slide-toggle .mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:opacity 75ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-mdc-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-elevation-overlay,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}.mat-mdc-slide-toggle .mdc-switch:enabled+.mdc-label{cursor:pointer}.mdc-switch__handle{transition:width 75ms cubic-bezier(0.4, 0, 0.2, 1),height 75ms cubic-bezier(0.4, 0, 0.2, 1),margin 75ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-switch--selected .mdc-switch__track::before{opacity:var(--mat-switch-hidden-track-opacity);transition:var(--mat-switch-hidden-track-transition)}.mdc-switch--selected .mdc-switch__track::after{opacity:var(--mat-switch-visible-track-opacity);transition:var(--mat-switch-visible-track-transition)}.mdc-switch--unselected .mdc-switch__track::before{opacity:var(--mat-switch-visible-track-opacity);transition:var(--mat-switch-visible-track-transition)}.mdc-switch--unselected .mdc-switch__track::after{opacity:var(--mat-switch-hidden-track-opacity);transition:var(--mat-switch-hidden-track-transition)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{width:var(--mat-switch-unselected-handle-size);height:var(--mat-switch-unselected-handle-size)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{width:var(--mat-switch-selected-handle-size);height:var(--mat-switch-selected-handle-size)}.mat-mdc-slide-toggle .mdc-switch__handle:has(.mdc-switch__icons){width:var(--mat-switch-with-icon-handle-size);height:var(--mat-switch-with-icon-handle-size)}.mat-mdc-slide-toggle:active .mdc-switch:not(.mdc-switch--disabled) .mdc-switch__handle{width:var(--mat-switch-pressed-handle-size);height:var(--mat-switch-pressed-handle-size)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{margin:var(--mat-switch-selected-handle-horizontal-margin)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-selected-with-icon-handle-horizontal-margin)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{margin:var(--mat-switch-unselected-handle-horizontal-margin)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-unselected-with-icon-handle-horizontal-margin)}.mat-mdc-slide-toggle:active .mdc-switch--selected:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-selected-pressed-handle-horizontal-margin)}.mat-mdc-slide-toggle:active .mdc-switch--unselected:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-unselected-pressed-handle-horizontal-margin)}.mdc-switch__track::after,.mdc-switch__track::before{border-width:var(--mat-switch-track-outline-width);border-color:var(--mat-switch-track-outline-color)}.mdc-switch--selected .mdc-switch__track::after,.mdc-switch--selected .mdc-switch__track::before{border-width:var(--mat-switch-selected-track-outline-width)}.mdc-switch--disabled .mdc-switch__track::after,.mdc-switch--disabled .mdc-switch__track::before{border-width:var(--mat-switch-disabled-unselected-track-outline-width);border-color:var(--mat-switch-disabled-unselected-track-outline-color)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-selected-handle-opacity)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-unselected-handle-opacity)}\"], dependencies: [{ kind: \"directive\", type: MatRipple, selector: \"[mat-ripple], [matRipple]\", inputs: [\"matRippleColor\", \"matRippleUnbounded\", \"matRippleCentered\", \"matRippleRadius\", \"matRippleAnimation\", \"matRippleDisabled\", \"matRippleTrigger\"], exportAs: [\"matRipple\"] }, { kind: \"component\", type: _MatInternalFormField, selector: \"div[mat-internal-form-field]\", inputs: [\"labelPosition\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSlideToggle, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-slide-toggle', host: {\n                        'class': 'mat-mdc-slide-toggle',\n                        '[id]': 'id',\n                        // Needs to be removed since it causes some a11y issues (see #21266).\n                        '[attr.tabindex]': 'null',\n                        '[attr.aria-label]': 'null',\n                        '[attr.name]': 'null',\n                        '[attr.aria-labelledby]': 'null',\n                        '[class.mat-mdc-slide-toggle-focused]': '_focused',\n                        '[class.mat-mdc-slide-toggle-checked]': 'checked',\n                        '[class._mat-animation-noopable]': '_noopAnimations',\n                        '[class]': 'color ? \"mat-\" + color : \"\"',\n                    }, exportAs: 'matSlideToggle', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        MAT_SLIDE_TOGGLE_VALUE_ACCESSOR,\n                        {\n                            provide: NG_VALIDATORS,\n                            useExisting: MatSlideToggle,\n                            multi: true,\n                        },\n                    ], standalone: true, imports: [MatRipple, _MatInternalFormField], template: \"<div mat-internal-form-field [labelPosition]=\\\"labelPosition\\\">\\n  <button\\n    class=\\\"mdc-switch\\\"\\n    role=\\\"switch\\\"\\n    type=\\\"button\\\"\\n    [class.mdc-switch--selected]=\\\"checked\\\"\\n    [class.mdc-switch--unselected]=\\\"!checked\\\"\\n    [class.mdc-switch--checked]=\\\"checked\\\"\\n    [class.mdc-switch--disabled]=\\\"disabled\\\"\\n    [tabIndex]=\\\"disabled ? -1 : tabIndex\\\"\\n    [disabled]=\\\"disabled\\\"\\n    [attr.id]=\\\"buttonId\\\"\\n    [attr.name]=\\\"name\\\"\\n    [attr.aria-label]=\\\"ariaLabel\\\"\\n    [attr.aria-labelledby]=\\\"_getAriaLabelledBy()\\\"\\n    [attr.aria-describedby]=\\\"ariaDescribedby\\\"\\n    [attr.aria-required]=\\\"required || null\\\"\\n    [attr.aria-checked]=\\\"checked\\\"\\n    (click)=\\\"_handleClick()\\\"\\n    #switch>\\n    <div class=\\\"mdc-switch__track\\\"></div>\\n    <div class=\\\"mdc-switch__handle-track\\\">\\n      <div class=\\\"mdc-switch__handle\\\">\\n        <div class=\\\"mdc-switch__shadow\\\">\\n          <div class=\\\"mdc-elevation-overlay\\\"></div>\\n        </div>\\n        <div class=\\\"mdc-switch__ripple\\\">\\n          <div class=\\\"mat-mdc-slide-toggle-ripple mat-mdc-focus-indicator\\\" mat-ripple\\n            [matRippleTrigger]=\\\"switch\\\"\\n            [matRippleDisabled]=\\\"disableRipple || disabled\\\"\\n            [matRippleCentered]=\\\"true\\\"></div>\\n        </div>\\n        @if (!hideIcon) {\\n          <div class=\\\"mdc-switch__icons\\\">\\n            <svg\\n              class=\\\"mdc-switch__icon mdc-switch__icon--on\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              aria-hidden=\\\"true\\\">\\n              <path d=\\\"M19.69,5.23L8.96,15.96l-4.23-4.23L2.96,13.5l6,6L21.46,7L19.69,5.23z\\\" />\\n            </svg>\\n            <svg\\n              class=\\\"mdc-switch__icon mdc-switch__icon--off\\\"\\n              viewBox=\\\"0 0 24 24\\\"\\n              aria-hidden=\\\"true\\\">\\n              <path d=\\\"M20 13H4v-2h16v2z\\\" />\\n            </svg>\\n          </div>\\n        }\\n      </div>\\n    </div>\\n  </button>\\n\\n  <!--\\n    Clicking on the label will trigger another click event from the button.\\n    Stop propagation here so other listeners further up in the DOM don't execute twice.\\n  -->\\n  <label class=\\\"mdc-label\\\" [for]=\\\"buttonId\\\" [attr.id]=\\\"_labelId\\\" (click)=\\\"$event.stopPropagation()\\\">\\n    <ng-content></ng-content>\\n  </label>\\n</div>\\n\", styles: [\".mdc-elevation-overlay{position:absolute;border-radius:inherit;pointer-events:none;opacity:var(--mdc-elevation-overlay-opacity);transition:opacity 280ms cubic-bezier(0.4, 0, 0.2, 1);background-color:var(--mdc-elevation-overlay-color)}.mdc-switch{align-items:center;background:none;border:none;cursor:pointer;display:inline-flex;flex-shrink:0;margin:0;outline:none;overflow:visible;padding:0;position:relative}.mdc-switch[hidden]{display:none}.mdc-switch:disabled{cursor:default;pointer-events:none}.mdc-switch__track{overflow:hidden;position:relative;width:100%}.mdc-switch__track::before,.mdc-switch__track::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";height:100%;left:0;position:absolute;width:100%}@media screen and (forced-colors: active){.mdc-switch__track::before,.mdc-switch__track::after{border-color:currentColor}}.mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(-100%)}[dir=rtl] .mdc-switch__track::after,.mdc-switch__track[dir=rtl]::after{transform:translateX(100%)}.mdc-switch--selected .mdc-switch__track::before{transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.6, 1);transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__track::before,.mdc-switch--selected .mdc-switch__track[dir=rtl]::before{transform:translateX(-100%)}.mdc-switch--selected .mdc-switch__track::after{transition:transform 75ms 0ms cubic-bezier(0, 0, 0.2, 1);transform:translateX(0)}.mdc-switch__handle-track{height:100%;pointer-events:none;position:absolute;top:0;transition:transform 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);left:0;right:auto;transform:translateX(0)}[dir=rtl] .mdc-switch__handle-track,.mdc-switch__handle-track[dir=rtl]{left:auto;right:0}.mdc-switch--selected .mdc-switch__handle-track{transform:translateX(100%)}[dir=rtl] .mdc-switch--selected .mdc-switch__handle-track,.mdc-switch--selected .mdc-switch__handle-track[dir=rtl]{transform:translateX(-100%)}.mdc-switch__handle{display:flex;pointer-events:auto;position:absolute;top:50%;transform:translateY(-50%);left:0;right:auto}[dir=rtl] .mdc-switch__handle,.mdc-switch__handle[dir=rtl]{left:auto;right:0}.mdc-switch__handle::before,.mdc-switch__handle::after{border:1px solid rgba(0,0,0,0);border-radius:inherit;box-sizing:border-box;content:\\\"\\\";width:100%;height:100%;left:0;position:absolute;top:0;transition:background-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1),border-color 75ms 0ms cubic-bezier(0.4, 0, 0.2, 1);z-index:-1}@media screen and (forced-colors: active){.mdc-switch__handle::before,.mdc-switch__handle::after{border-color:currentColor}}.mdc-switch__shadow{border-radius:inherit;bottom:0;left:0;position:absolute;right:0;top:0}.mdc-elevation-overlay{bottom:0;left:0;right:0;top:0}.mdc-switch__ripple{left:50%;position:absolute;top:50%;transform:translate(-50%, -50%);z-index:-1}.mdc-switch:disabled .mdc-switch__ripple{display:none}.mdc-switch__icons{height:100%;position:relative;width:100%;z-index:1}.mdc-switch__icon{bottom:0;left:0;margin:auto;position:absolute;right:0;top:0;opacity:0;transition:opacity 30ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-switch--selected .mdc-switch__icon--on,.mdc-switch--unselected .mdc-switch__icon--off{opacity:1;transition:opacity 45ms 30ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle .mdc-switch--disabled+label{color:var(--mdc-switch-disabled-label-text-color)}.mdc-switch{width:var(--mdc-switch-track-width)}.mdc-switch.mdc-switch--selected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-selected-handle-color)}.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-hover-handle-color)}.mdc-switch.mdc-switch--selected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-selected-focus-handle-color)}.mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-selected-pressed-handle-color)}.mdc-switch.mdc-switch--selected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-selected-handle-color)}.mdc-switch.mdc-switch--unselected:enabled .mdc-switch__handle::after{background:var(--mdc-switch-unselected-handle-color)}.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-hover-handle-color)}.mdc-switch.mdc-switch--unselected:enabled:focus:not(:active) .mdc-switch__handle::after{background:var(--mdc-switch-unselected-focus-handle-color)}.mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__handle::after{background:var(--mdc-switch-unselected-pressed-handle-color)}.mdc-switch.mdc-switch--unselected:disabled .mdc-switch__handle::after{background:var(--mdc-switch-disabled-unselected-handle-color)}.mdc-switch .mdc-switch__handle::before{background:var(--mdc-switch-handle-surface-color)}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation)}.mdc-switch:disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation)}.mdc-switch .mdc-switch__focus-ring-wrapper,.mdc-switch .mdc-switch__handle{height:var(--mdc-switch-handle-height)}.mdc-switch .mdc-switch__handle{border-radius:var(--mdc-switch-handle-shape)}.mdc-switch .mdc-switch__handle{width:var(--mdc-switch-handle-width)}.mdc-switch .mdc-switch__handle-track{width:calc(100% - var(--mdc-switch-handle-width))}.mdc-switch.mdc-switch--selected:enabled .mdc-switch__icon{fill:var(--mdc-switch-selected-icon-color)}.mdc-switch.mdc-switch--selected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-selected-icon-color)}.mdc-switch.mdc-switch--unselected:enabled .mdc-switch__icon{fill:var(--mdc-switch-unselected-icon-color)}.mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icon{fill:var(--mdc-switch-disabled-unselected-icon-color)}.mdc-switch.mdc-switch--selected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-selected-icon-opacity)}.mdc-switch.mdc-switch--unselected:disabled .mdc-switch__icons{opacity:var(--mdc-switch-disabled-unselected-icon-opacity)}.mdc-switch.mdc-switch--selected .mdc-switch__icon{width:var(--mdc-switch-selected-icon-size);height:var(--mdc-switch-selected-icon-size)}.mdc-switch.mdc-switch--unselected .mdc-switch__icon{width:var(--mdc-switch-unselected-icon-size);height:var(--mdc-switch-unselected-icon-size)}.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-hover-state-layer-color)}.mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-focus-state-layer-color)}.mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-selected-pressed-state-layer-color)}.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus) .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-hover-state-layer-color)}.mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:focus .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-focus-state-layer-color)}.mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:active .mdc-switch__ripple::after{background-color:var(--mdc-switch-unselected-pressed-state-layer-color)}.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-selected-hover-state-layer-opacity)}.mdc-switch.mdc-switch--selected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mdc-switch.mdc-switch--selected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-selected-focus-state-layer-opacity)}.mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mdc-switch.mdc-switch--selected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-selected-pressed-state-layer-opacity)}.mdc-switch.mdc-switch--selected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-selected-pressed-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus):hover .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:hover:not(:focus).mdc-ripple-surface--hover .mdc-switch__ripple::before{opacity:var(--mdc-switch-unselected-hover-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:focus.mdc-ripple-upgraded--background-focused .mdc-switch__ripple::before,.mdc-switch.mdc-switch--unselected:enabled:focus:not(.mdc-ripple-upgraded):focus .mdc-switch__ripple::before{transition-duration:75ms;opacity:var(--mdc-switch-unselected-focus-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded) .mdc-switch__ripple::after{transition:opacity 150ms linear}.mdc-switch.mdc-switch--unselected:enabled:active:not(.mdc-ripple-upgraded):active .mdc-switch__ripple::after{transition-duration:75ms;opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity)}.mdc-switch.mdc-switch--unselected:enabled:active.mdc-ripple-upgraded{--mdc-ripple-fg-opacity:var(--mdc-switch-unselected-pressed-state-layer-opacity)}.mdc-switch .mdc-switch__ripple{height:var(--mdc-switch-state-layer-size);width:var(--mdc-switch-state-layer-size)}.mdc-switch .mdc-switch__track{height:var(--mdc-switch-track-height)}.mdc-switch:disabled .mdc-switch__track{opacity:var(--mdc-switch-disabled-track-opacity)}.mdc-switch:enabled .mdc-switch__track::after{background:var(--mdc-switch-selected-track-color)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-hover-track-color)}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::after{background:var(--mdc-switch-selected-focus-track-color)}.mdc-switch:enabled:active .mdc-switch__track::after{background:var(--mdc-switch-selected-pressed-track-color)}.mdc-switch:disabled .mdc-switch__track::after{background:var(--mdc-switch-disabled-selected-track-color)}.mdc-switch:enabled .mdc-switch__track::before{background:var(--mdc-switch-unselected-track-color)}.mdc-switch:enabled:hover:not(:focus):not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-hover-track-color)}.mdc-switch:enabled:focus:not(:active) .mdc-switch__track::before{background:var(--mdc-switch-unselected-focus-track-color)}.mdc-switch:enabled:active .mdc-switch__track::before{background:var(--mdc-switch-unselected-pressed-track-color)}.mdc-switch:disabled .mdc-switch__track::before{background:var(--mdc-switch-disabled-unselected-track-color)}.mdc-switch .mdc-switch__track{border-radius:var(--mdc-switch-track-shape)}.mdc-switch:enabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-handle-elevation-shadow)}.mdc-switch:disabled .mdc-switch__shadow{box-shadow:var(--mdc-switch-disabled-handle-elevation-shadow)}.mat-mdc-slide-toggle{display:inline-block;-webkit-tap-highlight-color:rgba(0,0,0,0);outline:0}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple,.mat-mdc-slide-toggle .mdc-switch__ripple::after{top:0;left:0;right:0;bottom:0;position:absolute;border-radius:50%;pointer-events:none}.mat-mdc-slide-toggle .mat-mdc-slide-toggle-ripple:not(:empty),.mat-mdc-slide-toggle .mdc-switch__ripple::after:not(:empty){transform:translateZ(0)}.mat-mdc-slide-toggle .mdc-switch__ripple::after{content:\\\"\\\";opacity:0}.mat-mdc-slide-toggle .mdc-switch:hover .mdc-switch__ripple::after{opacity:.04;transition:opacity 75ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mdc-switch .mdc-switch__ripple::after{opacity:.12}.mat-mdc-slide-toggle.mat-mdc-slide-toggle-focused .mat-mdc-focus-indicator::before{content:\\\"\\\"}.mat-mdc-slide-toggle .mat-ripple-element{opacity:.12}.mat-mdc-slide-toggle .mat-mdc-focus-indicator::before{border-radius:50%}.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle-track,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-elevation-overlay,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__icon,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__handle::after,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::before,.mat-mdc-slide-toggle._mat-animation-noopable .mdc-switch__track::after{transition:none}.mat-mdc-slide-toggle .mdc-switch:enabled+.mdc-label{cursor:pointer}.mdc-switch__handle{transition:width 75ms cubic-bezier(0.4, 0, 0.2, 1),height 75ms cubic-bezier(0.4, 0, 0.2, 1),margin 75ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-switch--selected .mdc-switch__track::before{opacity:var(--mat-switch-hidden-track-opacity);transition:var(--mat-switch-hidden-track-transition)}.mdc-switch--selected .mdc-switch__track::after{opacity:var(--mat-switch-visible-track-opacity);transition:var(--mat-switch-visible-track-transition)}.mdc-switch--unselected .mdc-switch__track::before{opacity:var(--mat-switch-visible-track-opacity);transition:var(--mat-switch-visible-track-transition)}.mdc-switch--unselected .mdc-switch__track::after{opacity:var(--mat-switch-hidden-track-opacity);transition:var(--mat-switch-hidden-track-transition)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{width:var(--mat-switch-unselected-handle-size);height:var(--mat-switch-unselected-handle-size)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{width:var(--mat-switch-selected-handle-size);height:var(--mat-switch-selected-handle-size)}.mat-mdc-slide-toggle .mdc-switch__handle:has(.mdc-switch__icons){width:var(--mat-switch-with-icon-handle-size);height:var(--mat-switch-with-icon-handle-size)}.mat-mdc-slide-toggle:active .mdc-switch:not(.mdc-switch--disabled) .mdc-switch__handle{width:var(--mat-switch-pressed-handle-size);height:var(--mat-switch-pressed-handle-size)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle{margin:var(--mat-switch-selected-handle-horizontal-margin)}.mat-mdc-slide-toggle .mdc-switch--selected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-selected-with-icon-handle-horizontal-margin)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle{margin:var(--mat-switch-unselected-handle-horizontal-margin)}.mat-mdc-slide-toggle .mdc-switch--unselected .mdc-switch__handle:has(.mdc-switch__icons){margin:var(--mat-switch-unselected-with-icon-handle-horizontal-margin)}.mat-mdc-slide-toggle:active .mdc-switch--selected:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-selected-pressed-handle-horizontal-margin)}.mat-mdc-slide-toggle:active .mdc-switch--unselected:not(.mdc-switch--disabled) .mdc-switch__handle{margin:var(--mat-switch-unselected-pressed-handle-horizontal-margin)}.mdc-switch__track::after,.mdc-switch__track::before{border-width:var(--mat-switch-track-outline-width);border-color:var(--mat-switch-track-outline-color)}.mdc-switch--selected .mdc-switch__track::after,.mdc-switch--selected .mdc-switch__track::before{border-width:var(--mat-switch-selected-track-outline-width)}.mdc-switch--disabled .mdc-switch__track::after,.mdc-switch--disabled .mdc-switch__track::before{border-width:var(--mat-switch-disabled-unselected-track-outline-width);border-color:var(--mat-switch-disabled-unselected-track-outline-color)}.mdc-switch--disabled.mdc-switch--selected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-selected-handle-opacity)}.mdc-switch--disabled.mdc-switch--unselected .mdc-switch__handle::after{opacity:var(--mat-switch-disabled-unselected-handle-opacity)}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i1.FocusMonitor }, { type: i0.ChangeDetectorRef }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { _switchElement: [{\n                type: ViewChild,\n                args: ['switch']\n            }], name: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], labelPosition: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], ariaDescribedby: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], color: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{ transform: (value) => (value == null ? 0 : numberAttribute(value)) }]\n            }], checked: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], hideIcon: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], change: [{\n                type: Output\n            }], toggleChange: [{\n                type: Output\n            }] } });\n\n/**\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nconst MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR = {\n    provide: NG_VALIDATORS,\n    useExisting: forwardRef(() => MatSlideToggleRequiredValidator),\n    multi: true,\n};\n/**\n * Validator for Material slide-toggle components with the required attribute in a\n * template-driven form. The default validator for required form controls asserts\n * that the control value is not undefined but that is not appropriate for a slide-toggle\n * where the value is always defined.\n *\n * Required slide-toggle form controls are valid when checked.\n *\n * @deprecated No longer used, `MatCheckbox` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass MatSlideToggleRequiredValidator extends CheckboxRequiredValidator {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSlideToggleRequiredValidator, deps: null, target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatSlideToggleRequiredValidator, isStandalone: true, selector: \"mat-slide-toggle[required][formControlName],\\n             mat-slide-toggle[required][formControl], mat-slide-toggle[required][ngModel]\", providers: [MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR], usesInheritance: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSlideToggleRequiredValidator, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-slide-toggle[required][formControlName],\n             mat-slide-toggle[required][formControl], mat-slide-toggle[required][ngModel]`,\n                    providers: [MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR],\n                    standalone: true,\n                }]\n        }] });\n\n/**\n * @deprecated No longer used, `MatSlideToggle` implements required validation directly.\n * @breaking-change 19.0.0\n */\nclass _MatSlideToggleRequiredValidatorModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatSlideToggleRequiredValidatorModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatSlideToggleRequiredValidatorModule, imports: [MatSlideToggleRequiredValidator], exports: [MatSlideToggleRequiredValidator] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatSlideToggleRequiredValidatorModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatSlideToggleRequiredValidatorModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatSlideToggleRequiredValidator],\n                    exports: [MatSlideToggleRequiredValidator],\n                }]\n        }] });\nclass MatSlideToggleModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSlideToggleModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSlideToggleModule, imports: [MatSlideToggle, MatCommonModule], exports: [MatSlideToggle, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSlideToggleModule, imports: [MatSlideToggle, MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSlideToggleModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatSlideToggle, MatCommonModule],\n                    exports: [MatSlideToggle, MatCommonModule],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SLIDE_TOGGLE_DEFAULT_OPTIONS, MAT_SLIDE_TOGGLE_REQUIRED_VALIDATOR, MAT_SLIDE_TOGGLE_VALUE_ACCESSOR, MatSlideToggle, MatSlideToggleChange, MatSlideToggleModule, MatSlideToggleRequiredValidator, _MatSlideToggleRequiredValidatorModule };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,UAAU,EAAEC,YAAY,EAAEC,qBAAqB,EAAEC,gBAAgB,EAAEC,eAAe,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACrQ,SAASC,iBAAiB,EAAEC,aAAa,EAAEC,yBAAyB,QAAQ,gBAAgB;AAC5F,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,SAASC,SAAS,EAAEC,qBAAqB,EAAEC,eAAe,QAAQ,wBAAwB;;AAE1F;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,uCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA4KoG5B,EAAE,CAAA8B,cAAA,aAQg6C,CAAC;IARn6C9B,EAAE,CAAA+B,cAAA;IAAF/B,EAAE,CAAA8B,cAAA,aAQ2jD,CAAC;IAR9jD9B,EAAE,CAAAgC,SAAA,cAQ6pD,CAAC;IARhqDhC,EAAE,CAAAiC,YAAA,CAQirD,CAAC;IARprDjC,EAAE,CAAA8B,cAAA,aAQ60D,CAAC;IARh1D9B,EAAE,CAAAgC,SAAA,cAQ63D,CAAC;IARh4DhC,EAAE,CAAAiC,YAAA,CAQi5D,CAAC,CAAiB,CAAC;EAAA;AAAA;AAnL1gE,MAAMC,gCAAgC,GAAG,IAAIjC,cAAc,CAAC,kCAAkC,EAAE;EAC5FkC,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,MAAO;IAAEC,kBAAkB,EAAE,KAAK;IAAEC,QAAQ,EAAE;EAAM,CAAC;AAClE,CAAC,CAAC;;AAEF;AACA;AACA;AACA;AACA,MAAMC,+BAA+B,GAAG;EACpCC,OAAO,EAAEtB,iBAAiB;EAC1BuB,WAAW,EAAEvC,UAAU,CAAC,MAAMwC,cAAc,CAAC;EAC7CC,KAAK,EAAE;AACX,CAAC;AACD;AACA,MAAMC,oBAAoB,CAAC;EACvBC,WAAWA,CACX;EACAC,MAAM,EACN;EACAC,OAAO,EAAE;IACL,IAAI,CAACD,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;AACJ;AACA;AACA,IAAIC,YAAY,GAAG,CAAC;AACpB,MAAMN,cAAc,CAAC;EACjBO,kBAAkBA,CAACC,SAAS,EAAE;IAC1B,OAAO,IAAIN,oBAAoB,CAAC,IAAI,EAAEM,SAAS,CAAC;EACpD;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,GAAG,IAAI,CAACC,EAAE,IAAI,IAAI,CAACC,SAAS,SAAS;EAChD;EACA;EACAC,KAAKA,CAAA,EAAG;IACJ,IAAI,CAACC,cAAc,CAACC,aAAa,CAACF,KAAK,CAAC,CAAC;EAC7C;EACA;EACA,IAAIP,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACU,QAAQ;EACxB;EACA,IAAIV,OAAOA,CAACW,KAAK,EAAE;IACf,IAAI,CAACD,QAAQ,GAAGC,KAAK;IACrB,IAAI,CAACC,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,GAAG,IAAI,CAACT,EAAE,IAAI,IAAI,CAACC,SAAS,QAAQ;EAC/C;EACAR,WAAWA,CAACiB,WAAW,EAAEC,aAAa,EAAEJ,kBAAkB,EAAEK,QAAQ,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IAC3F,IAAI,CAACJ,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACJ,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACM,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACE,SAAS,GAAIC,CAAC,IAAK,CAAE,CAAC;IAC3B,IAAI,CAACC,UAAU,GAAG,MAAM,CAAE,CAAC;IAC3B,IAAI,CAACC,kBAAkB,GAAG,MAAM,CAAE,CAAC;IACnC,IAAI,CAACb,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACc,IAAI,GAAG,IAAI;IAChB;IACA,IAAI,CAACC,aAAa,GAAG,OAAO;IAC5B;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACC,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;IACA,IAAI,CAACZ,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,CAACa,MAAM,GAAG,IAAI1E,YAAY,CAAC,CAAC;IAChC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC2E,YAAY,GAAG,IAAI3E,YAAY,CAAC,CAAC;IACtC,IAAI,CAAC6D,QAAQ,GAAGe,QAAQ,CAACf,QAAQ,CAAC,IAAI,CAAC;IACvC,IAAI,CAACgB,KAAK,GAAGf,QAAQ,CAACe,KAAK,IAAI,QAAQ;IACvC,IAAI,CAACC,eAAe,GAAGf,aAAa,KAAK,gBAAgB;IACzD,IAAI,CAACd,EAAE,GAAG,IAAI,CAACC,SAAS,GAAG,wBAAwB,EAAEL,YAAY,EAAE;IACnE,IAAI,CAACV,QAAQ,GAAG2B,QAAQ,CAAC3B,QAAQ,IAAI,KAAK;IAC1C,IAAI,CAAC4C,QAAQ,GAAG,IAAI,CAAC7B,SAAS,GAAG,QAAQ;EAC7C;EACA8B,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACpB,aAAa,CAACqB,OAAO,CAAC,IAAI,CAACtB,WAAW,EAAE,IAAI,CAAC,CAACuB,SAAS,CAACC,WAAW,IAAI;MACxE,IAAIA,WAAW,KAAK,UAAU,IAAIA,WAAW,KAAK,SAAS,EAAE;QACzD,IAAI,CAACC,QAAQ,GAAG,IAAI;QACpB,IAAI,CAAC5B,kBAAkB,CAACC,YAAY,CAAC,CAAC;MAC1C,CAAC,MACI,IAAI,CAAC0B,WAAW,EAAE;QACnB;QACA;QACA;QACA;QACA;QACAE,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;UACzB,IAAI,CAACH,QAAQ,GAAG,KAAK;UACrB,IAAI,CAAClB,UAAU,CAAC,CAAC;UACjB,IAAI,CAACV,kBAAkB,CAACC,YAAY,CAAC,CAAC;QAC1C,CAAC,CAAC;MACN;IACJ,CAAC,CAAC;EACN;EACA+B,WAAWA,CAACC,OAAO,EAAE;IACjB,IAAIA,OAAO,CAAC,UAAU,CAAC,EAAE;MACrB,IAAI,CAACtB,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACAuB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9B,aAAa,CAAC+B,cAAc,CAAC,IAAI,CAAChC,WAAW,CAAC;EACvD;EACA;EACAiC,UAAUA,CAACrC,KAAK,EAAE;IACd,IAAI,CAACX,OAAO,GAAG,CAAC,CAACW,KAAK;EAC1B;EACA;EACAsC,gBAAgBA,CAACC,EAAE,EAAE;IACjB,IAAI,CAAC9B,SAAS,GAAG8B,EAAE;EACvB;EACA;EACAC,iBAAiBA,CAACD,EAAE,EAAE;IAClB,IAAI,CAAC5B,UAAU,GAAG4B,EAAE;EACxB;EACA;EACAE,QAAQA,CAACC,OAAO,EAAE;IACd,OAAO,IAAI,CAACC,QAAQ,IAAID,OAAO,CAAC1C,KAAK,KAAK,IAAI,GAAG;MAAE,UAAU,EAAE;IAAK,CAAC,GAAG,IAAI;EAChF;EACA;EACA4C,yBAAyBA,CAACL,EAAE,EAAE;IAC1B,IAAI,CAAC3B,kBAAkB,GAAG2B,EAAE;EAChC;EACA;EACAM,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAAC7B,QAAQ,GAAG6B,UAAU;IAC1B,IAAI,CAAC7C,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;EACA6C,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC1D,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;IAC5B,IAAI,CAACoB,SAAS,CAAC,IAAI,CAACpB,OAAO,CAAC;EAChC;EACA;AACJ;AACA;EACI2D,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACvC,SAAS,CAAC,IAAI,CAACpB,OAAO,CAAC;IAC5B,IAAI,CAAC8B,MAAM,CAAC8B,IAAI,CAAC,IAAI,CAAC1D,kBAAkB,CAAC,IAAI,CAACF,OAAO,CAAC,CAAC;EAC3D;EACA;EACA6D,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC9B,YAAY,CAAC6B,IAAI,CAAC,CAAC;IACxB,IAAI,CAAC,IAAI,CAAC1C,QAAQ,CAAC5B,kBAAkB,EAAE;MACnC,IAAI,CAACU,OAAO,GAAG,CAAC,IAAI,CAACA,OAAO;MAC5B,IAAI,CAACoB,SAAS,CAAC,IAAI,CAACpB,OAAO,CAAC;MAC5B,IAAI,CAAC8B,MAAM,CAAC8B,IAAI,CAAC,IAAI/D,oBAAoB,CAAC,IAAI,EAAE,IAAI,CAACG,OAAO,CAAC,CAAC;IAClE;EACJ;EACA8D,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACnC,cAAc,EAAE;MACrB,OAAO,IAAI,CAACA,cAAc;IAC9B;IACA;IACA;IACA,OAAO,IAAI,CAACD,SAAS,GAAG,IAAI,GAAG,IAAI,CAACS,QAAQ;EAChD;EACA;IAAS,IAAI,CAAC4B,IAAI,YAAAC,uBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFtE,cAAc,EAAxB1C,EAAE,CAAAiH,iBAAA,CAAwCjH,EAAE,CAACkH,UAAU,GAAvDlH,EAAE,CAAAiH,iBAAA,CAAkE5F,EAAE,CAAC8F,YAAY,GAAnFnH,EAAE,CAAAiH,iBAAA,CAA8FjH,EAAE,CAACoH,iBAAiB,GAApHpH,EAAE,CAAAqH,iBAAA,CAA+H,UAAU,GAA3IrH,EAAE,CAAAiH,iBAAA,CAAuK/E,gCAAgC,GAAzMlC,EAAE,CAAAiH,iBAAA,CAAoN7G,qBAAqB;IAAA,CAA4D;EAAE;EACzY;IAAS,IAAI,CAACkH,IAAI,kBAD8EtH,EAAE,CAAAuH,iBAAA;MAAAC,IAAA,EACJ9E,cAAc;MAAA+E,SAAA;MAAAC,SAAA,WAAAC,qBAAA/F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADZ5B,EAAE,CAAA4H,WAAA,CAAAnG,GAAA;QAAA;QAAA,IAAAG,EAAA;UAAA,IAAAiG,EAAA;UAAF7H,EAAE,CAAA8H,cAAA,CAAAD,EAAA,GAAF7H,EAAE,CAAA+H,WAAA,QAAAlG,GAAA,CAAA0B,cAAA,GAAAsE,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,4BAAAxG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF5B,EAAE,CAAAqI,cAAA,OAAAxG,GAAA,CAAAuB,EACS,CAAC;UADZpD,EAAE,CAAAsI,WAAA,aACJ,IAAI,gBAAJ,IAAI,UAAJ,IAAI,qBAAJ,IAAI;UADFtI,EAAE,CAAAuI,UAAA,CAAA1G,GAAA,CAAAmD,KAAA,GACI,MAAM,GAAAnD,GAAA,CAAAmD,KAAA,GAAW,EAAZ,CAAC;UADZhF,EAAE,CAAAwI,WAAA,iCAAA3G,GAAA,CAAA0D,QACS,CAAC,iCAAA1D,GAAA,CAAAkB,OAAD,CAAC,4BAAAlB,GAAA,CAAAoD,eAAD,CAAC;QAAA;MAAA;MAAAwD,MAAA;QAAAlE,IAAA;QAAAnB,EAAA;QAAAoB,aAAA;QAAAC,SAAA,GADZzE,EAAE,CAAA0I,YAAA,CAAAC,IAAA;QAAAjE,cAAA,GAAF1E,EAAE,CAAA0I,YAAA,CAAAC,IAAA;QAAAC,eAAA,GAAF5I,EAAE,CAAA0I,YAAA,CAAAC,IAAA;QAAAtC,QAAA,GAAFrG,EAAE,CAAA0I,YAAA,CAAAG,0BAAA,0BAC4TxI,gBAAgB;QAAA2E,KAAA;QAAAL,QAAA,GAD9U3E,EAAE,CAAA0I,YAAA,CAAAG,0BAAA,0BACkYxI,gBAAgB;QAAAuE,aAAA,GADpZ5E,EAAE,CAAA0I,YAAA,CAAAG,0BAAA,oCACucxI,gBAAgB;QAAA2D,QAAA,GADzdhE,EAAE,CAAA0I,YAAA,CAAAG,0BAAA,0BAC8fnF,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGpD,eAAe,CAACoD,KAAK,CAAE;QAAAX,OAAA,GADtjB/C,EAAE,CAAA0I,YAAA,CAAAG,0BAAA,wBACulBxI,gBAAgB;QAAAiC,QAAA,GADzmBtC,EAAE,CAAA0I,YAAA,CAAAG,0BAAA,0BAC6oBxI,gBAAgB;MAAA;MAAAyI,OAAA;QAAAjE,MAAA;QAAAC,YAAA;MAAA;MAAAiE,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAD/pBjJ,EAAE,CAAAkJ,kBAAA,CAComC,CAC9rC3G,+BAA+B,EAC/B;QACIC,OAAO,EAAErB,aAAa;QACtBsB,WAAW,EAAEC,cAAc;QAC3BC,KAAK,EAAE;MACX,CAAC,CACJ,GAR2F3C,EAAE,CAAAmJ,wBAAA,EAAFnJ,EAAE,CAAAoJ,oBAAA,EAAFpJ,EAAE,CAAAqJ,mBAAA;MAAAC,kBAAA,EAAA5H,GAAA;MAAA6H,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAA/H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAgI,GAAA,GAAF5J,EAAE,CAAA6J,gBAAA;UAAF7J,EAAE,CAAA8J,eAAA;UAAF9J,EAAE,CAAA8B,cAAA,YAQyJ,CAAC,kBAAspB,CAAC;UARnzB9B,EAAE,CAAA+J,UAAA,mBAAAC,gDAAA;YAAFhK,EAAE,CAAAiK,aAAA,CAAAL,GAAA;YAAA,OAAF5J,EAAE,CAAAkK,WAAA,CAQmxBrI,GAAA,CAAA+E,YAAA,CAAa,CAAC;UAAA,CAAC,CAAC;UARryB5G,EAAE,CAAAgC,SAAA,YAQ61B,CAAC;UARh2BhC,EAAE,CAAA8B,cAAA,YAQ24B,CAAC,YAAyC,CAAC,YAA2C,CAAC;UARp+B9B,EAAE,CAAAgC,SAAA,YAQwhC,CAAC;UAR3hChC,EAAE,CAAAiC,YAAA,CAQwiC,CAAC;UAR3iCjC,EAAE,CAAA8B,cAAA,YAQolC,CAAC;UARvlC9B,EAAE,CAAAgC,SAAA,YAQw0C,CAAC;UAR30ChC,EAAE,CAAAiC,YAAA,CAQw1C,CAAC;UAR31CjC,EAAE,CAAAmK,UAAA,KAAAxI,sCAAA,iBAQm3C,CAAC;UARt3C3B,EAAE,CAAAiC,YAAA,CAQ47D,CAAC,CAAW,CAAC,CAAY,CAAC;UARx9DjC,EAAE,CAAA8B,cAAA,gBAQ0vE,CAAC;UAR7vE9B,EAAE,CAAA+J,UAAA,mBAAAK,gDAAAC,MAAA;YAAFrK,EAAE,CAAAiK,aAAA,CAAAL,GAAA;YAAA,OAAF5J,EAAE,CAAAkK,WAAA,CAQguEG,MAAA,CAAAC,eAAA,CAAuB,CAAC;UAAA,CAAC,CAAC;UAR5vEtK,EAAE,CAAAuK,YAAA,GAQyxE,CAAC;UAR5xEvK,EAAE,CAAAiC,YAAA,CAQqyE,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAL,EAAA;UAAA,MAAA4I,SAAA,GARhzExK,EAAE,CAAAyK,WAAA;UAAFzK,EAAE,CAAA0K,UAAA,kBAAA7I,GAAA,CAAA2C,aAQwJ,CAAC;UAR3JxE,EAAE,CAAA2K,SAAA,CAQsR,CAAC;UARzR3K,EAAE,CAAAwI,WAAA,yBAAA3G,GAAA,CAAAkB,OAQsR,CAAC,4BAAAlB,GAAA,CAAAkB,OAAgD,CAAC,wBAAAlB,GAAA,CAAAkB,OAA4C,CAAC,yBAAAlB,GAAA,CAAA8C,QAA8C,CAAC;UARta3E,EAAE,CAAA0K,UAAA,aAAA7I,GAAA,CAAA8C,QAAA,QAAA9C,GAAA,CAAAmC,QAQgd,CAAC,aAAAnC,GAAA,CAAA8C,QAA4B,CAAC;UARhf3E,EAAE,CAAAsI,WAAA,OAAAzG,GAAA,CAAAsB,QAAA,UAAAtB,GAAA,CAAA0C,IAAA,gBAAA1C,GAAA,CAAA4C,SAAA,qBAAA5C,GAAA,CAAAgF,kBAAA,wBAAAhF,GAAA,CAAA+G,eAAA,mBAAA/G,GAAA,CAAAwE,QAAA,0BAAAxE,GAAA,CAAAkB,OAAA;UAAF/C,EAAE,CAAA2K,SAAA,EAQwtC,CAAC;UAR3tC3K,EAAE,CAAA0K,UAAA,qBAAAF,SAQwtC,CAAC,sBAAA3I,GAAA,CAAA+C,aAAA,IAAA/C,GAAA,CAAA8C,QAA8D,CAAC,0BAAyC,CAAC;UARp0C3E,EAAE,CAAA2K,SAAA,CAQ86D,CAAC;UARj7D3K,EAAE,CAAA4K,aAAA,MAAA/I,GAAA,CAAAS,QAAA,UAQ86D,CAAC;UARj7DtC,EAAE,CAAA2K,SAAA,CAQ6rE,CAAC;UARhsE3K,EAAE,CAAA0K,UAAA,QAAA7I,GAAA,CAAAsB,QAQ6rE,CAAC;UARhsEnD,EAAE,CAAAsI,WAAA,OAAAzG,GAAA,CAAAqD,QAAA;QAAA;MAAA;MAAA2F,YAAA,GAQi2kBvJ,SAAS,EAAwPC,qBAAqB;MAAAuJ,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAyK;EAAE;AACx4lB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAVoGjL,EAAE,CAAAkL,iBAAA,CAUXxI,cAAc,EAAc,CAAC;IAC5G8E,IAAI,EAAEjH,SAAS;IACf4K,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,kBAAkB;MAAEC,IAAI,EAAE;QACjC,OAAO,EAAE,sBAAsB;QAC/B,MAAM,EAAE,IAAI;QACZ;QACA,iBAAiB,EAAE,MAAM;QACzB,mBAAmB,EAAE,MAAM;QAC3B,aAAa,EAAE,MAAM;QACrB,wBAAwB,EAAE,MAAM;QAChC,sCAAsC,EAAE,UAAU;QAClD,sCAAsC,EAAE,SAAS;QACjD,iCAAiC,EAAE,iBAAiB;QACpD,SAAS,EAAE;MACf,CAAC;MAAEtC,QAAQ,EAAE,gBAAgB;MAAEgC,aAAa,EAAEvK,iBAAiB,CAACmI,IAAI;MAAEqC,eAAe,EAAEvK,uBAAuB,CAAC6K,MAAM;MAAEC,SAAS,EAAE,CAC9HhJ,+BAA+B,EAC/B;QACIC,OAAO,EAAErB,aAAa;QACtBsB,WAAW,EAAEC,cAAc;QAC3BC,KAAK,EAAE;MACX,CAAC,CACJ;MAAEqG,UAAU,EAAE,IAAI;MAAEwC,OAAO,EAAE,CAAClK,SAAS,EAAEC,qBAAqB,CAAC;MAAEmI,QAAQ,EAAE,utEAAutE;MAAEoB,MAAM,EAAE,CAAC,w/fAAw/f;IAAE,CAAC;EACrzkB,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEtD,IAAI,EAAExH,EAAE,CAACkH;EAAW,CAAC,EAAE;IAAEM,IAAI,EAAEnG,EAAE,CAAC8F;EAAa,CAAC,EAAE;IAAEK,IAAI,EAAExH,EAAE,CAACoH;EAAkB,CAAC,EAAE;IAAEI,IAAI,EAAEiE,SAAS;IAAEC,UAAU,EAAE,CAAC;MACnIlE,IAAI,EAAE9G,SAAS;MACfyK,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAE3D,IAAI,EAAEiE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClClE,IAAI,EAAE7G,MAAM;MACZwK,IAAI,EAAE,CAACjJ,gCAAgC;IAC3C,CAAC;EAAE,CAAC,EAAE;IAAEsF,IAAI,EAAEiE,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClClE,IAAI,EAAE5G;IACV,CAAC,EAAE;MACC4G,IAAI,EAAE7G,MAAM;MACZwK,IAAI,EAAE,CAAC/K,qBAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEmD,cAAc,EAAE,CAAC;MAC1CiE,IAAI,EAAE3G,SAAS;MACfsK,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAE5G,IAAI,EAAE,CAAC;MACPiD,IAAI,EAAE1G;IACV,CAAC,CAAC;IAAEsC,EAAE,EAAE,CAAC;MACLoE,IAAI,EAAE1G;IACV,CAAC,CAAC;IAAE0D,aAAa,EAAE,CAAC;MAChBgD,IAAI,EAAE1G;IACV,CAAC,CAAC;IAAE2D,SAAS,EAAE,CAAC;MACZ+C,IAAI,EAAE1G,KAAK;MACXqK,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEzG,cAAc,EAAE,CAAC;MACjB8C,IAAI,EAAE1G,KAAK;MACXqK,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAEvC,eAAe,EAAE,CAAC;MAClBpB,IAAI,EAAE1G,KAAK;MACXqK,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE9E,QAAQ,EAAE,CAAC;MACXmB,IAAI,EAAE1G,KAAK;MACXqK,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEtL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2E,KAAK,EAAE,CAAC;MACRwC,IAAI,EAAE1G;IACV,CAAC,CAAC;IAAE6D,QAAQ,EAAE,CAAC;MACX6C,IAAI,EAAE1G,KAAK;MACXqK,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEtL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEuE,aAAa,EAAE,CAAC;MAChB4C,IAAI,EAAE1G,KAAK;MACXqK,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEtL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAE2D,QAAQ,EAAE,CAAC;MACXwD,IAAI,EAAE1G,KAAK;MACXqK,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAGjI,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAGpD,eAAe,CAACoD,KAAK;MAAG,CAAC;IACjF,CAAC,CAAC;IAAEX,OAAO,EAAE,CAAC;MACVyE,IAAI,EAAE1G,KAAK;MACXqK,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEtL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEiC,QAAQ,EAAE,CAAC;MACXkF,IAAI,EAAE1G,KAAK;MACXqK,IAAI,EAAE,CAAC;QAAEQ,SAAS,EAAEtL;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEwE,MAAM,EAAE,CAAC;MACT2C,IAAI,EAAEzG;IACV,CAAC,CAAC;IAAE+D,YAAY,EAAE,CAAC;MACf0C,IAAI,EAAEzG;IACV,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAM6K,mCAAmC,GAAG;EACxCpJ,OAAO,EAAErB,aAAa;EACtBsB,WAAW,EAAEvC,UAAU,CAAC,MAAM2L,+BAA+B,CAAC;EAC9DlJ,KAAK,EAAE;AACX,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkJ,+BAA+B,SAASzK,yBAAyB,CAAC;EACpE;IAAS,IAAI,CAAC0F,IAAI;MAAA,IAAAgF,4CAAA;MAAA,gBAAAC,wCAAA/E,CAAA;QAAA,QAAA8E,4CAAA,KAAAA,4CAAA,GA5G8E9L,EAAE,CAAAgM,qBAAA,CA4GQH,+BAA+B,IAAA7E,CAAA,IAA/B6E,+BAA+B;MAAA;IAAA,IAAqD;EAAE;EAChM;IAAS,IAAI,CAACI,IAAI,kBA7G8EjM,EAAE,CAAAkM,iBAAA;MAAA1E,IAAA,EA6GJqE,+BAA+B;MAAApE,SAAA;MAAAuB,UAAA;MAAAC,QAAA,GA7G7BjJ,EAAE,CAAAkJ,kBAAA,CA6GiN,CAAC0C,mCAAmC,CAAC,GA7GxP5L,EAAE,CAAAmM,0BAAA;IAAA,EA6G8R;EAAE;AACtY;AACA;EAAA,QAAAlB,SAAA,oBAAAA,SAAA,KA/GoGjL,EAAE,CAAAkL,iBAAA,CA+GXW,+BAA+B,EAAc,CAAC;IAC7HrE,IAAI,EAAExG,SAAS;IACfmK,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE;AAC9B,0FAA0F;MACtEG,SAAS,EAAE,CAACK,mCAAmC,CAAC;MAChD5C,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMoD,sCAAsC,CAAC;EACzC;IAAS,IAAI,CAACtF,IAAI,YAAAuF,+CAAArF,CAAA;MAAA,YAAAA,CAAA,IAAwFoF,sCAAsC;IAAA,CAAkD;EAAE;EACpM;IAAS,IAAI,CAACE,IAAI,kBA/H8EtM,EAAE,CAAAuM,gBAAA;MAAA/E,IAAA,EA+HS4E;IAAsC,EAA2F;EAAE;EAC9O;IAAS,IAAI,CAACI,IAAI,kBAhI8ExM,EAAE,CAAAyM,gBAAA,IAgIkD;EAAE;AAC1J;AACA;EAAA,QAAAxB,SAAA,oBAAAA,SAAA,KAlIoGjL,EAAE,CAAAkL,iBAAA,CAkIXkB,sCAAsC,EAAc,CAAC;IACpI5E,IAAI,EAAEvG,QAAQ;IACdkK,IAAI,EAAE,CAAC;MACCK,OAAO,EAAE,CAACK,+BAA+B,CAAC;MAC1Ca,OAAO,EAAE,CAACb,+BAA+B;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;AACV,MAAMc,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAAC7F,IAAI,YAAA8F,6BAAA5F,CAAA;MAAA,YAAAA,CAAA,IAAwF2F,oBAAoB;IAAA,CAAkD;EAAE;EAClL;IAAS,IAAI,CAACL,IAAI,kBA3I8EtM,EAAE,CAAAuM,gBAAA;MAAA/E,IAAA,EA2ISmF;IAAoB,EAA2F;EAAE;EAC5N;IAAS,IAAI,CAACH,IAAI,kBA5I8ExM,EAAE,CAAAyM,gBAAA;MAAAjB,OAAA,GA4IyC9I,cAAc,EAAElB,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AACrM;AACA;EAAA,QAAAyJ,SAAA,oBAAAA,SAAA,KA9IoGjL,EAAE,CAAAkL,iBAAA,CA8IXyB,oBAAoB,EAAc,CAAC;IAClHnF,IAAI,EAAEvG,QAAQ;IACdkK,IAAI,EAAE,CAAC;MACCK,OAAO,EAAE,CAAC9I,cAAc,EAAElB,eAAe,CAAC;MAC1CkL,OAAO,EAAE,CAAChK,cAAc,EAAElB,eAAe;IAC7C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASU,gCAAgC,EAAE0J,mCAAmC,EAAErJ,+BAA+B,EAAEG,cAAc,EAAEE,oBAAoB,EAAE+J,oBAAoB,EAAEd,+BAA+B,EAAEO,sCAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}