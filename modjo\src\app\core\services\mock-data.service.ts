import { Injectable } from '@angular/core';
import { User, UserRole, TransactionType } from '../models';

@Injectable({
  providedIn: 'root'
})
export class MockDataService {

  constructor() {}

  /**
   * Retourne des utilisateurs de test pour chaque rôle
   */
  getMockUsers(): User[] {
    return [
      // 🙋‍♂️ Utilisateur standard
      {
        uid: 'user1',
        email: '<EMAIL>',
        name: '<PERSON>',
        role: UserRole.USER,
        city: 'Monastir',
        points: 245,
        isActive: true,
        createdAt: new Date('2024-01-15'),
        updatedAt: new Date(),
        history: [
          {
            id: '1',
            type: TransactionType.EARNED,
            description: 'Aide à la bibliothèque municipale',
            points: 15,
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2),
            toUserId: 'user1'
          },
          {
            id: '2',
            type: TransactionType.EARNED,
            description: 'Scan QR - Cours de français',
            points: 3,
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),
            toUserId: 'user1'
          },
          {
            id: '3',
            type: TransactionType.SPENT,
            description: 'Échange - Café gratuit',
            points: -50,
            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12),
            toUserId: 'partner1'
          }
        ]
      },

      // 🧑‍💼 Administrateur
      {
        uid: 'admin1',
        email: '<EMAIL>',
        name: 'Administrateur Modjo',
        role: UserRole.ADMIN,
        city: 'Monastir',
        points: 0,
        isActive: true,
        createdAt: new Date('2024-01-01'),
        updatedAt: new Date(),
        history: []
      },

      // 🧑‍🏫 Validateur
      {
        uid: 'validator1',
        email: '<EMAIL>',
        name: 'Fatma Trabelsi',
        role: UserRole.VALIDATOR,
        city: 'Sousse',
        points: 0,
        isActive: true,
        createdAt: new Date('2024-01-10'),
        updatedAt: new Date(),
        history: []
      },

      // 🧑‍🍳 Partenaire
      {
        uid: 'partner1',
        email: '<EMAIL>',
        name: 'Mohamed Gharbi',
        role: UserRole.PARTNER,
        city: 'Monastir',
        points: 0,
        isActive: true,
        createdAt: new Date('2024-01-08'),
        updatedAt: new Date(),
        history: []
      },

      // 🧑‍🔧 Prestataire
      {
        uid: 'provider1',
        email: '<EMAIL>',
        name: 'Leila Mansouri',
        role: UserRole.PROVIDER,
        city: 'Sousse',
        points: 0,
        isActive: true,
        createdAt: new Date('2024-01-05'),
        updatedAt: new Date(),
        history: []
      }
    ];
  }

  /**
   * Retourne un utilisateur par son email (pour la simulation de connexion)
   */
  getUserByEmail(email: string): User | null {
    const users = this.getMockUsers();
    return users.find(user => user.email === email) || null;
  }

  /**
   * Simule la création d'un nouvel utilisateur
   */
  createMockUser(email: string, name: string, role: UserRole, city: 'Monastir' | 'Sousse'): User {
    return {
      uid: 'user_' + Date.now(),
      email,
      name,
      role,
      city,
      points: role === UserRole.USER ? 0 : 0,
      isActive: true,
      createdAt: new Date(),
      updatedAt: new Date(),
      history: []
    };
  }

  /**
   * Retourne les données de test selon le rôle
   */
  getMockDataForRole(role: UserRole): any {
    switch (role) {
      case UserRole.USER:
        return this.getUserMockData();
      case UserRole.ADMIN:
        return this.getAdminMockData();
      case UserRole.VALIDATOR:
        return this.getValidatorMockData();
      case UserRole.PARTNER:
        return this.getPartnerMockData();
      case UserRole.PROVIDER:
        return this.getProviderMockData();
      default:
        return {};
    }
  }

  private getUserMockData() {
    return {
      availableRewards: [
        {
          id: 'reward1',
          title: 'Café gratuit',
          description: 'Un café offert au Café des Nattes',
          pointsRequired: 50,
          partnerId: 'partner1',
          partnerName: 'Café des Nattes',
          category: 'FOOD',
          imageUrl: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',
          isActive: true,
          city: 'Monastir'
        },
        {
          id: 'reward2',
          title: '10% de réduction',
          description: 'Réduction sur tous les produits artisanaux',
          pointsRequired: 75,
          partnerId: 'partner2',
          partnerName: 'Boutique Artisanat',
          category: 'SHOPPING',
          imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',
          isActive: true,
          city: 'Sousse'
        }
      ],
      nearbyPartners: [
        {
          id: 'partner1',
          name: 'Café des Nattes',
          category: 'RESTAURANT',
          address: 'Médina de Monastir',
          distance: 0.5
        },
        {
          id: 'partner2',
          name: 'Boutique Artisanat',
          category: 'RETAIL',
          address: 'Centre-ville Sousse',
          distance: 1.2
        }
      ]
    };
  }

  private getAdminMockData() {
    return {
      systemStats: {
        totalUsers: 1247,
        activeUsers: 892,
        totalPoints: 125000,
        pendingValidations: 23,
        totalPartners: 45,
        totalProviders: 67
      },
      recentActivities: [
        {
          type: 'user_created',
          description: 'Nouvel utilisateur: Ahmed Ben Ali',
          timestamp: new Date(Date.now() - 1000 * 60 * 15)
        },
        {
          type: 'validation_approved',
          description: 'Validation approuvée: Aide bibliothèque',
          timestamp: new Date(Date.now() - 1000 * 60 * 30)
        }
      ]
    };
  }

  private getValidatorMockData() {
    return {
      pendingValidations: [
        {
          id: '1',
          userId: 'user1',
          userName: 'Ahmed Ben Ali',
          actionType: 'LIBRARY_VOLUNTEER',
          description: 'Aide à la bibliothèque municipale',
          submittedAt: new Date(Date.now() - 1000 * 60 * 30),
          points: 15
        }
      ],
      validatorStats: {
        totalValidations: 156,
        validationsToday: 8,
        approvalRate: 94
      }
    };
  }

  private getPartnerMockData() {
    return {
      myRewards: [
        {
          id: 'reward1',
          title: 'Café gratuit',
          pointsCost: 50,
          redemptions: 23,
          isActive: true
        }
      ],
      partnerStats: {
        totalRedemptions: 156,
        totalPointsGenerated: 7800,
        activeRewards: 5
      }
    };
  }

  private getProviderMockData() {
    return {
      myQrCodes: [
        {
          id: 'qr1',
          description: 'Cours de français',
          pointsValue: 3,
          totalScans: 45,
          isActive: true
        }
      ],
      providerStats: {
        totalScans: 567,
        totalPointsDistributed: 2835,
        uniqueUsers: 189
      }
    };
  }
}
