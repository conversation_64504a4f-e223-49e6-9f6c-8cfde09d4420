{"ast": null, "code": "import { LoginComponent } from './components/login/login.component';\nimport { RegisterComponent } from './components/register/register.component';\nexport const authRoutes = [{\n  path: '',\n  redirectTo: 'login',\n  pathMatch: 'full'\n}, {\n  path: 'login',\n  component: LoginComponent\n}, {\n  path: 'register',\n  component: RegisterComponent\n}];", "map": {"version": 3, "names": ["LoginComponent", "RegisterComponent", "authRoutes", "path", "redirectTo", "pathMatch", "component"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\auth\\auth.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { LoginComponent } from './components/login/login.component';\nimport { RegisterComponent } from './components/register/register.component';\n\nexport const authRoutes: Routes = [\n  {\n    path: '',\n    redirectTo: 'login',\n    pathMatch: 'full'\n  },\n  {\n    path: 'login',\n    component: LoginComponent\n  },\n  {\n    path: 'register',\n    component: RegisterComponent\n  }\n];\n"], "mappings": "AACA,SAASA,cAAc,QAAQ,oCAAoC;AACnE,SAASC,iBAAiB,QAAQ,0CAA0C;AAE5E,OAAO,MAAMC,UAAU,GAAW,CAChC;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,OAAO;EACnBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,OAAO;EACbG,SAAS,EAAEN;CACZ,EACD;EACEG,IAAI,EAAE,UAAU;EAChBG,SAAS,EAAEL;CACZ,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}