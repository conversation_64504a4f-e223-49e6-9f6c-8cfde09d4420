import { NgModule } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';

// Material modules
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';
import { MatChipsModule } from '@angular/material/chips';
import { MatTabsModule } from '@angular/material/tabs';
import { MatDialogModule } from '@angular/material/dialog';
import { MatSnackBarModule } from '@angular/material/snack-bar';
import { MatProgressSpinnerModule } from '@angular/material/progress-spinner';
import { MatBadgeModule } from '@angular/material/badge';

import { RewardsListComponent } from './components/rewards-list/rewards-list.component';
import { RewardDetailComponent } from './components/reward-detail/reward-detail.component';
import { ExchangeHistoryComponent } from './components/exchange-history/exchange-history.component';
import { rewardsRoutes } from './rewards.routes';

@NgModule({
  declarations: [
    RewardsListComponent,
    RewardDetailComponent,
    ExchangeHistoryComponent
  ],
  imports: [
    CommonModule,
    RouterModule.forChild(rewardsRoutes),
    
    // Material modules
    MatCardModule,
    MatButtonModule,
    MatIconModule,
    MatChipsModule,
    MatTabsModule,
    MatDialogModule,
    MatSnackBarModule,
    MatProgressSpinnerModule,
    MatBadgeModule
  ]
})
export class RewardsModule { }
