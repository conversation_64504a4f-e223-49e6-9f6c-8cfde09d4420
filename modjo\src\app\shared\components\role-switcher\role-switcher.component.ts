import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../../core/services/auth.service';
import { MockDataService } from '../../../core/services/mock-data.service';
import { DashboardRouterService } from '../../../core/services/dashboard-router.service';
import { User, UserRole } from '../../../core/models';

@Component({
  selector: 'app-role-switcher',
  template: `
    <div class="role-switcher" *ngIf="!isProduction">
      <h4>🧪 Mode Test - Changer de Rôle</h4>
      <div class="role-buttons">
        <button *ngFor="let user of testUsers" 
                mat-raised-button 
                [color]="currentUser?.uid === user.uid ? 'primary' : 'basic'"
                (click)="switchToUser(user)"
                class="role-btn">
          <div class="role-info">
            <span class="role-emoji">{{ getRoleEmoji(user.role) }}</span>
            <div class="role-details">
              <strong>{{ user.name }}</strong>
              <small>{{ getRoleDisplayName(user.role) }}</small>
            </div>
          </div>
        </button>
      </div>
      <div class="current-user" *ngIf="currentUser">
        <p><strong>Utilisateur actuel:</strong> {{ currentUser.name }} ({{ getRoleDisplayName(currentUser.role) }})</p>
        <p><strong>Dashboard:</strong> {{ dashboardRouter.getDashboardDisplayName(currentUser.role) }}</p>
      </div>
    </div>
  `,
  styles: [`
    .role-switcher {
      position: fixed;
      top: 20px;
      right: 20px;
      background: white;
      border: 2px solid #667eea;
      border-radius: 16px;
      padding: 16px;
      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
      z-index: 1000;
      max-width: 300px;
    }
    
    .role-switcher h4 {
      margin: 0 0 16px 0;
      color: #667eea;
      font-size: 0.9rem;
      text-align: center;
    }
    
    .role-buttons {
      display: flex;
      flex-direction: column;
      gap: 8px;
      margin-bottom: 16px;
    }
    
    .role-btn {
      padding: 8px 12px;
      text-align: left;
      height: auto;
    }
    
    .role-info {
      display: flex;
      align-items: center;
      gap: 12px;
    }
    
    .role-emoji {
      font-size: 1.5rem;
    }
    
    .role-details {
      display: flex;
      flex-direction: column;
      align-items: flex-start;
    }
    
    .role-details strong {
      font-size: 0.9rem;
      color: #2d3748;
    }
    
    .role-details small {
      font-size: 0.7rem;
      color: #718096;
      text-transform: uppercase;
      font-weight: 600;
    }
    
    .current-user {
      padding-top: 12px;
      border-top: 1px solid #e2e8f0;
      font-size: 0.8rem;
      color: #4a5568;
    }
    
    .current-user p {
      margin: 4px 0;
    }
    
    .current-user strong {
      color: #2d3748;
    }
  `]
})
export class RoleSwitcherComponent implements OnInit {
  testUsers: User[] = [];
  currentUser: User | null = null;
  isProduction = false; // Set to true in production

  constructor(
    private authService: AuthService,
    private mockDataService: MockDataService,
    public dashboardRouter: DashboardRouterService
  ) {}

  ngOnInit(): void {
    // Only show in development mode
    this.isProduction = false; // Change to true for production
    
    if (!this.isProduction) {
      this.testUsers = this.mockDataService.getMockUsers();
      
      this.authService.currentUser$.subscribe(user => {
        this.currentUser = user;
      });
    }
  }

  switchToUser(user: User): void {
    // Simulate login with the selected user
    this.authService.setCurrentUser(user);
    
    // Navigate to appropriate dashboard
    this.dashboardRouter.navigateToUserDashboard(user);
  }

  getRoleDisplayName(role: UserRole): string {
    const roleNames = {
      [UserRole.USER]: 'Utilisateur',
      [UserRole.ADMIN]: 'Administrateur',
      [UserRole.VALIDATOR]: 'Validateur',
      [UserRole.PARTNER]: 'Partenaire',
      [UserRole.PROVIDER]: 'Prestataire'
    };
    return roleNames[role];
  }

  getRoleEmoji(role: UserRole): string {
    const roleEmojis = {
      [UserRole.USER]: '🙋‍♂️',
      [UserRole.ADMIN]: '🧑‍💼',
      [UserRole.VALIDATOR]: '🧑‍🏫',
      [UserRole.PARTNER]: '🧑‍🍳',
      [UserRole.PROVIDER]: '🧑‍🔧'
    };
    return roleEmojis[role];
  }
}
