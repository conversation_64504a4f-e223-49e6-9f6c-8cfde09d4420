import { Routes } from '@angular/router';
import { AuthGuard, AdminGuard, ValidatorGuard } from './core/guards/auth.guard';
import { UserRole } from './core/models';

export const appRoutes: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'auth',
    loadChildren: () => import('./features/auth/auth.module').then(m => m.AuthModule)
  },
  // 🙋‍♂️ User Dashboard (Utilisateur standard)
  {
    path: 'dashboard',
    loadChildren: () => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule)
    // canActivate: [AuthGuard] // Temporarily disabled
  },

  // 🧑‍💼 Admin Dashboard (Administrateur)
  {
    path: 'admin',
    loadChildren: () => import('./features/admin-dashboard/admin-dashboard.module').then(m => m.AdminDashboardModule)
    // canActivate: [AdminGuard] // Temporarily disabled
  },

  // 🧑‍🏫 Validator Dashboard (Validateur)
  {
    path: 'validator',
    loadChildren: () => import('./features/validator-dashboard/validator-dashboard.module').then(m => m.ValidatorDashboardModule)
    // canActivate: [ValidatorGuard] // Temporarily disabled
  },

  // 🧑‍🍳 Partner Dashboard (Partenaire)
  {
    path: 'partner',
    loadChildren: () => import('./features/partner-dashboard/partner-dashboard.module').then(m => m.PartnerDashboardModule)
    // canActivate: [PartnerGuard] // Temporarily disabled
  },

  // 🧑‍🔧 Provider Dashboard (Prestataire)
  {
    path: 'provider',
    loadChildren: () => import('./features/provider-dashboard/provider-dashboard.module').then(m => m.ProviderDashboardModule)
    // canActivate: [ProviderGuard] // Temporarily disabled
  },

  // Shared features
  {
    path: 'profile',
    loadChildren: () => import('./features/profile/profile.module').then(m => m.ProfileModule)
    // canActivate: [AuthGuard] // Temporarily disabled
  },
  {
    path: 'qr-scanner',
    loadChildren: () => import('./features/qr-scanner/qr-scanner.module').then(m => m.QrScannerModule)
    // canActivate: [AuthGuard] // Temporarily disabled
  },
  {
    path: 'rewards',
    loadChildren: () => import('./features/rewards/rewards.module').then(m => m.RewardsModule)
    // canActivate: [AuthGuard] // Temporarily disabled
  },
  {
    path: 'unauthorized',
    loadComponent: () => import('./shared/components/unauthorized/unauthorized.component').then(c => c.UnauthorizedComponent)
  },
  {
    path: '**',
    loadComponent: () => import('./shared/components/not-found/not-found.component').then(c => c.NotFoundComponent)
  }
];
