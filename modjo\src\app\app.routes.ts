import { Routes } from '@angular/router';
import { AuthGuard, AdminGuard, ValidatorGuard } from './core/guards/auth.guard';
import { UserRole } from './core/models';

export const appRoutes: Routes = [
  {
    path: '',
    redirectTo: '/dashboard',
    pathMatch: 'full'
  },
  {
    path: 'auth',
    loadChildren: () => import('./features/auth/auth.module').then(m => m.AuthModule)
  },
  {
    path: 'dashboard',
    loadChildren: () => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'profile',
    loadChildren: () => import('./features/profile/profile.module').then(m => m.ProfileModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'qr-scanner',
    loadChildren: () => import('./features/qr-scanner/qr-scanner.module').then(m => m.QrScannerModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'rewards',
    loadChildren: () => import('./features/rewards/rewards.module').then(m => m.RewardsModule),
    canActivate: [AuthGuard]
  },
  {
    path: 'validation',
    loadChildren: () => import('./features/validation/validation.module').then(m => m.ValidationModule),
    canActivate: [ValidatorGuard]
  },
  {
    path: 'admin',
    loadChildren: () => import('./features/admin/admin.module').then(m => m.AdminModule),
    canActivate: [AdminGuard],
    data: { roles: [UserRole.ADMIN] }
  },
  {
    path: 'unauthorized',
    loadComponent: () => import('./shared/components/unauthorized/unauthorized.component').then(c => c.UnauthorizedComponent)
  },
  {
    path: '**',
    loadComponent: () => import('./shared/components/not-found/not-found.component').then(c => c.NotFoundComponent)
  }
];
