{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { dashboardRoutes } from './dashboard.routes';\nlet DashboardModule = class DashboardModule {};\nDashboardModule = __decorate([NgModule({\n  declarations: [DashboardComponent],\n  imports: [SharedModule, RouterModule.forChild(dashboardRoutes)]\n})], DashboardModule);\nexport { DashboardModule };", "map": {"version": 3, "names": ["NgModule", "RouterModule", "SharedModule", "DashboardComponent", "dashboardRoutes", "DashboardModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\nimport { SharedModule } from '../../shared/shared.module';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { dashboardRoutes } from './dashboard.routes';\n\n@NgModule({\n  declarations: [\n    DashboardComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(dashboardRoutes)\n  ]\n})\nexport class DashboardModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,eAAe,QAAQ,oBAAoB;AAW7C,IAAMC,eAAe,GAArB,MAAMA,eAAe,GAAI;AAAnBA,eAAe,GAAAC,UAAA,EAT3BN,QAAQ,CAAC;EACRO,YAAY,EAAE,CACZJ,kBAAkB,CACnB;EACDK,OAAO,EAAE,CACPN,YAAY,EACZD,YAAY,CAACQ,QAAQ,CAACL,eAAe,CAAC;CAEzC,CAAC,C,EACWC,eAAe,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}