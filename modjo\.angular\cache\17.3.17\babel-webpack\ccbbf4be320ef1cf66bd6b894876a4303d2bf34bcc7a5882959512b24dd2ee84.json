{"ast": null, "code": "export var PartnerBusinessType;\n(function (PartnerBusinessType) {\n  PartnerBusinessType[\"RESTAURANT\"] = \"restaurant\";\n  PartnerBusinessType[\"RETAIL\"] = \"retail\";\n  PartnerBusinessType[\"SERVICES\"] = \"services\";\n  PartnerBusinessType[\"ENTERTAINMENT\"] = \"entertainment\";\n  PartnerBusinessType[\"HEALTH\"] = \"health\";\n  PartnerBusinessType[\"EDUCATION\"] = \"education\";\n  PartnerBusinessType[\"SPORTS\"] = \"sports\";\n  PartnerBusinessType[\"CULTURE\"] = \"culture\";\n  PartnerBusinessType[\"OTHER\"] = \"other\";\n})(PartnerBusinessType || (PartnerBusinessType = {}));\nexport var PartnerVerificationStatus;\n(function (PartnerVerificationStatus) {\n  PartnerVerificationStatus[\"PENDING\"] = \"pending\";\n  PartnerVerificationStatus[\"VERIFIED\"] = \"verified\";\n  PartnerVerificationStatus[\"REJECTED\"] = \"rejected\";\n  PartnerVerificationStatus[\"SUSPENDED\"] = \"suspended\";\n})(PartnerVerificationStatus || (PartnerVerificationStatus = {}));\nexport var RewardCategory;\n(function (RewardCategory) {\n  RewardCategory[\"FOOD\"] = \"food\";\n  RewardCategory[\"SHOPPING\"] = \"shopping\";\n  RewardCategory[\"SERVICES\"] = \"services\";\n  RewardCategory[\"ENTERTAINMENT\"] = \"entertainment\";\n  RewardCategory[\"HEALTH\"] = \"health\";\n  RewardCategory[\"EDUCATION\"] = \"education\";\n  RewardCategory[\"SPORTS\"] = \"sports\";\n  RewardCategory[\"CULTURE\"] = \"culture\";\n  RewardCategory[\"DISCOUNT\"] = \"discount\";\n  RewardCategory[\"FREE_ITEM\"] = \"free_item\";\n  RewardCategory[\"OTHER\"] = \"other\";\n})(RewardCategory || (RewardCategory = {}));\nexport var RedemptionStatus;\n(function (RedemptionStatus) {\n  RedemptionStatus[\"PENDING\"] = \"pending\";\n  RedemptionStatus[\"CONFIRMED\"] = \"confirmed\";\n  RedemptionStatus[\"USED\"] = \"used\";\n  RedemptionStatus[\"EXPIRED\"] = \"expired\";\n  RedemptionStatus[\"CANCELLED\"] = \"cancelled\";\n})(RedemptionStatus || (RedemptionStatus = {}));", "map": {"version": 3, "names": ["PartnerBusinessType", "PartnerVerificationStatus", "RewardCategory", "RedemptionStatus"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\models\\partner.model.ts"], "sourcesContent": ["export interface PartnerBusiness {\n  id: string;\n  userId: string; // Lié au compte utilisateur\n  businessName: string;\n  businessType: PartnerBusinessType;\n  description: string;\n  address: string;\n  city: 'Monastir' | 'Sousse';\n  phone: string;\n  email: string;\n  logo?: string;\n  website?: string;\n  socialMedia?: SocialMediaLinks;\n  isActive: boolean;\n  verificationStatus: PartnerVerificationStatus;\n  joinedAt: Date;\n  lastActiveAt: Date;\n}\n\nexport enum PartnerBusinessType {\n  RESTAURANT = 'restaurant',\n  RETAIL = 'retail',\n  SERVICES = 'services',\n  ENTERTAINMENT = 'entertainment',\n  HEALTH = 'health',\n  EDUCATION = 'education',\n  SPORTS = 'sports',\n  CULTURE = 'culture',\n  OTHER = 'other'\n}\n\nexport enum PartnerVerificationStatus {\n  PENDING = 'pending',\n  VERIFIED = 'verified',\n  REJECTED = 'rejected',\n  SUSPENDED = 'suspended'\n}\n\nexport interface SocialMediaLinks {\n  facebook?: string;\n  instagram?: string;\n  twitter?: string;\n  linkedin?: string;\n}\n\nexport interface Reward {\n  id: string;\n  partnerId: string;\n  partnerName: string;\n  title: string;\n  description: string;\n  pointsCost: number;\n  originalPrice?: number;\n  discountPercentage?: number;\n  category: RewardCategory;\n  image?: string;\n  terms: string;\n  validFrom: Date;\n  validUntil: Date;\n  maxRedemptions?: number;\n  currentRedemptions: number;\n  isActive: boolean;\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport enum RewardCategory {\n  FOOD = 'food',\n  SHOPPING = 'shopping',\n  SERVICES = 'services',\n  ENTERTAINMENT = 'entertainment',\n  HEALTH = 'health',\n  EDUCATION = 'education',\n  SPORTS = 'sports',\n  CULTURE = 'culture',\n  DISCOUNT = 'discount',\n  FREE_ITEM = 'free_item',\n  OTHER = 'other'\n}\n\nexport interface RewardRedemption {\n  id: string;\n  rewardId: string;\n  userId: string;\n  userName: string;\n  partnerId: string;\n  partnerName: string;\n  pointsUsed: number;\n  redemptionCode: string;\n  status: RedemptionStatus;\n  redeemedAt: Date;\n  usedAt?: Date;\n  expiresAt: Date;\n  qrCode?: string;\n}\n\nexport enum RedemptionStatus {\n  PENDING = 'pending',\n  CONFIRMED = 'confirmed',\n  USED = 'used',\n  EXPIRED = 'expired',\n  CANCELLED = 'cancelled'\n}\n\nexport interface PartnerStats {\n  partnerId: string;\n  totalRewards: number;\n  activeRewards: number;\n  totalRedemptions: number;\n  totalPointsGenerated: number;\n  averageRedemptionValue: number;\n  popularRewards: PopularReward[];\n  monthlyStats: MonthlyPartnerStats[];\n  customerRetention: number; // pourcentage\n  rating: number;\n  reviewCount: number;\n}\n\nexport interface PopularReward {\n  rewardId: string;\n  title: string;\n  redemptionCount: number;\n  pointsCost: number;\n}\n\nexport interface MonthlyPartnerStats {\n  month: string; // YYYY-MM\n  redemptions: number;\n  pointsGenerated: number;\n  newCustomers: number;\n  revenue: number;\n}\n\nexport interface CreateRewardRequest {\n  title: string;\n  description: string;\n  pointsCost: number;\n  originalPrice?: number;\n  category: RewardCategory;\n  image?: string;\n  terms: string;\n  validUntil: Date;\n  maxRedemptions?: number;\n}\n\nexport interface UpdateRewardRequest {\n  title?: string;\n  description?: string;\n  pointsCost?: number;\n  originalPrice?: number;\n  terms?: string;\n  validUntil?: Date;\n  maxRedemptions?: number;\n  isActive?: boolean;\n}\n"], "mappings": "AAmBA,WAAYA,mBAUX;AAVD,WAAYA,mBAAmB;EAC7BA,mBAAA,6BAAyB;EACzBA,mBAAA,qBAAiB;EACjBA,mBAAA,yBAAqB;EACrBA,mBAAA,mCAA+B;EAC/BA,mBAAA,qBAAiB;EACjBA,mBAAA,2BAAuB;EACvBA,mBAAA,qBAAiB;EACjBA,mBAAA,uBAAmB;EACnBA,mBAAA,mBAAe;AACjB,CAAC,EAVWA,mBAAmB,KAAnBA,mBAAmB;AAY/B,WAAYC,yBAKX;AALD,WAAYA,yBAAyB;EACnCA,yBAAA,uBAAmB;EACnBA,yBAAA,yBAAqB;EACrBA,yBAAA,yBAAqB;EACrBA,yBAAA,2BAAuB;AACzB,CAAC,EALWA,yBAAyB,KAAzBA,yBAAyB;AAmCrC,WAAYC,cAYX;AAZD,WAAYA,cAAc;EACxBA,cAAA,iBAAa;EACbA,cAAA,yBAAqB;EACrBA,cAAA,yBAAqB;EACrBA,cAAA,mCAA+B;EAC/BA,cAAA,qBAAiB;EACjBA,cAAA,2BAAuB;EACvBA,cAAA,qBAAiB;EACjBA,cAAA,uBAAmB;EACnBA,cAAA,yBAAqB;EACrBA,cAAA,2BAAuB;EACvBA,cAAA,mBAAe;AACjB,CAAC,EAZWA,cAAc,KAAdA,cAAc;AA8B1B,WAAYC,gBAMX;AAND,WAAYA,gBAAgB;EAC1BA,gBAAA,uBAAmB;EACnBA,gBAAA,2BAAuB;EACvBA,gBAAA,iBAAa;EACbA,gBAAA,uBAAmB;EACnBA,gBAAA,2BAAuB;AACzB,CAAC,EANWA,gBAAgB,KAAhBA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}