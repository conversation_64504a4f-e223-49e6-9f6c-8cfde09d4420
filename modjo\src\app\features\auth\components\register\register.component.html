<div class="register-container">
  <mat-card class="register-card">
    <mat-card-header>
      <div class="register-header">
        <img src="assets/logo.png" alt="Modjo" class="logo">
        <h1><PERSON><PERSON><PERSON><PERSON>jo</h1>
        <p>C<PERSON>ez votre compte pour commencer</p>
      </div>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="registerForm" (ngSubmit)="onSubmit()" class="register-form">
        <!-- Name field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Nom complet</mat-label>
          <input matInput 
                 type="text" 
                 formControlName="name"
                 placeholder="Votre nom complet">
          <mat-icon matSuffix>person</mat-icon>
          <mat-error>{{ getFieldError('name') }}</mat-error>
        </mat-form-field>

        <!-- Email field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Adresse email</mat-label>
          <input matInput 
                 type="email" 
                 formControlName="email"
                 placeholder="<EMAIL>">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error>{{ getFieldError('email') }}</mat-error>
        </mat-form-field>

        <!-- Phone field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Téléphone (optionnel)</mat-label>
          <input matInput 
                 type="tel" 
                 formControlName="phone"
                 placeholder="12345678">
          <mat-icon matSuffix>phone</mat-icon>
          <mat-error>{{ getFieldError('phone') }}</mat-error>
        </mat-form-field>

        <!-- City field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Ville</mat-label>
          <mat-select formControlName="city">
            <mat-option *ngFor="let city of cities" [value]="city.value">
              {{ city.label }}
            </mat-option>
          </mat-select>
          <mat-error>{{ getFieldError('city') }}</mat-error>
        </mat-form-field>

        <!-- Role field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Type de compte</mat-label>
          <mat-select formControlName="role">
            <mat-option *ngFor="let role of roles" [value]="role.value">
              <div class="role-option">
                <span class="role-label">{{ role.label }}</span>
                <span class="role-description">{{ role.description }}</span>
              </div>
            </mat-option>
          </mat-select>
          <mat-error>{{ getFieldError('role') }}</mat-error>
        </mat-form-field>

        <!-- Password field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Mot de passe</mat-label>
          <input matInput 
                 [type]="hidePassword ? 'password' : 'text'"
                 formControlName="password"
                 placeholder="Votre mot de passe">
          <button mat-icon-button 
                  matSuffix 
                  type="button"
                  (click)="hidePassword = !hidePassword">
            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
          </button>
          <mat-error>{{ getFieldError('password') }}</mat-error>
        </mat-form-field>

        <!-- Confirm Password field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Confirmer le mot de passe</mat-label>
          <input matInput 
                 [type]="hideConfirmPassword ? 'password' : 'text'"
                 formControlName="confirmPassword"
                 placeholder="Confirmez votre mot de passe">
          <button mat-icon-button 
                  matSuffix 
                  type="button"
                  (click)="hideConfirmPassword = !hideConfirmPassword">
            <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>
          </button>
          <mat-error>{{ getFieldError('confirmPassword') }}</mat-error>
        </mat-form-field>

        <!-- Terms checkbox -->
        <div class="terms-section">
          <mat-checkbox formControlName="acceptTerms" class="terms-checkbox">
            J'accepte les <a href="#" class="link">conditions d'utilisation</a> 
            et la <a href="#" class="link">politique de confidentialité</a>
          </mat-checkbox>
          <div class="error-message" *ngIf="getFieldError('acceptTerms')">
            {{ getFieldError('acceptTerms') }}
          </div>
        </div>

        <!-- Submit button -->
        <button mat-raised-button 
                color="primary" 
                type="submit"
                class="full-width register-button"
                [disabled]="isLoading">
          <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
          <span *ngIf="!isLoading">Créer mon compte</span>
        </button>

        <!-- Login link -->
        <div class="login-link">
          <p>Déjà un compte? 
            <a routerLink="/auth/login" class="link">Se connecter</a>
          </p>
        </div>
      </form>
    </mat-card-content>
  </mat-card>
</div>
