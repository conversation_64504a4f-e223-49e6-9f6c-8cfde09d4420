import { Component } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { MatCardModule } from '@angular/material/card';
import { MatButtonModule } from '@angular/material/button';
import { MatIconModule } from '@angular/material/icon';

@Component({
  selector: 'app-not-found',
  standalone: true,
  imports: [CommonModule, RouterModule, MatCardModule, MatButtonModule, MatIconModule],
  template: `
    <div class="error-container">
      <mat-card class="error-card">
        <mat-card-content>
          <div class="error-content">
            <mat-icon class="error-icon">search_off</mat-icon>
            <h1>Page non trouvée</h1>
            <p>La page que vous recherchez n'existe pas ou a été déplacée.</p>
            <button mat-raised-button color="primary" routerLink="/dashboard">
              <mat-icon>home</mat-icon>
              Retour au tableau de bord
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  `,
  styles: [`
    .error-container {
      display: flex;
      justify-content: center;
      align-items: center;
      min-height: 100vh;
      padding: 16px;
      background-color: #f5f5f5;
    }

    .error-card {
      max-width: 400px;
      text-align: center;
    }

    .error-content {
      padding: 32px;
    }

    .error-icon {
      font-size: 4rem;
      color: #ff9800;
      margin-bottom: 16px;
    }

    h1 {
      margin: 0 0 16px 0;
      color: #333;
    }

    p {
      margin: 0 0 24px 0;
      color: #666;
    }
  `]
})
export class NotFoundComponent { }
