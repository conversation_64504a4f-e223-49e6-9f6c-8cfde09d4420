.header {
  background-color: #ffffff;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  position: sticky;
  top: 0;
  z-index: 100;
}

.header-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 15px 20px;
  max-width: 1200px;
  margin: 0 auto;
}

.logo a {
  font-size: 24px;
  font-weight: 700;
  color: #4a90e2;
  text-decoration: none;
}

.menu-toggle {
  display: none;
  flex-direction: column;
  justify-content: space-between;
  width: 30px;
  height: 21px;
  background: transparent;
  border: none;
  cursor: pointer;
  padding: 0;
}

.menu-toggle span {
  display: block;
  height: 3px;
  width: 100%;
  background-color: #333;
  border-radius: 3px;
}

.nav-list {
  display: flex;
  list-style: none;
  margin: 0;
  padding: 0;
}

.nav-list li {
  margin-left: 20px;
}

.nav-list a {
  color: #333;
  text-decoration: none;
  font-size: 16px;
  transition: color 0.3s;
}

.nav-list a:hover, .nav-list a.active {
  color: #4a90e2;
}

.user-info {
  display: flex;
  align-items: center;
}

.user-name {
  font-weight: 500;
  margin-right: 15px;
}

.user-points {
  background-color: #4a90e2;
  color: white;
  padding: 5px 10px;
  border-radius: 20px;
  font-size: 14px;
  font-weight: 500;
}

@media (max-width: 768px) {
  .menu-toggle {
    display: flex;
  }
  
  .nav {
    position: fixed;
    top: 60px;
    left: 0;
    width: 100%;
    background-color: white;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.1);
    transform: translateY(-100%);
    transition: transform 0.3s ease-in-out;
    z-index: 99;
  }
  
  .nav.open {
    transform: translateY(0);
  }
  
  .nav-list {
    flex-direction: column;
    padding: 20px;
  }
  
  .nav-list li {
    margin: 0 0 15px 0;
  }
  
  .user-info {
    display: none;
  }
}
