<div class="user-management-container">
  <h2>🧑‍💼 Gestion des Utilisateurs</h2>
  <p>Ajou<PERSON>, modifier, supprimer des utilisateurs et gérer leurs rôles</p>
  
  <!-- Filters and Search -->
  <div class="filters-section">
    <mat-form-field>
      <mat-label>Rechercher</mat-label>
      <input matInput [(ngModel)]="searchTerm" (input)="onSearchChange()" placeholder="Nom, email, organisation...">
      <mat-icon matSuffix>search</mat-icon>
    </mat-form-field>
    
    <mat-form-field>
      <mat-label>Rôle</mat-label>
      <mat-select [(value)]="selectedRole" (selectionChange)="onRoleChange()">
        <mat-option value="all">Tous les rôles</mat-option>
        <mat-option *ngFor="let role of userRoles" [value]="role">
          {{ getRoleDisplayName(role) }}
        </mat-option>
      </mat-select>
    </mat-form-field>
    
    <mat-form-field>
      <mat-label>Ville</mat-label>
      <mat-select [(value)]="selectedCity" (selectionChange)="onCityChange()">
        <mat-option value="all">Toutes les villes</mat-option>
        <mat-option *ngFor="let city of cities" [value]="city">{{ city }}</mat-option>
      </mat-select>
    </mat-form-field>
    
    <button mat-raised-button color="primary" (click)="addUser()">
      <mat-icon>person_add</mat-icon>
      Ajouter Utilisateur
    </button>
  </div>

  <!-- Users Table -->
  <mat-card class="users-table-card">
    <mat-card-content>
      <div class="table-container">
        <table mat-table [dataSource]="getPaginatedUsers()" class="users-table">
          <!-- Name Column -->
          <ng-container matColumnDef="name">
            <th mat-header-cell *matHeaderCellDef>Nom</th>
            <td mat-cell *matCellDef="let user">
              <div class="user-info">
                <strong>{{ user.name }}</strong>
                <div class="user-email">{{ user.email }}</div>
                <div *ngIf="user.organizationName" class="user-org">{{ user.organizationName }}</div>
              </div>
            </td>
          </ng-container>

          <!-- Role Column -->
          <ng-container matColumnDef="role">
            <th mat-header-cell *matHeaderCellDef>Rôle</th>
            <td mat-cell *matCellDef="let user">
              <span class="role-badge" [style.background-color]="getRoleColor(user.role) + '20'" 
                    [style.color]="getRoleColor(user.role)">
                {{ getRoleDisplayName(user.role) }}
              </span>
            </td>
          </ng-container>

          <!-- City Column -->
          <ng-container matColumnDef="city">
            <th mat-header-cell *matHeaderCellDef>Ville</th>
            <td mat-cell *matCellDef="let user">{{ user.city }}</td>
          </ng-container>

          <!-- Points Column -->
          <ng-container matColumnDef="points">
            <th mat-header-cell *matHeaderCellDef>Points</th>
            <td mat-cell *matCellDef="let user">
              <span *ngIf="user.role === 'user'">{{ user.points }}</span>
              <span *ngIf="user.role !== 'user'">-</span>
            </td>
          </ng-container>

          <!-- Status Column -->
          <ng-container matColumnDef="status">
            <th mat-header-cell *matHeaderCellDef>Statut</th>
            <td mat-cell *matCellDef="let user">
              <span class="status-badge" [style.background-color]="getStatusColor(user.isActive) + '20'" 
                    [style.color]="getStatusColor(user.isActive)">
                {{ getStatusText(user.isActive) }}
              </span>
            </td>
          </ng-container>

          <!-- Last Login Column -->
          <ng-container matColumnDef="lastLogin">
            <th mat-header-cell *matHeaderCellDef>Dernière connexion</th>
            <td mat-cell *matCellDef="let user">
              <span *ngIf="user.lastLoginAt">{{ user.lastLoginAt | timeAgo }}</span>
              <span *ngIf="!user.lastLoginAt">Jamais</span>
            </td>
          </ng-container>

          <!-- Actions Column -->
          <ng-container matColumnDef="actions">
            <th mat-header-cell *matHeaderCellDef>Actions</th>
            <td mat-cell *matCellDef="let user">
              <button mat-icon-button [matMenuTriggerFor]="userMenu">
                <mat-icon>more_vert</mat-icon>
              </button>
              <mat-menu #userMenu="matMenu">
                <button mat-menu-item (click)="editUser(user)">
                  <mat-icon>edit</mat-icon>
                  Modifier
                </button>
                <button mat-menu-item (click)="toggleUserStatus(user)">
                  <mat-icon>{{ user.isActive ? 'block' : 'check_circle' }}</mat-icon>
                  {{ user.isActive ? 'Désactiver' : 'Activer' }}
                </button>
                <button mat-menu-item (click)="deleteUser(user)" class="delete-action">
                  <mat-icon>delete</mat-icon>
                  Supprimer
                </button>
              </mat-menu>
            </td>
          </ng-container>

          <tr mat-header-row *matHeaderRowDef="['name', 'role', 'city', 'points', 'status', 'lastLogin', 'actions']"></tr>
          <tr mat-row *matRowDef="let row; columns: ['name', 'role', 'city', 'points', 'status', 'lastLogin', 'actions'];"></tr>
        </table>
      </div>

      <!-- Pagination -->
      <div class="pagination-container">
        <div class="pagination-info">
          Affichage {{ currentPage * pageSize + 1 }} - {{ getDisplayEnd() }}
          sur {{ filteredUsers.length }} utilisateurs
        </div>
        <div class="pagination-controls">
          <button mat-icon-button [disabled]="currentPage === 0" (click)="previousPage()">
            <mat-icon>chevron_left</mat-icon>
          </button>
          <span class="page-info">{{ currentPage + 1 }} / {{ getTotalPages() }}</span>
          <button mat-icon-button [disabled]="currentPage >= getTotalPages() - 1" (click)="nextPage()">
            <mat-icon>chevron_right</mat-icon>
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
