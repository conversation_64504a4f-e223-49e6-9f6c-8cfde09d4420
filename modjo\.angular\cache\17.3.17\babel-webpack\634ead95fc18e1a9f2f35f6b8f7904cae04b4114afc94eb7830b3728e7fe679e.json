{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { rewardsRoutes } from './rewards.routes';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class RewardsModule {\n  static {\n    this.ɵfac = function RewardsModule_Factory(t) {\n      return new (t || RewardsModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: RewardsModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule.forChild(rewardsRoutes),\n      // Material modules\n      MatCardModule, MatButtonModule, MatIconModule, MatChipsModule, MatTabsModule, MatDialogModule, MatSnackBarModule, MatProgressSpinnerModule, MatBadgeModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(RewardsModule, {\n    imports: [CommonModule, i1.RouterModule,\n    // Material modules\n    MatCardModule, MatButtonModule, MatIconModule, MatChipsModule, MatTabsModule, MatDialogModule, MatSnackBarModule, MatProgressSpinnerModule, MatBadgeModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatChipsModule", "MatTabsModule", "MatDialogModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatBadgeModule", "rewardsRoutes", "RewardsModule", "<PERSON><PERSON><PERSON><PERSON>", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\rewards\\rewards.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatBadgeModule } from '@angular/material/badge';\n\nimport { RewardsListComponent } from './components/rewards-list/rewards-list.component';\nimport { RewardDetailComponent } from './components/reward-detail/reward-detail.component';\nimport { ExchangeHistoryComponent } from './components/exchange-history/exchange-history.component';\nimport { rewardsRoutes } from './rewards.routes';\n\n@NgModule({\n  declarations: [\n    // RewardsListComponent,\n    // RewardDetailComponent,\n    // ExchangeHistoryComponent\n  ],\n  imports: [\n    CommonModule,\n    RouterModule.forChild(rewardsRoutes),\n    \n    // Material modules\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatChipsModule,\n    MatTabsModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule,\n    MatBadgeModule\n  ]\n})\nexport class RewardsModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,cAAc,QAAQ,yBAAyB;AAKxD,SAASC,aAAa,QAAQ,kBAAkB;;;AAwBhD,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBAftBZ,YAAY,EACZC,YAAY,CAACY,QAAQ,CAACF,aAAa,CAAC;MAEpC;MACAT,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,EACxBC,cAAc;IAAA;EAAA;;;2EAGLE,aAAa;IAAAE,OAAA,GAftBd,YAAY,EAAAe,EAAA,CAAAd,YAAA;IAGZ;IACAC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,cAAc,EACdC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,EACxBC,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}