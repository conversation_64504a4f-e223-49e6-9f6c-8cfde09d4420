<div class="dashboard-container" *ngIf="user">
  <!-- Welcome section -->
  <div class="welcome-section slide-up">
    <div class="welcome-content">
      <h1 class="bounce-in">{{ getGreeting() }}, {{ user.name }}!</h1>
      <p class="user-info fade-in">
        <mat-icon>location_on</mat-icon>
        {{ user.city }} • {{ getRoleDisplayName(user.role) }}
      </p>
    </div>
    <div class="points-badge glow">
      <mat-icon>stars</mat-icon>
      <span>{{ user.points }} points</span>
    </div>
  </div>

  <!-- Stats cards -->
  <div class="stats-section">
    <h2 class="section-title slide-in-left">📊 Mes statistiques</h2>
    <div class="stats-grid">
      <mat-card *ngFor="let stat of stats; let i = index"
                class="stat-card floating-card"
                [style.animation-delay]="(i * 0.1) + 's'">
        <mat-card-content>
          <div class="stat-content">
            <div class="stat-icon" [style.color]="stat.color">
              <mat-icon>{{ stat.icon }}</mat-icon>
            </div>
            <div class="stat-info">
              <h3 class="counter-animation">{{ stat.value }}</h3>
              <p>{{ stat.title }}</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Quick actions -->
  <div class="actions-section">
    <h2>Actions rapides</h2>
    <div class="actions-grid">
      <mat-card *ngFor="let action of quickActions" 
                class="action-card" 
                [routerLink]="action.route">
        <mat-card-content>
          <div class="action-content">
            <mat-icon [color]="action.color" class="action-icon">
              {{ action.icon }}
            </mat-icon>
            <h3>{{ action.title }}</h3>
            <p>{{ action.description }}</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Recent activity -->
  <div class="activity-section" *ngIf="user.history && user.history.length > 0">
    <h2>Activité récente</h2>
    <mat-card class="activity-card">
      <mat-card-content>
        <div class="activity-list">
          <div *ngFor="let transaction of user.history.slice(-5)" class="activity-item">
            <div class="activity-icon">
              <mat-icon [class]="'activity-' + transaction.type">
                {{ getActivityIcon(transaction.type) }}
              </mat-icon>
            </div>
            <div class="activity-info">
              <p class="activity-description">{{ transaction.description }}</p>
              <p class="activity-date">{{ transaction.timestamp | date:'short' }}</p>
            </div>
            <div class="activity-points" [class]="transaction.points > 0 ? 'positive' : 'negative'">
              {{ transaction.points > 0 ? '+' : '' }}{{ transaction.points }} pts
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Empty state for new users -->
  <div class="empty-state" *ngIf="!user.history || user.history.length === 0">
    <mat-card class="empty-card">
      <mat-card-content>
        <div class="empty-content">
          <mat-icon class="empty-icon">explore</mat-icon>
          <h3>Commencez votre aventure!</h3>
          <p>Scannez votre premier code QR ou participez à une activité communautaire pour gagner vos premiers points.</p>
          <button mat-raised-button color="primary" routerLink="/qr-scanner">
            <mat-icon>qr_code_scanner</mat-icon>
            Scanner un QR Code
          </button>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
