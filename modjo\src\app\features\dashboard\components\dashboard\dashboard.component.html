<div class="dashboard-container" *ngIf="user">
  <!-- En-tête -->
  <div class="dashboard-header">
    <div class="user-welcome">
      <div class="user-avatar">
        <mat-icon>school</mat-icon>
      </div>
      <div class="user-info">
        <h2>Bonjour {{ user.name }} ! 👋</h2>
        <p class="user-role">Élève • {{ user.city }}</p>
      </div>
    </div>

    <div class="header-actions">
      <div class="points-card">
        <mat-icon class="points-icon">stars</mat-icon>
        <div class="points-info">
          <span class="points-value">{{ user.points }}</span>
          <span class="points-label">Points</span>
        </div>
      </div>

      <button mat-icon-button class="notifications-btn">
        <mat-icon>notifications</mat-icon>
      </button>
    </div>
  </div>

  <!-- Scanner QR -->
  <div class="qr-scan-section">
    <div class="qr-scan-card">
      <div class="qr-scan-content">
        <div class="qr-scan-icon">
          <mat-icon>qr_code_scanner</mat-icon>
        </div>
        <div class="qr-scan-text">
          <h3>Scanner un QR Code KnowMe</h3>
          <p>Découvre un prestataire et gagne des points !</p>
        </div>
        <button mat-raised-button color="primary" class="qr-scan-btn">
          <mat-icon>camera_alt</mat-icon>
          Scanner
        </button>
      </div>
    </div>
  </div>

  <!-- Progression -->
  <div class="progress-section">
    <div class="progress-card">
      <h3>Progression vers ta prochaine récompense</h3>
      <div class="reward-progress" *ngIf="nextReward">
        <div class="progress-info">
          <div class="next-reward">
            <img [src]="nextReward.imageUrl" [alt]="nextReward.title" class="reward-icon">
            <div class="reward-details">
              <h4>{{ nextReward.title }}</h4>
              <p>{{ nextReward.description }}</p>
            </div>
          </div>
          <div class="points-needed">
            <span class="points-remaining">{{ getPointsToNextReward() }}</span>
            <span class="points-label">points restants</span>
          </div>
        </div>
        <div class="progress-bar">
          <div class="progress-fill" [style.width]="getProgressPercentage() + '%'"></div>
        </div>
        <div class="progress-text">
          <span>{{ user.points }} / {{ nextReward.pointsRequired }} points</span>
        </div>
        <div class="motivation-message">
          <p>{{ getMotivationMessage() }}</p>
        </div>
      </div>
    </div>
  </div>

  <!-- Récompenses -->
  <div class="rewards-section">
    <h3 class="section-subtitle">
      <mat-icon>card_giftcard</mat-icon>
      Récompenses disponibles
    </h3>
    <div class="rewards-grid">
      <div *ngFor="let reward of availableRewards" class="reward-card"
           [class.affordable]="user.points >= reward.pointsRequired">
        <div class="reward-image">
          <img [src]="reward.imageUrl" [alt]="reward.title">
          <div class="reward-badge" *ngIf="user.points >= reward.pointsRequired">
            <mat-icon>check_circle</mat-icon>
          </div>
        </div>
        <div class="reward-content">
          <h4>{{ reward.title }}</h4>
          <p class="reward-description">{{ reward.description }}</p>
          <div class="reward-partner">
            <mat-icon>store</mat-icon>
            <span>{{ reward.partnerName }}</span>
          </div>
          <div class="reward-points">
            <mat-icon>stars</mat-icon>
            <span>{{ reward.pointsRequired }} points</span>
          </div>
          <button mat-raised-button
                  [color]="user.points >= reward.pointsRequired ? 'primary' : ''"
                  [disabled]="user.points < reward.pointsRequired"
                  class="exchange-btn">
            {{ user.points >= reward.pointsRequired ? 'Échanger' : 'Pas assez de points' }}
          </button>
        </div>
      </div>
    </div>
  </div>
</div>


<!-- Floating QR Scanner Button -->
<button mat-fab class="floating-qr-btn" color="primary">
  <mat-icon>qr_code_scanner</mat-icon>
</button>
