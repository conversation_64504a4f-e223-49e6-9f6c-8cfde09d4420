<div class="dashboard-container" *ngIf="user">
  <!-- En-tête avec informations utilisateur -->
  <div class="dashboard-header fade-in">
    <div class="user-welcome">
      <div class="user-avatar">
        <mat-icon>school</mat-icon>
      </div>
      <div class="user-info">
        <h2>Bonjour {{ user.name }} ! 👋</h2>
        <p class="user-role">Élève • {{ user.city }}</p>
      </div>
    </div>

    <div class="header-actions">
      <div class="points-display">
        <div class="points-card">
          <mat-icon class="points-icon">stars</mat-icon>
          <div class="points-info">
            <span class="points-value">{{ user.points }}</span>
            <span class="points-label">Points</span>
          </div>
        </div>
      </div>

      <!-- Notifications -->
      <button mat-icon-button class="notifications-btn"
              [matBadge]="unreadNotifications"
              matBadgeColor="warn"
              [matBadgeHidden]="unreadNotifications === 0"
              (click)="toggleNotifications()">
        <mat-icon>notifications</mat-icon>
      </button>

      <!-- Profil -->
      <button mat-icon-button [matMenuTriggerFor]="profileMenu">
        <mat-icon>account_circle</mat-icon>
      </button>
      <mat-menu #profileMenu="matMenu">
        <button mat-menu-item (click)="openProfile()">
          <mat-icon>person</mat-icon>
          <span>Mon Profil</span>
        </button>
        <button mat-menu-item (click)="logout()">
          <mat-icon>logout</mat-icon>
          <span>Déconnexion</span>
        </button>
      </mat-menu>
    </div>
  </div>

  <!-- Section principale -->
  <div class="main-section">
    <!-- Bouton Scanner QR - Bien visible -->
    <div class="qr-scan-section">
      <mat-card class="qr-scan-card">
        <mat-card-content>
          <div class="qr-scan-content">
            <div class="qr-scan-icon">
              <mat-icon>qr_code_scanner</mat-icon>
            </div>
            <div class="qr-scan-text">
              <h3>Scanner un QR Code KnowMe</h3>
              <p>Découvre un prestataire et gagne des points !</p>
            </div>
            <button mat-raised-button color="primary" class="qr-scan-btn" (click)="openQRScanner()">
              <mat-icon>camera_alt</mat-icon>
              Scanner
            </button>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Jauge d'avancement vers la prochaine récompense -->
    <div class="progress-section">
      <mat-card class="progress-card">
        <mat-card-content>
          <h3>Progression vers ta prochaine récompense</h3>
          <div class="reward-progress">
            <div class="progress-info">
              <div class="next-reward" *ngIf="nextReward">
                <img [src]="nextReward.imageUrl" [alt]="nextReward.title" class="reward-icon">
                <div class="reward-details">
                  <h4>{{ nextReward.title }}</h4>
                  <p>{{ nextReward.description }}</p>
                </div>
              </div>
              <div class="points-needed" *ngIf="nextReward">
                <span class="points-remaining">{{ getPointsToNextReward() }}</span>
                <span class="points-label">points restants</span>
              </div>
            </div>
            <div class="progress-bar">
              <div class="progress-fill" [style.width]="getProgressPercentage() + '%'"></div>
            </div>
            <div class="progress-text" *ngIf="nextReward">
              <span>{{ user.points }} / {{ nextReward.pointsRequired }} points</span>
            </div>
            <div class="motivation-message">
              <p>{{ getMotivationMessage() }}</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Offres/Récompenses disponibles -->
    <div class="rewards-section">
      <h3 class="section-subtitle">
        <mat-icon>card_giftcard</mat-icon>
        Récompenses disponibles
      </h3>
      <div class="rewards-grid">
        <mat-card *ngFor="let reward of availableRewards" class="reward-card"
                  [class.affordable]="user.points >= reward.pointsRequired">
          <div class="reward-image">
            <img [src]="reward.imageUrl" [alt]="reward.title">
            <div class="reward-badge" *ngIf="user.points >= reward.pointsRequired">
              <mat-icon>check_circle</mat-icon>
            </div>
          </div>
          <mat-card-content>
            <h4>{{ reward.title }}</h4>
            <p class="reward-description">{{ reward.description }}</p>
            <div class="reward-partner">
              <mat-icon>store</mat-icon>
              <span>{{ reward.partnerName }}</span>
            </div>
            <div class="reward-points">
              <mat-icon>stars</mat-icon>
              <span>{{ reward.pointsRequired }} points</span>
            </div>
            <button mat-raised-button
                    [color]="user.points >= reward.pointsRequired ? 'primary' : ''"
                    [disabled]="user.points < reward.pointsRequired"
                    class="exchange-btn"
                    (click)="exchangeReward(reward)">
              {{ user.points >= reward.pointsRequired ? 'Échanger' : 'Pas assez de points' }}
            </button>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  </div>

  <!-- Section secondaire -->
  <div class="secondary-section">
    <!-- Historique des bonnes actions -->
    <div class="history-section">
      <mat-card class="history-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>history</mat-icon>
            Historique de mes actions
          </mat-card-title>
          <div class="history-filters">
            <mat-form-field appearance="outline" class="filter-field">
              <mat-label>Période</mat-label>
              <mat-select [(value)]="selectedPeriod" (selectionChange)="filterHistory()">
                <mat-option value="week">Cette semaine</mat-option>
                <mat-option value="month">Ce mois</mat-option>
                <mat-option value="year">Cette année</mat-option>
                <mat-option value="all">Tout</mat-option>
              </mat-select>
            </mat-form-field>
          </div>
        </mat-card-header>
        <mat-card-content>
          <div class="history-list" *ngIf="filteredHistory.length > 0; else noHistory">
            <div *ngFor="let action of filteredHistory" class="history-item">
              <div class="action-icon">
                <mat-icon [style.color]="getActionColor(action.type)">
                  {{ getActionIcon(action.type) }}
                </mat-icon>
              </div>
              <div class="action-details">
                <h4>{{ action.description }}</h4>
                <div class="action-meta">
                  <span class="action-date">{{ action.timestamp | date:'short' }}</span>
                  <span class="action-location" *ngIf="action.location">
                    <mat-icon>location_on</mat-icon>
                    {{ action.location }}
                  </span>
                </div>
                <div class="action-provider" *ngIf="action.providerName">
                  <mat-icon>business</mat-icon>
                  <span>{{ action.providerName }}</span>
                </div>
              </div>
              <div class="action-points">
                <span class="points-earned">+{{ action.points }}</span>
                <span class="points-label">pts</span>
              </div>
            </div>
          </div>
          <ng-template #noHistory>
            <div class="no-history">
              <mat-icon>sentiment_neutral</mat-icon>
              <p>Aucune action pour cette période</p>
              <button mat-raised-button color="primary" (click)="openQRScanner()">
                Commencer maintenant
              </button>
            </div>
          </ng-template>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Statistiques mensuelles -->
    <div class="stats-section">
      <mat-card class="stats-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>analytics</mat-icon>
            Mes statistiques ce mois
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="stats-grid">
            <div class="stat-item">
              <div class="stat-icon">
                <mat-icon>trending_up</mat-icon>
              </div>
              <div class="stat-content">
                <span class="stat-value">{{ monthlyStats.pointsEarned }}</span>
                <span class="stat-label">Points gagnés</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <mat-icon>trending_down</mat-icon>
              </div>
              <div class="stat-content">
                <span class="stat-value">{{ monthlyStats.pointsSpent }}</span>
                <span class="stat-label">Points dépensés</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <mat-icon>qr_code_scanner</mat-icon>
              </div>
              <div class="stat-content">
                <span class="stat-value">{{ monthlyStats.scansCount }}</span>
                <span class="stat-label">QR scannés</span>
              </div>
            </div>
            <div class="stat-item">
              <div class="stat-icon">
                <mat-icon>verified</mat-icon>
              </div>
              <div class="stat-content">
                <span class="stat-value">{{ monthlyStats.validationsCount }}</span>
                <span class="stat-label">Actions validées</span>
              </div>
            </div>
          </div>

          <!-- Graphique simple des points -->
          <div class="points-chart">
            <h4>Évolution des points</h4>
            <div class="chart-container">
              <div class="chart-bars">
                <div *ngFor="let week of weeklyPointsData" class="chart-bar">
                  <div class="bar" [style.height]="(week.points / maxWeeklyPoints * 100) + '%'"></div>
                  <span class="bar-label">S{{ week.week }}</span>
                </div>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>

    <!-- Notifications -->
    <div class="notifications-section" *ngIf="showNotifications">
      <mat-card class="notifications-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>notifications</mat-icon>
            Notifications
          </mat-card-title>
          <button mat-icon-button (click)="toggleNotifications()">
            <mat-icon>close</mat-icon>
          </button>
        </mat-card-header>
        <mat-card-content>
          <div class="notifications-list" *ngIf="notifications.length > 0; else noNotifications">
            <div *ngFor="let notification of notifications"
                 class="notification-item"
                 [class.unread]="!notification.read">
              <div class="notification-icon">
                <mat-icon [style.color]="getNotificationColor(notification.type)">
                  {{ getNotificationIcon(notification.type) }}
                </mat-icon>
              </div>
              <div class="notification-content">
                <h4>{{ notification.title }}</h4>
                <p>{{ notification.message }}</p>
                <span class="notification-time">{{ notification.timestamp | timeAgo }}</span>
              </div>
              <button mat-icon-button *ngIf="!notification.read" (click)="markAsRead(notification)">
                <mat-icon>check</mat-icon>
              </button>
            </div>
          </div>
          <ng-template #noNotifications>
            <div class="no-notifications">
              <mat-icon>notifications_off</mat-icon>
              <p>Aucune notification</p>
            </div>
          </ng-template>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Tableau de motivation et badges -->
  <div class="motivation-section">
    <mat-card class="motivation-card">
      <mat-card-header>
        <mat-card-title>
          <mat-icon>emoji_events</mat-icon>
          Mes achievements
        </mat-card-title>
      </mat-card-header>
      <mat-card-content>
        <div class="badges-grid">
          <div *ngFor="let badge of achievements"
               class="badge-item"
               [class.earned]="badge.earned"
               [class.locked]="!badge.earned">
            <div class="badge-icon">
              <mat-icon [style.color]="badge.earned ? badge.color : '#ccc'">
                {{ badge.icon }}
              </mat-icon>
            </div>
            <div class="badge-info">
              <h4>{{ badge.title }}</h4>
              <p>{{ badge.description }}</p>
              <div class="badge-progress" *ngIf="!badge.earned">
                <div class="progress-bar">
                  <div class="progress-fill" [style.width]="badge.progress + '%'"></div>
                </div>
                <span>{{ badge.current }}/{{ badge.target }}</span>
              </div>
            </div>
          </div>
        </div>
      </mat-card-content>
    </mat-card>
  </div>

  <!-- Accès rapide aux actions -->
  <div class="quick-actions">
    <h3 class="section-subtitle">
      <mat-icon>flash_on</mat-icon>
      Actions rapides
    </h3>
    <div class="quick-actions-grid">
      <button mat-raised-button color="primary" class="quick-action-btn" (click)="openQRScanner()">
        <mat-icon>qr_code_scanner</mat-icon>
        <span>Scanner QR</span>
      </button>
      <button mat-raised-button class="quick-action-btn" routerLink="/rewards">
        <mat-icon>card_giftcard</mat-icon>
        <span>Mes récompenses</span>
      </button>
      <button mat-raised-button class="quick-action-btn" (click)="openProfile()">
        <mat-icon>person</mat-icon>
        <span>Mon profil</span>
      </button>
      <button mat-raised-button class="quick-action-btn" routerLink="/leaderboard">
        <mat-icon>leaderboard</mat-icon>
        <span>Classement</span>
      </button>
    </div>
  </div>
</div>

<!-- Floating QR Scanner Button -->
<button mat-fab
        class="floating-qr-btn"
        color="primary"
        (click)="openQRScanner()"
        matTooltip="Scanner un QR Code">
  <mat-icon>qr_code_scanner</mat-icon>
</button>
