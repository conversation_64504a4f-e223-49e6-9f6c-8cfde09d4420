{"ast": null, "code": "import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function bufferWhen(closingSelector) {\n  return operate((source, subscriber) => {\n    let buffer = null;\n    let closingSubscriber = null;\n    const openBuffer = () => {\n      closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n      const b = buffer;\n      buffer = [];\n      b && subscriber.next(b);\n      innerFrom(closingSelector()).subscribe(closingSubscriber = createOperatorSubscriber(subscriber, openBuffer, noop));\n    };\n    openBuffer();\n    source.subscribe(createOperatorSubscriber(subscriber, value => buffer === null || buffer === void 0 ? void 0 : buffer.push(value), () => {\n      buffer && subscriber.next(buffer);\n      subscriber.complete();\n    }, undefined, () => buffer = closingSubscriber = null));\n  });\n}", "map": {"version": 3, "names": ["operate", "noop", "createOperatorSubscriber", "innerFrom", "bufferWhen", "closingSelector", "source", "subscriber", "buffer", "closingSubscriber", "openBuffer", "unsubscribe", "b", "next", "subscribe", "value", "push", "complete", "undefined"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/rxjs/dist/esm/internal/operators/bufferWhen.js"], "sourcesContent": ["import { operate } from '../util/lift';\nimport { noop } from '../util/noop';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { innerFrom } from '../observable/innerFrom';\nexport function bufferWhen(closingSelector) {\n    return operate((source, subscriber) => {\n        let buffer = null;\n        let closingSubscriber = null;\n        const openBuffer = () => {\n            closingSubscriber === null || closingSubscriber === void 0 ? void 0 : closingSubscriber.unsubscribe();\n            const b = buffer;\n            buffer = [];\n            b && subscriber.next(b);\n            innerFrom(closingSelector()).subscribe((closingSubscriber = createOperatorSubscriber(subscriber, openBuffer, noop)));\n        };\n        openBuffer();\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => buffer === null || buffer === void 0 ? void 0 : buffer.push(value), () => {\n            buffer && subscriber.next(buffer);\n            subscriber.complete();\n        }, undefined, () => (buffer = closingSubscriber = null)));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,cAAc;AACtC,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,SAAS,QAAQ,yBAAyB;AACnD,OAAO,SAASC,UAAUA,CAACC,eAAe,EAAE;EACxC,OAAOL,OAAO,CAAC,CAACM,MAAM,EAAEC,UAAU,KAAK;IACnC,IAAIC,MAAM,GAAG,IAAI;IACjB,IAAIC,iBAAiB,GAAG,IAAI;IAC5B,MAAMC,UAAU,GAAGA,CAAA,KAAM;MACrBD,iBAAiB,KAAK,IAAI,IAAIA,iBAAiB,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,iBAAiB,CAACE,WAAW,CAAC,CAAC;MACrG,MAAMC,CAAC,GAAGJ,MAAM;MAChBA,MAAM,GAAG,EAAE;MACXI,CAAC,IAAIL,UAAU,CAACM,IAAI,CAACD,CAAC,CAAC;MACvBT,SAAS,CAACE,eAAe,CAAC,CAAC,CAAC,CAACS,SAAS,CAAEL,iBAAiB,GAAGP,wBAAwB,CAACK,UAAU,EAAEG,UAAU,EAAET,IAAI,CAAE,CAAC;IACxH,CAAC;IACDS,UAAU,CAAC,CAAC;IACZJ,MAAM,CAACQ,SAAS,CAACZ,wBAAwB,CAACK,UAAU,EAAGQ,KAAK,IAAKP,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACQ,IAAI,CAACD,KAAK,CAAC,EAAE,MAAM;MACvIP,MAAM,IAAID,UAAU,CAACM,IAAI,CAACL,MAAM,CAAC;MACjCD,UAAU,CAACU,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAEC,SAAS,EAAE,MAAOV,MAAM,GAAGC,iBAAiB,GAAG,IAAK,CAAC,CAAC;EAC7D,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}