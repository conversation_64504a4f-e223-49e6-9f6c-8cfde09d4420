{"ast": null, "code": "import { Observable } from '../Observable';\nimport { isFunction } from './isFunction';\nexport function isObservable(obj) {\n  return !!obj && (obj instanceof Observable || isFunction(obj.lift) && isFunction(obj.subscribe));\n}", "map": {"version": 3, "names": ["Observable", "isFunction", "isObservable", "obj", "lift", "subscribe"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/rxjs/dist/esm/internal/util/isObservable.js"], "sourcesContent": ["import { Observable } from '../Observable';\nimport { isFunction } from './isFunction';\nexport function isObservable(obj) {\n    return !!obj && (obj instanceof Observable || (isFunction(obj.lift) && isFunction(obj.subscribe)));\n}\n"], "mappings": "AAAA,SAASA,UAAU,QAAQ,eAAe;AAC1C,SAASC,UAAU,QAAQ,cAAc;AACzC,OAAO,SAASC,YAAYA,CAACC,GAAG,EAAE;EAC9B,OAAO,CAAC,CAACA,GAAG,KAAKA,GAAG,YAAYH,UAAU,IAAKC,UAAU,CAACE,GAAG,CAACC,IAAI,CAAC,IAAIH,UAAU,CAACE,GAAG,CAACE,SAAS,CAAE,CAAC;AACtG", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}