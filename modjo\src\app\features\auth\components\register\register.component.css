.register-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 100vh;
  padding: 16px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.register-card {
  width: 100%;
  max-width: 500px;
  padding: 24px;
  border-radius: 16px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  max-height: 90vh;
  overflow-y: auto;
}

.register-header {
  text-align: center;
  margin-bottom: 24px;
}

.logo {
  width: 64px;
  height: 64px;
  margin-bottom: 16px;
}

.register-header h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 2rem;
  font-weight: 600;
}

.register-header p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.register-form {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.full-width {
  width: 100%;
}

.role-option {
  display: flex;
  flex-direction: column;
  padding: 4px 0;
}

.role-label {
  font-weight: 500;
  color: #333;
}

.role-description {
  font-size: 0.8rem;
  color: #666;
  margin-top: 2px;
}

.terms-section {
  margin: 8px 0;
}

.terms-checkbox {
  font-size: 0.9rem;
}

.error-message {
  color: #f44336;
  font-size: 0.75rem;
  margin-top: 4px;
}

.register-button {
  height: 48px;
  font-size: 1rem;
  font-weight: 500;
  margin-top: 8px;
}

.login-link {
  text-align: center;
  margin-top: 16px;
}

.login-link p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.link {
  color: #667eea;
  text-decoration: none;
  font-weight: 500;
}

.link:hover {
  text-decoration: underline;
}

/* Mobile responsive */
@media (max-width: 480px) {
  .register-container {
    padding: 8px;
  }
  
  .register-card {
    padding: 16px;
    max-height: 95vh;
  }
  
  .logo {
    width: 48px;
    height: 48px;
  }
  
  .register-header h1 {
    font-size: 1.5rem;
  }
  
  .register-form {
    gap: 12px;
  }
}
