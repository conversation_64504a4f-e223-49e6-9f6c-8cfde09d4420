import { Routes } from '@angular/router';
import { AdminDashboardComponent } from './components/admin-dashboard/admin-dashboard.component';
import { UserManagementComponent } from './components/user-management/user-management.component';
import { PartnerManagementComponent } from './components/partner-management/partner-management.component';
import { ProviderManagementComponent } from './components/provider-management/provider-management.component';
import { ValidatorManagementComponent } from './components/validator-management/validator-management.component';
import { SystemConfigComponent } from './components/system-config/system-config.component';
import { AuditLogComponent } from './components/audit-log/audit-log.component';

export const adminDashboardRoutes: Routes = [
  {
    path: '',
    component: AdminDashboardComponent,
    children: [
      { path: '', redirectTo: 'overview', pathMatch: 'full' },
      { path: 'overview', component: AdminDashboardComponent },
      { path: 'users', component: UserManagementComponent },
      { path: 'partners', component: PartnerManagementComponent },
      { path: 'providers', component: ProviderManagementComponent },
      { path: 'validators', component: ValidatorManagementComponent },
      { path: 'config', component: SystemConfigComponent },
      { path: 'audit', component: AuditLogComponent }
    ]
  }
];
