{"ast": null, "code": "import { DashboardComponent } from './components/dashboard/dashboard.component';\nexport const dashboardRoutes = [{\n  path: '',\n  component: DashboardComponent\n}];", "map": {"version": 3, "names": ["DashboardComponent", "dashboardRoutes", "path", "component"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\dashboard.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\n\nexport const dashboardRoutes: Routes = [\n  {\n    path: '',\n    component: DashboardComponent\n  }\n];\n"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,4CAA4C;AAE/E,OAAO,MAAMC,eAAe,GAAW,CACrC;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}