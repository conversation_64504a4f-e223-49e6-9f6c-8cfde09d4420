{"version": 3, "file": "default-node_modules_angular_material_fesm2022_input_mjs-node_modules_angular_material_fesm20-41aefd.js", "mappings": ";;;;;;;;;;;;;;;;;;;AAAoC;AACuB;AAChB;AACqB;;AAEhE;AACA;AACA;AACA;AACA;AACA,MAAMS,6BAA6B,GAAIC,CAAC,IAAK;EACzC,IAAIA,CAAC,YAAYC,UAAU,IAAID,CAAC,CAACE,OAAO,KAAK,oCAAoC,EAAE;IAC/EC,OAAO,CAACC,KAAK,CAAC,GAAGJ,CAAC,CAACE,OAAO,8IAA8I,CAAC;EAC7K;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA,MAAMG,6BAA6B,CAAC;EAChCC,WAAWA,CACX;EACAC,IAAI,EAAE;IACF,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAACC,UAAU,GAAG,IAAId,yCAAO,CAAC,CAAC;IAC/B;IACA,IAAI,CAACe,cAAc,GAAG,IAAIf,yCAAO,CAAC,CAAC;IACnC;IACA,IAAI,CAACgB,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACpC,IAAI,OAAOC,cAAc,KAAK,WAAW,EAAE;MACvC,IAAI,CAACC,eAAe,GAAG,IAAID,cAAc,CAACE,OAAO,IAAI,IAAI,CAACL,cAAc,CAACM,IAAI,CAACD,OAAO,CAAC,CAAC;IAC3F;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIE,OAAOA,CAACC,MAAM,EAAE;IACZ,IAAI,CAAC,IAAI,CAACP,mBAAmB,CAACQ,GAAG,CAACD,MAAM,CAAC,EAAE;MACvC,IAAI,CAACP,mBAAmB,CAACS,GAAG,CAACF,MAAM,EAAE,IAAItB,4CAAU,CAACyB,QAAQ,IAAI;QAC5D,MAAMC,YAAY,GAAG,IAAI,CAACZ,cAAc,CAACa,SAAS,CAACF,QAAQ,CAAC;QAC5D,IAAI,CAACP,eAAe,EAAEG,OAAO,CAACC,MAAM,EAAE;UAAEM,GAAG,EAAE,IAAI,CAAChB;QAAK,CAAC,CAAC;QACzD,OAAO,MAAM;UACT,IAAI,CAACM,eAAe,EAAEW,SAAS,CAACP,MAAM,CAAC;UACvCI,YAAY,CAACI,WAAW,CAAC,CAAC;UAC1B,IAAI,CAACf,mBAAmB,CAACgB,MAAM,CAACT,MAAM,CAAC;QAC3C,CAAC;MACL,CAAC,CAAC,CAACU,IAAI,CAAC/B,sDAAM,CAACkB,OAAO,IAAIA,OAAO,CAACc,IAAI,CAACC,KAAK,IAAIA,KAAK,CAACZ,MAAM,KAAKA,MAAM,CAAC,CAAC;MACzE;MACA;MACA;MACApB,2DAAW,CAAC;QAAEiC,UAAU,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAK,CAAC,CAAC,EAAEjC,yDAAS,CAAC,IAAI,CAACU,UAAU,CAAC,CAAC,CAAC;IAChF;IACA,OAAO,IAAI,CAACE,mBAAmB,CAACsB,GAAG,CAACf,MAAM,CAAC;EAC/C;EACA;EACAgB,OAAOA,CAAA,EAAG;IACN,IAAI,CAACzB,UAAU,CAACO,IAAI,CAAC,CAAC;IACtB,IAAI,CAACP,UAAU,CAAC0B,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAACzB,cAAc,CAACyB,QAAQ,CAAC,CAAC;IAC9B,IAAI,CAACxB,mBAAmB,CAACyB,KAAK,CAAC,CAAC;EACpC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvB9B,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAAC+B,UAAU,GAAG,IAAI1B,GAAG,CAAC,CAAC;IAC3B;IACA,IAAI,CAAC2B,OAAO,GAAG/C,qDAAM,CAACC,iDAAM,CAAC;IAC7B,IAAI,OAAOoB,cAAc,KAAK,WAAW,KAAK,OAAO2B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC1F,IAAI,CAACD,OAAO,CAACE,iBAAiB,CAAC,MAAM;QACjCC,MAAM,CAACC,gBAAgB,CAAC,OAAO,EAAE3C,6BAA6B,CAAC;MACnE,CAAC,CAAC;IACN;EACJ;EACA4C,WAAWA,CAAA,EAAG;IACV,KAAK,MAAM,GAAGvB,QAAQ,CAAC,IAAI,IAAI,CAACiB,UAAU,EAAE;MACxCjB,QAAQ,CAACa,OAAO,CAAC,CAAC;IACtB;IACA,IAAI,CAACI,UAAU,CAACF,KAAK,CAAC,CAAC;IACvB,IAAI,OAAOvB,cAAc,KAAK,WAAW,KAAK,OAAO2B,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC1FE,MAAM,CAACG,mBAAmB,CAAC,OAAO,EAAE7C,6BAA6B,CAAC;IACtE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIiB,OAAOA,CAACC,MAAM,EAAE4B,OAAO,EAAE;IACrB,MAAMtB,GAAG,GAAGsB,OAAO,EAAEtB,GAAG,IAAI,aAAa;IACzC,IAAI,CAAC,IAAI,CAACc,UAAU,CAACnB,GAAG,CAACK,GAAG,CAAC,EAAE;MAC3B,IAAI,CAACc,UAAU,CAAClB,GAAG,CAACI,GAAG,EAAE,IAAIlB,6BAA6B,CAACkB,GAAG,CAAC,CAAC;IACpE;IACA,OAAO,IAAI,CAACc,UAAU,CAACL,GAAG,CAACT,GAAG,CAAC,CAACP,OAAO,CAACC,MAAM,CAAC;EACnD;EACA;IAAS,IAAI,CAAC6B,IAAI,YAAAC,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFZ,oBAAoB;IAAA,CAAoD;EAAE;EACpL;IAAS,IAAI,CAACa,KAAK,kBAD6E3D,gEAAE;MAAA6D,KAAA,EACYf,oBAAoB;MAAAgB,OAAA,EAApBhB,oBAAoB,CAAAU,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAC/J;AACA;EAAA,QAAAd,SAAA,oBAAAA,SAAA,KAHoGjD,+DAAE,CAGX8C,oBAAoB,EAAc,CAAC;IAClHmB,IAAI,EAAE9D,qDAAU;IAChB+D,IAAI,EAAE,CAAC;MACCH,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,EAAE;AAAA;;AAEpC;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzH4C;AAC4B;AACpC;AAC6F;AACrD;AAC3B;AACK;AACX;;AAE3C;AACA,MAAMoB,eAAe,GAAGf,sFAA+B,CAAC;EAAEgB,OAAO,EAAE;AAAK,CAAC,CAAC;AAC1E;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBrE,WAAWA,CAACsE,SAAS,EAAEtC,OAAO,EAAE;IAC5B,IAAI,CAACsC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACtC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACuC,kBAAkB,GAAG,IAAIlE,GAAG,CAAC,CAAC;EACvC;EACAmE,OAAOA,CAACC,YAAY,EAAE;IAClB,IAAI,CAAC,IAAI,CAACH,SAAS,CAACI,SAAS,EAAE;MAC3B,OAAOX,uCAAK;IAChB;IACA,MAAMY,OAAO,GAAGd,oEAAa,CAACY,YAAY,CAAC;IAC3C,MAAMG,IAAI,GAAG,IAAI,CAACL,kBAAkB,CAAC7C,GAAG,CAACiD,OAAO,CAAC;IACjD,IAAIC,IAAI,EAAE;MACN,OAAOA,IAAI,CAACC,OAAO;IACvB;IACA,MAAMC,MAAM,GAAG,IAAI1F,yCAAO,CAAC,CAAC;IAC5B,MAAM2F,QAAQ,GAAG,2BAA2B;IAC5C,MAAMC,QAAQ,GAAKC,KAAK,IAAK;MACzB;MACA;MACA;MACA,IAAIA,KAAK,CAACC,aAAa,KAAK,+BAA+B,IACvD,CAACP,OAAO,CAACQ,SAAS,CAACC,QAAQ,CAACL,QAAQ,CAAC,EAAE;QACvCJ,OAAO,CAACQ,SAAS,CAACE,GAAG,CAACN,QAAQ,CAAC;QAC/B,IAAI,CAAC/C,OAAO,CAACsD,GAAG,CAAC,MAAMR,MAAM,CAACrE,IAAI,CAAC;UAAEE,MAAM,EAAEsE,KAAK,CAACtE,MAAM;UAAE4E,YAAY,EAAE;QAAK,CAAC,CAAC,CAAC;MACrF,CAAC,MACI,IAAIN,KAAK,CAACC,aAAa,KAAK,6BAA6B,IAC1DP,OAAO,CAACQ,SAAS,CAACC,QAAQ,CAACL,QAAQ,CAAC,EAAE;QACtCJ,OAAO,CAACQ,SAAS,CAACK,MAAM,CAACT,QAAQ,CAAC;QAClC,IAAI,CAAC/C,OAAO,CAACsD,GAAG,CAAC,MAAMR,MAAM,CAACrE,IAAI,CAAC;UAAEE,MAAM,EAAEsE,KAAK,CAACtE,MAAM;UAAE4E,YAAY,EAAE;QAAM,CAAC,CAAC,CAAC;MACtF;IACJ,CAAE;IACF,IAAI,CAACvD,OAAO,CAACE,iBAAiB,CAAC,MAAM;MACjCyC,OAAO,CAACvC,gBAAgB,CAAC,gBAAgB,EAAE4C,QAAQ,EAAEb,eAAe,CAAC;MACrEQ,OAAO,CAACQ,SAAS,CAACE,GAAG,CAAC,mCAAmC,CAAC;IAC9D,CAAC,CAAC;IACF,IAAI,CAACd,kBAAkB,CAAC1D,GAAG,CAAC8D,OAAO,EAAE;MACjCE,OAAO,EAAEC,MAAM;MACfW,QAAQ,EAAEA,CAAA,KAAM;QACZd,OAAO,CAACrC,mBAAmB,CAAC,gBAAgB,EAAE0C,QAAQ,EAAEb,eAAe,CAAC;MAC5E;IACJ,CAAC,CAAC;IACF,OAAOW,MAAM;EACjB;EACAY,cAAcA,CAACjB,YAAY,EAAE;IACzB,MAAME,OAAO,GAAGd,oEAAa,CAACY,YAAY,CAAC;IAC3C,MAAMG,IAAI,GAAG,IAAI,CAACL,kBAAkB,CAAC7C,GAAG,CAACiD,OAAO,CAAC;IACjD,IAAIC,IAAI,EAAE;MACNA,IAAI,CAACa,QAAQ,CAAC,CAAC;MACfb,IAAI,CAACC,OAAO,CAACjD,QAAQ,CAAC,CAAC;MACvB+C,OAAO,CAACQ,SAAS,CAACK,MAAM,CAAC,mCAAmC,CAAC;MAC7Db,OAAO,CAACQ,SAAS,CAACK,MAAM,CAAC,2BAA2B,CAAC;MACrD,IAAI,CAACjB,kBAAkB,CAACnD,MAAM,CAACuD,OAAO,CAAC;IAC3C;EACJ;EACAtC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACkC,kBAAkB,CAACoB,OAAO,CAAC,CAACC,KAAK,EAAEjB,OAAO,KAAK,IAAI,CAACe,cAAc,CAACf,OAAO,CAAC,CAAC;EACrF;EACA;IAAS,IAAI,CAACnC,IAAI,YAAAqD,wBAAAnD,CAAA;MAAA,YAAAA,CAAA,IAAwF2B,eAAe,EAAzBrF,sDAAE,CAAyCmE,2DAAW,GAAtDnE,sDAAE,CAAiEA,iDAAS;IAAA,CAA6C;EAAE;EAC3N;IAAS,IAAI,CAAC2D,KAAK,kBAD6E3D,gEAAE;MAAA6D,KAAA,EACYwB,eAAe;MAAAvB,OAAA,EAAfuB,eAAe,CAAA7B,IAAA;MAAAO,UAAA,EAAc;IAAM,EAAG;EAAE;AAC1J;AACA;EAAA,QAAAd,SAAA,oBAAAA,SAAA,KAHoGjD,+DAAE,CAGXqF,eAAe,EAAc,CAAC;IAC7GpB,IAAI,EAAE9D,qDAAU;IAChB+D,IAAI,EAAE,CAAC;MAAEH,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEE,IAAI,EAAEE,2DAAW4C;EAAC,CAAC,EAAE;IAAE9C,IAAI,EAAEjE,iDAASE;EAAC,CAAC,CAAC;AAAA;AAC9E;AACA,MAAM8G,WAAW,CAAC;EACdhG,WAAWA,CAACiG,WAAW,EAAEC,gBAAgB,EAAE;IACvC,IAAI,CAACD,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,gBAAgB,GAAGA,gBAAgB;IACxC;IACA,IAAI,CAACC,WAAW,GAAG,IAAI9C,uDAAY,CAAC,CAAC;EACzC;EACA+C,QAAQA,CAAA,EAAG;IACP,IAAI,CAACF,gBAAgB,CAChB1B,OAAO,CAAC,IAAI,CAACyB,WAAW,CAAC,CACzBjF,SAAS,CAACiE,KAAK,IAAI,IAAI,CAACkB,WAAW,CAACE,IAAI,CAACpB,KAAK,CAAC,CAAC;EACzD;EACA5C,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC6D,gBAAgB,CAACR,cAAc,CAAC,IAAI,CAACO,WAAW,CAAC;EAC1D;EACA;IAAS,IAAI,CAACzD,IAAI,YAAA8D,oBAAA5D,CAAA;MAAA,YAAAA,CAAA,IAAwFsD,WAAW,EAvBrBhH,+DAAE,CAuBqCA,qDAAa,GAvBpDA,+DAAE,CAuB+DqF,eAAe;IAAA,CAA4C;EAAE;EAC9N;IAAS,IAAI,CAACoC,IAAI,kBAxB8EzH,+DAAE;MAAAiE,IAAA,EAwBJ+C,WAAW;MAAAW,SAAA;MAAAC,OAAA;QAAAT,WAAA;MAAA;MAAAU,UAAA;IAAA,EAAyG;EAAE;AACxN;AACA;EAAA,QAAA5E,SAAA,oBAAAA,SAAA,KA1BoGjD,+DAAE,CA0BXgH,WAAW,EAAc,CAAC;IACzG/C,IAAI,EAAEK,oDAAS;IACfJ,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE,eAAe;MACzBD,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE5D,IAAI,EAAEjE,qDAAawH;EAAC,CAAC,EAAE;IAAEvD,IAAI,EAAEoB;EAAgB,CAAC,CAAC,EAAkB;IAAE8B,WAAW,EAAE,CAAC;MACxGlD,IAAI,EAAEM,iDAAMA;IAChB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMwD,mBAAmB,CAAC;EACtB;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACE,KAAK,EAAE;IACf,IAAI,CAACD,QAAQ,GAAGnD,2EAAoB,CAACoD,KAAK,CAAC;IAC3C,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACA;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACF,KAAK,EAAE;IACf,IAAI,CAACG,QAAQ,GAAGvD,2EAAoB,CAACoD,KAAK,CAAC;IAC3C,IAAI,CAACI,aAAa,CAAC,CAAC;EACxB;EACA;EACA,IAAIC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACL,KAAK,EAAE;IACf;IACA;IACA,IAAI,IAAI,CAACM,QAAQ,KAAKN,KAAK,EAAE;MACzB,CAAC,IAAI,CAACM,QAAQ,GAAGN,KAAK,IAAI,IAAI,CAACO,kBAAkB,CAAC,IAAI,CAAC,GAAG,IAAI,CAACC,KAAK,CAAC,CAAC;IAC1E;EACJ;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,gBAAgB,CAACD,WAAW;EAC5C;EACA,IAAIA,WAAWA,CAACT,KAAK,EAAE;IACnB,IAAI,CAACW,wBAAwB,GAAGC,SAAS;IACzC,IAAIZ,KAAK,EAAE;MACP,IAAI,CAACU,gBAAgB,CAACG,YAAY,CAAC,aAAa,EAAEb,KAAK,CAAC;IAC5D,CAAC,MACI;MACD,IAAI,CAACU,gBAAgB,CAACI,eAAe,CAAC,aAAa,CAAC;IACxD;IACA,IAAI,CAACC,+BAA+B,CAAC,CAAC;EAC1C;EACAjI,WAAWA,CAACiG,WAAW,EAAE3B,SAAS,EAAEtC,OAAO,EAC3C;EACAkG,QAAQ,EAAE;IACN,IAAI,CAACjC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC3B,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACtC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC9B,UAAU,GAAG,IAAId,yCAAO,CAAC,CAAC;IAC/B,IAAI,CAACoI,QAAQ,GAAG,IAAI;IACpB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACW,gBAAgB,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,aAAa,GAAG,KAAK;IAC1B;IACA,IAAI,CAACC,iBAAiB,GAAIpD,KAAK,IAAK;MAChC,IAAI,CAACqD,SAAS,GAAGrD,KAAK,CAAChC,IAAI,KAAK,OAAO;IAC3C,CAAC;IACD,IAAI,CAACsF,SAAS,GAAGL,QAAQ;IACzB,IAAI,CAACN,gBAAgB,GAAG,IAAI,CAAC3B,WAAW,CAACuC,aAAa;EAC1D;EACA;EACArB,aAAaA,CAAA,EAAG;IACZ,MAAMsB,SAAS,GAAG,IAAI,CAACzB,OAAO,IAAI,IAAI,CAAC0B,iBAAiB,GAAG,GAAG,IAAI,CAAC1B,OAAO,GAAG,IAAI,CAAC0B,iBAAiB,IAAI,GAAG,IAAI;IAC9G,IAAID,SAAS,EAAE;MACX,IAAI,CAACb,gBAAgB,CAACe,KAAK,CAACF,SAAS,GAAGA,SAAS;IACrD;EACJ;EACA;EACAnB,aAAaA,CAAA,EAAG;IACZ,MAAMsB,SAAS,GAAG,IAAI,CAACxB,OAAO,IAAI,IAAI,CAACsB,iBAAiB,GAAG,GAAG,IAAI,CAACtB,OAAO,GAAG,IAAI,CAACsB,iBAAiB,IAAI,GAAG,IAAI;IAC9G,IAAIE,SAAS,EAAE;MACX,IAAI,CAAChB,gBAAgB,CAACe,KAAK,CAACC,SAAS,GAAGA,SAAS;IACrD;EACJ;EACAC,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACvE,SAAS,CAACI,SAAS,EAAE;MAC1B;MACA,IAAI,CAACoE,cAAc,GAAG,IAAI,CAAClB,gBAAgB,CAACe,KAAK,CAACI,MAAM;MACxD,IAAI,CAACtB,kBAAkB,CAAC,CAAC;MACzB,IAAI,CAACzF,OAAO,CAACE,iBAAiB,CAAC,MAAM;QACjC,MAAMC,MAAM,GAAG,IAAI,CAAC6G,UAAU,CAAC,CAAC;QAChChF,+CAAS,CAAC7B,MAAM,EAAE,QAAQ,CAAC,CACtBd,IAAI,CAAC4C,yDAAS,CAAC,EAAE,CAAC,EAAEzE,yDAAS,CAAC,IAAI,CAACU,UAAU,CAAC,CAAC,CAC/Cc,SAAS,CAAC,MAAM,IAAI,CAACyG,kBAAkB,CAAC,IAAI,CAAC,CAAC;QACnD,IAAI,CAACG,gBAAgB,CAACxF,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACiG,iBAAiB,CAAC;QACvE,IAAI,CAACT,gBAAgB,CAACxF,gBAAgB,CAAC,MAAM,EAAE,IAAI,CAACiG,iBAAiB,CAAC;MAC1E,CAAC,CAAC;MACF,IAAI,CAACD,aAAa,GAAG,IAAI;MACzB,IAAI,CAACX,kBAAkB,CAAC,IAAI,CAAC;IACjC;EACJ;EACApF,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuF,gBAAgB,CAACtF,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAAC+F,iBAAiB,CAAC;IAC1E,IAAI,CAACT,gBAAgB,CAACtF,mBAAmB,CAAC,MAAM,EAAE,IAAI,CAAC+F,iBAAiB,CAAC;IACzE,IAAI,CAACnI,UAAU,CAACO,IAAI,CAAC,CAAC;IACtB,IAAI,CAACP,UAAU,CAAC0B,QAAQ,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIqH,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACP,iBAAiB,EAAE;MACxB;IACJ;IACA;IACA,IAAIQ,aAAa,GAAG,IAAI,CAACtB,gBAAgB,CAACuB,SAAS,CAAC,KAAK,CAAC;IAC1DD,aAAa,CAACE,IAAI,GAAG,CAAC;IACtB;IACA;IACA;IACAF,aAAa,CAACP,KAAK,CAACU,QAAQ,GAAG,UAAU;IACzCH,aAAa,CAACP,KAAK,CAACW,UAAU,GAAG,QAAQ;IACzCJ,aAAa,CAACP,KAAK,CAACY,MAAM,GAAG,MAAM;IACnCL,aAAa,CAACP,KAAK,CAACa,OAAO,GAAG,GAAG;IACjCN,aAAa,CAACP,KAAK,CAACI,MAAM,GAAG,EAAE;IAC/BG,aAAa,CAACP,KAAK,CAACF,SAAS,GAAG,EAAE;IAClCS,aAAa,CAACP,KAAK,CAACC,SAAS,GAAG,EAAE;IAClC;IACA;IACA;IACA;IACA;IACAM,aAAa,CAACP,KAAK,CAACc,QAAQ,GAAG,QAAQ;IACvC,IAAI,CAAC7B,gBAAgB,CAAC8B,UAAU,CAACC,WAAW,CAACT,aAAa,CAAC;IAC3D,IAAI,CAACR,iBAAiB,GAAGQ,aAAa,CAACU,YAAY;IACnDV,aAAa,CAAC1D,MAAM,CAAC,CAAC;IACtB;IACA,IAAI,CAAC2B,aAAa,CAAC,CAAC;IACpB,IAAI,CAACG,aAAa,CAAC,CAAC;EACxB;EACAuC,oBAAoBA,CAAA,EAAG;IACnB,MAAMlF,OAAO,GAAG,IAAI,CAACiD,gBAAgB;IACrC,MAAMkC,cAAc,GAAGnF,OAAO,CAACgE,KAAK,CAACoB,YAAY,IAAI,EAAE;IACvD,MAAMC,SAAS,GAAG,IAAI,CAAC1F,SAAS,CAAC2F,OAAO;IACxC,MAAMC,iBAAiB,GAAGF,SAAS,IAAI,IAAI,CAAC1B,SAAS;IACrD,MAAM6B,cAAc,GAAGH,SAAS,GAC1B,yCAAyC,GACzC,iCAAiC;IACvC;IACA;IACA;IACA,IAAIE,iBAAiB,EAAE;MACnBvF,OAAO,CAACgE,KAAK,CAACoB,YAAY,GAAG,GAAGpF,OAAO,CAACiF,YAAY,IAAI;IAC5D;IACA;IACA;IACAjF,OAAO,CAACQ,SAAS,CAACE,GAAG,CAAC8E,cAAc,CAAC;IACrC;IACA;IACA,MAAMC,YAAY,GAAGzF,OAAO,CAACyF,YAAY,GAAG,CAAC;IAC7CzF,OAAO,CAACQ,SAAS,CAACK,MAAM,CAAC2E,cAAc,CAAC;IACxC,IAAID,iBAAiB,EAAE;MACnBvF,OAAO,CAACgE,KAAK,CAACoB,YAAY,GAAGD,cAAc;IAC/C;IACA,OAAOM,YAAY;EACvB;EACAnC,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,CAAC,IAAI,CAACG,aAAa,IAAI,IAAI,CAACP,wBAAwB,IAAIC,SAAS,EAAE;MACnE;IACJ;IACA,IAAI,CAAC,IAAI,CAACH,WAAW,EAAE;MACnB,IAAI,CAACE,wBAAwB,GAAG,CAAC;MACjC;IACJ;IACA,MAAMX,KAAK,GAAG,IAAI,CAACU,gBAAgB,CAACV,KAAK;IACzC,IAAI,CAACU,gBAAgB,CAACV,KAAK,GAAG,IAAI,CAACU,gBAAgB,CAACD,WAAW;IAC/D,IAAI,CAACE,wBAAwB,GAAG,IAAI,CAACgC,oBAAoB,CAAC,CAAC;IAC3D,IAAI,CAACjC,gBAAgB,CAACV,KAAK,GAAGA,KAAK;EACvC;EACAmD,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC/F,SAAS,CAACI,SAAS,EAAE;MAC1B,IAAI,CAAC+C,kBAAkB,CAAC,CAAC;IAC7B;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIA,kBAAkBA,CAAC6C,KAAK,GAAG,KAAK,EAAE;IAC9B;IACA,IAAI,CAAC,IAAI,CAAC9C,QAAQ,EAAE;MAChB;IACJ;IACA,IAAI,CAACyB,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAAChB,+BAA+B,CAAC,CAAC;IACtC;IACA;IACA,IAAI,CAAC,IAAI,CAACS,iBAAiB,EAAE;MACzB;IACJ;IACA,MAAM6B,QAAQ,GAAG,IAAI,CAACtE,WAAW,CAACuC,aAAa;IAC/C,MAAMtB,KAAK,GAAGqD,QAAQ,CAACrD,KAAK;IAC5B;IACA,IAAI,CAACoD,KAAK,IAAI,IAAI,CAACrD,QAAQ,KAAK,IAAI,CAACkB,gBAAgB,IAAIjB,KAAK,KAAK,IAAI,CAACsD,cAAc,EAAE;MACpF;IACJ;IACA,MAAMJ,YAAY,GAAG,IAAI,CAACP,oBAAoB,CAAC,CAAC;IAChD,MAAMd,MAAM,GAAG0B,IAAI,CAACC,GAAG,CAACN,YAAY,EAAE,IAAI,CAACvC,wBAAwB,IAAI,CAAC,CAAC;IACzE;IACA0C,QAAQ,CAAC5B,KAAK,CAACI,MAAM,GAAG,GAAGA,MAAM,IAAI;IACrC,IAAI,CAAC/G,OAAO,CAACE,iBAAiB,CAAC,MAAM;MACjC,IAAI,OAAOyI,qBAAqB,KAAK,WAAW,EAAE;QAC9CA,qBAAqB,CAAC,MAAM,IAAI,CAACC,sBAAsB,CAACL,QAAQ,CAAC,CAAC;MACtE,CAAC,MACI;QACDM,UAAU,CAAC,MAAM,IAAI,CAACD,sBAAsB,CAACL,QAAQ,CAAC,CAAC;MAC3D;IACJ,CAAC,CAAC;IACF,IAAI,CAACC,cAAc,GAAGtD,KAAK;IAC3B,IAAI,CAACiB,gBAAgB,GAAG,IAAI,CAAClB,QAAQ;EACzC;EACA;AACJ;AACA;EACIS,KAAKA,CAAA,EAAG;IACJ;IACA;IACA,IAAI,IAAI,CAACoB,cAAc,KAAKhB,SAAS,EAAE;MACnC,IAAI,CAACF,gBAAgB,CAACe,KAAK,CAACI,MAAM,GAAG,IAAI,CAACD,cAAc;IAC5D;EACJ;EACAgC,iBAAiBA,CAAA,EAAG;IAChB;EAAA;EAEJ;EACAC,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAACxC,SAAS,IAAIL,QAAQ;EACrC;EACA;EACAc,UAAUA,CAAA,EAAG;IACT,MAAMgC,GAAG,GAAG,IAAI,CAACD,YAAY,CAAC,CAAC;IAC/B,OAAOC,GAAG,CAACC,WAAW,IAAI9I,MAAM;EACpC;EACA;AACJ;AACA;AACA;AACA;EACIyI,sBAAsBA,CAACL,QAAQ,EAAE;IAC7B,MAAM;MAAEW,cAAc;MAAEC;IAAa,CAAC,GAAGZ,QAAQ;IACjD;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACrK,UAAU,CAACkL,SAAS,IAAI,IAAI,CAAC9C,SAAS,EAAE;MAC9CiC,QAAQ,CAACc,iBAAiB,CAACH,cAAc,EAAEC,YAAY,CAAC;IAC5D;EACJ;EACA;IAAS,IAAI,CAAC3I,IAAI,YAAA8I,4BAAA5I,CAAA;MAAA,YAAAA,CAAA,IAAwFqE,mBAAmB,EAvS7B/H,+DAAE,CAuS6CA,qDAAa,GAvS5DA,+DAAE,CAuSuEmE,2DAAW,GAvSpFnE,+DAAE,CAuS+FA,iDAAS,GAvS1GA,+DAAE,CAuSqHkF,qDAAQ;IAAA,CAA4D;EAAE;EAC7R;IAAS,IAAI,CAACuC,IAAI,kBAxS8EzH,+DAAE;MAAAiE,IAAA,EAwSJ8D,mBAAmB;MAAAJ,SAAA;MAAA4E,SAAA,WAA8R,GAAG;MAAAC,YAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxSlT1M,wDAAE,mBAAA6M,6CAAA;YAAA,OAwSJF,GAAA,CAAAb,iBAAA,CAAkB,CAAC;UAAA,CAAD,CAAC;QAAA;MAAA;MAAAgB,MAAA;QAAA9E,OAAA,GAxSjBhI,0DAAE,CAAAgN,IAAA;QAAA5E,OAAA,GAAFpI,0DAAE,CAAAgN,IAAA;QAAAzE,OAAA,GAAFvI,0DAAE,CAAAiN,0BAAA,oCAwS8NzI,2DAAgB;QAAAmE,WAAA;MAAA;MAAAuE,QAAA;MAAArF,UAAA;MAAAsF,QAAA,GAxShPnN,sEAAE;IAAA,EAwSgc;EAAE;AACxiB;AACA;EAAA,QAAAiD,SAAA,oBAAAA,SAAA,KA1SoGjD,+DAAE,CA0SX+H,mBAAmB,EAAc,CAAC;IACjH9D,IAAI,EAAEK,oDAAS;IACfJ,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE,+BAA+B;MACzCoF,QAAQ,EAAE,qBAAqB;MAC/BG,IAAI,EAAE;QACF,OAAO,EAAE,uBAAuB;QAChC;QACA;QACA,MAAM,EAAE,GAAG;QACX,SAAS,EAAE;MACf,CAAC;MACDxF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE5D,IAAI,EAAEjE,qDAAawH;EAAC,CAAC,EAAE;IAAEvD,IAAI,EAAEE,2DAAW4C;EAAC,CAAC,EAAE;IAAE9C,IAAI,EAAEjE,iDAASE;EAAC,CAAC,EAAE;IAAE+D,IAAI,EAAE6E,SAAS;IAAEwE,UAAU,EAAE,CAAC;MACpHrJ,IAAI,EAAEQ,mDAAQA;IAClB,CAAC,EAAE;MACCR,IAAI,EAAES,iDAAM;MACZR,IAAI,EAAE,CAACgB,qDAAQ;IACnB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE8C,OAAO,EAAE,CAAC;MACnC/D,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEkE,OAAO,EAAE,CAAC;MACVnE,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAEqE,OAAO,EAAE,CAAC;MACVtE,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC;QAAEqJ,KAAK,EAAE,qBAAqB;QAAEC,SAAS,EAAEhJ,2DAAgBA;MAAC,CAAC;IACxE,CAAC,CAAC;IAAEmE,WAAW,EAAE,CAAC;MACd1E,IAAI,EAAEU,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8I,eAAe,CAAC;EAClB;IAAS,IAAI,CAACjK,IAAI,YAAAkK,wBAAAhK,CAAA;MAAA,YAAAA,CAAA,IAAwF+J,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACE,IAAI,kBA5U8E3N,8DAAE;MAAAiE,IAAA,EA4USwJ;IAAe,EAA6F;EAAE;EACzN;IAAS,IAAI,CAACI,IAAI,kBA7U8E7N,8DAAE,IA6U2B;EAAE;AACnI;AACA;EAAA,QAAAiD,SAAA,oBAAAA,SAAA,KA/UoGjD,+DAAE,CA+UXyN,eAAe,EAAc,CAAC;IAC7GxJ,IAAI,EAAEW,mDAAQ;IACdV,IAAI,EAAE,CAAC;MACC6J,OAAO,EAAE,CAAC/G,WAAW,EAAEe,mBAAmB,CAAC;MAC3CiG,OAAO,EAAE,CAAChH,WAAW,EAAEe,mBAAmB;IAC9C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnaoC;AAC4M;AACxM;AACI;AACQ;AACT;AAC2B;AACR;AACmB;AACN;AAClB;AACA;;AAEzD;AAAA,MAAAuH,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAC,gEAAApD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAEoG1M,uDAAE,cA+2Bo7F,CAAC;EAAA;AAAA;AAAA,SAAAgQ,kDAAAtD,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2Bv7F1M,4DAAE,eA+2Bm8E,CAAC;IA/2Bt8EA,0DAAE,KA+2By/E,CAAC;IA/2B5/EA,wDAAE,IAAA8P,+DAAA,kBA+2BqyF,CAAC;IA/2BxyF9P,0DAAE,CA+2B48F,CAAC;EAAA;EAAA,IAAA0M,EAAA;IAAA,MAAA2D,MAAA,GA/2B/8FrQ,2DAAE;IAAFA,wDAAE,aAAAqQ,MAAA,CAAAG,iBAAA,EA+2BoyE,CAAC,kBAAAH,MAAA,CAAAI,WAAA,EAA6C,CAAC,OAAAJ,MAAA,CAAAK,QAA6B,CAAC;IA/2Bn3E1Q,yDAAE,QAAAqQ,MAAA,CAAAO,QAAA,CAAAC,wBAAA,UAAAR,MAAA,CAAAO,QAAA,CAAAE,EAAA;IAAF9Q,uDAAE,EA+2B87F,CAAC;IA/2Bj8FA,2DAAE,KAAAqQ,MAAA,CAAAY,kBAAA,IAAAZ,MAAA,CAAAO,QAAA,CAAAM,QAAA,SA+2B87F,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAAzE,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2Bj8F1M,wDAAE,IAAAgQ,iDAAA,mBA+2B+sE,CAAC;EAAA;EAAA,IAAAtD,EAAA;IAAA,MAAA2D,MAAA,GA/2BltErQ,2DAAE;IAAFA,2DAAE,IAAAqQ,MAAA,CAAAe,iBAAA,WA+2Bi9F,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAA3E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2Bp9F1M,uDAAE,YA+2Bq/G,CAAC;EAAA;AAAA;AAAA,SAAAsR,gEAAA5E,EAAA,EAAAC,GAAA;AAAA,SAAA4E,kDAAA7E,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2Bx/G1M,wDAAE,IAAAsR,+DAAA,yBA+2B2wH,CAAC;EAAA;EAAA,IAAA5E,EAAA;IA/2B9wH1M,2DAAE;IAAA,MAAAwR,gBAAA,GAAFxR,yDAAE;IAAFA,wDAAE,qBAAAwR,gBA+2B0wH,CAAC;EAAA;AAAA;AAAA,SAAAE,oCAAAhF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2B7wH1M,4DAAE,YA+2BiqH,CAAC;IA/2BpqHA,wDAAE,IAAAuR,iDAAA,gBA+2B6sH,CAAC;IA/2BhtHvR,0DAAE,CA+2BkzH,CAAC;EAAA;EAAA,IAAA0M,EAAA;IAAA,MAAA2D,MAAA,GA/2BrzHrQ,2DAAE;IAAFA,wDAAE,mCAAAqQ,MAAA,CAAAG,iBAAA,EA+2BgqH,CAAC;IA/2BnqHxQ,uDAAE,CA+2BoyH,CAAC;IA/2BvyHA,2DAAE,KAAAqQ,MAAA,CAAAsB,uBAAA,WA+2BoyH,CAAC;EAAA;AAAA;AAAA,SAAAC,oCAAAlF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2BvyH1M,4DAAE,eA+2Bk6H,CAAC;IA/2Br6HA,0DAAE,KA+2B6+H,CAAC;IA/2Bh/HA,0DAAE,CA+2B2/H,CAAC;EAAA;AAAA;AAAA,SAAA6R,oCAAAnF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2B9/H1M,4DAAE,eA+2B2mI,CAAC;IA/2B9mIA,0DAAE,KA+2ByqI,CAAC;IA/2B5qIA,0DAAE,CA+2BurI,CAAC;EAAA;AAAA;AAAA,SAAA8R,mDAAApF,EAAA,EAAAC,GAAA;AAAA,SAAAoF,qCAAArF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2B1rI1M,wDAAE,IAAA8R,kDAAA,yBA+2Bq2I,CAAC;EAAA;EAAA,IAAApF,EAAA;IA/2Bx2I1M,2DAAE;IAAA,MAAAwR,gBAAA,GAAFxR,yDAAE;IAAFA,wDAAE,qBAAAwR,gBA+2Bo2I,CAAC;EAAA;AAAA;AAAA,SAAAQ,qCAAAtF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2Bv2I1M,4DAAE,aA+2B+/I,CAAC;IA/2BlgJA,0DAAE,KA+2B6jJ,CAAC;IA/2BhkJA,0DAAE,CA+2B2kJ,CAAC;EAAA;AAAA;AAAA,SAAAiS,qCAAAvF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2B9kJ1M,4DAAE,aA+2BsqJ,CAAC;IA/2BzqJA,0DAAE,KA+2BivJ,CAAC;IA/2BpvJA,0DAAE,CA+2B+vJ,CAAC;EAAA;AAAA;AAAA,SAAAkS,qCAAAxF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2BlwJ1M,uDAAE,aA+2Bo1J,CAAC;EAAA;AAAA;AAAA,SAAAmS,8BAAAzF,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2Bv1J1M,4DAAE,aA+2B0sK,CAAC;IA/2B7sKA,0DAAE,KA+2B8wK,CAAC;IA/2BjxKA,0DAAE,CA+2B4xK,CAAC;EAAA;EAAA,IAAA0M,EAAA;IAAA,MAAA2D,MAAA,GA/2B/xKrQ,2DAAE;IAAFA,wDAAE,wBAAAqQ,MAAA,CAAA+B,wBA+2BysK,CAAC;EAAA;AAAA;AAAA,SAAAC,4CAAA3F,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2B5sK1M,4DAAE,kBA+2B4+K,CAAC;IA/2B/+KA,oDAAE,EA+2By/K,CAAC;IA/2B5/KA,0DAAE,CA+2BogL,CAAC;EAAA;EAAA,IAAA0M,EAAA;IAAA,MAAA2D,MAAA,GA/2BvgLrQ,2DAAE;IAAFA,wDAAE,OAAAqQ,MAAA,CAAAkC,YA+2B2+K,CAAC;IA/2B9+KvS,uDAAE,CA+2By/K,CAAC;IA/2B5/KA,+DAAE,CAAAqQ,MAAA,CAAAoC,SA+2By/K,CAAC;EAAA;AAAA;AAAA,SAAAC,8BAAAhG,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IA/2B5/K1M,4DAAE,aA+2Bq6K,CAAC;IA/2Bx6KA,wDAAE,IAAAqS,2CAAA,sBA+2Bg8K,CAAC;IA/2Bn8KrS,0DAAE,KA+2BylL,CAAC;IA/2B5lLA,uDAAE,aA+2BupL,CAAC;IA/2B1pLA,0DAAE,KA+2B2tL,CAAC;IA/2B9tLA,0DAAE,CA+2ByuL,CAAC;EAAA;EAAA,IAAA0M,EAAA;IAAA,MAAA2D,MAAA,GA/2B5uLrQ,2DAAE;IAAFA,wDAAE,wBAAAqQ,MAAA,CAAA+B,wBA+2Bo6K,CAAC;IA/2Bv6KpS,uDAAE,CA+2B+gL,CAAC;IA/2BlhLA,2DAAE,IAAAqQ,MAAA,CAAAoC,SAAA,SA+2B+gL,CAAC;EAAA;AAAA;AAh3BtnL,MAAME,QAAQ,CAAC;EACX;IAAS,IAAI,CAACnP,IAAI,YAAAoP,iBAAAlP,CAAA;MAAA,YAAAA,CAAA,IAAwFiP,QAAQ;IAAA,CAAmD;EAAE;EACvK;IAAS,IAAI,CAAClL,IAAI,kBAD8EzH,+DAAE;MAAAiE,IAAA,EACJ0O,QAAQ;MAAAhL,SAAA;MAAAE,UAAA;IAAA,EAA4D;EAAE;AACxK;AACA;EAAA,QAAA5E,SAAA,oBAAAA,SAAA,KAHoGjD,+DAAE,CAGX2S,QAAQ,EAAc,CAAC;IACtG1O,IAAI,EAAEK,oDAAS;IACfJ,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE,WAAW;MACrBD,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,IAAIgL,cAAc,GAAG,CAAC;AACtB;AACA;AACA;AACA;AACA;AACA,MAAMC,SAAS,GAAG,IAAI7E,yDAAc,CAAC,UAAU,CAAC;AAChD;AACA,MAAM8E,QAAQ,CAAC;EACX/R,WAAWA,CAACgS,QAAQ,EAAEC,UAAU,EAAE;IAC9B,IAAI,CAACnC,EAAE,GAAG,iBAAiB+B,cAAc,EAAE,EAAE;IAC7C;IACA;IACA,IAAI,CAACG,QAAQ,EAAE;MACXC,UAAU,CAACzJ,aAAa,CAACT,YAAY,CAAC,WAAW,EAAE,QAAQ,CAAC;IAChE;EACJ;EACA;IAAS,IAAI,CAACvF,IAAI,YAAA0P,iBAAAxP,CAAA;MAAA,YAAAA,CAAA,IAAwFqP,QAAQ,EA5BlB/S,+DAAE,CA4BkC,WAAW,GA5B/CA,+DAAE,CA4B2EA,qDAAa;IAAA,CAA4C;EAAE;EACxO;IAAS,IAAI,CAACyH,IAAI,kBA7B8EzH,+DAAE;MAAAiE,IAAA,EA6BJ8O,QAAQ;MAAApL,SAAA;MAAA4E,SAAA,kBAAoH,MAAM;MAAA6G,QAAA;MAAA5G,YAAA,WAAA6G,sBAAA3G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA7BhI1M,4DAAE,OAAA2M,GAAA,CAAAmE,EA6BG,CAAC;QAAA;MAAA;MAAAhE,MAAA;QAAAgE,EAAA;MAAA;MAAAjJ,UAAA;MAAAsF,QAAA,GA7BNnN,gEAAE,CA6BuP,CAAC;QAAEwT,OAAO,EAAEV,SAAS;QAAEW,WAAW,EAAEV;MAAS,CAAC,CAAC;IAAA,EAAiB;EAAE;AAC/Z;AACA;EAAA,QAAA9P,SAAA,oBAAAA,SAAA,KA/BoGjD,+DAAE,CA+BX+S,QAAQ,EAAc,CAAC;IACtG9O,IAAI,EAAEK,oDAAS;IACfJ,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE,uBAAuB;MACjCuF,IAAI,EAAE;QACF,OAAO,EAAE,0DAA0D;QACnE,aAAa,EAAE,MAAM;QACrB,MAAM,EAAE;MACZ,CAAC;MACDqG,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEV,SAAS;QAAEW,WAAW,EAAEV;MAAS,CAAC,CAAC;MAC1DlL,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE5D,IAAI,EAAE6E,SAAS;IAAEwE,UAAU,EAAE,CAAC;MAC/CrJ,IAAI,EAAEiK,oDAAS;MACfhK,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC;EAAE,CAAC,EAAE;IAAED,IAAI,EAAEjE,qDAAawH;EAAC,CAAC,CAAC,EAAkB;IAAEsJ,EAAE,EAAE,CAAC;MACvD7M,IAAI,EAAEU,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAEhB,IAAIgP,cAAc,GAAG,CAAC;AACtB;AACA,MAAMC,OAAO,CAAC;EACV5S,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAAC6S,KAAK,GAAG,OAAO;IACpB;IACA,IAAI,CAAC/C,EAAE,GAAG,gBAAgB6C,cAAc,EAAE,EAAE;EAChD;EACA;IAAS,IAAI,CAACnQ,IAAI,YAAAsQ,gBAAApQ,CAAA;MAAA,YAAAA,CAAA,IAAwFkQ,OAAO;IAAA,CAAmD;EAAE;EACtK;IAAS,IAAI,CAACnM,IAAI,kBA5D8EzH,+DAAE;MAAAiE,IAAA,EA4DJ2P,OAAO;MAAAjM,SAAA;MAAA4E,SAAA;MAAA6G,QAAA;MAAA5G,YAAA,WAAAuH,qBAAArH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA5DL1M,4DAAE,OAAA2M,GAAA,CAAAmE,EA4DE,CAAC;UA5DL9Q,yDAAE,UA4DJ,IAAI;UA5DFA,yDAAE,gCAAA2M,GAAA,CAAAkH,KAAA,KA4DM,KAAJ,CAAC;QAAA;MAAA;MAAA/G,MAAA;QAAA+G,KAAA;QAAA/C,EAAA;MAAA;MAAAjJ,UAAA;IAAA,EAAkS;EAAE;AAC7Y;AACA;EAAA,QAAA5E,SAAA,oBAAAA,SAAA,KA9DoGjD,+DAAE,CA8DX4T,OAAO,EAAc,CAAC;IACrG3P,IAAI,EAAEK,oDAAS;IACfJ,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE,UAAU;MACpBuF,IAAI,EAAE;QACF,OAAO,EAAE,yDAAyD;QAClE,qCAAqC,EAAE,iBAAiB;QACxD,MAAM,EAAE,IAAI;QACZ;QACA,cAAc,EAAE;MACpB,CAAC;MACDxF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEgM,KAAK,EAAE,CAAC;MACtB5P,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEmM,EAAE,EAAE,CAAC;MACL7M,IAAI,EAAEU,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMsP,UAAU,GAAG,IAAIhG,yDAAc,CAAC,WAAW,CAAC;AAClD;AACA,MAAMiG,SAAS,CAAC;EACZlT,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmT,OAAO,GAAG,KAAK;EACxB;EACA,IAAIC,eAAeA,CAAClM,KAAK,EAAE;IACvB,IAAI,CAACiM,OAAO,GAAG,IAAI;EACvB;EACA;IAAS,IAAI,CAAC3Q,IAAI,YAAA6Q,kBAAA3Q,CAAA;MAAA,YAAAA,CAAA,IAAwFwQ,SAAS;IAAA,CAAmD;EAAE;EACxK;IAAS,IAAI,CAACzM,IAAI,kBAhG8EzH,+DAAE;MAAAiE,IAAA,EAgGJiQ,SAAS;MAAAvM,SAAA;MAAAmF,MAAA;QAAAsH,eAAA,GAhGPpU,0DAAE,CAAAgN,IAAA;MAAA;MAAAnF,UAAA;MAAAsF,QAAA,GAAFnN,gEAAE,CAgGoK,CAAC;QAAEwT,OAAO,EAAES,UAAU;QAAER,WAAW,EAAES;MAAU,CAAC,CAAC;IAAA,EAAiB;EAAE;AAC9U;AACA;EAAA,QAAAjR,SAAA,oBAAAA,SAAA,KAlGoGjD,+DAAE,CAkGXkU,SAAS,EAAc,CAAC;IACvGjQ,IAAI,EAAEK,oDAAS;IACfJ,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE,+CAA+C;MACzD4L,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAES,UAAU;QAAER,WAAW,EAAES;MAAU,CAAC,CAAC;MAC5DrM,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEuM,eAAe,EAAE,CAAC;MAChCnQ,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA;AACA,MAAMoQ,UAAU,GAAG,IAAIrG,yDAAc,CAAC,WAAW,CAAC;AAClD;AACA,MAAMsG,SAAS,CAAC;EACZvT,WAAWA,CAAA,EAAG;IACV,IAAI,CAACmT,OAAO,GAAG,KAAK;EACxB;EACA,IAAIC,eAAeA,CAAClM,KAAK,EAAE;IACvB,IAAI,CAACiM,OAAO,GAAG,IAAI;EACvB;EACA;IAAS,IAAI,CAAC3Q,IAAI,YAAAgR,kBAAA9Q,CAAA;MAAA,YAAAA,CAAA,IAAwF6Q,SAAS;IAAA,CAAmD;EAAE;EACxK;IAAS,IAAI,CAAC9M,IAAI,kBA7H8EzH,+DAAE;MAAAiE,IAAA,EA6HJsQ,SAAS;MAAA5M,SAAA;MAAAmF,MAAA;QAAAsH,eAAA,GA7HPpU,0DAAE,CAAAgN,IAAA;MAAA;MAAAnF,UAAA;MAAAsF,QAAA,GAAFnN,gEAAE,CA6HoK,CAAC;QAAEwT,OAAO,EAAEc,UAAU;QAAEb,WAAW,EAAEc;MAAU,CAAC,CAAC;IAAA,EAAiB;EAAE;AAC9U;AACA;EAAA,QAAAtR,SAAA,oBAAAA,SAAA,KA/HoGjD,+DAAE,CA+HXuU,SAAS,EAAc,CAAC;IACvGtQ,IAAI,EAAEK,oDAAS;IACfJ,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE,+CAA+C;MACzD4L,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEc,UAAU;QAAEb,WAAW,EAAEc;MAAU,CAAC,CAAC;MAC5D1M,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEuM,eAAe,EAAE,CAAC;MAChCnQ,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,MAAMuQ,qBAAqB,GAAG,IAAIxG,yDAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyG,yBAAyB,CAAC;EAC5B;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACzM,KAAK,EAAE;IAChB,IAAI,CAAC0M,SAAS,GAAG1M,KAAK;IACtB,IAAI,IAAI,CAAC2M,aAAa,EAAE;MACpB,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;EACJ;EACA;EACA,IAAID,aAAaA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACE,cAAc;EAC9B;EACA,IAAIF,aAAaA,CAAC3M,KAAK,EAAE;IACrB,IAAI,CAAC6M,cAAc,GAAG7M,KAAK;IAC3B,IAAI,IAAI,CAAC6M,cAAc,EAAE;MACrB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IAC7B,CAAC,MACI;MACD,IAAI,CAACC,mBAAmB,CAAC9S,WAAW,CAAC,CAAC;IAC1C;EACJ;EACAnB,WAAWA,CAACiG,WAAW,EAAE;IACrB,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC2N,SAAS,GAAG,KAAK;IACtB,IAAI,CAACG,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACxT,eAAe,GAAGtB,qDAAM,CAAC6C,gFAAoB,CAAC;IACnD;IACA,IAAI,CAACE,OAAO,GAAG/C,qDAAM,CAACC,iDAAM,CAAC;IAC7B;IACA,IAAI,CAACgV,OAAO,GAAGjV,qDAAM,CAACwU,qBAAqB,CAAC;IAC5C;IACA,IAAI,CAACQ,mBAAmB,GAAG,IAAItG,8CAAY,CAAC,CAAC;EACjD;EACAtL,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4R,mBAAmB,CAAC9S,WAAW,CAAC,CAAC;EAC1C;EACA;EACAgT,QAAQA,CAAA,EAAG;IACP,OAAOC,mBAAmB,CAAC,IAAI,CAACnO,WAAW,CAACuC,aAAa,CAAC;EAC9D;EACA;EACA,IAAI7D,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACsB,WAAW,CAACuC,aAAa;EACzC;EACA;EACAsL,aAAaA,CAAA,EAAG;IACZ;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACAjJ,UAAU,CAAC,MAAM,IAAI,CAACqJ,OAAO,CAACG,mBAAmB,CAAC,CAAC,CAAC;EACxD;EACA;EACAL,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,mBAAmB,CAAC9S,WAAW,CAAC,CAAC;IACtC,IAAI,CAACa,OAAO,CAACE,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAAC+R,mBAAmB,GAAG,IAAI,CAAC1T,eAAe,CAC1CG,OAAO,CAAC,IAAI,CAACuF,WAAW,CAACuC,aAAa,EAAE;QAAEvH,GAAG,EAAE;MAAa,CAAC,CAAC,CAC9DD,SAAS,CAAC,MAAM,IAAI,CAAC8S,aAAa,CAAC,CAAC,CAAC;IAC9C,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACtR,IAAI,YAAA8R,kCAAA5R,CAAA;MAAA,YAAAA,CAAA,IAAwFgR,yBAAyB,EA/NnC1U,+DAAE,CA+NmDA,qDAAa;IAAA,CAA4C;EAAE;EAChN;IAAS,IAAI,CAACyH,IAAI,kBAhO8EzH,+DAAE;MAAAiE,IAAA,EAgOJyQ,yBAAyB;MAAA/M,SAAA;MAAA4E,SAAA;MAAA6G,QAAA;MAAA5G,YAAA,WAAA+I,uCAAA7I,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAhOvB1M,yDAAE,oCAAA2M,GAAA,CAAAgI,QAgOoB,CAAC;QAAA;MAAA;MAAA7H,MAAA;QAAA6H,QAAA;QAAAE,aAAA;MAAA;MAAAhN,UAAA;IAAA,EAAiS;EAAE;AAC9Z;AACA;EAAA,QAAA5E,SAAA,oBAAAA,SAAA,KAlOoGjD,+DAAE,CAkOX0U,yBAAyB,EAAc,CAAC;IACvHzQ,IAAI,EAAEK,oDAAS;IACfJ,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE,kCAAkC;MAC5CuF,IAAI,EAAE;QACF,OAAO,EAAE,2CAA2C;QACpD,yCAAyC,EAAE;MAC/C,CAAC;MACDxF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE5D,IAAI,EAAEjE,qDAAawH;EAAC,CAAC,CAAC,EAAkB;IAAEmN,QAAQ,EAAE,CAAC;MAC1E1Q,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEkQ,aAAa,EAAE,CAAC;MAChB5Q,IAAI,EAAEU,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,SAASyQ,mBAAmBA,CAACzP,OAAO,EAAE;EAClC;EACA;EACA;EACA;EACA,MAAM6P,MAAM,GAAG7P,OAAO;EACtB,IAAI6P,MAAM,CAACC,YAAY,KAAK,IAAI,EAAE;IAC9B,OAAOD,MAAM,CAACE,WAAW;EAC7B;EACA,MAAMC,KAAK,GAAGH,MAAM,CAACrL,SAAS,CAAC,IAAI,CAAC;EACpCwL,KAAK,CAAChM,KAAK,CAACiM,WAAW,CAAC,UAAU,EAAE,UAAU,CAAC;EAC/CD,KAAK,CAAChM,KAAK,CAACiM,WAAW,CAAC,WAAW,EAAE,6BAA6B,CAAC;EACnE1M,QAAQ,CAAC2M,eAAe,CAAClL,WAAW,CAACgL,KAAK,CAAC;EAC3C,MAAMD,WAAW,GAAGC,KAAK,CAACD,WAAW;EACrCC,KAAK,CAACnP,MAAM,CAAC,CAAC;EACd,OAAOkP,WAAW;AACtB;;AAEA;AACA,MAAMI,cAAc,GAAG,yBAAyB;AAChD;AACA,MAAMC,kBAAkB,GAAG,+BAA+B;AAC1D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,CAAC;EACzBhV,WAAWA,CAACiG,WAAW,EAAEgP,MAAM,EAAE;IAC7B,IAAI,CAAChP,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACiP,oBAAoB,GAAIjQ,KAAK,IAAK;MACnC,MAAME,SAAS,GAAG,IAAI,CAACc,WAAW,CAACuC,aAAa,CAACrD,SAAS;MAC1D,MAAMgQ,cAAc,GAAGhQ,SAAS,CAACC,QAAQ,CAAC2P,kBAAkB,CAAC;MAC7D,IAAI9P,KAAK,CAACmQ,YAAY,KAAK,SAAS,IAAID,cAAc,EAAE;QACpDhQ,SAAS,CAACK,MAAM,CAACsP,cAAc,EAAEC,kBAAkB,CAAC;MACxD;IACJ,CAAC;IACDE,MAAM,CAAC/S,iBAAiB,CAAC,MAAM;MAC3B+D,WAAW,CAACuC,aAAa,CAACpG,gBAAgB,CAAC,eAAe,EAAE,IAAI,CAAC8S,oBAAoB,CAAC;IAC1F,CAAC,CAAC;EACN;EACAG,QAAQA,CAAA,EAAG;IACP,MAAMlQ,SAAS,GAAG,IAAI,CAACc,WAAW,CAACuC,aAAa,CAACrD,SAAS;IAC1DA,SAAS,CAACK,MAAM,CAACuP,kBAAkB,CAAC;IACpC5P,SAAS,CAACE,GAAG,CAACyP,cAAc,CAAC;EACjC;EACAQ,UAAUA,CAAA,EAAG;IACT,IAAI,CAACrP,WAAW,CAACuC,aAAa,CAACrD,SAAS,CAACE,GAAG,CAAC0P,kBAAkB,CAAC;EACpE;EACA1S,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC4D,WAAW,CAACuC,aAAa,CAAClG,mBAAmB,CAAC,eAAe,EAAE,IAAI,CAAC4S,oBAAoB,CAAC;EAClG;EACA;IAAS,IAAI,CAAC1S,IAAI,YAAA+S,+BAAA7S,CAAA;MAAA,YAAAA,CAAA,IAAwFsS,sBAAsB,EA5ShChW,+DAAE,CA4SgDA,qDAAa,GA5S/DA,+DAAE,CA4S0EA,iDAAS;IAAA,CAA4C;EAAE;EACnO;IAAS,IAAI,CAACyH,IAAI,kBA7S8EzH,+DAAE;MAAAiE,IAAA,EA6SJ+R,sBAAsB;MAAArO,SAAA;MAAA4E,SAAA;MAAA1E,UAAA;IAAA,EAA2H;EAAE;AACrP;AACA;EAAA,QAAA5E,SAAA,oBAAAA,SAAA,KA/SoGjD,+DAAE,CA+SXgW,sBAAsB,EAAc,CAAC;IACpH/R,IAAI,EAAEK,oDAAS;IACfJ,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE,6BAA6B;MACvCuF,IAAI,EAAE;QACF,OAAO,EAAE;MACb,CAAC;MACDxF,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE5D,IAAI,EAAEjE,qDAAawH;EAAC,CAAC,EAAE;IAAEvD,IAAI,EAAEjE,iDAASE;EAAC,CAAC,CAAC;AAAA;;AAEhF;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsW,0BAA0B,CAAC;EAC7BxV,WAAWA,CAACiG,WAAW,EAAEjE,OAAO,EAAE;IAC9B,IAAI,CAACiE,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACjE,OAAO,GAAGA,OAAO;IACtB;IACA,IAAI,CAACyT,IAAI,GAAG,KAAK;EACrB;EACA5M,eAAeA,CAAA,EAAG;IACd,MAAM6M,KAAK,GAAG,IAAI,CAACzP,WAAW,CAACuC,aAAa,CAACmN,aAAa,CAAC,qBAAqB,CAAC;IACjF,IAAID,KAAK,EAAE;MACP,IAAI,CAACzP,WAAW,CAACuC,aAAa,CAACrD,SAAS,CAACE,GAAG,CAAC,+BAA+B,CAAC;MAC7E,IAAI,OAAOsF,qBAAqB,KAAK,UAAU,EAAE;QAC7C+K,KAAK,CAAC/M,KAAK,CAACiN,kBAAkB,GAAG,IAAI;QACrC,IAAI,CAAC5T,OAAO,CAACE,iBAAiB,CAAC,MAAM;UACjCyI,qBAAqB,CAAC,MAAO+K,KAAK,CAAC/M,KAAK,CAACiN,kBAAkB,GAAG,EAAG,CAAC;QACtE,CAAC,CAAC;MACN;IACJ,CAAC,MACI;MACD,IAAI,CAAC3P,WAAW,CAACuC,aAAa,CAACrD,SAAS,CAACE,GAAG,CAAC,+BAA+B,CAAC;IACjF;EACJ;EACAwQ,cAAcA,CAACC,UAAU,EAAE;IACvB,IAAI,CAAC,IAAI,CAACL,IAAI,IAAI,CAACK,UAAU,EAAE;MAC3B,IAAI,CAACC,MAAM,CAACvN,aAAa,CAACG,KAAK,CAACqN,KAAK,GAAG,EAAE;IAC9C,CAAC,MACI;MACD,MAAMC,qBAAqB,GAAG,CAAC;MAC/B,MAAMC,oBAAoB,GAAG,CAAC;MAC9B,IAAI,CAACH,MAAM,CAACvN,aAAa,CAACG,KAAK,CAACqN,KAAK,GAAG,QAAQF,UAAU,+DAA+DG,qBAAqB,GAAGC,oBAAoB,KAAK;IAC9K;EACJ;EACA;IAAS,IAAI,CAAC1T,IAAI,YAAA2T,mCAAAzT,CAAA;MAAA,YAAAA,CAAA,IAAwF8S,0BAA0B,EAhWpCxW,+DAAE,CAgWoDA,qDAAa,GAhWnEA,+DAAE,CAgW8EA,iDAAS;IAAA,CAA4C;EAAE;EACvO;IAAS,IAAI,CAACoX,IAAI,kBAjW8EpX,+DAAE;MAAAiE,IAAA,EAiWJuS,0BAA0B;MAAA7O,SAAA;MAAA2P,SAAA,WAAAC,iCAAA7K,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjWxB1M,yDAAE,CAAAsP,GAAA;QAAA;QAAA,IAAA5C,EAAA;UAAA,IAAA+K,EAAA;UAAFzX,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAoK,MAAA,GAAAU,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAArL,SAAA;MAAA6G,QAAA;MAAA5G,YAAA,WAAAqL,wCAAAnL,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1M,yDAAE,iCAAA2M,GAAA,CAAA8J,IAiWqB,CAAC;QAAA;MAAA;MAAA3J,MAAA;QAAA2J,IAAA,GAjWxBzW,0DAAE,CAAAgN,IAAA;MAAA;MAAAnF,UAAA;MAAAsF,QAAA,GAAFnN,iEAAE;MAAA+X,KAAA,EAAAxI,GAAA;MAAAyI,kBAAA,EAAAxI,GAAA;MAAAyI,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAA3L,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1M,6DAAE;UAAFA,uDAAE,YAiWib,CAAC;UAjWpbA,4DAAE,eAiWoe,CAAC;UAjWveA,0DAAE,EAiWigB,CAAC;UAjWpgBA,0DAAE,CAiWygB,CAAC;UAjW5gBA,uDAAE,YAiW8jB,CAAC;QAAA;MAAA;MAAAuY,aAAA;MAAAC,eAAA;IAAA,EAAoG;EAAE;AAC3wB;AACA;EAAA,QAAAvV,SAAA,oBAAAA,SAAA,KAnWoGjD,+DAAE,CAmWXwW,0BAA0B,EAAc,CAAC;IACxHvS,IAAI,EAAEkK,oDAAS;IACfjK,IAAI,EAAE,CAAC;MAAE4D,QAAQ,EAAE,iCAAiC;MAAEuF,IAAI,EAAE;QAChD,OAAO,EAAE,qBAAqB;QAC9B;QACA;QACA,sCAAsC,EAAE;MAC5C,CAAC;MAAEmL,eAAe,EAAEpK,kEAAuB,CAACqK,MAAM;MAAEF,aAAa,EAAElK,4DAAiB,CAACrB,IAAI;MAAEnF,UAAU,EAAE,IAAI;MAAEuQ,QAAQ,EAAE;IAAoM,CAAC;EACxU,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEnU,IAAI,EAAEjE,qDAAawH;EAAC,CAAC,EAAE;IAAEvD,IAAI,EAAEjE,iDAASE;EAAC,CAAC,CAAC,EAAkB;IAAEuW,IAAI,EAAE,CAAC;MAC3FxS,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC,gCAAgC;IAC3C,CAAC,CAAC;IAAE6S,MAAM,EAAE,CAAC;MACT9S,IAAI,EAAEqK,oDAAS;MACfpK,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMwU,sBAAsB,GAAG;EAC3B;EACAC,kBAAkB,EAAE7J,4DAAO,CAAC,oBAAoB,EAAE;EAC9C;EACAC,0DAAK,CAAC,OAAO,EAAEpF,0DAAK,CAAC;IAAEiP,OAAO,EAAE,CAAC;IAAEpL,SAAS,EAAE;EAAiB,CAAC,CAAC,CAAC,EAClEwB,+DAAU,CAAC,eAAe,EAAE,CACxBrF,0DAAK,CAAC;IAAEiP,OAAO,EAAE,CAAC;IAAEpL,SAAS,EAAE;EAAmB,CAAC,CAAC,EACpDyB,4DAAO,CAAC,wCAAwC,CAAC,CACpD,CAAC,CACL;AACL,CAAC;;AAED;AACA,MAAM4J,mBAAmB,CAAC;EACtB;IAAS,IAAI,CAACrV,IAAI,YAAAsV,4BAAApV,CAAA;MAAA,YAAAA,CAAA,IAAwFmV,mBAAmB;IAAA,CAAmD;EAAE;EAClL;IAAS,IAAI,CAACpR,IAAI,kBAtY8EzH,+DAAE;MAAAiE,IAAA,EAsYJ4U;IAAmB,EAAiB;EAAE;AACxI;AACA;EAAA,QAAA5V,SAAA,oBAAAA,SAAA,KAxYoGjD,+DAAE,CAwYX6Y,mBAAmB,EAAc,CAAC;IACjH5U,IAAI,EAAEK,oDAASA;EACnB,CAAC,CAAC;AAAA;;AAEV;AACA,SAASyU,uCAAuCA,CAAA,EAAG;EAC/C,OAAOC,KAAK,CAAC,8DAA8D,CAAC;AAChF;AACA;AACA,SAASC,kCAAkCA,CAACpF,KAAK,EAAE;EAC/C,OAAOmF,KAAK,CAAC,2CAA2CnF,KAAK,KAAK,CAAC;AACvE;AACA;AACA,SAASqF,kCAAkCA,CAAA,EAAG;EAC1C,OAAOF,KAAK,CAAC,oDAAoD,CAAC;AACtE;;AAEA;AACA;AACA;AACA;AACA;AACA,MAAMG,cAAc,GAAG,IAAIlL,yDAAc,CAAC,cAAc,CAAC;AACzD;AACA;AACA;AACA;AACA,MAAMmL,8BAA8B,GAAG,IAAInL,yDAAc,CAAC,gCAAgC,CAAC;AAC3F,IAAIoL,YAAY,GAAG,CAAC;AACpB;AACA,MAAMC,kBAAkB,GAAG,MAAM;AACjC;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,MAAM;AAClC;AACA,MAAMC,wBAAwB,GAAG,OAAO;AACxC;AACA;AACA;AACA;AACA;AACA,MAAMC,uCAAuC,GAAG,kBAAkB;AAClE;AACA,MAAMC,YAAY,CAAC;EACf;EACA,IAAIzI,kBAAkBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC0I,mBAAmB;EACnC;EACA,IAAI1I,kBAAkBA,CAAC/I,KAAK,EAAE;IAC1B,IAAI,CAACyR,mBAAmB,GAAG9K,4EAAqB,CAAC3G,KAAK,CAAC;EAC3D;EACA;EACA,IAAI0R,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW,IAAI,IAAI,CAACC,SAAS,EAAEF,UAAU,IAAIL,mBAAmB;EAChF;EACA,IAAIK,UAAUA,CAAC1R,KAAK,EAAE;IAClB,IAAIA,KAAK,KAAK,IAAI,CAAC2R,WAAW,EAAE;MAC5B,IAAI,CAACA,WAAW,GAAG3R,KAAK;MACxB;MACA;MACA;MACA;MACA,IAAI,CAAC6R,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C;EACJ;EACA;EACA,IAAIC,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACC,WAAW;EAC3B;EACA,IAAID,UAAUA,CAAC/R,KAAK,EAAE;IAClB,MAAMiS,QAAQ,GAAG,IAAI,CAACD,WAAW;IACjC,MAAME,aAAa,GAAGlS,KAAK,IAAI,IAAI,CAAC4R,SAAS,EAAEG,UAAU,IAAIX,kBAAkB;IAC/E,IAAI,OAAOrW,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,IAAImX,aAAa,KAAK,MAAM,IAAIA,aAAa,KAAK,SAAS,EAAE;QACzD,MAAM,IAAIpB,KAAK,CAAC,qCAAqCoB,aAAa,0CAA0C,CAAC;MACjH;IACJ;IACA,IAAI,CAACF,WAAW,GAAGE,aAAa;IAChC,IAAI,IAAI,CAACF,WAAW,KAAK,SAAS,IAAI,IAAI,CAACA,WAAW,KAAKC,QAAQ,EAAE;MACjE;MACA;MACA;MACA,IAAI,CAACE,sCAAsC,GAAG,IAAI;IACtD;EACJ;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIC,eAAeA,CAAA,EAAG;IAClB,OAAO,IAAI,CAACC,gBAAgB,IAAI,IAAI,CAACT,SAAS,EAAEQ,eAAe,IAAId,wBAAwB;EAC/F;EACA,IAAIc,eAAeA,CAACpS,KAAK,EAAE;IACvB,IAAI,CAACqS,gBAAgB,GAAGrS,KAAK,IAAI,IAAI,CAAC4R,SAAS,EAAEQ,eAAe,IAAId,wBAAwB;EAChG;EACA;EACA,IAAI/G,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAAC+H,UAAU;EAC1B;EACA,IAAI/H,SAASA,CAACvK,KAAK,EAAE;IACjB,IAAI,CAACsS,UAAU,GAAGtS,KAAK;IACvB,IAAI,CAACuS,aAAa,CAAC,CAAC;EACxB;EACA;EACA,IAAI7J,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC8J,yBAAyB,IAAI,IAAI,CAACC,iBAAiB;EACnE;EACA,IAAI/J,QAAQA,CAAC1I,KAAK,EAAE;IAChB,IAAI,CAACwS,yBAAyB,GAAGxS,KAAK;EAC1C;EACAlH,WAAWA,CAACiG,WAAW,EAAE8S,kBAAkB,EAAE/W,OAAO,EAAE4X,IAAI,EAAEtV,SAAS,EAAEwU,SAAS,EAAEe,cAAc;EAChG;AACJ;AACA;AACA;EACIC,eAAe,EAAE;IACb,IAAI,CAAC7T,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC8S,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC/W,OAAO,GAAGA,OAAO;IACtB,IAAI,CAAC4X,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACtV,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACwU,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACe,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAClB,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI,CAACoB,KAAK,GAAG,SAAS;IACtB,IAAI,CAACb,WAAW,GAAGZ,kBAAkB;IACrC,IAAI,CAACiB,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB,IAAI,CAACQ,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B,IAAI,CAACC,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACzK,QAAQ,GAAG,4BAA4B2I,YAAY,EAAE,EAAE;IAC5D;IACA,IAAI,CAAC9G,YAAY,GAAG,gBAAgB8G,YAAY,EAAE,EAAE;IACpD;IACA,IAAI,CAACjH,wBAAwB,GAAG,EAAE;IAClC,IAAI,CAAClR,UAAU,GAAG,IAAId,yCAAO,CAAC,CAAC;IAC/B,IAAI,CAACgb,UAAU,GAAG,IAAI;IACtB,IAAI,CAACf,sCAAsC,GAAG,KAAK;IACnD,IAAIP,SAAS,EAAE;MACX,IAAIA,SAAS,CAACG,UAAU,EAAE;QACtB,IAAI,CAACA,UAAU,GAAGH,SAAS,CAACG,UAAU;MAC1C;MACA,IAAI,CAACN,mBAAmB,GAAG0B,OAAO,CAACvB,SAAS,EAAE7I,kBAAkB,CAAC;MACjE,IAAI6I,SAAS,CAACiB,KAAK,EAAE;QACjB,IAAI,CAACA,KAAK,GAAGjB,SAAS,CAACiB,KAAK;MAChC;IACJ;EACJ;EACAlR,eAAeA,CAAA,EAAG;IACd;IACA;IACA,IAAI,CAACyR,iBAAiB,CAAC,CAAC;IACxB;IACA,IAAI,CAAClJ,wBAAwB,GAAG,OAAO;IACvC;IACA;IACA,IAAI,CAAC2H,kBAAkB,CAACwB,aAAa,CAAC,CAAC;EAC3C;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,IAAI,CAACC,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,0BAA0B,CAAC,CAAC;IACjC,IAAI,CAACC,0CAA0C,CAAC,CAAC;EACrD;EACAC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACL,uBAAuB,CAAC,CAAC;EAClC;EACApY,WAAWA,CAAA,EAAG;IACV,IAAI,CAACnC,UAAU,CAACO,IAAI,CAAC,CAAC;IACtB,IAAI,CAACP,UAAU,CAAC0B,QAAQ,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;EACImZ,UAAUA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC3K,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACV,QAAQ,GAAG,IAAI;EAC1D;EACA;AACJ;AACA;AACA;EACIsL,yBAAyBA,CAAA,EAAG;IACxB,OAAO,IAAI,CAACC,UAAU,IAAI,IAAI,CAAChV,WAAW;EAC9C;EACA;EACAiV,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC9K,iBAAiB,CAAC,CAAC,EAAE;MAC1B,IAAI,CAACwI,UAAU,GAAG,QAAQ;IAC9B;EACJ;EACA;EACA8B,kBAAkBA,CAAA,EAAG;IACjB,MAAMS,OAAO,GAAG,IAAI,CAACvL,QAAQ;IAC7B,IAAIuL,OAAO,CAACC,WAAW,EAAE;MACrB,IAAI,CAACnV,WAAW,CAACuC,aAAa,CAACrD,SAAS,CAACE,GAAG,CAAC,2BAA2B8V,OAAO,CAACC,WAAW,EAAE,CAAC;IAClG;IACA;IACAD,OAAO,CAACE,YAAY,CAACra,SAAS,CAAC,MAAM;MACjC,IAAI,CAACsZ,iBAAiB,CAAC,CAAC;MACxB,IAAI,CAACgB,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACvC,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACF;IACA,IAAImC,OAAO,CAACI,SAAS,IAAIJ,OAAO,CAACI,SAAS,CAACC,YAAY,EAAE;MACrDL,OAAO,CAACI,SAAS,CAACC,YAAY,CACzBna,IAAI,CAAC7B,yDAAS,CAAC,IAAI,CAACU,UAAU,CAAC,CAAC,CAChCc,SAAS,CAAC,MAAM,IAAI,CAAC+X,kBAAkB,CAACC,YAAY,CAAC,CAAC,CAAC;IAChE;EACJ;EACAyC,0BAA0BA,CAAA,EAAG;IACzB,IAAI,CAACzB,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC0B,eAAe,CAACC,IAAI,CAACC,CAAC,IAAI,CAACA,CAAC,CAACzI,OAAO,CAAC;IAClE,IAAI,CAAC8G,cAAc,GAAG,CAAC,CAAC,IAAI,CAACyB,eAAe,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzI,OAAO,CAAC;IACjE,IAAI,CAAC+G,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC2B,eAAe,CAACF,IAAI,CAACG,CAAC,IAAI,CAACA,CAAC,CAAC3I,OAAO,CAAC;IAClE,IAAI,CAACgH,cAAc,GAAG,CAAC,CAAC,IAAI,CAAC0B,eAAe,CAACF,IAAI,CAACG,CAAC,IAAIA,CAAC,CAAC3I,OAAO,CAAC;EACrE;EACA;EACAyH,0BAA0BA,CAAA,EAAG;IACzB,IAAI,CAACa,0BAA0B,CAAC,CAAC;IACjC;IACA;IACA;IACA7N,2CAAK,CAAC,IAAI,CAAC8N,eAAe,CAACK,OAAO,EAAE,IAAI,CAACF,eAAe,CAACE,OAAO,CAAC,CAAC/a,SAAS,CAAC,MAAM;MAC9E,IAAI,CAACya,0BAA0B,CAAC,CAAC;MACjC,IAAI,CAAC1C,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACI2B,oBAAoBA,CAAA,EAAG;IACnB;IACA,IAAI,CAACqB,aAAa,CAACD,OAAO,CAAC/a,SAAS,CAAC,MAAM;MACvC,IAAI,CAACyY,aAAa,CAAC,CAAC;MACpB,IAAI,CAACV,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACF;IACA,IAAI,CAACiD,cAAc,CAACF,OAAO,CAAC/a,SAAS,CAAC,MAAM;MACxC,IAAI,CAACsa,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACvC,kBAAkB,CAACC,YAAY,CAAC,CAAC;IAC1C,CAAC,CAAC;IACF;IACA,IAAI,CAACkD,cAAc,CAAC,CAAC;IACrB,IAAI,CAACZ,mBAAmB,CAAC,CAAC;EAC9B;EACA;EACAb,uBAAuBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAAC7K,QAAQ,KAAK,OAAO3N,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnE,MAAMiW,kCAAkC,CAAC,CAAC;IAC9C;EACJ;EACAoC,iBAAiBA,CAAA,EAAG;IAChB;IACA;IACA;IACA;IACA;IACA,IAAI,IAAI,CAAC1K,QAAQ,CAACuM,OAAO,IAAI,CAAC,IAAI,CAAC/B,UAAU,EAAE;MAC3C,IAAI,CAACA,UAAU,GAAG,IAAI;MACtB,IAAI,CAACgC,WAAW,EAAE/G,QAAQ,CAAC,CAAC;IAChC,CAAC,MACI,IAAI,CAAC,IAAI,CAACzF,QAAQ,CAACuM,OAAO,KAAK,IAAI,CAAC/B,UAAU,IAAI,IAAI,CAACA,UAAU,KAAK,IAAI,CAAC,EAAE;MAC9E,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB,IAAI,CAACgC,WAAW,EAAE9G,UAAU,CAAC,CAAC;IAClC;IACA,IAAI,CAAC2F,UAAU,EAAEzS,aAAa,CAACrD,SAAS,CAACkX,MAAM,CAAC,yBAAyB,EAAE,IAAI,CAACzM,QAAQ,CAACuM,OAAO,CAAC;EACrG;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACItB,0CAA0CA,CAAA,EAAG;IACzC;IACA,IAAI,CAACa,eAAe,CAACK,OAAO,CAAC/a,SAAS,CAAC,MAAO,IAAI,CAACqY,sCAAsC,GAAG,IAAK,CAAC;IAClG;IACA;IACA,IAAI,CAACrX,OAAO,CAACE,iBAAiB,CAAC,MAAM;MACjC,IAAI,CAACF,OAAO,CAACsa,QAAQ,CAACjb,IAAI,CAAC7B,yDAAS,CAAC,IAAI,CAACU,UAAU,CAAC,CAAC,CAACc,SAAS,CAAC,MAAM;QACnE,IAAI,IAAI,CAACqY,sCAAsC,EAAE;UAC7C,IAAI,CAACA,sCAAsC,GAAG,KAAK;UACnD,IAAI,CAACkD,yBAAyB,CAAC,CAAC;QACpC;MACJ,CAAC,CAAC;IACN,CAAC,CAAC;IACF,IAAI,CAAC3C,IAAI,CAAC4C,MAAM,CACXnb,IAAI,CAAC7B,yDAAS,CAAC,IAAI,CAACU,UAAU,CAAC,CAAC,CAChCc,SAAS,CAAC,MAAO,IAAI,CAACqY,sCAAsC,GAAG,IAAK,CAAC;EAC9E;EACA;EACAoD,kBAAkBA,CAAA,EAAG;IACjB,OAAO,IAAI,CAAC7D,UAAU,KAAK,QAAQ;EACvC;EACAnJ,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAACwJ,UAAU,KAAK,SAAS;EACxC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACItI,uBAAuBA,CAAA,EAAG;IACtB,OAAO,CAAC,IAAI,CAACrM,SAAS,CAACI,SAAS,IAAI,IAAI,CAACgX,eAAe,CAACgB,MAAM,IAAI,CAAC,IAAI,CAAClN,iBAAiB,CAAC,CAAC;EAChG;EACAY,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,CAAC,IAAI,CAACuM,oBAAoB,IAAI,CAAC,CAAC,IAAI,CAACC,iBAAiB;EAClE;EACApN,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI,CAACI,QAAQ,CAACiN,gBAAgB,IAAI,IAAI,CAACJ,kBAAkB,CAAC,CAAC;EACtE;EACA;AACJ;AACA;AACA;EACIK,cAAcA,CAACC,IAAI,EAAE;IACjB,MAAM5B,OAAO,GAAG,IAAI,CAACvL,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAAC2L,SAAS,GAAG,IAAI;IAC9D,OAAOJ,OAAO,IAAIA,OAAO,CAAC4B,IAAI,CAAC;EACnC;EACA;EACAC,qBAAqBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACf,cAAc,IAAI,IAAI,CAACA,cAAc,CAACS,MAAM,GAAG,CAAC,IAAI,IAAI,CAAC9M,QAAQ,CAACqN,UAAU,GAClF,OAAO,GACP,MAAM;EAChB;EACA;EACA5I,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAAC6I,yBAAyB,CAAC,CAAC;EACpC;EACA;EACAA,yBAAyBA,CAAA,EAAG;IACxB,IAAI,CAAC,IAAI,CAACzN,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC0N,cAAc,IAAI,CAAC,IAAI,CAAC3N,iBAAiB,CAAC,CAAC,EAAE;MAC1E,IAAI,CAAC4N,eAAe,EAAEvH,cAAc,CAAC,CAAC,CAAC;IAC3C,CAAC,MACI;MACD,IAAI,CAACuH,eAAe,EAAEvH,cAAc,CAAC,IAAI,CAACsH,cAAc,CAAChJ,QAAQ,CAAC,CAAC,CAAC;IACxE;EACJ;EACA;EACAsF,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACyC,cAAc,CAAC,CAAC;IACrB,IAAI,CAACZ,mBAAmB,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;EACIY,cAAcA,CAAA,EAAG;IACb,IAAI,IAAI,CAACF,aAAa,KAAK,OAAO/Z,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACvE,IAAIob,SAAS;MACb,IAAIC,OAAO;MACX,IAAI,CAACtB,aAAa,CAACrW,OAAO,CAAE4X,IAAI,IAAK;QACjC,IAAIA,IAAI,CAAC1K,KAAK,KAAK,OAAO,EAAE;UACxB,IAAIwK,SAAS,IAAI,IAAI,CAAC5L,SAAS,EAAE;YAC7B,MAAMwG,kCAAkC,CAAC,OAAO,CAAC;UACrD;UACAoF,SAAS,GAAGE,IAAI;QACpB,CAAC,MACI,IAAIA,IAAI,CAAC1K,KAAK,KAAK,KAAK,EAAE;UAC3B,IAAIyK,OAAO,EAAE;YACT,MAAMrF,kCAAkC,CAAC,KAAK,CAAC;UACnD;UACAqF,OAAO,GAAGC,IAAI;QAClB;MACJ,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;EACIjC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,IAAI,CAAC1L,QAAQ,EAAE;MACf,IAAI4N,GAAG,GAAG,EAAE;MACZ;MACA,IAAI,IAAI,CAAC5N,QAAQ,CAAC6N,mBAAmB,IACjC,OAAO,IAAI,CAAC7N,QAAQ,CAAC6N,mBAAmB,KAAK,QAAQ,EAAE;QACvDD,GAAG,CAACE,IAAI,CAAC,GAAG,IAAI,CAAC9N,QAAQ,CAAC6N,mBAAmB,CAACE,KAAK,CAAC,GAAG,CAAC,CAAC;MAC7D;MACA,IAAI,IAAI,CAACX,qBAAqB,CAAC,CAAC,KAAK,MAAM,EAAE;QACzC,MAAMK,SAAS,GAAG,IAAI,CAACrB,aAAa,GAC9B,IAAI,CAACA,aAAa,CAACL,IAAI,CAAC4B,IAAI,IAAIA,IAAI,CAAC1K,KAAK,KAAK,OAAO,CAAC,GACvD,IAAI;QACV,MAAMyK,OAAO,GAAG,IAAI,CAACtB,aAAa,GAC5B,IAAI,CAACA,aAAa,CAACL,IAAI,CAAC4B,IAAI,IAAIA,IAAI,CAAC1K,KAAK,KAAK,KAAK,CAAC,GACrD,IAAI;QACV,IAAIwK,SAAS,EAAE;UACXG,GAAG,CAACE,IAAI,CAACL,SAAS,CAACvN,EAAE,CAAC;QAC1B,CAAC,MACI,IAAI,IAAI,CAAC0J,UAAU,EAAE;UACtBgE,GAAG,CAACE,IAAI,CAAC,IAAI,CAACnM,YAAY,CAAC;QAC/B;QACA,IAAI+L,OAAO,EAAE;UACTE,GAAG,CAACE,IAAI,CAACJ,OAAO,CAACxN,EAAE,CAAC;QACxB;MACJ,CAAC,MACI,IAAI,IAAI,CAACmM,cAAc,EAAE;QAC1BuB,GAAG,CAACE,IAAI,CAAC,GAAG,IAAI,CAACzB,cAAc,CAAC2B,GAAG,CAAC9d,KAAK,IAAIA,KAAK,CAACgQ,EAAE,CAAC,CAAC;MAC3D;MACA,IAAI,CAACF,QAAQ,CAACiO,iBAAiB,CAACL,GAAG,CAAC;IACxC;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIjB,yBAAyBA,CAAA,EAAG;IACxB,IAAI,CAAC,IAAI,CAACjY,SAAS,CAACI,SAAS,IAAI,CAAC,IAAI,CAAC+K,WAAW,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC0N,cAAc,EAAE;MAC1E;IACJ;IACA,MAAMW,aAAa,GAAG,IAAI,CAACX,cAAc,CAACxY,OAAO;IACjD;IACA;IACA,IAAI,EAAE,IAAI,CAACoZ,oBAAoB,IAAI,IAAI,CAACC,oBAAoB,CAAC,EAAE;MAC3DF,aAAa,CAACnV,KAAK,CAAC6D,SAAS,GAAG,EAAE;MAClC;IACJ;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACyR,gBAAgB,CAAC,CAAC,EAAE;MAC1B,IAAI,CAAC5E,sCAAsC,GAAG,IAAI;MAClD;IACJ;IACA,MAAM6E,mBAAmB,GAAG,IAAI,CAACH,oBAAoB,EAAEvV,aAAa;IACpE,MAAM2V,mBAAmB,GAAG,IAAI,CAACH,oBAAoB,EAAExV,aAAa;IACpE,MAAM4V,wBAAwB,GAAGF,mBAAmB,EAAEG,qBAAqB,CAAC,CAAC,CAACrI,KAAK,IAAI,CAAC;IACxF,MAAMsI,wBAAwB,GAAGH,mBAAmB,EAAEE,qBAAqB,CAAC,CAAC,CAACrI,KAAK,IAAI,CAAC;IACxF;IACA;IACA,MAAMuI,MAAM,GAAG,IAAI,CAAC3E,IAAI,CAAC1S,KAAK,KAAK,KAAK,GAAG,IAAI,GAAG,GAAG;IACrD,MAAMsX,WAAW,GAAG,GAAGJ,wBAAwB,GAAGE,wBAAwB,IAAI;IAC9E,MAAMG,WAAW,GAAG,+CAA+C;IACnE,MAAMC,qBAAqB,GAAG,QAAQH,MAAM,OAAOC,WAAW,MAAMC,WAAW,IAAI;IACnF;IACA;IACA;IACAX,aAAa,CAACnV,KAAK,CAAC6D,SAAS,GAAG;AACxC;AACA,UAAUiM,uCAAuC,eAAeiG,qBAAqB;AACrF,MAAM;EACF;EACA;EACAT,gBAAgBA,CAAA,EAAG;IACf,MAAMtZ,OAAO,GAAG,IAAI,CAACsB,WAAW,CAACuC,aAAa;IAC9C,IAAI7D,OAAO,CAACga,WAAW,EAAE;MACrB,MAAMC,QAAQ,GAAGja,OAAO,CAACga,WAAW,CAAC,CAAC;MACtC;MACA;MACA,OAAOC,QAAQ,IAAIA,QAAQ,KAAKja,OAAO;IAC3C;IACA;IACA;IACA,OAAOuD,QAAQ,CAAC2M,eAAe,CAACzP,QAAQ,CAACT,OAAO,CAAC;EACrD;EACA;IAAS,IAAI,CAACnC,IAAI,YAAAqc,qBAAAnc,CAAA;MAAA,YAAAA,CAAA,IAAwFgW,YAAY,EA32BtB1Z,+DAAE,CA22BsCA,qDAAa,GA32BrDA,+DAAE,CA22BgEA,4DAAoB,GA32BtFA,+DAAE,CA22BiGA,iDAAS,GA32B5GA,+DAAE,CA22BuHmE,6DAAiB,GA32B1InE,+DAAE,CA22BqJ0O,2DAAW,GA32BlK1O,+DAAE,CA22B6KoZ,8BAA8B,MA32B7MpZ,+DAAE,CA22BwOuO,gEAAqB,MA32B/PvO,+DAAE,CA22B0RkF,sDAAQ;IAAA,CAA4C;EAAE;EAClb;IAAS,IAAI,CAACkS,IAAI,kBA52B8EpX,+DAAE;MAAAiE,IAAA,EA42BJyV,YAAY;MAAA/R,SAAA;MAAAqY,cAAA,WAAAC,4BAAAvT,EAAA,EAAAC,GAAA,EAAAuT,QAAA;QAAA,IAAAxT,EAAA;UA52BV1M,4DAAE,CAAAkgB,QAAA,EA+2BjBvN,QAAQ;UA/2BO3S,4DAAE,CAAAkgB,QAAA,EA+2B2EvN,QAAQ;UA/2BrF3S,4DAAE,CAAAkgB,QAAA,EA+2BqLrH,mBAAmB;UA/2B1M7Y,4DAAE,CAAAkgB,QAAA,EA+2B6QjM,UAAU;UA/2BzRjU,4DAAE,CAAAkgB,QAAA,EA+2B4V5L,UAAU;UA/2BxWtU,4DAAE,CAAAkgB,QAAA,EA+2B0apN,SAAS;UA/2Brb9S,4DAAE,CAAAkgB,QAAA,EA+2BsftM,OAAO;QAAA;QAAA,IAAAlH,EAAA;UAAA,IAAA+K,EAAA;UA/2B/fzX,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAgR,oBAAA,GAAAlG,EAAA,CAAAG,KAAA;UAAF5X,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAiR,iBAAA,GAAAnG,EAAA,CAAAG,KAAA;UAAF5X,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAgO,iBAAA,GAAAlD,EAAA,CAAAG,KAAA;UAAF5X,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAA+P,eAAA,GAAAjF,EAAA;UAAFzX,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAkQ,eAAA,GAAApF,EAAA;UAAFzX,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAsQ,cAAA,GAAAxF,EAAA;UAAFzX,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAqQ,aAAA,GAAAvF,EAAA;QAAA;MAAA;MAAAH,SAAA,WAAA8I,mBAAA1T,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1M,yDAAE,CAAAyP,GAAA;UAAFzP,yDAAE,CAAA0P,GAAA;UAAF1P,yDAAE,CAAA2P,GAAA;UAAF3P,yDAAE,CA+2Bm5B0U,yBAAyB;UA/2B96B1U,yDAAE,CA+2B8/BwW,0BAA0B;UA/2B1hCxW,yDAAE,CA+2BsmCgW,sBAAsB;QAAA;QAAA,IAAAtJ,EAAA;UAAA,IAAA+K,EAAA;UA/2B9nCzX,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAsP,UAAA,GAAAxE,EAAA,CAAAG,KAAA;UAAF5X,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAoS,oBAAA,GAAAtH,EAAA,CAAAG,KAAA;UAAF5X,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAqS,oBAAA,GAAAvH,EAAA,CAAAG,KAAA;UAAF5X,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAwR,cAAA,GAAA1G,EAAA,CAAAG,KAAA;UAAF5X,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAyR,eAAA,GAAA3G,EAAA,CAAAG,KAAA;UAAF5X,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAyQ,WAAA,GAAA3F,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAArL,SAAA;MAAA6G,QAAA;MAAA5G,YAAA,WAAA6T,0BAAA3T,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1M,yDAAE,0CA42BJ2M,GAAA,CAAA8Q,kBAAA,CAAmB,CAAR,CAAC,uCAAA9Q,GAAA,CAAAqO,cAAD,CAAC,uCAAArO,GAAA,CAAAuO,cAAD,CAAC,2BAAAvO,GAAA,CAAAiE,QAAA,CAAAqN,UAAD,CAAC,4BAAAtR,GAAA,CAAAiE,QAAA,CAAA0P,QAAD,CAAC,8BAAA3T,GAAA,CAAAiE,QAAA,CAAA2P,UAAD,CAAC,iCAAA5T,GAAA,CAAAkO,cAAA,KAAO,gBAAR,CAAC,mCAAAlO,GAAA,CAAAsN,UAAA,IAAE,MAAH,CAAC,sCAAAtN,GAAA,CAAAsN,UAAA,IAAE,SAAH,CAAC,oCAAZtN,GAAA,CAAAyE,iBAAA,CAAkB,CAAC,KAAKzE,GAAA,CAAA6D,iBAAA,CAAkB,CAA/B,CAAC,gBAAA7D,GAAA,CAAAiE,QAAA,CAAAuM,OAAD,CAAC,gBAAAxQ,GAAA,CAAAoO,KAAA,KAAF,QAAQ,IAAApO,GAAA,CAAAoO,KAAA,KAAc,MAArB,CAAC,eAAApO,GAAA,CAAAoO,KAAA,KAAF,QAAC,CAAC,aAAApO,GAAA,CAAAoO,KAAA,KAAF,MAAC,CAAC,iBAAZpO,GAAA,CAAAmR,cAAA,CAAe,WAAW,CAAf,CAAC,eAAZnR,GAAA,CAAAmR,cAAA,CAAe,SAAS,CAAb,CAAC,gBAAZnR,GAAA,CAAAmR,cAAA,CAAe,UAAU,CAAd,CAAC,aAAZnR,GAAA,CAAAmR,cAAA,CAAe,OAAO,CAAX,CAAC,aAAZnR,GAAA,CAAAmR,cAAA,CAAe,OAAO,CAAX,CAAC,eAAZnR,GAAA,CAAAmR,cAAA,CAAe,SAAS,CAAb,CAAC,eAAZnR,GAAA,CAAAmR,cAAA,CAAe,SAAS,CAAb,CAAC;QAAA;MAAA;MAAAhR,MAAA;QAAAmE,kBAAA;QAAA8J,KAAA;QAAAnB,UAAA;QAAAK,UAAA;QAAAK,eAAA;QAAA7H,SAAA;MAAA;MAAAvF,QAAA;MAAArF,UAAA;MAAAsF,QAAA,GA52BVnN,gEAAE,CA42By/C,CACnlD;QAAEwT,OAAO,EAAE2F,cAAc;QAAE1F,WAAW,EAAEiG;MAAa,CAAC,EACtD;QAAElG,OAAO,EAAEiB,qBAAqB;QAAEhB,WAAW,EAAEiG;MAAa,CAAC,CAChE,GA/2B2F1Z,iEAAE;MAAAgY,kBAAA,EAAAnI,GAAA;MAAAoI,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAoI,sBAAA9T,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA+T,GAAA,GAAFzgB,8DAAE;UAAFA,6DAAE,CAAA4P,GAAA;UAAF5P,wDAAE,IAAAmR,mCAAA,gCAAFnR,oEA+2BsuC,CAAC;UA/2BvuCA,4DAAE,eA+2By4G,CAAC;UA/2B54GA,wDAAE,mBAAA4gB,2CAAAC,MAAA;YAAF7gB,2DAAE,CAAAygB,GAAA;YAAA,OAAFzgB,yDAAE,CA+2Bs2G2M,GAAA,CAAAiE,QAAA,CAAAoQ,gBAAA,CAAAH,MAAgC,CAAC;UAAA,CAAC,CAAC;UA/2B34G7gB,wDAAE,IAAAqR,mCAAA,gBA+2By7G,CAAC;UA/2B57GrR,4DAAE,YA+2BqiH,CAAC;UA/2BxiHA,wDAAE,IAAA0R,mCAAA,gBA+2BgkH,CAAC,IAAAE,mCAAA,gBAAsR,CAAC,IAAAC,mCAAA,gBAAwM,CAAC;UA/2BniI7R,4DAAE,aA+2B8uI,CAAC;UA/2BjvIA,wDAAE,KAAA+R,oCAAA,gBA+2ByyI,CAAC;UA/2B5yI/R,0DAAE,GA+2B+5I,CAAC;UA/2Bl6IA,0DAAE,CA+2B26I,CAAC;UA/2B96IA,wDAAE,KAAAgS,oCAAA,iBA+2By8I,CAAC,KAAAC,oCAAA,iBAAsK,CAAC;UA/2BnnJjS,0DAAE,CA+2BgxJ,CAAC;UA/2BnxJA,wDAAE,KAAAkS,oCAAA,iBA+2B4yJ,CAAC;UA/2B/yJlS,0DAAE,CA+2Bi2J,CAAC;UA/2Bp2JA,4DAAE,cA+2BmhK,CAAC;UA/2BthKA,wDAAE,KAAAmS,6BAAA,MA+2BmlK,CAAC,KAAAO,6BAAA,MAAuO,CAAC;UA/2B9zK1S,0DAAE,CA+2B6vL,CAAC;QAAA;QAAA,IAAA0M,EAAA;UAAA,IAAAuU,QAAA;UA/2BhwLjhB,uDAAE,EA+2BgmG,CAAC;UA/2BnmGA,yDAAE,4BAAA2M,GAAA,CAAA8D,WAAA,EA+2BgmG,CAAC,6BAAA9D,GAAA,CAAA8D,WAAA,EAAwD,CAAC,8BAAA9D,GAAA,CAAAyE,iBAAA,EAA+D,CAAC,6BAAAzE,GAAA,CAAAiE,QAAA,CAAA0P,QAA4D,CAAC,4BAAA3T,GAAA,CAAAiE,QAAA,CAAAqN,UAA6D,CAAC;UA/2Bv1Gje,uDAAE,EA+2B0/G,CAAC;UA/2B7/GA,2DAAE,KAAA2M,GAAA,CAAA8D,WAAA,OAAA9D,GAAA,CAAAiE,QAAA,CAAA0P,QAAA,SA+2B0/G,CAAC;UA/2B7/GtgB,uDAAE,EA+2ByzH,CAAC;UA/2B5zHA,2DAAE,IAAA2M,GAAA,CAAA8D,WAAA,WA+2ByzH,CAAC;UA/2B5zHzQ,uDAAE,CA+2BkgI,CAAC;UA/2BrgIA,2DAAE,IAAA2M,GAAA,CAAAqO,cAAA,SA+2BkgI,CAAC;UA/2BrgIhb,uDAAE,CA+2B8rI,CAAC;UA/2BjsIA,2DAAE,IAAA2M,GAAA,CAAAsO,cAAA,SA+2B8rI,CAAC;UA/2BjsIjb,uDAAE,EA+2B43I,CAAC;UA/2B/3IA,2DAAE,MAAA2M,GAAA,CAAA8D,WAAA,MAAA9D,GAAA,CAAAgF,uBAAA,YA+2B43I,CAAC;UA/2B/3I3R,uDAAE,EA+2BklJ,CAAC;UA/2BrlJA,2DAAE,KAAA2M,GAAA,CAAAwO,cAAA,UA+2BklJ,CAAC;UA/2BrlJnb,uDAAE,CA+2BswJ,CAAC;UA/2BzwJA,2DAAE,KAAA2M,GAAA,CAAAuO,cAAA,UA+2BswJ,CAAC;UA/2BzwJlb,uDAAE,CA+2By1J,CAAC;UA/2B51JA,2DAAE,MAAA2M,GAAA,CAAA8D,WAAA,YA+2By1J,CAAC;UA/2B51JzQ,uDAAE,CA+2BkhK,CAAC;UA/2BrhKA,yDAAE,8CAAA2M,GAAA,CAAA2N,eAAA,cA+2BkhK,CAAC;UA/2BrhKta,uDAAE,CA+2BqvL,CAAC;UA/2BxvLA,2DAAE,MAAAihB,QAAA,GAAAtU,GAAA,CAAAqR,qBAAA,QA+2B0hK,OAAO,QAAAiD,QAAA,KAAP,MAAM,UAAqtB,CAAC;QAAA;MAAA;MAAAC,YAAA,GAAq8sDxM,yBAAyB,EAAoH8B,0BAA0B,EAAwHtH,8DAAgB,EAAoJ8G,sBAAsB,EAAwEpC,OAAO;MAAAuN,MAAA;MAAA5I,aAAA;MAAA6I,IAAA;QAAAC,SAAA,EAAgE,CAAC3I,sBAAsB,CAACC,kBAAkB;MAAC;MAAAH,eAAA;IAAA,EAAiG;EAAE;AACvh6D;AACA;EAAA,QAAAvV,SAAA,oBAAAA,SAAA,KAj3BoGjD,+DAAE,CAi3BX0Z,YAAY,EAAc,CAAC;IAC1GzV,IAAI,EAAEkK,oDAAS;IACfjK,IAAI,EAAE,CAAC;MAAE4D,QAAQ,EAAE,gBAAgB;MAAEoF,QAAQ,EAAE,cAAc;MAAEoU,UAAU,EAAE,CAAC5I,sBAAsB,CAACC,kBAAkB,CAAC;MAAEtL,IAAI,EAAE;QAClH,OAAO,EAAE,oBAAoB;QAC7B,+CAA+C,EAAE,sBAAsB;QACvE,4CAA4C,EAAE,gBAAgB;QAC9D,4CAA4C,EAAE,gBAAgB;QAC9D;QACA;QACA;QACA,gCAAgC,EAAE,qBAAqB;QACvD,iCAAiC,EAAE,mBAAmB;QACtD,mCAAmC,EAAE,qBAAqB;QAC1D,sCAAsC,EAAE,qCAAqC;QAC7E,wCAAwC,EAAE,sBAAsB;QAChE,2CAA2C,EAAE,yBAAyB;QACtE,yCAAyC,EAAE,6CAA6C;QACxF,qBAAqB,EAAE,kBAAkB;QACzC,qBAAqB,EAAE,wCAAwC;QAC/D,oBAAoB,EAAE,oBAAoB;QAC1C,kBAAkB,EAAE,kBAAkB;QACtC,sBAAsB,EAAE,6BAA6B;QACrD,oBAAoB,EAAE,2BAA2B;QACjD,qBAAqB,EAAE,4BAA4B;QACnD,kBAAkB,EAAE,yBAAyB;QAC7C,kBAAkB,EAAE,yBAAyB;QAC7C,oBAAoB,EAAE,2BAA2B;QACjD,oBAAoB,EAAE;MAC1B,CAAC;MAAEkL,aAAa,EAAElK,4DAAiB,CAACrB,IAAI;MAAEwL,eAAe,EAAEpK,kEAAuB,CAACqK,MAAM;MAAE/E,SAAS,EAAE,CAClG;QAAEF,OAAO,EAAE2F,cAAc;QAAE1F,WAAW,EAAEiG;MAAa,CAAC,EACtD;QAAElG,OAAO,EAAEiB,qBAAqB;QAAEhB,WAAW,EAAEiG;MAAa,CAAC,CAChE;MAAE7R,UAAU,EAAE,IAAI;MAAEkG,OAAO,EAAE,CAC1B2G,yBAAyB,EACzB8B,0BAA0B,EAC1BtH,8DAAgB,EAChB8G,sBAAsB,EACtBpC,OAAO,CACV;MAAEwE,QAAQ,EAAE,yjJAAyjJ;MAAE+I,MAAM,EAAE,CAAC,k4sDAAk4sD;IAAE,CAAC;EACl+1D,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEld,IAAI,EAAEjE,qDAAawH;EAAC,CAAC,EAAE;IAAEvD,IAAI,EAAEjE,4DAAoB8f;EAAC,CAAC,EAAE;IAAE7b,IAAI,EAAEjE,iDAASE;EAAC,CAAC,EAAE;IAAE+D,IAAI,EAAEE,6DAAiB4b;EAAC,CAAC,EAAE;IAAE9b,IAAI,EAAEyK,2DAAW3H;EAAC,CAAC,EAAE;IAAE9C,IAAI,EAAE6E,SAAS;IAAEwE,UAAU,EAAE,CAAC;MACjLrJ,IAAI,EAAEQ,mDAAQA;IAClB,CAAC,EAAE;MACCR,IAAI,EAAES,iDAAM;MACZR,IAAI,EAAE,CAACkV,8BAA8B;IACzC,CAAC;EAAE,CAAC,EAAE;IAAEnV,IAAI,EAAE6E,SAAS;IAAEwE,UAAU,EAAE,CAAC;MAClCrJ,IAAI,EAAEQ,mDAAQA;IAClB,CAAC,EAAE;MACCR,IAAI,EAAES,iDAAM;MACZR,IAAI,EAAE,CAACqK,gEAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAEtK,IAAI,EAAE6E,SAAS;IAAEwE,UAAU,EAAE,CAAC;MAClCrJ,IAAI,EAAES,iDAAM;MACZR,IAAI,EAAE,CAACgB,sDAAQ;IACnB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE+W,UAAU,EAAE,CAAC;MACtChY,IAAI,EAAEqK,oDAAS;MACfpK,IAAI,EAAE,CAAC,WAAW;IACtB,CAAC,CAAC;IAAE6a,oBAAoB,EAAE,CAAC;MACvB9a,IAAI,EAAEqK,oDAAS;MACfpK,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAE8a,oBAAoB,EAAE,CAAC;MACvB/a,IAAI,EAAEqK,oDAAS;MACfpK,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEia,cAAc,EAAE,CAAC;MACjBla,IAAI,EAAEqK,oDAAS;MACfpK,IAAI,EAAE,CAACwQ,yBAAyB;IACpC,CAAC,CAAC;IAAE0J,eAAe,EAAE,CAAC;MAClBna,IAAI,EAAEqK,oDAAS;MACfpK,IAAI,EAAE,CAACsS,0BAA0B;IACrC,CAAC,CAAC;IAAE4G,WAAW,EAAE,CAAC;MACdnZ,IAAI,EAAEqK,oDAAS;MACfpK,IAAI,EAAE,CAAC8R,sBAAsB;IACjC,CAAC,CAAC;IAAE2H,oBAAoB,EAAE,CAAC;MACvB1Z,IAAI,EAAEuK,uDAAY;MAClBtK,IAAI,EAAE,CAACyO,QAAQ;IACnB,CAAC,CAAC;IAAEiL,iBAAiB,EAAE,CAAC;MACpB3Z,IAAI,EAAEuK,uDAAY;MAClBtK,IAAI,EAAE,CAACyO,QAAQ,EAAE;QAAE4O,MAAM,EAAE;MAAK,CAAC;IACrC,CAAC,CAAC;IAAE5G,iBAAiB,EAAE,CAAC;MACpB1W,IAAI,EAAEuK,uDAAY;MAClBtK,IAAI,EAAE,CAAC2U,mBAAmB;IAC9B,CAAC,CAAC;IAAE6D,eAAe,EAAE,CAAC;MAClBzY,IAAI,EAAEwK,0DAAe;MACrBvK,IAAI,EAAE,CAAC+P,UAAU,EAAE;QAAEuN,WAAW,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAE3E,eAAe,EAAE,CAAC;MAClB5Y,IAAI,EAAEwK,0DAAe;MACrBvK,IAAI,EAAE,CAACoQ,UAAU,EAAE;QAAEkN,WAAW,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAEvE,cAAc,EAAE,CAAC;MACjBhZ,IAAI,EAAEwK,0DAAe;MACrBvK,IAAI,EAAE,CAAC4O,SAAS,EAAE;QAAE0O,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAExE,aAAa,EAAE,CAAC;MAChB/Y,IAAI,EAAEwK,0DAAe;MACrBvK,IAAI,EAAE,CAAC0P,OAAO,EAAE;QAAE4N,WAAW,EAAE;MAAK,CAAC;IACzC,CAAC,CAAC;IAAEvQ,kBAAkB,EAAE,CAAC;MACrBhN,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEoW,KAAK,EAAE,CAAC;MACR9W,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEiV,UAAU,EAAE,CAAC;MACb3V,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEsV,UAAU,EAAE,CAAC;MACbhW,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAE2V,eAAe,EAAE,CAAC;MAClBrW,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAE8N,SAAS,EAAE,CAAC;MACZxO,IAAI,EAAEU,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8c,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACje,IAAI,YAAAke,2BAAAhe,CAAA;MAAA,YAAAA,CAAA,IAAwF+d,kBAAkB;IAAA,CAAkD;EAAE;EAChL;IAAS,IAAI,CAAC9T,IAAI,kBA39B8E3N,8DAAE;MAAAiE,IAAA,EA29BSwd;IAAkB,EAQX;EAAE;EACpH;IAAS,IAAI,CAAC5T,IAAI,kBAp+B8E7N,8DAAE;MAAA+N,OAAA,GAo+BuCsB,oEAAe,EAChJF,0DAAY,EACZC,oEAAe,EAAEC,oEAAe;IAAA,EAAI;EAAE;AAClD;AACA;EAAA,QAAApM,SAAA,oBAAAA,SAAA,KAx+BoGjD,+DAAE,CAw+BXyhB,kBAAkB,EAAc,CAAC;IAChHxd,IAAI,EAAEW,mDAAQ;IACdV,IAAI,EAAE,CAAC;MACC6J,OAAO,EAAE,CACLsB,oEAAe,EACfF,0DAAY,EACZC,oEAAe,EACfsK,YAAY,EACZ/G,QAAQ,EACRI,QAAQ,EACRa,OAAO,EACPM,SAAS,EACTK,SAAS,CACZ;MACDvG,OAAO,EAAE,CAAC0L,YAAY,EAAE/G,QAAQ,EAAEiB,OAAO,EAAEb,QAAQ,EAAEmB,SAAS,EAAEK,SAAS,EAAElF,oEAAe;IAC9F,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3gC8D;AAClB;AACmB;AACjB;AACY;AACtB;AAC+D;AAC9D;AACO;AACC;AACgC;AAC1B;AACoD;AACQ;AAChF;;AAE/B;AACA,SAAS6S,+BAA+BA,CAACje,IAAI,EAAE;EAC3C,OAAO+U,KAAK,CAAC,eAAe/U,IAAI,gCAAgC,CAAC;AACrE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMke,wBAAwB,GAAG,IAAIlU,yDAAc,CAAC,0BAA0B,CAAC;;AAE/E;AACA,MAAMmU,uBAAuB,GAAG,CAC5B,QAAQ,EACR,UAAU,EACV,MAAM,EACN,QAAQ,EACR,OAAO,EACP,OAAO,EACP,OAAO,EACP,OAAO,EACP,QAAQ,CACX;AACD,IAAI/I,YAAY,GAAG,CAAC;AACpB,MAAMgJ,QAAQ,CAAC;EACX;AACJ;AACA;AACA;EACI,IAAI/B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACgC,SAAS;EACzB;EACA,IAAIhC,QAAQA,CAACpY,KAAK,EAAE;IAChB,IAAI,CAACoa,SAAS,GAAGzT,4EAAqB,CAAC3G,KAAK,CAAC;IAC7C;IACA;IACA,IAAI,IAAI,CAACiV,OAAO,EAAE;MACd,IAAI,CAACA,OAAO,GAAG,KAAK;MACpB,IAAI,CAACd,YAAY,CAAC5a,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIqP,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACyR,GAAG;EACnB;EACA,IAAIzR,EAAEA,CAAC5I,KAAK,EAAE;IACV,IAAI,CAACqa,GAAG,GAAGra,KAAK,IAAI,IAAI,CAACsa,IAAI;EACjC;EACA;AACJ;AACA;AACA;EACI,IAAItR,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACuR,SAAS,IAAI,IAAI,CAAClG,SAAS,EAAEJ,OAAO,EAAEuG,YAAY,CAACZ,sDAAU,CAAC5Q,QAAQ,CAAC,IAAI,KAAK;EAChG;EACA,IAAIA,QAAQA,CAAChJ,KAAK,EAAE;IAChB,IAAI,CAACua,SAAS,GAAG5T,4EAAqB,CAAC3G,KAAK,CAAC;EACjD;EACA;EACA,IAAIjE,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAAC0e,KAAK;EACrB;EACA,IAAI1e,IAAIA,CAACiE,KAAK,EAAE;IACZ,IAAI,CAACya,KAAK,GAAGza,KAAK,IAAI,MAAM;IAC5B,IAAI,CAAC0a,aAAa,CAAC,CAAC;IACpB;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAACC,WAAW,IAAIlB,6EAAsB,CAAC,CAAC,CAAC/f,GAAG,CAAC,IAAI,CAAC+gB,KAAK,CAAC,EAAE;MAC/D,IAAI,CAAC1b,WAAW,CAACuC,aAAa,CAACvF,IAAI,GAAG,IAAI,CAAC0e,KAAK;IACpD;EACJ;EACA;EACA,IAAIG,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB,CAACC,OAAO;EAC1C;EACA,IAAIF,iBAAiBA,CAAC5a,KAAK,EAAE;IACzB,IAAI,CAAC6a,kBAAkB,CAACC,OAAO,GAAG9a,KAAK;EAC3C;EACA;AACJ;AACA;AACA;EACI,IAAIA,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC+a,mBAAmB,CAAC/a,KAAK;EACzC;EACA,IAAIA,KAAKA,CAACA,KAAK,EAAE;IACb,IAAIA,KAAK,KAAK,IAAI,CAACA,KAAK,EAAE;MACtB,IAAI,CAAC+a,mBAAmB,CAAC/a,KAAK,GAAGA,KAAK;MACtC,IAAI,CAACmU,YAAY,CAAC5a,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;EACA,IAAIyhB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAAChb,KAAK,EAAE;IAChB,IAAI,CAACib,SAAS,GAAGtU,4EAAqB,CAAC3G,KAAK,CAAC;EACjD;EACA;EACA,IAAI+V,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC8E,kBAAkB,CAAC9E,UAAU;EAC7C;EACA,IAAIA,UAAUA,CAAC/V,KAAK,EAAE;IAClB,IAAI,CAAC6a,kBAAkB,CAAC9E,UAAU,GAAG/V,KAAK;EAC9C;EACAlH,WAAWA,CAACiG,WAAW,EAAE3B,SAAS,EAAEiX,SAAS,EAAE6G,UAAU,EAAEC,eAAe,EAAEC,wBAAwB,EAAEC,kBAAkB,EAAErc,gBAAgB,EAAE+O,MAAM;EAClJ;EACA;EACAuN,UAAU,EAAE;IACR,IAAI,CAACvc,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC3B,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACiX,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACrV,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAACsc,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAAChB,IAAI,GAAG,aAAanJ,YAAY,EAAE,EAAE;IACzC;AACR;AACA;AACA;IACQ,IAAI,CAAC8D,OAAO,GAAG,KAAK;IACpB;AACR;AACA;AACA;IACQ,IAAI,CAACd,YAAY,GAAG,IAAIjc,yCAAO,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACgc,WAAW,GAAG,WAAW;IAC9B;AACR;AACA;AACA;IACQ,IAAI,CAACmE,UAAU,GAAG,KAAK;IACvB,IAAI,CAAC+B,SAAS,GAAG,KAAK;IACtB,IAAI,CAACK,KAAK,GAAG,MAAM;IACnB,IAAI,CAACQ,SAAS,GAAG,KAAK;IACtB,IAAI,CAACM,qBAAqB,GAAG,CACzB,MAAM,EACN,UAAU,EACV,gBAAgB,EAChB,OAAO,EACP,MAAM,EACN,MAAM,CACT,CAACnjB,MAAM,CAACoD,CAAC,IAAIie,6EAAsB,CAAC,CAAC,CAAC/f,GAAG,CAAC8B,CAAC,CAAC,CAAC;IAC9C,IAAI,CAACggB,iBAAiB,GAAIzd,KAAK,IAAK;MAChC,MAAM0d,EAAE,GAAG1d,KAAK,CAACtE,MAAM;MACvB;MACA;MACA;MACA;MACA;MACA;MACA,IAAI,CAACgiB,EAAE,CAACzb,KAAK,IAAIyb,EAAE,CAACzX,cAAc,KAAK,CAAC,IAAIyX,EAAE,CAACxX,YAAY,KAAK,CAAC,EAAE;QAC/D;QACA;QACA;QACA;QACAwX,EAAE,CAACtX,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;QAC1BsX,EAAE,CAACtX,iBAAiB,CAAC,CAAC,EAAE,CAAC,CAAC;MAC9B;IACJ,CAAC;IACD,MAAM1G,OAAO,GAAG,IAAI,CAACsB,WAAW,CAACuC,aAAa;IAC9C,MAAMoa,QAAQ,GAAGje,OAAO,CAACie,QAAQ,CAACC,WAAW,CAAC,CAAC;IAC/C;IACA;IACA,IAAI,CAACZ,mBAAmB,GAAGM,kBAAkB,IAAI5d,OAAO;IACxD,IAAI,CAACme,oBAAoB,GAAG,IAAI,CAAC5b,KAAK;IACtC;IACA,IAAI,CAAC4I,EAAE,GAAG,IAAI,CAACA,EAAE;IACjB;IACA;IACA;IACA,IAAIxL,SAAS,CAACye,GAAG,EAAE;MACf9N,MAAM,CAAC/S,iBAAiB,CAAC,MAAM;QAC3B+D,WAAW,CAACuC,aAAa,CAACpG,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACsgB,iBAAiB,CAAC;MAC/E,CAAC,CAAC;IACN;IACA,IAAI,CAACX,kBAAkB,GAAG,IAAIf,sEAAkB,CAACsB,wBAAwB,EAAE/G,SAAS,EAAE8G,eAAe,EAAED,UAAU,EAAE,IAAI,CAAC/G,YAAY,CAAC;IACrI,IAAI,CAAC2H,SAAS,GAAG,CAAC,IAAI,CAAC1e,SAAS,CAACI,SAAS;IAC1C,IAAI,CAACue,eAAe,GAAGL,QAAQ,KAAK,QAAQ;IAC5C,IAAI,CAACf,WAAW,GAAGe,QAAQ,KAAK,UAAU;IAC1C,IAAI,CAACM,cAAc,GAAG,CAAC,CAACV,UAAU;IAClC,IAAI,IAAI,CAACS,eAAe,EAAE;MACtB,IAAI,CAAC7H,WAAW,GAAGzW,OAAO,CAACwe,QAAQ,GAC7B,4BAA4B,GAC5B,mBAAmB;IAC7B;EACJ;EACAta,eAAeA,CAAA,EAAG;IACd,IAAI,IAAI,CAACvE,SAAS,CAACI,SAAS,EAAE;MAC1B,IAAI,CAACwB,gBAAgB,CAAC1B,OAAO,CAAC,IAAI,CAACyB,WAAW,CAACuC,aAAa,CAAC,CAACxH,SAAS,CAACiE,KAAK,IAAI;QAC7E,IAAI,CAACsa,UAAU,GAAGta,KAAK,CAACM,YAAY;QACpC,IAAI,CAAC8V,YAAY,CAAC5a,IAAI,CAAC,CAAC;MAC5B,CAAC,CAAC;IACN;EACJ;EACA2iB,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC/H,YAAY,CAAC5a,IAAI,CAAC,CAAC;EAC5B;EACA4B,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgZ,YAAY,CAACzZ,QAAQ,CAAC,CAAC;IAC5B,IAAI,IAAI,CAAC0C,SAAS,CAACI,SAAS,EAAE;MAC1B,IAAI,CAACwB,gBAAgB,CAACR,cAAc,CAAC,IAAI,CAACO,WAAW,CAACuC,aAAa,CAAC;IACxE;IACA,IAAI,IAAI,CAAClE,SAAS,CAACye,GAAG,EAAE;MACpB,IAAI,CAAC9c,WAAW,CAACuC,aAAa,CAAClG,mBAAmB,CAAC,OAAO,EAAE,IAAI,CAACogB,iBAAiB,CAAC;IACvF;EACJ;EACArY,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACkR,SAAS,EAAE;MAChB;MACA;MACA;MACA,IAAI,CAAC8H,gBAAgB,CAAC,CAAC;MACvB;MACA;MACA;MACA;MACA,IAAI,IAAI,CAAC9H,SAAS,CAAC+D,QAAQ,KAAK,IAAI,IAAI,IAAI,CAAC/D,SAAS,CAAC+D,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;QAC/E,IAAI,CAACA,QAAQ,GAAG,IAAI,CAAC/D,SAAS,CAAC+D,QAAQ;QACvC,IAAI,CAACjE,YAAY,CAAC5a,IAAI,CAAC,CAAC;MAC5B;IACJ;IACA;IACA;IACA;IACA,IAAI,CAAC6iB,sBAAsB,CAAC,CAAC;IAC7B;IACA;IACA,IAAI,CAACC,sBAAsB,CAAC,CAAC;EACjC;EACA;EACAC,KAAKA,CAACjhB,OAAO,EAAE;IACX,IAAI,CAAC0D,WAAW,CAACuC,aAAa,CAACgb,KAAK,CAACjhB,OAAO,CAAC;EACjD;EACA;EACA8gB,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACtB,kBAAkB,CAACsB,gBAAgB,CAAC,CAAC;EAC9C;EACA;EACAI,aAAaA,CAACC,SAAS,EAAE;IACrB,IAAIA,SAAS,KAAK,IAAI,CAACvH,OAAO,EAAE;MAC5B,IAAI,CAACA,OAAO,GAAGuH,SAAS;MACxB,IAAI,CAACrI,YAAY,CAAC5a,IAAI,CAAC,CAAC;IAC5B;EACJ;EACAkjB,QAAQA,CAAA,EAAG;IACP;IACA;IACA;IACA;IACA;IACA;IACA;EAAA;EAEJ;EACAL,sBAAsBA,CAAA,EAAG;IACrB,MAAMM,QAAQ,GAAG,IAAI,CAAC3d,WAAW,CAACuC,aAAa,CAACtB,KAAK;IACrD,IAAI,IAAI,CAAC4b,oBAAoB,KAAKc,QAAQ,EAAE;MACxC,IAAI,CAACd,oBAAoB,GAAGc,QAAQ;MACpC,IAAI,CAACvI,YAAY,CAAC5a,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;EACA8iB,sBAAsBA,CAAA,EAAG;IACrB,MAAM5b,WAAW,GAAG,IAAI,CAACkc,eAAe,CAAC,CAAC;IAC1C,IAAIlc,WAAW,KAAK,IAAI,CAACmc,oBAAoB,EAAE;MAC3C,MAAMnf,OAAO,GAAG,IAAI,CAACsB,WAAW,CAACuC,aAAa;MAC9C,IAAI,CAACsb,oBAAoB,GAAGnc,WAAW;MACvCA,WAAW,GACLhD,OAAO,CAACoD,YAAY,CAAC,aAAa,EAAEJ,WAAW,CAAC,GAChDhD,OAAO,CAACqD,eAAe,CAAC,aAAa,CAAC;IAChD;EACJ;EACA;EACA6b,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAAClc,WAAW,IAAI,IAAI;EACnC;EACA;EACAia,aAAaA,CAAA,EAAG;IACZ,IAAIR,uBAAuB,CAAC2C,OAAO,CAAC,IAAI,CAACpC,KAAK,CAAC,GAAG,CAAC,CAAC,KAC/C,OAAO1f,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMif,+BAA+B,CAAC,IAAI,CAACS,KAAK,CAAC;IACrD;EACJ;EACA;EACAqC,aAAaA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACvB,qBAAqB,CAACsB,OAAO,CAAC,IAAI,CAACpC,KAAK,CAAC,GAAG,CAAC,CAAC;EAC9D;EACA;EACAsC,WAAWA,CAAA,EAAG;IACV;IACA,IAAIC,QAAQ,GAAG,IAAI,CAACje,WAAW,CAACuC,aAAa,CAAC0b,QAAQ;IACtD,OAAOA,QAAQ,IAAIA,QAAQ,CAACC,QAAQ;EACxC;EACA;AACJ;AACA;AACA;EACI,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAQ,CAAC,IAAI,CAACJ,aAAa,CAAC,CAAC,IACzB,CAAC,IAAI,CAAC/d,WAAW,CAACuC,aAAa,CAACtB,KAAK,IACrC,CAAC,IAAI,CAAC+c,WAAW,CAAC,CAAC,IACnB,CAAC,IAAI,CAAC1E,UAAU;EACxB;EACA;AACJ;AACA;AACA;EACI,IAAI1C,gBAAgBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACoG,eAAe,EAAE;MACtB;MACA;MACA;MACA,MAAMoB,aAAa,GAAG,IAAI,CAACpe,WAAW,CAACuC,aAAa;MACpD,MAAM8b,WAAW,GAAGD,aAAa,CAAC9hB,OAAO,CAAC,CAAC,CAAC;MAC5C;MACA;MACA,OAAQ,IAAI,CAAC4Z,OAAO,IAChBkI,aAAa,CAAClB,QAAQ,IACtB,CAAC,IAAI,CAACiB,KAAK,IACX,CAAC,EAAEC,aAAa,CAACE,aAAa,GAAG,CAAC,CAAC,IAAID,WAAW,IAAIA,WAAW,CAAC5O,KAAK,CAAC;IAChF,CAAC,MACI;MACD,OAAO,IAAI,CAACyG,OAAO,IAAI,CAAC,IAAI,CAACiI,KAAK;IACtC;EACJ;EACA;AACJ;AACA;AACA;EACIvG,iBAAiBA,CAACL,GAAG,EAAE;IACnB,IAAIA,GAAG,CAACd,MAAM,EAAE;MACZ,IAAI,CAACzW,WAAW,CAACuC,aAAa,CAACT,YAAY,CAAC,kBAAkB,EAAEyV,GAAG,CAACgH,IAAI,CAAC,GAAG,CAAC,CAAC;IAClF,CAAC,MACI;MACD,IAAI,CAACve,WAAW,CAACuC,aAAa,CAACR,eAAe,CAAC,kBAAkB,CAAC;IACtE;EACJ;EACA;AACJ;AACA;AACA;EACIgY,gBAAgBA,CAAA,EAAG;IACf;IACA;IACA;IACA,IAAI,CAAC,IAAI,CAAC7D,OAAO,EAAE;MACf,IAAI,CAACqH,KAAK,CAAC,CAAC;IAChB;EACJ;EACA;EACAiB,eAAeA,CAAA,EAAG;IACd,MAAM9f,OAAO,GAAG,IAAI,CAACsB,WAAW,CAACuC,aAAa;IAC9C,OAAO,IAAI,CAACya,eAAe,KAAKte,OAAO,CAACwe,QAAQ,IAAIxe,OAAO,CAAC+f,IAAI,GAAG,CAAC,CAAC;EACzE;EACA;IAAS,IAAI,CAACliB,IAAI,YAAAmiB,iBAAAjiB,CAAA;MAAA,YAAAA,CAAA,IAAwF2e,QAAQ,EAAlBriB,+DAAE,CAAkCA,qDAAa,GAAjDA,+DAAE,CAA4DmE,2DAAW,GAAzEnE,+DAAE,CAAoF0O,qDAAY,OAAlG1O,+DAAE,CAAyI0O,kDAAS,MAApJ1O,+DAAE,CAA+K0O,8DAAqB,MAAtM1O,+DAAE,CAAiO+hB,qEAAoB,GAAvP/hB,+DAAE,CAAkQmiB,wBAAwB,OAA5RniB,+DAAE,CAAmU4hB,oEAAkB,GAAvV5hB,+DAAE,CAAkWA,iDAAS,GAA7WA,+DAAE,CAAwXmZ,wEAAc;IAAA,CAA4D;EAAE;EACtiB;IAAS,IAAI,CAAC1R,IAAI,kBAD8EzH,+DAAE;MAAAiE,IAAA,EACJoe,QAAQ;MAAA1a,SAAA;MAAA4E,SAAA;MAAA6G,QAAA;MAAA5G,YAAA,WAAAwZ,sBAAAtZ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADN1M,wDAAE,mBAAAimB,kCAAA;YAAA,OACJtZ,GAAA,CAAA8X,aAAA,CAAc,IAAI,CAAC;UAAA,CAAZ,CAAC,kBAAAyB,iCAAA;YAAA,OAARvZ,GAAA,CAAA8X,aAAA,CAAc,KAAK,CAAC;UAAA,CAAb,CAAC,mBAAA0B,kCAAA;YAAA,OAARxZ,GAAA,CAAAgY,QAAA,CAAS,CAAC;UAAA,CAAH,CAAC;QAAA;QAAA,IAAAjY,EAAA;UADN1M,4DAAE,OAAA2M,GAAA,CAAAmE,EACG,CAAC,aAAAnE,GAAA,CAAA2T,QAAD,CAAC,aAAA3T,GAAA,CAAAuE,QAAD,CAAC;UADNlR,yDAAE,SAAA2M,GAAA,CAAAyZ,IAAA,IACI,IAAI,cAAAzZ,GAAA,CAAAuW,QAAA,KAAAvW,GAAA,CAAAsX,eAAA,IAAoB,IAAI,kBAAAtX,GAAA,CAAAyY,KAAA,IAAAzY,GAAA,CAAAuE,QAAA,GAAd,IAAI,GAAAvE,GAAA,CAAAsR,UAAA,mBAAAtR,GAAA,CAAAuE,QAAA,QAAAvE,GAAA,CAAAmE,EAAA;UADxB9Q,yDAAE,qBAAA2M,GAAA,CAAAqX,SACG,CAAC,wCAAArX,GAAA,CAAAuX,cAAA,IAAAvX,GAAA,CAAAkW,WAAD,CAAC,qCAAAlW,GAAA,CAAAuX,cAAD,CAAC,0BAAAvX,GAAA,CAAAuX,cAAD,CAAC,iCAARvX,GAAA,CAAA8Y,eAAA,CAAgB,CAAT,CAAC;QAAA;MAAA;MAAA3Y,MAAA;QAAAwT,QAAA;QAAAxP,EAAA;QAAAnI,WAAA;QAAAyd,IAAA;QAAAlV,QAAA;QAAAjN,IAAA;QAAA6e,iBAAA;QAAArE,mBAAA,GADNze,0DAAE,CAAAgN,IAAA;QAAA9E,KAAA;QAAAgb,QAAA;MAAA;MAAAhW,QAAA;MAAArF,UAAA;MAAAsF,QAAA,GAAFnN,gEAAE,CACioC,CAAC;QAAEwT,OAAO,EAAEqF,6EAAmB;QAAEpF,WAAW,EAAE4O;MAAS,CAAC,CAAC,GAD5rCriB,kEAAE;IAAA,EACwvC;EAAE;AACh2C;AACA;EAAA,QAAAiD,SAAA,oBAAAA,SAAA,KAHoGjD,+DAAE,CAGXqiB,QAAQ,EAAc,CAAC;IACtGpe,IAAI,EAAEK,oDAAS;IACfJ,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE;AAC9B,0DAA0D;MACtCoF,QAAQ,EAAE,UAAU;MACpBG,IAAI,EAAE;QACF,OAAO,EAAE,uBAAuB;QAChC;QACA;QACA;QACA,0BAA0B,EAAE,WAAW;QACvC,6CAA6C,EAAE,+BAA+B;QAC9E,0CAA0C,EAAE,gBAAgB;QAC5D,+BAA+B,EAAE,gBAAgB;QACjD,sCAAsC,EAAE,mBAAmB;QAC3D;QACA;QACA,MAAM,EAAE,IAAI;QACZ,YAAY,EAAE,UAAU;QACxB,YAAY,EAAE,UAAU;QACxB,aAAa,EAAE,cAAc;QAC7B,iBAAiB,EAAE,sCAAsC;QACzD;QACA;QACA,qBAAqB,EAAE,yCAAyC;QAChE,sBAAsB,EAAE,UAAU;QAClC;QACA;QACA,WAAW,EAAE,IAAI;QACjB,SAAS,EAAE,qBAAqB;QAChC,QAAQ,EAAE,sBAAsB;QAChC,SAAS,EAAE;MACf,CAAC;MACDqG,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEqF,6EAAmB;QAAEpF,WAAW,EAAE4O;MAAS,CAAC,CAAC;MACpExa,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE5D,IAAI,EAAEjE,qDAAawH;EAAC,CAAC,EAAE;IAAEvD,IAAI,EAAEE,2DAAW4C;EAAC,CAAC,EAAE;IAAE9C,IAAI,EAAEyK,qDAAY;IAAEpB,UAAU,EAAE,CAAC;MAClGrJ,IAAI,EAAEQ,mDAAQA;IAClB,CAAC,EAAE;MACCR,IAAI,EAAE4d,+CAAIA;IACd,CAAC;EAAE,CAAC,EAAE;IAAE5d,IAAI,EAAEyK,kDAAS;IAAEpB,UAAU,EAAE,CAAC;MAClCrJ,IAAI,EAAEQ,mDAAQA;IAClB,CAAC;EAAE,CAAC,EAAE;IAAER,IAAI,EAAEyK,8DAAqB;IAAEpB,UAAU,EAAE,CAAC;MAC9CrJ,IAAI,EAAEQ,mDAAQA;IAClB,CAAC;EAAE,CAAC,EAAE;IAAER,IAAI,EAAE8d,qEAAoBgE;EAAC,CAAC,EAAE;IAAE9hB,IAAI,EAAE6E,SAAS;IAAEwE,UAAU,EAAE,CAAC;MAClErJ,IAAI,EAAEQ,mDAAQA;IAClB,CAAC,EAAE;MACCR,IAAI,EAAE4d,+CAAIA;IACd,CAAC,EAAE;MACC5d,IAAI,EAAES,iDAAM;MACZR,IAAI,EAAE,CAACie,wBAAwB;IACnC,CAAC;EAAE,CAAC,EAAE;IAAEle,IAAI,EAAE2d,oEAAkBvc;EAAC,CAAC,EAAE;IAAEpB,IAAI,EAAEjE,iDAASE;EAAC,CAAC,EAAE;IAAE+D,IAAI,EAAEge,sEAAe;IAAE3U,UAAU,EAAE,CAAC;MAC3FrJ,IAAI,EAAEQ,mDAAQA;IAClB,CAAC,EAAE;MACCR,IAAI,EAAES,iDAAM;MACZR,IAAI,EAAE,CAACiV,wEAAc;IACzB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEmH,QAAQ,EAAE,CAAC;MACpCrc,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEmM,EAAE,EAAE,CAAC;MACL7M,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEgE,WAAW,EAAE,CAAC;MACd1E,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEyhB,IAAI,EAAE,CAAC;MACPniB,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEuM,QAAQ,EAAE,CAAC;MACXjN,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEV,IAAI,EAAE,CAAC;MACPA,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEme,iBAAiB,EAAE,CAAC;MACpB7e,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAE8Z,mBAAmB,EAAE,CAAC;MACtBxa,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEgE,KAAK,EAAE,CAAC;MACRjE,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEue,QAAQ,EAAE,CAAC;MACXjf,IAAI,EAAEU,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM2hB,cAAc,CAAC;EACjB;IAAS,IAAI,CAAC9iB,IAAI,YAAA+iB,uBAAA7iB,CAAA;MAAA,YAAAA,CAAA,IAAwF4iB,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAAC3Y,IAAI,kBArF8E3N,8DAAE;MAAAiE,IAAA,EAqFSqiB;IAAc,EAAwI;EAAE;EACnQ;IAAS,IAAI,CAACzY,IAAI,kBAtF8E7N,8DAAE;MAAA+N,OAAA,GAsFmCsB,mEAAe,EAAEoS,4EAAkB,EAAEA,4EAAkB,EAAEhU,oEAAe,EAAE4B,mEAAe;IAAA,EAAI;EAAE;AACxO;AACA;EAAA,QAAApM,SAAA,oBAAAA,SAAA,KAxFoGjD,+DAAE,CAwFXsmB,cAAc,EAAc,CAAC;IAC5GriB,IAAI,EAAEW,mDAAQ;IACdV,IAAI,EAAE,CAAC;MACC6J,OAAO,EAAE,CAACsB,mEAAe,EAAEoS,4EAAkB,EAAEY,QAAQ,CAAC;MACxDrU,OAAO,EAAE,CAACqU,QAAQ,EAAEZ,4EAAkB,EAAEhU,oEAAe,EAAE4B,mEAAe;IAC5E,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9dqG;AAC7C;AACpB;AAC4O;AACnO;AACgK;AAC7I;AACb;AACoD;AACQ;AAClE;AACgB;AACrB;AACoE;AACpE;AACkB;AAC6D;AAClF;AACO;AACC;AAC6D;AACJ;;AAEtG;AACA;AACA;AACA;AACA;AACA;AACA;AANA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,SAAAiZ,iCAAAhc,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAugCoG1M,4DAAE,aAI0zB,CAAC;IAJ7zBA,oDAAE,EAIy0B,CAAC;IAJ50BA,0DAAE,CAIg1B,CAAC;EAAA;EAAA,IAAA0M,EAAA;IAAA,MAAA2D,MAAA,GAJn1BrQ,2DAAE;IAAFA,uDAAE,CAIy0B,CAAC;IAJ50BA,+DAAE,CAAAqQ,MAAA,CAAA1H,WAIy0B,CAAC;EAAA;AAAA;AAAA,SAAAggB,+CAAAjc,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJ50B1M,0DAAE,EAIm/B,CAAC;EAAA;AAAA;AAAA,SAAA4oB,+CAAAlc,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJt/B1M,4DAAE,cAI0jC,CAAC;IAJ7jCA,oDAAE,EAI0kC,CAAC;IAJ7kCA,0DAAE,CAIilC,CAAC;EAAA;EAAA,IAAA0M,EAAA;IAAA,MAAA2D,MAAA,GAJplCrQ,2DAAE;IAAFA,uDAAE,CAI0kC,CAAC;IAJ7kCA,+DAAE,CAAAqQ,MAAA,CAAAwY,YAI0kC,CAAC;EAAA;AAAA;AAAA,SAAAC,iCAAApc,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAJ7kC1M,4DAAE,cAIi5B,CAAC;IAJp5BA,wDAAE,IAAA2oB,8CAAA,MAIg7B,CAAC,IAAAC,8CAAA,MAAqF,CAAC;IAJzgC5oB,0DAAE,CAI2mC,CAAC;EAAA;EAAA,IAAA0M,EAAA;IAAA,MAAA2D,MAAA,GAJ9mCrQ,2DAAE;IAAFA,uDAAE,CAI4lC,CAAC;IAJ/lCA,2DAAE,IAAAqQ,MAAA,CAAA0Y,aAAA,QAI4lC,CAAC;EAAA;AAAA;AAAA,SAAAC,kCAAAtc,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAuc,GAAA,GAJ/lCjpB,8DAAE;IAAFA,4DAAE,gBAIknF,CAAC;IAJrnFA,wDAAE,kCAAAkpB,+EAAArI,MAAA;MAAF7gB,2DAAE,CAAAipB,GAAA;MAAA,MAAA5Y,MAAA,GAAFrQ,2DAAE;MAAA,OAAFA,yDAAE,CAIwhFqQ,MAAA,CAAA8Y,yBAAA,CAAA1nB,IAAA,CAAAof,MAAA,CAAAuI,OAA6C,CAAC;IAAA,CAAC,CAAC,qBAAAC,yDAAAxI,MAAA;MAJ1kF7gB,2DAAE,CAAAipB,GAAA;MAAA,MAAA5Y,MAAA,GAAFrQ,2DAAE;MAAA,OAAFA,yDAAE,CAI0lFqQ,MAAA,CAAAiZ,cAAA,CAAAzI,MAAqB,CAAC;IAAA,CAAC,CAAC;IAJpnF7gB,0DAAE,KAIipF,CAAC;IAJppFA,0DAAE,CAI2pF,CAAC;EAAA;EAAA,IAAA0M,EAAA;IAAA,MAAA2D,MAAA,GAJ9pFrQ,2DAAE;IAAFA,oEAAE,kEAAAqQ,MAAA,CAAAmZ,cAAA,MAI+vE,CAAC;IAJlwExpB,wDAAE,YAAAqQ,MAAA,CAAAoZ,UAIm9E,CAAC,6BAAoC,CAAC;IAJ3/EzpB,yDAAE,OAAAqQ,MAAA,CAAAS,EAAA,qCAAAT,MAAA,CAAA8T,QAAA,gBAAA9T,MAAA,CAAAqZ,SAAA,6BAAArZ,MAAA,CAAAsZ,uBAAA;EAAA;AAAA;AAhgCtG,MAAMC,mBAAmB,GAAG;EACxB;AACJ;AACA;AACA;AACA;EACIC,kBAAkB,EAAE/a,4DAAO,CAAC,oBAAoB,EAAE,CAC9CE,+DAAU,CAAC,WAAW,EAAEwZ,0DAAK,CAAC,iBAAiB,EAAE,CAACC,iEAAY,CAAC,CAAC,CAAC,EAAE;IAAEqB,QAAQ,EAAE;EAAK,CAAC,CAAC,CAAC,CAC1F,CAAC;EACF;EACAC,cAAc,EAAEjb,4DAAO,CAAC,gBAAgB,EAAE,CACtCC,0DAAK,CAAC,MAAM,EAAEpF,0DAAK,CAAC;IAChBiP,OAAO,EAAE,CAAC;IACVpL,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,EACHwB,+DAAU,CAAC,iBAAiB,EAAEC,4DAAO,CAAC,kCAAkC,EAAEtF,0DAAK,CAAC;IAC5EiP,OAAO,EAAE,CAAC;IACVpL,SAAS,EAAE;EACf,CAAC,CAAC,CAAC,CAAC,EACJwB,+DAAU,CAAC,WAAW,EAAEC,4DAAO,CAAC,cAAc,EAAEtF,0DAAK,CAAC;IAAEiP,OAAO,EAAE;EAAE,CAAC,CAAC,CAAC,CAAC,CAC1E;AACL,CAAC;;AAED;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASoR,gCAAgCA,CAAA,EAAG;EACxC,OAAOhR,KAAK,CAAC,+DAA+D,CAAC;AACjF;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiR,8BAA8BA,CAAA,EAAG;EACtC,OAAOjR,KAAK,CAAC,oDAAoD,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA;AACA,SAASkR,iCAAiCA,CAAA,EAAG;EACzC,OAAOlR,KAAK,CAAC,mCAAmC,CAAC;AACrD;AAEA,IAAIK,YAAY,GAAG,CAAC;AACpB;AACA,MAAM8Q,0BAA0B,GAAG,IAAIlc,yDAAc,CAAC,4BAA4B,EAAE;EAChFlK,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMsmB,OAAO,GAAGnqB,qDAAM,CAACumB,yDAAO,CAAC;IAC/B,OAAO,MAAM4D,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;EACtD;AACJ,CAAC,CAAC;AACF;AACA,SAASC,2CAA2CA,CAACH,OAAO,EAAE;EAC1D,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC,CAAC;AACtD;AACA;AACA,MAAME,iBAAiB,GAAG,IAAIvc,yDAAc,CAAC,mBAAmB,CAAC;AACjE;AACA,MAAMwc,mCAAmC,GAAG;EACxCjX,OAAO,EAAE2W,0BAA0B;EACnCO,IAAI,EAAE,CAAClE,yDAAO,CAAC;EACfmE,UAAU,EAAEJ;AAChB,CAAC;AACD;AACA;AACA;AACA;AACA;AACA,MAAMK,kBAAkB,GAAG,IAAI3c,yDAAc,CAAC,kBAAkB,CAAC;AACjE;AACA,MAAM4c,eAAe,CAAC;EAClB7pB,WAAWA,CACX;EACA8pB,MAAM,EACN;EACA5iB,KAAK,EAAE;IACH,IAAI,CAAC4iB,MAAM,GAAGA,MAAM;IACpB,IAAI,CAAC5iB,KAAK,GAAGA,KAAK;EACtB;AACJ;AACA,MAAM6iB,SAAS,CAAC;EACZ;EACAC,qBAAqBA,CAACC,KAAK,EAAE;IACzB,MAAMC,MAAM,GAAG,IAAI,CAAC3nB,OAAO,CAAC4nB,OAAO,CAAC,CAAC,CAACF,KAAK,CAAC;IAC5C,IAAIC,MAAM,EAAE;MACR,MAAME,KAAK,GAAG,IAAI,CAACA,KAAK,CAAC5hB,aAAa;MACtC,MAAM6hB,UAAU,GAAGvE,qFAA6B,CAACmE,KAAK,EAAE,IAAI,CAAC1nB,OAAO,EAAE,IAAI,CAAC+nB,YAAY,CAAC;MACxF,MAAM3lB,OAAO,GAAGulB,MAAM,CAACK,eAAe,CAAC,CAAC;MACxC,IAAIN,KAAK,KAAK,CAAC,IAAII,UAAU,KAAK,CAAC,EAAE;QACjC;QACA;QACA;QACAD,KAAK,CAACI,SAAS,GAAG,CAAC;MACvB,CAAC,MACI;QACDJ,KAAK,CAACI,SAAS,GAAGzE,gFAAwB,CAACphB,OAAO,CAAC8lB,SAAS,EAAE9lB,OAAO,CAAC+lB,YAAY,EAAEN,KAAK,CAACI,SAAS,EAAEJ,KAAK,CAACM,YAAY,CAAC;MAC5H;IACJ;EACJ;EACA;EACAC,mBAAmBA,CAAA,EAAG;IAClB,IAAI,CAACX,qBAAqB,CAAC,IAAI,CAACY,WAAW,CAACC,eAAe,IAAI,CAAC,CAAC;EACrE;EACA;EACAC,eAAeA,CAAC5jB,KAAK,EAAE;IACnB,OAAO,IAAI2iB,eAAe,CAAC,IAAI,EAAE3iB,KAAK,CAAC;EAC3C;EACA;EACA,IAAIiV,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC4O,QAAQ,IAAI,IAAI,CAACC,UAAU;EAC3C;EACA;EACA,IAAIC,4BAA4BA,CAAA,EAAG;IAC/B,OAAO,IAAI,CAACC,6BAA6B;EAC7C;EACA,IAAID,4BAA4BA,CAAC/jB,KAAK,EAAE;IACpC,IAAI,CAACgkB,6BAA6B,GAAGhkB,KAAK;IAC1C,IAAI,CAACikB,qBAAqB,CAAC,CAAC;EAChC;EACA;EACA,IAAIxjB,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACyjB,YAAY;EAC5B;EACA,IAAIzjB,WAAWA,CAACT,KAAK,EAAE;IACnB,IAAI,CAACkkB,YAAY,GAAGlkB,KAAK;IACzB,IAAI,CAACmU,YAAY,CAAC5a,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAIyP,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACuR,SAAS,IAAI,IAAI,CAAClG,SAAS,EAAEJ,OAAO,EAAEuG,YAAY,CAACZ,sDAAU,CAAC5Q,QAAQ,CAAC,IAAI,KAAK;EAChG;EACA,IAAIA,QAAQA,CAAChJ,KAAK,EAAE;IAChB,IAAI,CAACua,SAAS,GAAGva,KAAK;IACtB,IAAI,CAACmU,YAAY,CAAC5a,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAI0iB,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACkI,SAAS;EACzB;EACA,IAAIlI,QAAQA,CAACjc,KAAK,EAAE;IAChB,IAAI,IAAI,CAACokB,eAAe,KAAK,OAAOrpB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACzE,MAAM+mB,gCAAgC,CAAC,CAAC;IAC5C;IACA,IAAI,CAACqC,SAAS,GAAGnkB,KAAK;EAC1B;EACA;AACJ;AACA;AACA;AACA;EACI,IAAIqkB,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,YAAY;EAC5B;EACA,IAAID,WAAWA,CAACE,EAAE,EAAE;IAChB,IAAI,OAAOA,EAAE,KAAK,UAAU,KAAK,OAAOxpB,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC7E,MAAMinB,iCAAiC,CAAC,CAAC;IAC7C;IACA,IAAI,CAACsC,YAAY,GAAGC,EAAE;IACtB,IAAI,IAAI,CAACH,eAAe,EAAE;MACtB;MACA,IAAI,CAACI,oBAAoB,CAAC,CAAC;IAC/B;EACJ;EACA;EACA,IAAIxkB,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACykB,MAAM;EACtB;EACA,IAAIzkB,KAAKA,CAAC0c,QAAQ,EAAE;IAChB,MAAMgI,WAAW,GAAG,IAAI,CAACC,YAAY,CAACjI,QAAQ,CAAC;IAC/C,IAAIgI,WAAW,EAAE;MACb,IAAI,CAACE,SAAS,CAAClI,QAAQ,CAAC;IAC5B;EACJ;EACA;EACA,IAAI9B,iBAAiBA,CAAA,EAAG;IACpB,OAAO,IAAI,CAACC,kBAAkB,CAACC,OAAO;EAC1C;EACA,IAAIF,iBAAiBA,CAAC5a,KAAK,EAAE;IACzB,IAAI,CAAC6a,kBAAkB,CAACC,OAAO,GAAG9a,KAAK;EAC3C;EACA;EACA,IAAI4I,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACyR,GAAG;EACnB;EACA,IAAIzR,EAAEA,CAAC5I,KAAK,EAAE;IACV,IAAI,CAACqa,GAAG,GAAGra,KAAK,IAAI,IAAI,CAACsa,IAAI;IAC7B,IAAI,CAACnG,YAAY,CAAC5a,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAIwc,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC8E,kBAAkB,CAAC9E,UAAU;EAC7C;EACA,IAAIA,UAAUA,CAAC/V,KAAK,EAAE;IAClB,IAAI,CAAC6a,kBAAkB,CAAC9E,UAAU,GAAG/V,KAAK;EAC9C;EACAlH,WAAWA,CAAC+rB,cAAc,EAAEhT,kBAAkB;EAC9C;AACJ;AACA;AACA;EACIiT,aAAa,EAAE1J,wBAAwB,EAAErc,WAAW,EAAE2T,IAAI,EAAEwI,UAAU,EAAEC,eAAe,EAAE4J,gBAAgB,EAAE1Q,SAAS,EAAE2Q,QAAQ,EAAEC,qBAAqB,EAAEC,cAAc,EAAEC,eAAe,EAAE;IACpL,IAAI,CAACN,cAAc,GAAGA,cAAc;IACpC,IAAI,CAAChT,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC9S,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC2T,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACqS,gBAAgB,GAAGA,gBAAgB;IACxC,IAAI,CAAC1Q,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAC6Q,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,UAAU,GAAG,CACd;MACIC,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,QAAQ;MACjBC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE;IACd,CAAC,EACD;MACIH,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,QAAQ;MACjBC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE;IACd,CAAC,EACD;MACIH,OAAO,EAAE,OAAO;MAChBC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,OAAO;MACjBC,QAAQ,EAAE,QAAQ;MAClBjE,UAAU,EAAE;IAChB,CAAC,EACD;MACI8D,OAAO,EAAE,KAAK;MACdC,OAAO,EAAE,KAAK;MACdC,QAAQ,EAAE,KAAK;MACfC,QAAQ,EAAE,QAAQ;MAClBjE,UAAU,EAAE;IAChB,CAAC,CACJ;IACD;IACA,IAAI,CAACuC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACQ,YAAY,GAAG,CAACmB,EAAE,EAAEC,EAAE,KAAKD,EAAE,KAAKC,EAAE;IACzC;IACA,IAAI,CAACpL,IAAI,GAAG,cAAcnJ,YAAY,EAAE,EAAE;IAC1C;IACA,IAAI,CAACwU,sBAAsB,GAAG,IAAI;IAClC;IACA,IAAI,CAACC,QAAQ,GAAG,IAAI1tB,yCAAO,CAAC,CAAC;IAC7B;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACic,YAAY,GAAG,IAAIjc,yCAAO,CAAC,CAAC;IACjC;AACR;AACA;AACA;IACQ,IAAI,CAACyQ,wBAAwB,GAAG,IAAI;IACpC;IACA,IAAI,CAACic,SAAS,GAAG,MAAM,CAAE,CAAC;IAC1B;IACA,IAAI,CAACiB,UAAU,GAAG,MAAM,CAAE,CAAC;IAC3B;IACA,IAAI,CAACC,QAAQ,GAAG,oBAAoB3U,YAAY,EAAE,EAAE;IACpD;IACA,IAAI,CAAC8P,yBAAyB,GAAG,IAAI/oB,yCAAO,CAAC,CAAC;IAC9C,IAAI,CAAC6tB,kBAAkB,GAAG,IAAI,CAACZ,eAAe,EAAEa,iBAAiB,IAAI,EAAE;IACvE,IAAI,CAACnC,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAAC3P,WAAW,GAAG,YAAY;IAC/B;IACA,IAAI,CAACkE,QAAQ,GAAG,KAAK;IACrB;IACA,IAAI,CAAC6N,aAAa,GAAG,KAAK;IAC1B;IACA,IAAI,CAACjB,QAAQ,GAAG,CAAC;IACjB,IAAI,CAAChB,6BAA6B,GAAG,IAAI,CAACmB,eAAe,EAAEpB,4BAA4B,IAAI,KAAK;IAChG,IAAI,CAACI,SAAS,GAAG,KAAK;IACtB;IACA,IAAI,CAAC+B,sBAAsB,GAAG,IAAI,CAACf,eAAe,EAAEe,sBAAsB,IAAI,KAAK;IACnF;IACA,IAAI,CAAC1E,SAAS,GAAG,EAAE;IACnB;AACR;AACA;AACA;IACQ,IAAI,CAAC2E,UAAU,GAAG,IAAI,CAAChB,eAAe,IAAI,OAAO,IAAI,CAACA,eAAe,CAACgB,UAAU,KAAK,WAAW,GAC1F,IAAI,CAAChB,eAAe,CAACgB,UAAU,GAC/B,MAAM;IACZ,IAAI,CAACC,YAAY,GAAG,IAAIluB,yCAAO,CAAC,CAAC;IACjC;IACA,IAAI,CAACmuB,sBAAsB,GAAGpG,2CAAK,CAAC,MAAM;MACtC,MAAM5kB,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5B,IAAIA,OAAO,EAAE;QACT,OAAOA,OAAO,CAACwZ,OAAO,CAAC1a,IAAI,CAAC+lB,yDAAS,CAAC7kB,OAAO,CAAC,EAAE8kB,yDAAS,CAAC,MAAMzZ,4CAAK,CAAC,GAAGrL,OAAO,CAACqb,GAAG,CAACsM,MAAM,IAAIA,MAAM,CAACsD,iBAAiB,CAAC,CAAC,CAAC,CAAC;MAC/H;MACA,OAAO,IAAI,CAACF,YAAY,CAACjsB,IAAI,CAACgmB,yDAAS,CAAC,MAAM,IAAI,CAACkG,sBAAsB,CAAC,CAAC;IAC/E,CAAC,CAAC;IACF;IACA,IAAI,CAACE,YAAY,GAAG,IAAIpqB,uDAAY,CAAC,CAAC;IACtC;IACA,IAAI,CAACqqB,aAAa,GAAG,IAAI,CAACD,YAAY,CAACpsB,IAAI,CAAC/B,uDAAM,CAACquB,CAAC,IAAIA,CAAC,CAAC,EAAE/P,oDAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IAC3E;IACA,IAAI,CAACgQ,aAAa,GAAG,IAAI,CAACH,YAAY,CAACpsB,IAAI,CAAC/B,uDAAM,CAACquB,CAAC,IAAI,CAACA,CAAC,CAAC,EAAE/P,oDAAG,CAAC,MAAM,CAAE,CAAC,CAAC,CAAC;IAC5E;IACA,IAAI,CAACiQ,eAAe,GAAG,IAAIxqB,uDAAY,CAAC,CAAC;IACzC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACyqB,WAAW,GAAG,IAAIzqB,uDAAY,CAAC,CAAC;IACrC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAAC0qB,aAAa,GAAG,IAAI;IACzB;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACC,cAAc,GAAI9D,MAAM,IAAK;MAC9B,IAAI,IAAI,CAAC+D,SAAS,EAAE;QAChB;QACA,OAAO,KAAK;MAChB;MACA;MACA;MACA;MACA,OAAO/D,MAAM,CAAC5K,QAAQ;IAC1B,CAAC;IACD,IAAI,IAAI,CAAC/D,SAAS,EAAE;MAChB;MACA;MACA,IAAI,CAACA,SAAS,CAAC2S,aAAa,GAAG,IAAI;IACvC;IACA;IACA;IACA,IAAI7B,eAAe,EAAE8B,yBAAyB,IAAI,IAAI,EAAE;MACpD,IAAI,CAACA,yBAAyB,GAAG9B,eAAe,CAAC8B,yBAAyB;IAC9E;IACA,IAAI,CAACpM,kBAAkB,GAAG,IAAIf,sEAAkB,CAACsB,wBAAwB,EAAE/G,SAAS,EAAE8G,eAAe,EAAED,UAAU,EAAE,IAAI,CAAC/G,YAAY,CAAC;IACrI,IAAI,CAAC+S,sBAAsB,GAAGjC,qBAAqB;IACnD,IAAI,CAACkC,eAAe,GAAG,IAAI,CAACD,sBAAsB,CAAC,CAAC;IACpD,IAAI,CAAClC,QAAQ,GAAGoC,QAAQ,CAACpC,QAAQ,CAAC,IAAI,CAAC;IACvC;IACA,IAAI,CAACpc,EAAE,GAAG,IAAI,CAACA,EAAE;EACrB;EACA1J,QAAQA,CAAA,EAAG;IACP,IAAI,CAACklB,eAAe,GAAG,IAAI5E,qEAAc,CAAC,IAAI,CAACvD,QAAQ,CAAC;IACxD,IAAI,CAAC9H,YAAY,CAAC5a,IAAI,CAAC,CAAC;IACxB;IACA;IACA;IACA,IAAI,CAAC0nB,yBAAyB,CACzB9mB,IAAI,CAACimB,qEAAoB,CAAC,CAAC,EAAE9nB,0DAAS,CAAC,IAAI,CAACstB,QAAQ,CAAC,CAAC,CACtD9rB,SAAS,CAAC,MAAM,IAAI,CAACutB,mBAAmB,CAAC,IAAI,CAACN,SAAS,CAAC,CAAC;IAC9D,IAAI,CAAClC,cAAc,CACdvP,MAAM,CAAC,CAAC,CACRnb,IAAI,CAAC7B,0DAAS,CAAC,IAAI,CAACstB,QAAQ,CAAC,CAAC,CAC9B9rB,SAAS,CAAC,MAAM;MACjB,IAAI,IAAI,CAACitB,SAAS,EAAE;QAChB,IAAI,CAACO,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACC,uBAAuB,CAAC;QACxE,IAAI,CAAC3V,kBAAkB,CAACwB,aAAa,CAAC,CAAC;MAC3C;IACJ,CAAC,CAAC;EACN;EACAC,kBAAkBA,CAAA,EAAG;IACjB,IAAI,CAAC8S,YAAY,CAAC7sB,IAAI,CAAC,CAAC;IACxB,IAAI,CAAC6sB,YAAY,CAAC1rB,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAAC+sB,eAAe,CAAC,CAAC;IACtB,IAAI,CAACrD,eAAe,CAACsD,OAAO,CAACvtB,IAAI,CAAC7B,0DAAS,CAAC,IAAI,CAACstB,QAAQ,CAAC,CAAC,CAAC9rB,SAAS,CAACiE,KAAK,IAAI;MAC3EA,KAAK,CAAC4pB,KAAK,CAAClpB,OAAO,CAACukB,MAAM,IAAIA,MAAM,CAAC4E,MAAM,CAAC,CAAC,CAAC;MAC9C7pB,KAAK,CAAC8pB,OAAO,CAACppB,OAAO,CAACukB,MAAM,IAAIA,MAAM,CAAC8E,QAAQ,CAAC,CAAC,CAAC;IACtD,CAAC,CAAC;IACF,IAAI,CAACzsB,OAAO,CAACwZ,OAAO,CAAC1a,IAAI,CAAC+lB,yDAAS,CAAC,IAAI,CAAC,EAAE5nB,0DAAS,CAAC,IAAI,CAACstB,QAAQ,CAAC,CAAC,CAAC9rB,SAAS,CAAC,MAAM;MACjF,IAAI,CAACiuB,aAAa,CAAC,CAAC;MACpB,IAAI,CAACvD,oBAAoB,CAAC,CAAC;IAC/B,CAAC,CAAC;EACN;EACArhB,SAASA,CAAA,EAAG;IACR,MAAM6kB,iBAAiB,GAAG,IAAI,CAACC,yBAAyB,CAAC,CAAC;IAC1D,MAAM5T,SAAS,GAAG,IAAI,CAACA,SAAS;IAChC;IACA;IACA;IACA,IAAI2T,iBAAiB,KAAK,IAAI,CAACrC,sBAAsB,EAAE;MACnD,MAAMloB,OAAO,GAAG,IAAI,CAACsB,WAAW,CAACuC,aAAa;MAC9C,IAAI,CAACqkB,sBAAsB,GAAGqC,iBAAiB;MAC/C,IAAIA,iBAAiB,EAAE;QACnBvqB,OAAO,CAACoD,YAAY,CAAC,iBAAiB,EAAEmnB,iBAAiB,CAAC;MAC9D,CAAC,MACI;QACDvqB,OAAO,CAACqD,eAAe,CAAC,iBAAiB,CAAC;MAC9C;IACJ;IACA,IAAIuT,SAAS,EAAE;MACX;MACA,IAAI,IAAI,CAAC6T,gBAAgB,KAAK7T,SAAS,CAACJ,OAAO,EAAE;QAC7C,IAAI,IAAI,CAACiU,gBAAgB,KAAKtnB,SAAS,IACnCyT,SAAS,CAAC+D,QAAQ,KAAK,IAAI,IAC3B/D,SAAS,CAAC+D,QAAQ,KAAK,IAAI,CAACA,QAAQ,EAAE;UACtC,IAAI,CAACA,QAAQ,GAAG/D,SAAS,CAAC+D,QAAQ;QACtC;QACA,IAAI,CAAC8P,gBAAgB,GAAG7T,SAAS,CAACJ,OAAO;MAC7C;MACA,IAAI,CAACkI,gBAAgB,CAAC,CAAC;IAC3B;EACJ;EACAD,WAAWA,CAACrH,OAAO,EAAE;IACjB;IACA;IACA,IAAIA,OAAO,CAAC,UAAU,CAAC,IAAIA,OAAO,CAAC,qBAAqB,CAAC,EAAE;MACvD,IAAI,CAACV,YAAY,CAAC5a,IAAI,CAAC,CAAC;IAC5B;IACA,IAAIsb,OAAO,CAAC,2BAA2B,CAAC,IAAI,IAAI,CAAC6O,WAAW,EAAE;MAC1D,IAAI,CAACA,WAAW,CAACyE,aAAa,CAAC,IAAI,CAAClB,yBAAyB,CAAC;IAClE;EACJ;EACA9rB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACuoB,WAAW,EAAEjpB,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACmrB,QAAQ,CAACrsB,IAAI,CAAC,CAAC;IACpB,IAAI,CAACqsB,QAAQ,CAAClrB,QAAQ,CAAC,CAAC;IACxB,IAAI,CAACyZ,YAAY,CAACzZ,QAAQ,CAAC,CAAC;IAC5B,IAAI,CAAC0tB,eAAe,CAAC,CAAC;EAC1B;EACA;EACAjT,MAAMA,CAAA,EAAG;IACL,IAAI,CAAC4R,SAAS,GAAG,IAAI,CAACsB,KAAK,CAAC,CAAC,GAAG,IAAI,CAAC9Z,IAAI,CAAC,CAAC;EAC/C;EACA;EACAA,IAAIA,CAAA,EAAG;IACH,IAAI,CAAC,IAAI,CAAC+Z,QAAQ,CAAC,CAAC,EAAE;MAClB;IACJ;IACA;IACA;IACA;IACA,IAAI,IAAI,CAACvD,gBAAgB,EAAE;MACvB,IAAI,CAACyC,uBAAuB,GAAG,IAAI,CAACzC,gBAAgB,CAACjR,yBAAyB,CAAC,CAAC;IACpF;IACA,IAAI,CAACwT,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAAC,IAAI,CAACC,uBAAuB,CAAC;IACxE,IAAI,CAACe,yBAAyB,CAAC,CAAC;IAChC,IAAI,CAACzE,UAAU,GAAG,IAAI;IACtB,IAAI,CAACJ,WAAW,CAAC8E,yBAAyB,CAAC,IAAI,CAAC;IAChD,IAAI,CAACC,uBAAuB,CAAC,CAAC;IAC9B,IAAI,CAAC5W,kBAAkB,CAACC,YAAY,CAAC,CAAC;IACtC;IACA,IAAI,CAACqC,YAAY,CAAC5a,IAAI,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIgvB,yBAAyBA,CAAA,EAAG;IACxB;IACA;IACA;IACA;IACA;IACA;IACA,MAAMG,KAAK,GAAG,IAAI,CAAC3pB,WAAW,CAACuC,aAAa,CAACqnB,OAAO,CAAC,mDAAmD,CAAC;IACzG,IAAI,CAACD,KAAK,EAAE;MACR;MACA;IACJ;IACA,MAAME,OAAO,GAAG,GAAG,IAAI,CAAChgB,EAAE,QAAQ;IAClC,IAAI,IAAI,CAACie,aAAa,EAAE;MACpBxH,0EAAsB,CAAC,IAAI,CAACwH,aAAa,EAAE,WAAW,EAAE+B,OAAO,CAAC;IACpE;IACAtJ,uEAAmB,CAACoJ,KAAK,EAAE,WAAW,EAAEE,OAAO,CAAC;IAChD,IAAI,CAAC/B,aAAa,GAAG6B,KAAK;EAC9B;EACA;EACAN,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACvB,aAAa,EAAE;MACrB;MACA;IACJ;IACA,MAAM+B,OAAO,GAAG,GAAG,IAAI,CAAChgB,EAAE,QAAQ;IAClCyW,0EAAsB,CAAC,IAAI,CAACwH,aAAa,EAAE,WAAW,EAAE+B,OAAO,CAAC;IAChE,IAAI,CAAC/B,aAAa,GAAG,IAAI;EAC7B;EACA;EACAwB,KAAKA,CAAA,EAAG;IACJ,IAAI,IAAI,CAACvE,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,GAAG,KAAK;MACvB,IAAI,CAACJ,WAAW,CAAC8E,yBAAyB,CAAC,IAAI,CAACK,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC;MACzE,IAAI,CAAChX,kBAAkB,CAACC,YAAY,CAAC,CAAC;MACtC,IAAI,CAAC+T,UAAU,CAAC,CAAC;MACjB;MACA,IAAI,CAAC1R,YAAY,CAAC5a,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;EACIuvB,UAAUA,CAAC9oB,KAAK,EAAE;IACd,IAAI,CAAC2kB,YAAY,CAAC3kB,KAAK,CAAC;EAC5B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI+oB,gBAAgBA,CAACxE,EAAE,EAAE;IACjB,IAAI,CAACK,SAAS,GAAGL,EAAE;EACvB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIyE,iBAAiBA,CAACzE,EAAE,EAAE;IAClB,IAAI,CAACsB,UAAU,GAAGtB,EAAE;EACxB;EACA;AACJ;AACA;AACA;AACA;AACA;EACI0E,gBAAgBA,CAACC,UAAU,EAAE;IACzB,IAAI,CAAC9Q,QAAQ,GAAG8Q,UAAU;IAC1B,IAAI,CAACrX,kBAAkB,CAACC,YAAY,CAAC,CAAC;IACtC,IAAI,CAACqC,YAAY,CAAC5a,IAAI,CAAC,CAAC;EAC5B;EACA;EACA,IAAIwtB,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACjD,UAAU;EAC1B;EACA;EACA,IAAIqF,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAClN,QAAQ,GAAG,IAAI,CAACmI,eAAe,EAAE+E,QAAQ,IAAI,EAAE,GAAG,IAAI,CAAC/E,eAAe,EAAE+E,QAAQ,CAAC,CAAC,CAAC;EACnG;EACA;EACA,IAAIxI,YAAYA,CAAA,EAAG;IACf,IAAI,IAAI,CAACzD,KAAK,EAAE;MACZ,OAAO,EAAE;IACb;IACA,IAAI,IAAI,CAACiH,SAAS,EAAE;MAChB,MAAMiF,eAAe,GAAG,IAAI,CAAChF,eAAe,CAAC+E,QAAQ,CAACzS,GAAG,CAACsM,MAAM,IAAIA,MAAM,CAACqG,SAAS,CAAC;MACrF,IAAI,IAAI,CAACR,MAAM,CAAC,CAAC,EAAE;QACfO,eAAe,CAACE,OAAO,CAAC,CAAC;MAC7B;MACA;MACA,OAAOF,eAAe,CAAC9L,IAAI,CAAC,IAAI,CAAC;IACrC;IACA,OAAO,IAAI,CAAC8G,eAAe,CAAC+E,QAAQ,CAAC,CAAC,CAAC,CAACE,SAAS;EACrD;EACA;EACAlN,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACtB,kBAAkB,CAACsB,gBAAgB,CAAC,CAAC;EAC9C;EACA;EACA0M,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACnW,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC1S,KAAK,KAAK,KAAK,GAAG,KAAK;EACxD;EACA;EACAohB,cAAcA,CAACrjB,KAAK,EAAE;IAClB,IAAI,CAAC,IAAI,CAACqa,QAAQ,EAAE;MAChB,IAAI,CAAC2O,SAAS,GAAG,IAAI,CAACwC,kBAAkB,CAACxrB,KAAK,CAAC,GAAG,IAAI,CAACyrB,oBAAoB,CAACzrB,KAAK,CAAC;IACtF;EACJ;EACA;EACAyrB,oBAAoBA,CAACzrB,KAAK,EAAE;IACxB,MAAM0rB,OAAO,GAAG1rB,KAAK,CAAC0rB,OAAO;IAC7B,MAAMC,UAAU,GAAGD,OAAO,KAAKhK,8DAAU,IACrCgK,OAAO,KAAK/J,4DAAQ,IACpB+J,OAAO,KAAK9J,8DAAU,IACtB8J,OAAO,KAAK7J,+DAAW;IAC3B,MAAM+J,SAAS,GAAGF,OAAO,KAAK5J,yDAAK,IAAI4J,OAAO,KAAK3J,yDAAK;IACxD,MAAM8J,OAAO,GAAG,IAAI,CAAClG,WAAW;IAChC;IACA,IAAK,CAACkG,OAAO,CAACC,QAAQ,CAAC,CAAC,IAAIF,SAAS,IAAI,CAAC5J,sEAAc,CAAChiB,KAAK,CAAC,IAC1D,CAAC,IAAI,CAACke,QAAQ,IAAIle,KAAK,CAAC+rB,MAAM,KAAKJ,UAAW,EAAE;MACjD3rB,KAAK,CAACgsB,cAAc,CAAC,CAAC,CAAC,CAAC;MACxB,IAAI,CAACxb,IAAI,CAAC,CAAC;IACf,CAAC,MACI,IAAI,CAAC,IAAI,CAAC0N,QAAQ,EAAE;MACrB,MAAM+N,wBAAwB,GAAG,IAAI,CAACb,QAAQ;MAC9CS,OAAO,CAACK,SAAS,CAAClsB,KAAK,CAAC;MACxB,MAAMmsB,cAAc,GAAG,IAAI,CAACf,QAAQ;MACpC;MACA,IAAIe,cAAc,IAAIF,wBAAwB,KAAKE,cAAc,EAAE;QAC/D;QACA;QACA,IAAI,CAAChF,cAAc,CAACiF,QAAQ,CAACD,cAAc,CAACb,SAAS,EAAE,KAAK,CAAC;MACjE;IACJ;EACJ;EACA;EACAE,kBAAkBA,CAACxrB,KAAK,EAAE;IACtB,MAAM6rB,OAAO,GAAG,IAAI,CAAClG,WAAW;IAChC,MAAM+F,OAAO,GAAG1rB,KAAK,CAAC0rB,OAAO;IAC7B,MAAMC,UAAU,GAAGD,OAAO,KAAKhK,8DAAU,IAAIgK,OAAO,KAAK/J,4DAAQ;IACjE,MAAMmK,QAAQ,GAAGD,OAAO,CAACC,QAAQ,CAAC,CAAC;IACnC,IAAIH,UAAU,IAAI3rB,KAAK,CAAC+rB,MAAM,EAAE;MAC5B;MACA/rB,KAAK,CAACgsB,cAAc,CAAC,CAAC;MACtB,IAAI,CAAC1B,KAAK,CAAC,CAAC;MACZ;MACA;IACJ,CAAC,MACI,IAAI,CAACwB,QAAQ,KACbJ,OAAO,KAAK5J,yDAAK,IAAI4J,OAAO,KAAK3J,yDAAK,CAAC,IACxC8J,OAAO,CAACQ,UAAU,IAClB,CAACrK,sEAAc,CAAChiB,KAAK,CAAC,EAAE;MACxBA,KAAK,CAACgsB,cAAc,CAAC,CAAC;MACtBH,OAAO,CAACQ,UAAU,CAACC,qBAAqB,CAAC,CAAC;IAC9C,CAAC,MACI,IAAI,CAACR,QAAQ,IAAI,IAAI,CAAC1F,SAAS,IAAIsF,OAAO,KAAKzJ,qDAAC,IAAIjiB,KAAK,CAACusB,OAAO,EAAE;MACpEvsB,KAAK,CAACgsB,cAAc,CAAC,CAAC;MACtB,MAAMQ,oBAAoB,GAAG,IAAI,CAAClvB,OAAO,CAACjB,IAAI,CAACowB,GAAG,IAAI,CAACA,GAAG,CAACpS,QAAQ,IAAI,CAACoS,GAAG,CAACrB,QAAQ,CAAC;MACrF,IAAI,CAAC9tB,OAAO,CAACoD,OAAO,CAACukB,MAAM,IAAI;QAC3B,IAAI,CAACA,MAAM,CAAC5K,QAAQ,EAAE;UAClBmS,oBAAoB,GAAGvH,MAAM,CAAC4E,MAAM,CAAC,CAAC,GAAG5E,MAAM,CAAC8E,QAAQ,CAAC,CAAC;QAC9D;MACJ,CAAC,CAAC;IACN,CAAC,MACI;MACD,MAAM2C,sBAAsB,GAAGb,OAAO,CAACjG,eAAe;MACtDiG,OAAO,CAACK,SAAS,CAAClsB,KAAK,CAAC;MACxB,IAAI,IAAI,CAAComB,SAAS,IACduF,UAAU,IACV3rB,KAAK,CAAC2sB,QAAQ,IACdd,OAAO,CAACQ,UAAU,IAClBR,OAAO,CAACjG,eAAe,KAAK8G,sBAAsB,EAAE;QACpDb,OAAO,CAACQ,UAAU,CAACC,qBAAqB,CAAC,CAAC;MAC9C;IACJ;EACJ;EACAM,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC,IAAI,CAACvS,QAAQ,EAAE;MAChB,IAAI,CAACyL,QAAQ,GAAG,IAAI;MACpB,IAAI,CAAC1P,YAAY,CAAC5a,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;AACA;EACIqxB,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC/G,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACH,WAAW,EAAEmH,eAAe,CAAC,CAAC;IACnC,IAAI,CAAC,IAAI,CAACzS,QAAQ,IAAI,CAAC,IAAI,CAAC2O,SAAS,EAAE;MACnC,IAAI,CAAClB,UAAU,CAAC,CAAC;MACjB,IAAI,CAAChU,kBAAkB,CAACC,YAAY,CAAC,CAAC;MACtC,IAAI,CAACqC,YAAY,CAAC5a,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;AACJ;AACA;EACIuxB,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,WAAW,CAACC,cAAc,CAAC7wB,IAAI,CAACkmB,qDAAI,CAAC,CAAC,CAAC,CAAC,CAACvmB,SAAS,CAAC,MAAM;MAC1D,IAAI,CAAC+X,kBAAkB,CAACwB,aAAa,CAAC,CAAC;MACvC,IAAI,CAACoQ,mBAAmB,CAAC,CAAC;IAC9B,CAAC,CAAC;EACN;EACA;EACAnC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACyD,gBAAgB,GAAG,OAAO,IAAI,CAACA,gBAAgB,CAAClS,KAAK,EAAE,GAAG,EAAE;EAC5E;EACA;EACA,IAAIqK,KAAKA,CAAA,EAAG;IACR,OAAO,CAAC,IAAI,CAACkH,eAAe,IAAI,IAAI,CAACA,eAAe,CAAC6G,OAAO,CAAC,CAAC;EAClE;EACAzG,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACA0G,OAAO,CAACC,OAAO,CAAC,CAAC,CAACC,IAAI,CAAC,MAAM;MACzB,IAAI,IAAI,CAAC/W,SAAS,EAAE;QAChB,IAAI,CAACoQ,MAAM,GAAG,IAAI,CAACpQ,SAAS,CAACrU,KAAK;MACtC;MACA,IAAI,CAACqrB,oBAAoB,CAAC,IAAI,CAAC5G,MAAM,CAAC;MACtC,IAAI,CAACtQ,YAAY,CAAC5a,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACI8xB,oBAAoBA,CAACrrB,KAAK,EAAE;IACxB,IAAI,CAAC3E,OAAO,CAACoD,OAAO,CAACukB,MAAM,IAAIA,MAAM,CAACsI,iBAAiB,CAAC,CAAC,CAAC;IAC1D,IAAI,CAAClH,eAAe,CAACzpB,KAAK,CAAC,CAAC;IAC5B,IAAI,IAAI,CAACshB,QAAQ,IAAIjc,KAAK,EAAE;MACxB,IAAI,CAACurB,KAAK,CAACC,OAAO,CAACxrB,KAAK,CAAC,KAAK,OAAOjF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QAC1E,MAAMgnB,8BAA8B,CAAC,CAAC;MAC1C;MACA/hB,KAAK,CAACvB,OAAO,CAAEgtB,YAAY,IAAK,IAAI,CAACC,oBAAoB,CAACD,YAAY,CAAC,CAAC;MACxE,IAAI,CAACE,WAAW,CAAC,CAAC;IACtB,CAAC,MACI;MACD,MAAMC,mBAAmB,GAAG,IAAI,CAACF,oBAAoB,CAAC1rB,KAAK,CAAC;MAC5D;MACA;MACA,IAAI4rB,mBAAmB,EAAE;QACrB,IAAI,CAAClI,WAAW,CAACmI,gBAAgB,CAACD,mBAAmB,CAAC;MAC1D,CAAC,MACI,IAAI,CAAC,IAAI,CAAC7E,SAAS,EAAE;QACtB;QACA;QACA,IAAI,CAACrD,WAAW,CAACmI,gBAAgB,CAAC,CAAC,CAAC,CAAC;MACzC;IACJ;IACA,IAAI,CAACha,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACI4Z,oBAAoBA,CAAC1rB,KAAK,EAAE;IACxB,MAAM4rB,mBAAmB,GAAG,IAAI,CAACvwB,OAAO,CAACoZ,IAAI,CAAEuO,MAAM,IAAK;MACtD;MACA;MACA,IAAI,IAAI,CAACoB,eAAe,CAAC0H,UAAU,CAAC9I,MAAM,CAAC,EAAE;QACzC,OAAO,KAAK;MAChB;MACA,IAAI;QACA;QACA,OAAOA,MAAM,CAAChjB,KAAK,IAAI,IAAI,IAAI,IAAI,CAACskB,YAAY,CAACtB,MAAM,CAAChjB,KAAK,EAAEA,KAAK,CAAC;MACzE,CAAC,CACD,OAAOpH,KAAK,EAAE;QACV,IAAI,OAAOmC,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;UAC/C;UACApC,OAAO,CAACozB,IAAI,CAACnzB,KAAK,CAAC;QACvB;QACA,OAAO,KAAK;MAChB;IACJ,CAAC,CAAC;IACF,IAAIgzB,mBAAmB,EAAE;MACrB,IAAI,CAACxH,eAAe,CAACwD,MAAM,CAACgE,mBAAmB,CAAC;IACpD;IACA,OAAOA,mBAAmB;EAC9B;EACA;EACAjH,YAAYA,CAACjI,QAAQ,EAAE;IACnB;IACA,IAAIA,QAAQ,KAAK,IAAI,CAAC+H,MAAM,IAAK,IAAI,CAACN,SAAS,IAAIoH,KAAK,CAACC,OAAO,CAAC9O,QAAQ,CAAE,EAAE;MACzE,IAAI,IAAI,CAACrhB,OAAO,EAAE;QACd,IAAI,CAACgwB,oBAAoB,CAAC3O,QAAQ,CAAC;MACvC;MACA,IAAI,CAAC+H,MAAM,GAAG/H,QAAQ;MACtB,OAAO,IAAI;IACf;IACA,OAAO,KAAK;EAChB;EACA;EACA6K,gBAAgBA,CAACyE,eAAe,EAAE;IAC9B,IAAI,IAAI,CAAC7F,UAAU,KAAK,MAAM,EAAE;MAC5B,MAAM8F,YAAY,GAAGD,eAAe,YAAYzN,kEAAgB,GAC1DyN,eAAe,CAACjhB,UAAU,GAC1BihB,eAAe,IAAI,IAAI,CAACjtB,WAAW;MACzC,OAAOktB,YAAY,CAAC3qB,aAAa,CAAC6V,qBAAqB,CAAC,CAAC,CAACrI,KAAK;IACnE;IACA,OAAO,IAAI,CAACqX,UAAU,KAAK,IAAI,GAAG,EAAE,GAAG,IAAI,CAACA,UAAU;EAC1D;EACA;EACAlC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,IAAI,CAAC5oB,OAAO,EAAE;MACd,KAAK,MAAM2nB,MAAM,IAAI,IAAI,CAAC3nB,OAAO,EAAE;QAC/B2nB,MAAM,CAACnR,kBAAkB,CAACC,YAAY,CAAC,CAAC;MAC5C;IACJ;EACJ;EACA;EACA2V,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC/D,WAAW,GAAG,IAAInE,0EAA0B,CAAC,IAAI,CAAClkB,OAAO,CAAC,CAC1D8sB,aAAa,CAAC,IAAI,CAAClB,yBAAyB,CAAC,CAC7CiF,uBAAuB,CAAC,CAAC,CACzB1D,yBAAyB,CAAC,IAAI,CAACK,MAAM,CAAC,CAAC,GAAG,KAAK,GAAG,KAAK,CAAC,CACxDsD,cAAc,CAAC,CAAC,CAChBC,cAAc,CAAC,CAAC,CAChBC,uBAAuB,CAAC,CAAC,UAAU,CAAC,CAAC,CACrCC,aAAa,CAAC,IAAI,CAACxF,cAAc,CAAC;IACvC,IAAI,CAACpD,WAAW,CAAC6I,MAAM,CAACzyB,SAAS,CAAC,MAAM;MACpC,IAAI,IAAI,CAACitB,SAAS,EAAE;QAChB;QACA;QACA,IAAI,CAAC,IAAI,CAAC9K,QAAQ,IAAI,IAAI,CAACyH,WAAW,CAAC0G,UAAU,EAAE;UAC/C,IAAI,CAAC1G,WAAW,CAAC0G,UAAU,CAACC,qBAAqB,CAAC,CAAC;QACvD;QACA;QACA;QACA,IAAI,CAAC/N,KAAK,CAAC,CAAC;QACZ,IAAI,CAAC+L,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;IACF,IAAI,CAAC3E,WAAW,CAACpO,MAAM,CAACxb,SAAS,CAAC,MAAM;MACpC,IAAI,IAAI,CAACgqB,UAAU,IAAI,IAAI,CAACZ,KAAK,EAAE;QAC/B,IAAI,CAACJ,qBAAqB,CAAC,IAAI,CAACY,WAAW,CAACC,eAAe,IAAI,CAAC,CAAC;MACrE,CAAC,MACI,IAAI,CAAC,IAAI,CAACG,UAAU,IAAI,CAAC,IAAI,CAAC7H,QAAQ,IAAI,IAAI,CAACyH,WAAW,CAAC0G,UAAU,EAAE;QACxE,IAAI,CAAC1G,WAAW,CAAC0G,UAAU,CAACC,qBAAqB,CAAC,CAAC;MACvD;IACJ,CAAC,CAAC;EACN;EACA;EACAtC,aAAaA,CAAA,EAAG;IACZ,MAAMyE,kBAAkB,GAAG9lB,4CAAK,CAAC,IAAI,CAACrL,OAAO,CAACwZ,OAAO,EAAE,IAAI,CAAC+Q,QAAQ,CAAC;IACrE,IAAI,CAACS,sBAAsB,CAAClsB,IAAI,CAAC7B,0DAAS,CAACk0B,kBAAkB,CAAC,CAAC,CAAC1yB,SAAS,CAACiE,KAAK,IAAI;MAC/E,IAAI,CAAC0uB,SAAS,CAAC1uB,KAAK,CAAC6kB,MAAM,EAAE7kB,KAAK,CAAC2uB,WAAW,CAAC;MAC/C,IAAI3uB,KAAK,CAAC2uB,WAAW,IAAI,CAAC,IAAI,CAACzQ,QAAQ,IAAI,IAAI,CAAC6H,UAAU,EAAE;QACxD,IAAI,CAACuE,KAAK,CAAC,CAAC;QACZ,IAAI,CAAC/L,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;IACF;IACA;IACA5V,4CAAK,CAAC,GAAG,IAAI,CAACrL,OAAO,CAACqb,GAAG,CAACsM,MAAM,IAAIA,MAAM,CAAC2J,aAAa,CAAC,CAAC,CACrDxyB,IAAI,CAAC7B,0DAAS,CAACk0B,kBAAkB,CAAC,CAAC,CACnC1yB,SAAS,CAAC,MAAM;MACjB;MACA;MACA;MACA,IAAI,CAAC+X,kBAAkB,CAACwB,aAAa,CAAC,CAAC;MACvC,IAAI,CAACc,YAAY,CAAC5a,IAAI,CAAC,CAAC;IAC5B,CAAC,CAAC;EACN;EACA;EACAkzB,SAASA,CAACzJ,MAAM,EAAE0J,WAAW,EAAE;IAC3B,MAAME,WAAW,GAAG,IAAI,CAACxI,eAAe,CAAC0H,UAAU,CAAC9I,MAAM,CAAC;IAC3D,IAAIA,MAAM,CAAChjB,KAAK,IAAI,IAAI,IAAI,CAAC,IAAI,CAACmkB,SAAS,EAAE;MACzCnB,MAAM,CAAC8E,QAAQ,CAAC,CAAC;MACjB,IAAI,CAAC1D,eAAe,CAACzpB,KAAK,CAAC,CAAC;MAC5B,IAAI,IAAI,CAACqF,KAAK,IAAI,IAAI,EAAE;QACpB,IAAI,CAAC6sB,iBAAiB,CAAC7J,MAAM,CAAChjB,KAAK,CAAC;MACxC;IACJ,CAAC,MACI;MACD,IAAI4sB,WAAW,KAAK5J,MAAM,CAACmG,QAAQ,EAAE;QACjCnG,MAAM,CAACmG,QAAQ,GACT,IAAI,CAAC/E,eAAe,CAACwD,MAAM,CAAC5E,MAAM,CAAC,GACnC,IAAI,CAACoB,eAAe,CAAC0D,QAAQ,CAAC9E,MAAM,CAAC;MAC/C;MACA,IAAI0J,WAAW,EAAE;QACb,IAAI,CAAChJ,WAAW,CAACoJ,aAAa,CAAC9J,MAAM,CAAC;MAC1C;MACA,IAAI,IAAI,CAAC/G,QAAQ,EAAE;QACf,IAAI,CAAC0P,WAAW,CAAC,CAAC;QAClB,IAAIe,WAAW,EAAE;UACb;UACA;UACA;UACA;UACA,IAAI,CAACpQ,KAAK,CAAC,CAAC;QAChB;MACJ;IACJ;IACA,IAAIsQ,WAAW,KAAK,IAAI,CAACxI,eAAe,CAAC0H,UAAU,CAAC9I,MAAM,CAAC,EAAE;MACzD,IAAI,CAAC6J,iBAAiB,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC1Y,YAAY,CAAC5a,IAAI,CAAC,CAAC;EAC5B;EACA;EACAoyB,WAAWA,CAAA,EAAG;IACV,IAAI,IAAI,CAAC1P,QAAQ,EAAE;MACf,MAAM5gB,OAAO,GAAG,IAAI,CAACA,OAAO,CAAC4nB,OAAO,CAAC,CAAC;MACtC,IAAI,CAACmB,eAAe,CAAC2I,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK;QAChC,OAAO,IAAI,CAACC,cAAc,GACpB,IAAI,CAACA,cAAc,CAACF,CAAC,EAAEC,CAAC,EAAE5xB,OAAO,CAAC,GAClCA,OAAO,CAACwhB,OAAO,CAACmQ,CAAC,CAAC,GAAG3xB,OAAO,CAACwhB,OAAO,CAACoQ,CAAC,CAAC;MACjD,CAAC,CAAC;MACF,IAAI,CAAC9Y,YAAY,CAAC5a,IAAI,CAAC,CAAC;IAC5B;EACJ;EACA;EACAszB,iBAAiBA,CAACM,aAAa,EAAE;IAC7B,IAAIC,WAAW;IACf,IAAI,IAAI,CAACnR,QAAQ,EAAE;MACfmR,WAAW,GAAG,IAAI,CAACjE,QAAQ,CAACzS,GAAG,CAACsM,MAAM,IAAIA,MAAM,CAAChjB,KAAK,CAAC;IAC3D,CAAC,MACI;MACDotB,WAAW,GAAG,IAAI,CAACjE,QAAQ,GAAG,IAAI,CAACA,QAAQ,CAACnpB,KAAK,GAAGmtB,aAAa;IACrE;IACA,IAAI,CAAC1I,MAAM,GAAG2I,WAAW;IACzB,IAAI,CAACxG,WAAW,CAACznB,IAAI,CAACiuB,WAAW,CAAC;IAClC,IAAI,CAACxI,SAAS,CAACwI,WAAW,CAAC;IAC3B,IAAI,CAACzG,eAAe,CAACxnB,IAAI,CAAC,IAAI,CAACykB,eAAe,CAACwJ,WAAW,CAAC,CAAC;IAC5D,IAAI,CAACvb,kBAAkB,CAACC,YAAY,CAAC,CAAC;EAC1C;EACA;AACJ;AACA;AACA;EACI2W,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAAC/E,WAAW,EAAE;MAClB,IAAI,IAAI,CAACxG,KAAK,EAAE;QACZ;QACA;QACA;QACA,IAAImQ,uBAAuB,GAAG,CAAC,CAAC;QAChC,KAAK,IAAItK,KAAK,GAAG,CAAC,EAAEA,KAAK,GAAG,IAAI,CAAC1nB,OAAO,CAACma,MAAM,EAAEuN,KAAK,EAAE,EAAE;UACtD,MAAMC,MAAM,GAAG,IAAI,CAAC3nB,OAAO,CAACb,GAAG,CAACuoB,KAAK,CAAC;UACtC,IAAI,CAACC,MAAM,CAAC5K,QAAQ,EAAE;YAClBiV,uBAAuB,GAAGtK,KAAK;YAC/B;UACJ;QACJ;QACA,IAAI,CAACW,WAAW,CAACoJ,aAAa,CAACO,uBAAuB,CAAC;MAC3D,CAAC,MACI;QACD,IAAI,CAAC3J,WAAW,CAACoJ,aAAa,CAAC,IAAI,CAAC1I,eAAe,CAAC+E,QAAQ,CAAC,CAAC,CAAC,CAAC;MACpE;IACJ;EACJ;EACA;EACAb,QAAQA,CAAA,EAAG;IACP,OAAO,CAAC,IAAI,CAACxE,UAAU,IAAI,CAAC,IAAI,CAAC1L,QAAQ,IAAI,IAAI,CAAC/c,OAAO,EAAEma,MAAM,GAAG,CAAC;EACzE;EACA;EACA8G,KAAKA,CAACjhB,OAAO,EAAE;IACX,IAAI,CAAC0D,WAAW,CAACuC,aAAa,CAACgb,KAAK,CAACjhB,OAAO,CAAC;EACjD;EACA;EACAomB,uBAAuBA,CAAA,EAAG;IACtB,IAAI,IAAI,CAACD,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,MAAM8L,OAAO,GAAG,IAAI,CAACvI,gBAAgB,EAAElR,UAAU,CAAC,CAAC;IACnD,MAAM0Z,eAAe,GAAGD,OAAO,GAAGA,OAAO,GAAG,GAAG,GAAG,EAAE;IACpD,OAAO,IAAI,CAACE,cAAc,GAAGD,eAAe,GAAG,IAAI,CAACC,cAAc,GAAGF,OAAO;EAChF;EACA;EACAG,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAAC1G,SAAS,IAAI,IAAI,CAACrD,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC0G,UAAU,EAAE;MACnE,OAAO,IAAI,CAAC1G,WAAW,CAAC0G,UAAU,CAACxhB,EAAE;IACzC;IACA,OAAO,IAAI;EACf;EACA;EACAqf,yBAAyBA,CAAA,EAAG;IACxB,IAAI,IAAI,CAACzG,SAAS,EAAE;MAChB,OAAO,IAAI;IACf;IACA,MAAM8L,OAAO,GAAG,IAAI,CAACvI,gBAAgB,EAAElR,UAAU,CAAC,CAAC;IACnD,IAAI7T,KAAK,GAAG,CAACstB,OAAO,GAAGA,OAAO,GAAG,GAAG,GAAG,EAAE,IAAI,IAAI,CAACxH,QAAQ;IAC1D,IAAI,IAAI,CAAC0H,cAAc,EAAE;MACrBxtB,KAAK,IAAI,GAAG,GAAG,IAAI,CAACwtB,cAAc;IACtC;IACA,OAAOxtB,KAAK;EAChB;EACA;EACAqnB,mBAAmBA,CAACqG,MAAM,EAAE;IACxB,IAAI,CAACnH,YAAY,CAACpnB,IAAI,CAACuuB,MAAM,CAAC;EAClC;EACA;AACJ;AACA;AACA;EACI/W,iBAAiBA,CAACL,GAAG,EAAE;IACnB,IAAIA,GAAG,CAACd,MAAM,EAAE;MACZ,IAAI,CAACzW,WAAW,CAACuC,aAAa,CAACT,YAAY,CAAC,kBAAkB,EAAEyV,GAAG,CAACgH,IAAI,CAAC,GAAG,CAAC,CAAC;IAClF,CAAC,MACI;MACD,IAAI,CAACve,WAAW,CAACuC,aAAa,CAACR,eAAe,CAAC,kBAAkB,CAAC;IACtE;EACJ;EACA;AACJ;AACA;AACA;EACIgY,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACwD,KAAK,CAAC,CAAC;IACZ,IAAI,CAAC/N,IAAI,CAAC,CAAC;EACf;EACA;AACJ;AACA;AACA;EACI,IAAIoH,gBAAgBA,CAAA,EAAG;IACnB;IACA;IACA,OAAO,IAAI,CAACoR,SAAS,IAAI,CAAC,IAAI,CAAC7J,KAAK,IAAK,IAAI,CAACjI,OAAO,IAAI,CAAC,CAAC,IAAI,CAACxU,WAAY;EAChF;EACA;IAAS,IAAI,CAACnF,IAAI,YAAAqyB,kBAAAnyB,CAAA;MAAA,YAAAA,CAAA,IAAwFqnB,SAAS,EAAnB/qB,+DAAE,CAAmCmE,kEAAgB,GAArDnE,+DAAE,CAAgEA,4DAAoB,GAAtFA,+DAAE,CAAiGA,iDAAS,GAA5GA,+DAAE,CAAuH0O,qEAAoB,GAA7I1O,+DAAE,CAAwJA,qDAAa,GAAvKA,+DAAE,CAAkL+hB,8DAAiB,MAArM/hB,+DAAE,CAAgO4hB,kDAAS,MAA3O5hB,+DAAE,CAAsQ4hB,8DAAqB,MAA7R5hB,+DAAE,CAAwTmZ,wEAAc,MAAxUnZ,+DAAE,CAAmW4hB,qDAAY,OAAjX5hB,+DAAE,CAAwZ,UAAU,GAApaA,+DAAE,CAAgcmqB,0BAA0B,GAA5dnqB,+DAAE,CAAueiiB,6DAAgB,GAAzfjiB,+DAAE,CAAogBwqB,iBAAiB;IAAA,CAA4D;EAAE;EACrrB;IAAS,IAAI,CAACpT,IAAI,kBAD8EpX,+DAAE;MAAAiE,IAAA,EACJ8mB,SAAS;MAAApjB,SAAA;MAAAqY,cAAA,WAAAgW,yBAAAtpB,EAAA,EAAAC,GAAA,EAAAuT,QAAA;QAAA,IAAAxT,EAAA;UADP1M,4DAAE,CAAAkgB,QAAA,EAIxB0K,kBAAkB;UAJI5qB,4DAAE,CAAAkgB,QAAA,EAIuD+G,6DAAS;UAJlEjnB,4DAAE,CAAAkgB,QAAA,EAIkIgH,gEAAY;QAAA;QAAA,IAAAxa,EAAA;UAAA,IAAA+K,EAAA;UAJhJzX,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAoc,aAAA,GAAAtR,EAAA,CAAAG,KAAA;UAAF5X,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAApJ,OAAA,GAAAkU,EAAA;UAAFzX,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAA2e,YAAA,GAAA7T,EAAA;QAAA;MAAA;MAAAH,SAAA,WAAA2e,gBAAAvpB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF1M,yDAAE,CAAAsP,GAAA;UAAFtP,yDAAE,CAAAuP,GAAA;UAAFvP,yDAAE,CAIiZ0mB,qEAAmB;QAAA;QAAA,IAAAha,EAAA;UAAA,IAAA+K,EAAA;UAJtazX,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAmC,OAAA,GAAA2I,EAAA,CAAAG,KAAA;UAAF5X,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAye,KAAA,GAAA3T,EAAA,CAAAG,KAAA;UAAF5X,4DAAE,CAAAyX,EAAA,GAAFzX,yDAAE,QAAA2M,GAAA,CAAAsmB,WAAA,GAAAxb,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAArL,SAAA,WACyuC,UAAU,uBAAuB,MAAM,mBAAmB,SAAS;MAAA6G,QAAA;MAAA5G,YAAA,WAAA0pB,uBAAAxpB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD9yC1M,wDAAE,qBAAAm2B,qCAAAtV,MAAA;YAAA,OACJlU,GAAA,CAAA2c,cAAA,CAAAzI,MAAqB,CAAC;UAAA,CAAd,CAAC,mBAAAuV,mCAAA;YAAA,OAATzpB,GAAA,CAAAkmB,QAAA,CAAS,CAAC;UAAA,CAAF,CAAC,kBAAAwD,kCAAA;YAAA,OAAT1pB,GAAA,CAAAmmB,OAAA,CAAQ,CAAC;UAAA,CAAD,CAAC;QAAA;QAAA,IAAApmB,EAAA;UADP1M,yDAAE,OAAA2M,GAAA,CAAAmE,EAAA,cAAAnE,GAAA,CAAA2T,QAAA,IACQ,CAAC,GAAA3T,GAAA,CAAAugB,QAAA,mBAAAvgB,GAAA,CAAAsiB,SAAA,GAAAtiB,GAAA,CAAAmE,EAAA,GAAI,QAAQ,GAAG,IAAI,mBAAAnE,GAAA,CAAAsiB,SAAA,gBAAAtiB,GAAA,CAAA+c,SAAA,IAAnB,IAAI,mBAAjB/c,GAAA,CAAAuE,QAAA,CAAAolB,QAAA,CAAkB,CAAC,mBAAnB3pB,GAAA,CAAA2T,QAAA,CAAAgW,QAAA,CAAkB,CAAC,kBAAA3pB,GAAA,CAAAsR,UAAA,2BAAnBtR,GAAA,CAAAgpB,wBAAA,CAAyB,CAAC;UADxB31B,yDAAE,4BAAA2M,GAAA,CAAA2T,QACI,CAAC,2BAAA3T,GAAA,CAAAsR,UAAD,CAAC,4BAAAtR,GAAA,CAAAuE,QAAD,CAAC,yBAAAvE,GAAA,CAAAyY,KAAD,CAAC,4BAAAzY,GAAA,CAAAwX,QAAD,CAAC;QAAA;MAAA;MAAArX,MAAA;QAAA2R,mBAAA,GADPze,0DAAE,CAAAgN,IAAA;QAAAyc,UAAA;QAAAnJ,QAAA,GAAFtgB,0DAAE,CAAAiN,0BAAA,0BAC4LzI,2DAAgB;QAAA2pB,aAAA,GAD9MnuB,0DAAE,CAAAiN,0BAAA,oCACiQzI,2DAAgB;QAAA0oB,QAAA,GADnRltB,0DAAE,CAAAiN,0BAAA,0BACwT/E,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG2e,8DAAe,CAAC3e,KAAK,CAAE;QAAA+jB,4BAAA,GADhXjsB,0DAAE,CAAAiN,0BAAA,kEACgdzI,2DAAgB;QAAAmE,WAAA;QAAAuI,QAAA,GADlelR,0DAAE,CAAAiN,0BAAA,0BACkiBzI,2DAAgB;QAAA2f,QAAA,GADpjBnkB,0DAAE,CAAAiN,0BAAA,0BACwlBzI,2DAAgB;QAAA4pB,sBAAA,GAD1mBpuB,0DAAE,CAAAiN,0BAAA,sDACwrBzI,2DAAgB;QAAA+nB,WAAA;QAAArkB,KAAA;QAAAwhB,SAAA,GAD1sB1pB,0DAAE,CAAAgN,IAAA;QAAA0oB,cAAA,GAAF11B,0DAAE,CAAAgN,IAAA;QAAA8V,iBAAA;QAAAqM,yBAAA,GAAFnvB,0DAAE,CAAAiN,0BAAA,4DACo9B4Z,0DAAe;QAAAuO,cAAA;QAAAtkB,EAAA;QAAAud,UAAA;MAAA;MAAAzmB,OAAA;QAAA6mB,YAAA;QAAAC,aAAA;QAAAE,aAAA;QAAAC,eAAA;QAAAC,WAAA;MAAA;MAAA5hB,QAAA;MAAArF,UAAA;MAAAsF,QAAA,GADr+BnN,gEAAE,CACyiE,CACnoE;QAAEwT,OAAO,EAAEqF,6EAAmB;QAAEpF,WAAW,EAAEsX;MAAU,CAAC,EACxD;QAAEvX,OAAO,EAAEwT,+EAA2B;QAAEvT,WAAW,EAAEsX;MAAU,CAAC,CACnE,GAJ2F/qB,sEAAE,EAAFA,kEAAE,EAAFA,iEAAE;MAAAgY,kBAAA,EAAAvI,GAAA;MAAAwI,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAme,mBAAA7pB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAA+T,GAAA,GAAFzgB,8DAAE;UAAFA,6DAAE,CAAAwP,GAAA;UAAFxP,4DAAE,eAI2pB,CAAC;UAJ9pBA,wDAAE,mBAAAw2B,wCAAA;YAAFx2B,2DAAE,CAAAygB,GAAA;YAAA,OAAFzgB,yDAAE,CAIklB2M,GAAA,CAAA8J,IAAA,CAAK,CAAC;UAAA,CAAC,CAAC;UAJ5lBzW,4DAAE,YAI4tB,CAAC;UAJ/tBA,wDAAE,IAAA0oB,gCAAA,iBAI+uB,CAAC,IAAAI,gCAAA,MAA+G,CAAC;UAJl2B9oB,0DAAE,CAI4nC,CAAC;UAJ/nCA,4DAAE,YAI8qC,CAAC,YAAyC,CAAC;UAJ3tCA,4DAAE;UAAFA,4DAAE,YAIy6C,CAAC;UAJ56CA,uDAAE,aAI+8C,CAAC;UAJl9CA,0DAAE,CAI69C,CAAC,CAAW,CAAC,CAAS,CAAC,CAAO,CAAC;UAJ9/CA,wDAAE,KAAAgpB,iCAAA,wBAI6lE,CAAC;UAJhmEhpB,wDAAE,2BAAA02B,yDAAA;YAAF12B,2DAAE,CAAAygB,GAAA;YAAA,OAAFzgB,yDAAE,CAI8hE2M,GAAA,CAAA4jB,KAAA,CAAM,CAAC;UAAA,CAAC,CAAC,oBAAAoG,kDAAA;YAJziE32B,2DAAE,CAAAygB,GAAA;YAAA,OAAFzgB,yDAAE,CAIsjE2M,GAAA,CAAAqmB,WAAA,CAAY,CAAC;UAAA,CAAC,CAAC,oBAAA4D,kDAAA;YAJvkE52B,2DAAE,CAAAygB,GAAA;YAAA,OAAFzgB,yDAAE,CAIolE2M,GAAA,CAAA4jB,KAAA,CAAM,CAAC;UAAA,CAAC,CAAC;QAAA;QAAA,IAAA7jB,EAAA;UAAA,MAAAmqB,wBAAA,GAJ/lE72B,yDAAE;UAAFA,uDAAE,EAI2tB,CAAC;UAJ9tBA,yDAAE,OAAA2M,GAAA,CAAAqhB,QAAA;UAAFhuB,uDAAE,CAIknC,CAAC;UAJrnCA,2DAAE,IAAA2M,GAAA,CAAAyY,KAAA,QAIknC,CAAC;UAJrnCplB,uDAAE,EAI4uD,CAAC;UAJ/uDA,wDAAE,kCAAA2M,GAAA,CAAAshB,kBAI4uD,CAAC,sCAAAthB,GAAA,CAAA0iB,eAA0D,CAAC,8BAAA1iB,GAAA,CAAA+iB,uBAAA,IAAAmH,wBAAmF,CAAC,4BAAAlqB,GAAA,CAAAsiB,SAA0C,CAAC,iCAAAtiB,GAAA,CAAA2gB,UAAgD,CAAC,6BAAA3gB,GAAA,CAAA6iB,aAA+C,CAAC;QAAA;MAAA;MAAAtO,YAAA,GAAqkJuF,kEAAgB,EAAuIC,qEAAmB,EAA4+BE,qDAAO;MAAAzF,MAAA;MAAA5I,aAAA;MAAA6I,IAAA;QAAAC,SAAA,EAAsE,CAACuI,mBAAmB,CAACG,cAAc;MAAC;MAAAvR,eAAA;IAAA,EAAiG;EAAE;AAC7hQ;AACA;EAAA,QAAAvV,SAAA,oBAAAA,SAAA,KANoGjD,+DAAE,CAMX+qB,SAAS,EAAc,CAAC;IACvG9mB,IAAI,EAAEkK,oDAAS;IACfjK,IAAI,EAAE,CAAC;MAAE4D,QAAQ,EAAE,YAAY;MAAEoF,QAAQ,EAAE,WAAW;MAAEqL,aAAa,EAAElK,4DAAiB,CAACrB,IAAI;MAAEwL,eAAe,EAAEpK,kEAAuB,CAACqK,MAAM;MAAEpL,IAAI,EAAE;QAC1I,MAAM,EAAE,UAAU;QAClB,mBAAmB,EAAE,MAAM;QAC3B,eAAe,EAAE,SAAS;QAC1B,OAAO,EAAE,gBAAgB;QACzB,WAAW,EAAE,IAAI;QACjB,iBAAiB,EAAE,0BAA0B;QAC7C,sBAAsB,EAAE,kCAAkC;QAC1D,sBAAsB,EAAE,WAAW;QACnC,mBAAmB,EAAE,mBAAmB;QACxC,sBAAsB,EAAE,qBAAqB;QAC7C,sBAAsB,EAAE,qBAAqB;QAC7C,qBAAqB,EAAE,YAAY;QACnC,8BAA8B,EAAE,4BAA4B;QAC5D,iCAAiC,EAAE,UAAU;QAC7C,gCAAgC,EAAE,YAAY;QAC9C,iCAAiC,EAAE,UAAU;QAC7C,8BAA8B,EAAE,OAAO;QACvC,iCAAiC,EAAE,UAAU;QAC7C,WAAW,EAAE,wBAAwB;QACrC,SAAS,EAAE,YAAY;QACvB,QAAQ,EAAE;MACd,CAAC;MAAEiU,UAAU,EAAE,CAACsI,mBAAmB,CAACG,cAAc,CAAC;MAAErW,SAAS,EAAE,CAC5D;QAAEF,OAAO,EAAEqF,6EAAmB;QAAEpF,WAAW,EAAEsX;MAAU,CAAC,EACxD;QAAEvX,OAAO,EAAEwT,+EAA2B;QAAEvT,WAAW,EAAEsX;MAAU,CAAC,CACnE;MAAEljB,UAAU,EAAE,IAAI;MAAEkG,OAAO,EAAE,CAAC0Y,kEAAgB,EAAEC,qEAAmB,EAAEE,qDAAO,CAAC;MAAExO,QAAQ,EAAE,6qEAA6qE;MAAE+I,MAAM,EAAE,CAAC,s2HAAs2H;IAAE,CAAC;EACvoM,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEld,IAAI,EAAEE,kEAAgB2xB;EAAC,CAAC,EAAE;IAAE7xB,IAAI,EAAEjE,4DAAoB8f;EAAC,CAAC,EAAE;IAAE7b,IAAI,EAAEjE,iDAASE;EAAC,CAAC,EAAE;IAAE+D,IAAI,EAAEyK,qEAAoBqX;EAAC,CAAC,EAAE;IAAE9hB,IAAI,EAAEjE,qDAAawH;EAAC,CAAC,EAAE;IAAEvD,IAAI,EAAE8d,8DAAiB;IAAEzU,UAAU,EAAE,CAAC;MACjMrJ,IAAI,EAAEQ,mDAAQA;IAClB,CAAC;EAAE,CAAC,EAAE;IAAER,IAAI,EAAE2d,kDAAS;IAAEtU,UAAU,EAAE,CAAC;MAClCrJ,IAAI,EAAEQ,mDAAQA;IAClB,CAAC;EAAE,CAAC,EAAE;IAAER,IAAI,EAAE2d,8DAAqB;IAAEtU,UAAU,EAAE,CAAC;MAC9CrJ,IAAI,EAAEQ,mDAAQA;IAClB,CAAC;EAAE,CAAC,EAAE;IAAER,IAAI,EAAEojB,sEAAe;IAAE/Z,UAAU,EAAE,CAAC;MACxCrJ,IAAI,EAAEQ,mDAAQA;IAClB,CAAC,EAAE;MACCR,IAAI,EAAES,iDAAM;MACZR,IAAI,EAAE,CAACiV,wEAAc;IACzB,CAAC;EAAE,CAAC,EAAE;IAAElV,IAAI,EAAE2d,qDAAY;IAAEtU,UAAU,EAAE,CAAC;MACrCrJ,IAAI,EAAE4d,+CAAIA;IACd,CAAC,EAAE;MACC5d,IAAI,EAAEQ,mDAAQA;IAClB,CAAC;EAAE,CAAC,EAAE;IAAER,IAAI,EAAE6E,SAAS;IAAEwE,UAAU,EAAE,CAAC;MAClCrJ,IAAI,EAAEiK,oDAAS;MACfhK,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC;EAAE,CAAC,EAAE;IAAED,IAAI,EAAE6E,SAAS;IAAEwE,UAAU,EAAE,CAAC;MAClCrJ,IAAI,EAAES,iDAAM;MACZR,IAAI,EAAE,CAACimB,0BAA0B;IACrC,CAAC;EAAE,CAAC,EAAE;IAAElmB,IAAI,EAAEge,6DAAgB8T;EAAC,CAAC,EAAE;IAAE9xB,IAAI,EAAE6E,SAAS;IAAEwE,UAAU,EAAE,CAAC;MAC9DrJ,IAAI,EAAEQ,mDAAQA;IAClB,CAAC,EAAE;MACCR,IAAI,EAAES,iDAAM;MACZR,IAAI,EAAE,CAACsmB,iBAAiB;IAC5B,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEjnB,OAAO,EAAE,CAAC;MACnCU,IAAI,EAAEwK,0DAAe;MACrBvK,IAAI,EAAE,CAAC+iB,6DAAS,EAAE;QAAEzF,WAAW,EAAE;MAAK,CAAC;IAC3C,CAAC,CAAC;IAAE8J,YAAY,EAAE,CAAC;MACfrnB,IAAI,EAAEwK,0DAAe;MACrBvK,IAAI,EAAE,CAACgjB,gEAAY,EAAE;QAAE1F,WAAW,EAAE;MAAK,CAAC;IAC9C,CAAC,CAAC;IAAEuH,aAAa,EAAE,CAAC;MAChB9kB,IAAI,EAAEuK,uDAAY;MAClBtK,IAAI,EAAE,CAAC0mB,kBAAkB;IAC7B,CAAC,CAAC;IAAEnM,mBAAmB,EAAE,CAAC;MACtBxa,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAE4K,OAAO,EAAE,CAAC;MACV7K,IAAI,EAAEqK,oDAAS;MACfpK,IAAI,EAAE,CAAC,SAAS;IACpB,CAAC,CAAC;IAAEknB,KAAK,EAAE,CAAC;MACRnnB,IAAI,EAAEqK,oDAAS;MACfpK,IAAI,EAAE,CAAC,OAAO;IAClB,CAAC,CAAC;IAAE+uB,WAAW,EAAE,CAAC;MACdhvB,IAAI,EAAEqK,oDAAS;MACfpK,IAAI,EAAE,CAACwiB,qEAAmB;IAC9B,CAAC,CAAC;IAAE+C,UAAU,EAAE,CAAC;MACbxlB,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAE2b,QAAQ,EAAE,CAAC;MACXrc,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC;QAAEsJ,SAAS,EAAEhJ,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAE2pB,aAAa,EAAE,CAAC;MAChBlqB,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC;QAAEsJ,SAAS,EAAEhJ,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAE0oB,QAAQ,EAAE,CAAC;MACXjpB,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC;QACCsJ,SAAS,EAAGtF,KAAK,IAAMA,KAAK,IAAI,IAAI,GAAG,CAAC,GAAG2e,8DAAe,CAAC3e,KAAK;MACpE,CAAC;IACT,CAAC,CAAC;IAAE+jB,4BAA4B,EAAE,CAAC;MAC/BhoB,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC;QAAEsJ,SAAS,EAAEhJ,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAEmE,WAAW,EAAE,CAAC;MACd1E,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEuM,QAAQ,EAAE,CAAC;MACXjN,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC;QAAEsJ,SAAS,EAAEhJ,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAE2f,QAAQ,EAAE,CAAC;MACXlgB,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC;QAAEsJ,SAAS,EAAEhJ,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAE4pB,sBAAsB,EAAE,CAAC;MACzBnqB,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC;QAAEsJ,SAAS,EAAEhJ,2DAAgBA;MAAC,CAAC;IAC1C,CAAC,CAAC;IAAE+nB,WAAW,EAAE,CAAC;MACdtoB,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEuD,KAAK,EAAE,CAAC;MACRjE,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAE+kB,SAAS,EAAE,CAAC;MACZzlB,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAEwxB,cAAc,EAAE,CAAC;MACjBzxB,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC,CAAC;IAAE4e,iBAAiB,EAAE,CAAC;MACpB7e,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEwqB,yBAAyB,EAAE,CAAC;MAC5BlrB,IAAI,EAAEU,gDAAK;MACXT,IAAI,EAAE,CAAC;QAAEsJ,SAAS,EAAEqZ,0DAAeA;MAAC,CAAC;IACzC,CAAC,CAAC;IAAEuO,cAAc,EAAE,CAAC;MACjBnxB,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAEmM,EAAE,EAAE,CAAC;MACL7M,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAE0pB,UAAU,EAAE,CAAC;MACbpqB,IAAI,EAAEU,gDAAKA;IACf,CAAC,CAAC;IAAE8pB,YAAY,EAAE,CAAC;MACfxqB,IAAI,EAAEM,iDAAMA;IAChB,CAAC,CAAC;IAAEmqB,aAAa,EAAE,CAAC;MAChBzqB,IAAI,EAAEM,iDAAM;MACZL,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAE0qB,aAAa,EAAE,CAAC;MAChB3qB,IAAI,EAAEM,iDAAM;MACZL,IAAI,EAAE,CAAC,QAAQ;IACnB,CAAC,CAAC;IAAE2qB,eAAe,EAAE,CAAC;MAClB5qB,IAAI,EAAEM,iDAAMA;IAChB,CAAC,CAAC;IAAEuqB,WAAW,EAAE,CAAC;MACd7qB,IAAI,EAAEM,iDAAMA;IAChB,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA,MAAMuyB,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACtzB,IAAI,YAAAuzB,yBAAArzB,CAAA;MAAA,YAAAA,CAAA,IAAwFozB,gBAAgB;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACrvB,IAAI,kBAnJ8EzH,+DAAE;MAAAiE,IAAA,EAmJJ6yB,gBAAgB;MAAAnvB,SAAA;MAAAE,UAAA;MAAAsF,QAAA,GAnJdnN,gEAAE,CAmJ6E,CAAC;QAAEwT,OAAO,EAAEoX,kBAAkB;QAAEnX,WAAW,EAAEqjB;MAAiB,CAAC,CAAC;IAAA,EAAiB;EAAE;AACtQ;AACA;EAAA,QAAA7zB,SAAA,oBAAAA,SAAA,KArJoGjD,+DAAE,CAqJX82B,gBAAgB,EAAc,CAAC;IAC9G7yB,IAAI,EAAEK,oDAAS;IACfJ,IAAI,EAAE,CAAC;MACC4D,QAAQ,EAAE,oBAAoB;MAC9B4L,SAAS,EAAE,CAAC;QAAEF,OAAO,EAAEoX,kBAAkB;QAAEnX,WAAW,EAAEqjB;MAAiB,CAAC,CAAC;MAC3EjvB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMmvB,eAAe,CAAC;EAClB;IAAS,IAAI,CAACxzB,IAAI,YAAAyzB,wBAAAvzB,CAAA;MAAA,YAAAA,CAAA,IAAwFszB,eAAe;IAAA,CAAkD;EAAE;EAC7K;IAAS,IAAI,CAACrpB,IAAI,kBAhK8E3N,8DAAE;MAAAiE,IAAA,EAgKS+yB;IAAe,EAU/F;EAAE;EAC7B;IAAS,IAAI,CAACnpB,IAAI,kBA3K8E7N,8DAAE;MAAA0T,SAAA,EA2KqC,CAAC+W,mCAAmC,CAAC;MAAA1c,OAAA,GAAYoB,0DAAY,EAC5LwX,+DAAa,EACbQ,mEAAe,EACf9X,mEAAe,EAAEiY,wEAAmB,EACpC7F,4EAAkB,EAClB0F,mEAAe,EACf9X,mEAAe;IAAA,EAAI;EAAE;AACjC;AACA;EAAA,QAAApM,SAAA,oBAAAA,SAAA,KAnLoGjD,+DAAE,CAmLXg3B,eAAe,EAAc,CAAC;IAC7G/yB,IAAI,EAAEW,mDAAQ;IACdV,IAAI,EAAE,CAAC;MACC6J,OAAO,EAAE,CACLoB,0DAAY,EACZwX,+DAAa,EACbQ,mEAAe,EACf9X,mEAAe,EACf0b,SAAS,EACT+L,gBAAgB,CACnB;MACD9oB,OAAO,EAAE,CACLsZ,wEAAmB,EACnB7F,4EAAkB,EAClBsJ,SAAS,EACT+L,gBAAgB,EAChB3P,mEAAe,EACf9X,mEAAe,CAClB;MACDqE,SAAS,EAAE,CAAC+W,mCAAmC;IACnD,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA", "sources": ["./node_modules/@angular/cdk/fesm2022/observers/private.mjs", "./node_modules/@angular/cdk/fesm2022/text-field.mjs", "./node_modules/@angular/material/fesm2022/form-field.mjs", "./node_modules/@angular/material/fesm2022/input.mjs", "./node_modules/@angular/material/fesm2022/select.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { inject, NgZone, Injectable } from '@angular/core';\nimport { Subject, Observable } from 'rxjs';\nimport { filter, shareReplay, takeUntil } from 'rxjs/operators';\n\n/**\n * Hand<PERSON> that logs \"ResizeObserver loop limit exceeded\" errors.\n * These errors are not shown in the Chrome console, so we log them to ensure developers are aware.\n * @param e The error\n */\nconst loopLimitExceededErrorHandler = (e) => {\n    if (e instanceof ErrorEvent && e.message === 'ResizeObserver loop limit exceeded') {\n        console.error(`${e.message}. This could indicate a performance issue with your app. See https://github.com/WICG/resize-observer/blob/master/explainer.md#error-handling`);\n    }\n};\n/**\n * A shared ResizeObserver to be used for a particular box type (content-box, border-box, or\n * device-pixel-content-box)\n */\nclass SingleBoxSharedResizeObserver {\n    constructor(\n    /** The box type to observe for resizes. */\n    _box) {\n        this._box = _box;\n        /** Stream that emits when the shared observer is destroyed. */\n        this._destroyed = new Subject();\n        /** Stream of all events from the ResizeObserver. */\n        this._resizeSubject = new Subject();\n        /** A map of elements to streams of their resize events. */\n        this._elementObservables = new Map();\n        if (typeof ResizeObserver !== 'undefined') {\n            this._resizeObserver = new ResizeObserver(entries => this._resizeSubject.next(entries));\n        }\n    }\n    /**\n     * Gets a stream of resize events for the given element.\n     * @param target The element to observe.\n     * @return The stream of resize events for the element.\n     */\n    observe(target) {\n        if (!this._elementObservables.has(target)) {\n            this._elementObservables.set(target, new Observable(observer => {\n                const subscription = this._resizeSubject.subscribe(observer);\n                this._resizeObserver?.observe(target, { box: this._box });\n                return () => {\n                    this._resizeObserver?.unobserve(target);\n                    subscription.unsubscribe();\n                    this._elementObservables.delete(target);\n                };\n            }).pipe(filter(entries => entries.some(entry => entry.target === target)), \n            // Share a replay of the last event so that subsequent calls to observe the same element\n            // receive initial sizing info like the first one. Also enable ref counting so the\n            // element will be automatically unobserved when there are no more subscriptions.\n            shareReplay({ bufferSize: 1, refCount: true }), takeUntil(this._destroyed)));\n        }\n        return this._elementObservables.get(target);\n    }\n    /** Destroys this instance. */\n    destroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._resizeSubject.complete();\n        this._elementObservables.clear();\n    }\n}\n/**\n * Allows observing resize events on multiple elements using a shared set of ResizeObserver.\n * Sharing a ResizeObserver instance is recommended for better performance (see\n * https://github.com/WICG/resize-observer/issues/59).\n *\n * Rather than share a single `ResizeObserver`, this class creates one `ResizeObserver` per type\n * of observed box ('content-box', 'border-box', and 'device-pixel-content-box'). This avoids\n * later calls to `observe` with a different box type from influencing the events dispatched to\n * earlier calls.\n */\nclass SharedResizeObserver {\n    constructor() {\n        /** Map of box type to shared resize observer. */\n        this._observers = new Map();\n        /** The Angular zone. */\n        this._ngZone = inject(NgZone);\n        if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            this._ngZone.runOutsideAngular(() => {\n                window.addEventListener('error', loopLimitExceededErrorHandler);\n            });\n        }\n    }\n    ngOnDestroy() {\n        for (const [, observer] of this._observers) {\n            observer.destroy();\n        }\n        this._observers.clear();\n        if (typeof ResizeObserver !== 'undefined' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            window.removeEventListener('error', loopLimitExceededErrorHandler);\n        }\n    }\n    /**\n     * Gets a stream of resize events for the given target element and box type.\n     * @param target The element to observe for resizes.\n     * @param options Options to pass to the `ResizeObserver`\n     * @return The stream of resize events for the element.\n     */\n    observe(target, options) {\n        const box = options?.box || 'content-box';\n        if (!this._observers.has(box)) {\n            this._observers.set(box, new SingleBoxSharedResizeObserver(box));\n        }\n        return this._observers.get(box).observe(target);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: SharedResizeObserver, deps: [], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: SharedResizeObserver, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: SharedResizeObserver, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'root',\n                }]\n        }], ctorParameters: () => [] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { SharedResizeObserver };\n", "import * as i1 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport * as i0 from '@angular/core';\nimport { Injectable, EventEmitter, Directive, Output, booleanAttribute, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { coerceElement, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { EMPTY, Subject, fromEvent } from 'rxjs';\nimport { auditTime, takeUntil } from 'rxjs/operators';\nimport { DOCUMENT } from '@angular/common';\n\n/** Options to pass to the animationstart listener. */\nconst listenerOptions = normalizePassiveListenerOptions({ passive: true });\n/**\n * An injectable service that can be used to monitor the autofill state of an input.\n * Based on the following blog post:\n * https://medium.com/@brunn/detecting-autofilled-fields-in-javascript-aed598d25da7\n */\nclass AutofillMonitor {\n    constructor(_platform, _ngZone) {\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._monitoredElements = new Map();\n    }\n    monitor(elementOrRef) {\n        if (!this._platform.isBrowser) {\n            return EMPTY;\n        }\n        const element = coerceElement(elementOrRef);\n        const info = this._monitoredElements.get(element);\n        if (info) {\n            return info.subject;\n        }\n        const result = new Subject();\n        const cssClass = 'cdk-text-field-autofilled';\n        const listener = ((event) => {\n            // Animation events fire on initial element render, we check for the presence of the autofill\n            // CSS class to make sure this is a real change in state, not just the initial render before\n            // we fire off events.\n            if (event.animationName === 'cdk-text-field-autofill-start' &&\n                !element.classList.contains(cssClass)) {\n                element.classList.add(cssClass);\n                this._ngZone.run(() => result.next({ target: event.target, isAutofilled: true }));\n            }\n            else if (event.animationName === 'cdk-text-field-autofill-end' &&\n                element.classList.contains(cssClass)) {\n                element.classList.remove(cssClass);\n                this._ngZone.run(() => result.next({ target: event.target, isAutofilled: false }));\n            }\n        });\n        this._ngZone.runOutsideAngular(() => {\n            element.addEventListener('animationstart', listener, listenerOptions);\n            element.classList.add('cdk-text-field-autofill-monitored');\n        });\n        this._monitoredElements.set(element, {\n            subject: result,\n            unlisten: () => {\n                element.removeEventListener('animationstart', listener, listenerOptions);\n            },\n        });\n        return result;\n    }\n    stopMonitoring(elementOrRef) {\n        const element = coerceElement(elementOrRef);\n        const info = this._monitoredElements.get(element);\n        if (info) {\n            info.unlisten();\n            info.subject.complete();\n            element.classList.remove('cdk-text-field-autofill-monitored');\n            element.classList.remove('cdk-text-field-autofilled');\n            this._monitoredElements.delete(element);\n        }\n    }\n    ngOnDestroy() {\n        this._monitoredElements.forEach((_info, element) => this.stopMonitoring(element));\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: AutofillMonitor, deps: [{ token: i1.Platform }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: AutofillMonitor, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: AutofillMonitor, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.Platform }, { type: i0.NgZone }] });\n/** A directive that can be used to monitor the autofill state of an input. */\nclass CdkAutofill {\n    constructor(_elementRef, _autofillMonitor) {\n        this._elementRef = _elementRef;\n        this._autofillMonitor = _autofillMonitor;\n        /** Emits when the autofill state of the element changes. */\n        this.cdkAutofill = new EventEmitter();\n    }\n    ngOnInit() {\n        this._autofillMonitor\n            .monitor(this._elementRef)\n            .subscribe(event => this.cdkAutofill.emit(event));\n    }\n    ngOnDestroy() {\n        this._autofillMonitor.stopMonitoring(this._elementRef);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkAutofill, deps: [{ token: i0.ElementRef }, { token: AutofillMonitor }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkAutofill, isStandalone: true, selector: \"[cdkAutofill]\", outputs: { cdkAutofill: \"cdkAutofill\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkAutofill, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[cdkAutofill]',\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: AutofillMonitor }], propDecorators: { cdkAutofill: [{\n                type: Output\n            }] } });\n\n/** Directive to automatically resize a textarea to fit its content. */\nclass CdkTextareaAutosize {\n    /** Minimum amount of rows in the textarea. */\n    get minRows() {\n        return this._minRows;\n    }\n    set minRows(value) {\n        this._minRows = coerceNumberProperty(value);\n        this._setMinHeight();\n    }\n    /** Maximum amount of rows in the textarea. */\n    get maxRows() {\n        return this._maxRows;\n    }\n    set maxRows(value) {\n        this._maxRows = coerceNumberProperty(value);\n        this._setMaxHeight();\n    }\n    /** Whether autosizing is enabled or not */\n    get enabled() {\n        return this._enabled;\n    }\n    set enabled(value) {\n        // Only act if the actual value changed. This specifically helps to not run\n        // resizeToFitContent too early (i.e. before ngAfterViewInit)\n        if (this._enabled !== value) {\n            (this._enabled = value) ? this.resizeToFitContent(true) : this.reset();\n        }\n    }\n    get placeholder() {\n        return this._textareaElement.placeholder;\n    }\n    set placeholder(value) {\n        this._cachedPlaceholderHeight = undefined;\n        if (value) {\n            this._textareaElement.setAttribute('placeholder', value);\n        }\n        else {\n            this._textareaElement.removeAttribute('placeholder');\n        }\n        this._cacheTextareaPlaceholderHeight();\n    }\n    constructor(_elementRef, _platform, _ngZone, \n    /** @breaking-change 11.0.0 make document required */\n    document) {\n        this._elementRef = _elementRef;\n        this._platform = _platform;\n        this._ngZone = _ngZone;\n        this._destroyed = new Subject();\n        this._enabled = true;\n        /**\n         * Value of minRows as of last resize. If the minRows has decreased, the\n         * height of the textarea needs to be recomputed to reflect the new minimum. The maxHeight\n         * does not have the same problem because it does not affect the textarea's scrollHeight.\n         */\n        this._previousMinRows = -1;\n        this._isViewInited = false;\n        /** Handles `focus` and `blur` events. */\n        this._handleFocusEvent = (event) => {\n            this._hasFocus = event.type === 'focus';\n        };\n        this._document = document;\n        this._textareaElement = this._elementRef.nativeElement;\n    }\n    /** Sets the minimum height of the textarea as determined by minRows. */\n    _setMinHeight() {\n        const minHeight = this.minRows && this._cachedLineHeight ? `${this.minRows * this._cachedLineHeight}px` : null;\n        if (minHeight) {\n            this._textareaElement.style.minHeight = minHeight;\n        }\n    }\n    /** Sets the maximum height of the textarea as determined by maxRows. */\n    _setMaxHeight() {\n        const maxHeight = this.maxRows && this._cachedLineHeight ? `${this.maxRows * this._cachedLineHeight}px` : null;\n        if (maxHeight) {\n            this._textareaElement.style.maxHeight = maxHeight;\n        }\n    }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            // Remember the height which we started with in case autosizing is disabled\n            this._initialHeight = this._textareaElement.style.height;\n            this.resizeToFitContent();\n            this._ngZone.runOutsideAngular(() => {\n                const window = this._getWindow();\n                fromEvent(window, 'resize')\n                    .pipe(auditTime(16), takeUntil(this._destroyed))\n                    .subscribe(() => this.resizeToFitContent(true));\n                this._textareaElement.addEventListener('focus', this._handleFocusEvent);\n                this._textareaElement.addEventListener('blur', this._handleFocusEvent);\n            });\n            this._isViewInited = true;\n            this.resizeToFitContent(true);\n        }\n    }\n    ngOnDestroy() {\n        this._textareaElement.removeEventListener('focus', this._handleFocusEvent);\n        this._textareaElement.removeEventListener('blur', this._handleFocusEvent);\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /**\n     * Cache the height of a single-row textarea if it has not already been cached.\n     *\n     * We need to know how large a single \"row\" of a textarea is in order to apply minRows and\n     * maxRows. For the initial version, we will assume that the height of a single line in the\n     * textarea does not ever change.\n     */\n    _cacheTextareaLineHeight() {\n        if (this._cachedLineHeight) {\n            return;\n        }\n        // Use a clone element because we have to override some styles.\n        let textareaClone = this._textareaElement.cloneNode(false);\n        textareaClone.rows = 1;\n        // Use `position: absolute` so that this doesn't cause a browser layout and use\n        // `visibility: hidden` so that nothing is rendered. Clear any other styles that\n        // would affect the height.\n        textareaClone.style.position = 'absolute';\n        textareaClone.style.visibility = 'hidden';\n        textareaClone.style.border = 'none';\n        textareaClone.style.padding = '0';\n        textareaClone.style.height = '';\n        textareaClone.style.minHeight = '';\n        textareaClone.style.maxHeight = '';\n        // In Firefox it happens that textarea elements are always bigger than the specified amount\n        // of rows. This is because Firefox tries to add extra space for the horizontal scrollbar.\n        // As a workaround that removes the extra space for the scrollbar, we can just set overflow\n        // to hidden. This ensures that there is no invalid calculation of the line height.\n        // See Firefox bug report: https://bugzilla.mozilla.org/show_bug.cgi?id=33654\n        textareaClone.style.overflow = 'hidden';\n        this._textareaElement.parentNode.appendChild(textareaClone);\n        this._cachedLineHeight = textareaClone.clientHeight;\n        textareaClone.remove();\n        // Min and max heights have to be re-calculated if the cached line height changes\n        this._setMinHeight();\n        this._setMaxHeight();\n    }\n    _measureScrollHeight() {\n        const element = this._textareaElement;\n        const previousMargin = element.style.marginBottom || '';\n        const isFirefox = this._platform.FIREFOX;\n        const needsMarginFiller = isFirefox && this._hasFocus;\n        const measuringClass = isFirefox\n            ? 'cdk-textarea-autosize-measuring-firefox'\n            : 'cdk-textarea-autosize-measuring';\n        // In some cases the page might move around while we're measuring the `textarea` on Firefox. We\n        // work around it by assigning a temporary margin with the same height as the `textarea` so that\n        // it occupies the same amount of space. See #23233.\n        if (needsMarginFiller) {\n            element.style.marginBottom = `${element.clientHeight}px`;\n        }\n        // Reset the textarea height to auto in order to shrink back to its default size.\n        // Also temporarily force overflow:hidden, so scroll bars do not interfere with calculations.\n        element.classList.add(measuringClass);\n        // The measuring class includes a 2px padding to workaround an issue with Chrome,\n        // so we account for that extra space here by subtracting 4 (2px top + 2px bottom).\n        const scrollHeight = element.scrollHeight - 4;\n        element.classList.remove(measuringClass);\n        if (needsMarginFiller) {\n            element.style.marginBottom = previousMargin;\n        }\n        return scrollHeight;\n    }\n    _cacheTextareaPlaceholderHeight() {\n        if (!this._isViewInited || this._cachedPlaceholderHeight != undefined) {\n            return;\n        }\n        if (!this.placeholder) {\n            this._cachedPlaceholderHeight = 0;\n            return;\n        }\n        const value = this._textareaElement.value;\n        this._textareaElement.value = this._textareaElement.placeholder;\n        this._cachedPlaceholderHeight = this._measureScrollHeight();\n        this._textareaElement.value = value;\n    }\n    ngDoCheck() {\n        if (this._platform.isBrowser) {\n            this.resizeToFitContent();\n        }\n    }\n    /**\n     * Resize the textarea to fit its content.\n     * @param force Whether to force a height recalculation. By default the height will be\n     *    recalculated only if the value changed since the last call.\n     */\n    resizeToFitContent(force = false) {\n        // If autosizing is disabled, just skip everything else\n        if (!this._enabled) {\n            return;\n        }\n        this._cacheTextareaLineHeight();\n        this._cacheTextareaPlaceholderHeight();\n        // If we haven't determined the line-height yet, we know we're still hidden and there's no point\n        // in checking the height of the textarea.\n        if (!this._cachedLineHeight) {\n            return;\n        }\n        const textarea = this._elementRef.nativeElement;\n        const value = textarea.value;\n        // Only resize if the value or minRows have changed since these calculations can be expensive.\n        if (!force && this._minRows === this._previousMinRows && value === this._previousValue) {\n            return;\n        }\n        const scrollHeight = this._measureScrollHeight();\n        const height = Math.max(scrollHeight, this._cachedPlaceholderHeight || 0);\n        // Use the scrollHeight to know how large the textarea *would* be if fit its entire value.\n        textarea.style.height = `${height}px`;\n        this._ngZone.runOutsideAngular(() => {\n            if (typeof requestAnimationFrame !== 'undefined') {\n                requestAnimationFrame(() => this._scrollToCaretPosition(textarea));\n            }\n            else {\n                setTimeout(() => this._scrollToCaretPosition(textarea));\n            }\n        });\n        this._previousValue = value;\n        this._previousMinRows = this._minRows;\n    }\n    /**\n     * Resets the textarea to its original size\n     */\n    reset() {\n        // Do not try to change the textarea, if the initialHeight has not been determined yet\n        // This might potentially remove styles when reset() is called before ngAfterViewInit\n        if (this._initialHeight !== undefined) {\n            this._textareaElement.style.height = this._initialHeight;\n        }\n    }\n    _noopInputHandler() {\n        // no-op handler that ensures we're running change detection on input events.\n    }\n    /** Access injected document if available or fallback to global document reference */\n    _getDocument() {\n        return this._document || document;\n    }\n    /** Use defaultView of injected document if available or fallback to global window reference */\n    _getWindow() {\n        const doc = this._getDocument();\n        return doc.defaultView || window;\n    }\n    /**\n     * Scrolls a textarea to the caret position. On Firefox resizing the textarea will\n     * prevent it from scrolling to the caret position. We need to re-set the selection\n     * in order for it to scroll to the proper position.\n     */\n    _scrollToCaretPosition(textarea) {\n        const { selectionStart, selectionEnd } = textarea;\n        // IE will throw an \"Unspecified error\" if we try to set the selection range after the\n        // element has been removed from the DOM. Assert that the directive hasn't been destroyed\n        // between the time we requested the animation frame and when it was executed.\n        // Also note that we have to assert that the textarea is focused before we set the\n        // selection range. Setting the selection range on a non-focused textarea will cause\n        // it to receive focus on IE and Edge.\n        if (!this._destroyed.isStopped && this._hasFocus) {\n            textarea.setSelectionRange(selectionStart, selectionEnd);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTextareaAutosize, deps: [{ token: i0.ElementRef }, { token: i1.Platform }, { token: i0.NgZone }, { token: DOCUMENT, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: CdkTextareaAutosize, isStandalone: true, selector: \"textarea[cdkTextareaAutosize]\", inputs: { minRows: [\"cdkAutosizeMinRows\", \"minRows\"], maxRows: [\"cdkAutosizeMaxRows\", \"maxRows\"], enabled: [\"cdkTextareaAutosize\", \"enabled\", booleanAttribute], placeholder: \"placeholder\" }, host: { attributes: { \"rows\": \"1\" }, listeners: { \"input\": \"_noopInputHandler()\" }, classAttribute: \"cdk-textarea-autosize\" }, exportAs: [\"cdkTextareaAutosize\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkTextareaAutosize, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'textarea[cdkTextareaAutosize]',\n                    exportAs: 'cdkTextareaAutosize',\n                    host: {\n                        'class': 'cdk-textarea-autosize',\n                        // Textarea elements that have the directive applied should have a single row by default.\n                        // Browsers normally show two rows by default and therefore this limits the minRows binding.\n                        'rows': '1',\n                        '(input)': '_noopInputHandler()',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i1.Platform }, { type: i0.NgZone }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }], propDecorators: { minRows: [{\n                type: Input,\n                args: ['cdkAutosizeMinRows']\n            }], maxRows: [{\n                type: Input,\n                args: ['cdkAutosizeMaxRows']\n            }], enabled: [{\n                type: Input,\n                args: [{ alias: 'cdkTextareaAutosize', transform: booleanAttribute }]\n            }], placeholder: [{\n                type: Input\n            }] } });\n\nclass TextFieldModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: TextFieldModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: TextFieldModule, imports: [CdkAutofill, CdkTextareaAutosize], exports: [CdkAutofill, CdkTextareaAutosize] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: TextFieldModule }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: TextFieldModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [CdkAutofill, CdkTextareaAutosize],\n                    exports: [CdkAutofill, CdkTextareaAutosize],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AutofillMonitor, CdkAutofill, CdkTextareaAutosize, TextFieldModule };\n", "import * as i0 from '@angular/core';\nimport { Directive, InjectionToken, Attribute, Input, inject, NgZone, Component, ChangeDetectionStrategy, ViewEncapsulation, ViewChild, ANIMATION_MODULE_TYPE, Optional, Inject, ContentChild, ContentChildren, NgModule } from '@angular/core';\nimport * as i1 from '@angular/cdk/bidi';\nimport * as i2 from '@angular/cdk/platform';\nimport { Subscription, Subject, merge } from 'rxjs';\nimport { takeUntil } from 'rxjs/operators';\nimport { SharedResizeObserver } from '@angular/cdk/observers/private';\nimport { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { DOCUMENT, NgTemplateOutlet, CommonModule } from '@angular/common';\nimport { ObserversModule } from '@angular/cdk/observers';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** The floating label for a `mat-form-field`. */\nclass MatLabel {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatLabel, isStandalone: true, selector: \"mat-label\", ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-label',\n                    standalone: true,\n                }]\n        }] });\n\nlet nextUniqueId$2 = 0;\n/**\n * Injection token that can be used to reference instances of `MatError`. It serves as\n * alternative token to the actual `MatError` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_ERROR = new InjectionToken('MatError');\n/** Single error message to be shown underneath the form-field. */\nclass MatError {\n    constructor(ariaLive, elementRef) {\n        this.id = `mat-mdc-error-${nextUniqueId$2++}`;\n        // If no aria-live value is set add 'polite' as a default. This is preferred over setting\n        // role='alert' so that screen readers do not interrupt the current task to read this aloud.\n        if (!ariaLive) {\n            elementRef.nativeElement.setAttribute('aria-live', 'polite');\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatError, deps: [{ token: 'aria-live', attribute: true }, { token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatError, isStandalone: true, selector: \"mat-error, [matError]\", inputs: { id: \"id\" }, host: { attributes: { \"aria-atomic\": \"true\" }, properties: { \"id\": \"id\" }, classAttribute: \"mat-mdc-form-field-error mat-mdc-form-field-bottom-align\" }, providers: [{ provide: MAT_ERROR, useExisting: MatError }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatError, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-error, [matError]',\n                    host: {\n                        'class': 'mat-mdc-form-field-error mat-mdc-form-field-bottom-align',\n                        'aria-atomic': 'true',\n                        '[id]': 'id',\n                    },\n                    providers: [{ provide: MAT_ERROR, useExisting: MatError }],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['aria-live']\n                }] }, { type: i0.ElementRef }], propDecorators: { id: [{\n                type: Input\n            }] } });\n\nlet nextUniqueId$1 = 0;\n/** Hint text to be shown underneath the form field control. */\nclass MatHint {\n    constructor() {\n        /** Whether to align the hint label at the start or end of the line. */\n        this.align = 'start';\n        /** Unique ID for the hint. Used for the aria-describedby on the form field control. */\n        this.id = `mat-mdc-hint-${nextUniqueId$1++}`;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatHint, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatHint, isStandalone: true, selector: \"mat-hint\", inputs: { align: \"align\", id: \"id\" }, host: { properties: { \"class.mat-mdc-form-field-hint-end\": \"align === \\\"end\\\"\", \"id\": \"id\", \"attr.align\": \"null\" }, classAttribute: \"mat-mdc-form-field-hint mat-mdc-form-field-bottom-align\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatHint, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-hint',\n                    host: {\n                        'class': 'mat-mdc-form-field-hint mat-mdc-form-field-bottom-align',\n                        '[class.mat-mdc-form-field-hint-end]': 'align === \"end\"',\n                        '[id]': 'id',\n                        // Remove align attribute to prevent it from interfering with layout.\n                        '[attr.align]': 'null',\n                    },\n                    standalone: true,\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of `MatPrefix`. It serves as\n * alternative token to the actual `MatPrefix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_PREFIX = new InjectionToken('MatPrefix');\n/** Prefix to be placed in front of the form field. */\nclass MatPrefix {\n    constructor() {\n        this._isText = false;\n    }\n    set _isTextSelector(value) {\n        this._isText = true;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPrefix, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatPrefix, isStandalone: true, selector: \"[matPrefix], [matIconPrefix], [matTextPrefix]\", inputs: { _isTextSelector: [\"matTextPrefix\", \"_isTextSelector\"] }, providers: [{ provide: MAT_PREFIX, useExisting: MatPrefix }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatPrefix, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matPrefix], [matIconPrefix], [matTextPrefix]',\n                    providers: [{ provide: MAT_PREFIX, useExisting: MatPrefix }],\n                    standalone: true,\n                }]\n        }], propDecorators: { _isTextSelector: [{\n                type: Input,\n                args: ['matTextPrefix']\n            }] } });\n\n/**\n * Injection token that can be used to reference instances of `MatSuffix`. It serves as\n * alternative token to the actual `MatSuffix` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SUFFIX = new InjectionToken('MatSuffix');\n/** Suffix to be placed at the end of the form field. */\nclass MatSuffix {\n    constructor() {\n        this._isText = false;\n    }\n    set _isTextSelector(value) {\n        this._isText = true;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSuffix, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatSuffix, isStandalone: true, selector: \"[matSuffix], [matIconSuffix], [matTextSuffix]\", inputs: { _isTextSelector: [\"matTextSuffix\", \"_isTextSelector\"] }, providers: [{ provide: MAT_SUFFIX, useExisting: MatSuffix }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSuffix, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matSuffix], [matIconSuffix], [matTextSuffix]',\n                    providers: [{ provide: MAT_SUFFIX, useExisting: MatSuffix }],\n                    standalone: true,\n                }]\n        }], propDecorators: { _isTextSelector: [{\n                type: Input,\n                args: ['matTextSuffix']\n            }] } });\n\n/** An injion token for the parent form-field. */\nconst FLOATING_LABEL_PARENT = new InjectionToken('FloatingLabelParent');\n/**\n * Internal directive that maintains a MDC floating label. This directive does not\n * use the `MDCFloatingLabelFoundation` class, as it is not worth the size cost of\n * including it just to measure the label width and toggle some classes.\n *\n * The use of a directive allows us to conditionally render a floating label in the\n * template without having to manually manage instantiation and destruction of the\n * floating label component based on.\n *\n * The component is responsible for setting up the floating label styles, measuring label\n * width for the outline notch, and providing inputs that can be used to toggle the\n * label's floating or required state.\n */\nclass MatFormFieldFloatingLabel {\n    /** Whether the label is floating. */\n    get floating() {\n        return this._floating;\n    }\n    set floating(value) {\n        this._floating = value;\n        if (this.monitorResize) {\n            this._handleResize();\n        }\n    }\n    /** Whether to monitor for resize events on the floating label. */\n    get monitorResize() {\n        return this._monitorResize;\n    }\n    set monitorResize(value) {\n        this._monitorResize = value;\n        if (this._monitorResize) {\n            this._subscribeToResize();\n        }\n        else {\n            this._resizeSubscription.unsubscribe();\n        }\n    }\n    constructor(_elementRef) {\n        this._elementRef = _elementRef;\n        this._floating = false;\n        this._monitorResize = false;\n        /** The shared ResizeObserver. */\n        this._resizeObserver = inject(SharedResizeObserver);\n        /** The Angular zone. */\n        this._ngZone = inject(NgZone);\n        /** The parent form-field. */\n        this._parent = inject(FLOATING_LABEL_PARENT);\n        /** The current resize event subscription. */\n        this._resizeSubscription = new Subscription();\n    }\n    ngOnDestroy() {\n        this._resizeSubscription.unsubscribe();\n    }\n    /** Gets the width of the label. Used for the outline notch. */\n    getWidth() {\n        return estimateScrollWidth(this._elementRef.nativeElement);\n    }\n    /** Gets the HTML element for the floating label. */\n    get element() {\n        return this._elementRef.nativeElement;\n    }\n    /** Handles resize events from the ResizeObserver. */\n    _handleResize() {\n        // In the case where the label grows in size, the following sequence of events occurs:\n        // 1. The label grows by 1px triggering the ResizeObserver\n        // 2. The notch is expanded to accommodate the entire label\n        // 3. The label expands to its full width, triggering the ResizeObserver again\n        //\n        // This is expected, but If we allow this to all happen within the same macro task it causes an\n        // error: `ResizeObserver loop limit exceeded`. Therefore we push the notch resize out until\n        // the next macro task.\n        setTimeout(() => this._parent._handleLabelResized());\n    }\n    /** Subscribes to resize events. */\n    _subscribeToResize() {\n        this._resizeSubscription.unsubscribe();\n        this._ngZone.runOutsideAngular(() => {\n            this._resizeSubscription = this._resizeObserver\n                .observe(this._elementRef.nativeElement, { box: 'border-box' })\n                .subscribe(() => this._handleResize());\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormFieldFloatingLabel, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatFormFieldFloatingLabel, isStandalone: true, selector: \"label[matFormFieldFloatingLabel]\", inputs: { floating: \"floating\", monitorResize: \"monitorResize\" }, host: { properties: { \"class.mdc-floating-label--float-above\": \"floating\" }, classAttribute: \"mdc-floating-label mat-mdc-floating-label\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormFieldFloatingLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'label[matFormFieldFloatingLabel]',\n                    host: {\n                        'class': 'mdc-floating-label mat-mdc-floating-label',\n                        '[class.mdc-floating-label--float-above]': 'floating',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }], propDecorators: { floating: [{\n                type: Input\n            }], monitorResize: [{\n                type: Input\n            }] } });\n/**\n * Estimates the scroll width of an element.\n * via https://github.com/material-components/material-components-web/blob/c0a11ef0d000a098fd0c372be8f12d6a99302855/packages/mdc-dom/ponyfill.ts\n */\nfunction estimateScrollWidth(element) {\n    // Check the offsetParent. If the element inherits display: none from any\n    // parent, the offsetParent property will be null (see\n    // https://developer.mozilla.org/en-US/docs/Web/API/HTMLElement/offsetParent).\n    // This check ensures we only clone the node when necessary.\n    const htmlEl = element;\n    if (htmlEl.offsetParent !== null) {\n        return htmlEl.scrollWidth;\n    }\n    const clone = htmlEl.cloneNode(true);\n    clone.style.setProperty('position', 'absolute');\n    clone.style.setProperty('transform', 'translate(-9999px, -9999px)');\n    document.documentElement.appendChild(clone);\n    const scrollWidth = clone.scrollWidth;\n    clone.remove();\n    return scrollWidth;\n}\n\n/** Class added when the line ripple is active. */\nconst ACTIVATE_CLASS = 'mdc-line-ripple--active';\n/** Class added when the line ripple is being deactivated. */\nconst DEACTIVATING_CLASS = 'mdc-line-ripple--deactivating';\n/**\n * Internal directive that creates an instance of the MDC line-ripple component. Using a\n * directive allows us to conditionally render a line-ripple in the template without having\n * to manually create and destroy the `MDCLineRipple` component whenever the condition changes.\n *\n * The directive sets up the styles for the line-ripple and provides an API for activating\n * and deactivating the line-ripple.\n */\nclass MatFormFieldLineRipple {\n    constructor(_elementRef, ngZone) {\n        this._elementRef = _elementRef;\n        this._handleTransitionEnd = (event) => {\n            const classList = this._elementRef.nativeElement.classList;\n            const isDeactivating = classList.contains(DEACTIVATING_CLASS);\n            if (event.propertyName === 'opacity' && isDeactivating) {\n                classList.remove(ACTIVATE_CLASS, DEACTIVATING_CLASS);\n            }\n        };\n        ngZone.runOutsideAngular(() => {\n            _elementRef.nativeElement.addEventListener('transitionend', this._handleTransitionEnd);\n        });\n    }\n    activate() {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove(DEACTIVATING_CLASS);\n        classList.add(ACTIVATE_CLASS);\n    }\n    deactivate() {\n        this._elementRef.nativeElement.classList.add(DEACTIVATING_CLASS);\n    }\n    ngOnDestroy() {\n        this._elementRef.nativeElement.removeEventListener('transitionend', this._handleTransitionEnd);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormFieldLineRipple, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatFormFieldLineRipple, isStandalone: true, selector: \"div[matFormFieldLineRipple]\", host: { classAttribute: \"mdc-line-ripple\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormFieldLineRipple, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'div[matFormFieldLineRipple]',\n                    host: {\n                        'class': 'mdc-line-ripple',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.NgZone }] });\n\n/**\n * Internal component that creates an instance of the MDC notched-outline component.\n *\n * The component sets up the HTML structure and styles for the notched-outline. It provides\n * inputs to toggle the notch state and width.\n */\nclass MatFormFieldNotchedOutline {\n    constructor(_elementRef, _ngZone) {\n        this._elementRef = _elementRef;\n        this._ngZone = _ngZone;\n        /** Whether the notch should be opened. */\n        this.open = false;\n    }\n    ngAfterViewInit() {\n        const label = this._elementRef.nativeElement.querySelector('.mdc-floating-label');\n        if (label) {\n            this._elementRef.nativeElement.classList.add('mdc-notched-outline--upgraded');\n            if (typeof requestAnimationFrame === 'function') {\n                label.style.transitionDuration = '0s';\n                this._ngZone.runOutsideAngular(() => {\n                    requestAnimationFrame(() => (label.style.transitionDuration = ''));\n                });\n            }\n        }\n        else {\n            this._elementRef.nativeElement.classList.add('mdc-notched-outline--no-label');\n        }\n    }\n    _setNotchWidth(labelWidth) {\n        if (!this.open || !labelWidth) {\n            this._notch.nativeElement.style.width = '';\n        }\n        else {\n            const NOTCH_ELEMENT_PADDING = 8;\n            const NOTCH_ELEMENT_BORDER = 1;\n            this._notch.nativeElement.style.width = `calc(${labelWidth}px * var(--mat-mdc-form-field-floating-label-scale, 0.75) + ${NOTCH_ELEMENT_PADDING + NOTCH_ELEMENT_BORDER}px)`;\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormFieldNotchedOutline, deps: [{ token: i0.ElementRef }, { token: i0.NgZone }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatFormFieldNotchedOutline, isStandalone: true, selector: \"div[matFormFieldNotchedOutline]\", inputs: { open: [\"matFormFieldNotchedOutlineOpen\", \"open\"] }, host: { properties: { \"class.mdc-notched-outline--notched\": \"open\" }, classAttribute: \"mdc-notched-outline\" }, viewQueries: [{ propertyName: \"_notch\", first: true, predicate: [\"notch\"], descendants: true }], ngImport: i0, template: \"<div class=\\\"mdc-notched-outline__leading\\\"></div>\\n<div class=\\\"mdc-notched-outline__notch\\\" #notch>\\n  <ng-content></ng-content>\\n</div>\\n<div class=\\\"mdc-notched-outline__trailing\\\"></div>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormFieldNotchedOutline, decorators: [{\n            type: Component,\n            args: [{ selector: 'div[matFormFieldNotchedOutline]', host: {\n                        'class': 'mdc-notched-outline',\n                        // Besides updating the notch state through the MDC component, we toggle this class through\n                        // a host binding in order to ensure that the notched-outline renders correctly on the server.\n                        '[class.mdc-notched-outline--notched]': 'open',\n                    }, changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, standalone: true, template: \"<div class=\\\"mdc-notched-outline__leading\\\"></div>\\n<div class=\\\"mdc-notched-outline__notch\\\" #notch>\\n  <ng-content></ng-content>\\n</div>\\n<div class=\\\"mdc-notched-outline__trailing\\\"></div>\\n\" }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.NgZone }], propDecorators: { open: [{\n                type: Input,\n                args: ['matFormFieldNotchedOutlineOpen']\n            }], _notch: [{\n                type: ViewChild,\n                args: ['notch']\n            }] } });\n\n/**\n * Animations used by the MatFormField.\n * @docs-private\n */\nconst matFormFieldAnimations = {\n    /** Animation that transitions the form field's error and hint messages. */\n    transitionMessages: trigger('transitionMessages', [\n        // TODO(mmalerba): Use angular animations for label animation as well.\n        state('enter', style({ opacity: 1, transform: 'translateY(0%)' })),\n        transition('void => enter', [\n            style({ opacity: 0, transform: 'translateY(-5px)' }),\n            animate('300ms cubic-bezier(0.55, 0, 0.55, 0.2)'),\n        ]),\n    ]),\n};\n\n/** An interface which allows a control to work inside of a `MatFormField`. */\nclass MatFormFieldControl {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormFieldControl, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatFormFieldControl, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormFieldControl, decorators: [{\n            type: Directive\n        }] });\n\n/** @docs-private */\nfunction getMatFormFieldPlaceholderConflictError() {\n    return Error('Placeholder attribute and child element were both specified.');\n}\n/** @docs-private */\nfunction getMatFormFieldDuplicatedHintError(align) {\n    return Error(`A hint was already declared for 'align=\"${align}\"'.`);\n}\n/** @docs-private */\nfunction getMatFormFieldMissingControlError() {\n    return Error('mat-form-field must contain a MatFormFieldControl.');\n}\n\n/**\n * Injection token that can be used to inject an instances of `MatFormField`. It serves\n * as alternative token to the actual `MatFormField` class which would cause unnecessary\n * retention of the `MatFormField` class and its component metadata.\n */\nconst MAT_FORM_FIELD = new InjectionToken('MatFormField');\n/**\n * Injection token that can be used to configure the\n * default options for all form field within an app.\n */\nconst MAT_FORM_FIELD_DEFAULT_OPTIONS = new InjectionToken('MAT_FORM_FIELD_DEFAULT_OPTIONS');\nlet nextUniqueId = 0;\n/** Default appearance used by the form field. */\nconst DEFAULT_APPEARANCE = 'fill';\n/**\n * Whether the label for form fields should by default float `always`,\n * `never`, or `auto`.\n */\nconst DEFAULT_FLOAT_LABEL = 'auto';\n/** Default way that the subscript element height is set. */\nconst DEFAULT_SUBSCRIPT_SIZING = 'fixed';\n/**\n * Default transform for docked floating labels in a MDC text-field. This value has been\n * extracted from the MDC text-field styles because we programmatically modify the docked\n * label transform, but do not want to accidentally discard the default label transform.\n */\nconst FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM = `translateY(-50%)`;\n/** Container for form controls that applies Material Design styling and behavior. */\nclass MatFormField {\n    /** Whether the required marker should be hidden. */\n    get hideRequiredMarker() {\n        return this._hideRequiredMarker;\n    }\n    set hideRequiredMarker(value) {\n        this._hideRequiredMarker = coerceBooleanProperty(value);\n    }\n    /** Whether the label should always float or float as the user types. */\n    get floatLabel() {\n        return this._floatLabel || this._defaults?.floatLabel || DEFAULT_FLOAT_LABEL;\n    }\n    set floatLabel(value) {\n        if (value !== this._floatLabel) {\n            this._floatLabel = value;\n            // For backwards compatibility. Custom form field controls or directives might set\n            // the \"floatLabel\" input and expect the form field view to be updated automatically.\n            // e.g. autocomplete trigger. Ideally we'd get rid of this and the consumers would just\n            // emit the \"stateChanges\" observable. TODO(devversion): consider removing.\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    /** The form field appearance style. */\n    get appearance() {\n        return this._appearance;\n    }\n    set appearance(value) {\n        const oldValue = this._appearance;\n        const newAppearance = value || this._defaults?.appearance || DEFAULT_APPEARANCE;\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            if (newAppearance !== 'fill' && newAppearance !== 'outline') {\n                throw new Error(`MatFormField: Invalid appearance \"${newAppearance}\", valid values are \"fill\" or \"outline\".`);\n            }\n        }\n        this._appearance = newAppearance;\n        if (this._appearance === 'outline' && this._appearance !== oldValue) {\n            // If the appearance has been switched to `outline`, the label offset needs to be updated.\n            // The update can happen once the view has been re-checked, but not immediately because\n            // the view has not been updated and the notched-outline floating label is not present.\n            this._needsOutlineLabelOffsetUpdateOnStable = true;\n        }\n    }\n    /**\n     * Whether the form field should reserve space for one line of hint/error text (default)\n     * or to have the spacing grow from 0px as needed based on the size of the hint/error content.\n     * Note that when using dynamic sizing, layout shifts will occur when hint/error text changes.\n     */\n    get subscriptSizing() {\n        return this._subscriptSizing || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n    }\n    set subscriptSizing(value) {\n        this._subscriptSizing = value || this._defaults?.subscriptSizing || DEFAULT_SUBSCRIPT_SIZING;\n    }\n    /** Text for the form field hint. */\n    get hintLabel() {\n        return this._hintLabel;\n    }\n    set hintLabel(value) {\n        this._hintLabel = value;\n        this._processHints();\n    }\n    /** Gets the current form field control */\n    get _control() {\n        return this._explicitFormFieldControl || this._formFieldControl;\n    }\n    set _control(value) {\n        this._explicitFormFieldControl = value;\n    }\n    constructor(_elementRef, _changeDetectorRef, _ngZone, _dir, _platform, _defaults, _animationMode, \n    /**\n     * @deprecated not needed, to be removed.\n     * @breaking-change 17.0.0 remove this param\n     */\n    _unusedDocument) {\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._ngZone = _ngZone;\n        this._dir = _dir;\n        this._platform = _platform;\n        this._defaults = _defaults;\n        this._animationMode = _animationMode;\n        this._hideRequiredMarker = false;\n        /** The color palette for the form field. */\n        this.color = 'primary';\n        this._appearance = DEFAULT_APPEARANCE;\n        this._subscriptSizing = null;\n        this._hintLabel = '';\n        this._hasIconPrefix = false;\n        this._hasTextPrefix = false;\n        this._hasIconSuffix = false;\n        this._hasTextSuffix = false;\n        // Unique id for the internal form field label.\n        this._labelId = `mat-mdc-form-field-label-${nextUniqueId++}`;\n        // Unique id for the hint label.\n        this._hintLabelId = `mat-mdc-hint-${nextUniqueId++}`;\n        /** State of the mat-hint and mat-error animations. */\n        this._subscriptAnimationState = '';\n        this._destroyed = new Subject();\n        this._isFocused = null;\n        this._needsOutlineLabelOffsetUpdateOnStable = false;\n        if (_defaults) {\n            if (_defaults.appearance) {\n                this.appearance = _defaults.appearance;\n            }\n            this._hideRequiredMarker = Boolean(_defaults?.hideRequiredMarker);\n            if (_defaults.color) {\n                this.color = _defaults.color;\n            }\n        }\n    }\n    ngAfterViewInit() {\n        // Initial focus state sync. This happens rarely, but we want to account for\n        // it in case the form field control has \"focused\" set to true on init.\n        this._updateFocusState();\n        // Enable animations now. This ensures we don't animate on initial render.\n        this._subscriptAnimationState = 'enter';\n        // Because the above changes a value used in the template after it was checked, we need\n        // to trigger CD or the change might not be reflected if there is no other CD scheduled.\n        this._changeDetectorRef.detectChanges();\n    }\n    ngAfterContentInit() {\n        this._assertFormFieldControl();\n        this._initializeControl();\n        this._initializeSubscript();\n        this._initializePrefixAndSuffix();\n        this._initializeOutlineLabelOffsetSubscriptions();\n    }\n    ngAfterContentChecked() {\n        this._assertFormFieldControl();\n    }\n    ngOnDestroy() {\n        this._destroyed.next();\n        this._destroyed.complete();\n    }\n    /**\n     * Gets the id of the label element. If no label is present, returns `null`.\n     */\n    getLabelId() {\n        return this._hasFloatingLabel() ? this._labelId : null;\n    }\n    /**\n     * Gets an ElementRef for the element that a overlay attached to the form field\n     * should be positioned relative to.\n     */\n    getConnectedOverlayOrigin() {\n        return this._textField || this._elementRef;\n    }\n    /** Animates the placeholder up and locks it in position. */\n    _animateAndLockLabel() {\n        // This is for backwards compatibility only. Consumers of the form field might use\n        // this method. e.g. the autocomplete trigger. This method has been added to the non-MDC\n        // form field because setting \"floatLabel\" to \"always\" caused the label to float without\n        // animation. This is different in MDC where the label always animates, so this method\n        // is no longer necessary. There doesn't seem any benefit in adding logic to allow changing\n        // the floating label state without animations. The non-MDC implementation was inconsistent\n        // because it always animates if \"floatLabel\" is set away from \"always\".\n        // TODO(devversion): consider removing this method when releasing the MDC form field.\n        if (this._hasFloatingLabel()) {\n            this.floatLabel = 'always';\n        }\n    }\n    /** Initializes the registered form field control. */\n    _initializeControl() {\n        const control = this._control;\n        if (control.controlType) {\n            this._elementRef.nativeElement.classList.add(`mat-mdc-form-field-type-${control.controlType}`);\n        }\n        // Subscribe to changes in the child control state in order to update the form field UI.\n        control.stateChanges.subscribe(() => {\n            this._updateFocusState();\n            this._syncDescribedByIds();\n            this._changeDetectorRef.markForCheck();\n        });\n        // Run change detection if the value changes.\n        if (control.ngControl && control.ngControl.valueChanges) {\n            control.ngControl.valueChanges\n                .pipe(takeUntil(this._destroyed))\n                .subscribe(() => this._changeDetectorRef.markForCheck());\n        }\n    }\n    _checkPrefixAndSuffixTypes() {\n        this._hasIconPrefix = !!this._prefixChildren.find(p => !p._isText);\n        this._hasTextPrefix = !!this._prefixChildren.find(p => p._isText);\n        this._hasIconSuffix = !!this._suffixChildren.find(s => !s._isText);\n        this._hasTextSuffix = !!this._suffixChildren.find(s => s._isText);\n    }\n    /** Initializes the prefix and suffix containers. */\n    _initializePrefixAndSuffix() {\n        this._checkPrefixAndSuffixTypes();\n        // Mark the form field as dirty whenever the prefix or suffix children change. This\n        // is necessary because we conditionally display the prefix/suffix containers based\n        // on whether there is projected content.\n        merge(this._prefixChildren.changes, this._suffixChildren.changes).subscribe(() => {\n            this._checkPrefixAndSuffixTypes();\n            this._changeDetectorRef.markForCheck();\n        });\n    }\n    /**\n     * Initializes the subscript by validating hints and synchronizing \"aria-describedby\" ids\n     * with the custom form field control. Also subscribes to hint and error changes in order\n     * to be able to validate and synchronize ids on change.\n     */\n    _initializeSubscript() {\n        // Re-validate when the number of hints changes.\n        this._hintChildren.changes.subscribe(() => {\n            this._processHints();\n            this._changeDetectorRef.markForCheck();\n        });\n        // Update the aria-described by when the number of errors changes.\n        this._errorChildren.changes.subscribe(() => {\n            this._syncDescribedByIds();\n            this._changeDetectorRef.markForCheck();\n        });\n        // Initial mat-hint validation and subscript describedByIds sync.\n        this._validateHints();\n        this._syncDescribedByIds();\n    }\n    /** Throws an error if the form field's control is missing. */\n    _assertFormFieldControl() {\n        if (!this._control && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatFormFieldMissingControlError();\n        }\n    }\n    _updateFocusState() {\n        // Usually the MDC foundation would call \"activateFocus\" and \"deactivateFocus\" whenever\n        // certain DOM events are emitted. This is not possible in our implementation of the\n        // form field because we support abstract form field controls which are not necessarily\n        // of type input, nor do we have a reference to a native form field control element. Instead\n        // we handle the focus by checking if the abstract form field control focused state changes.\n        if (this._control.focused && !this._isFocused) {\n            this._isFocused = true;\n            this._lineRipple?.activate();\n        }\n        else if (!this._control.focused && (this._isFocused || this._isFocused === null)) {\n            this._isFocused = false;\n            this._lineRipple?.deactivate();\n        }\n        this._textField?.nativeElement.classList.toggle('mdc-text-field--focused', this._control.focused);\n    }\n    /**\n     * The floating label in the docked state needs to account for prefixes. The horizontal offset\n     * is calculated whenever the appearance changes to `outline`, the prefixes change, or when the\n     * form field is added to the DOM. This method sets up all subscriptions which are needed to\n     * trigger the label offset update. In general, we want to avoid performing measurements often,\n     * so we rely on the `NgZone` as indicator when the offset should be recalculated, instead of\n     * checking every change detection cycle.\n     */\n    _initializeOutlineLabelOffsetSubscriptions() {\n        // Whenever the prefix changes, schedule an update of the label offset.\n        this._prefixChildren.changes.subscribe(() => (this._needsOutlineLabelOffsetUpdateOnStable = true));\n        // Note that we have to run outside of the `NgZone` explicitly, in order to avoid\n        // throwing users into an infinite loop if `zone-patch-rxjs` is included.\n        this._ngZone.runOutsideAngular(() => {\n            this._ngZone.onStable.pipe(takeUntil(this._destroyed)).subscribe(() => {\n                if (this._needsOutlineLabelOffsetUpdateOnStable) {\n                    this._needsOutlineLabelOffsetUpdateOnStable = false;\n                    this._updateOutlineLabelOffset();\n                }\n            });\n        });\n        this._dir.change\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => (this._needsOutlineLabelOffsetUpdateOnStable = true));\n    }\n    /** Whether the floating label should always float or not. */\n    _shouldAlwaysFloat() {\n        return this.floatLabel === 'always';\n    }\n    _hasOutline() {\n        return this.appearance === 'outline';\n    }\n    /**\n     * Whether the label should display in the infix. Labels in the outline appearance are\n     * displayed as part of the notched-outline and are horizontally offset to account for\n     * form field prefix content. This won't work in server side rendering since we cannot\n     * measure the width of the prefix container. To make the docked label appear as if the\n     * right offset has been calculated, we forcibly render the label inside the infix. Since\n     * the label is part of the infix, the label cannot overflow the prefix content.\n     */\n    _forceDisplayInfixLabel() {\n        return !this._platform.isBrowser && this._prefixChildren.length && !this._shouldLabelFloat();\n    }\n    _hasFloatingLabel() {\n        return !!this._labelChildNonStatic || !!this._labelChildStatic;\n    }\n    _shouldLabelFloat() {\n        return this._control.shouldLabelFloat || this._shouldAlwaysFloat();\n    }\n    /**\n     * Determines whether a class from the AbstractControlDirective\n     * should be forwarded to the host element.\n     */\n    _shouldForward(prop) {\n        const control = this._control ? this._control.ngControl : null;\n        return control && control[prop];\n    }\n    /** Determines whether to display hints or errors. */\n    _getDisplayedMessages() {\n        return this._errorChildren && this._errorChildren.length > 0 && this._control.errorState\n            ? 'error'\n            : 'hint';\n    }\n    /** Handle label resize events. */\n    _handleLabelResized() {\n        this._refreshOutlineNotchWidth();\n    }\n    /** Refreshes the width of the outline-notch, if present. */\n    _refreshOutlineNotchWidth() {\n        if (!this._hasOutline() || !this._floatingLabel || !this._shouldLabelFloat()) {\n            this._notchedOutline?._setNotchWidth(0);\n        }\n        else {\n            this._notchedOutline?._setNotchWidth(this._floatingLabel.getWidth());\n        }\n    }\n    /** Does any extra processing that is required when handling the hints. */\n    _processHints() {\n        this._validateHints();\n        this._syncDescribedByIds();\n    }\n    /**\n     * Ensure that there is a maximum of one of each \"mat-hint\" alignment specified. The hint\n     * label specified set through the input is being considered as \"start\" aligned.\n     *\n     * This method is a noop if Angular runs in production mode.\n     */\n    _validateHints() {\n        if (this._hintChildren && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            let startHint;\n            let endHint;\n            this._hintChildren.forEach((hint) => {\n                if (hint.align === 'start') {\n                    if (startHint || this.hintLabel) {\n                        throw getMatFormFieldDuplicatedHintError('start');\n                    }\n                    startHint = hint;\n                }\n                else if (hint.align === 'end') {\n                    if (endHint) {\n                        throw getMatFormFieldDuplicatedHintError('end');\n                    }\n                    endHint = hint;\n                }\n            });\n        }\n    }\n    /**\n     * Sets the list of element IDs that describe the child control. This allows the control to update\n     * its `aria-describedby` attribute accordingly.\n     */\n    _syncDescribedByIds() {\n        if (this._control) {\n            let ids = [];\n            // TODO(wagnermaciel): Remove the type check when we find the root cause of this bug.\n            if (this._control.userAriaDescribedBy &&\n                typeof this._control.userAriaDescribedBy === 'string') {\n                ids.push(...this._control.userAriaDescribedBy.split(' '));\n            }\n            if (this._getDisplayedMessages() === 'hint') {\n                const startHint = this._hintChildren\n                    ? this._hintChildren.find(hint => hint.align === 'start')\n                    : null;\n                const endHint = this._hintChildren\n                    ? this._hintChildren.find(hint => hint.align === 'end')\n                    : null;\n                if (startHint) {\n                    ids.push(startHint.id);\n                }\n                else if (this._hintLabel) {\n                    ids.push(this._hintLabelId);\n                }\n                if (endHint) {\n                    ids.push(endHint.id);\n                }\n            }\n            else if (this._errorChildren) {\n                ids.push(...this._errorChildren.map(error => error.id));\n            }\n            this._control.setDescribedByIds(ids);\n        }\n    }\n    /**\n     * Updates the horizontal offset of the label in the outline appearance. In the outline\n     * appearance, the notched-outline and label are not relative to the infix container because\n     * the outline intends to surround prefixes, suffixes and the infix. This means that the\n     * floating label by default overlaps prefixes in the docked state. To avoid this, we need to\n     * horizontally offset the label by the width of the prefix container. The MDC text-field does\n     * not need to do this because they use a fixed width for prefixes. Hence, they can simply\n     * incorporate the horizontal offset into their default text-field styles.\n     */\n    _updateOutlineLabelOffset() {\n        if (!this._platform.isBrowser || !this._hasOutline() || !this._floatingLabel) {\n            return;\n        }\n        const floatingLabel = this._floatingLabel.element;\n        // If no prefix is displayed, reset the outline label offset from potential\n        // previous label offset updates.\n        if (!(this._iconPrefixContainer || this._textPrefixContainer)) {\n            floatingLabel.style.transform = '';\n            return;\n        }\n        // If the form field is not attached to the DOM yet (e.g. in a tab), we defer\n        // the label offset update until the zone stabilizes.\n        if (!this._isAttachedToDom()) {\n            this._needsOutlineLabelOffsetUpdateOnStable = true;\n            return;\n        }\n        const iconPrefixContainer = this._iconPrefixContainer?.nativeElement;\n        const textPrefixContainer = this._textPrefixContainer?.nativeElement;\n        const iconPrefixContainerWidth = iconPrefixContainer?.getBoundingClientRect().width ?? 0;\n        const textPrefixContainerWidth = textPrefixContainer?.getBoundingClientRect().width ?? 0;\n        // If the directionality is RTL, the x-axis transform needs to be inverted. This\n        // is because `transformX` does not change based on the page directionality.\n        const negate = this._dir.value === 'rtl' ? '-1' : '1';\n        const prefixWidth = `${iconPrefixContainerWidth + textPrefixContainerWidth}px`;\n        const labelOffset = `var(--mat-mdc-form-field-label-offset-x, 0px)`;\n        const labelHorizontalOffset = `calc(${negate} * (${prefixWidth} + ${labelOffset}))`;\n        // Update the translateX of the floating label to account for the prefix container,\n        // but allow the CSS to override this setting via a CSS variable when the label is\n        // floating.\n        floatingLabel.style.transform = `var(\n        --mat-mdc-form-field-label-transform,\n        ${FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM} translateX(${labelHorizontalOffset})\n    )`;\n    }\n    /** Checks whether the form field is attached to the DOM. */\n    _isAttachedToDom() {\n        const element = this._elementRef.nativeElement;\n        if (element.getRootNode) {\n            const rootNode = element.getRootNode();\n            // If the element is inside the DOM the root node will be either the document\n            // or the closest shadow root, otherwise it'll be the element itself.\n            return rootNode && rootNode !== element;\n        }\n        // Otherwise fall back to checking if it's in the document. This doesn't account for\n        // shadow DOM, however browser that support shadow DOM should support `getRootNode` as well.\n        return document.documentElement.contains(element);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormField, deps: [{ token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: i1.Directionality }, { token: i2.Platform }, { token: MAT_FORM_FIELD_DEFAULT_OPTIONS, optional: true }, { token: ANIMATION_MODULE_TYPE, optional: true }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatFormField, isStandalone: true, selector: \"mat-form-field\", inputs: { hideRequiredMarker: \"hideRequiredMarker\", color: \"color\", floatLabel: \"floatLabel\", appearance: \"appearance\", subscriptSizing: \"subscriptSizing\", hintLabel: \"hintLabel\" }, host: { properties: { \"class.mat-mdc-form-field-label-always-float\": \"_shouldAlwaysFloat()\", \"class.mat-mdc-form-field-has-icon-prefix\": \"_hasIconPrefix\", \"class.mat-mdc-form-field-has-icon-suffix\": \"_hasIconSuffix\", \"class.mat-form-field-invalid\": \"_control.errorState\", \"class.mat-form-field-disabled\": \"_control.disabled\", \"class.mat-form-field-autofilled\": \"_control.autofilled\", \"class.mat-form-field-no-animations\": \"_animationMode === \\\"NoopAnimations\\\"\", \"class.mat-form-field-appearance-fill\": \"appearance == \\\"fill\\\"\", \"class.mat-form-field-appearance-outline\": \"appearance == \\\"outline\\\"\", \"class.mat-form-field-hide-placeholder\": \"_hasFloatingLabel() && !_shouldLabelFloat()\", \"class.mat-focused\": \"_control.focused\", \"class.mat-primary\": \"color !== \\\"accent\\\" && color !== \\\"warn\\\"\", \"class.mat-accent\": \"color === \\\"accent\\\"\", \"class.mat-warn\": \"color === \\\"warn\\\"\", \"class.ng-untouched\": \"_shouldForward(\\\"untouched\\\")\", \"class.ng-touched\": \"_shouldForward(\\\"touched\\\")\", \"class.ng-pristine\": \"_shouldForward(\\\"pristine\\\")\", \"class.ng-dirty\": \"_shouldForward(\\\"dirty\\\")\", \"class.ng-valid\": \"_shouldForward(\\\"valid\\\")\", \"class.ng-invalid\": \"_shouldForward(\\\"invalid\\\")\", \"class.ng-pending\": \"_shouldForward(\\\"pending\\\")\" }, classAttribute: \"mat-mdc-form-field\" }, providers: [\n            { provide: MAT_FORM_FIELD, useExisting: MatFormField },\n            { provide: FLOATING_LABEL_PARENT, useExisting: MatFormField },\n        ], queries: [{ propertyName: \"_labelChildNonStatic\", first: true, predicate: MatLabel, descendants: true }, { propertyName: \"_labelChildStatic\", first: true, predicate: MatLabel, descendants: true, static: true }, { propertyName: \"_formFieldControl\", first: true, predicate: MatFormFieldControl, descendants: true }, { propertyName: \"_prefixChildren\", predicate: MAT_PREFIX, descendants: true }, { propertyName: \"_suffixChildren\", predicate: MAT_SUFFIX, descendants: true }, { propertyName: \"_errorChildren\", predicate: MAT_ERROR, descendants: true }, { propertyName: \"_hintChildren\", predicate: MatHint, descendants: true }], viewQueries: [{ propertyName: \"_textField\", first: true, predicate: [\"textField\"], descendants: true }, { propertyName: \"_iconPrefixContainer\", first: true, predicate: [\"iconPrefixContainer\"], descendants: true }, { propertyName: \"_textPrefixContainer\", first: true, predicate: [\"textPrefixContainer\"], descendants: true }, { propertyName: \"_floatingLabel\", first: true, predicate: MatFormFieldFloatingLabel, descendants: true }, { propertyName: \"_notchedOutline\", first: true, predicate: MatFormFieldNotchedOutline, descendants: true }, { propertyName: \"_lineRipple\", first: true, predicate: MatFormFieldLineRipple, descendants: true }], exportAs: [\"matFormField\"], ngImport: i0, template: \"<ng-template #labelTemplate>\\n  <!--\\n    MDC recommends that the text-field is a `<label>` element. This rather complicates the\\n    setup because it would require every form-field control to explicitly set `aria-labelledby`.\\n    This is because the `<label>` itself contains more than the actual label (e.g. prefix, suffix\\n    or other projected content), and screen readers could potentially read out undesired content.\\n    Excluding elements from being printed out requires them to be marked with `aria-hidden`, or\\n    the form control is set to a scoped element for the label (using `aria-labelledby`). Both of\\n    these options seem to complicate the setup because we know exactly what content is rendered\\n    as part of the label, and we don't want to spend resources on walking through projected content\\n    to set `aria-hidden`. Nor do we want to set `aria-labelledby` on every form control if we could\\n    simply link the label to the control using the label `for` attribute.\\n  -->\\n  @if (_hasFloatingLabel()) {\\n    <label matFormFieldFloatingLabel\\n           [floating]=\\\"_shouldLabelFloat()\\\"\\n           [monitorResize]=\\\"_hasOutline()\\\"\\n           [id]=\\\"_labelId\\\"\\n           [attr.for]=\\\"_control.disableAutomaticLabeling ? null : _control.id\\\">\\n      <ng-content select=\\\"mat-label\\\"></ng-content>\\n      <!--\\n        We set the required marker as a separate element, in order to make it easier to target if\\n        apps want to override it and to be able to set `aria-hidden` so that screen readers don't\\n        pick it up.\\n       -->\\n       @if (!hideRequiredMarker && _control.required) {\\n         <span\\n           aria-hidden=\\\"true\\\"\\n           class=\\\"mat-mdc-form-field-required-marker mdc-floating-label--required\\\"></span>\\n       }\\n    </label>\\n  }\\n</ng-template>\\n\\n<div class=\\\"mat-mdc-text-field-wrapper mdc-text-field\\\" #textField\\n     [class.mdc-text-field--filled]=\\\"!_hasOutline()\\\"\\n     [class.mdc-text-field--outlined]=\\\"_hasOutline()\\\"\\n     [class.mdc-text-field--no-label]=\\\"!_hasFloatingLabel()\\\"\\n     [class.mdc-text-field--disabled]=\\\"_control.disabled\\\"\\n     [class.mdc-text-field--invalid]=\\\"_control.errorState\\\"\\n     (click)=\\\"_control.onContainerClick($event)\\\">\\n  @if (!_hasOutline() && !_control.disabled) {\\n    <div class=\\\"mat-mdc-form-field-focus-overlay\\\"></div>\\n  }\\n  <div class=\\\"mat-mdc-form-field-flex\\\">\\n    @if (_hasOutline()) {\\n      <div matFormFieldNotchedOutline [matFormFieldNotchedOutlineOpen]=\\\"_shouldLabelFloat()\\\">\\n        @if (!_forceDisplayInfixLabel()) {\\n          <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n        }\\n      </div>\\n    }\\n\\n    @if (_hasIconPrefix) {\\n      <div class=\\\"mat-mdc-form-field-icon-prefix\\\" #iconPrefixContainer>\\n        <ng-content select=\\\"[matPrefix], [matIconPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasTextPrefix) {\\n      <div class=\\\"mat-mdc-form-field-text-prefix\\\" #textPrefixContainer>\\n        <ng-content select=\\\"[matTextPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-form-field-infix\\\">\\n      @if (!_hasOutline() || _forceDisplayInfixLabel()) {\\n        <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n      }\\n\\n      <ng-content></ng-content>\\n    </div>\\n\\n    @if (_hasTextSuffix) {\\n      <div class=\\\"mat-mdc-form-field-text-suffix\\\">\\n        <ng-content select=\\\"[matTextSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasIconSuffix) {\\n      <div class=\\\"mat-mdc-form-field-icon-suffix\\\">\\n        <ng-content select=\\\"[matSuffix], [matIconSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n  </div>\\n\\n  @if (!_hasOutline()) {\\n    <div matFormFieldLineRipple></div>\\n  }\\n</div>\\n\\n<div class=\\\"mat-mdc-form-field-subscript-wrapper mat-mdc-form-field-bottom-align\\\"\\n     [class.mat-mdc-form-field-subscript-dynamic-size]=\\\"subscriptSizing === 'dynamic'\\\">\\n\\n  @switch (_getDisplayedMessages()) {\\n    @case ('error') {\\n      <div class=\\\"mat-mdc-form-field-error-wrapper\\\"\\n           [@transitionMessages]=\\\"_subscriptAnimationState\\\">\\n        <ng-content select=\\\"mat-error, [matError]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @case ('hint') {\\n      <div class=\\\"mat-mdc-form-field-hint-wrapper\\\" [@transitionMessages]=\\\"_subscriptAnimationState\\\">\\n        @if (hintLabel) {\\n          <mat-hint [id]=\\\"_hintLabelId\\\">{{hintLabel}}</mat-hint>\\n        }\\n        <ng-content select=\\\"mat-hint:not([align='end'])\\\"></ng-content>\\n        <div class=\\\"mat-mdc-form-field-hint-spacer\\\"></div>\\n        <ng-content select=\\\"mat-hint[align='end']\\\"></ng-content>\\n      </div>\\n    }\\n  }\\n</div>\\n\", styles: [\".mdc-text-field{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:0;border-bottom-left-radius:0;display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-text-field__input{height:28px;width:100%;min-width:0;border:none;border-radius:0;background:none;appearance:none;padding:0}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}@media all{.mdc-text-field__input::placeholder{opacity:0}}@media all{.mdc-text-field__input:-ms-input-placeholder{opacity:0}}@media all{.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}}@media all{.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}}.mdc-text-field__affix{height:28px;opacity:0;white-space:nowrap}.mdc-text-field--label-floating .mdc-text-field__affix,.mdc-text-field--no-label .mdc-text-field__affix{opacity:1}@supports(-webkit-hyphens: none){.mdc-text-field--outlined .mdc-text-field__affix{align-items:center;align-self:center;display:inline-flex;height:100%}}.mdc-text-field__affix--prefix{padding-left:0;padding-right:2px}[dir=rtl] .mdc-text-field__affix--prefix,.mdc-text-field__affix--prefix[dir=rtl]{padding-left:2px;padding-right:0}.mdc-text-field--end-aligned .mdc-text-field__affix--prefix{padding-left:0;padding-right:12px}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__affix--prefix,.mdc-text-field--end-aligned .mdc-text-field__affix--prefix[dir=rtl]{padding-left:12px;padding-right:0}.mdc-text-field__affix--suffix{padding-left:12px;padding-right:0}[dir=rtl] .mdc-text-field__affix--suffix,.mdc-text-field__affix--suffix[dir=rtl]{padding-left:0;padding-right:12px}.mdc-text-field--end-aligned .mdc-text-field__affix--suffix{padding-left:2px;padding-right:0}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__affix--suffix,.mdc-text-field--end-aligned .mdc-text-field__affix--suffix[dir=rtl]{padding-left:0;padding-right:2px}.mdc-text-field--filled{height:56px}.mdc-text-field--filled::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}.mdc-text-field--filled .mdc-floating-label{left:16px;right:initial}[dir=rtl] .mdc-text-field--filled .mdc-floating-label,.mdc-text-field--filled .mdc-floating-label[dir=rtl]{left:initial;right:16px}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled.mdc-text-field--no-label::before{display:none}@supports(-webkit-hyphens: none){.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__affix{align-items:center;align-self:center;display:inline-flex;height:100%}}.mdc-text-field--outlined{height:56px;overflow:visible}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--outlined .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-shape-small, 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px,var(--mdc-shape-small, 4px))*2)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}@supports(top: max(0%)){.mdc-text-field--outlined{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined{padding-right:max(16px,var(--mdc-shape-small, 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-right:max(16px,var(--mdc-shape-small, 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-left:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-right:max(16px,var(--mdc-shape-small, 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-right:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:max(16px,var(--mdc-shape-small, 4px))}}.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-right:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-right:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:initial}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label,.mdc-text-field--outlined .mdc-floating-label[dir=rtl]{left:initial;right:4px}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mdc-text-field--textarea{flex-direction:column;align-items:center;width:auto;height:auto;padding:0}.mdc-text-field--textarea .mdc-floating-label{top:19px}.mdc-text-field--textarea .mdc-floating-label:not(.mdc-floating-label--float-above){transform:none}.mdc-text-field--textarea .mdc-text-field__input{flex-grow:1;height:auto;min-height:1.5rem;overflow-x:hidden;overflow-y:auto;box-sizing:border-box;resize:none;padding:0 16px}.mdc-text-field--textarea.mdc-text-field--filled::before{display:none}.mdc-text-field--textarea.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-10.25px) scale(0.75)}.mdc-text-field--textarea.mdc-text-field--filled .mdc-text-field__input{margin-top:23px;margin-bottom:9px}.mdc-text-field--textarea.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{margin-top:16px;margin-bottom:16px}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:0}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-27.25px) scale(1)}.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-24.75px) scale(0.75)}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-text-field__input{margin-top:16px;margin-bottom:16px}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label{top:18px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field__input{margin-bottom:2px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter{align-self:flex-end;padding:0 16px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter::after{display:inline-block;width:0;height:16px;content:\\\"\\\";vertical-align:-16px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter::before{display:none}.mdc-text-field__resizer{align-self:stretch;display:inline-flex;flex-direction:column;flex-grow:1;max-height:100%;max-width:100%;min-height:56px;min-width:fit-content;min-width:-moz-available;min-width:-webkit-fill-available;overflow:hidden;resize:both}.mdc-text-field--filled .mdc-text-field__resizer{transform:translateY(-1px)}.mdc-text-field--filled .mdc-text-field__resizer .mdc-text-field__input,.mdc-text-field--filled .mdc-text-field__resizer .mdc-text-field-character-counter{transform:translateY(1px)}.mdc-text-field--outlined .mdc-text-field__resizer{transform:translateX(-1px) translateY(-1px)}[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer,.mdc-text-field--outlined .mdc-text-field__resizer[dir=rtl]{transform:translateX(1px) translateY(-1px)}.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input,.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter{transform:translateX(1px) translateY(1px)}[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input,[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter,.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input[dir=rtl],.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter[dir=rtl]{transform:translateX(-1px) translateY(1px)}.mdc-text-field--with-leading-icon{padding-left:0;padding-right:16px}[dir=rtl] .mdc-text-field--with-leading-icon,.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:16px;padding-right:0}.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 48px);left:48px;right:initial}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label,.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label[dir=rtl]{left:initial;right:48px}.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 64px/0.75)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label{left:36px;right:initial}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label[dir=rtl]{left:initial;right:36px}.mdc-text-field--with-leading-icon.mdc-text-field--outlined :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) translateX(-32px) scale(1)}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-37.25px) translateX(32px) scale(1)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) translateX(-32px) scale(0.75)}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl],.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-34.75px) translateX(32px) scale(0.75)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--with-trailing-icon{padding-left:16px;padding-right:0}[dir=rtl] .mdc-text-field--with-trailing-icon,.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0;padding-right:16px}.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 64px)}.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 64px/0.75)}.mdc-text-field--with-trailing-icon.mdc-text-field--outlined :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 96px)}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 96px/0.75)}.mdc-text-field-helper-line{display:flex;justify-content:space-between;box-sizing:border-box}.mdc-text-field+.mdc-text-field-helper-line{padding-right:16px;padding-left:16px}.mdc-form-field>.mdc-text-field+label{align-self:flex-start}.mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--focused .mdc-notched-outline__trailing{border-width:2px}.mdc-text-field--focused+.mdc-text-field-helper-line .mdc-text-field-helper-text:not(.mdc-text-field-helper-text--validation-msg){opacity:1}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-text-field--focused.mdc-text-field--outlined.mdc-text-field--textarea .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:0}.mdc-text-field--invalid+.mdc-text-field-helper-line .mdc-text-field-helper-text--validation-msg{opacity:1}.mdc-text-field--disabled{pointer-events:none}@media screen and (forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--disabled .mdc-floating-label{cursor:default}.mdc-text-field--disabled.mdc-text-field--filled .mdc-text-field__ripple{display:none}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--end-aligned .mdc-text-field__input{text-align:right}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__input,.mdc-text-field--end-aligned .mdc-text-field__input[dir=rtl]{text-align:left}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__input,[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__input,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix{direction:ltr}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--prefix{padding-left:0;padding-right:2px}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--suffix{padding-left:12px;padding-right:0}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__icon--leading,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__icon--leading{order:1}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--suffix{order:2}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__input,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__input{order:3}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--prefix{order:4}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__icon--trailing,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__icon--trailing{order:5}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__input,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__input{text-align:right}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__affix--prefix{padding-right:12px}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__affix--suffix{padding-left:2px}.mdc-floating-label{position:absolute;left:0;-webkit-transform-origin:left top;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label,.mdc-floating-label[dir=rtl]{right:0;left:auto;-webkit-transform-origin:right top;transform-origin:right top;text-align:right}.mdc-floating-label--float-above{cursor:auto}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0px;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after,.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)[dir=rtl]::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline,.mdc-notched-outline[dir=rtl]{text-align:right}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{box-sizing:border-box;height:100%;pointer-events:none}.mdc-notched-outline__trailing{flex-grow:1}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch,.mdc-notched-outline--notched .mdc-notched-outline__notch[dir=rtl]{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{z-index:1}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{border-top:1px solid;border-bottom:1px solid}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;width:12px}[dir=rtl] .mdc-notched-outline__leading,.mdc-notched-outline__leading[dir=rtl]{border-left:none;border-right:1px solid}.mdc-notched-outline__trailing{border-left:none;border-right:1px solid}[dir=rtl] .mdc-notched-outline__trailing,.mdc-notched-outline__trailing[dir=rtl]{border-left:1px solid;border-right:none}.mdc-notched-outline__notch{max-width:calc(100% - 12px*2)}.mdc-line-ripple::before{border-bottom-width:1px}.mdc-line-ripple::after{border-bottom-width:2px}.mdc-text-field--filled{border-top-left-radius:var(--mdc-filled-text-field-container-shape);border-top-right-radius:var(--mdc-filled-text-field-container-shape);border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-caret-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-error-caret-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-filled-text-field-input-text-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-filled-text-field-disabled-input-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-label-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-focus-label-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-hover-label-text-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-disabled-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-focus-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-hover-label-text-color)}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mdc-filled-text-field-label-text-font);font-size:var(--mdc-filled-text-field-label-text-size);font-weight:var(--mdc-filled-text-field-label-text-weight);letter-spacing:var(--mdc-filled-text-field-label-text-tracking)}@media all{.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}}@media all{.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mdc-filled-text-field-container-color)}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mdc-filled-text-field-disabled-container-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-active-indicator-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-hover-active-indicator-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-focus-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-disabled-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-hover-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-error-focus-active-indicator-color)}.mdc-text-field--filled .mdc-line-ripple::before{border-bottom-width:var(--mdc-filled-text-field-active-indicator-height)}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mdc-filled-text-field-focus-active-indicator-height)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-caret-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-error-caret-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-outlined-text-field-input-text-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-outlined-text-field-disabled-input-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-label-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-focus-label-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-hover-label-text-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-disabled-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-focus-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-hover-label-text-color)}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mdc-outlined-text-field-label-text-font);font-size:var(--mdc-outlined-text-field-label-text-size);font-weight:var(--mdc-outlined-text-field-label-text-weight);letter-spacing:var(--mdc-outlined-text-field-label-text-tracking)}@media all{.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color)}}@media all{.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color)}}.mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(.75*var(--mdc-outlined-text-field-label-text-size))}.mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mdc-outlined-text-field-label-text-size)}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:var(--mdc-outlined-text-field-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape)}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-left-radius:0}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-outlined-text-field-container-shape))}}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px,var(--mdc-outlined-text-field-container-shape))*2)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-left-radius:0}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:var(--mdc-outlined-text-field-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape)}@supports(top: max(0%)){.mdc-text-field--outlined{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-left:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-right:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:max(16px,var(--mdc-outlined-text-field-container-shape))}}.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-right:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-right:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-hover-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-focus-outline-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-disabled-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-hover-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-focus-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__trailing{border-width:var(--mdc-outlined-text-field-outline-width)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-width:var(--mdc-outlined-text-field-focus-outline-width)}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-text-field-wrapper::before{content:none}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height);padding-top:var(--mat-form-field-filled-with-label-container-padding-top);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding);padding-bottom:var(--mat-form-field-container-vertical-padding)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:\\\"\\\";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color)}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font);line-height:var(--mat-form-field-subscript-text-line-height);font-size:var(--mat-form-field-subscript-text-size);letter-spacing:var(--mat-form-field-subscript-text-tracking);font-weight:var(--mat-form-field-subscript-text-weight)}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color)}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity)}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color)}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color)}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color)}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color)}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color)}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}.cdk-high-contrast-active .mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font);line-height:var(--mat-form-field-container-text-line-height);font-size:var(--mat-form-field-container-text-size);letter-spacing:var(--mat-form-field-container-text-tracking);font-weight:var(--mat-form-field-container-text-weight)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color)}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color)}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color)}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color)}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color)}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color)}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color)}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input{transition:opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}@media all{.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder{transition:opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}}@media all{.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}}@media all{.mdc-text-field--no-label .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder,.mdc-text-field--focused .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}}@media all{.mdc-text-field--no-label .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__affix{transition:opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--filled.mdc-ripple-upgraded--background-focused .mdc-text-field__ripple::before,.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea{transition:none}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea.mdc-text-field--filled .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-textarea-filled 250ms 1}@keyframes mdc-floating-label-shake-float-above-textarea-filled{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-textarea-outlined 250ms 1}@keyframes mdc-floating-label-shake-float-above-textarea-outlined{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon{0%{transform:translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}}[dir=rtl] .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake,.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined[dir=rtl] .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl{0%{transform:translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-standard 250ms 1}@keyframes mdc-floating-label-shake-float-above-standard{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}\"], dependencies: [{ kind: \"directive\", type: MatFormFieldFloatingLabel, selector: \"label[matFormFieldFloatingLabel]\", inputs: [\"floating\", \"monitorResize\"] }, { kind: \"component\", type: MatFormFieldNotchedOutline, selector: \"div[matFormFieldNotchedOutline]\", inputs: [\"matFormFieldNotchedOutlineOpen\"] }, { kind: \"directive\", type: NgTemplateOutlet, selector: \"[ngTemplateOutlet]\", inputs: [\"ngTemplateOutletContext\", \"ngTemplateOutlet\", \"ngTemplateOutletInjector\"] }, { kind: \"directive\", type: MatFormFieldLineRipple, selector: \"div[matFormFieldLineRipple]\" }, { kind: \"directive\", type: MatHint, selector: \"mat-hint\", inputs: [\"align\", \"id\"] }], animations: [matFormFieldAnimations.transitionMessages], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormField, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-form-field', exportAs: 'matFormField', animations: [matFormFieldAnimations.transitionMessages], host: {\n                        'class': 'mat-mdc-form-field',\n                        '[class.mat-mdc-form-field-label-always-float]': '_shouldAlwaysFloat()',\n                        '[class.mat-mdc-form-field-has-icon-prefix]': '_hasIconPrefix',\n                        '[class.mat-mdc-form-field-has-icon-suffix]': '_hasIconSuffix',\n                        // Note that these classes reuse the same names as the non-MDC version, because they can be\n                        // considered a public API since custom form controls may use them to style themselves.\n                        // See https://github.com/angular/components/pull/20502#discussion_r486124901.\n                        '[class.mat-form-field-invalid]': '_control.errorState',\n                        '[class.mat-form-field-disabled]': '_control.disabled',\n                        '[class.mat-form-field-autofilled]': '_control.autofilled',\n                        '[class.mat-form-field-no-animations]': '_animationMode === \"NoopAnimations\"',\n                        '[class.mat-form-field-appearance-fill]': 'appearance == \"fill\"',\n                        '[class.mat-form-field-appearance-outline]': 'appearance == \"outline\"',\n                        '[class.mat-form-field-hide-placeholder]': '_hasFloatingLabel() && !_shouldLabelFloat()',\n                        '[class.mat-focused]': '_control.focused',\n                        '[class.mat-primary]': 'color !== \"accent\" && color !== \"warn\"',\n                        '[class.mat-accent]': 'color === \"accent\"',\n                        '[class.mat-warn]': 'color === \"warn\"',\n                        '[class.ng-untouched]': '_shouldForward(\"untouched\")',\n                        '[class.ng-touched]': '_shouldForward(\"touched\")',\n                        '[class.ng-pristine]': '_shouldForward(\"pristine\")',\n                        '[class.ng-dirty]': '_shouldForward(\"dirty\")',\n                        '[class.ng-valid]': '_shouldForward(\"valid\")',\n                        '[class.ng-invalid]': '_shouldForward(\"invalid\")',\n                        '[class.ng-pending]': '_shouldForward(\"pending\")',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, providers: [\n                        { provide: MAT_FORM_FIELD, useExisting: MatFormField },\n                        { provide: FLOATING_LABEL_PARENT, useExisting: MatFormField },\n                    ], standalone: true, imports: [\n                        MatFormFieldFloatingLabel,\n                        MatFormFieldNotchedOutline,\n                        NgTemplateOutlet,\n                        MatFormFieldLineRipple,\n                        MatHint,\n                    ], template: \"<ng-template #labelTemplate>\\n  <!--\\n    MDC recommends that the text-field is a `<label>` element. This rather complicates the\\n    setup because it would require every form-field control to explicitly set `aria-labelledby`.\\n    This is because the `<label>` itself contains more than the actual label (e.g. prefix, suffix\\n    or other projected content), and screen readers could potentially read out undesired content.\\n    Excluding elements from being printed out requires them to be marked with `aria-hidden`, or\\n    the form control is set to a scoped element for the label (using `aria-labelledby`). Both of\\n    these options seem to complicate the setup because we know exactly what content is rendered\\n    as part of the label, and we don't want to spend resources on walking through projected content\\n    to set `aria-hidden`. Nor do we want to set `aria-labelledby` on every form control if we could\\n    simply link the label to the control using the label `for` attribute.\\n  -->\\n  @if (_hasFloatingLabel()) {\\n    <label matFormFieldFloatingLabel\\n           [floating]=\\\"_shouldLabelFloat()\\\"\\n           [monitorResize]=\\\"_hasOutline()\\\"\\n           [id]=\\\"_labelId\\\"\\n           [attr.for]=\\\"_control.disableAutomaticLabeling ? null : _control.id\\\">\\n      <ng-content select=\\\"mat-label\\\"></ng-content>\\n      <!--\\n        We set the required marker as a separate element, in order to make it easier to target if\\n        apps want to override it and to be able to set `aria-hidden` so that screen readers don't\\n        pick it up.\\n       -->\\n       @if (!hideRequiredMarker && _control.required) {\\n         <span\\n           aria-hidden=\\\"true\\\"\\n           class=\\\"mat-mdc-form-field-required-marker mdc-floating-label--required\\\"></span>\\n       }\\n    </label>\\n  }\\n</ng-template>\\n\\n<div class=\\\"mat-mdc-text-field-wrapper mdc-text-field\\\" #textField\\n     [class.mdc-text-field--filled]=\\\"!_hasOutline()\\\"\\n     [class.mdc-text-field--outlined]=\\\"_hasOutline()\\\"\\n     [class.mdc-text-field--no-label]=\\\"!_hasFloatingLabel()\\\"\\n     [class.mdc-text-field--disabled]=\\\"_control.disabled\\\"\\n     [class.mdc-text-field--invalid]=\\\"_control.errorState\\\"\\n     (click)=\\\"_control.onContainerClick($event)\\\">\\n  @if (!_hasOutline() && !_control.disabled) {\\n    <div class=\\\"mat-mdc-form-field-focus-overlay\\\"></div>\\n  }\\n  <div class=\\\"mat-mdc-form-field-flex\\\">\\n    @if (_hasOutline()) {\\n      <div matFormFieldNotchedOutline [matFormFieldNotchedOutlineOpen]=\\\"_shouldLabelFloat()\\\">\\n        @if (!_forceDisplayInfixLabel()) {\\n          <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n        }\\n      </div>\\n    }\\n\\n    @if (_hasIconPrefix) {\\n      <div class=\\\"mat-mdc-form-field-icon-prefix\\\" #iconPrefixContainer>\\n        <ng-content select=\\\"[matPrefix], [matIconPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasTextPrefix) {\\n      <div class=\\\"mat-mdc-form-field-text-prefix\\\" #textPrefixContainer>\\n        <ng-content select=\\\"[matTextPrefix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    <div class=\\\"mat-mdc-form-field-infix\\\">\\n      @if (!_hasOutline() || _forceDisplayInfixLabel()) {\\n        <ng-template [ngTemplateOutlet]=\\\"labelTemplate\\\"></ng-template>\\n      }\\n\\n      <ng-content></ng-content>\\n    </div>\\n\\n    @if (_hasTextSuffix) {\\n      <div class=\\\"mat-mdc-form-field-text-suffix\\\">\\n        <ng-content select=\\\"[matTextSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @if (_hasIconSuffix) {\\n      <div class=\\\"mat-mdc-form-field-icon-suffix\\\">\\n        <ng-content select=\\\"[matSuffix], [matIconSuffix]\\\"></ng-content>\\n      </div>\\n    }\\n  </div>\\n\\n  @if (!_hasOutline()) {\\n    <div matFormFieldLineRipple></div>\\n  }\\n</div>\\n\\n<div class=\\\"mat-mdc-form-field-subscript-wrapper mat-mdc-form-field-bottom-align\\\"\\n     [class.mat-mdc-form-field-subscript-dynamic-size]=\\\"subscriptSizing === 'dynamic'\\\">\\n\\n  @switch (_getDisplayedMessages()) {\\n    @case ('error') {\\n      <div class=\\\"mat-mdc-form-field-error-wrapper\\\"\\n           [@transitionMessages]=\\\"_subscriptAnimationState\\\">\\n        <ng-content select=\\\"mat-error, [matError]\\\"></ng-content>\\n      </div>\\n    }\\n\\n    @case ('hint') {\\n      <div class=\\\"mat-mdc-form-field-hint-wrapper\\\" [@transitionMessages]=\\\"_subscriptAnimationState\\\">\\n        @if (hintLabel) {\\n          <mat-hint [id]=\\\"_hintLabelId\\\">{{hintLabel}}</mat-hint>\\n        }\\n        <ng-content select=\\\"mat-hint:not([align='end'])\\\"></ng-content>\\n        <div class=\\\"mat-mdc-form-field-hint-spacer\\\"></div>\\n        <ng-content select=\\\"mat-hint[align='end']\\\"></ng-content>\\n      </div>\\n    }\\n  }\\n</div>\\n\", styles: [\".mdc-text-field{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:0;border-bottom-left-radius:0;display:inline-flex;align-items:baseline;padding:0 16px;position:relative;box-sizing:border-box;overflow:hidden;will-change:opacity,transform,color}.mdc-text-field .mdc-floating-label{top:50%;transform:translateY(-50%);pointer-events:none}.mdc-text-field__input{height:28px;width:100%;min-width:0;border:none;border-radius:0;background:none;appearance:none;padding:0}.mdc-text-field__input::-ms-clear{display:none}.mdc-text-field__input::-webkit-calendar-picker-indicator{display:none}.mdc-text-field__input:focus{outline:none}.mdc-text-field__input:invalid{box-shadow:none}@media all{.mdc-text-field__input::placeholder{opacity:0}}@media all{.mdc-text-field__input:-ms-input-placeholder{opacity:0}}@media all{.mdc-text-field--no-label .mdc-text-field__input::placeholder,.mdc-text-field--focused .mdc-text-field__input::placeholder{opacity:1}}@media all{.mdc-text-field--no-label .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mdc-text-field__input:-ms-input-placeholder{opacity:1}}.mdc-text-field__affix{height:28px;opacity:0;white-space:nowrap}.mdc-text-field--label-floating .mdc-text-field__affix,.mdc-text-field--no-label .mdc-text-field__affix{opacity:1}@supports(-webkit-hyphens: none){.mdc-text-field--outlined .mdc-text-field__affix{align-items:center;align-self:center;display:inline-flex;height:100%}}.mdc-text-field__affix--prefix{padding-left:0;padding-right:2px}[dir=rtl] .mdc-text-field__affix--prefix,.mdc-text-field__affix--prefix[dir=rtl]{padding-left:2px;padding-right:0}.mdc-text-field--end-aligned .mdc-text-field__affix--prefix{padding-left:0;padding-right:12px}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__affix--prefix,.mdc-text-field--end-aligned .mdc-text-field__affix--prefix[dir=rtl]{padding-left:12px;padding-right:0}.mdc-text-field__affix--suffix{padding-left:12px;padding-right:0}[dir=rtl] .mdc-text-field__affix--suffix,.mdc-text-field__affix--suffix[dir=rtl]{padding-left:0;padding-right:12px}.mdc-text-field--end-aligned .mdc-text-field__affix--suffix{padding-left:2px;padding-right:0}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__affix--suffix,.mdc-text-field--end-aligned .mdc-text-field__affix--suffix[dir=rtl]{padding-left:0;padding-right:2px}.mdc-text-field--filled{height:56px}.mdc-text-field--filled::before{display:inline-block;width:0;height:40px;content:\\\"\\\";vertical-align:0}.mdc-text-field--filled .mdc-floating-label{left:16px;right:initial}[dir=rtl] .mdc-text-field--filled .mdc-floating-label,.mdc-text-field--filled .mdc-floating-label[dir=rtl]{left:initial;right:16px}.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{height:100%}.mdc-text-field--filled.mdc-text-field--no-label .mdc-floating-label{display:none}.mdc-text-field--filled.mdc-text-field--no-label::before{display:none}@supports(-webkit-hyphens: none){.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__affix{align-items:center;align-self:center;display:inline-flex;height:100%}}.mdc-text-field--outlined{height:56px;overflow:visible}.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) scale(1)}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) scale(0.75)}.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--outlined .mdc-text-field__input{height:100%}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-shape-small, 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px,var(--mdc-shape-small, 4px))*2)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:4px;border-top-right-radius:var(--mdc-shape-small, 4px);border-bottom-right-radius:4px;border-bottom-right-radius:var(--mdc-shape-small, 4px);border-bottom-left-radius:0}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:4px;border-top-left-radius:var(--mdc-shape-small, 4px);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:4px;border-bottom-left-radius:var(--mdc-shape-small, 4px)}@supports(top: max(0%)){.mdc-text-field--outlined{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined{padding-right:max(16px,var(--mdc-shape-small, 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-right:max(16px,var(--mdc-shape-small, 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-left:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-right:max(16px,var(--mdc-shape-small, 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-right:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:max(16px,var(--mdc-shape-small, 4px))}}.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-right:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-left:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-right:max(16px,calc(var(--mdc-shape-small, 4px) + 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:1px}.mdc-text-field--outlined .mdc-floating-label{left:4px;right:initial}[dir=rtl] .mdc-text-field--outlined .mdc-floating-label,.mdc-text-field--outlined .mdc-floating-label[dir=rtl]{left:initial;right:4px}.mdc-text-field--outlined .mdc-text-field__input{display:flex;border:none !important;background-color:rgba(0,0,0,0)}.mdc-text-field--outlined .mdc-notched-outline{z-index:1}.mdc-text-field--textarea{flex-direction:column;align-items:center;width:auto;height:auto;padding:0}.mdc-text-field--textarea .mdc-floating-label{top:19px}.mdc-text-field--textarea .mdc-floating-label:not(.mdc-floating-label--float-above){transform:none}.mdc-text-field--textarea .mdc-text-field__input{flex-grow:1;height:auto;min-height:1.5rem;overflow-x:hidden;overflow-y:auto;box-sizing:border-box;resize:none;padding:0 16px}.mdc-text-field--textarea.mdc-text-field--filled::before{display:none}.mdc-text-field--textarea.mdc-text-field--filled .mdc-floating-label--float-above{transform:translateY(-10.25px) scale(0.75)}.mdc-text-field--textarea.mdc-text-field--filled .mdc-text-field__input{margin-top:23px;margin-bottom:9px}.mdc-text-field--textarea.mdc-text-field--filled.mdc-text-field--no-label .mdc-text-field__input{margin-top:16px;margin-bottom:16px}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:0}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-27.25px) scale(1)}.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-24.75px) scale(0.75)}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-text-field__input{margin-top:16px;margin-bottom:16px}.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label{top:18px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field__input{margin-bottom:2px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter{align-self:flex-end;padding:0 16px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter::after{display:inline-block;width:0;height:16px;content:\\\"\\\";vertical-align:-16px}.mdc-text-field--textarea.mdc-text-field--with-internal-counter .mdc-text-field-character-counter::before{display:none}.mdc-text-field__resizer{align-self:stretch;display:inline-flex;flex-direction:column;flex-grow:1;max-height:100%;max-width:100%;min-height:56px;min-width:fit-content;min-width:-moz-available;min-width:-webkit-fill-available;overflow:hidden;resize:both}.mdc-text-field--filled .mdc-text-field__resizer{transform:translateY(-1px)}.mdc-text-field--filled .mdc-text-field__resizer .mdc-text-field__input,.mdc-text-field--filled .mdc-text-field__resizer .mdc-text-field-character-counter{transform:translateY(1px)}.mdc-text-field--outlined .mdc-text-field__resizer{transform:translateX(-1px) translateY(-1px)}[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer,.mdc-text-field--outlined .mdc-text-field__resizer[dir=rtl]{transform:translateX(1px) translateY(-1px)}.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input,.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter{transform:translateX(1px) translateY(1px)}[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input,[dir=rtl] .mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter,.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field__input[dir=rtl],.mdc-text-field--outlined .mdc-text-field__resizer .mdc-text-field-character-counter[dir=rtl]{transform:translateX(-1px) translateY(1px)}.mdc-text-field--with-leading-icon{padding-left:0;padding-right:16px}[dir=rtl] .mdc-text-field--with-leading-icon,.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:16px;padding-right:0}.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 48px);left:48px;right:initial}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label,.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label[dir=rtl]{left:initial;right:48px}.mdc-text-field--with-leading-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 64px/0.75)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label{left:36px;right:initial}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label[dir=rtl]{left:initial;right:36px}.mdc-text-field--with-leading-icon.mdc-text-field--outlined :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above{transform:translateY(-37.25px) translateX(-32px) scale(1)}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-37.25px) translateX(32px) scale(1)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{transform:translateY(-34.75px) translateX(-32px) scale(0.75)}[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,[dir=rtl] .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl],.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above[dir=rtl]{transform:translateY(-34.75px) translateX(32px) scale(0.75)}.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:.75rem}.mdc-text-field--with-leading-icon.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:1rem}.mdc-text-field--with-trailing-icon{padding-left:16px;padding-right:0}[dir=rtl] .mdc-text-field--with-trailing-icon,.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0;padding-right:16px}.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 64px)}.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 64px/0.75)}.mdc-text-field--with-trailing-icon.mdc-text-field--outlined :not(.mdc-notched-outline--notched) .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label{max-width:calc(100% - 96px)}.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon.mdc-text-field--filled .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 96px/0.75)}.mdc-text-field-helper-line{display:flex;justify-content:space-between;box-sizing:border-box}.mdc-text-field+.mdc-text-field-helper-line{padding-right:16px;padding-left:16px}.mdc-form-field>.mdc-text-field+label{align-self:flex-start}.mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--focused .mdc-notched-outline__trailing{border-width:2px}.mdc-text-field--focused+.mdc-text-field-helper-line .mdc-text-field-helper-text:not(.mdc-text-field-helper-text--validation-msg){opacity:1}.mdc-text-field--focused.mdc-text-field--outlined .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:2px}.mdc-text-field--focused.mdc-text-field--outlined.mdc-text-field--textarea .mdc-notched-outline--notched .mdc-notched-outline__notch{padding-top:0}.mdc-text-field--invalid+.mdc-text-field-helper-line .mdc-text-field-helper-text--validation-msg{opacity:1}.mdc-text-field--disabled{pointer-events:none}@media screen and (forced-colors: active){.mdc-text-field--disabled .mdc-text-field__input{background-color:Window}.mdc-text-field--disabled .mdc-floating-label{z-index:1}}.mdc-text-field--disabled .mdc-floating-label{cursor:default}.mdc-text-field--disabled.mdc-text-field--filled .mdc-text-field__ripple{display:none}.mdc-text-field--disabled .mdc-text-field__input{pointer-events:auto}.mdc-text-field--end-aligned .mdc-text-field__input{text-align:right}[dir=rtl] .mdc-text-field--end-aligned .mdc-text-field__input,.mdc-text-field--end-aligned .mdc-text-field__input[dir=rtl]{text-align:left}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__input,[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__input,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix{direction:ltr}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--prefix{padding-left:0;padding-right:2px}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--suffix{padding-left:12px;padding-right:0}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__icon--leading,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__icon--leading{order:1}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--suffix{order:2}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__input,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__input{order:3}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__affix--prefix{order:4}[dir=rtl] .mdc-text-field--ltr-text .mdc-text-field__icon--trailing,.mdc-text-field--ltr-text[dir=rtl] .mdc-text-field__icon--trailing{order:5}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__input,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__input{text-align:right}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__affix--prefix,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__affix--prefix{padding-right:12px}[dir=rtl] .mdc-text-field--ltr-text.mdc-text-field--end-aligned .mdc-text-field__affix--suffix,.mdc-text-field--ltr-text.mdc-text-field--end-aligned[dir=rtl] .mdc-text-field__affix--suffix{padding-left:2px}.mdc-floating-label{position:absolute;left:0;-webkit-transform-origin:left top;transform-origin:left top;line-height:1.15rem;text-align:left;text-overflow:ellipsis;white-space:nowrap;cursor:text;overflow:hidden;will-change:transform}[dir=rtl] .mdc-floating-label,.mdc-floating-label[dir=rtl]{right:0;left:auto;-webkit-transform-origin:right top;transform-origin:right top;text-align:right}.mdc-floating-label--float-above{cursor:auto}.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after{margin-left:1px;margin-right:0px;content:\\\"*\\\"}[dir=rtl] .mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)::after,.mdc-floating-label--required:not(.mdc-floating-label--hide-required-marker)[dir=rtl]::after{margin-left:0;margin-right:1px}.mdc-notched-outline{display:flex;position:absolute;top:0;right:0;left:0;box-sizing:border-box;width:100%;max-width:100%;height:100%;text-align:left;pointer-events:none}[dir=rtl] .mdc-notched-outline,.mdc-notched-outline[dir=rtl]{text-align:right}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{box-sizing:border-box;height:100%;pointer-events:none}.mdc-notched-outline__trailing{flex-grow:1}.mdc-notched-outline__notch{flex:0 0 auto;width:auto}.mdc-notched-outline .mdc-floating-label{display:inline-block;position:relative;max-width:100%}.mdc-notched-outline .mdc-floating-label--float-above{text-overflow:clip}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:133.3333333333%}.mdc-notched-outline--notched .mdc-notched-outline__notch{padding-left:0;padding-right:8px;border-top:none}[dir=rtl] .mdc-notched-outline--notched .mdc-notched-outline__notch,.mdc-notched-outline--notched .mdc-notched-outline__notch[dir=rtl]{padding-left:8px;padding-right:0}.mdc-notched-outline--no-label .mdc-notched-outline__notch{display:none}.mdc-line-ripple::before,.mdc-line-ripple::after{position:absolute;bottom:0;left:0;width:100%;border-bottom-style:solid;content:\\\"\\\"}.mdc-line-ripple::before{z-index:1}.mdc-line-ripple::after{transform:scaleX(0);opacity:0;z-index:2}.mdc-line-ripple--active::after{transform:scaleX(1);opacity:1}.mdc-line-ripple--deactivating::after{opacity:0}.mdc-floating-label--float-above{transform:translateY(-106%) scale(0.75)}.mdc-notched-outline__leading,.mdc-notched-outline__notch,.mdc-notched-outline__trailing{border-top:1px solid;border-bottom:1px solid}.mdc-notched-outline__leading{border-left:1px solid;border-right:none;width:12px}[dir=rtl] .mdc-notched-outline__leading,.mdc-notched-outline__leading[dir=rtl]{border-left:none;border-right:1px solid}.mdc-notched-outline__trailing{border-left:none;border-right:1px solid}[dir=rtl] .mdc-notched-outline__trailing,.mdc-notched-outline__trailing[dir=rtl]{border-left:1px solid;border-right:none}.mdc-notched-outline__notch{max-width:calc(100% - 12px*2)}.mdc-line-ripple::before{border-bottom-width:1px}.mdc-line-ripple::after{border-bottom-width:2px}.mdc-text-field--filled{border-top-left-radius:var(--mdc-filled-text-field-container-shape);border-top-right-radius:var(--mdc-filled-text-field-container-shape);border-bottom-right-radius:0;border-bottom-left-radius:0}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-caret-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-filled-text-field-error-caret-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-filled-text-field-input-text-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-filled-text-field-disabled-input-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-label-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-focus-label-text-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-hover-label-text-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--disabled .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-disabled-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-focus-label-text-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-filled-text-field-error-hover-label-text-color)}.mdc-text-field--filled .mdc-floating-label{font-family:var(--mdc-filled-text-field-label-text-font);font-size:var(--mdc-filled-text-field-label-text-size);font-weight:var(--mdc-filled-text-field-label-text-weight);letter-spacing:var(--mdc-filled-text-field-label-text-tracking)}@media all{.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}}@media all{.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-filled-text-field-input-text-placeholder-color)}}.mdc-text-field--filled:not(.mdc-text-field--disabled){background-color:var(--mdc-filled-text-field-container-color)}.mdc-text-field--filled.mdc-text-field--disabled{background-color:var(--mdc-filled-text-field-disabled-container-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-active-indicator-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-hover-active-indicator-color)}.mdc-text-field--filled:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-focus-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--disabled .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-disabled-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-line-ripple::before{border-bottom-color:var(--mdc-filled-text-field-error-hover-active-indicator-color)}.mdc-text-field--filled.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-line-ripple::after{border-bottom-color:var(--mdc-filled-text-field-error-focus-active-indicator-color)}.mdc-text-field--filled .mdc-line-ripple::before{border-bottom-width:var(--mdc-filled-text-field-active-indicator-height)}.mdc-text-field--filled .mdc-line-ripple::after{border-bottom-width:var(--mdc-filled-text-field-focus-active-indicator-height)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-caret-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-text-field__input{caret-color:var(--mdc-outlined-text-field-error-caret-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input{color:var(--mdc-outlined-text-field-input-text-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-text-field__input{color:var(--mdc-outlined-text-field-disabled-input-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-label-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-focus-label-text-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-hover-label-text-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-disabled-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-focus-label-text-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-floating-label--float-above{color:var(--mdc-outlined-text-field-error-hover-label-text-color)}.mdc-text-field--outlined .mdc-floating-label{font-family:var(--mdc-outlined-text-field-label-text-font);font-size:var(--mdc-outlined-text-field-label-text-size);font-weight:var(--mdc-outlined-text-field-label-text-weight);letter-spacing:var(--mdc-outlined-text-field-label-text-tracking)}@media all{.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input::placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color)}}@media all{.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-text-field__input:-ms-input-placeholder{color:var(--mdc-outlined-text-field-input-text-placeholder-color)}}.mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(.75*var(--mdc-outlined-text-field-label-text-size))}.mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined.mdc-notched-outline--upgraded .mdc-floating-label--float-above,.mdc-text-field--outlined.mdc-text-field--textarea.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mdc-outlined-text-field-label-text-size)}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{border-top-left-radius:var(--mdc-outlined-text-field-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape)}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading[dir=rtl]{border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-left-radius:0}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__leading{width:max(12px,var(--mdc-outlined-text-field-container-shape))}}@supports(top: max(0%)){.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__notch{max-width:calc(100% - max(12px,var(--mdc-outlined-text-field-container-shape))*2)}}.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing{border-top-left-radius:0;border-top-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-right-radius:var(--mdc-outlined-text-field-container-shape);border-bottom-left-radius:0}[dir=rtl] .mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing,.mdc-text-field--outlined .mdc-notched-outline .mdc-notched-outline__trailing[dir=rtl]{border-top-left-radius:var(--mdc-outlined-text-field-container-shape);border-top-right-radius:0;border-bottom-right-radius:0;border-bottom-left-radius:var(--mdc-outlined-text-field-container-shape)}@supports(top: max(0%)){.mdc-text-field--outlined{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}@supports(top: max(0%)){.mdc-text-field--outlined+.mdc-text-field-helper-line{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-left:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-leading-icon{padding-right:max(16px,var(--mdc-outlined-text-field-container-shape))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-right:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-leading-icon,.mdc-text-field--outlined.mdc-text-field--with-leading-icon[dir=rtl]{padding-left:max(16px,var(--mdc-outlined-text-field-container-shape))}}.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-right:0}@supports(top: max(0%)){.mdc-text-field--outlined.mdc-text-field--with-trailing-icon{padding-left:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-left:0}@supports(top: max(0%)){[dir=rtl] .mdc-text-field--outlined.mdc-text-field--with-trailing-icon,.mdc-text-field--outlined.mdc-text-field--with-trailing-icon[dir=rtl]{padding-right:max(16px,calc(var(--mdc-outlined-text-field-container-shape) + 4px))}}.mdc-text-field--outlined.mdc-text-field--with-leading-icon.mdc-text-field--with-trailing-icon{padding-left:0;padding-right:0}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-hover-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-focus-outline-color)}.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--disabled .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-disabled-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled) .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled):not(.mdc-text-field--focused):hover .mdc-notched-outline .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-hover-outline-color)}.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__leading,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__notch,.mdc-text-field--outlined.mdc-text-field--invalid:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline__trailing{border-color:var(--mdc-outlined-text-field-error-focus-outline-color)}.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled) .mdc-notched-outline .mdc-notched-outline__trailing{border-width:var(--mdc-outlined-text-field-outline-width)}.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__leading,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__notch,.mdc-text-field--outlined:not(.mdc-text-field--disabled).mdc-text-field--focused .mdc-notched-outline .mdc-notched-outline__trailing{border-width:var(--mdc-outlined-text-field-focus-outline-width)}.mat-mdc-form-field-textarea-control{vertical-align:middle;resize:vertical;box-sizing:border-box;height:auto;margin:0;padding:0;border:none;overflow:auto}.mat-mdc-form-field-input-control.mat-mdc-form-field-input-control{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font:inherit;letter-spacing:inherit;text-decoration:inherit;text-transform:inherit;border:none}.mat-mdc-form-field .mat-mdc-floating-label.mdc-floating-label{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;line-height:normal;pointer-events:all;will-change:auto}.mat-mdc-form-field:not(.mat-form-field-disabled) .mat-mdc-floating-label.mdc-floating-label{cursor:inherit}.mdc-text-field--no-label:not(.mdc-text-field--textarea) .mat-mdc-form-field-input-control.mdc-text-field__input,.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control{height:auto}.mat-mdc-text-field-wrapper .mat-mdc-form-field-input-control.mdc-text-field__input[type=color]{height:23px}.mat-mdc-text-field-wrapper{height:auto;flex:auto;will-change:auto}.mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-left:0;--mat-mdc-form-field-label-offset-x: -16px}.mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-right:0}[dir=rtl] .mat-mdc-text-field-wrapper{padding-left:16px;padding-right:16px}[dir=rtl] .mat-mdc-form-field-has-icon-suffix .mat-mdc-text-field-wrapper{padding-left:0}[dir=rtl] .mat-mdc-form-field-has-icon-prefix .mat-mdc-text-field-wrapper{padding-right:0}.mat-form-field-disabled .mdc-text-field__input::placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input::-moz-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input::-webkit-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-form-field-disabled .mdc-text-field__input:-ms-input-placeholder{color:var(--mat-form-field-disabled-input-text-placeholder-color)}.mat-mdc-form-field-label-always-float .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms;opacity:1}.mat-mdc-text-field-wrapper .mat-mdc-form-field-infix .mat-mdc-floating-label{left:auto;right:auto}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-text-field__input{display:inline-block}.mat-mdc-form-field .mat-mdc-text-field-wrapper.mdc-text-field .mdc-notched-outline__notch{padding-top:0}.mat-mdc-text-field-wrapper::before{content:none}.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:1px solid rgba(0,0,0,0)}[dir=rtl] .mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field.mat-mdc-form-field .mdc-notched-outline__notch{border-left:none;border-right:1px solid rgba(0,0,0,0)}.mat-mdc-form-field-infix{min-height:var(--mat-form-field-container-height);padding-top:var(--mat-form-field-filled-with-label-container-padding-top);padding-bottom:var(--mat-form-field-filled-with-label-container-padding-bottom)}.mdc-text-field--outlined .mat-mdc-form-field-infix,.mdc-text-field--no-label .mat-mdc-form-field-infix{padding-top:var(--mat-form-field-container-vertical-padding);padding-bottom:var(--mat-form-field-container-vertical-padding)}.mat-mdc-text-field-wrapper .mat-mdc-form-field-flex .mat-mdc-floating-label{top:calc(var(--mat-form-field-container-height)/2)}.mdc-text-field--filled .mat-mdc-floating-label{display:var(--mat-form-field-filled-label-display, block)}.mat-mdc-text-field-wrapper.mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{--mat-mdc-form-field-label-transform: translateY(calc(calc(6.75px + var(--mat-form-field-container-height) / 2) * -1)) scale(var(--mat-mdc-form-field-floating-label-scale, 0.75));transform:var(--mat-mdc-form-field-label-transform)}.mat-mdc-form-field-subscript-wrapper{box-sizing:border-box;width:100%;position:relative}.mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-error-wrapper{position:absolute;top:0;left:0;right:0;padding:0 16px}.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-hint-wrapper,.mat-mdc-form-field-subscript-dynamic-size .mat-mdc-form-field-error-wrapper{position:static}.mat-mdc-form-field-bottom-align::before{content:\\\"\\\";display:inline-block;height:16px}.mat-mdc-form-field-bottom-align.mat-mdc-form-field-subscript-dynamic-size::before{content:unset}.mat-mdc-form-field-hint-end{order:1}.mat-mdc-form-field-hint-wrapper{display:flex}.mat-mdc-form-field-hint-spacer{flex:1 0 1em}.mat-mdc-form-field-error{display:block;color:var(--mat-form-field-error-text-color)}.mat-mdc-form-field-subscript-wrapper,.mat-mdc-form-field-bottom-align::before{-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-subscript-text-font);line-height:var(--mat-form-field-subscript-text-line-height);font-size:var(--mat-form-field-subscript-text-size);letter-spacing:var(--mat-form-field-subscript-text-tracking);font-weight:var(--mat-form-field-subscript-text-weight)}.mat-mdc-form-field-focus-overlay{top:0;left:0;right:0;bottom:0;position:absolute;opacity:0;pointer-events:none;background-color:var(--mat-form-field-state-layer-color)}.mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-hover-state-layer-opacity)}.mat-mdc-form-field.mat-focused .mat-mdc-form-field-focus-overlay{opacity:var(--mat-form-field-focus-state-layer-opacity)}select.mat-mdc-form-field-input-control{-moz-appearance:none;-webkit-appearance:none;background-color:rgba(0,0,0,0);display:inline-flex;box-sizing:border-box}select.mat-mdc-form-field-input-control:not(:disabled){cursor:pointer}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option{color:var(--mat-form-field-select-option-text-color)}select.mat-mdc-form-field-input-control:not(.mat-mdc-native-select-inline) option:disabled{color:var(--mat-form-field-select-disabled-option-text-color)}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{content:\\\"\\\";width:0;height:0;border-left:5px solid rgba(0,0,0,0);border-right:5px solid rgba(0,0,0,0);border-top:5px solid;position:absolute;right:0;top:50%;margin-top:-2.5px;pointer-events:none;color:var(--mat-form-field-enabled-select-arrow-color)}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-infix::after{right:auto;left:0}.mat-mdc-form-field-type-mat-native-select.mat-focused .mat-mdc-form-field-infix::after{color:var(--mat-form-field-focus-select-arrow-color)}.mat-mdc-form-field-type-mat-native-select.mat-form-field-disabled .mat-mdc-form-field-infix::after{color:var(--mat-form-field-disabled-select-arrow-color)}.mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:15px}[dir=rtl] .mat-mdc-form-field-type-mat-native-select .mat-mdc-form-field-input-control{padding-right:0;padding-left:15px}.cdk-high-contrast-active .mat-form-field-appearance-fill .mat-mdc-text-field-wrapper{outline:solid 1px}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-form-field-disabled .mat-mdc-text-field-wrapper{outline-color:GrayText}.cdk-high-contrast-active .mat-form-field-appearance-fill.mat-focused .mat-mdc-text-field-wrapper{outline:dashed 3px}.cdk-high-contrast-active .mat-mdc-form-field.mat-focused .mdc-notched-outline{border:dashed 3px}.mat-mdc-form-field-input-control[type=date],.mat-mdc-form-field-input-control[type=datetime],.mat-mdc-form-field-input-control[type=datetime-local],.mat-mdc-form-field-input-control[type=month],.mat-mdc-form-field-input-control[type=week],.mat-mdc-form-field-input-control[type=time]{line-height:1}.mat-mdc-form-field-input-control::-webkit-datetime-edit{line-height:1;padding:0;margin-bottom:-2px}.mat-mdc-form-field{--mat-mdc-form-field-floating-label-scale: 0.75;display:inline-flex;flex-direction:column;min-width:0;text-align:left;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;font-family:var(--mat-form-field-container-text-font);line-height:var(--mat-form-field-container-text-line-height);font-size:var(--mat-form-field-container-text-size);letter-spacing:var(--mat-form-field-container-text-tracking);font-weight:var(--mat-form-field-container-text-weight)}[dir=rtl] .mat-mdc-form-field{text-align:right}.mat-mdc-form-field .mdc-text-field--outlined .mdc-floating-label--float-above{font-size:calc(var(--mat-form-field-outlined-label-text-populated-size)*var(--mat-mdc-form-field-floating-label-scale))}.mat-mdc-form-field .mdc-text-field--outlined .mdc-notched-outline--upgraded .mdc-floating-label--float-above{font-size:var(--mat-form-field-outlined-label-text-populated-size)}.mat-mdc-form-field-flex{display:inline-flex;align-items:baseline;box-sizing:border-box;width:100%}.mat-mdc-text-field-wrapper{width:100%;z-index:0}.mat-mdc-form-field-icon-prefix,.mat-mdc-form-field-icon-suffix{align-self:center;line-height:0;pointer-events:auto;position:relative;z-index:1}.mat-mdc-form-field-icon-prefix>.mat-icon,.mat-mdc-form-field-icon-suffix>.mat-icon{padding:0 12px;box-sizing:content-box}.mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-leading-icon-color)}.mat-form-field-disabled .mat-mdc-form-field-icon-prefix{color:var(--mat-form-field-disabled-leading-icon-color)}.mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-trailing-icon-color)}.mat-form-field-disabled .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-disabled-trailing-icon-color)}.mat-form-field-invalid .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-trailing-icon-color)}.mat-form-field-invalid:not(.mat-focused):not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper:hover .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-hover-trailing-icon-color)}.mat-form-field-invalid.mat-focused .mat-mdc-text-field-wrapper .mat-mdc-form-field-icon-suffix{color:var(--mat-form-field-error-focus-trailing-icon-color)}.mat-mdc-form-field-icon-prefix,[dir=rtl] .mat-mdc-form-field-icon-suffix{padding:0 4px 0 0}.mat-mdc-form-field-icon-suffix,[dir=rtl] .mat-mdc-form-field-icon-prefix{padding:0 0 0 4px}.mat-mdc-form-field-subscript-wrapper .mat-icon,.mat-mdc-form-field label .mat-icon{width:1em;height:1em;font-size:inherit}.mat-mdc-form-field-infix{flex:auto;min-width:0;width:180px;position:relative;box-sizing:border-box}.mat-mdc-form-field .mdc-notched-outline__notch{margin-left:-1px;-webkit-clip-path:inset(-9em -999em -9em 1px);clip-path:inset(-9em -999em -9em 1px)}[dir=rtl] .mat-mdc-form-field .mdc-notched-outline__notch{margin-left:0;margin-right:-1px;-webkit-clip-path:inset(-9em 1px -9em -999em);clip-path:inset(-9em 1px -9em -999em)}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input{transition:opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}@media all{.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder{transition:opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}}@media all{.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder{transition:opacity 67ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}}@media all{.mdc-text-field--no-label .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder,.mdc-text-field--focused .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input::placeholder{transition-delay:40ms;transition-duration:110ms}}@media all{.mdc-text-field--no-label .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder,.mdc-text-field--focused .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__input:-ms-input-placeholder{transition-delay:40ms;transition-duration:110ms}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field__affix{transition:opacity 150ms 0ms cubic-bezier(0.4, 0, 0.2, 1)}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--filled.mdc-ripple-upgraded--background-focused .mdc-text-field__ripple::before,.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--filled:not(.mdc-ripple-upgraded):focus .mdc-text-field__ripple::before{transition-duration:75ms}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 34.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea{transition:none}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea.mdc-text-field--filled .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-textarea-filled 250ms 1}@keyframes mdc-floating-label-shake-float-above-textarea-filled{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 10.25px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--textarea.mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-textarea-outlined 250ms 1}@keyframes mdc-floating-label-shake-float-above-textarea-outlined{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 24.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon{0%{transform:translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - 32px)) translateY(calc(0% - 34.75px)) scale(0.75)}}[dir=rtl] .mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined .mdc-floating-label--shake,.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-text-field--with-leading-icon.mdc-text-field--outlined[dir=rtl] .mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-text-field-outlined-leading-icon 250ms 1}@keyframes mdc-floating-label-shake-float-above-text-field-outlined-leading-icon-rtl{0%{transform:translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}100%{transform:translateX(calc(0% - -32px)) translateY(calc(0% - 34.75px)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-floating-label{transition:transform 150ms cubic-bezier(0.4, 0, 0.2, 1),color 150ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-floating-label--shake{animation:mdc-floating-label-shake-float-above-standard 250ms 1}@keyframes mdc-floating-label-shake-float-above-standard{0%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}33%{animation-timing-function:cubic-bezier(0.5, 0, 0.701732, 0.495819);transform:translateX(calc(4% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}66%{animation-timing-function:cubic-bezier(0.302435, 0.381352, 0.55, 0.956352);transform:translateX(calc(-4% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}100%{transform:translateX(calc(0% - 0%)) translateY(calc(0% - 106%)) scale(0.75)}}.mat-mdc-form-field:not(.mat-form-field-no-animations) .mdc-line-ripple::after{transition:transform 180ms cubic-bezier(0.4, 0, 0.2, 1),opacity 180ms cubic-bezier(0.4, 0, 0.2, 1)}.mdc-notched-outline .mdc-floating-label{max-width:calc(100% + 1px)}.mdc-notched-outline--upgraded .mdc-floating-label--float-above{max-width:calc(133.3333333333% + 1px)}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: i1.Directionality }, { type: i2.Platform }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_FORM_FIELD_DEFAULT_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }], propDecorators: { _textField: [{\n                type: ViewChild,\n                args: ['textField']\n            }], _iconPrefixContainer: [{\n                type: ViewChild,\n                args: ['iconPrefixContainer']\n            }], _textPrefixContainer: [{\n                type: ViewChild,\n                args: ['textPrefixContainer']\n            }], _floatingLabel: [{\n                type: ViewChild,\n                args: [MatFormFieldFloatingLabel]\n            }], _notchedOutline: [{\n                type: ViewChild,\n                args: [MatFormFieldNotchedOutline]\n            }], _lineRipple: [{\n                type: ViewChild,\n                args: [MatFormFieldLineRipple]\n            }], _labelChildNonStatic: [{\n                type: ContentChild,\n                args: [MatLabel]\n            }], _labelChildStatic: [{\n                type: ContentChild,\n                args: [MatLabel, { static: true }]\n            }], _formFieldControl: [{\n                type: ContentChild,\n                args: [MatFormFieldControl]\n            }], _prefixChildren: [{\n                type: ContentChildren,\n                args: [MAT_PREFIX, { descendants: true }]\n            }], _suffixChildren: [{\n                type: ContentChildren,\n                args: [MAT_SUFFIX, { descendants: true }]\n            }], _errorChildren: [{\n                type: ContentChildren,\n                args: [MAT_ERROR, { descendants: true }]\n            }], _hintChildren: [{\n                type: ContentChildren,\n                args: [MatHint, { descendants: true }]\n            }], hideRequiredMarker: [{\n                type: Input\n            }], color: [{\n                type: Input\n            }], floatLabel: [{\n                type: Input\n            }], appearance: [{\n                type: Input\n            }], subscriptSizing: [{\n                type: Input\n            }], hintLabel: [{\n                type: Input\n            }] } });\n\nclass MatFormFieldModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormFieldModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormFieldModule, imports: [MatCommonModule,\n            CommonModule,\n            ObserversModule,\n            MatFormField,\n            MatLabel,\n            MatError,\n            MatHint,\n            MatPrefix,\n            MatSuffix], exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormFieldModule, imports: [MatCommonModule,\n            CommonModule,\n            ObserversModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatFormFieldModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatCommonModule,\n                        CommonModule,\n                        ObserversModule,\n                        MatFormField,\n                        MatLabel,\n                        MatError,\n                        MatHint,\n                        MatPrefix,\n                        MatSuffix,\n                    ],\n                    exports: [MatFormField, MatLabel, MatHint, MatError, MatPrefix, MatSuffix, MatCommonModule],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_ERROR, MAT_FORM_FIELD, MAT_FORM_FIELD_DEFAULT_OPTIONS, MAT_PREFIX, MAT_SUFFIX, MatError, MatFormField, MatFormFieldControl, MatFormFieldModule, MatHint, MatLabel, MatPrefix, MatSuffix, getMatFormFieldDuplicatedHintError, getMatFormFieldMissingControlError, getMatFormFieldPlaceholderConflictError, matFormFieldAnimations };\n", "import { coerceBooleanProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/platform';\nimport { getSupportedInputTypes } from '@angular/cdk/platform';\nimport * as i4 from '@angular/cdk/text-field';\nimport { TextFieldModule } from '@angular/cdk/text-field';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Optional, Self, Inject, Input, NgModule } from '@angular/core';\nimport * as i2 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport * as i3 from '@angular/material/core';\nimport { _ErrorStateTracker, MatCommonModule } from '@angular/material/core';\nimport * as i5 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nexport { Mat<PERSON>rror, MatFormField, MatHint, MatLabel, MatPrefix, MatSuffix } from '@angular/material/form-field';\nimport { Subject } from 'rxjs';\n\n/** @docs-private */\nfunction getMatInputUnsupportedTypeError(type) {\n    return Error(`Input type \"${type}\" isn't supported by matInput.`);\n}\n\n/**\n * This token is used to inject the object whose value should be set into `MatInput`. If none is\n * provided, the native `HTMLInputElement` is used. Directives like `MatDatepickerInput` can provide\n * themselves for this token, in order to make `MatInput` delegate the getting and setting of the\n * value to them.\n */\nconst MAT_INPUT_VALUE_ACCESSOR = new InjectionToken('MAT_INPUT_VALUE_ACCESSOR');\n\n// Invalid input type. Using one of these will throw an MatInputUnsupportedTypeError.\nconst MAT_INPUT_INVALID_TYPES = [\n    'button',\n    'checkbox',\n    'file',\n    'hidden',\n    'image',\n    'radio',\n    'range',\n    'reset',\n    'submit',\n];\nlet nextUniqueId = 0;\nclass MatInput {\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        // Browsers may not fire the blur event if the input is disabled too quickly.\n        // Reset from here to ensure that the element doesn't become stuck.\n        if (this.focused) {\n            this.focused = false;\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get id() {\n        return this._id;\n    }\n    set id(value) {\n        this._id = value || this._uid;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = coerceBooleanProperty(value);\n    }\n    /** Input type of the element. */\n    get type() {\n        return this._type;\n    }\n    set type(value) {\n        this._type = value || 'text';\n        this._validateType();\n        // When using Angular inputs, developers are no longer able to set the properties on the native\n        // input element. To ensure that bindings for `type` work, we need to sync the setter\n        // with the native property. Textarea elements don't support the type property or attribute.\n        if (!this._isTextarea && getSupportedInputTypes().has(this._type)) {\n            this._elementRef.nativeElement.type = this._type;\n        }\n    }\n    /** An object used to control when error messages are shown. */\n    get errorStateMatcher() {\n        return this._errorStateTracker.matcher;\n    }\n    set errorStateMatcher(value) {\n        this._errorStateTracker.matcher = value;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get value() {\n        return this._inputValueAccessor.value;\n    }\n    set value(value) {\n        if (value !== this.value) {\n            this._inputValueAccessor.value = value;\n            this.stateChanges.next();\n        }\n    }\n    /** Whether the element is readonly. */\n    get readonly() {\n        return this._readonly;\n    }\n    set readonly(value) {\n        this._readonly = coerceBooleanProperty(value);\n    }\n    /** Whether the input is in an error state. */\n    get errorState() {\n        return this._errorStateTracker.errorState;\n    }\n    set errorState(value) {\n        this._errorStateTracker.errorState = value;\n    }\n    constructor(_elementRef, _platform, ngControl, parentForm, parentFormGroup, defaultErrorStateMatcher, inputValueAccessor, _autofillMonitor, ngZone, \n    // TODO: Remove this once the legacy appearance has been removed. We only need\n    // to inject the form field for determining whether the placeholder has been promoted.\n    _formField) {\n        this._elementRef = _elementRef;\n        this._platform = _platform;\n        this.ngControl = ngControl;\n        this._autofillMonitor = _autofillMonitor;\n        this._formField = _formField;\n        this._uid = `mat-input-${nextUniqueId++}`;\n        /**\n         * Implemented as part of MatFormFieldControl.\n         * @docs-private\n         */\n        this.focused = false;\n        /**\n         * Implemented as part of MatFormFieldControl.\n         * @docs-private\n         */\n        this.stateChanges = new Subject();\n        /**\n         * Implemented as part of MatFormFieldControl.\n         * @docs-private\n         */\n        this.controlType = 'mat-input';\n        /**\n         * Implemented as part of MatFormFieldControl.\n         * @docs-private\n         */\n        this.autofilled = false;\n        this._disabled = false;\n        this._type = 'text';\n        this._readonly = false;\n        this._neverEmptyInputTypes = [\n            'date',\n            'datetime',\n            'datetime-local',\n            'month',\n            'time',\n            'week',\n        ].filter(t => getSupportedInputTypes().has(t));\n        this._iOSKeyupListener = (event) => {\n            const el = event.target;\n            // Note: We specifically check for 0, rather than `!el.selectionStart`, because the two\n            // indicate different things. If the value is 0, it means that the caret is at the start\n            // of the input, whereas a value of `null` means that the input doesn't support\n            // manipulating the selection range. Inputs that don't support setting the selection range\n            // will throw an error so we want to avoid calling `setSelectionRange` on them. See:\n            // https://html.spec.whatwg.org/multipage/input.html#do-not-apply\n            if (!el.value && el.selectionStart === 0 && el.selectionEnd === 0) {\n                // Note: Just setting `0, 0` doesn't fix the issue. Setting\n                // `1, 1` fixes it for the first time that you type text and\n                // then hold delete. Toggling to `1, 1` and then back to\n                // `0, 0` seems to completely fix it.\n                el.setSelectionRange(1, 1);\n                el.setSelectionRange(0, 0);\n            }\n        };\n        const element = this._elementRef.nativeElement;\n        const nodeName = element.nodeName.toLowerCase();\n        // If no input value accessor was explicitly specified, use the element as the input value\n        // accessor.\n        this._inputValueAccessor = inputValueAccessor || element;\n        this._previousNativeValue = this.value;\n        // Force setter to be called in case id was not specified.\n        this.id = this.id;\n        // On some versions of iOS the caret gets stuck in the wrong place when holding down the delete\n        // key. In order to get around this we need to \"jiggle\" the caret loose. Since this bug only\n        // exists on iOS, we only bother to install the listener on iOS.\n        if (_platform.IOS) {\n            ngZone.runOutsideAngular(() => {\n                _elementRef.nativeElement.addEventListener('keyup', this._iOSKeyupListener);\n            });\n        }\n        this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, ngControl, parentFormGroup, parentForm, this.stateChanges);\n        this._isServer = !this._platform.isBrowser;\n        this._isNativeSelect = nodeName === 'select';\n        this._isTextarea = nodeName === 'textarea';\n        this._isInFormField = !!_formField;\n        if (this._isNativeSelect) {\n            this.controlType = element.multiple\n                ? 'mat-native-select-multiple'\n                : 'mat-native-select';\n        }\n    }\n    ngAfterViewInit() {\n        if (this._platform.isBrowser) {\n            this._autofillMonitor.monitor(this._elementRef.nativeElement).subscribe(event => {\n                this.autofilled = event.isAutofilled;\n                this.stateChanges.next();\n            });\n        }\n    }\n    ngOnChanges() {\n        this.stateChanges.next();\n    }\n    ngOnDestroy() {\n        this.stateChanges.complete();\n        if (this._platform.isBrowser) {\n            this._autofillMonitor.stopMonitoring(this._elementRef.nativeElement);\n        }\n        if (this._platform.IOS) {\n            this._elementRef.nativeElement.removeEventListener('keyup', this._iOSKeyupListener);\n        }\n    }\n    ngDoCheck() {\n        if (this.ngControl) {\n            // We need to re-evaluate this on every change detection cycle, because there are some\n            // error triggers that we can't subscribe to (e.g. parent form submissions). This means\n            // that whatever logic is in here has to be super lean or we risk destroying the performance.\n            this.updateErrorState();\n            // Since the input isn't a `ControlValueAccessor`, we don't have a good way of knowing when\n            // the disabled state has changed. We can't use the `ngControl.statusChanges`, because it\n            // won't fire if the input is disabled with `emitEvents = false`, despite the input becoming\n            // disabled.\n            if (this.ngControl.disabled !== null && this.ngControl.disabled !== this.disabled) {\n                this.disabled = this.ngControl.disabled;\n                this.stateChanges.next();\n            }\n        }\n        // We need to dirty-check the native element's value, because there are some cases where\n        // we won't be notified when it changes (e.g. the consumer isn't using forms or they're\n        // updating the value using `emitEvent: false`).\n        this._dirtyCheckNativeValue();\n        // We need to dirty-check and set the placeholder attribute ourselves, because whether it's\n        // present or not depends on a query which is prone to \"changed after checked\" errors.\n        this._dirtyCheckPlaceholder();\n    }\n    /** Focuses the input. */\n    focus(options) {\n        this._elementRef.nativeElement.focus(options);\n    }\n    /** Refreshes the error state of the input. */\n    updateErrorState() {\n        this._errorStateTracker.updateErrorState();\n    }\n    /** Callback for the cases where the focused state of the input changes. */\n    _focusChanged(isFocused) {\n        if (isFocused !== this.focused) {\n            this.focused = isFocused;\n            this.stateChanges.next();\n        }\n    }\n    _onInput() {\n        // This is a noop function and is used to let Angular know whenever the value changes.\n        // Angular will run a new change detection each time the `input` event has been dispatched.\n        // It's necessary that Angular recognizes the value change, because when floatingLabel\n        // is set to false and Angular forms aren't used, the placeholder won't recognize the\n        // value changes and will not disappear.\n        // Listening to the input event wouldn't be necessary when the input is using the\n        // FormsModule or ReactiveFormsModule, because Angular forms also listens to input events.\n    }\n    /** Does some manual dirty checking on the native input `value` property. */\n    _dirtyCheckNativeValue() {\n        const newValue = this._elementRef.nativeElement.value;\n        if (this._previousNativeValue !== newValue) {\n            this._previousNativeValue = newValue;\n            this.stateChanges.next();\n        }\n    }\n    /** Does some manual dirty checking on the native input `placeholder` attribute. */\n    _dirtyCheckPlaceholder() {\n        const placeholder = this._getPlaceholder();\n        if (placeholder !== this._previousPlaceholder) {\n            const element = this._elementRef.nativeElement;\n            this._previousPlaceholder = placeholder;\n            placeholder\n                ? element.setAttribute('placeholder', placeholder)\n                : element.removeAttribute('placeholder');\n        }\n    }\n    /** Gets the current placeholder of the form field. */\n    _getPlaceholder() {\n        return this.placeholder || null;\n    }\n    /** Make sure the input is a supported type. */\n    _validateType() {\n        if (MAT_INPUT_INVALID_TYPES.indexOf(this._type) > -1 &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatInputUnsupportedTypeError(this._type);\n        }\n    }\n    /** Checks whether the input type is one of the types that are never empty. */\n    _isNeverEmpty() {\n        return this._neverEmptyInputTypes.indexOf(this._type) > -1;\n    }\n    /** Checks whether the input is invalid based on the native validation. */\n    _isBadInput() {\n        // The `validity` property won't be present on platform-server.\n        let validity = this._elementRef.nativeElement.validity;\n        return validity && validity.badInput;\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get empty() {\n        return (!this._isNeverEmpty() &&\n            !this._elementRef.nativeElement.value &&\n            !this._isBadInput() &&\n            !this.autofilled);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        if (this._isNativeSelect) {\n            // For a single-selection `<select>`, the label should float when the selected option has\n            // a non-empty display value. For a `<select multiple>`, the label *always* floats to avoid\n            // overlapping the label with the options.\n            const selectElement = this._elementRef.nativeElement;\n            const firstOption = selectElement.options[0];\n            // On most browsers the `selectedIndex` will always be 0, however on IE and Edge it'll be\n            // -1 if the `value` is set to something, that isn't in the list of options, at a later point.\n            return (this.focused ||\n                selectElement.multiple ||\n                !this.empty ||\n                !!(selectElement.selectedIndex > -1 && firstOption && firstOption.label));\n        }\n        else {\n            return this.focused || !this.empty;\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        if (ids.length) {\n            this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n        }\n        else {\n            this._elementRef.nativeElement.removeAttribute('aria-describedby');\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n        // Do not re-focus the input element if the element is already focused. Otherwise it can happen\n        // that someone clicks on a time input and the cursor resets to the \"hours\" field while the\n        // \"minutes\" field was actually clicked. See: https://github.com/angular/components/issues/12849\n        if (!this.focused) {\n            this.focus();\n        }\n    }\n    /** Whether the form control is a native select that is displayed inline. */\n    _isInlineSelect() {\n        const element = this._elementRef.nativeElement;\n        return this._isNativeSelect && (element.multiple || element.size > 1);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatInput, deps: [{ token: i0.ElementRef }, { token: i1.Platform }, { token: i2.NgControl, optional: true, self: true }, { token: i2.NgForm, optional: true }, { token: i2.FormGroupDirective, optional: true }, { token: i3.ErrorStateMatcher }, { token: MAT_INPUT_VALUE_ACCESSOR, optional: true, self: true }, { token: i4.AutofillMonitor }, { token: i0.NgZone }, { token: MAT_FORM_FIELD, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatInput, isStandalone: true, selector: \"input[matInput], textarea[matInput], select[matNativeControl],\\n      input[matNativeControl], textarea[matNativeControl]\", inputs: { disabled: \"disabled\", id: \"id\", placeholder: \"placeholder\", name: \"name\", required: \"required\", type: \"type\", errorStateMatcher: \"errorStateMatcher\", userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"], value: \"value\", readonly: \"readonly\" }, host: { listeners: { \"focus\": \"_focusChanged(true)\", \"blur\": \"_focusChanged(false)\", \"input\": \"_onInput()\" }, properties: { \"class.mat-input-server\": \"_isServer\", \"class.mat-mdc-form-field-textarea-control\": \"_isInFormField && _isTextarea\", \"class.mat-mdc-form-field-input-control\": \"_isInFormField\", \"class.mdc-text-field__input\": \"_isInFormField\", \"class.mat-mdc-native-select-inline\": \"_isInlineSelect()\", \"id\": \"id\", \"disabled\": \"disabled\", \"required\": \"required\", \"attr.name\": \"name || null\", \"attr.readonly\": \"readonly && !_isNativeSelect || null\", \"attr.aria-invalid\": \"(empty && required) ? null : errorState\", \"attr.aria-required\": \"required\", \"attr.id\": \"id\" }, classAttribute: \"mat-mdc-input-element\" }, providers: [{ provide: MatFormFieldControl, useExisting: MatInput }], exportAs: [\"matInput\"], usesOnChanges: true, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatInput, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `input[matInput], textarea[matInput], select[matNativeControl],\n      input[matNativeControl], textarea[matNativeControl]`,\n                    exportAs: 'matInput',\n                    host: {\n                        'class': 'mat-mdc-input-element',\n                        // The BaseMatInput parent class adds `mat-input-element`, `mat-form-field-control` and\n                        // `mat-form-field-autofill-control` to the CSS class list, but this should not be added for\n                        // this MDC equivalent input.\n                        '[class.mat-input-server]': '_isServer',\n                        '[class.mat-mdc-form-field-textarea-control]': '_isInFormField && _isTextarea',\n                        '[class.mat-mdc-form-field-input-control]': '_isInFormField',\n                        '[class.mdc-text-field__input]': '_isInFormField',\n                        '[class.mat-mdc-native-select-inline]': '_isInlineSelect()',\n                        // Native input properties that are overwritten by Angular inputs need to be synced with\n                        // the native input element. Otherwise property bindings for those don't work.\n                        '[id]': 'id',\n                        '[disabled]': 'disabled',\n                        '[required]': 'required',\n                        '[attr.name]': 'name || null',\n                        '[attr.readonly]': 'readonly && !_isNativeSelect || null',\n                        // Only mark the input as invalid for assistive technology if it has a value since the\n                        // state usually overlaps with `aria-required` when the input is empty and can be redundant.\n                        '[attr.aria-invalid]': '(empty && required) ? null : errorState',\n                        '[attr.aria-required]': 'required',\n                        // Native input properties that are overwritten by Angular inputs need to be synced with\n                        // the native input element. Otherwise property bindings for those don't work.\n                        '[attr.id]': 'id',\n                        '(focus)': '_focusChanged(true)',\n                        '(blur)': '_focusChanged(false)',\n                        '(input)': '_onInput()',\n                    },\n                    providers: [{ provide: MatFormFieldControl, useExisting: MatInput }],\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i1.Platform }, { type: i2.NgControl, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }] }, { type: i2.NgForm, decorators: [{\n                    type: Optional\n                }] }, { type: i2.FormGroupDirective, decorators: [{\n                    type: Optional\n                }] }, { type: i3.ErrorStateMatcher }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Self\n                }, {\n                    type: Inject,\n                    args: [MAT_INPUT_VALUE_ACCESSOR]\n                }] }, { type: i4.AutofillMonitor }, { type: i0.NgZone }, { type: i5.MatFormField, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_FORM_FIELD]\n                }] }], propDecorators: { disabled: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], placeholder: [{\n                type: Input\n            }], name: [{\n                type: Input\n            }], required: [{\n                type: Input\n            }], type: [{\n                type: Input\n            }], errorStateMatcher: [{\n                type: Input\n            }], userAriaDescribedBy: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], value: [{\n                type: Input\n            }], readonly: [{\n                type: Input\n            }] } });\n\nclass MatInputModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatInputModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatInputModule, imports: [MatCommonModule, MatFormFieldModule, MatInput], exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatInputModule, imports: [MatCommonModule, MatFormFieldModule, MatFormFieldModule, TextFieldModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatInputModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatFormFieldModule, MatInput],\n                    exports: [MatInput, MatFormFieldModule, TextFieldModule, MatCommonModule],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_INPUT_VALUE_ACCESSOR, MatInput, MatInputModule, getMatInputUnsupportedTypeError };\n", "import { Overlay, CdkOverlayOrigin, CdkConnectedOverlay, OverlayModule } from '@angular/cdk/overlay';\nimport { NgClass, CommonModule } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, EventEmitter, booleanAttribute, numberAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Self, Attribute, ContentChildren, ContentChild, Input, ViewChild, Output, Directive, NgModule } from '@angular/core';\nimport * as i2 from '@angular/material/core';\nimport { _countGroupLabelsBeforeOption, _getOptionScrollPosition, _ErrorStateTracker, MAT_OPTION_PARENT_COMPONENT, MatOption, MAT_OPTGROUP, MatOptionModule, MatCommonModule } from '@angular/material/core';\nexport { MatOptgroup, MatOption } from '@angular/material/core';\nimport * as i6 from '@angular/material/form-field';\nimport { MAT_FORM_FIELD, MatFormFieldControl, MatFormFieldModule } from '@angular/material/form-field';\nexport { Mat<PERSON><PERSON>r, MatFormField, MatHint, MatLabel, MatPrefix, MatSuffix } from '@angular/material/form-field';\nimport * as i1 from '@angular/cdk/scrolling';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport * as i5 from '@angular/cdk/a11y';\nimport { removeAriaReferencedId, addAriaReferencedId, ActiveDescendantKeyManager } from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/bidi';\nimport { SelectionModel } from '@angular/cdk/collections';\nimport { DOWN_ARROW, UP_ARROW, LEFT_ARROW, RIGHT_ARROW, ENTER, SPACE, hasModifierKey, A } from '@angular/cdk/keycodes';\nimport * as i4 from '@angular/forms';\nimport { Validators } from '@angular/forms';\nimport { Subject, defer, merge } from 'rxjs';\nimport { startWith, switchMap, filter, map, distinctUntilChanged, takeUntil, take } from 'rxjs/operators';\nimport { trigger, transition, query, animateChild, state, style, animate } from '@angular/animations';\n\n/**\n * The following are all the animations for the mat-select component, with each\n * const containing the metadata for one animation.\n *\n * The values below match the implementation of the AngularJS Material mat-select animation.\n * @docs-private\n */\nconst matSelectAnimations = {\n    /**\n     * This animation ensures the select's overlay panel animation (transformPanel) is called when\n     * closing the select.\n     * This is needed due to https://github.com/angular/angular/issues/23302\n     */\n    transformPanelWrap: trigger('transformPanelWrap', [\n        transition('* => void', query('@transformPanel', [animateChild()], { optional: true })),\n    ]),\n    /** This animation transforms the select's overlay panel on and off the page. */\n    transformPanel: trigger('transformPanel', [\n        state('void', style({\n            opacity: 0,\n            transform: 'scale(1, 0.8)',\n        })),\n        transition('void => showing', animate('120ms cubic-bezier(0, 0, 0.2, 1)', style({\n            opacity: 1,\n            transform: 'scale(1, 1)',\n        }))),\n        transition('* => void', animate('100ms linear', style({ opacity: 0 }))),\n    ]),\n};\n\n// Note that these have been copied over verbatim from\n// `material/select` so that we don't have to expose them publicly.\n/**\n * Returns an exception to be thrown when attempting to change a select's `multiple` option\n * after initialization.\n * @docs-private\n */\nfunction getMatSelectDynamicMultipleError() {\n    return Error('Cannot change `multiple` mode of select after initialization.');\n}\n/**\n * Returns an exception to be thrown when attempting to assign a non-array value to a select\n * in `multiple` mode. Note that `undefined` and `null` are still valid values to allow for\n * resetting the value.\n * @docs-private\n */\nfunction getMatSelectNonArrayValueError() {\n    return Error('Value must be an array in multiple-selection mode.');\n}\n/**\n * Returns an exception to be thrown when assigning a non-function value to the comparator\n * used to determine if a value corresponds to an option. Note that whether the function\n * actually takes two values and returns a boolean is not checked.\n */\nfunction getMatSelectNonFunctionValueError() {\n    return Error('`compareWith` must be a function.');\n}\n\nlet nextUniqueId = 0;\n/** Injection token that determines the scroll handling while a select is open. */\nconst MAT_SELECT_SCROLL_STRATEGY = new InjectionToken('mat-select-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.reposition();\n    },\n});\n/** @docs-private */\nfunction MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition();\n}\n/** Injection token that can be used to provide the default options the select module. */\nconst MAT_SELECT_CONFIG = new InjectionToken('MAT_SELECT_CONFIG');\n/** @docs-private */\nconst MAT_SELECT_SCROLL_STRATEGY_PROVIDER = {\n    provide: MAT_SELECT_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n/**\n * Injection token that can be used to reference instances of `MatSelectTrigger`. It serves as\n * alternative token to the actual `MatSelectTrigger` class which could cause unnecessary\n * retention of the class and its directive metadata.\n */\nconst MAT_SELECT_TRIGGER = new InjectionToken('MatSelectTrigger');\n/** Change event object that is emitted when the select value has changed. */\nclass MatSelectChange {\n    constructor(\n    /** Reference to the select that emitted the change event. */\n    source, \n    /** Current value of the select that emitted the event. */\n    value) {\n        this.source = source;\n        this.value = value;\n    }\n}\nclass MatSelect {\n    /** Scrolls a particular option into the view. */\n    _scrollOptionIntoView(index) {\n        const option = this.options.toArray()[index];\n        if (option) {\n            const panel = this.panel.nativeElement;\n            const labelCount = _countGroupLabelsBeforeOption(index, this.options, this.optionGroups);\n            const element = option._getHostElement();\n            if (index === 0 && labelCount === 1) {\n                // If we've got one group label before the option and we're at the top option,\n                // scroll the list to the top. This is better UX than scrolling the list to the\n                // top of the option, because it allows the user to read the top group's label.\n                panel.scrollTop = 0;\n            }\n            else {\n                panel.scrollTop = _getOptionScrollPosition(element.offsetTop, element.offsetHeight, panel.scrollTop, panel.offsetHeight);\n            }\n        }\n    }\n    /** Called when the panel has been opened and the overlay has settled on its final position. */\n    _positioningSettled() {\n        this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n    }\n    /** Creates a change event object that should be emitted by the select. */\n    _getChangeEvent(value) {\n        return new MatSelectChange(this, value);\n    }\n    /** Whether the select is focused. */\n    get focused() {\n        return this._focused || this._panelOpen;\n    }\n    /** Whether checkmark indicator for single-selection options is hidden. */\n    get hideSingleSelectionIndicator() {\n        return this._hideSingleSelectionIndicator;\n    }\n    set hideSingleSelectionIndicator(value) {\n        this._hideSingleSelectionIndicator = value;\n        this._syncParentProperties();\n    }\n    /** Placeholder to be shown if no value has been selected. */\n    get placeholder() {\n        return this._placeholder;\n    }\n    set placeholder(value) {\n        this._placeholder = value;\n        this.stateChanges.next();\n    }\n    /** Whether the component is required. */\n    get required() {\n        return this._required ?? this.ngControl?.control?.hasValidator(Validators.required) ?? false;\n    }\n    set required(value) {\n        this._required = value;\n        this.stateChanges.next();\n    }\n    /** Whether the user should be allowed to select multiple options. */\n    get multiple() {\n        return this._multiple;\n    }\n    set multiple(value) {\n        if (this._selectionModel && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectDynamicMultipleError();\n        }\n        this._multiple = value;\n    }\n    /**\n     * Function to compare the option values with the selected values. The first argument\n     * is a value from an option. The second is a value from the selection. A boolean\n     * should be returned.\n     */\n    get compareWith() {\n        return this._compareWith;\n    }\n    set compareWith(fn) {\n        if (typeof fn !== 'function' && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw getMatSelectNonFunctionValueError();\n        }\n        this._compareWith = fn;\n        if (this._selectionModel) {\n            // A different comparator means the selection could change.\n            this._initializeSelection();\n        }\n    }\n    /** Value of the select control. */\n    get value() {\n        return this._value;\n    }\n    set value(newValue) {\n        const hasAssigned = this._assignValue(newValue);\n        if (hasAssigned) {\n            this._onChange(newValue);\n        }\n    }\n    /** Object used to control when error messages are shown. */\n    get errorStateMatcher() {\n        return this._errorStateTracker.matcher;\n    }\n    set errorStateMatcher(value) {\n        this._errorStateTracker.matcher = value;\n    }\n    /** Unique id of the element. */\n    get id() {\n        return this._id;\n    }\n    set id(value) {\n        this._id = value || this._uid;\n        this.stateChanges.next();\n    }\n    /** Whether the select is in an error state. */\n    get errorState() {\n        return this._errorStateTracker.errorState;\n    }\n    set errorState(value) {\n        this._errorStateTracker.errorState = value;\n    }\n    constructor(_viewportRuler, _changeDetectorRef, \n    /**\n     * @deprecated Unused param, will be removed.\n     * @breaking-change 19.0.0\n     */\n    _unusedNgZone, defaultErrorStateMatcher, _elementRef, _dir, parentForm, parentFormGroup, _parentFormField, ngControl, tabIndex, scrollStrategyFactory, _liveAnnouncer, _defaultOptions) {\n        this._viewportRuler = _viewportRuler;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elementRef = _elementRef;\n        this._dir = _dir;\n        this._parentFormField = _parentFormField;\n        this.ngControl = ngControl;\n        this._liveAnnouncer = _liveAnnouncer;\n        this._defaultOptions = _defaultOptions;\n        /**\n         * This position config ensures that the top \"start\" corner of the overlay\n         * is aligned with with the top \"start\" of the origin by default (overlapping\n         * the trigger completely). If the panel cannot fit below the trigger, it\n         * will fall back to a position above the trigger.\n         */\n        this._positions = [\n            {\n                originX: 'start',\n                originY: 'bottom',\n                overlayX: 'start',\n                overlayY: 'top',\n            },\n            {\n                originX: 'end',\n                originY: 'bottom',\n                overlayX: 'end',\n                overlayY: 'top',\n            },\n            {\n                originX: 'start',\n                originY: 'top',\n                overlayX: 'start',\n                overlayY: 'bottom',\n                panelClass: 'mat-mdc-select-panel-above',\n            },\n            {\n                originX: 'end',\n                originY: 'top',\n                overlayX: 'end',\n                overlayY: 'bottom',\n                panelClass: 'mat-mdc-select-panel-above',\n            },\n        ];\n        /** Whether or not the overlay panel is open. */\n        this._panelOpen = false;\n        /** Comparison function to specify which option is displayed. Defaults to object equality. */\n        this._compareWith = (o1, o2) => o1 === o2;\n        /** Unique id for this input. */\n        this._uid = `mat-select-${nextUniqueId++}`;\n        /** Current `aria-labelledby` value for the select trigger. */\n        this._triggerAriaLabelledBy = null;\n        /** Emits whenever the component is destroyed. */\n        this._destroy = new Subject();\n        /**\n         * Emits whenever the component state changes and should cause the parent\n         * form-field to update. Implemented as part of `MatFormFieldControl`.\n         * @docs-private\n         */\n        this.stateChanges = new Subject();\n        /**\n         * Disable the automatic labeling to avoid issues like #27241.\n         * @docs-private\n         */\n        this.disableAutomaticLabeling = true;\n        /** `View -> model callback called when value changes` */\n        this._onChange = () => { };\n        /** `View -> model callback called when select has been touched` */\n        this._onTouched = () => { };\n        /** ID for the DOM node containing the select's value. */\n        this._valueId = `mat-select-value-${nextUniqueId++}`;\n        /** Emits when the panel element is finished transforming in. */\n        this._panelDoneAnimatingStream = new Subject();\n        this._overlayPanelClass = this._defaultOptions?.overlayPanelClass || '';\n        this._focused = false;\n        /** A name for this control that can be used by `mat-form-field`. */\n        this.controlType = 'mat-select';\n        /** Whether the select is disabled. */\n        this.disabled = false;\n        /** Whether ripples in the select are disabled. */\n        this.disableRipple = false;\n        /** Tab index of the select. */\n        this.tabIndex = 0;\n        this._hideSingleSelectionIndicator = this._defaultOptions?.hideSingleSelectionIndicator ?? false;\n        this._multiple = false;\n        /** Whether to center the active option over the trigger. */\n        this.disableOptionCentering = this._defaultOptions?.disableOptionCentering ?? false;\n        /** Aria label of the select. */\n        this.ariaLabel = '';\n        /**\n         * Width of the panel. If set to `auto`, the panel will match the trigger width.\n         * If set to null or an empty string, the panel will grow to match the longest option's text.\n         */\n        this.panelWidth = this._defaultOptions && typeof this._defaultOptions.panelWidth !== 'undefined'\n            ? this._defaultOptions.panelWidth\n            : 'auto';\n        this._initialized = new Subject();\n        /** Combined stream of all of the child options' change events. */\n        this.optionSelectionChanges = defer(() => {\n            const options = this.options;\n            if (options) {\n                return options.changes.pipe(startWith(options), switchMap(() => merge(...options.map(option => option.onSelectionChange))));\n            }\n            return this._initialized.pipe(switchMap(() => this.optionSelectionChanges));\n        });\n        /** Event emitted when the select panel has been toggled. */\n        this.openedChange = new EventEmitter();\n        /** Event emitted when the select has been opened. */\n        this._openedStream = this.openedChange.pipe(filter(o => o), map(() => { }));\n        /** Event emitted when the select has been closed. */\n        this._closedStream = this.openedChange.pipe(filter(o => !o), map(() => { }));\n        /** Event emitted when the selected value has been changed by the user. */\n        this.selectionChange = new EventEmitter();\n        /**\n         * Event that emits whenever the raw value of the select changes. This is here primarily\n         * to facilitate the two-way binding for the `value` input.\n         * @docs-private\n         */\n        this.valueChange = new EventEmitter();\n        /**\n         * Track which modal we have modified the `aria-owns` attribute of. When the combobox trigger is\n         * inside an aria-modal, we apply aria-owns to the parent modal with the `id` of the options\n         * panel. Track the modal we have changed so we can undo the changes on destroy.\n         */\n        this._trackedModal = null;\n        // `skipPredicate` determines if key manager should avoid putting a given option in the tab\n        // order. Allow disabled list items to receive focus via keyboard to align with WAI ARIA\n        // recommendation.\n        //\n        // Normally WAI ARIA's instructions are to exclude disabled items from the tab order, but it\n        // makes a few exceptions for compound widgets.\n        //\n        // From [Developing a Keyboard Interface](\n        // https://www.w3.org/WAI/ARIA/apg/practices/keyboard-interface/):\n        //   \"For the following composite widget elements, keep them focusable when disabled: Options in a\n        //   Listbox...\"\n        //\n        // The user can focus disabled options using the keyboard, but the user cannot click disabled\n        // options.\n        this._skipPredicate = (option) => {\n            if (this.panelOpen) {\n                // Support keyboard focusing disabled options in an ARIA listbox.\n                return false;\n            }\n            // When the panel is closed, skip over disabled options. Support options via the UP/DOWN arrow\n            // keys on a closed select. ARIA listbox interaction pattern is less relevant when the panel is\n            // closed.\n            return option.disabled;\n        };\n        if (this.ngControl) {\n            // Note: we provide the value accessor through here, instead of\n            // the `providers` to avoid running into a circular import.\n            this.ngControl.valueAccessor = this;\n        }\n        // Note that we only want to set this when the defaults pass it in, otherwise it should\n        // stay as `undefined` so that it falls back to the default in the key manager.\n        if (_defaultOptions?.typeaheadDebounceInterval != null) {\n            this.typeaheadDebounceInterval = _defaultOptions.typeaheadDebounceInterval;\n        }\n        this._errorStateTracker = new _ErrorStateTracker(defaultErrorStateMatcher, ngControl, parentFormGroup, parentForm, this.stateChanges);\n        this._scrollStrategyFactory = scrollStrategyFactory;\n        this._scrollStrategy = this._scrollStrategyFactory();\n        this.tabIndex = parseInt(tabIndex) || 0;\n        // Force setter to be called in case id was not specified.\n        this.id = this.id;\n    }\n    ngOnInit() {\n        this._selectionModel = new SelectionModel(this.multiple);\n        this.stateChanges.next();\n        // We need `distinctUntilChanged` here, because some browsers will\n        // fire the animation end event twice for the same animation. See:\n        // https://github.com/angular/angular/issues/24084\n        this._panelDoneAnimatingStream\n            .pipe(distinctUntilChanged(), takeUntil(this._destroy))\n            .subscribe(() => this._panelDoneAnimating(this.panelOpen));\n        this._viewportRuler\n            .change()\n            .pipe(takeUntil(this._destroy))\n            .subscribe(() => {\n            if (this.panelOpen) {\n                this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n                this._changeDetectorRef.detectChanges();\n            }\n        });\n    }\n    ngAfterContentInit() {\n        this._initialized.next();\n        this._initialized.complete();\n        this._initKeyManager();\n        this._selectionModel.changed.pipe(takeUntil(this._destroy)).subscribe(event => {\n            event.added.forEach(option => option.select());\n            event.removed.forEach(option => option.deselect());\n        });\n        this.options.changes.pipe(startWith(null), takeUntil(this._destroy)).subscribe(() => {\n            this._resetOptions();\n            this._initializeSelection();\n        });\n    }\n    ngDoCheck() {\n        const newAriaLabelledby = this._getTriggerAriaLabelledby();\n        const ngControl = this.ngControl;\n        // We have to manage setting the `aria-labelledby` ourselves, because part of its value\n        // is computed as a result of a content query which can cause this binding to trigger a\n        // \"changed after checked\" error.\n        if (newAriaLabelledby !== this._triggerAriaLabelledBy) {\n            const element = this._elementRef.nativeElement;\n            this._triggerAriaLabelledBy = newAriaLabelledby;\n            if (newAriaLabelledby) {\n                element.setAttribute('aria-labelledby', newAriaLabelledby);\n            }\n            else {\n                element.removeAttribute('aria-labelledby');\n            }\n        }\n        if (ngControl) {\n            // The disabled state might go out of sync if the form group is swapped out. See #17860.\n            if (this._previousControl !== ngControl.control) {\n                if (this._previousControl !== undefined &&\n                    ngControl.disabled !== null &&\n                    ngControl.disabled !== this.disabled) {\n                    this.disabled = ngControl.disabled;\n                }\n                this._previousControl = ngControl.control;\n            }\n            this.updateErrorState();\n        }\n    }\n    ngOnChanges(changes) {\n        // Updating the disabled state is handled by the input, but we need to additionally let\n        // the parent form field know to run change detection when the disabled state changes.\n        if (changes['disabled'] || changes['userAriaDescribedBy']) {\n            this.stateChanges.next();\n        }\n        if (changes['typeaheadDebounceInterval'] && this._keyManager) {\n            this._keyManager.withTypeAhead(this.typeaheadDebounceInterval);\n        }\n    }\n    ngOnDestroy() {\n        this._keyManager?.destroy();\n        this._destroy.next();\n        this._destroy.complete();\n        this.stateChanges.complete();\n        this._clearFromModal();\n    }\n    /** Toggles the overlay panel open or closed. */\n    toggle() {\n        this.panelOpen ? this.close() : this.open();\n    }\n    /** Opens the overlay panel. */\n    open() {\n        if (!this._canOpen()) {\n            return;\n        }\n        // It's important that we read this as late as possible, because doing so earlier will\n        // return a different element since it's based on queries in the form field which may\n        // not have run yet. Also this needs to be assigned before we measure the overlay width.\n        if (this._parentFormField) {\n            this._preferredOverlayOrigin = this._parentFormField.getConnectedOverlayOrigin();\n        }\n        this._overlayWidth = this._getOverlayWidth(this._preferredOverlayOrigin);\n        this._applyModalPanelOwnership();\n        this._panelOpen = true;\n        this._keyManager.withHorizontalOrientation(null);\n        this._highlightCorrectOption();\n        this._changeDetectorRef.markForCheck();\n        // Required for the MDC form field to pick up when the overlay has been opened.\n        this.stateChanges.next();\n    }\n    /**\n     * If the autocomplete trigger is inside of an `aria-modal` element, connect\n     * that modal to the options panel with `aria-owns`.\n     *\n     * For some browser + screen reader combinations, when navigation is inside\n     * of an `aria-modal` element, the screen reader treats everything outside\n     * of that modal as hidden or invisible.\n     *\n     * This causes a problem when the combobox trigger is _inside_ of a modal, because the\n     * options panel is rendered _outside_ of that modal, preventing screen reader navigation\n     * from reaching the panel.\n     *\n     * We can work around this issue by applying `aria-owns` to the modal with the `id` of\n     * the options panel. This effectively communicates to assistive technology that the\n     * options panel is part of the same interaction as the modal.\n     *\n     * At time of this writing, this issue is present in VoiceOver.\n     * See https://github.com/angular/components/issues/20694\n     */\n    _applyModalPanelOwnership() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with\n        // the `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const modal = this._elementRef.nativeElement.closest('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        if (!modal) {\n            // Most commonly, the autocomplete trigger is not inside a modal.\n            return;\n        }\n        const panelId = `${this.id}-panel`;\n        if (this._trackedModal) {\n            removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        }\n        addAriaReferencedId(modal, 'aria-owns', panelId);\n        this._trackedModal = modal;\n    }\n    /** Clears the reference to the listbox overlay element from the modal it was added to. */\n    _clearFromModal() {\n        if (!this._trackedModal) {\n            // Most commonly, the autocomplete trigger is not used inside a modal.\n            return;\n        }\n        const panelId = `${this.id}-panel`;\n        removeAriaReferencedId(this._trackedModal, 'aria-owns', panelId);\n        this._trackedModal = null;\n    }\n    /** Closes the overlay panel and focuses the host element. */\n    close() {\n        if (this._panelOpen) {\n            this._panelOpen = false;\n            this._keyManager.withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr');\n            this._changeDetectorRef.markForCheck();\n            this._onTouched();\n            // Required for the MDC form field to pick up when the overlay has been closed.\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Sets the select's value. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param value New value to be written to the model.\n     */\n    writeValue(value) {\n        this._assignValue(value);\n    }\n    /**\n     * Saves a callback function to be invoked when the select's value\n     * changes from user input. Part of the ControlValueAccessor interface\n     * required to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the value changes.\n     */\n    registerOnChange(fn) {\n        this._onChange = fn;\n    }\n    /**\n     * Saves a callback function to be invoked when the select is blurred\n     * by the user. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param fn Callback to be triggered when the component has been touched.\n     */\n    registerOnTouched(fn) {\n        this._onTouched = fn;\n    }\n    /**\n     * Disables the select. Part of the ControlValueAccessor interface required\n     * to integrate with Angular's core forms API.\n     *\n     * @param isDisabled Sets whether the component is disabled.\n     */\n    setDisabledState(isDisabled) {\n        this.disabled = isDisabled;\n        this._changeDetectorRef.markForCheck();\n        this.stateChanges.next();\n    }\n    /** Whether or not the overlay panel is open. */\n    get panelOpen() {\n        return this._panelOpen;\n    }\n    /** The currently selected option. */\n    get selected() {\n        return this.multiple ? this._selectionModel?.selected || [] : this._selectionModel?.selected[0];\n    }\n    /** The value displayed in the trigger. */\n    get triggerValue() {\n        if (this.empty) {\n            return '';\n        }\n        if (this._multiple) {\n            const selectedOptions = this._selectionModel.selected.map(option => option.viewValue);\n            if (this._isRtl()) {\n                selectedOptions.reverse();\n            }\n            // TODO(crisbeto): delimiter should be configurable for proper localization.\n            return selectedOptions.join(', ');\n        }\n        return this._selectionModel.selected[0].viewValue;\n    }\n    /** Refreshes the error state of the select. */\n    updateErrorState() {\n        this._errorStateTracker.updateErrorState();\n    }\n    /** Whether the element is in RTL mode. */\n    _isRtl() {\n        return this._dir ? this._dir.value === 'rtl' : false;\n    }\n    /** Handles all keydown events on the select. */\n    _handleKeydown(event) {\n        if (!this.disabled) {\n            this.panelOpen ? this._handleOpenKeydown(event) : this._handleClosedKeydown(event);\n        }\n    }\n    /** Handles keyboard events while the select is closed. */\n    _handleClosedKeydown(event) {\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW ||\n            keyCode === UP_ARROW ||\n            keyCode === LEFT_ARROW ||\n            keyCode === RIGHT_ARROW;\n        const isOpenKey = keyCode === ENTER || keyCode === SPACE;\n        const manager = this._keyManager;\n        // Open the select on ALT + arrow key to match the native <select>\n        if ((!manager.isTyping() && isOpenKey && !hasModifierKey(event)) ||\n            ((this.multiple || event.altKey) && isArrowKey)) {\n            event.preventDefault(); // prevents the page from scrolling down when pressing space\n            this.open();\n        }\n        else if (!this.multiple) {\n            const previouslySelectedOption = this.selected;\n            manager.onKeydown(event);\n            const selectedOption = this.selected;\n            // Since the value has changed, we need to announce it ourselves.\n            if (selectedOption && previouslySelectedOption !== selectedOption) {\n                // We set a duration on the live announcement, because we want the live element to be\n                // cleared after a while so that users can't navigate to it using the arrow keys.\n                this._liveAnnouncer.announce(selectedOption.viewValue, 10000);\n            }\n        }\n    }\n    /** Handles keyboard events when the selected is open. */\n    _handleOpenKeydown(event) {\n        const manager = this._keyManager;\n        const keyCode = event.keyCode;\n        const isArrowKey = keyCode === DOWN_ARROW || keyCode === UP_ARROW;\n        const isTyping = manager.isTyping();\n        if (isArrowKey && event.altKey) {\n            // Close the select on ALT + arrow key to match the native <select>\n            event.preventDefault();\n            this.close();\n            // Don't do anything in this case if the user is typing,\n            // because the typing sequence can include the space key.\n        }\n        else if (!isTyping &&\n            (keyCode === ENTER || keyCode === SPACE) &&\n            manager.activeItem &&\n            !hasModifierKey(event)) {\n            event.preventDefault();\n            manager.activeItem._selectViaInteraction();\n        }\n        else if (!isTyping && this._multiple && keyCode === A && event.ctrlKey) {\n            event.preventDefault();\n            const hasDeselectedOptions = this.options.some(opt => !opt.disabled && !opt.selected);\n            this.options.forEach(option => {\n                if (!option.disabled) {\n                    hasDeselectedOptions ? option.select() : option.deselect();\n                }\n            });\n        }\n        else {\n            const previouslyFocusedIndex = manager.activeItemIndex;\n            manager.onKeydown(event);\n            if (this._multiple &&\n                isArrowKey &&\n                event.shiftKey &&\n                manager.activeItem &&\n                manager.activeItemIndex !== previouslyFocusedIndex) {\n                manager.activeItem._selectViaInteraction();\n            }\n        }\n    }\n    _onFocus() {\n        if (!this.disabled) {\n            this._focused = true;\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Calls the touched callback only if the panel is closed. Otherwise, the trigger will\n     * \"blur\" to the panel when it opens, causing a false positive.\n     */\n    _onBlur() {\n        this._focused = false;\n        this._keyManager?.cancelTypeahead();\n        if (!this.disabled && !this.panelOpen) {\n            this._onTouched();\n            this._changeDetectorRef.markForCheck();\n            this.stateChanges.next();\n        }\n    }\n    /**\n     * Callback that is invoked when the overlay panel has been attached.\n     */\n    _onAttached() {\n        this._overlayDir.positionChange.pipe(take(1)).subscribe(() => {\n            this._changeDetectorRef.detectChanges();\n            this._positioningSettled();\n        });\n    }\n    /** Returns the theme to be used on the panel. */\n    _getPanelTheme() {\n        return this._parentFormField ? `mat-${this._parentFormField.color}` : '';\n    }\n    /** Whether the select has a value. */\n    get empty() {\n        return !this._selectionModel || this._selectionModel.isEmpty();\n    }\n    _initializeSelection() {\n        // Defer setting the value in order to avoid the \"Expression\n        // has changed after it was checked\" errors from Angular.\n        Promise.resolve().then(() => {\n            if (this.ngControl) {\n                this._value = this.ngControl.value;\n            }\n            this._setSelectionByValue(this._value);\n            this.stateChanges.next();\n        });\n    }\n    /**\n     * Sets the selected option based on a value. If no option can be\n     * found with the designated value, the select trigger is cleared.\n     */\n    _setSelectionByValue(value) {\n        this.options.forEach(option => option.setInactiveStyles());\n        this._selectionModel.clear();\n        if (this.multiple && value) {\n            if (!Array.isArray(value) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throw getMatSelectNonArrayValueError();\n            }\n            value.forEach((currentValue) => this._selectOptionByValue(currentValue));\n            this._sortValues();\n        }\n        else {\n            const correspondingOption = this._selectOptionByValue(value);\n            // Shift focus to the active item. Note that we shouldn't do this in multiple\n            // mode, because we don't know what option the user interacted with last.\n            if (correspondingOption) {\n                this._keyManager.updateActiveItem(correspondingOption);\n            }\n            else if (!this.panelOpen) {\n                // Otherwise reset the highlighted option. Note that we only want to do this while\n                // closed, because doing it while open can shift the user's focus unnecessarily.\n                this._keyManager.updateActiveItem(-1);\n            }\n        }\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Finds and selects and option based on its value.\n     * @returns Option that has the corresponding value.\n     */\n    _selectOptionByValue(value) {\n        const correspondingOption = this.options.find((option) => {\n            // Skip options that are already in the model. This allows us to handle cases\n            // where the same primitive value is selected multiple times.\n            if (this._selectionModel.isSelected(option)) {\n                return false;\n            }\n            try {\n                // Treat null as a special reset value.\n                return option.value != null && this._compareWith(option.value, value);\n            }\n            catch (error) {\n                if (typeof ngDevMode === 'undefined' || ngDevMode) {\n                    // Notify developers of errors in their comparator.\n                    console.warn(error);\n                }\n                return false;\n            }\n        });\n        if (correspondingOption) {\n            this._selectionModel.select(correspondingOption);\n        }\n        return correspondingOption;\n    }\n    /** Assigns a specific value to the select. Returns whether the value has changed. */\n    _assignValue(newValue) {\n        // Always re-assign an array, because it might have been mutated.\n        if (newValue !== this._value || (this._multiple && Array.isArray(newValue))) {\n            if (this.options) {\n                this._setSelectionByValue(newValue);\n            }\n            this._value = newValue;\n            return true;\n        }\n        return false;\n    }\n    /** Gets how wide the overlay panel should be. */\n    _getOverlayWidth(preferredOrigin) {\n        if (this.panelWidth === 'auto') {\n            const refToMeasure = preferredOrigin instanceof CdkOverlayOrigin\n                ? preferredOrigin.elementRef\n                : preferredOrigin || this._elementRef;\n            return refToMeasure.nativeElement.getBoundingClientRect().width;\n        }\n        return this.panelWidth === null ? '' : this.panelWidth;\n    }\n    /** Syncs the parent state with the individual options. */\n    _syncParentProperties() {\n        if (this.options) {\n            for (const option of this.options) {\n                option._changeDetectorRef.markForCheck();\n            }\n        }\n    }\n    /** Sets up a key manager to listen to keyboard events on the overlay panel. */\n    _initKeyManager() {\n        this._keyManager = new ActiveDescendantKeyManager(this.options)\n            .withTypeAhead(this.typeaheadDebounceInterval)\n            .withVerticalOrientation()\n            .withHorizontalOrientation(this._isRtl() ? 'rtl' : 'ltr')\n            .withHomeAndEnd()\n            .withPageUpDown()\n            .withAllowedModifierKeys(['shiftKey'])\n            .skipPredicate(this._skipPredicate);\n        this._keyManager.tabOut.subscribe(() => {\n            if (this.panelOpen) {\n                // Select the active item when tabbing away. This is consistent with how the native\n                // select behaves. Note that we only want to do this in single selection mode.\n                if (!this.multiple && this._keyManager.activeItem) {\n                    this._keyManager.activeItem._selectViaInteraction();\n                }\n                // Restore focus to the trigger before closing. Ensures that the focus\n                // position won't be lost if the user got focus into the overlay.\n                this.focus();\n                this.close();\n            }\n        });\n        this._keyManager.change.subscribe(() => {\n            if (this._panelOpen && this.panel) {\n                this._scrollOptionIntoView(this._keyManager.activeItemIndex || 0);\n            }\n            else if (!this._panelOpen && !this.multiple && this._keyManager.activeItem) {\n                this._keyManager.activeItem._selectViaInteraction();\n            }\n        });\n    }\n    /** Drops current option subscriptions and IDs and resets from scratch. */\n    _resetOptions() {\n        const changedOrDestroyed = merge(this.options.changes, this._destroy);\n        this.optionSelectionChanges.pipe(takeUntil(changedOrDestroyed)).subscribe(event => {\n            this._onSelect(event.source, event.isUserInput);\n            if (event.isUserInput && !this.multiple && this._panelOpen) {\n                this.close();\n                this.focus();\n            }\n        });\n        // Listen to changes in the internal state of the options and react accordingly.\n        // Handles cases like the labels of the selected options changing.\n        merge(...this.options.map(option => option._stateChanges))\n            .pipe(takeUntil(changedOrDestroyed))\n            .subscribe(() => {\n            // `_stateChanges` can fire as a result of a change in the label's DOM value which may\n            // be the result of an expression changing. We have to use `detectChanges` in order\n            // to avoid \"changed after checked\" errors (see #14793).\n            this._changeDetectorRef.detectChanges();\n            this.stateChanges.next();\n        });\n    }\n    /** Invoked when an option is clicked. */\n    _onSelect(option, isUserInput) {\n        const wasSelected = this._selectionModel.isSelected(option);\n        if (option.value == null && !this._multiple) {\n            option.deselect();\n            this._selectionModel.clear();\n            if (this.value != null) {\n                this._propagateChanges(option.value);\n            }\n        }\n        else {\n            if (wasSelected !== option.selected) {\n                option.selected\n                    ? this._selectionModel.select(option)\n                    : this._selectionModel.deselect(option);\n            }\n            if (isUserInput) {\n                this._keyManager.setActiveItem(option);\n            }\n            if (this.multiple) {\n                this._sortValues();\n                if (isUserInput) {\n                    // In case the user selected the option with their mouse, we\n                    // want to restore focus back to the trigger, in order to\n                    // prevent the select keyboard controls from clashing with\n                    // the ones from `mat-option`.\n                    this.focus();\n                }\n            }\n        }\n        if (wasSelected !== this._selectionModel.isSelected(option)) {\n            this._propagateChanges();\n        }\n        this.stateChanges.next();\n    }\n    /** Sorts the selected values in the selected based on their order in the panel. */\n    _sortValues() {\n        if (this.multiple) {\n            const options = this.options.toArray();\n            this._selectionModel.sort((a, b) => {\n                return this.sortComparator\n                    ? this.sortComparator(a, b, options)\n                    : options.indexOf(a) - options.indexOf(b);\n            });\n            this.stateChanges.next();\n        }\n    }\n    /** Emits change event to set the model value. */\n    _propagateChanges(fallbackValue) {\n        let valueToEmit;\n        if (this.multiple) {\n            valueToEmit = this.selected.map(option => option.value);\n        }\n        else {\n            valueToEmit = this.selected ? this.selected.value : fallbackValue;\n        }\n        this._value = valueToEmit;\n        this.valueChange.emit(valueToEmit);\n        this._onChange(valueToEmit);\n        this.selectionChange.emit(this._getChangeEvent(valueToEmit));\n        this._changeDetectorRef.markForCheck();\n    }\n    /**\n     * Highlights the selected item. If no option is selected, it will highlight\n     * the first *enabled* option.\n     */\n    _highlightCorrectOption() {\n        if (this._keyManager) {\n            if (this.empty) {\n                // Find the index of the first *enabled* option. Avoid calling `_keyManager.setActiveItem`\n                // because it activates the first option that passes the skip predicate, rather than the\n                // first *enabled* option.\n                let firstEnabledOptionIndex = -1;\n                for (let index = 0; index < this.options.length; index++) {\n                    const option = this.options.get(index);\n                    if (!option.disabled) {\n                        firstEnabledOptionIndex = index;\n                        break;\n                    }\n                }\n                this._keyManager.setActiveItem(firstEnabledOptionIndex);\n            }\n            else {\n                this._keyManager.setActiveItem(this._selectionModel.selected[0]);\n            }\n        }\n    }\n    /** Whether the panel is allowed to open. */\n    _canOpen() {\n        return !this._panelOpen && !this.disabled && this.options?.length > 0;\n    }\n    /** Focuses the select element. */\n    focus(options) {\n        this._elementRef.nativeElement.focus(options);\n    }\n    /** Gets the aria-labelledby for the select panel. */\n    _getPanelAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelId = this._parentFormField?.getLabelId();\n        const labelExpression = labelId ? labelId + ' ' : '';\n        return this.ariaLabelledby ? labelExpression + this.ariaLabelledby : labelId;\n    }\n    /** Determines the `aria-activedescendant` to be set on the host. */\n    _getAriaActiveDescendant() {\n        if (this.panelOpen && this._keyManager && this._keyManager.activeItem) {\n            return this._keyManager.activeItem.id;\n        }\n        return null;\n    }\n    /** Gets the aria-labelledby of the select component trigger. */\n    _getTriggerAriaLabelledby() {\n        if (this.ariaLabel) {\n            return null;\n        }\n        const labelId = this._parentFormField?.getLabelId();\n        let value = (labelId ? labelId + ' ' : '') + this._valueId;\n        if (this.ariaLabelledby) {\n            value += ' ' + this.ariaLabelledby;\n        }\n        return value;\n    }\n    /** Called when the overlay panel is done animating. */\n    _panelDoneAnimating(isOpen) {\n        this.openedChange.emit(isOpen);\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    setDescribedByIds(ids) {\n        if (ids.length) {\n            this._elementRef.nativeElement.setAttribute('aria-describedby', ids.join(' '));\n        }\n        else {\n            this._elementRef.nativeElement.removeAttribute('aria-describedby');\n        }\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    onContainerClick() {\n        this.focus();\n        this.open();\n    }\n    /**\n     * Implemented as part of MatFormFieldControl.\n     * @docs-private\n     */\n    get shouldLabelFloat() {\n        // Since the panel doesn't overlap the trigger, we\n        // want the label to only float when there's a value.\n        return this.panelOpen || !this.empty || (this.focused && !!this.placeholder);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelect, deps: [{ token: i1.ViewportRuler }, { token: i0.ChangeDetectorRef }, { token: i0.NgZone }, { token: i2.ErrorStateMatcher }, { token: i0.ElementRef }, { token: i3.Directionality, optional: true }, { token: i4.NgForm, optional: true }, { token: i4.FormGroupDirective, optional: true }, { token: MAT_FORM_FIELD, optional: true }, { token: i4.NgControl, optional: true, self: true }, { token: 'tabindex', attribute: true }, { token: MAT_SELECT_SCROLL_STRATEGY }, { token: i5.LiveAnnouncer }, { token: MAT_SELECT_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: MatSelect, isStandalone: true, selector: \"mat-select\", inputs: { userAriaDescribedBy: [\"aria-describedby\", \"userAriaDescribedBy\"], panelClass: \"panelClass\", disabled: [\"disabled\", \"disabled\", booleanAttribute], disableRipple: [\"disableRipple\", \"disableRipple\", booleanAttribute], tabIndex: [\"tabIndex\", \"tabIndex\", (value) => (value == null ? 0 : numberAttribute(value))], hideSingleSelectionIndicator: [\"hideSingleSelectionIndicator\", \"hideSingleSelectionIndicator\", booleanAttribute], placeholder: \"placeholder\", required: [\"required\", \"required\", booleanAttribute], multiple: [\"multiple\", \"multiple\", booleanAttribute], disableOptionCentering: [\"disableOptionCentering\", \"disableOptionCentering\", booleanAttribute], compareWith: \"compareWith\", value: \"value\", ariaLabel: [\"aria-label\", \"ariaLabel\"], ariaLabelledby: [\"aria-labelledby\", \"ariaLabelledby\"], errorStateMatcher: \"errorStateMatcher\", typeaheadDebounceInterval: [\"typeaheadDebounceInterval\", \"typeaheadDebounceInterval\", numberAttribute], sortComparator: \"sortComparator\", id: \"id\", panelWidth: \"panelWidth\" }, outputs: { openedChange: \"openedChange\", _openedStream: \"opened\", _closedStream: \"closed\", selectionChange: \"selectionChange\", valueChange: \"valueChange\" }, host: { attributes: { \"role\": \"combobox\", \"aria-autocomplete\": \"none\", \"aria-haspopup\": \"listbox\" }, listeners: { \"keydown\": \"_handleKeydown($event)\", \"focus\": \"_onFocus()\", \"blur\": \"_onBlur()\" }, properties: { \"attr.id\": \"id\", \"attr.tabindex\": \"disabled ? -1 : tabIndex\", \"attr.aria-controls\": \"panelOpen ? id + \\\"-panel\\\" : null\", \"attr.aria-expanded\": \"panelOpen\", \"attr.aria-label\": \"ariaLabel || null\", \"attr.aria-required\": \"required.toString()\", \"attr.aria-disabled\": \"disabled.toString()\", \"attr.aria-invalid\": \"errorState\", \"attr.aria-activedescendant\": \"_getAriaActiveDescendant()\", \"class.mat-mdc-select-disabled\": \"disabled\", \"class.mat-mdc-select-invalid\": \"errorState\", \"class.mat-mdc-select-required\": \"required\", \"class.mat-mdc-select-empty\": \"empty\", \"class.mat-mdc-select-multiple\": \"multiple\" }, classAttribute: \"mat-mdc-select\" }, providers: [\n            { provide: MatFormFieldControl, useExisting: MatSelect },\n            { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n        ], queries: [{ propertyName: \"customTrigger\", first: true, predicate: MAT_SELECT_TRIGGER, descendants: true }, { propertyName: \"options\", predicate: MatOption, descendants: true }, { propertyName: \"optionGroups\", predicate: MAT_OPTGROUP, descendants: true }], viewQueries: [{ propertyName: \"trigger\", first: true, predicate: [\"trigger\"], descendants: true }, { propertyName: \"panel\", first: true, predicate: [\"panel\"], descendants: true }, { propertyName: \"_overlayDir\", first: true, predicate: CdkConnectedOverlay, descendants: true }], exportAs: [\"matSelect\"], usesOnChanges: true, ngImport: i0, template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"open()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n\\n  <div class=\\\"mat-mdc-select-value\\\" [attr.id]=\\\"_valueId\\\">\\n    @if (empty) {\\n      <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\">{{placeholder}}</span>\\n    } @else {\\n      <span class=\\\"mat-mdc-select-value-text\\\">\\n        @if (customTrigger) {\\n          <ng-content select=\\\"mat-select-trigger\\\"></ng-content>\\n        } @else {\\n          <span class=\\\"mat-mdc-select-min-line\\\">{{triggerValue}}</span>\\n        }\\n      </span>\\n    }\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    [@transformPanel]=\\\"'showing'\\\"\\n    (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\".mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}div.mat-mdc-select-panel .mat-mdc-option{--mdc-list-list-item-container-color: var(--mat-select-panel-background-color)}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform)}\"], dependencies: [{ kind: \"directive\", type: CdkOverlayOrigin, selector: \"[cdk-overlay-origin], [overlay-origin], [cdkOverlayOrigin]\", exportAs: [\"cdkOverlayOrigin\"] }, { kind: \"directive\", type: CdkConnectedOverlay, selector: \"[cdk-connected-overlay], [connected-overlay], [cdkConnectedOverlay]\", inputs: [\"cdkConnectedOverlayOrigin\", \"cdkConnectedOverlayPositions\", \"cdkConnectedOverlayPositionStrategy\", \"cdkConnectedOverlayOffsetX\", \"cdkConnectedOverlayOffsetY\", \"cdkConnectedOverlayWidth\", \"cdkConnectedOverlayHeight\", \"cdkConnectedOverlayMinWidth\", \"cdkConnectedOverlayMinHeight\", \"cdkConnectedOverlayBackdropClass\", \"cdkConnectedOverlayPanelClass\", \"cdkConnectedOverlayViewportMargin\", \"cdkConnectedOverlayScrollStrategy\", \"cdkConnectedOverlayOpen\", \"cdkConnectedOverlayDisableClose\", \"cdkConnectedOverlayTransformOriginOn\", \"cdkConnectedOverlayHasBackdrop\", \"cdkConnectedOverlayLockPosition\", \"cdkConnectedOverlayFlexibleDimensions\", \"cdkConnectedOverlayGrowAfterOpen\", \"cdkConnectedOverlayPush\", \"cdkConnectedOverlayDisposeOnNavigation\"], outputs: [\"backdropClick\", \"positionChange\", \"attach\", \"detach\", \"overlayKeydown\", \"overlayOutsideClick\"], exportAs: [\"cdkConnectedOverlay\"] }, { kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], animations: [matSelectAnimations.transformPanel], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelect, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-select', exportAs: 'matSelect', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        'role': 'combobox',\n                        'aria-autocomplete': 'none',\n                        'aria-haspopup': 'listbox',\n                        'class': 'mat-mdc-select',\n                        '[attr.id]': 'id',\n                        '[attr.tabindex]': 'disabled ? -1 : tabIndex',\n                        '[attr.aria-controls]': 'panelOpen ? id + \"-panel\" : null',\n                        '[attr.aria-expanded]': 'panelOpen',\n                        '[attr.aria-label]': 'ariaLabel || null',\n                        '[attr.aria-required]': 'required.toString()',\n                        '[attr.aria-disabled]': 'disabled.toString()',\n                        '[attr.aria-invalid]': 'errorState',\n                        '[attr.aria-activedescendant]': '_getAriaActiveDescendant()',\n                        '[class.mat-mdc-select-disabled]': 'disabled',\n                        '[class.mat-mdc-select-invalid]': 'errorState',\n                        '[class.mat-mdc-select-required]': 'required',\n                        '[class.mat-mdc-select-empty]': 'empty',\n                        '[class.mat-mdc-select-multiple]': 'multiple',\n                        '(keydown)': '_handleKeydown($event)',\n                        '(focus)': '_onFocus()',\n                        '(blur)': '_onBlur()',\n                    }, animations: [matSelectAnimations.transformPanel], providers: [\n                        { provide: MatFormFieldControl, useExisting: MatSelect },\n                        { provide: MAT_OPTION_PARENT_COMPONENT, useExisting: MatSelect },\n                    ], standalone: true, imports: [CdkOverlayOrigin, CdkConnectedOverlay, NgClass], template: \"<div cdk-overlay-origin\\n     class=\\\"mat-mdc-select-trigger\\\"\\n     (click)=\\\"open()\\\"\\n     #fallbackOverlayOrigin=\\\"cdkOverlayOrigin\\\"\\n     #trigger>\\n\\n  <div class=\\\"mat-mdc-select-value\\\" [attr.id]=\\\"_valueId\\\">\\n    @if (empty) {\\n      <span class=\\\"mat-mdc-select-placeholder mat-mdc-select-min-line\\\">{{placeholder}}</span>\\n    } @else {\\n      <span class=\\\"mat-mdc-select-value-text\\\">\\n        @if (customTrigger) {\\n          <ng-content select=\\\"mat-select-trigger\\\"></ng-content>\\n        } @else {\\n          <span class=\\\"mat-mdc-select-min-line\\\">{{triggerValue}}</span>\\n        }\\n      </span>\\n    }\\n  </div>\\n\\n  <div class=\\\"mat-mdc-select-arrow-wrapper\\\">\\n    <div class=\\\"mat-mdc-select-arrow\\\">\\n      <!-- Use an inline SVG, because it works better than a CSS triangle in high contrast mode. -->\\n      <svg viewBox=\\\"0 0 24 24\\\" width=\\\"24px\\\" height=\\\"24px\\\" focusable=\\\"false\\\" aria-hidden=\\\"true\\\">\\n        <path d=\\\"M7 10l5 5 5-5z\\\"/>\\n      </svg>\\n    </div>\\n  </div>\\n</div>\\n\\n<ng-template\\n  cdk-connected-overlay\\n  cdkConnectedOverlayLockPosition\\n  cdkConnectedOverlayHasBackdrop\\n  cdkConnectedOverlayBackdropClass=\\\"cdk-overlay-transparent-backdrop\\\"\\n  [cdkConnectedOverlayPanelClass]=\\\"_overlayPanelClass\\\"\\n  [cdkConnectedOverlayScrollStrategy]=\\\"_scrollStrategy\\\"\\n  [cdkConnectedOverlayOrigin]=\\\"_preferredOverlayOrigin || fallbackOverlayOrigin\\\"\\n  [cdkConnectedOverlayOpen]=\\\"panelOpen\\\"\\n  [cdkConnectedOverlayPositions]=\\\"_positions\\\"\\n  [cdkConnectedOverlayWidth]=\\\"_overlayWidth\\\"\\n  (backdropClick)=\\\"close()\\\"\\n  (attach)=\\\"_onAttached()\\\"\\n  (detach)=\\\"close()\\\">\\n  <div\\n    #panel\\n    role=\\\"listbox\\\"\\n    tabindex=\\\"-1\\\"\\n    class=\\\"mat-mdc-select-panel mdc-menu-surface mdc-menu-surface--open {{ _getPanelTheme() }}\\\"\\n    [attr.id]=\\\"id + '-panel'\\\"\\n    [attr.aria-multiselectable]=\\\"multiple\\\"\\n    [attr.aria-label]=\\\"ariaLabel || null\\\"\\n    [attr.aria-labelledby]=\\\"_getPanelAriaLabelledby()\\\"\\n    [ngClass]=\\\"panelClass\\\"\\n    [@transformPanel]=\\\"'showing'\\\"\\n    (@transformPanel.done)=\\\"_panelDoneAnimatingStream.next($event.toState)\\\"\\n    (keydown)=\\\"_handleKeydown($event)\\\">\\n    <ng-content></ng-content>\\n  </div>\\n</ng-template>\\n\", styles: [\".mat-mdc-select{display:inline-block;width:100%;outline:none;-moz-osx-font-smoothing:grayscale;-webkit-font-smoothing:antialiased;color:var(--mat-select-enabled-trigger-text-color);font-family:var(--mat-select-trigger-text-font);line-height:var(--mat-select-trigger-text-line-height);font-size:var(--mat-select-trigger-text-size);font-weight:var(--mat-select-trigger-text-weight);letter-spacing:var(--mat-select-trigger-text-tracking)}div.mat-mdc-select-panel{box-shadow:var(--mat-select-container-elevation-shadow)}.mat-mdc-select-disabled{color:var(--mat-select-disabled-trigger-text-color)}.mat-mdc-select-trigger{display:inline-flex;align-items:center;cursor:pointer;position:relative;box-sizing:border-box;width:100%}.mat-mdc-select-disabled .mat-mdc-select-trigger{-webkit-user-select:none;user-select:none;cursor:default}.mat-mdc-select-value{width:100%;overflow:hidden;text-overflow:ellipsis;white-space:nowrap}.mat-mdc-select-value-text{white-space:nowrap;overflow:hidden;text-overflow:ellipsis}.mat-mdc-select-arrow-wrapper{height:24px;flex-shrink:0;display:inline-flex;align-items:center}.mat-form-field-appearance-fill .mdc-text-field--no-label .mat-mdc-select-arrow-wrapper{transform:none}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-invalid .mat-mdc-select-arrow,.mat-form-field-invalid:not(.mat-form-field-disabled) .mat-mdc-form-field-infix::after{color:var(--mat-select-invalid-arrow-color)}.mat-mdc-select-arrow{width:10px;height:5px;position:relative;color:var(--mat-select-enabled-arrow-color)}.mat-mdc-form-field.mat-focused .mat-mdc-select-arrow{color:var(--mat-select-focused-arrow-color)}.mat-mdc-form-field .mat-mdc-select.mat-mdc-select-disabled .mat-mdc-select-arrow{color:var(--mat-select-disabled-arrow-color)}.mat-mdc-select-arrow svg{fill:currentColor;position:absolute;top:50%;left:50%;transform:translate(-50%, -50%)}.cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:CanvasText}.mat-mdc-select-disabled .cdk-high-contrast-active .mat-mdc-select-arrow svg{fill:GrayText}div.mat-mdc-select-panel{width:100%;max-height:275px;outline:0;overflow:auto;padding:8px 0;border-radius:4px;box-sizing:border-box;position:static;background-color:var(--mat-select-panel-background-color)}.cdk-high-contrast-active div.mat-mdc-select-panel{outline:solid 1px}.cdk-overlay-pane:not(.mat-mdc-select-panel-above) div.mat-mdc-select-panel{border-top-left-radius:0;border-top-right-radius:0;transform-origin:top center}.mat-mdc-select-panel-above div.mat-mdc-select-panel{border-bottom-left-radius:0;border-bottom-right-radius:0;transform-origin:bottom center}div.mat-mdc-select-panel .mat-mdc-option{--mdc-list-list-item-container-color: var(--mat-select-panel-background-color)}.mat-mdc-select-placeholder{transition:color 400ms 133.3333333333ms cubic-bezier(0.25, 0.8, 0.25, 1);color:var(--mat-select-placeholder-text-color)}._mat-animation-noopable .mat-mdc-select-placeholder{transition:none}.mat-form-field-hide-placeholder .mat-mdc-select-placeholder{color:rgba(0,0,0,0);-webkit-text-fill-color:rgba(0,0,0,0);transition:none;display:block}.mat-mdc-form-field-type-mat-select:not(.mat-form-field-disabled) .mat-mdc-text-field-wrapper{cursor:pointer}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mat-mdc-floating-label{max-width:calc(100% - 18px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-fill .mdc-floating-label--float-above{max-width:calc(100%/0.75 - 24px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-notched-outline__notch{max-width:calc(100% - 60px)}.mat-mdc-form-field-type-mat-select.mat-form-field-appearance-outline .mdc-text-field--label-floating .mdc-notched-outline__notch{max-width:calc(100% - 24px)}.mat-mdc-select-min-line:empty::before{content:\\\" \\\";white-space:pre;width:1px;display:inline-block;visibility:hidden}.mat-form-field-appearance-fill .mat-mdc-select-arrow-wrapper{transform:var(--mat-select-arrow-transform)}\"] }]\n        }], ctorParameters: () => [{ type: i1.ViewportRuler }, { type: i0.ChangeDetectorRef }, { type: i0.NgZone }, { type: i2.ErrorStateMatcher }, { type: i0.ElementRef }, { type: i3.Directionality, decorators: [{\n                    type: Optional\n                }] }, { type: i4.NgForm, decorators: [{\n                    type: Optional\n                }] }, { type: i4.FormGroupDirective, decorators: [{\n                    type: Optional\n                }] }, { type: i6.MatFormField, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_FORM_FIELD]\n                }] }, { type: i4.NgControl, decorators: [{\n                    type: Self\n                }, {\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['tabindex']\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SELECT_SCROLL_STRATEGY]\n                }] }, { type: i5.LiveAnnouncer }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_SELECT_CONFIG]\n                }] }], propDecorators: { options: [{\n                type: ContentChildren,\n                args: [MatOption, { descendants: true }]\n            }], optionGroups: [{\n                type: ContentChildren,\n                args: [MAT_OPTGROUP, { descendants: true }]\n            }], customTrigger: [{\n                type: ContentChild,\n                args: [MAT_SELECT_TRIGGER]\n            }], userAriaDescribedBy: [{\n                type: Input,\n                args: ['aria-describedby']\n            }], trigger: [{\n                type: ViewChild,\n                args: ['trigger']\n            }], panel: [{\n                type: ViewChild,\n                args: ['panel']\n            }], _overlayDir: [{\n                type: ViewChild,\n                args: [CdkConnectedOverlay]\n            }], panelClass: [{\n                type: Input\n            }], disabled: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableRipple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], tabIndex: [{\n                type: Input,\n                args: [{\n                        transform: (value) => (value == null ? 0 : numberAttribute(value)),\n                    }]\n            }], hideSingleSelectionIndicator: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], placeholder: [{\n                type: Input\n            }], required: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], multiple: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], disableOptionCentering: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], compareWith: [{\n                type: Input\n            }], value: [{\n                type: Input\n            }], ariaLabel: [{\n                type: Input,\n                args: ['aria-label']\n            }], ariaLabelledby: [{\n                type: Input,\n                args: ['aria-labelledby']\n            }], errorStateMatcher: [{\n                type: Input\n            }], typeaheadDebounceInterval: [{\n                type: Input,\n                args: [{ transform: numberAttribute }]\n            }], sortComparator: [{\n                type: Input\n            }], id: [{\n                type: Input\n            }], panelWidth: [{\n                type: Input\n            }], openedChange: [{\n                type: Output\n            }], _openedStream: [{\n                type: Output,\n                args: ['opened']\n            }], _closedStream: [{\n                type: Output,\n                args: ['closed']\n            }], selectionChange: [{\n                type: Output\n            }], valueChange: [{\n                type: Output\n            }] } });\n/**\n * Allows the user to customize the trigger that is displayed when the select has a value.\n */\nclass MatSelectTrigger {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelectTrigger, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatSelectTrigger, isStandalone: true, selector: \"mat-select-trigger\", providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelectTrigger, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-select-trigger',\n                    providers: [{ provide: MAT_SELECT_TRIGGER, useExisting: MatSelectTrigger }],\n                    standalone: true,\n                }]\n        }] });\n\nclass MatSelectModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelectModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelectModule, imports: [CommonModule,\n            OverlayModule,\n            MatOptionModule,\n            MatCommonModule,\n            MatSelect,\n            MatSelectTrigger], exports: [CdkScrollableModule,\n            MatFormFieldModule,\n            MatSelect,\n            MatSelectTrigger,\n            MatOptionModule,\n            MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelectModule, providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER], imports: [CommonModule,\n            OverlayModule,\n            MatOptionModule,\n            MatCommonModule, CdkScrollableModule,\n            MatFormFieldModule,\n            MatOptionModule,\n            MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSelectModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        CommonModule,\n                        OverlayModule,\n                        MatOptionModule,\n                        MatCommonModule,\n                        MatSelect,\n                        MatSelectTrigger,\n                    ],\n                    exports: [\n                        CdkScrollableModule,\n                        MatFormFieldModule,\n                        MatSelect,\n                        MatSelectTrigger,\n                        MatOptionModule,\n                        MatCommonModule,\n                    ],\n                    providers: [MAT_SELECT_SCROLL_STRATEGY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SELECT_CONFIG, MAT_SELECT_SCROLL_STRATEGY, MAT_SELECT_SCROLL_STRATEGY_PROVIDER, MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY, MAT_SELECT_TRIGGER, MatSelect, MatSelectChange, MatSelectModule, MatSelectTrigger, matSelectAnimations };\n"], "names": ["i0", "inject", "NgZone", "Injectable", "Subject", "Observable", "filter", "shareReplay", "takeUntil", "loopLimitExceededErrorHandler", "e", "ErrorEvent", "message", "console", "error", "SingleBoxSharedResizeObserver", "constructor", "_box", "_destroyed", "_resizeSubject", "_elementObservables", "Map", "ResizeObserver", "_resizeObserver", "entries", "next", "observe", "target", "has", "set", "observer", "subscription", "subscribe", "box", "unobserve", "unsubscribe", "delete", "pipe", "some", "entry", "bufferSize", "refCount", "get", "destroy", "complete", "clear", "SharedResizeObserver", "_observers", "_ngZone", "ngDevMode", "runOutsideAngular", "window", "addEventListener", "ngOnDestroy", "removeEventListener", "options", "ɵfac", "SharedResizeObserver_Factory", "t", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ɵsetClassMetadata", "type", "args", "i1", "normalizePassiveListenerOptions", "EventEmitter", "Directive", "Output", "booleanAttribute", "Optional", "Inject", "Input", "NgModule", "coerceElement", "coerceNumberProperty", "EMPTY", "fromEvent", "auditTime", "DOCUMENT", "listenerOptions", "passive", "AutofillMonitor", "_platform", "_monitoredElements", "monitor", "elementOrRef", "<PERSON><PERSON><PERSON><PERSON>", "element", "info", "subject", "result", "cssClass", "listener", "event", "animationName", "classList", "contains", "add", "run", "isAutofilled", "remove", "unlisten", "stopMonitoring", "for<PERSON>ach", "_info", "AutofillMonitor_Factory", "ɵɵinject", "Platform", "CdkAutofill", "_elementRef", "_autofillMonitor", "cdkAutofill", "ngOnInit", "emit", "CdkAutofill_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵdir", "ɵɵdefineDirective", "selectors", "outputs", "standalone", "selector", "CdkTextareaAutosize", "minRows", "_minRows", "value", "_setMinHeight", "maxRows", "_maxRows", "_setMaxHeight", "enabled", "_enabled", "resizeToFitContent", "reset", "placeholder", "_textareaElement", "_cachedPlaceholderHeight", "undefined", "setAttribute", "removeAttribute", "_cacheTextareaPlaceholderHeight", "document", "_previousMinRows", "_isViewInited", "_handleFocusEvent", "_hasFocus", "_document", "nativeElement", "minHeight", "_cachedLineHeight", "style", "maxHeight", "ngAfterViewInit", "_initialHeight", "height", "_getWindow", "_cacheTextareaLineHeight", "textareaClone", "cloneNode", "rows", "position", "visibility", "border", "padding", "overflow", "parentNode", "append<PERSON><PERSON><PERSON>", "clientHeight", "_measureScrollHeight", "<PERSON><PERSON><PERSON><PERSON>", "marginBottom", "isFirefox", "FIREFOX", "needsMarginFiller", "measuringClass", "scrollHeight", "ngDoCheck", "force", "textarea", "_previousValue", "Math", "max", "requestAnimationFrame", "_scrollToCaretPosition", "setTimeout", "_noopInputHandler", "_getDocument", "doc", "defaultView", "selectionStart", "selectionEnd", "isStopped", "setSelectionRange", "CdkTextareaAutosize_Factory", "hostAttrs", "hostBindings", "CdkTextareaAutosize_HostBindings", "rf", "ctx", "ɵɵlistener", "CdkTextareaAutosize_input_HostBindingHandler", "inputs", "ɵɵInputFlags", "None", "HasDecoratorInputTransform", "exportAs", "features", "ɵɵInputTransformsFeature", "host", "decorators", "alias", "transform", "TextFieldModule", "TextFieldModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "InjectionToken", "Attribute", "Component", "ChangeDetectionStrategy", "ViewEncapsulation", "ViewChild", "ANIMATION_MODULE_TYPE", "ContentChild", "ContentChildren", "i2", "Subscription", "merge", "coerceBooleanProperty", "trigger", "state", "transition", "animate", "NgTemplateOutlet", "CommonModule", "ObserversModule", "MatCommonModule", "_c0", "_c1", "_c2", "_c3", "_c4", "_c5", "_c6", "_c7", "MatFormField_ng_template_0_Conditional_0_Conditional_2_Template", "ɵɵelement", "MatFormField_ng_template_0_Conditional_0_Template", "ɵɵelementStart", "ɵɵprojection", "ɵɵtemplate", "ɵɵelementEnd", "ctx_r1", "ɵɵnextContext", "ɵɵproperty", "_shouldLabelFloat", "_hasOutline", "_labelId", "ɵɵattribute", "_control", "disableAutomaticLabeling", "id", "ɵɵadvance", "ɵɵconditional", "hideRequiredMarker", "required", "MatFormField_ng_template_0_Template", "_hasFloatingLabel", "MatFormField_Conditional_4_Template", "MatFormField_Conditional_6_Conditional_1_ng_template_0_Template", "MatFormField_Conditional_6_Conditional_1_Template", "labelTemplate_r3", "ɵɵreference", "MatFormField_Conditional_6_Template", "_forceDisplayInfixLabel", "MatFormField_Conditional_7_Template", "MatFormField_Conditional_8_Template", "MatFormField_Conditional_10_ng_template_0_Template", "MatFormField_Conditional_10_Template", "MatFormField_Conditional_12_Template", "MatFormField_Conditional_13_Template", "MatFormField_Conditional_14_Template", "<PERSON><PERSON><PERSON><PERSON><PERSON>_Case_16_Template", "_subscriptAnimationState", "<PERSON><PERSON><PERSON><PERSON><PERSON>_Case_17_Conditional_1_Template", "ɵɵtext", "_hintLabelId", "ɵɵtextInterpolate", "hintLabel", "<PERSON><PERSON><PERSON><PERSON><PERSON>_Case_17_Template", "<PERSON><PERSON><PERSON><PERSON>", "MatLabel_Factory", "nextUniqueId$2", "MAT_ERROR", "<PERSON><PERSON><PERSON><PERSON>", "ariaLive", "elementRef", "MatError_Factory", "ɵɵinjectAttribute", "hostVars", "MatE<PERSON>r_HostBindings", "ɵɵhostProperty", "ɵɵProvidersFeature", "provide", "useExisting", "providers", "nextUniqueId$1", "MatHint", "align", "MatHint_Factory", "MatHint_HostBindings", "ɵɵclassProp", "MAT_PREFIX", "MatPrefix", "_isText", "_isTextSelector", "MatPrefix_Factory", "MAT_SUFFIX", "MatSuffix", "MatSuffix_Factory", "FLOATING_LABEL_PARENT", "MatFormFieldFloatingLabel", "floating", "_floating", "monitorResize", "_handleResize", "_monitorResize", "_subscribeToResize", "_resizeSubscription", "_parent", "getWidth", "estimateScrollWidth", "_handleLabelResized", "MatFormFieldFloatingLabel_Factory", "MatFormFieldFloatingLabel_HostBindings", "htmlEl", "offsetParent", "scrollWidth", "clone", "setProperty", "documentElement", "ACTIVATE_CLASS", "DEACTIVATING_CLASS", "MatFormFieldLineRipple", "ngZone", "_handleTransitionEnd", "isDeactivating", "propertyName", "activate", "deactivate", "MatFormFieldLineRipple_Factory", "MatFormFieldNotchedOutline", "open", "label", "querySelector", "transitionDuration", "_setNotchWidth", "labelWidth", "_notch", "width", "NOTCH_ELEMENT_PADDING", "NOTCH_ELEMENT_BORDER", "MatFormFieldNotchedOutline_Factory", "ɵcmp", "ɵɵdefineComponent", "viewQuery", "MatFormFieldNotchedOutline_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "MatFormFieldNotchedOutline_HostBindings", "ɵɵStandaloneFeature", "attrs", "ngContentSelectors", "decls", "vars", "consts", "template", "MatFormFieldNotchedOutline_Template", "ɵɵprojectionDef", "encapsulation", "changeDetection", "OnPush", "matFormFieldAnimations", "transitionMessages", "opacity", "MatFormFieldControl", "MatFormFieldControl_Factory", "getMatFormFieldPlaceholderConflictError", "Error", "getMatFormFieldDuplicatedHintError", "getMatFormFieldMissingControlError", "MAT_FORM_FIELD", "MAT_FORM_FIELD_DEFAULT_OPTIONS", "nextUniqueId", "DEFAULT_APPEARANCE", "DEFAULT_FLOAT_LABEL", "DEFAULT_SUBSCRIPT_SIZING", "FLOATING_LABEL_DEFAULT_DOCKED_TRANSFORM", "MatFormField", "_hideRequiredMarker", "floatLabel", "_floatLabel", "_defaults", "_changeDetectorRef", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "appearance", "_appearance", "oldValue", "newAppearance", "_needsOutlineLabelOffsetUpdateOnStable", "subscriptSizing", "_subscriptSizing", "_<PERSON><PERSON><PERSON>l", "_processHints", "_explicitFormFieldControl", "_formFieldControl", "_dir", "_animationMode", "_unusedDocument", "color", "_hasIconPrefix", "_hasTextPrefix", "_hasIconSuffix", "_hasTextSuffix", "_isFocused", "Boolean", "_updateFocusState", "detectChanges", "ngAfterContentInit", "_assertFormFieldControl", "_initializeControl", "_initializeSubscript", "_initializePrefixAndSuffix", "_initializeOutlineLabelOffsetSubscriptions", "ngAfterContentChecked", "getLabelId", "getConnectedOverlayOrigin", "_textField", "_animateAndLockLabel", "control", "controlType", "stateChanges", "_syncDescribedByIds", "ngControl", "valueChanges", "_checkPrefixAndSuffixTypes", "_prefixChildren", "find", "p", "_suffixC<PERSON><PERSON>n", "s", "changes", "_hint<PERSON><PERSON><PERSON>n", "_errorC<PERSON><PERSON>n", "_validateHints", "focused", "_lineRipple", "toggle", "onStable", "_updateOutlineLabelOffset", "change", "_shouldAlwaysFloat", "length", "_labelChildNonStatic", "_labelChildStatic", "shouldLabelFloat", "_shouldForward", "prop", "_getDisplayedMessages", "errorState", "_refreshOutlineNotchWidth", "_floating<PERSON>abel", "_notchedOutline", "startHint", "endHint", "hint", "ids", "userAriaDescribedBy", "push", "split", "map", "setDescribedByIds", "floatingLabel", "_iconPrefixContainer", "_textPrefixContainer", "_isAttachedToDom", "iconPrefixContainer", "textPrefixContainer", "iconPrefixContainer<PERSON>idth", "getBoundingClientRect", "textPrefixContainer<PERSON><PERSON><PERSON>", "negate", "prefixWidth", "labelOffset", "labelHorizontalOffset", "getRootNode", "rootNode", "MatFormField_Factory", "ChangeDetectorRef", "Directionality", "contentQueries", "MatFormField_ContentQueries", "dirIndex", "ɵɵcontentQuery", "MatFormField_Query", "MatFormField_HostBindings", "disabled", "autofilled", "MatFormField_Template", "_r1", "ɵɵgetCurrentView", "ɵɵtemplateRefExtractor", "<PERSON><PERSON><PERSON><PERSON><PERSON>_Template_div_click_2_listener", "$event", "ɵɵrestoreView", "ɵɵresetView", "onContainerClick", "tmp_16_0", "dependencies", "styles", "data", "animation", "animations", "static", "descendants", "MatFormFieldModule", "MatFormFieldModule_Factory", "getSupportedInputTypes", "i4", "Self", "Validators", "i3", "_ErrorStateTracker", "i5", "getMatInputUnsupportedTypeError", "MAT_INPUT_VALUE_ACCESSOR", "MAT_INPUT_INVALID_TYPES", "MatInput", "_disabled", "_id", "_uid", "_required", "hasValidator", "_type", "_validateType", "_isTextarea", "errorStateMatcher", "_errorStateTracker", "matcher", "_inputValueAccessor", "readonly", "_readonly", "parentForm", "parentFormGroup", "defaultErrorStateMatcher", "inputValueAccessor", "_formField", "_neverEmptyInputTypes", "_iOSKeyupListener", "el", "nodeName", "toLowerCase", "_previousNativeValue", "IOS", "_isServer", "_isNativeSelect", "_isInFormField", "multiple", "ngOnChanges", "updateErrorState", "_dirtyCheckNativeValue", "_dirtyCheckPlaceholder", "focus", "_focusChanged", "isFocused", "_onInput", "newValue", "_getPlaceholder", "_previousPlaceholder", "indexOf", "_isNeverEmpty", "_isBadInput", "validity", "badInput", "empty", "selectElement", "firstOption", "selectedIndex", "join", "_isInlineSelect", "size", "MatInput_Factory", "NgControl", "NgForm", "FormGroupDirective", "ErrorStateMatcher", "MatInput_HostBindings", "MatInput_focus_HostBindingHandler", "MatInput_blur_HostBindingHandler", "MatInput_input_HostBindingHandler", "name", "ɵɵNgOnChangesFeature", "MatInputModule", "MatInputModule_Factory", "Overlay", "CdkOverlayOrigin", "CdkConnectedOverlay", "OverlayModule", "Ng<PERSON><PERSON>", "numberAttribute", "_countGroupLabelsBeforeOption", "_getOptionScrollPosition", "MAT_OPTION_PARENT_COMPONENT", "MatOption", "MAT_OPTGROUP", "MatOptionModule", "MatOptgroup", "i6", "CdkScrollableModule", "removeAriaReferencedId", "addAriaReferencedId", "ActiveDescendantKeyManager", "SelectionModel", "DOWN_ARROW", "UP_ARROW", "LEFT_ARROW", "RIGHT_ARROW", "ENTER", "SPACE", "hasModifierKey", "A", "defer", "startWith", "switchMap", "distinctUntilChanged", "take", "query", "animate<PERSON><PERSON><PERSON>", "MatSelect_Conditional_4_Template", "MatSelect_Conditional_5_Conditional_1_Template", "MatSelect_Conditional_5_Conditional_2_Template", "triggerValue", "MatSelect_Conditional_5_Template", "customTrigger", "MatSelect_ng_template_10_Template", "_r3", "MatSelect_ng_template_10_Template_div_animation_transformPanel_done_0_listener", "_panelDoneAnimatingStream", "toState", "MatSelect_ng_template_10_Template_div_keydown_0_listener", "_handleKeydown", "ɵɵclassMapInterpolate1", "_getPanelTheme", "panelClass", "aria<PERSON><PERSON><PERSON>", "_getPanelAriaLabe<PERSON>by", "matSelectAnimations", "transformPanelWrap", "optional", "transformPanel", "getMatSelectDynamicMultipleError", "getMatSelectNonArrayValueError", "getMatSelectNonFunctionValueError", "MAT_SELECT_SCROLL_STRATEGY", "overlay", "scrollStrategies", "reposition", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER_FACTORY", "MAT_SELECT_CONFIG", "MAT_SELECT_SCROLL_STRATEGY_PROVIDER", "deps", "useFactory", "MAT_SELECT_TRIGGER", "MatSelectChange", "source", "MatSelect", "_scrollOptionIntoView", "index", "option", "toArray", "panel", "labelCount", "optionGroups", "_getHostElement", "scrollTop", "offsetTop", "offsetHeight", "_positioningSettled", "_keyManager", "activeItemIndex", "_getChangeEvent", "_focused", "_panelOpen", "hideSingleSelectionIndicator", "_hideSingleSelectionIndicator", "_syncParentProperties", "_placeholder", "_multiple", "_selectionModel", "compareWith", "_compareWith", "fn", "_initializeSelection", "_value", "hasAssigned", "_assignValue", "_onChange", "_viewportRuler", "_unusedNgZone", "_parentFormField", "tabIndex", "scrollStrategyFactory", "_liveAnnouncer", "_defaultOptions", "_positions", "originX", "originY", "overlayX", "overlayY", "o1", "o2", "_triggerAriaLabelledBy", "_destroy", "_onTouched", "_valueId", "_overlayPanelClass", "overlayPanelClass", "disable<PERSON><PERSON><PERSON>", "disableOptionCentering", "panelWidth", "_initialized", "optionSelectionChanges", "onSelectionChange", "openedChange", "_openedStream", "o", "_closedStream", "selectionChange", "valueChange", "_trackedModal", "_skipPredicate", "panelOpen", "valueAccessor", "typeaheadDebounceInterval", "_scrollStrategyFactory", "_scrollStrategy", "parseInt", "_panelDoneAnimating", "_overlayWidth", "_getOverlayWidth", "_preferredOverlayOrigin", "_initKeyManager", "changed", "added", "select", "removed", "deselect", "_resetOptions", "newAria<PERSON><PERSON><PERSON><PERSON>", "_getTriggerAriaLabe<PERSON>by", "_previousControl", "withTypeAhead", "_clearFromModal", "close", "_canOpen", "_applyModalPanelOwnership", "withHorizontalOrientation", "_highlightCorrectOption", "modal", "closest", "panelId", "_isRtl", "writeValue", "registerOnChange", "registerOnTouched", "setDisabledState", "isDisabled", "selected", "selectedOptions", "viewValue", "reverse", "_handleOpenKeydown", "_handleClosedKeydown", "keyCode", "isArrowKey", "isOpenKey", "manager", "isTyping", "altKey", "preventDefault", "previouslySelectedOption", "onKeydown", "selectedOption", "announce", "activeItem", "_selectViaInteraction", "ctrl<PERSON>ey", "hasDeselectedOptions", "opt", "previouslyFocusedIndex", "shift<PERSON>ey", "_onFocus", "_onBlur", "cancelTypeahead", "_onAttached", "_overlayDir", "positionChange", "isEmpty", "Promise", "resolve", "then", "_setSelectionByValue", "setInactiveStyles", "Array", "isArray", "currentValue", "_selectOptionByValue", "_sortValues", "correspondingOption", "updateActiveItem", "isSelected", "warn", "preferred<PERSON><PERSON>in", "refToMeasure", "withVerticalOrientation", "withHomeAndEnd", "withPageUpDown", "withAllowedModifierKeys", "skipPredicate", "tabOut", "changedOrDestroyed", "_onSelect", "isUserInput", "_stateChanges", "wasSelected", "_propagateChanges", "setActiveItem", "sort", "a", "b", "sortComparator", "fallback<PERSON><PERSON><PERSON>", "valueToEmit", "firstEnabledOptionIndex", "labelId", "labelExpression", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_getAriaActiveDescendant", "isOpen", "MatSelect_Factory", "ViewportRuler", "LiveAnnouncer", "MatSelect_ContentQueries", "MatSelect_Query", "MatSelect_HostBindings", "MatSelect_keydown_HostBindingHandler", "MatSelect_focus_HostBindingHandler", "MatSelect_blur_HostBindingHandler", "toString", "MatSelect_Template", "MatSelect_Template_div_click_0_listener", "ɵɵnamespaceSVG", "MatSelect_Template_ng_template_backdropClick_10_listener", "MatSelect_Template_ng_template_attach_10_listener", "MatSelect_Template_ng_template_detach_10_listener", "fallbackOverlayOrigin_r4", "MatSelectTrigger", "MatSelectTrigger_Factory", "MatSelectModule", "MatSelectModule_Factory"], "sourceRoot": "webpack:///", "x_google_ignoreList": [0, 1, 2, 3, 4]}