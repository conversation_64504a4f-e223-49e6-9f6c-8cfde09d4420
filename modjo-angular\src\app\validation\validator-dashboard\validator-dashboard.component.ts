import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { FormsModule } from '@angular/forms';
import { ValidationService } from '../validation.service';
import { AuthService } from '../../auth/auth.service';
import { Action, ActionType } from '../../models/action.model';

@Component({
  selector: 'app-validator-dashboard',
  standalone: true,
  imports: [CommonModule, FormsModule],
  templateUrl: './validator-dashboard.component.html',
  styleUrls: ['./validator-dashboard.component.css']
})
export class ValidatorDashboardComponent implements OnInit {
  pendingActions: Action[] = [];
  validatedActions: Action[] = [];
  isLoading: boolean = true;
  error: string = '';

  // For new action creation
  newActionUserId: string = '';
  newActionDescription: string = '';
  newActionPoints: number = 10;

  successMessage: string = '';
  errorMessage: string = '';

  constructor(
    private validationService: ValidationService,
    private authService: AuthService
  ) {}

  ngOnInit(): void {
    this.loadActions();
  }

  private async loadActions(): Promise<void> {
    try {
      const currentUser = this.authService.getCurrentUser();

      if (!currentUser) {
        this.error = 'Utilisateur non connecté';
        this.isLoading = false;
        return;
      }

      // Get pending actions
      this.pendingActions = await this.validationService.getPendingActions().toPromise() || [];

      // Get validated actions by this validator
      this.validatedActions = await this.validationService.getActionsByValidator(currentUser.uid).toPromise() || [];

      this.isLoading = false;
    } catch (error) {
      console.error('Error loading actions:', error);
      this.error = 'Erreur lors du chargement des actions';
      this.isLoading = false;
    }
  }

  async validateAction(actionId: string, approved: boolean): Promise<void> {
    try {
      const currentUser = this.authService.getCurrentUser();

      if (!currentUser) {
        this.errorMessage = 'Utilisateur non connecté';
        return;
      }

      await this.validationService.validateAction(actionId, currentUser.uid, approved);

      this.successMessage = approved
        ? 'Action validée avec succès'
        : 'Action rejetée avec succès';

      // Reload actions
      this.loadActions();

      // Clear success message after 3 seconds
      setTimeout(() => {
        this.successMessage = '';
      }, 3000);
    } catch (error: any) {
      console.error('Error validating action:', error);
      this.errorMessage = error.message || 'Erreur lors de la validation de l\'action';

      // Clear error message after 3 seconds
      setTimeout(() => {
        this.errorMessage = '';
      }, 3000);
    }
  }

  async createAction(): Promise<void> {
    this.errorMessage = '';

    if (!this.newActionUserId || !this.newActionDescription || this.newActionPoints <= 0) {
      this.errorMessage = 'Veuillez remplir tous les champs correctement';
      return;
    }

    try {
      const currentUser = this.authService.getCurrentUser();

      if (!currentUser) {
        this.errorMessage = 'Utilisateur non connecté';
        return;
      }

      await this.validationService.createAction({
        userId: this.newActionUserId,
        validatorId: currentUser.uid,
        type: ActionType.EDUCATIONAL_ACHIEVEMENT,
        description: this.newActionDescription,
        pointsAwarded: this.newActionPoints
      });

      this.successMessage = 'Action créée avec succès';

      // Reset form
      this.newActionUserId = '';
      this.newActionDescription = '';
      this.newActionPoints = 10;

      // Reload actions
      this.loadActions();

      // Clear success message after 3 seconds
      setTimeout(() => {
        this.successMessage = '';
      }, 3000);
    } catch (error: any) {
      console.error('Error creating action:', error);
      this.errorMessage = error.message || 'Erreur lors de la création de l\'action';

      // Clear error message after 3 seconds
      setTimeout(() => {
        this.errorMessage = '';
      }, 3000);
    }
  }
}
