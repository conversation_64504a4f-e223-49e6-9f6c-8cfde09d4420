describe('Authentication Flow', () => {
  beforeEach(() => {
    cy.visit('/auth/login');
  });

  it('should display login form', () => {
    cy.contains('Modjo');
    cy.contains('Connectez-vous à votre compte');
    cy.get('input[formControlName="email"]').should('be.visible');
    cy.get('input[formControlName="password"]').should('be.visible');
    cy.get('button[type="submit"]').should('be.visible');
  });

  it('should show validation errors for empty form', () => {
    cy.get('button[type="submit"]').click();
    cy.contains('Email requis');
    cy.contains('Mot de passe requis');
  });

  it('should show error for invalid email format', () => {
    cy.get('input[formControlName="email"]').type('invalid-email');
    cy.get('input[formControlName="password"]').type('password123');
    cy.get('button[type="submit"]').click();
    cy.contains('Format email invalide');
  });

  it('should navigate to register page', () => {
    cy.contains('Créer un compte').click();
    cy.url().should('include', '/auth/register');
    cy.contains('Rejoignez Modjo');
  });

  it('should test demo login buttons', () => {
    cy.contains('Utilisateur').click();
    cy.get('input[formControlName="email"]').should('have.value', '<EMAIL>');
    cy.get('input[formControlName="password"]').should('have.value', 'password123');
  });

  it('should handle demo admin login', () => {
    cy.contains('Admin').click();
    cy.get('input[formControlName="email"]').should('have.value', '<EMAIL>');
    cy.get('input[formControlName="password"]').should('have.value', 'password123');
  });
});

describe('Registration Flow', () => {
  beforeEach(() => {
    cy.visit('/auth/register');
  });

  it('should display registration form', () => {
    cy.contains('Rejoignez Modjo');
    cy.contains('Créez votre compte pour commencer');
    cy.get('input[formControlName="name"]').should('be.visible');
    cy.get('input[formControlName="email"]').should('be.visible');
    cy.get('input[formControlName="password"]').should('be.visible');
    cy.get('mat-select[formControlName="city"]').should('be.visible');
    cy.get('mat-select[formControlName="role"]').should('be.visible');
  });

  it('should show validation errors for empty required fields', () => {
    cy.get('button[type="submit"]').click();
    cy.contains('Nom requis');
    cy.contains('Email requis');
    cy.contains('Ville requise');
    cy.contains('Mot de passe requis');
  });

  it('should validate password confirmation', () => {
    cy.get('input[formControlName="password"]').type('password123');
    cy.get('input[formControlName="confirmPassword"]').type('different');
    cy.get('button[type="submit"]').click();
    cy.contains('Les mots de passe ne correspondent pas');
  });

  it('should navigate back to login', () => {
    cy.contains('Se connecter').click();
    cy.url().should('include', '/auth/login');
    cy.contains('Connectez-vous à votre compte');
  });
});
