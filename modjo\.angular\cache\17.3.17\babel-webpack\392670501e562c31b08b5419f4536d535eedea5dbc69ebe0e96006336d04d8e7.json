{"ast": null, "code": "import { ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, VERSION, ɵAngularFireSchedulers, ɵzoneWrap } from '@angular/fire';\nimport { timer, from } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, isDevMode, Optional, PLATFORM_ID, NgModule, NgZone, Injector } from '@angular/core';\nimport { FirebaseApp, FirebaseApps } from '@angular/fire/app';\nimport { registerVersion } from 'firebase/app';\nimport { isPlatformServer } from '@angular/common';\nimport { getToken as getToken$1, initializeAppCheck as initializeAppCheck$1, onTokenChanged as onTokenChanged$1, setTokenAutoRefreshEnabled as setTokenAutoRefreshEnabled$1 } from 'firebase/app-check';\nexport * from 'firebase/app-check';\nconst APP_CHECK_PROVIDER_NAME = 'app-check';\nclass AppCheck {\n  constructor(appCheck) {\n    return appCheck;\n  }\n}\nclass AppCheckInstances {\n  constructor() {\n    return ɵgetAllInstancesOf(APP_CHECK_PROVIDER_NAME);\n  }\n}\nconst appCheckInstance$ = timer(0, 300).pipe(concatMap(() => from(ɵgetAllInstancesOf(APP_CHECK_PROVIDER_NAME))), distinct());\nconst PROVIDED_APP_CHECK_INSTANCES = new InjectionToken('angularfire2.app-check-instances');\nconst APP_CHECK_NAMESPACE_SYMBOL = Symbol('angularfire2.app-check.namespace');\nfunction defaultAppCheckInstanceFactory(provided, defaultApp) {\n  const defaultAppCheck = ɵgetDefaultInstanceOf(APP_CHECK_PROVIDER_NAME, provided, defaultApp);\n  return defaultAppCheck && new AppCheck(defaultAppCheck);\n}\nconst LOCALHOSTS = ['localhost', '0.0.0.0', '127.0.0.1'];\nconst isLocalhost = typeof window !== 'undefined' && LOCALHOSTS.includes(window.location.hostname);\nfunction appCheckInstanceFactory(fn) {\n  // tslint:disable-next-line:ban-types\n  return (zone, injector, platformId) => {\n    var _a;\n    // Node should use admin token provider, browser devmode and localhost should use debug token\n    if (!isPlatformServer(platformId) && (isDevMode() || isLocalhost)) {\n      (_a = globalThis.FIREBASE_APPCHECK_DEBUG_TOKEN) !== null && _a !== void 0 ? _a : globalThis.FIREBASE_APPCHECK_DEBUG_TOKEN = true;\n    }\n    const appCheck = zone.runOutsideAngular(() => fn(injector));\n    return new AppCheck(appCheck);\n  };\n}\nconst APP_CHECK_INSTANCES_PROVIDER = {\n  provide: AppCheckInstances,\n  deps: [[new Optional(), PROVIDED_APP_CHECK_INSTANCES]]\n};\nconst DEFAULT_APP_CHECK_INSTANCE_PROVIDER = {\n  provide: AppCheck,\n  useFactory: defaultAppCheckInstanceFactory,\n  deps: [[new Optional(), PROVIDED_APP_CHECK_INSTANCES], FirebaseApp, PLATFORM_ID]\n};\nclass AppCheckModule {\n  constructor() {\n    registerVersion('angularfire', VERSION.full, 'app-check');\n  }\n}\nAppCheckModule.ɵfac = function AppCheckModule_Factory(t) {\n  return new (t || AppCheckModule)();\n};\nAppCheckModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: AppCheckModule\n});\nAppCheckModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [DEFAULT_APP_CHECK_INSTANCE_PROVIDER, APP_CHECK_INSTANCES_PROVIDER]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AppCheckModule, [{\n    type: NgModule,\n    args: [{\n      providers: [DEFAULT_APP_CHECK_INSTANCE_PROVIDER, APP_CHECK_INSTANCES_PROVIDER]\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\nfunction provideAppCheck(fn, ...deps) {\n  return {\n    ngModule: AppCheckModule,\n    providers: [{\n      provide: PROVIDED_APP_CHECK_INSTANCES,\n      useFactory: appCheckInstanceFactory(fn),\n      multi: true,\n      deps: [NgZone, Injector, PLATFORM_ID, ɵAngularFireSchedulers, FirebaseApps, ...deps]\n    }]\n  };\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst getToken = ɵzoneWrap(getToken$1, true);\nconst initializeAppCheck = ɵzoneWrap(initializeAppCheck$1, true);\nconst onTokenChanged = ɵzoneWrap(onTokenChanged$1, true);\nconst setTokenAutoRefreshEnabled = ɵzoneWrap(setTokenAutoRefreshEnabled$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AppCheck, AppCheckInstances, AppCheckModule, appCheckInstance$, getToken, initializeAppCheck, onTokenChanged, provideAppCheck, setTokenAutoRefreshEnabled };", "map": {"version": 3, "names": ["ɵgetAllInstancesOf", "ɵgetDefaultInstanceOf", "VERSION", "ɵAngularFireSchedulers", "ɵzoneWrap", "timer", "from", "concatMap", "distinct", "i0", "InjectionToken", "isDevMode", "Optional", "PLATFORM_ID", "NgModule", "NgZone", "Injector", "FirebaseApp", "FirebaseApps", "registerVersion", "isPlatformServer", "getToken", "getToken$1", "initializeAppCheck", "initializeAppCheck$1", "onTokenChanged", "onTokenChanged$1", "setTokenAutoRefreshEnabled", "setTokenAutoRefreshEnabled$1", "APP_CHECK_PROVIDER_NAME", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "constructor", "appCheck", "AppCheckInstances", "appCheckInstance$", "pipe", "PROVIDED_APP_CHECK_INSTANCES", "APP_CHECK_NAMESPACE_SYMBOL", "Symbol", "defaultAppCheckInstanceFactory", "provided", "defaultApp", "defaultAppCheck", "LOCALHOSTS", "isLocalhost", "window", "includes", "location", "hostname", "appCheckInstanceFactory", "fn", "zone", "injector", "platformId", "_a", "globalThis", "FIREBASE_APPCHECK_DEBUG_TOKEN", "runOutsideAngular", "APP_CHECK_INSTANCES_PROVIDER", "provide", "deps", "DEFAULT_APP_CHECK_INSTANCE_PROVIDER", "useFactory", "AppCheckModule", "full", "ɵfac", "AppCheckModule_Factory", "t", "ɵmod", "ɵɵdefineNgModule", "type", "ɵinj", "ɵɵdefineInjector", "providers", "ngDevMode", "ɵsetClassMetadata", "args", "provideAppCheck", "ngModule", "multi"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/fire/fesm2015/angular-fire-app-check.js"], "sourcesContent": ["import { ɵgetAllInstancesOf, ɵgetDefaultInstanceOf, VERSION, ɵAngularFireSchedulers, ɵzoneWrap } from '@angular/fire';\nimport { timer, from } from 'rxjs';\nimport { concatMap, distinct } from 'rxjs/operators';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, isDevMode, Optional, PLATFORM_ID, NgModule, NgZone, Injector } from '@angular/core';\nimport { FirebaseApp, FirebaseApps } from '@angular/fire/app';\nimport { registerVersion } from 'firebase/app';\nimport { isPlatformServer } from '@angular/common';\nimport { getToken as getToken$1, initializeAppCheck as initializeAppCheck$1, onTokenChanged as onTokenChanged$1, setTokenAutoRefreshEnabled as setTokenAutoRefreshEnabled$1 } from 'firebase/app-check';\nexport * from 'firebase/app-check';\n\nconst APP_CHECK_PROVIDER_NAME = 'app-check';\nclass AppCheck {\n    constructor(appCheck) {\n        return appCheck;\n    }\n}\nclass AppCheckInstances {\n    constructor() {\n        return ɵgetAllInstancesOf(APP_CHECK_PROVIDER_NAME);\n    }\n}\nconst appCheckInstance$ = timer(0, 300).pipe(concatMap(() => from(ɵgetAllInstancesOf(APP_CHECK_PROVIDER_NAME))), distinct());\n\nconst PROVIDED_APP_CHECK_INSTANCES = new InjectionToken('angularfire2.app-check-instances');\nconst APP_CHECK_NAMESPACE_SYMBOL = Symbol('angularfire2.app-check.namespace');\nfunction defaultAppCheckInstanceFactory(provided, defaultApp) {\n    const defaultAppCheck = ɵgetDefaultInstanceOf(APP_CHECK_PROVIDER_NAME, provided, defaultApp);\n    return defaultAppCheck && new AppCheck(defaultAppCheck);\n}\nconst LOCALHOSTS = ['localhost', '0.0.0.0', '127.0.0.1'];\nconst isLocalhost = typeof window !== 'undefined' && LOCALHOSTS.includes(window.location.hostname);\nfunction appCheckInstanceFactory(fn) {\n    // tslint:disable-next-line:ban-types\n    return (zone, injector, platformId) => {\n        var _a;\n        // Node should use admin token provider, browser devmode and localhost should use debug token\n        if (!isPlatformServer(platformId) && (isDevMode() || isLocalhost)) {\n            (_a = globalThis.FIREBASE_APPCHECK_DEBUG_TOKEN) !== null && _a !== void 0 ? _a : (globalThis.FIREBASE_APPCHECK_DEBUG_TOKEN = true);\n        }\n        const appCheck = zone.runOutsideAngular(() => fn(injector));\n        return new AppCheck(appCheck);\n    };\n}\nconst APP_CHECK_INSTANCES_PROVIDER = {\n    provide: AppCheckInstances,\n    deps: [\n        [new Optional(), PROVIDED_APP_CHECK_INSTANCES],\n    ]\n};\nconst DEFAULT_APP_CHECK_INSTANCE_PROVIDER = {\n    provide: AppCheck,\n    useFactory: defaultAppCheckInstanceFactory,\n    deps: [\n        [new Optional(), PROVIDED_APP_CHECK_INSTANCES],\n        FirebaseApp,\n        PLATFORM_ID,\n    ]\n};\nclass AppCheckModule {\n    constructor() {\n        registerVersion('angularfire', VERSION.full, 'app-check');\n    }\n}\nAppCheckModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AppCheckModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nAppCheckModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AppCheckModule });\nAppCheckModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AppCheckModule, providers: [\n        DEFAULT_APP_CHECK_INSTANCE_PROVIDER,\n        APP_CHECK_INSTANCES_PROVIDER,\n    ] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AppCheckModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [\n                        DEFAULT_APP_CHECK_INSTANCE_PROVIDER,\n                        APP_CHECK_INSTANCES_PROVIDER,\n                    ]\n                }]\n        }], ctorParameters: function () { return []; } });\nfunction provideAppCheck(fn, ...deps) {\n    return {\n        ngModule: AppCheckModule,\n        providers: [{\n                provide: PROVIDED_APP_CHECK_INSTANCES,\n                useFactory: appCheckInstanceFactory(fn),\n                multi: true,\n                deps: [\n                    NgZone,\n                    Injector,\n                    PLATFORM_ID,\n                    ɵAngularFireSchedulers,\n                    FirebaseApps,\n                    ...deps,\n                ]\n            }]\n    };\n}\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\nconst getToken = ɵzoneWrap(getToken$1, true);\nconst initializeAppCheck = ɵzoneWrap(initializeAppCheck$1, true);\nconst onTokenChanged = ɵzoneWrap(onTokenChanged$1, true);\nconst setTokenAutoRefreshEnabled = ɵzoneWrap(setTokenAutoRefreshEnabled$1, true);\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AppCheck, AppCheckInstances, AppCheckModule, appCheckInstance$, getToken, initializeAppCheck, onTokenChanged, provideAppCheck, setTokenAutoRefreshEnabled };\n"], "mappings": "AAAA,SAASA,kBAAkB,EAAEC,qBAAqB,EAAEC,OAAO,EAAEC,sBAAsB,EAAEC,SAAS,QAAQ,eAAe;AACrH,SAASC,KAAK,EAAEC,IAAI,QAAQ,MAAM;AAClC,SAASC,SAAS,EAAEC,QAAQ,QAAQ,gBAAgB;AACpD,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,QAAQ,eAAe;AAC5G,SAASC,WAAW,EAAEC,YAAY,QAAQ,mBAAmB;AAC7D,SAASC,eAAe,QAAQ,cAAc;AAC9C,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,SAASC,QAAQ,IAAIC,UAAU,EAAEC,kBAAkB,IAAIC,oBAAoB,EAAEC,cAAc,IAAIC,gBAAgB,EAAEC,0BAA0B,IAAIC,4BAA4B,QAAQ,oBAAoB;AACvM,cAAc,oBAAoB;AAElC,MAAMC,uBAAuB,GAAG,WAAW;AAC3C,MAAMC,QAAQ,CAAC;EACXC,WAAWA,CAACC,QAAQ,EAAE;IAClB,OAAOA,QAAQ;EACnB;AACJ;AACA,MAAMC,iBAAiB,CAAC;EACpBF,WAAWA,CAAA,EAAG;IACV,OAAO/B,kBAAkB,CAAC6B,uBAAuB,CAAC;EACtD;AACJ;AACA,MAAMK,iBAAiB,GAAG7B,KAAK,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC8B,IAAI,CAAC5B,SAAS,CAAC,MAAMD,IAAI,CAACN,kBAAkB,CAAC6B,uBAAuB,CAAC,CAAC,CAAC,EAAErB,QAAQ,CAAC,CAAC,CAAC;AAE5H,MAAM4B,4BAA4B,GAAG,IAAI1B,cAAc,CAAC,kCAAkC,CAAC;AAC3F,MAAM2B,0BAA0B,GAAGC,MAAM,CAAC,kCAAkC,CAAC;AAC7E,SAASC,8BAA8BA,CAACC,QAAQ,EAAEC,UAAU,EAAE;EAC1D,MAAMC,eAAe,GAAGzC,qBAAqB,CAAC4B,uBAAuB,EAAEW,QAAQ,EAAEC,UAAU,CAAC;EAC5F,OAAOC,eAAe,IAAI,IAAIZ,QAAQ,CAACY,eAAe,CAAC;AAC3D;AACA,MAAMC,UAAU,GAAG,CAAC,WAAW,EAAE,SAAS,EAAE,WAAW,CAAC;AACxD,MAAMC,WAAW,GAAG,OAAOC,MAAM,KAAK,WAAW,IAAIF,UAAU,CAACG,QAAQ,CAACD,MAAM,CAACE,QAAQ,CAACC,QAAQ,CAAC;AAClG,SAASC,uBAAuBA,CAACC,EAAE,EAAE;EACjC;EACA,OAAO,CAACC,IAAI,EAAEC,QAAQ,EAAEC,UAAU,KAAK;IACnC,IAAIC,EAAE;IACN;IACA,IAAI,CAAClC,gBAAgB,CAACiC,UAAU,CAAC,KAAK1C,SAAS,CAAC,CAAC,IAAIiC,WAAW,CAAC,EAAE;MAC/D,CAACU,EAAE,GAAGC,UAAU,CAACC,6BAA6B,MAAM,IAAI,IAAIF,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAIC,UAAU,CAACC,6BAA6B,GAAG,IAAK;IACtI;IACA,MAAMxB,QAAQ,GAAGmB,IAAI,CAACM,iBAAiB,CAAC,MAAMP,EAAE,CAACE,QAAQ,CAAC,CAAC;IAC3D,OAAO,IAAItB,QAAQ,CAACE,QAAQ,CAAC;EACjC,CAAC;AACL;AACA,MAAM0B,4BAA4B,GAAG;EACjCC,OAAO,EAAE1B,iBAAiB;EAC1B2B,IAAI,EAAE,CACF,CAAC,IAAIhD,QAAQ,CAAC,CAAC,EAAEwB,4BAA4B,CAAC;AAEtD,CAAC;AACD,MAAMyB,mCAAmC,GAAG;EACxCF,OAAO,EAAE7B,QAAQ;EACjBgC,UAAU,EAAEvB,8BAA8B;EAC1CqB,IAAI,EAAE,CACF,CAAC,IAAIhD,QAAQ,CAAC,CAAC,EAAEwB,4BAA4B,CAAC,EAC9CnB,WAAW,EACXJ,WAAW;AAEnB,CAAC;AACD,MAAMkD,cAAc,CAAC;EACjBhC,WAAWA,CAAA,EAAG;IACVZ,eAAe,CAAC,aAAa,EAAEjB,OAAO,CAAC8D,IAAI,EAAE,WAAW,CAAC;EAC7D;AACJ;AACAD,cAAc,CAACE,IAAI,YAAAC,uBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFJ,cAAc;AAAA,CAAkD;AAC3KA,cAAc,CAACK,IAAI,kBAD8E3D,EAAE,CAAA4D,gBAAA;EAAAC,IAAA,EACSP;AAAc,EAAG;AAC7HA,cAAc,CAACQ,IAAI,kBAF8E9D,EAAE,CAAA+D,gBAAA;EAAAC,SAAA,EAEoC,CAC/HZ,mCAAmC,EACnCH,4BAA4B;AAC/B,EAAG;AACR;EAAA,QAAAgB,SAAA,oBAAAA,SAAA,KANiGjE,EAAE,CAAAkE,iBAAA,CAMRZ,cAAc,EAAc,CAAC;IAC5GO,IAAI,EAAExD,QAAQ;IACd8D,IAAI,EAAE,CAAC;MACCH,SAAS,EAAE,CACPZ,mCAAmC,EACnCH,4BAA4B;IAEpC,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;AACtD,SAASmB,eAAeA,CAAC3B,EAAE,EAAE,GAAGU,IAAI,EAAE;EAClC,OAAO;IACHkB,QAAQ,EAAEf,cAAc;IACxBU,SAAS,EAAE,CAAC;MACJd,OAAO,EAAEvB,4BAA4B;MACrC0B,UAAU,EAAEb,uBAAuB,CAACC,EAAE,CAAC;MACvC6B,KAAK,EAAE,IAAI;MACXnB,IAAI,EAAE,CACF7C,MAAM,EACNC,QAAQ,EACRH,WAAW,EACXV,sBAAsB,EACtBe,YAAY,EACZ,GAAG0C,IAAI;IAEf,CAAC;EACT,CAAC;AACL;;AAEA;AACA,MAAMvC,QAAQ,GAAGjB,SAAS,CAACkB,UAAU,EAAE,IAAI,CAAC;AAC5C,MAAMC,kBAAkB,GAAGnB,SAAS,CAACoB,oBAAoB,EAAE,IAAI,CAAC;AAChE,MAAMC,cAAc,GAAGrB,SAAS,CAACsB,gBAAgB,EAAE,IAAI,CAAC;AACxD,MAAMC,0BAA0B,GAAGvB,SAAS,CAACwB,4BAA4B,EAAE,IAAI,CAAC;;AAEhF;AACA;AACA;;AAEA,SAASE,QAAQ,EAAEG,iBAAiB,EAAE8B,cAAc,EAAE7B,iBAAiB,EAAEb,QAAQ,EAAEE,kBAAkB,EAAEE,cAAc,EAAEoD,eAAe,EAAElD,0BAA0B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}