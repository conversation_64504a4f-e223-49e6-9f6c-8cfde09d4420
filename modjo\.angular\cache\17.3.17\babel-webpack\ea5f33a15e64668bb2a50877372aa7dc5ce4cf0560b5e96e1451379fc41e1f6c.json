{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/divider\";\nfunction LoginComponent_mat_spinner_29_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 20);\n  }\n}\nfunction LoginComponent_span_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Se connecter\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(fb, authService, router, route, snackBar) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.returnUrl = '/dashboard';\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  ngOnInit() {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate([this.returnUrl]);\n    }\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.loginForm.invalid) {\n        _this.markFormGroupTouched();\n        return;\n      }\n      _this.isLoading = true;\n      const {\n        email,\n        password\n      } = _this.loginForm.value;\n      try {\n        yield _this.authService.login(email, password);\n        _this.snackBar.open('Connexion réussie!', 'Fermer', {\n          duration: 3000\n        });\n        _this.router.navigate([_this.returnUrl]);\n      } catch (error) {\n        console.error('Login error:', error);\n        _this.snackBar.open(_this.getErrorMessage(error), 'Fermer', {\n          duration: 5000\n        });\n      } finally {\n        _this.isLoading = false;\n      }\n    })();\n  }\n  getErrorMessage(error) {\n    if (error.code) {\n      switch (error.code) {\n        case 'auth/user-not-found':\n          return 'Aucun utilisateur trouvé avec cette adresse email.';\n        case 'auth/wrong-password':\n          return 'Mot de passe incorrect.';\n        case 'auth/invalid-email':\n          return 'Adresse email invalide.';\n        case 'auth/user-disabled':\n          return 'Ce compte a été désactivé.';\n        case 'auth/too-many-requests':\n          return 'Trop de tentatives. Veuillez réessayer plus tard.';\n        default:\n          return 'Erreur de connexion. Veuillez réessayer.';\n      }\n    }\n    return 'Erreur de connexion. Veuillez réessayer.';\n  }\n  markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getFieldError(fieldName) {\n    const field = this.loginForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) {\n        return `${fieldName === 'email' ? 'Email' : 'Mot de passe'} requis`;\n      }\n      if (field.errors['email']) {\n        return 'Format email invalide';\n      }\n      if (field.errors['minlength']) {\n        return 'Mot de passe trop court (min. 6 caractères)';\n      }\n    }\n    return '';\n  }\n  // Demo login methods\n  loginAsUser() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.loginForm.patchValue({\n        email: '<EMAIL>',\n        password: 'password123'\n      });\n      yield _this2.onSubmit();\n    })();\n  }\n  loginAsValidator() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.loginForm.patchValue({\n        email: '<EMAIL>',\n        password: 'password123'\n      });\n      yield _this3.onSubmit();\n    })();\n  }\n  loginAsAdmin() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4.loginForm.patchValue({\n        email: '<EMAIL>',\n        password: 'password123'\n      });\n      yield _this4.onSubmit();\n    })();\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 53,\n      vars: 10,\n      consts: [[1, \"login-container\"], [1, \"login-card\"], [1, \"login-header\"], [\"src\", \"assets/logo.png\", \"alt\", \"Modjo\", 1, \"logo\"], [1, \"login-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Votre mot de passe\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"login-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"register-link\"], [\"routerLink\", \"/auth/register\", 1, \"link\"], [1, \"demo-section\"], [1, \"demo-buttons\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"demo-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"accent\", 1, \"demo-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"warn\", 1, \"demo-button\", 3, \"click\"], [\"diameter\", \"20\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"div\", 2);\n          i0.ɵɵelement(4, \"img\", 3);\n          i0.ɵɵelementStart(5, \"h1\");\n          i0.ɵɵtext(6, \"Modjo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\");\n          i0.ɵɵtext(8, \"Connectez-vous \\u00E0 votre compte\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(11, \"mat-form-field\", 5)(12, \"mat-label\");\n          i0.ɵɵtext(13, \"Adresse email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"input\", 6);\n          i0.ɵɵelementStart(15, \"mat-icon\", 7);\n          i0.ɵɵtext(16, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"mat-error\");\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"mat-form-field\", 5)(20, \"mat-label\");\n          i0.ɵɵtext(21, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 8);\n          i0.ɵɵelementStart(23, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_23_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(24, \"mat-icon\");\n          i0.ɵɵtext(25);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(26, \"mat-error\");\n          i0.ɵɵtext(27);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(28, \"button\", 10);\n          i0.ɵɵtemplate(29, LoginComponent_mat_spinner_29_Template, 1, 0, \"mat-spinner\", 11)(30, LoginComponent_span_30_Template, 2, 0, \"span\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"div\", 13)(32, \"p\");\n          i0.ɵɵtext(33, \"Pas encore de compte? \");\n          i0.ɵɵelementStart(34, \"a\", 14);\n          i0.ɵɵtext(35, \"Cr\\u00E9er un compte\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(36, \"div\", 15);\n          i0.ɵɵelement(37, \"mat-divider\");\n          i0.ɵɵelementStart(38, \"h3\");\n          i0.ɵɵtext(39, \"Connexion d\\u00E9mo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\", 16)(41, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_41_listener() {\n            return ctx.loginAsUser();\n          });\n          i0.ɵɵelementStart(42, \"mat-icon\");\n          i0.ɵɵtext(43, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(44, \" Utilisateur \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_45_listener() {\n            return ctx.loginAsValidator();\n          });\n          i0.ɵɵelementStart(46, \"mat-icon\");\n          i0.ɵɵtext(47, \"verified\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(48, \" Validateur \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(49, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_49_listener() {\n            return ctx.loginAsAdmin();\n          });\n          i0.ɵɵelementStart(50, \"mat-icon\");\n          i0.ɵɵtext(51, \"admin_panel_settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(52, \" Admin \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"email\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"password\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatButton, i9.MatIconButton, i10.MatIcon, i11.MatProgressSpinner, i12.MatDivider],\n      styles: [\".login-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.login-container[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\\n    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 450px;\\n  padding: 40px;\\n  border-radius: 24px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  position: relative;\\n  z-index: 1;\\n  animation: slideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.login-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  margin-bottom: 16px;\\n}\\n\\n.login-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n  font-size: 2rem;\\n  font-weight: 600;\\n}\\n\\n.login-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.login-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  margin-top: 8px;\\n}\\n\\n.register-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 16px;\\n}\\n\\n.register-link[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.demo-section[_ngcontent-%COMP%] {\\n  margin-top: 32px;\\n  text-align: center;\\n}\\n\\n.demo-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n  font-weight: 500;\\n}\\n\\n.demo-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.demo-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  height: 40px;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .login-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  \\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  \\n  .logo[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  \\n  .login-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "LoginComponent", "constructor", "fb", "authService", "router", "route", "snackBar", "isLoading", "hidePassword", "returnUrl", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "snapshot", "queryParams", "isAuthenticated", "navigate", "onSubmit", "_this", "_asyncToGenerator", "invalid", "markFormGroupTouched", "value", "login", "open", "duration", "error", "console", "getErrorMessage", "code", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "getFieldError", "fieldName", "field", "errors", "touched", "loginAsUser", "_this2", "patchValue", "loginAsValidator", "_this3", "loginAsAdmin", "_this4", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_10_listener", "LoginComponent_Template_button_click_23_listener", "ɵɵtemplate", "LoginComponent_mat_spinner_29_Template", "LoginComponent_span_30_Template", "LoginComponent_Template_button_click_41_listener", "LoginComponent_Template_button_click_45_listener", "LoginComponent_Template_button_click_49_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\auth\\components\\login\\login.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\auth\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { AuthService } from '../../../../core/services/auth.service';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  isLoading = false;\n  hidePassword = true;\n  returnUrl = '/dashboard';\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  ngOnInit(): void {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    \n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate([this.returnUrl]);\n    }\n  }\n\n  async onSubmit(): Promise<void> {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.isLoading = true;\n    const { email, password } = this.loginForm.value;\n\n    try {\n      await this.authService.login(email, password);\n      this.snackBar.open('Connexion réussie!', 'Fermer', { duration: 3000 });\n      this.router.navigate([this.returnUrl]);\n    } catch (error: any) {\n      console.error('Login error:', error);\n      this.snackBar.open(\n        this.getErrorMessage(error), \n        'Fermer', \n        { duration: 5000 }\n      );\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  private getErrorMessage(error: any): string {\n    if (error.code) {\n      switch (error.code) {\n        case 'auth/user-not-found':\n          return 'Aucun utilisateur trouvé avec cette adresse email.';\n        case 'auth/wrong-password':\n          return 'Mot de passe incorrect.';\n        case 'auth/invalid-email':\n          return 'Adresse email invalide.';\n        case 'auth/user-disabled':\n          return 'Ce compte a été désactivé.';\n        case 'auth/too-many-requests':\n          return 'Trop de tentatives. Veuillez réessayer plus tard.';\n        default:\n          return 'Erreur de connexion. Veuillez réessayer.';\n      }\n    }\n    return 'Erreur de connexion. Veuillez réessayer.';\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.loginForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) {\n        return `${fieldName === 'email' ? 'Email' : 'Mot de passe'} requis`;\n      }\n      if (field.errors['email']) {\n        return 'Format email invalide';\n      }\n      if (field.errors['minlength']) {\n        return 'Mot de passe trop court (min. 6 caractères)';\n      }\n    }\n    return '';\n  }\n\n  // Demo login methods\n  async loginAsUser(): Promise<void> {\n    this.loginForm.patchValue({\n      email: '<EMAIL>',\n      password: 'password123'\n    });\n    await this.onSubmit();\n  }\n\n  async loginAsValidator(): Promise<void> {\n    this.loginForm.patchValue({\n      email: '<EMAIL>',\n      password: 'password123'\n    });\n    await this.onSubmit();\n  }\n\n  async loginAsAdmin(): Promise<void> {\n    this.loginForm.patchValue({\n      email: '<EMAIL>',\n      password: 'password123'\n    });\n    await this.onSubmit();\n  }\n}\n", "<div class=\"login-container\">\n  <mat-card class=\"login-card\">\n    <mat-card-header>\n      <div class=\"login-header\">\n        <img src=\"assets/logo.png\" alt=\"Modjo\" class=\"logo\">\n        <h1>Modjo</h1>\n        <p>Connectez-vous à votre compte</p>\n      </div>\n    </mat-card-header>\n\n    <mat-card-content>\n      <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\">\n        <!-- Email field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Adresse email</mat-label>\n          <input matInput \n                 type=\"email\" \n                 formControlName=\"email\"\n                 placeholder=\"<EMAIL>\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error>{{ getFieldError('email') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Password field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Mot de passe</mat-label>\n          <input matInput \n                 [type]=\"hidePassword ? 'password' : 'text'\"\n                 formControlName=\"password\"\n                 placeholder=\"Votre mot de passe\">\n          <button mat-icon-button \n                  matSuffix \n                  type=\"button\"\n                  (click)=\"hidePassword = !hidePassword\"\n                  [attr.aria-label]=\"'Hide password'\"\n                  [attr.aria-pressed]=\"hidePassword\">\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError('password') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Submit button -->\n        <button mat-raised-button \n                color=\"primary\" \n                type=\"submit\"\n                class=\"full-width login-button\"\n                [disabled]=\"isLoading\">\n          <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n          <span *ngIf=\"!isLoading\">Se connecter</span>\n        </button>\n\n        <!-- Register link -->\n        <div class=\"register-link\">\n          <p>Pas encore de compte? \n            <a routerLink=\"/auth/register\" class=\"link\">Créer un compte</a>\n          </p>\n        </div>\n      </form>\n\n      <!-- Demo login buttons -->\n      <div class=\"demo-section\">\n        <mat-divider></mat-divider>\n        <h3>Connexion démo</h3>\n        <div class=\"demo-buttons\">\n          <button mat-stroked-button \n                  color=\"primary\" \n                  (click)=\"loginAsUser()\"\n                  class=\"demo-button\">\n            <mat-icon>person</mat-icon>\n            Utilisateur\n          </button>\n          \n          <button mat-stroked-button \n                  color=\"accent\" \n                  (click)=\"loginAsValidator()\"\n                  class=\"demo-button\">\n            <mat-icon>verified</mat-icon>\n            Validateur\n          </button>\n          \n          <button mat-stroked-button \n                  color=\"warn\" \n                  (click)=\"loginAsAdmin()\"\n                  class=\"demo-button\">\n            <mat-icon>admin_panel_settings</mat-icon>\n            Admin\n          </button>\n        </div>\n      </div>\n    </mat-card-content>\n  </mat-card>\n</div>\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;IC8CzDC,EAAA,CAAAC,SAAA,sBAA2D;;;;;IAC3DD,EAAA,CAAAE,cAAA,WAAyB;IAAAF,EAAA,CAAAG,MAAA,mBAAY;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;ADrCtD,OAAM,MAAOC,cAAc;EAMzBC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,YAAY;IAStB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACkB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACpB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACqB,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,SAAS,GAAG,IAAI,CAACJ,KAAK,CAACY,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,YAAY;IAE7E;IACA,IAAI,IAAI,CAACf,WAAW,CAACgB,eAAe,EAAE,EAAE;MACtC,IAAI,CAACf,MAAM,CAACgB,QAAQ,CAAC,CAAC,IAAI,CAACX,SAAS,CAAC,CAAC;;EAE1C;EAEMY,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,IAAID,KAAI,CAACZ,SAAS,CAACc,OAAO,EAAE;QAC1BF,KAAI,CAACG,oBAAoB,EAAE;QAC3B;;MAGFH,KAAI,CAACf,SAAS,GAAG,IAAI;MACrB,MAAM;QAAEK,KAAK;QAAEE;MAAQ,CAAE,GAAGQ,KAAI,CAACZ,SAAS,CAACgB,KAAK;MAEhD,IAAI;QACF,MAAMJ,KAAI,CAACnB,WAAW,CAACwB,KAAK,CAACf,KAAK,EAAEE,QAAQ,CAAC;QAC7CQ,KAAI,CAAChB,QAAQ,CAACsB,IAAI,CAAC,oBAAoB,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACtEP,KAAI,CAAClB,MAAM,CAACgB,QAAQ,CAAC,CAACE,KAAI,CAACb,SAAS,CAAC,CAAC;OACvC,CAAC,OAAOqB,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;QACpCR,KAAI,CAAChB,QAAQ,CAACsB,IAAI,CAChBN,KAAI,CAACU,eAAe,CAACF,KAAK,CAAC,EAC3B,QAAQ,EACR;UAAED,QAAQ,EAAE;QAAI,CAAE,CACnB;OACF,SAAS;QACRP,KAAI,CAACf,SAAS,GAAG,KAAK;;IACvB;EACH;EAEQyB,eAAeA,CAACF,KAAU;IAChC,IAAIA,KAAK,CAACG,IAAI,EAAE;MACd,QAAQH,KAAK,CAACG,IAAI;QAChB,KAAK,qBAAqB;UACxB,OAAO,oDAAoD;QAC7D,KAAK,qBAAqB;UACxB,OAAO,yBAAyB;QAClC,KAAK,oBAAoB;UACvB,OAAO,yBAAyB;QAClC,KAAK,oBAAoB;UACvB,OAAO,4BAA4B;QACrC,KAAK,wBAAwB;UAC3B,OAAO,mDAAmD;QAC5D;UACE,OAAO,0CAA0C;;;IAGvD,OAAO,0CAA0C;EACnD;EAEQR,oBAAoBA,CAAA;IAC1BS,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzB,SAAS,CAAC0B,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACjD,MAAMC,OAAO,GAAG,IAAI,CAAC7B,SAAS,CAAC8B,GAAG,CAACF,GAAG,CAAC;MACvCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAACC,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAClC,SAAS,CAAC8B,GAAG,CAACG,SAAS,CAAC;IAC3C,IAAIC,KAAK,EAAEC,MAAM,IAAID,KAAK,CAACE,OAAO,EAAE;MAClC,IAAIF,KAAK,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,GAAGF,SAAS,KAAK,OAAO,GAAG,OAAO,GAAG,cAAc,SAAS;;MAErE,IAAIC,KAAK,CAACC,MAAM,CAAC,OAAO,CAAC,EAAE;QACzB,OAAO,uBAAuB;;MAEhC,IAAID,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,EAAE;QAC7B,OAAO,6CAA6C;;;IAGxD,OAAO,EAAE;EACX;EAEA;EACME,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAzB,iBAAA;MACfyB,MAAI,CAACtC,SAAS,CAACuC,UAAU,CAAC;QACxBrC,KAAK,EAAE,eAAe;QACtBE,QAAQ,EAAE;OACX,CAAC;MACF,MAAMkC,MAAI,CAAC3B,QAAQ,EAAE;IAAC;EACxB;EAEM6B,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA5B,iBAAA;MACpB4B,MAAI,CAACzC,SAAS,CAACuC,UAAU,CAAC;QACxBrC,KAAK,EAAE,oBAAoB;QAC3BE,QAAQ,EAAE;OACX,CAAC;MACF,MAAMqC,MAAI,CAAC9B,QAAQ,EAAE;IAAC;EACxB;EAEM+B,YAAYA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA9B,iBAAA;MAChB8B,MAAI,CAAC3C,SAAS,CAACuC,UAAU,CAAC;QACxBrC,KAAK,EAAE,gBAAgB;QACvBE,QAAQ,EAAE;OACX,CAAC;MACF,MAAMuC,MAAI,CAAChC,QAAQ,EAAE;IAAC;EACxB;;;uBAxHWrB,cAAc,EAAAL,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7D,EAAA,CAAA2D,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA/D,EAAA,CAAA2D,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAjE,EAAA,CAAA2D,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAAlE,EAAA,CAAA2D,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAd/D,cAAc;MAAAgE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCRrB3E,EAHN,CAAAE,cAAA,aAA6B,kBACE,sBACV,aACW;UACxBF,EAAA,CAAAC,SAAA,aAAoD;UACpDD,EAAA,CAAAE,cAAA,SAAI;UAAAF,EAAA,CAAAG,MAAA,YAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAE,cAAA,QAAG;UAAAF,EAAA,CAAAG,MAAA,yCAA6B;UAEpCH,EAFoC,CAAAI,YAAA,EAAI,EAChC,EACU;UAGhBJ,EADF,CAAAE,cAAA,uBAAkB,eACyD;UAA3CF,EAAA,CAAA6E,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAAlD,QAAA,EAAU;UAAA,EAAC;UAGjD1B,EADF,CAAAE,cAAA,yBAAwD,iBAC3C;UAAAF,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAY;UACpCJ,EAAA,CAAAC,SAAA,gBAGqC;UACrCD,EAAA,CAAAE,cAAA,mBAAoB;UAAAF,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAW;UACpCJ,EAAA,CAAAE,cAAA,iBAAW;UAAAF,EAAA,CAAAG,MAAA,IAA4B;UACzCH,EADyC,CAAAI,YAAA,EAAY,EACpC;UAIfJ,EADF,CAAAE,cAAA,yBAAwD,iBAC3C;UAAAF,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAY;UACnCJ,EAAA,CAAAC,SAAA,gBAGwC;UACxCD,EAAA,CAAAE,cAAA,iBAK2C;UAFnCF,EAAA,CAAA6E,UAAA,mBAAAE,iDAAA;YAAA,OAAAH,GAAA,CAAA/D,YAAA,IAAA+D,GAAA,CAAA/D,YAAA;UAAA,EAAsC;UAG5Cb,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,IAAoD;UAChEH,EADgE,CAAAI,YAAA,EAAW,EAClE;UACTJ,EAAA,CAAAE,cAAA,iBAAW;UAAAF,EAAA,CAAAG,MAAA,IAA+B;UAC5CH,EAD4C,CAAAI,YAAA,EAAY,EACvC;UAGjBJ,EAAA,CAAAE,cAAA,kBAI+B;UAE7BF,EADA,CAAAgF,UAAA,KAAAC,sCAAA,0BAA6C,KAAAC,+BAAA,mBACpB;UAC3BlF,EAAA,CAAAI,YAAA,EAAS;UAIPJ,EADF,CAAAE,cAAA,eAA2B,SACtB;UAAAF,EAAA,CAAAG,MAAA,8BACD;UAAAH,EAAA,CAAAE,cAAA,aAA4C;UAAAF,EAAA,CAAAG,MAAA,4BAAe;UAGjEH,EAHiE,CAAAI,YAAA,EAAI,EAC7D,EACA,EACD;UAGPJ,EAAA,CAAAE,cAAA,eAA0B;UACxBF,EAAA,CAAAC,SAAA,mBAA2B;UAC3BD,EAAA,CAAAE,cAAA,UAAI;UAAAF,EAAA,CAAAG,MAAA,2BAAc;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAErBJ,EADF,CAAAE,cAAA,eAA0B,kBAII;UADpBF,EAAA,CAAA6E,UAAA,mBAAAM,iDAAA;YAAA,OAASP,GAAA,CAAAxB,WAAA,EAAa;UAAA,EAAC;UAE7BpD,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAC3BJ,EAAA,CAAAG,MAAA,qBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAETJ,EAAA,CAAAE,cAAA,kBAG4B;UADpBF,EAAA,CAAA6E,UAAA,mBAAAO,iDAAA;YAAA,OAASR,GAAA,CAAArB,gBAAA,EAAkB;UAAA,EAAC;UAElCvD,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAC7BJ,EAAA,CAAAG,MAAA,oBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAETJ,EAAA,CAAAE,cAAA,kBAG4B;UADpBF,EAAA,CAAA6E,UAAA,mBAAAQ,iDAAA;YAAA,OAAST,GAAA,CAAAnB,YAAA,EAAc;UAAA,EAAC;UAE9BzD,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,4BAAoB;UAAAH,EAAA,CAAAI,YAAA,EAAW;UACzCJ,EAAA,CAAAG,MAAA,eACF;UAKVH,EALU,CAAAI,YAAA,EAAS,EACL,EACF,EACW,EACV,EACP;;;UAhFMJ,EAAA,CAAAsF,SAAA,IAAuB;UAAvBtF,EAAA,CAAAuF,UAAA,cAAAX,GAAA,CAAA7D,SAAA,CAAuB;UASdf,EAAA,CAAAsF,SAAA,GAA4B;UAA5BtF,EAAA,CAAAwF,iBAAA,CAAAZ,GAAA,CAAA7B,aAAA,UAA4B;UAOhC/C,EAAA,CAAAsF,SAAA,GAA2C;UAA3CtF,EAAA,CAAAuF,UAAA,SAAAX,GAAA,CAAA/D,YAAA,uBAA2C;UAO1Cb,EAAA,CAAAsF,SAAA,EAAmC;;UAE/BtF,EAAA,CAAAsF,SAAA,GAAoD;UAApDtF,EAAA,CAAAwF,iBAAA,CAAAZ,GAAA,CAAA/D,YAAA,mCAAoD;UAErDb,EAAA,CAAAsF,SAAA,GAA+B;UAA/BtF,EAAA,CAAAwF,iBAAA,CAAAZ,GAAA,CAAA7B,aAAA,aAA+B;UAQpC/C,EAAA,CAAAsF,SAAA,EAAsB;UAAtBtF,EAAA,CAAAuF,UAAA,aAAAX,GAAA,CAAAhE,SAAA,CAAsB;UACAZ,EAAA,CAAAsF,SAAA,EAAe;UAAftF,EAAA,CAAAuF,UAAA,SAAAX,GAAA,CAAAhE,SAAA,CAAe;UACpCZ,EAAA,CAAAsF,SAAA,EAAgB;UAAhBtF,EAAA,CAAAuF,UAAA,UAAAX,GAAA,CAAAhE,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}