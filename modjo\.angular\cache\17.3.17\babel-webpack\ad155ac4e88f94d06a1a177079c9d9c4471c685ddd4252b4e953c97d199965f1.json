{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, inject, ViewChild, Injector, TemplateRef, Injectable, Optional, SkipSelf, NgModule } from '@angular/core';\nimport { MatButton, MatButtonModule } from '@angular/material/button';\nimport { Subject } from 'rxjs';\nimport { DOCUMENT } from '@angular/common';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport * as i1 from '@angular/cdk/platform';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/layout';\nimport { Breakpoints } from '@angular/cdk/layout';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { takeUntil } from 'rxjs/operators';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nfunction SimpleSnackBar_Conditional_2_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"button\", 2);\n    i0.ɵɵlistener(\"click\", function SimpleSnackBar_Conditional_2_Template_button_click_1_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.action());\n    });\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.data.action, \" \");\n  }\n}\nconst _c0 = [\"label\"];\nfunction MatSnackBarContainer_ng_template_4_Template(rf, ctx) {}\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n  constructor(containerInstance, _overlayRef) {\n    this._overlayRef = _overlayRef;\n    /** Subject for notifying the user that the snack bar has been dismissed. */\n    this._afterDismissed = new Subject();\n    /** Subject for notifying the user that the snack bar has opened and appeared. */\n    this._afterOpened = new Subject();\n    /** Subject for notifying the user that the snack bar action was called. */\n    this._onAction = new Subject();\n    /** Whether the snack bar was dismissed using the action button. */\n    this._dismissedByAction = false;\n    this.containerInstance = containerInstance;\n    containerInstance._onExit.subscribe(() => this._finishDismiss());\n  }\n  /** Dismisses the snack bar. */\n  dismiss() {\n    if (!this._afterDismissed.closed) {\n      this.containerInstance.exit();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /** Marks the snackbar action clicked. */\n  dismissWithAction() {\n    if (!this._onAction.closed) {\n      this._dismissedByAction = true;\n      this._onAction.next();\n      this._onAction.complete();\n      this.dismiss();\n    }\n    clearTimeout(this._durationTimeoutId);\n  }\n  /**\n   * Marks the snackbar action clicked.\n   * @deprecated Use `dismissWithAction` instead.\n   * @breaking-change 8.0.0\n   */\n  closeWithAction() {\n    this.dismissWithAction();\n  }\n  /** Dismisses the snack bar after some duration */\n  _dismissAfter(duration) {\n    // Note that we need to cap the duration to the maximum value for setTimeout, because\n    // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n    this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n  }\n  /** Marks the snackbar as opened */\n  _open() {\n    if (!this._afterOpened.closed) {\n      this._afterOpened.next();\n      this._afterOpened.complete();\n    }\n  }\n  /** Cleans up the DOM after closing. */\n  _finishDismiss() {\n    this._overlayRef.dispose();\n    if (!this._onAction.closed) {\n      this._onAction.complete();\n    }\n    this._afterDismissed.next({\n      dismissedByAction: this._dismissedByAction\n    });\n    this._afterDismissed.complete();\n    this._dismissedByAction = false;\n  }\n  /** Gets an observable that is notified when the snack bar is finished closing. */\n  afterDismissed() {\n    return this._afterDismissed;\n  }\n  /** Gets an observable that is notified when the snack bar has opened and appeared. */\n  afterOpened() {\n    return this.containerInstance._onEnter;\n  }\n  /** Gets an observable that is notified when the snack bar action is called. */\n  onAction() {\n    return this._onAction;\n  }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n  constructor() {\n    /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n    this.politeness = 'assertive';\n    /**\n     * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n     * component or template, the announcement message will default to the specified message.\n     */\n    this.announcementMessage = '';\n    /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n    this.duration = 0;\n    /** Data being injected into the child component. */\n    this.data = null;\n    /** The horizontal position to place the snack bar. */\n    this.horizontalPosition = 'center';\n    /** The vertical position to place the snack bar. */\n    this.verticalPosition = 'bottom';\n  }\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {\n  static {\n    this.ɵfac = function MatSnackBarLabel_Factory(t) {\n      return new (t || MatSnackBarLabel)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSnackBarLabel,\n      selectors: [[\"\", \"matSnackBarLabel\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-snack-bar-label\", \"mdc-snackbar__label\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarLabel, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarLabel]`,\n      standalone: true,\n      host: {\n        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {\n  static {\n    this.ɵfac = function MatSnackBarActions_Factory(t) {\n      return new (t || MatSnackBarActions)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSnackBarActions,\n      selectors: [[\"\", \"matSnackBarActions\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-snack-bar-actions\", \"mdc-snackbar__actions\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarActions, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarActions]`,\n      standalone: true,\n      host: {\n        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions'\n      }\n    }]\n  }], null, null);\n})();\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {\n  static {\n    this.ɵfac = function MatSnackBarAction_Factory(t) {\n      return new (t || MatSnackBarAction)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatSnackBarAction,\n      selectors: [[\"\", \"matSnackBarAction\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-snack-bar-action\", \"mdc-snackbar__action\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarAction, [{\n    type: Directive,\n    args: [{\n      selector: `[matSnackBarAction]`,\n      standalone: true,\n      host: {\n        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action'\n      }\n    }]\n  }], null, null);\n})();\nclass SimpleSnackBar {\n  constructor(snackBarRef, data) {\n    this.snackBarRef = snackBarRef;\n    this.data = data;\n  }\n  /** Performs the action on the snack bar. */\n  action() {\n    this.snackBarRef.dismissWithAction();\n  }\n  /** If the action button should be shown. */\n  get hasAction() {\n    return !!this.data.action;\n  }\n  static {\n    this.ɵfac = function SimpleSnackBar_Factory(t) {\n      return new (t || SimpleSnackBar)(i0.ɵɵdirectiveInject(MatSnackBarRef), i0.ɵɵdirectiveInject(MAT_SNACK_BAR_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: SimpleSnackBar,\n      selectors: [[\"simple-snack-bar\"]],\n      hostAttrs: [1, \"mat-mdc-simple-snack-bar\"],\n      exportAs: [\"matSnackBar\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      decls: 3,\n      vars: 2,\n      consts: [[\"matSnackBarLabel\", \"\"], [\"matSnackBarActions\", \"\"], [\"mat-button\", \"\", \"matSnackBarAction\", \"\", 3, \"click\"]],\n      template: function SimpleSnackBar_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵtext(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtemplate(2, SimpleSnackBar_Conditional_2_Template, 3, 1, \"div\", 1);\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate1(\" \", ctx.data.message, \"\\n\");\n          i0.ɵɵadvance();\n          i0.ɵɵconditional(2, ctx.hasAction ? 2 : -1);\n        }\n      },\n      dependencies: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n      styles: [\".mat-mdc-simple-snack-bar{display:flex}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SimpleSnackBar, [{\n    type: Component,\n    args: [{\n      selector: 'simple-snack-bar',\n      exportAs: 'matSnackBar',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      imports: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction],\n      standalone: true,\n      host: {\n        'class': 'mat-mdc-simple-snack-bar'\n      },\n      template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\",\n      styles: [\".mat-mdc-simple-snack-bar{display:flex}\"]\n    }]\n  }], () => [{\n    type: MatSnackBarRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_SNACK_BAR_DATA]\n    }]\n  }], null);\n})();\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n */\nconst matSnackBarAnimations = {\n  /** Animation that shows and hides a snack bar. */\n  snackBarState: trigger('state', [state('void, hidden', style({\n    transform: 'scale(0.8)',\n    opacity: 0\n  })), state('visible', style({\n    transform: 'scale(1)',\n    opacity: 1\n  })), transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')), transition('* => void, * => hidden', animate('75ms cubic-bezier(0.4, 0.0, 1, 1)', style({\n    opacity: 0\n  })))])\n};\nlet uniqueId = 0;\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends BasePortalOutlet {\n  constructor(_ngZone, _elementRef, _changeDetectorRef, _platform, /** The snack bar configuration. */\n  snackBarConfig) {\n    super();\n    this._ngZone = _ngZone;\n    this._elementRef = _elementRef;\n    this._changeDetectorRef = _changeDetectorRef;\n    this._platform = _platform;\n    this.snackBarConfig = snackBarConfig;\n    this._document = inject(DOCUMENT);\n    this._trackedModals = new Set();\n    /** The number of milliseconds to wait before announcing the snack bar's content. */\n    this._announceDelay = 150;\n    /** Whether the component has been destroyed. */\n    this._destroyed = false;\n    /** Subject for notifying that the snack bar has announced to screen readers. */\n    this._onAnnounce = new Subject();\n    /** Subject for notifying that the snack bar has exited from view. */\n    this._onExit = new Subject();\n    /** Subject for notifying that the snack bar has finished entering the view. */\n    this._onEnter = new Subject();\n    /** The state of the snack bar animations. */\n    this._animationState = 'void';\n    /** Unique ID of the aria-live element. */\n    this._liveElementId = `mat-snack-bar-container-live-${uniqueId++}`;\n    /**\n     * Attaches a DOM portal to the snack bar container.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    this.attachDomPortal = portal => {\n      this._assertNotAttached();\n      const result = this._portalOutlet.attachDomPortal(portal);\n      this._afterPortalAttached();\n      return result;\n    };\n    // Use aria-live rather than a live role like 'alert' or 'status'\n    // because NVDA and JAWS have show inconsistent behavior with live roles.\n    if (snackBarConfig.politeness === 'assertive' && !snackBarConfig.announcementMessage) {\n      this._live = 'assertive';\n    } else if (snackBarConfig.politeness === 'off') {\n      this._live = 'off';\n    } else {\n      this._live = 'polite';\n    }\n    // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n    // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n    if (this._platform.FIREFOX) {\n      if (this._live === 'polite') {\n        this._role = 'status';\n      }\n      if (this._live === 'assertive') {\n        this._role = 'alert';\n      }\n    }\n  }\n  /** Attach a component portal as content to this snack bar container. */\n  attachComponentPortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /** Attach a template portal as content to this snack bar container. */\n  attachTemplatePortal(portal) {\n    this._assertNotAttached();\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._afterPortalAttached();\n    return result;\n  }\n  /** Handle end of animations, updating the state of the snackbar. */\n  onAnimationEnd(event) {\n    const {\n      fromState,\n      toState\n    } = event;\n    if (toState === 'void' && fromState !== 'void' || toState === 'hidden') {\n      this._completeExit();\n    }\n    if (toState === 'visible') {\n      // Note: we shouldn't use `this` inside the zone callback,\n      // because it can cause a memory leak.\n      const onEnter = this._onEnter;\n      this._ngZone.run(() => {\n        onEnter.next();\n        onEnter.complete();\n      });\n    }\n  }\n  /** Begin animation of snack bar entrance into view. */\n  enter() {\n    if (!this._destroyed) {\n      this._animationState = 'visible';\n      // _animationState lives in host bindings and `detectChanges` does not refresh host bindings\n      // so we have to call `markForCheck` to ensure the host view is refreshed eventually.\n      this._changeDetectorRef.markForCheck();\n      this._changeDetectorRef.detectChanges();\n      this._screenReaderAnnounce();\n    }\n  }\n  /** Begin animation of the snack bar exiting from view. */\n  exit() {\n    // It's common for snack bars to be opened by random outside calls like HTTP requests or\n    // errors. Run inside the NgZone to ensure that it functions correctly.\n    this._ngZone.run(() => {\n      // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n      // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n      // `MatSnackBar.open`).\n      this._animationState = 'hidden';\n      this._changeDetectorRef.markForCheck();\n      // Mark this element with an 'exit' attribute to indicate that the snackbar has\n      // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n      // test harness.\n      this._elementRef.nativeElement.setAttribute('mat-exit', '');\n      // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n      // long enough to visually read it either, so clear the timeout for announcing.\n      clearTimeout(this._announceTimeoutId);\n    });\n    return this._onExit;\n  }\n  /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n  ngOnDestroy() {\n    this._destroyed = true;\n    this._clearFromModals();\n    this._completeExit();\n  }\n  /**\n   * Removes the element in a microtask. Helps prevent errors where we end up\n   * removing an element which is in the middle of an animation.\n   */\n  _completeExit() {\n    queueMicrotask(() => {\n      this._onExit.next();\n      this._onExit.complete();\n    });\n  }\n  /**\n   * Called after the portal contents have been attached. Can be\n   * used to modify the DOM once it's guaranteed to be in place.\n   */\n  _afterPortalAttached() {\n    const element = this._elementRef.nativeElement;\n    const panelClasses = this.snackBarConfig.panelClass;\n    if (panelClasses) {\n      if (Array.isArray(panelClasses)) {\n        // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n        panelClasses.forEach(cssClass => element.classList.add(cssClass));\n      } else {\n        element.classList.add(panelClasses);\n      }\n    }\n    this._exposeToModals();\n    // Check to see if the attached component or template uses the MDC template structure,\n    // specifically the MDC label. If not, the container should apply the MDC label class to this\n    // component's label container, which will apply MDC's label styles to the attached view.\n    const label = this._label.nativeElement;\n    const labelClass = 'mdc-snackbar__label';\n    label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n  }\n  /**\n   * Some browsers won't expose the accessibility node of the live element if there is an\n   * `aria-modal` and the live element is outside of it. This method works around the issue by\n   * pointing the `aria-owns` of all modals to the live element.\n   */\n  _exposeToModals() {\n    // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n    // `LiveAnnouncer` and any other usages.\n    //\n    // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n    // section of the DOM we need to look through. This should cover all the cases we support, but\n    // the selector can be expanded if it turns out to be too narrow.\n    const id = this._liveElementId;\n    const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n    for (let i = 0; i < modals.length; i++) {\n      const modal = modals[i];\n      const ariaOwns = modal.getAttribute('aria-owns');\n      this._trackedModals.add(modal);\n      if (!ariaOwns) {\n        modal.setAttribute('aria-owns', id);\n      } else if (ariaOwns.indexOf(id) === -1) {\n        modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n      }\n    }\n  }\n  /** Clears the references to the live element from any modals it was added to. */\n  _clearFromModals() {\n    this._trackedModals.forEach(modal => {\n      const ariaOwns = modal.getAttribute('aria-owns');\n      if (ariaOwns) {\n        const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n        if (newValue.length > 0) {\n          modal.setAttribute('aria-owns', newValue);\n        } else {\n          modal.removeAttribute('aria-owns');\n        }\n      }\n    });\n    this._trackedModals.clear();\n  }\n  /** Asserts that no content is already attached to the container. */\n  _assertNotAttached() {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error('Attempting to attach snack bar content after content is already attached');\n    }\n  }\n  /**\n   * Starts a timeout to move the snack bar content to the live region so screen readers will\n   * announce it.\n   */\n  _screenReaderAnnounce() {\n    if (!this._announceTimeoutId) {\n      this._ngZone.runOutsideAngular(() => {\n        this._announceTimeoutId = setTimeout(() => {\n          const inertElement = this._elementRef.nativeElement.querySelector('[aria-hidden]');\n          const liveElement = this._elementRef.nativeElement.querySelector('[aria-live]');\n          if (inertElement && liveElement) {\n            // If an element in the snack bar content is focused before being moved\n            // track it and restore focus after moving to the live region.\n            let focusedElement = null;\n            if (this._platform.isBrowser && document.activeElement instanceof HTMLElement && inertElement.contains(document.activeElement)) {\n              focusedElement = document.activeElement;\n            }\n            inertElement.removeAttribute('aria-hidden');\n            liveElement.appendChild(inertElement);\n            focusedElement?.focus();\n            this._onAnnounce.next();\n            this._onAnnounce.complete();\n          }\n        }, this._announceDelay);\n      });\n    }\n  }\n  static {\n    this.ɵfac = function MatSnackBarContainer_Factory(t) {\n      return new (t || MatSnackBarContainer)(i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i0.ChangeDetectorRef), i0.ɵɵdirectiveInject(i1.Platform), i0.ɵɵdirectiveInject(MatSnackBarConfig));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatSnackBarContainer,\n      selectors: [[\"mat-snack-bar-container\"]],\n      viewQuery: function MatSnackBarContainer_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n          i0.ɵɵviewQuery(_c0, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._label = _t.first);\n        }\n      },\n      hostAttrs: [1, \"mdc-snackbar\", \"mat-mdc-snack-bar-container\", \"mdc-snackbar--open\"],\n      hostVars: 1,\n      hostBindings: function MatSnackBarContainer_HostBindings(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵsyntheticHostListener(\"@state.done\", function MatSnackBarContainer_animation_state_done_HostBindingHandler($event) {\n            return ctx.onAnimationEnd($event);\n          });\n        }\n        if (rf & 2) {\n          i0.ɵɵsyntheticHostProperty(\"@state\", ctx._animationState);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 6,\n      vars: 3,\n      consts: [[\"label\", \"\"], [1, \"mdc-snackbar__surface\"], [1, \"mat-mdc-snack-bar-label\"], [\"aria-hidden\", \"true\"], [\"cdkPortalOutlet\", \"\"]],\n      template: function MatSnackBarContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2, 0)(3, \"div\", 3);\n          i0.ɵɵtemplate(4, MatSnackBarContainer_ng_template_4_Template, 0, 0, \"ng-template\", 4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(5, \"div\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(5);\n          i0.ɵɵattribute(\"aria-live\", ctx._live)(\"role\", ctx._role)(\"id\", ctx._liveElementId);\n        }\n      },\n      dependencies: [CdkPortalOutlet],\n      styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape)}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size);font-family:var(--mdc-snackbar-supporting-text-font);font-weight:var(--mdc-snackbar-supporting-text-weight);line-height:var(--mdc-snackbar-supporting-text-line-height)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-snack-bar-button-color);--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"],\n      encapsulation: 2,\n      data: {\n        animation: [matSnackBarAnimations.snackBarState]\n      }\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarContainer, [{\n    type: Component,\n    args: [{\n      selector: 'mat-snack-bar-container',\n      changeDetection: ChangeDetectionStrategy.Default,\n      encapsulation: ViewEncapsulation.None,\n      animations: [matSnackBarAnimations.snackBarState],\n      standalone: true,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'mdc-snackbar mat-mdc-snack-bar-container mdc-snackbar--open',\n        '[@state]': '_animationState',\n        '(@state.done)': 'onAnimationEnd($event)'\n      },\n      template: \"<div class=\\\"mdc-snackbar__surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\",\n      styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape)}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size);font-family:var(--mdc-snackbar-supporting-text-font);font-weight:var(--mdc-snackbar-supporting-text-weight);line-height:var(--mdc-snackbar-supporting-text-line-height)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-snack-bar-button-color);--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"]\n    }]\n  }], () => [{\n    type: i0.NgZone\n  }, {\n    type: i0.ElementRef\n  }, {\n    type: i0.ChangeDetectorRef\n  }, {\n    type: i1.Platform\n  }, {\n    type: MatSnackBarConfig\n  }], {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }],\n    _label: [{\n      type: ViewChild,\n      args: ['label', {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/** @docs-private */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n  return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n  providedIn: 'root',\n  factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY\n});\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar {\n  /** Reference to the currently opened snackbar at *any* level. */\n  get _openedSnackBarRef() {\n    const parent = this._parentSnackBar;\n    return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n  }\n  set _openedSnackBarRef(value) {\n    if (this._parentSnackBar) {\n      this._parentSnackBar._openedSnackBarRef = value;\n    } else {\n      this._snackBarRefAtThisLevel = value;\n    }\n  }\n  constructor(_overlay, _live, _injector, _breakpointObserver, _parentSnackBar, _defaultConfig) {\n    this._overlay = _overlay;\n    this._live = _live;\n    this._injector = _injector;\n    this._breakpointObserver = _breakpointObserver;\n    this._parentSnackBar = _parentSnackBar;\n    this._defaultConfig = _defaultConfig;\n    /**\n     * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n     * If there is a parent snack-bar service, all operations should delegate to that parent\n     * via `_openedSnackBarRef`.\n     */\n    this._snackBarRefAtThisLevel = null;\n    /** The component that should be rendered as the snack bar's simple component. */\n    this.simpleSnackBarComponent = SimpleSnackBar;\n    /** The container component that attaches the provided template or component. */\n    this.snackBarContainerComponent = MatSnackBarContainer;\n    /** The CSS class to apply for handset mode. */\n    this.handsetCssClass = 'mat-mdc-snack-bar-handset';\n  }\n  /**\n   * Creates and dispatches a snack bar with a custom component for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param component Component to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromComponent(component, config) {\n    return this._attach(component, config);\n  }\n  /**\n   * Creates and dispatches a snack bar with a custom template for the content, removing any\n   * currently opened snack bars.\n   *\n   * @param template Template to be instantiated.\n   * @param config Extra configuration for the snack bar.\n   */\n  openFromTemplate(template, config) {\n    return this._attach(template, config);\n  }\n  /**\n   * Opens a snackbar with a message and an optional action.\n   * @param message The message to show in the snackbar.\n   * @param action The label for the snackbar action.\n   * @param config Additional configuration options for the snackbar.\n   */\n  open(message, action = '', config) {\n    const _config = {\n      ...this._defaultConfig,\n      ...config\n    };\n    // Since the user doesn't have access to the component, we can\n    // override the data to pass in our own message and action.\n    _config.data = {\n      message,\n      action\n    };\n    // Since the snack bar has `role=\"alert\"`, we don't\n    // want to announce the same message twice.\n    if (_config.announcementMessage === message) {\n      _config.announcementMessage = undefined;\n    }\n    return this.openFromComponent(this.simpleSnackBarComponent, _config);\n  }\n  /**\n   * Dismisses the currently-visible snack bar.\n   */\n  dismiss() {\n    if (this._openedSnackBarRef) {\n      this._openedSnackBarRef.dismiss();\n    }\n  }\n  ngOnDestroy() {\n    // Only dismiss the snack bar at the current level on destroy.\n    if (this._snackBarRefAtThisLevel) {\n      this._snackBarRefAtThisLevel.dismiss();\n    }\n  }\n  /**\n   * Attaches the snack bar container component to the overlay.\n   */\n  _attachSnackBarContainer(overlayRef, config) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    const injector = Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarConfig,\n        useValue: config\n      }]\n    });\n    const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n    const containerRef = overlayRef.attach(containerPortal);\n    containerRef.instance.snackBarConfig = config;\n    return containerRef.instance;\n  }\n  /**\n   * Places a new component or a template as the content of the snack bar container.\n   */\n  _attach(content, userConfig) {\n    const config = {\n      ...new MatSnackBarConfig(),\n      ...this._defaultConfig,\n      ...userConfig\n    };\n    const overlayRef = this._createOverlay(config);\n    const container = this._attachSnackBarContainer(overlayRef, config);\n    const snackBarRef = new MatSnackBarRef(container, overlayRef);\n    if (content instanceof TemplateRef) {\n      const portal = new TemplatePortal(content, null, {\n        $implicit: config.data,\n        snackBarRef\n      });\n      snackBarRef.instance = container.attachTemplatePortal(portal);\n    } else {\n      const injector = this._createInjector(config, snackBarRef);\n      const portal = new ComponentPortal(content, undefined, injector);\n      const contentRef = container.attachComponentPortal(portal);\n      // We can't pass this via the injector, because the injector is created earlier.\n      snackBarRef.instance = contentRef.instance;\n    }\n    // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n    // appropriate. This class is applied to the overlay element because the overlay must expand to\n    // fill the width of the screen for full width snackbars.\n    this._breakpointObserver.observe(Breakpoints.HandsetPortrait).pipe(takeUntil(overlayRef.detachments())).subscribe(state => {\n      overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n    });\n    if (config.announcementMessage) {\n      // Wait until the snack bar contents have been announced then deliver this message.\n      container._onAnnounce.subscribe(() => {\n        this._live.announce(config.announcementMessage, config.politeness);\n      });\n    }\n    this._animateSnackBar(snackBarRef, config);\n    this._openedSnackBarRef = snackBarRef;\n    return this._openedSnackBarRef;\n  }\n  /** Animates the old snack bar out and the new one in. */\n  _animateSnackBar(snackBarRef, config) {\n    // When the snackbar is dismissed, clear the reference to it.\n    snackBarRef.afterDismissed().subscribe(() => {\n      // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n      if (this._openedSnackBarRef == snackBarRef) {\n        this._openedSnackBarRef = null;\n      }\n      if (config.announcementMessage) {\n        this._live.clear();\n      }\n    });\n    if (this._openedSnackBarRef) {\n      // If a snack bar is already in view, dismiss it and enter the\n      // new snack bar after exit animation is complete.\n      this._openedSnackBarRef.afterDismissed().subscribe(() => {\n        snackBarRef.containerInstance.enter();\n      });\n      this._openedSnackBarRef.dismiss();\n    } else {\n      // If no snack bar is in view, enter the new snack bar.\n      snackBarRef.containerInstance.enter();\n    }\n    // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n    if (config.duration && config.duration > 0) {\n      snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n    }\n  }\n  /**\n   * Creates a new overlay and places it in the correct location.\n   * @param config The user-specified snack bar config.\n   */\n  _createOverlay(config) {\n    const overlayConfig = new OverlayConfig();\n    overlayConfig.direction = config.direction;\n    let positionStrategy = this._overlay.position().global();\n    // Set horizontal position.\n    const isRtl = config.direction === 'rtl';\n    const isLeft = config.horizontalPosition === 'left' || config.horizontalPosition === 'start' && !isRtl || config.horizontalPosition === 'end' && isRtl;\n    const isRight = !isLeft && config.horizontalPosition !== 'center';\n    if (isLeft) {\n      positionStrategy.left('0');\n    } else if (isRight) {\n      positionStrategy.right('0');\n    } else {\n      positionStrategy.centerHorizontally();\n    }\n    // Set horizontal position.\n    if (config.verticalPosition === 'top') {\n      positionStrategy.top('0');\n    } else {\n      positionStrategy.bottom('0');\n    }\n    overlayConfig.positionStrategy = positionStrategy;\n    return this._overlay.create(overlayConfig);\n  }\n  /**\n   * Creates an injector to be used inside of a snack bar component.\n   * @param config Config that was used to create the snack bar.\n   * @param snackBarRef Reference to the snack bar.\n   */\n  _createInjector(config, snackBarRef) {\n    const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n    return Injector.create({\n      parent: userInjector || this._injector,\n      providers: [{\n        provide: MatSnackBarRef,\n        useValue: snackBarRef\n      }, {\n        provide: MAT_SNACK_BAR_DATA,\n        useValue: config.data\n      }]\n    });\n  }\n  static {\n    this.ɵfac = function MatSnackBar_Factory(t) {\n      return new (t || MatSnackBar)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i2.LiveAnnouncer), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(i3.BreakpointObserver), i0.ɵɵinject(MatSnackBar, 12), i0.ɵɵinject(MAT_SNACK_BAR_DEFAULT_OPTIONS));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatSnackBar,\n      factory: MatSnackBar.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBar, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1$1.Overlay\n  }, {\n    type: i2.LiveAnnouncer\n  }, {\n    type: i0.Injector\n  }, {\n    type: i3.BreakpointObserver\n  }, {\n    type: MatSnackBar,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: MatSnackBarConfig,\n    decorators: [{\n      type: Inject,\n      args: [MAT_SNACK_BAR_DEFAULT_OPTIONS]\n    }]\n  }], null);\n})();\nconst DIRECTIVES = [MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction];\nclass MatSnackBarModule {\n  static {\n    this.ɵfac = function MatSnackBarModule_Factory(t) {\n      return new (t || MatSnackBarModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatSnackBarModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [MatSnackBar],\n      imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatSnackBarModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, MatButtonModule, MatCommonModule, SimpleSnackBar, ...DIRECTIVES],\n      exports: [MatCommonModule, ...DIRECTIVES],\n      providers: [MatSnackBar]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, matSnackBarAnimations };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Directive", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Inject", "inject", "ViewChild", "Injector", "TemplateRef", "Injectable", "Optional", "SkipSelf", "NgModule", "MatButton", "MatButtonModule", "Subject", "DOCUMENT", "trigger", "state", "style", "transition", "animate", "BasePortalOutlet", "CdkPortalOutlet", "ComponentPortal", "TemplatePortal", "PortalModule", "i1", "i2", "i3", "Breakpoints", "i1$1", "OverlayConfig", "OverlayModule", "takeUntil", "MatCommonModule", "SimpleSnackBar_Conditional_2_Template", "rf", "ctx", "_r1", "ɵɵgetCurrentView", "ɵɵelementStart", "ɵɵlistener", "SimpleSnackBar_Conditional_2_Template_button_click_1_listener", "ɵɵrestoreView", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "action", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "data", "_c0", "MatSnackBarContainer_ng_template_4_Template", "MAX_TIMEOUT", "Math", "pow", "MatSnackBarRef", "constructor", "containerInstance", "_overlayRef", "_afterDismissed", "_afterOpened", "_onAction", "_dismissedByAction", "_onExit", "subscribe", "_finishDismiss", "dismiss", "closed", "exit", "clearTimeout", "_durationTimeoutId", "dismissWithAction", "next", "complete", "closeWithAction", "_dismissAfter", "duration", "setTimeout", "min", "_open", "dispose", "dismissedByAction", "afterDismissed", "afterOpened", "_onEnter", "onAction", "MAT_SNACK_BAR_DATA", "MatSnackBarConfig", "politeness", "announcementMessage", "horizontalPosition", "verticalPosition", "MatSnackBarLabel", "ɵfac", "MatSnackBarLabel_Factory", "t", "ɵdir", "ɵɵdefineDirective", "type", "selectors", "hostAttrs", "standalone", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "MatSnackBarActions", "MatSnackBarActions_Factory", "MatSnackBarAction", "MatSnackBarAction_Factory", "SimpleSnackBar", "snackBarRef", "hasAction", "SimpleSnackBar_Factory", "ɵɵdirectiveInject", "ɵcmp", "ɵɵdefineComponent", "exportAs", "features", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "SimpleSnackBar_Template", "ɵɵtemplate", "message", "ɵɵconditional", "dependencies", "styles", "encapsulation", "changeDetection", "None", "OnPush", "imports", "undefined", "decorators", "matSnackBarAnimations", "snackBarState", "transform", "opacity", "uniqueId", "MatSnackBarContainer", "_ngZone", "_elementRef", "_changeDetectorRef", "_platform", "snackBarConfig", "_document", "_trackedModals", "Set", "_announce<PERSON><PERSON>y", "_destroyed", "_onAnnounce", "_animationState", "_liveElementId", "attachDomPortal", "portal", "_assertNotAttached", "result", "_portalOutlet", "_afterPortalAttached", "_live", "FIREFOX", "_role", "attachComponentPortal", "attachTemplatePortal", "onAnimationEnd", "event", "fromState", "toState", "_completeExit", "onEnter", "run", "enter", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "detectChanges", "_screenReaderAnnounce", "nativeElement", "setAttribute", "_announceTimeoutId", "ngOnDestroy", "_clearFromModals", "queueMicrotask", "element", "panelClasses", "panelClass", "Array", "isArray", "for<PERSON>ach", "cssClass", "classList", "add", "_exposeToModals", "label", "_label", "labelClass", "toggle", "querySelector", "id", "modals", "querySelectorAll", "i", "length", "modal", "ariaOwns", "getAttribute", "indexOf", "newValue", "replace", "trim", "removeAttribute", "clear", "has<PERSON>tta<PERSON>", "Error", "runOutsideAngular", "inertElement", "liveElement", "focusedElement", "<PERSON><PERSON><PERSON><PERSON>", "document", "activeElement", "HTMLElement", "contains", "append<PERSON><PERSON><PERSON>", "focus", "MatSnackBarContainer_Factory", "NgZone", "ElementRef", "ChangeDetectorRef", "Platform", "viewQuery", "MatSnackBarContainer_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostVars", "hostBindings", "MatSnackBarContainer_HostBindings", "ɵɵsyntheticHostListener", "MatSnackBarContainer_animation_state_done_HostBindingHandler", "$event", "ɵɵsyntheticHostProperty", "ɵɵInheritDefinitionFeature", "MatSnackBarContainer_Template", "ɵɵelement", "ɵɵattribute", "animation", "<PERSON><PERSON><PERSON>", "animations", "static", "MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY", "MAT_SNACK_BAR_DEFAULT_OPTIONS", "providedIn", "factory", "MatSnackBar", "_openedSnackBarRef", "parent", "_parentSnackBar", "_snackBarRefAtThisLevel", "value", "_overlay", "_injector", "_breakpointObserver", "_defaultConfig", "simpleSnackBarComponent", "snackBarContainerComponent", "handsetCssClass", "openFromComponent", "component", "config", "_attach", "openFromTemplate", "open", "_config", "_attachSnackBarContainer", "overlayRef", "userInjector", "viewContainerRef", "injector", "create", "providers", "provide", "useValue", "containerPortal", "containerRef", "attach", "instance", "content", "userConfig", "_createOverlay", "container", "$implicit", "_createInjector", "contentRef", "observe", "HandsetPortrait", "pipe", "detachments", "overlayElement", "matches", "announce", "_animateSnackBar", "overlayConfig", "direction", "positionStrategy", "position", "global", "isRtl", "isLeft", "isRight", "left", "right", "centerHorizontally", "top", "bottom", "MatSnackBar_Factory", "ɵɵinject", "Overlay", "LiveAnnouncer", "BreakpointObserver", "ɵprov", "ɵɵdefineInjectable", "token", "DIRECTIVES", "MatSnackBarModule", "MatSnackBarModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/material/fesm2022/snack-bar.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Directive, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, inject, ViewChild, Injector, TemplateRef, Injectable, Optional, SkipSelf, NgModule } from '@angular/core';\nimport { MatButton, MatButtonModule } from '@angular/material/button';\nimport { Subject } from 'rxjs';\nimport { DOCUMENT } from '@angular/common';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport * as i1 from '@angular/cdk/platform';\nimport * as i2 from '@angular/cdk/a11y';\nimport * as i3 from '@angular/cdk/layout';\nimport { Breakpoints } from '@angular/cdk/layout';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { OverlayConfig, OverlayModule } from '@angular/cdk/overlay';\nimport { takeUntil } from 'rxjs/operators';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Maximum amount of milliseconds that can be passed into setTimeout. */\nconst MAX_TIMEOUT = Math.pow(2, 31) - 1;\n/**\n * Reference to a snack bar dispatched from the snack bar service.\n */\nclass MatSnackBarRef {\n    constructor(containerInstance, _overlayRef) {\n        this._overlayRef = _overlayRef;\n        /** Subject for notifying the user that the snack bar has been dismissed. */\n        this._afterDismissed = new Subject();\n        /** Subject for notifying the user that the snack bar has opened and appeared. */\n        this._afterOpened = new Subject();\n        /** Subject for notifying the user that the snack bar action was called. */\n        this._onAction = new Subject();\n        /** Whether the snack bar was dismissed using the action button. */\n        this._dismissedByAction = false;\n        this.containerInstance = containerInstance;\n        containerInstance._onExit.subscribe(() => this._finishDismiss());\n    }\n    /** Dismisses the snack bar. */\n    dismiss() {\n        if (!this._afterDismissed.closed) {\n            this.containerInstance.exit();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /** Marks the snackbar action clicked. */\n    dismissWithAction() {\n        if (!this._onAction.closed) {\n            this._dismissedByAction = true;\n            this._onAction.next();\n            this._onAction.complete();\n            this.dismiss();\n        }\n        clearTimeout(this._durationTimeoutId);\n    }\n    /**\n     * Marks the snackbar action clicked.\n     * @deprecated Use `dismissWithAction` instead.\n     * @breaking-change 8.0.0\n     */\n    closeWithAction() {\n        this.dismissWithAction();\n    }\n    /** Dismisses the snack bar after some duration */\n    _dismissAfter(duration) {\n        // Note that we need to cap the duration to the maximum value for setTimeout, because\n        // it'll revert to 1 if somebody passes in something greater (e.g. `Infinity`). See #17234.\n        this._durationTimeoutId = setTimeout(() => this.dismiss(), Math.min(duration, MAX_TIMEOUT));\n    }\n    /** Marks the snackbar as opened */\n    _open() {\n        if (!this._afterOpened.closed) {\n            this._afterOpened.next();\n            this._afterOpened.complete();\n        }\n    }\n    /** Cleans up the DOM after closing. */\n    _finishDismiss() {\n        this._overlayRef.dispose();\n        if (!this._onAction.closed) {\n            this._onAction.complete();\n        }\n        this._afterDismissed.next({ dismissedByAction: this._dismissedByAction });\n        this._afterDismissed.complete();\n        this._dismissedByAction = false;\n    }\n    /** Gets an observable that is notified when the snack bar is finished closing. */\n    afterDismissed() {\n        return this._afterDismissed;\n    }\n    /** Gets an observable that is notified when the snack bar has opened and appeared. */\n    afterOpened() {\n        return this.containerInstance._onEnter;\n    }\n    /** Gets an observable that is notified when the snack bar action is called. */\n    onAction() {\n        return this._onAction;\n    }\n}\n\n/** Injection token that can be used to access the data that was passed in to a snack bar. */\nconst MAT_SNACK_BAR_DATA = new InjectionToken('MatSnackBarData');\n/**\n * Configuration used when opening a snack-bar.\n */\nclass MatSnackBarConfig {\n    constructor() {\n        /** The politeness level for the MatAriaLiveAnnouncer announcement. */\n        this.politeness = 'assertive';\n        /**\n         * Message to be announced by the LiveAnnouncer. When opening a snackbar without a custom\n         * component or template, the announcement message will default to the specified message.\n         */\n        this.announcementMessage = '';\n        /** The length of time in milliseconds to wait before automatically dismissing the snack bar. */\n        this.duration = 0;\n        /** Data being injected into the child component. */\n        this.data = null;\n        /** The horizontal position to place the snack bar. */\n        this.horizontalPosition = 'center';\n        /** The vertical position to place the snack bar. */\n        this.verticalPosition = 'bottom';\n    }\n}\n\n/** Directive that should be applied to the text element to be rendered in the snack bar. */\nclass MatSnackBarLabel {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBarLabel, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatSnackBarLabel, isStandalone: true, selector: \"[matSnackBarLabel]\", host: { classAttribute: \"mat-mdc-snack-bar-label mdc-snackbar__label\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBarLabel, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarLabel]`,\n                    standalone: true,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-label mdc-snackbar__label',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to the element containing the snack bar's action buttons. */\nclass MatSnackBarActions {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBarActions, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatSnackBarActions, isStandalone: true, selector: \"[matSnackBarActions]\", host: { classAttribute: \"mat-mdc-snack-bar-actions mdc-snackbar__actions\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBarActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarActions]`,\n                    standalone: true,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-actions mdc-snackbar__actions',\n                    },\n                }]\n        }] });\n/** Directive that should be applied to each of the snack bar's action buttons. */\nclass MatSnackBarAction {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBarAction, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatSnackBarAction, isStandalone: true, selector: \"[matSnackBarAction]\", host: { classAttribute: \"mat-mdc-snack-bar-action mdc-snackbar__action\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBarAction, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `[matSnackBarAction]`,\n                    standalone: true,\n                    host: {\n                        'class': 'mat-mdc-snack-bar-action mdc-snackbar__action',\n                    },\n                }]\n        }] });\n\nclass SimpleSnackBar {\n    constructor(snackBarRef, data) {\n        this.snackBarRef = snackBarRef;\n        this.data = data;\n    }\n    /** Performs the action on the snack bar. */\n    action() {\n        this.snackBarRef.dismissWithAction();\n    }\n    /** If the action button should be shown. */\n    get hasAction() {\n        return !!this.data.action;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: SimpleSnackBar, deps: [{ token: MatSnackBarRef }, { token: MAT_SNACK_BAR_DATA }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"17.0.0\", version: \"17.2.0\", type: SimpleSnackBar, isStandalone: true, selector: \"simple-snack-bar\", host: { classAttribute: \"mat-mdc-simple-snack-bar\" }, exportAs: [\"matSnackBar\"], ngImport: i0, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\"], dependencies: [{ kind: \"component\", type: MatButton, selector: \"    button[mat-button], button[mat-raised-button], button[mat-flat-button],    button[mat-stroked-button]  \", exportAs: [\"matButton\"] }, { kind: \"directive\", type: MatSnackBarLabel, selector: \"[matSnackBarLabel]\" }, { kind: \"directive\", type: MatSnackBarActions, selector: \"[matSnackBarActions]\" }, { kind: \"directive\", type: MatSnackBarAction, selector: \"[matSnackBarAction]\" }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: SimpleSnackBar, decorators: [{\n            type: Component,\n            args: [{ selector: 'simple-snack-bar', exportAs: 'matSnackBar', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, imports: [MatButton, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction], standalone: true, host: {\n                        'class': 'mat-mdc-simple-snack-bar',\n                    }, template: \"<div matSnackBarLabel>\\n  {{data.message}}\\n</div>\\n\\n@if (hasAction) {\\n  <div matSnackBarActions>\\n    <button mat-button matSnackBarAction (click)=\\\"action()\\\">\\n      {{data.action}}\\n    </button>\\n  </div>\\n}\\n\", styles: [\".mat-mdc-simple-snack-bar{display:flex}\"] }]\n        }], ctorParameters: () => [{ type: MatSnackBarRef }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_SNACK_BAR_DATA]\n                }] }] });\n\n/**\n * Animations used by the Material snack bar.\n * @docs-private\n */\nconst matSnackBarAnimations = {\n    /** Animation that shows and hides a snack bar. */\n    snackBarState: trigger('state', [\n        state('void, hidden', style({\n            transform: 'scale(0.8)',\n            opacity: 0,\n        })),\n        state('visible', style({\n            transform: 'scale(1)',\n            opacity: 1,\n        })),\n        transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n        transition('* => void, * => hidden', animate('75ms cubic-bezier(0.4, 0.0, 1, 1)', style({\n            opacity: 0,\n        }))),\n    ]),\n};\n\nlet uniqueId = 0;\n/**\n * Internal component that wraps user-provided snack bar content.\n * @docs-private\n */\nclass MatSnackBarContainer extends BasePortalOutlet {\n    constructor(_ngZone, _elementRef, _changeDetectorRef, _platform, \n    /** The snack bar configuration. */\n    snackBarConfig) {\n        super();\n        this._ngZone = _ngZone;\n        this._elementRef = _elementRef;\n        this._changeDetectorRef = _changeDetectorRef;\n        this._platform = _platform;\n        this.snackBarConfig = snackBarConfig;\n        this._document = inject(DOCUMENT);\n        this._trackedModals = new Set();\n        /** The number of milliseconds to wait before announcing the snack bar's content. */\n        this._announceDelay = 150;\n        /** Whether the component has been destroyed. */\n        this._destroyed = false;\n        /** Subject for notifying that the snack bar has announced to screen readers. */\n        this._onAnnounce = new Subject();\n        /** Subject for notifying that the snack bar has exited from view. */\n        this._onExit = new Subject();\n        /** Subject for notifying that the snack bar has finished entering the view. */\n        this._onEnter = new Subject();\n        /** The state of the snack bar animations. */\n        this._animationState = 'void';\n        /** Unique ID of the aria-live element. */\n        this._liveElementId = `mat-snack-bar-container-live-${uniqueId++}`;\n        /**\n         * Attaches a DOM portal to the snack bar container.\n         * @deprecated To be turned into a method.\n         * @breaking-change 10.0.0\n         */\n        this.attachDomPortal = (portal) => {\n            this._assertNotAttached();\n            const result = this._portalOutlet.attachDomPortal(portal);\n            this._afterPortalAttached();\n            return result;\n        };\n        // Use aria-live rather than a live role like 'alert' or 'status'\n        // because NVDA and JAWS have show inconsistent behavior with live roles.\n        if (snackBarConfig.politeness === 'assertive' && !snackBarConfig.announcementMessage) {\n            this._live = 'assertive';\n        }\n        else if (snackBarConfig.politeness === 'off') {\n            this._live = 'off';\n        }\n        else {\n            this._live = 'polite';\n        }\n        // Only set role for Firefox. Set role based on aria-live because setting role=\"alert\" implies\n        // aria-live=\"assertive\" which may cause issues if aria-live is set to \"polite\" above.\n        if (this._platform.FIREFOX) {\n            if (this._live === 'polite') {\n                this._role = 'status';\n            }\n            if (this._live === 'assertive') {\n                this._role = 'alert';\n            }\n        }\n    }\n    /** Attach a component portal as content to this snack bar container. */\n    attachComponentPortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /** Attach a template portal as content to this snack bar container. */\n    attachTemplatePortal(portal) {\n        this._assertNotAttached();\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._afterPortalAttached();\n        return result;\n    }\n    /** Handle end of animations, updating the state of the snackbar. */\n    onAnimationEnd(event) {\n        const { fromState, toState } = event;\n        if ((toState === 'void' && fromState !== 'void') || toState === 'hidden') {\n            this._completeExit();\n        }\n        if (toState === 'visible') {\n            // Note: we shouldn't use `this` inside the zone callback,\n            // because it can cause a memory leak.\n            const onEnter = this._onEnter;\n            this._ngZone.run(() => {\n                onEnter.next();\n                onEnter.complete();\n            });\n        }\n    }\n    /** Begin animation of snack bar entrance into view. */\n    enter() {\n        if (!this._destroyed) {\n            this._animationState = 'visible';\n            // _animationState lives in host bindings and `detectChanges` does not refresh host bindings\n            // so we have to call `markForCheck` to ensure the host view is refreshed eventually.\n            this._changeDetectorRef.markForCheck();\n            this._changeDetectorRef.detectChanges();\n            this._screenReaderAnnounce();\n        }\n    }\n    /** Begin animation of the snack bar exiting from view. */\n    exit() {\n        // It's common for snack bars to be opened by random outside calls like HTTP requests or\n        // errors. Run inside the NgZone to ensure that it functions correctly.\n        this._ngZone.run(() => {\n            // Note: this one transitions to `hidden`, rather than `void`, in order to handle the case\n            // where multiple snack bars are opened in quick succession (e.g. two consecutive calls to\n            // `MatSnackBar.open`).\n            this._animationState = 'hidden';\n            this._changeDetectorRef.markForCheck();\n            // Mark this element with an 'exit' attribute to indicate that the snackbar has\n            // been dismissed and will soon be removed from the DOM. This is used by the snackbar\n            // test harness.\n            this._elementRef.nativeElement.setAttribute('mat-exit', '');\n            // If the snack bar hasn't been announced by the time it exits it wouldn't have been open\n            // long enough to visually read it either, so clear the timeout for announcing.\n            clearTimeout(this._announceTimeoutId);\n        });\n        return this._onExit;\n    }\n    /** Makes sure the exit callbacks have been invoked when the element is destroyed. */\n    ngOnDestroy() {\n        this._destroyed = true;\n        this._clearFromModals();\n        this._completeExit();\n    }\n    /**\n     * Removes the element in a microtask. Helps prevent errors where we end up\n     * removing an element which is in the middle of an animation.\n     */\n    _completeExit() {\n        queueMicrotask(() => {\n            this._onExit.next();\n            this._onExit.complete();\n        });\n    }\n    /**\n     * Called after the portal contents have been attached. Can be\n     * used to modify the DOM once it's guaranteed to be in place.\n     */\n    _afterPortalAttached() {\n        const element = this._elementRef.nativeElement;\n        const panelClasses = this.snackBarConfig.panelClass;\n        if (panelClasses) {\n            if (Array.isArray(panelClasses)) {\n                // Note that we can't use a spread here, because IE doesn't support multiple arguments.\n                panelClasses.forEach(cssClass => element.classList.add(cssClass));\n            }\n            else {\n                element.classList.add(panelClasses);\n            }\n        }\n        this._exposeToModals();\n        // Check to see if the attached component or template uses the MDC template structure,\n        // specifically the MDC label. If not, the container should apply the MDC label class to this\n        // component's label container, which will apply MDC's label styles to the attached view.\n        const label = this._label.nativeElement;\n        const labelClass = 'mdc-snackbar__label';\n        label.classList.toggle(labelClass, !label.querySelector(`.${labelClass}`));\n    }\n    /**\n     * Some browsers won't expose the accessibility node of the live element if there is an\n     * `aria-modal` and the live element is outside of it. This method works around the issue by\n     * pointing the `aria-owns` of all modals to the live element.\n     */\n    _exposeToModals() {\n        // TODO(http://github.com/angular/components/issues/26853): consider de-duplicating this with the\n        // `LiveAnnouncer` and any other usages.\n        //\n        // Note that the selector here is limited to CDK overlays at the moment in order to reduce the\n        // section of the DOM we need to look through. This should cover all the cases we support, but\n        // the selector can be expanded if it turns out to be too narrow.\n        const id = this._liveElementId;\n        const modals = this._document.querySelectorAll('body > .cdk-overlay-container [aria-modal=\"true\"]');\n        for (let i = 0; i < modals.length; i++) {\n            const modal = modals[i];\n            const ariaOwns = modal.getAttribute('aria-owns');\n            this._trackedModals.add(modal);\n            if (!ariaOwns) {\n                modal.setAttribute('aria-owns', id);\n            }\n            else if (ariaOwns.indexOf(id) === -1) {\n                modal.setAttribute('aria-owns', ariaOwns + ' ' + id);\n            }\n        }\n    }\n    /** Clears the references to the live element from any modals it was added to. */\n    _clearFromModals() {\n        this._trackedModals.forEach(modal => {\n            const ariaOwns = modal.getAttribute('aria-owns');\n            if (ariaOwns) {\n                const newValue = ariaOwns.replace(this._liveElementId, '').trim();\n                if (newValue.length > 0) {\n                    modal.setAttribute('aria-owns', newValue);\n                }\n                else {\n                    modal.removeAttribute('aria-owns');\n                }\n            }\n        });\n        this._trackedModals.clear();\n    }\n    /** Asserts that no content is already attached to the container. */\n    _assertNotAttached() {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error('Attempting to attach snack bar content after content is already attached');\n        }\n    }\n    /**\n     * Starts a timeout to move the snack bar content to the live region so screen readers will\n     * announce it.\n     */\n    _screenReaderAnnounce() {\n        if (!this._announceTimeoutId) {\n            this._ngZone.runOutsideAngular(() => {\n                this._announceTimeoutId = setTimeout(() => {\n                    const inertElement = this._elementRef.nativeElement.querySelector('[aria-hidden]');\n                    const liveElement = this._elementRef.nativeElement.querySelector('[aria-live]');\n                    if (inertElement && liveElement) {\n                        // If an element in the snack bar content is focused before being moved\n                        // track it and restore focus after moving to the live region.\n                        let focusedElement = null;\n                        if (this._platform.isBrowser &&\n                            document.activeElement instanceof HTMLElement &&\n                            inertElement.contains(document.activeElement)) {\n                            focusedElement = document.activeElement;\n                        }\n                        inertElement.removeAttribute('aria-hidden');\n                        liveElement.appendChild(inertElement);\n                        focusedElement?.focus();\n                        this._onAnnounce.next();\n                        this._onAnnounce.complete();\n                    }\n                }, this._announceDelay);\n            });\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBarContainer, deps: [{ token: i0.NgZone }, { token: i0.ElementRef }, { token: i0.ChangeDetectorRef }, { token: i1.Platform }, { token: MatSnackBarConfig }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatSnackBarContainer, isStandalone: true, selector: \"mat-snack-bar-container\", host: { listeners: { \"@state.done\": \"onAnimationEnd($event)\" }, properties: { \"@state\": \"_animationState\" }, classAttribute: \"mdc-snackbar mat-mdc-snack-bar-container mdc-snackbar--open\" }, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }, { propertyName: \"_label\", first: true, predicate: [\"label\"], descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<div class=\\\"mdc-snackbar__surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape)}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size);font-family:var(--mdc-snackbar-supporting-text-font);font-weight:var(--mdc-snackbar-supporting-text-weight);line-height:var(--mdc-snackbar-supporting-text-line-height)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-snack-bar-button-color);--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], animations: [matSnackBarAnimations.snackBarState], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBarContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-snack-bar-container', changeDetection: ChangeDetectionStrategy.Default, encapsulation: ViewEncapsulation.None, animations: [matSnackBarAnimations.snackBarState], standalone: true, imports: [CdkPortalOutlet], host: {\n                        'class': 'mdc-snackbar mat-mdc-snack-bar-container mdc-snackbar--open',\n                        '[@state]': '_animationState',\n                        '(@state.done)': 'onAnimationEnd($event)',\n                    }, template: \"<div class=\\\"mdc-snackbar__surface\\\">\\n  <!--\\n    This outer label wrapper will have the class `mdc-snackbar__label` applied if\\n    the attached template/component does not contain it.\\n  -->\\n  <div class=\\\"mat-mdc-snack-bar-label\\\" #label>\\n    <!-- Initialy holds the snack bar content, will be empty after announcing to screen readers. -->\\n    <div aria-hidden=\\\"true\\\">\\n      <ng-template cdkPortalOutlet />\\n    </div>\\n\\n    <!-- Will receive the snack bar content from the non-live div, move will happen a short delay after opening -->\\n    <div [attr.aria-live]=\\\"_live\\\" [attr.role]=\\\"_role\\\" [attr.id]=\\\"_liveElementId\\\"></div>\\n  </div>\\n</div>\\n\", styles: [\".mdc-snackbar{display:none;position:fixed;right:0;bottom:0;left:0;align-items:center;justify-content:center;box-sizing:border-box;pointer-events:none;-webkit-tap-highlight-color:rgba(0,0,0,0)}.mdc-snackbar--opening,.mdc-snackbar--open,.mdc-snackbar--closing{display:flex}.mdc-snackbar--open .mdc-snackbar__label,.mdc-snackbar--open .mdc-snackbar__actions{visibility:visible}.mdc-snackbar__surface{padding-left:0;padding-right:8px;display:flex;align-items:center;justify-content:flex-start;box-sizing:border-box;transform:scale(0.8);opacity:0}.mdc-snackbar__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-snackbar__surface::before{border-color:CanvasText}}[dir=rtl] .mdc-snackbar__surface,.mdc-snackbar__surface[dir=rtl]{padding-left:8px;padding-right:0}.mdc-snackbar--open .mdc-snackbar__surface{transform:scale(1);opacity:1;pointer-events:auto}.mdc-snackbar--closing .mdc-snackbar__surface{transform:scale(1)}.mdc-snackbar__label{padding-left:16px;padding-right:8px;width:100%;flex-grow:1;box-sizing:border-box;margin:0;visibility:hidden;padding-top:14px;padding-bottom:14px}[dir=rtl] .mdc-snackbar__label,.mdc-snackbar__label[dir=rtl]{padding-left:8px;padding-right:16px}.mdc-snackbar__label::before{display:inline;content:attr(data-mdc-snackbar-label-text)}.mdc-snackbar__actions{display:flex;flex-shrink:0;align-items:center;box-sizing:border-box;visibility:hidden}.mdc-snackbar__action+.mdc-snackbar__dismiss{margin-left:8px;margin-right:0}[dir=rtl] .mdc-snackbar__action+.mdc-snackbar__dismiss,.mdc-snackbar__action+.mdc-snackbar__dismiss[dir=rtl]{margin-left:0;margin-right:8px}.mat-mdc-snack-bar-container{margin:8px;position:static}.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:344px}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container .mdc-snackbar__surface{min-width:100%}}@media(max-width: 480px),(max-width: 344px){.mat-mdc-snack-bar-container{width:100vw}}.mat-mdc-snack-bar-container .mdc-snackbar__surface{max-width:672px}.mat-mdc-snack-bar-container .mdc-snackbar__surface{box-shadow:0px 3px 5px -1px rgba(0, 0, 0, 0.2), 0px 6px 10px 0px rgba(0, 0, 0, 0.14), 0px 1px 18px 0px rgba(0, 0, 0, 0.12)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{background-color:var(--mdc-snackbar-container-color)}.mat-mdc-snack-bar-container .mdc-snackbar__surface{border-radius:var(--mdc-snackbar-container-shape)}.mat-mdc-snack-bar-container .mdc-snackbar__label{color:var(--mdc-snackbar-supporting-text-color)}.mat-mdc-snack-bar-container .mdc-snackbar__label{font-size:var(--mdc-snackbar-supporting-text-size);font-family:var(--mdc-snackbar-supporting-text-font);font-weight:var(--mdc-snackbar-supporting-text-weight);line-height:var(--mdc-snackbar-supporting-text-line-height)}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled){color:var(--mat-snack-bar-button-color);--mat-text-button-state-layer-color:currentColor;--mat-text-button-ripple-color:currentColor}.mat-mdc-snack-bar-container .mat-mdc-button.mat-mdc-snack-bar-action:not(:disabled) .mat-ripple-element{opacity:.1}.mat-mdc-snack-bar-container .mdc-snackbar__label::before{display:none}.mat-mdc-snack-bar-handset,.mat-mdc-snack-bar-container,.mat-mdc-snack-bar-label{flex:1 1 auto}.mat-mdc-snack-bar-handset .mdc-snackbar__surface{width:100%}\"] }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i0.ElementRef }, { type: i0.ChangeDetectorRef }, { type: i1.Platform }, { type: MatSnackBarConfig }], propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }], _label: [{\n                type: ViewChild,\n                args: ['label', { static: true }]\n            }] } });\n\n/** @docs-private */\nfunction MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY() {\n    return new MatSnackBarConfig();\n}\n/** Injection token that can be used to specify default snack bar. */\nconst MAT_SNACK_BAR_DEFAULT_OPTIONS = new InjectionToken('mat-snack-bar-default-options', {\n    providedIn: 'root',\n    factory: MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * Service to dispatch Material Design snack bar messages.\n */\nclass MatSnackBar {\n    /** Reference to the currently opened snackbar at *any* level. */\n    get _openedSnackBarRef() {\n        const parent = this._parentSnackBar;\n        return parent ? parent._openedSnackBarRef : this._snackBarRefAtThisLevel;\n    }\n    set _openedSnackBarRef(value) {\n        if (this._parentSnackBar) {\n            this._parentSnackBar._openedSnackBarRef = value;\n        }\n        else {\n            this._snackBarRefAtThisLevel = value;\n        }\n    }\n    constructor(_overlay, _live, _injector, _breakpointObserver, _parentSnackBar, _defaultConfig) {\n        this._overlay = _overlay;\n        this._live = _live;\n        this._injector = _injector;\n        this._breakpointObserver = _breakpointObserver;\n        this._parentSnackBar = _parentSnackBar;\n        this._defaultConfig = _defaultConfig;\n        /**\n         * Reference to the current snack bar in the view *at this level* (in the Angular injector tree).\n         * If there is a parent snack-bar service, all operations should delegate to that parent\n         * via `_openedSnackBarRef`.\n         */\n        this._snackBarRefAtThisLevel = null;\n        /** The component that should be rendered as the snack bar's simple component. */\n        this.simpleSnackBarComponent = SimpleSnackBar;\n        /** The container component that attaches the provided template or component. */\n        this.snackBarContainerComponent = MatSnackBarContainer;\n        /** The CSS class to apply for handset mode. */\n        this.handsetCssClass = 'mat-mdc-snack-bar-handset';\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom component for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param component Component to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromComponent(component, config) {\n        return this._attach(component, config);\n    }\n    /**\n     * Creates and dispatches a snack bar with a custom template for the content, removing any\n     * currently opened snack bars.\n     *\n     * @param template Template to be instantiated.\n     * @param config Extra configuration for the snack bar.\n     */\n    openFromTemplate(template, config) {\n        return this._attach(template, config);\n    }\n    /**\n     * Opens a snackbar with a message and an optional action.\n     * @param message The message to show in the snackbar.\n     * @param action The label for the snackbar action.\n     * @param config Additional configuration options for the snackbar.\n     */\n    open(message, action = '', config) {\n        const _config = { ...this._defaultConfig, ...config };\n        // Since the user doesn't have access to the component, we can\n        // override the data to pass in our own message and action.\n        _config.data = { message, action };\n        // Since the snack bar has `role=\"alert\"`, we don't\n        // want to announce the same message twice.\n        if (_config.announcementMessage === message) {\n            _config.announcementMessage = undefined;\n        }\n        return this.openFromComponent(this.simpleSnackBarComponent, _config);\n    }\n    /**\n     * Dismisses the currently-visible snack bar.\n     */\n    dismiss() {\n        if (this._openedSnackBarRef) {\n            this._openedSnackBarRef.dismiss();\n        }\n    }\n    ngOnDestroy() {\n        // Only dismiss the snack bar at the current level on destroy.\n        if (this._snackBarRefAtThisLevel) {\n            this._snackBarRefAtThisLevel.dismiss();\n        }\n    }\n    /**\n     * Attaches the snack bar container component to the overlay.\n     */\n    _attachSnackBarContainer(overlayRef, config) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        const injector = Injector.create({\n            parent: userInjector || this._injector,\n            providers: [{ provide: MatSnackBarConfig, useValue: config }],\n        });\n        const containerPortal = new ComponentPortal(this.snackBarContainerComponent, config.viewContainerRef, injector);\n        const containerRef = overlayRef.attach(containerPortal);\n        containerRef.instance.snackBarConfig = config;\n        return containerRef.instance;\n    }\n    /**\n     * Places a new component or a template as the content of the snack bar container.\n     */\n    _attach(content, userConfig) {\n        const config = { ...new MatSnackBarConfig(), ...this._defaultConfig, ...userConfig };\n        const overlayRef = this._createOverlay(config);\n        const container = this._attachSnackBarContainer(overlayRef, config);\n        const snackBarRef = new MatSnackBarRef(container, overlayRef);\n        if (content instanceof TemplateRef) {\n            const portal = new TemplatePortal(content, null, {\n                $implicit: config.data,\n                snackBarRef,\n            });\n            snackBarRef.instance = container.attachTemplatePortal(portal);\n        }\n        else {\n            const injector = this._createInjector(config, snackBarRef);\n            const portal = new ComponentPortal(content, undefined, injector);\n            const contentRef = container.attachComponentPortal(portal);\n            // We can't pass this via the injector, because the injector is created earlier.\n            snackBarRef.instance = contentRef.instance;\n        }\n        // Subscribe to the breakpoint observer and attach the mat-snack-bar-handset class as\n        // appropriate. This class is applied to the overlay element because the overlay must expand to\n        // fill the width of the screen for full width snackbars.\n        this._breakpointObserver\n            .observe(Breakpoints.HandsetPortrait)\n            .pipe(takeUntil(overlayRef.detachments()))\n            .subscribe(state => {\n            overlayRef.overlayElement.classList.toggle(this.handsetCssClass, state.matches);\n        });\n        if (config.announcementMessage) {\n            // Wait until the snack bar contents have been announced then deliver this message.\n            container._onAnnounce.subscribe(() => {\n                this._live.announce(config.announcementMessage, config.politeness);\n            });\n        }\n        this._animateSnackBar(snackBarRef, config);\n        this._openedSnackBarRef = snackBarRef;\n        return this._openedSnackBarRef;\n    }\n    /** Animates the old snack bar out and the new one in. */\n    _animateSnackBar(snackBarRef, config) {\n        // When the snackbar is dismissed, clear the reference to it.\n        snackBarRef.afterDismissed().subscribe(() => {\n            // Clear the snackbar ref if it hasn't already been replaced by a newer snackbar.\n            if (this._openedSnackBarRef == snackBarRef) {\n                this._openedSnackBarRef = null;\n            }\n            if (config.announcementMessage) {\n                this._live.clear();\n            }\n        });\n        if (this._openedSnackBarRef) {\n            // If a snack bar is already in view, dismiss it and enter the\n            // new snack bar after exit animation is complete.\n            this._openedSnackBarRef.afterDismissed().subscribe(() => {\n                snackBarRef.containerInstance.enter();\n            });\n            this._openedSnackBarRef.dismiss();\n        }\n        else {\n            // If no snack bar is in view, enter the new snack bar.\n            snackBarRef.containerInstance.enter();\n        }\n        // If a dismiss timeout is provided, set up dismiss based on after the snackbar is opened.\n        if (config.duration && config.duration > 0) {\n            snackBarRef.afterOpened().subscribe(() => snackBarRef._dismissAfter(config.duration));\n        }\n    }\n    /**\n     * Creates a new overlay and places it in the correct location.\n     * @param config The user-specified snack bar config.\n     */\n    _createOverlay(config) {\n        const overlayConfig = new OverlayConfig();\n        overlayConfig.direction = config.direction;\n        let positionStrategy = this._overlay.position().global();\n        // Set horizontal position.\n        const isRtl = config.direction === 'rtl';\n        const isLeft = config.horizontalPosition === 'left' ||\n            (config.horizontalPosition === 'start' && !isRtl) ||\n            (config.horizontalPosition === 'end' && isRtl);\n        const isRight = !isLeft && config.horizontalPosition !== 'center';\n        if (isLeft) {\n            positionStrategy.left('0');\n        }\n        else if (isRight) {\n            positionStrategy.right('0');\n        }\n        else {\n            positionStrategy.centerHorizontally();\n        }\n        // Set horizontal position.\n        if (config.verticalPosition === 'top') {\n            positionStrategy.top('0');\n        }\n        else {\n            positionStrategy.bottom('0');\n        }\n        overlayConfig.positionStrategy = positionStrategy;\n        return this._overlay.create(overlayConfig);\n    }\n    /**\n     * Creates an injector to be used inside of a snack bar component.\n     * @param config Config that was used to create the snack bar.\n     * @param snackBarRef Reference to the snack bar.\n     */\n    _createInjector(config, snackBarRef) {\n        const userInjector = config && config.viewContainerRef && config.viewContainerRef.injector;\n        return Injector.create({\n            parent: userInjector || this._injector,\n            providers: [\n                { provide: MatSnackBarRef, useValue: snackBarRef },\n                { provide: MAT_SNACK_BAR_DATA, useValue: config.data },\n            ],\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBar, deps: [{ token: i1$1.Overlay }, { token: i2.LiveAnnouncer }, { token: i0.Injector }, { token: i3.BreakpointObserver }, { token: MatSnackBar, optional: true, skipSelf: true }, { token: MAT_SNACK_BAR_DEFAULT_OPTIONS }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBar, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBar, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1$1.Overlay }, { type: i2.LiveAnnouncer }, { type: i0.Injector }, { type: i3.BreakpointObserver }, { type: MatSnackBar, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: MatSnackBarConfig, decorators: [{\n                    type: Inject,\n                    args: [MAT_SNACK_BAR_DEFAULT_OPTIONS]\n                }] }] });\n\nconst DIRECTIVES = [MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction];\nclass MatSnackBarModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBarModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBarModule, imports: [OverlayModule,\n            PortalModule,\n            MatButtonModule,\n            MatCommonModule,\n            SimpleSnackBar, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction], exports: [MatCommonModule, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarActions, MatSnackBarAction] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBarModule, providers: [MatSnackBar], imports: [OverlayModule,\n            PortalModule,\n            MatButtonModule,\n            MatCommonModule,\n            SimpleSnackBar, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatSnackBarModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        OverlayModule,\n                        PortalModule,\n                        MatButtonModule,\n                        MatCommonModule,\n                        SimpleSnackBar,\n                        ...DIRECTIVES,\n                    ],\n                    exports: [MatCommonModule, ...DIRECTIVES],\n                    providers: [MatSnackBar],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_SNACK_BAR_DATA, MAT_SNACK_BAR_DEFAULT_OPTIONS, MAT_SNACK_BAR_DEFAULT_OPTIONS_FACTORY, MatSnackBar, MatSnackBarAction, MatSnackBarActions, MatSnackBarConfig, MatSnackBarContainer, MatSnackBarLabel, MatSnackBarModule, MatSnackBarRef, SimpleSnackBar, matSnackBarAnimations };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,MAAM,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAC5M,SAASC,SAAS,EAAEC,eAAe,QAAQ,0BAA0B;AACrE,SAASC,OAAO,QAAQ,MAAM;AAC9B,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,OAAO,EAAEC,KAAK,EAAEC,KAAK,EAAEC,UAAU,EAAEC,OAAO,QAAQ,qBAAqB;AAChF,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AACtH,OAAO,KAAKC,EAAE,MAAM,uBAAuB;AAC3C,OAAO,KAAKC,EAAE,MAAM,mBAAmB;AACvC,OAAO,KAAKC,EAAE,MAAM,qBAAqB;AACzC,SAASC,WAAW,QAAQ,qBAAqB;AACjD,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAC5C,SAASC,aAAa,EAAEC,aAAa,QAAQ,sBAAsB;AACnE,SAASC,SAAS,QAAQ,gBAAgB;AAC1C,SAASC,eAAe,QAAQ,wBAAwB;;AAExD;AAAA,SAAAC,sCAAAC,EAAA,EAAAC,GAAA;EAAA,IAAAD,EAAA;IAAA,MAAAE,GAAA,GA4GoGzC,EAAE,CAAA0C,gBAAA;IAAF1C,EAAE,CAAA2C,cAAA,YA0D0Q,CAAC,eAA+D,CAAC;IA1D7U3C,EAAE,CAAA4C,UAAA,mBAAAC,8DAAA;MAAF7C,EAAE,CAAA8C,aAAA,CAAAL,GAAA;MAAA,MAAAM,MAAA,GAAF/C,EAAE,CAAAgD,aAAA;MAAA,OAAFhD,EAAE,CAAAiD,WAAA,CA0DgUF,MAAA,CAAAG,MAAA,CAAO,CAAC;IAAA,CAAC,CAAC;IA1D5UlD,EAAE,CAAAmD,MAAA,EA0DuW,CAAC;IA1D1WnD,EAAE,CAAAoD,YAAA,CA0DgX,CAAC,CAAS,CAAC;EAAA;EAAA,IAAAb,EAAA;IAAA,MAAAQ,MAAA,GA1D7X/C,EAAE,CAAAgD,aAAA;IAAFhD,EAAE,CAAAqD,SAAA,EA0DuW,CAAC;IA1D1WrD,EAAE,CAAAsD,kBAAA,MAAAP,MAAA,CAAAQ,IAAA,CAAAL,MAAA,KA0DuW,CAAC;EAAA;AAAA;AAAA,MAAAM,GAAA;AAAA,SAAAC,4CAAAlB,EAAA,EAAAC,GAAA;AArK9c,MAAMkB,WAAW,GAAGC,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAE,CAAC,GAAG,CAAC;AACvC;AACA;AACA;AACA,MAAMC,cAAc,CAAC;EACjBC,WAAWA,CAACC,iBAAiB,EAAEC,WAAW,EAAE;IACxC,IAAI,CAACA,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAACC,eAAe,GAAG,IAAIhD,OAAO,CAAC,CAAC;IACpC;IACA,IAAI,CAACiD,YAAY,GAAG,IAAIjD,OAAO,CAAC,CAAC;IACjC;IACA,IAAI,CAACkD,SAAS,GAAG,IAAIlD,OAAO,CAAC,CAAC;IAC9B;IACA,IAAI,CAACmD,kBAAkB,GAAG,KAAK;IAC/B,IAAI,CAACL,iBAAiB,GAAGA,iBAAiB;IAC1CA,iBAAiB,CAACM,OAAO,CAACC,SAAS,CAAC,MAAM,IAAI,CAACC,cAAc,CAAC,CAAC,CAAC;EACpE;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACP,eAAe,CAACQ,MAAM,EAAE;MAC9B,IAAI,CAACV,iBAAiB,CAACW,IAAI,CAAC,CAAC;IACjC;IACAC,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;EACzC;EACA;EACAC,iBAAiBA,CAAA,EAAG;IAChB,IAAI,CAAC,IAAI,CAACV,SAAS,CAACM,MAAM,EAAE;MACxB,IAAI,CAACL,kBAAkB,GAAG,IAAI;MAC9B,IAAI,CAACD,SAAS,CAACW,IAAI,CAAC,CAAC;MACrB,IAAI,CAACX,SAAS,CAACY,QAAQ,CAAC,CAAC;MACzB,IAAI,CAACP,OAAO,CAAC,CAAC;IAClB;IACAG,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;EACII,eAAeA,CAAA,EAAG;IACd,IAAI,CAACH,iBAAiB,CAAC,CAAC;EAC5B;EACA;EACAI,aAAaA,CAACC,QAAQ,EAAE;IACpB;IACA;IACA,IAAI,CAACN,kBAAkB,GAAGO,UAAU,CAAC,MAAM,IAAI,CAACX,OAAO,CAAC,CAAC,EAAEb,IAAI,CAACyB,GAAG,CAACF,QAAQ,EAAExB,WAAW,CAAC,CAAC;EAC/F;EACA;EACA2B,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACnB,YAAY,CAACO,MAAM,EAAE;MAC3B,IAAI,CAACP,YAAY,CAACY,IAAI,CAAC,CAAC;MACxB,IAAI,CAACZ,YAAY,CAACa,QAAQ,CAAC,CAAC;IAChC;EACJ;EACA;EACAR,cAAcA,CAAA,EAAG;IACb,IAAI,CAACP,WAAW,CAACsB,OAAO,CAAC,CAAC;IAC1B,IAAI,CAAC,IAAI,CAACnB,SAAS,CAACM,MAAM,EAAE;MACxB,IAAI,CAACN,SAAS,CAACY,QAAQ,CAAC,CAAC;IAC7B;IACA,IAAI,CAACd,eAAe,CAACa,IAAI,CAAC;MAAES,iBAAiB,EAAE,IAAI,CAACnB;IAAmB,CAAC,CAAC;IACzE,IAAI,CAACH,eAAe,CAACc,QAAQ,CAAC,CAAC;IAC/B,IAAI,CAACX,kBAAkB,GAAG,KAAK;EACnC;EACA;EACAoB,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAACvB,eAAe;EAC/B;EACA;EACAwB,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC1B,iBAAiB,CAAC2B,QAAQ;EAC1C;EACA;EACAC,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACxB,SAAS;EACzB;AACJ;;AAEA;AACA,MAAMyB,kBAAkB,GAAG,IAAI3F,cAAc,CAAC,iBAAiB,CAAC;AAChE;AACA;AACA;AACA,MAAM4F,iBAAiB,CAAC;EACpB/B,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACgC,UAAU,GAAG,WAAW;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACC,mBAAmB,GAAG,EAAE;IAC7B;IACA,IAAI,CAACb,QAAQ,GAAG,CAAC;IACjB;IACA,IAAI,CAAC3B,IAAI,GAAG,IAAI;IAChB;IACA,IAAI,CAACyC,kBAAkB,GAAG,QAAQ;IAClC;IACA,IAAI,CAACC,gBAAgB,GAAG,QAAQ;EACpC;AACJ;;AAEA;AACA,MAAMC,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACC,IAAI,YAAAC,yBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFH,gBAAgB;IAAA,CAAmD;EAAE;EAC/K;IAAS,IAAI,CAACI,IAAI,kBAD8EtG,EAAE,CAAAuG,iBAAA;MAAAC,IAAA,EACJN,gBAAgB;MAAAO,SAAA;MAAAC,SAAA;MAAAC,UAAA;IAAA,EAA8I;EAAE;AAClQ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoG5G,EAAE,CAAA6G,iBAAA,CAGXX,gBAAgB,EAAc,CAAC;IAC9GM,IAAI,EAAEtG,SAAS;IACf4G,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oBAAoB;MAC9BJ,UAAU,EAAE,IAAI;MAChBK,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMC,kBAAkB,CAAC;EACrB;IAAS,IAAI,CAACd,IAAI,YAAAe,2BAAAb,CAAA;MAAA,YAAAA,CAAA,IAAwFY,kBAAkB;IAAA,CAAmD;EAAE;EACjL;IAAS,IAAI,CAACX,IAAI,kBAhB8EtG,EAAE,CAAAuG,iBAAA;MAAAC,IAAA,EAgBJS,kBAAkB;MAAAR,SAAA;MAAAC,SAAA;MAAAC,UAAA;IAAA,EAAoJ;EAAE;AAC1Q;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAlBoG5G,EAAE,CAAA6G,iBAAA,CAkBXI,kBAAkB,EAAc,CAAC;IAChHT,IAAI,EAAEtG,SAAS;IACf4G,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCJ,UAAU,EAAE,IAAI;MAChBK,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMG,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAAChB,IAAI,YAAAiB,0BAAAf,CAAA;MAAA,YAAAA,CAAA,IAAwFc,iBAAiB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACb,IAAI,kBA/B8EtG,EAAE,CAAAuG,iBAAA;MAAAC,IAAA,EA+BJW,iBAAiB;MAAAV,SAAA;MAAAC,SAAA;MAAAC,UAAA;IAAA,EAAiJ;EAAE;AACtQ;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAjCoG5G,EAAE,CAAA6G,iBAAA,CAiCXM,iBAAiB,EAAc,CAAC;IAC/GX,IAAI,EAAEtG,SAAS;IACf4G,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,qBAAqB;MAC/BJ,UAAU,EAAE,IAAI;MAChBK,IAAI,EAAE;QACF,OAAO,EAAE;MACb;IACJ,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMK,cAAc,CAAC;EACjBvD,WAAWA,CAACwD,WAAW,EAAE/D,IAAI,EAAE;IAC3B,IAAI,CAAC+D,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAAC/D,IAAI,GAAGA,IAAI;EACpB;EACA;EACAL,MAAMA,CAAA,EAAG;IACL,IAAI,CAACoE,WAAW,CAACzC,iBAAiB,CAAC,CAAC;EACxC;EACA;EACA,IAAI0C,SAASA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC,IAAI,CAAChE,IAAI,CAACL,MAAM;EAC7B;EACA;IAAS,IAAI,CAACiD,IAAI,YAAAqB,uBAAAnB,CAAA;MAAA,YAAAA,CAAA,IAAwFgB,cAAc,EAzDxBrH,EAAE,CAAAyH,iBAAA,CAyDwC5D,cAAc,GAzDxD7D,EAAE,CAAAyH,iBAAA,CAyDmE7B,kBAAkB;IAAA,CAA4C;EAAE;EACrO;IAAS,IAAI,CAAC8B,IAAI,kBA1D8E1H,EAAE,CAAA2H,iBAAA;MAAAnB,IAAA,EA0DJa,cAAc;MAAAZ,SAAA;MAAAC,SAAA;MAAAkB,QAAA;MAAAjB,UAAA;MAAAkB,QAAA,GA1DZ7H,EAAE,CAAA8H,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAA5F,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvC,EAAE,CAAA2C,cAAA,YA0D6L,CAAC;UA1DhM3C,EAAE,CAAAmD,MAAA,EA0DmN,CAAC;UA1DtNnD,EAAE,CAAAoD,YAAA,CA0DyN,CAAC;UA1D5NpD,EAAE,CAAAoI,UAAA,IAAA9F,qCAAA,gBA0D8O,CAAC;QAAA;QAAA,IAAAC,EAAA;UA1DjPvC,EAAE,CAAAqD,SAAA,CA0DmN,CAAC;UA1DtNrD,EAAE,CAAAsD,kBAAA,MAAAd,GAAA,CAAAe,IAAA,CAAA8E,OAAA,MA0DmN,CAAC;UA1DtNrI,EAAE,CAAAqD,SAAA,CA0D6X,CAAC;UA1DhYrD,EAAE,CAAAsI,aAAA,IAAA9F,GAAA,CAAA+E,SAAA,SA0D6X,CAAC;QAAA;MAAA;MAAAgB,YAAA,GAAoGxH,SAAS,EAAiLmF,gBAAgB,EAA+De,kBAAkB,EAAiEE,iBAAiB;MAAAqB,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAqI;EAAE;AAC5jC;AACA;EAAA,QAAA9B,SAAA,oBAAAA,SAAA,KA5DoG5G,EAAE,CAAA6G,iBAAA,CA4DXQ,cAAc,EAAc,CAAC;IAC5Gb,IAAI,EAAErG,SAAS;IACf2G,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,kBAAkB;MAAEa,QAAQ,EAAE,aAAa;MAAEa,aAAa,EAAErI,iBAAiB,CAACuI,IAAI;MAAED,eAAe,EAAErI,uBAAuB,CAACuI,MAAM;MAAEC,OAAO,EAAE,CAAC9H,SAAS,EAAEmF,gBAAgB,EAAEe,kBAAkB,EAAEE,iBAAiB,CAAC;MAAER,UAAU,EAAE,IAAI;MAAEK,IAAI,EAAE;QACnP,OAAO,EAAE;MACb,CAAC;MAAEkB,QAAQ,EAAE,0NAA0N;MAAEM,MAAM,EAAE,CAAC,yCAAyC;IAAE,CAAC;EAC1S,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEhC,IAAI,EAAE3C;EAAe,CAAC,EAAE;IAAE2C,IAAI,EAAEsC,SAAS;IAAEC,UAAU,EAAE,CAAC;MACzEvC,IAAI,EAAElG,MAAM;MACZwG,IAAI,EAAE,CAAClB,kBAAkB;IAC7B,CAAC;EAAE,CAAC,CAAC;AAAA;;AAErB;AACA;AACA;AACA;AACA,MAAMoD,qBAAqB,GAAG;EAC1B;EACAC,aAAa,EAAE9H,OAAO,CAAC,OAAO,EAAE,CAC5BC,KAAK,CAAC,cAAc,EAAEC,KAAK,CAAC;IACxB6H,SAAS,EAAE,YAAY;IACvBC,OAAO,EAAE;EACb,CAAC,CAAC,CAAC,EACH/H,KAAK,CAAC,SAAS,EAAEC,KAAK,CAAC;IACnB6H,SAAS,EAAE,UAAU;IACrBC,OAAO,EAAE;EACb,CAAC,CAAC,CAAC,EACH7H,UAAU,CAAC,cAAc,EAAEC,OAAO,CAAC,kCAAkC,CAAC,CAAC,EACvED,UAAU,CAAC,wBAAwB,EAAEC,OAAO,CAAC,mCAAmC,EAAEF,KAAK,CAAC;IACpF8H,OAAO,EAAE;EACb,CAAC,CAAC,CAAC,CAAC,CACP;AACL,CAAC;AAED,IAAIC,QAAQ,GAAG,CAAC;AAChB;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,SAAS7H,gBAAgB,CAAC;EAChDsC,WAAWA,CAACwF,OAAO,EAAEC,WAAW,EAAEC,kBAAkB,EAAEC,SAAS,EAC/D;EACAC,cAAc,EAAE;IACZ,KAAK,CAAC,CAAC;IACP,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,SAAS,GAAGpJ,MAAM,CAACW,QAAQ,CAAC;IACjC,IAAI,CAAC0I,cAAc,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC/B;IACA,IAAI,CAACC,cAAc,GAAG,GAAG;IACzB;IACA,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACC,WAAW,GAAG,IAAI/I,OAAO,CAAC,CAAC;IAChC;IACA,IAAI,CAACoD,OAAO,GAAG,IAAIpD,OAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAACyE,QAAQ,GAAG,IAAIzE,OAAO,CAAC,CAAC;IAC7B;IACA,IAAI,CAACgJ,eAAe,GAAG,MAAM;IAC7B;IACA,IAAI,CAACC,cAAc,GAAG,gCAAgCd,QAAQ,EAAE,EAAE;IAClE;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACe,eAAe,GAAIC,MAAM,IAAK;MAC/B,IAAI,CAACC,kBAAkB,CAAC,CAAC;MACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACJ,eAAe,CAACC,MAAM,CAAC;MACzD,IAAI,CAACI,oBAAoB,CAAC,CAAC;MAC3B,OAAOF,MAAM;IACjB,CAAC;IACD;IACA;IACA,IAAIZ,cAAc,CAAC5D,UAAU,KAAK,WAAW,IAAI,CAAC4D,cAAc,CAAC3D,mBAAmB,EAAE;MAClF,IAAI,CAAC0E,KAAK,GAAG,WAAW;IAC5B,CAAC,MACI,IAAIf,cAAc,CAAC5D,UAAU,KAAK,KAAK,EAAE;MAC1C,IAAI,CAAC2E,KAAK,GAAG,KAAK;IACtB,CAAC,MACI;MACD,IAAI,CAACA,KAAK,GAAG,QAAQ;IACzB;IACA;IACA;IACA,IAAI,IAAI,CAAChB,SAAS,CAACiB,OAAO,EAAE;MACxB,IAAI,IAAI,CAACD,KAAK,KAAK,QAAQ,EAAE;QACzB,IAAI,CAACE,KAAK,GAAG,QAAQ;MACzB;MACA,IAAI,IAAI,CAACF,KAAK,KAAK,WAAW,EAAE;QAC5B,IAAI,CAACE,KAAK,GAAG,OAAO;MACxB;IACJ;EACJ;EACA;EACAC,qBAAqBA,CAACR,MAAM,EAAE;IAC1B,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACK,qBAAqB,CAACR,MAAM,CAAC;IAC/D,IAAI,CAACI,oBAAoB,CAAC,CAAC;IAC3B,OAAOF,MAAM;EACjB;EACA;EACAO,oBAAoBA,CAACT,MAAM,EAAE;IACzB,IAAI,CAACC,kBAAkB,CAAC,CAAC;IACzB,MAAMC,MAAM,GAAG,IAAI,CAACC,aAAa,CAACM,oBAAoB,CAACT,MAAM,CAAC;IAC9D,IAAI,CAACI,oBAAoB,CAAC,CAAC;IAC3B,OAAOF,MAAM;EACjB;EACA;EACAQ,cAAcA,CAACC,KAAK,EAAE;IAClB,MAAM;MAAEC,SAAS;MAAEC;IAAQ,CAAC,GAAGF,KAAK;IACpC,IAAKE,OAAO,KAAK,MAAM,IAAID,SAAS,KAAK,MAAM,IAAKC,OAAO,KAAK,QAAQ,EAAE;MACtE,IAAI,CAACC,aAAa,CAAC,CAAC;IACxB;IACA,IAAID,OAAO,KAAK,SAAS,EAAE;MACvB;MACA;MACA,MAAME,OAAO,GAAG,IAAI,CAACzF,QAAQ;MAC7B,IAAI,CAAC4D,OAAO,CAAC8B,GAAG,CAAC,MAAM;QACnBD,OAAO,CAACrG,IAAI,CAAC,CAAC;QACdqG,OAAO,CAACpG,QAAQ,CAAC,CAAC;MACtB,CAAC,CAAC;IACN;EACJ;EACA;EACAsG,KAAKA,CAAA,EAAG;IACJ,IAAI,CAAC,IAAI,CAACtB,UAAU,EAAE;MAClB,IAAI,CAACE,eAAe,GAAG,SAAS;MAChC;MACA;MACA,IAAI,CAACT,kBAAkB,CAAC8B,YAAY,CAAC,CAAC;MACtC,IAAI,CAAC9B,kBAAkB,CAAC+B,aAAa,CAAC,CAAC;MACvC,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAChC;EACJ;EACA;EACA9G,IAAIA,CAAA,EAAG;IACH;IACA;IACA,IAAI,CAAC4E,OAAO,CAAC8B,GAAG,CAAC,MAAM;MACnB;MACA;MACA;MACA,IAAI,CAACnB,eAAe,GAAG,QAAQ;MAC/B,IAAI,CAACT,kBAAkB,CAAC8B,YAAY,CAAC,CAAC;MACtC;MACA;MACA;MACA,IAAI,CAAC/B,WAAW,CAACkC,aAAa,CAACC,YAAY,CAAC,UAAU,EAAE,EAAE,CAAC;MAC3D;MACA;MACA/G,YAAY,CAAC,IAAI,CAACgH,kBAAkB,CAAC;IACzC,CAAC,CAAC;IACF,OAAO,IAAI,CAACtH,OAAO;EACvB;EACA;EACAuH,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC7B,UAAU,GAAG,IAAI;IACtB,IAAI,CAAC8B,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACX,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;EACIA,aAAaA,CAAA,EAAG;IACZY,cAAc,CAAC,MAAM;MACjB,IAAI,CAACzH,OAAO,CAACS,IAAI,CAAC,CAAC;MACnB,IAAI,CAACT,OAAO,CAACU,QAAQ,CAAC,CAAC;IAC3B,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;EACIyF,oBAAoBA,CAAA,EAAG;IACnB,MAAMuB,OAAO,GAAG,IAAI,CAACxC,WAAW,CAACkC,aAAa;IAC9C,MAAMO,YAAY,GAAG,IAAI,CAACtC,cAAc,CAACuC,UAAU;IACnD,IAAID,YAAY,EAAE;MACd,IAAIE,KAAK,CAACC,OAAO,CAACH,YAAY,CAAC,EAAE;QAC7B;QACAA,YAAY,CAACI,OAAO,CAACC,QAAQ,IAAIN,OAAO,CAACO,SAAS,CAACC,GAAG,CAACF,QAAQ,CAAC,CAAC;MACrE,CAAC,MACI;QACDN,OAAO,CAACO,SAAS,CAACC,GAAG,CAACP,YAAY,CAAC;MACvC;IACJ;IACA,IAAI,CAACQ,eAAe,CAAC,CAAC;IACtB;IACA;IACA;IACA,MAAMC,KAAK,GAAG,IAAI,CAACC,MAAM,CAACjB,aAAa;IACvC,MAAMkB,UAAU,GAAG,qBAAqB;IACxCF,KAAK,CAACH,SAAS,CAACM,MAAM,CAACD,UAAU,EAAE,CAACF,KAAK,CAACI,aAAa,CAAC,IAAIF,UAAU,EAAE,CAAC,CAAC;EAC9E;EACA;AACJ;AACA;AACA;AACA;EACIH,eAAeA,CAAA,EAAG;IACd;IACA;IACA;IACA;IACA;IACA;IACA,MAAMM,EAAE,GAAG,IAAI,CAAC5C,cAAc;IAC9B,MAAM6C,MAAM,GAAG,IAAI,CAACpD,SAAS,CAACqD,gBAAgB,CAAC,mDAAmD,CAAC;IACnG,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGF,MAAM,CAACG,MAAM,EAAED,CAAC,EAAE,EAAE;MACpC,MAAME,KAAK,GAAGJ,MAAM,CAACE,CAAC,CAAC;MACvB,MAAMG,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;MAChD,IAAI,CAACzD,cAAc,CAAC2C,GAAG,CAACY,KAAK,CAAC;MAC9B,IAAI,CAACC,QAAQ,EAAE;QACXD,KAAK,CAACzB,YAAY,CAAC,WAAW,EAAEoB,EAAE,CAAC;MACvC,CAAC,MACI,IAAIM,QAAQ,CAACE,OAAO,CAACR,EAAE,CAAC,KAAK,CAAC,CAAC,EAAE;QAClCK,KAAK,CAACzB,YAAY,CAAC,WAAW,EAAE0B,QAAQ,GAAG,GAAG,GAAGN,EAAE,CAAC;MACxD;IACJ;EACJ;EACA;EACAjB,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACjC,cAAc,CAACwC,OAAO,CAACe,KAAK,IAAI;MACjC,MAAMC,QAAQ,GAAGD,KAAK,CAACE,YAAY,CAAC,WAAW,CAAC;MAChD,IAAID,QAAQ,EAAE;QACV,MAAMG,QAAQ,GAAGH,QAAQ,CAACI,OAAO,CAAC,IAAI,CAACtD,cAAc,EAAE,EAAE,CAAC,CAACuD,IAAI,CAAC,CAAC;QACjE,IAAIF,QAAQ,CAACL,MAAM,GAAG,CAAC,EAAE;UACrBC,KAAK,CAACzB,YAAY,CAAC,WAAW,EAAE6B,QAAQ,CAAC;QAC7C,CAAC,MACI;UACDJ,KAAK,CAACO,eAAe,CAAC,WAAW,CAAC;QACtC;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAAC9D,cAAc,CAAC+D,KAAK,CAAC,CAAC;EAC/B;EACA;EACAtD,kBAAkBA,CAAA,EAAG;IACjB,IAAI,IAAI,CAACE,aAAa,CAACqD,WAAW,CAAC,CAAC,KAAK,OAAOhH,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrF,MAAMiH,KAAK,CAAC,0EAA0E,CAAC;IAC3F;EACJ;EACA;AACJ;AACA;AACA;EACIrC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAAC,IAAI,CAACG,kBAAkB,EAAE;MAC1B,IAAI,CAACrC,OAAO,CAACwE,iBAAiB,CAAC,MAAM;QACjC,IAAI,CAACnC,kBAAkB,GAAGxG,UAAU,CAAC,MAAM;UACvC,MAAM4I,YAAY,GAAG,IAAI,CAACxE,WAAW,CAACkC,aAAa,CAACoB,aAAa,CAAC,eAAe,CAAC;UAClF,MAAMmB,WAAW,GAAG,IAAI,CAACzE,WAAW,CAACkC,aAAa,CAACoB,aAAa,CAAC,aAAa,CAAC;UAC/E,IAAIkB,YAAY,IAAIC,WAAW,EAAE;YAC7B;YACA;YACA,IAAIC,cAAc,GAAG,IAAI;YACzB,IAAI,IAAI,CAACxE,SAAS,CAACyE,SAAS,IACxBC,QAAQ,CAACC,aAAa,YAAYC,WAAW,IAC7CN,YAAY,CAACO,QAAQ,CAACH,QAAQ,CAACC,aAAa,CAAC,EAAE;cAC/CH,cAAc,GAAGE,QAAQ,CAACC,aAAa;YAC3C;YACAL,YAAY,CAACL,eAAe,CAAC,aAAa,CAAC;YAC3CM,WAAW,CAACO,WAAW,CAACR,YAAY,CAAC;YACrCE,cAAc,EAAEO,KAAK,CAAC,CAAC;YACvB,IAAI,CAACxE,WAAW,CAAClF,IAAI,CAAC,CAAC;YACvB,IAAI,CAACkF,WAAW,CAACjF,QAAQ,CAAC,CAAC;UAC/B;QACJ,CAAC,EAAE,IAAI,CAAC+E,cAAc,CAAC;MAC3B,CAAC,CAAC;IACN;EACJ;EACA;IAAS,IAAI,CAAC3D,IAAI,YAAAsI,6BAAApI,CAAA;MAAA,YAAAA,CAAA,IAAwFgD,oBAAoB,EA9U9BrJ,EAAE,CAAAyH,iBAAA,CA8U8CzH,EAAE,CAAC0O,MAAM,GA9UzD1O,EAAE,CAAAyH,iBAAA,CA8UoEzH,EAAE,CAAC2O,UAAU,GA9UnF3O,EAAE,CAAAyH,iBAAA,CA8U8FzH,EAAE,CAAC4O,iBAAiB,GA9UpH5O,EAAE,CAAAyH,iBAAA,CA8U+H5F,EAAE,CAACgN,QAAQ,GA9U5I7O,EAAE,CAAAyH,iBAAA,CA8UuJ5B,iBAAiB;IAAA,CAA4C;EAAE;EACxT;IAAS,IAAI,CAAC6B,IAAI,kBA/U8E1H,EAAE,CAAA2H,iBAAA;MAAAnB,IAAA,EA+UJ6C,oBAAoB;MAAA5C,SAAA;MAAAqI,SAAA,WAAAC,2BAAAxM,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA/UlBvC,EAAE,CAAAgP,WAAA,CA+UgVvN,eAAe;UA/UjWzB,EAAE,CAAAgP,WAAA,CAAAxL,GAAA;QAAA;QAAA,IAAAjB,EAAA;UAAA,IAAA0M,EAAA;UAAFjP,EAAE,CAAAkP,cAAA,CAAAD,EAAA,GAAFjP,EAAE,CAAAmP,WAAA,QAAA3M,GAAA,CAAA+H,aAAA,GAAA0E,EAAA,CAAAG,KAAA;UAAFpP,EAAE,CAAAkP,cAAA,CAAAD,EAAA,GAAFjP,EAAE,CAAAmP,WAAA,QAAA3M,GAAA,CAAAkK,MAAA,GAAAuC,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAA1I,SAAA;MAAA2I,QAAA;MAAAC,YAAA,WAAAC,kCAAAhN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvC,EAAE,CAAAwP,uBAAA,yBAAAC,6DAAAC,MAAA;YAAA,OA+UJlN,GAAA,CAAAsI,cAAA,CAAA4E,MAAqB,CAAC;UAAA,CAAH,CAAC;QAAA;QAAA,IAAAnN,EAAA;UA/UlBvC,EAAE,CAAA2P,uBAAA,WAAAnN,GAAA,CAAAyH,eA+Ue,CAAC;QAAA;MAAA;MAAAtD,UAAA;MAAAkB,QAAA,GA/UlB7H,EAAE,CAAA4P,0BAAA,EAAF5P,EAAE,CAAA8H,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA2H,8BAAAtN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvC,EAAE,CAAA2C,cAAA,YA+UyjB,CAAC,eAA6M,CAAC,YAAqI,CAAC;UA/Uh5B3C,EAAE,CAAAoI,UAAA,IAAA3E,2CAAA,wBA+Uo7B,CAAC;UA/Uv7BzD,EAAE,CAAAoD,YAAA,CA+Ug8B,CAAC;UA/Un8BpD,EAAE,CAAA8P,SAAA,SA+UspC,CAAC;UA/UzpC9P,EAAE,CAAAoD,YAAA,CA+UgqC,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAb,EAAA;UA/U3qCvC,EAAE,CAAAqD,SAAA,EA+U4lC,CAAC;UA/U/lCrD,EAAE,CAAA+P,WAAA,cAAAvN,GAAA,CAAAiI,KAAA,UAAAjI,GAAA,CAAAmI,KAAA,QAAAnI,GAAA,CAAA0H,cAAA;QAAA;MAAA;MAAA3B,YAAA,GA+U6mJ9G,eAAe;MAAA+G,MAAA;MAAAC,aAAA;MAAAlF,IAAA;QAAAyM,SAAA,EAAmI,CAAChH,qBAAqB,CAACC,aAAa;MAAC;IAAA,EAAkG;EAAE;AAC9+J;AACA;EAAA,QAAArC,SAAA,oBAAAA,SAAA,KAjVoG5G,EAAE,CAAA6G,iBAAA,CAiVXwC,oBAAoB,EAAc,CAAC;IAClH7C,IAAI,EAAErG,SAAS;IACf2G,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,yBAAyB;MAAE2B,eAAe,EAAErI,uBAAuB,CAAC4P,OAAO;MAAExH,aAAa,EAAErI,iBAAiB,CAACuI,IAAI;MAAEuH,UAAU,EAAE,CAAClH,qBAAqB,CAACC,aAAa,CAAC;MAAEtC,UAAU,EAAE,IAAI;MAAEkC,OAAO,EAAE,CAACpH,eAAe,CAAC;MAAEuF,IAAI,EAAE;QAClO,OAAO,EAAE,6DAA6D;QACtE,UAAU,EAAE,iBAAiB;QAC7B,eAAe,EAAE;MACrB,CAAC;MAAEkB,QAAQ,EAAE,wpBAAwpB;MAAEM,MAAM,EAAE,CAAC,y4GAAy4G;IAAE,CAAC;EACxkI,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEhC,IAAI,EAAExG,EAAE,CAAC0O;EAAO,CAAC,EAAE;IAAElI,IAAI,EAAExG,EAAE,CAAC2O;EAAW,CAAC,EAAE;IAAEnI,IAAI,EAAExG,EAAE,CAAC4O;EAAkB,CAAC,EAAE;IAAEpI,IAAI,EAAE3E,EAAE,CAACgN;EAAS,CAAC,EAAE;IAAErI,IAAI,EAAEX;EAAkB,CAAC,CAAC,EAAkB;IAAE0E,aAAa,EAAE,CAAC;MACxL/D,IAAI,EAAEhG,SAAS;MACfsG,IAAI,EAAE,CAACrF,eAAe,EAAE;QAAE0O,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC,CAAC;IAAEzD,MAAM,EAAE,CAAC;MACTlG,IAAI,EAAEhG,SAAS;MACfsG,IAAI,EAAE,CAAC,OAAO,EAAE;QAAEqJ,MAAM,EAAE;MAAK,CAAC;IACpC,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA,SAASC,qCAAqCA,CAAA,EAAG;EAC7C,OAAO,IAAIvK,iBAAiB,CAAC,CAAC;AAClC;AACA;AACA,MAAMwK,6BAA6B,GAAG,IAAIpQ,cAAc,CAAC,+BAA+B,EAAE;EACtFqQ,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEH;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA,MAAMI,WAAW,CAAC;EACd;EACA,IAAIC,kBAAkBA,CAAA,EAAG;IACrB,MAAMC,MAAM,GAAG,IAAI,CAACC,eAAe;IACnC,OAAOD,MAAM,GAAGA,MAAM,CAACD,kBAAkB,GAAG,IAAI,CAACG,uBAAuB;EAC5E;EACA,IAAIH,kBAAkBA,CAACI,KAAK,EAAE;IAC1B,IAAI,IAAI,CAACF,eAAe,EAAE;MACtB,IAAI,CAACA,eAAe,CAACF,kBAAkB,GAAGI,KAAK;IACnD,CAAC,MACI;MACD,IAAI,CAACD,uBAAuB,GAAGC,KAAK;IACxC;EACJ;EACA/M,WAAWA,CAACgN,QAAQ,EAAErG,KAAK,EAAEsG,SAAS,EAAEC,mBAAmB,EAAEL,eAAe,EAAEM,cAAc,EAAE;IAC1F,IAAI,CAACH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACrG,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACsG,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,mBAAmB,GAAGA,mBAAmB;IAC9C,IAAI,CAACL,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACM,cAAc,GAAGA,cAAc;IACpC;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACL,uBAAuB,GAAG,IAAI;IACnC;IACA,IAAI,CAACM,uBAAuB,GAAG7J,cAAc;IAC7C;IACA,IAAI,CAAC8J,0BAA0B,GAAG9H,oBAAoB;IACtD;IACA,IAAI,CAAC+H,eAAe,GAAG,2BAA2B;EACtD;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIC,iBAAiBA,CAACC,SAAS,EAAEC,MAAM,EAAE;IACjC,OAAO,IAAI,CAACC,OAAO,CAACF,SAAS,EAAEC,MAAM,CAAC;EAC1C;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIE,gBAAgBA,CAACvJ,QAAQ,EAAEqJ,MAAM,EAAE;IAC/B,OAAO,IAAI,CAACC,OAAO,CAACtJ,QAAQ,EAAEqJ,MAAM,CAAC;EACzC;EACA;AACJ;AACA;AACA;AACA;AACA;EACIG,IAAIA,CAACrJ,OAAO,EAAEnF,MAAM,GAAG,EAAE,EAAEqO,MAAM,EAAE;IAC/B,MAAMI,OAAO,GAAG;MAAE,GAAG,IAAI,CAACV,cAAc;MAAE,GAAGM;IAAO,CAAC;IACrD;IACA;IACAI,OAAO,CAACpO,IAAI,GAAG;MAAE8E,OAAO;MAAEnF;IAAO,CAAC;IAClC;IACA;IACA,IAAIyO,OAAO,CAAC5L,mBAAmB,KAAKsC,OAAO,EAAE;MACzCsJ,OAAO,CAAC5L,mBAAmB,GAAG+C,SAAS;IAC3C;IACA,OAAO,IAAI,CAACuI,iBAAiB,CAAC,IAAI,CAACH,uBAAuB,EAAES,OAAO,CAAC;EACxE;EACA;AACJ;AACA;EACInN,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACiM,kBAAkB,EAAE;MACzB,IAAI,CAACA,kBAAkB,CAACjM,OAAO,CAAC,CAAC;IACrC;EACJ;EACAoH,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,IAAI,CAACgF,uBAAuB,EAAE;MAC9B,IAAI,CAACA,uBAAuB,CAACpM,OAAO,CAAC,CAAC;IAC1C;EACJ;EACA;AACJ;AACA;EACIoN,wBAAwBA,CAACC,UAAU,EAAEN,MAAM,EAAE;IACzC,MAAMO,YAAY,GAAGP,MAAM,IAAIA,MAAM,CAACQ,gBAAgB,IAAIR,MAAM,CAACQ,gBAAgB,CAACC,QAAQ;IAC1F,MAAMA,QAAQ,GAAGvR,QAAQ,CAACwR,MAAM,CAAC;MAC7BvB,MAAM,EAAEoB,YAAY,IAAI,IAAI,CAACf,SAAS;MACtCmB,SAAS,EAAE,CAAC;QAAEC,OAAO,EAAEtM,iBAAiB;QAAEuM,QAAQ,EAAEb;MAAO,CAAC;IAChE,CAAC,CAAC;IACF,MAAMc,eAAe,GAAG,IAAI3Q,eAAe,CAAC,IAAI,CAACyP,0BAA0B,EAAEI,MAAM,CAACQ,gBAAgB,EAAEC,QAAQ,CAAC;IAC/G,MAAMM,YAAY,GAAGT,UAAU,CAACU,MAAM,CAACF,eAAe,CAAC;IACvDC,YAAY,CAACE,QAAQ,CAAC9I,cAAc,GAAG6H,MAAM;IAC7C,OAAOe,YAAY,CAACE,QAAQ;EAChC;EACA;AACJ;AACA;EACIhB,OAAOA,CAACiB,OAAO,EAAEC,UAAU,EAAE;IACzB,MAAMnB,MAAM,GAAG;MAAE,GAAG,IAAI1L,iBAAiB,CAAC,CAAC;MAAE,GAAG,IAAI,CAACoL,cAAc;MAAE,GAAGyB;IAAW,CAAC;IACpF,MAAMb,UAAU,GAAG,IAAI,CAACc,cAAc,CAACpB,MAAM,CAAC;IAC9C,MAAMqB,SAAS,GAAG,IAAI,CAAChB,wBAAwB,CAACC,UAAU,EAAEN,MAAM,CAAC;IACnE,MAAMjK,WAAW,GAAG,IAAIzD,cAAc,CAAC+O,SAAS,EAAEf,UAAU,CAAC;IAC7D,IAAIY,OAAO,YAAY/R,WAAW,EAAE;MAChC,MAAM0J,MAAM,GAAG,IAAIzI,cAAc,CAAC8Q,OAAO,EAAE,IAAI,EAAE;QAC7CI,SAAS,EAAEtB,MAAM,CAAChO,IAAI;QACtB+D;MACJ,CAAC,CAAC;MACFA,WAAW,CAACkL,QAAQ,GAAGI,SAAS,CAAC/H,oBAAoB,CAACT,MAAM,CAAC;IACjE,CAAC,MACI;MACD,MAAM4H,QAAQ,GAAG,IAAI,CAACc,eAAe,CAACvB,MAAM,EAAEjK,WAAW,CAAC;MAC1D,MAAM8C,MAAM,GAAG,IAAI1I,eAAe,CAAC+Q,OAAO,EAAE3J,SAAS,EAAEkJ,QAAQ,CAAC;MAChE,MAAMe,UAAU,GAAGH,SAAS,CAAChI,qBAAqB,CAACR,MAAM,CAAC;MAC1D;MACA9C,WAAW,CAACkL,QAAQ,GAAGO,UAAU,CAACP,QAAQ;IAC9C;IACA;IACA;IACA;IACA,IAAI,CAACxB,mBAAmB,CACnBgC,OAAO,CAAChR,WAAW,CAACiR,eAAe,CAAC,CACpCC,IAAI,CAAC9Q,SAAS,CAACyP,UAAU,CAACsB,WAAW,CAAC,CAAC,CAAC,CAAC,CACzC7O,SAAS,CAAClD,KAAK,IAAI;MACpByQ,UAAU,CAACuB,cAAc,CAAC9G,SAAS,CAACM,MAAM,CAAC,IAAI,CAACwE,eAAe,EAAEhQ,KAAK,CAACiS,OAAO,CAAC;IACnF,CAAC,CAAC;IACF,IAAI9B,MAAM,CAACxL,mBAAmB,EAAE;MAC5B;MACA6M,SAAS,CAAC5I,WAAW,CAAC1F,SAAS,CAAC,MAAM;QAClC,IAAI,CAACmG,KAAK,CAAC6I,QAAQ,CAAC/B,MAAM,CAACxL,mBAAmB,EAAEwL,MAAM,CAACzL,UAAU,CAAC;MACtE,CAAC,CAAC;IACN;IACA,IAAI,CAACyN,gBAAgB,CAACjM,WAAW,EAAEiK,MAAM,CAAC;IAC1C,IAAI,CAACd,kBAAkB,GAAGnJ,WAAW;IACrC,OAAO,IAAI,CAACmJ,kBAAkB;EAClC;EACA;EACA8C,gBAAgBA,CAACjM,WAAW,EAAEiK,MAAM,EAAE;IAClC;IACAjK,WAAW,CAAC9B,cAAc,CAAC,CAAC,CAAClB,SAAS,CAAC,MAAM;MACzC;MACA,IAAI,IAAI,CAACmM,kBAAkB,IAAInJ,WAAW,EAAE;QACxC,IAAI,CAACmJ,kBAAkB,GAAG,IAAI;MAClC;MACA,IAAIc,MAAM,CAACxL,mBAAmB,EAAE;QAC5B,IAAI,CAAC0E,KAAK,CAACkD,KAAK,CAAC,CAAC;MACtB;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAAC8C,kBAAkB,EAAE;MACzB;MACA;MACA,IAAI,CAACA,kBAAkB,CAACjL,cAAc,CAAC,CAAC,CAAClB,SAAS,CAAC,MAAM;QACrDgD,WAAW,CAACvD,iBAAiB,CAACsH,KAAK,CAAC,CAAC;MACzC,CAAC,CAAC;MACF,IAAI,CAACoF,kBAAkB,CAACjM,OAAO,CAAC,CAAC;IACrC,CAAC,MACI;MACD;MACA8C,WAAW,CAACvD,iBAAiB,CAACsH,KAAK,CAAC,CAAC;IACzC;IACA;IACA,IAAIkG,MAAM,CAACrM,QAAQ,IAAIqM,MAAM,CAACrM,QAAQ,GAAG,CAAC,EAAE;MACxCoC,WAAW,CAAC7B,WAAW,CAAC,CAAC,CAACnB,SAAS,CAAC,MAAMgD,WAAW,CAACrC,aAAa,CAACsM,MAAM,CAACrM,QAAQ,CAAC,CAAC;IACzF;EACJ;EACA;AACJ;AACA;AACA;EACIyN,cAAcA,CAACpB,MAAM,EAAE;IACnB,MAAMiC,aAAa,GAAG,IAAItR,aAAa,CAAC,CAAC;IACzCsR,aAAa,CAACC,SAAS,GAAGlC,MAAM,CAACkC,SAAS;IAC1C,IAAIC,gBAAgB,GAAG,IAAI,CAAC5C,QAAQ,CAAC6C,QAAQ,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC;IACxD;IACA,MAAMC,KAAK,GAAGtC,MAAM,CAACkC,SAAS,KAAK,KAAK;IACxC,MAAMK,MAAM,GAAGvC,MAAM,CAACvL,kBAAkB,KAAK,MAAM,IAC9CuL,MAAM,CAACvL,kBAAkB,KAAK,OAAO,IAAI,CAAC6N,KAAM,IAChDtC,MAAM,CAACvL,kBAAkB,KAAK,KAAK,IAAI6N,KAAM;IAClD,MAAME,OAAO,GAAG,CAACD,MAAM,IAAIvC,MAAM,CAACvL,kBAAkB,KAAK,QAAQ;IACjE,IAAI8N,MAAM,EAAE;MACRJ,gBAAgB,CAACM,IAAI,CAAC,GAAG,CAAC;IAC9B,CAAC,MACI,IAAID,OAAO,EAAE;MACdL,gBAAgB,CAACO,KAAK,CAAC,GAAG,CAAC;IAC/B,CAAC,MACI;MACDP,gBAAgB,CAACQ,kBAAkB,CAAC,CAAC;IACzC;IACA;IACA,IAAI3C,MAAM,CAACtL,gBAAgB,KAAK,KAAK,EAAE;MACnCyN,gBAAgB,CAACS,GAAG,CAAC,GAAG,CAAC;IAC7B,CAAC,MACI;MACDT,gBAAgB,CAACU,MAAM,CAAC,GAAG,CAAC;IAChC;IACAZ,aAAa,CAACE,gBAAgB,GAAGA,gBAAgB;IACjD,OAAO,IAAI,CAAC5C,QAAQ,CAACmB,MAAM,CAACuB,aAAa,CAAC;EAC9C;EACA;AACJ;AACA;AACA;AACA;EACIV,eAAeA,CAACvB,MAAM,EAAEjK,WAAW,EAAE;IACjC,MAAMwK,YAAY,GAAGP,MAAM,IAAIA,MAAM,CAACQ,gBAAgB,IAAIR,MAAM,CAACQ,gBAAgB,CAACC,QAAQ;IAC1F,OAAOvR,QAAQ,CAACwR,MAAM,CAAC;MACnBvB,MAAM,EAAEoB,YAAY,IAAI,IAAI,CAACf,SAAS;MACtCmB,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEtO,cAAc;QAAEuO,QAAQ,EAAE9K;MAAY,CAAC,EAClD;QAAE6K,OAAO,EAAEvM,kBAAkB;QAAEwM,QAAQ,EAAEb,MAAM,CAAChO;MAAK,CAAC;IAE9D,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAAC4C,IAAI,YAAAkO,oBAAAhO,CAAA;MAAA,YAAAA,CAAA,IAAwFmK,WAAW,EAtkBrBxQ,EAAE,CAAAsU,QAAA,CAskBqCrS,IAAI,CAACsS,OAAO,GAtkBnDvU,EAAE,CAAAsU,QAAA,CAskB8DxS,EAAE,CAAC0S,aAAa,GAtkBhFxU,EAAE,CAAAsU,QAAA,CAskB2FtU,EAAE,CAACS,QAAQ,GAtkBxGT,EAAE,CAAAsU,QAAA,CAskBmHvS,EAAE,CAAC0S,kBAAkB,GAtkB1IzU,EAAE,CAAAsU,QAAA,CAskBqJ9D,WAAW,OAtkBlKxQ,EAAE,CAAAsU,QAAA,CAskB6MjE,6BAA6B;IAAA,CAA6C;EAAE;EAC3X;IAAS,IAAI,CAACqE,KAAK,kBAvkB6E1U,EAAE,CAAA2U,kBAAA;MAAAC,KAAA,EAukBYpE,WAAW;MAAAD,OAAA,EAAXC,WAAW,CAAArK,IAAA;MAAAmK,UAAA,EAAc;IAAM,EAAG;EAAE;AACtJ;AACA;EAAA,QAAA1J,SAAA,oBAAAA,SAAA,KAzkBoG5G,EAAE,CAAA6G,iBAAA,CAykBX2J,WAAW,EAAc,CAAC;IACzGhK,IAAI,EAAE7F,UAAU;IAChBmG,IAAI,EAAE,CAAC;MAAEwJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE9J,IAAI,EAAEvE,IAAI,CAACsS;EAAQ,CAAC,EAAE;IAAE/N,IAAI,EAAE1E,EAAE,CAAC0S;EAAc,CAAC,EAAE;IAAEhO,IAAI,EAAExG,EAAE,CAACS;EAAS,CAAC,EAAE;IAAE+F,IAAI,EAAEzE,EAAE,CAAC0S;EAAmB,CAAC,EAAE;IAAEjO,IAAI,EAAEgK,WAAW;IAAEzH,UAAU,EAAE,CAAC;MAC7JvC,IAAI,EAAE5F;IACV,CAAC,EAAE;MACC4F,IAAI,EAAE3F;IACV,CAAC;EAAE,CAAC,EAAE;IAAE2F,IAAI,EAAEX,iBAAiB;IAAEkD,UAAU,EAAE,CAAC;MAC1CvC,IAAI,EAAElG,MAAM;MACZwG,IAAI,EAAE,CAACuJ,6BAA6B;IACxC,CAAC;EAAE,CAAC,CAAC;AAAA;AAErB,MAAMwE,UAAU,GAAG,CAACxL,oBAAoB,EAAEnD,gBAAgB,EAAEe,kBAAkB,EAAEE,iBAAiB,CAAC;AAClG,MAAM2N,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAAC3O,IAAI,YAAA4O,0BAAA1O,CAAA;MAAA,YAAAA,CAAA,IAAwFyO,iBAAiB;IAAA,CAAkD;EAAE;EAC/K;IAAS,IAAI,CAACE,IAAI,kBAxlB8EhV,EAAE,CAAAiV,gBAAA;MAAAzO,IAAA,EAwlBSsO;IAAiB,EAIwF;EAAE;EACtN;IAAS,IAAI,CAACI,IAAI,kBA7lB8ElV,EAAE,CAAAmV,gBAAA;MAAAjD,SAAA,EA6lBuC,CAAC1B,WAAW,CAAC;MAAA3H,OAAA,GAAY1G,aAAa,EACvKP,YAAY,EACZZ,eAAe,EACfqB,eAAe,EACfgF,cAAc,EAAEhF,eAAe;IAAA,EAAI;EAAE;AACjD;AACA;EAAA,QAAAuE,SAAA,oBAAAA,SAAA,KAnmBoG5G,EAAE,CAAA6G,iBAAA,CAmmBXiO,iBAAiB,EAAc,CAAC;IAC/GtO,IAAI,EAAE1F,QAAQ;IACdgG,IAAI,EAAE,CAAC;MACC+B,OAAO,EAAE,CACL1G,aAAa,EACbP,YAAY,EACZZ,eAAe,EACfqB,eAAe,EACfgF,cAAc,EACd,GAAGwN,UAAU,CAChB;MACDO,OAAO,EAAE,CAAC/S,eAAe,EAAE,GAAGwS,UAAU,CAAC;MACzC3C,SAAS,EAAE,CAAC1B,WAAW;IAC3B,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS5K,kBAAkB,EAAEyK,6BAA6B,EAAED,qCAAqC,EAAEI,WAAW,EAAErJ,iBAAiB,EAAEF,kBAAkB,EAAEpB,iBAAiB,EAAEwD,oBAAoB,EAAEnD,gBAAgB,EAAE4O,iBAAiB,EAAEjR,cAAc,EAAEwD,cAAc,EAAE2B,qBAAqB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}