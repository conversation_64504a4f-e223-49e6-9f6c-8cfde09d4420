import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../../core/services/auth.service';
import { User, UserRole, ValidationStatus, ValidatorStats } from '../../../../core/models';

// Interface locale pour les validations
interface ValidationRequest {
  id: string;
  userId: string;
  userName: string;
  userEmail: string;
  actionType: string;
  description: string;
  location?: string;
  submittedAt: Date;
  validatedAt?: Date;
  validatedBy?: string;
  validatorName?: string;
  points: number;
  status: ValidationStatus;
  evidence?: string[];
  comments?: string;
}

@Component({
  selector: 'app-validator-dashboard',
  templateUrl: './validator-dashboard.component.html',
  styleUrls: ['./validator-dashboard.component.css']
})
export class ValidatorDashboardComponent implements OnInit {
  user: User | null = null;
  validatorStats: ValidatorStats | null = null;
  pendingValidations: ValidationRequest[] = [];
  recentValidations: ValidationRequest[] = [];

  // Quick stats
  quickStats = [
    {
      title: 'Validations en Attente',
      value: '0',
      icon: 'pending_actions',
      color: '#FF9800',
      description: 'Actions à valider'
    },
    {
      title: 'Validations Aujourd\'hui',
      value: '0',
      icon: 'today',
      color: '#4CAF50',
      description: 'Validées aujourd\'hui'
    },
    {
      title: 'Total Validations',
      value: '0',
      icon: 'verified',
      color: '#2196F3',
      description: 'Toutes validations'
    },
    {
      title: 'Taux d\'Approbation',
      value: '0%',
      icon: 'thumb_up',
      color: '#9C27B0',
      description: 'Pourcentage approuvé'
    }
  ];

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.user = user;
      if (user && user.role === UserRole.VALIDATOR) {
        this.loadValidatorData();
      } else {
        this.router.navigate(['/dashboard']);
      }
    });
  }

  private loadValidatorData(): void {
    this.loadValidatorStats();
    this.loadPendingValidations();
    this.loadRecentValidations();
  }

  private loadValidatorStats(): void {
    // Simulate validator stats
    this.validatorStats = {
      validatorId: this.user!.uid,
      totalValidations: 156,
      validationsToday: 8,
      averageValidationTime: 2.5,
      approvalRate: 94,
      lastValidationAt: new Date(Date.now() - 1000 * 60 * 30)
    };

    // Update quick stats
    this.quickStats[0].value = this.pendingValidations.length.toString();
    this.quickStats[1].value = this.validatorStats.validationsToday.toString();
    this.quickStats[2].value = this.validatorStats.totalValidations.toString();
    this.quickStats[3].value = this.validatorStats.approvalRate + '%';
  }

  private loadPendingValidations(): void {
    // Simulate pending validations
    this.pendingValidations = [
      {
        id: '1',
        userId: 'user1',
        userName: 'Ahmed Ben Ali',
        userEmail: '<EMAIL>',
        actionType: 'LIBRARY_VOLUNTEER',
        description: 'Aide à la bibliothèque municipale - organisation des livres',
        location: 'Bibliothèque Municipale Monastir',
        submittedAt: new Date(Date.now() - 1000 * 60 * 30),
        points: 10,
        status: ValidationStatus.PENDING,
        evidence: ['https://example.com/photo1.jpg']
      },
      {
        id: '2',
        userId: 'user2',
        userName: 'Fatma Trabelsi',
        userEmail: '<EMAIL>',
        actionType: 'SCHOOL_HELP',
        description: 'Aide aux devoirs pour les élèves de primaire',
        location: 'École Primaire Sousse',
        submittedAt: new Date(Date.now() - 1000 * 60 * 60),
        points: 15,
        status: ValidationStatus.PENDING,
        evidence: ['https://example.com/photo2.jpg', 'https://example.com/photo3.jpg']
      },
      {
        id: '3',
        userId: 'user3',
        userName: 'Mohamed Gharbi',
        userEmail: '<EMAIL>',
        actionType: 'COMMUNITY_SERVICE',
        description: 'Nettoyage du parc municipal',
        location: 'Parc Central Monastir',
        submittedAt: new Date(Date.now() - 1000 * 60 * 90),
        points: 12,
        status: ValidationStatus.PENDING
      }
    ];

    this.quickStats[0].value = this.pendingValidations.length.toString();
  }

  private loadRecentValidations(): void {
    // Simulate recent validations
    this.recentValidations = [
      {
        id: '4',
        userId: 'user4',
        userName: 'Leila Mansouri',
        userEmail: '<EMAIL>',
        actionType: 'TUTORING',
        description: 'Cours de mathématiques pour lycéens',
        submittedAt: new Date(Date.now() - 1000 * 60 * 60 * 2),
        validatedAt: new Date(Date.now() - 1000 * 60 * 60),
        validatedBy: this.user!.uid,
        validatorName: this.user!.name,
        points: 20,
        status: ValidationStatus.APPROVED
      },
      {
        id: '5',
        userId: 'user5',
        userName: 'Karim Bouazizi',
        userEmail: '<EMAIL>',
        actionType: 'ENVIRONMENTAL_ACTION',
        description: 'Plantation d\'arbres dans le quartier',
        submittedAt: new Date(Date.now() - 1000 * 60 * 60 * 3),
        validatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2),
        validatedBy: this.user!.uid,
        validatorName: this.user!.name,
        points: 15,
        status: ValidationStatus.APPROVED
      }
    ];
  }

  getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Bonjour';
    if (hour < 18) return 'Bon après-midi';
    return 'Bonsoir';
  }

  getActionTypeDisplayName(actionType: string): string {
    const actionNames: { [key: string]: string } = {
      'SCHOOL_HELP': 'Aide scolaire',
      'LIBRARY_VOLUNTEER': 'Bénévolat bibliothèque',
      'ASSOCIATION_WORK': 'Travail associatif',
      'COMMUNITY_SERVICE': 'Service communautaire',
      'ENVIRONMENTAL_ACTION': 'Action environnementale',
      'CULTURAL_EVENT': 'Événement culturel',
      'SPORTS_COACHING': 'Coaching sportif',
      'TUTORING': 'Tutorat',
      'ELDERLY_CARE': 'Aide aux personnes âgées',
      'OTHER': 'Autre'
    };
    return actionNames[actionType] || actionType;
  }

  getStatusColor(status: ValidationStatus): string {
    const colors: { [key: string]: string } = {
      'pending': '#FF9800',
      'approved': '#4CAF50',
      'rejected': '#F44336'
    };
    return colors[status] || '#718096';
  }

  getStatusDisplayName(status: ValidationStatus): string {
    const statusNames: { [key: string]: string } = {
      'pending': 'En attente',
      'approved': 'Approuvée',
      'rejected': 'Rejetée'
    };
    return statusNames[status] || status;
  }

  validateAction(validation: ValidationRequest, approved: boolean): void {
    validation.status = approved ? ValidationStatus.APPROVED : ValidationStatus.REJECTED;
    validation.validatedAt = new Date();
    validation.validatedBy = this.user!.uid;
    validation.validatorName = this.user!.name;

    // Remove from pending list
    this.pendingValidations = this.pendingValidations.filter(v => v.id !== validation.id);
    
    // Add to recent validations
    this.recentValidations.unshift(validation);
    
    // Update stats
    this.validatorStats!.validationsToday++;
    this.validatorStats!.totalValidations++;
    this.quickStats[0].value = this.pendingValidations.length.toString();
    this.quickStats[1].value = this.validatorStats!.validationsToday.toString();
    this.quickStats[2].value = this.validatorStats!.totalValidations.toString();

    console.log(`Validation ${approved ? 'approved' : 'rejected'}:`, validation);
  }

  viewValidationDetails(validation: ValidationRequest): void {
    console.log('View validation details:', validation);
    // TODO: Open validation details modal
  }

  viewEvidence(evidenceUrl: string): void {
    console.log('View evidence:', evidenceUrl);
    // TODO: Open evidence viewer modal
    window.open(evidenceUrl, '_blank');
  }

  navigateTo(route: string): void {
    this.router.navigate([route]);
  }
}
