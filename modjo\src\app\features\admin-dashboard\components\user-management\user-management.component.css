.user-management-container {
  padding: 20px;
}

.user-management-container h2 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-size: 2rem;
  font-weight: 700;
}

.user-management-container p {
  margin: 0 0 32px 0;
  color: #718096;
  font-size: 1.1rem;
}

.filters-section {
  display: flex;
  gap: 16px;
  margin-bottom: 24px;
  align-items: center;
  flex-wrap: wrap;
}

.filters-section mat-form-field {
  min-width: 200px;
}

.users-table-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.table-container {
  overflow-x: auto;
}

.users-table {
  width: 100%;
}

.user-info strong {
  display: block;
  color: #2d3748;
  font-weight: 600;
}

.user-email {
  color: #718096;
  font-size: 0.9rem;
  margin-top: 2px;
}

.user-org {
  color: #4a5568;
  font-size: 0.8rem;
  font-style: italic;
  margin-top: 2px;
}

.role-badge, .status-badge {
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.delete-action {
  color: #F44336 !important;
}

.pagination-container {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 16px;
  padding-top: 16px;
  border-top: 1px solid #e2e8f0;
}

.pagination-info {
  color: #718096;
  font-size: 0.9rem;
}

.pagination-controls {
  display: flex;
  align-items: center;
  gap: 8px;
}

.page-info {
  color: #4a5568;
  font-weight: 500;
  min-width: 60px;
  text-align: center;
}

@media (max-width: 768px) {
  .filters-section {
    flex-direction: column;
    align-items: stretch;
  }
  
  .filters-section mat-form-field {
    min-width: auto;
  }
  
  .pagination-container {
    flex-direction: column;
    gap: 12px;
  }
}
