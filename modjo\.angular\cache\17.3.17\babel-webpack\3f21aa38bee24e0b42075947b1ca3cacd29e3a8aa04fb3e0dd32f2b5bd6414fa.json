{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\nimport { ValidatorDashboardComponent } from './components/validator-dashboard/validator-dashboard.component';\nimport { ValidationListComponent } from './components/validation-list/validation-list.component';\nimport { ValidationHistoryComponent } from './components/validation-history/validation-history.component';\nimport { validatorDashboardRoutes } from './validator-dashboard.routes';\nlet ValidatorDashboardModule = class ValidatorDashboardModule {};\nValidatorDashboardModule = __decorate([NgModule({\n  declarations: [ValidatorDashboardComponent, ValidationListComponent, ValidationHistoryComponent],\n  imports: [SharedModule, RouterModule.forChild(validatorDashboardRoutes)]\n})], ValidatorDashboardModule);\nexport { ValidatorDashboardModule };", "map": {"version": 3, "names": ["NgModule", "RouterModule", "SharedModule", "ValidatorDashboardComponent", "ValidationListComponent", "ValidationHistoryComponent", "validatorDashboardRoutes", "ValidatorDashboardModule", "__decorate", "declarations", "imports", "<PERSON><PERSON><PERSON><PERSON>"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\validator-dashboard\\validator-dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\nimport { SharedModule } from '../../shared/shared.module';\nimport { ValidatorDashboardComponent } from './components/validator-dashboard/validator-dashboard.component';\nimport { ValidationListComponent } from './components/validation-list/validation-list.component';\nimport { ValidationHistoryComponent } from './components/validation-history/validation-history.component';\nimport { validatorDashboardRoutes } from './validator-dashboard.routes';\n\n@NgModule({\n  declarations: [\n    ValidatorDashboardComponent,\n    ValidationListComponent,\n    ValidationHistoryComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(validatorDashboardRoutes)\n  ]\n})\nexport class ValidatorDashboardModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,2BAA2B,QAAQ,gEAAgE;AAC5G,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,0BAA0B,QAAQ,8DAA8D;AACzG,SAASC,wBAAwB,QAAQ,8BAA8B;AAahE,IAAMC,wBAAwB,GAA9B,MAAMA,wBAAwB,GAAI;AAA5BA,wBAAwB,GAAAC,UAAA,EAXpCR,QAAQ,CAAC;EACRS,YAAY,EAAE,CACZN,2BAA2B,EAC3BC,uBAAuB,EACvBC,0BAA0B,CAC3B;EACDK,OAAO,EAAE,CACPR,YAAY,EACZD,YAAY,CAACU,QAAQ,CAACL,wBAAwB,CAAC;CAElD,CAAC,C,EACWC,wBAAwB,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}