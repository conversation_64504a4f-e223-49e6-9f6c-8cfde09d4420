{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/icon\";\nfunction ProviderDashboardComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h1\");\n    i0.ɵɵtext(3, \"\\uD83E\\uDDD1\\u200D\\uD83D\\uDD27 Dashboard Prestataire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 3);\n    i0.ɵɵtext(7, \"G\\u00E9n\\u00E9rez des QR codes et suivez l'engagement\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 4)(9, \"mat-card\", 5)(10, \"mat-card-content\")(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"qr_code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"h3\");\n    i0.ɵɵtext(14, \"G\\u00E9n\\u00E9rer QR Code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\");\n    i0.ɵɵtext(16, \"Cr\\u00E9ez des QR codes pour 1, 3 ou 5 points\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 6);\n    i0.ɵɵtext(18, \"G\\u00E9n\\u00E9rer QR\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"mat-card\", 5)(20, \"mat-card-content\")(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"h3\");\n    i0.ɵɵtext(24, \"Statistiques d'Usage\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p\");\n    i0.ɵɵtext(26, \"Voir combien de fois vos QR codes ont \\u00E9t\\u00E9 scann\\u00E9s\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 7);\n    i0.ɵɵtext(28, \"Voir les Stats\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"mat-card\", 5)(30, \"mat-card-content\")(31, \"mat-icon\");\n    i0.ɵɵtext(32, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"h3\");\n    i0.ɵɵtext(34, \"Historique\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"p\");\n    i0.ɵɵtext(36, \"Acc\\u00E9dez \\u00E0 votre profil et mini historique\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 8);\n    i0.ɵɵtext(38, \"Voir l'Historique\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.getGreeting(), \", \", ctx_r0.user.name, \"!\");\n  }\n}\nexport class ProviderDashboardComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.user = null;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n    });\n  }\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n  static {\n    this.ɵfac = function ProviderDashboardComponent_Factory(t) {\n      return new (t || ProviderDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProviderDashboardComponent,\n      selectors: [[\"app-provider-dashboard\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"provider-dashboard-container\", 4, \"ngIf\"], [1, \"provider-dashboard-container\"], [1, \"provider-header\"], [1, \"subtitle\"], [1, \"provider-features\"], [1, \"feature-card\"], [\"mat-raised-button\", \"\", \"color\", \"primary\"], [\"mat-raised-button\", \"\", \"color\", \"accent\"], [\"mat-raised-button\", \"\", \"color\", \"warn\"]],\n      template: function ProviderDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ProviderDashboardComponent_div_0_Template, 39, 2, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n        }\n      },\n      dependencies: [i2.NgIf, i3.MatButton, i4.MatCard, i4.MatCardContent, i5.MatIcon],\n      styles: [\".provider-dashboard-container[_ngcontent-%COMP%] { padding: 20px; }\\n    .provider-header[_ngcontent-%COMP%] { text-align: center; margin-bottom: 40px; }\\n    .provider-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] { color: #2d3748; font-size: 2.5rem; margin-bottom: 16px; }\\n    .subtitle[_ngcontent-%COMP%] { color: #718096; font-size: 1.1rem; }\\n    .provider-features[_ngcontent-%COMP%] { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; }\\n    .feature-card[_ngcontent-%COMP%] { text-align: center; padding: 24px; }\\n    .feature-card[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] { font-size: 3rem; color: #667eea; margin-bottom: 16px; }\\n    .feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] { color: #2d3748; margin-bottom: 12px; }\\n    .feature-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] { color: #718096; margin-bottom: 20px; }\\n  \\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb3ZpZGVyLWRhc2hib2FyZC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJLGdDQUFnQyxhQUFhLEVBQUU7SUFDL0MsbUJBQW1CLGtCQUFrQixFQUFFLG1CQUFtQixFQUFFO0lBQzVELHNCQUFzQixjQUFjLEVBQUUsaUJBQWlCLEVBQUUsbUJBQW1CLEVBQUU7SUFDOUUsWUFBWSxjQUFjLEVBQUUsaUJBQWlCLEVBQUU7SUFDL0MscUJBQXFCLGFBQWEsRUFBRSwyREFBMkQsRUFBRSxTQUFTLEVBQUU7SUFDNUcsZ0JBQWdCLGtCQUFrQixFQUFFLGFBQWEsRUFBRTtJQUNuRCx5QkFBeUIsZUFBZSxFQUFFLGNBQWMsRUFBRSxtQkFBbUIsRUFBRTtJQUMvRSxtQkFBbUIsY0FBYyxFQUFFLG1CQUFtQixFQUFFO0lBQ3hELGtCQUFrQixjQUFjLEVBQUUsbUJBQW1CLEVBQUUiLCJmaWxlIjoicHJvdmlkZXItZGFzaGJvYXJkLmNvbXBvbmVudC50cyIsInNvdXJjZXNDb250ZW50IjpbIlxuICAgIC5wcm92aWRlci1kYXNoYm9hcmQtY29udGFpbmVyIHsgcGFkZGluZzogMjBweDsgfVxuICAgIC5wcm92aWRlci1oZWFkZXIgeyB0ZXh0LWFsaWduOiBjZW50ZXI7IG1hcmdpbi1ib3R0b206IDQwcHg7IH1cbiAgICAucHJvdmlkZXItaGVhZGVyIGgxIHsgY29sb3I6ICMyZDM3NDg7IGZvbnQtc2l6ZTogMi41cmVtOyBtYXJnaW4tYm90dG9tOiAxNnB4OyB9XG4gICAgLnN1YnRpdGxlIHsgY29sb3I6ICM3MTgwOTY7IGZvbnQtc2l6ZTogMS4xcmVtOyB9XG4gICAgLnByb3ZpZGVyLWZlYXR1cmVzIHsgZGlzcGxheTogZ3JpZDsgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgzMDBweCwgMWZyKSk7IGdhcDogMjRweDsgfVxuICAgIC5mZWF0dXJlLWNhcmQgeyB0ZXh0LWFsaWduOiBjZW50ZXI7IHBhZGRpbmc6IDI0cHg7IH1cbiAgICAuZmVhdHVyZS1jYXJkIG1hdC1pY29uIHsgZm9udC1zaXplOiAzcmVtOyBjb2xvcjogIzY2N2VlYTsgbWFyZ2luLWJvdHRvbTogMTZweDsgfVxuICAgIC5mZWF0dXJlLWNhcmQgaDMgeyBjb2xvcjogIzJkMzc0ODsgbWFyZ2luLWJvdHRvbTogMTJweDsgfVxuICAgIC5mZWF0dXJlLWNhcmQgcCB7IGNvbG9yOiAjNzE4MDk2OyBtYXJnaW4tYm90dG9tOiAyMHB4OyB9XG4gICJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvcHJvdmlkZXItZGFzaGJvYXJkL2NvbXBvbmVudHMvcHJvdmlkZXItZGFzaGJvYXJkL3Byb3ZpZGVyLWRhc2hib2FyZC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJLGdDQUFnQyxhQUFhLEVBQUU7SUFDL0MsbUJBQW1CLGtCQUFrQixFQUFFLG1CQUFtQixFQUFFO0lBQzVELHNCQUFzQixjQUFjLEVBQUUsaUJBQWlCLEVBQUUsbUJBQW1CLEVBQUU7SUFDOUUsWUFBWSxjQUFjLEVBQUUsaUJBQWlCLEVBQUU7SUFDL0MscUJBQXFCLGFBQWEsRUFBRSwyREFBMkQsRUFBRSxTQUFTLEVBQUU7SUFDNUcsZ0JBQWdCLGtCQUFrQixFQUFFLGFBQWEsRUFBRTtJQUNuRCx5QkFBeUIsZUFBZSxFQUFFLGNBQWMsRUFBRSxtQkFBbUIsRUFBRTtJQUMvRSxtQkFBbUIsY0FBYyxFQUFFLG1CQUFtQixFQUFFO0lBQ3hELGtCQUFrQixjQUFjLEVBQUUsbUJBQW1CLEVBQUU7O0FBRTNELHdoREFBd2hEIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLnByb3ZpZGVyLWRhc2hib2FyZC1jb250YWluZXIgeyBwYWRkaW5nOiAyMHB4OyB9XG4gICAgLnByb3ZpZGVyLWhlYWRlciB7IHRleHQtYWxpZ246IGNlbnRlcjsgbWFyZ2luLWJvdHRvbTogNDBweDsgfVxuICAgIC5wcm92aWRlci1oZWFkZXIgaDEgeyBjb2xvcjogIzJkMzc0ODsgZm9udC1zaXplOiAyLjVyZW07IG1hcmdpbi1ib3R0b206IDE2cHg7IH1cbiAgICAuc3VidGl0bGUgeyBjb2xvcjogIzcxODA5NjsgZm9udC1zaXplOiAxLjFyZW07IH1cbiAgICAucHJvdmlkZXItZmVhdHVyZXMgeyBkaXNwbGF5OiBncmlkOyBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDMwMHB4LCAxZnIpKTsgZ2FwOiAyNHB4OyB9XG4gICAgLmZlYXR1cmUtY2FyZCB7IHRleHQtYWxpZ246IGNlbnRlcjsgcGFkZGluZzogMjRweDsgfVxuICAgIC5mZWF0dXJlLWNhcmQgbWF0LWljb24geyBmb250LXNpemU6IDNyZW07IGNvbG9yOiAjNjY3ZWVhOyBtYXJnaW4tYm90dG9tOiAxNnB4OyB9XG4gICAgLmZlYXR1cmUtY2FyZCBoMyB7IGNvbG9yOiAjMmQzNzQ4OyBtYXJnaW4tYm90dG9tOiAxMnB4OyB9XG4gICAgLmZlYXR1cmUtY2FyZCBwIHsgY29sb3I6ICM3MTgwOTY7IG1hcmdpbi1ib3R0b206IDIwcHg7IH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r0", "getGreeting", "user", "name", "ProviderDashboardComponent", "constructor", "authService", "ngOnInit", "currentUser$", "subscribe", "hour", "Date", "getHours", "ɵɵdirectiveInject", "i1", "AuthService", "selectors", "decls", "vars", "consts", "template", "ProviderDashboardComponent_Template", "rf", "ctx", "ɵɵtemplate", "ProviderDashboardComponent_div_0_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\provider-dashboard\\components\\provider-dashboard\\provider-dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { User, UserRole } from '../../../../core/models';\n\n@Component({\n  selector: 'app-provider-dashboard',\n  template: `\n    <div class=\"provider-dashboard-container\" *ngIf=\"user\">\n      <div class=\"provider-header\">\n        <h1>🧑‍🔧 Dashboard Prestataire</h1>\n        <p>{{ getGreeting() }}, {{ user.name }}!</p>\n        <p class=\"subtitle\">Générez des QR codes et suivez l'engagement</p>\n      </div>\n      \n      <div class=\"provider-features\">\n        <mat-card class=\"feature-card\">\n          <mat-card-content>\n            <mat-icon>qr_code</mat-icon>\n            <h3>Générer QR Code</h3>\n            <p>Créez des QR codes pour 1, 3 ou 5 points</p>\n            <button mat-raised-button color=\"primary\">G<PERSON>érer QR</button>\n          </mat-card-content>\n        </mat-card>\n        \n        <mat-card class=\"feature-card\">\n          <mat-card-content>\n            <mat-icon>analytics</mat-icon>\n            <h3>Statistiques d'Usage</h3>\n            <p>Voir combien de fois vos QR codes ont été scannés</p>\n            <button mat-raised-button color=\"accent\">Voir les Stats</button>\n          </mat-card-content>\n        </mat-card>\n        \n        <mat-card class=\"feature-card\">\n          <mat-card-content>\n            <mat-icon>history</mat-icon>\n            <h3>Historique</h3>\n            <p>Accédez à votre profil et mini historique</p>\n            <button mat-raised-button color=\"warn\">Voir l'Historique</button>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .provider-dashboard-container { padding: 20px; }\n    .provider-header { text-align: center; margin-bottom: 40px; }\n    .provider-header h1 { color: #2d3748; font-size: 2.5rem; margin-bottom: 16px; }\n    .subtitle { color: #718096; font-size: 1.1rem; }\n    .provider-features { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; }\n    .feature-card { text-align: center; padding: 24px; }\n    .feature-card mat-icon { font-size: 3rem; color: #667eea; margin-bottom: 16px; }\n    .feature-card h3 { color: #2d3748; margin-bottom: 12px; }\n    .feature-card p { color: #718096; margin-bottom: 20px; }\n  `]\n})\nexport class ProviderDashboardComponent implements OnInit {\n  user: User | null = null;\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n    });\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n}\n"], "mappings": ";;;;;;;;IASQA,EAFJ,CAAAC,cAAA,aAAuD,aACxB,SACvB;IAAAD,EAAA,CAAAE,MAAA,2DAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5CH,EAAA,CAAAC,cAAA,WAAoB;IAAAD,EAAA,CAAAE,MAAA,4DAA2C;IACjEF,EADiE,CAAAG,YAAA,EAAI,EAC/D;IAKAH,EAHN,CAAAC,cAAA,aAA+B,kBACE,wBACX,gBACN;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,iCAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,qDAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC/CH,EAAA,CAAAC,cAAA,iBAA0C;IAAAD,EAAA,CAAAE,MAAA,4BAAU;IAExDF,EAFwD,CAAAG,YAAA,EAAS,EAC5C,EACV;IAIPH,EAFJ,CAAAC,cAAA,mBAA+B,wBACX,gBACN;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,wEAAiD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACxDH,EAAA,CAAAC,cAAA,iBAAyC;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAE3DF,EAF2D,CAAAG,YAAA,EAAS,EAC/C,EACV;IAIPH,EAFJ,CAAAC,cAAA,mBAA+B,wBACX,gBACN;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnBH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,2DAAyC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAChDH,EAAA,CAAAC,cAAA,iBAAuC;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAIhEF,EAJgE,CAAAG,YAAA,EAAS,EAChD,EACV,EACP,EACF;;;;IAhCCH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAC,WAAA,UAAAD,MAAA,CAAAE,IAAA,CAAAC,IAAA,MAAqC;;;AA8ChD,OAAM,MAAOC,0BAA0B;EAGrCC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAF/B,KAAAJ,IAAI,GAAgB,IAAI;EAEuB;EAE/CK,QAAQA,CAAA;IACN,IAAI,CAACD,WAAW,CAACE,YAAY,CAACC,SAAS,CAACP,IAAI,IAAG;MAC7C,IAAI,CAACA,IAAI,GAAGA,IAAI;IAClB,CAAC,CAAC;EACJ;EAEAD,WAAWA,CAAA;IACT,MAAMS,IAAI,GAAG,IAAIC,IAAI,EAAE,CAACC,QAAQ,EAAE;IAClC,IAAIF,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,SAAS;EAClB;;;uBAhBWN,0BAA0B,EAAAV,EAAA,CAAAmB,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAA1BX,0BAA0B;MAAAY,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjDnC5B,EAAA,CAAA8B,UAAA,IAAAC,yCAAA,kBAAuD;;;UAAZ/B,EAAA,CAAAgC,UAAA,SAAAH,GAAA,CAAArB,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}