{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class PartnerManagementComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function PartnerManagementComponent_Factory(t) {\n      return new (t || PartnerManagementComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PartnerManagementComponent,\n      selectors: [[\"app-partner-management\"]],\n      decls: 5,\n      vars: 0,\n      template: function PartnerManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"h2\");\n          i0.ɵɵtext(2, \"\\uD83E\\uDDD1\\u200D\\uD83C\\uDF73 Gestion Partenaires\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"Validation et suivi des partenaires commerciaux\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\"div[_ngcontent-%COMP%] { padding: 20px; }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInBhcnRuZXItbWFuYWdlbWVudC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsTUFBTSxhQUFhLEVBQUUiLCJmaWxlIjoicGFydG5lci1tYW5hZ2VtZW50LmNvbXBvbmVudC50cyIsInNvdXJjZXNDb250ZW50IjpbImRpdiB7IHBhZGRpbmc6IDIwcHg7IH0iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvYWRtaW4tZGFzaGJvYXJkL2NvbXBvbmVudHMvcGFydG5lci1tYW5hZ2VtZW50L3BhcnRuZXItbWFuYWdlbWVudC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IkFBQUEsTUFBTSxhQUFhLEVBQUU7QUFDckIsZ1RBQWdUIiwic291cmNlc0NvbnRlbnQiOlsiZGl2IHsgcGFkZGluZzogMjBweDsgfSJdLCJzb3VyY2VSb290IjoiIn0= */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["PartnerManagementComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "template", "PartnerManagementComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\admin-dashboard\\components\\partner-management\\partner-management.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-partner-management',\n  template: '<div><h2>🧑‍🍳 Gestion Partenaires</h2><p>Validation et suivi des partenaires commerciaux</p></div>',\n  styles: ['div { padding: 20px; }']\n})\nexport class PartnerManagementComponent implements OnInit {\n  constructor() { }\n  ngOnInit(): void { }\n}\n"], "mappings": ";AAOA,OAAM,MAAOA,0BAA0B;EACrCC,YAAA,GAAgB;EAChBC,QAAQA,CAAA,GAAW;;;uBAFRF,0BAA0B;IAAA;EAAA;;;YAA1BA,0BAA0B;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,oCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAHrBE,EAAL,CAAAC,cAAA,UAAK,SAAI;UAAAD,EAAA,CAAAE,MAAA,yDAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAAAH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,sDAA+C;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}