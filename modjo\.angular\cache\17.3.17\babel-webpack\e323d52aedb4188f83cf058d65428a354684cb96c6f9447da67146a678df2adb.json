{"ast": null, "code": "import { UserRole } from '../../../../core/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../core/services/dashboard-router.service\";\nimport * as i4 from \"../../../../core/services/mock-data.service\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nfunction DashboardComponent_div_0_div_42_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 28)(1, \"div\", 29)(2, \"div\", 30);\n    i0.ɵɵelement(3, \"img\", 31);\n    i0.ɵɵelementStart(4, \"div\", 32)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(9, \"div\", 33)(10, \"span\", 34);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 13);\n    i0.ɵɵtext(13, \"points restants\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(14, \"div\", 35);\n    i0.ɵɵelement(15, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 37)(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"div\", 38)(20, \"p\");\n    i0.ɵɵtext(21);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"src\", ctx_r0.nextReward.imageUrl, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r0.nextReward.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.nextReward.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r0.nextReward.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getPointsToNextReward());\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"width\", ctx_r0.getProgressPercentage() + \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.user.points, \" / \", ctx_r0.nextReward.pointsRequired, \" points\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r0.getMotivationMessage());\n  }\n}\nfunction DashboardComponent_div_0_div_49_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 48)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_0_div_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40);\n    i0.ɵɵelement(2, \"img\", 41);\n    i0.ɵɵtemplate(3, DashboardComponent_div_0_div_49_div_3_Template, 3, 0, \"div\", 42);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 43)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 44);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 45)(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"store\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 46)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"button\", 47);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const reward_r2 = ctx.$implicit;\n    const ctx_r0 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"affordable\", ctx_r0.user.points >= reward_r2.pointsRequired);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", reward_r2.imageUrl, i0.ɵɵsanitizeUrl)(\"alt\", reward_r2.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.user.points >= reward_r2.pointsRequired);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(reward_r2.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reward_r2.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(reward_r2.partnerName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", reward_r2.pointsRequired, \" points\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r0.user.points >= reward_r2.pointsRequired ? \"primary\" : \"\")(\"disabled\", ctx_r0.user.points < reward_r2.pointsRequired);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r0.user.points >= reward_r2.pointsRequired ? \"\\u00C9changer\" : \"Pas assez de points\", \" \");\n  }\n}\nfunction DashboardComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"div\", 4)(3, \"div\", 5)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"school\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 6)(7, \"h2\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 7);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 8)(12, \"div\", 9)(13, \"mat-icon\", 10);\n    i0.ɵɵtext(14, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 11)(16, \"span\", 12);\n    i0.ɵɵtext(17);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"span\", 13);\n    i0.ɵɵtext(19, \"Points\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(20, \"button\", 14)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"notifications\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(23, \"div\", 15)(24, \"div\", 16)(25, \"div\", 17)(26, \"div\", 18)(27, \"mat-icon\");\n    i0.ɵɵtext(28, \"qr_code_scanner\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"div\", 19)(30, \"h3\");\n    i0.ɵɵtext(31, \"Scanner un QR Code KnowMe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"p\");\n    i0.ɵɵtext(33, \"D\\u00E9couvre un prestataire et gagne des points !\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"button\", 20)(35, \"mat-icon\");\n    i0.ɵɵtext(36, \"camera_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37, \" Scanner \");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(38, \"div\", 21)(39, \"div\", 22)(40, \"h3\");\n    i0.ɵɵtext(41, \"Progression vers ta prochaine r\\u00E9compense\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(42, DashboardComponent_div_0_div_42_Template, 22, 10, \"div\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(43, \"div\", 24)(44, \"h3\", 25)(45, \"mat-icon\");\n    i0.ɵɵtext(46, \"card_giftcard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(47, \" R\\u00E9compenses disponibles \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(48, \"div\", 26);\n    i0.ɵɵtemplate(49, DashboardComponent_div_0_div_49_Template, 21, 12, \"div\", 27);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"Bonjour \", ctx_r0.user.name, \" ! \\uD83D\\uDC4B\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u00C9l\\u00E8ve \\u2022 \", ctx_r0.user.city, \"\");\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r0.user.points);\n    i0.ɵɵadvance(25);\n    i0.ɵɵproperty(\"ngIf\", ctx_r0.nextReward);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r0.availableRewards);\n  }\n}\nexport class DashboardComponent {\n  constructor(authService, router, dashboardRouter, mockDataService) {\n    this.authService = authService;\n    this.router = router;\n    this.dashboardRouter = dashboardRouter;\n    this.mockDataService = mockDataService;\n    this.user = null;\n    // Données pour le dashboard élève\n    this.availableRewards = [];\n    this.nextReward = null;\n    this.filteredHistory = [];\n    this.monthlyStats = {\n      pointsEarned: 0,\n      pointsSpent: 0,\n      scansCount: 0,\n      validationsCount: 0\n    };\n    this.achievements = [];\n    this.notifications = [];\n    this.weeklyPointsData = [];\n    this.maxWeeklyPoints = 100;\n    // État de l'interface\n    this.selectedPeriod = 'month';\n    this.showNotifications = false;\n    this.unreadNotifications = 0;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        // Rediriger vers le dashboard approprié selon le rôle\n        if (user.role !== UserRole.USER) {\n          this.dashboardRouter.navigateToUserDashboard(user);\n          return;\n        }\n        this.loadStudentDashboardData();\n      }\n    });\n  }\n  loadStudentDashboardData() {\n    this.loadAvailableRewards();\n    this.loadUserHistory();\n    this.loadMonthlyStats();\n    this.loadAchievements();\n    this.loadNotifications();\n    this.loadWeeklyPointsData();\n    this.setNextReward();\n  }\n  loadAvailableRewards() {\n    const mockData = this.mockDataService.getMockDataForRole(UserRole.USER);\n    this.availableRewards = mockData.availableRewards || [{\n      id: 'reward1',\n      title: 'Café gratuit',\n      description: 'Un café offert au Café des Nattes',\n      pointsRequired: 50,\n      partnerName: 'Café des Nattes',\n      imageUrl: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',\n      image: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',\n      category: 'FOOD',\n      isActive: true\n    }, {\n      id: 'reward2',\n      title: '10% de réduction',\n      description: 'Réduction sur tous les produits artisanaux',\n      pointsRequired: 75,\n      partnerName: 'Boutique Artisanat',\n      imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',\n      image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',\n      category: 'SHOPPING',\n      isActive: true\n    }, {\n      id: 'reward3',\n      title: 'Entrée gratuite',\n      description: 'Visite gratuite du musée de Sousse',\n      pointsRequired: 100,\n      partnerName: 'Musée de Sousse',\n      imageUrl: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300',\n      image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300',\n      category: 'CULTURE',\n      isActive: true\n    }];\n  }\n  loadUserHistory() {\n    if (!this.user) return;\n    // Charger l'historique depuis les données utilisateur\n    this.filteredHistory = this.user.history?.map(h => ({\n      id: h.id,\n      type: h.type,\n      description: h.description,\n      points: h.points,\n      timestamp: h.timestamp,\n      location: 'Monastir',\n      providerName: 'Prestataire local' // Exemple\n    })) || [];\n    this.applyHistoryFilter();\n  }\n  loadMonthlyStats() {\n    if (!this.user) return;\n    // Calculer les statistiques du mois en cours\n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n    const monthlyActions = this.user.history?.filter(h => {\n      const actionDate = new Date(h.timestamp);\n      return actionDate.getMonth() === currentMonth && actionDate.getFullYear() === currentYear;\n    }) || [];\n    this.monthlyStats = {\n      pointsEarned: monthlyActions.filter(a => a.points > 0).reduce((sum, a) => sum + a.points, 0),\n      pointsSpent: Math.abs(monthlyActions.filter(a => a.points < 0).reduce((sum, a) => sum + a.points, 0)),\n      scansCount: monthlyActions.filter(a => a.description.includes('Scan QR')).length,\n      validationsCount: monthlyActions.filter(a => !a.description.includes('Scan QR')).length\n    };\n  }\n  loadAchievements() {\n    this.achievements = [{\n      id: 'first_scan',\n      title: 'Premier Scan',\n      description: 'Scanner votre premier QR Code',\n      icon: 'qr_code_scanner',\n      color: '#4CAF50',\n      earned: (this.user?.history?.length || 0) > 0,\n      progress: Math.min((this.user?.history?.length || 0) * 100, 100),\n      current: this.user?.history?.length || 0,\n      target: 1\n    }, {\n      id: 'point_collector',\n      title: 'Collecteur de Points',\n      description: 'Atteindre 100 points',\n      icon: 'stars',\n      color: '#FFD700',\n      earned: (this.user?.points || 0) >= 100,\n      progress: Math.min(this.user?.points || 0, 100),\n      current: this.user?.points || 0,\n      target: 100\n    }, {\n      id: 'community_helper',\n      title: 'Aide Communautaire',\n      description: 'Effectuer 5 bonnes actions',\n      icon: 'volunteer_activism',\n      color: '#E91E63',\n      earned: (this.user?.history?.length || 0) >= 5,\n      progress: Math.min((this.user?.history?.length || 0) * 20, 100),\n      current: this.user?.history?.length || 0,\n      target: 5\n    }, {\n      id: 'reward_redeemer',\n      title: 'Échangeur de Récompenses',\n      description: 'Échanger votre première récompense',\n      icon: 'card_giftcard',\n      color: '#FF9800',\n      earned: false,\n      progress: 0,\n      current: 0,\n      target: 1\n    }];\n  }\n  loadNotifications() {\n    this.notifications = [{\n      id: 'notif1',\n      type: 'points_earned',\n      title: 'Points gagnés !',\n      message: 'Vous avez gagné 15 points pour votre aide à la bibliothèque',\n      timestamp: new Date(Date.now() - 1000 * 60 * 30),\n      read: false\n    }, {\n      id: 'notif2',\n      type: 'new_reward',\n      title: 'Nouvelle récompense !',\n      message: 'Une nouvelle récompense est disponible au Café des Nattes',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2),\n      read: false\n    }, {\n      id: 'notif3',\n      type: 'achievement',\n      title: 'Achievement débloqué !',\n      message: 'Félicitations ! Vous avez débloqué le badge \"Premier Scan\"',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),\n      read: true\n    }];\n    this.unreadNotifications = this.notifications.filter(n => !n.read).length;\n  }\n  loadWeeklyPointsData() {\n    // Simuler les données de points par semaine pour le graphique\n    this.weeklyPointsData = [{\n      week: 1,\n      points: 45\n    }, {\n      week: 2,\n      points: 62\n    }, {\n      week: 3,\n      points: 38\n    }, {\n      week: 4,\n      points: 75\n    }];\n    this.maxWeeklyPoints = Math.max(...this.weeklyPointsData.map(w => w.points));\n  }\n  setNextReward() {\n    if (!this.user || this.availableRewards.length === 0) return;\n    // Trouver la prochaine récompense atteignable\n    const affordableRewards = this.availableRewards.filter(r => r.pointsRequired > this.user.points).sort((a, b) => a.pointsRequired - b.pointsRequired);\n    this.nextReward = affordableRewards[0] || this.availableRewards[0];\n  }\n  // Méthodes pour le dashboard élève\n  // Progression vers la prochaine récompense\n  getPointsToNextReward() {\n    if (!this.user || !this.nextReward) return 0;\n    return Math.max(0, this.nextReward.pointsRequired - this.user.points);\n  }\n  getProgressPercentage() {\n    if (!this.user || !this.nextReward) return 0;\n    return Math.min(this.user.points / this.nextReward.pointsRequired * 100, 100);\n  }\n  getMotivationMessage() {\n    const pointsNeeded = this.getPointsToNextReward();\n    if (pointsNeeded === 0) {\n      return \"🎉 Félicitations ! Vous pouvez échanger cette récompense !\";\n    } else if (pointsNeeded <= 10) {\n      return `🔥 Plus que ${pointsNeeded} points ! Vous y êtes presque !`;\n    } else if (pointsNeeded <= 25) {\n      return `💪 Encore ${pointsNeeded} points et c'est à vous !`;\n    } else {\n      return `🎯 Objectif : ${pointsNeeded} points pour votre prochaine récompense !`;\n    }\n  }\n  // Filtrage de l'historique\n  filterHistory() {\n    this.applyHistoryFilter();\n  }\n  applyHistoryFilter() {\n    if (!this.user?.history) {\n      this.filteredHistory = [];\n      return;\n    }\n    const now = new Date();\n    let startDate;\n    switch (this.selectedPeriod) {\n      case 'week':\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        break;\n      case 'month':\n        startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n        break;\n      case 'year':\n        startDate = new Date(now.getFullYear(), 0, 1);\n        break;\n      default:\n        this.filteredHistory = this.user.history.map(h => ({\n          id: h.id,\n          type: h.type,\n          description: h.description,\n          points: h.points,\n          timestamp: h.timestamp,\n          location: 'Monastir',\n          providerName: 'Prestataire local'\n        }));\n        return;\n    }\n    this.filteredHistory = this.user.history.filter(h => new Date(h.timestamp) >= startDate).map(h => ({\n      id: h.id,\n      type: h.type,\n      description: h.description,\n      points: h.points,\n      timestamp: h.timestamp,\n      location: 'Monastir',\n      providerName: 'Prestataire local'\n    }));\n  }\n  // Méthodes helper pour l'interface\n  getActionColor(type) {\n    const colorMap = {\n      'earned': '#4CAF50',\n      'spent': '#F44336',\n      'qr_scan': '#2196F3',\n      'validation': '#FF9800'\n    };\n    return colorMap[type] || '#667eea';\n  }\n  getActionIcon(type) {\n    const iconMap = {\n      'earned': 'trending_up',\n      'spent': 'trending_down',\n      'qr_scan': 'qr_code_scanner',\n      'validation': 'verified'\n    };\n    return iconMap[type] || 'circle';\n  }\n  getNotificationColor(type) {\n    const colorMap = {\n      'points_earned': '#4CAF50',\n      'new_reward': '#FF9800',\n      'achievement': '#9C27B0',\n      'reminder': '#2196F3'\n    };\n    return colorMap[type] || '#667eea';\n  }\n  getNotificationIcon(type) {\n    const iconMap = {\n      'points_earned': 'stars',\n      'new_reward': 'card_giftcard',\n      'achievement': 'emoji_events',\n      'reminder': 'notifications'\n    };\n    return iconMap[type] || 'info';\n  }\n  // Méthodes d'action\n  openQRScanner() {\n    console.log('Ouverture du scanner QR...');\n    this.router.navigate(['/qr-scanner']);\n  }\n  exchangeReward(reward) {\n    if (!this.user || this.user.points < reward.pointsRequired) {\n      console.log('Pas assez de points pour cette récompense');\n      return;\n    }\n    console.log('Échange de récompense:', reward);\n    // TODO: Implémenter l'échange de récompense\n    // - Déduire les points\n    // - Générer un code d'échange\n    // - Mettre à jour l'historique\n  }\n  toggleNotifications() {\n    this.showNotifications = !this.showNotifications;\n  }\n  markAsRead(notification) {\n    notification.read = true;\n    this.unreadNotifications = this.notifications.filter(n => !n.read).length;\n  }\n  openProfile() {\n    this.router.navigate(['/profile']);\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/auth/login']);\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.DashboardRouterService), i0.ɵɵdirectiveInject(i4.MockDataService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 4,\n      vars: 1,\n      consts: [[\"class\", \"dashboard-container\", 4, \"ngIf\"], [\"mat-fab\", \"\", \"color\", \"primary\", 1, \"floating-qr-btn\"], [1, \"dashboard-container\"], [1, \"dashboard-header\"], [1, \"user-welcome\"], [1, \"user-avatar\"], [1, \"user-info\"], [1, \"user-role\"], [1, \"header-actions\"], [1, \"points-card\"], [1, \"points-icon\"], [1, \"points-info\"], [1, \"points-value\"], [1, \"points-label\"], [\"mat-icon-button\", \"\", 1, \"notifications-btn\"], [1, \"qr-scan-section\"], [1, \"qr-scan-card\"], [1, \"qr-scan-content\"], [1, \"qr-scan-icon\"], [1, \"qr-scan-text\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"qr-scan-btn\"], [1, \"progress-section\"], [1, \"progress-card\"], [\"class\", \"reward-progress\", 4, \"ngIf\"], [1, \"rewards-section\"], [1, \"section-subtitle\"], [1, \"rewards-grid\"], [\"class\", \"reward-card\", 3, \"affordable\", 4, \"ngFor\", \"ngForOf\"], [1, \"reward-progress\"], [1, \"progress-info\"], [1, \"next-reward\"], [1, \"reward-icon\", 3, \"src\", \"alt\"], [1, \"reward-details\"], [1, \"points-needed\"], [1, \"points-remaining\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"motivation-message\"], [1, \"reward-card\"], [1, \"reward-image\"], [3, \"src\", \"alt\"], [\"class\", \"reward-badge\", 4, \"ngIf\"], [1, \"reward-content\"], [1, \"reward-description\"], [1, \"reward-partner\"], [1, \"reward-points\"], [\"mat-raised-button\", \"\", 1, \"exchange-btn\", 3, \"color\", \"disabled\"], [1, \"reward-badge\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, DashboardComponent_div_0_Template, 50, 5, \"div\", 0);\n          i0.ɵɵelementStart(1, \"button\", 1)(2, \"mat-icon\");\n          i0.ɵɵtext(3, \"qr_code_scanner\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i6.MatButton, i6.MatIconButton, i6.MatFabButton, i7.MatIcon],\n      styles: [\"\\n\\n.dashboard-container[_ngcontent-%COMP%] {\\n  padding: 20px;\\n  max-width: 1200px;\\n  margin: 0 auto;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  min-height: 100vh;\\n  font-family: 'Roboto', sans-serif;\\n}\\n\\n\\n\\n.dashboard-header[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  margin-bottom: 24px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n}\\n\\n.user-welcome[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  width: 56px;\\n  height: 56px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n}\\n\\n.user-avatar[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.8rem;\\n}\\n\\n.user-info[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #2d3748;\\n  font-weight: 600;\\n  font-size: 1.3rem;\\n}\\n\\n.user-role[_ngcontent-%COMP%] {\\n  color: #718096;\\n  margin: 0;\\n  font-size: 0.9rem;\\n}\\n\\n.header-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.points-card[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  background: linear-gradient(135deg, #FFD700, #FFA500);\\n  color: white;\\n  padding: 12px 20px;\\n  border-radius: 20px;\\n  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);\\n}\\n\\n.points-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem !important;\\n}\\n\\n.points-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.points-value[_ngcontent-%COMP%] {\\n  font-size: 1.4rem;\\n  font-weight: 700;\\n  line-height: 1;\\n}\\n\\n.points-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  opacity: 0.9;\\n}\\n\\n.notifications-btn[_ngcontent-%COMP%] {\\n  background: #f7fafc;\\n  color: #4a5568;\\n}\\n\\n\\n\\n.qr-scan-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.qr-scan-card[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4CAF50, #45a049);\\n  color: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);\\n}\\n\\n.qr-scan-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 24px;\\n}\\n\\n.qr-scan-icon[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  flex-shrink: 0;\\n}\\n\\n.qr-scan-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem !important;\\n}\\n\\n.qr-scan-text[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.qr-scan-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n}\\n\\n.qr-scan-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  opacity: 0.9;\\n}\\n\\n.qr-scan-btn[_ngcontent-%COMP%] {\\n  background: white;\\n  color: #4CAF50;\\n  font-weight: 600;\\n  padding: 12px 24px;\\n  border-radius: 12px;\\n}\\n\\n\\n\\n.progress-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.progress-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);\\n}\\n\\n.progress-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #2d3748;\\n  font-weight: 600;\\n  font-size: 1.2rem;\\n}\\n\\n.progress-info[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n\\n.next-reward[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.reward-icon[_ngcontent-%COMP%] {\\n  width: 50px;\\n  height: 50px;\\n  border-radius: 8px;\\n  object-fit: cover;\\n}\\n\\n.reward-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #2d3748;\\n  font-weight: 600;\\n}\\n\\n.reward-details[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n\\n.points-needed[_ngcontent-%COMP%] {\\n  text-align: right;\\n}\\n\\n.points-remaining[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #667eea;\\n}\\n\\n.points-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 12px;\\n  background: #e2e8f0;\\n  border-radius: 6px;\\n  overflow: hidden;\\n  margin-bottom: 8px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 6px;\\n  transition: width 0.3s ease;\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 0.9rem;\\n  color: #718096;\\n  margin-bottom: 12px;\\n}\\n\\n.motivation-message[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 12px;\\n  background: #f7fafc;\\n  border-radius: 8px;\\n}\\n\\n.motivation-message[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #4a5568;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.rewards-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.section-subtitle[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  color: white;\\n  font-size: 1.4rem;\\n  font-weight: 700;\\n  margin-bottom: 20px;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);\\n}\\n\\n.section-subtitle[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.6rem !important;\\n  width: 1.6rem !important;\\n  height: 1.6rem !important;\\n}\\n\\n.rewards-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n  gap: 20px;\\n}\\n\\n.reward-card[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 20px;\\n  overflow: hidden;\\n  transition: transform 0.3s ease, box-shadow 0.3s ease;\\n  box-shadow: 0 6px 24px rgba(0, 0, 0, 0.1);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.reward-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-6px);\\n  box-shadow: 0 12px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.reward-card.affordable[_ngcontent-%COMP%] {\\n  border: 3px solid #4CAF50;\\n  box-shadow: 0 6px 24px rgba(76, 175, 80, 0.2);\\n}\\n\\n.reward-card.affordable[_ngcontent-%COMP%]:hover {\\n  box-shadow: 0 12px 40px rgba(76, 175, 80, 0.3);\\n}\\n\\n.reward-image[_ngcontent-%COMP%] {\\n  position: relative;\\n  height: 180px;\\n  overflow: hidden;\\n}\\n\\n.reward-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 100%;\\n  object-fit: cover;\\n  transition: transform 0.3s ease;\\n}\\n\\n.reward-card[_ngcontent-%COMP%]:hover   .reward-image[_ngcontent-%COMP%]   img[_ngcontent-%COMP%] {\\n  transform: scale(1.05);\\n}\\n\\n.reward-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  background: #4CAF50;\\n  color: white;\\n  border-radius: 50%;\\n  width: 40px;\\n  height: 40px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  box-shadow: 0 4px 12px rgba(76, 175, 80, 0.4);\\n  border: 2px solid white;\\n}\\n\\n.reward-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 10px 0;\\n  color: #2d3748;\\n  font-weight: 700;\\n  font-size: 1.2rem;\\n  padding: 20px 20px 0;\\n}\\n\\n.reward-description[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: 0.95rem;\\n  margin-bottom: 16px;\\n  padding: 0 20px;\\n  font-weight: 500;\\n  line-height: 1.5;\\n}\\n\\n.reward-partner[_ngcontent-%COMP%], .reward-points[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 10px;\\n  margin-bottom: 12px;\\n  font-size: 0.95rem;\\n  color: #4a5568;\\n  padding: 0 20px;\\n  font-weight: 600;\\n}\\n\\n.reward-partner[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%], .reward-points[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem !important;\\n  width: 1.2rem !important;\\n  height: 1.2rem !important;\\n}\\n\\n.exchange-btn[_ngcontent-%COMP%] {\\n  width: calc(100% - 40px);\\n  margin: 0 20px 20px;\\n  padding: 12px;\\n  font-weight: 700;\\n  border-radius: 16px;\\n  font-size: 1rem;\\n  transition: all 0.2s ease;\\n}\\n\\n.exchange-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n}\\n\\n\\n\\n.floating-qr-btn[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 32px;\\n  right: 32px;\\n  z-index: 1000;\\n  box-shadow: 0 12px 32px rgba(76, 175, 80, 0.4);\\n  width: 64px;\\n  height: 64px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #4CAF50, #45a049);\\n  border: 3px solid white;\\n  transition: all 0.3s ease;\\n}\\n\\n.floating-qr-btn[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1);\\n  box-shadow: 0 16px 40px rgba(76, 175, 80, 0.5);\\n}\\n\\n.floating-qr-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem !important;\\n  width: 2rem !important;\\n  height: 2rem !important;\\n  color: white;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 12px;\\n  }\\n\\n  .dashboard-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n    text-align: center;\\n    padding: 20px;\\n  }\\n\\n  .qr-scan-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 24px;\\n    padding: 24px;\\n  }\\n\\n  .progress-info[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n    text-align: center;\\n  }\\n\\n  .rewards-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .floating-qr-btn[_ngcontent-%COMP%] {\\n    bottom: 20px;\\n    right: 20px;\\n    width: 56px;\\n    height: 56px;\\n  }\\n\\n  .floating-qr-btn[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    font-size: 1.8rem !important;\\n    width: 1.8rem !important;\\n    height: 1.8rem !important;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n\\n  .dashboard-header[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .qr-scan-content[_ngcontent-%COMP%] {\\n    padding: 20px;\\n  }\\n\\n  .points-card[_ngcontent-%COMP%] {\\n    padding: 12px 16px;\\n  }\\n\\n  .points-value[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .qr-scan-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .qr-scan-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .qr-scan-btn[_ngcontent-%COMP%] {\\n    padding: 12px 24px;\\n    font-size: 1rem;\\n  }\\n}\\n\\n.hero-section.city-monastir[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.hero-section.city-sousse[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n}\\n\\n.hero-section[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n.city-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.hero-title[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 3rem;\\n  font-weight: 800;\\n  font-family: 'Poppins', sans-serif;\\n  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\\n  background: linear-gradient(45deg, #ffffff, #f0f0f0);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.hero-subtitle[_ngcontent-%COMP%] {\\n  margin: 0 0 32px 0;\\n  font-size: 1.2rem;\\n  opacity: 0.9;\\n  font-weight: 400;\\n}\\n\\n.hero-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 32px;\\n}\\n\\n.hero-stat[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  line-height: 1;\\n  margin-bottom: 4px;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  opacity: 0.8;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n}\\n\\n.hero-visual[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.city-illustration[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 120px;\\n  background: rgba(255, 255, 255, 0.15);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.city-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem !important;\\n  width: 4rem !important;\\n  height: 4rem !important;\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n\\n\\n\\n.impact-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  margin: 0 0 32px 0;\\n  color: #2d3748;\\n  font-size: 2rem;\\n  font-weight: 700;\\n  font-family: 'Poppins', sans-serif;\\n  text-align: center;\\n  position: relative;\\n  padding-bottom: 16px;\\n}\\n\\n.section-title[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 80px;\\n  height: 4px;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  border-radius: 2px;\\n}\\n\\n.impact-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 24px;\\n}\\n\\n.impact-card[_ngcontent-%COMP%] {\\n  border-radius: 20px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.impact-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.impact-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #2d3748;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n\\n\\n\\n.city-progress[_ngcontent-%COMP%]   .progress-ring[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 auto 24px;\\n  width: 120px;\\n  height: 120px;\\n}\\n\\n.progress-svg[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n\\n.progress-bg[_ngcontent-%COMP%] {\\n  fill: none;\\n  stroke: #e2e8f0;\\n  stroke-width: 8;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  fill: none;\\n  stroke: #667eea;\\n  stroke-width: 8;\\n  stroke-linecap: round;\\n  transition: stroke-dasharray 1s ease-in-out;\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n}\\n\\n.progress-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #2d3748;\\n}\\n\\n.progress-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.progress-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  padding: 8px 0;\\n  border-bottom: 1px solid #e2e8f0;\\n}\\n\\n.detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n\\n\\n.community-feed[_ngcontent-%COMP%]   .feed-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n\\n.feed-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background: #f7fafc;\\n  border-radius: 12px;\\n  transition: all 0.2s ease;\\n}\\n\\n.feed-item[_ngcontent-%COMP%]:hover {\\n  background: #edf2f7;\\n  transform: translateX(4px);\\n}\\n\\n.feed-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  flex-shrink: 0;\\n}\\n\\n.feed-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.feed-text[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 0.9rem;\\n  color: #2d3748;\\n  font-weight: 500;\\n}\\n\\n.feed-time[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.feed-points[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78, #38a169);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n}\\n\\n.view-all-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-top: 8px;\\n}\\n\\n\\n\\n.leaderboard[_ngcontent-%COMP%]   .leaderboard-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.leader-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  border-radius: 12px;\\n  transition: all 0.2s ease;\\n}\\n\\n.leader-item[_ngcontent-%COMP%]:hover {\\n  background: #f7fafc;\\n}\\n\\n.leader-item.current-user[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\\n  border: 2px solid rgba(102, 126, 234, 0.3);\\n}\\n\\n.leader-rank[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 700;\\n  border-radius: 50%;\\n  background: #e2e8f0;\\n  color: #4a5568;\\n}\\n\\n.trophy-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem !important;\\n  width: 1.5rem !important;\\n  height: 1.5rem !important;\\n}\\n\\n.rank-number[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.leader-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.leader-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  color: #2d3748;\\n  font-size: 0.9rem;\\n}\\n\\n.leader-city[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.leader-points[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #667eea;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.events-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.events-carousel[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 24px;\\n  overflow-x: auto;\\n  padding-bottom: 16px;\\n  scroll-behavior: smooth;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 8px;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #667eea;\\n  border-radius: 4px;\\n}\\n\\n.event-card[_ngcontent-%COMP%] {\\n  min-width: 320px;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.event-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.event-image[_ngcontent-%COMP%] {\\n  height: 160px;\\n  background-size: cover;\\n  background-position: center;\\n  position: relative;\\n  background-color: #e2e8f0;\\n}\\n\\n.event-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  color: white;\\n  text-transform: uppercase;\\n}\\n\\n.badge-environnement[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78, #38a169);\\n}\\n\\n.badge-culture[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ed8936, #dd6b20);\\n}\\n\\n.badge-\\u00E9ducation[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4299e1, #3182ce);\\n}\\n\\n.event-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #2d3748;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.event-description[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n}\\n\\n.event-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.event-detail[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 0.8rem;\\n  color: #4a5568;\\n}\\n\\n.event-detail[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  width: 1rem !important;\\n  height: 1rem !important;\\n  color: #667eea;\\n}\\n\\n.event-join-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 36px;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.partners-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.partners-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 24px;\\n}\\n\\n.partner-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.partner-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.partner-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 20px 20px 0;\\n}\\n\\n.partner-logo[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 12px;\\n  object-fit: cover;\\n  background: #f7fafc;\\n}\\n\\n.partner-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #2d3748;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.partner-category[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n\\n.partner-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.star-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  width: 1rem !important;\\n  height: 1rem !important;\\n  color: #ffd700;\\n}\\n\\n.partner-offer[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  color: #2d3748;\\n  font-weight: 500;\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\\n  padding: 12px;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n}\\n\\n.partner-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.partner-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 36px;\\n  font-size: 0.8rem;\\n}\\n\\n\\n\\n.actions-hub[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 32px;\\n}\\n\\n.action-category[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.category-title[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #2d3748;\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  text-align: center;\\n  padding-bottom: 12px;\\n  border-bottom: 2px solid #e2e8f0;\\n}\\n\\n.category-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n  border-radius: 12px;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: left;\\n  margin-left: 12px;\\n  font-weight: 500;\\n}\\n\\n.action-points[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #2d3748;\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.challenges-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.challenges-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n  gap: 24px;\\n}\\n\\n.challenge-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n}\\n\\n.challenge-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.challenge-header[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  text-align: center;\\n  color: white;\\n  position: relative;\\n}\\n\\n.challenge-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem !important;\\n  width: 3rem !important;\\n  height: 3rem !important;\\n  margin-bottom: 12px;\\n}\\n\\n.challenge-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n}\\n\\n.challenge-description[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #4a5568;\\n  line-height: 1.5;\\n}\\n\\n.challenge-progress[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 8px;\\n  background: #e2e8f0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  margin-bottom: 8px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  border-radius: 4px;\\n  transition: width 1s ease-in-out;\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 0.9rem;\\n  color: #4a5568;\\n  font-weight: 600;\\n}\\n\\n.challenge-reward[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 20px;\\n  padding: 12px;\\n  background: #f7fafc;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  color: #2d3748;\\n}\\n\\n\\n\\n.comparison-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.comparison-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.comparison-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 32px;\\n  padding: 0 20px;\\n}\\n\\n.city-score[_ngcontent-%COMP%] {\\n  text-align: center;\\n  flex: 1;\\n}\\n\\n.city-score.monastir[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n\\n.city-score.sousse[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #f093fb;\\n}\\n\\n.city-score[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n}\\n\\n.city-score[_ngcontent-%COMP%]   .score[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  font-weight: 800;\\n  margin-bottom: 8px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.city-score.sousse[_ngcontent-%COMP%]   .score[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.city-score[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n\\n.vs-divider[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 0 20px;\\n}\\n\\n.vs-divider[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem !important;\\n  width: 2rem !important;\\n  height: 2rem !important;\\n  color: #667eea;\\n}\\n\\n.vs-divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #2d3748;\\n  font-size: 1.2rem;\\n}\\n\\n.comparison-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n\\n.stat-comparison[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.stat-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2d3748;\\n  font-size: 0.9rem;\\n  text-align: center;\\n}\\n\\n.stat-bars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  align-items: center;\\n}\\n\\n.stat-bar[_ngcontent-%COMP%] {\\n  height: 24px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  min-width: 60px;\\n  transition: all 0.3s ease;\\n}\\n\\n.stat-bar.monastir[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n}\\n\\n.stat-bar.sousse[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .hero-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 24px;\\n    text-align: center;\\n    padding: 40px 24px;\\n  }\\n\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .hero-stats[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    gap: 20px;\\n  }\\n\\n  .impact-grid[_ngcontent-%COMP%], .partners-grid[_ngcontent-%COMP%], .actions-grid[_ngcontent-%COMP%], .challenges-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .events-scroll[_ngcontent-%COMP%] {\\n    padding-left: 16px;\\n  }\\n\\n  .comparison-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n  }\\n\\n  .vs-divider[_ngcontent-%COMP%] {\\n    order: -1;\\n  }\\n\\n  .stat-bars[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n\\n  .stat-bar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n    padding: 0 12px;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .hero-stats[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .action-btn[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n    text-align: center;\\n  }\\n\\n  .action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["UserRole", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r0", "nextReward", "imageUrl", "ɵɵsanitizeUrl", "title", "ɵɵtextInterpolate", "description", "getPointsToNextReward", "ɵɵstyleProp", "getProgressPercentage", "ɵɵtextInterpolate2", "user", "points", "pointsRequired", "getMotivationMessage", "ɵɵtemplate", "DashboardComponent_div_0_div_49_div_3_Template", "ɵɵclassProp", "reward_r2", "partner<PERSON>ame", "ɵɵtextInterpolate1", "DashboardComponent_div_0_div_42_Template", "DashboardComponent_div_0_div_49_Template", "name", "city", "availableRewards", "DashboardComponent", "constructor", "authService", "router", "dashboardRouter", "mockDataService", "filteredHistory", "monthlyStats", "pointsEarned", "pointsSpent", "scansCount", "validationsCount", "achievements", "notifications", "weeklyPointsData", "maxWeeklyPoints", "<PERSON><PERSON><PERSON><PERSON>", "showNotifications", "unreadNotifications", "ngOnInit", "currentUser$", "subscribe", "role", "USER", "navigateToUserDashboard", "loadStudentDashboardData", "loadAvailableRewards", "loadUserHistory", "loadMonthlyStats", "loadAchievements", "loadNotifications", "loadWeeklyPointsData", "setNextReward", "mockData", "getMockDataForRole", "id", "image", "category", "isActive", "history", "map", "h", "type", "timestamp", "location", "providerName", "apply<PERSON>istoryFilter", "currentMonth", "Date", "getMonth", "currentYear", "getFullYear", "monthlyActions", "filter", "actionDate", "a", "reduce", "sum", "Math", "abs", "includes", "length", "icon", "color", "earned", "progress", "min", "current", "target", "message", "now", "read", "n", "week", "max", "w", "affordableRewards", "r", "sort", "b", "pointsNeeded", "filterHistory", "startDate", "getTime", "getActionColor", "colorMap", "getActionIcon", "iconMap", "getNotificationColor", "getNotificationIcon", "openQRScanner", "console", "log", "navigate", "exchangeReward", "reward", "toggleNotifications", "mark<PERSON><PERSON><PERSON>", "notification", "openProfile", "logout", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "DashboardRouterService", "i4", "MockDataService", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { DashboardRouterService } from '../../../../core/services/dashboard-router.service';\nimport { MockDataService } from '../../../../core/services/mock-data.service';\nimport { User, UserRole } from '../../../../core/models';\n\n// Interfaces pour le dashboard élève\ninterface Reward {\n  id: string;\n  title: string;\n  description: string;\n  pointsRequired: number;\n  partnerName: string;\n  imageUrl: string;\n  image?: string; // Alias pour imageUrl\n  category: string;\n  isActive: boolean;\n}\n\ninterface HistoryAction {\n  id: string;\n  type: string;\n  description: string;\n  points: number;\n  timestamp: Date;\n  location?: string;\n  providerName?: string;\n}\n\ninterface MonthlyStats {\n  pointsEarned: number;\n  pointsSpent: number;\n  scansCount: number;\n  validationsCount: number;\n}\n\ninterface Achievement {\n  id: string;\n  title: string;\n  description: string;\n  icon: string;\n  color: string;\n  earned: boolean;\n  progress: number;\n  current: number;\n  target: number;\n}\n\ninterface Notification {\n  id: string;\n  type: string;\n  title: string;\n  message: string;\n  timestamp: Date;\n  read: boolean;\n}\n\ninterface CommunityActivity {\n  id: string;\n  description: string;\n  points: number;\n  type: string;\n  timestamp: Date;\n  userId: string;\n  userName: string;\n}\n\ninterface LocalEvent {\n  id: string;\n  title: string;\n  description: string;\n  date: Date;\n  location: string;\n  category: string;\n  points: number;\n  image: string;\n  participants: number;\n  maxParticipants: number;\n}\n\ninterface Partner {\n  id: string;\n  name: string;\n  category: string;\n  logo: string;\n  rating: number;\n  reviews: number;\n  currentOffer: string;\n  location: string;\n  qrCode: string;\n}\n\ninterface Challenge {\n  id: string;\n  title: string;\n  description: string;\n  icon: string;\n  gradient: string;\n  progress: number;\n  current: number;\n  target: number;\n  reward: string;\n  endDate: Date;\n}\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit {\n  user: User | null = null;\n\n  // Données pour le dashboard élève\n  availableRewards: Reward[] = [];\n  nextReward: Reward | null = null;\n  filteredHistory: HistoryAction[] = [];\n  monthlyStats: MonthlyStats = {\n    pointsEarned: 0,\n    pointsSpent: 0,\n    scansCount: 0,\n    validationsCount: 0\n  };\n  achievements: Achievement[] = [];\n  notifications: Notification[] = [];\n  weeklyPointsData: any[] = [];\n  maxWeeklyPoints = 100;\n\n  // État de l'interface\n  selectedPeriod = 'month';\n  showNotifications = false;\n  unreadNotifications = 0;\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private dashboardRouter: DashboardRouterService,\n    private mockDataService: MockDataService\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        // Rediriger vers le dashboard approprié selon le rôle\n        if (user.role !== UserRole.USER) {\n          this.dashboardRouter.navigateToUserDashboard(user);\n          return;\n        }\n        this.loadStudentDashboardData();\n      }\n    });\n  }\n\n  private loadStudentDashboardData(): void {\n    this.loadAvailableRewards();\n    this.loadUserHistory();\n    this.loadMonthlyStats();\n    this.loadAchievements();\n    this.loadNotifications();\n    this.loadWeeklyPointsData();\n    this.setNextReward();\n  }\n\n  private loadAvailableRewards(): void {\n    const mockData = this.mockDataService.getMockDataForRole(UserRole.USER);\n    this.availableRewards = mockData.availableRewards || [\n      {\n        id: 'reward1',\n        title: 'Café gratuit',\n        description: 'Un café offert au Café des Nattes',\n        pointsRequired: 50,\n        partnerName: 'Café des Nattes',\n        imageUrl: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',\n        image: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',\n        category: 'FOOD',\n        isActive: true\n      },\n      {\n        id: 'reward2',\n        title: '10% de réduction',\n        description: 'Réduction sur tous les produits artisanaux',\n        pointsRequired: 75,\n        partnerName: 'Boutique Artisanat',\n        imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',\n        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',\n        category: 'SHOPPING',\n        isActive: true\n      },\n      {\n        id: 'reward3',\n        title: 'Entrée gratuite',\n        description: 'Visite gratuite du musée de Sousse',\n        pointsRequired: 100,\n        partnerName: 'Musée de Sousse',\n        imageUrl: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300',\n        image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300',\n        category: 'CULTURE',\n        isActive: true\n      }\n    ];\n  }\n\n  private loadUserHistory(): void {\n    if (!this.user) return;\n\n    // Charger l'historique depuis les données utilisateur\n    this.filteredHistory = this.user.history?.map(h => ({\n      id: h.id,\n      type: h.type,\n      description: h.description,\n      points: h.points,\n      timestamp: h.timestamp,\n      location: 'Monastir', // Exemple\n      providerName: 'Prestataire local' // Exemple\n    })) || [];\n\n    this.applyHistoryFilter();\n  }\n\n  private loadMonthlyStats(): void {\n    if (!this.user) return;\n\n    // Calculer les statistiques du mois en cours\n    const currentMonth = new Date().getMonth();\n    const currentYear = new Date().getFullYear();\n\n    const monthlyActions = this.user.history?.filter(h => {\n      const actionDate = new Date(h.timestamp);\n      return actionDate.getMonth() === currentMonth && actionDate.getFullYear() === currentYear;\n    }) || [];\n\n    this.monthlyStats = {\n      pointsEarned: monthlyActions.filter(a => a.points > 0).reduce((sum, a) => sum + a.points, 0),\n      pointsSpent: Math.abs(monthlyActions.filter(a => a.points < 0).reduce((sum, a) => sum + a.points, 0)),\n      scansCount: monthlyActions.filter(a => a.description.includes('Scan QR')).length,\n      validationsCount: monthlyActions.filter(a => !a.description.includes('Scan QR')).length\n    };\n  }\n\n  private loadAchievements(): void {\n    this.achievements = [\n      {\n        id: 'first_scan',\n        title: 'Premier Scan',\n        description: 'Scanner votre premier QR Code',\n        icon: 'qr_code_scanner',\n        color: '#4CAF50',\n        earned: (this.user?.history?.length || 0) > 0,\n        progress: Math.min((this.user?.history?.length || 0) * 100, 100),\n        current: this.user?.history?.length || 0,\n        target: 1\n      },\n      {\n        id: 'point_collector',\n        title: 'Collecteur de Points',\n        description: 'Atteindre 100 points',\n        icon: 'stars',\n        color: '#FFD700',\n        earned: (this.user?.points || 0) >= 100,\n        progress: Math.min((this.user?.points || 0), 100),\n        current: this.user?.points || 0,\n        target: 100\n      },\n      {\n        id: 'community_helper',\n        title: 'Aide Communautaire',\n        description: 'Effectuer 5 bonnes actions',\n        icon: 'volunteer_activism',\n        color: '#E91E63',\n        earned: (this.user?.history?.length || 0) >= 5,\n        progress: Math.min((this.user?.history?.length || 0) * 20, 100),\n        current: this.user?.history?.length || 0,\n        target: 5\n      },\n      {\n        id: 'reward_redeemer',\n        title: 'Échangeur de Récompenses',\n        description: 'Échanger votre première récompense',\n        icon: 'card_giftcard',\n        color: '#FF9800',\n        earned: false, // À implémenter avec l'historique des échanges\n        progress: 0,\n        current: 0,\n        target: 1\n      }\n    ];\n  }\n\n  private loadNotifications(): void {\n    this.notifications = [\n      {\n        id: 'notif1',\n        type: 'points_earned',\n        title: 'Points gagnés !',\n        message: 'Vous avez gagné 15 points pour votre aide à la bibliothèque',\n        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 min ago\n        read: false\n      },\n      {\n        id: 'notif2',\n        type: 'new_reward',\n        title: 'Nouvelle récompense !',\n        message: 'Une nouvelle récompense est disponible au Café des Nattes',\n        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago\n        read: false\n      },\n      {\n        id: 'notif3',\n        type: 'achievement',\n        title: 'Achievement débloqué !',\n        message: 'Félicitations ! Vous avez débloqué le badge \"Premier Scan\"',\n        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago\n        read: true\n      }\n    ];\n\n    this.unreadNotifications = this.notifications.filter(n => !n.read).length;\n  }\n\n  private loadWeeklyPointsData(): void {\n    // Simuler les données de points par semaine pour le graphique\n    this.weeklyPointsData = [\n      { week: 1, points: 45 },\n      { week: 2, points: 62 },\n      { week: 3, points: 38 },\n      { week: 4, points: 75 }\n    ];\n\n    this.maxWeeklyPoints = Math.max(...this.weeklyPointsData.map(w => w.points));\n  }\n\n  private setNextReward(): void {\n    if (!this.user || this.availableRewards.length === 0) return;\n\n    // Trouver la prochaine récompense atteignable\n    const affordableRewards = this.availableRewards\n      .filter(r => r.pointsRequired > this.user!.points)\n      .sort((a, b) => a.pointsRequired - b.pointsRequired);\n\n    this.nextReward = affordableRewards[0] || this.availableRewards[0];\n  }\n\n  // Méthodes pour le dashboard élève\n\n  // Progression vers la prochaine récompense\n  getPointsToNextReward(): number {\n    if (!this.user || !this.nextReward) return 0;\n    return Math.max(0, this.nextReward.pointsRequired - this.user.points);\n  }\n\n  getProgressPercentage(): number {\n    if (!this.user || !this.nextReward) return 0;\n    return Math.min((this.user.points / this.nextReward.pointsRequired) * 100, 100);\n  }\n\n  getMotivationMessage(): string {\n    const pointsNeeded = this.getPointsToNextReward();\n    if (pointsNeeded === 0) {\n      return \"🎉 Félicitations ! Vous pouvez échanger cette récompense !\";\n    } else if (pointsNeeded <= 10) {\n      return `🔥 Plus que ${pointsNeeded} points ! Vous y êtes presque !`;\n    } else if (pointsNeeded <= 25) {\n      return `💪 Encore ${pointsNeeded} points et c'est à vous !`;\n    } else {\n      return `🎯 Objectif : ${pointsNeeded} points pour votre prochaine récompense !`;\n    }\n  }\n\n  // Filtrage de l'historique\n  filterHistory(): void {\n    this.applyHistoryFilter();\n  }\n\n  applyHistoryFilter(): void {\n    if (!this.user?.history) {\n      this.filteredHistory = [];\n      return;\n    }\n\n    const now = new Date();\n    let startDate: Date;\n\n    switch (this.selectedPeriod) {\n      case 'week':\n        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);\n        break;\n      case 'month':\n        startDate = new Date(now.getFullYear(), now.getMonth(), 1);\n        break;\n      case 'year':\n        startDate = new Date(now.getFullYear(), 0, 1);\n        break;\n      default:\n        this.filteredHistory = this.user.history.map(h => ({\n          id: h.id,\n          type: h.type,\n          description: h.description,\n          points: h.points,\n          timestamp: h.timestamp,\n          location: 'Monastir',\n          providerName: 'Prestataire local'\n        }));\n        return;\n    }\n\n    this.filteredHistory = this.user.history\n      .filter(h => new Date(h.timestamp) >= startDate)\n      .map(h => ({\n        id: h.id,\n        type: h.type,\n        description: h.description,\n        points: h.points,\n        timestamp: h.timestamp,\n        location: 'Monastir',\n        providerName: 'Prestataire local'\n      }));\n  }\n\n  // Méthodes helper pour l'interface\n  getActionColor(type: string): string {\n    const colorMap: { [key: string]: string } = {\n      'earned': '#4CAF50',\n      'spent': '#F44336',\n      'qr_scan': '#2196F3',\n      'validation': '#FF9800'\n    };\n    return colorMap[type] || '#667eea';\n  }\n\n  getActionIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'earned': 'trending_up',\n      'spent': 'trending_down',\n      'qr_scan': 'qr_code_scanner',\n      'validation': 'verified'\n    };\n    return iconMap[type] || 'circle';\n  }\n\n  getNotificationColor(type: string): string {\n    const colorMap: { [key: string]: string } = {\n      'points_earned': '#4CAF50',\n      'new_reward': '#FF9800',\n      'achievement': '#9C27B0',\n      'reminder': '#2196F3'\n    };\n    return colorMap[type] || '#667eea';\n  }\n\n  getNotificationIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'points_earned': 'stars',\n      'new_reward': 'card_giftcard',\n      'achievement': 'emoji_events',\n      'reminder': 'notifications'\n    };\n    return iconMap[type] || 'info';\n  }\n\n  // Méthodes d'action\n  openQRScanner(): void {\n    console.log('Ouverture du scanner QR...');\n    this.router.navigate(['/qr-scanner']);\n  }\n\n  exchangeReward(reward: Reward): void {\n    if (!this.user || this.user.points < reward.pointsRequired) {\n      console.log('Pas assez de points pour cette récompense');\n      return;\n    }\n\n    console.log('Échange de récompense:', reward);\n    // TODO: Implémenter l'échange de récompense\n    // - Déduire les points\n    // - Générer un code d'échange\n    // - Mettre à jour l'historique\n  }\n\n  toggleNotifications(): void {\n    this.showNotifications = !this.showNotifications;\n  }\n\n  markAsRead(notification: Notification): void {\n    notification.read = true;\n    this.unreadNotifications = this.notifications.filter(n => !n.read).length;\n  }\n\n  openProfile(): void {\n    this.router.navigate(['/profile']);\n  }\n\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/auth/login']);\n  }\n}\n", "<div class=\"dashboard-container\" *ngIf=\"user\">\n  <!-- En-tête -->\n  <div class=\"dashboard-header\">\n    <div class=\"user-welcome\">\n      <div class=\"user-avatar\">\n        <mat-icon>school</mat-icon>\n      </div>\n      <div class=\"user-info\">\n        <h2>Bonjour {{ user.name }} ! 👋</h2>\n        <p class=\"user-role\">Élève • {{ user.city }}</p>\n      </div>\n    </div>\n\n    <div class=\"header-actions\">\n      <div class=\"points-card\">\n        <mat-icon class=\"points-icon\">stars</mat-icon>\n        <div class=\"points-info\">\n          <span class=\"points-value\">{{ user.points }}</span>\n          <span class=\"points-label\">Points</span>\n        </div>\n      </div>\n\n      <button mat-icon-button class=\"notifications-btn\">\n        <mat-icon>notifications</mat-icon>\n      </button>\n    </div>\n  </div>\n\n  <!-- Scanner QR -->\n  <div class=\"qr-scan-section\">\n    <div class=\"qr-scan-card\">\n      <div class=\"qr-scan-content\">\n        <div class=\"qr-scan-icon\">\n          <mat-icon>qr_code_scanner</mat-icon>\n        </div>\n        <div class=\"qr-scan-text\">\n          <h3>Scanner un QR Code KnowMe</h3>\n          <p>Découvre un prestataire et gagne des points !</p>\n        </div>\n        <button mat-raised-button color=\"primary\" class=\"qr-scan-btn\">\n          <mat-icon>camera_alt</mat-icon>\n          Scanner\n        </button>\n      </div>\n    </div>\n  </div>\n\n  <!-- Progression -->\n  <div class=\"progress-section\">\n    <div class=\"progress-card\">\n      <h3>Progression vers ta prochaine récompense</h3>\n      <div class=\"reward-progress\" *ngIf=\"nextReward\">\n        <div class=\"progress-info\">\n          <div class=\"next-reward\">\n            <img [src]=\"nextReward.imageUrl\" [alt]=\"nextReward.title\" class=\"reward-icon\">\n            <div class=\"reward-details\">\n              <h4>{{ nextReward.title }}</h4>\n              <p>{{ nextReward.description }}</p>\n            </div>\n          </div>\n          <div class=\"points-needed\">\n            <span class=\"points-remaining\">{{ getPointsToNextReward() }}</span>\n            <span class=\"points-label\">points restants</span>\n          </div>\n        </div>\n        <div class=\"progress-bar\">\n          <div class=\"progress-fill\" [style.width]=\"getProgressPercentage() + '%'\"></div>\n        </div>\n        <div class=\"progress-text\">\n          <span>{{ user.points }} / {{ nextReward.pointsRequired }} points</span>\n        </div>\n        <div class=\"motivation-message\">\n          <p>{{ getMotivationMessage() }}</p>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Récompenses -->\n  <div class=\"rewards-section\">\n    <h3 class=\"section-subtitle\">\n      <mat-icon>card_giftcard</mat-icon>\n      Récompenses disponibles\n    </h3>\n    <div class=\"rewards-grid\">\n      <div *ngFor=\"let reward of availableRewards\" class=\"reward-card\"\n           [class.affordable]=\"user.points >= reward.pointsRequired\">\n        <div class=\"reward-image\">\n          <img [src]=\"reward.imageUrl\" [alt]=\"reward.title\">\n          <div class=\"reward-badge\" *ngIf=\"user.points >= reward.pointsRequired\">\n            <mat-icon>check_circle</mat-icon>\n          </div>\n        </div>\n        <div class=\"reward-content\">\n          <h4>{{ reward.title }}</h4>\n          <p class=\"reward-description\">{{ reward.description }}</p>\n          <div class=\"reward-partner\">\n            <mat-icon>store</mat-icon>\n            <span>{{ reward.partnerName }}</span>\n          </div>\n          <div class=\"reward-points\">\n            <mat-icon>stars</mat-icon>\n            <span>{{ reward.pointsRequired }} points</span>\n          </div>\n          <button mat-raised-button\n                  [color]=\"user.points >= reward.pointsRequired ? 'primary' : ''\"\n                  [disabled]=\"user.points < reward.pointsRequired\"\n                  class=\"exchange-btn\">\n            {{ user.points >= reward.pointsRequired ? 'Échanger' : 'Pas assez de points' }}\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n</div>\n\n\n<!-- Floating QR Scanner Button -->\n<button mat-fab class=\"floating-qr-btn\" color=\"primary\">\n  <mat-icon>qr_code_scanner</mat-icon>\n</button>\n"], "mappings": "AAKA,SAAeA,QAAQ,QAAQ,yBAAyB;;;;;;;;;;;ICgD9CC,EAFJ,CAAAC,cAAA,cAAgD,cACnB,cACA;IACvBD,EAAA,CAAAE,SAAA,cAA8E;IAE5EF,EADF,CAAAC,cAAA,cAA4B,SACtB;IAAAD,EAAA,CAAAG,MAAA,GAAsB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC/BJ,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IAEnCH,EAFmC,CAAAI,YAAA,EAAI,EAC/B,EACF;IAEJJ,EADF,CAAAC,cAAA,cAA2B,gBACM;IAAAD,EAAA,CAAAG,MAAA,IAA6B;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACnEJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAE9CH,EAF8C,CAAAI,YAAA,EAAO,EAC7C,EACF;IACNJ,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAE,SAAA,eAA+E;IACjFF,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,eAA2B,YACnB;IAAAD,EAAA,CAAAG,MAAA,IAA0D;IAClEH,EADkE,CAAAI,YAAA,EAAO,EACnE;IAEJJ,EADF,CAAAC,cAAA,eAAgC,SAC3B;IAAAD,EAAA,CAAAG,MAAA,IAA4B;IAEnCH,EAFmC,CAAAI,YAAA,EAAI,EAC/B,EACF;;;;IApBKJ,EAAA,CAAAK,SAAA,GAA2B;IAACL,EAA5B,CAAAM,UAAA,QAAAC,MAAA,CAAAC,UAAA,CAAAC,QAAA,EAAAT,EAAA,CAAAU,aAAA,CAA2B,QAAAH,MAAA,CAAAC,UAAA,CAAAG,KAAA,CAAyB;IAEnDX,EAAA,CAAAK,SAAA,GAAsB;IAAtBL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAC,UAAA,CAAAG,KAAA,CAAsB;IACvBX,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAC,UAAA,CAAAK,WAAA,CAA4B;IAIFb,EAAA,CAAAK,SAAA,GAA6B;IAA7BL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAO,qBAAA,GAA6B;IAKnCd,EAAA,CAAAK,SAAA,GAA6C;IAA7CL,EAAA,CAAAe,WAAA,UAAAR,MAAA,CAAAS,qBAAA,SAA6C;IAGlEhB,EAAA,CAAAK,SAAA,GAA0D;IAA1DL,EAAA,CAAAiB,kBAAA,KAAAV,MAAA,CAAAW,IAAA,CAAAC,MAAA,SAAAZ,MAAA,CAAAC,UAAA,CAAAY,cAAA,YAA0D;IAG7DpB,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAc,oBAAA,GAA4B;;;;;IAkB7BrB,EADF,CAAAC,cAAA,cAAuE,eAC3D;IAAAD,EAAA,CAAAG,MAAA,mBAAY;IACxBH,EADwB,CAAAI,YAAA,EAAW,EAC7B;;;;;IAJRJ,EAFF,CAAAC,cAAA,cAC+D,cACnC;IACxBD,EAAA,CAAAE,SAAA,cAAkD;IAClDF,EAAA,CAAAsB,UAAA,IAAAC,8CAAA,kBAAuE;IAGzEvB,EAAA,CAAAI,YAAA,EAAM;IAEJJ,EADF,CAAAC,cAAA,cAA4B,SACtB;IAAAD,EAAA,CAAAG,MAAA,GAAkB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAC3BJ,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAG,MAAA,GAAwB;IAAAH,EAAA,CAAAI,YAAA,EAAI;IAExDJ,EADF,CAAAC,cAAA,cAA4B,gBAChB;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAwB;IAChCH,EADgC,CAAAI,YAAA,EAAO,EACjC;IAEJJ,EADF,CAAAC,cAAA,eAA2B,gBACf;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAG,MAAA,IAAkC;IAC1CH,EAD0C,CAAAI,YAAA,EAAO,EAC3C;IACNJ,EAAA,CAAAC,cAAA,kBAG6B;IAC3BD,EAAA,CAAAG,MAAA,IACF;IAEJH,EAFI,CAAAI,YAAA,EAAS,EACL,EACF;;;;;IAzBDJ,EAAA,CAAAwB,WAAA,eAAAjB,MAAA,CAAAW,IAAA,CAAAC,MAAA,IAAAM,SAAA,CAAAL,cAAA,CAAyD;IAErDpB,EAAA,CAAAK,SAAA,GAAuB;IAACL,EAAxB,CAAAM,UAAA,QAAAmB,SAAA,CAAAhB,QAAA,EAAAT,EAAA,CAAAU,aAAA,CAAuB,QAAAe,SAAA,CAAAd,KAAA,CAAqB;IACtBX,EAAA,CAAAK,SAAA,EAA0C;IAA1CL,EAAA,CAAAM,UAAA,SAAAC,MAAA,CAAAW,IAAA,CAAAC,MAAA,IAAAM,SAAA,CAAAL,cAAA,CAA0C;IAKjEpB,EAAA,CAAAK,SAAA,GAAkB;IAAlBL,EAAA,CAAAY,iBAAA,CAAAa,SAAA,CAAAd,KAAA,CAAkB;IACQX,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAY,iBAAA,CAAAa,SAAA,CAAAZ,WAAA,CAAwB;IAG9Cb,EAAA,CAAAK,SAAA,GAAwB;IAAxBL,EAAA,CAAAY,iBAAA,CAAAa,SAAA,CAAAC,WAAA,CAAwB;IAIxB1B,EAAA,CAAAK,SAAA,GAAkC;IAAlCL,EAAA,CAAA2B,kBAAA,KAAAF,SAAA,CAAAL,cAAA,YAAkC;IAGlCpB,EAAA,CAAAK,SAAA,EAA+D;IAC/DL,EADA,CAAAM,UAAA,UAAAC,MAAA,CAAAW,IAAA,CAAAC,MAAA,IAAAM,SAAA,CAAAL,cAAA,kBAA+D,aAAAb,MAAA,CAAAW,IAAA,CAAAC,MAAA,GAAAM,SAAA,CAAAL,cAAA,CACf;IAEtDpB,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAA2B,kBAAA,MAAApB,MAAA,CAAAW,IAAA,CAAAC,MAAA,IAAAM,SAAA,CAAAL,cAAA,gDACF;;;;;IAxGFpB,EALR,CAAAC,cAAA,aAA8C,aAEd,aACF,aACC,eACb;IAAAD,EAAA,CAAAG,MAAA,aAAM;IAClBH,EADkB,CAAAI,YAAA,EAAW,EACvB;IAEJJ,EADF,CAAAC,cAAA,aAAuB,SACjB;IAAAD,EAAA,CAAAG,MAAA,GAA4B;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACrCJ,EAAA,CAAAC,cAAA,WAAqB;IAAAD,EAAA,CAAAG,MAAA,IAAuB;IAEhDH,EAFgD,CAAAI,YAAA,EAAI,EAC5C,EACF;IAIFJ,EAFJ,CAAAC,cAAA,cAA4B,cACD,oBACO;IAAAD,EAAA,CAAAG,MAAA,aAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAE5CJ,EADF,CAAAC,cAAA,eAAyB,gBACI;IAAAD,EAAA,CAAAG,MAAA,IAAiB;IAAAH,EAAA,CAAAI,YAAA,EAAO;IACnDJ,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAG,MAAA,cAAM;IAErCH,EAFqC,CAAAI,YAAA,EAAO,EACpC,EACF;IAGJJ,EADF,CAAAC,cAAA,kBAAkD,gBACtC;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAG7BH,EAH6B,CAAAI,YAAA,EAAW,EAC3B,EACL,EACF;IAOEJ,EAJR,CAAAC,cAAA,eAA6B,eACD,eACK,eACD,gBACd;IAAAD,EAAA,CAAAG,MAAA,uBAAe;IAC3BH,EAD2B,CAAAI,YAAA,EAAW,EAChC;IAEJJ,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAG,MAAA,iCAAyB;IAAAH,EAAA,CAAAI,YAAA,EAAK;IAClCJ,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAG,MAAA,0DAA6C;IAClDH,EADkD,CAAAI,YAAA,EAAI,EAChD;IAEJJ,EADF,CAAAC,cAAA,kBAA8D,gBAClD;IAAAD,EAAA,CAAAG,MAAA,kBAAU;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC/BJ,EAAA,CAAAG,MAAA,iBACF;IAGNH,EAHM,CAAAI,YAAA,EAAS,EACL,EACF,EACF;IAKFJ,EAFJ,CAAAC,cAAA,eAA8B,eACD,UACrB;IAAAD,EAAA,CAAAG,MAAA,qDAAwC;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACjDJ,EAAA,CAAAsB,UAAA,KAAAM,wCAAA,oBAAgD;IAyBpD5B,EADE,CAAAI,YAAA,EAAM,EACF;IAKFJ,EAFJ,CAAAC,cAAA,eAA6B,cACE,gBACjB;IAAAD,EAAA,CAAAG,MAAA,qBAAa;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAClCJ,EAAA,CAAAG,MAAA,sCACF;IAAAH,EAAA,CAAAI,YAAA,EAAK;IACLJ,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAsB,UAAA,KAAAO,wCAAA,oBAC+D;IA4BrE7B,EAFI,CAAAI,YAAA,EAAM,EACF,EACF;;;;IA1GMJ,EAAA,CAAAK,SAAA,GAA4B;IAA5BL,EAAA,CAAA2B,kBAAA,aAAApB,MAAA,CAAAW,IAAA,CAAAY,IAAA,oBAA4B;IACX9B,EAAA,CAAAK,SAAA,GAAuB;IAAvBL,EAAA,CAAA2B,kBAAA,4BAAApB,MAAA,CAAAW,IAAA,CAAAa,IAAA,KAAuB;IAQf/B,EAAA,CAAAK,SAAA,GAAiB;IAAjBL,EAAA,CAAAY,iBAAA,CAAAL,MAAA,CAAAW,IAAA,CAAAC,MAAA,CAAiB;IAkClBnB,EAAA,CAAAK,SAAA,IAAgB;IAAhBL,EAAA,CAAAM,UAAA,SAAAC,MAAA,CAAAC,UAAA,CAAgB;IAkCtBR,EAAA,CAAAK,SAAA,GAAmB;IAAnBL,EAAA,CAAAM,UAAA,YAAAC,MAAA,CAAAyB,gBAAA,CAAmB;;;AD0BjD,OAAM,MAAOC,kBAAkB;EAuB7BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,eAAuC,EACvCC,eAAgC;IAHhC,KAAAH,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,eAAe,GAAfA,eAAe;IA1BzB,KAAApB,IAAI,GAAgB,IAAI;IAExB;IACA,KAAAc,gBAAgB,GAAa,EAAE;IAC/B,KAAAxB,UAAU,GAAkB,IAAI;IAChC,KAAA+B,eAAe,GAAoB,EAAE;IACrC,KAAAC,YAAY,GAAiB;MAC3BC,YAAY,EAAE,CAAC;MACfC,WAAW,EAAE,CAAC;MACdC,UAAU,EAAE,CAAC;MACbC,gBAAgB,EAAE;KACnB;IACD,KAAAC,YAAY,GAAkB,EAAE;IAChC,KAAAC,aAAa,GAAmB,EAAE;IAClC,KAAAC,gBAAgB,GAAU,EAAE;IAC5B,KAAAC,eAAe,GAAG,GAAG;IAErB;IACA,KAAAC,cAAc,GAAG,OAAO;IACxB,KAAAC,iBAAiB,GAAG,KAAK;IACzB,KAAAC,mBAAmB,GAAG,CAAC;EAOpB;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACjB,WAAW,CAACkB,YAAY,CAACC,SAAS,CAACpC,IAAI,IAAG;MAC7C,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAIA,IAAI,EAAE;QACR;QACA,IAAIA,IAAI,CAACqC,IAAI,KAAKxD,QAAQ,CAACyD,IAAI,EAAE;UAC/B,IAAI,CAACnB,eAAe,CAACoB,uBAAuB,CAACvC,IAAI,CAAC;UAClD;;QAEF,IAAI,CAACwC,wBAAwB,EAAE;;IAEnC,CAAC,CAAC;EACJ;EAEQA,wBAAwBA,CAAA;IAC9B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,gBAAgB,EAAE;IACvB,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,aAAa,EAAE;EACtB;EAEQN,oBAAoBA,CAAA;IAC1B,MAAMO,QAAQ,GAAG,IAAI,CAAC5B,eAAe,CAAC6B,kBAAkB,CAACpE,QAAQ,CAACyD,IAAI,CAAC;IACvE,IAAI,CAACxB,gBAAgB,GAAGkC,QAAQ,CAAClC,gBAAgB,IAAI,CACnD;MACEoC,EAAE,EAAE,SAAS;MACbzD,KAAK,EAAE,cAAc;MACrBE,WAAW,EAAE,mCAAmC;MAChDO,cAAc,EAAE,EAAE;MAClBM,WAAW,EAAE,iBAAiB;MAC9BjB,QAAQ,EAAE,oEAAoE;MAC9E4D,KAAK,EAAE,oEAAoE;MAC3EC,QAAQ,EAAE,MAAM;MAChBC,QAAQ,EAAE;KACX,EACD;MACEH,EAAE,EAAE,SAAS;MACbzD,KAAK,EAAE,kBAAkB;MACzBE,WAAW,EAAE,4CAA4C;MACzDO,cAAc,EAAE,EAAE;MAClBM,WAAW,EAAE,oBAAoB;MACjCjB,QAAQ,EAAE,oEAAoE;MAC9E4D,KAAK,EAAE,oEAAoE;MAC3EC,QAAQ,EAAE,UAAU;MACpBC,QAAQ,EAAE;KACX,EACD;MACEH,EAAE,EAAE,SAAS;MACbzD,KAAK,EAAE,iBAAiB;MACxBE,WAAW,EAAE,oCAAoC;MACjDO,cAAc,EAAE,GAAG;MACnBM,WAAW,EAAE,iBAAiB;MAC9BjB,QAAQ,EAAE,oEAAoE;MAC9E4D,KAAK,EAAE,oEAAoE;MAC3EC,QAAQ,EAAE,SAAS;MACnBC,QAAQ,EAAE;KACX,CACF;EACH;EAEQX,eAAeA,CAAA;IACrB,IAAI,CAAC,IAAI,CAAC1C,IAAI,EAAE;IAEhB;IACA,IAAI,CAACqB,eAAe,GAAG,IAAI,CAACrB,IAAI,CAACsD,OAAO,EAAEC,GAAG,CAACC,CAAC,KAAK;MAClDN,EAAE,EAAEM,CAAC,CAACN,EAAE;MACRO,IAAI,EAAED,CAAC,CAACC,IAAI;MACZ9D,WAAW,EAAE6D,CAAC,CAAC7D,WAAW;MAC1BM,MAAM,EAAEuD,CAAC,CAACvD,MAAM;MAChByD,SAAS,EAAEF,CAAC,CAACE,SAAS;MACtBC,QAAQ,EAAE,UAAU;MACpBC,YAAY,EAAE,mBAAmB,CAAC;KACnC,CAAC,CAAC,IAAI,EAAE;IAET,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQlB,gBAAgBA,CAAA;IACtB,IAAI,CAAC,IAAI,CAAC3C,IAAI,EAAE;IAEhB;IACA,MAAM8D,YAAY,GAAG,IAAIC,IAAI,EAAE,CAACC,QAAQ,EAAE;IAC1C,MAAMC,WAAW,GAAG,IAAIF,IAAI,EAAE,CAACG,WAAW,EAAE;IAE5C,MAAMC,cAAc,GAAG,IAAI,CAACnE,IAAI,CAACsD,OAAO,EAAEc,MAAM,CAACZ,CAAC,IAAG;MACnD,MAAMa,UAAU,GAAG,IAAIN,IAAI,CAACP,CAAC,CAACE,SAAS,CAAC;MACxC,OAAOW,UAAU,CAACL,QAAQ,EAAE,KAAKF,YAAY,IAAIO,UAAU,CAACH,WAAW,EAAE,KAAKD,WAAW;IAC3F,CAAC,CAAC,IAAI,EAAE;IAER,IAAI,CAAC3C,YAAY,GAAG;MAClBC,YAAY,EAAE4C,cAAc,CAACC,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACrE,MAAM,GAAG,CAAC,CAAC,CAACsE,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGF,CAAC,CAACrE,MAAM,EAAE,CAAC,CAAC;MAC5FuB,WAAW,EAAEiD,IAAI,CAACC,GAAG,CAACP,cAAc,CAACC,MAAM,CAACE,CAAC,IAAIA,CAAC,CAACrE,MAAM,GAAG,CAAC,CAAC,CAACsE,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGF,CAAC,CAACrE,MAAM,EAAE,CAAC,CAAC,CAAC;MACrGwB,UAAU,EAAE0C,cAAc,CAACC,MAAM,CAACE,CAAC,IAAIA,CAAC,CAAC3E,WAAW,CAACgF,QAAQ,CAAC,SAAS,CAAC,CAAC,CAACC,MAAM;MAChFlD,gBAAgB,EAAEyC,cAAc,CAACC,MAAM,CAACE,CAAC,IAAI,CAACA,CAAC,CAAC3E,WAAW,CAACgF,QAAQ,CAAC,SAAS,CAAC,CAAC,CAACC;KAClF;EACH;EAEQhC,gBAAgBA,CAAA;IACtB,IAAI,CAACjB,YAAY,GAAG,CAClB;MACEuB,EAAE,EAAE,YAAY;MAChBzD,KAAK,EAAE,cAAc;MACrBE,WAAW,EAAE,+BAA+B;MAC5CkF,IAAI,EAAE,iBAAiB;MACvBC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,CAAC,IAAI,CAAC/E,IAAI,EAAEsD,OAAO,EAAEsB,MAAM,IAAI,CAAC,IAAI,CAAC;MAC7CI,QAAQ,EAAEP,IAAI,CAACQ,GAAG,CAAC,CAAC,IAAI,CAACjF,IAAI,EAAEsD,OAAO,EAAEsB,MAAM,IAAI,CAAC,IAAI,GAAG,EAAE,GAAG,CAAC;MAChEM,OAAO,EAAE,IAAI,CAAClF,IAAI,EAAEsD,OAAO,EAAEsB,MAAM,IAAI,CAAC;MACxCO,MAAM,EAAE;KACT,EACD;MACEjC,EAAE,EAAE,iBAAiB;MACrBzD,KAAK,EAAE,sBAAsB;MAC7BE,WAAW,EAAE,sBAAsB;MACnCkF,IAAI,EAAE,OAAO;MACbC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,CAAC,IAAI,CAAC/E,IAAI,EAAEC,MAAM,IAAI,CAAC,KAAK,GAAG;MACvC+E,QAAQ,EAAEP,IAAI,CAACQ,GAAG,CAAE,IAAI,CAACjF,IAAI,EAAEC,MAAM,IAAI,CAAC,EAAG,GAAG,CAAC;MACjDiF,OAAO,EAAE,IAAI,CAAClF,IAAI,EAAEC,MAAM,IAAI,CAAC;MAC/BkF,MAAM,EAAE;KACT,EACD;MACEjC,EAAE,EAAE,kBAAkB;MACtBzD,KAAK,EAAE,oBAAoB;MAC3BE,WAAW,EAAE,4BAA4B;MACzCkF,IAAI,EAAE,oBAAoB;MAC1BC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,CAAC,IAAI,CAAC/E,IAAI,EAAEsD,OAAO,EAAEsB,MAAM,IAAI,CAAC,KAAK,CAAC;MAC9CI,QAAQ,EAAEP,IAAI,CAACQ,GAAG,CAAC,CAAC,IAAI,CAACjF,IAAI,EAAEsD,OAAO,EAAEsB,MAAM,IAAI,CAAC,IAAI,EAAE,EAAE,GAAG,CAAC;MAC/DM,OAAO,EAAE,IAAI,CAAClF,IAAI,EAAEsD,OAAO,EAAEsB,MAAM,IAAI,CAAC;MACxCO,MAAM,EAAE;KACT,EACD;MACEjC,EAAE,EAAE,iBAAiB;MACrBzD,KAAK,EAAE,0BAA0B;MACjCE,WAAW,EAAE,oCAAoC;MACjDkF,IAAI,EAAE,eAAe;MACrBC,KAAK,EAAE,SAAS;MAChBC,MAAM,EAAE,KAAK;MACbC,QAAQ,EAAE,CAAC;MACXE,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE;KACT,CACF;EACH;EAEQtC,iBAAiBA,CAAA;IACvB,IAAI,CAACjB,aAAa,GAAG,CACnB;MACEsB,EAAE,EAAE,QAAQ;MACZO,IAAI,EAAE,eAAe;MACrBhE,KAAK,EAAE,iBAAiB;MACxB2F,OAAO,EAAE,6DAA6D;MACtE1B,SAAS,EAAE,IAAIK,IAAI,CAACA,IAAI,CAACsB,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDC,IAAI,EAAE;KACP,EACD;MACEpC,EAAE,EAAE,QAAQ;MACZO,IAAI,EAAE,YAAY;MAClBhE,KAAK,EAAE,uBAAuB;MAC9B2F,OAAO,EAAE,2DAA2D;MACpE1B,SAAS,EAAE,IAAIK,IAAI,CAACA,IAAI,CAACsB,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACpDC,IAAI,EAAE;KACP,EACD;MACEpC,EAAE,EAAE,QAAQ;MACZO,IAAI,EAAE,aAAa;MACnBhE,KAAK,EAAE,wBAAwB;MAC/B2F,OAAO,EAAE,4DAA4D;MACrE1B,SAAS,EAAE,IAAIK,IAAI,CAACA,IAAI,CAACsB,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;MACrDC,IAAI,EAAE;KACP,CACF;IAED,IAAI,CAACrD,mBAAmB,GAAG,IAAI,CAACL,aAAa,CAACwC,MAAM,CAACmB,CAAC,IAAI,CAACA,CAAC,CAACD,IAAI,CAAC,CAACV,MAAM;EAC3E;EAEQ9B,oBAAoBA,CAAA;IAC1B;IACA,IAAI,CAACjB,gBAAgB,GAAG,CACtB;MAAE2D,IAAI,EAAE,CAAC;MAAEvF,MAAM,EAAE;IAAE,CAAE,EACvB;MAAEuF,IAAI,EAAE,CAAC;MAAEvF,MAAM,EAAE;IAAE,CAAE,EACvB;MAAEuF,IAAI,EAAE,CAAC;MAAEvF,MAAM,EAAE;IAAE,CAAE,EACvB;MAAEuF,IAAI,EAAE,CAAC;MAAEvF,MAAM,EAAE;IAAE,CAAE,CACxB;IAED,IAAI,CAAC6B,eAAe,GAAG2C,IAAI,CAACgB,GAAG,CAAC,GAAG,IAAI,CAAC5D,gBAAgB,CAAC0B,GAAG,CAACmC,CAAC,IAAIA,CAAC,CAACzF,MAAM,CAAC,CAAC;EAC9E;EAEQ8C,aAAaA,CAAA;IACnB,IAAI,CAAC,IAAI,CAAC/C,IAAI,IAAI,IAAI,CAACc,gBAAgB,CAAC8D,MAAM,KAAK,CAAC,EAAE;IAEtD;IACA,MAAMe,iBAAiB,GAAG,IAAI,CAAC7E,gBAAgB,CAC5CsD,MAAM,CAACwB,CAAC,IAAIA,CAAC,CAAC1F,cAAc,GAAG,IAAI,CAACF,IAAK,CAACC,MAAM,CAAC,CACjD4F,IAAI,CAAC,CAACvB,CAAC,EAAEwB,CAAC,KAAKxB,CAAC,CAACpE,cAAc,GAAG4F,CAAC,CAAC5F,cAAc,CAAC;IAEtD,IAAI,CAACZ,UAAU,GAAGqG,iBAAiB,CAAC,CAAC,CAAC,IAAI,IAAI,CAAC7E,gBAAgB,CAAC,CAAC,CAAC;EACpE;EAEA;EAEA;EACAlB,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACI,IAAI,IAAI,CAAC,IAAI,CAACV,UAAU,EAAE,OAAO,CAAC;IAC5C,OAAOmF,IAAI,CAACgB,GAAG,CAAC,CAAC,EAAE,IAAI,CAACnG,UAAU,CAACY,cAAc,GAAG,IAAI,CAACF,IAAI,CAACC,MAAM,CAAC;EACvE;EAEAH,qBAAqBA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACE,IAAI,IAAI,CAAC,IAAI,CAACV,UAAU,EAAE,OAAO,CAAC;IAC5C,OAAOmF,IAAI,CAACQ,GAAG,CAAE,IAAI,CAACjF,IAAI,CAACC,MAAM,GAAG,IAAI,CAACX,UAAU,CAACY,cAAc,GAAI,GAAG,EAAE,GAAG,CAAC;EACjF;EAEAC,oBAAoBA,CAAA;IAClB,MAAM4F,YAAY,GAAG,IAAI,CAACnG,qBAAqB,EAAE;IACjD,IAAImG,YAAY,KAAK,CAAC,EAAE;MACtB,OAAO,4DAA4D;KACpE,MAAM,IAAIA,YAAY,IAAI,EAAE,EAAE;MAC7B,OAAO,eAAeA,YAAY,iCAAiC;KACpE,MAAM,IAAIA,YAAY,IAAI,EAAE,EAAE;MAC7B,OAAO,aAAaA,YAAY,2BAA2B;KAC5D,MAAM;MACL,OAAO,iBAAiBA,YAAY,2CAA2C;;EAEnF;EAEA;EACAC,aAAaA,CAAA;IACX,IAAI,CAACnC,kBAAkB,EAAE;EAC3B;EAEAA,kBAAkBA,CAAA;IAChB,IAAI,CAAC,IAAI,CAAC7D,IAAI,EAAEsD,OAAO,EAAE;MACvB,IAAI,CAACjC,eAAe,GAAG,EAAE;MACzB;;IAGF,MAAMgE,GAAG,GAAG,IAAItB,IAAI,EAAE;IACtB,IAAIkC,SAAe;IAEnB,QAAQ,IAAI,CAAClE,cAAc;MACzB,KAAK,MAAM;QACTkE,SAAS,GAAG,IAAIlC,IAAI,CAACsB,GAAG,CAACa,OAAO,EAAE,GAAG,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC;QAC7D;MACF,KAAK,OAAO;QACVD,SAAS,GAAG,IAAIlC,IAAI,CAACsB,GAAG,CAACnB,WAAW,EAAE,EAAEmB,GAAG,CAACrB,QAAQ,EAAE,EAAE,CAAC,CAAC;QAC1D;MACF,KAAK,MAAM;QACTiC,SAAS,GAAG,IAAIlC,IAAI,CAACsB,GAAG,CAACnB,WAAW,EAAE,EAAE,CAAC,EAAE,CAAC,CAAC;QAC7C;MACF;QACE,IAAI,CAAC7C,eAAe,GAAG,IAAI,CAACrB,IAAI,CAACsD,OAAO,CAACC,GAAG,CAACC,CAAC,KAAK;UACjDN,EAAE,EAAEM,CAAC,CAACN,EAAE;UACRO,IAAI,EAAED,CAAC,CAACC,IAAI;UACZ9D,WAAW,EAAE6D,CAAC,CAAC7D,WAAW;UAC1BM,MAAM,EAAEuD,CAAC,CAACvD,MAAM;UAChByD,SAAS,EAAEF,CAAC,CAACE,SAAS;UACtBC,QAAQ,EAAE,UAAU;UACpBC,YAAY,EAAE;SACf,CAAC,CAAC;QACH;;IAGJ,IAAI,CAACvC,eAAe,GAAG,IAAI,CAACrB,IAAI,CAACsD,OAAO,CACrCc,MAAM,CAACZ,CAAC,IAAI,IAAIO,IAAI,CAACP,CAAC,CAACE,SAAS,CAAC,IAAIuC,SAAS,CAAC,CAC/C1C,GAAG,CAACC,CAAC,KAAK;MACTN,EAAE,EAAEM,CAAC,CAACN,EAAE;MACRO,IAAI,EAAED,CAAC,CAACC,IAAI;MACZ9D,WAAW,EAAE6D,CAAC,CAAC7D,WAAW;MAC1BM,MAAM,EAAEuD,CAAC,CAACvD,MAAM;MAChByD,SAAS,EAAEF,CAAC,CAACE,SAAS;MACtBC,QAAQ,EAAE,UAAU;MACpBC,YAAY,EAAE;KACf,CAAC,CAAC;EACP;EAEA;EACAuC,cAAcA,CAAC1C,IAAY;IACzB,MAAM2C,QAAQ,GAA8B;MAC1C,QAAQ,EAAE,SAAS;MACnB,OAAO,EAAE,SAAS;MAClB,SAAS,EAAE,SAAS;MACpB,YAAY,EAAE;KACf;IACD,OAAOA,QAAQ,CAAC3C,IAAI,CAAC,IAAI,SAAS;EACpC;EAEA4C,aAAaA,CAAC5C,IAAY;IACxB,MAAM6C,OAAO,GAA8B;MACzC,QAAQ,EAAE,aAAa;MACvB,OAAO,EAAE,eAAe;MACxB,SAAS,EAAE,iBAAiB;MAC5B,YAAY,EAAE;KACf;IACD,OAAOA,OAAO,CAAC7C,IAAI,CAAC,IAAI,QAAQ;EAClC;EAEA8C,oBAAoBA,CAAC9C,IAAY;IAC/B,MAAM2C,QAAQ,GAA8B;MAC1C,eAAe,EAAE,SAAS;MAC1B,YAAY,EAAE,SAAS;MACvB,aAAa,EAAE,SAAS;MACxB,UAAU,EAAE;KACb;IACD,OAAOA,QAAQ,CAAC3C,IAAI,CAAC,IAAI,SAAS;EACpC;EAEA+C,mBAAmBA,CAAC/C,IAAY;IAC9B,MAAM6C,OAAO,GAA8B;MACzC,eAAe,EAAE,OAAO;MACxB,YAAY,EAAE,eAAe;MAC7B,aAAa,EAAE,cAAc;MAC7B,UAAU,EAAE;KACb;IACD,OAAOA,OAAO,CAAC7C,IAAI,CAAC,IAAI,MAAM;EAChC;EAEA;EACAgD,aAAaA,CAAA;IACXC,OAAO,CAACC,GAAG,CAAC,4BAA4B,CAAC;IACzC,IAAI,CAACzF,MAAM,CAAC0F,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;EAEAC,cAAcA,CAACC,MAAc;IAC3B,IAAI,CAAC,IAAI,CAAC9G,IAAI,IAAI,IAAI,CAACA,IAAI,CAACC,MAAM,GAAG6G,MAAM,CAAC5G,cAAc,EAAE;MAC1DwG,OAAO,CAACC,GAAG,CAAC,2CAA2C,CAAC;MACxD;;IAGFD,OAAO,CAACC,GAAG,CAAC,wBAAwB,EAAEG,MAAM,CAAC;IAC7C;IACA;IACA;IACA;EACF;EAEAC,mBAAmBA,CAAA;IACjB,IAAI,CAAC/E,iBAAiB,GAAG,CAAC,IAAI,CAACA,iBAAiB;EAClD;EAEAgF,UAAUA,CAACC,YAA0B;IACnCA,YAAY,CAAC3B,IAAI,GAAG,IAAI;IACxB,IAAI,CAACrD,mBAAmB,GAAG,IAAI,CAACL,aAAa,CAACwC,MAAM,CAACmB,CAAC,IAAI,CAACA,CAAC,CAACD,IAAI,CAAC,CAACV,MAAM;EAC3E;EAEAsC,WAAWA,CAAA;IACT,IAAI,CAAChG,MAAM,CAAC0F,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEAO,MAAMA,CAAA;IACJ,IAAI,CAAClG,WAAW,CAACkG,MAAM,EAAE;IACzB,IAAI,CAACjG,MAAM,CAAC0F,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;;;uBAjYW7F,kBAAkB,EAAAjC,EAAA,CAAAsI,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAxI,EAAA,CAAAsI,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAA1I,EAAA,CAAAsI,iBAAA,CAAAK,EAAA,CAAAC,sBAAA,GAAA5I,EAAA,CAAAsI,iBAAA,CAAAO,EAAA,CAAAC,eAAA;IAAA;EAAA;;;YAAlB7G,kBAAkB;MAAA8G,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC/G/BrJ,EAAA,CAAAsB,UAAA,IAAAiI,iCAAA,kBAA8C;UAuH5CvJ,EADF,CAAAC,cAAA,gBAAwD,eAC5C;UAAAD,EAAA,CAAAG,MAAA,sBAAe;UAC3BH,EAD2B,CAAAI,YAAA,EAAW,EAC7B;;;UAxHyBJ,EAAA,CAAAM,UAAA,SAAAgJ,GAAA,CAAApI,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}