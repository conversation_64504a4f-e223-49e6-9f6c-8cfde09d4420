{"ast": null, "code": "export var ConfigCategory;\n(function (ConfigCategory) {\n  ConfigCategory[\"POINTS\"] = \"points\";\n  ConfigCategory[\"VALIDATION\"] = \"validation\";\n  ConfigCategory[\"REWARDS\"] = \"rewards\";\n  ConfigCategory[\"SECURITY\"] = \"security\";\n  ConfigCategory[\"NOTIFICATIONS\"] = \"notifications\";\n  ConfigCategory[\"GENERAL\"] = \"general\";\n})(ConfigCategory || (ConfigCategory = {}));\nexport var AuditAction;\n(function (AuditAction) {\n  AuditAction[\"CREATE\"] = \"create\";\n  AuditAction[\"UPDATE\"] = \"update\";\n  AuditAction[\"DELETE\"] = \"delete\";\n  AuditAction[\"LOGIN\"] = \"login\";\n  AuditAction[\"LOGOUT\"] = \"logout\";\n  AuditAction[\"VALIDATE\"] = \"validate\";\n  AuditAction[\"REDEEM\"] = \"redeem\";\n  AuditAction[\"SCAN\"] = \"scan\";\n  AuditAction[\"APPROVE\"] = \"approve\";\n  AuditAction[\"REJECT\"] = \"reject\";\n  AuditAction[\"SUSPEND\"] = \"suspend\";\n  AuditAction[\"ACTIVATE\"] = \"activate\";\n})(AuditAction || (AuditAction = {}));\nexport var EntityType;\n(function (EntityType) {\n  EntityType[\"USER\"] = \"user\";\n  EntityType[\"VALIDATION\"] = \"validation\";\n  EntityType[\"REWARD\"] = \"reward\";\n  EntityType[\"QR_CODE\"] = \"qr_code\";\n  EntityType[\"PARTNER\"] = \"partner\";\n  EntityType[\"PROVIDER\"] = \"provider\";\n  EntityType[\"CONFIGURATION\"] = \"configuration\";\n})(EntityType || (EntityType = {}));", "map": {"version": 3, "names": ["ConfigCategory", "AuditAction", "EntityType"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\models\\admin.model.ts"], "sourcesContent": ["export interface AdminStats {\n  totalUsers: number;\n  activeUsers: number;\n  newUsersThisMonth: number;\n  totalPoints: number;\n  pointsDistributedThisMonth: number;\n  pointsRedeemedThisMonth: number;\n  totalValidations: number;\n  pendingValidations: number;\n  totalPartners: number;\n  activePartners: number;\n  totalProviders: number;\n  activeProviders: number;\n  totalValidators: number;\n  activeValidators: number;\n}\n\nexport interface UserManagement {\n  id: string;\n  email: string;\n  name: string;\n  role: UserRole;\n  city: 'Monastir' | 'Sousse';\n  points: number;\n  isActive: boolean;\n  lastLoginAt?: Date;\n  createdAt: Date;\n  totalValidations?: number;\n  totalRedemptions?: number;\n  organizationName?: string; // Pour providers/partners\n}\n\nexport interface SystemConfiguration {\n  id: string;\n  key: string;\n  value: string | number | boolean;\n  description: string;\n  category: ConfigCategory;\n  updatedAt: Date;\n  updatedBy: string;\n}\n\nexport enum ConfigCategory {\n  POINTS = 'points',\n  VALIDATION = 'validation',\n  REWARDS = 'rewards',\n  SECURITY = 'security',\n  NOTIFICATIONS = 'notifications',\n  GENERAL = 'general'\n}\n\nexport interface PointsConfiguration {\n  maxPointsPerValidation: number;\n  maxValidationsPerDay: number;\n  maxQrScansPerDay: number;\n  pointsExpirationDays: number;\n  minRedemptionPoints: number;\n  bonusMultiplier: number;\n}\n\nexport interface ValidationConfiguration {\n  autoApprovalThreshold: number;\n  maxPendingDays: number;\n  requireEvidence: boolean;\n  maxValidationsPerValidator: number;\n  validationCooldown: number; // en heures\n}\n\nexport interface GlobalStats {\n  totalPointsInCirculation: number;\n  totalPointsDistributed: number;\n  totalPointsRedeemed: number;\n  averageUserPoints: number;\n  mostActiveCity: 'Monastir' | 'Sousse';\n  topValidators: TopValidator[];\n  topPartners: TopPartner[];\n  topProviders: TopProvider[];\n  monthlyGrowth: MonthlyGrowth[];\n}\n\nexport interface TopValidator {\n  id: string;\n  name: string;\n  organizationName: string;\n  totalValidations: number;\n  averageResponseTime: number; // en heures\n  approvalRate: number; // pourcentage\n}\n\nexport interface TopPartner {\n  id: string;\n  businessName: string;\n  totalRedemptions: number;\n  totalPointsGenerated: number;\n  customerSatisfaction: number; // pourcentage\n  activeRewards: number;\n}\n\nexport interface TopProvider {\n  id: string;\n  organizationName: string;\n  totalScans: number;\n  totalPointsDistributed: number;\n  uniqueUsers: number;\n  engagementRate: number; // pourcentage\n}\n\nexport interface MonthlyGrowth {\n  month: string; // YYYY-MM\n  newUsers: number;\n  pointsDistributed: number;\n  pointsRedeemed: number;\n  validations: number;\n  redemptions: number;\n}\n\nexport interface AuditLog {\n  id: string;\n  userId: string;\n  userName: string;\n  action: AuditAction;\n  entityType: EntityType;\n  entityId: string;\n  oldValue?: any;\n  newValue?: any;\n  timestamp: Date;\n  ipAddress?: string;\n  userAgent?: string;\n}\n\nexport enum AuditAction {\n  CREATE = 'create',\n  UPDATE = 'update',\n  DELETE = 'delete',\n  LOGIN = 'login',\n  LOGOUT = 'logout',\n  VALIDATE = 'validate',\n  REDEEM = 'redeem',\n  SCAN = 'scan',\n  APPROVE = 'approve',\n  REJECT = 'reject',\n  SUSPEND = 'suspend',\n  ACTIVATE = 'activate'\n}\n\nexport enum EntityType {\n  USER = 'user',\n  VALIDATION = 'validation',\n  REWARD = 'reward',\n  QR_CODE = 'qr_code',\n  PARTNER = 'partner',\n  PROVIDER = 'provider',\n  CONFIGURATION = 'configuration'\n}\n\nexport interface CreateUserRequest {\n  email: string;\n  name: string;\n  role: UserRole;\n  city: 'Monastir' | 'Sousse';\n  phone?: string;\n  organizationName?: string; // Pour providers/partners\n  organizationType?: string;\n}\n\nexport interface UpdateUserRequest {\n  name?: string;\n  role?: UserRole;\n  city?: 'Monastir' | 'Sousse';\n  phone?: string;\n  isActive?: boolean;\n  points?: number;\n}\n\nimport { UserRole } from './user.model';\n"], "mappings": "AA0CA,WAAYA,cAOX;AAPD,WAAYA,cAAc;EACxBA,cAAA,qBAAiB;EACjBA,cAAA,6BAAyB;EACzBA,cAAA,uBAAmB;EACnBA,cAAA,yBAAqB;EACrBA,cAAA,mCAA+B;EAC/BA,cAAA,uBAAmB;AACrB,CAAC,EAPWA,cAAc,KAAdA,cAAc;AAwF1B,WAAYC,WAaX;AAbD,WAAYA,WAAW;EACrBA,WAAA,qBAAiB;EACjBA,WAAA,qBAAiB;EACjBA,WAAA,qBAAiB;EACjBA,WAAA,mBAAe;EACfA,WAAA,qBAAiB;EACjBA,WAAA,yBAAqB;EACrBA,WAAA,qBAAiB;EACjBA,WAAA,iBAAa;EACbA,WAAA,uBAAmB;EACnBA,WAAA,qBAAiB;EACjBA,WAAA,uBAAmB;EACnBA,WAAA,yBAAqB;AACvB,CAAC,EAbWA,WAAW,KAAXA,WAAW;AAevB,WAAYC,UAQX;AARD,WAAYA,UAAU;EACpBA,UAAA,iBAAa;EACbA,UAAA,6BAAyB;EACzBA,UAAA,qBAAiB;EACjBA,UAAA,uBAAmB;EACnBA,UAAA,uBAAmB;EACnBA,UAAA,yBAAqB;EACrBA,UAAA,mCAA+B;AACjC,CAAC,EARWA,UAAU,KAAVA,UAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}