import { Component, Inject, OnInit } from '@angular/core';
import { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';
import { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';
import { MatSnackBar } from '@angular/material/snack-bar';
import { UserService } from '../../../../core/services/user.service';
import { AuthService } from '../../../../core/services/auth.service';
import { QrCodeData, User, TransactionType } from '../../../../core/models';

interface DialogData {
  scanResult: any;
  qrData: QrCodeData;
  currentUser: User;
}

@Component({
  selector: 'app-points-assignment',
  templateUrl: './points-assignment.component.html',
  styleUrls: ['./points-assignment.component.css']
})
export class PointsAssignmentComponent implements OnInit {
  assignmentForm: FormGroup;
  isLoading = false;
  
  pointsOptions = [
    { value: 1, label: '1 point', description: 'Action simple' },
    { value: 3, label: '3 points', description: 'Action modérée' },
    { value: 5, label: '5 points', description: 'Action importante' },
    { value: 10, label: '10 points', description: 'Action majeure' }
  ];

  constructor(
    private fb: FormBuilder,
    private userService: UserService,
    private authService: AuthService,
    private snackBar: MatSnackBar,
    public dialogRef: MatDialogRef<PointsAssignmentComponent>,
    @Inject(MAT_DIALOG_DATA) public data: DialogData
  ) {
    this.assignmentForm = this.fb.group({
      points: [this.getDefaultPoints(), [Validators.required, Validators.min(1)]],
      description: [this.getDefaultDescription(), Validators.required],
      targetUserId: [this.getTargetUserId()]
    });
  }

  ngOnInit(): void {
    // Auto-fill form based on QR scan result
    this.prefillForm();
  }

  private getDefaultPoints(): number {
    if (this.data.scanResult?.points) {
      return this.data.scanResult.points;
    }
    
    switch (this.data.qrData?.type) {
      case 'user_transfer':
        return 1;
      case 'validator_action':
        return 10;
      case 'partner_reward':
        return 5;
      case 'system_bonus':
        return 3;
      default:
        return 1;
    }
  }

  private getDefaultDescription(): string {
    if (this.data.scanResult?.message) {
      return this.data.scanResult.message;
    }
    
    switch (this.data.qrData?.type) {
      case 'user_transfer':
        return 'Transfert de points entre utilisateurs';
      case 'validator_action':
        return `Action validée: ${this.data.qrData.action || 'activité communautaire'}`;
      case 'partner_reward':
        return 'Récompense partenaire scannée';
      case 'system_bonus':
        return 'Bonus système activé';
      default:
        return 'Points gagnés via QR code';
    }
  }

  private getTargetUserId(): string {
    return this.data.qrData?.userId || this.data.currentUser?.uid || '';
  }

  private prefillForm(): void {
    // Additional logic to prefill form based on scan type
    if (this.data.qrData?.type === 'user_transfer' && this.data.qrData.userId) {
      // For user transfers, we might want to show the target user info
      this.assignmentForm.patchValue({
        description: `Transfert vers utilisateur ${this.data.qrData.userId}`
      });
    }
  }

  async onSubmit(): Promise<void> {
    if (this.assignmentForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading = true;
    const formValue = this.assignmentForm.value;

    try {
      const currentUser = this.authService.getCurrentUser();
      if (!currentUser) {
        throw new Error('Utilisateur non connecté');
      }

      // Determine transaction type based on QR code type
      const transactionType = this.getTransactionType();
      
      // Add points to the target user (usually current user)
      const targetUserId = formValue.targetUserId || currentUser.uid;
      
      await this.userService.addPointsToUser(
        targetUserId,
        formValue.points,
        transactionType,
        formValue.description,
        this.data.qrData?.validatorId || currentUser.uid
      );

      this.snackBar.open(
        `${formValue.points} point(s) attribué(s) avec succès!`, 
        'Fermer', 
        { duration: 3000 }
      );

      this.dialogRef.close(true);

    } catch (error: any) {
      console.error('Error assigning points:', error);
      this.snackBar.open(
        'Erreur lors de l\'attribution des points', 
        'Fermer', 
        { duration: 5000 }
      );
    } finally {
      this.isLoading = false;
    }
  }

  private getTransactionType(): TransactionType {
    switch (this.data.qrData?.type) {
      case 'validator_action':
        return TransactionType.VALIDATED;
      case 'partner_reward':
        return TransactionType.EARNED;
      case 'system_bonus':
        return TransactionType.BONUS;
      case 'user_transfer':
        return TransactionType.TRANSFERRED;
      default:
        return TransactionType.EARNED;
    }
  }

  private markFormGroupTouched(): void {
    Object.keys(this.assignmentForm.controls).forEach(key => {
      const control = this.assignmentForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string {
    const field = this.assignmentForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return 'Champ requis';
      }
      if (field.errors['min']) {
        return 'Minimum 1 point';
      }
    }
    return '';
  }

  onCancel(): void {
    this.dialogRef.close(false);
  }

  getScanTypeLabel(): string {
    const typeLabels: { [key: string]: string } = {
      'user_transfer': 'Transfert utilisateur',
      'validator_action': 'Action validateur',
      'partner_reward': 'Récompense partenaire',
      'system_bonus': 'Bonus système'
    };
    return typeLabels[this.data.qrData?.type || ''] || 'Scan QR';
  }

  getScanTypeIcon(): string {
    const typeIcons: { [key: string]: string } = {
      'user_transfer': 'swap_horiz',
      'validator_action': 'verified',
      'partner_reward': 'card_giftcard',
      'system_bonus': 'star'
    };
    return typeIcons[this.data.qrData?.type || ''] || 'qr_code';
  }
}
