{"version": 3, "file": "src_app_features_qr-scanner_qr-scanner_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;AACmD;AACoB;;;;AAOjE,MAAOE,SAAS;EAIpBC,YACUC,WAAwB,EACxBC,WAAwB;IADxB,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IALb,KAAAC,iBAAiB,GAAG,IAAIN,iDAAe,CAAsB,IAAI,CAAC;IACnE,KAAAO,WAAW,GAAG,IAAI,CAACD,iBAAiB,CAACE,YAAY,EAAE;EAKvD;EAEH;EACMC,iBAAiBA,CAACC,OAAe,EAAEC,QAAc;IAAA,IAAAC,KAAA;IAAA,OAAAC,wJAAA;MACrD,MAAMC,UAAU,GAAiB;QAC/BC,IAAI,EAAEL,OAAO;QACbM,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBP;OACD;MAEDC,KAAI,CAACN,iBAAiB,CAACa,IAAI,CAACL,UAAU,CAAC;MACvC,OAAOA,UAAU;IAAC;EACpB;EAEA;EACAM,WAAWA,CAACV,OAAe;IACzB,IAAI;MACF;MACA,MAAMW,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACb,OAAO,CAAC;MAClC,IAAI,IAAI,CAACc,iBAAiB,CAACH,MAAM,CAAC,EAAE;QAClC,OAAOA,MAAoB;;KAE9B,CAAC,MAAM;MACN;MACA,OAAO,IAAI,CAACI,iBAAiB,CAACf,OAAO,CAAC;;IAExC,OAAO,IAAI;EACb;EAEA;EACAgB,sBAAsBA,CAACC,MAAc,EAAEC,MAAe;IACpD,MAAMC,MAAM,GAAe;MACzBC,IAAI,EAAE7B,+CAAU,CAAC8B,aAAa;MAC9BJ,MAAM;MACNC,MAAM;MACNX,SAAS,EAAEC,IAAI,CAACc,GAAG,EAAE;MACrBC,SAAS,EAAE,IAAI,CAACC,iBAAiB,CAACP,MAAM,EAAEC,MAAM;KACjD;IACD,OAAON,IAAI,CAACa,SAAS,CAACN,MAAM,CAAC;EAC/B;EAEA;EACAO,mBAAmBA,CAACC,WAAmB,EAAEC,MAAc,EAAEV,MAAc;IACrE,MAAMC,MAAM,GAAe;MACzBC,IAAI,EAAE7B,+CAAU,CAACsC,gBAAgB;MACjCF,WAAW;MACXC,MAAM;MACNV,MAAM;MACNX,SAAS,EAAEC,IAAI,CAACc,GAAG,EAAE;MACrBC,SAAS,EAAE,IAAI,CAACC,iBAAiB,CAACG,WAAW,EAAET,MAAM,EAAEU,MAAM;KAC9D;IACD,OAAOhB,IAAI,CAACa,SAAS,CAACN,MAAM,CAAC;EAC/B;EAEA;EACMW,YAAYA,CAACX,MAAkB;IAAA,IAAAY,MAAA;IAAA,OAAA5B,wJAAA;MACnC,MAAM6B,WAAW,GAAGD,MAAI,CAACpC,WAAW,CAACsC,cAAc,EAAE;MACrD,IAAI,CAACD,WAAW,EAAE;QAChB,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;;MAG3C,QAAQf,MAAM,CAACC,IAAI;QACjB,KAAK7B,+CAAU,CAAC8B,aAAa;UAC3B,OAAOU,MAAI,CAACI,kBAAkB,CAAChB,MAAM,EAAEa,WAAW,CAAC;QAErD,KAAKzC,+CAAU,CAACsC,gBAAgB;UAC9B,OAAOE,MAAI,CAACK,qBAAqB,CAACjB,MAAM,EAAEa,WAAW,CAAC;QAExD,KAAKzC,+CAAU,CAAC8C,cAAc;UAC5B,OAAON,MAAI,CAACO,mBAAmB,CAACnB,MAAM,EAAEa,WAAW,CAAC;QAEtD,KAAKzC,+CAAU,CAACgD,YAAY;UAC1B,OAAOR,MAAI,CAACS,iBAAiB,CAACrB,MAAM,EAAEa,WAAW,CAAC;QAEpD;UACE,MAAM,IAAIE,KAAK,CAAC,sBAAsB,CAAC;;IAC1C;EACH;EAEcC,kBAAkBA,CAAChB,MAAkB,EAAEa,WAAiB;IAAA,OAAA7B,wJAAA;MACpE,IAAI,CAACgB,MAAM,CAACF,MAAM,EAAE;QAClB,MAAM,IAAIiB,KAAK,CAAC,+BAA+B,CAAC;;MAGlD;MACA,MAAMhB,MAAM,GAAGC,MAAM,CAACD,MAAM,IAAI,CAAC,CAAC,CAAC;MAEnC,OAAO;QACLE,IAAI,EAAE,eAAe;QACrBqB,YAAY,EAAEtB,MAAM,CAACF,MAAM;QAC3BC,MAAM;QACNwB,OAAO,EAAE,qBAAqBxB,MAAM;OACrC;IAAC;EACJ;EAEckB,qBAAqBA,CAACjB,MAAkB,EAAEa,WAAiB;IAAA,OAAA7B,wJAAA;MACvE,IAAI6B,WAAW,CAACW,IAAI,KAAK,WAAW,EAAE;QACpC,MAAM,IAAIT,KAAK,CAAC,gDAAgD,CAAC;;MAGnE,OAAO;QACLd,IAAI,EAAE,kBAAkB;QACxBQ,MAAM,EAAET,MAAM,CAACS,MAAM;QACrBV,MAAM,EAAEC,MAAM,CAACD,MAAM,IAAI,EAAE;QAC3BwB,OAAO,EAAE,sBAAsBvB,MAAM,CAACS,MAAM;OAC7C;IAAC;EACJ;EAEcU,mBAAmBA,CAACnB,MAAkB,EAAEa,WAAiB;IAAA,OAAA7B,wJAAA;MACrE,OAAO;QACLiB,IAAI,EAAE,gBAAgB;QACtBF,MAAM,EAAEC,MAAM,CAACD,MAAM,IAAI,CAAC;QAC1BwB,OAAO,EAAE;OACV;IAAC;EACJ;EAEcF,iBAAiBA,CAACrB,MAAkB,EAAEa,WAAiB;IAAA,OAAA7B,wJAAA;MACnE,OAAO;QACLiB,IAAI,EAAE,cAAc;QACpBF,MAAM,EAAEC,MAAM,CAACD,MAAM,IAAI,CAAC;QAC1BwB,OAAO,EAAE;OACV;IAAC;EACJ;EAEA;EACQ5B,iBAAiBA,CAACT,IAAS;IACjC,OAAOA,IAAI,IACJ,OAAOA,IAAI,CAACe,IAAI,KAAK,QAAQ,IAC7BwB,MAAM,CAACC,MAAM,CAACtD,+CAAU,CAAC,CAACuD,QAAQ,CAACzC,IAAI,CAACe,IAAI,CAAC,IAC7C,OAAOf,IAAI,CAACE,SAAS,KAAK,QAAQ;EAC3C;EAEA;EACQQ,iBAAiBA,CAACf,OAAe;IACvC;IACA,IAAIA,OAAO,CAAC+C,UAAU,CAAC,OAAO,CAAC,EAAE;MAC/B,MAAM9B,MAAM,GAAGjB,OAAO,CAACgD,SAAS,CAAC,CAAC,CAAC;MACnC,OAAO;QACL5B,IAAI,EAAE7B,+CAAU,CAAC8B,aAAa;QAC9BJ,MAAM;QACNV,SAAS,EAAEC,IAAI,CAACc,GAAG;OACpB;;IAGH,IAAItB,OAAO,CAAC+C,UAAU,CAAC,YAAY,CAAC,EAAE;MACpC,MAAME,KAAK,GAAGjD,OAAO,CAACgD,SAAS,CAAC,EAAE,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC;MAC9C,OAAO;QACL9B,IAAI,EAAE7B,+CAAU,CAACsC,gBAAgB;QACjCF,WAAW,EAAEsB,KAAK,CAAC,CAAC,CAAC;QACrBrB,MAAM,EAAEqB,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAmB;QACvC/B,MAAM,EAAEiC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;QAChC1C,SAAS,EAAEC,IAAI,CAACc,GAAG;OACpB;;IAGH;IACA,OAAO;MACLF,IAAI,EAAE7B,+CAAU,CAAC8B,aAAa;MAC9BJ,MAAM,EAAEjB,OAAO;MACfO,SAAS,EAAEC,IAAI,CAACc,GAAG;KACpB;EACH;EAEA;EACQE,iBAAiBA,CAACP,MAAc,EAAEC,MAAe,EAAEU,MAAe;IACxE,MAAMvB,IAAI,GAAG,GAAGY,MAAM,IAAIC,MAAM,IAAI,CAAC,IAAIU,MAAM,IAAI,EAAE,IAAIpB,IAAI,CAACc,GAAG,EAAE,EAAE;IACrE,OAAO8B,IAAI,CAAC/C,IAAI,CAAC,CAAC2C,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EACpC;EAEA;EACAK,qBAAqBA,CAAA;IACnB,OAAO,CACL,IAAI,CAACrC,sBAAsB,CAAC,SAAS,EAAE,CAAC,CAAC,EACzC,IAAI,CAACA,sBAAsB,CAAC,SAAS,EAAE,CAAC,CAAC,EACzC,IAAI,CAACU,mBAAmB,CAAC,cAAc,EAAE,mBAAmB,EAAE,EAAE,CAAC,EACjEd,IAAI,CAACa,SAAS,CAAC;MACbL,IAAI,EAAE7B,+CAAU,CAAC8C,cAAc;MAC/BnB,MAAM,EAAE,CAAC;MACTX,SAAS,EAAEC,IAAI,CAACc,GAAG;KACpB,CAAC,EACFV,IAAI,CAACa,SAAS,CAAC;MACbL,IAAI,EAAE7B,+CAAU,CAACgD,YAAY;MAC7BrB,MAAM,EAAE,CAAC;MACTX,SAAS,EAAEC,IAAI,CAACc,GAAG;KACpB,CAAC,CACH;EACH;EAEA;EACAgC,eAAeA,CAAA;IACb,IAAI,CAAC1D,iBAAiB,CAACa,IAAI,CAAC,IAAI,CAAC;EACnC;;;uBAvMWjB,SAAS,EAAA+D,sDAAA,CAAAE,sDAAA,GAAAF,sDAAA,CAAAI,sDAAA;IAAA;EAAA;;;aAATnE,SAAS;MAAAqE,OAAA,EAATrE,SAAS,CAAAsE,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACNgD;AACK;AAIG;;;;;;;;;;;;;;;;;ICW9DR,4DAFJ,qBAAwE,cAC3C,eACE;IAAAA,oDAAA,GAAkB;IAAAA,0DAAA,EAAO;IACpDA,4DAAA,eAAiC;IAAAA,oDAAA,GAAwB;IAE7DA,0DAF6D,EAAO,EAC5D,EACK;;;;IALoCA,wDAAA,UAAAgB,SAAA,CAAAC,KAAA,CAAsB;IAExCjB,uDAAA,GAAkB;IAAlBA,+DAAA,CAAAgB,SAAA,CAAAI,KAAA,CAAkB;IACZpB,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAgB,SAAA,CAAAK,WAAA,CAAwB;;;;;IAqB/DrB,4DAFF,wBAC8D,gBACjD;IAAAA,oDAAA,wBAAiB;IAAAA,0DAAA,EAAY;IACxCA,uDAAA,gBAGyC;IACzCA,4DAAA,mBAAoB;IAAAA,oDAAA,aAAM;IAC5BA,0DAD4B,EAAW,EACtB;;;;;IAUbA,4DADF,cAAwD,eAC5C;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAW;IAC7BA,4DAAA,WAAM;IAAAA,oDAAA,GAAkD;;IAC1DA,0DAD0D,EAAO,EAC3D;;;;IADEA,uDAAA,GAAkD;IAAlDA,gEAAA,kBAAAA,yDAAA,OAAAyB,MAAA,CAAA3E,IAAA,CAAAc,MAAA,CAAAZ,SAAA,eAAkD;;;;;IAGxDgD,4DADF,cAAqD,eACzC;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAW;IAC7BA,4DAAA,WAAM;IAAAA,oDAAA,GAAgC;IACxCA,0DADwC,EAAO,EACzC;;;;IADEA,uDAAA,GAAgC;IAAhCA,gEAAA,aAAAyB,MAAA,CAAA3E,IAAA,CAAAc,MAAA,CAAAS,MAAA,KAAgC;;;;;IAyB1C2B,uDAAA,sBAA2D;;;;;IAC3DA,4DAAA,WAAyB;IAAAA,oDAAA,2BAAoB;IAAAA,0DAAA,EAAO;;;ADnEpD,MAAO0B,yBAAyB;EAWpCxF,YACUyF,EAAe,EACfxF,WAAwB,EACxBC,WAAwB,EACxBwF,QAAqB,EACtBC,SAAkD,EACzB/E,IAAgB;IALxC,KAAA6E,EAAE,GAAFA,EAAE;IACF,KAAAxF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAwF,QAAQ,GAARA,QAAQ;IACT,KAAAC,SAAS,GAATA,SAAS;IACgB,KAAA/E,IAAI,GAAJA,IAAI;IAftC,KAAAgF,SAAS,GAAG,KAAK;IAEjB,KAAAC,aAAa,GAAG,CACd;MAAEd,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE,SAAS;MAAEC,WAAW,EAAE;IAAe,CAAE,EAC5D;MAAEJ,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE,UAAU;MAAEC,WAAW,EAAE;IAAgB,CAAE,EAC9D;MAAEJ,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE,UAAU;MAAEC,WAAW,EAAE;IAAmB,CAAE,EACjE;MAAEJ,KAAK,EAAE,EAAE;MAAEG,KAAK,EAAE,WAAW;MAAEC,WAAW,EAAE;IAAgB,CAAE,CACjE;IAUC,IAAI,CAACW,cAAc,GAAG,IAAI,CAACL,EAAE,CAACM,KAAK,CAAC;MAClCtE,MAAM,EAAE,CAAC,IAAI,CAACuE,gBAAgB,EAAE,EAAE,CAACzB,sDAAU,CAAC0B,QAAQ,EAAE1B,sDAAU,CAAC2B,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3Ef,WAAW,EAAE,CAAC,IAAI,CAACgB,qBAAqB,EAAE,EAAE5B,sDAAU,CAAC0B,QAAQ,CAAC;MAChEjD,YAAY,EAAE,CAAC,IAAI,CAACoD,eAAe,EAAE;KACtC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,WAAW,EAAE;EACpB;EAEQN,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAACpF,IAAI,CAACD,UAAU,EAAEc,MAAM,EAAE;MAChC,OAAO,IAAI,CAACb,IAAI,CAACD,UAAU,CAACc,MAAM;;IAGpC,QAAQ,IAAI,CAACb,IAAI,CAACc,MAAM,EAAEC,IAAI;MAC5B,KAAK,eAAe;QAClB,OAAO,CAAC;MACV,KAAK,kBAAkB;QACrB,OAAO,EAAE;MACX,KAAK,gBAAgB;QACnB,OAAO,CAAC;MACV,KAAK,cAAc;QACjB,OAAO,CAAC;MACV;QACE,OAAO,CAAC;;EAEd;EAEQwE,qBAAqBA,CAAA;IAC3B,IAAI,IAAI,CAACvF,IAAI,CAACD,UAAU,EAAEsC,OAAO,EAAE;MACjC,OAAO,IAAI,CAACrC,IAAI,CAACD,UAAU,CAACsC,OAAO;;IAGrC,QAAQ,IAAI,CAACrC,IAAI,CAACc,MAAM,EAAEC,IAAI;MAC5B,KAAK,eAAe;QAClB,OAAO,wCAAwC;MACjD,KAAK,kBAAkB;QACrB,OAAO,mBAAmB,IAAI,CAACf,IAAI,CAACc,MAAM,CAACS,MAAM,IAAI,wBAAwB,EAAE;MACjF,KAAK,gBAAgB;QACnB,OAAO,+BAA+B;MACxC,KAAK,cAAc;QACjB,OAAO,sBAAsB;MAC/B;QACE,OAAO,2BAA2B;;EAExC;EAEQiE,eAAeA,CAAA;IACrB,OAAO,IAAI,CAACxF,IAAI,CAACc,MAAM,EAAEF,MAAM,IAAI,IAAI,CAACZ,IAAI,CAAC2B,WAAW,EAAEgE,GAAG,IAAI,EAAE;EACrE;EAEQD,WAAWA,CAAA;IACjB;IACA,IAAI,IAAI,CAAC1F,IAAI,CAACc,MAAM,EAAEC,IAAI,KAAK,eAAe,IAAI,IAAI,CAACf,IAAI,CAACc,MAAM,CAACF,MAAM,EAAE;MACzE;MACA,IAAI,CAACsE,cAAc,CAACU,UAAU,CAAC;QAC7BrB,WAAW,EAAE,8BAA8B,IAAI,CAACvE,IAAI,CAACc,MAAM,CAACF,MAAM;OACnE,CAAC;;EAEN;EAEMiF,QAAQA,CAAA;IAAA,IAAAhG,KAAA;IAAA,OAAAC,wJAAA;MACZ,IAAID,KAAI,CAACqF,cAAc,CAACY,OAAO,EAAE;QAC/BjG,KAAI,CAACkG,oBAAoB,EAAE;QAC3B;;MAGFlG,KAAI,CAACmF,SAAS,GAAG,IAAI;MACrB,MAAMgB,SAAS,GAAGnG,KAAI,CAACqF,cAAc,CAACf,KAAK;MAE3C,IAAI;QACF,MAAMxC,WAAW,GAAG9B,KAAI,CAACP,WAAW,CAACsC,cAAc,EAAE;QACrD,IAAI,CAACD,WAAW,EAAE;UAChB,MAAM,IAAIE,KAAK,CAAC,0BAA0B,CAAC;;QAG7C;QACA,MAAMoE,eAAe,GAAGpG,KAAI,CAACqG,kBAAkB,EAAE;QAEjD;QACA,MAAM9D,YAAY,GAAG4D,SAAS,CAAC5D,YAAY,IAAIT,WAAW,CAACgE,GAAG;QAE9D,MAAM9F,KAAI,CAACR,WAAW,CAAC8G,eAAe,CACpC/D,YAAY,EACZ4D,SAAS,CAACnF,MAAM,EAChBoF,eAAe,EACfD,SAAS,CAACzB,WAAW,EACrB1E,KAAI,CAACG,IAAI,CAACc,MAAM,EAAEQ,WAAW,IAAIK,WAAW,CAACgE,GAAG,CACjD;QAED9F,KAAI,CAACiF,QAAQ,CAACsB,IAAI,CAChB,GAAGJ,SAAS,CAACnF,MAAM,oCAAoC,EACvD,QAAQ,EACR;UAAEwF,QAAQ,EAAE;QAAI,CAAE,CACnB;QAEDxG,KAAI,CAACkF,SAAS,CAACuB,KAAK,CAAC,IAAI,CAAC;OAE3B,CAAC,OAAOC,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/C1G,KAAI,CAACiF,QAAQ,CAACsB,IAAI,CAChB,0CAA0C,EAC1C,QAAQ,EACR;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;OACF,SAAS;QACRxG,KAAI,CAACmF,SAAS,GAAG,KAAK;;IACvB;EACH;EAEQkB,kBAAkBA,CAAA;IACxB,QAAQ,IAAI,CAAClG,IAAI,CAACc,MAAM,EAAEC,IAAI;MAC5B,KAAK,kBAAkB;QACrB,OAAO8C,yDAAe,CAAC4C,SAAS;MAClC,KAAK,gBAAgB;QACnB,OAAO5C,yDAAe,CAAC6C,MAAM;MAC/B,KAAK,cAAc;QACjB,OAAO7C,yDAAe,CAAC8C,KAAK;MAC9B,KAAK,eAAe;QAClB,OAAO9C,yDAAe,CAAC+C,WAAW;MACpC;QACE,OAAO/C,yDAAe,CAAC6C,MAAM;;EAEnC;EAEQX,oBAAoBA,CAAA;IAC1BxD,MAAM,CAACsE,IAAI,CAAC,IAAI,CAAC3B,cAAc,CAAC4B,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACtD,MAAMC,OAAO,GAAG,IAAI,CAAC/B,cAAc,CAACgC,GAAG,CAACF,GAAG,CAAC;MAC5CC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAACC,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAACpC,cAAc,CAACgC,GAAG,CAACG,SAAS,CAAC;IAChD,IAAIC,KAAK,EAAEC,MAAM,IAAID,KAAK,CAACE,OAAO,EAAE;MAClC,IAAIF,KAAK,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,cAAc;;MAEvB,IAAID,KAAK,CAACC,MAAM,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,iBAAiB;;;IAG5B,OAAO,EAAE;EACX;EAEAE,QAAQA,CAAA;IACN,IAAI,CAAC1C,SAAS,CAACuB,KAAK,CAAC,KAAK,CAAC;EAC7B;EAEAoB,gBAAgBA,CAAA;IACd,MAAMC,UAAU,GAA8B;MAC5C,eAAe,EAAE,uBAAuB;MACxC,kBAAkB,EAAE,mBAAmB;MACvC,gBAAgB,EAAE,uBAAuB;MACzC,cAAc,EAAE;KACjB;IACD,OAAOA,UAAU,CAAC,IAAI,CAAC3H,IAAI,CAACc,MAAM,EAAEC,IAAI,IAAI,EAAE,CAAC,IAAI,SAAS;EAC9D;EAEA6G,eAAeA,CAAA;IACb,MAAMC,SAAS,GAA8B;MAC3C,eAAe,EAAE,YAAY;MAC7B,kBAAkB,EAAE,UAAU;MAC9B,gBAAgB,EAAE,eAAe;MACjC,cAAc,EAAE;KACjB;IACD,OAAOA,SAAS,CAAC,IAAI,CAAC7H,IAAI,CAACc,MAAM,EAAEC,IAAI,IAAI,EAAE,CAAC,IAAI,SAAS;EAC7D;;;uBA7LW6D,yBAAyB,EAAA1B,+DAAA,CAAAE,uDAAA,GAAAF,+DAAA,CAAAI,oEAAA,GAAAJ,+DAAA,CAAA8E,oEAAA,GAAA9E,+DAAA,CAAA+E,oEAAA,GAAA/E,+DAAA,CAAAiF,kEAAA,GAAAjF,+DAAA,CAiB1BU,qEAAe;IAAA;EAAA;;;YAjBdgB,yBAAyB;MAAAyD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBhCzF,4DAHN,aAA+B,aACF,YACJ,kBACW;UAAAA,oDAAA,GAAuB;UAAAA,0DAAA,EAAW;UAChEA,oDAAA,8BACF;UAAAA,0DAAA,EAAK;UACLA,4DAAA,WAAqB;UAAAA,oDAAA,GAAwB;UAC/CA,0DAD+C,EAAI,EAC7C;UAMAA,4DAJN,yBAAoB,cACyC,yBAED,iBAC3C;UAAAA,oDAAA,wBAAgB;UAAAA,0DAAA,EAAY;UACvCA,4DAAA,qBAAqC;UACnCA,wDAAA,KAAA4F,gDAAA,wBAAwE;UAM1E5F,0DAAA,EAAa;UACbA,4DAAA,iBAAW;UAAAA,oDAAA,IAA6B;UAC1CA,0DAD0C,EAAY,EACrC;UAIfA,4DADF,yBAAwD,iBAC3C;UAAAA,oDAAA,mBAAW;UAAAA,0DAAA,EAAY;UAClCA,4DAAA,mBAGwE;UACxEA,oDAAA;UAAAA,0DAAA,EAAW;UACXA,4DAAA,iBAAW;UAAAA,oDAAA,IAAkC;UAC/CA,0DAD+C,EAAY,EAC1C;UAGjBA,wDAAA,KAAA6F,oDAAA,6BAC8D;UAW5D7F,4DADF,eAA0B,UACpB;UAAAA,oDAAA,4BAAe;UAAAA,0DAAA,EAAK;UAEtBA,4DADF,eAAyB,gBACb;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAW;UAC5BA,4DAAA,YAAM;UAAAA,oDAAA,IAA8B;UACtCA,0DADsC,EAAO,EACvC;UAKNA,wDAJA,KAAA8F,yCAAA,kBAAwD,KAAAC,yCAAA,kBAIH;UAIvD/F,0DAAA,EAAM;UAKFA,4DAFJ,eAA4B,eACG,oBACI;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAW;UAE7CA,4DADF,eAA0B,UACpB;UAAAA,oDAAA,IAAuD;UAAAA,0DAAA,EAAK;UAChEA,4DAAA,SAAG;UAAAA,oDAAA,IAAgE;UAK7EA,0DAL6E,EAAI,EACnE,EACF,EACF,EACD,EACY;UAGnBA,4DADF,8BAAgC,kBACiC;UAA5CA,wDAAA,mBAAAiG,4DAAA;YAAA,OAASP,GAAA,CAAAnB,QAAA,EAAU;UAAA,EAAC;UACrCvE,oDAAA,iBACF;UAAAA,0DAAA,EAAS;UACTA,4DAAA,kBAGyD;UADjDA,wDAAA,mBAAAkG,4DAAA;YAAA,OAASR,GAAA,CAAA/C,QAAA,EAAU;UAAA,EAAC;UAG1B3C,wDADA,KAAAmG,iDAAA,0BAA6C,KAAAC,0CAAA,mBACpB;UAG/BpG,0DAFI,EAAS,EACU,EACjB;;;;UAtFUA,uDAAA,GAAmB;UAAnBA,wDAAA,oBAAmB;UAACA,uDAAA,EAAuB;UAAvBA,+DAAA,CAAA0F,GAAA,CAAAhB,eAAA,GAAuB;UAGlC1E,uDAAA,GAAwB;UAAxBA,+DAAA,CAAA0F,GAAA,CAAAlB,gBAAA,GAAwB;UAIvCxE,uDAAA,GAA4B;UAA5BA,wDAAA,cAAA0F,GAAA,CAAA1D,cAAA,CAA4B;UAKGhC,uDAAA,GAAgB;UAAhBA,wDAAA,YAAA0F,GAAA,CAAA3D,aAAA,CAAgB;UAOtC/B,uDAAA,GAA6B;UAA7BA,+DAAA,CAAA0F,GAAA,CAAAxB,aAAA,WAA6B;UAW7BlE,uDAAA,GAAkC;UAAlCA,+DAAA,CAAA0F,GAAA,CAAAxB,aAAA,gBAAkC;UAK9BlE,uDAAA,EAA2C;UAA3CA,wDAAA,UAAA0F,GAAA,CAAA5I,IAAA,CAAAc,MAAA,kBAAA8H,GAAA,CAAA5I,IAAA,CAAAc,MAAA,CAAAC,IAAA,sBAA2C;UAclDmC,uDAAA,GAA8B;UAA9BA,gEAAA,WAAA0F,GAAA,CAAAlB,gBAAA,OAA8B;UAEZxE,uDAAA,EAA4B;UAA5BA,wDAAA,SAAA0F,GAAA,CAAA5I,IAAA,CAAAc,MAAA,kBAAA8H,GAAA,CAAA5I,IAAA,CAAAc,MAAA,CAAAZ,SAAA,CAA4B;UAI5BgD,uDAAA,EAAyB;UAAzBA,wDAAA,SAAA0F,GAAA,CAAA5I,IAAA,CAAAc,MAAA,kBAAA8H,GAAA,CAAA5I,IAAA,CAAAc,MAAA,CAAAS,MAAA,CAAyB;UAW3C2B,uDAAA,GAAuD;UAAvDA,gEAAA,OAAAqG,QAAA,GAAAX,GAAA,CAAA1D,cAAA,CAAAgC,GAAA,6BAAAqC,QAAA,CAAApF,KAAA,oBAAuD;UACxDjB,uDAAA,GAAgE;UAAhEA,gEAAA,mCAAA0F,GAAA,CAAA5I,IAAA,CAAA2B,WAAA,kBAAAiH,GAAA,CAAA5I,IAAA,CAAA2B,WAAA,CAAA6H,IAAA,uBAAgE;UAQnCtG,uDAAA,GAAsB;UAAtBA,wDAAA,aAAA0F,GAAA,CAAA5D,SAAA,CAAsB;UAMtD9B,uDAAA,GAAgD;UAAhDA,wDAAA,aAAA0F,GAAA,CAAA5D,SAAA,IAAA4D,GAAA,CAAA1D,cAAA,CAAAY,OAAA,CAAgD;UAC1B5C,uDAAA,EAAe;UAAfA,wDAAA,SAAA0F,GAAA,CAAA5D,SAAA,CAAe;UACpC9B,uDAAA,EAAgB;UAAhBA,wDAAA,UAAA0F,GAAA,CAAA5D,SAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AChFgE;;;;;;;;;;;;;;;ICiB/E9B,4DAFJ,cAAgD,cACnB,cACI;IAI3BA,uDAHA,cAAmC,cACC,cACE,cACC;IAE3CA,0DADE,EAAM,EACF;IACNA,4DAAA,YAA+B;IAAAA,oDAAA,sCAA+B;IAChEA,0DADgE,EAAI,EAC9D;;;;;;IAKNA,4DAAA,iBAIyC;IAFjCA,wDAAA,mBAAAuG,oEAAA;MAAAvG,2DAAA,CAAAyG,GAAA;MAAA,MAAAC,MAAA,GAAA1G,2DAAA;MAAA,OAAAA,yDAAA,CAAS0G,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IAG/B7G,4DAAA,eAAU;IAAAA,oDAAA,sBAAe;IAAAA,0DAAA,EAAW;IACpCA,oDAAA,0BACF;IAAAA,0DAAA,EAAS;;;;;;IAETA,4DAAA,iBAIyC;IAFjCA,wDAAA,mBAAA8G,oEAAA;MAAA9G,2DAAA,CAAA+G,GAAA;MAAA,MAAAL,MAAA,GAAA1G,2DAAA;MAAA,OAAAA,yDAAA,CAAS0G,MAAA,CAAAM,YAAA,EAAc;IAAA,EAAC;IAG9BhH,4DAAA,eAAU;IAAAA,oDAAA,WAAI;IAAAA,0DAAA,EAAW;IACzBA,oDAAA,6BACF;IAAAA,0DAAA,EAAS;;;;;;IAzCXA,4DAHN,cAA8C,mBACd,uBACV,cACc;IAO5BA,uDANA,mBAKQ,oBAC8D;IAGtEA,wDAAA,IAAAiH,uCAAA,kBAAgD;IAWlDjH,0DAAA,EAAM;IAGNA,4DAAA,cAA6B;IAU3BA,wDATA,KAAAkH,2CAAA,qBAIyC,KAAAC,2CAAA,qBASA;IAKzCnH,4DAAA,kBAI0C;IAFlCA,wDAAA,mBAAAoH,2DAAA;MAAApH,2DAAA,CAAAqH,GAAA;MAAA,MAAAX,MAAA,GAAA1G,2DAAA;MAAA,OAAAA,yDAAA,CAAS0G,MAAA,CAAAY,gBAAA,EAAkB;IAAA,EAAC;IAGlCtH,4DAAA,gBAAU;IAAAA,oDAAA,IAA6C;IAAAA,0DAAA,EAAW;IAClEA,oDAAA,IACF;IAAAA,0DAAA,EAAS;IAETA,4DAAA,kBAI2C;IAFnCA,wDAAA,mBAAAuH,2DAAA;MAAAvH,2DAAA,CAAAqH,GAAA;MAAA,MAAAX,MAAA,GAAA1G,2DAAA;MAAA,OAAAA,yDAAA,CAAS0G,MAAA,CAAAc,YAAA,EAAc;IAAA,EAAC;IAG9BxH,4DAAA,gBAAU;IAAAA,oDAAA,2BAAmB;IAAAA,0DAAA,EAAW;IACxCA,oDAAA,6BACF;IAIRA,0DAJQ,EAAS,EACL,EACW,EACV,EACP;;;;IA5DSA,uDAAA,GAA2B;IAA3BA,yDAAA,WAAA0G,MAAA,CAAAgB,UAAA,CAA2B;IAOJ1H,uDAAA,GAAgB;IAAhBA,wDAAA,SAAA0G,MAAA,CAAAgB,UAAA,CAAgB;IAkBrC1H,uDAAA,GAAiB;IAAjBA,wDAAA,UAAA0G,MAAA,CAAAgB,UAAA,CAAiB;IASjB1H,uDAAA,EAAgB;IAAhBA,wDAAA,SAAA0G,MAAA,CAAAgB,UAAA,CAAgB;IASjB1H,uDAAA,EAAwB;IAAxBA,wDAAA,cAAA0G,MAAA,CAAAgB,UAAA,CAAwB;IAEpB1H,uDAAA,GAA6C;IAA7CA,+DAAA,CAAA0G,MAAA,CAAAiB,YAAA,4BAA6C;IACvD3H,uDAAA,EACF;IADEA,gEAAA,MAAA0G,MAAA,CAAAiB,YAAA,iCACF;IAKQ3H,uDAAA,EAAwB;IAAxBA,wDAAA,cAAA0G,MAAA,CAAAgB,UAAA,CAAwB;;;;;;IAehC1H,4DAJR,cAAkD,mBACf,uBACb,cACe,mBACI;IAAAA,oDAAA,iBAAU;IAAAA,0DAAA,EAAW;IACtDA,4DAAA,SAAI;IAAAA,oDAAA,iCAAqB;IAAAA,0DAAA,EAAK;IAC9BA,4DAAA,QAAG;IAAAA,oDAAA,gGAAgE;IAAAA,0DAAA,EAAI;IACvEA,4DAAA,kBAAoE;IAA1BA,wDAAA,mBAAA4H,2DAAA;MAAA5H,2DAAA,CAAA6H,GAAA;MAAA,MAAAnB,MAAA,GAAA1G,2DAAA;MAAA,OAAAA,yDAAA,CAAS0G,MAAA,CAAAoB,aAAA,EAAe;IAAA,EAAC;IACjE9H,4DAAA,gBAAU;IAAAA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAW;IAC7BA,oDAAA,yBACF;IAIRA,0DAJQ,EAAS,EACL,EACW,EACV,EACP;;;;;;IAcEA,4DAAA,iBAKkD;IAH1CA,wDAAA,mBAAA+H,8DAAA;MAAA,MAAAC,OAAA,GAAAhI,2DAAA,CAAAiI,GAAA,EAAAC,SAAA;MAAA,MAAAxB,MAAA,GAAA1G,2DAAA;MAAA,OAAAA,yDAAA,CAAS0G,MAAA,CAAAyB,UAAA,CAAAH,OAAA,CAAAlL,IAAA,CAAqB;IAAA,EAAC;IAIrCkD,4DAAA,eAAU;IAAAA,oDAAA,GAAmC;IAAAA,0DAAA,EAAW;IAEtDA,4DADF,cAAiC,eACN;IAAAA,oDAAA,GAAgB;IAAAA,0DAAA,EAAO;IAChDA,4DAAA,gBAA2B;IAAAA,oDAAA,GAA+B;IAE9DA,0DAF8D,EAAQ,EAC9D,EACC;;;;;;IANDA,yDAAA,oBAAAqI,IAAA,aAAyC;IAFzCrI,wDAAA,UAAA0G,MAAA,CAAA4B,kBAAA,CAAAD,IAAA,EAA+B;IAG3BrI,uDAAA,GAAmC;IAAnCA,+DAAA,CAAA0G,MAAA,CAAA6B,iBAAA,CAAAP,OAAA,CAAA5G,KAAA,EAAmC;IAElBpB,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAgI,OAAA,CAAA5G,KAAA,CAAgB;IACdpB,uDAAA,GAA+B;IAA/BA,+DAAA,CAAA0G,MAAA,CAAA8B,aAAA,CAAAR,OAAA,CAAA5G,KAAA,EAA+B;;;ADpGlE,MAAOqH,kBAAkB;EAmB7BvM,YACUwM,SAAoB,EACpBvM,WAAwB,EACxBC,WAAwB,EACxBuM,MAAiB,EACjB/G,QAAqB;IAJrB,KAAA8G,SAAS,GAATA,SAAS;IACT,KAAAvM,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAuM,MAAM,GAANA,MAAM;IACN,KAAA/G,QAAQ,GAARA,QAAQ;IApBlB,KAAA8F,UAAU,GAAG,KAAK;IAClB,KAAAkB,SAAS,GAAG,KAAK;IACjB,KAAAjB,YAAY,GAAG,KAAK;IACpB,KAAAlJ,WAAW,GAAgB,IAAI;IAC/B,KAAAoK,MAAM,GAAuB,IAAI;IAGjC;IACA,KAAAC,WAAW,GAAG,CACZ;MAAE1H,KAAK,EAAE,8BAA8B;MAAEtE,IAAI,EAAE;IAAc,CAAE,EAC/D;MAAEsE,KAAK,EAAE,4BAA4B;MAAEtE,IAAI,EAAE;IAAuC,CAAE,EACtF;MAAEsE,KAAK,EAAE,+BAA+B;MAAEtE,IAAI,EAAE,kDAAkD,GAAGG,IAAI,CAACc,GAAG,EAAE,GAAG;IAAG,CAAE,EACvH;MAAEqD,KAAK,EAAE,uBAAuB;MAAEtE,IAAI,EAAE,gDAAgD,GAAGG,IAAI,CAACc,GAAG,EAAE,GAAG;IAAG,CAAE,CAC9G;EAQE;EAEHwE,QAAQA,CAAA;IACN,IAAI,CAACnG,WAAW,CAAC2M,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACxK,WAAW,GAAGwK,IAAI;IACzB,CAAC,CAAC;IACF,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAACnC,YAAY,EAAE;EACrB;EAEMkC,kBAAkBA,CAAA;IAAA,IAAAvM,KAAA;IAAA,OAAAC,wJAAA;MACtB,IAAI;QACF,MAAMwM,OAAO,SAASC,SAAS,CAACC,YAAY,CAACC,gBAAgB,EAAE;QAC/D5M,KAAI,CAACiM,SAAS,GAAGQ,OAAO,CAACI,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,CAAC;OACtE,CAAC,OAAOrG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtD1G,KAAI,CAACiM,SAAS,GAAG,KAAK;;IACvB;EACH;EAEM/B,aAAaA,CAAA;IAAA,IAAArI,MAAA;IAAA,OAAA5B,wJAAA;MACjB,IAAI,CAAC4B,MAAI,CAACoK,SAAS,EAAE;QACnBpK,MAAI,CAACoD,QAAQ,CAACsB,IAAI,CAAC,uBAAuB,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACzE;;MAGF,IAAI;QACF3E,MAAI,CAACqK,MAAM,SAASQ,SAAS,CAACC,YAAY,CAACK,YAAY,CAAC;UACtDC,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAa;SACnC,CAAC;QAEFrL,MAAI,CAACoL,KAAK,CAACE,aAAa,CAACC,SAAS,GAAGvL,MAAI,CAACqK,MAAM;QAChDrK,MAAI,CAACoL,KAAK,CAACE,aAAa,CAACE,IAAI,EAAE;QAC/BxL,MAAI,CAACkJ,UAAU,GAAG,IAAI;QAEtB;QACAlJ,MAAI,CAACyL,YAAY,GAAGC,WAAW,CAAC,MAAK;UACnC1L,MAAI,CAAC2L,aAAa,EAAE;QACtB,CAAC,EAAE,GAAG,CAAC;OAER,CAAC,OAAO9G,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9C7E,MAAI,CAACoD,QAAQ,CAACsB,IAAI,CAAC,6BAA6B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;;IAChF;EACH;EAEA6D,YAAYA,CAAA;IACV,IAAI,IAAI,CAAC6B,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACuB,SAAS,EAAE,CAACvG,OAAO,CAACwG,KAAK,IAAIA,KAAK,CAACC,IAAI,EAAE,CAAC;MACtD,IAAI,CAACzB,MAAM,GAAG,IAAI;;IAGpB,IAAI,IAAI,CAACoB,YAAY,EAAE;MACrBM,aAAa,CAAC,IAAI,CAACN,YAAY,CAAC;MAChC,IAAI,CAACA,YAAY,GAAG,IAAI;;IAG1B,IAAI,CAACvC,UAAU,GAAG,KAAK;EACzB;EAEQyC,aAAaA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACP,KAAK,CAACE,aAAa,IAAI,CAAC,IAAI,CAACU,MAAM,CAACV,aAAa,EAAE;IAE7D,MAAMF,KAAK,GAAG,IAAI,CAACA,KAAK,CAACE,aAAa;IACtC,MAAMU,MAAM,GAAG,IAAI,CAACA,MAAM,CAACV,aAAa;IACxC,MAAMW,OAAO,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IAEvC,IAAI,CAACD,OAAO,IAAIb,KAAK,CAACe,UAAU,KAAKf,KAAK,CAACgB,gBAAgB,EAAE;IAE7DJ,MAAM,CAACK,KAAK,GAAGjB,KAAK,CAACkB,UAAU;IAC/BN,MAAM,CAACO,MAAM,GAAGnB,KAAK,CAACoB,WAAW;IACjCP,OAAO,CAACQ,SAAS,CAACrB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEY,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACO,MAAM,CAAC;IAE3D;IACA;IACA,IAAI,CAACG,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB;IACA,IAAIC,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG,EAAE;MAAE;MACzB,MAAMC,QAAQ,GAAG,IAAI,CAACvC,WAAW,CAACqC,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,MAAM,EAAE,GAAG,IAAI,CAACtC,WAAW,CAACyC,MAAM,CAAC,CAAC;MACtF,IAAI,CAACC,aAAa,CAACH,QAAQ,CAACvO,IAAI,CAAC;;EAErC;EAEM0O,aAAaA,CAAC5N,MAAc;IAAA,IAAA6N,MAAA;IAAA,OAAA7O,wJAAA;MAChC6O,MAAI,CAACzE,YAAY,EAAE;MAEnB,IAAI;QACF,MAAM0E,UAAU,GAAGD,MAAI,CAAC/C,SAAS,CAACvL,WAAW,CAACS,MAAM,CAAC;QACrD,IAAI,CAAC8N,UAAU,EAAE;UACfD,MAAI,CAAC7J,QAAQ,CAACsB,IAAI,CAAC,kBAAkB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACpE;;QAGF,MAAMwI,MAAM,SAASF,MAAI,CAAC/C,SAAS,CAACnK,YAAY,CAACmN,UAAU,CAAC;QAC5DD,MAAI,CAACG,0BAA0B,CAACD,MAAM,EAAED,UAAU,CAAC;OAEpD,CAAC,OAAOrI,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDoI,MAAI,CAAC7J,QAAQ,CAACsB,IAAI,CAAC,sCAAsC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;;IACzF;EACH;EAEQyI,0BAA0BA,CAAC/O,UAAe,EAAEe,MAAkB;IACpE,MAAMiE,SAAS,GAAG,IAAI,CAAC8G,MAAM,CAACzF,IAAI,CAACxB,qGAAyB,EAAE;MAC5DmJ,KAAK,EAAE,OAAO;MACd/N,IAAI,EAAE;QACJD,UAAU;QACVe,MAAM;QACNa,WAAW,EAAE,IAAI,CAACA;;KAErB,CAAC;IAEFoD,SAAS,CAACgK,WAAW,EAAE,CAAC7C,SAAS,CAAC2C,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAE;QACV,IAAI,CAAC/J,QAAQ,CAACsB,IAAI,CAAC,+BAA+B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;;IAErF,CAAC,CAAC;EACJ;EAEA;EACAgF,UAAUA,CAACvK,MAAc;IACvB,IAAI,CAAC4N,aAAa,CAAC5N,MAAM,CAAC;EAC5B;EAEA0K,kBAAkBA,CAACwD,KAAa;IAC9B,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;IACvD,OAAOA,MAAM,CAACD,KAAK,GAAGC,MAAM,CAACR,MAAM,CAAC;EACtC;EAEAhD,iBAAiBA,CAACnH,KAAa;IAC7B,IAAIA,KAAK,CAAC7B,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,QAAQ;IAClD,IAAI6B,KAAK,CAAC7B,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,UAAU;IACnD,IAAI6B,KAAK,CAAC7B,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,OAAO;IAChD,IAAI6B,KAAK,CAAC7B,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,cAAc;IACpD,OAAO,SAAS;EAClB;EAEAiJ,aAAaA,CAACpH,KAAa;IACzB,IAAIA,KAAK,CAAC7B,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,UAAU;IAC7C,IAAI6B,KAAK,CAAC7B,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,YAAY;IACjD,IAAI6B,KAAK,CAAC7B,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,WAAW;IAC/C,IAAI6B,KAAK,CAAC7B,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,WAAW;IAC/C,OAAO,kBAAkB;EAC3B;EAEA+H,gBAAgBA,CAAA;IACd,IAAI,CAACK,YAAY,GAAG,CAAC,IAAI,CAACA,YAAY;IACtCrE,OAAO,CAAC0I,GAAG,CAAC,SAAS,IAAI,CAACrE,YAAY,GAAG,QAAQ,GAAG,WAAW,EAAE,CAAC;IAClE;EACF;EAEAH,YAAYA,CAAA;IACVlE,OAAO,CAAC0I,GAAG,CAAC,yBAAyB,CAAC;IACtC;EACF;EAEMlE,aAAaA,CAAA;IAAA,IAAAmE,MAAA;IAAA,OAAArP,wJAAA;MACjB;MACA,MAAMgB,MAAM,GAAGsO,MAAM,CAAC,iCAAiC,CAAC;MACxD,IAAItO,MAAM,EAAE;QACV,MAAMqO,MAAI,CAACT,aAAa,CAAC5N,MAAM,CAAC;;IACjC;EACH;;;uBAjMW6K,kBAAkB,EAAAzI,+DAAA,CAAAE,gEAAA,GAAAF,+DAAA,CAAAI,oEAAA,GAAAJ,+DAAA,CAAA8E,oEAAA,GAAA9E,+DAAA,CAAA+E,+DAAA,GAAA/E,+DAAA,CAAAiF,oEAAA;IAAA;EAAA;;;YAAlBwD,kBAAkB;MAAAtD,SAAA;MAAAiH,SAAA,WAAAC,yBAAA5G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCX3BzF,4DAHJ,aAA+B,aAED,SACtB;UAAAA,oDAAA,sBAAe;UAAAA,0DAAA,EAAK;UACxBA,4DAAA,QAAG;UAAAA,oDAAA,uEAA2D;UAChEA,0DADgE,EAAI,EAC9D;UAwENA,wDArEA,IAAAsM,iCAAA,kBAA8C,IAAAC,iCAAA,kBAqEI;UAqB1CvM,4DAJR,aAAkC,kBACU,uBACvB,sBACC,gBACJ;UAAAA,oDAAA,eAAO;UAAAA,0DAAA,EAAW;UAC5BA,oDAAA,qDACF;UACFA,0DADE,EAAiB,EACD;UAEhBA,4DADF,wBAAkB,SACb;UAAAA,oDAAA,sEAAyD;UAAAA,0DAAA,EAAI;UAChEA,4DAAA,cAA0B;UACxBA,wDAAA,KAAAwM,qCAAA,oBAKkD;UAU1DxM,0DAHM,EAAM,EACW,EACV,EACP;UAOEA,4DAJR,eAAkC,oBACI,uBACjB,sBACC,gBACJ;UAAAA,oDAAA,oBAAY;UAAAA,0DAAA,EAAW;UACjCA,oDAAA,kCACF;UACFA,0DADE,EAAiB,EACD;UAIZA,4DAHN,wBAAkB,eACe,eACC,oBACF;UAAAA,oDAAA,uBAAe;UAAAA,0DAAA,EAAW;UAElDA,4DADF,WAAK,UACC;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAK;UACnBA,4DAAA,SAAG;UAAAA,oDAAA,wDAA2C;UAElDA,0DAFkD,EAAI,EAC9C,EACF;UAGJA,4DADF,eAA8B,oBACF;UAAAA,oDAAA,gBAAQ;UAAAA,0DAAA,EAAW;UAE3CA,4DADF,WAAK,UACC;UAAAA,oDAAA,qBAAa;UAAAA,0DAAA,EAAK;UACtBA,4DAAA,SAAG;UAAAA,oDAAA,oEAA6C;UAEpDA,0DAFoD,EAAI,EAChD,EACF;UAGJA,4DADF,eAA8B,oBACF;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAW;UAExCA,4DADF,WAAK,UACC;UAAAA,oDAAA,iBAAS;UAAAA,0DAAA,EAAK;UAClBA,4DAAA,SAAG;UAAAA,oDAAA,uDAA0C;UAO3DA,0DAP2D,EAAI,EAC7C,EACF,EACF,EACW,EACV,EACP,EACF;;;UAxJyBA,uDAAA,GAAe;UAAfA,wDAAA,SAAA0F,GAAA,CAAAkD,SAAA,CAAe;UAqEZ5I,uDAAA,EAAgB;UAAhBA,wDAAA,UAAA0F,GAAA,CAAAkD,SAAA,CAAgB;UA6Bf5I,uDAAA,IAAgB;UAAhBA,wDAAA,YAAA0F,GAAA,CAAAoD,WAAA,CAAgB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACzGJ;AACM;AACN;AAE/C;AACuD;AACI;AACJ;AACW;AACT;AACE;AACA;AACK;AACc;AAEI;AACqB;AACjD;;;AAwBhD,MAAOwE,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAhBxBb,yDAAY,EACZC,+DAAmB,EACnBC,yDAAY,CAACY,QAAQ,CAACF,+DAAe,CAAC;MAEtC;MACAT,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,6EAAkB,EAClBC,oEAAc,EACdC,sEAAe,EACfC,sEAAe,EACfC,2EAAiB,EACjBC,yFAAwB;IAAA;EAAA;;;sHAGfE,eAAe;IAAAE,YAAA,GApBxB/E,2FAAkB,EAClB/G,gHAAyB;IAAA+L,OAAA,GAGzBhB,yDAAY,EACZC,+DAAmB,EAAAxM,yDAAA;IAGnB;IACA0M,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,6EAAkB,EAClBC,oEAAc,EACdC,sEAAe,EACfC,sEAAe,EACfC,2EAAiB,EACjBC,yFAAwB;EAAA;AAAA;;;;;;;;;;;;;;;ACtCsD;AAE3E,MAAMC,eAAe,GAAW,CACrC;EACEK,IAAI,EAAE,EAAE;EACRC,SAAS,EAAElF,2FAAkBA;CAC9B,CACF", "sources": ["./src/app/core/services/qr.service.ts", "./src/app/features/qr-scanner/components/points-assignment/points-assignment.component.ts", "./src/app/features/qr-scanner/components/points-assignment/points-assignment.component.html", "./src/app/features/qr-scanner/components/qr-scanner/qr-scanner.component.ts", "./src/app/features/qr-scanner/components/qr-scanner/qr-scanner.component.html", "./src/app/features/qr-scanner/qr-scanner.module.ts", "./src/app/features/qr-scanner/qr-scanner.routes.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { QrScanResult, QrCodeData, QrCodeType, User } from '../models';\nimport { UserService } from './user.service';\nimport { AuthService } from './auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class QrService {\n  private scanResultSubject = new BehaviorSubject<QrScanResult | null>(null);\n  public scanResult$ = this.scanResultSubject.asObservable();\n\n  constructor(\n    private userService: UserService,\n    private authService: AuthService\n  ) {}\n\n  // Process scanned QR code\n  async processScanResult(rawData: string, location?: any): Promise<QrScanResult> {\n    const scanResult: QrScanResult = {\n      data: rawData,\n      format: 'QR_CODE',\n      timestamp: new Date(),\n      location\n    };\n\n    this.scanResultSubject.next(scanResult);\n    return scanResult;\n  }\n\n  // Parse QR code data\n  parseQrData(rawData: string): QrCodeData | null {\n    try {\n      // Try to parse as JSON first\n      const parsed = JSON.parse(rawData);\n      if (this.isValidQrCodeData(parsed)) {\n        return parsed as QrCodeData;\n      }\n    } catch {\n      // If not JSON, try to parse as simple formats\n      return this.parseSimpleQrData(rawData);\n    }\n    return null;\n  }\n\n  // Generate QR code data for user transfers\n  generateUserTransferQr(userId: string, points?: number): string {\n    const qrData: QrCodeData = {\n      type: QrCodeType.USER_TRANSFER,\n      userId,\n      points,\n      timestamp: Date.now(),\n      signature: this.generateSignature(userId, points)\n    };\n    return JSON.stringify(qrData);\n  }\n\n  // Generate QR code for validator actions\n  generateValidatorQr(validatorId: string, action: string, points: number): string {\n    const qrData: QrCodeData = {\n      type: QrCodeType.VALIDATOR_ACTION,\n      validatorId,\n      action,\n      points,\n      timestamp: Date.now(),\n      signature: this.generateSignature(validatorId, points, action)\n    };\n    return JSON.stringify(qrData);\n  }\n\n  // Handle different types of QR scans\n  async handleQrScan(qrData: QrCodeData): Promise<any> {\n    const currentUser = this.authService.getCurrentUser();\n    if (!currentUser) {\n      throw new Error('User not authenticated');\n    }\n\n    switch (qrData.type) {\n      case QrCodeType.USER_TRANSFER:\n        return this.handleUserTransfer(qrData, currentUser);\n      \n      case QrCodeType.VALIDATOR_ACTION:\n        return this.handleValidatorAction(qrData, currentUser);\n      \n      case QrCodeType.PARTNER_REWARD:\n        return this.handlePartnerReward(qrData, currentUser);\n      \n      case QrCodeType.SYSTEM_BONUS:\n        return this.handleSystemBonus(qrData, currentUser);\n      \n      default:\n        throw new Error('Unknown QR code type');\n    }\n  }\n\n  private async handleUserTransfer(qrData: QrCodeData, currentUser: User): Promise<any> {\n    if (!qrData.userId) {\n      throw new Error('Invalid user transfer QR code');\n    }\n\n    // For demo purposes, we'll simulate a simple point transfer\n    const points = qrData.points || 1; // Default 1 point if not specified\n    \n    return {\n      type: 'user_transfer',\n      targetUserId: qrData.userId,\n      points,\n      message: `Ready to transfer ${points} point(s) to user`\n    };\n  }\n\n  private async handleValidatorAction(qrData: QrCodeData, currentUser: User): Promise<any> {\n    if (currentUser.role !== 'validator') {\n      throw new Error('Only validators can process validator QR codes');\n    }\n\n    return {\n      type: 'validator_action',\n      action: qrData.action,\n      points: qrData.points || 10,\n      message: `Validation action: ${qrData.action}`\n    };\n  }\n\n  private async handlePartnerReward(qrData: QrCodeData, currentUser: User): Promise<any> {\n    return {\n      type: 'partner_reward',\n      points: qrData.points || 5,\n      message: 'Partner reward scanned'\n    };\n  }\n\n  private async handleSystemBonus(qrData: QrCodeData, currentUser: User): Promise<any> {\n    return {\n      type: 'system_bonus',\n      points: qrData.points || 3,\n      message: 'System bonus activated'\n    };\n  }\n\n  // Validate QR code data structure\n  private isValidQrCodeData(data: any): boolean {\n    return data &&\n           typeof data.type === 'string' &&\n           Object.values(QrCodeType).includes(data.type) &&\n           typeof data.timestamp === 'number';\n  }\n\n  // Parse simple QR data formats (for demo/testing)\n  private parseSimpleQrData(rawData: string): QrCodeData | null {\n    // Handle simple formats like \"user:123\" or \"points:5\"\n    if (rawData.startsWith('user:')) {\n      const userId = rawData.substring(5);\n      return {\n        type: QrCodeType.USER_TRANSFER,\n        userId,\n        timestamp: Date.now()\n      };\n    }\n\n    if (rawData.startsWith('validator:')) {\n      const parts = rawData.substring(10).split(':');\n      return {\n        type: QrCodeType.VALIDATOR_ACTION,\n        validatorId: parts[0],\n        action: parts[1] || 'community_service',\n        points: parseInt(parts[2]) || 10,\n        timestamp: Date.now()\n      };\n    }\n\n    // Default: treat as user ID\n    return {\n      type: QrCodeType.USER_TRANSFER,\n      userId: rawData,\n      timestamp: Date.now()\n    };\n  }\n\n  // Generate simple signature for QR codes (for demo purposes)\n  private generateSignature(userId: string, points?: number, action?: string): string {\n    const data = `${userId}-${points || 0}-${action || ''}-${Date.now()}`;\n    return btoa(data).substring(0, 16);\n  }\n\n  // Generate sample QR codes for testing\n  generateSampleQrCodes(): string[] {\n    return [\n      this.generateUserTransferQr('user123', 1),\n      this.generateUserTransferQr('user456', 3),\n      this.generateValidatorQr('validator789', 'community_service', 10),\n      JSON.stringify({\n        type: QrCodeType.PARTNER_REWARD,\n        points: 5,\n        timestamp: Date.now()\n      }),\n      JSON.stringify({\n        type: QrCodeType.SYSTEM_BONUS,\n        points: 3,\n        timestamp: Date.now()\n      })\n    ];\n  }\n\n  // Clear scan result\n  clearScanResult(): void {\n    this.scanResultSubject.next(null);\n  }\n}\n", "import { Component, Inject, OnInit } from '@angular/core';\nimport { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';\nimport { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { UserService } from '../../../../core/services/user.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { QrCodeData, User, TransactionType } from '../../../../core/models';\n\ninterface DialogData {\n  scanResult: any;\n  qrData: QrCodeData;\n  currentUser: User;\n}\n\n@Component({\n  selector: 'app-points-assignment',\n  templateUrl: './points-assignment.component.html',\n  styleUrls: ['./points-assignment.component.css']\n})\nexport class PointsAssignmentComponent implements OnInit {\n  assignmentForm: FormGroup;\n  isLoading = false;\n  \n  pointsOptions = [\n    { value: 1, label: '1 point', description: 'Action simple' },\n    { value: 3, label: '3 points', description: 'Action modérée' },\n    { value: 5, label: '5 points', description: 'Action importante' },\n    { value: 10, label: '10 points', description: 'Action majeure' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private userService: UserService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n    public dialogRef: MatDialogRef<PointsAssignmentComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: DialogData\n  ) {\n    this.assignmentForm = this.fb.group({\n      points: [this.getDefaultPoints(), [Validators.required, Validators.min(1)]],\n      description: [this.getDefaultDescription(), Validators.required],\n      targetUserId: [this.getTargetUserId()]\n    });\n  }\n\n  ngOnInit(): void {\n    // Auto-fill form based on QR scan result\n    this.prefillForm();\n  }\n\n  private getDefaultPoints(): number {\n    if (this.data.scanResult?.points) {\n      return this.data.scanResult.points;\n    }\n    \n    switch (this.data.qrData?.type) {\n      case 'user_transfer':\n        return 1;\n      case 'validator_action':\n        return 10;\n      case 'partner_reward':\n        return 5;\n      case 'system_bonus':\n        return 3;\n      default:\n        return 1;\n    }\n  }\n\n  private getDefaultDescription(): string {\n    if (this.data.scanResult?.message) {\n      return this.data.scanResult.message;\n    }\n    \n    switch (this.data.qrData?.type) {\n      case 'user_transfer':\n        return 'Transfert de points entre utilisateurs';\n      case 'validator_action':\n        return `Action validée: ${this.data.qrData.action || 'activité communautaire'}`;\n      case 'partner_reward':\n        return 'Récompense partenaire scannée';\n      case 'system_bonus':\n        return 'Bonus système activé';\n      default:\n        return 'Points gagnés via QR code';\n    }\n  }\n\n  private getTargetUserId(): string {\n    return this.data.qrData?.userId || this.data.currentUser?.uid || '';\n  }\n\n  private prefillForm(): void {\n    // Additional logic to prefill form based on scan type\n    if (this.data.qrData?.type === 'user_transfer' && this.data.qrData.userId) {\n      // For user transfers, we might want to show the target user info\n      this.assignmentForm.patchValue({\n        description: `Transfert vers utilisateur ${this.data.qrData.userId}`\n      });\n    }\n  }\n\n  async onSubmit(): Promise<void> {\n    if (this.assignmentForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.isLoading = true;\n    const formValue = this.assignmentForm.value;\n\n    try {\n      const currentUser = this.authService.getCurrentUser();\n      if (!currentUser) {\n        throw new Error('Utilisateur non connecté');\n      }\n\n      // Determine transaction type based on QR code type\n      const transactionType = this.getTransactionType();\n      \n      // Add points to the target user (usually current user)\n      const targetUserId = formValue.targetUserId || currentUser.uid;\n      \n      await this.userService.addPointsToUser(\n        targetUserId,\n        formValue.points,\n        transactionType,\n        formValue.description,\n        this.data.qrData?.validatorId || currentUser.uid\n      );\n\n      this.snackBar.open(\n        `${formValue.points} point(s) attribué(s) avec succès!`, \n        'Fermer', \n        { duration: 3000 }\n      );\n\n      this.dialogRef.close(true);\n\n    } catch (error: any) {\n      console.error('Error assigning points:', error);\n      this.snackBar.open(\n        'Erreur lors de l\\'attribution des points', \n        'Fermer', \n        { duration: 5000 }\n      );\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  private getTransactionType(): TransactionType {\n    switch (this.data.qrData?.type) {\n      case 'validator_action':\n        return TransactionType.VALIDATED;\n      case 'partner_reward':\n        return TransactionType.EARNED;\n      case 'system_bonus':\n        return TransactionType.BONUS;\n      case 'user_transfer':\n        return TransactionType.TRANSFERRED;\n      default:\n        return TransactionType.EARNED;\n    }\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.assignmentForm.controls).forEach(key => {\n      const control = this.assignmentForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.assignmentForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) {\n        return 'Champ requis';\n      }\n      if (field.errors['min']) {\n        return 'Minimum 1 point';\n      }\n    }\n    return '';\n  }\n\n  onCancel(): void {\n    this.dialogRef.close(false);\n  }\n\n  getScanTypeLabel(): string {\n    const typeLabels: { [key: string]: string } = {\n      'user_transfer': 'Transfert utilisateur',\n      'validator_action': 'Action validateur',\n      'partner_reward': 'Récompense partenaire',\n      'system_bonus': 'Bonus système'\n    };\n    return typeLabels[this.data.qrData?.type || ''] || 'Scan QR';\n  }\n\n  getScanTypeIcon(): string {\n    const typeIcons: { [key: string]: string } = {\n      'user_transfer': 'swap_horiz',\n      'validator_action': 'verified',\n      'partner_reward': 'card_giftcard',\n      'system_bonus': 'star'\n    };\n    return typeIcons[this.data.qrData?.type || ''] || 'qr_code';\n  }\n}\n", "<div class=\"assignment-dialog\">\n  <div class=\"dialog-header\">\n    <h2 mat-dialog-title>\n      <mat-icon [color]=\"'primary'\">{{ getScanTypeIcon() }}</mat-icon>\n      Attribution de points\n    </h2>\n    <p class=\"scan-type\">{{ getScanTypeLabel() }}</p>\n  </div>\n\n  <mat-dialog-content>\n    <form [formGroup]=\"assignmentForm\" class=\"assignment-form\">\n      <!-- Points selection -->\n      <mat-form-field appearance=\"outline\" class=\"full-width\">\n        <mat-label>Nombre de points</mat-label>\n        <mat-select formControlName=\"points\">\n          <mat-option *ngFor=\"let option of pointsOptions\" [value]=\"option.value\">\n            <div class=\"points-option\">\n              <span class=\"points-value\">{{ option.label }}</span>\n              <span class=\"points-description\">{{ option.description }}</span>\n            </div>\n          </mat-option>\n        </mat-select>\n        <mat-error>{{ getFieldError('points') }}</mat-error>\n      </mat-form-field>\n\n      <!-- Description -->\n      <mat-form-field appearance=\"outline\" class=\"full-width\">\n        <mat-label>Description</mat-label>\n        <textarea matInput \n                  formControlName=\"description\"\n                  rows=\"3\"\n                  placeholder=\"Décrivez l'action ou la raison de l'attribution\">\n        </textarea>\n        <mat-error>{{ getFieldError('description') }}</mat-error>\n      </mat-form-field>\n\n      <!-- Target user (if applicable) -->\n      <mat-form-field appearance=\"outline\" class=\"full-width\" \n                      *ngIf=\"data.qrData?.type === 'user_transfer'\">\n        <mat-label>Utilisateur cible</mat-label>\n        <input matInput \n               formControlName=\"targetUserId\"\n               readonly\n               placeholder=\"ID de l'utilisateur\">\n        <mat-icon matSuffix>person</mat-icon>\n      </mat-form-field>\n\n      <!-- Scan details -->\n      <div class=\"scan-details\">\n        <h4>Détails du scan</h4>\n        <div class=\"detail-item\">\n          <mat-icon>qr_code</mat-icon>\n          <span>Type: {{ getScanTypeLabel() }}</span>\n        </div>\n        <div class=\"detail-item\" *ngIf=\"data.qrData?.timestamp\">\n          <mat-icon>schedule</mat-icon>\n          <span>Scanné: {{ data.qrData.timestamp | date:'short' }}</span>\n        </div>\n        <div class=\"detail-item\" *ngIf=\"data.qrData?.action\">\n          <mat-icon>task_alt</mat-icon>\n          <span>Action: {{ data.qrData.action }}</span>\n        </div>\n      </div>\n\n      <!-- Points preview -->\n      <div class=\"points-preview\">\n        <div class=\"preview-content\">\n          <mat-icon class=\"preview-icon\">stars</mat-icon>\n          <div class=\"preview-text\">\n            <h3>{{ assignmentForm.get('points')?.value || 0 }} point(s)</h3>\n            <p>seront attribués à {{ data.currentUser?.name || 'utilisateur' }}</p>\n          </div>\n        </div>\n      </div>\n    </form>\n  </mat-dialog-content>\n\n  <mat-dialog-actions align=\"end\">\n    <button mat-button (click)=\"onCancel()\" [disabled]=\"isLoading\">\n      Annuler\n    </button>\n    <button mat-raised-button \n            color=\"primary\" \n            (click)=\"onSubmit()\"\n            [disabled]=\"isLoading || assignmentForm.invalid\">\n      <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n      <span *ngIf=\"!isLoading\">Attribuer les points</span>\n    </button>\n  </mat-dialog-actions>\n</div>\n", "import { Compo<PERSON>, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON>roy, ViewChild, ElementRef } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { QrService } from '../../../../core/services/qr.service';\nimport { UserService } from '../../../../core/services/user.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { PointsAssignmentComponent } from '../points-assignment/points-assignment.component';\nimport { QrCodeData, User } from '../../../../core/models';\n\n@Component({\n  selector: 'app-qr-scanner',\n  templateUrl: './qr-scanner.component.html',\n  styleUrls: ['./qr-scanner.component.css']\n})\nexport class QrScannerComponent implements OnInit, On<PERSON><PERSON>roy {\n  @ViewChild('video', { static: false }) video!: ElementRef<HTMLVideoElement>;\n  @ViewChild('canvas', { static: false }) canvas!: ElementRef<HTMLCanvasElement>;\n\n  isScanning = false;\n  hasCamera = false;\n  flashlightOn = false;\n  currentUser: User | null = null;\n  stream: MediaStream | null = null;\n  scanInterval: any;\n\n  // Demo QR codes for testing\n  demoQrCodes = [\n    { label: 'Transfert utilisateur (1 pt)', data: 'user:demo123' },\n    { label: 'Action validateur (10 pts)', data: 'validator:val456:community_service:10' },\n    { label: 'Récompense partenaire (5 pts)', data: '{\"type\":\"partner_reward\",\"points\":5,\"timestamp\":' + Date.now() + '}' },\n    { label: 'Bonus système (3 pts)', data: '{\"type\":\"system_bonus\",\"points\":3,\"timestamp\":' + Date.now() + '}' }\n  ];\n\n  constructor(\n    private qrService: QrService,\n    private userService: UserService,\n    private authService: AuthService,\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.checkCameraSupport();\n  }\n\n  ngOnDestroy(): void {\n    this.stopScanning();\n  }\n\n  async checkCameraSupport(): Promise<void> {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      this.hasCamera = devices.some(device => device.kind === 'videoinput');\n    } catch (error) {\n      console.error('Error checking camera support:', error);\n      this.hasCamera = false;\n    }\n  }\n\n  async startScanning(): Promise<void> {\n    if (!this.hasCamera) {\n      this.snackBar.open('Caméra non disponible', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    try {\n      this.stream = await navigator.mediaDevices.getUserMedia({\n        video: { facingMode: 'environment' }\n      });\n      \n      this.video.nativeElement.srcObject = this.stream;\n      this.video.nativeElement.play();\n      this.isScanning = true;\n\n      // Start scanning for QR codes\n      this.scanInterval = setInterval(() => {\n        this.scanForQrCode();\n      }, 500);\n\n    } catch (error) {\n      console.error('Error starting camera:', error);\n      this.snackBar.open('Erreur d\\'accès à la caméra', 'Fermer', { duration: 3000 });\n    }\n  }\n\n  stopScanning(): void {\n    if (this.stream) {\n      this.stream.getTracks().forEach(track => track.stop());\n      this.stream = null;\n    }\n    \n    if (this.scanInterval) {\n      clearInterval(this.scanInterval);\n      this.scanInterval = null;\n    }\n    \n    this.isScanning = false;\n  }\n\n  private scanForQrCode(): void {\n    if (!this.video.nativeElement || !this.canvas.nativeElement) return;\n\n    const video = this.video.nativeElement;\n    const canvas = this.canvas.nativeElement;\n    const context = canvas.getContext('2d');\n\n    if (!context || video.readyState !== video.HAVE_ENOUGH_DATA) return;\n\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    context.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // In a real implementation, you would use a QR code library like @zxing/library\n    // For demo purposes, we'll simulate QR detection\n    this.simulateQrDetection();\n  }\n\n  private simulateQrDetection(): void {\n    // Simulate random QR code detection for demo\n    if (Math.random() < 0.1) { // 10% chance per scan\n      const randomQr = this.demoQrCodes[Math.floor(Math.random() * this.demoQrCodes.length)];\n      this.processQrCode(randomQr.data);\n    }\n  }\n\n  async processQrCode(qrData: string): Promise<void> {\n    this.stopScanning();\n\n    try {\n      const parsedData = this.qrService.parseQrData(qrData);\n      if (!parsedData) {\n        this.snackBar.open('Code QR invalide', 'Fermer', { duration: 3000 });\n        return;\n      }\n\n      const result = await this.qrService.handleQrScan(parsedData);\n      this.openPointsAssignmentDialog(result, parsedData);\n\n    } catch (error) {\n      console.error('Error processing QR code:', error);\n      this.snackBar.open('Erreur lors du traitement du QR code', 'Fermer', { duration: 3000 });\n    }\n  }\n\n  private openPointsAssignmentDialog(scanResult: any, qrData: QrCodeData): void {\n    const dialogRef = this.dialog.open(PointsAssignmentComponent, {\n      width: '400px',\n      data: {\n        scanResult,\n        qrData,\n        currentUser: this.currentUser\n      }\n    });\n\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.snackBar.open('Points attribués avec succès!', 'Fermer', { duration: 3000 });\n      }\n    });\n  }\n\n  // Demo methods for testing\n  testQrCode(qrData: string): void {\n    this.processQrCode(qrData);\n  }\n\n  getDemoButtonColor(index: number): string {\n    const colors = ['primary', 'accent', 'warn', 'primary'];\n    return colors[index % colors.length];\n  }\n\n  getDemoButtonIcon(label: string): string {\n    if (label.includes('utilisateur')) return 'person';\n    if (label.includes('validateur')) return 'verified';\n    if (label.includes('partenaire')) return 'store';\n    if (label.includes('système')) return 'auto_awesome';\n    return 'qr_code';\n  }\n\n  getDemoPoints(label: string): string {\n    if (label.includes('1 pt')) return '+1 point';\n    if (label.includes('10 pts')) return '+10 points';\n    if (label.includes('5 pts')) return '+5 points';\n    if (label.includes('3 pts')) return '+3 points';\n    return 'Points variables';\n  }\n\n  toggleFlashlight(): void {\n    this.flashlightOn = !this.flashlightOn;\n    console.log(`Flash ${this.flashlightOn ? 'activé' : 'désactivé'}`);\n    // In a real implementation, this would control the camera flash\n  }\n\n  switchCamera(): void {\n    console.log('Changement de caméra...');\n    // In a real implementation, this would switch between front/back camera\n  }\n\n  async manualQrInput(): Promise<void> {\n    // In a real app, you might open a dialog for manual input\n    const qrData = prompt('Entrez le code QR manuellement:');\n    if (qrData) {\n      await this.processQrCode(qrData);\n    }\n  }\n}\n", "<div class=\"scanner-container\">\n  <!-- Scanner header -->\n  <div class=\"scanner-header\">\n    <h1>Scanner QR Code</h1>\n    <p>Pointez votre caméra vers un code QR pour gagner des points</p>\n  </div>\n\n  <!-- Camera section -->\n  <div class=\"camera-section\" *ngIf=\"hasCamera\">\n    <mat-card class=\"camera-card\">\n      <mat-card-content>\n        <div class=\"camera-container\">\n          <video #video \n                 class=\"camera-video\" \n                 [class.active]=\"isScanning\"\n                 autoplay \n                 playsinline>\n          </video>\n          <canvas #canvas class=\"camera-canvas\" style=\"display: none;\"></canvas>\n          \n          <!-- Scanner overlay -->\n          <div class=\"scanner-overlay\" *ngIf=\"isScanning\">\n            <div class=\"scanner-frame\">\n              <div class=\"scanner-corners\">\n                <div class=\"corner top-left\"></div>\n                <div class=\"corner top-right\"></div>\n                <div class=\"corner bottom-left\"></div>\n                <div class=\"corner bottom-right\"></div>\n              </div>\n            </div>\n            <p class=\"scanner-instruction\">Placez le QR code dans le cadre</p>\n          </div>\n        </div>\n\n        <!-- Camera controls -->\n        <div class=\"camera-controls\">\n          <button mat-raised-button\n                  color=\"primary\"\n                  (click)=\"startScanning()\"\n                  *ngIf=\"!isScanning\"\n                  class=\"scan-button enhanced-btn\">\n            <mat-icon>qr_code_scanner</mat-icon>\n            Commencer le scan\n          </button>\n\n          <button mat-raised-button\n                  color=\"warn\"\n                  (click)=\"stopScanning()\"\n                  *ngIf=\"isScanning\"\n                  class=\"stop-button enhanced-btn\">\n            <mat-icon>stop</mat-icon>\n            Arrêter le scan\n          </button>\n\n          <button mat-stroked-button\n                  color=\"accent\"\n                  (click)=\"toggleFlashlight()\"\n                  [disabled]=\"!isScanning\"\n                  class=\"flash-button enhanced-btn\">\n            <mat-icon>{{ flashlightOn ? 'flash_off' : 'flash_on' }}</mat-icon>\n            {{ flashlightOn ? 'Flash Off' : 'Flash On' }}\n          </button>\n\n          <button mat-stroked-button\n                  color=\"primary\"\n                  (click)=\"switchCamera()\"\n                  [disabled]=\"!isScanning\"\n                  class=\"camera-button enhanced-btn\">\n            <mat-icon>flip_camera_android</mat-icon>\n            Changer caméra\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- No camera fallback -->\n  <div class=\"no-camera-section\" *ngIf=\"!hasCamera\">\n    <mat-card class=\"no-camera-card\">\n      <mat-card-content>\n        <div class=\"no-camera-content\">\n          <mat-icon class=\"no-camera-icon\">camera_alt</mat-icon>\n          <h3>Caméra non disponible</h3>\n          <p>Votre appareil ne dispose pas de caméra ou l'accès a été refusé.</p>\n          <button mat-raised-button color=\"primary\" (click)=\"manualQrInput()\">\n            <mat-icon>keyboard</mat-icon>\n            Saisie manuelle\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Demo section -->\n  <div class=\"demo-section fade-in\">\n    <mat-card class=\"demo-card floating-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>science</mat-icon>\n          🎯 Codes QR de démonstration\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <p>Testez l'application avec ces codes QR de démonstration :</p>\n        <div class=\"demo-buttons\">\n          <button mat-raised-button\n                  *ngFor=\"let demo of demoQrCodes; let i = index\"\n                  (click)=\"testQrCode(demo.data)\"\n                  [color]=\"getDemoButtonColor(i)\"\n                  class=\"demo-qr-button\"\n                  [style.animation-delay]=\"(i * 0.1) + 's'\">\n            <mat-icon>{{ getDemoButtonIcon(demo.label) }}</mat-icon>\n            <div class=\"demo-button-content\">\n              <span class=\"demo-title\">{{ demo.label }}</span>\n              <small class=\"demo-points\">{{ getDemoPoints(demo.label) }}</small>\n            </div>\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Instructions -->\n  <div class=\"instructions-section\">\n    <mat-card class=\"instructions-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>help_outline</mat-icon>\n          Comment ça marche ?\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"instructions-list\">\n          <div class=\"instruction-item\">\n            <mat-icon color=\"primary\">qr_code_scanner</mat-icon>\n            <div>\n              <h4>1. Scanner</h4>\n              <p>Pointez votre caméra vers un code QR valide</p>\n            </div>\n          </div>\n          \n          <div class=\"instruction-item\">\n            <mat-icon color=\"primary\">verified</mat-icon>\n            <div>\n              <h4>2. Validation</h4>\n              <p>Le code est automatiquement vérifié et traité</p>\n            </div>\n          </div>\n          \n          <div class=\"instruction-item\">\n            <mat-icon color=\"primary\">stars</mat-icon>\n            <div>\n              <h4>3. Points</h4>\n              <p>Gagnez des points selon l'action effectuée</p>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\n\nimport { QrScannerComponent } from './components/qr-scanner/qr-scanner.component';\nimport { PointsAssignmentComponent } from './components/points-assignment/points-assignment.component';\nimport { qrScannerRoutes } from './qr-scanner.routes';\n\n@NgModule({\n  declarations: [\n    QrScannerComponent,\n    PointsAssignmentComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule.forChild(qrScannerRoutes),\n    \n    // Material modules\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule\n  ]\n})\nexport class QrScannerModule { }\n", "import { Routes } from '@angular/router';\nimport { QrScannerComponent } from './components/qr-scanner/qr-scanner.component';\n\nexport const qrScannerRoutes: Routes = [\n  {\n    path: '',\n    component: QrScannerComponent\n  }\n];\n"], "names": ["BehaviorSubject", "QrCodeType", "QrService", "constructor", "userService", "authService", "scanResultSubject", "scanResult$", "asObservable", "processScanResult", "rawData", "location", "_this", "_asyncToGenerator", "scanResult", "data", "format", "timestamp", "Date", "next", "parseQrData", "parsed", "JSON", "parse", "isValidQrCodeData", "parseSimpleQrData", "generateUserTransferQr", "userId", "points", "qrData", "type", "USER_TRANSFER", "now", "signature", "generateSignature", "stringify", "generateValidatorQr", "validatorId", "action", "VALIDATOR_ACTION", "handleQrScan", "_this2", "currentUser", "getCurrentUser", "Error", "handleUserTransfer", "handleValidatorAction", "PARTNER_REWARD", "handlePartnerReward", "SYSTEM_BONUS", "handleSystemBonus", "targetUserId", "message", "role", "Object", "values", "includes", "startsWith", "substring", "parts", "split", "parseInt", "btoa", "generateSampleQrCodes", "clearScanResult", "i0", "ɵɵinject", "i1", "UserService", "i2", "AuthService", "factory", "ɵfac", "providedIn", "Validators", "MAT_DIALOG_DATA", "TransactionType", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r1", "value", "ɵɵadvance", "ɵɵtextInterpolate", "label", "description", "ɵɵelement", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r1", "PointsAssignmentComponent", "fb", "snackBar", "dialogRef", "isLoading", "pointsOptions", "assignmentForm", "group", "getDefaultPoints", "required", "min", "getDefaultDescription", "getTargetUserId", "ngOnInit", "prefillForm", "uid", "patchValue", "onSubmit", "invalid", "markFormGroupTouched", "formValue", "transactionType", "getTransactionType", "addPointsToUser", "open", "duration", "close", "error", "console", "VALIDATED", "EARNED", "BONUS", "TRANSFERRED", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "getFieldError", "fieldName", "field", "errors", "touched", "onCancel", "getScanTypeLabel", "typeLabels", "getScanTypeIcon", "typeIcons", "ɵɵdirectiveInject", "FormBuilder", "i3", "i4", "MatSnackBar", "i5", "MatDialogRef", "selectors", "decls", "vars", "consts", "template", "PointsAssignmentComponent_Template", "rf", "ctx", "ɵɵtemplate", "PointsAssignmentComponent_mat_option_14_Template", "PointsAssignmentComponent_mat_form_field_24_Template", "PointsAssignmentComponent_div_33_Template", "PointsAssignmentComponent_div_34_Template", "ɵɵlistener", "PointsAssignmentComponent_Template_button_click_45_listener", "PointsAssignmentComponent_Template_button_click_47_listener", "PointsAssignmentComponent_mat_spinner_48_Template", "PointsAssignmentComponent_span_49_Template", "tmp_11_0", "name", "QrScannerComponent_div_6_button_10_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "startScanning", "QrScannerComponent_div_6_button_11_Template_button_click_0_listener", "_r4", "stopScanning", "QrScannerComponent_div_6_div_8_Template", "QrScannerComponent_div_6_button_10_Template", "QrScannerComponent_div_6_button_11_Template", "QrScannerComponent_div_6_Template_button_click_12_listener", "_r1", "toggleFlashlight", "QrScannerComponent_div_6_Template_button_click_16_listener", "switchCamera", "ɵɵclassProp", "isScanning", "flashlightOn", "QrScannerComponent_div_7_Template_button_click_10_listener", "_r5", "manualQrInput", "QrScannerComponent_button_19_Template_button_click_0_listener", "demo_r7", "_r6", "$implicit", "testQrCode", "ɵɵstyleProp", "i_r8", "getDemoButtonColor", "getDemoButtonIcon", "getDemoPoints", "QrScannerComponent", "qrService", "dialog", "hasCamera", "stream", "demoQrCodes", "currentUser$", "subscribe", "user", "checkCameraSupport", "ngOnDestroy", "devices", "navigator", "mediaDevices", "enumerateDevices", "some", "device", "kind", "getUserMedia", "video", "facingMode", "nativeElement", "srcObject", "play", "scanInterval", "setInterval", "scanForQrCode", "getTracks", "track", "stop", "clearInterval", "canvas", "context", "getContext", "readyState", "HAVE_ENOUGH_DATA", "width", "videoWidth", "height", "videoHeight", "drawImage", "simulateQrDetection", "Math", "random", "randomQr", "floor", "length", "processQrCode", "_this3", "parsedData", "result", "openPointsAssignmentDialog", "afterClosed", "index", "colors", "log", "_this4", "prompt", "MatDialog", "viewQuery", "QrScannerComponent_Query", "QrScannerComponent_div_6_Template", "QrScannerComponent_div_7_Template", "QrScannerComponent_button_19_Template", "CommonModule", "ReactiveFormsModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatDialogModule", "MatSnackBarModule", "MatProgressSpinnerModule", "qrScannerRoutes", "QrScannerModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "path", "component"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}