{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatListModule } from '@angular/material/list';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { ProfileComponent } from './components/profile/profile.component';\n// import { ProfileEditComponent } from './components/profile-edit/profile-edit.component';\n// import { PointsHistoryComponent } from './components/points-history/points-history.component';\nimport { profileRoutes } from './profile.routes';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class ProfileModule {\n  static {\n    this.ɵfac = function ProfileModule_Factory(t) {\n      return new (t || ProfileModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ProfileModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, RouterModule.forChild(profileRoutes),\n      // Material modules\n      MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatTabsModule, MatListModule, MatDividerModule, MatChipsModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ProfileModule, {\n    declarations: [ProfileComponent],\n    imports: [CommonModule, ReactiveFormsModule, i1.RouterModule,\n    // Material modules\n    MatCardModule, MatButtonModule, MatIconModule, MatFormFieldModule, MatInputModule, MatSelectModule, MatTabsModule, MatListModule, MatDividerModule, MatChipsModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatTabsModule", "MatListModule", "MatDividerModule", "MatChipsModule", "ProfileComponent", "profileRoutes", "ProfileModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\profile\\profile.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatListModule } from '@angular/material/list';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatChipsModule } from '@angular/material/chips';\n\nimport { ProfileComponent } from './components/profile/profile.component';\n// import { ProfileEditComponent } from './components/profile-edit/profile-edit.component';\n// import { PointsHistoryComponent } from './components/points-history/points-history.component';\nimport { profileRoutes } from './profile.routes';\n\n@NgModule({\n  declarations: [\n    ProfileComponent,\n    // ProfileEditComponent,\n    // PointsHistoryComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule.forChild(profileRoutes),\n    \n    // Material modules\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatTabsModule,\n    MatListModule,\n    MatDividerModule,\n    MatChipsModule\n  ]\n})\nexport class ProfileModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,cAAc,QAAQ,yBAAyB;AAExD,SAASC,gBAAgB,QAAQ,wCAAwC;AACzE;AACA;AACA,SAASC,aAAa,QAAQ,kBAAkB;;;AA0BhD,OAAM,MAAOC,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBAjBtBf,YAAY,EACZC,mBAAmB,EACnBC,YAAY,CAACc,QAAQ,CAACF,aAAa,CAAC;MAEpC;MACAX,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,cAAc;IAAA;EAAA;;;2EAGLG,aAAa;IAAAE,YAAA,GAtBtBJ,gBAAgB;IAAAK,OAAA,GAKhBlB,YAAY,EACZC,mBAAmB,EAAAkB,EAAA,CAAAjB,YAAA;IAGnB;IACAC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,gBAAgB,EAChBC,cAAc;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}