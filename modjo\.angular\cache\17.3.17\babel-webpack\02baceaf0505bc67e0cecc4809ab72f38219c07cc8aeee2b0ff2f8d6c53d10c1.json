{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { from, map } from 'rxjs';\nimport { TransactionType } from '../models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/fire/compat/firestore\";\nexport class UserService {\n  constructor(firestore) {\n    this.firestore = firestore;\n  }\n  getUserById(uid) {\n    return from(getDoc(doc(this.firestore, 'users', uid))).pipe(map(docSnap => docSnap.exists() ? docSnap.data() : null));\n  }\n  updateUser(uid, updates) {\n    const updateData = {\n      ...updates,\n      updatedAt: new Date()\n    };\n    return from(updateDoc(doc(this.firestore, 'users', uid), updateData));\n  }\n  getUsersByCity(city) {\n    const q = query(this.usersCollection, where('city', '==', city), where('isActive', '==', true), orderBy('points', 'desc'));\n    return from(getDocs(q)).pipe(map(snapshot => snapshot.docs.map(doc => doc.data())));\n  }\n  getTopUsers(limitCount = 10) {\n    const q = query(this.usersCollection, where('isActive', '==', true), orderBy('points', 'desc'), limit(limitCount));\n    return from(getDocs(q)).pipe(map(snapshot => snapshot.docs.map(doc => doc.data())));\n  }\n  getUserTransactions(uid, limitCount = 50) {\n    const q = query(this.transactionsCollection, where('toUserId', '==', uid), orderBy('timestamp', 'desc'), limit(limitCount));\n    return from(getDocs(q)).pipe(map(snapshot => snapshot.docs.map(doc => doc.data())));\n  }\n  addPointsToUser(uid, points, type, description, fromUserId) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Get current user data\n        const userDoc = yield getDoc(doc(_this.firestore, 'users', uid));\n        if (!userDoc.exists()) {\n          throw new Error('User not found');\n        }\n        const userData = userDoc.data();\n        const newPoints = userData.points + points;\n        // Create transaction record\n        const transaction = {\n          id: _this.generateTransactionId(),\n          fromUserId,\n          toUserId: uid,\n          points,\n          type,\n          description,\n          timestamp: new Date()\n        };\n        // Update user points and add transaction to history\n        const updatedHistory = [...(userData.history || []), transaction];\n        yield updateDoc(doc(_this.firestore, 'users', uid), {\n          points: newPoints,\n          history: updatedHistory,\n          updatedAt: new Date()\n        });\n        // Also store transaction in separate collection for better querying\n        yield updateDoc(doc(_this.firestore, 'transactions', transaction.id), transaction);\n      } catch (error) {\n        console.error('Error adding points:', error);\n        throw error;\n      }\n    })();\n  }\n  transferPoints(fromUid, toUid, points, description) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      try {\n        // Get both users\n        const [fromUserDoc, toUserDoc] = yield Promise.all([getDoc(doc(_this2.firestore, 'users', fromUid)), getDoc(doc(_this2.firestore, 'users', toUid))]);\n        if (!fromUserDoc.exists() || !toUserDoc.exists()) {\n          throw new Error('One or both users not found');\n        }\n        const fromUser = fromUserDoc.data();\n        const toUser = toUserDoc.data();\n        if (fromUser.points < points) {\n          throw new Error('Insufficient points');\n        }\n        // Create transactions\n        const transactionId = _this2.generateTransactionId();\n        const timestamp = new Date();\n        const fromTransaction = {\n          id: transactionId + '_from',\n          fromUserId: fromUid,\n          toUserId: toUid,\n          points: -points,\n          type: TransactionType.TRANSFERRED,\n          description: `Transferred to ${toUser.name}: ${description}`,\n          timestamp\n        };\n        const toTransaction = {\n          id: transactionId + '_to',\n          fromUserId: fromUid,\n          toUserId: toUid,\n          points: points,\n          type: TransactionType.TRANSFERRED,\n          description: `Received from ${fromUser.name}: ${description}`,\n          timestamp\n        };\n        // Update both users\n        yield Promise.all([updateDoc(doc(_this2.firestore, 'users', fromUid), {\n          points: fromUser.points - points,\n          history: [...(fromUser.history || []), fromTransaction],\n          updatedAt: timestamp\n        }), updateDoc(doc(_this2.firestore, 'users', toUid), {\n          points: toUser.points + points,\n          history: [...(toUser.history || []), toTransaction],\n          updatedAt: timestamp\n        })]);\n      } catch (error) {\n        console.error('Error transferring points:', error);\n        throw error;\n      }\n    })();\n  }\n  generateTransactionId() {\n    return Date.now().toString() + Math.random().toString(36).substr(2, 9);\n  }\n  // Get user statistics\n  getUserStats(uid) {\n    return this.getUserById(uid).pipe(map(user => {\n      if (!user) return null;\n      const history = user.history || [];\n      const earned = history.filter(t => t.points > 0).reduce((sum, t) => sum + t.points, 0);\n      const spent = history.filter(t => t.points < 0).reduce((sum, t) => sum + Math.abs(t.points), 0);\n      return {\n        totalPoints: user.points,\n        totalEarned: earned,\n        totalSpent: spent,\n        transactionCount: history.length,\n        joinDate: user.createdAt,\n        lastActivity: user.updatedAt\n      };\n    }));\n  }\n  static {\n    this.ɵfac = function UserService_Factory(t) {\n      return new (t || UserService)(i0.ɵɵinject(i1.AngularFirestore));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserService,\n      factory: UserService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["from", "map", "TransactionType", "UserService", "constructor", "firestore", "getUserById", "uid", "getDoc", "doc", "pipe", "docSnap", "exists", "data", "updateUser", "updates", "updateData", "updatedAt", "Date", "updateDoc", "getUsersByCity", "city", "q", "query", "usersCollection", "where", "orderBy", "getDocs", "snapshot", "docs", "getTopUsers", "limitCount", "limit", "getUserTransactions", "transactionsCollection", "addPointsToUser", "points", "type", "description", "fromUserId", "_this", "_asyncToGenerator", "userDoc", "Error", "userData", "newPoints", "transaction", "id", "generateTransactionId", "toUserId", "timestamp", "updatedHistory", "history", "error", "console", "transferPoints", "fromUid", "toUid", "_this2", "fromUserDoc", "toUserDoc", "Promise", "all", "fromUser", "to<PERSON><PERSON>", "transactionId", "fromTransaction", "TRANSFERRED", "name", "toTransaction", "now", "toString", "Math", "random", "substr", "getUserStats", "user", "earned", "filter", "t", "reduce", "sum", "spent", "abs", "totalPoints", "totalEarned", "totalSpent", "transactionCount", "length", "joinDate", "createdAt", "lastActivity", "i0", "ɵɵinject", "i1", "AngularFirestore", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\services\\user.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { AngularFirestore } from '@angular/fire/compat/firestore';\nimport { Observable, from, map } from 'rxjs';\nimport { User, PointsTransaction, TransactionType, UpdateUserRequest, PaginatedResponse } from '../models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class UserService {\n  constructor(private firestore: AngularFirestore) {}\n\n  getUserById(uid: string): Observable<User | null> {\n    return from(getDoc(doc(this.firestore, 'users', uid))).pipe(\n      map(docSnap => docSnap.exists() ? docSnap.data() as User : null)\n    );\n  }\n\n  updateUser(uid: string, updates: UpdateUserRequest): Observable<void> {\n    const updateData = {\n      ...updates,\n      updatedAt: new Date()\n    };\n    return from(updateDoc(doc(this.firestore, 'users', uid), updateData));\n  }\n\n  getUsersByCity(city: 'Monastir' | 'Sousse'): Observable<User[]> {\n    const q = query(\n      this.usersCollection,\n      where('city', '==', city),\n      where('isActive', '==', true),\n      orderBy('points', 'desc')\n    );\n    \n    return from(getDocs(q)).pipe(\n      map(snapshot => snapshot.docs.map(doc => doc.data() as User))\n    );\n  }\n\n  getTopUsers(limitCount: number = 10): Observable<User[]> {\n    const q = query(\n      this.usersCollection,\n      where('isActive', '==', true),\n      orderBy('points', 'desc'),\n      limit(limitCount)\n    );\n    \n    return from(getDocs(q)).pipe(\n      map(snapshot => snapshot.docs.map(doc => doc.data() as User))\n    );\n  }\n\n  getUserTransactions(uid: string, limitCount: number = 50): Observable<PointsTransaction[]> {\n    const q = query(\n      this.transactionsCollection,\n      where('toUserId', '==', uid),\n      orderBy('timestamp', 'desc'),\n      limit(limitCount)\n    );\n    \n    return from(getDocs(q)).pipe(\n      map(snapshot => snapshot.docs.map(doc => doc.data() as PointsTransaction))\n    );\n  }\n\n  async addPointsToUser(uid: string, points: number, type: TransactionType, description: string, fromUserId?: string): Promise<void> {\n    try {\n      // Get current user data\n      const userDoc = await getDoc(doc(this.firestore, 'users', uid));\n      if (!userDoc.exists()) {\n        throw new Error('User not found');\n      }\n\n      const userData = userDoc.data() as User;\n      const newPoints = userData.points + points;\n\n      // Create transaction record\n      const transaction: PointsTransaction = {\n        id: this.generateTransactionId(),\n        fromUserId,\n        toUserId: uid,\n        points,\n        type,\n        description,\n        timestamp: new Date()\n      };\n\n      // Update user points and add transaction to history\n      const updatedHistory = [...(userData.history || []), transaction];\n      \n      await updateDoc(doc(this.firestore, 'users', uid), {\n        points: newPoints,\n        history: updatedHistory,\n        updatedAt: new Date()\n      });\n\n      // Also store transaction in separate collection for better querying\n      await updateDoc(doc(this.firestore, 'transactions', transaction.id), transaction);\n    } catch (error) {\n      console.error('Error adding points:', error);\n      throw error;\n    }\n  }\n\n  async transferPoints(fromUid: string, toUid: string, points: number, description: string): Promise<void> {\n    try {\n      // Get both users\n      const [fromUserDoc, toUserDoc] = await Promise.all([\n        getDoc(doc(this.firestore, 'users', fromUid)),\n        getDoc(doc(this.firestore, 'users', toUid))\n      ]);\n\n      if (!fromUserDoc.exists() || !toUserDoc.exists()) {\n        throw new Error('One or both users not found');\n      }\n\n      const fromUser = fromUserDoc.data() as User;\n      const toUser = toUserDoc.data() as User;\n\n      if (fromUser.points < points) {\n        throw new Error('Insufficient points');\n      }\n\n      // Create transactions\n      const transactionId = this.generateTransactionId();\n      const timestamp = new Date();\n\n      const fromTransaction: PointsTransaction = {\n        id: transactionId + '_from',\n        fromUserId: fromUid,\n        toUserId: toUid,\n        points: -points,\n        type: TransactionType.TRANSFERRED,\n        description: `Transferred to ${toUser.name}: ${description}`,\n        timestamp\n      };\n\n      const toTransaction: PointsTransaction = {\n        id: transactionId + '_to',\n        fromUserId: fromUid,\n        toUserId: toUid,\n        points: points,\n        type: TransactionType.TRANSFERRED,\n        description: `Received from ${fromUser.name}: ${description}`,\n        timestamp\n      };\n\n      // Update both users\n      await Promise.all([\n        updateDoc(doc(this.firestore, 'users', fromUid), {\n          points: fromUser.points - points,\n          history: [...(fromUser.history || []), fromTransaction],\n          updatedAt: timestamp\n        }),\n        updateDoc(doc(this.firestore, 'users', toUid), {\n          points: toUser.points + points,\n          history: [...(toUser.history || []), toTransaction],\n          updatedAt: timestamp\n        })\n      ]);\n    } catch (error) {\n      console.error('Error transferring points:', error);\n      throw error;\n    }\n  }\n\n  private generateTransactionId(): string {\n    return Date.now().toString() + Math.random().toString(36).substr(2, 9);\n  }\n\n  // Get user statistics\n  getUserStats(uid: string): Observable<any> {\n    return this.getUserById(uid).pipe(\n      map(user => {\n        if (!user) return null;\n        \n        const history = user.history || [];\n        const earned = history.filter(t => t.points > 0).reduce((sum, t) => sum + t.points, 0);\n        const spent = history.filter(t => t.points < 0).reduce((sum, t) => sum + Math.abs(t.points), 0);\n        \n        return {\n          totalPoints: user.points,\n          totalEarned: earned,\n          totalSpent: spent,\n          transactionCount: history.length,\n          joinDate: user.createdAt,\n          lastActivity: user.updatedAt\n        };\n      })\n    );\n  }\n}\n"], "mappings": ";AAEA,SAAqBA,IAAI,EAAEC,GAAG,QAAQ,MAAM;AAC5C,SAAkCC,eAAe,QAA8C,WAAW;;;AAK1G,OAAM,MAAOC,WAAW;EACtBC,YAAoBC,SAA2B;IAA3B,KAAAA,SAAS,GAATA,SAAS;EAAqB;EAElDC,WAAWA,CAACC,GAAW;IACrB,OAAOP,IAAI,CAACQ,MAAM,CAACC,GAAG,CAAC,IAAI,CAACJ,SAAS,EAAE,OAAO,EAAEE,GAAG,CAAC,CAAC,CAAC,CAACG,IAAI,CACzDT,GAAG,CAACU,OAAO,IAAIA,OAAO,CAACC,MAAM,EAAE,GAAGD,OAAO,CAACE,IAAI,EAAU,GAAG,IAAI,CAAC,CACjE;EACH;EAEAC,UAAUA,CAACP,GAAW,EAAEQ,OAA0B;IAChD,MAAMC,UAAU,GAAG;MACjB,GAAGD,OAAO;MACVE,SAAS,EAAE,IAAIC,IAAI;KACpB;IACD,OAAOlB,IAAI,CAACmB,SAAS,CAACV,GAAG,CAAC,IAAI,CAACJ,SAAS,EAAE,OAAO,EAAEE,GAAG,CAAC,EAAES,UAAU,CAAC,CAAC;EACvE;EAEAI,cAAcA,CAACC,IAA2B;IACxC,MAAMC,CAAC,GAAGC,KAAK,CACb,IAAI,CAACC,eAAe,EACpBC,KAAK,CAAC,MAAM,EAAE,IAAI,EAAEJ,IAAI,CAAC,EACzBI,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,EAC7BC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,CAC1B;IAED,OAAO1B,IAAI,CAAC2B,OAAO,CAACL,CAAC,CAAC,CAAC,CAACZ,IAAI,CAC1BT,GAAG,CAAC2B,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC5B,GAAG,CAACQ,GAAG,IAAIA,GAAG,CAACI,IAAI,EAAU,CAAC,CAAC,CAC9D;EACH;EAEAiB,WAAWA,CAACC,UAAA,GAAqB,EAAE;IACjC,MAAMT,CAAC,GAAGC,KAAK,CACb,IAAI,CAACC,eAAe,EACpBC,KAAK,CAAC,UAAU,EAAE,IAAI,EAAE,IAAI,CAAC,EAC7BC,OAAO,CAAC,QAAQ,EAAE,MAAM,CAAC,EACzBM,KAAK,CAACD,UAAU,CAAC,CAClB;IAED,OAAO/B,IAAI,CAAC2B,OAAO,CAACL,CAAC,CAAC,CAAC,CAACZ,IAAI,CAC1BT,GAAG,CAAC2B,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC5B,GAAG,CAACQ,GAAG,IAAIA,GAAG,CAACI,IAAI,EAAU,CAAC,CAAC,CAC9D;EACH;EAEAoB,mBAAmBA,CAAC1B,GAAW,EAAEwB,UAAA,GAAqB,EAAE;IACtD,MAAMT,CAAC,GAAGC,KAAK,CACb,IAAI,CAACW,sBAAsB,EAC3BT,KAAK,CAAC,UAAU,EAAE,IAAI,EAAElB,GAAG,CAAC,EAC5BmB,OAAO,CAAC,WAAW,EAAE,MAAM,CAAC,EAC5BM,KAAK,CAACD,UAAU,CAAC,CAClB;IAED,OAAO/B,IAAI,CAAC2B,OAAO,CAACL,CAAC,CAAC,CAAC,CAACZ,IAAI,CAC1BT,GAAG,CAAC2B,QAAQ,IAAIA,QAAQ,CAACC,IAAI,CAAC5B,GAAG,CAACQ,GAAG,IAAIA,GAAG,CAACI,IAAI,EAAuB,CAAC,CAAC,CAC3E;EACH;EAEMsB,eAAeA,CAAC5B,GAAW,EAAE6B,MAAc,EAAEC,IAAqB,EAAEC,WAAmB,EAAEC,UAAmB;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MAChH,IAAI;QACF;QACA,MAAMC,OAAO,SAASlC,MAAM,CAACC,GAAG,CAAC+B,KAAI,CAACnC,SAAS,EAAE,OAAO,EAAEE,GAAG,CAAC,CAAC;QAC/D,IAAI,CAACmC,OAAO,CAAC9B,MAAM,EAAE,EAAE;UACrB,MAAM,IAAI+B,KAAK,CAAC,gBAAgB,CAAC;;QAGnC,MAAMC,QAAQ,GAAGF,OAAO,CAAC7B,IAAI,EAAU;QACvC,MAAMgC,SAAS,GAAGD,QAAQ,CAACR,MAAM,GAAGA,MAAM;QAE1C;QACA,MAAMU,WAAW,GAAsB;UACrCC,EAAE,EAAEP,KAAI,CAACQ,qBAAqB,EAAE;UAChCT,UAAU;UACVU,QAAQ,EAAE1C,GAAG;UACb6B,MAAM;UACNC,IAAI;UACJC,WAAW;UACXY,SAAS,EAAE,IAAIhC,IAAI;SACpB;QAED;QACA,MAAMiC,cAAc,GAAG,CAAC,IAAIP,QAAQ,CAACQ,OAAO,IAAI,EAAE,CAAC,EAAEN,WAAW,CAAC;QAEjE,MAAM3B,SAAS,CAACV,GAAG,CAAC+B,KAAI,CAACnC,SAAS,EAAE,OAAO,EAAEE,GAAG,CAAC,EAAE;UACjD6B,MAAM,EAAES,SAAS;UACjBO,OAAO,EAAED,cAAc;UACvBlC,SAAS,EAAE,IAAIC,IAAI;SACpB,CAAC;QAEF;QACA,MAAMC,SAAS,CAACV,GAAG,CAAC+B,KAAI,CAACnC,SAAS,EAAE,cAAc,EAAEyC,WAAW,CAACC,EAAE,CAAC,EAAED,WAAW,CAAC;OAClF,CAAC,OAAOO,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,sBAAsB,EAAEA,KAAK,CAAC;QAC5C,MAAMA,KAAK;;IACZ;EACH;EAEME,cAAcA,CAACC,OAAe,EAAEC,KAAa,EAAErB,MAAc,EAAEE,WAAmB;IAAA,IAAAoB,MAAA;IAAA,OAAAjB,iBAAA;MACtF,IAAI;QACF;QACA,MAAM,CAACkB,WAAW,EAAEC,SAAS,CAAC,SAASC,OAAO,CAACC,GAAG,CAAC,CACjDtD,MAAM,CAACC,GAAG,CAACiD,MAAI,CAACrD,SAAS,EAAE,OAAO,EAAEmD,OAAO,CAAC,CAAC,EAC7ChD,MAAM,CAACC,GAAG,CAACiD,MAAI,CAACrD,SAAS,EAAE,OAAO,EAAEoD,KAAK,CAAC,CAAC,CAC5C,CAAC;QAEF,IAAI,CAACE,WAAW,CAAC/C,MAAM,EAAE,IAAI,CAACgD,SAAS,CAAChD,MAAM,EAAE,EAAE;UAChD,MAAM,IAAI+B,KAAK,CAAC,6BAA6B,CAAC;;QAGhD,MAAMoB,QAAQ,GAAGJ,WAAW,CAAC9C,IAAI,EAAU;QAC3C,MAAMmD,MAAM,GAAGJ,SAAS,CAAC/C,IAAI,EAAU;QAEvC,IAAIkD,QAAQ,CAAC3B,MAAM,GAAGA,MAAM,EAAE;UAC5B,MAAM,IAAIO,KAAK,CAAC,qBAAqB,CAAC;;QAGxC;QACA,MAAMsB,aAAa,GAAGP,MAAI,CAACV,qBAAqB,EAAE;QAClD,MAAME,SAAS,GAAG,IAAIhC,IAAI,EAAE;QAE5B,MAAMgD,eAAe,GAAsB;UACzCnB,EAAE,EAAEkB,aAAa,GAAG,OAAO;UAC3B1B,UAAU,EAAEiB,OAAO;UACnBP,QAAQ,EAAEQ,KAAK;UACfrB,MAAM,EAAE,CAACA,MAAM;UACfC,IAAI,EAAEnC,eAAe,CAACiE,WAAW;UACjC7B,WAAW,EAAE,kBAAkB0B,MAAM,CAACI,IAAI,KAAK9B,WAAW,EAAE;UAC5DY;SACD;QAED,MAAMmB,aAAa,GAAsB;UACvCtB,EAAE,EAAEkB,aAAa,GAAG,KAAK;UACzB1B,UAAU,EAAEiB,OAAO;UACnBP,QAAQ,EAAEQ,KAAK;UACfrB,MAAM,EAAEA,MAAM;UACdC,IAAI,EAAEnC,eAAe,CAACiE,WAAW;UACjC7B,WAAW,EAAE,iBAAiByB,QAAQ,CAACK,IAAI,KAAK9B,WAAW,EAAE;UAC7DY;SACD;QAED;QACA,MAAMW,OAAO,CAACC,GAAG,CAAC,CAChB3C,SAAS,CAACV,GAAG,CAACiD,MAAI,CAACrD,SAAS,EAAE,OAAO,EAAEmD,OAAO,CAAC,EAAE;UAC/CpB,MAAM,EAAE2B,QAAQ,CAAC3B,MAAM,GAAGA,MAAM;UAChCgB,OAAO,EAAE,CAAC,IAAIW,QAAQ,CAACX,OAAO,IAAI,EAAE,CAAC,EAAEc,eAAe,CAAC;UACvDjD,SAAS,EAAEiC;SACZ,CAAC,EACF/B,SAAS,CAACV,GAAG,CAACiD,MAAI,CAACrD,SAAS,EAAE,OAAO,EAAEoD,KAAK,CAAC,EAAE;UAC7CrB,MAAM,EAAE4B,MAAM,CAAC5B,MAAM,GAAGA,MAAM;UAC9BgB,OAAO,EAAE,CAAC,IAAIY,MAAM,CAACZ,OAAO,IAAI,EAAE,CAAC,EAAEiB,aAAa,CAAC;UACnDpD,SAAS,EAAEiC;SACZ,CAAC,CACH,CAAC;OACH,CAAC,OAAOG,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,4BAA4B,EAAEA,KAAK,CAAC;QAClD,MAAMA,KAAK;;IACZ;EACH;EAEQL,qBAAqBA,CAAA;IAC3B,OAAO9B,IAAI,CAACoD,GAAG,EAAE,CAACC,QAAQ,EAAE,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACxE;EAEA;EACAC,YAAYA,CAACpE,GAAW;IACtB,OAAO,IAAI,CAACD,WAAW,CAACC,GAAG,CAAC,CAACG,IAAI,CAC/BT,GAAG,CAAC2E,IAAI,IAAG;MACT,IAAI,CAACA,IAAI,EAAE,OAAO,IAAI;MAEtB,MAAMxB,OAAO,GAAGwB,IAAI,CAACxB,OAAO,IAAI,EAAE;MAClC,MAAMyB,MAAM,GAAGzB,OAAO,CAAC0B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC3C,MAAM,GAAG,CAAC,CAAC,CAAC4C,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGF,CAAC,CAAC3C,MAAM,EAAE,CAAC,CAAC;MACtF,MAAM8C,KAAK,GAAG9B,OAAO,CAAC0B,MAAM,CAACC,CAAC,IAAIA,CAAC,CAAC3C,MAAM,GAAG,CAAC,CAAC,CAAC4C,MAAM,CAAC,CAACC,GAAG,EAAEF,CAAC,KAAKE,GAAG,GAAGT,IAAI,CAACW,GAAG,CAACJ,CAAC,CAAC3C,MAAM,CAAC,EAAE,CAAC,CAAC;MAE/F,OAAO;QACLgD,WAAW,EAAER,IAAI,CAACxC,MAAM;QACxBiD,WAAW,EAAER,MAAM;QACnBS,UAAU,EAAEJ,KAAK;QACjBK,gBAAgB,EAAEnC,OAAO,CAACoC,MAAM;QAChCC,QAAQ,EAAEb,IAAI,CAACc,SAAS;QACxBC,YAAY,EAAEf,IAAI,CAAC3D;OACpB;IACH,CAAC,CAAC,CACH;EACH;;;uBArLWd,WAAW,EAAAyF,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;aAAX5F,WAAW;MAAA6F,OAAA,EAAX7F,WAAW,CAAA8F,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}