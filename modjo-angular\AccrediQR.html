<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AccrédiQR - Système d'accréditation par QR code</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f8f9fa;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      background-color: #ffffff;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 15px 0;
    }
    
    .header-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .logo a {
      font-size: 24px;
      font-weight: 700;
      color: #4a90e2;
      text-decoration: none;
    }
    
    .nav-list {
      display: flex;
      list-style: none;
    }
    
    .nav-list li {
      margin-left: 20px;
    }
    
    .nav-list a {
      color: #333;
      text-decoration: none;
      font-size: 16px;
      transition: color 0.3s;
    }
    
    .nav-list a:hover {
      color: #4a90e2;
    }
    
    .hero-section {
      text-align: center;
      padding: 60px 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
      margin: 40px 0;
    }
    
    h1 {
      font-size: 2.5rem;
      margin-bottom: 20px;
      color: #333;
    }
    
    .subtitle {
      font-size: 1.2rem;
      color: #666;
      margin-bottom: 30px;
    }
    
    .cta-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;
    }
    
    .btn {
      display: inline-block;
      padding: 12px 24px;
      border-radius: 30px;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
    }
    
    .btn-primary {
      background-color: #4a90e2;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #3a7bc8;
    }
    
    .features-section {
      margin-bottom: 60px;
    }
    
    h2 {
      text-align: center;
      margin-bottom: 40px;
      color: #333;
      font-size: 2rem;
    }
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 30px;
    }
    
    .feature-card {
      background-color: white;
      border-radius: 8px;
      padding: 30px;
      text-align: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease;
    }
    
    .feature-card:hover {
      transform: translateY(-5px);
    }
    
    .feature-icon {
      font-size: 3rem;
      margin-bottom: 20px;
    }
    
    h3 {
      margin-bottom: 15px;
      color: #333;
    }
    
    .partners-section {
      background-color: #f8f9fa;
      padding: 60px 20px;
      border-radius: 8px;
      text-align: center;
      margin-bottom: 40px;
    }
    
    .partners-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 20px;
      margin-top: 30px;
    }
    
    .partner {
      background-color: white;
      padding: 15px 25px;
      border-radius: 30px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    footer {
      background-color: #333;
      color: white;
      padding: 40px 0;
      text-align: center;
    }
    
    .footer-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
    
    .footer-links {
      margin-top: 20px;
    }
    
    .footer-links a {
      color: #ddd;
      margin: 0 10px;
      text-decoration: none;
    }
    
    .footer-links a:hover {
      color: white;
    }
    
    /* Modal styles */
    .modal {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
    }
    
    .modal-content {
      background-color: white;
      margin: 10% auto;
      padding: 30px;
      width: 90%;
      max-width: 500px;
      border-radius: 8px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      position: relative;
    }
    
    .close-modal {
      position: absolute;
      top: 15px;
      right: 20px;
      font-size: 24px;
      cursor: pointer;
      color: #666;
    }
    
    .close-modal:hover {
      color: #333;
    }
    
    .form-group {
      margin-bottom: 20px;
    }
    
    .form-group label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }
    
    .form-group input {
      width: 100%;
      padding: 12px;
      border: 1px solid #ddd;
      border-radius: 4px;
      font-size: 16px;
    }
    
    .form-group input:focus {
      border-color: #4a90e2;
      outline: none;
    }
    
    .modal-btn {
      width: 100%;
      padding: 12px;
      background-color: #4a90e2;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
    }
    
    .modal-btn:hover {
      background-color: #3a7bc8;
    }
    
    .error-message {
      color: #d32f2f;
      margin-top: 5px;
      font-size: 14px;
    }
    
    /* QR Scanner styles */
    .scanner-container {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.9);
    }
    
    .scanner-content {
      background-color: white;
      margin: 5% auto;
      padding: 30px;
      width: 90%;
      max-width: 600px;
      border-radius: 8px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
      text-align: center;
    }
    
    .scanner-view {
      width: 100%;
      height: 300px;
      background-color: #f5f5f5;
      margin: 20px 0;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;
    }
    
    .scanner-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;
      margin-top: 20px;
    }
    
    .scanner-btn {
      padding: 12px 24px;
      background-color: #4a90e2;
      color: white;
      border: none;
      border-radius: 4px;
      font-size: 16px;
      cursor: pointer;
    }
    
    .scanner-btn.cancel {
      background-color: #f44336;
    }
    
    .scanner-btn:hover {
      opacity: 0.9;
    }
    
    /* Profile view styles */
    .profile-container {
      display: none;
      position: fixed;
      z-index: 1000;
      left: 0;
      top: 0;
      width: 100%;
      height: 100%;
      background-color: rgba(0, 0, 0, 0.5);
      overflow-y: auto;
    }
    
    .profile-content {
      background-color: white;
      margin: 5% auto;
      padding: 0;
      width: 90%;
      max-width: 600px;
      border-radius: 8px;
      box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
    }
    
    .profile-header {
      display: flex;
      padding: 30px;
      background-color: #f9f9f9;
      border-bottom: 1px solid #eee;
    }
    
    .profile-image {
      width: 80px;
      height: 80px;
      border-radius: 50%;
      overflow: hidden;
      margin-right: 20px;
      background-color: #ddd;
    }
    
    .profile-info h3 {
      margin-top: 0;
      margin-bottom: 5px;
    }
    
    .profile-details {
      padding: 20px 30px;
    }
    
    .skills {
      display: flex;
      flex-wrap: wrap;
      gap: 10px;
      margin-top: 15px;
    }
    
    .skill-tag {
      padding: 5px 15px;
      background-color: #e3f2fd;
      color: #1976d2;
      border-radius: 20px;
      font-size: 14px;
    }
    
    .points-section {
      padding: 20px 30px;
      border-top: 1px solid #eee;
    }
    
    .points-options {
      display: flex;
      gap: 15px;
      margin-top: 15px;
    }
    
    .point-btn {
      flex: 1;
      padding: 10px;
      border: 1px solid #ddd;
      border-radius: 4px;
      background-color: white;
      cursor: pointer;
      transition: all 0.3s;
    }
    
    .point-btn.selected {
      background-color: #4a90e2;
      color: white;
      border-color: #4a90e2;
    }
    
    .profile-actions {
      padding: 20px 30px;
      text-align: right;
      border-top: 1px solid #eee;
    }
    
    @media (max-width: 768px) {
      .cta-buttons {
        flex-direction: column;
        align-items: center;
      }
      
      .hero-section {
        padding: 40px 20px;
      }
      
      h1 {
        font-size: 2rem;
      }
      
      .profile-header {
        flex-direction: column;
        align-items: center;
        text-align: center;
      }
      
      .profile-image {
        margin-right: 0;
        margin-bottom: 15px;
      }
    }
  </style>
</head>
<body>
  <header>
    <div class="header-container">
      <div class="logo">
        <a href="#">AccrédiQR</a>
      </div>
      
      <nav>
        <ul class="nav-list">
          <li><a href="#" id="home-link">Accueil</a></li>
          <li><a href="#" id="scanner-link">Scanner</a></li>
          <li><a href="#" id="profile-link">Mon Profil</a></li>
          <li><a href="#" id="login-link">Connexion</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <div class="container">
    <div class="hero-section">
      <h1>Bienvenue sur AccrédiQR</h1>
      <p class="subtitle">La plateforme qui valorise les interactions avec les institutions</p>
      
      <div class="cta-buttons">
        <a href="#" class="btn btn-primary" id="scan-btn">Scanner un QR code</a>
        <a href="#" class="btn btn-primary" id="login-btn">Se connecter</a>
      </div>
    </div>

    <div class="features-section">
      <h2>Comment ça fonctionne</h2>
      
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">📱</div>
          <h3>Scannez</h3>
          <p>Utilisez votre appareil pour scanner le QR code d'une institution ou d'un prestataire</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">👤</div>
          <h3>Consultez</h3>
          <p>Accédez au profil KnowMe du prestataire et découvrez ses compétences</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">⭐</div>
          <h3>Attribuez</h3>
          <p>Donnez des points pour valoriser les services rendus ou les bonnes actions</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">🎁</div>
          <h3>Échangez</h3>
          <p>Utilisez vos points pour obtenir des récompenses auprès de nos partenaires</p>
        </div>
      </div>
    </div>

    <div class="partners-section">
      <h2>Nos partenaires</h2>
      <p>Rejoignez notre réseau d'institutions et de prestataires à Monastir et Sousse</p>
      
      <div class="partners-list">
        <div class="partner">Écoles</div>
        <div class="partner">Universités</div>
        <div class="partner">Bibliothèques</div>
        <div class="partner">Associations</div>
        <div class="partner">Commerces locaux</div>
      </div>
    </div>
  </div>

  <!-- Login Modal -->
  <div id="login-modal" class="modal">
    <div class="modal-content">
      <span class="close-modal">&times;</span>
      <h2>Connexion</h2>
      <p>Connectez-vous à votre compte AccrédiQR</p>
      
      <form id="login-form">
        <div class="form-group">
          <label for="email">Email</label>
          <input type="email" id="email" placeholder="Votre email" required>
        </div>
        
        <div class="form-group">
          <label for="password">Mot de passe</label>
          <input type="password" id="password" placeholder="Votre mot de passe" required>
        </div>
        
        <div id="login-error" class="error-message"></div>
        
        <button type="submit" class="modal-btn">Se connecter</button>
      </form>
      
      <p style="margin-top: 20px; text-align: center;">
        Comptes de démonstration:<br>
        user&#64;example.com / password<br>
        provider&#64;example.com / password
      </p>
    </div>
  </div>

  <!-- QR Scanner Modal -->
  <div id="scanner-modal" class="scanner-container">
    <div class="scanner-content">
      <h2>Scanner un QR code</h2>
      <p>Pointez votre caméra vers un QR code KnowMe</p>
      
      <div class="scanner-view" id="scanner-view">
        <p>Simulation de caméra</p>
      </div>
      
      <div class="scanner-buttons">
        <button id="simulate-scan" class="scanner-btn">Simuler un scan</button>
        <button id="cancel-scan" class="scanner-btn cancel">Annuler</button>
      </div>
    </div>
  </div>

  <!-- Profile View Modal -->
  <div id="profile-modal" class="profile-container">
    <div class="profile-content">
      <div class="profile-header">
        <div class="profile-image"></div>
        <div class="profile-info">
          <h3>Ahmed Ben Salem</h3>
          <p>Coiffeur</p>
          <p>Monastir</p>
          <div style="margin-top: 10px;">
            <span style="color: #ffc107;">★★★★</span><span style="color: #ddd;">★</span>
            <span style="font-size: 14px; color: #666;">(27 avis)</span>
          </div>
        </div>
      </div>
      
      <div class="profile-details">
        <h3>Compétences</h3>
        <div class="skills">
          <span class="skill-tag">Coupe homme</span>
          <span class="skill-tag">Barbe</span>
          <span class="skill-tag">Coloration</span>
        </div>
      </div>
      
      <div class="points-section">
        <h3>Attribuer des points</h3>
        <div class="points-options">
          <button class="point-btn selected" data-points="1">1 point</button>
          <button class="point-btn" data-points="3">3 points</button>
          <button class="point-btn" data-points="5">5 points</button>
        </div>
        
        <div class="form-group" style="margin-top: 15px;">
          <label for="description">Description</label>
          <input type="text" id="description" placeholder="Ex: Coupe de cheveux, consultation, etc.">
        </div>
      </div>
      
      <div class="profile-actions">
        <button id="award-points" class="scanner-btn">Attribuer les points</button>
        <button id="close-profile" class="scanner-btn cancel">Fermer</button>
      </div>
    </div>
  </div>

  <footer>
    <div class="footer-content">
      <p>&copy; 2024 AccrédiQR - Tous droits réservés</p>
      <div class="footer-links">
        <a href="#">Mentions légales</a>
        <a href="#">Politique de confidentialité</a>
        <a href="#">Contact</a>
      </div>
    </div>
  </footer>

  <script>
    document.addEventListener('DOMContentLoaded', function() {
      // Elements
      const loginModal = document.getElementById('login-modal');
      const scannerModal = document.getElementById('scanner-modal');
      const profileModal = document.getElementById('profile-modal');
      const loginForm = document.getElementById('login-form');
      const loginError = document.getElementById('login-error');
      
      // Login buttons
      document.getElementById('login-link').addEventListener('click', function(e) {
        e.preventDefault();
        loginModal.style.display = 'block';
      });
      
      document.getElementById('login-btn').addEventListener('click', function(e) {
        e.preventDefault();
        loginModal.style.display = 'block';
      });
      
      // Scanner buttons
      document.getElementById('scanner-link').addEventListener('click', function(e) {
        e.preventDefault();
        scannerModal.style.display = 'block';
      });
      
      document.getElementById('scan-btn').addEventListener('click', function(e) {
        e.preventDefault();
        scannerModal.style.display = 'block';
      });
      
      // Close modals
      document.querySelectorAll('.close-modal').forEach(function(element) {
        element.addEventListener('click', function() {
          loginModal.style.display = 'none';
          scannerModal.style.display = 'none';
          profileModal.style.display = 'none';
        });
      });
      
      // Cancel scan
      document.getElementById('cancel-scan').addEventListener('click', function() {
        scannerModal.style.display = 'none';
      });
      
      // Close profile
      document.getElementById('close-profile').addEventListener('click', function() {
        profileModal.style.display = 'none';
      });
      
      // Simulate scan
      document.getElementById('simulate-scan').addEventListener('click', function() {
        scannerModal.style.display = 'none';
        profileModal.style.display = 'block';
      });
      
      // Award points
      document.getElementById('award-points').addEventListener('click', function() {
        const selectedBtn = document.querySelector('.point-btn.selected');
        const points = selectedBtn ? selectedBtn.getAttribute('data-points') : 1;
        const description = document.getElementById('description').value || 'Service rendu';
        
        alert(`Points attribués avec succès!\n\n${points} point(s) pour "${description}"`);
        profileModal.style.display = 'none';
      });
      
      // Point selection
      document.querySelectorAll('.point-btn').forEach(function(btn) {
        btn.addEventListener('click', function() {
          document.querySelectorAll('.point-btn').forEach(function(b) {
            b.classList.remove('selected');
          });
          this.classList.add('selected');
        });
      });
      
      // Login form submission
      loginForm.addEventListener('submit', function(e) {
        e.preventDefault();
        
        const email = document.getElementById('email').value;
        const password = document.getElementById('password').value;
        
        if (email === '<EMAIL>' && password === 'password') {
          loginModal.style.display = 'none';
          alert('Connexion réussie en tant qu\'utilisateur!');
        } else if (email === '<EMAIL>' && password === 'password') {
          loginModal.style.display = 'none';
          alert('Connexion réussie en tant que prestataire!');
        } else {
          loginError.textContent = 'Email ou mot de passe incorrect';
        }
      });
      
      // Close modals when clicking outside
      window.addEventListener('click', function(event) {
        if (event.target === loginModal) {
          loginModal.style.display = 'none';
        } else if (event.target === scannerModal) {
          scannerModal.style.display = 'none';
        } else if (event.target === profileModal) {
          profileModal.style.display = 'none';
        }
      });
    });
  </script>
</body>
</html>
