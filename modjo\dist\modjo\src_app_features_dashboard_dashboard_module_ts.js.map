{"version": 3, "file": "src_app_features_dashboard_dashboard_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC0BcA,4DANR,mBAEoD,uBAChC,cACU,cAC0B,eACtC;IAAAA,oDAAA,GAAe;IAC3BA,0DAD2B,EAAW,EAChC;IAEJA,4DADF,cAAuB,aACS;IAAAA,oDAAA,GAAgB;IAAAA,0DAAA,EAAK;IACnDA,4DAAA,QAAG;IAAAA,oDAAA,IAAgB;IAI3BA,0DAJ2B,EAAI,EACnB,EACF,EACW,EACV;;;;;IAZDA,yDAAA,oBAAAK,IAAA,aAAyC;IAGtBL,uDAAA,GAA0B;IAA1BA,yDAAA,UAAAO,OAAA,CAAAC,KAAA,CAA0B;IACrCR,uDAAA,GAAe;IAAfA,+DAAA,CAAAO,OAAA,CAAAG,IAAA,CAAe;IAGKV,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAO,OAAA,CAAAI,KAAA,CAAgB;IAC3CX,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAO,OAAA,CAAAK,KAAA,CAAgB;;;;;IAkBnBZ,4DANR,mBAEoD,uBAChC,cACY,cACO,mBAC4C;IACzEA,oDAAA,GACF;IACFA,0DADE,EAAW,EACP;IACNA,4DAAA,SAAI;IAAAA,oDAAA,GAAkB;IAAAA,0DAAA,EAAK;IAC3BA,4DAAA,QAAG;IAAAA,oDAAA,GAAwB;IAAAA,0DAAA,EAAI;IAK7BA,4DAJF,kBAG8B,gBAClB;IAAAA,oDAAA,IAAiB;IAAAA,0DAAA,EAAW;IACtCA,oDAAA,IACF;IAGNA,0DAHM,EAAS,EACL,EACW,EACV;;;;;;IAnBDA,yDAAA,oBAAAa,IAAA,aAAyC;IAIbb,uDAAA,GAA4C;IAA5CA,yDAAA,UAAAc,MAAA,CAAAC,cAAA,CAAAC,SAAA,CAAAR,KAAA,EAA4C;IACxER,uDAAA,EACF;IADEA,gEAAA,MAAAgB,SAAA,CAAAN,IAAA,MACF;IAEEV,uDAAA,GAAkB;IAAlBA,+DAAA,CAAAgB,SAAA,CAAAJ,KAAA,CAAkB;IACnBZ,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAgB,SAAA,CAAAE,WAAA,CAAwB;IAEnBlB,uDAAA,EAAsB;IACtBA,wDADA,UAAAgB,SAAA,CAAAR,KAAA,CAAsB,eAAAQ,SAAA,CAAAI,KAAA,CACK;IAEvBpB,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAgB,SAAA,CAAAN,IAAA,CAAiB;IAC3BV,uDAAA,EACF;IADEA,gEAAA,MAAAc,MAAA,CAAAO,mBAAA,CAAAL,SAAA,CAAAJ,KAAA,OACF;;;;;IAeEZ,4DAFJ,cAA8E,cACjD,eAC0B;IACjDA,oDAAA,GACF;IACFA,0DADE,EAAW,EACP;IAEJA,4DADF,cAA2B,YACO;IAAAA,oDAAA,GAA6B;IAAAA,0DAAA,EAAI;IACjEA,4DAAA,YAAyB;IAAAA,oDAAA,GAA0C;;IACrEA,0DADqE,EAAI,EACnE;IACNA,4DAAA,eAAwF;IACtFA,oDAAA,IACF;IACFA,0DADE,EAAM,EACF;;;;;IAXQA,uDAAA,GAAwC;IAAxCA,wDAAA,eAAAuB,cAAA,CAAAC,IAAA,CAAwC;IAChDxB,uDAAA,EACF;IADEA,gEAAA,MAAAc,MAAA,CAAAW,eAAA,CAAAF,cAAA,CAAAC,IAAA,OACF;IAGgCxB,uDAAA,GAA6B;IAA7BA,+DAAA,CAAAuB,cAAA,CAAAL,WAAA,CAA6B;IACpClB,uDAAA,GAA0C;IAA1CA,+DAAA,CAAAA,yDAAA,OAAAuB,cAAA,CAAAI,SAAA,WAA0C;IAExC3B,uDAAA,GAA0D;IAA1DA,wDAAA,CAAAuB,cAAA,CAAAK,MAAA,+BAA0D;IACrF5B,uDAAA,EACF;IADEA,gEAAA,MAAAuB,cAAA,CAAAK,MAAA,qBAAAL,cAAA,CAAAK,MAAA,UACF;;;;;IAhBR5B,4DADF,cAA8E,SACxE;IAAAA,oDAAA,iCAAgB;IAAAA,0DAAA,EAAK;IAGrBA,4DAFJ,mBAAgC,uBACZ,cACW;IACzBA,wDAAA,IAAA+B,8CAAA,oBAA8E;IAiBtF/B,0DAHM,EAAM,EACW,EACV,EACP;;;;IAjB+BA,uDAAA,GAAyB;IAAzBA,wDAAA,YAAAc,MAAA,CAAAkB,IAAA,CAAAC,OAAA,CAAAC,KAAA,KAAyB;;;;;IAwBtDlC,4DAJR,cAA4E,mBAC7C,uBACT,cACW,mBACI;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAW;IAC/CA,4DAAA,SAAI;IAAAA,oDAAA,gCAAyB;IAAAA,0DAAA,EAAK;IAClCA,4DAAA,QAAG;IAAAA,oDAAA,0HAAyG;IAAAA,0DAAA,EAAI;IAE9GA,4DADF,kBAAmE,gBACvD;IAAAA,oDAAA,uBAAe;IAAAA,0DAAA,EAAW;IACpCA,oDAAA,4BACF;IAIRA,0DAJQ,EAAS,EACL,EACW,EACV,EACP;;;;;IAvGFA,4DAJN,aAA8C,aAEN,aACP,YACL;IAAAA,oDAAA,GAAqC;IAAAA,0DAAA,EAAK;IAE9DA,4DADF,WAA6B,eACjB;IAAAA,oDAAA,kBAAW;IAAAA,0DAAA,EAAW;IAChCA,oDAAA,GACF;IACFA,0DADE,EAAI,EACA;IAEJA,4DADF,aAA+B,gBACnB;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,YAAM;IAAAA,oDAAA,IAAwB;IAElCA,0DAFkC,EAAO,EACjC,EACF;IAIJA,4DADF,cAA2B,aACe;IAAAA,oDAAA,qCAAmB;IAAAA,0DAAA,EAAK;IAChEA,4DAAA,cAAwB;IACtBA,wDAAA,KAAAmC,6CAAA,wBAEoD;IAcxDnC,0DADE,EAAM,EACF;IAIJA,4DADF,eAA6B,aACa;IAAAA,oDAAA,8BAAiB;IAAAA,0DAAA,EAAK;IAC9DA,4DAAA,eAA0B;IACxBA,wDAAA,KAAAoC,6CAAA,yBAEoD;IAqBxDpC,0DADE,EAAM,EACF;IA4BNA,wDAzBA,KAAAqC,wCAAA,kBAA8E,KAAAC,wCAAA,mBAyBF;IAe9EtC,0DAAA,EAAM;;;;IAxGsBA,uDAAA,GAAqC;IAArCA,gEAAA,KAAAc,MAAA,CAAAyB,WAAA,UAAAzB,MAAA,CAAAkB,IAAA,CAAAQ,IAAA,MAAqC;IAGzDxC,uDAAA,GACF;IADEA,gEAAA,MAAAc,MAAA,CAAAkB,IAAA,CAAAS,IAAA,cAAA3B,MAAA,CAAA4B,kBAAA,CAAA5B,MAAA,CAAAkB,IAAA,CAAAW,IAAA,OACF;IAIM3C,uDAAA,GAAwB;IAAxBA,gEAAA,KAAAc,MAAA,CAAAkB,IAAA,CAAAJ,MAAA,YAAwB;IAQH5B,uDAAA,GAAU;IAAVA,wDAAA,YAAAc,MAAA,CAAA8B,KAAA,CAAU;IAsBR5C,uDAAA,GAAiB;IAAjBA,wDAAA,YAAAc,MAAA,CAAA+B,YAAA,CAAiB;IA0BnB7C,uDAAA,EAA6C;IAA7CA,wDAAA,SAAAc,MAAA,CAAAkB,IAAA,CAAAC,OAAA,IAAAnB,MAAA,CAAAkB,IAAA,CAAAC,OAAA,CAAAa,MAAA,KAA6C;IAyBlD9C,uDAAA,EAAgD;IAAhDA,wDAAA,UAAAc,MAAA,CAAAkB,IAAA,CAAAC,OAAA,IAAAnB,MAAA,CAAAkB,IAAA,CAAAC,OAAA,CAAAa,MAAA,OAAgD;;;ADpFtE,MAAOC,kBAAkB;EAgD7BC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IA/C/B,KAAAjB,IAAI,GAAgB,IAAI;IAExB,KAAAa,YAAY,GAAG,CACb;MACEjC,KAAK,EAAE,YAAY;MACnBM,WAAW,EAAE,2CAA2C;MACxDR,IAAI,EAAE,iBAAiB;MACvBU,KAAK,EAAE,aAAa;MACpBZ,KAAK,EAAE;KACR,EACD;MACEI,KAAK,EAAE,aAAa;MACpBM,WAAW,EAAE,uCAAuC;MACpDR,IAAI,EAAE,eAAe;MACrBU,KAAK,EAAE,UAAU;MACjBZ,KAAK,EAAE;KACR,EACD;MACEI,KAAK,EAAE,YAAY;MACnBM,WAAW,EAAE,6BAA6B;MAC1CR,IAAI,EAAE,QAAQ;MACdU,KAAK,EAAE,UAAU;MACjBZ,KAAK,EAAE;KACR,CACF;IAED,KAAAoC,KAAK,GAAG,CACN;MACEhC,KAAK,EAAE,eAAe;MACtBD,KAAK,EAAE,GAAG;MACVD,IAAI,EAAE,OAAO;MACbF,KAAK,EAAE;KACR,EACD;MACEI,KAAK,EAAE,kBAAkB;MACzBD,KAAK,EAAE,GAAG;MACVD,IAAI,EAAE,UAAU;MAChBF,KAAK,EAAE;KACR,EACD;MACEI,KAAK,EAAE,sBAAsB;MAC7BD,KAAK,EAAE,GAAG;MACVD,IAAI,EAAE,eAAe;MACrBF,KAAK,EAAE;KACR,CACF;EAE8C;EAE/C0C,QAAQA,CAAA;IACN,IAAI,CAACD,WAAW,CAACE,YAAY,CAACC,SAAS,CAACpB,IAAI,IAAG;MAC7C,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACqB,WAAW,CAACrB,IAAI,CAAC;;IAE1B,CAAC,CAAC;EACJ;EAEQqB,WAAWA,CAACrB,IAAU;IAC5B,IAAI,CAACY,KAAK,CAAC,CAAC,CAAC,CAACjC,KAAK,GAAGqB,IAAI,CAACJ,MAAM,CAAC0B,QAAQ,EAAE;IAE5C;IACA,MAAMC,gBAAgB,GAAGvB,IAAI,CAACC,OAAO,EAAEuB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjC,IAAI,KAAK,WAAW,CAAC,CAACsB,MAAM,IAAI,CAAC;IACtF,IAAI,CAACF,KAAK,CAAC,CAAC,CAAC,CAACjC,KAAK,GAAG4C,gBAAgB,CAACD,QAAQ,EAAE;IAEjD;IACA,MAAMI,eAAe,GAAG1B,IAAI,CAACC,OAAO,EAAEuB,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACjC,IAAI,KAAK,OAAO,CAAC,CAACsB,MAAM,IAAI,CAAC;IACjF,IAAI,CAACF,KAAK,CAAC,CAAC,CAAC,CAACjC,KAAK,GAAG+C,eAAe,CAACJ,QAAQ,EAAE;EAClD;EAEAf,WAAWA,CAAA;IACT,MAAMoB,IAAI,GAAG,IAAIC,IAAI,EAAE,CAACC,QAAQ,EAAE;IAClC,IAAIF,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,SAAS;EAClB;EAEAjB,kBAAkBA,CAACC,IAAY;IAC7B,MAAMmB,SAAS,GAA8B;MAC3C,MAAM,EAAE,aAAa;MACrB,UAAU,EAAE,YAAY;MACxB,WAAW,EAAE,YAAY;MACzB,OAAO,EAAE;KACV;IACD,OAAOA,SAAS,CAACnB,IAAI,CAAC,IAAIA,IAAI;EAChC;EAEAlB,eAAeA,CAACD,IAAY;IAC1B,MAAMuC,OAAO,GAA8B;MACzC,QAAQ,EAAE,YAAY;MACtB,OAAO,EAAE,eAAe;MACxB,aAAa,EAAE,YAAY;MAC3B,WAAW,EAAE,UAAU;MACvB,OAAO,EAAE;KACV;IACD,OAAOA,OAAO,CAACvC,IAAI,CAAC,IAAI,QAAQ;EAClC;EAEAT,cAAcA,CAACP,KAAa;IAC1B,MAAMwD,QAAQ,GAA8B;MAC1C,SAAS,EAAE,SAAS;MACpB,QAAQ,EAAE,SAAS;MACnB,MAAM,EAAE;KACT;IACD,OAAOA,QAAQ,CAACxD,KAAK,CAAC,IAAI,SAAS;EACrC;EAEAa,mBAAmBA,CAACT,KAAa;IAC/B,MAAMqD,aAAa,GAA8B;MAC/C,YAAY,EAAE,SAAS;MACvB,aAAa,EAAE,UAAU;MACzB,YAAY,EAAE;KACf;IACD,OAAOA,aAAa,CAACrD,KAAK,CAAC,IAAI,OAAO;EACxC;;;uBAnHWmC,kBAAkB,EAAA/C,+DAAA,CAAAmE,oEAAA;IAAA;EAAA;;;YAAlBpB,kBAAkB;MAAAsB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCT/B3E,wDAAA,IAAA6E,iCAAA,kBAA8C;;;UAAZ7E,wDAAA,SAAA4E,GAAA,CAAA5C,IAAA,CAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACCG;AACA;AAE/C;AACuD;AACI;AACJ;AACS;AAEgB;AAC3B;;;AAiB/C,MAAOqD,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAVxBP,yDAAY,EACZC,yDAAY,CAACO,QAAQ,CAACF,8DAAe,CAAC;MAEtC;MACAJ,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,0EAAiB;IAAA;EAAA;;;sHAGRE,eAAe;IAAAE,YAAA,GAbxBxC,yFAAkB;IAAAyC,OAAA,GAGlBV,yDAAY,EAAAX,yDAAA;IAGZ;IACAa,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,0EAAiB;EAAA;AAAA;;;;;;;;;;;;;;;ACxB2D;AAEzE,MAAMC,eAAe,GAAW,CACrC;EACEK,IAAI,EAAE,EAAE;EACRC,SAAS,EAAE3C,yFAAkBA;CAC9B,CACF;;;;;;;;;;;;;;;;;;;;;;;;;ACRmC;AACiI;AAC1E;AAC9B;AACrB;;AAExC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,MAAA2D,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAiBA,MAAMC,eAAe,CAAC;EAClB9D,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAAC+D,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACC,QAAQ,GAAG,CAAC;EACrB;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACD,QAAQ,GAAG,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACI,IAAIE,OAAOA,CAAA,EAAG;IACV,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI,CAACC,OAAO,CAAC;IAC5C;IACA;IACA,OAAOH,UAAU,GAAG,CAAC,GAAG,IAAI,CAACF,QAAQ,GAAGE,UAAU,GAAG,CAAC,GAAG,IAAI,CAACF,QAAQ;EAC1E;EACA;AACJ;AACA;AACA;AACA;EACIM,MAAMA,CAACC,UAAU,EAAEC,KAAK,EAAE;IACtB,IAAI,CAACV,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACM,OAAO,GAAG,IAAII,KAAK,CAACF,UAAU,CAAC;IACpC,IAAI,CAACF,OAAO,CAACK,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACL,OAAO,CAACxE,MAAM,CAAC;IAC5C,IAAI,CAAC8E,SAAS,GAAGH,KAAK,CAACI,GAAG,CAACC,IAAI,IAAI,IAAI,CAACC,UAAU,CAACD,IAAI,CAAC,CAAC;EAC7D;EACA;EACAC,UAAUA,CAACD,IAAI,EAAE;IACb;IACA,MAAME,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAACH,IAAI,CAACI,OAAO,CAAC;IACzD;IACA,IAAI,CAACC,iBAAiB,CAACH,aAAa,EAAEF,IAAI,CAAC;IAC3C;IACA;IACA,IAAI,CAACf,WAAW,GAAGiB,aAAa,GAAGF,IAAI,CAACI,OAAO;IAC/C,OAAO,IAAIE,YAAY,CAAC,IAAI,CAACpB,QAAQ,EAAEgB,aAAa,CAAC;EACzD;EACA;EACAC,gBAAgBA,CAACI,QAAQ,EAAE;IACvB,IAAIA,QAAQ,GAAG,IAAI,CAACf,OAAO,CAACxE,MAAM,KAAK,OAAOwF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnF,MAAMC,KAAK,CAAC,oCAAoCF,QAAQ,iBAAiB,GACrE,mBAAmB,IAAI,CAACf,OAAO,CAACxE,MAAM,IAAI,CAAC;IACnD;IACA;IACA,IAAIkF,aAAa,GAAG,CAAC,CAAC;IACtB,IAAIQ,WAAW,GAAG,CAAC,CAAC;IACpB;IACA,GAAG;MACC;MACA,IAAI,IAAI,CAACzB,WAAW,GAAGsB,QAAQ,GAAG,IAAI,CAACf,OAAO,CAACxE,MAAM,EAAE;QACnD,IAAI,CAAC2F,QAAQ,CAAC,CAAC;QACfT,aAAa,GAAG,IAAI,CAACV,OAAO,CAACoB,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC3B,WAAW,CAAC;QACzDyB,WAAW,GAAG,IAAI,CAACG,gBAAgB,CAACX,aAAa,CAAC;QAClD;MACJ;MACAA,aAAa,GAAG,IAAI,CAACV,OAAO,CAACoB,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC3B,WAAW,CAAC;MACzD;MACA,IAAIiB,aAAa,IAAI,CAAC,CAAC,EAAE;QACrB,IAAI,CAACS,QAAQ,CAAC,CAAC;QACfT,aAAa,GAAG,IAAI,CAACV,OAAO,CAACoB,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC3B,WAAW,CAAC;QACzDyB,WAAW,GAAG,IAAI,CAACG,gBAAgB,CAACX,aAAa,CAAC;QAClD;MACJ;MACAQ,WAAW,GAAG,IAAI,CAACG,gBAAgB,CAACX,aAAa,CAAC;MAClD;MACA;MACA,IAAI,CAACjB,WAAW,GAAGiB,aAAa,GAAG,CAAC;MACpC;MACA;IACJ,CAAC,QAAQQ,WAAW,GAAGR,aAAa,GAAGK,QAAQ,IAAIG,WAAW,IAAI,CAAC;IACnE;IACA;IACA,OAAOpB,IAAI,CAACC,GAAG,CAACW,aAAa,EAAE,CAAC,CAAC;EACrC;EACA;EACAS,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC1B,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,QAAQ,EAAE;IACf;IACA,KAAK,IAAI4B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACtB,OAAO,CAACxE,MAAM,EAAE8F,CAAC,EAAE,EAAE;MAC1C,IAAI,CAACtB,OAAO,CAACsB,CAAC,CAAC,GAAGxB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,OAAO,CAACsB,CAAC,CAAC,GAAG,CAAC,CAAC;IACtD;EACJ;EACA;AACJ;AACA;AACA;EACID,gBAAgBA,CAACX,aAAa,EAAE;IAC5B,KAAK,IAAIY,CAAC,GAAGZ,aAAa,GAAG,CAAC,EAAEY,CAAC,GAAG,IAAI,CAACtB,OAAO,CAACxE,MAAM,EAAE8F,CAAC,EAAE,EAAE;MAC1D,IAAI,IAAI,CAACtB,OAAO,CAACsB,CAAC,CAAC,IAAI,CAAC,EAAE;QACtB,OAAOA,CAAC;MACZ;IACJ;IACA;IACA,OAAO,IAAI,CAACtB,OAAO,CAACxE,MAAM;EAC9B;EACA;EACAqF,iBAAiBA,CAACU,KAAK,EAAEf,IAAI,EAAE;IAC3B,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,IAAI,CAACI,OAAO,EAAEU,CAAC,EAAE,EAAE;MACnC,IAAI,CAACtB,OAAO,CAACuB,KAAK,GAAGD,CAAC,CAAC,GAAGd,IAAI,CAACZ,OAAO;IAC1C;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMkB,YAAY,CAAC;EACfpF,WAAWA,CAAC8F,GAAG,EAAEC,GAAG,EAAE;IAClB,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,IAAIrD,yDAAc,CAAC,eAAe,CAAC;AAEzD,MAAMsD,WAAW,CAAC;EACdjG,WAAWA,CAACkG,QAAQ,EAAEC,SAAS,EAAE;IAC7B,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,QAAQ,GAAG,CAAC;EACrB;EACA;EACA,IAAInC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACkC,QAAQ;EACxB;EACA,IAAIlC,OAAOA,CAACvG,KAAK,EAAE;IACf,IAAI,CAACyI,QAAQ,GAAGhC,IAAI,CAACkC,KAAK,CAAC7C,2EAAoB,CAAC9F,KAAK,CAAC,CAAC;EAC3D;EACA;EACA,IAAIuH,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACmB,QAAQ;EACxB;EACA,IAAInB,OAAOA,CAACvH,KAAK,EAAE;IACf,IAAI,CAAC0I,QAAQ,GAAGjC,IAAI,CAACkC,KAAK,CAAC7C,2EAAoB,CAAC9F,KAAK,CAAC,CAAC;EAC3D;EACA;AACJ;AACA;AACA;EACI4I,SAASA,CAACC,QAAQ,EAAE7I,KAAK,EAAE;IACvB,IAAI,CAACuI,QAAQ,CAACO,aAAa,CAACC,KAAK,CAACF,QAAQ,CAAC,GAAG7I,KAAK;EACvD;EACA;IAAS,IAAI,CAACgJ,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFZ,WAAW,EAArBjJ,+DAAE,CAAqCA,qDAAa,GAApDA,+DAAE,CAA+DgJ,aAAa;IAAA,CAA4D;EAAE;EAC5O;IAAS,IAAI,CAACe,IAAI,kBAD8E/J,+DAAE;MAAAwB,IAAA,EACJyH,WAAW;MAAA5E,SAAA;MAAA4F,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,yBAAAzF,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADT3E,yDAAE,YAAA4E,GAAA,CAAAsC,OAAA,aAAAtC,GAAA,CAAAsD,OAAA;QAAA;MAAA;MAAAoC,MAAA;QAAApD,OAAA;QAAAgB,OAAA;MAAA;MAAAqC,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFzK,iEAAE;MAAA2K,kBAAA,EAAAjE,GAAA;MAAApC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAmG,qBAAAjG,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3E,6DAAE;UAAFA,4DAAE,YACqT,CAAC;UADxTA,0DAAE,EACkV,CAAC;UADrVA,0DAAE,CAC0V,CAAC;QAAA;MAAA;MAAA+K,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAA69D;EAAE;AACh6E;AACA;EAAA,QAAA3C,SAAA,oBAAAA,SAAA,KAHoGtI,+DAAE,CAGXiJ,WAAW,EAAc,CAAC;IACzGzH,IAAI,EAAEoE,oDAAS;IACfuF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEb,QAAQ,EAAE,aAAa;MAAEc,IAAI,EAAE;QACvD,OAAO,EAAE,eAAe;QACxB;QACA;QACA,gBAAgB,EAAE,SAAS;QAC3B,gBAAgB,EAAE;MACtB,CAAC;MAAEL,aAAa,EAAEnF,4DAAiB,CAACyF,IAAI;MAAEL,eAAe,EAAEnF,kEAAuB,CAACyF,MAAM;MAAEf,UAAU,EAAE,IAAI;MAAE/F,QAAQ,EAAE,8EAA8E;MAAEsG,MAAM,EAAE,CAAC,62DAA62D;IAAE,CAAC;EAC5kE,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEvJ,IAAI,EAAExB,qDAAa8J;EAAC,CAAC,EAAE;IAAEtI,IAAI,EAAEgK,SAAS;IAAEC,UAAU,EAAE,CAAC;MACxEjK,IAAI,EAAEuE,mDAAQA;IAClB,CAAC,EAAE;MACCvE,IAAI,EAAEwE,iDAAM;MACZmF,IAAI,EAAE,CAACnC,aAAa;IACxB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE9B,OAAO,EAAE,CAAC;MACnC1F,IAAI,EAAEyE,gDAAKA;IACf,CAAC,CAAC;IAAEiC,OAAO,EAAE,CAAC;MACV1G,IAAI,EAAEyE,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMyF,eAAe,CAAC;EAClB1I,WAAWA,CAACkG,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAyC,kBAAkBA,CAAA,EAAG;IACjBtF,gEAAQ,CAAC,IAAI,CAACuF,MAAM,EAAE,IAAI,CAAC1C,QAAQ,CAAC;EACxC;EACA;IAAS,IAAI,CAACS,IAAI,YAAAkC,wBAAAhC,CAAA;MAAA,YAAAA,CAAA,IAAwF6B,eAAe,EA7BzB1L,+DAAE,CA6ByCA,qDAAa;IAAA,CAA4C;EAAE;EACtM;IAAS,IAAI,CAAC+J,IAAI,kBA9B8E/J,+DAAE;MAAAwB,IAAA,EA8BJkK,eAAe;MAAArH,SAAA;MAAAyH,cAAA,WAAAC,+BAAApH,EAAA,EAAAC,GAAA,EAAAoH,QAAA;QAAA,IAAArH,EAAA;UA9Bb3E,4DAAE,CAAAgM,QAAA,EA8BwI1F,2DAAO;QAAA;QAAA,IAAA3B,EAAA;UAAA,IAAAuH,EAAA;UA9BjJlM,4DAAE,CAAAkM,EAAA,GAAFlM,yDAAE,QAAA4E,GAAA,CAAAgH,MAAA,GAAAM,EAAA;QAAA;MAAA;MAAA1B,UAAA;MAAAC,QAAA,GAAFzK,iEAAE;MAAA2K,kBAAA,EAAA/D,GAAA;MAAAtC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA4H,yBAAA1H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3E,6DAAE,CAAA2G,GAAA;UAAF3G,0DAAE,EA8BsQ,CAAC;UA9BzQA,4DAAE,YA8B0S,CAAC;UA9B7SA,0DAAE,KA8BoW,CAAC;UA9BvWA,0DAAE,CA8B0W,CAAC;UA9B7WA,0DAAE,KA8BqY,CAAC;QAAA;MAAA;MAAAgL,aAAA;MAAAC,eAAA;IAAA,EAAoG;EAAE;AACllB;AACA;EAAA,QAAA3C,SAAA,oBAAAA,SAAA,KAhCoGtI,+DAAE,CAgCX0L,eAAe,EAAc,CAAC;IAC7GlK,IAAI,EAAEoE,oDAAS;IACfuF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,4CAA4C;MAAEH,eAAe,EAAEnF,kEAAuB,CAACyF,MAAM;MAAEP,aAAa,EAAEnF,4DAAiB,CAACyF,IAAI;MAAEd,UAAU,EAAE,IAAI;MAAE/F,QAAQ,EAAE;IAA2M,CAAC;EACrY,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEjD,IAAI,EAAExB,qDAAa8J;EAAC,CAAC,CAAC,EAAkB;IAAE8B,MAAM,EAAE,CAAC;MACxEpK,IAAI,EAAE0E,0DAAe;MACrBiF,IAAI,EAAE,CAAC7E,2DAAO,EAAE;QAAEgG,WAAW,EAAE;MAAK,CAAC;IACzC,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5B;IAAS,IAAI,CAAC5C,IAAI,YAAA6C,kCAAA3C,CAAA;MAAA,YAAAA,CAAA,IAAwF0C,yBAAyB;IAAA,CAAmD;EAAE;EACxL;IAAS,IAAI,CAACE,IAAI,kBA7C8EzM,+DAAE;MAAAwB,IAAA,EA6CJ+K,yBAAyB;MAAAlI,SAAA;MAAA4F,SAAA;MAAAO,UAAA;IAAA,EAAkI;EAAE;AAC/P;AACA;EAAA,QAAAlC,SAAA,oBAAAA,SAAA,KA/CoGtI,+DAAE,CA+CXuM,yBAAyB,EAAc,CAAC;IACvH/K,IAAI,EAAE2E,oDAAS;IACfgF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAkB,CAAC;MACpCb,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMmC,6BAA6B,CAAC;EAChC;IAAS,IAAI,CAAChD,IAAI,YAAAiD,sCAAA/C,CAAA;MAAA,YAAAA,CAAA,IAAwF8C,6BAA6B;IAAA,CAAmD;EAAE;EAC5L;IAAS,IAAI,CAACF,IAAI,kBA7D8EzM,+DAAE;MAAAwB,IAAA,EA6DJmL,6BAA6B;MAAAtI,SAAA;MAAA4F,SAAA;MAAAO,UAAA;IAAA,EAAyH;EAAE;AAC1P;AACA;EAAA,QAAAlC,SAAA,oBAAAA,SAAA,KA/DoGtI,+DAAE,CA+DX2M,6BAA6B,EAAc,CAAC;IAC3HnL,IAAI,EAAE2E,oDAAS;IACfgF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAuB,CAAC;MACzCb,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMqC,6BAA6B,CAAC;EAChC;IAAS,IAAI,CAAClD,IAAI,YAAAmD,sCAAAjD,CAAA;MAAA,YAAAA,CAAA,IAAwFgD,6BAA6B;IAAA,CAAmD;EAAE;EAC5L;IAAS,IAAI,CAACJ,IAAI,kBA7E8EzM,+DAAE;MAAAwB,IAAA,EA6EJqL,6BAA6B;MAAAxI,SAAA;MAAA4F,SAAA;MAAAO,UAAA;IAAA,EAAyH;EAAE;AAC1P;AACA;EAAA,QAAAlC,SAAA,oBAAAA,SAAA,KA/EoGtI,+DAAE,CA+EX6M,6BAA6B,EAAc,CAAC;IAC3HrL,IAAI,EAAE2E,oDAAS;IACfgF,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAuB,CAAC;MACzCb,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAMuC,mBAAmB,GAAG,+BAA+B;AAC3D;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbhK,WAAWA,CAAA,EAAG;IACV,IAAI,CAACiK,KAAK,GAAG,CAAC;IACd,IAAI,CAAC7D,QAAQ,GAAG,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI8D,IAAIA,CAACC,UAAU,EAAE7F,OAAO,EAAE8F,IAAI,EAAEC,SAAS,EAAE;IACvC,IAAI,CAACC,WAAW,GAAGC,cAAc,CAACJ,UAAU,CAAC;IAC7C,IAAI,CAACF,KAAK,GAAG3F,OAAO,CAACL,QAAQ;IAC7B,IAAI,CAACmC,QAAQ,GAAG9B,OAAO,CAACJ,OAAO;IAC/B,IAAI,CAACsG,KAAK,GAAGJ,IAAI;IACjB,IAAI,CAACK,UAAU,GAAGJ,SAAS;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIK,eAAeA,CAACC,WAAW,EAAEC,cAAc,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA,OAAO,IAAID,WAAW,QAAQ,IAAI,CAACL,WAAW,MAAMM,cAAc,IAAI;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACC,QAAQ,EAAEC,MAAM,EAAE;IAC9B;IACA;IACA,OAAOA,MAAM,KAAK,CAAC,GAAG,GAAG,GAAGC,IAAI,CAAC,IAAIF,QAAQ,MAAM,IAAI,CAACR,WAAW,OAAOS,MAAM,EAAE,CAAC;EACvF;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,WAAWA,CAACH,QAAQ,EAAEI,IAAI,EAAE;IACxB,OAAO,IAAIJ,QAAQ,MAAMI,IAAI,QAAQA,IAAI,GAAG,CAAC,MAAM,IAAI,CAACZ,WAAW,GAAG;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIa,QAAQA,CAACrG,IAAI,EAAEd,QAAQ,EAAEoH,QAAQ,EAAE;IAC/B;IACA,IAAIC,mBAAmB,GAAG,GAAG,GAAG,IAAI,CAACb,KAAK;IAC1C;IACA;IACA,IAAIc,0BAA0B,GAAG,CAAC,IAAI,CAACd,KAAK,GAAG,CAAC,IAAI,IAAI,CAACA,KAAK;IAC9D,IAAI,CAACe,YAAY,CAACzG,IAAI,EAAEsG,QAAQ,EAAEC,mBAAmB,EAAEC,0BAA0B,CAAC;IAClF,IAAI,CAACE,YAAY,CAAC1G,IAAI,EAAEd,QAAQ,EAAEqH,mBAAmB,EAAEC,0BAA0B,CAAC;EACtF;EACA;EACAC,YAAYA,CAACzG,IAAI,EAAEsG,QAAQ,EAAEK,YAAY,EAAEC,WAAW,EAAE;IACpD;IACA,IAAIC,aAAa,GAAG,IAAI,CAACjB,eAAe,CAACe,YAAY,EAAEC,WAAW,CAAC;IACnE;IACA;IACA,IAAIE,IAAI,GAAG,IAAI,CAACnB,UAAU,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;IACvD3F,IAAI,CAACyB,SAAS,CAACqF,IAAI,EAAE,IAAI,CAACf,eAAe,CAACc,aAAa,EAAEP,QAAQ,CAAC,CAAC;IACnEtG,IAAI,CAACyB,SAAS,CAAC,OAAO,EAAEyE,IAAI,CAAC,IAAI,CAACC,WAAW,CAACU,aAAa,EAAE7G,IAAI,CAACI,OAAO,CAAC,CAAC,CAAC;EAChF;EACA;AACJ;AACA;EACI2G,aAAaA,CAAA,EAAG;IACZ,OAAO,GAAG,IAAI,CAACvB,WAAW,OAAO,IAAI,CAAClE,QAAQ,OAAO;EACzD;EACA;AACJ;AACA;AACA;EACI0F,WAAWA,CAACC,UAAU,EAAE;IACpB,OAAO,GAAG,IAAI,CAAC3F,QAAQ,MAAM,IAAI,CAAC6E,WAAW,CAACc,UAAU,EAAE,CAAC,CAAC,EAAE;EAClE;EACA;AACJ;AACA;AACA;AACA;EACIC,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI;EACf;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASjC,UAAU,CAAC;EACrChK,WAAWA,CAACkM,cAAc,EAAE;IACxB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,cAAc,GAAGA,cAAc;EACxC;EACAhC,IAAIA,CAACC,UAAU,EAAE7F,OAAO,EAAE8F,IAAI,EAAEC,SAAS,EAAE;IACvC,KAAK,CAACH,IAAI,CAACC,UAAU,EAAE7F,OAAO,EAAE8F,IAAI,EAAEC,SAAS,CAAC;IAChD,IAAI,CAAC6B,cAAc,GAAG3B,cAAc,CAAC,IAAI,CAAC2B,cAAc,CAAC;IACzD,IAAI,CAACnC,mBAAmB,CAACoC,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,KAC7C,OAAO5G,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMC,KAAK,CAAC,kBAAkB,IAAI,CAAC2G,cAAc,qBAAqB,CAAC;IAC3E;EACJ;EACAV,YAAYA,CAAC1G,IAAI,EAAEd,QAAQ,EAAE;IACzBc,IAAI,CAACyB,SAAS,CAAC,KAAK,EAAE,IAAI,CAACsE,eAAe,CAAC,IAAI,CAACqB,cAAc,EAAElI,QAAQ,CAAC,CAAC;IAC1Ec,IAAI,CAACyB,SAAS,CAAC,QAAQ,EAAEyE,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACiB,cAAc,EAAEpH,IAAI,CAACZ,OAAO,CAAC,CAAC,CAAC;EACvF;EACA8H,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,QAAQ,EAAEhB,IAAI,CAAC,GAAG,IAAI,CAACc,WAAW,CAAC,IAAI,CAACI,cAAc,CAAC,MAAM,IAAI,CAACL,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;EACjG;EACAO,KAAKA,CAACC,IAAI,EAAE;IACRA,IAAI,CAACC,aAAa,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACpC,IAAID,IAAI,CAACE,MAAM,EAAE;MACbF,IAAI,CAACE,MAAM,CAACC,OAAO,CAAC1H,IAAI,IAAI;QACxBA,IAAI,CAACyB,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;QAC3BzB,IAAI,CAACyB,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC;MAClC,CAAC,CAAC;IACN;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkG,eAAe,SAASzC,UAAU,CAAC;EACrChK,WAAWA,CAACrC,KAAK,EAAE;IACf,KAAK,CAAC,CAAC;IACP,IAAI,CAAC+O,WAAW,CAAC/O,KAAK,CAAC;EAC3B;EACA6N,YAAYA,CAAC1G,IAAI,EAAEd,QAAQ,EAAEyH,YAAY,EAAEC,WAAW,EAAE;IACpD,IAAIiB,oBAAoB,GAAGlB,YAAY,GAAG,IAAI,CAACmB,cAAc;IAC7D,IAAI,CAACC,cAAc,GAAG,IAAI,CAACnC,eAAe,CAACiC,oBAAoB,EAAEjB,WAAW,CAAC;IAC7E;IACA;IACA;IACA5G,IAAI,CAACyB,SAAS,CAAC,WAAW,EAAE,IAAI,CAACsE,eAAe,CAAC,IAAI,CAACgC,cAAc,EAAE7I,QAAQ,CAAC,CAAC;IAChFc,IAAI,CAACyB,SAAS,CAAC,YAAY,EAAEyE,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC4B,cAAc,EAAE/H,IAAI,CAACZ,OAAO,CAAC,CAAC,CAAC;EAC3F;EACA8H,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CACH,eAAe,EACfhB,IAAI,CAAC,GAAG,IAAI,CAACc,WAAW,CAAC,IAAI,CAACe,cAAc,CAAC,MAAM,IAAI,CAAChB,aAAa,CAAC,CAAC,EAAE,CAAC,CAC7E;EACL;EACAO,KAAKA,CAACC,IAAI,EAAE;IACRA,IAAI,CAACC,aAAa,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;IAC3CD,IAAI,CAACE,MAAM,CAACC,OAAO,CAAC1H,IAAI,IAAI;MACxBA,IAAI,CAACyB,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC;MACjCzB,IAAI,CAACyB,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC;IACtC,CAAC,CAAC;EACN;EACAmG,WAAWA,CAAC/O,KAAK,EAAE;IACf,MAAMmP,UAAU,GAAGnP,KAAK,CAACoP,KAAK,CAAC,GAAG,CAAC;IACnC,IAAID,UAAU,CAAChN,MAAM,KAAK,CAAC,KAAK,OAAOwF,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC5E,MAAMC,KAAK,CAAC,uDAAuD5H,KAAK,GAAG,CAAC;IAChF;IACA,IAAI,CAACiP,cAAc,GAAGI,UAAU,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGE,UAAU,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC;EAC/E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,aAAa,SAASjD,UAAU,CAAC;EACnCwB,YAAYA,CAAC1G,IAAI,EAAEd,QAAQ,EAAE;IACzB;IACA,IAAI2I,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAACvG,QAAQ;IAC9C;IACA,IAAI8G,mBAAmB,GAAG,CAAC,IAAI,CAACjD,KAAK,GAAG,CAAC,IAAI,IAAI,CAACA,KAAK;IACvD;IACA,IAAI4C,cAAc,GAAG,IAAI,CAACnC,eAAe,CAACiC,oBAAoB,EAAEO,mBAAmB,CAAC;IACpFpI,IAAI,CAACyB,SAAS,CAAC,KAAK,EAAE,IAAI,CAACsE,eAAe,CAACgC,cAAc,EAAE7I,QAAQ,CAAC,CAAC;IACrEc,IAAI,CAACyB,SAAS,CAAC,QAAQ,EAAEyE,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC4B,cAAc,EAAE/H,IAAI,CAACZ,OAAO,CAAC,CAAC,CAAC;EAClF;EACAkI,KAAKA,CAACC,IAAI,EAAE;IACR,IAAIA,IAAI,CAACE,MAAM,EAAE;MACbF,IAAI,CAACE,MAAM,CAACC,OAAO,CAAC1H,IAAI,IAAI;QACxBA,IAAI,CAACyB,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;QAC3BzB,IAAI,CAACyB,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC;MAClC,CAAC,CAAC;IACN;EACJ;AACJ;AACA;AACA,SAASyE,IAAIA,CAACmC,GAAG,EAAE;EACf,OAAO,QAAQA,GAAG,GAAG;AACzB;AACA;AACA,SAAS5C,cAAcA,CAAC5M,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAACyP,KAAK,CAAC,eAAe,CAAC,GAAGzP,KAAK,GAAG,GAAGA,KAAK,IAAI;AAC9D;;AAEA;AACA;AACA;AACA,MAAM0P,YAAY,GAAG,KAAK;AAC1B,MAAMC,WAAW,CAAC;EACdtN,WAAWA,CAACkG,QAAQ,EAAEqH,IAAI,EAAE;IACxB,IAAI,CAACrH,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACqH,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACA;EACA,IAAIpD,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACI,KAAK;EACrB;EACA,IAAIJ,IAAIA,CAACzM,KAAK,EAAE;IACZ,IAAI,CAAC6M,KAAK,GAAGpG,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACkC,KAAK,CAAC7C,2EAAoB,CAAC9F,KAAK,CAAC,CAAC,CAAC;EACrE;EACA;EACA,IAAIwM,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACqD,OAAO;EACvB;EACA,IAAIrD,UAAUA,CAACxM,KAAK,EAAE;IAClB,IAAI,CAAC6P,OAAO,GAAG,GAAG7P,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,EAAE;EAClD;EACA;EACA,IAAI8P,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAASA,CAAC9P,KAAK,EAAE;IACjB,MAAMgQ,QAAQ,GAAG,GAAGhQ,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,EAAE;IAChD,IAAIgQ,QAAQ,KAAK,IAAI,CAACD,UAAU,EAAE;MAC9B,IAAI,CAACA,UAAU,GAAGC,QAAQ;MAC1B,IAAI,CAACC,cAAc,CAAC,IAAI,CAACF,UAAU,CAAC;IACxC;EACJ;EACAxN,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC2N,UAAU,CAAC,CAAC;IACjB,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACC,YAAY,CAAC,CAAC;EACvB;EACA;EACAH,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAACzD,IAAI,KAAK,OAAO9E,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC/D,MAAMC,KAAK,CAAC,iDAAiD,GAAG,mCAAmC,CAAC;IACxG;EACJ;EACA;EACAuI,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACJ,UAAU,EAAE;MAClB,IAAI,CAACE,cAAc,CAAC,KAAK,CAAC;IAC9B;EACJ;EACA;EACAA,cAAcA,CAACH,SAAS,EAAE;IACtB,IAAI,IAAI,CAACQ,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC7B,KAAK,CAAC,IAAI,CAAC;IAChC;IACA,IAAIqB,SAAS,KAAKJ,YAAY,EAAE;MAC5B,IAAI,CAACY,WAAW,GAAG,IAAIhB,aAAa,CAAC,CAAC;IAC1C,CAAC,MACI,IAAIQ,SAAS,IAAIA,SAAS,CAAC/H,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MAC/C,IAAI,CAACuI,WAAW,GAAG,IAAIxB,eAAe,CAACgB,SAAS,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAACQ,WAAW,GAAG,IAAIhC,eAAe,CAACwB,SAAS,CAAC;IACrD;EACJ;EACA;EACAO,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACE,gBAAgB,EAAE;MACxB,IAAI,CAACA,gBAAgB,GAAG,IAAIpK,eAAe,CAAC,CAAC;IACjD;IACA,MAAMQ,OAAO,GAAG,IAAI,CAAC4J,gBAAgB;IACrC,MAAMzJ,KAAK,GAAG,IAAI,CAAC8H,MAAM,CAAC/L,MAAM,CAACsE,IAAI,IAAI,CAACA,IAAI,CAACqB,SAAS,IAAIrB,IAAI,CAACqB,SAAS,KAAK,IAAI,CAAC;IACpF,MAAMkE,SAAS,GAAG,IAAI,CAACkD,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC5P,KAAK,GAAG,KAAK;IACrD,IAAI,CAACuQ,gBAAgB,CAAC3J,MAAM,CAAC,IAAI,CAAC6F,IAAI,EAAE3F,KAAK,CAAC;IAC9C,IAAI,CAACwJ,WAAW,CAAC/D,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE7F,OAAO,EAAE,IAAI,CAAC8F,IAAI,EAAEC,SAAS,CAAC;IACrE5F,KAAK,CAAC+H,OAAO,CAAC,CAAC1H,IAAI,EAAEqJ,KAAK,KAAK;MAC3B,MAAMC,GAAG,GAAG9J,OAAO,CAACM,SAAS,CAACuJ,KAAK,CAAC;MACpC,IAAI,CAACF,WAAW,CAAC9C,QAAQ,CAACrG,IAAI,EAAEsJ,GAAG,CAACtI,GAAG,EAAEsI,GAAG,CAACrI,GAAG,CAAC;IACrD,CAAC,CAAC;IACF,IAAI,CAACuG,aAAa,CAAC,IAAI,CAAC2B,WAAW,CAACjC,iBAAiB,CAAC,CAAC,CAAC;EAC5D;EACA;EACAM,aAAaA,CAAC5F,KAAK,EAAE;IACjB,IAAIA,KAAK,EAAE;MACP,IAAI,CAACR,QAAQ,CAACO,aAAa,CAACC,KAAK,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;IAC1D;EACJ;EACA;IAAS,IAAI,CAACC,IAAI,YAAA0H,oBAAAxH,CAAA;MAAA,YAAAA,CAAA,IAAwFyG,WAAW,EAxZrBtQ,+DAAE,CAwZqCA,qDAAa,GAxZpDA,+DAAE,CAwZ+DmE,6DAAiB;IAAA,CAA4D;EAAE;EAChP;IAAS,IAAI,CAAC4F,IAAI,kBAzZ8E/J,+DAAE;MAAAwB,IAAA,EAyZJ8O,WAAW;MAAAjM,SAAA;MAAAyH,cAAA,WAAAyF,2BAAA5M,EAAA,EAAAC,GAAA,EAAAoH,QAAA;QAAA,IAAArH,EAAA;UAzZT3E,4DAAE,CAAAgM,QAAA,EA8Z5C/C,WAAW;QAAA;QAAA,IAAAtE,EAAA;UAAA,IAAAuH,EAAA;UA9Z+BlM,4DAAE,CAAAkM,EAAA,GAAFlM,yDAAE,QAAA4E,GAAA,CAAA2K,MAAA,GAAArD,EAAA;QAAA;MAAA;MAAAjC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAqH,yBAAA7M,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3E,yDAAE,SAAA4E,GAAA,CAAAwI,IAAA;QAAA;MAAA;MAAA9C,MAAA;QAAA8C,IAAA;QAAAD,UAAA;QAAAsD,SAAA;MAAA;MAAAlG,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFzK,gEAAE,CAyZ+N,CACzT;QACI0R,OAAO,EAAE1I,aAAa;QACtB2I,WAAW,EAAErB;MACjB,CAAC,CACJ,GA9Z2FtQ,iEAAE;MAAA2K,kBAAA,EAAAjE,GAAA;MAAApC,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAAmN,qBAAAjN,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAF3E,6DAAE;UAAFA,4DAAE,SA8Z+C,CAAC;UA9ZlDA,0DAAE,EA8Z4E,CAAC;UA9Z/EA,0DAAE,CA8ZoF,CAAC;QAAA;MAAA;MAAA+K,MAAA,GAAAlE,GAAA;MAAAmE,aAAA;MAAAC,eAAA;IAAA,EAA29D;EAAE;AACxpE;AACA;EAAA,QAAA3C,SAAA,oBAAAA,SAAA,KAhaoGtI,+DAAE,CAgaXsQ,WAAW,EAAc,CAAC;IACzG9O,IAAI,EAAEoE,oDAAS;IACfuF,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEb,QAAQ,EAAE,aAAa;MAAEc,IAAI,EAAE;QACvD,OAAO,EAAE,eAAe;QACxB;QACA;QACA,aAAa,EAAE;MACnB,CAAC;MAAEwG,SAAS,EAAE,CACV;QACIH,OAAO,EAAE1I,aAAa;QACtB2I,WAAW,EAAErB;MACjB,CAAC,CACJ;MAAErF,eAAe,EAAEnF,kEAAuB,CAACyF,MAAM;MAAEP,aAAa,EAAEnF,4DAAiB,CAACyF,IAAI;MAAEd,UAAU,EAAE,IAAI;MAAE/F,QAAQ,EAAE,4CAA4C;MAAEsG,MAAM,EAAE,CAAC,62DAA62D;IAAE,CAAC;EAC1iE,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEvJ,IAAI,EAAExB,qDAAa8J;EAAC,CAAC,EAAE;IAAEtI,IAAI,EAAE2C,6DAAiB;IAAEsH,UAAU,EAAE,CAAC;MAChFjK,IAAI,EAAEuE,mDAAQA;IAClB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEwJ,MAAM,EAAE,CAAC;MAClC/N,IAAI,EAAE0E,0DAAe;MACrBiF,IAAI,EAAE,CAAClC,WAAW,EAAE;QAAEqD,WAAW,EAAE;MAAK,CAAC;IAC7C,CAAC,CAAC;IAAEc,IAAI,EAAE,CAAC;MACP5L,IAAI,EAAEyE,gDAAKA;IACf,CAAC,CAAC;IAAEkH,UAAU,EAAE,CAAC;MACb3L,IAAI,EAAEyE,gDAAKA;IACf,CAAC,CAAC;IAAEwK,SAAS,EAAE,CAAC;MACZjP,IAAI,EAAEyE,gDAAKA;IACf,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMd,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAACwE,IAAI,YAAAmI,0BAAAjI,CAAA;MAAA,YAAAA,CAAA,IAAwF1E,iBAAiB;IAAA,CAAkD;EAAE;EAC/K;IAAS,IAAI,CAAC4M,IAAI,kBA5b8E/R,8DAAE;MAAAwB,IAAA,EA4bS2D;IAAiB,EAcvF;EAAE;EACvC;IAAS,IAAI,CAAC8M,IAAI,kBA3c8EjS,8DAAE;MAAAwF,OAAA,GA2csCe,iEAAa,EAC7IC,mEAAe,EAAED,iEAAa,EAC9BC,mEAAe;IAAA,EAAI;EAAE;AACjC;AACA;EAAA,QAAA8B,SAAA,oBAAAA,SAAA,KA/coGtI,+DAAE,CA+cXmF,iBAAiB,EAAc,CAAC;IAC/G3D,IAAI,EAAE4E,mDAAQ;IACd+E,IAAI,EAAE,CAAC;MACC3F,OAAO,EAAE,CACLe,iEAAa,EACbC,mEAAe,EACf8J,WAAW,EACXrH,WAAW,EACXyC,eAAe,EACfiB,6BAA6B,EAC7BE,6BAA6B,EAC7BN,yBAAyB,CAC5B;MACD4F,OAAO,EAAE,CACL7B,WAAW,EACXrH,WAAW,EACXyC,eAAe,EACfnF,iEAAa,EACbC,mEAAe,EACfmG,6BAA6B,EAC7BE,6BAA6B,EAC7BN,yBAAyB;IAEjC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,MAAM6F,gBAAgB,GAAGtL,eAAe;;AAExC;AACA;AACA", "sources": ["./src/app/features/dashboard/components/dashboard/dashboard.component.ts", "./src/app/features/dashboard/components/dashboard/dashboard.component.html", "./src/app/features/dashboard/dashboard.module.ts", "./src/app/features/dashboard/dashboard.routes.ts", "./node_modules/@angular/material/fesm2022/grid-list.mjs"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { User } from '../../../../core/models';\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit {\n  user: User | null = null;\n\n  quickActions = [\n    {\n      title: 'Scanner QR',\n      description: 'Scanner un code QR pour gagner des points',\n      icon: 'qr_code_scanner',\n      route: '/qr-scanner',\n      color: 'primary'\n    },\n    {\n      title: 'Récompenses',\n      description: 'Découvrir les récompenses disponibles',\n      icon: 'card_giftcard',\n      route: '/rewards',\n      color: 'accent'\n    },\n    {\n      title: 'Mon Profil',\n      description: 'Voir et modifier mon profil',\n      icon: 'person',\n      route: '/profile',\n      color: 'primary'\n    }\n  ];\n\n  stats = [\n    {\n      title: 'Points totaux',\n      value: '0',\n      icon: 'stars',\n      color: '#ffd700'\n    },\n    {\n      title: 'Actions validées',\n      value: '0',\n      icon: 'verified',\n      color: '#4caf50'\n    },\n    {\n      title: 'Récompenses obtenues',\n      value: '0',\n      icon: 'card_giftcard',\n      color: '#ff9800'\n    }\n  ];\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        this.updateStats(user);\n      }\n    });\n  }\n\n  private updateStats(user: User): void {\n    this.stats[0].value = user.points.toString();\n    \n    // Count validated actions from history\n    const validatedActions = user.history?.filter(h => h.type === 'validated').length || 0;\n    this.stats[1].value = validatedActions.toString();\n    \n    // Count spent points (rewards obtained)\n    const rewardsObtained = user.history?.filter(h => h.type === 'spent').length || 0;\n    this.stats[2].value = rewardsObtained.toString();\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n\n  getRoleDisplayName(role: string): string {\n    const roleNames: { [key: string]: string } = {\n      'user': 'Utilisateur',\n      'provider': 'Partenaire',\n      'validator': 'Validateur',\n      'admin': 'Administrateur'\n    };\n    return roleNames[role] || role;\n  }\n\n  getActivityIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'earned': 'add_circle',\n      'spent': 'remove_circle',\n      'transferred': 'swap_horiz',\n      'validated': 'verified',\n      'bonus': 'star'\n    };\n    return iconMap[type] || 'circle';\n  }\n\n  getActionColor(color: string): string {\n    const colorMap: { [key: string]: string } = {\n      'primary': '#667eea',\n      'accent': '#f093fb',\n      'warn': '#ff6b6b'\n    };\n    return colorMap[color] || '#667eea';\n  }\n\n  getActionButtonText(title: string): string {\n    const buttonTextMap: { [key: string]: string } = {\n      'Scanner QR': 'Scanner',\n      'Récompenses': 'Explorer',\n      'Mon Profil': 'Voir'\n    };\n    return buttonTextMap[title] || 'Aller';\n  }\n}\n", "<div class=\"dashboard-container\" *ngIf=\"user\">\n  <!-- Welcome section -->\n  <div class=\"welcome-section slide-up\">\n    <div class=\"welcome-content\">\n      <h1 class=\"bounce-in\">{{ getGreeting() }}, {{ user.name }}!</h1>\n      <p class=\"user-info fade-in\">\n        <mat-icon>location_on</mat-icon>\n        {{ user.city }} • {{ getRoleDisplayName(user.role) }}\n      </p>\n    </div>\n    <div class=\"points-badge glow\">\n      <mat-icon>stars</mat-icon>\n      <span>{{ user.points }} points</span>\n    </div>\n  </div>\n\n  <!-- Stats cards -->\n  <div class=\"stats-section\">\n    <h2 class=\"section-title slide-in-left\">📊 Mes statistiques</h2>\n    <div class=\"stats-grid\">\n      <mat-card *ngFor=\"let stat of stats; let i = index\"\n                class=\"stat-card floating-card\"\n                [style.animation-delay]=\"(i * 0.1) + 's'\">\n        <mat-card-content>\n          <div class=\"stat-content\">\n            <div class=\"stat-icon\" [style.color]=\"stat.color\">\n              <mat-icon>{{ stat.icon }}</mat-icon>\n            </div>\n            <div class=\"stat-info\">\n              <h3 class=\"counter-animation\">{{ stat.value }}</h3>\n              <p>{{ stat.title }}</p>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Quick actions -->\n  <div class=\"actions-section\">\n    <h2 class=\"section-title slide-in-left\">⚡ Actions rapides</h2>\n    <div class=\"actions-grid\">\n      <mat-card *ngFor=\"let action of quickActions; let i = index\"\n                class=\"action-card floating-card\"\n                [style.animation-delay]=\"(i * 0.1) + 's'\">\n        <mat-card-content>\n          <div class=\"action-content\">\n            <div class=\"action-icon-wrapper\">\n              <mat-icon class=\"action-icon\" [style.color]=\"getActionColor(action.color)\">\n                {{ action.icon }}\n              </mat-icon>\n            </div>\n            <h3>{{ action.title }}</h3>\n            <p>{{ action.description }}</p>\n            <button mat-raised-button\n                    [color]=\"action.color\"\n                    [routerLink]=\"action.route\"\n                    class=\"action-button\">\n              <mat-icon>{{ action.icon }}</mat-icon>\n              {{ getActionButtonText(action.title) }}\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Recent activity -->\n  <div class=\"activity-section\" *ngIf=\"user.history && user.history.length > 0\">\n    <h2>Activité récente</h2>\n    <mat-card class=\"activity-card\">\n      <mat-card-content>\n        <div class=\"activity-list\">\n          <div *ngFor=\"let transaction of user.history.slice(-5)\" class=\"activity-item\">\n            <div class=\"activity-icon\">\n              <mat-icon [class]=\"'activity-' + transaction.type\">\n                {{ getActivityIcon(transaction.type) }}\n              </mat-icon>\n            </div>\n            <div class=\"activity-info\">\n              <p class=\"activity-description\">{{ transaction.description }}</p>\n              <p class=\"activity-date\">{{ transaction.timestamp | date:'short' }}</p>\n            </div>\n            <div class=\"activity-points\" [class]=\"transaction.points > 0 ? 'positive' : 'negative'\">\n              {{ transaction.points > 0 ? '+' : '' }}{{ transaction.points }} pts\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Empty state for new users -->\n  <div class=\"empty-state\" *ngIf=\"!user.history || user.history.length === 0\">\n    <mat-card class=\"empty-card\">\n      <mat-card-content>\n        <div class=\"empty-content\">\n          <mat-icon class=\"empty-icon\">explore</mat-icon>\n          <h3>Commencez votre aventure!</h3>\n          <p>Scannez votre premier code QR ou participez à une activité communautaire pour gagner vos premiers points.</p>\n          <button mat-raised-button color=\"primary\" routerLink=\"/qr-scanner\">\n            <mat-icon>qr_code_scanner</mat-icon>\n            Scanner un QR Code\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatGridListModule } from '@angular/material/grid-list';\n\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { dashboardRoutes } from './dashboard.routes';\n\n@NgModule({\n  declarations: [\n    DashboardComponent\n  ],\n  imports: [\n    CommonModule,\n    RouterModule.forChild(dashboardRoutes),\n    \n    // Material modules\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatGridListModule\n  ]\n})\nexport class DashboardModule { }\n", "import { Routes } from '@angular/router';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\n\nexport const dashboardRoutes: Routes = [\n  {\n    path: '',\n    component: DashboardComponent\n  }\n];\n", "import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Input, ContentChildren, Directive, NgModule } from '@angular/core';\nimport { setLines, MatLine, MatLineModule, MatCommonModule } from '@angular/material/core';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/bidi';\n\n/**\n * Class for determining, from a list of tiles, the (row, col) position of each of those tiles\n * in the grid. This is necessary (rather than just rendering the tiles in normal document flow)\n * because the tiles can have a rowspan.\n *\n * The positioning algorithm greedily places each tile as soon as it encounters a gap in the grid\n * large enough to accommodate it so that the tiles still render in the same order in which they\n * are given.\n *\n * The basis of the algorithm is the use of an array to track the already placed tiles. Each\n * element of the array corresponds to a column, and the value indicates how many cells in that\n * column are already occupied; zero indicates an empty cell. Moving \"down\" to the next row\n * decrements each value in the tracking array (indicating that the column is one cell closer to\n * being free).\n *\n * @docs-private\n */\nclass TileCoordinator {\n    constructor() {\n        /** Index at which the search for the next gap will start. */\n        this.columnIndex = 0;\n        /** The current row index. */\n        this.rowIndex = 0;\n    }\n    /** Gets the total number of rows occupied by tiles */\n    get rowCount() {\n        return this.rowIndex + 1;\n    }\n    /**\n     * Gets the total span of rows occupied by tiles.\n     * Ex: A list with 1 row that contains a tile with rowspan 2 will have a total rowspan of 2.\n     */\n    get rowspan() {\n        const lastRowMax = Math.max(...this.tracker);\n        // if any of the tiles has a rowspan that pushes it beyond the total row count,\n        // add the difference to the rowcount\n        return lastRowMax > 1 ? this.rowCount + lastRowMax - 1 : this.rowCount;\n    }\n    /**\n     * Updates the tile positions.\n     * @param numColumns Amount of columns in the grid.\n     * @param tiles Tiles to be positioned.\n     */\n    update(numColumns, tiles) {\n        this.columnIndex = 0;\n        this.rowIndex = 0;\n        this.tracker = new Array(numColumns);\n        this.tracker.fill(0, 0, this.tracker.length);\n        this.positions = tiles.map(tile => this._trackTile(tile));\n    }\n    /** Calculates the row and col position of a tile. */\n    _trackTile(tile) {\n        // Find a gap large enough for this tile.\n        const gapStartIndex = this._findMatchingGap(tile.colspan);\n        // Place tile in the resulting gap.\n        this._markTilePosition(gapStartIndex, tile);\n        // The next time we look for a gap, the search will start at columnIndex, which should be\n        // immediately after the tile that has just been placed.\n        this.columnIndex = gapStartIndex + tile.colspan;\n        return new TilePosition(this.rowIndex, gapStartIndex);\n    }\n    /** Finds the next available space large enough to fit the tile. */\n    _findMatchingGap(tileCols) {\n        if (tileCols > this.tracker.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`mat-grid-list: tile with colspan ${tileCols} is wider than ` +\n                `grid with cols=\"${this.tracker.length}\".`);\n        }\n        // Start index is inclusive, end index is exclusive.\n        let gapStartIndex = -1;\n        let gapEndIndex = -1;\n        // Look for a gap large enough to fit the given tile. Empty spaces are marked with a zero.\n        do {\n            // If we've reached the end of the row, go to the next row.\n            if (this.columnIndex + tileCols > this.tracker.length) {\n                this._nextRow();\n                gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n                gapEndIndex = this._findGapEndIndex(gapStartIndex);\n                continue;\n            }\n            gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n            // If there are no more empty spaces in this row at all, move on to the next row.\n            if (gapStartIndex == -1) {\n                this._nextRow();\n                gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n                gapEndIndex = this._findGapEndIndex(gapStartIndex);\n                continue;\n            }\n            gapEndIndex = this._findGapEndIndex(gapStartIndex);\n            // If a gap large enough isn't found, we want to start looking immediately after the current\n            // gap on the next iteration.\n            this.columnIndex = gapStartIndex + 1;\n            // Continue iterating until we find a gap wide enough for this tile. Since gapEndIndex is\n            // exclusive, gapEndIndex is 0 means we didn't find a gap and should continue.\n        } while (gapEndIndex - gapStartIndex < tileCols || gapEndIndex == 0);\n        // If we still didn't manage to find a gap, ensure that the index is\n        // at least zero so the tile doesn't get pulled out of the grid.\n        return Math.max(gapStartIndex, 0);\n    }\n    /** Move \"down\" to the next row. */\n    _nextRow() {\n        this.columnIndex = 0;\n        this.rowIndex++;\n        // Decrement all spaces by one to reflect moving down one row.\n        for (let i = 0; i < this.tracker.length; i++) {\n            this.tracker[i] = Math.max(0, this.tracker[i] - 1);\n        }\n    }\n    /**\n     * Finds the end index (exclusive) of a gap given the index from which to start looking.\n     * The gap ends when a non-zero value is found.\n     */\n    _findGapEndIndex(gapStartIndex) {\n        for (let i = gapStartIndex + 1; i < this.tracker.length; i++) {\n            if (this.tracker[i] != 0) {\n                return i;\n            }\n        }\n        // The gap ends with the end of the row.\n        return this.tracker.length;\n    }\n    /** Update the tile tracker to account for the given tile in the given space. */\n    _markTilePosition(start, tile) {\n        for (let i = 0; i < tile.colspan; i++) {\n            this.tracker[start + i] = tile.rowspan;\n        }\n    }\n}\n/**\n * Simple data structure for tile position (row, col).\n * @docs-private\n */\nclass TilePosition {\n    constructor(row, col) {\n        this.row = row;\n        this.col = col;\n    }\n}\n\n/**\n * Injection token used to provide a grid list to a tile and to avoid circular imports.\n * @docs-private\n */\nconst MAT_GRID_LIST = new InjectionToken('MAT_GRID_LIST');\n\nclass MatGridTile {\n    constructor(_element, _gridList) {\n        this._element = _element;\n        this._gridList = _gridList;\n        this._rowspan = 1;\n        this._colspan = 1;\n    }\n    /** Amount of rows that the grid tile takes up. */\n    get rowspan() {\n        return this._rowspan;\n    }\n    set rowspan(value) {\n        this._rowspan = Math.round(coerceNumberProperty(value));\n    }\n    /** Amount of columns that the grid tile takes up. */\n    get colspan() {\n        return this._colspan;\n    }\n    set colspan(value) {\n        this._colspan = Math.round(coerceNumberProperty(value));\n    }\n    /**\n     * Sets the style of the grid-tile element.  Needs to be set manually to avoid\n     * \"Changed after checked\" errors that would occur with HostBinding.\n     */\n    _setStyle(property, value) {\n        this._element.nativeElement.style[property] = value;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTile, deps: [{ token: i0.ElementRef }, { token: MAT_GRID_LIST, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatGridTile, isStandalone: true, selector: \"mat-grid-tile\", inputs: { rowspan: \"rowspan\", colspan: \"colspan\" }, host: { properties: { \"attr.rowspan\": \"rowspan\", \"attr.colspan\": \"colspan\" }, classAttribute: \"mat-grid-tile\" }, exportAs: [\"matGridTile\"], ngImport: i0, template: \"<div class=\\\"mat-grid-tile-content\\\">\\n  <ng-content></ng-content>\\n</div>\\n\", styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size)}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size)}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size)}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size)}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTile, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-grid-tile', exportAs: 'matGridTile', host: {\n                        'class': 'mat-grid-tile',\n                        // Ensures that the \"rowspan\" and \"colspan\" input value is reflected in\n                        // the DOM. This is needed for the grid-tile harness.\n                        '[attr.rowspan]': 'rowspan',\n                        '[attr.colspan]': 'colspan',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, standalone: true, template: \"<div class=\\\"mat-grid-tile-content\\\">\\n  <ng-content></ng-content>\\n</div>\\n\", styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size)}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size)}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size)}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size)}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_GRID_LIST]\n                }] }], propDecorators: { rowspan: [{\n                type: Input\n            }], colspan: [{\n                type: Input\n            }] } });\nclass MatGridTileText {\n    constructor(_element) {\n        this._element = _element;\n    }\n    ngAfterContentInit() {\n        setLines(this._lines, this._element);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTileText, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatGridTileText, isStandalone: true, selector: \"mat-grid-tile-header, mat-grid-tile-footer\", queries: [{ propertyName: \"_lines\", predicate: MatLine, descendants: true }], ngImport: i0, template: \"<ng-content select=\\\"[mat-grid-avatar], [matGridAvatar]\\\"></ng-content>\\n<div class=\\\"mat-grid-list-text\\\"><ng-content select=\\\"[mat-line], [matLine]\\\"></ng-content></div>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTileText, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-grid-tile-header, mat-grid-tile-footer', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, standalone: true, template: \"<ng-content select=\\\"[mat-grid-avatar], [matGridAvatar]\\\"></ng-content>\\n<div class=\\\"mat-grid-list-text\\\"><ng-content select=\\\"[mat-line], [matLine]\\\"></ng-content></div>\\n<ng-content></ng-content>\\n\" }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }], propDecorators: { _lines: [{\n                type: ContentChildren,\n                args: [MatLine, { descendants: true }]\n            }] } });\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridAvatarCssMatStyler {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridAvatarCssMatStyler, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatGridAvatarCssMatStyler, isStandalone: true, selector: \"[mat-grid-avatar], [matGridAvatar]\", host: { classAttribute: \"mat-grid-avatar\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridAvatarCssMatStyler, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-grid-avatar], [matGridAvatar]',\n                    host: { 'class': 'mat-grid-avatar' },\n                    standalone: true,\n                }]\n        }] });\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridTileHeaderCssMatStyler {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTileHeaderCssMatStyler, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatGridTileHeaderCssMatStyler, isStandalone: true, selector: \"mat-grid-tile-header\", host: { classAttribute: \"mat-grid-tile-header\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTileHeaderCssMatStyler, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-grid-tile-header',\n                    host: { 'class': 'mat-grid-tile-header' },\n                    standalone: true,\n                }]\n        }] });\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridTileFooterCssMatStyler {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTileFooterCssMatStyler, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatGridTileFooterCssMatStyler, isStandalone: true, selector: \"mat-grid-tile-footer\", host: { classAttribute: \"mat-grid-tile-footer\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTileFooterCssMatStyler, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-grid-tile-footer',\n                    host: { 'class': 'mat-grid-tile-footer' },\n                    standalone: true,\n                }]\n        }] });\n\n/**\n * RegExp that can be used to check whether a value will\n * be allowed inside a CSS `calc()` expression.\n */\nconst cssCalcAllowedValue = /^-?\\d+((\\.\\d+)?[A-Za-z%$]?)+$/;\n/**\n * Sets the style properties for an individual tile, given the position calculated by the\n * Tile Coordinator.\n * @docs-private\n */\nclass TileStyler {\n    constructor() {\n        this._rows = 0;\n        this._rowspan = 0;\n    }\n    /**\n     * Adds grid-list layout info once it is available. Cannot be processed in the constructor\n     * because these properties haven't been calculated by that point.\n     *\n     * @param gutterSize Size of the grid's gutter.\n     * @param tracker Instance of the TileCoordinator.\n     * @param cols Amount of columns in the grid.\n     * @param direction Layout direction of the grid.\n     */\n    init(gutterSize, tracker, cols, direction) {\n        this._gutterSize = normalizeUnits(gutterSize);\n        this._rows = tracker.rowCount;\n        this._rowspan = tracker.rowspan;\n        this._cols = cols;\n        this._direction = direction;\n    }\n    /**\n     * Computes the amount of space a single 1x1 tile would take up (width or height).\n     * Used as a basis for other calculations.\n     * @param sizePercent Percent of the total grid-list space that one 1x1 tile would take up.\n     * @param gutterFraction Fraction of the gutter size taken up by one 1x1 tile.\n     * @return The size of a 1x1 tile as an expression that can be evaluated via CSS calc().\n     */\n    getBaseTileSize(sizePercent, gutterFraction) {\n        // Take the base size percent (as would be if evenly dividing the size between cells),\n        // and then subtracting the size of one gutter. However, since there are no gutters on the\n        // edges, each tile only uses a fraction (gutterShare = numGutters / numCells) of the gutter\n        // size. (Imagine having one gutter per tile, and then breaking up the extra gutter on the\n        // edge evenly among the cells).\n        return `(${sizePercent}% - (${this._gutterSize} * ${gutterFraction}))`;\n    }\n    /**\n     * Gets The horizontal or vertical position of a tile, e.g., the 'top' or 'left' property value.\n     * @param offset Number of tiles that have already been rendered in the row/column.\n     * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n     * @return Position of the tile as a CSS calc() expression.\n     */\n    getTilePosition(baseSize, offset) {\n        // The position comes the size of a 1x1 tile plus gutter for each previous tile in the\n        // row/column (offset).\n        return offset === 0 ? '0' : calc(`(${baseSize} + ${this._gutterSize}) * ${offset}`);\n    }\n    /**\n     * Gets the actual size of a tile, e.g., width or height, taking rowspan or colspan into account.\n     * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n     * @param span The tile's rowspan or colspan.\n     * @return Size of the tile as a CSS calc() expression.\n     */\n    getTileSize(baseSize, span) {\n        return `(${baseSize} * ${span}) + (${span - 1} * ${this._gutterSize})`;\n    }\n    /**\n     * Sets the style properties to be applied to a tile for the given row and column index.\n     * @param tile Tile to which to apply the styling.\n     * @param rowIndex Index of the tile's row.\n     * @param colIndex Index of the tile's column.\n     */\n    setStyle(tile, rowIndex, colIndex) {\n        // Percent of the available horizontal space that one column takes up.\n        let percentWidthPerTile = 100 / this._cols;\n        // Fraction of the vertical gutter size that each column takes up.\n        // For example, if there are 5 columns, each column uses 4/5 = 0.8 times the gutter width.\n        let gutterWidthFractionPerTile = (this._cols - 1) / this._cols;\n        this.setColStyles(tile, colIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n        this.setRowStyles(tile, rowIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n    }\n    /** Sets the horizontal placement of the tile in the list. */\n    setColStyles(tile, colIndex, percentWidth, gutterWidth) {\n        // Base horizontal size of a column.\n        let baseTileWidth = this.getBaseTileSize(percentWidth, gutterWidth);\n        // The width and horizontal position of each tile is always calculated the same way, but the\n        // height and vertical position depends on the rowMode.\n        let side = this._direction === 'rtl' ? 'right' : 'left';\n        tile._setStyle(side, this.getTilePosition(baseTileWidth, colIndex));\n        tile._setStyle('width', calc(this.getTileSize(baseTileWidth, tile.colspan)));\n    }\n    /**\n     * Calculates the total size taken up by gutters across one axis of a list.\n     */\n    getGutterSpan() {\n        return `${this._gutterSize} * (${this._rowspan} - 1)`;\n    }\n    /**\n     * Calculates the total size taken up by tiles across one axis of a list.\n     * @param tileHeight Height of the tile.\n     */\n    getTileSpan(tileHeight) {\n        return `${this._rowspan} * ${this.getTileSize(tileHeight, 1)}`;\n    }\n    /**\n     * Calculates the computed height and returns the correct style property to set.\n     * This method can be implemented by each type of TileStyler.\n     * @docs-private\n     */\n    getComputedHeight() {\n        return null;\n    }\n}\n/**\n * This type of styler is instantiated when the user passes in a fixed row height.\n * Example `<mat-grid-list cols=\"3\" rowHeight=\"100px\">`\n * @docs-private\n */\nclass FixedTileStyler extends TileStyler {\n    constructor(fixedRowHeight) {\n        super();\n        this.fixedRowHeight = fixedRowHeight;\n    }\n    init(gutterSize, tracker, cols, direction) {\n        super.init(gutterSize, tracker, cols, direction);\n        this.fixedRowHeight = normalizeUnits(this.fixedRowHeight);\n        if (!cssCalcAllowedValue.test(this.fixedRowHeight) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Invalid value \"${this.fixedRowHeight}\" set as rowHeight.`);\n        }\n    }\n    setRowStyles(tile, rowIndex) {\n        tile._setStyle('top', this.getTilePosition(this.fixedRowHeight, rowIndex));\n        tile._setStyle('height', calc(this.getTileSize(this.fixedRowHeight, tile.rowspan)));\n    }\n    getComputedHeight() {\n        return ['height', calc(`${this.getTileSpan(this.fixedRowHeight)} + ${this.getGutterSpan()}`)];\n    }\n    reset(list) {\n        list._setListStyle(['height', null]);\n        if (list._tiles) {\n            list._tiles.forEach(tile => {\n                tile._setStyle('top', null);\n                tile._setStyle('height', null);\n            });\n        }\n    }\n}\n/**\n * This type of styler is instantiated when the user passes in a width:height ratio\n * for the row height.  Example `<mat-grid-list cols=\"3\" rowHeight=\"3:1\">`\n * @docs-private\n */\nclass RatioTileStyler extends TileStyler {\n    constructor(value) {\n        super();\n        this._parseRatio(value);\n    }\n    setRowStyles(tile, rowIndex, percentWidth, gutterWidth) {\n        let percentHeightPerTile = percentWidth / this.rowHeightRatio;\n        this.baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterWidth);\n        // Use padding-top and margin-top to maintain the given aspect ratio, as\n        // a percentage-based value for these properties is applied versus the *width* of the\n        // containing block. See http://www.w3.org/TR/CSS2/box.html#margin-properties\n        tile._setStyle('marginTop', this.getTilePosition(this.baseTileHeight, rowIndex));\n        tile._setStyle('paddingTop', calc(this.getTileSize(this.baseTileHeight, tile.rowspan)));\n    }\n    getComputedHeight() {\n        return [\n            'paddingBottom',\n            calc(`${this.getTileSpan(this.baseTileHeight)} + ${this.getGutterSpan()}`),\n        ];\n    }\n    reset(list) {\n        list._setListStyle(['paddingBottom', null]);\n        list._tiles.forEach(tile => {\n            tile._setStyle('marginTop', null);\n            tile._setStyle('paddingTop', null);\n        });\n    }\n    _parseRatio(value) {\n        const ratioParts = value.split(':');\n        if (ratioParts.length !== 2 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`mat-grid-list: invalid ratio given for row-height: \"${value}\"`);\n        }\n        this.rowHeightRatio = parseFloat(ratioParts[0]) / parseFloat(ratioParts[1]);\n    }\n}\n/**\n * This type of styler is instantiated when the user selects a \"fit\" row height mode.\n * In other words, the row height will reflect the total height of the container divided\n * by the number of rows.  Example `<mat-grid-list cols=\"3\" rowHeight=\"fit\">`\n *\n * @docs-private\n */\nclass FitTileStyler extends TileStyler {\n    setRowStyles(tile, rowIndex) {\n        // Percent of the available vertical space that one row takes up.\n        let percentHeightPerTile = 100 / this._rowspan;\n        // Fraction of the horizontal gutter size that each column takes up.\n        let gutterHeightPerTile = (this._rows - 1) / this._rows;\n        // Base vertical size of a column.\n        let baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterHeightPerTile);\n        tile._setStyle('top', this.getTilePosition(baseTileHeight, rowIndex));\n        tile._setStyle('height', calc(this.getTileSize(baseTileHeight, tile.rowspan)));\n    }\n    reset(list) {\n        if (list._tiles) {\n            list._tiles.forEach(tile => {\n                tile._setStyle('top', null);\n                tile._setStyle('height', null);\n            });\n        }\n    }\n}\n/** Wraps a CSS string in a calc function */\nfunction calc(exp) {\n    return `calc(${exp})`;\n}\n/** Appends pixels to a CSS string if no units are given. */\nfunction normalizeUnits(value) {\n    return value.match(/([A-Za-z%]+)$/) ? value : `${value}px`;\n}\n\n// TODO(kara): Conditional (responsive) column count / row size.\n// TODO(kara): Re-layout on window resize / media change (debounced).\n// TODO(kara): gridTileHeader and gridTileFooter.\nconst MAT_FIT_MODE = 'fit';\nclass MatGridList {\n    constructor(_element, _dir) {\n        this._element = _element;\n        this._dir = _dir;\n        /** The amount of space between tiles. This will be something like '5px' or '2em'. */\n        this._gutter = '1px';\n    }\n    /** Amount of columns in the grid list. */\n    get cols() {\n        return this._cols;\n    }\n    set cols(value) {\n        this._cols = Math.max(1, Math.round(coerceNumberProperty(value)));\n    }\n    /** Size of the grid list's gutter in pixels. */\n    get gutterSize() {\n        return this._gutter;\n    }\n    set gutterSize(value) {\n        this._gutter = `${value == null ? '' : value}`;\n    }\n    /** Set internal representation of row height from the user-provided value. */\n    get rowHeight() {\n        return this._rowHeight;\n    }\n    set rowHeight(value) {\n        const newValue = `${value == null ? '' : value}`;\n        if (newValue !== this._rowHeight) {\n            this._rowHeight = newValue;\n            this._setTileStyler(this._rowHeight);\n        }\n    }\n    ngOnInit() {\n        this._checkCols();\n        this._checkRowHeight();\n    }\n    /**\n     * The layout calculation is fairly cheap if nothing changes, so there's little cost\n     * to run it frequently.\n     */\n    ngAfterContentChecked() {\n        this._layoutTiles();\n    }\n    /** Throw a friendly error if cols property is missing */\n    _checkCols() {\n        if (!this.cols && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`mat-grid-list: must pass in number of columns. ` + `Example: <mat-grid-list cols=\"3\">`);\n        }\n    }\n    /** Default to equal width:height if rowHeight property is missing */\n    _checkRowHeight() {\n        if (!this._rowHeight) {\n            this._setTileStyler('1:1');\n        }\n    }\n    /** Creates correct Tile Styler subtype based on rowHeight passed in by user */\n    _setTileStyler(rowHeight) {\n        if (this._tileStyler) {\n            this._tileStyler.reset(this);\n        }\n        if (rowHeight === MAT_FIT_MODE) {\n            this._tileStyler = new FitTileStyler();\n        }\n        else if (rowHeight && rowHeight.indexOf(':') > -1) {\n            this._tileStyler = new RatioTileStyler(rowHeight);\n        }\n        else {\n            this._tileStyler = new FixedTileStyler(rowHeight);\n        }\n    }\n    /** Computes and applies the size and position for all children grid tiles. */\n    _layoutTiles() {\n        if (!this._tileCoordinator) {\n            this._tileCoordinator = new TileCoordinator();\n        }\n        const tracker = this._tileCoordinator;\n        const tiles = this._tiles.filter(tile => !tile._gridList || tile._gridList === this);\n        const direction = this._dir ? this._dir.value : 'ltr';\n        this._tileCoordinator.update(this.cols, tiles);\n        this._tileStyler.init(this.gutterSize, tracker, this.cols, direction);\n        tiles.forEach((tile, index) => {\n            const pos = tracker.positions[index];\n            this._tileStyler.setStyle(tile, pos.row, pos.col);\n        });\n        this._setListStyle(this._tileStyler.getComputedHeight());\n    }\n    /** Sets style on the main grid-list element, given the style name and value. */\n    _setListStyle(style) {\n        if (style) {\n            this._element.nativeElement.style[style[0]] = style[1];\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridList, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatGridList, isStandalone: true, selector: \"mat-grid-list\", inputs: { cols: \"cols\", gutterSize: \"gutterSize\", rowHeight: \"rowHeight\" }, host: { properties: { \"attr.cols\": \"cols\" }, classAttribute: \"mat-grid-list\" }, providers: [\n            {\n                provide: MAT_GRID_LIST,\n                useExisting: MatGridList,\n            },\n        ], queries: [{ propertyName: \"_tiles\", predicate: MatGridTile, descendants: true }], exportAs: [\"matGridList\"], ngImport: i0, template: \"<div>\\n  <ng-content></ng-content>\\n</div>\", styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size)}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size)}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size)}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size)}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridList, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-grid-list', exportAs: 'matGridList', host: {\n                        'class': 'mat-grid-list',\n                        // Ensures that the \"cols\" input value is reflected in the DOM. This is\n                        // needed for the grid-list harness.\n                        '[attr.cols]': 'cols',\n                    }, providers: [\n                        {\n                            provide: MAT_GRID_LIST,\n                            useExisting: MatGridList,\n                        },\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, standalone: true, template: \"<div>\\n  <ng-content></ng-content>\\n</div>\", styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size)}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size)}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size)}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size)}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { _tiles: [{\n                type: ContentChildren,\n                args: [MatGridTile, { descendants: true }]\n            }], cols: [{\n                type: Input\n            }], gutterSize: [{\n                type: Input\n            }], rowHeight: [{\n                type: Input\n            }] } });\n\nclass MatGridListModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridListModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridListModule, imports: [MatLineModule,\n            MatCommonModule,\n            MatGridList,\n            MatGridTile,\n            MatGridTileText,\n            MatGridTileHeaderCssMatStyler,\n            MatGridTileFooterCssMatStyler,\n            MatGridAvatarCssMatStyler], exports: [MatGridList,\n            MatGridTile,\n            MatGridTileText,\n            MatLineModule,\n            MatCommonModule,\n            MatGridTileHeaderCssMatStyler,\n            MatGridTileFooterCssMatStyler,\n            MatGridAvatarCssMatStyler] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridListModule, imports: [MatLineModule,\n            MatCommonModule, MatLineModule,\n            MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridListModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatLineModule,\n                        MatCommonModule,\n                        MatGridList,\n                        MatGridTile,\n                        MatGridTileText,\n                        MatGridTileHeaderCssMatStyler,\n                        MatGridTileFooterCssMatStyler,\n                        MatGridAvatarCssMatStyler,\n                    ],\n                    exports: [\n                        MatGridList,\n                        MatGridTile,\n                        MatGridTileText,\n                        MatLineModule,\n                        MatCommonModule,\n                        MatGridTileHeaderCssMatStyler,\n                        MatGridTileFooterCssMatStyler,\n                        MatGridAvatarCssMatStyler,\n                    ],\n                }]\n        }] });\n\n// Privately exported for the grid-list harness.\nconst ɵTileCoordinator = TileCoordinator;\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatGridAvatarCssMatStyler, MatGridList, MatGridListModule, MatGridTile, MatGridTileFooterCssMatStyler, MatGridTileHeaderCssMatStyler, MatGridTileText, ɵTileCoordinator };\n"], "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵstyleProp", "i_r2", "ɵɵadvance", "stat_r1", "color", "ɵɵtextInterpolate", "icon", "value", "title", "i_r4", "ctx_r4", "getActionColor", "action_r3", "ɵɵtextInterpolate1", "description", "ɵɵproperty", "route", "getActionButtonText", "ɵɵclassMap", "transaction_r6", "type", "getActivityIcon", "ɵɵpipeBind2", "timestamp", "points", "ɵɵtextInterpolate2", "ɵɵtemplate", "DashboardComponent_div_0_div_24_div_6_Template", "user", "history", "slice", "DashboardComponent_div_0_mat_card_18_Template", "DashboardComponent_div_0_mat_card_23_Template", "DashboardComponent_div_0_div_24_Template", "DashboardComponent_div_0_div_25_Template", "getGreeting", "name", "city", "getRoleDisplayName", "role", "stats", "quickActions", "length", "DashboardComponent", "constructor", "authService", "ngOnInit", "currentUser$", "subscribe", "updateStats", "toString", "validatedActions", "filter", "h", "rewardsObtained", "hour", "Date", "getHours", "roleNames", "iconMap", "colorMap", "buttonTextMap", "ɵɵdirectiveInject", "i1", "AuthService", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_div_0_Template", "CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatGridListModule", "dashboardRoutes", "DashboardModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "path", "component", "InjectionToken", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Optional", "Inject", "Input", "ContentChildren", "Directive", "NgModule", "setLines", "MatLine", "MatLineModule", "MatCommonModule", "coerceNumberProperty", "_c0", "_c1", "_c2", "_c3", "TileCoordinator", "columnIndex", "rowIndex", "rowCount", "rowspan", "lastRowMax", "Math", "max", "tracker", "update", "numColumns", "tiles", "Array", "fill", "positions", "map", "tile", "_trackTile", "gapStartIndex", "_findMatchingGap", "colspan", "_markTilePosition", "TilePosition", "tileCols", "ngDevMode", "Error", "gapEndIndex", "_nextRow", "indexOf", "_findGapEndIndex", "i", "start", "row", "col", "MAT_GRID_LIST", "MatGridTile", "_element", "_gridList", "_rowspan", "_colspan", "round", "_setStyle", "property", "nativeElement", "style", "ɵfac", "MatGridTile_Factory", "t", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "hostAttrs", "hostVars", "hostBindings", "MatGridTile_HostBindings", "ɵɵattribute", "inputs", "exportAs", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "MatGridTile_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "ɵsetClassMetadata", "args", "selector", "host", "None", "OnPush", "undefined", "decorators", "MatGridTileText", "ngAfterContentInit", "_lines", "MatGridTileText_Factory", "contentQueries", "MatGridTileText_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "MatGridTileText_Template", "descendants", "MatGridAvatarCssMatStyler", "MatGridAvatarCssMatStyler_Factory", "ɵdir", "ɵɵdefineDirective", "MatGridTileHeaderCssMatStyler", "MatGridTileHeaderCssMatStyler_Factory", "MatGridTileFooterCssMatStyler", "MatGridTileFooterCssMatStyler_Factory", "cssCalcAllowedValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_rows", "init", "gutterSize", "cols", "direction", "_gutterSize", "normalizeUnits", "_cols", "_direction", "getBaseTileSize", "sizePercent", "gutterFraction", "getTilePosition", "baseSize", "offset", "calc", "getTileSize", "span", "setStyle", "colIndex", "percentWidthPerTile", "gutterWidthFractionPerTile", "setColStyles", "setRowStyles", "percentWidth", "gutterWidth", "baseTileWidth", "side", "getGutterSpan", "getTileSpan", "tileHeight", "getComputedHeight", "FixedTileStyler", "fixedRowHeight", "test", "reset", "list", "_setListStyle", "_tiles", "for<PERSON>ach", "RatioTileStyler", "_parseRatio", "percentHeightPerTile", "rowHeightRatio", "baseTileHeight", "ratioParts", "split", "parseFloat", "FitTileStyler", "gutterHeightPerTile", "exp", "match", "MAT_FIT_MODE", "MatGridList", "_dir", "_gutter", "rowHeight", "_rowHeight", "newValue", "_setTileStyler", "_checkCols", "_checkRowHeight", "ngAfterContentChecked", "_layoutTiles", "_tileStyler", "_tileCoordinator", "index", "pos", "MatGridList_Factory", "Directionality", "MatGridList_ContentQueries", "MatGridList_HostBindings", "ɵɵProvidersFeature", "provide", "useExisting", "MatGridList_Template", "providers", "MatGridListModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "ɵTileCoordinator"], "sourceRoot": "webpack:///", "x_google_ignoreList": [4]}