{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ValidatorManagementComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function ValidatorManagementComponent_Factory(t) {\n      return new (t || ValidatorManagementComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ValidatorManagementComponent,\n      selectors: [[\"app-validator-management\"]],\n      decls: 5,\n      vars: 0,\n      template: function ValidatorManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"h2\");\n          i0.ɵɵtext(2, \"\\uD83E\\uDDD1\\u200D\\uD83C\\uDFEB Gestion Validateurs\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"Suivi des validateurs et leurs performances\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\"div[_ngcontent-%COMP%] { padding: 20px; }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInZhbGlkYXRvci1tYW5hZ2VtZW50LmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxNQUFNLGFBQWEsRUFBRSIsImZpbGUiOiJ2YWxpZGF0b3ItbWFuYWdlbWVudC5jb21wb25lbnQudHMiLCJzb3VyY2VzQ29udGVudCI6WyJkaXYgeyBwYWRkaW5nOiAyMHB4OyB9Il19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvYWRtaW4tZGFzaGJvYXJkL2NvbXBvbmVudHMvdmFsaWRhdG9yLW1hbmFnZW1lbnQvdmFsaWRhdG9yLW1hbmFnZW1lbnQuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLE1BQU0sYUFBYSxFQUFFO0FBQ3JCLG9UQUFvVCIsInNvdXJjZXNDb250ZW50IjpbImRpdiB7IHBhZGRpbmc6IDIwcHg7IH0iXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ValidatorManagementComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "template", "ValidatorManagementComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\admin-dashboard\\components\\validator-management\\validator-management.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-validator-management',\n  template: '<div><h2>🧑‍🏫 Gestion Validateurs</h2><p>Suivi des validateurs et leurs performances</p></div>',\n  styles: ['div { padding: 20px; }']\n})\nexport class ValidatorManagementComponent implements OnInit {\n  constructor() { }\n  ngOnInit(): void { }\n}\n"], "mappings": ";AAOA,OAAM,MAAOA,4BAA4B;EACvCC,YAAA,GAAgB;EAChBC,QAAQA,CAAA,GAAW;;;uBAFRF,4BAA4B;IAAA;EAAA;;;YAA5BA,4BAA4B;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,sCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAHvBE,EAAL,CAAAC,cAAA,UAAK,SAAI;UAAAD,EAAA,CAAAE,MAAA,yDAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAAAH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,kDAA2C;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}