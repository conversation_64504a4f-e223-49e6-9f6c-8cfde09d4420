{"ast": null, "code": "import { AdminDashboardComponent } from './components/admin-dashboard/admin-dashboard.component';\nimport { UserManagementComponent } from './components/user-management/user-management.component';\nimport { PartnerManagementComponent } from './components/partner-management/partner-management.component';\nimport { ProviderManagementComponent } from './components/provider-management/provider-management.component';\nimport { ValidatorManagementComponent } from './components/validator-management/validator-management.component';\nimport { SystemConfigComponent } from './components/system-config/system-config.component';\nimport { AuditLogComponent } from './components/audit-log/audit-log.component';\nexport const adminDashboardRoutes = [{\n  path: '',\n  component: AdminDashboardComponent,\n  children: [{\n    path: '',\n    redirectTo: 'overview',\n    pathMatch: 'full'\n  }, {\n    path: 'overview',\n    component: AdminDashboardComponent\n  }, {\n    path: 'users',\n    component: UserManagementComponent\n  }, {\n    path: 'partners',\n    component: PartnerManagementComponent\n  }, {\n    path: 'providers',\n    component: ProviderManagementComponent\n  }, {\n    path: 'validators',\n    component: ValidatorManagementComponent\n  }, {\n    path: 'config',\n    component: SystemConfigComponent\n  }, {\n    path: 'audit',\n    component: AuditLogComponent\n  }]\n}];", "map": {"version": 3, "names": ["AdminDashboardComponent", "UserManagementComponent", "PartnerManagementComponent", "ProviderManagementComponent", "ValidatorManagementComponent", "SystemConfigComponent", "AuditLogComponent", "adminDashboardRoutes", "path", "component", "children", "redirectTo", "pathMatch"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\admin-dashboard\\admin-dashboard.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { AdminDashboardComponent } from './components/admin-dashboard/admin-dashboard.component';\nimport { UserManagementComponent } from './components/user-management/user-management.component';\nimport { PartnerManagementComponent } from './components/partner-management/partner-management.component';\nimport { ProviderManagementComponent } from './components/provider-management/provider-management.component';\nimport { ValidatorManagementComponent } from './components/validator-management/validator-management.component';\nimport { SystemConfigComponent } from './components/system-config/system-config.component';\nimport { AuditLogComponent } from './components/audit-log/audit-log.component';\n\nexport const adminDashboardRoutes: Routes = [\n  {\n    path: '',\n    component: AdminDashboardComponent,\n    children: [\n      { path: '', redirectTo: 'overview', pathMatch: 'full' },\n      { path: 'overview', component: AdminDashboardComponent },\n      { path: 'users', component: UserManagementComponent },\n      { path: 'partners', component: PartnerManagementComponent },\n      { path: 'providers', component: ProviderManagementComponent },\n      { path: 'validators', component: ValidatorManagementComponent },\n      { path: 'config', component: SystemConfigComponent },\n      { path: 'audit', component: AuditLogComponent }\n    ]\n  }\n];\n"], "mappings": "AACA,SAASA,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,0BAA0B,QAAQ,8DAA8D;AACzG,SAASC,2BAA2B,QAAQ,gEAAgE;AAC5G,SAASC,4BAA4B,QAAQ,kEAAkE;AAC/G,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,iBAAiB,QAAQ,4CAA4C;AAE9E,OAAO,MAAMC,oBAAoB,GAAW,CAC1C;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAET,uBAAuB;EAClCU,QAAQ,EAAE,CACR;IAAEF,IAAI,EAAE,EAAE;IAAEG,UAAU,EAAE,UAAU;IAAEC,SAAS,EAAE;EAAM,CAAE,EACvD;IAAEJ,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAET;EAAuB,CAAE,EACxD;IAAEQ,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAER;EAAuB,CAAE,EACrD;IAAEO,IAAI,EAAE,UAAU;IAAEC,SAAS,EAAEP;EAA0B,CAAE,EAC3D;IAAEM,IAAI,EAAE,WAAW;IAAEC,SAAS,EAAEN;EAA2B,CAAE,EAC7D;IAAEK,IAAI,EAAE,YAAY;IAAEC,SAAS,EAAEL;EAA4B,CAAE,EAC/D;IAAEI,IAAI,EAAE,QAAQ;IAAEC,SAAS,EAAEJ;EAAqB,CAAE,EACpD;IAAEG,IAAI,EAAE,OAAO;IAAEC,SAAS,EAAEH;EAAiB,CAAE;CAElD,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}