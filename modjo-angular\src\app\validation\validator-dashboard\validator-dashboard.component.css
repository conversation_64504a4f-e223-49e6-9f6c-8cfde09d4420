.dashboard-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 20px;
}

h1 {
  margin-bottom: 30px;
  color: #333;
}

.loading, .error, .no-data {
  text-align: center;
  padding: 30px;
  background-color: #f5f5f5;
  border-radius: 8px;
  margin-bottom: 20px;
}

.error {
  color: #d32f2f;
}

.success-message {
  padding: 15px;
  background-color: #e8f5e9;
  color: #2e7d32;
  border-radius: 8px;
  margin-bottom: 20px;
}

.error-message {
  padding: 15px;
  background-color: #ffebee;
  color: #d32f2f;
  border-radius: 8px;
  margin-bottom: 20px;
}

.dashboard-section {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  padding: 20px;
  margin-bottom: 30px;
}

h2 {
  margin-top: 0;
  margin-bottom: 20px;
  font-size: 18px;
  color: #333;
}

.action-form {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 20px;
}

.form-group {
  margin-bottom: 0;
}

label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

button {
  padding: 12px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

button:hover {
  background-color: #3a7bc8;
}

.actions-list {
  display: flex;
  flex-direction: column;
  gap: 15px;
}

.action-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px;
  background-color: #f9f9f9;
  border-radius: 8px;
}

.action-item.validated {
  background-color: #f5f5f5;
}

.action-info {
  flex: 1;
}

.action-description {
  margin: 0 0 5px 0;
  font-weight: 500;
  color: #333;
}

.action-details {
  display: flex;
  flex-wrap: wrap;
  gap: 15px;
  margin: 0;
  font-size: 14px;
  color: #666;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.approve-button {
  background-color: #4caf50;
}

.approve-button:hover {
  background-color: #3d8b40;
}

.reject-button {
  background-color: #f44336;
}

.reject-button:hover {
  background-color: #d32f2f;
}

.action-status {
  padding: 5px 10px;
  border-radius: 4px;
  font-size: 14px;
  font-weight: 500;
}

.action-status.approved {
  background-color: #e8f5e9;
  color: #2e7d32;
}

.action-status.rejected {
  background-color: #ffebee;
  color: #d32f2f;
}

@media (max-width: 768px) {
  .action-form {
    grid-template-columns: 1fr;
  }
  
  .action-item {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .action-buttons {
    margin-top: 15px;
    align-self: flex-end;
  }
}
