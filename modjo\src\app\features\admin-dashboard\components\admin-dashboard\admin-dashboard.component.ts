import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../../core/services/auth.service';
import { User, UserRole, AdminStats, GlobalStats, TopValidator, TopPartner, TopProvider } from '../../../../core/models';

@Component({
  selector: 'app-admin-dashboard',
  templateUrl: './admin-dashboard.component.html',
  styleUrls: ['./admin-dashboard.component.css']
})
export class AdminDashboardComponent implements OnInit {
  user: User | null = null;
  adminStats: AdminStats | null = null;
  globalStats: GlobalStats | null = null;
  
  // Navigation items
  navigationItems = [
    { 
      title: 'Vue d\'ensemble', 
      icon: 'dashboard', 
      route: '/admin/overview',
      description: 'Statistiques globales et KPIs'
    },
    { 
      title: 'Gestion Utilisateurs', 
      icon: 'people', 
      route: '/admin/users',
      description: 'Ajouter, modifier, supprimer des utilisateurs'
    },
    { 
      title: 'Gestion Partenaires', 
      icon: 'store', 
      route: '/admin/partners',
      description: 'Validation et suivi des partenaires'
    },
    { 
      title: 'Gestion Prestataires', 
      icon: 'business', 
      route: '/admin/providers',
      description: 'Validation et suivi des prestataires'
    },
    { 
      title: 'Gestion Validateurs', 
      icon: 'verified_user', 
      route: '/admin/validators',
      description: 'Suivi des validateurs et leurs performances'
    },
    { 
      title: 'Configuration', 
      icon: 'settings', 
      route: '/admin/config',
      description: 'Paramètres système et règles'
    },
    { 
      title: 'Journal d\'audit', 
      icon: 'history', 
      route: '/admin/audit',
      description: 'Historique des actions système'
    }
  ];

  // Quick stats cards
  quickStats = [
    {
      title: 'Utilisateurs Actifs',
      value: '0',
      icon: 'people',
      color: '#4CAF50',
      trend: '+12%',
      trendUp: true
    },
    {
      title: 'Points en Circulation',
      value: '0',
      icon: 'stars',
      color: '#FF9800',
      trend: '+8%',
      trendUp: true
    },
    {
      title: 'Validations en Attente',
      value: '0',
      icon: 'pending',
      color: '#F44336',
      trend: '-5%',
      trendUp: false
    },
    {
      title: 'Partenaires Actifs',
      value: '0',
      icon: 'store',
      color: '#2196F3',
      trend: '+15%',
      trendUp: true
    }
  ];

  // Recent activities
  recentActivities: any[] = [];

  constructor(
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.user = user;
      if (user && user.role === UserRole.ADMIN) {
        this.loadAdminData();
      } else {
        // Redirect non-admin users
        this.router.navigate(['/dashboard']);
      }
    });
  }

  private loadAdminData(): void {
    this.loadAdminStats();
    this.loadGlobalStats();
    this.loadRecentActivities();
  }

  private loadAdminStats(): void {
    // Simulate admin stats data
    this.adminStats = {
      totalUsers: 1247,
      activeUsers: 892,
      newUsersThisMonth: 156,
      totalPoints: 125000,
      pointsDistributedThisMonth: 15600,
      pointsRedeemedThisMonth: 8900,
      totalValidations: 2340,
      pendingValidations: 23,
      totalPartners: 45,
      activePartners: 38,
      totalProviders: 67,
      activeProviders: 52,
      totalValidators: 28,
      activeValidators: 24
    };

    // Update quick stats
    this.quickStats[0].value = this.adminStats.activeUsers.toString();
    this.quickStats[1].value = this.formatNumber(this.adminStats.totalPoints);
    this.quickStats[2].value = this.adminStats.pendingValidations.toString();
    this.quickStats[3].value = this.adminStats.activePartners.toString();
  }

  private loadGlobalStats(): void {
    // Simulate global stats
    this.globalStats = {
      totalPointsInCirculation: 125000,
      totalPointsDistributed: 180000,
      totalPointsRedeemed: 55000,
      averageUserPoints: 140,
      mostActiveCity: 'Sousse',
      topValidators: [
        { id: '1', name: 'École Primaire Monastir', organizationName: 'École Primaire', totalValidations: 245, averageResponseTime: 2.5, approvalRate: 95 },
        { id: '2', name: 'Bibliothèque Sousse', organizationName: 'Bibliothèque Municipale', totalValidations: 189, averageResponseTime: 1.8, approvalRate: 98 },
        { id: '3', name: 'Association Jeunesse', organizationName: 'Association', totalValidations: 167, averageResponseTime: 3.2, approvalRate: 92 }
      ],
      topPartners: [
        { id: '1', businessName: 'Café des Nattes', totalRedemptions: 156, totalPointsGenerated: 7800, customerSatisfaction: 94, activeRewards: 5 },
        { id: '2', businessName: 'Librairie Culturelle', totalRedemptions: 134, totalPointsGenerated: 6700, customerSatisfaction: 96, activeRewards: 8 },
        { id: '3', businessName: 'Restaurant Al Medina', totalRedemptions: 98, totalPointsGenerated: 4900, customerSatisfaction: 91, activeRewards: 3 }
      ],
      topProviders: [
        { id: '1', organizationName: 'Club de Football Monastir', totalScans: 567, totalPointsDistributed: 2835, uniqueUsers: 189, engagementRate: 78 },
        { id: '2', organizationName: 'Centre Culturel Sousse', totalScans: 445, totalPointsDistributed: 2225, uniqueUsers: 156, engagementRate: 82 },
        { id: '3', organizationName: 'Association Environnement', totalScans: 389, totalPointsDistributed: 1945, uniqueUsers: 134, engagementRate: 75 }
      ],
      monthlyGrowth: []
    };
  }

  private loadRecentActivities(): void {
    this.recentActivities = [
      {
        type: 'user_created',
        description: 'Nouvel utilisateur inscrit: Ahmed Ben Ali',
        timestamp: new Date(Date.now() - 1000 * 60 * 15),
        icon: 'person_add',
        color: '#4CAF50'
      },
      {
        type: 'validation_approved',
        description: 'Validation approuvée: Aide bibliothèque (15 pts)',
        timestamp: new Date(Date.now() - 1000 * 60 * 30),
        icon: 'check_circle',
        color: '#2196F3'
      },
      {
        type: 'partner_joined',
        description: 'Nouveau partenaire: Restaurant La Médina',
        timestamp: new Date(Date.now() - 1000 * 60 * 45),
        icon: 'store',
        color: '#FF9800'
      },
      {
        type: 'reward_redeemed',
        description: 'Récompense échangée: Café gratuit (50 pts)',
        timestamp: new Date(Date.now() - 1000 * 60 * 60),
        icon: 'card_giftcard',
        color: '#9C27B0'
      },
      {
        type: 'qr_scanned',
        description: 'QR Code scanné: Cours de français (3 pts)',
        timestamp: new Date(Date.now() - 1000 * 60 * 90),
        icon: 'qr_code_scanner',
        color: '#607D8B'
      }
    ];
  }

  private formatNumber(num: number): string {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + 'M';
    } else if (num >= 1000) {
      return (num / 1000).toFixed(1) + 'K';
    }
    return num.toString();
  }

  navigateTo(route: string): void {
    this.router.navigate([route]);
  }

  getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Bonjour';
    if (hour < 18) return 'Bon après-midi';
    return 'Bonsoir';
  }
}
