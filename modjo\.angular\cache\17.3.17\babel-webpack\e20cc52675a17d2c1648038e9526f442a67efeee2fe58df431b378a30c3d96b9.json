{"ast": null, "code": "import { UserRole, TransactionType } from '../models';\nimport * as i0 from \"@angular/core\";\nexport class MockDataService {\n  constructor() {}\n  /**\n   * Retourne des utilisateurs de test pour chaque rôle\n   */\n  getMockUsers() {\n    return [\n    // 🙋‍♂️ Utilisateur standard\n    {\n      uid: 'user1',\n      email: '<EMAIL>',\n      name: '<PERSON>',\n      role: UserRole.USER,\n      city: 'Monastir',\n      points: 245,\n      isActive: true,\n      createdAt: new Date('2024-01-15'),\n      updatedAt: new Date(),\n      history: [{\n        id: '1',\n        type: TransactionType.EARNED,\n        description: 'Aide à la bibliothèque municipale',\n        points: 15,\n        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2),\n        toUserId: 'user1'\n      }, {\n        id: '2',\n        type: TransactionType.EARNED,\n        description: 'Scan QR - Cours de français',\n        points: 3,\n        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),\n        toUserId: 'user1'\n      }, {\n        id: '3',\n        type: TransactionType.SPENT,\n        description: 'Échange - Café gratuit',\n        points: -50,\n        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12),\n        toUserId: 'partner1'\n      }]\n    },\n    // 🧑‍💼 Administrateur\n    {\n      uid: 'admin1',\n      email: '<EMAIL>',\n      name: 'Administrateur Modjo',\n      role: UserRole.ADMIN,\n      city: 'Monastir',\n      points: 0,\n      isActive: true,\n      createdAt: new Date('2024-01-01'),\n      updatedAt: new Date(),\n      history: []\n    },\n    // 🧑‍🏫 Validateur\n    {\n      uid: 'validator1',\n      email: '<EMAIL>',\n      name: 'Fatma Trabelsi',\n      role: UserRole.VALIDATOR,\n      city: 'Sousse',\n      points: 0,\n      isActive: true,\n      createdAt: new Date('2024-01-10'),\n      updatedAt: new Date(),\n      history: []\n    },\n    // 🧑‍🍳 Partenaire\n    {\n      uid: 'partner1',\n      email: '<EMAIL>',\n      name: 'Mohamed Gharbi',\n      role: UserRole.PARTNER,\n      city: 'Monastir',\n      points: 0,\n      isActive: true,\n      createdAt: new Date('2024-01-08'),\n      updatedAt: new Date(),\n      history: []\n    },\n    // 🧑‍🔧 Prestataire\n    {\n      uid: 'provider1',\n      email: '<EMAIL>',\n      name: 'Leila Mansouri',\n      role: UserRole.PROVIDER,\n      city: 'Sousse',\n      points: 0,\n      isActive: true,\n      createdAt: new Date('2024-01-05'),\n      updatedAt: new Date(),\n      history: []\n    }];\n  }\n  /**\n   * Retourne un utilisateur par son email (pour la simulation de connexion)\n   */\n  getUserByEmail(email) {\n    const users = this.getMockUsers();\n    return users.find(user => user.email === email) || null;\n  }\n  /**\n   * Simule la création d'un nouvel utilisateur\n   */\n  createMockUser(email, name, role, city) {\n    return {\n      uid: 'user_' + Date.now(),\n      email,\n      name,\n      role,\n      city,\n      points: role === UserRole.USER ? 0 : 0,\n      isActive: true,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      history: []\n    };\n  }\n  /**\n   * Retourne les données de test selon le rôle\n   */\n  getMockDataForRole(role) {\n    switch (role) {\n      case UserRole.USER:\n        return this.getUserMockData();\n      case UserRole.ADMIN:\n        return this.getAdminMockData();\n      case UserRole.VALIDATOR:\n        return this.getValidatorMockData();\n      case UserRole.PARTNER:\n        return this.getPartnerMockData();\n      case UserRole.PROVIDER:\n        return this.getProviderMockData();\n      default:\n        return {};\n    }\n  }\n  getUserMockData() {\n    return {\n      availableRewards: [{\n        id: 'reward1',\n        title: 'Café gratuit',\n        description: 'Un café offert au Café des Nattes',\n        pointsRequired: 50,\n        partnerId: 'partner1',\n        partnerName: 'Café des Nattes',\n        category: 'FOOD',\n        imageUrl: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',\n        isActive: true,\n        city: 'Monastir'\n      }, {\n        id: 'reward2',\n        title: '10% de réduction',\n        description: 'Réduction sur tous les produits artisanaux',\n        pointsRequired: 75,\n        partnerId: 'partner2',\n        partnerName: 'Boutique Artisanat',\n        category: 'SHOPPING',\n        imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',\n        isActive: true,\n        city: 'Sousse'\n      }],\n      nearbyPartners: [{\n        id: 'partner1',\n        name: 'Café des Nattes',\n        category: 'RESTAURANT',\n        address: 'Médina de Monastir',\n        distance: 0.5\n      }, {\n        id: 'partner2',\n        name: 'Boutique Artisanat',\n        category: 'RETAIL',\n        address: 'Centre-ville Sousse',\n        distance: 1.2\n      }]\n    };\n  }\n  getAdminMockData() {\n    return {\n      systemStats: {\n        totalUsers: 1247,\n        activeUsers: 892,\n        totalPoints: 125000,\n        pendingValidations: 23,\n        totalPartners: 45,\n        totalProviders: 67\n      },\n      recentActivities: [{\n        type: 'user_created',\n        description: 'Nouvel utilisateur: Ahmed Ben Ali',\n        timestamp: new Date(Date.now() - 1000 * 60 * 15)\n      }, {\n        type: 'validation_approved',\n        description: 'Validation approuvée: Aide bibliothèque',\n        timestamp: new Date(Date.now() - 1000 * 60 * 30)\n      }]\n    };\n  }\n  getValidatorMockData() {\n    return {\n      pendingValidations: [{\n        id: '1',\n        userId: 'user1',\n        userName: 'Ahmed Ben Ali',\n        actionType: 'LIBRARY_VOLUNTEER',\n        description: 'Aide à la bibliothèque municipale',\n        submittedAt: new Date(Date.now() - 1000 * 60 * 30),\n        points: 15\n      }],\n      validatorStats: {\n        totalValidations: 156,\n        validationsToday: 8,\n        approvalRate: 94\n      }\n    };\n  }\n  getPartnerMockData() {\n    return {\n      myRewards: [{\n        id: 'reward1',\n        title: 'Café gratuit',\n        pointsCost: 50,\n        redemptions: 23,\n        isActive: true\n      }],\n      partnerStats: {\n        totalRedemptions: 156,\n        totalPointsGenerated: 7800,\n        activeRewards: 5\n      }\n    };\n  }\n  getProviderMockData() {\n    return {\n      myQrCodes: [{\n        id: 'qr1',\n        description: 'Cours de français',\n        pointsValue: 3,\n        totalScans: 45,\n        isActive: true\n      }],\n      providerStats: {\n        totalScans: 567,\n        totalPointsDistributed: 2835,\n        uniqueUsers: 189\n      }\n    };\n  }\n  static {\n    this.ɵfac = function MockDataService_Factory(t) {\n      return new (t || MockDataService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: MockDataService,\n      factory: MockDataService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["UserRole", "TransactionType", "MockDataService", "constructor", "getMockUsers", "uid", "email", "name", "role", "USER", "city", "points", "isActive", "createdAt", "Date", "updatedAt", "history", "id", "type", "EARNED", "description", "timestamp", "now", "toUserId", "SPENT", "ADMIN", "VALIDATOR", "PARTNER", "PROVIDER", "getUserByEmail", "users", "find", "user", "createMockUser", "getMockDataForRole", "getUserMockData", "getAdminMockData", "getValidatorMockData", "getPartnerMockData", "getProviderMockData", "availableRewards", "title", "pointsRequired", "partnerId", "partner<PERSON>ame", "category", "imageUrl", "nearbyPartners", "address", "distance", "systemStats", "totalUsers", "activeUsers", "totalPoints", "pendingValidations", "totalPartners", "totalProviders", "recentActivities", "userId", "userName", "actionType", "submittedAt", "validatorStats", "totalValidations", "validationsToday", "approvalRate", "myRewards", "pointsCost", "redemptions", "partnerStats", "totalRedemptions", "totalPointsGenerated", "activeRewards", "myQrCodes", "pointsValue", "totalScans", "providerStats", "totalPointsDistributed", "uniqueUsers", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\services\\mock-data.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { User, UserRole, TransactionType } from '../models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class MockDataService {\n\n  constructor() {}\n\n  /**\n   * Retourne des utilisateurs de test pour chaque rôle\n   */\n  getMockUsers(): User[] {\n    return [\n      // 🙋‍♂️ Utilisateur standard\n      {\n        uid: 'user1',\n        email: '<EMAIL>',\n        name: '<PERSON>',\n        role: UserRole.USER,\n        city: 'Monastir',\n        points: 245,\n        isActive: true,\n        createdAt: new Date('2024-01-15'),\n        updatedAt: new Date(),\n        history: [\n          {\n            id: '1',\n            type: TransactionType.EARNED,\n            description: 'Aide à la bibliothèque municipale',\n            points: 15,\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24 * 2),\n            toUserId: 'user1'\n          },\n          {\n            id: '2',\n            type: TransactionType.EARNED,\n            description: 'Scan QR - Cours de français',\n            points: 3,\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24),\n            toUserId: 'user1'\n          },\n          {\n            id: '3',\n            type: TransactionType.SPENT,\n            description: 'Échange - Café gratuit',\n            points: -50,\n            timestamp: new Date(Date.now() - 1000 * 60 * 60 * 12),\n            toUserId: 'partner1'\n          }\n        ]\n      },\n\n      // 🧑‍💼 Administrateur\n      {\n        uid: 'admin1',\n        email: '<EMAIL>',\n        name: 'Administrateur Modjo',\n        role: UserRole.ADMIN,\n        city: 'Monastir',\n        points: 0,\n        isActive: true,\n        createdAt: new Date('2024-01-01'),\n        updatedAt: new Date(),\n        history: []\n      },\n\n      // 🧑‍🏫 Validateur\n      {\n        uid: 'validator1',\n        email: '<EMAIL>',\n        name: 'Fatma Trabelsi',\n        role: UserRole.VALIDATOR,\n        city: 'Sousse',\n        points: 0,\n        isActive: true,\n        createdAt: new Date('2024-01-10'),\n        updatedAt: new Date(),\n        history: []\n      },\n\n      // 🧑‍🍳 Partenaire\n      {\n        uid: 'partner1',\n        email: '<EMAIL>',\n        name: 'Mohamed Gharbi',\n        role: UserRole.PARTNER,\n        city: 'Monastir',\n        points: 0,\n        isActive: true,\n        createdAt: new Date('2024-01-08'),\n        updatedAt: new Date(),\n        history: []\n      },\n\n      // 🧑‍🔧 Prestataire\n      {\n        uid: 'provider1',\n        email: '<EMAIL>',\n        name: 'Leila Mansouri',\n        role: UserRole.PROVIDER,\n        city: 'Sousse',\n        points: 0,\n        isActive: true,\n        createdAt: new Date('2024-01-05'),\n        updatedAt: new Date(),\n        history: []\n      }\n    ];\n  }\n\n  /**\n   * Retourne un utilisateur par son email (pour la simulation de connexion)\n   */\n  getUserByEmail(email: string): User | null {\n    const users = this.getMockUsers();\n    return users.find(user => user.email === email) || null;\n  }\n\n  /**\n   * Simule la création d'un nouvel utilisateur\n   */\n  createMockUser(email: string, name: string, role: UserRole, city: 'Monastir' | 'Sousse'): User {\n    return {\n      uid: 'user_' + Date.now(),\n      email,\n      name,\n      role,\n      city,\n      points: role === UserRole.USER ? 0 : 0,\n      isActive: true,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      history: []\n    };\n  }\n\n  /**\n   * Retourne les données de test selon le rôle\n   */\n  getMockDataForRole(role: UserRole): any {\n    switch (role) {\n      case UserRole.USER:\n        return this.getUserMockData();\n      case UserRole.ADMIN:\n        return this.getAdminMockData();\n      case UserRole.VALIDATOR:\n        return this.getValidatorMockData();\n      case UserRole.PARTNER:\n        return this.getPartnerMockData();\n      case UserRole.PROVIDER:\n        return this.getProviderMockData();\n      default:\n        return {};\n    }\n  }\n\n  private getUserMockData() {\n    return {\n      availableRewards: [\n        {\n          id: 'reward1',\n          title: 'Café gratuit',\n          description: 'Un café offert au Café des Nattes',\n          pointsRequired: 50,\n          partnerId: 'partner1',\n          partnerName: 'Café des Nattes',\n          category: 'FOOD',\n          imageUrl: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',\n          isActive: true,\n          city: 'Monastir'\n        },\n        {\n          id: 'reward2',\n          title: '10% de réduction',\n          description: 'Réduction sur tous les produits artisanaux',\n          pointsRequired: 75,\n          partnerId: 'partner2',\n          partnerName: 'Boutique Artisanat',\n          category: 'SHOPPING',\n          imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',\n          isActive: true,\n          city: 'Sousse'\n        }\n      ],\n      nearbyPartners: [\n        {\n          id: 'partner1',\n          name: 'Café des Nattes',\n          category: 'RESTAURANT',\n          address: 'Médina de Monastir',\n          distance: 0.5\n        },\n        {\n          id: 'partner2',\n          name: 'Boutique Artisanat',\n          category: 'RETAIL',\n          address: 'Centre-ville Sousse',\n          distance: 1.2\n        }\n      ]\n    };\n  }\n\n  private getAdminMockData() {\n    return {\n      systemStats: {\n        totalUsers: 1247,\n        activeUsers: 892,\n        totalPoints: 125000,\n        pendingValidations: 23,\n        totalPartners: 45,\n        totalProviders: 67\n      },\n      recentActivities: [\n        {\n          type: 'user_created',\n          description: 'Nouvel utilisateur: Ahmed Ben Ali',\n          timestamp: new Date(Date.now() - 1000 * 60 * 15)\n        },\n        {\n          type: 'validation_approved',\n          description: 'Validation approuvée: Aide bibliothèque',\n          timestamp: new Date(Date.now() - 1000 * 60 * 30)\n        }\n      ]\n    };\n  }\n\n  private getValidatorMockData() {\n    return {\n      pendingValidations: [\n        {\n          id: '1',\n          userId: 'user1',\n          userName: 'Ahmed Ben Ali',\n          actionType: 'LIBRARY_VOLUNTEER',\n          description: 'Aide à la bibliothèque municipale',\n          submittedAt: new Date(Date.now() - 1000 * 60 * 30),\n          points: 15\n        }\n      ],\n      validatorStats: {\n        totalValidations: 156,\n        validationsToday: 8,\n        approvalRate: 94\n      }\n    };\n  }\n\n  private getPartnerMockData() {\n    return {\n      myRewards: [\n        {\n          id: 'reward1',\n          title: 'Café gratuit',\n          pointsCost: 50,\n          redemptions: 23,\n          isActive: true\n        }\n      ],\n      partnerStats: {\n        totalRedemptions: 156,\n        totalPointsGenerated: 7800,\n        activeRewards: 5\n      }\n    };\n  }\n\n  private getProviderMockData() {\n    return {\n      myQrCodes: [\n        {\n          id: 'qr1',\n          description: 'Cours de français',\n          pointsValue: 3,\n          totalScans: 45,\n          isActive: true\n        }\n      ],\n      providerStats: {\n        totalScans: 567,\n        totalPointsDistributed: 2835,\n        uniqueUsers: 189\n      }\n    };\n  }\n}\n"], "mappings": "AACA,SAAeA,QAAQ,EAAEC,eAAe,QAAQ,WAAW;;AAK3D,OAAM,MAAOC,eAAe;EAE1BC,YAAA,GAAe;EAEf;;;EAGAC,YAAYA,CAAA;IACV,OAAO;IACL;IACA;MACEC,GAAG,EAAE,OAAO;MACZC,KAAK,EAAE,wBAAwB;MAC/BC,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAER,QAAQ,CAACS,IAAI;MACnBC,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE,GAAG;MACXC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,EAAE;MACrBE,OAAO,EAAE,CACP;QACEC,EAAE,EAAE,GAAG;QACPC,IAAI,EAAEjB,eAAe,CAACkB,MAAM;QAC5BC,WAAW,EAAE,mCAAmC;QAChDT,MAAM,EAAE,EAAE;QACVU,SAAS,EAAE,IAAIP,IAAI,CAACA,IAAI,CAACQ,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;QACzDC,QAAQ,EAAE;OACX,EACD;QACEN,EAAE,EAAE,GAAG;QACPC,IAAI,EAAEjB,eAAe,CAACkB,MAAM;QAC5BC,WAAW,EAAE,6BAA6B;QAC1CT,MAAM,EAAE,CAAC;QACTU,SAAS,EAAE,IAAIP,IAAI,CAACA,IAAI,CAACQ,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;QACrDC,QAAQ,EAAE;OACX,EACD;QACEN,EAAE,EAAE,GAAG;QACPC,IAAI,EAAEjB,eAAe,CAACuB,KAAK;QAC3BJ,WAAW,EAAE,wBAAwB;QACrCT,MAAM,EAAE,CAAC,EAAE;QACXU,SAAS,EAAE,IAAIP,IAAI,CAACA,IAAI,CAACQ,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC;QACrDC,QAAQ,EAAE;OACX;KAEJ;IAED;IACA;MACElB,GAAG,EAAE,QAAQ;MACbC,KAAK,EAAE,gBAAgB;MACvBC,IAAI,EAAE,sBAAsB;MAC5BC,IAAI,EAAER,QAAQ,CAACyB,KAAK;MACpBf,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,EAAE;MACrBE,OAAO,EAAE;KACV;IAED;IACA;MACEX,GAAG,EAAE,YAAY;MACjBC,KAAK,EAAE,gCAAgC;MACvCC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAER,QAAQ,CAAC0B,SAAS;MACxBhB,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,EAAE;MACrBE,OAAO,EAAE;KACV;IAED;IACA;MACEX,GAAG,EAAE,UAAU;MACfC,KAAK,EAAE,+BAA+B;MACtCC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAER,QAAQ,CAAC2B,OAAO;MACtBjB,IAAI,EAAE,UAAU;MAChBC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,EAAE;MACrBE,OAAO,EAAE;KACV;IAED;IACA;MACEX,GAAG,EAAE,WAAW;MAChBC,KAAK,EAAE,iCAAiC;MACxCC,IAAI,EAAE,gBAAgB;MACtBC,IAAI,EAAER,QAAQ,CAAC4B,QAAQ;MACvBlB,IAAI,EAAE,QAAQ;MACdC,MAAM,EAAE,CAAC;MACTC,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,EAAE;MACrBE,OAAO,EAAE;KACV,CACF;EACH;EAEA;;;EAGAa,cAAcA,CAACvB,KAAa;IAC1B,MAAMwB,KAAK,GAAG,IAAI,CAAC1B,YAAY,EAAE;IACjC,OAAO0B,KAAK,CAACC,IAAI,CAACC,IAAI,IAAIA,IAAI,CAAC1B,KAAK,KAAKA,KAAK,CAAC,IAAI,IAAI;EACzD;EAEA;;;EAGA2B,cAAcA,CAAC3B,KAAa,EAAEC,IAAY,EAAEC,IAAc,EAAEE,IAA2B;IACrF,OAAO;MACLL,GAAG,EAAE,OAAO,GAAGS,IAAI,CAACQ,GAAG,EAAE;MACzBhB,KAAK;MACLC,IAAI;MACJC,IAAI;MACJE,IAAI;MACJC,MAAM,EAAEH,IAAI,KAAKR,QAAQ,CAACS,IAAI,GAAG,CAAC,GAAG,CAAC;MACtCG,QAAQ,EAAE,IAAI;MACdC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,SAAS,EAAE,IAAID,IAAI,EAAE;MACrBE,OAAO,EAAE;KACV;EACH;EAEA;;;EAGAkB,kBAAkBA,CAAC1B,IAAc;IAC/B,QAAQA,IAAI;MACV,KAAKR,QAAQ,CAACS,IAAI;QAChB,OAAO,IAAI,CAAC0B,eAAe,EAAE;MAC/B,KAAKnC,QAAQ,CAACyB,KAAK;QACjB,OAAO,IAAI,CAACW,gBAAgB,EAAE;MAChC,KAAKpC,QAAQ,CAAC0B,SAAS;QACrB,OAAO,IAAI,CAACW,oBAAoB,EAAE;MACpC,KAAKrC,QAAQ,CAAC2B,OAAO;QACnB,OAAO,IAAI,CAACW,kBAAkB,EAAE;MAClC,KAAKtC,QAAQ,CAAC4B,QAAQ;QACpB,OAAO,IAAI,CAACW,mBAAmB,EAAE;MACnC;QACE,OAAO,EAAE;;EAEf;EAEQJ,eAAeA,CAAA;IACrB,OAAO;MACLK,gBAAgB,EAAE,CAChB;QACEvB,EAAE,EAAE,SAAS;QACbwB,KAAK,EAAE,cAAc;QACrBrB,WAAW,EAAE,mCAAmC;QAChDsB,cAAc,EAAE,EAAE;QAClBC,SAAS,EAAE,UAAU;QACrBC,WAAW,EAAE,iBAAiB;QAC9BC,QAAQ,EAAE,MAAM;QAChBC,QAAQ,EAAE,oEAAoE;QAC9ElC,QAAQ,EAAE,IAAI;QACdF,IAAI,EAAE;OACP,EACD;QACEO,EAAE,EAAE,SAAS;QACbwB,KAAK,EAAE,kBAAkB;QACzBrB,WAAW,EAAE,4CAA4C;QACzDsB,cAAc,EAAE,EAAE;QAClBC,SAAS,EAAE,UAAU;QACrBC,WAAW,EAAE,oBAAoB;QACjCC,QAAQ,EAAE,UAAU;QACpBC,QAAQ,EAAE,oEAAoE;QAC9ElC,QAAQ,EAAE,IAAI;QACdF,IAAI,EAAE;OACP,CACF;MACDqC,cAAc,EAAE,CACd;QACE9B,EAAE,EAAE,UAAU;QACdV,IAAI,EAAE,iBAAiB;QACvBsC,QAAQ,EAAE,YAAY;QACtBG,OAAO,EAAE,oBAAoB;QAC7BC,QAAQ,EAAE;OACX,EACD;QACEhC,EAAE,EAAE,UAAU;QACdV,IAAI,EAAE,oBAAoB;QAC1BsC,QAAQ,EAAE,QAAQ;QAClBG,OAAO,EAAE,qBAAqB;QAC9BC,QAAQ,EAAE;OACX;KAEJ;EACH;EAEQb,gBAAgBA,CAAA;IACtB,OAAO;MACLc,WAAW,EAAE;QACXC,UAAU,EAAE,IAAI;QAChBC,WAAW,EAAE,GAAG;QAChBC,WAAW,EAAE,MAAM;QACnBC,kBAAkB,EAAE,EAAE;QACtBC,aAAa,EAAE,EAAE;QACjBC,cAAc,EAAE;OACjB;MACDC,gBAAgB,EAAE,CAChB;QACEvC,IAAI,EAAE,cAAc;QACpBE,WAAW,EAAE,mCAAmC;QAChDC,SAAS,EAAE,IAAIP,IAAI,CAACA,IAAI,CAACQ,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;OAChD,EACD;QACEJ,IAAI,EAAE,qBAAqB;QAC3BE,WAAW,EAAE,yCAAyC;QACtDC,SAAS,EAAE,IAAIP,IAAI,CAACA,IAAI,CAACQ,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;OAChD;KAEJ;EACH;EAEQe,oBAAoBA,CAAA;IAC1B,OAAO;MACLiB,kBAAkB,EAAE,CAClB;QACErC,EAAE,EAAE,GAAG;QACPyC,MAAM,EAAE,OAAO;QACfC,QAAQ,EAAE,eAAe;QACzBC,UAAU,EAAE,mBAAmB;QAC/BxC,WAAW,EAAE,mCAAmC;QAChDyC,WAAW,EAAE,IAAI/C,IAAI,CAACA,IAAI,CAACQ,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;QAClDX,MAAM,EAAE;OACT,CACF;MACDmD,cAAc,EAAE;QACdC,gBAAgB,EAAE,GAAG;QACrBC,gBAAgB,EAAE,CAAC;QACnBC,YAAY,EAAE;;KAEjB;EACH;EAEQ3B,kBAAkBA,CAAA;IACxB,OAAO;MACL4B,SAAS,EAAE,CACT;QACEjD,EAAE,EAAE,SAAS;QACbwB,KAAK,EAAE,cAAc;QACrB0B,UAAU,EAAE,EAAE;QACdC,WAAW,EAAE,EAAE;QACfxD,QAAQ,EAAE;OACX,CACF;MACDyD,YAAY,EAAE;QACZC,gBAAgB,EAAE,GAAG;QACrBC,oBAAoB,EAAE,IAAI;QAC1BC,aAAa,EAAE;;KAElB;EACH;EAEQjC,mBAAmBA,CAAA;IACzB,OAAO;MACLkC,SAAS,EAAE,CACT;QACExD,EAAE,EAAE,KAAK;QACTG,WAAW,EAAE,mBAAmB;QAChCsD,WAAW,EAAE,CAAC;QACdC,UAAU,EAAE,EAAE;QACd/D,QAAQ,EAAE;OACX,CACF;MACDgE,aAAa,EAAE;QACbD,UAAU,EAAE,GAAG;QACfE,sBAAsB,EAAE,IAAI;QAC5BC,WAAW,EAAE;;KAEhB;EACH;;;uBAzRW5E,eAAe;IAAA;EAAA;;;aAAfA,eAAe;MAAA6E,OAAA,EAAf7E,eAAe,CAAA8E,IAAA;MAAAC,UAAA,EAFd;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}