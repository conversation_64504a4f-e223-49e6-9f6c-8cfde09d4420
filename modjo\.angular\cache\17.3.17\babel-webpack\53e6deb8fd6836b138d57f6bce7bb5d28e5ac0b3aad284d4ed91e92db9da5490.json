{"ast": null, "code": "import * as i1 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { Platform, _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ChangeDetectorRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, InjectionToken, Injector, TemplateRef, Injectable, SkipSelf, NgModule } from '@angular/core';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Subject, defer, of } from 'rxjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { startWith } from 'rxjs/operators';\n\n/** Configuration for opening a modal dialog. */\nfunction CdkDialogContainer_ng_template_0_Template(rf, ctx) {}\nclass DialogConfig {\n  constructor() {\n    /** The ARIA role of the dialog element. */\n    this.role = 'dialog';\n    /** Optional CSS class or classes applied to the overlay panel. */\n    this.panelClass = '';\n    /** Whether the dialog has a backdrop. */\n    this.hasBackdrop = true;\n    /** Optional CSS class or classes applied to the overlay backdrop. */\n    this.backdropClass = '';\n    /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n    this.disableClose = false;\n    /** Width of the dialog. */\n    this.width = '';\n    /** Height of the dialog. */\n    this.height = '';\n    /** Data being injected into the child component. */\n    this.data = null;\n    /** ID of the element that describes the dialog. */\n    this.ariaDescribedBy = null;\n    /** ID of the element that labels the dialog. */\n    this.ariaLabelledBy = null;\n    /** Dialog label applied via `aria-label` */\n    this.ariaLabel = null;\n    /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n    this.ariaModal = true;\n    /**\n     * Where the dialog should focus on open.\n     * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n     * AutoFocusTarget instead.\n     */\n    this.autoFocus = 'first-tabbable';\n    /**\n     * Whether the dialog should restore focus to the previously-focused element upon closing.\n     * Has the following behavior based on the type that is passed in:\n     * - `boolean` - when true, will return focus to the element that was focused before the dialog\n     *    was opened, otherwise won't restore focus at all.\n     * - `string` - focus will be restored to the first element that matches the CSS selector.\n     * - `HTMLElement` - focus will be restored to the specific element.\n     */\n    this.restoreFocus = true;\n    /**\n     * Whether the dialog should close when the user navigates backwards or forwards through browser\n     * history. This does not apply to navigation via anchor element unless using URL-hash based\n     * routing (`HashLocationStrategy` in the Angular router).\n     */\n    this.closeOnNavigation = true;\n    /**\n     * Whether the dialog should close when the dialog service is destroyed. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead.\n     */\n    this.closeOnDestroy = true;\n    /**\n     * Whether the dialog should close when the underlying overlay is detached. This is useful if\n     * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n     * external detachment can happen as a result of a scroll strategy triggering it or when the\n     * browser location changes.\n     */\n    this.closeOnOverlayDetachments = true;\n  }\n}\nfunction throwDialogContentAlreadyAttachedError() {\n  throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n  constructor(_elementRef, _focusTrapFactory, _document, _config, _interactivityChecker, _ngZone, _overlayRef, _focusMonitor) {\n    super();\n    this._elementRef = _elementRef;\n    this._focusTrapFactory = _focusTrapFactory;\n    this._config = _config;\n    this._interactivityChecker = _interactivityChecker;\n    this._ngZone = _ngZone;\n    this._overlayRef = _overlayRef;\n    this._focusMonitor = _focusMonitor;\n    this._platform = inject(Platform);\n    /** The class that traps and manages focus within the dialog. */\n    this._focusTrap = null;\n    /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n    this._elementFocusedBeforeDialogWasOpened = null;\n    /**\n     * Type of interaction that led to the dialog being closed. This is used to determine\n     * whether the focus style will be applied when returning focus to its original location\n     * after the dialog is closed.\n     */\n    this._closeInteractionType = null;\n    /**\n     * Queue of the IDs of the dialog's label element, based on their definition order. The first\n     * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n     * where there are two or more titles in the DOM at a time and the first one is destroyed while\n     * the rest are present.\n     */\n    this._ariaLabelledByQueue = [];\n    this._changeDetectorRef = inject(ChangeDetectorRef);\n    /**\n     * Attaches a DOM portal to the dialog container.\n     * @param portal Portal to be attached.\n     * @deprecated To be turned into a method.\n     * @breaking-change 10.0.0\n     */\n    this.attachDomPortal = portal => {\n      if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n        throwDialogContentAlreadyAttachedError();\n      }\n      const result = this._portalOutlet.attachDomPortal(portal);\n      this._contentAttached();\n      return result;\n    };\n    this._document = _document;\n    if (this._config.ariaLabelledBy) {\n      this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n    }\n  }\n  _addAriaLabelledBy(id) {\n    this._ariaLabelledByQueue.push(id);\n    this._changeDetectorRef.markForCheck();\n  }\n  _removeAriaLabelledBy(id) {\n    const index = this._ariaLabelledByQueue.indexOf(id);\n    if (index > -1) {\n      this._ariaLabelledByQueue.splice(index, 1);\n      this._changeDetectorRef.markForCheck();\n    }\n  }\n  _contentAttached() {\n    this._initializeFocusTrap();\n    this._handleBackdropClicks();\n    this._captureInitialFocus();\n  }\n  /**\n   * Can be used by child classes to customize the initial focus\n   * capturing behavior (e.g. if it's tied to an animation).\n   */\n  _captureInitialFocus() {\n    this._trapFocus();\n  }\n  ngOnDestroy() {\n    this._restoreFocus();\n  }\n  /**\n   * Attach a ComponentPortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachComponentPortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachComponentPortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  /**\n   * Attach a TemplatePortal as content to this dialog container.\n   * @param portal Portal to be attached as the dialog content.\n   */\n  attachTemplatePortal(portal) {\n    if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throwDialogContentAlreadyAttachedError();\n    }\n    const result = this._portalOutlet.attachTemplatePortal(portal);\n    this._contentAttached();\n    return result;\n  }\n  // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n  /** Captures focus if it isn't already inside the dialog. */\n  _recaptureFocus() {\n    if (!this._containsFocus()) {\n      this._trapFocus();\n    }\n  }\n  /**\n   * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n   * attribute to forcefully focus it. The attribute is removed after focus is moved.\n   * @param element The element to focus.\n   */\n  _forceFocus(element, options) {\n    if (!this._interactivityChecker.isFocusable(element)) {\n      element.tabIndex = -1;\n      // The tabindex attribute should be removed to avoid navigating to that element again\n      this._ngZone.runOutsideAngular(() => {\n        const callback = () => {\n          element.removeEventListener('blur', callback);\n          element.removeEventListener('mousedown', callback);\n          element.removeAttribute('tabindex');\n        };\n        element.addEventListener('blur', callback);\n        element.addEventListener('mousedown', callback);\n      });\n    }\n    element.focus(options);\n  }\n  /**\n   * Focuses the first element that matches the given selector within the focus trap.\n   * @param selector The CSS selector for the element to set focus to.\n   */\n  _focusByCssSelector(selector, options) {\n    let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n    if (elementToFocus) {\n      this._forceFocus(elementToFocus, options);\n    }\n  }\n  /**\n   * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n   * cannot be moved then focus will go to the dialog container.\n   */\n  _trapFocus() {\n    const element = this._elementRef.nativeElement;\n    // If were to attempt to focus immediately, then the content of the dialog would not yet be\n    // ready in instances where change detection has to run first. To deal with this, we simply\n    // wait for the microtask queue to be empty when setting focus when autoFocus isn't set to\n    // dialog. If the element inside the dialog can't be focused, then the container is focused\n    // so the user can't tab into other elements behind it.\n    switch (this._config.autoFocus) {\n      case false:\n      case 'dialog':\n        // Ensure that focus is on the dialog container. It's possible that a different\n        // component tried to move focus while the open animation was running. See:\n        // https://github.com/angular/components/issues/16215. Note that we only want to do this\n        // if the focus isn't inside the dialog already, because it's possible that the consumer\n        // turned off `autoFocus` in order to move focus themselves.\n        if (!this._containsFocus()) {\n          element.focus();\n        }\n        break;\n      case true:\n      case 'first-tabbable':\n        this._focusTrap?.focusInitialElementWhenReady().then(focusedSuccessfully => {\n          // If we weren't able to find a focusable element in the dialog, then focus the dialog\n          // container instead.\n          if (!focusedSuccessfully) {\n            this._focusDialogContainer();\n          }\n        });\n        break;\n      case 'first-heading':\n        this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n        break;\n      default:\n        this._focusByCssSelector(this._config.autoFocus);\n        break;\n    }\n  }\n  /** Restores focus to the element that was focused before the dialog opened. */\n  _restoreFocus() {\n    const focusConfig = this._config.restoreFocus;\n    let focusTargetElement = null;\n    if (typeof focusConfig === 'string') {\n      focusTargetElement = this._document.querySelector(focusConfig);\n    } else if (typeof focusConfig === 'boolean') {\n      focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n    } else if (focusConfig) {\n      focusTargetElement = focusConfig;\n    }\n    // We need the extra check, because IE can set the `activeElement` to null in some cases.\n    if (this._config.restoreFocus && focusTargetElement && typeof focusTargetElement.focus === 'function') {\n      const activeElement = _getFocusedElementPierceShadowDom();\n      const element = this._elementRef.nativeElement;\n      // Make sure that focus is still inside the dialog or is on the body (usually because a\n      // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n      // the consumer moved it themselves before the animation was done, in which case we shouldn't\n      // do anything.\n      if (!activeElement || activeElement === this._document.body || activeElement === element || element.contains(activeElement)) {\n        if (this._focusMonitor) {\n          this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n          this._closeInteractionType = null;\n        } else {\n          focusTargetElement.focus();\n        }\n      }\n    }\n    if (this._focusTrap) {\n      this._focusTrap.destroy();\n    }\n  }\n  /** Focuses the dialog container. */\n  _focusDialogContainer() {\n    // Note that there is no focus method when rendering on the server.\n    if (this._elementRef.nativeElement.focus) {\n      this._elementRef.nativeElement.focus();\n    }\n  }\n  /** Returns whether focus is inside the dialog. */\n  _containsFocus() {\n    const element = this._elementRef.nativeElement;\n    const activeElement = _getFocusedElementPierceShadowDom();\n    return element === activeElement || element.contains(activeElement);\n  }\n  /** Sets up the focus trap. */\n  _initializeFocusTrap() {\n    if (this._platform.isBrowser) {\n      this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n      // Save the previously focused element. This element will be re-focused\n      // when the dialog closes.\n      if (this._document) {\n        this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n      }\n    }\n  }\n  /** Sets up the listener that handles clicks on the dialog backdrop. */\n  _handleBackdropClicks() {\n    // Clicking on the backdrop will move focus out of dialog.\n    // Recapture it if closing via the backdrop is disabled.\n    this._overlayRef.backdropClick().subscribe(() => {\n      if (this._config.disableClose) {\n        this._recaptureFocus();\n      }\n    });\n  }\n  static {\n    this.ɵfac = function CdkDialogContainer_Factory(t) {\n      return new (t || CdkDialogContainer)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.FocusTrapFactory), i0.ɵɵdirectiveInject(DOCUMENT, 8), i0.ɵɵdirectiveInject(DialogConfig), i0.ɵɵdirectiveInject(i1.InteractivityChecker), i0.ɵɵdirectiveInject(i0.NgZone), i0.ɵɵdirectiveInject(i1$1.OverlayRef), i0.ɵɵdirectiveInject(i1.FocusMonitor));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: CdkDialogContainer,\n      selectors: [[\"cdk-dialog-container\"]],\n      viewQuery: function CdkDialogContainer_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(CdkPortalOutlet, 7);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._portalOutlet = _t.first);\n        }\n      },\n      hostAttrs: [\"tabindex\", \"-1\", 1, \"cdk-dialog-container\"],\n      hostVars: 6,\n      hostBindings: function CdkDialogContainer_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"id\", ctx._config.id || null)(\"role\", ctx._config.role)(\"aria-modal\", ctx._config.ariaModal)(\"aria-labelledby\", ctx._config.ariaLabel ? null : ctx._ariaLabelledByQueue[0])(\"aria-label\", ctx._config.ariaLabel)(\"aria-describedby\", ctx._config.ariaDescribedBy || null);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵInheritDefinitionFeature, i0.ɵɵStandaloneFeature],\n      decls: 1,\n      vars: 0,\n      consts: [[\"cdkPortalOutlet\", \"\"]],\n      template: function CdkDialogContainer_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, CdkDialogContainer_ng_template_0_Template, 0, 0, \"ng-template\", 0);\n        }\n      },\n      dependencies: [CdkPortalOutlet],\n      styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"],\n      encapsulation: 2\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(CdkDialogContainer, [{\n    type: Component,\n    args: [{\n      selector: 'cdk-dialog-container',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.Default,\n      standalone: true,\n      imports: [CdkPortalOutlet],\n      host: {\n        'class': 'cdk-dialog-container',\n        'tabindex': '-1',\n        '[attr.id]': '_config.id || null',\n        '[attr.role]': '_config.role',\n        '[attr.aria-modal]': '_config.ariaModal',\n        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n        '[attr.aria-label]': '_config.ariaLabel',\n        '[attr.aria-describedby]': '_config.ariaDescribedBy || null'\n      },\n      template: \"<ng-template cdkPortalOutlet />\\n\",\n      styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1.FocusTrapFactory\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DialogConfig]\n    }]\n  }, {\n    type: i1.InteractivityChecker\n  }, {\n    type: i0.NgZone\n  }, {\n    type: i1$1.OverlayRef\n  }, {\n    type: i1.FocusMonitor\n  }], {\n    _portalOutlet: [{\n      type: ViewChild,\n      args: [CdkPortalOutlet, {\n        static: true\n      }]\n    }]\n  });\n})();\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n  constructor(overlayRef, config) {\n    this.overlayRef = overlayRef;\n    this.config = config;\n    /** Emits when the dialog has been closed. */\n    this.closed = new Subject();\n    this.disableClose = config.disableClose;\n    this.backdropClick = overlayRef.backdropClick();\n    this.keydownEvents = overlayRef.keydownEvents();\n    this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n    this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n    this.keydownEvents.subscribe(event => {\n      if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n        event.preventDefault();\n        this.close(undefined, {\n          focusOrigin: 'keyboard'\n        });\n      }\n    });\n    this.backdropClick.subscribe(() => {\n      if (!this.disableClose) {\n        this.close(undefined, {\n          focusOrigin: 'mouse'\n        });\n      }\n    });\n    this._detachSubscription = overlayRef.detachments().subscribe(() => {\n      // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n      if (config.closeOnOverlayDetachments !== false) {\n        this.close();\n      }\n    });\n  }\n  /**\n   * Close the dialog.\n   * @param result Optional result to return to the dialog opener.\n   * @param options Additional options to customize the closing behavior.\n   */\n  close(result, options) {\n    if (this.containerInstance) {\n      const closedSubject = this.closed;\n      this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n      // Drop the detach subscription first since it can be triggered by the\n      // `dispose` call and override the result of this closing sequence.\n      this._detachSubscription.unsubscribe();\n      this.overlayRef.dispose();\n      closedSubject.next(result);\n      closedSubject.complete();\n      this.componentInstance = this.containerInstance = null;\n    }\n  }\n  /** Updates the position of the dialog based on the current position strategy. */\n  updatePosition() {\n    this.overlayRef.updatePosition();\n    return this;\n  }\n  /**\n   * Updates the dialog's width and height.\n   * @param width New width of the dialog.\n   * @param height New height of the dialog.\n   */\n  updateSize(width = '', height = '') {\n    this.overlayRef.updateSize({\n      width,\n      height\n    });\n    return this;\n  }\n  /** Add a CSS class or an array of classes to the overlay pane. */\n  addPanelClass(classes) {\n    this.overlayRef.addPanelClass(classes);\n    return this;\n  }\n  /** Remove a CSS class or an array of classes from the overlay pane. */\n  removePanelClass(classes) {\n    this.overlayRef.removePanelClass(classes);\n    return this;\n  }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy', {\n  providedIn: 'root',\n  factory: () => {\n    const overlay = inject(Overlay);\n    return () => overlay.scrollStrategies.block();\n  }\n});\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n  return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n  provide: DIALOG_SCROLL_STRATEGY,\n  deps: [Overlay],\n  useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY\n};\n\n/** Unique id for the created dialog. */\nlet uniqueId = 0;\nclass Dialog {\n  /** Keeps track of the currently-open dialogs. */\n  get openDialogs() {\n    return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n  }\n  /** Stream that emits when a dialog has been opened. */\n  get afterOpened() {\n    return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n  }\n  constructor(_overlay, _injector, _defaultOptions, _parentDialog, _overlayContainer, scrollStrategy) {\n    this._overlay = _overlay;\n    this._injector = _injector;\n    this._defaultOptions = _defaultOptions;\n    this._parentDialog = _parentDialog;\n    this._overlayContainer = _overlayContainer;\n    this._openDialogsAtThisLevel = [];\n    this._afterAllClosedAtThisLevel = new Subject();\n    this._afterOpenedAtThisLevel = new Subject();\n    this._ariaHiddenElements = new Map();\n    /**\n     * Stream that emits when all open dialog have finished closing.\n     * Will emit on subscribe if there are no open dialogs to begin with.\n     */\n    this.afterAllClosed = defer(() => this.openDialogs.length ? this._getAfterAllClosed() : this._getAfterAllClosed().pipe(startWith(undefined)));\n    this._scrollStrategy = scrollStrategy;\n  }\n  open(componentOrTemplateRef, config) {\n    const defaults = this._defaultOptions || new DialogConfig();\n    config = {\n      ...defaults,\n      ...config\n    };\n    config.id = config.id || `cdk-dialog-${uniqueId++}`;\n    if (config.id && this.getDialogById(config.id) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n    }\n    const overlayConfig = this._getOverlayConfig(config);\n    const overlayRef = this._overlay.create(overlayConfig);\n    const dialogRef = new DialogRef(overlayRef, config);\n    const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n    dialogRef.containerInstance = dialogContainer;\n    this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n    // If this is the first dialog that we're opening, hide all the non-overlay content.\n    if (!this.openDialogs.length) {\n      this._hideNonDialogContentFromAssistiveTechnology();\n    }\n    this.openDialogs.push(dialogRef);\n    dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n    this.afterOpened.next(dialogRef);\n    return dialogRef;\n  }\n  /**\n   * Closes all of the currently-open dialogs.\n   */\n  closeAll() {\n    reverseForEach(this.openDialogs, dialog => dialog.close());\n  }\n  /**\n   * Finds an open dialog by its id.\n   * @param id ID to use when looking up the dialog.\n   */\n  getDialogById(id) {\n    return this.openDialogs.find(dialog => dialog.id === id);\n  }\n  ngOnDestroy() {\n    // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n    // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n    // determines when `aria-hidden` is removed from elements outside the dialog.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => {\n      // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n      if (dialog.config.closeOnDestroy === false) {\n        this._removeOpenDialog(dialog, false);\n      }\n    });\n    // Make a second pass and close the remaining dialogs. We do this second pass in order to\n    // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n    // that should be closed and dialogs that should not.\n    reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n    this._afterAllClosedAtThisLevel.complete();\n    this._afterOpenedAtThisLevel.complete();\n    this._openDialogsAtThisLevel = [];\n  }\n  /**\n   * Creates an overlay config from a dialog config.\n   * @param config The dialog configuration.\n   * @returns The overlay configuration.\n   */\n  _getOverlayConfig(config) {\n    const state = new OverlayConfig({\n      positionStrategy: config.positionStrategy || this._overlay.position().global().centerHorizontally().centerVertically(),\n      scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n      panelClass: config.panelClass,\n      hasBackdrop: config.hasBackdrop,\n      direction: config.direction,\n      minWidth: config.minWidth,\n      minHeight: config.minHeight,\n      maxWidth: config.maxWidth,\n      maxHeight: config.maxHeight,\n      width: config.width,\n      height: config.height,\n      disposeOnNavigation: config.closeOnNavigation\n    });\n    if (config.backdropClass) {\n      state.backdropClass = config.backdropClass;\n    }\n    return state;\n  }\n  /**\n   * Attaches a dialog container to a dialog's already-created overlay.\n   * @param overlay Reference to the dialog's underlying overlay.\n   * @param config The dialog configuration.\n   * @returns A promise resolving to a ComponentRef for the attached container.\n   */\n  _attachContainer(overlay, dialogRef, config) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DialogConfig,\n      useValue: config\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }, {\n      provide: OverlayRef,\n      useValue: overlay\n    }];\n    let containerType;\n    if (config.container) {\n      if (typeof config.container === 'function') {\n        containerType = config.container;\n      } else {\n        containerType = config.container.type;\n        providers.push(...config.container.providers(config));\n      }\n    } else {\n      containerType = CdkDialogContainer;\n    }\n    const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({\n      parent: userInjector || this._injector,\n      providers\n    }), config.componentFactoryResolver);\n    const containerRef = overlay.attach(containerPortal);\n    return containerRef.instance;\n  }\n  /**\n   * Attaches the user-provided component to the already-created dialog container.\n   * @param componentOrTemplateRef The type of component being loaded into the dialog,\n   *     or a TemplateRef to instantiate as the content.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param config Configuration used to open the dialog.\n   */\n  _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n    if (componentOrTemplateRef instanceof TemplateRef) {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n      let context = {\n        $implicit: config.data,\n        dialogRef\n      };\n      if (config.templateContext) {\n        context = {\n          ...context,\n          ...(typeof config.templateContext === 'function' ? config.templateContext() : config.templateContext)\n        };\n      }\n      dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n    } else {\n      const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n      const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector, config.componentFactoryResolver));\n      dialogRef.componentRef = contentRef;\n      dialogRef.componentInstance = contentRef.instance;\n    }\n  }\n  /**\n   * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n   * of a dialog to close itself and, optionally, to return a value.\n   * @param config Config object that is used to construct the dialog.\n   * @param dialogRef Reference to the dialog being opened.\n   * @param dialogContainer Component that is going to wrap the dialog content.\n   * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n   * dialog injector, if the user didn't provide a custom one.\n   * @returns The custom injector that can be used inside the dialog.\n   */\n  _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n    const userInjector = config.injector || config.viewContainerRef?.injector;\n    const providers = [{\n      provide: DIALOG_DATA,\n      useValue: config.data\n    }, {\n      provide: DialogRef,\n      useValue: dialogRef\n    }];\n    if (config.providers) {\n      if (typeof config.providers === 'function') {\n        providers.push(...config.providers(dialogRef, config, dialogContainer));\n      } else {\n        providers.push(...config.providers);\n      }\n    }\n    if (config.direction && (!userInjector || !userInjector.get(Directionality, null, {\n      optional: true\n    }))) {\n      providers.push({\n        provide: Directionality,\n        useValue: {\n          value: config.direction,\n          change: of()\n        }\n      });\n    }\n    return Injector.create({\n      parent: userInjector || fallbackInjector,\n      providers\n    });\n  }\n  /**\n   * Removes a dialog from the array of open dialogs.\n   * @param dialogRef Dialog to be removed.\n   * @param emitEvent Whether to emit an event if this is the last dialog.\n   */\n  _removeOpenDialog(dialogRef, emitEvent) {\n    const index = this.openDialogs.indexOf(dialogRef);\n    if (index > -1) {\n      this.openDialogs.splice(index, 1);\n      // If all the dialogs were closed, remove/restore the `aria-hidden`\n      // to a the siblings and emit to the `afterAllClosed` stream.\n      if (!this.openDialogs.length) {\n        this._ariaHiddenElements.forEach((previousValue, element) => {\n          if (previousValue) {\n            element.setAttribute('aria-hidden', previousValue);\n          } else {\n            element.removeAttribute('aria-hidden');\n          }\n        });\n        this._ariaHiddenElements.clear();\n        if (emitEvent) {\n          this._getAfterAllClosed().next();\n        }\n      }\n    }\n  }\n  /** Hides all of the content that isn't an overlay from assistive technology. */\n  _hideNonDialogContentFromAssistiveTechnology() {\n    const overlayContainer = this._overlayContainer.getContainerElement();\n    // Ensure that the overlay container is attached to the DOM.\n    if (overlayContainer.parentElement) {\n      const siblings = overlayContainer.parentElement.children;\n      for (let i = siblings.length - 1; i > -1; i--) {\n        const sibling = siblings[i];\n        if (sibling !== overlayContainer && sibling.nodeName !== 'SCRIPT' && sibling.nodeName !== 'STYLE' && !sibling.hasAttribute('aria-live')) {\n          this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n          sibling.setAttribute('aria-hidden', 'true');\n        }\n      }\n    }\n  }\n  _getAfterAllClosed() {\n    const parent = this._parentDialog;\n    return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n  }\n  static {\n    this.ɵfac = function Dialog_Factory(t) {\n      return new (t || Dialog)(i0.ɵɵinject(i1$1.Overlay), i0.ɵɵinject(i0.Injector), i0.ɵɵinject(DEFAULT_DIALOG_CONFIG, 8), i0.ɵɵinject(Dialog, 12), i0.ɵɵinject(i1$1.OverlayContainer), i0.ɵɵinject(DIALOG_SCROLL_STRATEGY));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: Dialog,\n      factory: Dialog.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(Dialog, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1$1.Overlay\n  }, {\n    type: i0.Injector\n  }, {\n    type: DialogConfig,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DEFAULT_DIALOG_CONFIG]\n    }]\n  }, {\n    type: Dialog,\n    decorators: [{\n      type: Optional\n    }, {\n      type: SkipSelf\n    }]\n  }, {\n    type: i1$1.OverlayContainer\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [DIALOG_SCROLL_STRATEGY]\n    }]\n  }], null);\n})();\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n  let i = items.length;\n  while (i--) {\n    callback(items[i]);\n  }\n}\nclass DialogModule {\n  static {\n    this.ɵfac = function DialogModule_Factory(t) {\n      return new (t || DialogModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: DialogModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [Dialog],\n      imports: [OverlayModule, PortalModule, A11yModule,\n      // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n      // don't have to remember to import it or be faced with an unhelpful error.\n      PortalModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(DialogModule, [{\n    type: NgModule,\n    args: [{\n      imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n      exports: [\n      // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n      // don't have to remember to import it or be faced with an unhelpful error.\n      PortalModule, CdkDialogContainer],\n      providers: [Dialog]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError };", "map": {"version": 3, "names": ["i1", "A11yModule", "i1$1", "Overlay", "OverlayConfig", "OverlayRef", "OverlayModule", "Platform", "_getFocusedElementPierceShadowDom", "BasePortalOutlet", "CdkPortalOutlet", "ComponentPortal", "TemplatePortal", "PortalModule", "DOCUMENT", "i0", "inject", "ChangeDetectorRef", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Optional", "Inject", "ViewChild", "InjectionToken", "Injector", "TemplateRef", "Injectable", "SkipSelf", "NgModule", "ESCAPE", "hasModifierKey", "Subject", "defer", "of", "Directionality", "startWith", "CdkDialogContainer_ng_template_0_Template", "rf", "ctx", "DialogConfig", "constructor", "role", "panelClass", "hasBackdrop", "backdropClass", "disableClose", "width", "height", "data", "ariaDescribedBy", "ariaLabelledBy", "aria<PERSON><PERSON><PERSON>", "ariaModal", "autoFocus", "restoreFocus", "closeOnNavigation", "closeOnDestroy", "closeOnOverlayDetachments", "throwDialogContentAlreadyAttachedError", "Error", "CdkDialogContainer", "_elementRef", "_focusTrapFactory", "_document", "_config", "_interactivityC<PERSON>cker", "_ngZone", "_overlayRef", "_focusMonitor", "_platform", "_focusTrap", "_elementFocusedBeforeDialogWasOpened", "_closeInteractionType", "_ariaLabelledByQueue", "_changeDetectorRef", "attachDomPortal", "portal", "_portalOutlet", "has<PERSON>tta<PERSON>", "ngDevMode", "result", "_contentAttached", "push", "_addAriaLabelledBy", "id", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_removeAriaLabelledBy", "index", "indexOf", "splice", "_initializeFocusTrap", "_handleBackdropClicks", "_captureInitialFocus", "_trapFocus", "ngOnDestroy", "_restoreFocus", "attachComponentPortal", "attachTemplatePortal", "_recaptureFocus", "_containsFocus", "_forceFocus", "element", "options", "isFocusable", "tabIndex", "runOutsideAngular", "callback", "removeEventListener", "removeAttribute", "addEventListener", "focus", "_focusByCssSelector", "selector", "elementToFocus", "nativeElement", "querySelector", "focusInitialElementWhenReady", "then", "focusedSuccessfully", "_focusDialogContainer", "focusConfig", "focusTargetElement", "activeElement", "body", "contains", "focusVia", "destroy", "<PERSON><PERSON><PERSON><PERSON>", "create", "backdropClick", "subscribe", "ɵfac", "CdkDialogContainer_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "FocusTrapFactory", "InteractivityChecker", "NgZone", "FocusMonitor", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "viewQuery", "CdkDialogContainer_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "hostAttrs", "hostVars", "hostBindings", "CdkDialogContainer_HostBindings", "ɵɵattribute", "standalone", "features", "ɵɵInheritDefinitionFeature", "ɵɵStandaloneFeature", "decls", "vars", "consts", "template", "CdkDialogContainer_Template", "ɵɵtemplate", "dependencies", "styles", "encapsulation", "ɵsetClassMetadata", "args", "None", "changeDetection", "<PERSON><PERSON><PERSON>", "imports", "host", "undefined", "decorators", "static", "DialogRef", "overlayRef", "config", "closed", "keydownEvents", "outsidePointerEvents", "event", "keyCode", "preventDefault", "close", "<PERSON><PERSON><PERSON><PERSON>", "_detachSubscription", "detachments", "containerInstance", "closedSubject", "unsubscribe", "dispose", "next", "complete", "componentInstance", "updatePosition", "updateSize", "addPanelClass", "classes", "removePanelClass", "DIALOG_SCROLL_STRATEGY", "providedIn", "factory", "overlay", "scrollStrategies", "block", "DIALOG_DATA", "DEFAULT_DIALOG_CONFIG", "DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY", "DIALOG_SCROLL_STRATEGY_PROVIDER", "provide", "deps", "useFactory", "uniqueId", "Dialog", "openDialogs", "_parentDialog", "_openDialogsAtThisLevel", "afterOpened", "_afterOpenedAtThisLevel", "_overlay", "_injector", "_defaultOptions", "_overlayContainer", "scrollStrategy", "_afterAllClosedAtThisLevel", "_ariaHiddenElements", "Map", "afterAllClosed", "length", "_getAfterAllClosed", "pipe", "_scrollStrategy", "open", "componentOrTemplateRef", "defaults", "getDialogById", "overlayConfig", "_getOverlayConfig", "dialogRef", "dialogContainer", "_attachC<PERSON>r", "_attach<PERSON><PERSON>og<PERSON><PERSON>nt", "_hideNonDialogContentFromAssistiveTechnology", "_removeOpenDialog", "closeAll", "reverseForEach", "dialog", "find", "state", "positionStrategy", "position", "global", "centerHorizontally", "centerVertically", "direction", "min<PERSON><PERSON><PERSON>", "minHeight", "max<PERSON><PERSON><PERSON>", "maxHeight", "disposeOnNavigation", "userInjector", "injector", "viewContainerRef", "providers", "useValue", "containerType", "container", "containerPortal", "parent", "componentFactoryResolver", "containerRef", "attach", "instance", "_createInjector", "context", "$implicit", "templateContext", "contentRef", "componentRef", "fallbackInjector", "get", "optional", "value", "change", "emitEvent", "for<PERSON>ach", "previousValue", "setAttribute", "clear", "overlayContainer", "getContainerElement", "parentElement", "siblings", "children", "i", "sibling", "nodeName", "hasAttribute", "set", "getAttribute", "Dialog_Factory", "ɵɵinject", "OverlayContainer", "ɵprov", "ɵɵdefineInjectable", "token", "items", "DialogModule", "DialogModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/cdk/fesm2022/dialog.mjs"], "sourcesContent": ["import * as i1 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i1$1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayConfig, OverlayRef, OverlayModule } from '@angular/cdk/overlay';\nimport { Platform, _getFocusedElementPierceShadowDom } from '@angular/cdk/platform';\nimport { BasePortalOutlet, CdkPortalOutlet, ComponentPortal, TemplatePortal, PortalModule } from '@angular/cdk/portal';\nimport { DOCUMENT } from '@angular/common';\nimport * as i0 from '@angular/core';\nimport { inject, ChangeDetectorRef, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, ViewChild, InjectionToken, Injector, TemplateRef, Injectable, SkipSelf, NgModule } from '@angular/core';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport { Subject, defer, of } from 'rxjs';\nimport { Directionality } from '@angular/cdk/bidi';\nimport { startWith } from 'rxjs/operators';\n\n/** Configuration for opening a modal dialog. */\nclass DialogConfig {\n    constructor() {\n        /** The ARIA role of the dialog element. */\n        this.role = 'dialog';\n        /** Optional CSS class or classes applied to the overlay panel. */\n        this.panelClass = '';\n        /** Whether the dialog has a backdrop. */\n        this.hasBackdrop = true;\n        /** Optional CSS class or classes applied to the overlay backdrop. */\n        this.backdropClass = '';\n        /** Whether the dialog closes with the escape key or pointer events outside the panel element. */\n        this.disableClose = false;\n        /** Width of the dialog. */\n        this.width = '';\n        /** Height of the dialog. */\n        this.height = '';\n        /** Data being injected into the child component. */\n        this.data = null;\n        /** ID of the element that describes the dialog. */\n        this.ariaDescribedBy = null;\n        /** ID of the element that labels the dialog. */\n        this.ariaLabelledBy = null;\n        /** Dialog label applied via `aria-label` */\n        this.ariaLabel = null;\n        /** Whether this is a modal dialog. Used to set the `aria-modal` attribute. */\n        this.ariaModal = true;\n        /**\n         * Where the dialog should focus on open.\n         * @breaking-change 14.0.0 Remove boolean option from autoFocus. Use string or\n         * AutoFocusTarget instead.\n         */\n        this.autoFocus = 'first-tabbable';\n        /**\n         * Whether the dialog should restore focus to the previously-focused element upon closing.\n         * Has the following behavior based on the type that is passed in:\n         * - `boolean` - when true, will return focus to the element that was focused before the dialog\n         *    was opened, otherwise won't restore focus at all.\n         * - `string` - focus will be restored to the first element that matches the CSS selector.\n         * - `HTMLElement` - focus will be restored to the specific element.\n         */\n        this.restoreFocus = true;\n        /**\n         * Whether the dialog should close when the user navigates backwards or forwards through browser\n         * history. This does not apply to navigation via anchor element unless using URL-hash based\n         * routing (`HashLocationStrategy` in the Angular router).\n         */\n        this.closeOnNavigation = true;\n        /**\n         * Whether the dialog should close when the dialog service is destroyed. This is useful if\n         * another service is wrapping the dialog and is managing the destruction instead.\n         */\n        this.closeOnDestroy = true;\n        /**\n         * Whether the dialog should close when the underlying overlay is detached. This is useful if\n         * another service is wrapping the dialog and is managing the destruction instead. E.g. an\n         * external detachment can happen as a result of a scroll strategy triggering it or when the\n         * browser location changes.\n         */\n        this.closeOnOverlayDetachments = true;\n    }\n}\n\nfunction throwDialogContentAlreadyAttachedError() {\n    throw Error('Attempting to attach dialog content after content is already attached');\n}\n/**\n * Internal component that wraps user-provided dialog content.\n * @docs-private\n */\nclass CdkDialogContainer extends BasePortalOutlet {\n    constructor(_elementRef, _focusTrapFactory, _document, _config, _interactivityChecker, _ngZone, _overlayRef, _focusMonitor) {\n        super();\n        this._elementRef = _elementRef;\n        this._focusTrapFactory = _focusTrapFactory;\n        this._config = _config;\n        this._interactivityChecker = _interactivityChecker;\n        this._ngZone = _ngZone;\n        this._overlayRef = _overlayRef;\n        this._focusMonitor = _focusMonitor;\n        this._platform = inject(Platform);\n        /** The class that traps and manages focus within the dialog. */\n        this._focusTrap = null;\n        /** Element that was focused before the dialog was opened. Save this to restore upon close. */\n        this._elementFocusedBeforeDialogWasOpened = null;\n        /**\n         * Type of interaction that led to the dialog being closed. This is used to determine\n         * whether the focus style will be applied when returning focus to its original location\n         * after the dialog is closed.\n         */\n        this._closeInteractionType = null;\n        /**\n         * Queue of the IDs of the dialog's label element, based on their definition order. The first\n         * ID will be used as the `aria-labelledby` value. We use a queue here to handle the case\n         * where there are two or more titles in the DOM at a time and the first one is destroyed while\n         * the rest are present.\n         */\n        this._ariaLabelledByQueue = [];\n        this._changeDetectorRef = inject(ChangeDetectorRef);\n        /**\n         * Attaches a DOM portal to the dialog container.\n         * @param portal Portal to be attached.\n         * @deprecated To be turned into a method.\n         * @breaking-change 10.0.0\n         */\n        this.attachDomPortal = (portal) => {\n            if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n                throwDialogContentAlreadyAttachedError();\n            }\n            const result = this._portalOutlet.attachDomPortal(portal);\n            this._contentAttached();\n            return result;\n        };\n        this._document = _document;\n        if (this._config.ariaLabelledBy) {\n            this._ariaLabelledByQueue.push(this._config.ariaLabelledBy);\n        }\n    }\n    _addAriaLabelledBy(id) {\n        this._ariaLabelledByQueue.push(id);\n        this._changeDetectorRef.markForCheck();\n    }\n    _removeAriaLabelledBy(id) {\n        const index = this._ariaLabelledByQueue.indexOf(id);\n        if (index > -1) {\n            this._ariaLabelledByQueue.splice(index, 1);\n            this._changeDetectorRef.markForCheck();\n        }\n    }\n    _contentAttached() {\n        this._initializeFocusTrap();\n        this._handleBackdropClicks();\n        this._captureInitialFocus();\n    }\n    /**\n     * Can be used by child classes to customize the initial focus\n     * capturing behavior (e.g. if it's tied to an animation).\n     */\n    _captureInitialFocus() {\n        this._trapFocus();\n    }\n    ngOnDestroy() {\n        this._restoreFocus();\n    }\n    /**\n     * Attach a ComponentPortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachComponentPortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachComponentPortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    /**\n     * Attach a TemplatePortal as content to this dialog container.\n     * @param portal Portal to be attached as the dialog content.\n     */\n    attachTemplatePortal(portal) {\n        if (this._portalOutlet.hasAttached() && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throwDialogContentAlreadyAttachedError();\n        }\n        const result = this._portalOutlet.attachTemplatePortal(portal);\n        this._contentAttached();\n        return result;\n    }\n    // TODO(crisbeto): this shouldn't be exposed, but there are internal references to it.\n    /** Captures focus if it isn't already inside the dialog. */\n    _recaptureFocus() {\n        if (!this._containsFocus()) {\n            this._trapFocus();\n        }\n    }\n    /**\n     * Focuses the provided element. If the element is not focusable, it will add a tabIndex\n     * attribute to forcefully focus it. The attribute is removed after focus is moved.\n     * @param element The element to focus.\n     */\n    _forceFocus(element, options) {\n        if (!this._interactivityChecker.isFocusable(element)) {\n            element.tabIndex = -1;\n            // The tabindex attribute should be removed to avoid navigating to that element again\n            this._ngZone.runOutsideAngular(() => {\n                const callback = () => {\n                    element.removeEventListener('blur', callback);\n                    element.removeEventListener('mousedown', callback);\n                    element.removeAttribute('tabindex');\n                };\n                element.addEventListener('blur', callback);\n                element.addEventListener('mousedown', callback);\n            });\n        }\n        element.focus(options);\n    }\n    /**\n     * Focuses the first element that matches the given selector within the focus trap.\n     * @param selector The CSS selector for the element to set focus to.\n     */\n    _focusByCssSelector(selector, options) {\n        let elementToFocus = this._elementRef.nativeElement.querySelector(selector);\n        if (elementToFocus) {\n            this._forceFocus(elementToFocus, options);\n        }\n    }\n    /**\n     * Moves the focus inside the focus trap. When autoFocus is not set to 'dialog', if focus\n     * cannot be moved then focus will go to the dialog container.\n     */\n    _trapFocus() {\n        const element = this._elementRef.nativeElement;\n        // If were to attempt to focus immediately, then the content of the dialog would not yet be\n        // ready in instances where change detection has to run first. To deal with this, we simply\n        // wait for the microtask queue to be empty when setting focus when autoFocus isn't set to\n        // dialog. If the element inside the dialog can't be focused, then the container is focused\n        // so the user can't tab into other elements behind it.\n        switch (this._config.autoFocus) {\n            case false:\n            case 'dialog':\n                // Ensure that focus is on the dialog container. It's possible that a different\n                // component tried to move focus while the open animation was running. See:\n                // https://github.com/angular/components/issues/16215. Note that we only want to do this\n                // if the focus isn't inside the dialog already, because it's possible that the consumer\n                // turned off `autoFocus` in order to move focus themselves.\n                if (!this._containsFocus()) {\n                    element.focus();\n                }\n                break;\n            case true:\n            case 'first-tabbable':\n                this._focusTrap?.focusInitialElementWhenReady().then(focusedSuccessfully => {\n                    // If we weren't able to find a focusable element in the dialog, then focus the dialog\n                    // container instead.\n                    if (!focusedSuccessfully) {\n                        this._focusDialogContainer();\n                    }\n                });\n                break;\n            case 'first-heading':\n                this._focusByCssSelector('h1, h2, h3, h4, h5, h6, [role=\"heading\"]');\n                break;\n            default:\n                this._focusByCssSelector(this._config.autoFocus);\n                break;\n        }\n    }\n    /** Restores focus to the element that was focused before the dialog opened. */\n    _restoreFocus() {\n        const focusConfig = this._config.restoreFocus;\n        let focusTargetElement = null;\n        if (typeof focusConfig === 'string') {\n            focusTargetElement = this._document.querySelector(focusConfig);\n        }\n        else if (typeof focusConfig === 'boolean') {\n            focusTargetElement = focusConfig ? this._elementFocusedBeforeDialogWasOpened : null;\n        }\n        else if (focusConfig) {\n            focusTargetElement = focusConfig;\n        }\n        // We need the extra check, because IE can set the `activeElement` to null in some cases.\n        if (this._config.restoreFocus &&\n            focusTargetElement &&\n            typeof focusTargetElement.focus === 'function') {\n            const activeElement = _getFocusedElementPierceShadowDom();\n            const element = this._elementRef.nativeElement;\n            // Make sure that focus is still inside the dialog or is on the body (usually because a\n            // non-focusable element like the backdrop was clicked) before moving it. It's possible that\n            // the consumer moved it themselves before the animation was done, in which case we shouldn't\n            // do anything.\n            if (!activeElement ||\n                activeElement === this._document.body ||\n                activeElement === element ||\n                element.contains(activeElement)) {\n                if (this._focusMonitor) {\n                    this._focusMonitor.focusVia(focusTargetElement, this._closeInteractionType);\n                    this._closeInteractionType = null;\n                }\n                else {\n                    focusTargetElement.focus();\n                }\n            }\n        }\n        if (this._focusTrap) {\n            this._focusTrap.destroy();\n        }\n    }\n    /** Focuses the dialog container. */\n    _focusDialogContainer() {\n        // Note that there is no focus method when rendering on the server.\n        if (this._elementRef.nativeElement.focus) {\n            this._elementRef.nativeElement.focus();\n        }\n    }\n    /** Returns whether focus is inside the dialog. */\n    _containsFocus() {\n        const element = this._elementRef.nativeElement;\n        const activeElement = _getFocusedElementPierceShadowDom();\n        return element === activeElement || element.contains(activeElement);\n    }\n    /** Sets up the focus trap. */\n    _initializeFocusTrap() {\n        if (this._platform.isBrowser) {\n            this._focusTrap = this._focusTrapFactory.create(this._elementRef.nativeElement);\n            // Save the previously focused element. This element will be re-focused\n            // when the dialog closes.\n            if (this._document) {\n                this._elementFocusedBeforeDialogWasOpened = _getFocusedElementPierceShadowDom();\n            }\n        }\n    }\n    /** Sets up the listener that handles clicks on the dialog backdrop. */\n    _handleBackdropClicks() {\n        // Clicking on the backdrop will move focus out of dialog.\n        // Recapture it if closing via the backdrop is disabled.\n        this._overlayRef.backdropClick().subscribe(() => {\n            if (this._config.disableClose) {\n                this._recaptureFocus();\n            }\n        });\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDialogContainer, deps: [{ token: i0.ElementRef }, { token: i1.FocusTrapFactory }, { token: DOCUMENT, optional: true }, { token: DialogConfig }, { token: i1.InteractivityChecker }, { token: i0.NgZone }, { token: i1$1.OverlayRef }, { token: i1.FocusMonitor }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: CdkDialogContainer, isStandalone: true, selector: \"cdk-dialog-container\", host: { attributes: { \"tabindex\": \"-1\" }, properties: { \"attr.id\": \"_config.id || null\", \"attr.role\": \"_config.role\", \"attr.aria-modal\": \"_config.ariaModal\", \"attr.aria-labelledby\": \"_config.ariaLabel ? null : _ariaLabelledByQueue[0]\", \"attr.aria-label\": \"_config.ariaLabel\", \"attr.aria-describedby\": \"_config.ariaDescribedBy || null\" }, classAttribute: \"cdk-dialog-container\" }, viewQueries: [{ propertyName: \"_portalOutlet\", first: true, predicate: CdkPortalOutlet, descendants: true, static: true }], usesInheritance: true, ngImport: i0, template: \"<ng-template cdkPortalOutlet />\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"], dependencies: [{ kind: \"directive\", type: CdkPortalOutlet, selector: \"[cdkPortalOutlet]\", inputs: [\"cdkPortalOutlet\"], outputs: [\"attached\"], exportAs: [\"cdkPortalOutlet\"] }], changeDetection: i0.ChangeDetectionStrategy.Default, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: CdkDialogContainer, decorators: [{\n            type: Component,\n            args: [{ selector: 'cdk-dialog-container', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.Default, standalone: true, imports: [CdkPortalOutlet], host: {\n                        'class': 'cdk-dialog-container',\n                        'tabindex': '-1',\n                        '[attr.id]': '_config.id || null',\n                        '[attr.role]': '_config.role',\n                        '[attr.aria-modal]': '_config.ariaModal',\n                        '[attr.aria-labelledby]': '_config.ariaLabel ? null : _ariaLabelledByQueue[0]',\n                        '[attr.aria-label]': '_config.ariaLabel',\n                        '[attr.aria-describedby]': '_config.ariaDescribedBy || null',\n                    }, template: \"<ng-template cdkPortalOutlet />\\n\", styles: [\".cdk-dialog-container{display:block;width:100%;height:100%;min-height:inherit;max-height:inherit}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i1.FocusTrapFactory }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DialogConfig]\n                }] }, { type: i1.InteractivityChecker }, { type: i0.NgZone }, { type: i1$1.OverlayRef }, { type: i1.FocusMonitor }], propDecorators: { _portalOutlet: [{\n                type: ViewChild,\n                args: [CdkPortalOutlet, { static: true }]\n            }] } });\n\n/**\n * Reference to a dialog opened via the Dialog service.\n */\nclass DialogRef {\n    constructor(overlayRef, config) {\n        this.overlayRef = overlayRef;\n        this.config = config;\n        /** Emits when the dialog has been closed. */\n        this.closed = new Subject();\n        this.disableClose = config.disableClose;\n        this.backdropClick = overlayRef.backdropClick();\n        this.keydownEvents = overlayRef.keydownEvents();\n        this.outsidePointerEvents = overlayRef.outsidePointerEvents();\n        this.id = config.id; // By the time the dialog is created we are guaranteed to have an ID.\n        this.keydownEvents.subscribe(event => {\n            if (event.keyCode === ESCAPE && !this.disableClose && !hasModifierKey(event)) {\n                event.preventDefault();\n                this.close(undefined, { focusOrigin: 'keyboard' });\n            }\n        });\n        this.backdropClick.subscribe(() => {\n            if (!this.disableClose) {\n                this.close(undefined, { focusOrigin: 'mouse' });\n            }\n        });\n        this._detachSubscription = overlayRef.detachments().subscribe(() => {\n            // Check specifically for `false`, because we want `undefined` to be treated like `true`.\n            if (config.closeOnOverlayDetachments !== false) {\n                this.close();\n            }\n        });\n    }\n    /**\n     * Close the dialog.\n     * @param result Optional result to return to the dialog opener.\n     * @param options Additional options to customize the closing behavior.\n     */\n    close(result, options) {\n        if (this.containerInstance) {\n            const closedSubject = this.closed;\n            this.containerInstance._closeInteractionType = options?.focusOrigin || 'program';\n            // Drop the detach subscription first since it can be triggered by the\n            // `dispose` call and override the result of this closing sequence.\n            this._detachSubscription.unsubscribe();\n            this.overlayRef.dispose();\n            closedSubject.next(result);\n            closedSubject.complete();\n            this.componentInstance = this.containerInstance = null;\n        }\n    }\n    /** Updates the position of the dialog based on the current position strategy. */\n    updatePosition() {\n        this.overlayRef.updatePosition();\n        return this;\n    }\n    /**\n     * Updates the dialog's width and height.\n     * @param width New width of the dialog.\n     * @param height New height of the dialog.\n     */\n    updateSize(width = '', height = '') {\n        this.overlayRef.updateSize({ width, height });\n        return this;\n    }\n    /** Add a CSS class or an array of classes to the overlay pane. */\n    addPanelClass(classes) {\n        this.overlayRef.addPanelClass(classes);\n        return this;\n    }\n    /** Remove a CSS class or an array of classes from the overlay pane. */\n    removePanelClass(classes) {\n        this.overlayRef.removePanelClass(classes);\n        return this;\n    }\n}\n\n/** Injection token for the Dialog's ScrollStrategy. */\nconst DIALOG_SCROLL_STRATEGY = new InjectionToken('DialogScrollStrategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.block();\n    },\n});\n/** Injection token for the Dialog's Data. */\nconst DIALOG_DATA = new InjectionToken('DialogData');\n/** Injection token that can be used to provide default options for the dialog module. */\nconst DEFAULT_DIALOG_CONFIG = new InjectionToken('DefaultDialogConfig');\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nfunction DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.block();\n}\n/**\n * @docs-private\n * @deprecated No longer used. To be removed.\n * @breaking-change 19.0.0\n */\nconst DIALOG_SCROLL_STRATEGY_PROVIDER = {\n    provide: DIALOG_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY,\n};\n\n/** Unique id for the created dialog. */\nlet uniqueId = 0;\nclass Dialog {\n    /** Keeps track of the currently-open dialogs. */\n    get openDialogs() {\n        return this._parentDialog ? this._parentDialog.openDialogs : this._openDialogsAtThisLevel;\n    }\n    /** Stream that emits when a dialog has been opened. */\n    get afterOpened() {\n        return this._parentDialog ? this._parentDialog.afterOpened : this._afterOpenedAtThisLevel;\n    }\n    constructor(_overlay, _injector, _defaultOptions, _parentDialog, _overlayContainer, scrollStrategy) {\n        this._overlay = _overlay;\n        this._injector = _injector;\n        this._defaultOptions = _defaultOptions;\n        this._parentDialog = _parentDialog;\n        this._overlayContainer = _overlayContainer;\n        this._openDialogsAtThisLevel = [];\n        this._afterAllClosedAtThisLevel = new Subject();\n        this._afterOpenedAtThisLevel = new Subject();\n        this._ariaHiddenElements = new Map();\n        /**\n         * Stream that emits when all open dialog have finished closing.\n         * Will emit on subscribe if there are no open dialogs to begin with.\n         */\n        this.afterAllClosed = defer(() => this.openDialogs.length\n            ? this._getAfterAllClosed()\n            : this._getAfterAllClosed().pipe(startWith(undefined)));\n        this._scrollStrategy = scrollStrategy;\n    }\n    open(componentOrTemplateRef, config) {\n        const defaults = (this._defaultOptions || new DialogConfig());\n        config = { ...defaults, ...config };\n        config.id = config.id || `cdk-dialog-${uniqueId++}`;\n        if (config.id &&\n            this.getDialogById(config.id) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Dialog with id \"${config.id}\" exists already. The dialog id must be unique.`);\n        }\n        const overlayConfig = this._getOverlayConfig(config);\n        const overlayRef = this._overlay.create(overlayConfig);\n        const dialogRef = new DialogRef(overlayRef, config);\n        const dialogContainer = this._attachContainer(overlayRef, dialogRef, config);\n        dialogRef.containerInstance = dialogContainer;\n        this._attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config);\n        // If this is the first dialog that we're opening, hide all the non-overlay content.\n        if (!this.openDialogs.length) {\n            this._hideNonDialogContentFromAssistiveTechnology();\n        }\n        this.openDialogs.push(dialogRef);\n        dialogRef.closed.subscribe(() => this._removeOpenDialog(dialogRef, true));\n        this.afterOpened.next(dialogRef);\n        return dialogRef;\n    }\n    /**\n     * Closes all of the currently-open dialogs.\n     */\n    closeAll() {\n        reverseForEach(this.openDialogs, dialog => dialog.close());\n    }\n    /**\n     * Finds an open dialog by its id.\n     * @param id ID to use when looking up the dialog.\n     */\n    getDialogById(id) {\n        return this.openDialogs.find(dialog => dialog.id === id);\n    }\n    ngOnDestroy() {\n        // Make one pass over all the dialogs that need to be untracked, but should not be closed. We\n        // want to stop tracking the open dialog even if it hasn't been closed, because the tracking\n        // determines when `aria-hidden` is removed from elements outside the dialog.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => {\n            // Check for `false` specifically since we want `undefined` to be interpreted as `true`.\n            if (dialog.config.closeOnDestroy === false) {\n                this._removeOpenDialog(dialog, false);\n            }\n        });\n        // Make a second pass and close the remaining dialogs. We do this second pass in order to\n        // correctly dispatch the `afterAllClosed` event in case we have a mixed array of dialogs\n        // that should be closed and dialogs that should not.\n        reverseForEach(this._openDialogsAtThisLevel, dialog => dialog.close());\n        this._afterAllClosedAtThisLevel.complete();\n        this._afterOpenedAtThisLevel.complete();\n        this._openDialogsAtThisLevel = [];\n    }\n    /**\n     * Creates an overlay config from a dialog config.\n     * @param config The dialog configuration.\n     * @returns The overlay configuration.\n     */\n    _getOverlayConfig(config) {\n        const state = new OverlayConfig({\n            positionStrategy: config.positionStrategy ||\n                this._overlay.position().global().centerHorizontally().centerVertically(),\n            scrollStrategy: config.scrollStrategy || this._scrollStrategy(),\n            panelClass: config.panelClass,\n            hasBackdrop: config.hasBackdrop,\n            direction: config.direction,\n            minWidth: config.minWidth,\n            minHeight: config.minHeight,\n            maxWidth: config.maxWidth,\n            maxHeight: config.maxHeight,\n            width: config.width,\n            height: config.height,\n            disposeOnNavigation: config.closeOnNavigation,\n        });\n        if (config.backdropClass) {\n            state.backdropClass = config.backdropClass;\n        }\n        return state;\n    }\n    /**\n     * Attaches a dialog container to a dialog's already-created overlay.\n     * @param overlay Reference to the dialog's underlying overlay.\n     * @param config The dialog configuration.\n     * @returns A promise resolving to a ComponentRef for the attached container.\n     */\n    _attachContainer(overlay, dialogRef, config) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DialogConfig, useValue: config },\n            { provide: DialogRef, useValue: dialogRef },\n            { provide: OverlayRef, useValue: overlay },\n        ];\n        let containerType;\n        if (config.container) {\n            if (typeof config.container === 'function') {\n                containerType = config.container;\n            }\n            else {\n                containerType = config.container.type;\n                providers.push(...config.container.providers(config));\n            }\n        }\n        else {\n            containerType = CdkDialogContainer;\n        }\n        const containerPortal = new ComponentPortal(containerType, config.viewContainerRef, Injector.create({ parent: userInjector || this._injector, providers }), config.componentFactoryResolver);\n        const containerRef = overlay.attach(containerPortal);\n        return containerRef.instance;\n    }\n    /**\n     * Attaches the user-provided component to the already-created dialog container.\n     * @param componentOrTemplateRef The type of component being loaded into the dialog,\n     *     or a TemplateRef to instantiate as the content.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param config Configuration used to open the dialog.\n     */\n    _attachDialogContent(componentOrTemplateRef, dialogRef, dialogContainer, config) {\n        if (componentOrTemplateRef instanceof TemplateRef) {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, undefined);\n            let context = { $implicit: config.data, dialogRef };\n            if (config.templateContext) {\n                context = {\n                    ...context,\n                    ...(typeof config.templateContext === 'function'\n                        ? config.templateContext()\n                        : config.templateContext),\n                };\n            }\n            dialogContainer.attachTemplatePortal(new TemplatePortal(componentOrTemplateRef, null, context, injector));\n        }\n        else {\n            const injector = this._createInjector(config, dialogRef, dialogContainer, this._injector);\n            const contentRef = dialogContainer.attachComponentPortal(new ComponentPortal(componentOrTemplateRef, config.viewContainerRef, injector, config.componentFactoryResolver));\n            dialogRef.componentRef = contentRef;\n            dialogRef.componentInstance = contentRef.instance;\n        }\n    }\n    /**\n     * Creates a custom injector to be used inside the dialog. This allows a component loaded inside\n     * of a dialog to close itself and, optionally, to return a value.\n     * @param config Config object that is used to construct the dialog.\n     * @param dialogRef Reference to the dialog being opened.\n     * @param dialogContainer Component that is going to wrap the dialog content.\n     * @param fallbackInjector Injector to use as a fallback when a lookup fails in the custom\n     * dialog injector, if the user didn't provide a custom one.\n     * @returns The custom injector that can be used inside the dialog.\n     */\n    _createInjector(config, dialogRef, dialogContainer, fallbackInjector) {\n        const userInjector = config.injector || config.viewContainerRef?.injector;\n        const providers = [\n            { provide: DIALOG_DATA, useValue: config.data },\n            { provide: DialogRef, useValue: dialogRef },\n        ];\n        if (config.providers) {\n            if (typeof config.providers === 'function') {\n                providers.push(...config.providers(dialogRef, config, dialogContainer));\n            }\n            else {\n                providers.push(...config.providers);\n            }\n        }\n        if (config.direction &&\n            (!userInjector ||\n                !userInjector.get(Directionality, null, { optional: true }))) {\n            providers.push({\n                provide: Directionality,\n                useValue: { value: config.direction, change: of() },\n            });\n        }\n        return Injector.create({ parent: userInjector || fallbackInjector, providers });\n    }\n    /**\n     * Removes a dialog from the array of open dialogs.\n     * @param dialogRef Dialog to be removed.\n     * @param emitEvent Whether to emit an event if this is the last dialog.\n     */\n    _removeOpenDialog(dialogRef, emitEvent) {\n        const index = this.openDialogs.indexOf(dialogRef);\n        if (index > -1) {\n            this.openDialogs.splice(index, 1);\n            // If all the dialogs were closed, remove/restore the `aria-hidden`\n            // to a the siblings and emit to the `afterAllClosed` stream.\n            if (!this.openDialogs.length) {\n                this._ariaHiddenElements.forEach((previousValue, element) => {\n                    if (previousValue) {\n                        element.setAttribute('aria-hidden', previousValue);\n                    }\n                    else {\n                        element.removeAttribute('aria-hidden');\n                    }\n                });\n                this._ariaHiddenElements.clear();\n                if (emitEvent) {\n                    this._getAfterAllClosed().next();\n                }\n            }\n        }\n    }\n    /** Hides all of the content that isn't an overlay from assistive technology. */\n    _hideNonDialogContentFromAssistiveTechnology() {\n        const overlayContainer = this._overlayContainer.getContainerElement();\n        // Ensure that the overlay container is attached to the DOM.\n        if (overlayContainer.parentElement) {\n            const siblings = overlayContainer.parentElement.children;\n            for (let i = siblings.length - 1; i > -1; i--) {\n                const sibling = siblings[i];\n                if (sibling !== overlayContainer &&\n                    sibling.nodeName !== 'SCRIPT' &&\n                    sibling.nodeName !== 'STYLE' &&\n                    !sibling.hasAttribute('aria-live')) {\n                    this._ariaHiddenElements.set(sibling, sibling.getAttribute('aria-hidden'));\n                    sibling.setAttribute('aria-hidden', 'true');\n                }\n            }\n        }\n    }\n    _getAfterAllClosed() {\n        const parent = this._parentDialog;\n        return parent ? parent._getAfterAllClosed() : this._afterAllClosedAtThisLevel;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: Dialog, deps: [{ token: i1$1.Overlay }, { token: i0.Injector }, { token: DEFAULT_DIALOG_CONFIG, optional: true }, { token: Dialog, optional: true, skipSelf: true }, { token: i1$1.OverlayContainer }, { token: DIALOG_SCROLL_STRATEGY }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: Dialog, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: Dialog, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1$1.Overlay }, { type: i0.Injector }, { type: DialogConfig, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DEFAULT_DIALOG_CONFIG]\n                }] }, { type: Dialog, decorators: [{\n                    type: Optional\n                }, {\n                    type: SkipSelf\n                }] }, { type: i1$1.OverlayContainer }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DIALOG_SCROLL_STRATEGY]\n                }] }] });\n/**\n * Executes a callback against all elements in an array while iterating in reverse.\n * Useful if the array is being modified as it is being iterated.\n */\nfunction reverseForEach(items, callback) {\n    let i = items.length;\n    while (i--) {\n        callback(items[i]);\n    }\n}\n\nclass DialogModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DialogModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: DialogModule, imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer], exports: [\n            // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n            // don't have to remember to import it or be faced with an unhelpful error.\n            PortalModule,\n            CdkDialogContainer] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DialogModule, providers: [Dialog], imports: [OverlayModule, PortalModule, A11yModule, \n            // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n            // don't have to remember to import it or be faced with an unhelpful error.\n            PortalModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: DialogModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [OverlayModule, PortalModule, A11yModule, CdkDialogContainer],\n                    exports: [\n                        // Re-export the PortalModule so that people extending the `CdkDialogContainer`\n                        // don't have to remember to import it or be faced with an unhelpful error.\n                        PortalModule,\n                        CdkDialogContainer,\n                    ],\n                    providers: [Dialog],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { CdkDialogContainer, DEFAULT_DIALOG_CONFIG, DIALOG_DATA, DIALOG_SCROLL_STRATEGY, DIALOG_SCROLL_STRATEGY_PROVIDER, DIALOG_SCROLL_STRATEGY_PROVIDER_FACTORY, Dialog, DialogConfig, DialogModule, DialogRef, throwDialogContentAlreadyAttachedError };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,mBAAmB;AACvC,SAASC,UAAU,QAAQ,mBAAmB;AAC9C,OAAO,KAAKC,IAAI,MAAM,sBAAsB;AAC5C,SAASC,OAAO,EAAEC,aAAa,EAAEC,UAAU,EAAEC,aAAa,QAAQ,sBAAsB;AACxF,SAASC,QAAQ,EAAEC,iCAAiC,QAAQ,uBAAuB;AACnF,SAASC,gBAAgB,EAAEC,eAAe,EAAEC,eAAe,EAAEC,cAAc,EAAEC,YAAY,QAAQ,qBAAqB;AACtH,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,MAAM,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,cAAc,EAAEC,QAAQ,EAAEC,WAAW,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AACpN,SAASC,MAAM,EAAEC,cAAc,QAAQ,uBAAuB;AAC9D,SAASC,OAAO,EAAEC,KAAK,EAAEC,EAAE,QAAQ,MAAM;AACzC,SAASC,cAAc,QAAQ,mBAAmB;AAClD,SAASC,SAAS,QAAQ,gBAAgB;;AAE1C;AAAA,SAAAC,0CAAAC,EAAA,EAAAC,GAAA;AACA,MAAMC,YAAY,CAAC;EACfC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;IACA,IAAI,CAACC,UAAU,GAAG,EAAE;IACpB;IACA,IAAI,CAACC,WAAW,GAAG,IAAI;IACvB;IACA,IAAI,CAACC,aAAa,GAAG,EAAE;IACvB;IACA,IAAI,CAACC,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,KAAK,GAAG,EAAE;IACf;IACA,IAAI,CAACC,MAAM,GAAG,EAAE;IAChB;IACA,IAAI,CAACC,IAAI,GAAG,IAAI;IAChB;IACA,IAAI,CAACC,eAAe,GAAG,IAAI;IAC3B;IACA,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;IACA,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,SAAS,GAAG,gBAAgB;IACjC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,IAAI;IACxB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,iBAAiB,GAAG,IAAI;IAC7B;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAG,IAAI;IAC1B;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,yBAAyB,GAAG,IAAI;EACzC;AACJ;AAEA,SAASC,sCAAsCA,CAAA,EAAG;EAC9C,MAAMC,KAAK,CAAC,uEAAuE,CAAC;AACxF;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,SAASpD,gBAAgB,CAAC;EAC9CgC,WAAWA,CAACqB,WAAW,EAAEC,iBAAiB,EAAEC,SAAS,EAAEC,OAAO,EAAEC,qBAAqB,EAAEC,OAAO,EAAEC,WAAW,EAAEC,aAAa,EAAE;IACxH,KAAK,CAAC,CAAC;IACP,IAAI,CAACP,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACE,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,qBAAqB,GAAGA,qBAAqB;IAClD,IAAI,CAACC,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACC,SAAS,GAAGtD,MAAM,CAACT,QAAQ,CAAC;IACjC;IACA,IAAI,CAACgE,UAAU,GAAG,IAAI;IACtB;IACA,IAAI,CAACC,oCAAoC,GAAG,IAAI;IAChD;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,qBAAqB,GAAG,IAAI;IACjC;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,EAAE;IAC9B,IAAI,CAACC,kBAAkB,GAAG3D,MAAM,CAACC,iBAAiB,CAAC;IACnD;AACR;AACA;AACA;AACA;AACA;IACQ,IAAI,CAAC2D,eAAe,GAAIC,MAAM,IAAK;MAC/B,IAAI,IAAI,CAACC,aAAa,CAACC,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;QACrFrB,sCAAsC,CAAC,CAAC;MAC5C;MACA,MAAMsB,MAAM,GAAG,IAAI,CAACH,aAAa,CAACF,eAAe,CAACC,MAAM,CAAC;MACzD,IAAI,CAACK,gBAAgB,CAAC,CAAC;MACvB,OAAOD,MAAM;IACjB,CAAC;IACD,IAAI,CAACjB,SAAS,GAAGA,SAAS;IAC1B,IAAI,IAAI,CAACC,OAAO,CAACd,cAAc,EAAE;MAC7B,IAAI,CAACuB,oBAAoB,CAACS,IAAI,CAAC,IAAI,CAAClB,OAAO,CAACd,cAAc,CAAC;IAC/D;EACJ;EACAiC,kBAAkBA,CAACC,EAAE,EAAE;IACnB,IAAI,CAACX,oBAAoB,CAACS,IAAI,CAACE,EAAE,CAAC;IAClC,IAAI,CAACV,kBAAkB,CAACW,YAAY,CAAC,CAAC;EAC1C;EACAC,qBAAqBA,CAACF,EAAE,EAAE;IACtB,MAAMG,KAAK,GAAG,IAAI,CAACd,oBAAoB,CAACe,OAAO,CAACJ,EAAE,CAAC;IACnD,IAAIG,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACd,oBAAoB,CAACgB,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MAC1C,IAAI,CAACb,kBAAkB,CAACW,YAAY,CAAC,CAAC;IAC1C;EACJ;EACAJ,gBAAgBA,CAAA,EAAG;IACf,IAAI,CAACS,oBAAoB,CAAC,CAAC;IAC3B,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAC5B,IAAI,CAACC,oBAAoB,CAAC,CAAC;EAC/B;EACA;AACJ;AACA;AACA;EACIA,oBAAoBA,CAAA,EAAG;IACnB,IAAI,CAACC,UAAU,CAAC,CAAC;EACrB;EACAC,WAAWA,CAAA,EAAG;IACV,IAAI,CAACC,aAAa,CAAC,CAAC;EACxB;EACA;AACJ;AACA;AACA;EACIC,qBAAqBA,CAACpB,MAAM,EAAE;IAC1B,IAAI,IAAI,CAACC,aAAa,CAACC,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFrB,sCAAsC,CAAC,CAAC;IAC5C;IACA,MAAMsB,MAAM,GAAG,IAAI,CAACH,aAAa,CAACmB,qBAAqB,CAACpB,MAAM,CAAC;IAC/D,IAAI,CAACK,gBAAgB,CAAC,CAAC;IACvB,OAAOD,MAAM;EACjB;EACA;AACJ;AACA;AACA;EACIiB,oBAAoBA,CAACrB,MAAM,EAAE;IACzB,IAAI,IAAI,CAACC,aAAa,CAACC,WAAW,CAAC,CAAC,KAAK,OAAOC,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACrFrB,sCAAsC,CAAC,CAAC;IAC5C;IACA,MAAMsB,MAAM,GAAG,IAAI,CAACH,aAAa,CAACoB,oBAAoB,CAACrB,MAAM,CAAC;IAC9D,IAAI,CAACK,gBAAgB,CAAC,CAAC;IACvB,OAAOD,MAAM;EACjB;EACA;EACA;EACAkB,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACC,cAAc,CAAC,CAAC,EAAE;MACxB,IAAI,CAACN,UAAU,CAAC,CAAC;IACrB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIO,WAAWA,CAACC,OAAO,EAAEC,OAAO,EAAE;IAC1B,IAAI,CAAC,IAAI,CAACrC,qBAAqB,CAACsC,WAAW,CAACF,OAAO,CAAC,EAAE;MAClDA,OAAO,CAACG,QAAQ,GAAG,CAAC,CAAC;MACrB;MACA,IAAI,CAACtC,OAAO,CAACuC,iBAAiB,CAAC,MAAM;QACjC,MAAMC,QAAQ,GAAGA,CAAA,KAAM;UACnBL,OAAO,CAACM,mBAAmB,CAAC,MAAM,EAAED,QAAQ,CAAC;UAC7CL,OAAO,CAACM,mBAAmB,CAAC,WAAW,EAAED,QAAQ,CAAC;UAClDL,OAAO,CAACO,eAAe,CAAC,UAAU,CAAC;QACvC,CAAC;QACDP,OAAO,CAACQ,gBAAgB,CAAC,MAAM,EAAEH,QAAQ,CAAC;QAC1CL,OAAO,CAACQ,gBAAgB,CAAC,WAAW,EAAEH,QAAQ,CAAC;MACnD,CAAC,CAAC;IACN;IACAL,OAAO,CAACS,KAAK,CAACR,OAAO,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIS,mBAAmBA,CAACC,QAAQ,EAAEV,OAAO,EAAE;IACnC,IAAIW,cAAc,GAAG,IAAI,CAACpD,WAAW,CAACqD,aAAa,CAACC,aAAa,CAACH,QAAQ,CAAC;IAC3E,IAAIC,cAAc,EAAE;MAChB,IAAI,CAACb,WAAW,CAACa,cAAc,EAAEX,OAAO,CAAC;IAC7C;EACJ;EACA;AACJ;AACA;AACA;EACIT,UAAUA,CAAA,EAAG;IACT,MAAMQ,OAAO,GAAG,IAAI,CAACxC,WAAW,CAACqD,aAAa;IAC9C;IACA;IACA;IACA;IACA;IACA,QAAQ,IAAI,CAAClD,OAAO,CAACX,SAAS;MAC1B,KAAK,KAAK;MACV,KAAK,QAAQ;QACT;QACA;QACA;QACA;QACA;QACA,IAAI,CAAC,IAAI,CAAC8C,cAAc,CAAC,CAAC,EAAE;UACxBE,OAAO,CAACS,KAAK,CAAC,CAAC;QACnB;QACA;MACJ,KAAK,IAAI;MACT,KAAK,gBAAgB;QACjB,IAAI,CAACxC,UAAU,EAAE8C,4BAA4B,CAAC,CAAC,CAACC,IAAI,CAACC,mBAAmB,IAAI;UACxE;UACA;UACA,IAAI,CAACA,mBAAmB,EAAE;YACtB,IAAI,CAACC,qBAAqB,CAAC,CAAC;UAChC;QACJ,CAAC,CAAC;QACF;MACJ,KAAK,eAAe;QAChB,IAAI,CAACR,mBAAmB,CAAC,0CAA0C,CAAC;QACpE;MACJ;QACI,IAAI,CAACA,mBAAmB,CAAC,IAAI,CAAC/C,OAAO,CAACX,SAAS,CAAC;QAChD;IACR;EACJ;EACA;EACA0C,aAAaA,CAAA,EAAG;IACZ,MAAMyB,WAAW,GAAG,IAAI,CAACxD,OAAO,CAACV,YAAY;IAC7C,IAAImE,kBAAkB,GAAG,IAAI;IAC7B,IAAI,OAAOD,WAAW,KAAK,QAAQ,EAAE;MACjCC,kBAAkB,GAAG,IAAI,CAAC1D,SAAS,CAACoD,aAAa,CAACK,WAAW,CAAC;IAClE,CAAC,MACI,IAAI,OAAOA,WAAW,KAAK,SAAS,EAAE;MACvCC,kBAAkB,GAAGD,WAAW,GAAG,IAAI,CAACjD,oCAAoC,GAAG,IAAI;IACvF,CAAC,MACI,IAAIiD,WAAW,EAAE;MAClBC,kBAAkB,GAAGD,WAAW;IACpC;IACA;IACA,IAAI,IAAI,CAACxD,OAAO,CAACV,YAAY,IACzBmE,kBAAkB,IAClB,OAAOA,kBAAkB,CAACX,KAAK,KAAK,UAAU,EAAE;MAChD,MAAMY,aAAa,GAAGnH,iCAAiC,CAAC,CAAC;MACzD,MAAM8F,OAAO,GAAG,IAAI,CAACxC,WAAW,CAACqD,aAAa;MAC9C;MACA;MACA;MACA;MACA,IAAI,CAACQ,aAAa,IACdA,aAAa,KAAK,IAAI,CAAC3D,SAAS,CAAC4D,IAAI,IACrCD,aAAa,KAAKrB,OAAO,IACzBA,OAAO,CAACuB,QAAQ,CAACF,aAAa,CAAC,EAAE;QACjC,IAAI,IAAI,CAACtD,aAAa,EAAE;UACpB,IAAI,CAACA,aAAa,CAACyD,QAAQ,CAACJ,kBAAkB,EAAE,IAAI,CAACjD,qBAAqB,CAAC;UAC3E,IAAI,CAACA,qBAAqB,GAAG,IAAI;QACrC,CAAC,MACI;UACDiD,kBAAkB,CAACX,KAAK,CAAC,CAAC;QAC9B;MACJ;IACJ;IACA,IAAI,IAAI,CAACxC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACwD,OAAO,CAAC,CAAC;IAC7B;EACJ;EACA;EACAP,qBAAqBA,CAAA,EAAG;IACpB;IACA,IAAI,IAAI,CAAC1D,WAAW,CAACqD,aAAa,CAACJ,KAAK,EAAE;MACtC,IAAI,CAACjD,WAAW,CAACqD,aAAa,CAACJ,KAAK,CAAC,CAAC;IAC1C;EACJ;EACA;EACAX,cAAcA,CAAA,EAAG;IACb,MAAME,OAAO,GAAG,IAAI,CAACxC,WAAW,CAACqD,aAAa;IAC9C,MAAMQ,aAAa,GAAGnH,iCAAiC,CAAC,CAAC;IACzD,OAAO8F,OAAO,KAAKqB,aAAa,IAAIrB,OAAO,CAACuB,QAAQ,CAACF,aAAa,CAAC;EACvE;EACA;EACAhC,oBAAoBA,CAAA,EAAG;IACnB,IAAI,IAAI,CAACrB,SAAS,CAAC0D,SAAS,EAAE;MAC1B,IAAI,CAACzD,UAAU,GAAG,IAAI,CAACR,iBAAiB,CAACkE,MAAM,CAAC,IAAI,CAACnE,WAAW,CAACqD,aAAa,CAAC;MAC/E;MACA;MACA,IAAI,IAAI,CAACnD,SAAS,EAAE;QAChB,IAAI,CAACQ,oCAAoC,GAAGhE,iCAAiC,CAAC,CAAC;MACnF;IACJ;EACJ;EACA;EACAoF,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,IAAI,CAACxB,WAAW,CAAC8D,aAAa,CAAC,CAAC,CAACC,SAAS,CAAC,MAAM;MAC7C,IAAI,IAAI,CAAClE,OAAO,CAACnB,YAAY,EAAE;QAC3B,IAAI,CAACqD,eAAe,CAAC,CAAC;MAC1B;IACJ,CAAC,CAAC;EACN;EACA;IAAS,IAAI,CAACiC,IAAI,YAAAC,2BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFzE,kBAAkB,EAA5B9C,EAAE,CAAAwH,iBAAA,CAA4CxH,EAAE,CAACyH,UAAU,GAA3DzH,EAAE,CAAAwH,iBAAA,CAAsEvI,EAAE,CAACyI,gBAAgB,GAA3F1H,EAAE,CAAAwH,iBAAA,CAAsGzH,QAAQ,MAAhHC,EAAE,CAAAwH,iBAAA,CAA2I/F,YAAY,GAAzJzB,EAAE,CAAAwH,iBAAA,CAAoKvI,EAAE,CAAC0I,oBAAoB,GAA7L3H,EAAE,CAAAwH,iBAAA,CAAwMxH,EAAE,CAAC4H,MAAM,GAAnN5H,EAAE,CAAAwH,iBAAA,CAA8NrI,IAAI,CAACG,UAAU,GAA/OU,EAAE,CAAAwH,iBAAA,CAA0PvI,EAAE,CAAC4I,YAAY;IAAA,CAA4C;EAAE;EACzZ;IAAS,IAAI,CAACC,IAAI,kBAD8E9H,EAAE,CAAA+H,iBAAA;MAAAC,IAAA,EACJlF,kBAAkB;MAAAmF,SAAA;MAAAC,SAAA,WAAAC,yBAAA5G,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADhBvB,EAAE,CAAAoI,WAAA,CACygBzI,eAAe;QAAA;QAAA,IAAA4B,EAAA;UAAA,IAAA8G,EAAA;UAD1hBrI,EAAE,CAAAsI,cAAA,CAAAD,EAAA,GAAFrI,EAAE,CAAAuI,WAAA,QAAA/G,GAAA,CAAAuC,aAAA,GAAAsE,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAC,SAAA,eACwG,IAAI;MAAAC,QAAA;MAAAC,YAAA,WAAAC,gCAAArH,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAD9GvB,EAAE,CAAA6I,WAAA,OAAArH,GAAA,CAAA0B,OAAA,CAAAoB,EAAA,IACU,IAAI,UAAA9C,GAAA,CAAA0B,OAAA,CAAAvB,IAAA,gBAAAH,GAAA,CAAA0B,OAAA,CAAAZ,SAAA,qBAAAd,GAAA,CAAA0B,OAAA,CAAAb,SAAA,GAAE,IAAI,GAAAb,GAAA,CAAAmC,oBAAA,CAAwB,CAAC,iBAAAnC,GAAA,CAAA0B,OAAA,CAAAb,SAAA,sBAAAb,GAAA,CAAA0B,OAAA,CAAAf,eAAA,IAAtB,IAAI;QAAA;MAAA;MAAA2G,UAAA;MAAAC,QAAA,GAD7B/I,EAAE,CAAAgJ,0BAAA,EAAFhJ,EAAE,CAAAiJ,mBAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAA/H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFvB,EAAE,CAAAuJ,UAAA,IAAAjI,yCAAA,wBAC4oB,CAAC;QAAA;MAAA;MAAAkI,YAAA,GAA8J7J,eAAe;MAAA8J,MAAA;MAAAC,aAAA;IAAA,EAAuN;EAAE;AACznC;AACA;EAAA,QAAAzF,SAAA,oBAAAA,SAAA,KAHoGjE,EAAE,CAAA2J,iBAAA,CAGX7G,kBAAkB,EAAc,CAAC;IAChHkF,IAAI,EAAE7H,SAAS;IACfyJ,IAAI,EAAE,CAAC;MAAE1D,QAAQ,EAAE,sBAAsB;MAAEwD,aAAa,EAAEtJ,iBAAiB,CAACyJ,IAAI;MAAEC,eAAe,EAAEzJ,uBAAuB,CAAC0J,OAAO;MAAEjB,UAAU,EAAE,IAAI;MAAEkB,OAAO,EAAE,CAACrK,eAAe,CAAC;MAAEsK,IAAI,EAAE;QAC5K,OAAO,EAAE,sBAAsB;QAC/B,UAAU,EAAE,IAAI;QAChB,WAAW,EAAE,oBAAoB;QACjC,aAAa,EAAE,cAAc;QAC7B,mBAAmB,EAAE,mBAAmB;QACxC,wBAAwB,EAAE,oDAAoD;QAC9E,mBAAmB,EAAE,mBAAmB;QACxC,yBAAyB,EAAE;MAC/B,CAAC;MAAEZ,QAAQ,EAAE,mCAAmC;MAAEI,MAAM,EAAE,CAAC,mGAAmG;IAAE,CAAC;EAC7K,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEzB,IAAI,EAAEhI,EAAE,CAACyH;EAAW,CAAC,EAAE;IAAEO,IAAI,EAAE/I,EAAE,CAACyI;EAAiB,CAAC,EAAE;IAAEM,IAAI,EAAEkC,SAAS;IAAEC,UAAU,EAAE,CAAC;MACvGnC,IAAI,EAAE1H;IACV,CAAC,EAAE;MACC0H,IAAI,EAAEzH,MAAM;MACZqJ,IAAI,EAAE,CAAC7J,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEiI,IAAI,EAAEkC,SAAS;IAAEC,UAAU,EAAE,CAAC;MAClCnC,IAAI,EAAEzH,MAAM;MACZqJ,IAAI,EAAE,CAACnI,YAAY;IACvB,CAAC;EAAE,CAAC,EAAE;IAAEuG,IAAI,EAAE/I,EAAE,CAAC0I;EAAqB,CAAC,EAAE;IAAEK,IAAI,EAAEhI,EAAE,CAAC4H;EAAO,CAAC,EAAE;IAAEI,IAAI,EAAE7I,IAAI,CAACG;EAAW,CAAC,EAAE;IAAE0I,IAAI,EAAE/I,EAAE,CAAC4I;EAAa,CAAC,CAAC,EAAkB;IAAE9D,aAAa,EAAE,CAAC;MACvJiE,IAAI,EAAExH,SAAS;MACfoJ,IAAI,EAAE,CAACjK,eAAe,EAAE;QAAEyK,MAAM,EAAE;MAAK,CAAC;IAC5C,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZ3I,WAAWA,CAAC4I,UAAU,EAAEC,MAAM,EAAE;IAC5B,IAAI,CAACD,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB;IACA,IAAI,CAACC,MAAM,GAAG,IAAIvJ,OAAO,CAAC,CAAC;IAC3B,IAAI,CAACc,YAAY,GAAGwI,MAAM,CAACxI,YAAY;IACvC,IAAI,CAACoF,aAAa,GAAGmD,UAAU,CAACnD,aAAa,CAAC,CAAC;IAC/C,IAAI,CAACsD,aAAa,GAAGH,UAAU,CAACG,aAAa,CAAC,CAAC;IAC/C,IAAI,CAACC,oBAAoB,GAAGJ,UAAU,CAACI,oBAAoB,CAAC,CAAC;IAC7D,IAAI,CAACpG,EAAE,GAAGiG,MAAM,CAACjG,EAAE,CAAC,CAAC;IACrB,IAAI,CAACmG,aAAa,CAACrD,SAAS,CAACuD,KAAK,IAAI;MAClC,IAAIA,KAAK,CAACC,OAAO,KAAK7J,MAAM,IAAI,CAAC,IAAI,CAACgB,YAAY,IAAI,CAACf,cAAc,CAAC2J,KAAK,CAAC,EAAE;QAC1EA,KAAK,CAACE,cAAc,CAAC,CAAC;QACtB,IAAI,CAACC,KAAK,CAACZ,SAAS,EAAE;UAAEa,WAAW,EAAE;QAAW,CAAC,CAAC;MACtD;IACJ,CAAC,CAAC;IACF,IAAI,CAAC5D,aAAa,CAACC,SAAS,CAAC,MAAM;MAC/B,IAAI,CAAC,IAAI,CAACrF,YAAY,EAAE;QACpB,IAAI,CAAC+I,KAAK,CAACZ,SAAS,EAAE;UAAEa,WAAW,EAAE;QAAQ,CAAC,CAAC;MACnD;IACJ,CAAC,CAAC;IACF,IAAI,CAACC,mBAAmB,GAAGV,UAAU,CAACW,WAAW,CAAC,CAAC,CAAC7D,SAAS,CAAC,MAAM;MAChE;MACA,IAAImD,MAAM,CAAC5H,yBAAyB,KAAK,KAAK,EAAE;QAC5C,IAAI,CAACmI,KAAK,CAAC,CAAC;MAChB;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;EACIA,KAAKA,CAAC5G,MAAM,EAAEsB,OAAO,EAAE;IACnB,IAAI,IAAI,CAAC0F,iBAAiB,EAAE;MACxB,MAAMC,aAAa,GAAG,IAAI,CAACX,MAAM;MACjC,IAAI,CAACU,iBAAiB,CAACxH,qBAAqB,GAAG8B,OAAO,EAAEuF,WAAW,IAAI,SAAS;MAChF;MACA;MACA,IAAI,CAACC,mBAAmB,CAACI,WAAW,CAAC,CAAC;MACtC,IAAI,CAACd,UAAU,CAACe,OAAO,CAAC,CAAC;MACzBF,aAAa,CAACG,IAAI,CAACpH,MAAM,CAAC;MAC1BiH,aAAa,CAACI,QAAQ,CAAC,CAAC;MACxB,IAAI,CAACC,iBAAiB,GAAG,IAAI,CAACN,iBAAiB,GAAG,IAAI;IAC1D;EACJ;EACA;EACAO,cAAcA,CAAA,EAAG;IACb,IAAI,CAACnB,UAAU,CAACmB,cAAc,CAAC,CAAC;IAChC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIC,UAAUA,CAAC1J,KAAK,GAAG,EAAE,EAAEC,MAAM,GAAG,EAAE,EAAE;IAChC,IAAI,CAACqI,UAAU,CAACoB,UAAU,CAAC;MAAE1J,KAAK;MAAEC;IAAO,CAAC,CAAC;IAC7C,OAAO,IAAI;EACf;EACA;EACA0J,aAAaA,CAACC,OAAO,EAAE;IACnB,IAAI,CAACtB,UAAU,CAACqB,aAAa,CAACC,OAAO,CAAC;IACtC,OAAO,IAAI;EACf;EACA;EACAC,gBAAgBA,CAACD,OAAO,EAAE;IACtB,IAAI,CAACtB,UAAU,CAACuB,gBAAgB,CAACD,OAAO,CAAC;IACzC,OAAO,IAAI;EACf;AACJ;;AAEA;AACA,MAAME,sBAAsB,GAAG,IAAIrL,cAAc,CAAC,sBAAsB,EAAE;EACtEsL,UAAU,EAAE,MAAM;EAClBC,OAAO,EAAEA,CAAA,KAAM;IACX,MAAMC,OAAO,GAAGhM,MAAM,CAACb,OAAO,CAAC;IAC/B,OAAO,MAAM6M,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAAC,CAAC;EACjD;AACJ,CAAC,CAAC;AACF;AACA,MAAMC,WAAW,GAAG,IAAI3L,cAAc,CAAC,YAAY,CAAC;AACpD;AACA,MAAM4L,qBAAqB,GAAG,IAAI5L,cAAc,CAAC,qBAAqB,CAAC;AACvE;AACA;AACA;AACA;AACA;AACA,SAAS6L,uCAAuCA,CAACL,OAAO,EAAE;EACtD,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,KAAK,CAAC,CAAC;AACjD;AACA;AACA;AACA;AACA;AACA;AACA,MAAMI,+BAA+B,GAAG;EACpCC,OAAO,EAAEV,sBAAsB;EAC/BW,IAAI,EAAE,CAACrN,OAAO,CAAC;EACfsN,UAAU,EAAEJ;AAChB,CAAC;;AAED;AACA,IAAIK,QAAQ,GAAG,CAAC;AAChB,MAAMC,MAAM,CAAC;EACT;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,aAAa,GAAG,IAAI,CAACA,aAAa,CAACD,WAAW,GAAG,IAAI,CAACE,uBAAuB;EAC7F;EACA;EACA,IAAIC,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACF,aAAa,GAAG,IAAI,CAACA,aAAa,CAACE,WAAW,GAAG,IAAI,CAACC,uBAAuB;EAC7F;EACAvL,WAAWA,CAACwL,QAAQ,EAAEC,SAAS,EAAEC,eAAe,EAAEN,aAAa,EAAEO,iBAAiB,EAAEC,cAAc,EAAE;IAChG,IAAI,CAACJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACN,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACO,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACN,uBAAuB,GAAG,EAAE;IACjC,IAAI,CAACQ,0BAA0B,GAAG,IAAItM,OAAO,CAAC,CAAC;IAC/C,IAAI,CAACgM,uBAAuB,GAAG,IAAIhM,OAAO,CAAC,CAAC;IAC5C,IAAI,CAACuM,mBAAmB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACpC;AACR;AACA;AACA;IACQ,IAAI,CAACC,cAAc,GAAGxM,KAAK,CAAC,MAAM,IAAI,CAAC2L,WAAW,CAACc,MAAM,GACnD,IAAI,CAACC,kBAAkB,CAAC,CAAC,GACzB,IAAI,CAACA,kBAAkB,CAAC,CAAC,CAACC,IAAI,CAACxM,SAAS,CAAC6I,SAAS,CAAC,CAAC,CAAC;IAC3D,IAAI,CAAC4D,eAAe,GAAGR,cAAc;EACzC;EACAS,IAAIA,CAACC,sBAAsB,EAAEzD,MAAM,EAAE;IACjC,MAAM0D,QAAQ,GAAI,IAAI,CAACb,eAAe,IAAI,IAAI3L,YAAY,CAAC,CAAE;IAC7D8I,MAAM,GAAG;MAAE,GAAG0D,QAAQ;MAAE,GAAG1D;IAAO,CAAC;IACnCA,MAAM,CAACjG,EAAE,GAAGiG,MAAM,CAACjG,EAAE,IAAI,cAAcqI,QAAQ,EAAE,EAAE;IACnD,IAAIpC,MAAM,CAACjG,EAAE,IACT,IAAI,CAAC4J,aAAa,CAAC3D,MAAM,CAACjG,EAAE,CAAC,KAC5B,OAAOL,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMpB,KAAK,CAAC,mBAAmB0H,MAAM,CAACjG,EAAE,iDAAiD,CAAC;IAC9F;IACA,MAAM6J,aAAa,GAAG,IAAI,CAACC,iBAAiB,CAAC7D,MAAM,CAAC;IACpD,MAAMD,UAAU,GAAG,IAAI,CAAC4C,QAAQ,CAAChG,MAAM,CAACiH,aAAa,CAAC;IACtD,MAAME,SAAS,GAAG,IAAIhE,SAAS,CAACC,UAAU,EAAEC,MAAM,CAAC;IACnD,MAAM+D,eAAe,GAAG,IAAI,CAACC,gBAAgB,CAACjE,UAAU,EAAE+D,SAAS,EAAE9D,MAAM,CAAC;IAC5E8D,SAAS,CAACnD,iBAAiB,GAAGoD,eAAe;IAC7C,IAAI,CAACE,oBAAoB,CAACR,sBAAsB,EAAEK,SAAS,EAAEC,eAAe,EAAE/D,MAAM,CAAC;IACrF;IACA,IAAI,CAAC,IAAI,CAACsC,WAAW,CAACc,MAAM,EAAE;MAC1B,IAAI,CAACc,4CAA4C,CAAC,CAAC;IACvD;IACA,IAAI,CAAC5B,WAAW,CAACzI,IAAI,CAACiK,SAAS,CAAC;IAChCA,SAAS,CAAC7D,MAAM,CAACpD,SAAS,CAAC,MAAM,IAAI,CAACsH,iBAAiB,CAACL,SAAS,EAAE,IAAI,CAAC,CAAC;IACzE,IAAI,CAACrB,WAAW,CAAC1B,IAAI,CAAC+C,SAAS,CAAC;IAChC,OAAOA,SAAS;EACpB;EACA;AACJ;AACA;EACIM,QAAQA,CAAA,EAAG;IACPC,cAAc,CAAC,IAAI,CAAC/B,WAAW,EAAEgC,MAAM,IAAIA,MAAM,CAAC/D,KAAK,CAAC,CAAC,CAAC;EAC9D;EACA;AACJ;AACA;AACA;EACIoD,aAAaA,CAAC5J,EAAE,EAAE;IACd,OAAO,IAAI,CAACuI,WAAW,CAACiC,IAAI,CAACD,MAAM,IAAIA,MAAM,CAACvK,EAAE,KAAKA,EAAE,CAAC;EAC5D;EACAU,WAAWA,CAAA,EAAG;IACV;IACA;IACA;IACA4J,cAAc,CAAC,IAAI,CAAC7B,uBAAuB,EAAE8B,MAAM,IAAI;MACnD;MACA,IAAIA,MAAM,CAACtE,MAAM,CAAC7H,cAAc,KAAK,KAAK,EAAE;QACxC,IAAI,CAACgM,iBAAiB,CAACG,MAAM,EAAE,KAAK,CAAC;MACzC;IACJ,CAAC,CAAC;IACF;IACA;IACA;IACAD,cAAc,CAAC,IAAI,CAAC7B,uBAAuB,EAAE8B,MAAM,IAAIA,MAAM,CAAC/D,KAAK,CAAC,CAAC,CAAC;IACtE,IAAI,CAACyC,0BAA0B,CAAChC,QAAQ,CAAC,CAAC;IAC1C,IAAI,CAAC0B,uBAAuB,CAAC1B,QAAQ,CAAC,CAAC;IACvC,IAAI,CAACwB,uBAAuB,GAAG,EAAE;EACrC;EACA;AACJ;AACA;AACA;AACA;EACIqB,iBAAiBA,CAAC7D,MAAM,EAAE;IACtB,MAAMwE,KAAK,GAAG,IAAI1P,aAAa,CAAC;MAC5B2P,gBAAgB,EAAEzE,MAAM,CAACyE,gBAAgB,IACrC,IAAI,CAAC9B,QAAQ,CAAC+B,QAAQ,CAAC,CAAC,CAACC,MAAM,CAAC,CAAC,CAACC,kBAAkB,CAAC,CAAC,CAACC,gBAAgB,CAAC,CAAC;MAC7E9B,cAAc,EAAE/C,MAAM,CAAC+C,cAAc,IAAI,IAAI,CAACQ,eAAe,CAAC,CAAC;MAC/DlM,UAAU,EAAE2I,MAAM,CAAC3I,UAAU;MAC7BC,WAAW,EAAE0I,MAAM,CAAC1I,WAAW;MAC/BwN,SAAS,EAAE9E,MAAM,CAAC8E,SAAS;MAC3BC,QAAQ,EAAE/E,MAAM,CAAC+E,QAAQ;MACzBC,SAAS,EAAEhF,MAAM,CAACgF,SAAS;MAC3BC,QAAQ,EAAEjF,MAAM,CAACiF,QAAQ;MACzBC,SAAS,EAAElF,MAAM,CAACkF,SAAS;MAC3BzN,KAAK,EAAEuI,MAAM,CAACvI,KAAK;MACnBC,MAAM,EAAEsI,MAAM,CAACtI,MAAM;MACrByN,mBAAmB,EAAEnF,MAAM,CAAC9H;IAChC,CAAC,CAAC;IACF,IAAI8H,MAAM,CAACzI,aAAa,EAAE;MACtBiN,KAAK,CAACjN,aAAa,GAAGyI,MAAM,CAACzI,aAAa;IAC9C;IACA,OAAOiN,KAAK;EAChB;EACA;AACJ;AACA;AACA;AACA;AACA;EACIR,gBAAgBA,CAACtC,OAAO,EAAEoC,SAAS,EAAE9D,MAAM,EAAE;IACzC,MAAMoF,YAAY,GAAGpF,MAAM,CAACqF,QAAQ,IAAIrF,MAAM,CAACsF,gBAAgB,EAAED,QAAQ;IACzE,MAAME,SAAS,GAAG,CACd;MAAEtD,OAAO,EAAE/K,YAAY;MAAEsO,QAAQ,EAAExF;IAAO,CAAC,EAC3C;MAAEiC,OAAO,EAAEnC,SAAS;MAAE0F,QAAQ,EAAE1B;IAAU,CAAC,EAC3C;MAAE7B,OAAO,EAAElN,UAAU;MAAEyQ,QAAQ,EAAE9D;IAAQ,CAAC,CAC7C;IACD,IAAI+D,aAAa;IACjB,IAAIzF,MAAM,CAAC0F,SAAS,EAAE;MAClB,IAAI,OAAO1F,MAAM,CAAC0F,SAAS,KAAK,UAAU,EAAE;QACxCD,aAAa,GAAGzF,MAAM,CAAC0F,SAAS;MACpC,CAAC,MACI;QACDD,aAAa,GAAGzF,MAAM,CAAC0F,SAAS,CAACjI,IAAI;QACrC8H,SAAS,CAAC1L,IAAI,CAAC,GAAGmG,MAAM,CAAC0F,SAAS,CAACH,SAAS,CAACvF,MAAM,CAAC,CAAC;MACzD;IACJ,CAAC,MACI;MACDyF,aAAa,GAAGlN,kBAAkB;IACtC;IACA,MAAMoN,eAAe,GAAG,IAAItQ,eAAe,CAACoQ,aAAa,EAAEzF,MAAM,CAACsF,gBAAgB,EAAEnP,QAAQ,CAACwG,MAAM,CAAC;MAAEiJ,MAAM,EAAER,YAAY,IAAI,IAAI,CAACxC,SAAS;MAAE2C;IAAU,CAAC,CAAC,EAAEvF,MAAM,CAAC6F,wBAAwB,CAAC;IAC5L,MAAMC,YAAY,GAAGpE,OAAO,CAACqE,MAAM,CAACJ,eAAe,CAAC;IACpD,OAAOG,YAAY,CAACE,QAAQ;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI/B,oBAAoBA,CAACR,sBAAsB,EAAEK,SAAS,EAAEC,eAAe,EAAE/D,MAAM,EAAE;IAC7E,IAAIyD,sBAAsB,YAAYrN,WAAW,EAAE;MAC/C,MAAMiP,QAAQ,GAAG,IAAI,CAACY,eAAe,CAACjG,MAAM,EAAE8D,SAAS,EAAEC,eAAe,EAAEpE,SAAS,CAAC;MACpF,IAAIuG,OAAO,GAAG;QAAEC,SAAS,EAAEnG,MAAM,CAACrI,IAAI;QAAEmM;MAAU,CAAC;MACnD,IAAI9D,MAAM,CAACoG,eAAe,EAAE;QACxBF,OAAO,GAAG;UACN,GAAGA,OAAO;UACV,IAAI,OAAOlG,MAAM,CAACoG,eAAe,KAAK,UAAU,GAC1CpG,MAAM,CAACoG,eAAe,CAAC,CAAC,GACxBpG,MAAM,CAACoG,eAAe;QAChC,CAAC;MACL;MACArC,eAAe,CAACnJ,oBAAoB,CAAC,IAAItF,cAAc,CAACmO,sBAAsB,EAAE,IAAI,EAAEyC,OAAO,EAAEb,QAAQ,CAAC,CAAC;IAC7G,CAAC,MACI;MACD,MAAMA,QAAQ,GAAG,IAAI,CAACY,eAAe,CAACjG,MAAM,EAAE8D,SAAS,EAAEC,eAAe,EAAE,IAAI,CAACnB,SAAS,CAAC;MACzF,MAAMyD,UAAU,GAAGtC,eAAe,CAACpJ,qBAAqB,CAAC,IAAItF,eAAe,CAACoO,sBAAsB,EAAEzD,MAAM,CAACsF,gBAAgB,EAAED,QAAQ,EAAErF,MAAM,CAAC6F,wBAAwB,CAAC,CAAC;MACzK/B,SAAS,CAACwC,YAAY,GAAGD,UAAU;MACnCvC,SAAS,CAAC7C,iBAAiB,GAAGoF,UAAU,CAACL,QAAQ;IACrD;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACjG,MAAM,EAAE8D,SAAS,EAAEC,eAAe,EAAEwC,gBAAgB,EAAE;IAClE,MAAMnB,YAAY,GAAGpF,MAAM,CAACqF,QAAQ,IAAIrF,MAAM,CAACsF,gBAAgB,EAAED,QAAQ;IACzE,MAAME,SAAS,GAAG,CACd;MAAEtD,OAAO,EAAEJ,WAAW;MAAE2D,QAAQ,EAAExF,MAAM,CAACrI;IAAK,CAAC,EAC/C;MAAEsK,OAAO,EAAEnC,SAAS;MAAE0F,QAAQ,EAAE1B;IAAU,CAAC,CAC9C;IACD,IAAI9D,MAAM,CAACuF,SAAS,EAAE;MAClB,IAAI,OAAOvF,MAAM,CAACuF,SAAS,KAAK,UAAU,EAAE;QACxCA,SAAS,CAAC1L,IAAI,CAAC,GAAGmG,MAAM,CAACuF,SAAS,CAACzB,SAAS,EAAE9D,MAAM,EAAE+D,eAAe,CAAC,CAAC;MAC3E,CAAC,MACI;QACDwB,SAAS,CAAC1L,IAAI,CAAC,GAAGmG,MAAM,CAACuF,SAAS,CAAC;MACvC;IACJ;IACA,IAAIvF,MAAM,CAAC8E,SAAS,KACf,CAACM,YAAY,IACV,CAACA,YAAY,CAACoB,GAAG,CAAC3P,cAAc,EAAE,IAAI,EAAE;MAAE4P,QAAQ,EAAE;IAAK,CAAC,CAAC,CAAC,EAAE;MAClElB,SAAS,CAAC1L,IAAI,CAAC;QACXoI,OAAO,EAAEpL,cAAc;QACvB2O,QAAQ,EAAE;UAAEkB,KAAK,EAAE1G,MAAM,CAAC8E,SAAS;UAAE6B,MAAM,EAAE/P,EAAE,CAAC;QAAE;MACtD,CAAC,CAAC;IACN;IACA,OAAOT,QAAQ,CAACwG,MAAM,CAAC;MAAEiJ,MAAM,EAAER,YAAY,IAAImB,gBAAgB;MAAEhB;IAAU,CAAC,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACIpB,iBAAiBA,CAACL,SAAS,EAAE8C,SAAS,EAAE;IACpC,MAAM1M,KAAK,GAAG,IAAI,CAACoI,WAAW,CAACnI,OAAO,CAAC2J,SAAS,CAAC;IACjD,IAAI5J,KAAK,GAAG,CAAC,CAAC,EAAE;MACZ,IAAI,CAACoI,WAAW,CAAClI,MAAM,CAACF,KAAK,EAAE,CAAC,CAAC;MACjC;MACA;MACA,IAAI,CAAC,IAAI,CAACoI,WAAW,CAACc,MAAM,EAAE;QAC1B,IAAI,CAACH,mBAAmB,CAAC4D,OAAO,CAAC,CAACC,aAAa,EAAE9L,OAAO,KAAK;UACzD,IAAI8L,aAAa,EAAE;YACf9L,OAAO,CAAC+L,YAAY,CAAC,aAAa,EAAED,aAAa,CAAC;UACtD,CAAC,MACI;YACD9L,OAAO,CAACO,eAAe,CAAC,aAAa,CAAC;UAC1C;QACJ,CAAC,CAAC;QACF,IAAI,CAAC0H,mBAAmB,CAAC+D,KAAK,CAAC,CAAC;QAChC,IAAIJ,SAAS,EAAE;UACX,IAAI,CAACvD,kBAAkB,CAAC,CAAC,CAACtC,IAAI,CAAC,CAAC;QACpC;MACJ;IACJ;EACJ;EACA;EACAmD,4CAA4CA,CAAA,EAAG;IAC3C,MAAM+C,gBAAgB,GAAG,IAAI,CAACnE,iBAAiB,CAACoE,mBAAmB,CAAC,CAAC;IACrE;IACA,IAAID,gBAAgB,CAACE,aAAa,EAAE;MAChC,MAAMC,QAAQ,GAAGH,gBAAgB,CAACE,aAAa,CAACE,QAAQ;MACxD,KAAK,IAAIC,CAAC,GAAGF,QAAQ,CAAChE,MAAM,GAAG,CAAC,EAAEkE,CAAC,GAAG,CAAC,CAAC,EAAEA,CAAC,EAAE,EAAE;QAC3C,MAAMC,OAAO,GAAGH,QAAQ,CAACE,CAAC,CAAC;QAC3B,IAAIC,OAAO,KAAKN,gBAAgB,IAC5BM,OAAO,CAACC,QAAQ,KAAK,QAAQ,IAC7BD,OAAO,CAACC,QAAQ,KAAK,OAAO,IAC5B,CAACD,OAAO,CAACE,YAAY,CAAC,WAAW,CAAC,EAAE;UACpC,IAAI,CAACxE,mBAAmB,CAACyE,GAAG,CAACH,OAAO,EAAEA,OAAO,CAACI,YAAY,CAAC,aAAa,CAAC,CAAC;UAC1EJ,OAAO,CAACR,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;QAC/C;MACJ;IACJ;EACJ;EACA1D,kBAAkBA,CAAA,EAAG;IACjB,MAAMuC,MAAM,GAAG,IAAI,CAACrD,aAAa;IACjC,OAAOqD,MAAM,GAAGA,MAAM,CAACvC,kBAAkB,CAAC,CAAC,GAAG,IAAI,CAACL,0BAA0B;EACjF;EACA;IAAS,IAAI,CAAClG,IAAI,YAAA8K,eAAA5K,CAAA;MAAA,YAAAA,CAAA,IAAwFqF,MAAM,EApYhB5M,EAAE,CAAAoS,QAAA,CAoYgCjT,IAAI,CAACC,OAAO,GApY9CY,EAAE,CAAAoS,QAAA,CAoYyDpS,EAAE,CAACU,QAAQ,GApYtEV,EAAE,CAAAoS,QAAA,CAoYiF/F,qBAAqB,MApYxGrM,EAAE,CAAAoS,QAAA,CAoYmIxF,MAAM,OApY3I5M,EAAE,CAAAoS,QAAA,CAoYsLjT,IAAI,CAACkT,gBAAgB,GApY7MrS,EAAE,CAAAoS,QAAA,CAoYwNtG,sBAAsB;IAAA,CAA6C;EAAE;EAC/X;IAAS,IAAI,CAACwG,KAAK,kBArY6EtS,EAAE,CAAAuS,kBAAA;MAAAC,KAAA,EAqYY5F,MAAM;MAAAZ,OAAA,EAANY,MAAM,CAAAvF,IAAA;MAAA0E,UAAA,EAAc;IAAM,EAAG;EAAE;AACjJ;AACA;EAAA,QAAA9H,SAAA,oBAAAA,SAAA,KAvYoGjE,EAAE,CAAA2J,iBAAA,CAuYXiD,MAAM,EAAc,CAAC;IACpG5E,IAAI,EAAEpH,UAAU;IAChBgJ,IAAI,EAAE,CAAC;MAAEmC,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAE/D,IAAI,EAAE7I,IAAI,CAACC;EAAQ,CAAC,EAAE;IAAE4I,IAAI,EAAEhI,EAAE,CAACU;EAAS,CAAC,EAAE;IAAEsH,IAAI,EAAEvG,YAAY;IAAE0I,UAAU,EAAE,CAAC;MACjGnC,IAAI,EAAE1H;IACV,CAAC,EAAE;MACC0H,IAAI,EAAEzH,MAAM;MACZqJ,IAAI,EAAE,CAACyC,qBAAqB;IAChC,CAAC;EAAE,CAAC,EAAE;IAAErE,IAAI,EAAE4E,MAAM;IAAEzC,UAAU,EAAE,CAAC;MAC/BnC,IAAI,EAAE1H;IACV,CAAC,EAAE;MACC0H,IAAI,EAAEnH;IACV,CAAC;EAAE,CAAC,EAAE;IAAEmH,IAAI,EAAE7I,IAAI,CAACkT;EAAiB,CAAC,EAAE;IAAErK,IAAI,EAAEkC,SAAS;IAAEC,UAAU,EAAE,CAAC;MACnEnC,IAAI,EAAEzH,MAAM;MACZqJ,IAAI,EAAE,CAACkC,sBAAsB;IACjC,CAAC;EAAE,CAAC,CAAC;AAAA;AACrB;AACA;AACA;AACA;AACA,SAAS8C,cAAcA,CAAC6D,KAAK,EAAE7M,QAAQ,EAAE;EACrC,IAAIiM,CAAC,GAAGY,KAAK,CAAC9E,MAAM;EACpB,OAAOkE,CAAC,EAAE,EAAE;IACRjM,QAAQ,CAAC6M,KAAK,CAACZ,CAAC,CAAC,CAAC;EACtB;AACJ;AAEA,MAAMa,YAAY,CAAC;EACf;IAAS,IAAI,CAACrL,IAAI,YAAAsL,qBAAApL,CAAA;MAAA,YAAAA,CAAA,IAAwFmL,YAAY;IAAA,CAAkD;EAAE;EAC1K;IAAS,IAAI,CAACE,IAAI,kBApa8E5S,EAAE,CAAA6S,gBAAA;MAAA7K,IAAA,EAoaS0K;IAAY,EAIzF;EAAE;EAChC;IAAS,IAAI,CAACI,IAAI,kBAza8E9S,EAAE,CAAA+S,gBAAA;MAAAjD,SAAA,EAyakC,CAAClD,MAAM,CAAC;MAAA5C,OAAA,GAAYzK,aAAa,EAAEO,YAAY,EAAEZ,UAAU;MACvL;MACA;MACAY,YAAY;IAAA,EAAI;EAAE;AAC9B;AACA;EAAA,QAAAmE,SAAA,oBAAAA,SAAA,KA9aoGjE,EAAE,CAAA2J,iBAAA,CA8aX+I,YAAY,EAAc,CAAC;IAC1G1K,IAAI,EAAElH,QAAQ;IACd8I,IAAI,EAAE,CAAC;MACCI,OAAO,EAAE,CAACzK,aAAa,EAAEO,YAAY,EAAEZ,UAAU,EAAE4D,kBAAkB,CAAC;MACtEkQ,OAAO,EAAE;MACL;MACA;MACAlT,YAAY,EACZgD,kBAAkB,CACrB;MACDgN,SAAS,EAAE,CAAClD,MAAM;IACtB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS9J,kBAAkB,EAAEuJ,qBAAqB,EAAED,WAAW,EAAEN,sBAAsB,EAAES,+BAA+B,EAAED,uCAAuC,EAAEM,MAAM,EAAEnL,YAAY,EAAEiR,YAAY,EAAErI,SAAS,EAAEzH,sCAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}