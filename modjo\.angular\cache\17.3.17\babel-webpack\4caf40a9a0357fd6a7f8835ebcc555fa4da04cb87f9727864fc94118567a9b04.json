{"ast": null, "code": "export const appRoutes = [{\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'auth',\n  loadChildren: () => import('./features/auth/auth.module').then(m => m.AuthModule)\n}, {\n  path: 'dashboard',\n  loadChildren: () => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule)\n  // canActivate: [AuthGuard] // Temporarily disabled\n}, {\n  path: 'profile',\n  loadChildren: () => import('./features/profile/profile.module').then(m => m.ProfileModule)\n  // canActivate: [AuthGuard] // Temporarily disabled\n}, {\n  path: 'qr-scanner',\n  loadChildren: () => import('./features/qr-scanner/qr-scanner.module').then(m => m.QrScannerModule)\n  // canActivate: [AuthGuard] // Temporarily disabled\n}, {\n  path: 'rewards',\n  loadChildren: () => import('./features/rewards/rewards.module').then(m => m.RewardsModule)\n  // canActivate: [AuthGuard] // Temporarily disabled\n},\n// {\n//   path: 'validation',\n//   loadChildren: () => import('./features/validation/validation.module').then(m => m.ValidationModule),\n//   canActivate: [ValidatorGuard]\n// },\n// {\n//   path: 'admin',\n//   loadChildren: () => import('./features/admin/admin.module').then(m => m.AdminModule),\n//   canActivate: [AdminGuard],\n//   data: { roles: [UserRole.ADMIN] }\n// },\n{\n  path: 'unauthorized',\n  loadComponent: () => import('./shared/components/unauthorized/unauthorized.component').then(c => c.UnauthorizedComponent)\n}, {\n  path: '**',\n  loadComponent: () => import('./shared/components/not-found/not-found.component').then(c => c.NotFoundComponent)\n}];", "map": {"version": 3, "names": ["appRoutes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "AuthModule", "DashboardModule", "ProfileModule", "QrScannerModule", "RewardsModule", "loadComponent", "c", "UnauthorizedComponent", "NotFoundComponent"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { AuthGuard, AdminGuard, ValidatorGuard } from './core/guards/auth.guard';\nimport { UserRole } from './core/models';\n\nexport const appRoutes: Routes = [\n  {\n    path: '',\n    redirectTo: '/dashboard',\n    pathMatch: 'full'\n  },\n  {\n    path: 'auth',\n    loadChildren: () => import('./features/auth/auth.module').then(m => m.AuthModule)\n  },\n  {\n    path: 'dashboard',\n    loadChildren: () => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule)\n    // canActivate: [AuthGuard] // Temporarily disabled\n  },\n  {\n    path: 'profile',\n    loadChildren: () => import('./features/profile/profile.module').then(m => m.ProfileModule)\n    // canActivate: [AuthGuard] // Temporarily disabled\n  },\n  {\n    path: 'qr-scanner',\n    loadChildren: () => import('./features/qr-scanner/qr-scanner.module').then(m => m.QrScannerModule)\n    // canActivate: [AuthGuard] // Temporarily disabled\n  },\n  {\n    path: 'rewards',\n    loadChildren: () => import('./features/rewards/rewards.module').then(m => m.RewardsModule)\n    // canActivate: [AuthGuard] // Temporarily disabled\n  },\n  // {\n  //   path: 'validation',\n  //   loadChildren: () => import('./features/validation/validation.module').then(m => m.ValidationModule),\n  //   canActivate: [ValidatorGuard]\n  // },\n  // {\n  //   path: 'admin',\n  //   loadChildren: () => import('./features/admin/admin.module').then(m => m.AdminModule),\n  //   canActivate: [AdminGuard],\n  //   data: { roles: [UserRole.ADMIN] }\n  // },\n  {\n    path: 'unauthorized',\n    loadComponent: () => import('./shared/components/unauthorized/unauthorized.component').then(c => c.UnauthorizedComponent)\n  },\n  {\n    path: '**',\n    loadComponent: () => import('./shared/components/not-found/not-found.component').then(c => c.NotFoundComponent)\n  }\n];\n"], "mappings": "AAIA,OAAO,MAAMA,SAAS,GAAW,CAC/B;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU;CACjF,EACD;EACEN,IAAI,EAAE,WAAW;EACjBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,eAAe;EAC/F;CACD,EACD;EACEP,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,aAAa;EACzF;CACD,EACD;EACER,IAAI,EAAE,YAAY;EAClBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,eAAe;EACjG;CACD,EACD;EACET,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,aAAa;EACzF;CACD;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACEV,IAAI,EAAE,cAAc;EACpBW,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,yDAAyD,CAAC,CAACP,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAACC,qBAAqB;CACzH,EACD;EACEb,IAAI,EAAE,IAAI;EACVW,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,mDAAmD,CAAC,CAACP,IAAI,CAACQ,CAAC,IAAIA,CAAC,CAACE,iBAAiB;CAC/G,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}