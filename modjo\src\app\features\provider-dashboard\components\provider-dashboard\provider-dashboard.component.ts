import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../../../core/services/auth.service';
import { User, UserRole } from '../../../../core/models';

@Component({
  selector: 'app-provider-dashboard',
  template: `
    <div class="provider-dashboard-container" *ngIf="user">
      <div class="provider-header">
        <h1>🧑‍🔧 Dashboard Prestataire</h1>
        <p>{{ getGreeting() }}, {{ user.name }}!</p>
        <p class="subtitle">Générez des QR codes et suivez l'engagement</p>
      </div>
      
      <div class="provider-features">
        <mat-card class="feature-card">
          <mat-card-content>
            <mat-icon>qr_code</mat-icon>
            <h3>Générer QR Code</h3>
            <p>Créez des QR codes pour 1, 3 ou 5 points</p>
            <button mat-raised-button color="primary">G<PERSON>érer QR</button>
          </mat-card-content>
        </mat-card>
        
        <mat-card class="feature-card">
          <mat-card-content>
            <mat-icon>analytics</mat-icon>
            <h3>Statistiques d'Usage</h3>
            <p>Voir combien de fois vos QR codes ont été scannés</p>
            <button mat-raised-button color="accent">Voir les Stats</button>
          </mat-card-content>
        </mat-card>
        
        <mat-card class="feature-card">
          <mat-card-content>
            <mat-icon>history</mat-icon>
            <h3>Historique</h3>
            <p>Accédez à votre profil et mini historique</p>
            <button mat-raised-button color="warn">Voir l'Historique</button>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .provider-dashboard-container { padding: 20px; }
    .provider-header { text-align: center; margin-bottom: 40px; }
    .provider-header h1 { color: #2d3748; font-size: 2.5rem; margin-bottom: 16px; }
    .subtitle { color: #718096; font-size: 1.1rem; }
    .provider-features { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; }
    .feature-card { text-align: center; padding: 24px; }
    .feature-card mat-icon { font-size: 3rem; color: #667eea; margin-bottom: 16px; }
    .feature-card h3 { color: #2d3748; margin-bottom: 12px; }
    .feature-card p { color: #718096; margin-bottom: 20px; }
  `]
})
export class ProviderDashboardComponent implements OnInit {
  user: User | null = null;

  constructor(private authService: AuthService) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.user = user;
    });
  }

  getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Bonjour';
    if (hour < 18) return 'Bon après-midi';
    return 'Bonsoir';
  }
}
