import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from '../../auth/auth.service';
import { UserService } from '../user.service';
import { User } from '../../models/user.model';
import { PointHistory } from '../../models/point.model';

@Component({
  selector: 'app-profile',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css']
})
export class ProfileComponent implements OnInit {
  user: User | null = null;
  pointHistory: PointHistory | null = null;
  isLoading: boolean = true;
  error: string = '';

  constructor(
    private authService: AuthService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.loadUserData();
  }

  private async loadUserData(): Promise<void> {
    try {
      const currentUser = this.authService.getCurrentUser();

      if (!currentUser) {
        this.error = 'Utilisateur non connecté';
        this.isLoading = false;
        return;
      }

      this.user = currentUser;

      // Get point history
      this.pointHistory = await this.userService.getUserPointHistory(currentUser.uid).toPromise() || null;

      this.isLoading = false;
    } catch (error) {
      console.error('Error loading user data:', error);
      this.error = 'Erreur lors du chargement des données utilisateur';
      this.isLoading = false;
    }
  }
}
