{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\n// import { AngularFireAuth } from '@angular/fire/compat/auth';\n// import { AngularFirestore } from '@angular/fire/compat/firestore';\nimport { BehaviorSubject } from 'rxjs';\nimport { UserRole } from '../models';\nimport * as i0 from \"@angular/core\";\nexport class AuthService {\n  constructor() {\n    this.currentUserSubject = new BehaviorSubject(null);\n    this.currentUser$ = this.currentUserSubject.asObservable();\n    // Temporarily disabled for initial setup\n    // Create a mock user for testing\n    const mockUser = {\n      uid: 'mock-user-123',\n      email: '<EMAIL>',\n      name: 'Utilisateur Test',\n      city: 'Monastir',\n      role: UserRole.USER,\n      points: 150,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    };\n    this.currentUserSubject.next(mockUser);\n  }\n  register(userData) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      // Mock registration for initial setup\n      const user = {\n        uid: 'mock-user-' + Date.now(),\n        email: userData.email,\n        name: userData.name,\n        phone: userData.phone,\n        city: userData.city,\n        role: userData.role,\n        points: 0,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n        isActive: true,\n        history: []\n      };\n      _this.currentUserSubject.next(user);\n      return Promise.resolve(user);\n    })();\n  }\n  login(email, password) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      // Mock login for initial setup\n      const mockUsers = {\n        '<EMAIL>': {\n          uid: 'user-123',\n          email: '<EMAIL>',\n          name: 'Utilisateur Test',\n          city: 'Monastir',\n          role: UserRole.USER,\n          points: 150,\n          createdAt: new Date(),\n          updatedAt: new Date(),\n          isActive: true,\n          history: []\n        },\n        '<EMAIL>': {\n          uid: 'admin-123',\n          email: '<EMAIL>',\n          name: 'Admin Test',\n          city: 'Sousse',\n          role: UserRole.ADMIN,\n          points: 500,\n          createdAt: new Date(),\n          updatedAt: new Date(),\n          isActive: true,\n          history: []\n        },\n        '<EMAIL>': {\n          uid: 'validator-123',\n          email: '<EMAIL>',\n          name: 'Validateur Test',\n          city: 'Monastir',\n          role: UserRole.VALIDATOR,\n          points: 300,\n          createdAt: new Date(),\n          updatedAt: new Date(),\n          isActive: true,\n          history: []\n        }\n      };\n      const user = mockUsers[email];\n      if (user && password === 'password123') {\n        _this2.currentUserSubject.next(user);\n        return Promise.resolve(user);\n      }\n      throw new Error('Invalid credentials');\n    })();\n  }\n  logout() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.currentUserSubject.next(null);\n      return Promise.resolve();\n    })();\n  }\n  getUserData(uid) {\n    return _asyncToGenerator(function* () {\n      // Mock implementation\n      throw new Error('User data not found');\n    })();\n  }\n  getCurrentUser() {\n    return this.currentUserSubject.value;\n  }\n  isAuthenticated() {\n    return this.currentUserSubject.value !== null;\n  }\n  hasRole(role) {\n    const user = this.getCurrentUser();\n    return user?.role === role;\n  }\n  hasAnyRole(roles) {\n    const user = this.getCurrentUser();\n    return user ? roles.includes(user.role) : false;\n  }\n  updateUserProfile(updates) {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      const user = _this4.getCurrentUser();\n      if (!user) throw new Error('No authenticated user');\n      const updatedUser = {\n        ...user,\n        ...updates,\n        updatedAt: new Date()\n      };\n      _this4.currentUserSubject.next(updatedUser);\n      return Promise.resolve();\n    })();\n  }\n  // Password reset\n  resetPassword(email) {\n    return _asyncToGenerator(function* () {\n      // Implementation would use Firebase Auth sendPasswordResetEmail\n      // For now, just a placeholder\n      console.log('Password reset requested for:', email);\n    })();\n  }\n  // Method for testing - set current user directly\n  setCurrentUser(user) {\n    this.currentUserSubject.next(user);\n  }\n  static {\n    this.ɵfac = function AuthService_Factory(t) {\n      return new (t || AuthService)();\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: AuthService,\n      factory: AuthService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "UserRole", "AuthService", "constructor", "currentUserSubject", "currentUser$", "asObservable", "mockUser", "uid", "email", "name", "city", "role", "USER", "points", "createdAt", "Date", "updatedAt", "isActive", "history", "next", "register", "userData", "_this", "_asyncToGenerator", "user", "now", "phone", "Promise", "resolve", "login", "password", "_this2", "mockUsers", "ADMIN", "VALIDATOR", "Error", "logout", "_this3", "getUserData", "getCurrentUser", "value", "isAuthenticated", "hasRole", "hasAnyRole", "roles", "includes", "updateUserProfile", "updates", "_this4", "updatedUser", "resetPassword", "console", "log", "setCurrentUser", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\services\\auth.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\n// import { AngularFireAuth } from '@angular/fire/compat/auth';\n// import { AngularFirestore } from '@angular/fire/compat/firestore';\nimport { BehaviorSubject, Observable, from, map, switchMap, of } from 'rxjs';\nimport { User, CreateUserRequest, UserRole } from '../models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class AuthService {\n  private currentUserSubject = new BehaviorSubject<User | null>(null);\n  public currentUser$ = this.currentUserSubject.asObservable();\n\n  constructor(\n    // private auth: AngularFireAuth,\n    // private firestore: AngularFirestore\n  ) {\n    // Temporarily disabled for initial setup\n    // Create a mock user for testing\n    const mockUser: User = {\n      uid: 'mock-user-123',\n      email: '<EMAIL>',\n      name: 'Utilisateur Test',\n      city: 'Monastir',\n      role: UserRole.USER,\n      points: 150,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    };\n    this.currentUserSubject.next(mockUser);\n  }\n\n  async register(userData: CreateUserRequest): Promise<User> {\n    // Mock registration for initial setup\n    const user: User = {\n      uid: 'mock-user-' + Date.now(),\n      email: userData.email,\n      name: userData.name,\n      phone: userData.phone,\n      city: userData.city,\n      role: userData.role,\n      points: 0,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    };\n\n    this.currentUserSubject.next(user);\n    return Promise.resolve(user);\n  }\n\n  async login(email: string, password: string): Promise<User> {\n    // Mock login for initial setup\n    const mockUsers: { [key: string]: User } = {\n      '<EMAIL>': {\n        uid: 'user-123',\n        email: '<EMAIL>',\n        name: 'Utilisateur Test',\n        city: 'Monastir',\n        role: UserRole.USER,\n        points: 150,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n        isActive: true,\n        history: []\n      },\n      '<EMAIL>': {\n        uid: 'admin-123',\n        email: '<EMAIL>',\n        name: 'Admin Test',\n        city: 'Sousse',\n        role: UserRole.ADMIN,\n        points: 500,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n        isActive: true,\n        history: []\n      },\n      '<EMAIL>': {\n        uid: 'validator-123',\n        email: '<EMAIL>',\n        name: 'Validateur Test',\n        city: 'Monastir',\n        role: UserRole.VALIDATOR,\n        points: 300,\n        createdAt: new Date(),\n        updatedAt: new Date(),\n        isActive: true,\n        history: []\n      }\n    };\n\n    const user = mockUsers[email];\n    if (user && password === 'password123') {\n      this.currentUserSubject.next(user);\n      return Promise.resolve(user);\n    }\n\n    throw new Error('Invalid credentials');\n  }\n\n  async logout(): Promise<void> {\n    this.currentUserSubject.next(null);\n    return Promise.resolve();\n  }\n\n  private async getUserData(uid: string): Promise<User> {\n    // Mock implementation\n    throw new Error('User data not found');\n  }\n\n  getCurrentUser(): User | null {\n    return this.currentUserSubject.value;\n  }\n\n  isAuthenticated(): boolean {\n    return this.currentUserSubject.value !== null;\n  }\n\n  hasRole(role: UserRole): boolean {\n    const user = this.getCurrentUser();\n    return user?.role === role;\n  }\n\n  hasAnyRole(roles: UserRole[]): boolean {\n    const user = this.getCurrentUser();\n    return user ? roles.includes(user.role) : false;\n  }\n\n  async updateUserProfile(updates: Partial<User>): Promise<void> {\n    const user = this.getCurrentUser();\n    if (!user) throw new Error('No authenticated user');\n\n    const updatedUser = { ...user, ...updates, updatedAt: new Date() };\n    this.currentUserSubject.next(updatedUser);\n    return Promise.resolve();\n  }\n\n  // Password reset\n  async resetPassword(email: string): Promise<void> {\n    // Implementation would use Firebase Auth sendPasswordResetEmail\n    // For now, just a placeholder\n    console.log('Password reset requested for:', email);\n  }\n\n  // Method for testing - set current user directly\n  setCurrentUser(user: User | null): void {\n    this.currentUserSubject.next(user);\n  }\n}\n"], "mappings": ";AACA;AACA;AACA,SAASA,eAAe,QAA8C,MAAM;AAC5E,SAAkCC,QAAQ,QAAQ,WAAW;;AAK7D,OAAM,MAAOC,WAAW;EAItBC,YAAA;IAHQ,KAAAC,kBAAkB,GAAG,IAAIJ,eAAe,CAAc,IAAI,CAAC;IAC5D,KAAAK,YAAY,GAAG,IAAI,CAACD,kBAAkB,CAACE,YAAY,EAAE;IAM1D;IACA;IACA,MAAMC,QAAQ,GAAS;MACrBC,GAAG,EAAE,eAAe;MACpBC,KAAK,EAAE,eAAe;MACtBC,IAAI,EAAE,kBAAkB;MACxBC,IAAI,EAAE,UAAU;MAChBC,IAAI,EAAEX,QAAQ,CAACY,IAAI;MACnBC,MAAM,EAAE,GAAG;MACXC,SAAS,EAAE,IAAIC,IAAI,EAAE;MACrBC,SAAS,EAAE,IAAID,IAAI,EAAE;MACrBE,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE;KACV;IACD,IAAI,CAACf,kBAAkB,CAACgB,IAAI,CAACb,QAAQ,CAAC;EACxC;EAEMc,QAAQA,CAACC,QAA2B;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACxC;MACA,MAAMC,IAAI,GAAS;QACjBjB,GAAG,EAAE,YAAY,GAAGQ,IAAI,CAACU,GAAG,EAAE;QAC9BjB,KAAK,EAAEa,QAAQ,CAACb,KAAK;QACrBC,IAAI,EAAEY,QAAQ,CAACZ,IAAI;QACnBiB,KAAK,EAAEL,QAAQ,CAACK,KAAK;QACrBhB,IAAI,EAAEW,QAAQ,CAACX,IAAI;QACnBC,IAAI,EAAEU,QAAQ,CAACV,IAAI;QACnBE,MAAM,EAAE,CAAC;QACTC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBC,SAAS,EAAE,IAAID,IAAI,EAAE;QACrBE,QAAQ,EAAE,IAAI;QACdC,OAAO,EAAE;OACV;MAEDI,KAAI,CAACnB,kBAAkB,CAACgB,IAAI,CAACK,IAAI,CAAC;MAClC,OAAOG,OAAO,CAACC,OAAO,CAACJ,IAAI,CAAC;IAAC;EAC/B;EAEMK,KAAKA,CAACrB,KAAa,EAAEsB,QAAgB;IAAA,IAAAC,MAAA;IAAA,OAAAR,iBAAA;MACzC;MACA,MAAMS,SAAS,GAA4B;QACzC,eAAe,EAAE;UACfzB,GAAG,EAAE,UAAU;UACfC,KAAK,EAAE,eAAe;UACtBC,IAAI,EAAE,kBAAkB;UACxBC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAEX,QAAQ,CAACY,IAAI;UACnBC,MAAM,EAAE,GAAG;UACXC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,SAAS,EAAE,IAAID,IAAI,EAAE;UACrBE,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE;SACV;QACD,gBAAgB,EAAE;UAChBX,GAAG,EAAE,WAAW;UAChBC,KAAK,EAAE,gBAAgB;UACvBC,IAAI,EAAE,YAAY;UAClBC,IAAI,EAAE,QAAQ;UACdC,IAAI,EAAEX,QAAQ,CAACiC,KAAK;UACpBpB,MAAM,EAAE,GAAG;UACXC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,SAAS,EAAE,IAAID,IAAI,EAAE;UACrBE,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE;SACV;QACD,oBAAoB,EAAE;UACpBX,GAAG,EAAE,eAAe;UACpBC,KAAK,EAAE,oBAAoB;UAC3BC,IAAI,EAAE,iBAAiB;UACvBC,IAAI,EAAE,UAAU;UAChBC,IAAI,EAAEX,QAAQ,CAACkC,SAAS;UACxBrB,MAAM,EAAE,GAAG;UACXC,SAAS,EAAE,IAAIC,IAAI,EAAE;UACrBC,SAAS,EAAE,IAAID,IAAI,EAAE;UACrBE,QAAQ,EAAE,IAAI;UACdC,OAAO,EAAE;;OAEZ;MAED,MAAMM,IAAI,GAAGQ,SAAS,CAACxB,KAAK,CAAC;MAC7B,IAAIgB,IAAI,IAAIM,QAAQ,KAAK,aAAa,EAAE;QACtCC,MAAI,CAAC5B,kBAAkB,CAACgB,IAAI,CAACK,IAAI,CAAC;QAClC,OAAOG,OAAO,CAACC,OAAO,CAACJ,IAAI,CAAC;;MAG9B,MAAM,IAAIW,KAAK,CAAC,qBAAqB,CAAC;IAAC;EACzC;EAEMC,MAAMA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAd,iBAAA;MACVc,MAAI,CAAClC,kBAAkB,CAACgB,IAAI,CAAC,IAAI,CAAC;MAClC,OAAOQ,OAAO,CAACC,OAAO,EAAE;IAAC;EAC3B;EAEcU,WAAWA,CAAC/B,GAAW;IAAA,OAAAgB,iBAAA;MACnC;MACA,MAAM,IAAIY,KAAK,CAAC,qBAAqB,CAAC;IAAC;EACzC;EAEAI,cAAcA,CAAA;IACZ,OAAO,IAAI,CAACpC,kBAAkB,CAACqC,KAAK;EACtC;EAEAC,eAAeA,CAAA;IACb,OAAO,IAAI,CAACtC,kBAAkB,CAACqC,KAAK,KAAK,IAAI;EAC/C;EAEAE,OAAOA,CAAC/B,IAAc;IACpB,MAAMa,IAAI,GAAG,IAAI,CAACe,cAAc,EAAE;IAClC,OAAOf,IAAI,EAAEb,IAAI,KAAKA,IAAI;EAC5B;EAEAgC,UAAUA,CAACC,KAAiB;IAC1B,MAAMpB,IAAI,GAAG,IAAI,CAACe,cAAc,EAAE;IAClC,OAAOf,IAAI,GAAGoB,KAAK,CAACC,QAAQ,CAACrB,IAAI,CAACb,IAAI,CAAC,GAAG,KAAK;EACjD;EAEMmC,iBAAiBA,CAACC,OAAsB;IAAA,IAAAC,MAAA;IAAA,OAAAzB,iBAAA;MAC5C,MAAMC,IAAI,GAAGwB,MAAI,CAACT,cAAc,EAAE;MAClC,IAAI,CAACf,IAAI,EAAE,MAAM,IAAIW,KAAK,CAAC,uBAAuB,CAAC;MAEnD,MAAMc,WAAW,GAAG;QAAE,GAAGzB,IAAI;QAAE,GAAGuB,OAAO;QAAE/B,SAAS,EAAE,IAAID,IAAI;MAAE,CAAE;MAClEiC,MAAI,CAAC7C,kBAAkB,CAACgB,IAAI,CAAC8B,WAAW,CAAC;MACzC,OAAOtB,OAAO,CAACC,OAAO,EAAE;IAAC;EAC3B;EAEA;EACMsB,aAAaA,CAAC1C,KAAa;IAAA,OAAAe,iBAAA;MAC/B;MACA;MACA4B,OAAO,CAACC,GAAG,CAAC,+BAA+B,EAAE5C,KAAK,CAAC;IAAC;EACtD;EAEA;EACA6C,cAAcA,CAAC7B,IAAiB;IAC9B,IAAI,CAACrB,kBAAkB,CAACgB,IAAI,CAACK,IAAI,CAAC;EACpC;;;uBA9IWvB,WAAW;IAAA;EAAA;;;aAAXA,WAAW;MAAAqD,OAAA,EAAXrD,WAAW,CAAAsD,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}