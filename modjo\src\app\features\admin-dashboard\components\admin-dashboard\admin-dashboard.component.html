<div class="admin-dashboard-container" *ngIf="user">
  <!-- Header Section -->
  <div class="admin-header">
    <div class="header-content">
      <div class="admin-welcome">
        <h1>{{ getGreeting() }}, {{ user.name }}</h1>
        <p class="admin-subtitle">🧑‍💼 Tableau de bord administrateur - Supervision complète de Modjo</p>
      </div>
      <div class="admin-badge">
        <mat-icon>admin_panel_settings</mat-icon>
        <span>Administrateur</span>
      </div>
    </div>
  </div>

  <!-- Quick Stats -->
  <div class="quick-stats-section">
    <h2 class="section-title">
      <mat-icon>analytics</mat-icon>
      Statistiques Rapides
    </h2>
    <div class="stats-grid">
      <mat-card *ngFor="let stat of quickStats" class="stat-card" [style.border-left-color]="stat.color">
        <mat-card-content>
          <div class="stat-header">
            <div class="stat-icon" [style.background-color]="stat.color + '20'">
              <mat-icon [style.color]="stat.color">{{ stat.icon }}</mat-icon>
            </div>
            <div class="stat-trend" [class.trend-up]="stat.trendUp" [class.trend-down]="!stat.trendUp">
              <mat-icon>{{ stat.trendUp ? 'trending_up' : 'trending_down' }}</mat-icon>
              <span>{{ stat.trend }}</span>
            </div>
          </div>
          <div class="stat-content">
            <h3 class="stat-value">{{ stat.value }}</h3>
            <p class="stat-title">{{ stat.title }}</p>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Navigation Grid -->
  <div class="navigation-section">
    <h2 class="section-title">
      <mat-icon>apps</mat-icon>
      Gestion & Administration
    </h2>
    <div class="navigation-grid">
      <mat-card *ngFor="let item of navigationItems" 
                class="nav-card" 
                (click)="navigateTo(item.route)">
        <mat-card-content>
          <div class="nav-icon">
            <mat-icon>{{ item.icon }}</mat-icon>
          </div>
          <h3>{{ item.title }}</h3>
          <p>{{ item.description }}</p>
          <button mat-raised-button color="primary" class="nav-button">
            Accéder
            <mat-icon>arrow_forward</mat-icon>
          </button>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- System Overview -->
  <div class="overview-section">
    <div class="overview-grid">
      <!-- Top Validators -->
      <mat-card class="overview-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>verified_user</mat-icon>
            Top Validateurs
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="top-list">
            <div *ngFor="let validator of globalStats?.topValidators; let i = index" 
                 class="top-item">
              <div class="rank">{{ i + 1 }}</div>
              <div class="item-info">
                <h4>{{ validator.organizationName }}</h4>
                <p>{{ validator.totalValidations }} validations</p>
                <div class="metrics">
                  <span class="metric">
                    <mat-icon>schedule</mat-icon>
                    {{ validator.averageResponseTime }}h
                  </span>
                  <span class="metric">
                    <mat-icon>check_circle</mat-icon>
                    {{ validator.approvalRate }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Top Partners -->
      <mat-card class="overview-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>store</mat-icon>
            Top Partenaires
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="top-list">
            <div *ngFor="let partner of globalStats?.topPartners; let i = index" 
                 class="top-item">
              <div class="rank">{{ i + 1 }}</div>
              <div class="item-info">
                <h4>{{ partner.businessName }}</h4>
                <p>{{ partner.totalRedemptions }} échanges</p>
                <div class="metrics">
                  <span class="metric">
                    <mat-icon>stars</mat-icon>
                    {{ partner.totalPointsGenerated }} pts
                  </span>
                  <span class="metric">
                    <mat-icon>sentiment_satisfied</mat-icon>
                    {{ partner.customerSatisfaction }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <!-- Top Providers -->
      <mat-card class="overview-card">
        <mat-card-header>
          <mat-card-title>
            <mat-icon>business</mat-icon>
            Top Prestataires
          </mat-card-title>
        </mat-card-header>
        <mat-card-content>
          <div class="top-list">
            <div *ngFor="let provider of globalStats?.topProviders; let i = index" 
                 class="top-item">
              <div class="rank">{{ i + 1 }}</div>
              <div class="item-info">
                <h4>{{ provider.organizationName }}</h4>
                <p>{{ provider.totalScans }} scans</p>
                <div class="metrics">
                  <span class="metric">
                    <mat-icon>people</mat-icon>
                    {{ provider.uniqueUsers }} users
                  </span>
                  <span class="metric">
                    <mat-icon>trending_up</mat-icon>
                    {{ provider.engagementRate }}%
                  </span>
                </div>
              </div>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Recent Activities -->
  <div class="activities-section">
    <h2 class="section-title">
      <mat-icon>history</mat-icon>
      Activité Récente du Système
    </h2>
    <mat-card class="activities-card">
      <mat-card-content>
        <div class="activities-list">
          <div *ngFor="let activity of recentActivities" class="activity-item">
            <div class="activity-icon" [style.background-color]="activity.color + '20'">
              <mat-icon [style.color]="activity.color">{{ activity.icon }}</mat-icon>
            </div>
            <div class="activity-content">
              <p class="activity-description">{{ activity.description }}</p>
              <span class="activity-time">{{ activity.timestamp | timeAgo }}</span>
            </div>
          </div>
        </div>
        <button mat-stroked-button class="view-all-btn" (click)="navigateTo('/admin/audit')">
          Voir le journal complet
          <mat-icon>arrow_forward</mat-icon>
        </button>
      </mat-card-content>
    </mat-card>
  </div>
</div>
