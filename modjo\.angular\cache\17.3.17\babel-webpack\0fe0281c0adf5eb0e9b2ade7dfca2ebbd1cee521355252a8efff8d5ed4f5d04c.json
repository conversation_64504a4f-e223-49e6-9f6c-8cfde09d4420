{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Observable } from 'rxjs';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/fire/compat/firestore\";\nexport class UserService {\n  constructor(firestore) {\n    this.firestore = firestore;\n  }\n  getUserById(uid) {\n    // Temporarily disabled for initial setup\n    return new Observable(observer => {\n      observer.next(null);\n      observer.complete();\n    });\n  }\n  updateUser(uid, updates) {\n    // Temporarily disabled for initial setup\n    return new Observable(observer => {\n      observer.next();\n      observer.complete();\n    });\n  }\n  getUsersByCity(city) {\n    // Temporarily disabled for initial setup\n    return new Observable(observer => {\n      observer.next([]);\n      observer.complete();\n    });\n  }\n  getTopUsers(limitCount = 10) {\n    // Temporarily disabled for initial setup\n    return new Observable(observer => {\n      observer.next([]);\n      observer.complete();\n    });\n  }\n  getUserTransactions(uid, limitCount = 50) {\n    // Temporarily disabled for initial setup\n    return new Observable(observer => {\n      observer.next([]);\n      observer.complete();\n    });\n  }\n  addPointsToUser(uid, points, type, description, fromUserId) {\n    return _asyncToGenerator(function* () {\n      // Temporarily disabled for initial setup\n      console.log('Mock: Adding points', {\n        uid,\n        points,\n        type,\n        description\n      });\n      return Promise.resolve();\n    })();\n  }\n  transferPoints(fromUid, toUid, points, description) {\n    return _asyncToGenerator(function* () {\n      // Temporarily disabled for initial setup\n      console.log('Mock: Transferring points', {\n        fromUid,\n        toUid,\n        points,\n        description\n      });\n      return Promise.resolve();\n    })();\n  }\n  generateTransactionId() {\n    return Date.now().toString() + Math.random().toString(36).substr(2, 9);\n  }\n  // Get user statistics\n  getUserStats(uid) {\n    // Temporarily disabled for initial setup\n    return new Observable(observer => {\n      observer.next({\n        totalPoints: 0,\n        totalEarned: 0,\n        totalSpent: 0,\n        transactionCount: 0,\n        joinDate: new Date(),\n        lastActivity: new Date()\n      });\n      observer.complete();\n    });\n  }\n  static {\n    this.ɵfac = function UserService_Factory(t) {\n      return new (t || UserService)(i0.ɵɵinject(i1.AngularFirestore));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: UserService,\n      factory: UserService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["Observable", "UserService", "constructor", "firestore", "getUserById", "uid", "observer", "next", "complete", "updateUser", "updates", "getUsersByCity", "city", "getTopUsers", "limitCount", "getUserTransactions", "addPointsToUser", "points", "type", "description", "fromUserId", "_asyncToGenerator", "console", "log", "Promise", "resolve", "transferPoints", "fromUid", "toUid", "generateTransactionId", "Date", "now", "toString", "Math", "random", "substr", "getUserStats", "totalPoints", "totalEarned", "totalSpent", "transactionCount", "joinDate", "lastActivity", "i0", "ɵɵinject", "i1", "AngularFirestore", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\services\\user.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { AngularFirestore } from '@angular/fire/compat/firestore';\nimport { Observable, from, map } from 'rxjs';\nimport { User, PointsTransaction, TransactionType, UpdateUserRequest, PaginatedResponse } from '../models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class UserService {\n  constructor(private firestore: AngularFirestore) {}\n\n  getUserById(uid: string): Observable<User | null> {\n    // Temporarily disabled for initial setup\n    return new Observable(observer => {\n      observer.next(null);\n      observer.complete();\n    });\n  }\n\n  updateUser(uid: string, updates: UpdateUserRequest): Observable<void> {\n    // Temporarily disabled for initial setup\n    return new Observable(observer => {\n      observer.next();\n      observer.complete();\n    });\n  }\n\n  getUsersByCity(city: 'Monastir' | 'Sousse'): Observable<User[]> {\n    // Temporarily disabled for initial setup\n    return new Observable(observer => {\n      observer.next([]);\n      observer.complete();\n    });\n  }\n\n  getTopUsers(limitCount: number = 10): Observable<User[]> {\n    // Temporarily disabled for initial setup\n    return new Observable(observer => {\n      observer.next([]);\n      observer.complete();\n    });\n  }\n\n  getUserTransactions(uid: string, limitCount: number = 50): Observable<PointsTransaction[]> {\n    // Temporarily disabled for initial setup\n    return new Observable(observer => {\n      observer.next([]);\n      observer.complete();\n    });\n  }\n\n  async addPointsToUser(uid: string, points: number, type: TransactionType, description: string, fromUserId?: string): Promise<void> {\n    // Temporarily disabled for initial setup\n    console.log('Mock: Adding points', { uid, points, type, description });\n    return Promise.resolve();\n  }\n\n  async transferPoints(fromUid: string, toUid: string, points: number, description: string): Promise<void> {\n    // Temporarily disabled for initial setup\n    console.log('Mock: Transferring points', { fromUid, toUid, points, description });\n    return Promise.resolve();\n  }\n\n  private generateTransactionId(): string {\n    return Date.now().toString() + Math.random().toString(36).substr(2, 9);\n  }\n\n  // Get user statistics\n  getUserStats(uid: string): Observable<any> {\n    // Temporarily disabled for initial setup\n    return new Observable(observer => {\n      observer.next({\n        totalPoints: 0,\n        totalEarned: 0,\n        totalSpent: 0,\n        transactionCount: 0,\n        joinDate: new Date(),\n        lastActivity: new Date()\n      });\n      observer.complete();\n    });\n  }\n}\n"], "mappings": ";AAEA,SAASA,UAAU,QAAmB,MAAM;;;AAM5C,OAAM,MAAOC,WAAW;EACtBC,YAAoBC,SAA2B;IAA3B,KAAAA,SAAS,GAATA,SAAS;EAAqB;EAElDC,WAAWA,CAACC,GAAW;IACrB;IACA,OAAO,IAAIL,UAAU,CAACM,QAAQ,IAAG;MAC/BA,QAAQ,CAACC,IAAI,CAAC,IAAI,CAAC;MACnBD,QAAQ,CAACE,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAC,UAAUA,CAACJ,GAAW,EAAEK,OAA0B;IAChD;IACA,OAAO,IAAIV,UAAU,CAACM,QAAQ,IAAG;MAC/BA,QAAQ,CAACC,IAAI,EAAE;MACfD,QAAQ,CAACE,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAG,cAAcA,CAACC,IAA2B;IACxC;IACA,OAAO,IAAIZ,UAAU,CAACM,QAAQ,IAAG;MAC/BA,QAAQ,CAACC,IAAI,CAAC,EAAE,CAAC;MACjBD,QAAQ,CAACE,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAK,WAAWA,CAACC,UAAA,GAAqB,EAAE;IACjC;IACA,OAAO,IAAId,UAAU,CAACM,QAAQ,IAAG;MAC/BA,QAAQ,CAACC,IAAI,CAAC,EAAE,CAAC;MACjBD,QAAQ,CAACE,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEAO,mBAAmBA,CAACV,GAAW,EAAES,UAAA,GAAqB,EAAE;IACtD;IACA,OAAO,IAAId,UAAU,CAACM,QAAQ,IAAG;MAC/BA,QAAQ,CAACC,IAAI,CAAC,EAAE,CAAC;MACjBD,QAAQ,CAACE,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;EAEMQ,eAAeA,CAACX,GAAW,EAAEY,MAAc,EAAEC,IAAqB,EAAEC,WAAmB,EAAEC,UAAmB;IAAA,OAAAC,iBAAA;MAChH;MACAC,OAAO,CAACC,GAAG,CAAC,qBAAqB,EAAE;QAAElB,GAAG;QAAEY,MAAM;QAAEC,IAAI;QAAEC;MAAW,CAAE,CAAC;MACtE,OAAOK,OAAO,CAACC,OAAO,EAAE;IAAC;EAC3B;EAEMC,cAAcA,CAACC,OAAe,EAAEC,KAAa,EAAEX,MAAc,EAAEE,WAAmB;IAAA,OAAAE,iBAAA;MACtF;MACAC,OAAO,CAACC,GAAG,CAAC,2BAA2B,EAAE;QAAEI,OAAO;QAAEC,KAAK;QAAEX,MAAM;QAAEE;MAAW,CAAE,CAAC;MACjF,OAAOK,OAAO,CAACC,OAAO,EAAE;IAAC;EAC3B;EAEQI,qBAAqBA,CAAA;IAC3B,OAAOC,IAAI,CAACC,GAAG,EAAE,CAACC,QAAQ,EAAE,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EACxE;EAEA;EACAC,YAAYA,CAAC/B,GAAW;IACtB;IACA,OAAO,IAAIL,UAAU,CAACM,QAAQ,IAAG;MAC/BA,QAAQ,CAACC,IAAI,CAAC;QACZ8B,WAAW,EAAE,CAAC;QACdC,WAAW,EAAE,CAAC;QACdC,UAAU,EAAE,CAAC;QACbC,gBAAgB,EAAE,CAAC;QACnBC,QAAQ,EAAE,IAAIX,IAAI,EAAE;QACpBY,YAAY,EAAE,IAAIZ,IAAI;OACvB,CAAC;MACFxB,QAAQ,CAACE,QAAQ,EAAE;IACrB,CAAC,CAAC;EACJ;;;uBAzEWP,WAAW,EAAA0C,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,gBAAA;IAAA;EAAA;;;aAAX7C,WAAW;MAAA8C,OAAA,EAAX9C,WAAW,CAAA+C,IAAA;MAAAC,UAAA,EAFV;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}