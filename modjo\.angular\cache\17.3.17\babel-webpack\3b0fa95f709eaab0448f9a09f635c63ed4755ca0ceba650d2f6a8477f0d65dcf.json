{"ast": null, "code": "export const environment = {\n  production: false,\n  firebase: {\n    apiKey: \"your-api-key\",\n    authDomain: \"modjo-app.firebaseapp.com\",\n    projectId: \"modjo-app\",\n    storageBucket: \"modjo-app.appspot.com\",\n    messagingSenderId: \"123456789\",\n    appId: \"1:123456789:web:abcdef123456\"\n  },\n  app: {\n    name: 'Modjo',\n    version: '1.0.0',\n    environment: 'development'\n  },\n  features: {\n    pushNotifications: true,\n    geolocation: true,\n    darkMode: true,\n    multiLanguage: true\n  }\n};", "map": {"version": 3, "names": ["environment", "production", "firebase", "<PERSON><PERSON><PERSON><PERSON>", "authDomain", "projectId", "storageBucket", "messagingSenderId", "appId", "app", "name", "version", "features", "pushNotifications", "geolocation", "darkMode", "multiLanguage"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\environments\\environment.ts"], "sourcesContent": ["export const environment = {\n  production: false,\n  firebase: {\n    apiKey: \"your-api-key\",\n    authDomain: \"modjo-app.firebaseapp.com\",\n    projectId: \"modjo-app\",\n    storageBucket: \"modjo-app.appspot.com\",\n    messagingSenderId: \"123456789\",\n    appId: \"1:123456789:web:abcdef123456\"\n  },\n  app: {\n    name: 'Modjo',\n    version: '1.0.0',\n    environment: 'development' as const\n  },\n  features: {\n    pushNotifications: true,\n    geolocation: true,\n    darkMode: true,\n    multiLanguage: true\n  }\n};\n"], "mappings": "AAAA,OAAO,MAAMA,WAAW,GAAG;EACzBC,UAAU,EAAE,KAAK;EACjBC,QAAQ,EAAE;IACRC,MAAM,EAAE,cAAc;IACtBC,UAAU,EAAE,2BAA2B;IACvCC,SAAS,EAAE,WAAW;IACtBC,aAAa,EAAE,uBAAuB;IACtCC,iBAAiB,EAAE,WAAW;IAC9BC,KAAK,EAAE;GACR;EACDC,GAAG,EAAE;IACHC,IAAI,EAAE,OAAO;IACbC,OAAO,EAAE,OAAO;IAChBX,WAAW,EAAE;GACd;EACDY,QAAQ,EAAE;IACRC,iBAAiB,EAAE,IAAI;IACvBC,WAAW,EAAE,IAAI;IACjBC,QAAQ,EAAE,IAAI;IACdC,aAAa,EAAE;;CAElB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}