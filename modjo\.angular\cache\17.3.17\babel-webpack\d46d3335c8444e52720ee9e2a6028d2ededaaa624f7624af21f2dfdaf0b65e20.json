{"ast": null, "code": "import { mergeAll } from './mergeAll';\nexport function concatAll() {\n  return mergeAll(1);\n}", "map": {"version": 3, "names": ["mergeAll", "concatAll"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/rxjs/dist/esm/internal/operators/concatAll.js"], "sourcesContent": ["import { mergeAll } from './mergeAll';\nexport function concatAll() {\n    return mergeAll(1);\n}\n"], "mappings": "AAAA,SAASA,QAAQ,QAAQ,YAAY;AACrC,OAAO,SAASC,SAASA,CAAA,EAAG;EACxB,OAAOD,QAAQ,CAAC,CAAC,CAAC;AACtB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}