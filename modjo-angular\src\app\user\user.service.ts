import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { User, UserProfile } from '../models/user.model';
import { PointHistory, PointTransaction, PointType } from '../models/point.model';

// These imports would normally come from Firebase, but we're mocking them for now
// import { Firestore, collection, doc, getDoc, getDocs, query, where, orderBy, limit, updateDoc, addDoc } from '@angular/fire/firestore';

@Injectable({
  providedIn: 'root'
})
export class UserService {
  // Mock data for development
  private mockUsers: User[] = [
    {
      uid: '1',
      email: '<EMAIL>',
      displayName: 'Test User',
      firstName: 'Test',
      lastName: 'User',
      city: 'Monastir',
      role: 'user' as any,
      points: 50,
      createdAt: new Date(),
      updatedAt: new Date()
    },
    {
      uid: '2',
      email: '<EMAIL>',
      displayName: 'Test Provider',
      firstName: 'Test',
      lastName: 'Provider',
      city: 'Sousse',
      role: 'provider' as any,
      activity: 'Coiffeur',
      points: 100,
      createdAt: new Date(),
      updatedAt: new Date()
    }
  ];

  private mockPointHistory: PointTransaction[] = [
    {
      id: '1',
      userId: '1',
      providerId: '2',
      type: PointType.SCAN,
      amount: 5,
      description: 'Coupe de cheveux',
      createdAt: new Date(Date.now() - 86400000) // 1 day ago
    },
    {
      id: '2',
      userId: '1',
      validatorId: '3',
      type: PointType.VALIDATION,
      amount: 10,
      description: 'Devoir complété',
      createdAt: new Date(Date.now() - 172800000) // 2 days ago
    },
    {
      id: '3',
      userId: '1',
      type: PointType.REWARD_REDEMPTION,
      amount: -15,
      description: 'Boisson gratuite chez Café X',
      createdAt: new Date(Date.now() - 259200000) // 3 days ago
    }
  ];

  constructor(
    // private firestore: Firestore
  ) {}

  // Get user by ID
  getUserById(uid: string): Observable<User | null> {
    // Mock implementation
    const user = this.mockUsers.find(u => u.uid === uid);
    return of(user || null);

    // Firebase implementation
    // const userRef = doc(this.firestore, `users/${uid}`);
    // return from(getDoc(userRef)).pipe(
    //   map(docSnap => {
    //     if (docSnap.exists()) {
    //       return { uid, ...docSnap.data() } as User;
    //     } else {
    //       return null;
    //     }
    //   })
    // );
  }

  // Get user by KnowMe ID
  getUserByKnowMeId(knowMeId: string): Observable<UserProfile | null> {
    // Mock implementation
    // For demo purposes, we'll just return the first provider
    const provider = this.mockUsers.find(u => u.role === 'provider');
    if (provider) {
      return of({
        ...provider,
        knowMeId: knowMeId,
        phoneNumber: '+216 12 345 678'
      });
    }
    return of(null);

    // Firebase implementation
    // const usersRef = collection(this.firestore, 'users');
    // const q = query(usersRef, where('knowMeId', '==', knowMeId), limit(1));
    // return from(getDocs(q)).pipe(
    //   map(querySnapshot => {
    //     if (!querySnapshot.empty) {
    //       const doc = querySnapshot.docs[0];
    //       return { uid: doc.id, ...doc.data() } as UserProfile;
    //     } else {
    //       return null;
    //     }
    //   })
    // );
  }

  // Get user point history
  getUserPointHistory(userId: string): Observable<PointHistory> {
    // Mock implementation
    const transactions = this.mockPointHistory.filter(t => t.userId === userId);
    const totalPoints = transactions.reduce((sum, t) => sum + t.amount, 0);
    
    return of({
      userId,
      transactions,
      totalPoints
    });

    // Firebase implementation
    // const transactionsRef = collection(this.firestore, 'pointTransactions');
    // const q = query(
    //   transactionsRef,
    //   where('userId', '==', userId),
    //   orderBy('createdAt', 'desc')
    // );
    // 
    // return from(getDocs(q)).pipe(
    //   map(querySnapshot => {
    //     const transactions = querySnapshot.docs.map(doc => ({
    //       id: doc.id,
    //       ...doc.data()
    //     } as PointTransaction));
    //     
    //     const totalPoints = transactions.reduce((sum, t) => sum + t.amount, 0);
    //     
    //     return {
    //       userId,
    //       transactions,
    //       totalPoints
    //     };
    //   })
    // );
  }

  // Add points to a user
  async addPoints(userId: string, providerId: string | undefined, amount: number, description: string, type: PointType): Promise<void> {
    // Mock implementation
    const user = this.mockUsers.find(u => u.uid === userId);
    if (user) {
      user.points += amount;
      user.updatedAt = new Date();
      
      this.mockPointHistory.push({
        id: Math.random().toString(36).substring(2, 15),
        userId,
        providerId,
        type,
        amount,
        description,
        createdAt: new Date()
      });
    }

    // Firebase implementation
    // const userRef = doc(this.firestore, `users/${userId}`);
    // const userDoc = await getDoc(userRef);
    // 
    // if (userDoc.exists()) {
    //   const userData = userDoc.data() as User;
    //   const newPoints = userData.points + amount;
    //   
    //   // Update user points
    //   await updateDoc(userRef, { 
    //     points: newPoints,
    //     updatedAt: new Date()
    //   });
    //   
    //   // Add transaction record
    //   const transactionData: Omit<PointTransaction, 'id'> = {
    //     userId,
    //     providerId,
    //     type,
    //     amount,
    //     description,
    //     createdAt: new Date()
    //   };
    //   
    //   await addDoc(collection(this.firestore, 'pointTransactions'), transactionData);
    // }
  }

  // Update user profile
  async updateUserProfile(uid: string, data: Partial<UserProfile>): Promise<void> {
    // Mock implementation
    const userIndex = this.mockUsers.findIndex(u => u.uid === uid);
    if (userIndex !== -1) {
      this.mockUsers[userIndex] = {
        ...this.mockUsers[userIndex],
        ...data,
        updatedAt: new Date()
      };
    }

    // Firebase implementation
    // const userRef = doc(this.firestore, `users/${uid}`);
    // await updateDoc(userRef, { 
    //   ...data,
    //   updatedAt: new Date()
    // });
  }
}
