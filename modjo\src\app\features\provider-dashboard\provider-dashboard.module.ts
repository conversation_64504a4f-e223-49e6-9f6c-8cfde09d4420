import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { SharedModule } from '../../shared/shared.module';
import { ProviderDashboardComponent } from './components/provider-dashboard/provider-dashboard.component';

const routes = [
  { path: '', component: ProviderDashboardComponent }
];

@NgModule({
  declarations: [
    ProviderDashboardComponent
  ],
  imports: [
    SharedModule,
    RouterModule.forChild(routes)
  ]
})
export class ProviderDashboardModule { }
