/* Admin Dashboard Container */
.admin-dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* Header Section */
.admin-header {
  margin-bottom: 40px;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  color: white;
  box-shadow: 0 20px 40px rgba(103, 126, 234, 0.3);
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.admin-welcome h1 {
  margin: 0 0 8px 0;
  font-size: 2.5rem;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
}

.admin-subtitle {
  margin: 0;
  font-size: 1.1rem;
  opacity: 0.9;
}

.admin-badge {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.2);
  padding: 16px 24px;
  border-radius: 20px;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
}

.admin-badge mat-icon {
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
}

.admin-badge span {
  font-weight: 600;
  font-size: 1.1rem;
}

/* Section Titles */
.section-title {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0 0 24px 0;
  color: #2d3748;
  font-size: 1.8rem;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
}

/* Quick Stats */
.quick-stats-section {
  margin-bottom: 48px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
}

.stat-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  border-left: 4px solid;
  transition: all 0.3s ease;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.stat-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.stat-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.stat-icon mat-icon {
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
}

.stat-trend {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.9rem;
  font-weight: 600;
}

.trend-up {
  color: #4CAF50;
}

.trend-down {
  color: #F44336;
}

.stat-value {
  margin: 0 0 4px 0;
  font-size: 2.2rem;
  font-weight: 700;
  color: #2d3748;
}

.stat-title {
  margin: 0;
  color: #718096;
  font-size: 1rem;
  font-weight: 500;
}

/* Navigation Section */
.navigation-section {
  margin-bottom: 48px;
}

.navigation-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

.nav-card {
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.nav-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.nav-card mat-card-content {
  text-align: center;
  padding: 32px 24px;
}

.nav-icon {
  width: 80px;
  height: 80px;
  margin: 0 auto 20px;
  background: linear-gradient(135deg, rgba(103, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.nav-icon mat-icon {
  font-size: 2.5rem;
  width: 2.5rem;
  height: 2.5rem;
  color: #667eea;
}

.nav-card h3 {
  margin: 0 0 12px 0;
  color: #2d3748;
  font-size: 1.3rem;
  font-weight: 600;
}

.nav-card p {
  margin: 0 0 24px 0;
  color: #718096;
  line-height: 1.5;
}

.nav-button {
  width: 100%;
  height: 44px;
  font-weight: 600;
}

/* Overview Section */
.overview-section {
  margin-bottom: 48px;
}

.overview-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.overview-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.overview-card mat-card-title {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #2d3748;
  font-weight: 600;
}

.top-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.top-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f7fafc;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.top-item:hover {
  background: #edf2f7;
  transform: translateX(4px);
}

.rank {
  width: 32px;
  height: 32px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.9rem;
}

.item-info {
  flex: 1;
}

.item-info h4 {
  margin: 0 0 4px 0;
  color: #2d3748;
  font-size: 1rem;
  font-weight: 600;
}

.item-info p {
  margin: 0 0 8px 0;
  color: #718096;
  font-size: 0.9rem;
}

.metrics {
  display: flex;
  gap: 16px;
}

.metric {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: #4a5568;
}

.metric mat-icon {
  font-size: 1rem !important;
  width: 1rem !important;
  height: 1rem !important;
  color: #667eea;
}

/* Activities Section */
.activities-section {
  margin-bottom: 48px;
}

.activities-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.activities-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-bottom: 24px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 16px;
  background: #f7fafc;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.activity-item:hover {
  background: #edf2f7;
  transform: translateX(4px);
}

.activity-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.activity-icon mat-icon {
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
}

.activity-content {
  flex: 1;
}

.activity-description {
  margin: 0 0 4px 0;
  color: #2d3748;
  font-weight: 500;
}

.activity-time {
  color: #718096;
  font-size: 0.8rem;
}

.view-all-btn {
  width: 100%;
  height: 44px;
  font-weight: 600;
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .admin-dashboard-container {
    padding: 16px;
  }
  
  .header-content {
    flex-direction: column;
    gap: 20px;
    text-align: center;
  }
  
  .admin-welcome h1 {
    font-size: 2rem;
  }
  
  .stats-grid,
  .navigation-grid,
  .overview-grid {
    grid-template-columns: 1fr;
  }
}
