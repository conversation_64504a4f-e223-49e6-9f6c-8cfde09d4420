.scanner-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 20px;
  max-width: 600px;
  margin: 0 auto;
}

h2 {
  margin-bottom: 20px;
  color: #333;
}

.scanner-instructions {
  margin-bottom: 20px;
  text-align: center;
  color: #666;
}

.scanner-view {
  width: 100%;
  margin-bottom: 20px;
}

#reader {
  width: 100%;
  min-height: 300px;
  background-color: #f5f5f5;
  border-radius: 8px;
  overflow: hidden;
  position: relative;
}

.scan-error {
  margin-top: 20px;
  padding: 15px;
  background-color: #ffebee;
  color: #d32f2f;
  border-radius: 8px;
  text-align: center;
}

.scan-error button {
  margin-top: 10px;
  padding: 8px 16px;
  background-color: #d32f2f;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.scan-result {
  margin-top: 20px;
  padding: 15px;
  background-color: #e8f5e9;
  color: #2e7d32;
  border-radius: 8px;
  text-align: center;
}

.scanner-footer {
  margin-top: 20px;
  text-align: center;
  color: #666;
  font-size: 14px;
}

/* Demo styles */
.demo-container {
  padding: 20px;
  background-color: #f0f0f0;
  border-radius: 8px;
  margin-top: 20px;
}

.demo-container h3 {
  margin-top: 0;
  margin-bottom: 15px;
  color: #333;
  font-size: 16px;
}

.demo-container button {
  margin-right: 10px;
  margin-bottom: 10px;
  padding: 10px 15px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.demo-container button:hover {
  background-color: #3a7bc8;
}
