.profile-container {
  max-width: 1000px;
  margin: 0 auto;
  padding: 16px;
}

.profile-header {
  display: flex;
  align-items: center;
  gap: 24px;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
}

.profile-avatar {
  flex-shrink: 0;
}

.avatar-icon {
  font-size: 4rem;
  width: 4rem;
  height: 4rem;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  padding: 8px;
}

.profile-info {
  flex: 1;
}

.profile-info h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 600;
}

.user-email {
  margin: 0 0 16px 0;
  opacity: 0.9;
  font-size: 1rem;
}

.user-badges {
  display: flex;
  gap: 8px;
}

.profile-actions {
  flex-shrink: 0;
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 24px;
}

.action-btn {
  height: 48px;
  border-radius: 12px;
  font-weight: 600;
  padding: 0 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 140px;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.profile-tabs {
  background: white;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.tab-content {
  padding: 24px;
}

.stats-section {
  margin-bottom: 32px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 2.5rem;
  width: 2.5rem;
  height: 2.5rem;
}

.stat-info h3 {
  margin: 0 0 4px 0;
  font-size: 2rem;
  font-weight: 600;
  color: #333;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.details-section {
  margin-bottom: 32px;
}

.details-card,
.activity-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.details-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 12px;
}

.detail-item mat-icon {
  color: #666;
  font-size: 1.2rem;
  width: 1.2rem;
  height: 1.2rem;
}

.detail-label {
  font-weight: 500;
  color: #333;
  min-width: 120px;
}

.detail-value {
  color: #666;
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  flex-shrink: 0;
}

.activity-icon mat-icon {
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
}

.activity-earned mat-icon { color: #4caf50; }
.activity-spent mat-icon { color: #f44336; }
.activity-transferred mat-icon { color: #2196f3; }
.activity-validated mat-icon { color: #ff9800; }
.activity-bonus mat-icon { color: #9c27b0; }

.activity-info {
  flex: 1;
}

.activity-description {
  margin: 0 0 4px 0;
  color: #333;
  font-weight: 500;
}

.activity-date {
  margin: 0;
  color: #666;
  font-size: 0.8rem;
}

.activity-points {
  font-weight: 600;
  font-size: 1rem;
}

.activity-points.positive {
  color: #4caf50;
}

.activity-points.negative {
  color: #f44336;
}

.no-activity {
  text-align: center;
  padding: 32px;
}

.no-activity-icon {
  font-size: 3rem;
  color: #ccc;
  margin-bottom: 16px;
}

.no-activity h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.no-activity p {
  margin: 0 0 24px 0;
  color: #666;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.loading-content {
  text-align: center;
  padding: 32px;
}

.loading-icon {
  font-size: 3rem;
  color: #ccc;
  margin-bottom: 16px;
}

.loading-content h3 {
  margin: 0;
  color: #333;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .profile-header {
    flex-direction: column;
    text-align: center;
    gap: 16px;
  }
  
  .profile-info h1 {
    font-size: 1.5rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .stat-content {
    flex-direction: column;
    text-align: center;
    gap: 8px;
  }
  
  .detail-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .detail-label {
    min-width: auto;
  }
  
  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .activity-points {
    align-self: flex-end;
  }
}
