{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { dashboardRoutes } from './dashboard.routes';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class DashboardModule {\n  static {\n    this.ɵfac = function DashboardModule_Factory(t) {\n      return new (t || DashboardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DashboardModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, RouterModule.forChild(dashboardRoutes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DashboardModule, {\n    declarations: [DashboardComponent],\n    imports: [SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SharedModule", "DashboardComponent", "dashboardRoutes", "DashboardModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\nimport { SharedModule } from '../../shared/shared.module';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { dashboardRoutes } from './dashboard.routes';\n\n@NgModule({\n  declarations: [\n    DashboardComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(dashboardRoutes)\n  ]\n})\nexport class DashboardModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,eAAe,QAAQ,oBAAoB;;;AAWpD,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAJxBH,YAAY,EACZD,YAAY,CAACK,QAAQ,CAACF,eAAe,CAAC;IAAA;EAAA;;;2EAG7BC,eAAe;IAAAE,YAAA,GAPxBJ,kBAAkB;IAAAK,OAAA,GAGlBN,YAAY,EAAAO,EAAA,CAAAR,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}