import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router, ActivatedRoute } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../../../../core/services/auth.service';

@Component({
  selector: 'app-login',
  templateUrl: './login.component.html',
  styleUrls: ['./login.component.css']
})
export class LoginComponent implements OnInit {
  loginForm: FormGroup;
  isLoading = false;
  hidePassword = true;
  returnUrl = '/dashboard';

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private route: ActivatedRoute,
    private snackBar: MatSnackBar
  ) {
    this.loginForm = this.fb.group({
      email: ['', [Validators.required, Validators.email]],
      password: ['', [Validators.required, Validators.minLength(6)]]
    });
  }

  ngOnInit(): void {
    // Get return URL from route parameters or default to dashboard
    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';
    
    // Redirect if already authenticated
    if (this.authService.isAuthenticated()) {
      this.router.navigate([this.returnUrl]);
    }
  }

  async onSubmit(): Promise<void> {
    if (this.loginForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading = true;
    const { email, password } = this.loginForm.value;

    try {
      await this.authService.login(email, password);
      this.snackBar.open('Connexion réussie!', 'Fermer', { duration: 3000 });
      this.router.navigate([this.returnUrl]);
    } catch (error: any) {
      console.error('Login error:', error);
      this.snackBar.open(
        this.getErrorMessage(error), 
        'Fermer', 
        { duration: 5000 }
      );
    } finally {
      this.isLoading = false;
    }
  }

  private getErrorMessage(error: any): string {
    if (error.code) {
      switch (error.code) {
        case 'auth/user-not-found':
          return 'Aucun utilisateur trouvé avec cette adresse email.';
        case 'auth/wrong-password':
          return 'Mot de passe incorrect.';
        case 'auth/invalid-email':
          return 'Adresse email invalide.';
        case 'auth/user-disabled':
          return 'Ce compte a été désactivé.';
        case 'auth/too-many-requests':
          return 'Trop de tentatives. Veuillez réessayer plus tard.';
        default:
          return 'Erreur de connexion. Veuillez réessayer.';
      }
    }
    return 'Erreur de connexion. Veuillez réessayer.';
  }

  private markFormGroupTouched(): void {
    Object.keys(this.loginForm.controls).forEach(key => {
      const control = this.loginForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string {
    const field = this.loginForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return `${fieldName === 'email' ? 'Email' : 'Mot de passe'} requis`;
      }
      if (field.errors['email']) {
        return 'Format email invalide';
      }
      if (field.errors['minlength']) {
        return 'Mot de passe trop court (min. 6 caractères)';
      }
    }
    return '';
  }

  // Demo login methods
  async loginAsUser(): Promise<void> {
    this.loginForm.patchValue({
      email: '<EMAIL>',
      password: 'password123'
    });
    await this.onSubmit();
  }

  async loginAsValidator(): Promise<void> {
    this.loginForm.patchValue({
      email: '<EMAIL>',
      password: 'password123'
    });
    await this.onSubmit();
  }

  async loginAsAdmin(): Promise<void> {
    this.loginForm.patchValue({
      email: '<EMAIL>',
      password: 'password123'
    });
    await this.onSubmit();
  }
}
