<!DOCTYPE html>
<html lang="fr">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>AccrédiQR - Système d'accréditation par QR code</title>
  <style>
    * {
      box-sizing: border-box;
      margin: 0;
      padding: 0;
    }
    
    body {
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      line-height: 1.6;
      color: #333;
      background-color: #f8f9fa;
    }
    
    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }
    
    header {
      background-color: #ffffff;
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 15px 0;
    }
    
    .header-container {
      display: flex;
      align-items: center;
      justify-content: space-between;
      padding: 0 20px;
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .logo a {
      font-size: 24px;
      font-weight: 700;
      color: #4a90e2;
      text-decoration: none;
    }
    
    .nav-list {
      display: flex;
      list-style: none;
    }
    
    .nav-list li {
      margin-left: 20px;
    }
    
    .nav-list a {
      color: #333;
      text-decoration: none;
      font-size: 16px;
      transition: color 0.3s;
    }
    
    .nav-list a:hover {
      color: #4a90e2;
    }
    
    .hero-section {
      text-align: center;
      padding: 60px 20px;
      background-color: #f8f9fa;
      border-radius: 8px;
      margin: 40px 0;
    }
    
    h1 {
      font-size: 2.5rem;
      margin-bottom: 20px;
      color: #333;
    }
    
    .subtitle {
      font-size: 1.2rem;
      color: #666;
      margin-bottom: 30px;
    }
    
    .cta-buttons {
      display: flex;
      justify-content: center;
      gap: 20px;
    }
    
    .btn {
      display: inline-block;
      padding: 12px 24px;
      border-radius: 30px;
      text-decoration: none;
      font-weight: 500;
      transition: all 0.3s ease;
    }
    
    .btn-primary {
      background-color: #4a90e2;
      color: white;
    }
    
    .btn-primary:hover {
      background-color: #3a7bc8;
    }
    
    .features-section {
      margin-bottom: 60px;
    }
    
    h2 {
      text-align: center;
      margin-bottom: 40px;
      color: #333;
      font-size: 2rem;
    }
    
    .features-grid {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      gap: 30px;
    }
    
    .feature-card {
      background-color: white;
      border-radius: 8px;
      padding: 30px;
      text-align: center;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
      transition: transform 0.3s ease;
    }
    
    .feature-card:hover {
      transform: translateY(-5px);
    }
    
    .feature-icon {
      font-size: 3rem;
      margin-bottom: 20px;
    }
    
    h3 {
      margin-bottom: 15px;
      color: #333;
    }
    
    .partners-section {
      background-color: #f8f9fa;
      padding: 60px 20px;
      border-radius: 8px;
      text-align: center;
      margin-bottom: 40px;
    }
    
    .partners-list {
      display: flex;
      flex-wrap: wrap;
      justify-content: center;
      gap: 20px;
      margin-top: 30px;
    }
    
    .partner {
      background-color: white;
      padding: 15px 25px;
      border-radius: 30px;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
    }
    
    footer {
      background-color: #333;
      color: white;
      padding: 40px 0;
      text-align: center;
    }
    
    .footer-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 0 20px;
    }
    
    .footer-links {
      margin-top: 20px;
    }
    
    .footer-links a {
      color: #ddd;
      margin: 0 10px;
      text-decoration: none;
    }
    
    .footer-links a:hover {
      color: white;
    }
    
    @media (max-width: 768px) {
      .cta-buttons {
        flex-direction: column;
        align-items: center;
      }
      
      .hero-section {
        padding: 40px 20px;
      }
      
      h1 {
        font-size: 2rem;
      }
    }
  </style>
</head>
<body>
  <header>
    <div class="header-container">
      <div class="logo">
        <a href="#">AccrédiQR</a>
      </div>
      
      <nav>
        <ul class="nav-list">
          <li><a href="#">Accueil</a></li>
          <li><a href="#">Fonctionnalités</a></li>
          <li><a href="#">Partenaires</a></li>
          <li><a href="#">Contact</a></li>
        </ul>
      </nav>
    </div>
  </header>

  <div class="container">
    <div class="hero-section">
      <h1>Bienvenue sur AccrédiQR</h1>
      <p class="subtitle">La plateforme qui valorise les interactions avec les institutions</p>
      
      <div class="cta-buttons">
        <a href="#" class="btn btn-primary">Se connecter</a>
        <a href="#" class="btn btn-primary">S'inscrire</a>
      </div>
    </div>

    <div class="features-section">
      <h2>Comment ça fonctionne</h2>
      
      <div class="features-grid">
        <div class="feature-card">
          <div class="feature-icon">📱</div>
          <h3>Scannez</h3>
          <p>Utilisez votre appareil pour scanner le QR code d'une institution ou d'un prestataire</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">👤</div>
          <h3>Consultez</h3>
          <p>Accédez au profil KnowMe du prestataire et découvrez ses compétences</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">⭐</div>
          <h3>Attribuez</h3>
          <p>Donnez des points pour valoriser les services rendus ou les bonnes actions</p>
        </div>
        
        <div class="feature-card">
          <div class="feature-icon">🎁</div>
          <h3>Échangez</h3>
          <p>Utilisez vos points pour obtenir des récompenses auprès de nos partenaires</p>
        </div>
      </div>
    </div>

    <div class="partners-section">
      <h2>Nos partenaires</h2>
      <p>Rejoignez notre réseau d'institutions et de prestataires à Monastir et Sousse</p>
      
      <div class="partners-list">
        <div class="partner">Écoles</div>
        <div class="partner">Universités</div>
        <div class="partner">Bibliothèques</div>
        <div class="partner">Associations</div>
        <div class="partner">Commerces locaux</div>
      </div>
    </div>
  </div>

  <footer>
    <div class="footer-content">
      <p>&copy; 2024 AccrédiQR - Tous droits réservés</p>
      <div class="footer-links">
        <a href="#">Mentions légales</a>
        <a href="#">Politique de confidentialité</a>
        <a href="#">Contact</a>
      </div>
    </div>
  </footer>

  <script>
    // Simulation de l'application
    document.addEventListener('DOMContentLoaded', function() {
      const ctaButtons = document.querySelectorAll('.cta-buttons .btn');
      
      ctaButtons.forEach(button => {
        button.addEventListener('click', function(e) {
          e.preventDefault();
          alert('Cette fonctionnalité sera disponible prochainement. L\'application est en cours de développement.');
        });
      });
    });
  </script>
</body>
</html>
