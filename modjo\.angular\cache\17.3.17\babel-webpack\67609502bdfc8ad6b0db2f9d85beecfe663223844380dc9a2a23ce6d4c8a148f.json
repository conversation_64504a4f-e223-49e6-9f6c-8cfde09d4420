{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { LoginComponent } from './components/login/login.component';\nimport { RegisterComponent } from './components/register/register.component';\nimport { authRoutes } from './auth.routes';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AuthModule {\n  static {\n    this.ɵfac = function AuthModule_Factory(t) {\n      return new (t || AuthModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AuthModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, ReactiveFormsModule, RouterModule.forChild(authRoutes),\n      // Material modules\n      MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatSelectModule, MatCheckboxModule, MatProgressSpinnerModule, MatDividerModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AuthModule, {\n    declarations: [LoginComponent, RegisterComponent],\n    imports: [CommonModule, ReactiveFormsModule, i1.RouterModule,\n    // Material modules\n    MatCardModule, MatFormFieldModule, MatInputModule, MatButtonModule, MatIconModule, MatSelectModule, MatCheckboxModule, MatProgressSpinnerModule, MatDividerModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "ReactiveFormsModule", "RouterModule", "MatCardModule", "MatFormFieldModule", "MatInputModule", "MatButtonModule", "MatIconModule", "MatSelectModule", "MatCheckboxModule", "MatProgressSpinnerModule", "MatDividerModule", "LoginComponent", "RegisterComponent", "authRoutes", "AuthModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\auth\\auth.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDividerModule } from '@angular/material/divider';\n\nimport { LoginComponent } from './components/login/login.component';\nimport { RegisterComponent } from './components/register/register.component';\nimport { authRoutes } from './auth.routes';\n\n@NgModule({\n  declarations: [\n    LoginComponent,\n    RegisterComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule.forChild(authRoutes),\n    \n    // Material modules\n    MatCardModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatButtonModule,\n    MatIconModule,\n    MatSelectModule,\n    MatCheckboxModule,\n    MatProgressSpinnerModule,\n    MatDividerModule\n  ]\n})\nexport class AuthModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,QAAQ,gBAAgB;AACpD,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D,SAASC,cAAc,QAAQ,oCAAoC;AACnE,SAASC,iBAAiB,QAAQ,0CAA0C;AAC5E,SAASC,UAAU,QAAQ,eAAe;;;AAwB1C,OAAM,MAAOC,UAAU;;;uBAAVA,UAAU;IAAA;EAAA;;;YAAVA;IAAU;EAAA;;;gBAhBnBf,YAAY,EACZC,mBAAmB,EACnBC,YAAY,CAACc,QAAQ,CAACF,UAAU,CAAC;MAEjC;MACAX,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,EACxBC,gBAAgB;IAAA;EAAA;;;2EAGPI,UAAU;IAAAE,YAAA,GApBnBL,cAAc,EACdC,iBAAiB;IAAAK,OAAA,GAGjBlB,YAAY,EACZC,mBAAmB,EAAAkB,EAAA,CAAAjB,YAAA;IAGnB;IACAC,aAAa,EACbC,kBAAkB,EAClBC,cAAc,EACdC,eAAe,EACfC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,wBAAwB,EACxBC,gBAAgB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}