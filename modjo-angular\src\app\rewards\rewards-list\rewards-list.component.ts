import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RewardsService } from '../rewards.service';
import { AuthService } from '../../auth/auth.service';
import { UserService } from '../../user/user.service';
import { Reward, RewardRedemption } from '../../models/reward.model';
import { PointHistory } from '../../models/point.model';

@Component({
  selector: 'app-rewards-list',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './rewards-list.component.html',
  styleUrls: ['./rewards-list.component.css']
})
export class RewardsListComponent implements OnInit {
  rewards: Reward[] = [];
  userRedemptions: RewardRedemption[] = [];
  pointHistory: PointHistory | null = null;
  isLoading: boolean = true;
  error: string = '';

  redeemingRewardId: string | null = null;
  successMessage: string = '';
  errorMessage: string = '';

  constructor(
    private rewardsService: RewardsService,
    private authService: AuthService,
    private userService: UserService
  ) {}

  ngOnInit(): void {
    this.loadData();
  }

  private async loadData(): Promise<void> {
    try {
      const currentUser = this.authService.getCurrentUser();

      if (!currentUser) {
        this.error = 'Utilisateur non connecté';
        this.isLoading = false;
        return;
      }

      // Get available rewards
      this.rewards = await this.rewardsService.getAvailableRewards().toPromise() || [];

      // Get user redemptions
      this.userRedemptions = await this.rewardsService.getUserRedemptions(currentUser.uid).toPromise() || [];

      // Get point history
      this.pointHistory = await this.userService.getUserPointHistory(currentUser.uid).toPromise() || null;

      this.isLoading = false;
    } catch (error) {
      console.error('Error loading rewards data:', error);
      this.error = 'Erreur lors du chargement des récompenses';
      this.isLoading = false;
    }
  }

  async redeemReward(rewardId: string): Promise<void> {
    this.errorMessage = '';
    this.successMessage = '';
    this.redeemingRewardId = rewardId;

    try {
      const currentUser = this.authService.getCurrentUser();

      if (!currentUser) {
        this.errorMessage = 'Utilisateur non connecté';
        this.redeemingRewardId = null;
        return;
      }

      // Check if user has enough points
      const reward = this.rewards.find(r => r.id === rewardId);
      if (!reward) {
        this.errorMessage = 'Récompense non trouvée';
        this.redeemingRewardId = null;
        return;
      }

      if (!this.pointHistory || this.pointHistory.totalPoints < reward.pointsCost) {
        this.errorMessage = 'Vous n\'avez pas assez de points pour cette récompense';
        this.redeemingRewardId = null;
        return;
      }

      // Redeem reward
      await this.rewardsService.redeemReward(currentUser.uid, rewardId);

      this.successMessage = 'Récompense échangée avec succès!';

      // Reload data
      this.loadData();
    } catch (error: any) {
      console.error('Error redeeming reward:', error);
      this.errorMessage = error.message || 'Erreur lors de l\'échange de la récompense';
    } finally {
      this.redeemingRewardId = null;
    }
  }

  getUserPoints(): number {
    return this.pointHistory ? this.pointHistory.totalPoints : 0;
  }

  canRedeemReward(reward: Reward): boolean {
    return this.pointHistory ? this.pointHistory.totalPoints >= reward.pointsCost : false;
  }
}
