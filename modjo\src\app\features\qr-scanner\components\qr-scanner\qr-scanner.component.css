.scanner-container {
  max-width: 800px;
  margin: 0 auto;
  padding: 16px;
}

.scanner-header {
  text-align: center;
  margin-bottom: 24px;
}

.scanner-header h1 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 2rem;
  font-weight: 600;
}

.scanner-header p {
  margin: 0;
  color: #666;
  font-size: 1rem;
}

.camera-section,
.no-camera-section,
.demo-section,
.instructions-section {
  margin-bottom: 24px;
}

.camera-card,
.no-camera-card,
.demo-card,
.instructions-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.camera-container {
  position: relative;
  width: 100%;
  max-width: 400px;
  margin: 0 auto;
  background: #000;
  border-radius: 8px;
  overflow: hidden;
}

.camera-video {
  width: 100%;
  height: 300px;
  object-fit: cover;
  display: block;
}

.camera-video.active {
  height: 400px;
}

.scanner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(0, 0, 0, 0.3);
}

.scanner-frame {
  width: 200px;
  height: 200px;
  position: relative;
}

.scanner-corners {
  position: relative;
  width: 100%;
  height: 100%;
}

.corner {
  position: absolute;
  width: 20px;
  height: 20px;
  border: 3px solid #fff;
}

.corner.top-left {
  top: 0;
  left: 0;
  border-right: none;
  border-bottom: none;
}

.corner.top-right {
  top: 0;
  right: 0;
  border-left: none;
  border-bottom: none;
}

.corner.bottom-left {
  bottom: 0;
  left: 0;
  border-right: none;
  border-top: none;
}

.corner.bottom-right {
  bottom: 0;
  right: 0;
  border-left: none;
  border-top: none;
}

.scanner-instruction {
  color: white;
  margin-top: 16px;
  text-align: center;
  font-weight: 500;
}

.camera-controls {
  display: flex;
  justify-content: center;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 24px;
}

.enhanced-btn {
  height: 48px;
  font-weight: 600;
  border-radius: 12px;
  padding: 0 20px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-width: 140px;
}

.enhanced-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.flash-button,
.camera-button {
  height: 48px;
  font-weight: 500;
  border-radius: 12px;
}

.scan-button,
.stop-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 24px;
  font-size: 1rem;
}

.no-camera-content {
  text-align: center;
  padding: 32px;
}

.no-camera-icon {
  font-size: 4rem;
  color: #ccc;
  margin-bottom: 16px;
}

.no-camera-content h3 {
  margin: 0 0 8px 0;
  color: #333;
}

.no-camera-content p {
  margin: 0 0 24px 0;
  color: #666;
}

.demo-buttons {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
  margin-top: 24px;
}

.demo-qr-button {
  height: 64px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 12px;
  padding: 0 20px;
  border-radius: 16px;
  font-weight: 500;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;
  text-align: left;
}

.demo-qr-button:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.demo-button-content {
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  text-align: left;
}

.demo-title {
  font-size: 1rem;
  font-weight: 600;
  margin-bottom: 2px;
}

.demo-points {
  font-size: 0.8rem;
  opacity: 0.8;
  font-weight: 400;
}

.instructions-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
  margin-top: 16px;
}

.instruction-item {
  display: flex;
  align-items: flex-start;
  gap: 16px;
}

.instruction-item mat-icon {
  margin-top: 4px;
  flex-shrink: 0;
}

.instruction-item h4 {
  margin: 0 0 4px 0;
  color: #333;
  font-size: 1.1rem;
}

.instruction-item p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .scanner-container {
    padding: 8px;
  }
  
  .scanner-header h1 {
    font-size: 1.5rem;
  }
  
  .camera-video {
    height: 250px;
  }
  
  .camera-video.active {
    height: 300px;
  }
  
  .scanner-frame {
    width: 150px;
    height: 150px;
  }
  
  .demo-buttons {
    gap: 6px;
  }
  
  .demo-qr-button {
    padding: 8px 12px;
    font-size: 0.9rem;
  }
}
