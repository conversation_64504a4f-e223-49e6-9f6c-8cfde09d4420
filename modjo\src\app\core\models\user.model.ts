export interface User {
  uid: string;
  email: string;
  name: string;
  phone?: string;
  city: '<PERSON><PERSON>ir' | 'Sousse';
  role: UserR<PERSON>;
  points: number;
  avatar?: string;
  createdAt: Date;
  updatedAt: Date;
  isActive: boolean;
  history: PointsTransaction[];
}

export interface UserProfile {
  uid: string;
  name: string;
  email: string;
  phone?: string;
  city: 'Monastir' | 'Sousse';
  avatar?: string;
  bio?: string;
  dateOfBirth?: Date;
  preferences: UserPreferences;
}

export interface UserPreferences {
  notifications: boolean;
  darkMode: boolean;
  language: 'fr' | 'ar' | 'en';
  location: boolean;
}

export enum UserRole {
  USER = 'user',           // 🙋‍♂️ Utilisateur standard
  PROVIDER = 'provider',   // 🧑‍🔧 Prestataire (prof, coach, association)
  VALIDATOR = 'validator', // 🧑‍🏫 Validateur (école, bibliothèque)
  PARTNER = 'partner',     // 🧑‍🍳 Partenaire (commerçant, entreprise)
  ADMIN = 'admin'          // 🧑‍💼 Administrateur
}

export interface PointsTransaction {
  id: string;
  fromUserId?: string;
  toUserId: string;
  points: number;
  type: TransactionType;
  description: string;
  timestamp: Date;
  validatedBy?: string;
  qrCode?: string;
  location?: GeoLocation;
}

export enum TransactionType {
  EARNED = 'earned',
  SPENT = 'spent',
  TRANSFERRED = 'transferred',
  VALIDATED = 'validated',
  BONUS = 'bonus'
}

export interface GeoLocation {
  latitude: number;
  longitude: number;
  address?: string;
}

export interface CreateUserRequest {
  email: string;
  password: string;
  name: string;
  phone?: string;
  city: 'Monastir' | 'Sousse';
  role: UserRole;
}

export interface UpdateUserRequest {
  name?: string;
  phone?: string;
  city?: 'Monastir' | 'Sousse';
  avatar?: string;
  bio?: string;
  preferences?: Partial<UserPreferences>;
}
