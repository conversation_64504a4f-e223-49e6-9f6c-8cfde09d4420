{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Angular Material\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatStepperModule } from '@angular/material/stepper';\n// Components\nimport { RoleNavigationComponent } from './components/role-navigation/role-navigation.component';\nimport { RoleSwitcherComponent } from './components/role-switcher/role-switcher.component';\n// Pipes\nimport { TimeAgoPipe } from './pipes/time-ago.pipe';\nimport * as i0 from \"@angular/core\";\nconst MATERIAL_MODULES = [MatButtonModule, MatCardModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSnackBarModule, MatProgressSpinnerModule, MatDialogModule, MatMenuModule, MatToolbarModule, MatSidenavModule, MatListModule, MatTabsModule, MatSelectModule, MatCheckboxModule, MatRadioModule, MatDatepickerModule, MatNativeDateModule, MatSlideToggleModule, MatProgressBarModule, MatChipsModule, MatBadgeModule, MatTooltipModule, MatExpansionModule, MatStepperModule];\nexport class SharedModule {\n  static {\n    this.ɵfac = function SharedModule_Factory(t) {\n      return new (t || SharedModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: SharedModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule, ReactiveFormsModule, FormsModule, MATERIAL_MODULES, CommonModule, RouterModule, ReactiveFormsModule, FormsModule, MatButtonModule, MatCardModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSnackBarModule, MatProgressSpinnerModule, MatDialogModule, MatMenuModule, MatToolbarModule, MatSidenavModule, MatListModule, MatTabsModule, MatSelectModule, MatCheckboxModule, MatRadioModule, MatDatepickerModule, MatNativeDateModule, MatSlideToggleModule, MatProgressBarModule, MatChipsModule, MatBadgeModule, MatTooltipModule, MatExpansionModule, MatStepperModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(SharedModule, {\n    declarations: [RoleNavigationComponent, RoleSwitcherComponent, TimeAgoPipe],\n    imports: [CommonModule, RouterModule, ReactiveFormsModule, FormsModule, MatButtonModule, MatCardModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSnackBarModule, MatProgressSpinnerModule, MatDialogModule, MatMenuModule, MatToolbarModule, MatSidenavModule, MatListModule, MatTabsModule, MatSelectModule, MatCheckboxModule, MatRadioModule, MatDatepickerModule, MatNativeDateModule, MatSlideToggleModule, MatProgressBarModule, MatChipsModule, MatBadgeModule, MatTooltipModule, MatExpansionModule, MatStepperModule],\n    exports: [CommonModule, RouterModule, ReactiveFormsModule, FormsModule, MatButtonModule, MatCardModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSnackBarModule, MatProgressSpinnerModule, MatDialogModule, MatMenuModule, MatToolbarModule, MatSidenavModule, MatListModule, MatTabsModule, MatSelectModule, MatCheckboxModule, MatRadioModule, MatDatepickerModule, MatNativeDateModule, MatSlideToggleModule, MatProgressBarModule, MatChipsModule, MatBadgeModule, MatTooltipModule, MatExpansionModule, MatStepperModule, RoleNavigationComponent, RoleSwitcherComponent, TimeAgoPipe]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "ReactiveFormsModule", "FormsModule", "MatButtonModule", "MatCardModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatDialogModule", "MatMenuModule", "MatToolbarModule", "MatSidenavModule", "MatListModule", "MatTabsModule", "MatSelectModule", "MatCheckboxModule", "MatRadioModule", "MatDatepickerModule", "MatNativeDateModule", "MatSlideToggleModule", "MatProgressBarModule", "MatChipsModule", "MatBadgeModule", "MatTooltipModule", "MatExpansionModule", "MatStepperModule", "RoleNavigationComponent", "RoleSwitcherComponent", "TimeAgoPipe", "MATERIAL_MODULES", "SharedModule", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n\n// Angular Material\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatStepperModule } from '@angular/material/stepper';\n\n// Components\nimport { RoleNavigationComponent } from './components/role-navigation/role-navigation.component';\nimport { RoleSwitcherComponent } from './components/role-switcher/role-switcher.component';\n\n// Pipes\nimport { TimeAgoPipe } from './pipes/time-ago.pipe';\n\nconst MATERIAL_MODULES = [\n  MatButtonModule,\n  MatCardModule,\n  MatIconModule,\n  MatInputModule,\n  MatFormFieldModule,\n  MatSnackBarModule,\n  MatProgressSpinnerModule,\n  MatDialogModule,\n  MatMenuModule,\n  MatToolbarModule,\n  MatSidenavModule,\n  MatListModule,\n  MatTabsModule,\n  MatSelectModule,\n  MatCheckboxModule,\n  MatRadioModule,\n  MatDatepickerModule,\n  MatNativeDateModule,\n  MatSlideToggleModule,\n  MatProgressBarModule,\n  MatChipsModule,\n  MatBadgeModule,\n  MatTooltipModule,\n  MatExpansionModule,\n  MatStepperModule\n];\n\n@NgModule({\n  declarations: [\n    RoleNavigationComponent,\n    RoleSwitcherComponent,\n    TimeAgoPipe\n  ],\n  imports: [\n    CommonModule,\n    RouterModule,\n    ReactiveFormsModule,\n    FormsModule,\n    ...MATERIAL_MODULES\n  ],\n  exports: [\n    CommonModule,\n    RouterModule,\n    ReactiveFormsModule,\n    FormsModule,\n    ...MATERIAL_MODULES,\n    RoleNavigationComponent,\n    RoleSwitcherComponent,\n    TimeAgoPipe\n  ]\n})\nexport class SharedModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAEjE;AACA,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D;AACA,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,qBAAqB,QAAQ,oDAAoD;AAE1F;AACA,SAASC,WAAW,QAAQ,uBAAuB;;AAEnD,MAAMC,gBAAgB,GAAG,CACvB5B,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,iBAAiB,EACjBC,wBAAwB,EACxBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,oBAAoB,EACpBC,oBAAoB,EACpBC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,gBAAgB,CACjB;AA0BD,OAAM,MAAOK,YAAY;;;uBAAZA,YAAY;IAAA;EAAA;;;YAAZA;IAAY;EAAA;;;gBAjBrBjC,YAAY,EACZC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACR6B,gBAAgB,EAGnBhC,YAAY,EACZC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EA5CbC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,iBAAiB,EACjBC,wBAAwB,EACxBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,oBAAoB,EACpBC,oBAAoB,EACpBC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,gBAAgB;IAAA;EAAA;;;2EA2BLK,YAAY;IAAAC,YAAA,GAtBrBL,uBAAuB,EACvBC,qBAAqB,EACrBC,WAAW;IAAAI,OAAA,GAGXnC,YAAY,EACZC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EArCbC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,iBAAiB,EACjBC,wBAAwB,EACxBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,oBAAoB,EACpBC,oBAAoB,EACpBC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,gBAAgB;IAAAQ,OAAA,GAiBdpC,YAAY,EACZC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EA5CbC,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,iBAAiB,EACjBC,wBAAwB,EACxBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,oBAAoB,EACpBC,oBAAoB,EACpBC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,gBAAgB,EAsBdC,uBAAuB,EACvBC,qBAAqB,EACrBC,WAAW;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}