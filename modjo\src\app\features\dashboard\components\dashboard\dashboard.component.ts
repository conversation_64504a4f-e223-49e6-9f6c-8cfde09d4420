import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../../../core/services/auth.service';
import { User } from '../../../../core/models';

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {
  user: User | null = null;

  quickActions = [
    {
      title: 'Scanner QR',
      description: 'Scanner un code QR pour gagner des points',
      icon: 'qr_code_scanner',
      route: '/qr-scanner',
      color: 'primary'
    },
    {
      title: 'Récompenses',
      description: 'Découvrir les récompenses disponibles',
      icon: 'card_giftcard',
      route: '/rewards',
      color: 'accent'
    },
    {
      title: 'Mon Profil',
      description: 'Voir et modifier mon profil',
      icon: 'person',
      route: '/profile',
      color: 'primary'
    }
  ];

  stats = [
    {
      title: 'Points totaux',
      value: '0',
      icon: 'stars',
      color: '#ffd700'
    },
    {
      title: 'Actions validées',
      value: '0',
      icon: 'verified',
      color: '#4caf50'
    },
    {
      title: 'Récompenses obtenues',
      value: '0',
      icon: 'card_giftcard',
      color: '#ff9800'
    }
  ];

  constructor(private authService: AuthService) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.user = user;
      if (user) {
        this.updateStats(user);
      }
    });
  }

  private updateStats(user: User): void {
    this.stats[0].value = user.points.toString();
    
    // Count validated actions from history
    const validatedActions = user.history?.filter(h => h.type === 'validated').length || 0;
    this.stats[1].value = validatedActions.toString();
    
    // Count spent points (rewards obtained)
    const rewardsObtained = user.history?.filter(h => h.type === 'spent').length || 0;
    this.stats[2].value = rewardsObtained.toString();
  }

  getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Bonjour';
    if (hour < 18) return 'Bon après-midi';
    return 'Bonsoir';
  }

  getRoleDisplayName(role: string): string {
    const roleNames: { [key: string]: string } = {
      'user': 'Utilisateur',
      'provider': 'Partenaire',
      'validator': 'Validateur',
      'admin': 'Administrateur'
    };
    return roleNames[role] || role;
  }

  getActivityIcon(type: string): string {
    const iconMap: { [key: string]: string } = {
      'earned': 'add_circle',
      'spent': 'remove_circle',
      'transferred': 'swap_horiz',
      'validated': 'verified',
      'bonus': 'star'
    };
    return iconMap[type] || 'circle';
  }
}
