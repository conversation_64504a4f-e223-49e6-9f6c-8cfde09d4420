import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../../core/services/auth.service';
import { DashboardRouterService } from '../../../../core/services/dashboard-router.service';
import { User, UserRole } from '../../../../core/models';

interface CommunityActivity {
  id: string;
  description: string;
  points: number;
  type: string;
  timestamp: Date;
  userId: string;
  userName: string;
}

interface LocalEvent {
  id: string;
  title: string;
  description: string;
  date: Date;
  location: string;
  category: string;
  points: number;
  image: string;
  participants: number;
  maxParticipants: number;
}

interface Partner {
  id: string;
  name: string;
  category: string;
  logo: string;
  rating: number;
  reviews: number;
  currentOffer: string;
  location: string;
  qrCode: string;
}

interface Challenge {
  id: string;
  title: string;
  description: string;
  icon: string;
  gradient: string;
  progress: number;
  current: number;
  target: number;
  reward: string;
  endDate: Date;
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {
  user: User | null = null;

  // Community data
  communityFeed: CommunityActivity[] = [];
  topContributors: User[] = [];
  localEvents: LocalEvent[] = [];
  featuredPartners: Partner[] = [];
  activeChallenges: Challenge[] = [];

  // City comparison data
  cityComparison = {
    monastir: { score: 0, participants: 0 },
    sousse: { score: 0, participants: 0 }
  };

  comparisonStats = [
    { name: 'Actions validées', monastir: 0, sousse: 0, monastirValue: 0, sousseValue: 0 },
    { name: 'Événements organisés', monastir: 0, sousse: 0, monastirValue: 0, sousseValue: 0 },
    { name: 'Partenaires actifs', monastir: 0, sousse: 0, monastirValue: 0, sousseValue: 0 }
  ];

  actionCategories = [
    {
      title: 'Environnement',
      actions: [
        { title: 'Nettoyage plage', icon: 'waves', color: 'primary', points: 15 },
        { title: 'Plantation arbres', icon: 'park', color: 'primary', points: 20 },
        { title: 'Recyclage', icon: 'recycling', color: 'primary', points: 10 }
      ]
    },
    {
      title: 'Social',
      actions: [
        { title: 'Aide personnes âgées', icon: 'elderly', color: 'accent', points: 25 },
        { title: 'Cours bénévoles', icon: 'school', color: 'accent', points: 30 },
        { title: 'Distribution repas', icon: 'restaurant', color: 'accent', points: 20 }
      ]
    },
    {
      title: 'Culture',
      actions: [
        { title: 'Guide touristique', icon: 'tour', color: 'warn', points: 15 },
        { title: 'Animation enfants', icon: 'child_care', color: 'warn', points: 18 },
        { title: 'Événement culturel', icon: 'theater_comedy', color: 'warn', points: 25 }
      ]
    }
  ];

  constructor(
    private authService: AuthService,
    private router: Router,
    private dashboardRouter: DashboardRouterService
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.user = user;
      if (user) {
        // Rediriger vers le dashboard approprié selon le rôle
        if (user.role !== UserRole.USER) {
          this.dashboardRouter.navigateToUserDashboard(user);
          return;
        }
        this.loadDashboardData();
      }
    });
  }

  private loadDashboardData(): void {
    this.loadCommunityFeed();
    this.loadTopContributors();
    this.loadLocalEvents();
    this.loadFeaturedPartners();
    this.loadActiveChallenges();
    this.loadCityComparison();
  }

  private loadCommunityFeed(): void {
    // Simulate real community activity feed
    this.communityFeed = [
      {
        id: '1',
        description: 'Ahmed a nettoyé la plage de Monastir',
        points: 15,
        type: 'environment',
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 min ago
        userId: 'user1',
        userName: 'Ahmed'
      },
      {
        id: '2',
        description: 'Fatma a aidé des personnes âgées à Sousse',
        points: 25,
        type: 'social',
        timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago
        userId: 'user2',
        userName: 'Fatma'
      },
      {
        id: '3',
        description: 'Mohamed a organisé un cours de français',
        points: 30,
        type: 'education',
        timestamp: new Date(Date.now() - 1000 * 60 * 90), // 1.5 hours ago
        userId: 'user3',
        userName: 'Mohamed'
      },
      {
        id: '4',
        description: 'Leila a guidé des touristes au Ribat',
        points: 15,
        type: 'culture',
        timestamp: new Date(Date.now() - 1000 * 60 * 120), // 2 hours ago
        userId: 'user4',
        userName: 'Leila'
      }
    ];
  }

  private loadTopContributors(): void {
    this.topContributors = [
      { uid: 'top1', name: 'Ahmed Ben Ali', city: 'Monastir', points: 1250, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },
      { uid: 'top2', name: 'Fatma Trabelsi', city: 'Sousse', points: 1180, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },
      { uid: 'top3', name: 'Mohamed Gharbi', city: 'Monastir', points: 1050, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },
      { uid: 'top4', name: 'Leila Mansouri', city: 'Sousse', points: 980, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },
      { uid: 'top5', name: 'Karim Bouazizi', city: 'Monastir', points: 920, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] }
    ];
  }

  private loadLocalEvents(): void {
    this.localEvents = [
      {
        id: 'event1',
        title: 'Nettoyage de la Plage de Monastir',
        description: 'Rejoignez-nous pour nettoyer notre belle plage',
        date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2), // In 2 days
        location: 'Plage de Monastir',
        category: 'Environnement',
        points: 20,
        image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=200&fit=crop',
        participants: 45,
        maxParticipants: 100
      },
      {
        id: 'event2',
        title: 'Festival Culturel de Sousse',
        description: 'Célébrons notre patrimoine culturel ensemble',
        date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5), // In 5 days
        location: 'Médina de Sousse',
        category: 'Culture',
        points: 15,
        image: 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=400&h=200&fit=crop',
        participants: 120,
        maxParticipants: 200
      },
      {
        id: 'event3',
        title: 'Cours de Français pour Réfugiés',
        description: 'Aidez à enseigner le français aux nouveaux arrivants',
        date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7), // In 1 week
        location: 'Centre Communautaire',
        category: 'Éducation',
        points: 30,
        image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=200&fit=crop',
        participants: 15,
        maxParticipants: 30
      }
    ];
  }

  private loadFeaturedPartners(): void {
    this.featuredPartners = [
      {
        id: 'partner1',
        name: 'Café des Nattes',
        category: 'Restaurant',
        logo: 'https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=100&h=100&fit=crop',
        rating: 4.5,
        reviews: 127,
        currentOffer: '10% de réduction avec 50 points',
        location: 'Médina de Monastir',
        qrCode: 'PARTNER_CAFE_NATTES'
      },
      {
        id: 'partner2',
        name: 'Boutique Artisanat Sousse',
        category: 'Artisanat',
        logo: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop',
        rating: 4.8,
        reviews: 89,
        currentOffer: 'Produit gratuit avec 100 points',
        location: 'Médina de Sousse',
        qrCode: 'PARTNER_ARTISANAT_SOUSSE'
      },
      {
        id: 'partner3',
        name: 'Hammam Traditionnel',
        category: 'Bien-être',
        logo: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=100&h=100&fit=crop',
        rating: 4.3,
        reviews: 156,
        currentOffer: 'Séance gratuite avec 75 points',
        location: 'Centre-ville Monastir',
        qrCode: 'PARTNER_HAMMAM'
      }
    ];
  }

  private loadActiveChallenges(): void {
    this.activeChallenges = [
      {
        id: 'challenge1',
        title: 'Éco-Warrior',
        description: 'Participez à 5 actions environnementales ce mois-ci',
        icon: 'eco',
        gradient: 'linear-gradient(135deg, #4CAF50, #8BC34A)',
        progress: 60,
        current: 3,
        target: 5,
        reward: 'Badge Éco-Warrior + 100 points bonus',
        endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 15) // 15 days
      },
      {
        id: 'challenge2',
        title: 'Ambassadeur Culturel',
        description: 'Guidez 10 touristes dans votre ville',
        icon: 'tour',
        gradient: 'linear-gradient(135deg, #FF9800, #FFC107)',
        progress: 30,
        current: 3,
        target: 10,
        reward: 'Badge Ambassadeur + Visite gratuite musée',
        endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 20) // 20 days
      },
      {
        id: 'challenge3',
        title: 'Solidarité Communautaire',
        description: 'Aidez 15 personnes dans le besoin',
        icon: 'volunteer_activism',
        gradient: 'linear-gradient(135deg, #E91E63, #F06292)',
        progress: 80,
        current: 12,
        target: 15,
        reward: 'Badge Solidarité + 200 points bonus',
        endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 10) // 10 days
      }
    ];
  }

  private loadCityComparison(): void {
    // Simulate city competition data
    this.cityComparison = {
      monastir: { score: 15420, participants: 342 },
      sousse: { score: 16180, participants: 389 }
    };

    this.comparisonStats = [
      {
        name: 'Actions validées',
        monastir: 65,
        sousse: 78,
        monastirValue: 1250,
        sousseValue: 1520
      },
      {
        name: 'Événements organisés',
        monastir: 45,
        sousse: 52,
        monastirValue: 23,
        sousseValue: 27
      },
      {
        name: 'Partenaires actifs',
        monastir: 80,
        sousse: 70,
        monastirValue: 16,
        sousseValue: 14
      }
    ];
  }

  // UI Helper Methods
  getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Bonjour';
    if (hour < 18) return 'Bon après-midi';
    return 'Bonsoir';
  }

  getCityIcon(): string {
    if (!this.user) return 'location_city';
    return this.user.city === 'Monastir' ? 'account_balance' : 'museum';
  }

  getCommunityRank(): number {
    if (!this.user) return 0;
    const userIndex = this.topContributors.findIndex(u => u.uid === this.user!.uid);
    return userIndex >= 0 ? userIndex + 1 : this.topContributors.length + 1;
  }

  getImpactScore(): number {
    if (!this.user) return 0;
    // Calculate impact based on points and community actions
    return Math.floor(this.user.points * 1.2 + (this.user.history?.length || 0) * 5);
  }

  getCityProgress(): number {
    if (!this.user) return 0;
    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;
    const monthlyTarget = 20000;
    return Math.min((cityData.score / monthlyTarget) * 314, 314); // 314 = 2π * 50 (circle circumference)
  }

  getCityProgressPercent(): number {
    if (!this.user) return 0;
    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;
    const monthlyTarget = 20000;
    return Math.min(Math.round((cityData.score / monthlyTarget) * 100), 100);
  }

  getCityStats() {
    if (!this.user) return { validatedActions: 0, activeUsers: 0 };
    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;
    return {
      validatedActions: Math.floor(cityData.score / 15), // Estimate based on average points per action
      activeUsers: cityData.participants
    };
  }

  getActivityColor(type: string): string {
    const colorMap: { [key: string]: string } = {
      'environment': '#4CAF50',
      'social': '#E91E63',
      'education': '#2196F3',
      'culture': '#FF9800',
      'health': '#9C27B0'
    };
    return colorMap[type] || '#667eea';
  }

  getActivityIcon(type: string): string {
    const iconMap: { [key: string]: string } = {
      'environment': 'eco',
      'social': 'volunteer_activism',
      'education': 'school',
      'culture': 'theater_comedy',
      'health': 'health_and_safety'
    };
    return iconMap[type] || 'circle';
  }

  getTrophyColor(index: number): string {
    const colors = ['#FFD700', '#C0C0C0', '#CD7F32']; // Gold, Silver, Bronze
    return colors[index] || '#667eea';
  }

  getTrophyIcon(index: number): string {
    return index < 3 ? 'emoji_events' : 'star';
  }

  getStars(rating: number): number[] {
    return Array(Math.floor(rating)).fill(0);
  }

  // Action Methods
  viewEvent(event: LocalEvent): void {
    // Navigate to event details or open modal
    console.log('Viewing event:', event);
  }

  viewPartner(partner: Partner): void {
    // Navigate to partner details or open modal
    console.log('Viewing partner:', partner);
  }

  executeAction(action: any): void {
    // Execute the selected action
    console.log('Executing action:', action);
    // This would typically open a form or navigate to action details
  }

  viewAllActivity(): void {
    this.router.navigate(['/community/activity']);
  }
}
