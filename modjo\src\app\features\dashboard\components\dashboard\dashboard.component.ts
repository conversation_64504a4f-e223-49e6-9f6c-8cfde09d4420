import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../../core/services/auth.service';
import { DashboardRouterService } from '../../../../core/services/dashboard-router.service';
import { MockDataService } from '../../../../core/services/mock-data.service';
import { User, UserRole } from '../../../../core/models';

// Interfaces pour le dashboard élève
interface Reward {
  id: string;
  title: string;
  description: string;
  pointsRequired: number;
  partnerName: string;
  imageUrl: string;
  image?: string; // Alias pour imageUrl
  category: string;
  isActive: boolean;
}

interface HistoryAction {
  id: string;
  type: string;
  description: string;
  points: number;
  timestamp: Date;
  location?: string;
  providerName?: string;
}

interface MonthlyStats {
  pointsEarned: number;
  pointsSpent: number;
  scansCount: number;
  validationsCount: number;
}

interface Achievement {
  id: string;
  title: string;
  description: string;
  icon: string;
  color: string;
  earned: boolean;
  progress: number;
  current: number;
  target: number;
}

interface Notification {
  id: string;
  type: string;
  title: string;
  message: string;
  timestamp: Date;
  read: boolean;
}

interface CommunityActivity {
  id: string;
  description: string;
  points: number;
  type: string;
  timestamp: Date;
  userId: string;
  userName: string;
}

interface LocalEvent {
  id: string;
  title: string;
  description: string;
  date: Date;
  location: string;
  category: string;
  points: number;
  image: string;
  participants: number;
  maxParticipants: number;
}

interface Partner {
  id: string;
  name: string;
  category: string;
  logo: string;
  rating: number;
  reviews: number;
  currentOffer: string;
  location: string;
  qrCode: string;
}

interface Challenge {
  id: string;
  title: string;
  description: string;
  icon: string;
  gradient: string;
  progress: number;
  current: number;
  target: number;
  reward: string;
  endDate: Date;
}

@Component({
  selector: 'app-dashboard',
  templateUrl: './dashboard.component.html',
  styleUrls: ['./dashboard.component.css']
})
export class DashboardComponent implements OnInit {
  user: User | null = null;

  // Données pour le dashboard élève
  availableRewards: Reward[] = [];
  nextReward: Reward | null = null;
  filteredHistory: HistoryAction[] = [];
  monthlyStats: MonthlyStats = {
    pointsEarned: 0,
    pointsSpent: 0,
    scansCount: 0,
    validationsCount: 0
  };
  achievements: Achievement[] = [];
  notifications: Notification[] = [];
  weeklyPointsData: any[] = [];
  maxWeeklyPoints = 100;

  // État de l'interface
  selectedPeriod = 'month';
  showNotifications = false;
  unreadNotifications = 0;

  constructor(
    private authService: AuthService,
    private router: Router,
    private dashboardRouter: DashboardRouterService,
    private mockDataService: MockDataService
  ) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.user = user;
      if (user) {
        // Rediriger vers le dashboard approprié selon le rôle
        if (user.role !== UserRole.USER) {
          this.dashboardRouter.navigateToUserDashboard(user);
          return;
        }
        this.loadStudentDashboardData();
      }
    });
  }

  private loadStudentDashboardData(): void {
    this.loadAvailableRewards();
    this.loadUserHistory();
    this.loadMonthlyStats();
    this.loadAchievements();
    this.loadNotifications();
    this.loadWeeklyPointsData();
    this.setNextReward();
  }

  private loadAvailableRewards(): void {
    const mockData = this.mockDataService.getMockDataForRole(UserRole.USER);
    this.availableRewards = mockData.availableRewards || [
      {
        id: 'reward1',
        title: 'Café gratuit',
        description: 'Un café offert au Café des Nattes',
        pointsRequired: 50,
        partnerName: 'Café des Nattes',
        imageUrl: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',
        image: 'https://images.unsplash.com/photo-1509042239860-f550ce710b93?w=300',
        category: 'FOOD',
        isActive: true
      },
      {
        id: 'reward2',
        title: '10% de réduction',
        description: 'Réduction sur tous les produits artisanaux',
        pointsRequired: 75,
        partnerName: 'Boutique Artisanat',
        imageUrl: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',
        image: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=300',
        category: 'SHOPPING',
        isActive: true
      },
      {
        id: 'reward3',
        title: 'Entrée gratuite',
        description: 'Visite gratuite du musée de Sousse',
        pointsRequired: 100,
        partnerName: 'Musée de Sousse',
        imageUrl: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300',
        image: 'https://images.unsplash.com/photo-1594736797933-d0401ba2fe65?w=300',
        category: 'CULTURE',
        isActive: true
      }
    ];
  }

  private loadUserHistory(): void {
    if (!this.user) return;

    // Charger l'historique depuis les données utilisateur
    this.filteredHistory = this.user.history?.map(h => ({
      id: h.id,
      type: h.type,
      description: h.description,
      points: h.points,
      timestamp: h.timestamp,
      location: 'Monastir', // Exemple
      providerName: 'Prestataire local' // Exemple
    })) || [];

    this.applyHistoryFilter();
  }

  private loadMonthlyStats(): void {
    if (!this.user) return;

    // Calculer les statistiques du mois en cours
    const currentMonth = new Date().getMonth();
    const currentYear = new Date().getFullYear();

    const monthlyActions = this.user.history?.filter(h => {
      const actionDate = new Date(h.timestamp);
      return actionDate.getMonth() === currentMonth && actionDate.getFullYear() === currentYear;
    }) || [];

    this.monthlyStats = {
      pointsEarned: monthlyActions.filter(a => a.points > 0).reduce((sum, a) => sum + a.points, 0),
      pointsSpent: Math.abs(monthlyActions.filter(a => a.points < 0).reduce((sum, a) => sum + a.points, 0)),
      scansCount: monthlyActions.filter(a => a.description.includes('Scan QR')).length,
      validationsCount: monthlyActions.filter(a => !a.description.includes('Scan QR')).length
    };
  }

  private loadAchievements(): void {
    this.achievements = [
      {
        id: 'first_scan',
        title: 'Premier Scan',
        description: 'Scanner votre premier QR Code',
        icon: 'qr_code_scanner',
        color: '#4CAF50',
        earned: (this.user?.history?.length || 0) > 0,
        progress: Math.min((this.user?.history?.length || 0) * 100, 100),
        current: this.user?.history?.length || 0,
        target: 1
      },
      {
        id: 'point_collector',
        title: 'Collecteur de Points',
        description: 'Atteindre 100 points',
        icon: 'stars',
        color: '#FFD700',
        earned: (this.user?.points || 0) >= 100,
        progress: Math.min((this.user?.points || 0), 100),
        current: this.user?.points || 0,
        target: 100
      },
      {
        id: 'community_helper',
        title: 'Aide Communautaire',
        description: 'Effectuer 5 bonnes actions',
        icon: 'volunteer_activism',
        color: '#E91E63',
        earned: (this.user?.history?.length || 0) >= 5,
        progress: Math.min((this.user?.history?.length || 0) * 20, 100),
        current: this.user?.history?.length || 0,
        target: 5
      },
      {
        id: 'reward_redeemer',
        title: 'Échangeur de Récompenses',
        description: 'Échanger votre première récompense',
        icon: 'card_giftcard',
        color: '#FF9800',
        earned: false, // À implémenter avec l'historique des échanges
        progress: 0,
        current: 0,
        target: 1
      }
    ];
  }

  private loadNotifications(): void {
    this.notifications = [
      {
        id: 'notif1',
        type: 'points_earned',
        title: 'Points gagnés !',
        message: 'Vous avez gagné 15 points pour votre aide à la bibliothèque',
        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 min ago
        read: false
      },
      {
        id: 'notif2',
        type: 'new_reward',
        title: 'Nouvelle récompense !',
        message: 'Une nouvelle récompense est disponible au Café des Nattes',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 2), // 2 hours ago
        read: false
      },
      {
        id: 'notif3',
        type: 'achievement',
        title: 'Achievement débloqué !',
        message: 'Félicitations ! Vous avez débloqué le badge "Premier Scan"',
        timestamp: new Date(Date.now() - 1000 * 60 * 60 * 24), // 1 day ago
        read: true
      }
    ];

    this.unreadNotifications = this.notifications.filter(n => !n.read).length;
  }

  private loadWeeklyPointsData(): void {
    // Simuler les données de points par semaine pour le graphique
    this.weeklyPointsData = [
      { week: 1, points: 45 },
      { week: 2, points: 62 },
      { week: 3, points: 38 },
      { week: 4, points: 75 }
    ];

    this.maxWeeklyPoints = Math.max(...this.weeklyPointsData.map(w => w.points));
  }

  private setNextReward(): void {
    if (!this.user || this.availableRewards.length === 0) return;

    // Trouver la prochaine récompense atteignable
    const affordableRewards = this.availableRewards
      .filter(r => r.pointsRequired > this.user!.points)
      .sort((a, b) => a.pointsRequired - b.pointsRequired);

    this.nextReward = affordableRewards[0] || this.availableRewards[0];
  }

  // Méthodes pour le dashboard élève

  // Progression vers la prochaine récompense
  getPointsToNextReward(): number {
    if (!this.user || !this.nextReward) return 0;
    return Math.max(0, this.nextReward.pointsRequired - this.user.points);
  }

  getProgressPercentage(): number {
    if (!this.user || !this.nextReward) return 0;
    return Math.min((this.user.points / this.nextReward.pointsRequired) * 100, 100);
  }

  getMotivationMessage(): string {
    const pointsNeeded = this.getPointsToNextReward();
    if (pointsNeeded === 0) {
      return "🎉 Félicitations ! Vous pouvez échanger cette récompense !";
    } else if (pointsNeeded <= 10) {
      return `🔥 Plus que ${pointsNeeded} points ! Vous y êtes presque !`;
    } else if (pointsNeeded <= 25) {
      return `💪 Encore ${pointsNeeded} points et c'est à vous !`;
    } else {
      return `🎯 Objectif : ${pointsNeeded} points pour votre prochaine récompense !`;
    }
  }

  // Filtrage de l'historique
  filterHistory(): void {
    this.applyHistoryFilter();
  }

  applyHistoryFilter(): void {
    if (!this.user?.history) {
      this.filteredHistory = [];
      return;
    }

    const now = new Date();
    let startDate: Date;

    switch (this.selectedPeriod) {
      case 'week':
        startDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
        break;
      case 'month':
        startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        break;
      case 'year':
        startDate = new Date(now.getFullYear(), 0, 1);
        break;
      default:
        this.filteredHistory = this.user.history.map(h => ({
          id: h.id,
          type: h.type,
          description: h.description,
          points: h.points,
          timestamp: h.timestamp,
          location: 'Monastir',
          providerName: 'Prestataire local'
        }));
        return;
    }

    this.filteredHistory = this.user.history
      .filter(h => new Date(h.timestamp) >= startDate)
      .map(h => ({
        id: h.id,
        type: h.type,
        description: h.description,
        points: h.points,
        timestamp: h.timestamp,
        location: 'Monastir',
        providerName: 'Prestataire local'
      }));
  }

  // Méthodes helper pour l'interface
  getActionColor(type: string): string {
    const colorMap: { [key: string]: string } = {
      'earned': '#4CAF50',
      'spent': '#F44336',
      'qr_scan': '#2196F3',
      'validation': '#FF9800'
    };
    return colorMap[type] || '#667eea';
  }

  getActionIcon(type: string): string {
    const iconMap: { [key: string]: string } = {
      'earned': 'trending_up',
      'spent': 'trending_down',
      'qr_scan': 'qr_code_scanner',
      'validation': 'verified'
    };
    return iconMap[type] || 'circle';
  }

  getNotificationColor(type: string): string {
    const colorMap: { [key: string]: string } = {
      'points_earned': '#4CAF50',
      'new_reward': '#FF9800',
      'achievement': '#9C27B0',
      'reminder': '#2196F3'
    };
    return colorMap[type] || '#667eea';
  }

  getNotificationIcon(type: string): string {
    const iconMap: { [key: string]: string } = {
      'points_earned': 'stars',
      'new_reward': 'card_giftcard',
      'achievement': 'emoji_events',
      'reminder': 'notifications'
    };
    return iconMap[type] || 'info';
  }

  // Méthodes d'action
  openQRScanner(): void {
    console.log('Ouverture du scanner QR...');
    this.router.navigate(['/qr-scanner']);
  }

  exchangeReward(reward: Reward): void {
    if (!this.user || this.user.points < reward.pointsRequired) {
      console.log('Pas assez de points pour cette récompense');
      return;
    }

    console.log('Échange de récompense:', reward);
    // TODO: Implémenter l'échange de récompense
    // - Déduire les points
    // - Générer un code d'échange
    // - Mettre à jour l'historique
  }

  toggleNotifications(): void {
    this.showNotifications = !this.showNotifications;
  }

  markAsRead(notification: Notification): void {
    notification.read = true;
    this.unreadNotifications = this.notifications.filter(n => !n.read).length;
  }

  openProfile(): void {
    this.router.navigate(['/profile']);
  }

  logout(): void {
    this.authService.logout();
    this.router.navigate(['/auth/login']);
  }
}
