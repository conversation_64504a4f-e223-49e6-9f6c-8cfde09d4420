{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Optional, Input, Directive, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Injection token that can be used to provide the default options the card module. */\nconst _c0 = [\"*\"];\nconst _c1 = [[[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"], [\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"], [\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"], [\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"], [\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]], \"*\"];\nconst _c2 = [\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\", \"*\"];\nconst _c3 = [[[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]], [[\"mat-card-title\"], [\"mat-card-subtitle\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardTitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]], \"*\"];\nconst _c4 = [\"[mat-card-avatar], [matCardAvatar]\", \"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\", \"*\"];\nconst MAT_CARD_CONFIG = new InjectionToken('MAT_CARD_CONFIG');\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCard {\n  constructor(config) {\n    this.appearance = config?.appearance || 'raised';\n  }\n  static {\n    this.ɵfac = function MatCard_Factory(t) {\n      return new (t || MatCard)(i0.ɵɵdirectiveInject(MAT_CARD_CONFIG, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatCard,\n      selectors: [[\"mat-card\"]],\n      hostAttrs: [1, \"mat-mdc-card\", \"mdc-card\"],\n      hostVars: 4,\n      hostBindings: function MatCard_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-card-outlined\", ctx.appearance === \"outlined\")(\"mdc-card--outlined\", ctx.appearance === \"outlined\");\n        }\n      },\n      inputs: {\n        appearance: \"appearance\"\n      },\n      exportAs: [\"matCard\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatCard_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{color:rgba(0, 0, 0, 0.6);flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mdc-card__action--icon:not(:disabled){color:rgba(0, 0, 0, 0.6)}.mat-mdc-card{border-radius:var(--mdc-elevated-card-container-shape);background-color:var(--mdc-elevated-card-container-color);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color);box-shadow:var(--mdc-elevated-card-container-elevation)}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape)}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width);border-style:solid;border-color:var(--mdc-outlined-card-outline-color);border-radius:var(--mdc-outlined-card-container-shape);background-color:var(--mdc-outlined-card-container-color);box-shadow:var(--mdc-outlined-card-container-elevation)}.mat-mdc-card-outlined .mdc-card::after{border-radius:var(--mdc-outlined-card-container-shape)}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font);line-height:var(--mat-card-title-text-line-height);font-size:var(--mat-card-title-text-size);letter-spacing:var(--mat-card-title-text-tracking);font-weight:var(--mat-card-title-text-weight)}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color);font-family:var(--mat-card-subtitle-text-font);line-height:var(--mat-card-subtitle-text-line-height);font-size:var(--mat-card-subtitle-text-size);letter-spacing:var(--mat-card-subtitle-text-tracking);font-weight:var(--mat-card-subtitle-text-weight)}.mat-mdc-card{position:relative}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCard, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card',\n      host: {\n        'class': 'mat-mdc-card mdc-card',\n        '[class.mat-mdc-card-outlined]': 'appearance === \"outlined\"',\n        '[class.mdc-card--outlined]': 'appearance === \"outlined\"'\n      },\n      exportAs: 'matCard',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      template: \"<ng-content></ng-content>\\n\",\n      styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{color:rgba(0, 0, 0, 0.6);flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mdc-card__action--icon:not(:disabled){color:rgba(0, 0, 0, 0.6)}.mat-mdc-card{border-radius:var(--mdc-elevated-card-container-shape);background-color:var(--mdc-elevated-card-container-color);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color);box-shadow:var(--mdc-elevated-card-container-elevation)}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape)}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width);border-style:solid;border-color:var(--mdc-outlined-card-outline-color);border-radius:var(--mdc-outlined-card-container-shape);background-color:var(--mdc-outlined-card-container-color);box-shadow:var(--mdc-outlined-card-container-elevation)}.mat-mdc-card-outlined .mdc-card::after{border-radius:var(--mdc-outlined-card-container-shape)}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font);line-height:var(--mat-card-title-text-line-height);font-size:var(--mat-card-title-text-size);letter-spacing:var(--mat-card-title-text-tracking);font-weight:var(--mat-card-title-text-weight)}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color);font-family:var(--mat-card-subtitle-text-font);line-height:var(--mat-card-subtitle-text-line-height);font-size:var(--mat-card-subtitle-text-size);letter-spacing:var(--mat-card-subtitle-text-tracking);font-weight:var(--mat-card-subtitle-text-weight)}.mat-mdc-card{position:relative}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"]\n    }]\n  }], () => [{\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_CARD_CONFIG]\n    }, {\n      type: Optional\n    }]\n  }], {\n    appearance: [{\n      type: Input\n    }]\n  });\n})();\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardTitle {\n  static {\n    this.ɵfac = function MatCardTitle_Factory(t) {\n      return new (t || MatCardTitle)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardTitle,\n      selectors: [[\"mat-card-title\"], [\"\", \"mat-card-title\", \"\"], [\"\", \"matCardTitle\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-title\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n      host: {\n        'class': 'mat-mdc-card-title'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\nclass MatCardTitleGroup {\n  static {\n    this.ɵfac = function MatCardTitleGroup_Factory(t) {\n      return new (t || MatCardTitleGroup)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatCardTitleGroup,\n      selectors: [[\"mat-card-title-group\"]],\n      hostAttrs: [1, \"mat-mdc-card-title-group\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 4,\n      vars: 0,\n      template: function MatCardTitleGroup_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵelementStart(0, \"div\");\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵprojection(3, 2);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardTitleGroup, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-title-group',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-card-title-group'\n      },\n      standalone: true,\n      template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardContent {\n  static {\n    this.ɵfac = function MatCardContent_Factory(t) {\n      return new (t || MatCardContent)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardContent,\n      selectors: [[\"mat-card-content\"]],\n      hostAttrs: [1, \"mat-mdc-card-content\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardContent, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-content',\n      host: {\n        'class': 'mat-mdc-card-content'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardSubtitle {\n  static {\n    this.ɵfac = function MatCardSubtitle_Factory(t) {\n      return new (t || MatCardSubtitle)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardSubtitle,\n      selectors: [[\"mat-card-subtitle\"], [\"\", \"mat-card-subtitle\", \"\"], [\"\", \"matCardSubtitle\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-subtitle\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSubtitle, [{\n    type: Directive,\n    args: [{\n      selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n      host: {\n        'class': 'mat-mdc-card-subtitle'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardActions {\n  constructor() {\n    // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n    // as to not conflict with the native `align` attribute.\n    /** Position of the actions inside the card. */\n    this.align = 'start';\n  }\n  static {\n    this.ɵfac = function MatCardActions_Factory(t) {\n      return new (t || MatCardActions)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardActions,\n      selectors: [[\"mat-card-actions\"]],\n      hostAttrs: [1, \"mat-mdc-card-actions\", \"mdc-card__actions\"],\n      hostVars: 2,\n      hostBindings: function MatCardActions_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵclassProp(\"mat-mdc-card-actions-align-end\", ctx.align === \"end\");\n        }\n      },\n      inputs: {\n        align: \"align\"\n      },\n      exportAs: [\"matCardActions\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardActions, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-actions',\n      exportAs: 'matCardActions',\n      host: {\n        'class': 'mat-mdc-card-actions mdc-card__actions',\n        '[class.mat-mdc-card-actions-align-end]': 'align === \"end\"'\n      },\n      standalone: true\n    }]\n  }], null, {\n    align: [{\n      type: Input\n    }]\n  });\n})();\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardHeader {\n  static {\n    this.ɵfac = function MatCardHeader_Factory(t) {\n      return new (t || MatCardHeader)();\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatCardHeader,\n      selectors: [[\"mat-card-header\"]],\n      hostAttrs: [1, \"mat-mdc-card-header\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c4,\n      decls: 4,\n      vars: 0,\n      consts: [[1, \"mat-mdc-card-header-text\"]],\n      template: function MatCardHeader_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c3);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 0);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(3, 2);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardHeader, [{\n    type: Component,\n    args: [{\n      selector: 'mat-card-header',\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      host: {\n        'class': 'mat-mdc-card-header'\n      },\n      standalone: true,\n      template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], null, null);\n})();\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardFooter {\n  static {\n    this.ɵfac = function MatCardFooter_Factory(t) {\n      return new (t || MatCardFooter)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardFooter,\n      selectors: [[\"mat-card-footer\"]],\n      hostAttrs: [1, \"mat-mdc-card-footer\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardFooter, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-card-footer',\n      host: {\n        'class': 'mat-mdc-card-footer'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n// TODO(jelbourn): support `.mdc-card__media-content`.\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardImage {\n  static {\n    this.ɵfac = function MatCardImage_Factory(t) {\n      return new (t || MatCardImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardImage,\n      selectors: [[\"\", \"mat-card-image\", \"\"], [\"\", \"matCardImage\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-image\", \"mdc-card__media\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-image], [matCardImage]',\n      host: {\n        'class': 'mat-mdc-card-image mdc-card__media'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but small. */\nclass MatCardSmImage {\n  static {\n    this.ɵfac = function MatCardSmImage_Factory(t) {\n      return new (t || MatCardSmImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardSmImage,\n      selectors: [[\"\", \"mat-card-sm-image\", \"\"], [\"\", \"matCardImageSmall\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-sm-image\", \"mdc-card__media\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardSmImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-sm-image], [matCardImageSmall]',\n      host: {\n        'class': 'mat-mdc-card-sm-image mdc-card__media'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but medium. */\nclass MatCardMdImage {\n  static {\n    this.ɵfac = function MatCardMdImage_Factory(t) {\n      return new (t || MatCardMdImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardMdImage,\n      selectors: [[\"\", \"mat-card-md-image\", \"\"], [\"\", \"matCardImageMedium\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-md-image\", \"mdc-card__media\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardMdImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-md-image], [matCardImageMedium]',\n      host: {\n        'class': 'mat-mdc-card-md-image mdc-card__media'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but large. */\nclass MatCardLgImage {\n  static {\n    this.ɵfac = function MatCardLgImage_Factory(t) {\n      return new (t || MatCardLgImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardLgImage,\n      selectors: [[\"\", \"mat-card-lg-image\", \"\"], [\"\", \"matCardImageLarge\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-lg-image\", \"mdc-card__media\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardLgImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-lg-image], [matCardImageLarge]',\n      host: {\n        'class': 'mat-mdc-card-lg-image mdc-card__media'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/** Same as `MatCardImage`, but extra-large. */\nclass MatCardXlImage {\n  static {\n    this.ɵfac = function MatCardXlImage_Factory(t) {\n      return new (t || MatCardXlImage)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardXlImage,\n      selectors: [[\"\", \"mat-card-xl-image\", \"\"], [\"\", \"matCardImageXLarge\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-xl-image\", \"mdc-card__media\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardXlImage, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-xl-image], [matCardImageXLarge]',\n      host: {\n        'class': 'mat-mdc-card-xl-image mdc-card__media'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardAvatar {\n  static {\n    this.ɵfac = function MatCardAvatar_Factory(t) {\n      return new (t || MatCardAvatar)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatCardAvatar,\n      selectors: [[\"\", \"mat-card-avatar\", \"\"], [\"\", \"matCardAvatar\", \"\"]],\n      hostAttrs: [1, \"mat-mdc-card-avatar\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardAvatar, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-card-avatar], [matCardAvatar]',\n      host: {\n        'class': 'mat-mdc-card-avatar'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\nconst CARD_DIRECTIVES = [MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage];\nclass MatCardModule {\n  static {\n    this.ɵfac = function MatCardModule_Factory(t) {\n      return new (t || MatCardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatCardModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, CommonModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatCardModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, CommonModule, ...CARD_DIRECTIVES],\n      exports: [CARD_DIRECTIVES, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CARD_CONFIG, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Inject", "Optional", "Input", "Directive", "NgModule", "CommonModule", "MatCommonModule", "_c0", "_c1", "_c2", "_c3", "_c4", "MAT_CARD_CONFIG", "MatCard", "constructor", "config", "appearance", "ɵfac", "MatCard_Factory", "t", "ɵɵdirectiveInject", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatCard_HostBindings", "rf", "ctx", "ɵɵclassProp", "inputs", "exportAs", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "template", "MatCard_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "selector", "host", "None", "OnPush", "undefined", "decorators", "MatCardTitle", "MatCardTitle_Factory", "ɵdir", "ɵɵdefineDirective", "MatCardTitleGroup", "MatCardTitleGroup_Factory", "MatCardTitleGroup_Template", "ɵɵelementStart", "ɵɵelementEnd", "Mat<PERSON><PERSON><PERSON><PERSON>nt", "MatCardContent_Factory", "MatCardSubtitle", "MatCardSubtitle_Factory", "MatCardActions", "align", "MatCardActions_Factory", "MatCardActions_HostBindings", "MatCardHeader", "MatCardHeader_Factory", "consts", "MatCardHeader_Template", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "MatCardFooter_Factory", "MatCardImage", "MatCardImage_Factory", "MatCardSmImage", "MatCardSmImage_Factory", "MatCardMdImage", "MatCardMdImage_Factory", "MatCardLgImage", "MatCardLgImage_Factory", "MatCardXlImage", "MatCardXlImage_Factory", "MatCardAvatar", "MatCardAvatar_Factory", "CARD_DIRECTIVES", "MatCardModule", "MatCardModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/material/fesm2022/card.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Inject, Optional, Input, Directive, NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Injection token that can be used to provide the default options the card module. */\nconst MAT_CARD_CONFIG = new InjectionToken('MAT_CARD_CONFIG');\n/**\n * Material Design card component. Cards contain content and actions about a single subject.\n * See https://material.io/design/components/cards.html\n *\n * MatCard provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCard {\n    constructor(config) {\n        this.appearance = config?.appearance || 'raised';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCard, deps: [{ token: MAT_CARD_CONFIG, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCard, isStandalone: true, selector: \"mat-card\", inputs: { appearance: \"appearance\" }, host: { properties: { \"class.mat-mdc-card-outlined\": \"appearance === \\\"outlined\\\"\", \"class.mdc-card--outlined\": \"appearance === \\\"outlined\\\"\" }, classAttribute: \"mat-mdc-card mdc-card\" }, exportAs: [\"matCard\"], ngImport: i0, template: \"<ng-content></ng-content>\\n\", styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{color:rgba(0, 0, 0, 0.6);flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mdc-card__action--icon:not(:disabled){color:rgba(0, 0, 0, 0.6)}.mat-mdc-card{border-radius:var(--mdc-elevated-card-container-shape);background-color:var(--mdc-elevated-card-container-color);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color);box-shadow:var(--mdc-elevated-card-container-elevation)}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape)}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width);border-style:solid;border-color:var(--mdc-outlined-card-outline-color);border-radius:var(--mdc-outlined-card-container-shape);background-color:var(--mdc-outlined-card-container-color);box-shadow:var(--mdc-outlined-card-container-elevation)}.mat-mdc-card-outlined .mdc-card::after{border-radius:var(--mdc-outlined-card-container-shape)}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font);line-height:var(--mat-card-title-text-line-height);font-size:var(--mat-card-title-text-size);letter-spacing:var(--mat-card-title-text-tracking);font-weight:var(--mat-card-title-text-weight)}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color);font-family:var(--mat-card-subtitle-text-font);line-height:var(--mat-card-subtitle-text-line-height);font-size:var(--mat-card-subtitle-text-size);letter-spacing:var(--mat-card-subtitle-text-tracking);font-weight:var(--mat-card-subtitle-text-weight)}.mat-mdc-card{position:relative}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCard, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card', host: {\n                        'class': 'mat-mdc-card mdc-card',\n                        '[class.mat-mdc-card-outlined]': 'appearance === \"outlined\"',\n                        '[class.mdc-card--outlined]': 'appearance === \"outlined\"',\n                    }, exportAs: 'matCard', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, standalone: true, template: \"<ng-content></ng-content>\\n\", styles: [\".mdc-card{display:flex;flex-direction:column;box-sizing:border-box}.mdc-card::after{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none;pointer-events:none}@media screen and (forced-colors: active){.mdc-card::after{border-color:CanvasText}}.mdc-card--outlined::after{border:none}.mdc-card__content{border-radius:inherit;height:100%}.mdc-card__media{position:relative;box-sizing:border-box;background-repeat:no-repeat;background-position:center;background-size:cover}.mdc-card__media::before{display:block;content:\\\"\\\"}.mdc-card__media:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__media:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__media--square::before{margin-top:100%}.mdc-card__media--16-9::before{margin-top:56.25%}.mdc-card__media-content{position:absolute;top:0;right:0;bottom:0;left:0;box-sizing:border-box}.mdc-card__primary-action{display:flex;flex-direction:column;box-sizing:border-box;position:relative;outline:none;color:inherit;text-decoration:none;cursor:pointer;overflow:hidden}.mdc-card__primary-action:first-child{border-top-left-radius:inherit;border-top-right-radius:inherit}.mdc-card__primary-action:last-child{border-bottom-left-radius:inherit;border-bottom-right-radius:inherit}.mdc-card__actions{display:flex;flex-direction:row;align-items:center;box-sizing:border-box;min-height:52px;padding:8px}.mdc-card__actions--full-bleed{padding:0}.mdc-card__action-buttons,.mdc-card__action-icons{display:flex;flex-direction:row;align-items:center;box-sizing:border-box}.mdc-card__action-icons{color:rgba(0, 0, 0, 0.6);flex-grow:1;justify-content:flex-end}.mdc-card__action-buttons+.mdc-card__action-icons{margin-left:16px;margin-right:0}[dir=rtl] .mdc-card__action-buttons+.mdc-card__action-icons,.mdc-card__action-buttons+.mdc-card__action-icons[dir=rtl]{margin-left:0;margin-right:16px}.mdc-card__action{display:inline-flex;flex-direction:row;align-items:center;box-sizing:border-box;justify-content:center;cursor:pointer;user-select:none}.mdc-card__action:focus{outline:none}.mdc-card__action--button{margin-left:0;margin-right:8px;padding:0 8px}[dir=rtl] .mdc-card__action--button,.mdc-card__action--button[dir=rtl]{margin-left:8px;margin-right:0}.mdc-card__action--button:last-child{margin-left:0;margin-right:0}[dir=rtl] .mdc-card__action--button:last-child,.mdc-card__action--button:last-child[dir=rtl]{margin-left:0;margin-right:0}.mdc-card__actions--full-bleed .mdc-card__action--button{justify-content:space-between;width:100%;height:auto;max-height:none;margin:0;padding:8px 16px;text-align:left}[dir=rtl] .mdc-card__actions--full-bleed .mdc-card__action--button,.mdc-card__actions--full-bleed .mdc-card__action--button[dir=rtl]{text-align:right}.mdc-card__action--icon{margin:-6px 0;padding:12px}.mdc-card__action--icon:not(:disabled){color:rgba(0, 0, 0, 0.6)}.mat-mdc-card{border-radius:var(--mdc-elevated-card-container-shape);background-color:var(--mdc-elevated-card-container-color);border-width:0;border-style:solid;border-color:var(--mdc-elevated-card-container-color);box-shadow:var(--mdc-elevated-card-container-elevation)}.mat-mdc-card .mdc-card::after{border-radius:var(--mdc-elevated-card-container-shape)}.mat-mdc-card-outlined{border-width:var(--mdc-outlined-card-outline-width);border-style:solid;border-color:var(--mdc-outlined-card-outline-color);border-radius:var(--mdc-outlined-card-container-shape);background-color:var(--mdc-outlined-card-container-color);box-shadow:var(--mdc-outlined-card-container-elevation)}.mat-mdc-card-outlined .mdc-card::after{border-radius:var(--mdc-outlined-card-container-shape)}.mat-mdc-card-title{font-family:var(--mat-card-title-text-font);line-height:var(--mat-card-title-text-line-height);font-size:var(--mat-card-title-text-size);letter-spacing:var(--mat-card-title-text-tracking);font-weight:var(--mat-card-title-text-weight)}.mat-mdc-card-subtitle{color:var(--mat-card-subtitle-text-color);font-family:var(--mat-card-subtitle-text-font);line-height:var(--mat-card-subtitle-text-line-height);font-size:var(--mat-card-subtitle-text-size);letter-spacing:var(--mat-card-subtitle-text-tracking);font-weight:var(--mat-card-subtitle-text-weight)}.mat-mdc-card{position:relative}.mat-mdc-card-title,.mat-mdc-card-subtitle{display:block;margin:0}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle{padding:16px 16px 0}.mat-mdc-card-header{display:flex;padding:16px 16px 0}.mat-mdc-card-content{display:block;padding:0 16px}.mat-mdc-card-content:first-child{padding-top:16px}.mat-mdc-card-content:last-child{padding-bottom:16px}.mat-mdc-card-title-group{display:flex;justify-content:space-between;width:100%}.mat-mdc-card-avatar{height:40px;width:40px;border-radius:50%;flex-shrink:0;margin-bottom:16px;object-fit:cover}.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-avatar~.mat-mdc-card-header-text .mat-mdc-card-title{line-height:normal}.mat-mdc-card-sm-image{width:80px;height:80px}.mat-mdc-card-md-image{width:112px;height:112px}.mat-mdc-card-lg-image{width:152px;height:152px}.mat-mdc-card-xl-image{width:240px;height:240px}.mat-mdc-card-subtitle~.mat-mdc-card-title,.mat-mdc-card-title~.mat-mdc-card-subtitle,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-title,.mat-mdc-card-header .mat-mdc-card-header-text .mat-mdc-card-subtitle,.mat-mdc-card-title-group .mat-mdc-card-title,.mat-mdc-card-title-group .mat-mdc-card-subtitle{padding-top:0}.mat-mdc-card-content>:last-child:not(.mat-mdc-card-footer){margin-bottom:0}.mat-mdc-card-actions-align-end{justify-content:flex-end}\"] }]\n        }], ctorParameters: () => [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_CARD_CONFIG]\n                }, {\n                    type: Optional\n                }] }], propDecorators: { appearance: [{\n                type: Input\n            }] } });\n// TODO(jelbourn): add `MatActionCard`, which is a card that acts like a button (and has a ripple).\n// Supported in MDC with `.mdc-card__primary-action`. Will require additional a11y docs for users.\n/**\n * Title of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for one variety of card title; any custom title element may be used in its place.\n *\n * MatCardTitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardTitle {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardTitle, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCardTitle, isStandalone: true, selector: \"mat-card-title, [mat-card-title], [matCardTitle]\", host: { classAttribute: \"mat-mdc-card-title\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardTitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-card-title, [mat-card-title], [matCardTitle]`,\n                    host: { 'class': 'mat-mdc-card-title' },\n                    standalone: true,\n                }]\n        }] });\n/**\n * Container intended to be used within the `<mat-card>` component. Can contain exactly one\n * `<mat-card-title>`, one `<mat-card-subtitle>` and one content image of any size\n * (e.g. `<img matCardLgImage>`).\n */\nclass MatCardTitleGroup {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardTitleGroup, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCardTitleGroup, isStandalone: true, selector: \"mat-card-title-group\", host: { classAttribute: \"mat-mdc-card-title-group\" }, ngImport: i0, template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardTitleGroup, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card-title-group', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { 'class': 'mat-mdc-card-title-group' }, standalone: true, template: \"<div>\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content select=\\\"[mat-card-image], [matCardImage],\\n                    [mat-card-sm-image], [matCardImageSmall],\\n                    [mat-card-md-image], [matCardImageMedium],\\n                    [mat-card-lg-image], [matCardImageLarge],\\n                    [mat-card-xl-image], [matCardImageXLarge]\\\"></ng-content>\\n<ng-content></ng-content>\\n\" }]\n        }] });\n/**\n * Content of a card, intended for use within `<mat-card>`. This component is an optional\n * convenience for use with other convenience elements, such as `<mat-card-title>`; any custom\n * content block element may be used in its place.\n *\n * MatCardContent provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardContent {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardContent, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCardContent, isStandalone: true, selector: \"mat-card-content\", host: { classAttribute: \"mat-mdc-card-content\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardContent, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-content',\n                    host: { 'class': 'mat-mdc-card-content' },\n                    standalone: true,\n                }]\n        }] });\n/**\n * Sub-title of a card, intended for use within `<mat-card>` beneath a `<mat-card-title>`. This\n * component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`.\n *\n * MatCardSubtitle provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardSubtitle {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardSubtitle, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCardSubtitle, isStandalone: true, selector: \"mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]\", host: { classAttribute: \"mat-mdc-card-subtitle\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardSubtitle, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: `mat-card-subtitle, [mat-card-subtitle], [matCardSubtitle]`,\n                    host: { 'class': 'mat-mdc-card-subtitle' },\n                    standalone: true,\n                }]\n        }] });\n/**\n * Bottom area of a card that contains action buttons, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom action block element may be used in its place.\n *\n * MatCardActions provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardActions {\n    constructor() {\n        // TODO(jelbourn): deprecate `align` in favor of `actionPosition` or `actionAlignment`\n        // as to not conflict with the native `align` attribute.\n        /** Position of the actions inside the card. */\n        this.align = 'start';\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardActions, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCardActions, isStandalone: true, selector: \"mat-card-actions\", inputs: { align: \"align\" }, host: { properties: { \"class.mat-mdc-card-actions-align-end\": \"align === \\\"end\\\"\" }, classAttribute: \"mat-mdc-card-actions mdc-card__actions\" }, exportAs: [\"matCardActions\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardActions, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-actions',\n                    exportAs: 'matCardActions',\n                    host: {\n                        'class': 'mat-mdc-card-actions mdc-card__actions',\n                        '[class.mat-mdc-card-actions-align-end]': 'align === \"end\"',\n                    },\n                    standalone: true,\n                }]\n        }], propDecorators: { align: [{\n                type: Input\n            }] } });\n/**\n * Header region of a card, intended for use within `<mat-card>`. This header captures\n * a card title, subtitle, and avatar.  This component is an optional convenience for use with\n * other convenience elements, such as `<mat-card-footer>`; any custom header block element may be\n * used in its place.\n *\n * MatCardHeader provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardHeader {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardHeader, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCardHeader, isStandalone: true, selector: \"mat-card-header\", host: { classAttribute: \"mat-mdc-card-header\" }, ngImport: i0, template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardHeader, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-card-header', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: { 'class': 'mat-mdc-card-header' }, standalone: true, template: \"<ng-content select=\\\"[mat-card-avatar], [matCardAvatar]\\\"></ng-content>\\n<div class=\\\"mat-mdc-card-header-text\\\">\\n  <ng-content\\n      select=\\\"mat-card-title, mat-card-subtitle,\\n      [mat-card-title], [mat-card-subtitle],\\n      [matCardTitle], [matCardSubtitle]\\\"></ng-content>\\n</div>\\n<ng-content></ng-content>\\n\" }]\n        }] });\n/**\n * Footer area a card, intended for use within `<mat-card>`.\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom footer block element may be used in its place.\n *\n * MatCardFooter provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardFooter {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardFooter, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCardFooter, isStandalone: true, selector: \"mat-card-footer\", host: { classAttribute: \"mat-mdc-card-footer\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardFooter, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-card-footer',\n                    host: { 'class': 'mat-mdc-card-footer' },\n                    standalone: true,\n                }]\n        }] });\n// TODO(jelbourn): deprecate the \"image\" selectors to replace with \"media\".\n// TODO(jelbourn): support `.mdc-card__media-content`.\n/**\n * Primary image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-content>`; any custom media element may be used in its place.\n *\n * MatCardImage provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardImage {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardImage, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCardImage, isStandalone: true, selector: \"[mat-card-image], [matCardImage]\", host: { classAttribute: \"mat-mdc-card-image mdc-card__media\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-image], [matCardImage]',\n                    host: { 'class': 'mat-mdc-card-image mdc-card__media' },\n                    standalone: true,\n                }]\n        }] });\n/** Same as `MatCardImage`, but small. */\nclass MatCardSmImage {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardSmImage, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCardSmImage, isStandalone: true, selector: \"[mat-card-sm-image], [matCardImageSmall]\", host: { classAttribute: \"mat-mdc-card-sm-image mdc-card__media\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardSmImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-sm-image], [matCardImageSmall]',\n                    host: { 'class': 'mat-mdc-card-sm-image mdc-card__media' },\n                    standalone: true,\n                }]\n        }] });\n/** Same as `MatCardImage`, but medium. */\nclass MatCardMdImage {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardMdImage, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCardMdImage, isStandalone: true, selector: \"[mat-card-md-image], [matCardImageMedium]\", host: { classAttribute: \"mat-mdc-card-md-image mdc-card__media\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardMdImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-md-image], [matCardImageMedium]',\n                    host: { 'class': 'mat-mdc-card-md-image mdc-card__media' },\n                    standalone: true,\n                }]\n        }] });\n/** Same as `MatCardImage`, but large. */\nclass MatCardLgImage {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardLgImage, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCardLgImage, isStandalone: true, selector: \"[mat-card-lg-image], [matCardImageLarge]\", host: { classAttribute: \"mat-mdc-card-lg-image mdc-card__media\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardLgImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-lg-image], [matCardImageLarge]',\n                    host: { 'class': 'mat-mdc-card-lg-image mdc-card__media' },\n                    standalone: true,\n                }]\n        }] });\n/** Same as `MatCardImage`, but extra-large. */\nclass MatCardXlImage {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardXlImage, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCardXlImage, isStandalone: true, selector: \"[mat-card-xl-image], [matCardImageXLarge]\", host: { classAttribute: \"mat-mdc-card-xl-image mdc-card__media\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardXlImage, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-xl-image], [matCardImageXLarge]',\n                    host: { 'class': 'mat-mdc-card-xl-image mdc-card__media' },\n                    standalone: true,\n                }]\n        }] });\n/**\n * Avatar image content for a card, intended for use within `<mat-card>`. Can be applied to\n * any media element, such as `<img>` or `<picture>`.\n *\n * This component is an optional convenience for use with other convenience elements, such as\n * `<mat-card-title>`; any custom media element may be used in its place.\n *\n * MatCardAvatar provides no behaviors, instead serving as a purely visual treatment.\n */\nclass MatCardAvatar {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardAvatar, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatCardAvatar, isStandalone: true, selector: \"[mat-card-avatar], [matCardAvatar]\", host: { classAttribute: \"mat-mdc-card-avatar\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardAvatar, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-card-avatar], [matCardAvatar]',\n                    host: { 'class': 'mat-mdc-card-avatar' },\n                    standalone: true,\n                }]\n        }] });\n\nconst CARD_DIRECTIVES = [\n    MatCard,\n    MatCardActions,\n    MatCardAvatar,\n    MatCardContent,\n    MatCardFooter,\n    MatCardHeader,\n    MatCardImage,\n    MatCardLgImage,\n    MatCardMdImage,\n    MatCardSmImage,\n    MatCardSubtitle,\n    MatCardTitle,\n    MatCardTitleGroup,\n    MatCardXlImage,\n];\nclass MatCardModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardModule, imports: [MatCommonModule, CommonModule, MatCard,\n            MatCardActions,\n            MatCardAvatar,\n            MatCardContent,\n            MatCardFooter,\n            MatCardHeader,\n            MatCardImage,\n            MatCardLgImage,\n            MatCardMdImage,\n            MatCardSmImage,\n            MatCardSubtitle,\n            MatCardTitle,\n            MatCardTitleGroup,\n            MatCardXlImage], exports: [MatCard,\n            MatCardActions,\n            MatCardAvatar,\n            MatCardContent,\n            MatCardFooter,\n            MatCardHeader,\n            MatCardImage,\n            MatCardLgImage,\n            MatCardMdImage,\n            MatCardSmImage,\n            MatCardSubtitle,\n            MatCardTitle,\n            MatCardTitleGroup,\n            MatCardXlImage, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardModule, imports: [MatCommonModule, CommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatCardModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, CommonModule, ...CARD_DIRECTIVES],\n                    exports: [CARD_DIRECTIVES, MatCommonModule],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_CARD_CONFIG, MatCard, MatCardActions, MatCardAvatar, MatCardContent, MatCardFooter, MatCardHeader, MatCardImage, MatCardLgImage, MatCardMdImage, MatCardModule, MatCardSmImage, MatCardSubtitle, MatCardTitle, MatCardTitleGroup, MatCardXlImage };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACnJ,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,eAAe,QAAQ,wBAAwB;;AAExD;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AACA,MAAMC,eAAe,GAAG,IAAIhB,cAAc,CAAC,iBAAiB,CAAC;AAC7D;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiB,OAAO,CAAC;EACVC,WAAWA,CAACC,MAAM,EAAE;IAChB,IAAI,CAACC,UAAU,GAAGD,MAAM,EAAEC,UAAU,IAAI,QAAQ;EACpD;EACA;IAAS,IAAI,CAACC,IAAI,YAAAC,gBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFN,OAAO,EAAjBlB,EAAE,CAAAyB,iBAAA,CAAiCR,eAAe;IAAA,CAA4D;EAAE;EAChN;IAAS,IAAI,CAACS,IAAI,kBAD8E1B,EAAE,CAAA2B,iBAAA;MAAAC,IAAA,EACJV,OAAO;MAAAW,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADLlC,EAAE,CAAAoC,WAAA,0BAAAD,GAAA,CAAAd,UAAA,KACW,UAAT,CAAC,uBAAAc,GAAA,CAAAd,UAAA,KAAQ,UAAT,CAAC;QAAA;MAAA;MAAAgB,MAAA;QAAAhB,UAAA;MAAA;MAAAiB,QAAA;MAAAC,UAAA;MAAAC,QAAA,GADLxC,EAAE,CAAAyC,mBAAA;MAAAC,kBAAA,EAAA9B,GAAA;MAAA+B,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,iBAAAZ,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlC,EAAE,CAAA+C,eAAA;UAAF/C,EAAE,CAAAgD,YAAA,EACyV,CAAC;QAAA;MAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAA4wL;EAAE;AAC9sM;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGpD,EAAE,CAAAqD,iBAAA,CAGXnC,OAAO,EAAc,CAAC;IACrGU,IAAI,EAAE1B,SAAS;IACfoD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,UAAU;MAAEC,IAAI,EAAE;QACzB,OAAO,EAAE,uBAAuB;QAChC,+BAA+B,EAAE,2BAA2B;QAC5D,4BAA4B,EAAE;MAClC,CAAC;MAAElB,QAAQ,EAAE,SAAS;MAAEY,aAAa,EAAE/C,iBAAiB,CAACsD,IAAI;MAAEN,eAAe,EAAE/C,uBAAuB,CAACsD,MAAM;MAAEnB,UAAU,EAAE,IAAI;MAAEM,QAAQ,EAAE,6BAA6B;MAAEI,MAAM,EAAE,CAAC,4pLAA4pL;IAAE,CAAC;EAC/1L,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAErB,IAAI,EAAE+B,SAAS;IAAEC,UAAU,EAAE,CAAC;MAC/ChC,IAAI,EAAEvB,MAAM;MACZiD,IAAI,EAAE,CAACrC,eAAe;IAC1B,CAAC,EAAE;MACCW,IAAI,EAAEtB;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEe,UAAU,EAAE,CAAC;MACtCO,IAAI,EAAErB;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMsD,YAAY,CAAC;EACf;IAAS,IAAI,CAACvC,IAAI,YAAAwC,qBAAAtC,CAAA;MAAA,YAAAA,CAAA,IAAwFqC,YAAY;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBA5B8E/D,EAAE,CAAAgE,iBAAA;MAAApC,IAAA,EA4BJiC,YAAY;MAAAhC,SAAA;MAAAC,SAAA;MAAAS,UAAA;IAAA,EAAmJ;EAAE;AACnQ;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KA9BoGpD,EAAE,CAAAqD,iBAAA,CA8BXQ,YAAY,EAAc,CAAC;IAC1GjC,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kDAAkD;MAC5DC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAqB,CAAC;MACvCjB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA,MAAM0B,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAAC3C,IAAI,YAAA4C,0BAAA1C,CAAA;MAAA,YAAAA,CAAA,IAAwFyC,iBAAiB;IAAA,CAAmD;EAAE;EAChL;IAAS,IAAI,CAACvC,IAAI,kBA7C8E1B,EAAE,CAAA2B,iBAAA;MAAAC,IAAA,EA6CJqC,iBAAiB;MAAApC,SAAA;MAAAC,SAAA;MAAAS,UAAA;MAAAC,QAAA,GA7CfxC,EAAE,CAAAyC,mBAAA;MAAAC,kBAAA,EAAA5B,GAAA;MAAA6B,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAsB,2BAAAjC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlC,EAAE,CAAA+C,eAAA,CAAAlC,GAAA;UAAFb,EAAE,CAAAoE,cAAA,SA6CwJ,CAAC;UA7C3JpE,EAAE,CAAAgD,YAAA,EA6CiU,CAAC;UA7CpUhD,EAAE,CAAAqE,YAAA,CA6CyU,CAAC;UA7C5UrE,EAAE,CAAAgD,YAAA,KA6C8oB,CAAC;UA7CjpBhD,EAAE,CAAAgD,YAAA,KA6CyqB,CAAC;QAAA;MAAA;MAAAE,aAAA;MAAAC,eAAA;IAAA,EAAoG;EAAE;AACt3B;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KA/CoGpD,EAAE,CAAAqD,iBAAA,CA+CXY,iBAAiB,EAAc,CAAC;IAC/GrC,IAAI,EAAE1B,SAAS;IACfoD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,sBAAsB;MAAEL,aAAa,EAAE/C,iBAAiB,CAACsD,IAAI;MAAEN,eAAe,EAAE/C,uBAAuB,CAACsD,MAAM;MAAEF,IAAI,EAAE;QAAE,OAAO,EAAE;MAA2B,CAAC;MAAEjB,UAAU,EAAE,IAAI;MAAEM,QAAQ,EAAE;IAA2hB,CAAC;EAC9uB,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyB,cAAc,CAAC;EACjB;IAAS,IAAI,CAAChD,IAAI,YAAAiD,uBAAA/C,CAAA;MAAA,YAAAA,CAAA,IAAwF8C,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACP,IAAI,kBA5D8E/D,EAAE,CAAAgE,iBAAA;MAAApC,IAAA,EA4DJ0C,cAAc;MAAAzC,SAAA;MAAAC,SAAA;MAAAS,UAAA;IAAA,EAAqH;EAAE;AACvO;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KA9DoGpD,EAAE,CAAAqD,iBAAA,CA8DXiB,cAAc,EAAc,CAAC;IAC5G1C,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAuB,CAAC;MACzCjB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMiC,eAAe,CAAC;EAClB;IAAS,IAAI,CAAClD,IAAI,YAAAmD,wBAAAjD,CAAA;MAAA,YAAAA,CAAA,IAAwFgD,eAAe;IAAA,CAAmD;EAAE;EAC9K;IAAS,IAAI,CAACT,IAAI,kBA/E8E/D,EAAE,CAAAgE,iBAAA;MAAApC,IAAA,EA+EJ4C,eAAe;MAAA3C,SAAA;MAAAC,SAAA;MAAAS,UAAA;IAAA,EAA+J;EAAE;AAClR;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAjFoGpD,EAAE,CAAAqD,iBAAA,CAiFXmB,eAAe,EAAc,CAAC;IAC7G5C,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2DAA2D;MACrEC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwB,CAAC;MAC1CjB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMmC,cAAc,CAAC;EACjBvD,WAAWA,CAAA,EAAG;IACV;IACA;IACA;IACA,IAAI,CAACwD,KAAK,GAAG,OAAO;EACxB;EACA;IAAS,IAAI,CAACrD,IAAI,YAAAsD,uBAAApD,CAAA;MAAA,YAAAA,CAAA,IAAwFkD,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACX,IAAI,kBAxG8E/D,EAAE,CAAAgE,iBAAA;MAAApC,IAAA,EAwGJ8C,cAAc;MAAA7C,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAA6C,4BAAA3C,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAxGZlC,EAAE,CAAAoC,WAAA,mCAAAD,GAAA,CAAAwC,KAAA,KAwGM,KAAG,CAAC;QAAA;MAAA;MAAAtC,MAAA;QAAAsC,KAAA;MAAA;MAAArC,QAAA;MAAAC,UAAA;IAAA,EAA8Q;EAAE;AAChY;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KA1GoGpD,EAAE,CAAAqD,iBAAA,CA0GXqB,cAAc,EAAc,CAAC;IAC5G9C,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kBAAkB;MAC5BjB,QAAQ,EAAE,gBAAgB;MAC1BkB,IAAI,EAAE;QACF,OAAO,EAAE,wCAAwC;QACjD,wCAAwC,EAAE;MAC9C,CAAC;MACDjB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,QAAkB;IAAEoC,KAAK,EAAE,CAAC;MACtB/C,IAAI,EAAErB;IACV,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuE,aAAa,CAAC;EAChB;IAAS,IAAI,CAACxD,IAAI,YAAAyD,sBAAAvD,CAAA;MAAA,YAAAA,CAAA,IAAwFsD,aAAa;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACpD,IAAI,kBAlI8E1B,EAAE,CAAA2B,iBAAA;MAAAC,IAAA,EAkIJkD,aAAa;MAAAjD,SAAA;MAAAC,SAAA;MAAAS,UAAA;MAAAC,QAAA,GAlIXxC,EAAE,CAAAyC,mBAAA;MAAAC,kBAAA,EAAA1B,GAAA;MAAA2B,KAAA;MAAAC,IAAA;MAAAoC,MAAA;MAAAnC,QAAA,WAAAoC,uBAAA/C,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlC,EAAE,CAAA+C,eAAA,CAAAhC,GAAA;UAAFf,EAAE,CAAAgD,YAAA,EAkI4M,CAAC;UAlI/MhD,EAAE,CAAAoE,cAAA,YAkIsP,CAAC;UAlIzPpE,EAAE,CAAAgD,YAAA,KAkI+Z,CAAC;UAlIlahD,EAAE,CAAAqE,YAAA,CAkIua,CAAC;UAlI1arE,EAAE,CAAAgD,YAAA,KAkIkc,CAAC;QAAA;MAAA;MAAAE,aAAA;MAAAC,eAAA;IAAA,EAAoG;EAAE;AAC/oB;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KApIoGpD,EAAE,CAAAqD,iBAAA,CAoIXyB,aAAa,EAAc,CAAC;IAC3GlD,IAAI,EAAE1B,SAAS;IACfoD,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,iBAAiB;MAAEL,aAAa,EAAE/C,iBAAiB,CAACsD,IAAI;MAAEN,eAAe,EAAE/C,uBAAuB,CAACsD,MAAM;MAAEF,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB,CAAC;MAAEjB,UAAU,EAAE,IAAI;MAAEM,QAAQ,EAAE;IAAkU,CAAC;EAC3gB,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqC,aAAa,CAAC;EAChB;IAAS,IAAI,CAAC5D,IAAI,YAAA6D,sBAAA3D,CAAA;MAAA,YAAAA,CAAA,IAAwF0D,aAAa;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAACnB,IAAI,kBAjJ8E/D,EAAE,CAAAgE,iBAAA;MAAApC,IAAA,EAiJJsD,aAAa;MAAArD,SAAA;MAAAC,SAAA;MAAAS,UAAA;IAAA,EAAmH;EAAE;AACpO;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAnJoGpD,EAAE,CAAAqD,iBAAA,CAmJX6B,aAAa,EAAc,CAAC;IAC3GtD,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,iBAAiB;MAC3BC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB,CAAC;MACxCjB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6C,YAAY,CAAC;EACf;IAAS,IAAI,CAAC9D,IAAI,YAAA+D,qBAAA7D,CAAA;MAAA,YAAAA,CAAA,IAAwF4D,YAAY;IAAA,CAAmD;EAAE;EAC3K;IAAS,IAAI,CAACrB,IAAI,kBAxK8E/D,EAAE,CAAAgE,iBAAA;MAAApC,IAAA,EAwKJwD,YAAY;MAAAvD,SAAA;MAAAC,SAAA;MAAAS,UAAA;IAAA,EAAmJ;EAAE;AACnQ;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KA1KoGpD,EAAE,CAAAqD,iBAAA,CA0KX+B,YAAY,EAAc,CAAC;IAC1GxD,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,kCAAkC;MAC5CC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAqC,CAAC;MACvDjB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAM+C,cAAc,CAAC;EACjB;IAAS,IAAI,CAAChE,IAAI,YAAAiE,uBAAA/D,CAAA;MAAA,YAAAA,CAAA,IAAwF8D,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACvB,IAAI,kBArL8E/D,EAAE,CAAAgE,iBAAA;MAAApC,IAAA,EAqLJ0D,cAAc;MAAAzD,SAAA;MAAAC,SAAA;MAAAS,UAAA;IAAA,EAA8J;EAAE;AAChR;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAvLoGpD,EAAE,CAAAqD,iBAAA,CAuLXiC,cAAc,EAAc,CAAC;IAC5G1D,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0CAA0C;MACpDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC,CAAC;MAC1DjB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMiD,cAAc,CAAC;EACjB;IAAS,IAAI,CAAClE,IAAI,YAAAmE,uBAAAjE,CAAA;MAAA,YAAAA,CAAA,IAAwFgE,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAACzB,IAAI,kBAlM8E/D,EAAE,CAAAgE,iBAAA;MAAApC,IAAA,EAkMJ4D,cAAc;MAAA3D,SAAA;MAAAC,SAAA;MAAAS,UAAA;IAAA,EAA+J;EAAE;AACjR;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KApMoGpD,EAAE,CAAAqD,iBAAA,CAoMXmC,cAAc,EAAc,CAAC;IAC5G5D,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC,CAAC;MAC1DjB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMmD,cAAc,CAAC;EACjB;IAAS,IAAI,CAACpE,IAAI,YAAAqE,uBAAAnE,CAAA;MAAA,YAAAA,CAAA,IAAwFkE,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAAC3B,IAAI,kBA/M8E/D,EAAE,CAAAgE,iBAAA;MAAApC,IAAA,EA+MJ8D,cAAc;MAAA7D,SAAA;MAAAC,SAAA;MAAAS,UAAA;IAAA,EAA8J;EAAE;AAChR;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAjNoGpD,EAAE,CAAAqD,iBAAA,CAiNXqC,cAAc,EAAc,CAAC;IAC5G9D,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,0CAA0C;MACpDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC,CAAC;MAC1DjB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA,MAAMqD,cAAc,CAAC;EACjB;IAAS,IAAI,CAACtE,IAAI,YAAAuE,uBAAArE,CAAA;MAAA,YAAAA,CAAA,IAAwFoE,cAAc;IAAA,CAAmD;EAAE;EAC7K;IAAS,IAAI,CAAC7B,IAAI,kBA5N8E/D,EAAE,CAAAgE,iBAAA;MAAApC,IAAA,EA4NJgE,cAAc;MAAA/D,SAAA;MAAAC,SAAA;MAAAS,UAAA;IAAA,EAA+J;EAAE;AACjR;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KA9NoGpD,EAAE,CAAAqD,iBAAA,CA8NXuC,cAAc,EAAc,CAAC;IAC5GhE,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,2CAA2C;MACrDC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAwC,CAAC;MAC1DjB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuD,aAAa,CAAC;EAChB;IAAS,IAAI,CAACxE,IAAI,YAAAyE,sBAAAvE,CAAA;MAAA,YAAAA,CAAA,IAAwFsE,aAAa;IAAA,CAAmD;EAAE;EAC5K;IAAS,IAAI,CAAC/B,IAAI,kBAjP8E/D,EAAE,CAAAgE,iBAAA;MAAApC,IAAA,EAiPJkE,aAAa;MAAAjE,SAAA;MAAAC,SAAA;MAAAS,UAAA;IAAA,EAAsI;EAAE;AACvP;AACA;EAAA,QAAAa,SAAA,oBAAAA,SAAA,KAnPoGpD,EAAE,CAAAqD,iBAAA,CAmPXyC,aAAa,EAAc,CAAC;IAC3GlE,IAAI,EAAEpB,SAAS;IACf8C,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAsB,CAAC;MACxCjB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AAEV,MAAMyD,eAAe,GAAG,CACpB9E,OAAO,EACPwD,cAAc,EACdoB,aAAa,EACbxB,cAAc,EACdY,aAAa,EACbJ,aAAa,EACbM,YAAY,EACZM,cAAc,EACdF,cAAc,EACdF,cAAc,EACdd,eAAe,EACfX,YAAY,EACZI,iBAAiB,EACjB2B,cAAc,CACjB;AACD,MAAMK,aAAa,CAAC;EAChB;IAAS,IAAI,CAAC3E,IAAI,YAAA4E,sBAAA1E,CAAA;MAAA,YAAAA,CAAA,IAAwFyE,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBA9Q8EnG,EAAE,CAAAoG,gBAAA;MAAAxE,IAAA,EA8QSqE;IAAa,EA0B7E;EAAE;EAC7C;IAAS,IAAI,CAACI,IAAI,kBAzS8ErG,EAAE,CAAAsG,gBAAA;MAAAC,OAAA,GAySkC5F,eAAe,EAAED,YAAY,EAAEC,eAAe;IAAA,EAAI;EAAE;AAC5L;AACA;EAAA,QAAAyC,SAAA,oBAAAA,SAAA,KA3SoGpD,EAAE,CAAAqD,iBAAA,CA2SX4C,aAAa,EAAc,CAAC;IAC3GrE,IAAI,EAAEnB,QAAQ;IACd6C,IAAI,EAAE,CAAC;MACCiD,OAAO,EAAE,CAAC5F,eAAe,EAAED,YAAY,EAAE,GAAGsF,eAAe,CAAC;MAC5DQ,OAAO,EAAE,CAACR,eAAe,EAAErF,eAAe;IAC9C,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAASM,eAAe,EAAEC,OAAO,EAAEwD,cAAc,EAAEoB,aAAa,EAAExB,cAAc,EAAEY,aAAa,EAAEJ,aAAa,EAAEM,YAAY,EAAEM,cAAc,EAAEF,cAAc,EAAES,aAAa,EAAEX,cAAc,EAAEd,eAAe,EAAEX,YAAY,EAAEI,iBAAiB,EAAE2B,cAAc", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}