{"ast": null, "code": "export const appRoutes = [{\n  path: '',\n  redirectTo: '/dashboard',\n  pathMatch: 'full'\n}, {\n  path: 'auth',\n  loadChildren: () => import('./features/auth/auth.module').then(m => m.AuthModule)\n},\n// 🙋‍♂️ User Dashboard (Utilisateur standard)\n{\n  path: 'dashboard',\n  loadChildren: () => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule)\n  // canActivate: [AuthGuard] // Temporarily disabled\n},\n// 🧑‍💼 Admin Dashboard (Administrateur)\n{\n  path: 'admin',\n  loadChildren: () => import('./features/admin-dashboard/admin-dashboard.module').then(m => m.AdminDashboardModule)\n  // canActivate: [AdminGuard] // Temporarily disabled\n},\n// 🧑‍🏫 Validator Dashboard (Validateur)\n{\n  path: 'validator',\n  loadChildren: () => import('./features/validator-dashboard/validator-dashboard.module').then(m => m.ValidatorDashboardModule)\n  // canActivate: [ValidatorGuard] // Temporarily disabled\n},\n// 🧑‍🍳 Partner Dashboard (Partenaire)\n{\n  path: 'partner',\n  loadChildren: () => import('./features/partner-dashboard/partner-dashboard.module').then(m => m.PartnerDashboardModule)\n  // canActivate: [PartnerGuard] // Temporarily disabled\n},\n// 🧑‍🔧 Provider Dashboard (Prestataire)\n{\n  path: 'provider',\n  loadChildren: () => import('./features/provider-dashboard/provider-dashboard.module').then(m => m.ProviderDashboardModule)\n  // canActivate: [ProviderGuard] // Temporarily disabled\n},\n// Shared features\n{\n  path: 'profile',\n  loadChildren: () => import('./features/profile/profile.module').then(m => m.ProfileModule)\n  // canActivate: [AuthGuard] // Temporarily disabled\n}, {\n  path: 'qr-scanner',\n  loadChildren: () => import('./features/qr-scanner/qr-scanner.module').then(m => m.QrScannerModule)\n  // canActivate: [AuthGuard] // Temporarily disabled\n}, {\n  path: 'rewards',\n  loadChildren: () => import('./features/rewards/rewards.module').then(m => m.RewardsModule)\n  // canActivate: [AuthGuard] // Temporarily disabled\n}, {\n  path: 'unauthorized',\n  loadComponent: () => import('./shared/components/unauthorized/unauthorized.component').then(c => c.UnauthorizedComponent)\n}, {\n  path: '**',\n  loadComponent: () => import('./shared/components/not-found/not-found.component').then(c => c.NotFoundComponent)\n}];", "map": {"version": 3, "names": ["appRoutes", "path", "redirectTo", "pathMatch", "loadChildren", "then", "m", "AuthModule", "DashboardModule", "AdminDashboardModule", "ValidatorDashboardModule", "PartnerDashboardModule", "ProviderDashboardModule", "ProfileModule", "QrScannerModule", "RewardsModule", "loadComponent", "c", "UnauthorizedComponent", "NotFoundComponent"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\app.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { AuthGuard, AdminGuard, ValidatorGuard } from './core/guards/auth.guard';\nimport { UserRole } from './core/models';\n\nexport const appRoutes: Routes = [\n  {\n    path: '',\n    redirectTo: '/dashboard',\n    pathMatch: 'full'\n  },\n  {\n    path: 'auth',\n    loadChildren: () => import('./features/auth/auth.module').then(m => m.AuthModule)\n  },\n  // 🙋‍♂️ User Dashboard (Utilisateur standard)\n  {\n    path: 'dashboard',\n    loadChildren: () => import('./features/dashboard/dashboard.module').then(m => m.DashboardModule)\n    // canActivate: [AuthGuard] // Temporarily disabled\n  },\n\n  // 🧑‍💼 Admin Dashboard (Administrateur)\n  {\n    path: 'admin',\n    loadChildren: () => import('./features/admin-dashboard/admin-dashboard.module').then(m => m.AdminDashboardModule)\n    // canActivate: [AdminGuard] // Temporarily disabled\n  },\n\n  // 🧑‍🏫 Validator Dashboard (Validateur)\n  {\n    path: 'validator',\n    loadChildren: () => import('./features/validator-dashboard/validator-dashboard.module').then(m => m.ValidatorDashboardModule)\n    // canActivate: [ValidatorGuard] // Temporarily disabled\n  },\n\n  // 🧑‍🍳 Partner Dashboard (Partenaire)\n  {\n    path: 'partner',\n    loadChildren: () => import('./features/partner-dashboard/partner-dashboard.module').then(m => m.PartnerDashboardModule)\n    // canActivate: [PartnerGuard] // Temporarily disabled\n  },\n\n  // 🧑‍🔧 Provider Dashboard (Prestataire)\n  {\n    path: 'provider',\n    loadChildren: () => import('./features/provider-dashboard/provider-dashboard.module').then(m => m.ProviderDashboardModule)\n    // canActivate: [ProviderGuard] // Temporarily disabled\n  },\n\n  // Shared features\n  {\n    path: 'profile',\n    loadChildren: () => import('./features/profile/profile.module').then(m => m.ProfileModule)\n    // canActivate: [AuthGuard] // Temporarily disabled\n  },\n  {\n    path: 'qr-scanner',\n    loadChildren: () => import('./features/qr-scanner/qr-scanner.module').then(m => m.QrScannerModule)\n    // canActivate: [AuthGuard] // Temporarily disabled\n  },\n  {\n    path: 'rewards',\n    loadChildren: () => import('./features/rewards/rewards.module').then(m => m.RewardsModule)\n    // canActivate: [AuthGuard] // Temporarily disabled\n  },\n  {\n    path: 'unauthorized',\n    loadComponent: () => import('./shared/components/unauthorized/unauthorized.component').then(c => c.UnauthorizedComponent)\n  },\n  {\n    path: '**',\n    loadComponent: () => import('./shared/components/not-found/not-found.component').then(c => c.NotFoundComponent)\n  }\n];\n"], "mappings": "AAIA,OAAO,MAAMA,SAAS,GAAW,CAC/B;EACEC,IAAI,EAAE,EAAE;EACRC,UAAU,EAAE,YAAY;EACxBC,SAAS,EAAE;CACZ,EACD;EACEF,IAAI,EAAE,MAAM;EACZG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,6BAA6B,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,UAAU;CACjF;AACD;AACA;EACEN,IAAI,EAAE,WAAW;EACjBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,uCAAuC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACE,eAAe;EAC/F;CACD;AAED;AACA;EACEP,IAAI,EAAE,OAAO;EACbG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mDAAmD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACG,oBAAoB;EAChH;CACD;AAED;AACA;EACER,IAAI,EAAE,WAAW;EACjBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,2DAA2D,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACI,wBAAwB;EAC5H;CACD;AAED;AACA;EACET,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,uDAAuD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACK,sBAAsB;EACtH;CACD;AAED;AACA;EACEV,IAAI,EAAE,UAAU;EAChBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,yDAAyD,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACM,uBAAuB;EACzH;CACD;AAED;AACA;EACEX,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACO,aAAa;EACzF;CACD,EACD;EACEZ,IAAI,EAAE,YAAY;EAClBG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,yCAAyC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACQ,eAAe;EACjG;CACD,EACD;EACEb,IAAI,EAAE,SAAS;EACfG,YAAY,EAAEA,CAAA,KAAM,MAAM,CAAC,mCAAmC,CAAC,CAACC,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACS,aAAa;EACzF;CACD,EACD;EACEd,IAAI,EAAE,cAAc;EACpBe,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,yDAAyD,CAAC,CAACX,IAAI,CAACY,CAAC,IAAIA,CAAC,CAACC,qBAAqB;CACzH,EACD;EACEjB,IAAI,EAAE,IAAI;EACVe,aAAa,EAAEA,CAAA,KAAM,MAAM,CAAC,mDAAmD,CAAC,CAACX,IAAI,CAACY,CAAC,IAAIA,CAAC,CAACE,iBAAiB;CAC/G,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}