<div class="app-container">
  <!-- Enhanced main layout -->
  <div class="welcome-container fade-in">
    <mat-toolbar color="primary" class="main-toolbar">
      <div class="logo">🌟</div>
      <span class="toolbar-title">Mo<PERSON><PERSON></span>
      <span class="spacer"></span>

      <!-- Navigation buttons -->
      <ng-container *ngIf="authService.currentUser$ | async as user; else authButtons">
        <button mat-raised-button
                color="accent"
                [routerLink]="getDashboardRoute(user.role)"
                class="nav-button">
          <mat-icon>{{ getDashboardIcon(user.role) }}</mat-icon>
          {{ getDashboardDisplayName(user.role) }}
        </button>
      </ng-container>

      <ng-template #authButtons>
        <button mat-stroked-button
                routerLink="/auth"
                class="nav-button">
          <mat-icon>login</mat-icon>
          Connexion
        </button>
      </ng-template>

      <!-- Points display (only for users) -->
      <div class="points-display" *ngIf="authService.currentUser$ | async as user">
        <ng-container *ngIf="user.role === 'user'">
          <mat-icon>stars</mat-icon>
          <span>{{ user.points }} pts</span>
        </ng-container>
      </div>

      <!-- User menu -->
      <button mat-icon-button
              [matMenuTriggerFor]="userMenu"
              class="user-avatar"
              *ngIf="authService.currentUser$ | async">
        <mat-icon>account_circle</mat-icon>
      </button>

      <mat-menu #userMenu="matMenu" class="user-menu">
        <button mat-menu-item routerLink="/profile">
          <mat-icon>person</mat-icon>
          <span>Profil</span>
        </button>
        <button mat-menu-item (click)="logout()">
          <mat-icon>logout</mat-icon>
          <span>Déconnexion</span>
        </button>
      </mat-menu>
    </mat-toolbar>

    <main class="main-content">
      <div class="content-wrapper slide-up">
        <router-outlet></router-outlet>
      </div>
    </main>

    <!-- Floating Action Button for Quick QR Scan -->
    <button mat-fab
            class="floating-fab"
            color="primary"
            routerLink="/qr-scanner"
            matTooltip="Scanner QR Code"
            *ngIf="authService.currentUser$ | async">
      <mat-icon>qr_code_scanner</mat-icon>
    </button>

    <!-- Role Switcher for Testing -->
    <app-role-switcher></app-role-switcher>
  </div>
</div>
