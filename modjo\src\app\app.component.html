<div class="app-container">
  <!-- Enhanced main layout -->
  <div class="welcome-container fade-in">
    <mat-toolbar color="primary" class="main-toolbar">
      <div class="logo">🌟</div>
      <span class="toolbar-title">Modjo <PERSON></span>
      <span class="spacer"></span>

      <!-- Navigation buttons -->
      <button mat-raised-button
              color="accent"
              routerLink="/dashboard"
              class="nav-button">
        <mat-icon>dashboard</mat-icon>
        Dashboard
      </button>

      <button mat-stroked-button
              routerLink="/auth"
              class="nav-button">
        <mat-icon>login</mat-icon>
        Auth
      </button>

      <!-- Points display (when user is logged in) -->
      <div class="points-display" *ngIf="authService.currentUser$ | async as user">
        <mat-icon>stars</mat-icon>
        <span>{{ user.points }} pts</span>
      </div>

      <!-- User menu -->
      <button mat-icon-button
              [matMenuTriggerFor]="userMenu"
              class="user-avatar"
              *ngIf="authService.currentUser$ | async">
        <mat-icon>account_circle</mat-icon>
      </button>

      <mat-menu #userMenu="matMenu" class="user-menu">
        <button mat-menu-item routerLink="/profile">
          <mat-icon>person</mat-icon>
          <span>Profil</span>
        </button>
        <button mat-menu-item (click)="logout()">
          <mat-icon>logout</mat-icon>
          <span>Déconnexion</span>
        </button>
      </mat-menu>
    </mat-toolbar>

    <main class="main-content">
      <div class="content-wrapper slide-up">
        <router-outlet></router-outlet>
      </div>
    </main>

    <!-- Floating Action Button for Quick QR Scan -->
    <button mat-fab
            class="floating-fab"
            color="primary"
            routerLink="/qr-scanner"
            matTooltip="Scanner QR Code"
            *ngIf="authService.currentUser$ | async">
      <mat-icon>qr_code_scanner</mat-icon>
    </button>
  </div>
</div>
