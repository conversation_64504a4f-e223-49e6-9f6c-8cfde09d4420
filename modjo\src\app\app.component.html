<div class="app-container">
  <!-- Temporarily simplified for initial setup -->
  <div class="welcome-container">
    <mat-toolbar color="primary">
      <span>🌟 Modjo PWA</span>
      <span class="spacer"></span>
      <button mat-button routerLink="/dashboard">Dashboard</button>
      <button mat-button routerLink="/auth">Auth</button>
    </mat-toolbar>

    <main class="main-content">
      <router-outlet></router-outlet>
    </main>
  </div>

  <!-- Original layout (temporarily disabled) -->
  <!-- <mat-sidenav-container class="sidenav-container" *ngIf="(authService.currentUser$ | async) as user; else authLayout">
    <!-- Sidebar -->
    <mat-sidenav #drawer class="sidenav" fixedInViewport
                 [attr.role]="'navigation'"
                 [mode]="'over'"
                 [opened]="false">
      <mat-toolbar class="sidenav-header">
        <img src="assets/logo.png" alt="Modjo" class="logo">
        <span class="app-name">Modjo</span>
      </mat-toolbar>

      <mat-nav-list>
        <a mat-list-item routerLink="/dashboard" routerLinkActive="active" (click)="drawer.close()">
          <mat-icon>dashboard</mat-icon>
          <span>Tableau de bord</span>
        </a>

        <a mat-list-item routerLink="/profile" routerLinkActive="active" (click)="drawer.close()">
          <mat-icon>person</mat-icon>
          <span>Profil</span>
        </a>

        <a mat-list-item routerLink="/qr-scanner" routerLinkActive="active" (click)="drawer.close()">
          <mat-icon>qr_code_scanner</mat-icon>
          <span>Scanner QR</span>
        </a>

        <a mat-list-item routerLink="/rewards" routerLinkActive="active" (click)="drawer.close()">
          <mat-icon>card_giftcard</mat-icon>
          <span>Récompenses</span>
        </a>

        <a mat-list-item routerLink="/validation" routerLinkActive="active"
           *ngIf="user.role === 'validator' || user.role === 'admin'" (click)="drawer.close()">
          <mat-icon>verified</mat-icon>
          <span>Validation</span>
        </a>

        <a mat-list-item routerLink="/admin" routerLinkActive="active"
           *ngIf="user.role === 'admin'" (click)="drawer.close()">
          <mat-icon>admin_panel_settings</mat-icon>
          <span>Administration</span>
        </a>

        <mat-divider></mat-divider>

        <a mat-list-item (click)="logout(); drawer.close()">
          <mat-icon>logout</mat-icon>
          <span>Déconnexion</span>
        </a>
      </mat-nav-list>
    </mat-sidenav>

    <!-- Main content -->
    <mat-sidenav-content>
      <!-- Header -->
      <mat-toolbar color="primary" class="main-toolbar">
        <button type="button" mat-icon-button (click)="drawer.toggle()" class="menu-button">
          <mat-icon>menu</mat-icon>
        </button>

        <span class="toolbar-title">{{ getPageTitle() }}</span>

        <span class="spacer"></span>

        <!-- Points display -->
        <div class="points-display">
          <mat-icon>stars</mat-icon>
          <span>{{ user.points }} pts</span>
        </div>

        <!-- User avatar -->
        <button mat-icon-button [matMenuTriggerFor]="userMenu" class="user-avatar">
          <mat-icon>account_circle</mat-icon>
        </button>

        <mat-menu #userMenu="matMenu">
          <button mat-menu-item routerLink="/profile">
            <mat-icon>person</mat-icon>
            <span>Profil</span>
          </button>
          <button mat-menu-item (click)="logout()">
            <mat-icon>logout</mat-icon>
            <span>Déconnexion</span>
          </button>
        </mat-menu>
      </mat-toolbar>

      <!-- Page content -->
      <!-- <main class="main-content">
        <router-outlet></router-outlet>
      </main>
    </mat-sidenav-content>
  </mat-sidenav-container> -->

  <!-- Auth layout -->
  <!-- <ng-template #authLayout>
    <div class="auth-layout">
      <router-outlet></router-outlet>
    </div>
  </ng-template> -->
</div>
