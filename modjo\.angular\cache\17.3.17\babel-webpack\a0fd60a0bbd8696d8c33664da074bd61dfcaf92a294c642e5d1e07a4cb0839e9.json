{"ast": null, "code": "import * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/common\";\nimport * as i3 from \"@angular/material/button\";\nimport * as i4 from \"@angular/material/card\";\nimport * as i5 from \"@angular/material/icon\";\nfunction PartnerDashboardComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"h1\");\n    i0.ɵɵtext(3, \"\\uD83E\\uDDD1\\u200D\\uD83C\\uDF73 Dashboard Partenaire\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"p\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 3);\n    i0.ɵɵtext(7, \"G\\u00E9rez vos r\\u00E9compenses et suivez vos performances\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 4)(9, \"mat-card\", 5)(10, \"mat-card-content\")(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"card_giftcard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"h3\");\n    i0.ɵɵtext(14, \"Ajouter des R\\u00E9compenses\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"p\");\n    i0.ɵɵtext(16, \"Cr\\u00E9ez et g\\u00E9rez vos offres (valeur, stock, conditions)\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"button\", 6);\n    i0.ɵɵtext(18, \"G\\u00E9rer les R\\u00E9compenses\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(19, \"mat-card\", 5)(20, \"mat-card-content\")(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(23, \"h3\");\n    i0.ɵɵtext(24, \"Statistiques d'Utilisation\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"p\");\n    i0.ɵɵtext(26, \"Suivez combien de fois vos r\\u00E9compenses sont \\u00E9chang\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(27, \"button\", 7);\n    i0.ɵɵtext(28, \"Voir les Stats\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(29, \"mat-card\", 5)(30, \"mat-card-content\")(31, \"mat-icon\");\n    i0.ɵɵtext(32, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(33, \"h3\");\n    i0.ɵɵtext(34, \"Modifier les Offres\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(35, \"p\");\n    i0.ɵɵtext(36, \"Modifiez ou supprimez vos r\\u00E9compenses existantes\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"button\", 8);\n    i0.ɵɵtext(38, \"Modifier les Offres\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r0 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r0.getGreeting(), \", \", ctx_r0.user.name, \"!\");\n  }\n}\nexport class PartnerDashboardComponent {\n  constructor(authService) {\n    this.authService = authService;\n    this.user = null;\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n    });\n  }\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n  static {\n    this.ɵfac = function PartnerDashboardComponent_Factory(t) {\n      return new (t || PartnerDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PartnerDashboardComponent,\n      selectors: [[\"app-partner-dashboard\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"partner-dashboard-container\", 4, \"ngIf\"], [1, \"partner-dashboard-container\"], [1, \"partner-header\"], [1, \"subtitle\"], [1, \"partner-features\"], [1, \"feature-card\"], [\"mat-raised-button\", \"\", \"color\", \"primary\"], [\"mat-raised-button\", \"\", \"color\", \"accent\"], [\"mat-raised-button\", \"\", \"color\", \"warn\"]],\n      template: function PartnerDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, PartnerDashboardComponent_div_0_Template, 39, 2, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n        }\n      },\n      dependencies: [i2.NgIf, i3.MatButton, i4.MatCard, i4.MatCardContent, i5.MatIcon],\n      styles: [\".partner-dashboard-container[_ngcontent-%COMP%] { padding: 20px; }\\n    .partner-header[_ngcontent-%COMP%] { text-align: center; margin-bottom: 40px; }\\n    .partner-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] { color: #2d3748; font-size: 2.5rem; margin-bottom: 16px; }\\n    .subtitle[_ngcontent-%COMP%] { color: #718096; font-size: 1.1rem; }\\n    .partner-features[_ngcontent-%COMP%] { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; }\\n    .feature-card[_ngcontent-%COMP%] { text-align: center; padding: 24px; }\\n    .feature-card[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] { font-size: 3rem; color: #667eea; margin-bottom: 16px; }\\n    .feature-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] { color: #2d3748; margin-bottom: 12px; }\\n    .feature-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] { color: #718096; margin-bottom: 20px; }\\n  \\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInBhcnRuZXItZGFzaGJvYXJkLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiO0lBQ0ksK0JBQStCLGFBQWEsRUFBRTtJQUM5QyxrQkFBa0Isa0JBQWtCLEVBQUUsbUJBQW1CLEVBQUU7SUFDM0QscUJBQXFCLGNBQWMsRUFBRSxpQkFBaUIsRUFBRSxtQkFBbUIsRUFBRTtJQUM3RSxZQUFZLGNBQWMsRUFBRSxpQkFBaUIsRUFBRTtJQUMvQyxvQkFBb0IsYUFBYSxFQUFFLDJEQUEyRCxFQUFFLFNBQVMsRUFBRTtJQUMzRyxnQkFBZ0Isa0JBQWtCLEVBQUUsYUFBYSxFQUFFO0lBQ25ELHlCQUF5QixlQUFlLEVBQUUsY0FBYyxFQUFFLG1CQUFtQixFQUFFO0lBQy9FLG1CQUFtQixjQUFjLEVBQUUsbUJBQW1CLEVBQUU7SUFDeEQsa0JBQWtCLGNBQWMsRUFBRSxtQkFBbUIsRUFBRSIsImZpbGUiOiJwYXJ0bmVyLWRhc2hib2FyZC5jb21wb25lbnQudHMiLCJzb3VyY2VzQ29udGVudCI6WyJcbiAgICAucGFydG5lci1kYXNoYm9hcmQtY29udGFpbmVyIHsgcGFkZGluZzogMjBweDsgfVxuICAgIC5wYXJ0bmVyLWhlYWRlciB7IHRleHQtYWxpZ246IGNlbnRlcjsgbWFyZ2luLWJvdHRvbTogNDBweDsgfVxuICAgIC5wYXJ0bmVyLWhlYWRlciBoMSB7IGNvbG9yOiAjMmQzNzQ4OyBmb250LXNpemU6IDIuNXJlbTsgbWFyZ2luLWJvdHRvbTogMTZweDsgfVxuICAgIC5zdWJ0aXRsZSB7IGNvbG9yOiAjNzE4MDk2OyBmb250LXNpemU6IDEuMXJlbTsgfVxuICAgIC5wYXJ0bmVyLWZlYXR1cmVzIHsgZGlzcGxheTogZ3JpZDsgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgzMDBweCwgMWZyKSk7IGdhcDogMjRweDsgfVxuICAgIC5mZWF0dXJlLWNhcmQgeyB0ZXh0LWFsaWduOiBjZW50ZXI7IHBhZGRpbmc6IDI0cHg7IH1cbiAgICAuZmVhdHVyZS1jYXJkIG1hdC1pY29uIHsgZm9udC1zaXplOiAzcmVtOyBjb2xvcjogIzY2N2VlYTsgbWFyZ2luLWJvdHRvbTogMTZweDsgfVxuICAgIC5mZWF0dXJlLWNhcmQgaDMgeyBjb2xvcjogIzJkMzc0ODsgbWFyZ2luLWJvdHRvbTogMTJweDsgfVxuICAgIC5mZWF0dXJlLWNhcmQgcCB7IGNvbG9yOiAjNzE4MDk2OyBtYXJnaW4tYm90dG9tOiAyMHB4OyB9XG4gICJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvcGFydG5lci1kYXNoYm9hcmQvY29tcG9uZW50cy9wYXJ0bmVyLWRhc2hib2FyZC9wYXJ0bmVyLWRhc2hib2FyZC5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJLCtCQUErQixhQUFhLEVBQUU7SUFDOUMsa0JBQWtCLGtCQUFrQixFQUFFLG1CQUFtQixFQUFFO0lBQzNELHFCQUFxQixjQUFjLEVBQUUsaUJBQWlCLEVBQUUsbUJBQW1CLEVBQUU7SUFDN0UsWUFBWSxjQUFjLEVBQUUsaUJBQWlCLEVBQUU7SUFDL0Msb0JBQW9CLGFBQWEsRUFBRSwyREFBMkQsRUFBRSxTQUFTLEVBQUU7SUFDM0csZ0JBQWdCLGtCQUFrQixFQUFFLGFBQWEsRUFBRTtJQUNuRCx5QkFBeUIsZUFBZSxFQUFFLGNBQWMsRUFBRSxtQkFBbUIsRUFBRTtJQUMvRSxtQkFBbUIsY0FBYyxFQUFFLG1CQUFtQixFQUFFO0lBQ3hELGtCQUFrQixjQUFjLEVBQUUsbUJBQW1CLEVBQUU7O0FBRTNELGdoREFBZ2hEIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLnBhcnRuZXItZGFzaGJvYXJkLWNvbnRhaW5lciB7IHBhZGRpbmc6IDIwcHg7IH1cbiAgICAucGFydG5lci1oZWFkZXIgeyB0ZXh0LWFsaWduOiBjZW50ZXI7IG1hcmdpbi1ib3R0b206IDQwcHg7IH1cbiAgICAucGFydG5lci1oZWFkZXIgaDEgeyBjb2xvcjogIzJkMzc0ODsgZm9udC1zaXplOiAyLjVyZW07IG1hcmdpbi1ib3R0b206IDE2cHg7IH1cbiAgICAuc3VidGl0bGUgeyBjb2xvcjogIzcxODA5NjsgZm9udC1zaXplOiAxLjFyZW07IH1cbiAgICAucGFydG5lci1mZWF0dXJlcyB7IGRpc3BsYXk6IGdyaWQ7IGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMzAwcHgsIDFmcikpOyBnYXA6IDI0cHg7IH1cbiAgICAuZmVhdHVyZS1jYXJkIHsgdGV4dC1hbGlnbjogY2VudGVyOyBwYWRkaW5nOiAyNHB4OyB9XG4gICAgLmZlYXR1cmUtY2FyZCBtYXQtaWNvbiB7IGZvbnQtc2l6ZTogM3JlbTsgY29sb3I6ICM2NjdlZWE7IG1hcmdpbi1ib3R0b206IDE2cHg7IH1cbiAgICAuZmVhdHVyZS1jYXJkIGgzIHsgY29sb3I6ICMyZDM3NDg7IG1hcmdpbi1ib3R0b206IDEycHg7IH1cbiAgICAuZmVhdHVyZS1jYXJkIHAgeyBjb2xvcjogIzcxODA5NjsgbWFyZ2luLWJvdHRvbTogMjBweDsgfVxuICAiXSwic291cmNlUm9vdCI6IiJ9 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate2", "ctx_r0", "getGreeting", "user", "name", "PartnerDashboardComponent", "constructor", "authService", "ngOnInit", "currentUser$", "subscribe", "hour", "Date", "getHours", "ɵɵdirectiveInject", "i1", "AuthService", "selectors", "decls", "vars", "consts", "template", "PartnerDashboardComponent_Template", "rf", "ctx", "ɵɵtemplate", "PartnerDashboardComponent_div_0_Template", "ɵɵproperty"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\partner-dashboard\\components\\partner-dashboard\\partner-dashboard.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { User, UserRole } from '../../../../core/models';\n\n@Component({\n  selector: 'app-partner-dashboard',\n  template: `\n    <div class=\"partner-dashboard-container\" *ngIf=\"user\">\n      <div class=\"partner-header\">\n        <h1>🧑‍🍳 Dashboard Partenaire</h1>\n        <p>{{ getGreeting() }}, {{ user.name }}!</p>\n        <p class=\"subtitle\">G<PERSON>rez vos récompenses et suivez vos performances</p>\n      </div>\n      \n      <div class=\"partner-features\">\n        <mat-card class=\"feature-card\">\n          <mat-card-content>\n            <mat-icon>card_giftcard</mat-icon>\n            <h3>Ajouter des Récompenses</h3>\n            <p>Créez et gérez vos offres (valeur, stock, conditions)</p>\n            <button mat-raised-button color=\"primary\">G<PERSON><PERSON> les Récompenses</button>\n          </mat-card-content>\n        </mat-card>\n        \n        <mat-card class=\"feature-card\">\n          <mat-card-content>\n            <mat-icon>analytics</mat-icon>\n            <h3>Statistiques d'Utilisation</h3>\n            <p>Suivez combien de fois vos récompenses sont échangées</p>\n            <button mat-raised-button color=\"accent\">Voir les Stats</button>\n          </mat-card-content>\n        </mat-card>\n        \n        <mat-card class=\"feature-card\">\n          <mat-card-content>\n            <mat-icon>edit</mat-icon>\n            <h3>Modifier les Offres</h3>\n            <p>Modifiez ou supprimez vos récompenses existantes</p>\n            <button mat-raised-button color=\"warn\">Modifier les Offres</button>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .partner-dashboard-container { padding: 20px; }\n    .partner-header { text-align: center; margin-bottom: 40px; }\n    .partner-header h1 { color: #2d3748; font-size: 2.5rem; margin-bottom: 16px; }\n    .subtitle { color: #718096; font-size: 1.1rem; }\n    .partner-features { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; }\n    .feature-card { text-align: center; padding: 24px; }\n    .feature-card mat-icon { font-size: 3rem; color: #667eea; margin-bottom: 16px; }\n    .feature-card h3 { color: #2d3748; margin-bottom: 12px; }\n    .feature-card p { color: #718096; margin-bottom: 20px; }\n  `]\n})\nexport class PartnerDashboardComponent implements OnInit {\n  user: User | null = null;\n\n  constructor(private authService: AuthService) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n    });\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n}\n"], "mappings": ";;;;;;;;IASQA,EAFJ,CAAAC,cAAA,aAAsD,aACxB,SACtB;IAAAD,EAAA,CAAAE,MAAA,0DAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5CH,EAAA,CAAAC,cAAA,WAAoB;IAAAD,EAAA,CAAAE,MAAA,iEAAgD;IACtEF,EADsE,CAAAG,YAAA,EAAI,EACpE;IAKAH,EAHN,CAAAC,cAAA,aAA8B,kBACG,wBACX,gBACN;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,oCAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,uEAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5DH,EAAA,CAAAC,cAAA,iBAA0C;IAAAD,EAAA,CAAAE,MAAA,uCAAqB;IAEnEF,EAFmE,CAAAG,YAAA,EAAS,EACvD,EACV;IAIPH,EAFJ,CAAAC,cAAA,mBAA+B,wBACX,gBACN;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,kCAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,4EAAqD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC5DH,EAAA,CAAAC,cAAA,iBAAyC;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAE3DF,EAF2D,CAAAG,YAAA,EAAS,EAC/C,EACV;IAIPH,EAFJ,CAAAC,cAAA,mBAA+B,wBACX,gBACN;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAC,cAAA,UAAI;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,6DAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvDH,EAAA,CAAAC,cAAA,iBAAuC;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAIlEF,EAJkE,CAAAG,YAAA,EAAS,EAClD,EACV,EACP,EACF;;;;IAhCCH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAC,WAAA,UAAAD,MAAA,CAAAE,IAAA,CAAAC,IAAA,MAAqC;;;AA8ChD,OAAM,MAAOC,yBAAyB;EAGpCC,YAAoBC,WAAwB;IAAxB,KAAAA,WAAW,GAAXA,WAAW;IAF/B,KAAAJ,IAAI,GAAgB,IAAI;EAEuB;EAE/CK,QAAQA,CAAA;IACN,IAAI,CAACD,WAAW,CAACE,YAAY,CAACC,SAAS,CAACP,IAAI,IAAG;MAC7C,IAAI,CAACA,IAAI,GAAGA,IAAI;IAClB,CAAC,CAAC;EACJ;EAEAD,WAAWA,CAAA;IACT,MAAMS,IAAI,GAAG,IAAIC,IAAI,EAAE,CAACC,QAAQ,EAAE;IAClC,IAAIF,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,SAAS;EAClB;;;uBAhBWN,yBAAyB,EAAAV,EAAA,CAAAmB,iBAAA,CAAAC,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAzBX,yBAAyB;MAAAY,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAjDlC5B,EAAA,CAAA8B,UAAA,IAAAC,wCAAA,kBAAsD;;;UAAZ/B,EAAA,CAAAgC,UAAA,SAAAH,GAAA,CAAArB,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}