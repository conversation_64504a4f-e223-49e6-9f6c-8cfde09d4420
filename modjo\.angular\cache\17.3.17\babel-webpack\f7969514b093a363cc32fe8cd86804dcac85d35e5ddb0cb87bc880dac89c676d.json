{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { UserRole } from '../../../../core/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nimport * as i13 from \"@angular/material/checkbox\";\nimport * as i14 from \"@angular/material/progress-spinner\";\nfunction RegisterComponent_mat_option_39_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 25);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const city_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", city_r1.value);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", city_r1.label, \" \");\n  }\n}\nfunction RegisterComponent_mat_option_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 25)(1, \"div\", 26)(2, \"span\", 27);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 28);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const role_r2 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", role_r2.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(role_r2.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(role_r2.description);\n  }\n}\nfunction RegisterComponent_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 29);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getFieldError(\"acceptTerms\"), \" \");\n  }\n}\nfunction RegisterComponent_mat_spinner_77_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 30);\n  }\n}\nfunction RegisterComponent_span_78_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Cr\\u00E9er mon compte\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class RegisterComponent {\n  constructor(fb, authService, router, snackBar) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.hideConfirmPassword = true;\n    this.cities = [{\n      value: 'Monastir',\n      label: 'Monastir'\n    }, {\n      value: 'Sousse',\n      label: 'Sousse'\n    }];\n    this.roles = [{\n      value: UserRole.USER,\n      label: 'Utilisateur',\n      description: 'Participer aux activités communautaires'\n    }, {\n      value: UserRole.PROVIDER,\n      label: 'Partenaire',\n      description: 'Offrir des récompenses et services'\n    }, {\n      value: UserRole.VALIDATOR,\n      label: 'Validateur',\n      description: 'Valider les actions communautaires'\n    }];\n    this.registerForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', [Validators.pattern(/^[0-9]{8}$/)]],\n      city: ['', Validators.required],\n      role: [UserRole.USER, Validators.required],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', Validators.required],\n      acceptTerms: [false, Validators.requiredTrue]\n    }, {\n      validators: this.passwordMatchValidator\n    });\n  }\n  ngOnInit() {\n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n  passwordMatchValidator(form) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({\n        passwordMismatch: true\n      });\n    } else if (confirmPassword?.errors?.['passwordMismatch']) {\n      delete confirmPassword.errors['passwordMismatch'];\n      if (Object.keys(confirmPassword.errors).length === 0) {\n        confirmPassword.setErrors(null);\n      }\n    }\n    return null;\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.registerForm.invalid) {\n        _this.markFormGroupTouched();\n        return;\n      }\n      _this.isLoading = true;\n      const formValue = _this.registerForm.value;\n      try {\n        yield _this.authService.register({\n          email: formValue.email,\n          password: formValue.password,\n          name: formValue.name,\n          phone: formValue.phone,\n          city: formValue.city,\n          role: formValue.role\n        });\n        _this.snackBar.open('Compte créé avec succès!', 'Fermer', {\n          duration: 3000\n        });\n        _this.router.navigate(['/dashboard']);\n      } catch (error) {\n        console.error('Registration error:', error);\n        _this.snackBar.open(_this.getErrorMessage(error), 'Fermer', {\n          duration: 5000\n        });\n      } finally {\n        _this.isLoading = false;\n      }\n    })();\n  }\n  getErrorMessage(error) {\n    if (error.code) {\n      switch (error.code) {\n        case 'auth/email-already-in-use':\n          return 'Cette adresse email est déjà utilisée.';\n        case 'auth/invalid-email':\n          return 'Adresse email invalide.';\n        case 'auth/weak-password':\n          return 'Le mot de passe est trop faible.';\n        case 'auth/operation-not-allowed':\n          return 'Création de compte désactivée.';\n        default:\n          return 'Erreur lors de la création du compte.';\n      }\n    }\n    return 'Erreur lors de la création du compte.';\n  }\n  markFormGroupTouched() {\n    Object.keys(this.registerForm.controls).forEach(key => {\n      const control = this.registerForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getFieldError(fieldName) {\n    const field = this.registerForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) {\n        return this.getRequiredMessage(fieldName);\n      }\n      if (field.errors['email']) {\n        return 'Format email invalide';\n      }\n      if (field.errors['minlength']) {\n        return fieldName === 'password' ? 'Mot de passe trop court (min. 6 caractères)' : 'Nom trop court (min. 2 caractères)';\n      }\n      if (field.errors['pattern']) {\n        return 'Numéro de téléphone invalide (8 chiffres)';\n      }\n      if (field.errors['passwordMismatch']) {\n        return 'Les mots de passe ne correspondent pas';\n      }\n      if (field.errors['requiredTrue']) {\n        return 'Vous devez accepter les conditions';\n      }\n    }\n    return '';\n  }\n  getRequiredMessage(fieldName) {\n    const messages = {\n      name: 'Nom requis',\n      email: 'Email requis',\n      city: 'Ville requise',\n      role: 'Rôle requis',\n      password: 'Mot de passe requis',\n      confirmPassword: 'Confirmation requise',\n      acceptTerms: 'Acceptation requise'\n    };\n    return messages[fieldName] || 'Champ requis';\n  }\n  static {\n    this.ɵfac = function RegisterComponent_Factory(t) {\n      return new (t || RegisterComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RegisterComponent,\n      selectors: [[\"app-register\"]],\n      decls: 84,\n      vars: 18,\n      consts: [[1, \"register-container\"], [1, \"register-card\"], [1, \"register-header\"], [\"src\", \"assets/logo.png\", \"alt\", \"Modjo\", 1, \"logo\"], [1, \"register-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"text\", \"formControlName\", \"name\", \"placeholder\", \"Votre nom complet\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"matInput\", \"\", \"type\", \"tel\", \"formControlName\", \"phone\", \"placeholder\", \"12345678\"], [\"formControlName\", \"city\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"formControlName\", \"role\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Votre mot de passe\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"matInput\", \"\", \"formControlName\", \"confirmPassword\", \"placeholder\", \"Confirmez votre mot de passe\", 3, \"type\"], [1, \"terms-section\"], [\"formControlName\", \"acceptTerms\", 1, \"terms-checkbox\"], [\"href\", \"#\", 1, \"link\"], [\"class\", \"error-message\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"register-button\", 3, \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"login-link\"], [\"routerLink\", \"/auth/login\", 1, \"link\"], [3, \"value\"], [1, \"role-option\"], [1, \"role-label\"], [1, \"role-description\"], [1, \"error-message\"], [\"diameter\", \"20\"]],\n      template: function RegisterComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"div\", 2);\n          i0.ɵɵelement(4, \"img\", 3);\n          i0.ɵɵelementStart(5, \"h1\");\n          i0.ɵɵtext(6, \"Rejoignez Modjo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(7, \"p\");\n          i0.ɵɵtext(8, \"Cr\\u00E9ez votre compte pour commencer\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(9, \"mat-card-content\")(10, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function RegisterComponent_Template_form_ngSubmit_10_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(11, \"mat-form-field\", 5)(12, \"mat-label\");\n          i0.ɵɵtext(13, \"Nom complet\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(14, \"input\", 6);\n          i0.ɵɵelementStart(15, \"mat-icon\", 7);\n          i0.ɵɵtext(16, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(17, \"mat-error\");\n          i0.ɵɵtext(18);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(19, \"mat-form-field\", 5)(20, \"mat-label\");\n          i0.ɵɵtext(21, \"Adresse email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(22, \"input\", 8);\n          i0.ɵɵelementStart(23, \"mat-icon\", 7);\n          i0.ɵɵtext(24, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(25, \"mat-error\");\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"mat-form-field\", 5)(28, \"mat-label\");\n          i0.ɵɵtext(29, \"T\\u00E9l\\u00E9phone (optionnel)\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(30, \"input\", 9);\n          i0.ɵɵelementStart(31, \"mat-icon\", 7);\n          i0.ɵɵtext(32, \"phone\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(33, \"mat-error\");\n          i0.ɵɵtext(34);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(35, \"mat-form-field\", 5)(36, \"mat-label\");\n          i0.ɵɵtext(37, \"Ville\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(38, \"mat-select\", 10);\n          i0.ɵɵtemplate(39, RegisterComponent_mat_option_39_Template, 2, 2, \"mat-option\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"mat-error\");\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(42, \"mat-form-field\", 5)(43, \"mat-label\");\n          i0.ɵɵtext(44, \"Type de compte\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(45, \"mat-select\", 12);\n          i0.ɵɵtemplate(46, RegisterComponent_mat_option_46_Template, 6, 3, \"mat-option\", 11);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"mat-error\");\n          i0.ɵɵtext(48);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(49, \"mat-form-field\", 5)(50, \"mat-label\");\n          i0.ɵɵtext(51, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(52, \"input\", 13);\n          i0.ɵɵelementStart(53, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_53_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(54, \"mat-icon\");\n          i0.ɵɵtext(55);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(56, \"mat-error\");\n          i0.ɵɵtext(57);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(58, \"mat-form-field\", 5)(59, \"mat-label\");\n          i0.ɵɵtext(60, \"Confirmer le mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(61, \"input\", 15);\n          i0.ɵɵelementStart(62, \"button\", 14);\n          i0.ɵɵlistener(\"click\", function RegisterComponent_Template_button_click_62_listener() {\n            return ctx.hideConfirmPassword = !ctx.hideConfirmPassword;\n          });\n          i0.ɵɵelementStart(63, \"mat-icon\");\n          i0.ɵɵtext(64);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(65, \"mat-error\");\n          i0.ɵɵtext(66);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(67, \"div\", 16)(68, \"mat-checkbox\", 17);\n          i0.ɵɵtext(69, \" J'accepte les \");\n          i0.ɵɵelementStart(70, \"a\", 18);\n          i0.ɵɵtext(71, \"conditions d'utilisation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(72, \" et la \");\n          i0.ɵɵelementStart(73, \"a\", 18);\n          i0.ɵɵtext(74, \"politique de confidentialit\\u00E9\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(75, RegisterComponent_div_75_Template, 2, 1, \"div\", 19);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(76, \"button\", 20);\n          i0.ɵɵtemplate(77, RegisterComponent_mat_spinner_77_Template, 1, 0, \"mat-spinner\", 21)(78, RegisterComponent_span_78_Template, 2, 0, \"span\", 22);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(79, \"div\", 23)(80, \"p\");\n          i0.ɵɵtext(81, \"D\\u00E9j\\u00E0 un compte? \");\n          i0.ɵɵelementStart(82, \"a\", 24);\n          i0.ɵɵtext(83, \"Se connecter\");\n          i0.ɵɵelementEnd()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(10);\n          i0.ɵɵproperty(\"formGroup\", ctx.registerForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"name\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"email\"));\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"phone\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.cities);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"city\"));\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.roles);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"role\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"password\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hideConfirmPassword ? \"password\" : \"text\");\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.hideConfirmPassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"confirmPassword\"));\n          i0.ɵɵadvance(9);\n          i0.ɵɵproperty(\"ngIf\", ctx.getFieldError(\"acceptTerms\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgForOf, i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatButton, i9.MatIconButton, i10.MatIcon, i11.MatSelect, i12.MatOption, i13.MatCheckbox, i14.MatProgressSpinner],\n      styles: [\".register-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  padding: 16px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.register-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 500px;\\n  padding: 24px;\\n  border-radius: 16px;\\n  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);\\n  max-height: 90vh;\\n  overflow-y: auto;\\n}\\n\\n.register-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 24px;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  width: 64px;\\n  height: 64px;\\n  margin-bottom: 16px;\\n}\\n\\n.register-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n  font-size: 2rem;\\n  font-weight: 600;\\n}\\n\\n.register-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.register-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.role-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  padding: 4px 0;\\n}\\n\\n.role-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.role-description[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n  margin-top: 2px;\\n}\\n\\n.terms-section[_ngcontent-%COMP%] {\\n  margin: 8px 0;\\n}\\n\\n.terms-checkbox[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.error-message[_ngcontent-%COMP%] {\\n  color: #f44336;\\n  font-size: 0.75rem;\\n  margin-top: 4px;\\n}\\n\\n.register-button[_ngcontent-%COMP%] {\\n  height: 48px;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  margin-top: 8px;\\n}\\n\\n.login-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 16px;\\n}\\n\\n.login-link[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .register-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  \\n  .register-card[_ngcontent-%COMP%] {\\n    padding: 16px;\\n    max-height: 95vh;\\n  }\\n  \\n  .logo[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  \\n  .register-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  \\n  .register-form[_ngcontent-%COMP%] {\\n    gap: 12px;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "UserRole", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "city_r1", "value", "ɵɵadvance", "ɵɵtextInterpolate1", "label", "role_r2", "ɵɵtextInterpolate", "description", "ctx_r2", "getFieldError", "ɵɵelement", "RegisterComponent", "constructor", "fb", "authService", "router", "snackBar", "isLoading", "hidePassword", "hideConfirmPassword", "cities", "roles", "USER", "PROVIDER", "VALIDATOR", "registerForm", "group", "name", "required", "<PERSON><PERSON><PERSON><PERSON>", "email", "phone", "pattern", "city", "role", "password", "confirmPassword", "acceptTerms", "requiredTrue", "validators", "passwordMatchValidator", "ngOnInit", "isAuthenticated", "navigate", "form", "get", "setErrors", "passwordMismatch", "errors", "Object", "keys", "length", "onSubmit", "_this", "_asyncToGenerator", "invalid", "markFormGroupTouched", "formValue", "register", "open", "duration", "error", "console", "getErrorMessage", "code", "controls", "for<PERSON>ach", "key", "control", "<PERSON><PERSON><PERSON><PERSON>ched", "fieldName", "field", "touched", "getRequiredMessage", "messages", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "RegisterComponent_Template", "rf", "ctx", "ɵɵlistener", "RegisterComponent_Template_form_ngSubmit_10_listener", "ɵɵtemplate", "RegisterComponent_mat_option_39_Template", "RegisterComponent_mat_option_46_Template", "RegisterComponent_Template_button_click_53_listener", "RegisterComponent_Template_button_click_62_listener", "RegisterComponent_div_75_Template", "RegisterComponent_mat_spinner_77_Template", "RegisterComponent_span_78_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\auth\\components\\register\\register.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\auth\\components\\register\\register.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { UserRole } from '../../../../core/models';\n\n@Component({\n  selector: 'app-register',\n  templateUrl: './register.component.html',\n  styleUrls: ['./register.component.css']\n})\nexport class RegisterComponent implements OnInit {\n  registerForm: FormGroup;\n  isLoading = false;\n  hidePassword = true;\n  hideConfirmPassword = true;\n\n  cities = [\n    { value: 'Monastir', label: 'Monastir' },\n    { value: 'Sousse', label: 'Sousse' }\n  ];\n\n  roles = [\n    { value: UserRole.USER, label: 'Utilisateur', description: 'Participer aux activités communautaires' },\n    { value: UserRole.PROVIDER, label: 'Partenaire', description: 'Offrir des récompenses et services' },\n    { value: UserRole.VALIDATOR, label: 'Validateur', description: 'Valider les actions communautaires' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private snackBar: MatSnackBar\n  ) {\n    this.registerForm = this.fb.group({\n      name: ['', [Validators.required, Validators.minLength(2)]],\n      email: ['', [Validators.required, Validators.email]],\n      phone: ['', [Validators.pattern(/^[0-9]{8}$/)]],\n      city: ['', Validators.required],\n      role: [UserRole.USER, Validators.required],\n      password: ['', [Validators.required, Validators.minLength(6)]],\n      confirmPassword: ['', Validators.required],\n      acceptTerms: [false, Validators.requiredTrue]\n    }, { validators: this.passwordMatchValidator });\n  }\n\n  ngOnInit(): void {\n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate(['/dashboard']);\n    }\n  }\n\n  passwordMatchValidator(form: FormGroup) {\n    const password = form.get('password');\n    const confirmPassword = form.get('confirmPassword');\n    \n    if (password && confirmPassword && password.value !== confirmPassword.value) {\n      confirmPassword.setErrors({ passwordMismatch: true });\n    } else if (confirmPassword?.errors?.['passwordMismatch']) {\n      delete confirmPassword.errors['passwordMismatch'];\n      if (Object.keys(confirmPassword.errors).length === 0) {\n        confirmPassword.setErrors(null);\n      }\n    }\n    return null;\n  }\n\n  async onSubmit(): Promise<void> {\n    if (this.registerForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.isLoading = true;\n    const formValue = this.registerForm.value;\n\n    try {\n      await this.authService.register({\n        email: formValue.email,\n        password: formValue.password,\n        name: formValue.name,\n        phone: formValue.phone,\n        city: formValue.city,\n        role: formValue.role\n      });\n\n      this.snackBar.open('Compte créé avec succès!', 'Fermer', { duration: 3000 });\n      this.router.navigate(['/dashboard']);\n    } catch (error: any) {\n      console.error('Registration error:', error);\n      this.snackBar.open(\n        this.getErrorMessage(error), \n        'Fermer', \n        { duration: 5000 }\n      );\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  private getErrorMessage(error: any): string {\n    if (error.code) {\n      switch (error.code) {\n        case 'auth/email-already-in-use':\n          return 'Cette adresse email est déjà utilisée.';\n        case 'auth/invalid-email':\n          return 'Adresse email invalide.';\n        case 'auth/weak-password':\n          return 'Le mot de passe est trop faible.';\n        case 'auth/operation-not-allowed':\n          return 'Création de compte désactivée.';\n        default:\n          return 'Erreur lors de la création du compte.';\n      }\n    }\n    return 'Erreur lors de la création du compte.';\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.registerForm.controls).forEach(key => {\n      const control = this.registerForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.registerForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) {\n        return this.getRequiredMessage(fieldName);\n      }\n      if (field.errors['email']) {\n        return 'Format email invalide';\n      }\n      if (field.errors['minlength']) {\n        return fieldName === 'password' ? \n          'Mot de passe trop court (min. 6 caractères)' : \n          'Nom trop court (min. 2 caractères)';\n      }\n      if (field.errors['pattern']) {\n        return 'Numéro de téléphone invalide (8 chiffres)';\n      }\n      if (field.errors['passwordMismatch']) {\n        return 'Les mots de passe ne correspondent pas';\n      }\n      if (field.errors['requiredTrue']) {\n        return 'Vous devez accepter les conditions';\n      }\n    }\n    return '';\n  }\n\n  private getRequiredMessage(fieldName: string): string {\n    const messages: { [key: string]: string } = {\n      name: 'Nom requis',\n      email: 'Email requis',\n      city: 'Ville requise',\n      role: 'Rôle requis',\n      password: 'Mot de passe requis',\n      confirmPassword: 'Confirmation requise',\n      acceptTerms: 'Acceptation requise'\n    };\n    return messages[fieldName] || 'Champ requis';\n  }\n}\n", "<div class=\"register-container\">\n  <mat-card class=\"register-card\">\n    <mat-card-header>\n      <div class=\"register-header\">\n        <img src=\"assets/logo.png\" alt=\"Modjo\" class=\"logo\">\n        <h1><PERSON><PERSON><PERSON><PERSON>jo</h1>\n        <p>C<PERSON>ez votre compte pour commencer</p>\n      </div>\n    </mat-card-header>\n\n    <mat-card-content>\n      <form [formGroup]=\"registerForm\" (ngSubmit)=\"onSubmit()\" class=\"register-form\">\n        <!-- Name field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Nom complet</mat-label>\n          <input matInput \n                 type=\"text\" \n                 formControlName=\"name\"\n                 placeholder=\"Votre nom complet\">\n          <mat-icon matSuffix>person</mat-icon>\n          <mat-error>{{ getFieldError('name') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Email field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Adresse email</mat-label>\n          <input matInput \n                 type=\"email\" \n                 formControlName=\"email\"\n                 placeholder=\"<EMAIL>\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error>{{ getFieldError('email') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Phone field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Téléphone (optionnel)</mat-label>\n          <input matInput \n                 type=\"tel\" \n                 formControlName=\"phone\"\n                 placeholder=\"12345678\">\n          <mat-icon matSuffix>phone</mat-icon>\n          <mat-error>{{ getFieldError('phone') }}</mat-error>\n        </mat-form-field>\n\n        <!-- City field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Ville</mat-label>\n          <mat-select formControlName=\"city\">\n            <mat-option *ngFor=\"let city of cities\" [value]=\"city.value\">\n              {{ city.label }}\n            </mat-option>\n          </mat-select>\n          <mat-error>{{ getFieldError('city') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Role field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Type de compte</mat-label>\n          <mat-select formControlName=\"role\">\n            <mat-option *ngFor=\"let role of roles\" [value]=\"role.value\">\n              <div class=\"role-option\">\n                <span class=\"role-label\">{{ role.label }}</span>\n                <span class=\"role-description\">{{ role.description }}</span>\n              </div>\n            </mat-option>\n          </mat-select>\n          <mat-error>{{ getFieldError('role') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Password field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Mot de passe</mat-label>\n          <input matInput \n                 [type]=\"hidePassword ? 'password' : 'text'\"\n                 formControlName=\"password\"\n                 placeholder=\"Votre mot de passe\">\n          <button mat-icon-button \n                  matSuffix \n                  type=\"button\"\n                  (click)=\"hidePassword = !hidePassword\">\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError('password') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Confirm Password field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Confirmer le mot de passe</mat-label>\n          <input matInput \n                 [type]=\"hideConfirmPassword ? 'password' : 'text'\"\n                 formControlName=\"confirmPassword\"\n                 placeholder=\"Confirmez votre mot de passe\">\n          <button mat-icon-button \n                  matSuffix \n                  type=\"button\"\n                  (click)=\"hideConfirmPassword = !hideConfirmPassword\">\n            <mat-icon>{{ hideConfirmPassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError('confirmPassword') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Terms checkbox -->\n        <div class=\"terms-section\">\n          <mat-checkbox formControlName=\"acceptTerms\" class=\"terms-checkbox\">\n            J'accepte les <a href=\"#\" class=\"link\">conditions d'utilisation</a> \n            et la <a href=\"#\" class=\"link\">politique de confidentialité</a>\n          </mat-checkbox>\n          <div class=\"error-message\" *ngIf=\"getFieldError('acceptTerms')\">\n            {{ getFieldError('acceptTerms') }}\n          </div>\n        </div>\n\n        <!-- Submit button -->\n        <button mat-raised-button \n                color=\"primary\" \n                type=\"submit\"\n                class=\"full-width register-button\"\n                [disabled]=\"isLoading\">\n          <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n          <span *ngIf=\"!isLoading\">Créer mon compte</span>\n        </button>\n\n        <!-- Login link -->\n        <div class=\"login-link\">\n          <p>Déjà un compte? \n            <a routerLink=\"/auth/login\" class=\"link\">Se connecter</a>\n          </p>\n        </div>\n      </form>\n    </mat-card-content>\n  </mat-card>\n</div>\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AAInE,SAASC,QAAQ,QAAQ,yBAAyB;;;;;;;;;;;;;;;;;;IC4CtCC,EAAA,CAAAC,cAAA,qBAA6D;IAC3DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAa;;;;IAF2BH,EAAA,CAAAI,UAAA,UAAAC,OAAA,CAAAC,KAAA,CAAoB;IAC1DN,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAH,OAAA,CAAAI,KAAA,MACF;;;;;IAWIT,EAFJ,CAAAC,cAAA,qBAA4D,cACjC,eACE;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,cAAA,eAA+B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAEzDF,EAFyD,CAAAG,YAAA,EAAO,EACxD,EACK;;;;IAL0BH,EAAA,CAAAI,UAAA,UAAAM,OAAA,CAAAJ,KAAA,CAAoB;IAE9BN,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAW,iBAAA,CAAAD,OAAA,CAAAD,KAAA,CAAgB;IACVT,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAW,iBAAA,CAAAD,OAAA,CAAAE,WAAA,CAAsB;;;;;IA6C3DZ,EAAA,CAAAC,cAAA,cAAgE;IAC9DD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAM;;;;IADJH,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAAQ,kBAAA,MAAAK,MAAA,CAAAC,aAAA,qBACF;;;;;IASAd,EAAA,CAAAe,SAAA,sBAA2D;;;;;IAC3Df,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,4BAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;AD5G1D,OAAM,MAAOa,iBAAiB;EAiB5BC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,QAAqB;IAHrB,KAAAH,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAnBlB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,mBAAmB,GAAG,IAAI;IAE1B,KAAAC,MAAM,GAAG,CACP;MAAEnB,KAAK,EAAE,UAAU;MAAEG,KAAK,EAAE;IAAU,CAAE,EACxC;MAAEH,KAAK,EAAE,QAAQ;MAAEG,KAAK,EAAE;IAAQ,CAAE,CACrC;IAED,KAAAiB,KAAK,GAAG,CACN;MAAEpB,KAAK,EAAEP,QAAQ,CAAC4B,IAAI;MAAElB,KAAK,EAAE,aAAa;MAAEG,WAAW,EAAE;IAAyC,CAAE,EACtG;MAAEN,KAAK,EAAEP,QAAQ,CAAC6B,QAAQ;MAAEnB,KAAK,EAAE,YAAY;MAAEG,WAAW,EAAE;IAAoC,CAAE,EACpG;MAAEN,KAAK,EAAEP,QAAQ,CAAC8B,SAAS;MAAEpB,KAAK,EAAE,YAAY;MAAEG,WAAW,EAAE;IAAoC,CAAE,CACtG;IAQC,IAAI,CAACkB,YAAY,GAAG,IAAI,CAACZ,EAAE,CAACa,KAAK,CAAC;MAChCC,IAAI,EAAE,CAAC,EAAE,EAAE,CAAClC,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACoC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC1DC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACrC,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACqC,KAAK,CAAC,CAAC;MACpDC,KAAK,EAAE,CAAC,EAAE,EAAE,CAACtC,UAAU,CAACuC,OAAO,CAAC,YAAY,CAAC,CAAC,CAAC;MAC/CC,IAAI,EAAE,CAAC,EAAE,EAAExC,UAAU,CAACmC,QAAQ,CAAC;MAC/BM,IAAI,EAAE,CAACxC,QAAQ,CAAC4B,IAAI,EAAE7B,UAAU,CAACmC,QAAQ,CAAC;MAC1CO,QAAQ,EAAE,CAAC,EAAE,EAAE,CAAC1C,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACoC,SAAS,CAAC,CAAC,CAAC,CAAC,CAAC;MAC9DO,eAAe,EAAE,CAAC,EAAE,EAAE3C,UAAU,CAACmC,QAAQ,CAAC;MAC1CS,WAAW,EAAE,CAAC,KAAK,EAAE5C,UAAU,CAAC6C,YAAY;KAC7C,EAAE;MAAEC,UAAU,EAAE,IAAI,CAACC;IAAsB,CAAE,CAAC;EACjD;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,IAAI,CAAC3B,WAAW,CAAC4B,eAAe,EAAE,EAAE;MACtC,IAAI,CAAC3B,MAAM,CAAC4B,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;EAExC;EAEAH,sBAAsBA,CAACI,IAAe;IACpC,MAAMT,QAAQ,GAAGS,IAAI,CAACC,GAAG,CAAC,UAAU,CAAC;IACrC,MAAMT,eAAe,GAAGQ,IAAI,CAACC,GAAG,CAAC,iBAAiB,CAAC;IAEnD,IAAIV,QAAQ,IAAIC,eAAe,IAAID,QAAQ,CAAClC,KAAK,KAAKmC,eAAe,CAACnC,KAAK,EAAE;MAC3EmC,eAAe,CAACU,SAAS,CAAC;QAAEC,gBAAgB,EAAE;MAAI,CAAE,CAAC;KACtD,MAAM,IAAIX,eAAe,EAAEY,MAAM,GAAG,kBAAkB,CAAC,EAAE;MACxD,OAAOZ,eAAe,CAACY,MAAM,CAAC,kBAAkB,CAAC;MACjD,IAAIC,MAAM,CAACC,IAAI,CAACd,eAAe,CAACY,MAAM,CAAC,CAACG,MAAM,KAAK,CAAC,EAAE;QACpDf,eAAe,CAACU,SAAS,CAAC,IAAI,CAAC;;;IAGnC,OAAO,IAAI;EACb;EAEMM,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,IAAID,KAAI,CAAC5B,YAAY,CAAC8B,OAAO,EAAE;QAC7BF,KAAI,CAACG,oBAAoB,EAAE;QAC3B;;MAGFH,KAAI,CAACpC,SAAS,GAAG,IAAI;MACrB,MAAMwC,SAAS,GAAGJ,KAAI,CAAC5B,YAAY,CAACxB,KAAK;MAEzC,IAAI;QACF,MAAMoD,KAAI,CAACvC,WAAW,CAAC4C,QAAQ,CAAC;UAC9B5B,KAAK,EAAE2B,SAAS,CAAC3B,KAAK;UACtBK,QAAQ,EAAEsB,SAAS,CAACtB,QAAQ;UAC5BR,IAAI,EAAE8B,SAAS,CAAC9B,IAAI;UACpBI,KAAK,EAAE0B,SAAS,CAAC1B,KAAK;UACtBE,IAAI,EAAEwB,SAAS,CAACxB,IAAI;UACpBC,IAAI,EAAEuB,SAAS,CAACvB;SACjB,CAAC;QAEFmB,KAAI,CAACrC,QAAQ,CAAC2C,IAAI,CAAC,0BAA0B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC5EP,KAAI,CAACtC,MAAM,CAAC4B,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;OACrC,CAAC,OAAOkB,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,qBAAqB,EAAEA,KAAK,CAAC;QAC3CR,KAAI,CAACrC,QAAQ,CAAC2C,IAAI,CAChBN,KAAI,CAACU,eAAe,CAACF,KAAK,CAAC,EAC3B,QAAQ,EACR;UAAED,QAAQ,EAAE;QAAI,CAAE,CACnB;OACF,SAAS;QACRP,KAAI,CAACpC,SAAS,GAAG,KAAK;;IACvB;EACH;EAEQ8C,eAAeA,CAACF,KAAU;IAChC,IAAIA,KAAK,CAACG,IAAI,EAAE;MACd,QAAQH,KAAK,CAACG,IAAI;QAChB,KAAK,2BAA2B;UAC9B,OAAO,wCAAwC;QACjD,KAAK,oBAAoB;UACvB,OAAO,yBAAyB;QAClC,KAAK,oBAAoB;UACvB,OAAO,kCAAkC;QAC3C,KAAK,4BAA4B;UAC/B,OAAO,gCAAgC;QACzC;UACE,OAAO,uCAAuC;;;IAGpD,OAAO,uCAAuC;EAChD;EAEQR,oBAAoBA,CAAA;IAC1BP,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzB,YAAY,CAACwC,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACpD,MAAMC,OAAO,GAAG,IAAI,CAAC3C,YAAY,CAACoB,GAAG,CAACsB,GAAG,CAAC;MAC1CC,OAAO,EAAEC,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEA5D,aAAaA,CAAC6D,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAC9C,YAAY,CAACoB,GAAG,CAACyB,SAAS,CAAC;IAC9C,IAAIC,KAAK,EAAEvB,MAAM,IAAIuB,KAAK,CAACC,OAAO,EAAE;MAClC,IAAID,KAAK,CAACvB,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,IAAI,CAACyB,kBAAkB,CAACH,SAAS,CAAC;;MAE3C,IAAIC,KAAK,CAACvB,MAAM,CAAC,OAAO,CAAC,EAAE;QACzB,OAAO,uBAAuB;;MAEhC,IAAIuB,KAAK,CAACvB,MAAM,CAAC,WAAW,CAAC,EAAE;QAC7B,OAAOsB,SAAS,KAAK,UAAU,GAC7B,6CAA6C,GAC7C,oCAAoC;;MAExC,IAAIC,KAAK,CAACvB,MAAM,CAAC,SAAS,CAAC,EAAE;QAC3B,OAAO,2CAA2C;;MAEpD,IAAIuB,KAAK,CAACvB,MAAM,CAAC,kBAAkB,CAAC,EAAE;QACpC,OAAO,wCAAwC;;MAEjD,IAAIuB,KAAK,CAACvB,MAAM,CAAC,cAAc,CAAC,EAAE;QAChC,OAAO,oCAAoC;;;IAG/C,OAAO,EAAE;EACX;EAEQyB,kBAAkBA,CAACH,SAAiB;IAC1C,MAAMI,QAAQ,GAA8B;MAC1C/C,IAAI,EAAE,YAAY;MAClBG,KAAK,EAAE,cAAc;MACrBG,IAAI,EAAE,eAAe;MACrBC,IAAI,EAAE,aAAa;MACnBC,QAAQ,EAAE,qBAAqB;MAC/BC,eAAe,EAAE,sBAAsB;MACvCC,WAAW,EAAE;KACd;IACD,OAAOqC,QAAQ,CAACJ,SAAS,CAAC,IAAI,cAAc;EAC9C;;;uBAzJW3D,iBAAiB,EAAAhB,EAAA,CAAAgF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAlF,EAAA,CAAAgF,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAApF,EAAA,CAAAgF,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAtF,EAAA,CAAAgF,iBAAA,CAAAO,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAjBxE,iBAAiB;MAAAyE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,2BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTxB/F,EAHN,CAAAC,cAAA,aAAgC,kBACE,sBACb,aACc;UAC3BD,EAAA,CAAAe,SAAA,aAAoD;UACpDf,EAAA,CAAAC,cAAA,SAAI;UAAAD,EAAA,CAAAE,MAAA,sBAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,6CAAiC;UAExCF,EAFwC,CAAAG,YAAA,EAAI,EACpC,EACU;UAGhBH,EADF,CAAAC,cAAA,uBAAkB,eAC+D;UAA9CD,EAAA,CAAAiG,UAAA,sBAAAC,qDAAA;YAAA,OAAYF,GAAA,CAAAvC,QAAA,EAAU;UAAA,EAAC;UAGpDzD,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAe,SAAA,gBAGuC;UACvCf,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,cAAM;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACrCH,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,IAA2B;UACxCF,EADwC,CAAAG,YAAA,EAAY,EACnC;UAIfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,qBAAa;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACpCH,EAAA,CAAAe,SAAA,gBAGqC;UACrCf,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,IAA4B;UACzCF,EADyC,CAAAG,YAAA,EAAY,EACpC;UAIfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,uCAAqB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5CH,EAAA,CAAAe,SAAA,gBAG8B;UAC9Bf,EAAA,CAAAC,cAAA,mBAAoB;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACpCH,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,IAA4B;UACzCF,EADyC,CAAAG,YAAA,EAAY,EACpC;UAIfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAC5BH,EAAA,CAAAC,cAAA,sBAAmC;UACjCD,EAAA,CAAAmG,UAAA,KAAAC,wCAAA,yBAA6D;UAG/DpG,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,IAA2B;UACxCF,EADwC,CAAAG,YAAA,EAAY,EACnC;UAIfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,sBAAc;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACrCH,EAAA,CAAAC,cAAA,sBAAmC;UACjCD,EAAA,CAAAmG,UAAA,KAAAE,wCAAA,yBAA4D;UAM9DrG,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,IAA2B;UACxCF,EADwC,CAAAG,YAAA,EAAY,EACnC;UAIfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACnCH,EAAA,CAAAe,SAAA,iBAGwC;UACxCf,EAAA,CAAAC,cAAA,kBAG+C;UAAvCD,EAAA,CAAAiG,UAAA,mBAAAK,oDAAA;YAAA,OAAAN,GAAA,CAAAzE,YAAA,IAAAyE,GAAA,CAAAzE,YAAA;UAAA,EAAsC;UAC5CvB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAAoD;UAChEF,EADgE,CAAAG,YAAA,EAAW,EAClE;UACTH,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,IAA+B;UAC5CF,EAD4C,CAAAG,YAAA,EAAY,EACvC;UAIfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,iCAAyB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAChDH,EAAA,CAAAe,SAAA,iBAGkD;UAClDf,EAAA,CAAAC,cAAA,kBAG6D;UAArDD,EAAA,CAAAiG,UAAA,mBAAAM,oDAAA;YAAA,OAAAP,GAAA,CAAAxE,mBAAA,IAAAwE,GAAA,CAAAxE,mBAAA;UAAA,EAAoD;UAC1DxB,EAAA,CAAAC,cAAA,gBAAU;UAAAD,EAAA,CAAAE,MAAA,IAA2D;UACvEF,EADuE,CAAAG,YAAA,EAAW,EACzE;UACTH,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,IAAsC;UACnDF,EADmD,CAAAG,YAAA,EAAY,EAC9C;UAIfH,EADF,CAAAC,cAAA,eAA2B,wBAC0C;UACjED,EAAA,CAAAE,MAAA,uBAAc;UAAAF,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAE,MAAA,gCAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAI;UACnEH,EAAA,CAAAE,MAAA,eAAM;UAAAF,EAAA,CAAAC,cAAA,aAAyB;UAAAD,EAAA,CAAAE,MAAA,yCAA4B;UAC7DF,EAD6D,CAAAG,YAAA,EAAI,EAClD;UACfH,EAAA,CAAAmG,UAAA,KAAAK,iCAAA,kBAAgE;UAGlExG,EAAA,CAAAG,YAAA,EAAM;UAGNH,EAAA,CAAAC,cAAA,kBAI+B;UAE7BD,EADA,CAAAmG,UAAA,KAAAM,yCAAA,0BAA6C,KAAAC,kCAAA,mBACpB;UAC3B1G,EAAA,CAAAG,YAAA,EAAS;UAIPH,EADF,CAAAC,cAAA,eAAwB,SACnB;UAAAD,EAAA,CAAAE,MAAA,kCACD;UAAAF,EAAA,CAAAC,cAAA,aAAyC;UAAAD,EAAA,CAAAE,MAAA,oBAAY;UAMjEF,EANiE,CAAAG,YAAA,EAAI,EACvD,EACA,EACD,EACU,EACV,EACP;;;UAzHMH,EAAA,CAAAO,SAAA,IAA0B;UAA1BP,EAAA,CAAAI,UAAA,cAAA4F,GAAA,CAAAlE,YAAA,CAA0B;UASjB9B,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAAW,iBAAA,CAAAqF,GAAA,CAAAlF,aAAA,SAA2B;UAW3Bd,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAW,iBAAA,CAAAqF,GAAA,CAAAlF,aAAA,UAA4B;UAW5Bd,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAW,iBAAA,CAAAqF,GAAA,CAAAlF,aAAA,UAA4B;UAORd,EAAA,CAAAO,SAAA,GAAS;UAATP,EAAA,CAAAI,UAAA,YAAA4F,GAAA,CAAAvE,MAAA,CAAS;UAI7BzB,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAAW,iBAAA,CAAAqF,GAAA,CAAAlF,aAAA,SAA2B;UAOPd,EAAA,CAAAO,SAAA,GAAQ;UAARP,EAAA,CAAAI,UAAA,YAAA4F,GAAA,CAAAtE,KAAA,CAAQ;UAO5B1B,EAAA,CAAAO,SAAA,GAA2B;UAA3BP,EAAA,CAAAW,iBAAA,CAAAqF,GAAA,CAAAlF,aAAA,SAA2B;UAO/Bd,EAAA,CAAAO,SAAA,GAA2C;UAA3CP,EAAA,CAAAI,UAAA,SAAA4F,GAAA,CAAAzE,YAAA,uBAA2C;UAOtCvB,EAAA,CAAAO,SAAA,GAAoD;UAApDP,EAAA,CAAAW,iBAAA,CAAAqF,GAAA,CAAAzE,YAAA,mCAAoD;UAErDvB,EAAA,CAAAO,SAAA,GAA+B;UAA/BP,EAAA,CAAAW,iBAAA,CAAAqF,GAAA,CAAAlF,aAAA,aAA+B;UAOnCd,EAAA,CAAAO,SAAA,GAAkD;UAAlDP,EAAA,CAAAI,UAAA,SAAA4F,GAAA,CAAAxE,mBAAA,uBAAkD;UAO7CxB,EAAA,CAAAO,SAAA,GAA2D;UAA3DP,EAAA,CAAAW,iBAAA,CAAAqF,GAAA,CAAAxE,mBAAA,mCAA2D;UAE5DxB,EAAA,CAAAO,SAAA,GAAsC;UAAtCP,EAAA,CAAAW,iBAAA,CAAAqF,GAAA,CAAAlF,aAAA,oBAAsC;UASrBd,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAI,UAAA,SAAA4F,GAAA,CAAAlF,aAAA,gBAAkC;UAUxDd,EAAA,CAAAO,SAAA,EAAsB;UAAtBP,EAAA,CAAAI,UAAA,aAAA4F,GAAA,CAAA1E,SAAA,CAAsB;UACAtB,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAI,UAAA,SAAA4F,GAAA,CAAA1E,SAAA,CAAe;UACpCtB,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAI,UAAA,UAAA4F,GAAA,CAAA1E,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}