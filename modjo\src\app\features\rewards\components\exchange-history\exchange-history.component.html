<div class="exchange-history-container">
  <!-- Header -->
  <div class="history-header slide-up">
    <h1>📋 Historique des échanges</h1>
    <p>Consultez vos récompenses échangées</p>
  </div>

  <!-- Statistics -->
  <div class="stats-section fade-in" *ngIf="exchanges$ | async as exchanges">
    <div class="stats-grid">
      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon" style="color: #4caf50;">redeem</mat-icon>
            <div class="stat-info">
              <h3>{{ exchanges.length }}</h3>
              <p>Échanges totaux</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon" style="color: #ff9800;">stars</mat-icon>
            <div class="stat-info">
              <h3>{{ getTotalPointsSpent(exchanges) }}</h3>
              <p>Points dépensés</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon" style="color: #2196f3;">check_circle</mat-icon>
            <div class="stat-info">
              <h3>{{ getExchangesByStatus(exchanges, ExchangeStatus.CONFIRMED).length }}</h3>
              <p>Disponibles</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>

      <mat-card class="stat-card">
        <mat-card-content>
          <div class="stat-content">
            <mat-icon class="stat-icon" style="color: #9c27b0;">done_all</mat-icon>
            <div class="stat-info">
              <h3>{{ getExchangesByStatus(exchanges, ExchangeStatus.USED).length }}</h3>
              <p>Utilisées</p>
            </div>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Exchanges list -->
  <div class="exchanges-section" *ngIf="exchanges$ | async as exchanges">
    <div class="exchanges-list" *ngIf="exchanges.length > 0; else noExchanges">
      <mat-card *ngFor="let exchange of exchanges; let i = index" 
                class="exchange-card floating-card"
                [style.animation-delay]="(i * 0.1) + 's'">
        
        <!-- Exchange header -->
        <mat-card-header>
          <div class="exchange-header">
            <div class="exchange-title">
              <h3>Récompense échangée</h3>
              <mat-chip [color]="getStatusColor(exchange.status)" selected>
                <mat-icon>{{ getStatusIcon(exchange.status) }}</mat-icon>
                {{ getStatusLabel(exchange.status) }}
              </mat-chip>
            </div>
            
            <div class="exchange-points">
              <mat-icon>stars</mat-icon>
              <span>{{ exchange.pointsSpent }} pts</span>
            </div>
          </div>
        </mat-card-header>

        <!-- Exchange content -->
        <mat-card-content>
          <div class="exchange-details">
            <!-- Exchange code -->
            <div class="detail-row" *ngIf="canUseExchange(exchange)">
              <div class="exchange-code">
                <mat-icon>qr_code</mat-icon>
                <div class="code-info">
                  <label>Code d'échange</label>
                  <div class="code-value">
                    <span class="code">{{ exchange.exchangeCode }}</span>
                    <button mat-icon-button 
                            (click)="copyExchangeCode(exchange.exchangeCode)"
                            matTooltip="Copier le code">
                      <mat-icon>content_copy</mat-icon>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            <!-- Exchange date -->
            <div class="detail-row">
              <mat-icon>schedule</mat-icon>
              <div>
                <label>Date d'échange</label>
                <span>{{ formatDate(exchange.exchangedAt) }}</span>
              </div>
            </div>

            <!-- Valid until -->
            <div class="detail-row" *ngIf="exchange.validUntil">
              <mat-icon [style.color]="isExpired(exchange) ? '#f44336' : '#4caf50'">
                {{ isExpired(exchange) ? 'event_busy' : 'event_available' }}
              </mat-icon>
              <div>
                <label>Valide jusqu'au</label>
                <span [class.expired]="isExpired(exchange)">
                  {{ formatDate(exchange.validUntil) }}
                </span>
              </div>
            </div>

            <!-- Used date -->
            <div class="detail-row" *ngIf="exchange.usedAt">
              <mat-icon style="color: #9c27b0;">done_all</mat-icon>
              <div>
                <label>Utilisé le</label>
                <span>{{ formatDate(exchange.usedAt) }}</span>
              </div>
            </div>

            <!-- Partner info -->
            <div class="detail-row">
              <mat-icon>store</mat-icon>
              <div>
                <label>Partenaire</label>
                <span>Partenaire local</span>
              </div>
            </div>
          </div>
        </mat-card-content>

        <!-- Exchange actions -->
        <mat-card-actions *ngIf="canUseExchange(exchange)">
          <div class="exchange-actions">
            <div class="usage-instructions">
              <mat-icon>info</mat-icon>
              <span>Présentez ce code au partenaire pour utiliser votre récompense</span>
            </div>
          </div>
        </mat-card-actions>
      </mat-card>
    </div>

    <!-- Empty state -->
    <ng-template #noExchanges>
      <div class="empty-state">
        <mat-card class="empty-card">
          <mat-card-content>
            <div class="empty-content">
              <mat-icon class="empty-icon">redeem</mat-icon>
              <h3>Aucun échange pour le moment</h3>
              <p>Vous n'avez pas encore échangé de récompenses. Explorez notre catalogue pour découvrir des offres exclusives!</p>
              <button mat-raised-button color="primary" routerLink="/rewards">
                <mat-icon>card_giftcard</mat-icon>
                Découvrir les récompenses
              </button>
            </div>
          </mat-card-content>
        </mat-card>
      </div>
    </ng-template>
  </div>

  <!-- Loading state -->
  <div class="loading-container" *ngIf="!(exchanges$ | async)">
    <mat-card class="loading-card">
      <mat-card-content>
        <div class="loading-content">
          <mat-spinner diameter="48"></mat-spinner>
          <h3>Chargement de l'historique...</h3>
        </div>
      </mat-card-content>
    </mat-card>
  </div>
</div>
