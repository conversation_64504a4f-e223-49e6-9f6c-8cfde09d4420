{"ast": null, "code": "import { __decorate } from \"tslib\";\nimport { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n// Angular Material\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatStepperModule } from '@angular/material/stepper';\n// Components\nimport { NotFoundComponent } from './components/not-found/not-found.component';\nimport { UnauthorizedComponent } from './components/unauthorized/unauthorized.component';\n// Pipes\nimport { TimeAgoPipe } from './pipes/time-ago.pipe';\nconst MATERIAL_MODULES = [MatButtonModule, MatCardModule, MatIconModule, MatInputModule, MatFormFieldModule, MatSnackBarModule, MatProgressSpinnerModule, MatDialogModule, MatMenuModule, MatToolbarModule, MatSidenavModule, MatListModule, MatTabsModule, MatSelectModule, MatCheckboxModule, MatRadioModule, MatDatepickerModule, MatNativeDateModule, MatSlideToggleModule, MatProgressBarModule, MatChipsModule, MatBadgeModule, MatTooltipModule, MatExpansionModule, MatStepperModule];\nlet SharedModule = class SharedModule {};\nSharedModule = __decorate([NgModule({\n  declarations: [NotFoundComponent, UnauthorizedComponent, TimeAgoPipe],\n  imports: [CommonModule, RouterModule, ReactiveFormsModule, FormsModule, ...MATERIAL_MODULES],\n  exports: [CommonModule, RouterModule, ReactiveFormsModule, FormsModule, ...MATERIAL_MODULES, NotFoundComponent, UnauthorizedComponent, TimeAgoPipe]\n})], SharedModule);\nexport { SharedModule };", "map": {"version": 3, "names": ["NgModule", "CommonModule", "RouterModule", "ReactiveFormsModule", "FormsModule", "MatButtonModule", "MatCardModule", "MatIconModule", "MatInputModule", "MatFormFieldModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatDialogModule", "MatMenuModule", "MatToolbarModule", "MatSidenavModule", "MatListModule", "MatTabsModule", "MatSelectModule", "MatCheckboxModule", "MatRadioModule", "MatDatepickerModule", "MatNativeDateModule", "MatSlideToggleModule", "MatProgressBarModule", "MatChipsModule", "MatBadgeModule", "MatTooltipModule", "MatExpansionModule", "MatStepperModule", "NotFoundComponent", "UnauthorizedComponent", "TimeAgoPipe", "MATERIAL_MODULES", "SharedModule", "__decorate", "declarations", "imports", "exports"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\shared\\shared.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\nimport { ReactiveFormsModule, FormsModule } from '@angular/forms';\n\n// Angular Material\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatCardModule } from '@angular/material/card';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatToolbarModule } from '@angular/material/toolbar';\nimport { MatSidenavModule } from '@angular/material/sidenav';\nimport { MatListModule } from '@angular/material/list';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatCheckboxModule } from '@angular/material/checkbox';\nimport { MatRadioModule } from '@angular/material/radio';\nimport { MatDatepickerModule } from '@angular/material/datepicker';\nimport { MatNativeDateModule } from '@angular/material/core';\nimport { MatSlideToggleModule } from '@angular/material/slide-toggle';\nimport { MatProgressBarModule } from '@angular/material/progress-bar';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatExpansionModule } from '@angular/material/expansion';\nimport { MatStepperModule } from '@angular/material/stepper';\n\n// Components\nimport { NotFoundComponent } from './components/not-found/not-found.component';\nimport { UnauthorizedComponent } from './components/unauthorized/unauthorized.component';\n\n// Pipes\nimport { TimeAgoPipe } from './pipes/time-ago.pipe';\n\nconst MATERIAL_MODULES = [\n  MatButtonModule,\n  MatCardModule,\n  MatIconModule,\n  MatInputModule,\n  MatFormFieldModule,\n  MatSnackBarModule,\n  MatProgressSpinnerModule,\n  MatDialogModule,\n  MatMenuModule,\n  MatToolbarModule,\n  MatSidenavModule,\n  MatListModule,\n  MatTabsModule,\n  MatSelectModule,\n  MatCheckboxModule,\n  MatRadioModule,\n  MatDatepickerModule,\n  MatNativeDateModule,\n  MatSlideToggleModule,\n  MatProgressBarModule,\n  MatChipsModule,\n  MatBadgeModule,\n  MatTooltipModule,\n  MatExpansionModule,\n  MatStepperModule\n];\n\n@NgModule({\n  declarations: [\n    NotFoundComponent,\n    UnauthorizedComponent,\n    TimeAgoPipe\n  ],\n  imports: [\n    CommonModule,\n    RouterModule,\n    ReactiveFormsModule,\n    FormsModule,\n    ...MATERIAL_MODULES\n  ],\n  exports: [\n    CommonModule,\n    RouterModule,\n    ReactiveFormsModule,\n    FormsModule,\n    ...MATERIAL_MODULES,\n    NotFoundComponent,\n    UnauthorizedComponent,\n    TimeAgoPipe\n  ]\n})\nexport class SharedModule { }\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,eAAe;AACxC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,mBAAmB,EAAEC,WAAW,QAAQ,gBAAgB;AAEjE;AACA,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AACjE,SAASC,iBAAiB,QAAQ,6BAA6B;AAC/D,SAASC,wBAAwB,QAAQ,oCAAoC;AAC7E,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,iBAAiB,QAAQ,4BAA4B;AAC9D,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,mBAAmB,QAAQ,8BAA8B;AAClE,SAASC,mBAAmB,QAAQ,wBAAwB;AAC5D,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,oBAAoB,QAAQ,gCAAgC;AACrE,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,cAAc,QAAQ,yBAAyB;AACxD,SAASC,gBAAgB,QAAQ,2BAA2B;AAC5D,SAASC,kBAAkB,QAAQ,6BAA6B;AAChE,SAASC,gBAAgB,QAAQ,2BAA2B;AAE5D;AACA,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,qBAAqB,QAAQ,kDAAkD;AAExF;AACA,SAASC,WAAW,QAAQ,uBAAuB;AAEnD,MAAMC,gBAAgB,GAAG,CACvB5B,eAAe,EACfC,aAAa,EACbC,aAAa,EACbC,cAAc,EACdC,kBAAkB,EAClBC,iBAAiB,EACjBC,wBAAwB,EACxBC,eAAe,EACfC,aAAa,EACbC,gBAAgB,EAChBC,gBAAgB,EAChBC,aAAa,EACbC,aAAa,EACbC,eAAe,EACfC,iBAAiB,EACjBC,cAAc,EACdC,mBAAmB,EACnBC,mBAAmB,EACnBC,oBAAoB,EACpBC,oBAAoB,EACpBC,cAAc,EACdC,cAAc,EACdC,gBAAgB,EAChBC,kBAAkB,EAClBC,gBAAgB,CACjB;AA0BM,IAAMK,YAAY,GAAlB,MAAMA,YAAY,GAAI;AAAhBA,YAAY,GAAAC,UAAA,EAxBxBnC,QAAQ,CAAC;EACRoC,YAAY,EAAE,CACZN,iBAAiB,EACjBC,qBAAqB,EACrBC,WAAW,CACZ;EACDK,OAAO,EAAE,CACPpC,YAAY,EACZC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACX,GAAG6B,gBAAgB,CACpB;EACDK,OAAO,EAAE,CACPrC,YAAY,EACZC,YAAY,EACZC,mBAAmB,EACnBC,WAAW,EACX,GAAG6B,gBAAgB,EACnBH,iBAAiB,EACjBC,qBAAqB,EACrBC,WAAW;CAEd,CAAC,C,EACWE,YAAY,CAAI", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}