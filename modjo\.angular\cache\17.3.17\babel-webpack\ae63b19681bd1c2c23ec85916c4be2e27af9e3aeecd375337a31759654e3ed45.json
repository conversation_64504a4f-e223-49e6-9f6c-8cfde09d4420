{"ast": null, "code": "import { QrScannerComponent } from './components/qr-scanner/qr-scanner.component';\nexport const qrScannerRoutes = [{\n  path: '',\n  component: QrScannerComponent\n}];", "map": {"version": 3, "names": ["QrScannerComponent", "qrScannerRoutes", "path", "component"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\qr-scanner\\qr-scanner.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { QrScannerComponent } from './components/qr-scanner/qr-scanner.component';\n\nexport const qrScannerRoutes: Routes = [\n  {\n    path: '',\n    component: QrScannerComponent\n  }\n];\n"], "mappings": "AACA,SAASA,kBAAkB,QAAQ,8CAA8C;AAEjF,OAAO,MAAMC,eAAe,GAAW,CACrC;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEH;CACZ,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}