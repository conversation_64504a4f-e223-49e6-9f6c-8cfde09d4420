{"ast": null, "code": "import { UserRole } from '../../../../core/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"../../../../shared/pipes/time-ago.pipe\";\nfunction AdminDashboardComponent_div_0_mat_card_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 24)(1, \"mat-card-content\")(2, \"div\", 25)(3, \"div\", 26)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 27)(7, \"mat-icon\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"span\");\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 28)(12, \"h3\", 29);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"p\", 30);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const stat_r2 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-left-color\", stat_r2.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", stat_r2.color + \"20\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", stat_r2.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(stat_r2.icon);\n    i0.ɵɵadvance();\n    i0.ɵɵclassProp(\"trend-up\", stat_r2.trendUp)(\"trend-down\", !stat_r2.trendUp);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r2.trendUp ? \"trending_up\" : \"trending_down\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r2.trend);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r2.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r2.title);\n  }\n}\nfunction AdminDashboardComponent_div_0_mat_card_26_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 31);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_0_mat_card_26_Template_mat_card_click_0_listener() {\n      const item_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r4 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r4.navigateTo(item_r4.route));\n    });\n    i0.ɵɵelementStart(1, \"mat-card-content\")(2, \"div\", 32)(3, \"mat-icon\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 33);\n    i0.ɵɵtext(10, \" Acc\\u00E9der \");\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"arrow_forward\");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const item_r4 = ctx.$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(item_r4.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r4.description);\n  }\n}\nfunction AdminDashboardComponent_div_0_div_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 37)(9, \"span\", 38)(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 38)(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const validator_r6 = ctx.$implicit;\n    const i_r7 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r7 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(validator_r6.organizationName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", validator_r6.totalValidations, \" validations\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", validator_r6.averageResponseTime, \"h \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", validator_r6.approvalRate, \"% \");\n  }\n}\nfunction AdminDashboardComponent_div_0_div_46_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 37)(9, \"span\", 38)(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 38)(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"sentiment_satisfied\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const partner_r8 = ctx.$implicit;\n    const i_r9 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r9 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(partner_r8.businessName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", partner_r8.totalRedemptions, \" \\u00E9changes\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", partner_r8.totalPointsGenerated, \" pts \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", partner_r8.customerSatisfaction, \"% \");\n  }\n}\nfunction AdminDashboardComponent_div_0_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 34)(1, \"div\", 35);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 36)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 37)(9, \"span\", 38)(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"people\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 38)(14, \"mat-icon\");\n    i0.ɵɵtext(15, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const provider_r10 = ctx.$implicit;\n    const i_r11 = ctx.index;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i_r11 + 1);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(provider_r10.organizationName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", provider_r10.totalScans, \" scans\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" \", provider_r10.uniqueUsers, \" users \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", provider_r10.engagementRate, \"% \");\n  }\n}\nfunction AdminDashboardComponent_div_0_div_64_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"div\", 40)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 41)(5, \"p\", 42);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 43);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"timeAgo\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const activity_r12 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", activity_r12.color + \"20\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", activity_r12.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(activity_r12.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r12.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 7, activity_r12.timestamp));\n  }\n}\nfunction AdminDashboardComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"h1\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 5);\n    i0.ɵɵtext(7, \"\\uD83E\\uDDD1\\u200D\\uD83D\\uDCBC Tableau de bord administrateur - Supervision compl\\u00E8te de Modjo\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 6)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"admin_panel_settings\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Administrateur\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 7)(14, \"h2\", 8)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Statistiques Rapides \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 9);\n    i0.ɵɵtemplate(19, AdminDashboardComponent_div_0_mat_card_19_Template, 16, 15, \"mat-card\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 11)(21, \"h2\", 8)(22, \"mat-icon\");\n    i0.ɵɵtext(23, \"apps\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24, \" Gestion & Administration \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(25, \"div\", 12);\n    i0.ɵɵtemplate(26, AdminDashboardComponent_div_0_mat_card_26_Template, 13, 3, \"mat-card\", 13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"div\", 14)(28, \"div\", 15)(29, \"mat-card\", 16)(30, \"mat-card-header\")(31, \"mat-card-title\")(32, \"mat-icon\");\n    i0.ɵɵtext(33, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(34, \" Top Validateurs \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(35, \"mat-card-content\")(36, \"div\", 17);\n    i0.ɵɵtemplate(37, AdminDashboardComponent_div_0_div_37_Template, 17, 5, \"div\", 18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(38, \"mat-card\", 16)(39, \"mat-card-header\")(40, \"mat-card-title\")(41, \"mat-icon\");\n    i0.ɵɵtext(42, \"store\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(43, \" Top Partenaires \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(44, \"mat-card-content\")(45, \"div\", 17);\n    i0.ɵɵtemplate(46, AdminDashboardComponent_div_0_div_46_Template, 17, 5, \"div\", 18);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(47, \"mat-card\", 16)(48, \"mat-card-header\")(49, \"mat-card-title\")(50, \"mat-icon\");\n    i0.ɵɵtext(51, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(52, \" Top Prestataires \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(53, \"mat-card-content\")(54, \"div\", 17);\n    i0.ɵɵtemplate(55, AdminDashboardComponent_div_0_div_55_Template, 17, 5, \"div\", 18);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(56, \"div\", 19)(57, \"h2\", 8)(58, \"mat-icon\");\n    i0.ɵɵtext(59, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(60, \" Activit\\u00E9 R\\u00E9cente du Syst\\u00E8me \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"mat-card\", 20)(62, \"mat-card-content\")(63, \"div\", 21);\n    i0.ɵɵtemplate(64, AdminDashboardComponent_div_0_div_64_Template, 10, 9, \"div\", 22);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(65, \"button\", 23);\n    i0.ɵɵlistener(\"click\", function AdminDashboardComponent_div_0_Template_button_click_65_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r4 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r4.navigateTo(\"/admin/audit\"));\n    });\n    i0.ɵɵtext(66, \" Voir le journal complet \");\n    i0.ɵɵelementStart(67, \"mat-icon\");\n    i0.ɵɵtext(68, \"arrow_forward\");\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r4 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r4.getGreeting(), \", \", ctx_r4.user.name, \"\");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.quickStats);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.navigationItems);\n    i0.ɵɵadvance(11);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.globalStats == null ? null : ctx_r4.globalStats.topValidators);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.globalStats == null ? null : ctx_r4.globalStats.topPartners);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.globalStats == null ? null : ctx_r4.globalStats.topProviders);\n    i0.ɵɵadvance(9);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r4.recentActivities);\n  }\n}\nexport class AdminDashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.user = null;\n    this.adminStats = null;\n    this.globalStats = null;\n    // Navigation items\n    this.navigationItems = [{\n      title: 'Vue d\\'ensemble',\n      icon: 'dashboard',\n      route: '/admin/overview',\n      description: 'Statistiques globales et KPIs'\n    }, {\n      title: 'Gestion Utilisateurs',\n      icon: 'people',\n      route: '/admin/users',\n      description: 'Ajouter, modifier, supprimer des utilisateurs'\n    }, {\n      title: 'Gestion Partenaires',\n      icon: 'store',\n      route: '/admin/partners',\n      description: 'Validation et suivi des partenaires'\n    }, {\n      title: 'Gestion Prestataires',\n      icon: 'business',\n      route: '/admin/providers',\n      description: 'Validation et suivi des prestataires'\n    }, {\n      title: 'Gestion Validateurs',\n      icon: 'verified_user',\n      route: '/admin/validators',\n      description: 'Suivi des validateurs et leurs performances'\n    }, {\n      title: 'Configuration',\n      icon: 'settings',\n      route: '/admin/config',\n      description: 'Paramètres système et règles'\n    }, {\n      title: 'Journal d\\'audit',\n      icon: 'history',\n      route: '/admin/audit',\n      description: 'Historique des actions système'\n    }];\n    // Quick stats cards\n    this.quickStats = [{\n      title: 'Utilisateurs Actifs',\n      value: '0',\n      icon: 'people',\n      color: '#4CAF50',\n      trend: '+12%',\n      trendUp: true\n    }, {\n      title: 'Points en Circulation',\n      value: '0',\n      icon: 'stars',\n      color: '#FF9800',\n      trend: '+8%',\n      trendUp: true\n    }, {\n      title: 'Validations en Attente',\n      value: '0',\n      icon: 'pending',\n      color: '#F44336',\n      trend: '-5%',\n      trendUp: false\n    }, {\n      title: 'Partenaires Actifs',\n      value: '0',\n      icon: 'store',\n      color: '#2196F3',\n      trend: '+15%',\n      trendUp: true\n    }];\n    // Recent activities\n    this.recentActivities = [];\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user && user.role === UserRole.ADMIN) {\n        this.loadAdminData();\n      } else {\n        // Redirect non-admin users\n        this.router.navigate(['/dashboard']);\n      }\n    });\n  }\n  loadAdminData() {\n    this.loadAdminStats();\n    this.loadGlobalStats();\n    this.loadRecentActivities();\n  }\n  loadAdminStats() {\n    // Simulate admin stats data\n    this.adminStats = {\n      totalUsers: 1247,\n      activeUsers: 892,\n      newUsersThisMonth: 156,\n      totalPoints: 125000,\n      pointsDistributedThisMonth: 15600,\n      pointsRedeemedThisMonth: 8900,\n      totalValidations: 2340,\n      pendingValidations: 23,\n      totalPartners: 45,\n      activePartners: 38,\n      totalProviders: 67,\n      activeProviders: 52,\n      totalValidators: 28,\n      activeValidators: 24\n    };\n    // Update quick stats\n    this.quickStats[0].value = this.adminStats.activeUsers.toString();\n    this.quickStats[1].value = this.formatNumber(this.adminStats.totalPoints);\n    this.quickStats[2].value = this.adminStats.pendingValidations.toString();\n    this.quickStats[3].value = this.adminStats.activePartners.toString();\n  }\n  loadGlobalStats() {\n    // Simulate global stats\n    this.globalStats = {\n      totalPointsInCirculation: 125000,\n      totalPointsDistributed: 180000,\n      totalPointsRedeemed: 55000,\n      averageUserPoints: 140,\n      mostActiveCity: 'Sousse',\n      topValidators: [{\n        id: '1',\n        name: 'École Primaire Monastir',\n        organizationName: 'École Primaire',\n        totalValidations: 245,\n        averageResponseTime: 2.5,\n        approvalRate: 95\n      }, {\n        id: '2',\n        name: 'Bibliothèque Sousse',\n        organizationName: 'Bibliothèque Municipale',\n        totalValidations: 189,\n        averageResponseTime: 1.8,\n        approvalRate: 98\n      }, {\n        id: '3',\n        name: 'Association Jeunesse',\n        organizationName: 'Association',\n        totalValidations: 167,\n        averageResponseTime: 3.2,\n        approvalRate: 92\n      }],\n      topPartners: [{\n        id: '1',\n        businessName: 'Café des Nattes',\n        totalRedemptions: 156,\n        totalPointsGenerated: 7800,\n        customerSatisfaction: 94,\n        activeRewards: 5\n      }, {\n        id: '2',\n        businessName: 'Librairie Culturelle',\n        totalRedemptions: 134,\n        totalPointsGenerated: 6700,\n        customerSatisfaction: 96,\n        activeRewards: 8\n      }, {\n        id: '3',\n        businessName: 'Restaurant Al Medina',\n        totalRedemptions: 98,\n        totalPointsGenerated: 4900,\n        customerSatisfaction: 91,\n        activeRewards: 3\n      }],\n      topProviders: [{\n        id: '1',\n        organizationName: 'Club de Football Monastir',\n        totalScans: 567,\n        totalPointsDistributed: 2835,\n        uniqueUsers: 189,\n        engagementRate: 78\n      }, {\n        id: '2',\n        organizationName: 'Centre Culturel Sousse',\n        totalScans: 445,\n        totalPointsDistributed: 2225,\n        uniqueUsers: 156,\n        engagementRate: 82\n      }, {\n        id: '3',\n        organizationName: 'Association Environnement',\n        totalScans: 389,\n        totalPointsDistributed: 1945,\n        uniqueUsers: 134,\n        engagementRate: 75\n      }],\n      monthlyGrowth: []\n    };\n  }\n  loadRecentActivities() {\n    this.recentActivities = [{\n      type: 'user_created',\n      description: 'Nouvel utilisateur inscrit: Ahmed Ben Ali',\n      timestamp: new Date(Date.now() - 1000 * 60 * 15),\n      icon: 'person_add',\n      color: '#4CAF50'\n    }, {\n      type: 'validation_approved',\n      description: 'Validation approuvée: Aide bibliothèque (15 pts)',\n      timestamp: new Date(Date.now() - 1000 * 60 * 30),\n      icon: 'check_circle',\n      color: '#2196F3'\n    }, {\n      type: 'partner_joined',\n      description: 'Nouveau partenaire: Restaurant La Médina',\n      timestamp: new Date(Date.now() - 1000 * 60 * 45),\n      icon: 'store',\n      color: '#FF9800'\n    }, {\n      type: 'reward_redeemed',\n      description: 'Récompense échangée: Café gratuit (50 pts)',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60),\n      icon: 'card_giftcard',\n      color: '#9C27B0'\n    }, {\n      type: 'qr_scanned',\n      description: 'QR Code scanné: Cours de français (3 pts)',\n      timestamp: new Date(Date.now() - 1000 * 60 * 90),\n      icon: 'qr_code_scanner',\n      color: '#607D8B'\n    }];\n  }\n  formatNumber(num) {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n  navigateTo(route) {\n    this.router.navigate([route]);\n  }\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n  static {\n    this.ɵfac = function AdminDashboardComponent_Factory(t) {\n      return new (t || AdminDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AdminDashboardComponent,\n      selectors: [[\"app-admin-dashboard\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"admin-dashboard-container\", 4, \"ngIf\"], [1, \"admin-dashboard-container\"], [1, \"admin-header\"], [1, \"header-content\"], [1, \"admin-welcome\"], [1, \"admin-subtitle\"], [1, \"admin-badge\"], [1, \"quick-stats-section\"], [1, \"section-title\"], [1, \"stats-grid\"], [\"class\", \"stat-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"navigation-section\"], [1, \"navigation-grid\"], [\"class\", \"nav-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"overview-section\"], [1, \"overview-grid\"], [1, \"overview-card\"], [1, \"top-list\"], [\"class\", \"top-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"activities-section\"], [1, \"activities-card\"], [1, \"activities-list\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", 1, \"view-all-btn\", 3, \"click\"], [1, \"stat-card\"], [1, \"stat-header\"], [1, \"stat-icon\"], [1, \"stat-trend\"], [1, \"stat-content\"], [1, \"stat-value\"], [1, \"stat-title\"], [1, \"nav-card\", 3, \"click\"], [1, \"nav-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"nav-button\"], [1, \"top-item\"], [1, \"rank\"], [1, \"item-info\"], [1, \"metrics\"], [1, \"metric\"], [1, \"activity-item\"], [1, \"activity-icon\"], [1, \"activity-content\"], [1, \"activity-description\"], [1, \"activity-time\"]],\n      template: function AdminDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, AdminDashboardComponent_div_0_Template, 69, 8, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.MatButton, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardTitle, i6.MatIcon, i7.TimeAgoPipe],\n      styles: [\"\\n\\n.admin-dashboard-container[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  min-height: 100vh;\\n}\\n\\n\\n\\n.admin-header[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n  padding: 40px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 24px;\\n  color: white;\\n  box-shadow: 0 20px 40px rgba(103, 126, 234, 0.3);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.admin-welcome[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  font-family: 'Poppins', sans-serif;\\n}\\n\\n.admin-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  opacity: 0.9;\\n}\\n\\n.admin-badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 16px 24px;\\n  border-radius: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n}\\n\\n.admin-badge[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.admin-badge[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n\\n\\n\\n.section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin: 0 0 24px 0;\\n  color: #2d3748;\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  font-family: 'Poppins', sans-serif;\\n}\\n\\n\\n\\n.quick-stats-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));\\n  gap: 24px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  border-left: 4px solid;\\n  transition: all 0.3s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.stat-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 16px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.stat-trend[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n}\\n\\n.trend-up[_ngcontent-%COMP%] {\\n  color: #4CAF50;\\n}\\n\\n.trend-down[_ngcontent-%COMP%] {\\n  color: #F44336;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 2.2rem;\\n  font-weight: 700;\\n  color: #2d3748;\\n}\\n\\n.stat-title[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #718096;\\n  font-size: 1rem;\\n  font-weight: 500;\\n}\\n\\n\\n\\n.navigation-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.navigation-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n  gap: 24px;\\n}\\n\\n.nav-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.nav-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.nav-card[_ngcontent-%COMP%]   mat-card-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 32px 24px;\\n}\\n\\n.nav-icon[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  margin: 0 auto 20px;\\n  background: linear-gradient(135deg, rgba(103, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\\n  border-radius: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.nav-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  width: 2.5rem;\\n  height: 2.5rem;\\n  color: #667eea;\\n}\\n\\n.nav-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  color: #2d3748;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n}\\n\\n.nav-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: #718096;\\n  line-height: 1.5;\\n}\\n\\n.nav-button[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 44px;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.overview-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.overview-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 24px;\\n}\\n\\n.overview-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.overview-card[_ngcontent-%COMP%]   mat-card-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  color: #2d3748;\\n  font-weight: 600;\\n}\\n\\n.top-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.top-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 16px;\\n  background: #f7fafc;\\n  border-radius: 12px;\\n  transition: all 0.2s ease;\\n}\\n\\n.top-item[_ngcontent-%COMP%]:hover {\\n  background: #edf2f7;\\n  transform: translateX(4px);\\n}\\n\\n.rank[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  color: white;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 700;\\n  font-size: 0.9rem;\\n}\\n\\n.item-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.item-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #2d3748;\\n  font-size: 1rem;\\n  font-weight: 600;\\n}\\n\\n.item-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n\\n.metrics[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n}\\n\\n.metric[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 0.8rem;\\n  color: #4a5568;\\n}\\n\\n.metric[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  width: 1rem !important;\\n  height: 1rem !important;\\n  color: #667eea;\\n}\\n\\n\\n\\n.activities-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.activities-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.activities-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 16px;\\n  background: #f7fafc;\\n  border-radius: 12px;\\n  transition: all 0.2s ease;\\n}\\n\\n.activity-item[_ngcontent-%COMP%]:hover {\\n  background: #edf2f7;\\n  transform: translateX(4px);\\n}\\n\\n.activity-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.activity-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.activity-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.activity-description[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #2d3748;\\n  font-weight: 500;\\n}\\n\\n.activity-time[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: 0.8rem;\\n}\\n\\n.view-all-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 44px;\\n  font-weight: 600;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .admin-dashboard-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  \\n  .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n    text-align: center;\\n  }\\n  \\n  .admin-welcome[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  \\n  .stats-grid[_ngcontent-%COMP%], .navigation-grid[_ngcontent-%COMP%], .overview-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["UserRole", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵstyleProp", "stat_r2", "color", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "ɵɵclassProp", "trendUp", "trend", "value", "title", "ɵɵlistener", "AdminDashboardComponent_div_0_mat_card_26_Template_mat_card_click_0_listener", "item_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r4", "ɵɵnextContext", "ɵɵresetView", "navigateTo", "route", "description", "i_r7", "validator_r6", "organizationName", "ɵɵtextInterpolate1", "totalValidations", "averageResponseTime", "approvalRate", "i_r9", "partner_r8", "businessName", "totalRedemptions", "totalPointsGenerated", "customerSatisfaction", "i_r11", "provider_r10", "totalScans", "uniqueUsers", "engagementRate", "activity_r12", "ɵɵpipeBind1", "timestamp", "ɵɵtemplate", "AdminDashboardComponent_div_0_mat_card_19_Template", "AdminDashboardComponent_div_0_mat_card_26_Template", "AdminDashboardComponent_div_0_div_37_Template", "AdminDashboardComponent_div_0_div_46_Template", "AdminDashboardComponent_div_0_div_55_Template", "AdminDashboardComponent_div_0_div_64_Template", "AdminDashboardComponent_div_0_Template_button_click_65_listener", "_r1", "ɵɵtextInterpolate2", "getGreeting", "user", "name", "ɵɵproperty", "quickStats", "navigationItems", "globalStats", "topValidators", "topPartners", "topProviders", "recentActivities", "AdminDashboardComponent", "constructor", "authService", "router", "adminStats", "ngOnInit", "currentUser$", "subscribe", "role", "ADMIN", "loadAdminData", "navigate", "loadAdminStats", "loadGlobalStats", "loadRecentActivities", "totalUsers", "activeUsers", "newUsersThisMonth", "totalPoints", "pointsDistributedThisMonth", "pointsRedeemedThisMonth", "pendingValidations", "totalPartners", "activePartners", "totalProviders", "activeProviders", "totalValidators", "activeValidators", "toString", "formatNumber", "totalPointsInCirculation", "totalPointsDistributed", "totalPointsRedeemed", "averageUserPoints", "mostActiveCity", "id", "activeRewards", "monthlyGrowth", "type", "Date", "now", "num", "toFixed", "hour", "getHours", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "AdminDashboardComponent_Template", "rf", "ctx", "AdminDashboardComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\admin-dashboard\\components\\admin-dashboard\\admin-dashboard.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\admin-dashboard\\components\\admin-dashboard\\admin-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { User, UserRole, AdminStats, GlobalStats, TopValidator, TopPartner, TopProvider } from '../../../../core/models';\n\n@Component({\n  selector: 'app-admin-dashboard',\n  templateUrl: './admin-dashboard.component.html',\n  styleUrls: ['./admin-dashboard.component.css']\n})\nexport class AdminDashboardComponent implements OnInit {\n  user: User | null = null;\n  adminStats: AdminStats | null = null;\n  globalStats: GlobalStats | null = null;\n  \n  // Navigation items\n  navigationItems = [\n    { \n      title: 'Vue d\\'ensemble', \n      icon: 'dashboard', \n      route: '/admin/overview',\n      description: 'Statistiques globales et KPIs'\n    },\n    { \n      title: 'Gestion Utilisateurs', \n      icon: 'people', \n      route: '/admin/users',\n      description: 'Ajouter, modifier, supprimer des utilisateurs'\n    },\n    { \n      title: 'Gestion Partenaires', \n      icon: 'store', \n      route: '/admin/partners',\n      description: 'Validation et suivi des partenaires'\n    },\n    { \n      title: 'Gestion Prestataires', \n      icon: 'business', \n      route: '/admin/providers',\n      description: 'Validation et suivi des prestataires'\n    },\n    { \n      title: 'Gestion Validateurs', \n      icon: 'verified_user', \n      route: '/admin/validators',\n      description: 'Suivi des validateurs et leurs performances'\n    },\n    { \n      title: 'Configuration', \n      icon: 'settings', \n      route: '/admin/config',\n      description: 'Paramètres système et règles'\n    },\n    { \n      title: 'Journal d\\'audit', \n      icon: 'history', \n      route: '/admin/audit',\n      description: 'Historique des actions système'\n    }\n  ];\n\n  // Quick stats cards\n  quickStats = [\n    {\n      title: 'Utilisateurs Actifs',\n      value: '0',\n      icon: 'people',\n      color: '#4CAF50',\n      trend: '+12%',\n      trendUp: true\n    },\n    {\n      title: 'Points en Circulation',\n      value: '0',\n      icon: 'stars',\n      color: '#FF9800',\n      trend: '+8%',\n      trendUp: true\n    },\n    {\n      title: 'Validations en Attente',\n      value: '0',\n      icon: 'pending',\n      color: '#F44336',\n      trend: '-5%',\n      trendUp: false\n    },\n    {\n      title: 'Partenaires Actifs',\n      value: '0',\n      icon: 'store',\n      color: '#2196F3',\n      trend: '+15%',\n      trendUp: true\n    }\n  ];\n\n  // Recent activities\n  recentActivities: any[] = [];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user && user.role === UserRole.ADMIN) {\n        this.loadAdminData();\n      } else {\n        // Redirect non-admin users\n        this.router.navigate(['/dashboard']);\n      }\n    });\n  }\n\n  private loadAdminData(): void {\n    this.loadAdminStats();\n    this.loadGlobalStats();\n    this.loadRecentActivities();\n  }\n\n  private loadAdminStats(): void {\n    // Simulate admin stats data\n    this.adminStats = {\n      totalUsers: 1247,\n      activeUsers: 892,\n      newUsersThisMonth: 156,\n      totalPoints: 125000,\n      pointsDistributedThisMonth: 15600,\n      pointsRedeemedThisMonth: 8900,\n      totalValidations: 2340,\n      pendingValidations: 23,\n      totalPartners: 45,\n      activePartners: 38,\n      totalProviders: 67,\n      activeProviders: 52,\n      totalValidators: 28,\n      activeValidators: 24\n    };\n\n    // Update quick stats\n    this.quickStats[0].value = this.adminStats.activeUsers.toString();\n    this.quickStats[1].value = this.formatNumber(this.adminStats.totalPoints);\n    this.quickStats[2].value = this.adminStats.pendingValidations.toString();\n    this.quickStats[3].value = this.adminStats.activePartners.toString();\n  }\n\n  private loadGlobalStats(): void {\n    // Simulate global stats\n    this.globalStats = {\n      totalPointsInCirculation: 125000,\n      totalPointsDistributed: 180000,\n      totalPointsRedeemed: 55000,\n      averageUserPoints: 140,\n      mostActiveCity: 'Sousse',\n      topValidators: [\n        { id: '1', name: 'École Primaire Monastir', organizationName: 'École Primaire', totalValidations: 245, averageResponseTime: 2.5, approvalRate: 95 },\n        { id: '2', name: 'Bibliothèque Sousse', organizationName: 'Bibliothèque Municipale', totalValidations: 189, averageResponseTime: 1.8, approvalRate: 98 },\n        { id: '3', name: 'Association Jeunesse', organizationName: 'Association', totalValidations: 167, averageResponseTime: 3.2, approvalRate: 92 }\n      ],\n      topPartners: [\n        { id: '1', businessName: 'Café des Nattes', totalRedemptions: 156, totalPointsGenerated: 7800, customerSatisfaction: 94, activeRewards: 5 },\n        { id: '2', businessName: 'Librairie Culturelle', totalRedemptions: 134, totalPointsGenerated: 6700, customerSatisfaction: 96, activeRewards: 8 },\n        { id: '3', businessName: 'Restaurant Al Medina', totalRedemptions: 98, totalPointsGenerated: 4900, customerSatisfaction: 91, activeRewards: 3 }\n      ],\n      topProviders: [\n        { id: '1', organizationName: 'Club de Football Monastir', totalScans: 567, totalPointsDistributed: 2835, uniqueUsers: 189, engagementRate: 78 },\n        { id: '2', organizationName: 'Centre Culturel Sousse', totalScans: 445, totalPointsDistributed: 2225, uniqueUsers: 156, engagementRate: 82 },\n        { id: '3', organizationName: 'Association Environnement', totalScans: 389, totalPointsDistributed: 1945, uniqueUsers: 134, engagementRate: 75 }\n      ],\n      monthlyGrowth: []\n    };\n  }\n\n  private loadRecentActivities(): void {\n    this.recentActivities = [\n      {\n        type: 'user_created',\n        description: 'Nouvel utilisateur inscrit: Ahmed Ben Ali',\n        timestamp: new Date(Date.now() - 1000 * 60 * 15),\n        icon: 'person_add',\n        color: '#4CAF50'\n      },\n      {\n        type: 'validation_approved',\n        description: 'Validation approuvée: Aide bibliothèque (15 pts)',\n        timestamp: new Date(Date.now() - 1000 * 60 * 30),\n        icon: 'check_circle',\n        color: '#2196F3'\n      },\n      {\n        type: 'partner_joined',\n        description: 'Nouveau partenaire: Restaurant La Médina',\n        timestamp: new Date(Date.now() - 1000 * 60 * 45),\n        icon: 'store',\n        color: '#FF9800'\n      },\n      {\n        type: 'reward_redeemed',\n        description: 'Récompense échangée: Café gratuit (50 pts)',\n        timestamp: new Date(Date.now() - 1000 * 60 * 60),\n        icon: 'card_giftcard',\n        color: '#9C27B0'\n      },\n      {\n        type: 'qr_scanned',\n        description: 'QR Code scanné: Cours de français (3 pts)',\n        timestamp: new Date(Date.now() - 1000 * 60 * 90),\n        icon: 'qr_code_scanner',\n        color: '#607D8B'\n      }\n    ];\n  }\n\n  private formatNumber(num: number): string {\n    if (num >= 1000000) {\n      return (num / 1000000).toFixed(1) + 'M';\n    } else if (num >= 1000) {\n      return (num / 1000).toFixed(1) + 'K';\n    }\n    return num.toString();\n  }\n\n  navigateTo(route: string): void {\n    this.router.navigate([route]);\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n}\n", "<div class=\"admin-dashboard-container\" *ngIf=\"user\">\n  <!-- Header Section -->\n  <div class=\"admin-header\">\n    <div class=\"header-content\">\n      <div class=\"admin-welcome\">\n        <h1>{{ getGreeting() }}, {{ user.name }}</h1>\n        <p class=\"admin-subtitle\">🧑‍💼 Tableau de bord administrateur - Supervision complète de Modjo</p>\n      </div>\n      <div class=\"admin-badge\">\n        <mat-icon>admin_panel_settings</mat-icon>\n        <span>Administrateur</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Quick Stats -->\n  <div class=\"quick-stats-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>analytics</mat-icon>\n      Statistiques Rapides\n    </h2>\n    <div class=\"stats-grid\">\n      <mat-card *ngFor=\"let stat of quickStats\" class=\"stat-card\" [style.border-left-color]=\"stat.color\">\n        <mat-card-content>\n          <div class=\"stat-header\">\n            <div class=\"stat-icon\" [style.background-color]=\"stat.color + '20'\">\n              <mat-icon [style.color]=\"stat.color\">{{ stat.icon }}</mat-icon>\n            </div>\n            <div class=\"stat-trend\" [class.trend-up]=\"stat.trendUp\" [class.trend-down]=\"!stat.trendUp\">\n              <mat-icon>{{ stat.trendUp ? 'trending_up' : 'trending_down' }}</mat-icon>\n              <span>{{ stat.trend }}</span>\n            </div>\n          </div>\n          <div class=\"stat-content\">\n            <h3 class=\"stat-value\">{{ stat.value }}</h3>\n            <p class=\"stat-title\">{{ stat.title }}</p>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Navigation Grid -->\n  <div class=\"navigation-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>apps</mat-icon>\n      Gestion & Administration\n    </h2>\n    <div class=\"navigation-grid\">\n      <mat-card *ngFor=\"let item of navigationItems\" \n                class=\"nav-card\" \n                (click)=\"navigateTo(item.route)\">\n        <mat-card-content>\n          <div class=\"nav-icon\">\n            <mat-icon>{{ item.icon }}</mat-icon>\n          </div>\n          <h3>{{ item.title }}</h3>\n          <p>{{ item.description }}</p>\n          <button mat-raised-button color=\"primary\" class=\"nav-button\">\n            Accéder\n            <mat-icon>arrow_forward</mat-icon>\n          </button>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- System Overview -->\n  <div class=\"overview-section\">\n    <div class=\"overview-grid\">\n      <!-- Top Validators -->\n      <mat-card class=\"overview-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>verified_user</mat-icon>\n            Top Validateurs\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"top-list\">\n            <div *ngFor=\"let validator of globalStats?.topValidators; let i = index\" \n                 class=\"top-item\">\n              <div class=\"rank\">{{ i + 1 }}</div>\n              <div class=\"item-info\">\n                <h4>{{ validator.organizationName }}</h4>\n                <p>{{ validator.totalValidations }} validations</p>\n                <div class=\"metrics\">\n                  <span class=\"metric\">\n                    <mat-icon>schedule</mat-icon>\n                    {{ validator.averageResponseTime }}h\n                  </span>\n                  <span class=\"metric\">\n                    <mat-icon>check_circle</mat-icon>\n                    {{ validator.approvalRate }}%\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Top Partners -->\n      <mat-card class=\"overview-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>store</mat-icon>\n            Top Partenaires\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"top-list\">\n            <div *ngFor=\"let partner of globalStats?.topPartners; let i = index\" \n                 class=\"top-item\">\n              <div class=\"rank\">{{ i + 1 }}</div>\n              <div class=\"item-info\">\n                <h4>{{ partner.businessName }}</h4>\n                <p>{{ partner.totalRedemptions }} échanges</p>\n                <div class=\"metrics\">\n                  <span class=\"metric\">\n                    <mat-icon>stars</mat-icon>\n                    {{ partner.totalPointsGenerated }} pts\n                  </span>\n                  <span class=\"metric\">\n                    <mat-icon>sentiment_satisfied</mat-icon>\n                    {{ partner.customerSatisfaction }}%\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <!-- Top Providers -->\n      <mat-card class=\"overview-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>business</mat-icon>\n            Top Prestataires\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"top-list\">\n            <div *ngFor=\"let provider of globalStats?.topProviders; let i = index\" \n                 class=\"top-item\">\n              <div class=\"rank\">{{ i + 1 }}</div>\n              <div class=\"item-info\">\n                <h4>{{ provider.organizationName }}</h4>\n                <p>{{ provider.totalScans }} scans</p>\n                <div class=\"metrics\">\n                  <span class=\"metric\">\n                    <mat-icon>people</mat-icon>\n                    {{ provider.uniqueUsers }} users\n                  </span>\n                  <span class=\"metric\">\n                    <mat-icon>trending_up</mat-icon>\n                    {{ provider.engagementRate }}%\n                  </span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Recent Activities -->\n  <div class=\"activities-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>history</mat-icon>\n      Activité Récente du Système\n    </h2>\n    <mat-card class=\"activities-card\">\n      <mat-card-content>\n        <div class=\"activities-list\">\n          <div *ngFor=\"let activity of recentActivities\" class=\"activity-item\">\n            <div class=\"activity-icon\" [style.background-color]=\"activity.color + '20'\">\n              <mat-icon [style.color]=\"activity.color\">{{ activity.icon }}</mat-icon>\n            </div>\n            <div class=\"activity-content\">\n              <p class=\"activity-description\">{{ activity.description }}</p>\n              <span class=\"activity-time\">{{ activity.timestamp | timeAgo }}</span>\n            </div>\n          </div>\n        </div>\n        <button mat-stroked-button class=\"view-all-btn\" (click)=\"navigateTo('/admin/audit')\">\n          Voir le journal complet\n          <mat-icon>arrow_forward</mat-icon>\n        </button>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AAGA,SAAeA,QAAQ,QAAwE,yBAAyB;;;;;;;;;;;ICuB1GC,EAJR,CAAAC,cAAA,mBAAmG,uBAC/E,cACS,cAC6C,eAC7B;IAAAD,EAAA,CAAAE,MAAA,GAAe;IACtDF,EADsD,CAAAG,YAAA,EAAW,EAC3D;IAEJH,EADF,CAAAC,cAAA,cAA2F,eAC/E;IAAAD,EAAA,CAAAE,MAAA,GAAoD;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzEH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAE1BF,EAF0B,CAAAG,YAAA,EAAO,EACzB,EACF;IAEJH,EADF,CAAAC,cAAA,eAA0B,cACD;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,aAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAG5CF,EAH4C,CAAAG,YAAA,EAAI,EACtC,EACW,EACV;;;;IAhBiDH,EAAA,CAAAI,WAAA,sBAAAC,OAAA,CAAAC,KAAA,CAAsC;IAGrEN,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,WAAA,qBAAAC,OAAA,CAAAC,KAAA,QAA4C;IACvDN,EAAA,CAAAO,SAAA,EAA0B;IAA1BP,EAAA,CAAAI,WAAA,UAAAC,OAAA,CAAAC,KAAA,CAA0B;IAACN,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAI,IAAA,CAAe;IAE9BT,EAAA,CAAAO,SAAA,EAA+B;IAACP,EAAhC,CAAAU,WAAA,aAAAL,OAAA,CAAAM,OAAA,CAA+B,gBAAAN,OAAA,CAAAM,OAAA,CAAmC;IAC9EX,EAAA,CAAAO,SAAA,GAAoD;IAApDP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAM,OAAA,mCAAoD;IACxDX,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAO,KAAA,CAAgB;IAIDZ,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAQ,KAAA,CAAgB;IACjBb,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAS,KAAA,CAAgB;;;;;;IAc5Cd,EAAA,CAAAC,cAAA,mBAE2C;IAAjCD,EAAA,CAAAe,UAAA,mBAAAC,6EAAA;MAAA,MAAAC,OAAA,GAAAjB,EAAA,CAAAkB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAAP,OAAA,CAAAQ,KAAA,CAAsB;IAAA,EAAC;IAGpCzB,EAFJ,CAAAC,cAAA,uBAAkB,cACM,eACV;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAChC;IACNH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC7BH,EAAA,CAAAC,cAAA,iBAA6D;IAC3DD,EAAA,CAAAE,MAAA,sBACA;IAAAF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAG7BF,EAH6B,CAAAG,YAAA,EAAW,EAC3B,EACQ,EACV;;;;IATKH,EAAA,CAAAO,SAAA,GAAe;IAAfP,EAAA,CAAAQ,iBAAA,CAAAS,OAAA,CAAAR,IAAA,CAAe;IAEvBT,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAS,OAAA,CAAAH,KAAA,CAAgB;IACjBd,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAQ,iBAAA,CAAAS,OAAA,CAAAS,WAAA,CAAsB;;;;;IAyBrB1B,EAFF,CAAAC,cAAA,cACsB,cACF;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEjCH,EADF,CAAAC,cAAA,cAAuB,SACjB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA4C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG/CH,EAFJ,CAAAC,cAAA,cAAqB,eACE,gBACT;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAELH,EADF,CAAAC,cAAA,gBAAqB,gBACT;IAAAD,EAAA,CAAAE,MAAA,oBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAO,EACH,EACF,EACF;;;;;IAfcH,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAQ,iBAAA,CAAAmB,IAAA,KAAW;IAEvB3B,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAQ,iBAAA,CAAAoB,YAAA,CAAAC,gBAAA,CAAgC;IACjC7B,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAA8B,kBAAA,KAAAF,YAAA,CAAAG,gBAAA,iBAA4C;IAI3C/B,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA8B,kBAAA,MAAAF,YAAA,CAAAI,mBAAA,OACF;IAGEhC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA8B,kBAAA,MAAAF,YAAA,CAAAK,YAAA,OACF;;;;;IAoBJjC,EAFF,CAAAC,cAAA,cACsB,cACF;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEjCH,EADF,CAAAC,cAAA,cAAuB,SACjB;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAG1CH,EAFJ,CAAAC,cAAA,cAAqB,eACE,gBACT;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAELH,EADF,CAAAC,cAAA,gBAAqB,gBACT;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxCH,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAO,EACH,EACF,EACF;;;;;IAfcH,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAQ,iBAAA,CAAA0B,IAAA,KAAW;IAEvBlC,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAQ,iBAAA,CAAA2B,UAAA,CAAAC,YAAA,CAA0B;IAC3BpC,EAAA,CAAAO,SAAA,GAAuC;IAAvCP,EAAA,CAAA8B,kBAAA,KAAAK,UAAA,CAAAE,gBAAA,mBAAuC;IAItCrC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA8B,kBAAA,MAAAK,UAAA,CAAAG,oBAAA,UACF;IAGEtC,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA8B,kBAAA,MAAAK,UAAA,CAAAI,oBAAA,OACF;;;;;IAoBJvC,EAFF,CAAAC,cAAA,cACsB,cACF;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAEjCH,EADF,CAAAC,cAAA,cAAuB,SACjB;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGlCH,EAFJ,CAAAC,cAAA,cAAqB,eACE,gBACT;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAELH,EADF,CAAAC,cAAA,gBAAqB,gBACT;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAO,EACH,EACF,EACF;;;;;IAfcH,EAAA,CAAAO,SAAA,GAAW;IAAXP,EAAA,CAAAQ,iBAAA,CAAAgC,KAAA,KAAW;IAEvBxC,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAAQ,iBAAA,CAAAiC,YAAA,CAAAZ,gBAAA,CAA+B;IAChC7B,EAAA,CAAAO,SAAA,GAA+B;IAA/BP,EAAA,CAAA8B,kBAAA,KAAAW,YAAA,CAAAC,UAAA,WAA+B;IAI9B1C,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA8B,kBAAA,MAAAW,YAAA,CAAAE,WAAA,YACF;IAGE3C,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA8B,kBAAA,MAAAW,YAAA,CAAAG,cAAA,OACF;;;;;IAqBJ5C,EAFJ,CAAAC,cAAA,cAAqE,cACS,eACjC;IAAAD,EAAA,CAAAE,MAAA,GAAmB;IAC9DF,EAD8D,CAAAG,YAAA,EAAW,EACnE;IAEJH,EADF,CAAAC,cAAA,cAA8B,YACI;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC9DH,EAAA,CAAAC,cAAA,eAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAkC;;IAElEF,EAFkE,CAAAG,YAAA,EAAO,EACjE,EACF;;;;IAPuBH,EAAA,CAAAO,SAAA,EAAgD;IAAhDP,EAAA,CAAAI,WAAA,qBAAAyC,YAAA,CAAAvC,KAAA,QAAgD;IAC/DN,EAAA,CAAAO,SAAA,EAA8B;IAA9BP,EAAA,CAAAI,WAAA,UAAAyC,YAAA,CAAAvC,KAAA,CAA8B;IAACN,EAAA,CAAAO,SAAA,EAAmB;IAAnBP,EAAA,CAAAQ,iBAAA,CAAAqC,YAAA,CAAApC,IAAA,CAAmB;IAG5BT,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAQ,iBAAA,CAAAqC,YAAA,CAAAnB,WAAA,CAA0B;IAC9B1B,EAAA,CAAAO,SAAA,GAAkC;IAAlCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAA8C,WAAA,OAAAD,YAAA,CAAAE,SAAA,EAAkC;;;;;;IAlLpE/C,EALR,CAAAC,cAAA,aAAoD,aAExB,aACI,aACC,SACrB;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7CH,EAAA,CAAAC,cAAA,WAA0B;IAAAD,EAAA,CAAAE,MAAA,yGAAoE;IAChGF,EADgG,CAAAG,YAAA,EAAI,EAC9F;IAEJH,EADF,CAAAC,cAAA,aAAyB,eACb;IAAAD,EAAA,CAAAE,MAAA,4BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAG1BF,EAH0B,CAAAG,YAAA,EAAO,EACvB,EACF,EACF;IAKFH,EAFJ,CAAAC,cAAA,cAAiC,aACL,gBACd;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAAgD,UAAA,KAAAC,kDAAA,yBAAmG;IAkBvGjD,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAAgC,aACJ,gBACd;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAAgD,UAAA,KAAAE,kDAAA,wBAE2C;IAc/ClD,EADE,CAAAG,YAAA,EAAM,EACF;IASIH,EANV,CAAAC,cAAA,eAA8B,eACD,oBAEO,uBACb,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAE,MAAA,yBACF;IACFF,EADE,CAAAG,YAAA,EAAiB,EACD;IAEhBH,EADF,CAAAC,cAAA,wBAAkB,eACM;IACpBD,EAAA,CAAAgD,UAAA,KAAAG,6CAAA,mBACsB;IAmB5BnD,EAFI,CAAAG,YAAA,EAAM,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAAgC,uBACb,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,yBACF;IACFF,EADE,CAAAG,YAAA,EAAiB,EACD;IAEhBH,EADF,CAAAC,cAAA,wBAAkB,eACM;IACpBD,EAAA,CAAAgD,UAAA,KAAAI,6CAAA,mBACsB;IAmB5BpD,EAFI,CAAAG,YAAA,EAAM,EACW,EACV;IAMLH,EAHN,CAAAC,cAAA,oBAAgC,uBACb,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,0BACF;IACFF,EADE,CAAAG,YAAA,EAAiB,EACD;IAEhBH,EADF,CAAAC,cAAA,wBAAkB,eACM;IACpBD,EAAA,CAAAgD,UAAA,KAAAK,6CAAA,mBACsB;IAqBhCrD,EAJQ,CAAAG,YAAA,EAAM,EACW,EACV,EACP,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAAgC,aACJ,gBACd;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,oDACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGDH,EAFJ,CAAAC,cAAA,oBAAkC,wBACd,eACa;IAC3BD,EAAA,CAAAgD,UAAA,KAAAM,6CAAA,mBAAqE;IASvEtD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAAqF;IAArCD,EAAA,CAAAe,UAAA,mBAAAwC,gEAAA;MAAAvD,EAAA,CAAAkB,aAAA,CAAAsC,GAAA;MAAA,MAAAnC,MAAA,GAAArB,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASF,MAAA,CAAAG,UAAA,CAAW,cAAc,CAAC;IAAA,EAAC;IAClFxB,EAAA,CAAAE,MAAA,iCACA;IAAAF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAKjCF,EALiC,CAAAG,YAAA,EAAW,EAC3B,EACQ,EACV,EACP,EACF;;;;IA7LMH,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAyD,kBAAA,KAAApC,MAAA,CAAAqC,WAAA,UAAArC,MAAA,CAAAsC,IAAA,CAAAC,IAAA,KAAoC;IAiBf5D,EAAA,CAAAO,SAAA,IAAa;IAAbP,EAAA,CAAA6D,UAAA,YAAAxC,MAAA,CAAAyC,UAAA,CAAa;IA2Bb9D,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAA6D,UAAA,YAAAxC,MAAA,CAAA0C,eAAA,CAAkB;IA+BZ/D,EAAA,CAAAO,SAAA,IAA+B;IAA/BP,EAAA,CAAA6D,UAAA,YAAAxC,MAAA,CAAA2C,WAAA,kBAAA3C,MAAA,CAAA2C,WAAA,CAAAC,aAAA,CAA+B;IAgCjCjE,EAAA,CAAAO,SAAA,GAA6B;IAA7BP,EAAA,CAAA6D,UAAA,YAAAxC,MAAA,CAAA2C,WAAA,kBAAA3C,MAAA,CAAA2C,WAAA,CAAAE,WAAA,CAA6B;IAgC5BlE,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAA6D,UAAA,YAAAxC,MAAA,CAAA2C,WAAA,kBAAA3C,MAAA,CAAA2C,WAAA,CAAAG,YAAA,CAA8B;IAiChCnE,EAAA,CAAAO,SAAA,GAAmB;IAAnBP,EAAA,CAAA6D,UAAA,YAAAxC,MAAA,CAAA+C,gBAAA,CAAmB;;;ADvKvD,OAAM,MAAOC,uBAAuB;EA0FlCC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IA3FhB,KAAAb,IAAI,GAAgB,IAAI;IACxB,KAAAc,UAAU,GAAsB,IAAI;IACpC,KAAAT,WAAW,GAAuB,IAAI;IAEtC;IACA,KAAAD,eAAe,GAAG,CAChB;MACEjD,KAAK,EAAE,iBAAiB;MACxBL,IAAI,EAAE,WAAW;MACjBgB,KAAK,EAAE,iBAAiB;MACxBC,WAAW,EAAE;KACd,EACD;MACEZ,KAAK,EAAE,sBAAsB;MAC7BL,IAAI,EAAE,QAAQ;MACdgB,KAAK,EAAE,cAAc;MACrBC,WAAW,EAAE;KACd,EACD;MACEZ,KAAK,EAAE,qBAAqB;MAC5BL,IAAI,EAAE,OAAO;MACbgB,KAAK,EAAE,iBAAiB;MACxBC,WAAW,EAAE;KACd,EACD;MACEZ,KAAK,EAAE,sBAAsB;MAC7BL,IAAI,EAAE,UAAU;MAChBgB,KAAK,EAAE,kBAAkB;MACzBC,WAAW,EAAE;KACd,EACD;MACEZ,KAAK,EAAE,qBAAqB;MAC5BL,IAAI,EAAE,eAAe;MACrBgB,KAAK,EAAE,mBAAmB;MAC1BC,WAAW,EAAE;KACd,EACD;MACEZ,KAAK,EAAE,eAAe;MACtBL,IAAI,EAAE,UAAU;MAChBgB,KAAK,EAAE,eAAe;MACtBC,WAAW,EAAE;KACd,EACD;MACEZ,KAAK,EAAE,kBAAkB;MACzBL,IAAI,EAAE,SAAS;MACfgB,KAAK,EAAE,cAAc;MACrBC,WAAW,EAAE;KACd,CACF;IAED;IACA,KAAAoC,UAAU,GAAG,CACX;MACEhD,KAAK,EAAE,qBAAqB;MAC5BD,KAAK,EAAE,GAAG;MACVJ,IAAI,EAAE,QAAQ;MACdH,KAAK,EAAE,SAAS;MAChBM,KAAK,EAAE,MAAM;MACbD,OAAO,EAAE;KACV,EACD;MACEG,KAAK,EAAE,uBAAuB;MAC9BD,KAAK,EAAE,GAAG;MACVJ,IAAI,EAAE,OAAO;MACbH,KAAK,EAAE,SAAS;MAChBM,KAAK,EAAE,KAAK;MACZD,OAAO,EAAE;KACV,EACD;MACEG,KAAK,EAAE,wBAAwB;MAC/BD,KAAK,EAAE,GAAG;MACVJ,IAAI,EAAE,SAAS;MACfH,KAAK,EAAE,SAAS;MAChBM,KAAK,EAAE,KAAK;MACZD,OAAO,EAAE;KACV,EACD;MACEG,KAAK,EAAE,oBAAoB;MAC3BD,KAAK,EAAE,GAAG;MACVJ,IAAI,EAAE,OAAO;MACbH,KAAK,EAAE,SAAS;MAChBM,KAAK,EAAE,MAAM;MACbD,OAAO,EAAE;KACV,CACF;IAED;IACA,KAAAyD,gBAAgB,GAAU,EAAE;EAKzB;EAEHM,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,CAACI,YAAY,CAACC,SAAS,CAACjB,IAAI,IAAG;MAC7C,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAIA,IAAI,IAAIA,IAAI,CAACkB,IAAI,KAAK9E,QAAQ,CAAC+E,KAAK,EAAE;QACxC,IAAI,CAACC,aAAa,EAAE;OACrB,MAAM;QACL;QACA,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;IAExC,CAAC,CAAC;EACJ;EAEQD,aAAaA,CAAA;IACnB,IAAI,CAACE,cAAc,EAAE;IACrB,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,oBAAoB,EAAE;EAC7B;EAEQF,cAAcA,CAAA;IACpB;IACA,IAAI,CAACR,UAAU,GAAG;MAChBW,UAAU,EAAE,IAAI;MAChBC,WAAW,EAAE,GAAG;MAChBC,iBAAiB,EAAE,GAAG;MACtBC,WAAW,EAAE,MAAM;MACnBC,0BAA0B,EAAE,KAAK;MACjCC,uBAAuB,EAAE,IAAI;MAC7B1D,gBAAgB,EAAE,IAAI;MACtB2D,kBAAkB,EAAE,EAAE;MACtBC,aAAa,EAAE,EAAE;MACjBC,cAAc,EAAE,EAAE;MAClBC,cAAc,EAAE,EAAE;MAClBC,eAAe,EAAE,EAAE;MACnBC,eAAe,EAAE,EAAE;MACnBC,gBAAgB,EAAE;KACnB;IAED;IACA,IAAI,CAAClC,UAAU,CAAC,CAAC,CAAC,CAACjD,KAAK,GAAG,IAAI,CAAC4D,UAAU,CAACY,WAAW,CAACY,QAAQ,EAAE;IACjE,IAAI,CAACnC,UAAU,CAAC,CAAC,CAAC,CAACjD,KAAK,GAAG,IAAI,CAACqF,YAAY,CAAC,IAAI,CAACzB,UAAU,CAACc,WAAW,CAAC;IACzE,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAACjD,KAAK,GAAG,IAAI,CAAC4D,UAAU,CAACiB,kBAAkB,CAACO,QAAQ,EAAE;IACxE,IAAI,CAACnC,UAAU,CAAC,CAAC,CAAC,CAACjD,KAAK,GAAG,IAAI,CAAC4D,UAAU,CAACmB,cAAc,CAACK,QAAQ,EAAE;EACtE;EAEQf,eAAeA,CAAA;IACrB;IACA,IAAI,CAAClB,WAAW,GAAG;MACjBmC,wBAAwB,EAAE,MAAM;MAChCC,sBAAsB,EAAE,MAAM;MAC9BC,mBAAmB,EAAE,KAAK;MAC1BC,iBAAiB,EAAE,GAAG;MACtBC,cAAc,EAAE,QAAQ;MACxBtC,aAAa,EAAE,CACb;QAAEuC,EAAE,EAAE,GAAG;QAAE5C,IAAI,EAAE,yBAAyB;QAAE/B,gBAAgB,EAAE,gBAAgB;QAAEE,gBAAgB,EAAE,GAAG;QAAEC,mBAAmB,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAE,CAAE,EACnJ;QAAEuE,EAAE,EAAE,GAAG;QAAE5C,IAAI,EAAE,qBAAqB;QAAE/B,gBAAgB,EAAE,yBAAyB;QAAEE,gBAAgB,EAAE,GAAG;QAAEC,mBAAmB,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAE,CAAE,EACxJ;QAAEuE,EAAE,EAAE,GAAG;QAAE5C,IAAI,EAAE,sBAAsB;QAAE/B,gBAAgB,EAAE,aAAa;QAAEE,gBAAgB,EAAE,GAAG;QAAEC,mBAAmB,EAAE,GAAG;QAAEC,YAAY,EAAE;MAAE,CAAE,CAC9I;MACDiC,WAAW,EAAE,CACX;QAAEsC,EAAE,EAAE,GAAG;QAAEpE,YAAY,EAAE,iBAAiB;QAAEC,gBAAgB,EAAE,GAAG;QAAEC,oBAAoB,EAAE,IAAI;QAAEC,oBAAoB,EAAE,EAAE;QAAEkE,aAAa,EAAE;MAAC,CAAE,EAC3I;QAAED,EAAE,EAAE,GAAG;QAAEpE,YAAY,EAAE,sBAAsB;QAAEC,gBAAgB,EAAE,GAAG;QAAEC,oBAAoB,EAAE,IAAI;QAAEC,oBAAoB,EAAE,EAAE;QAAEkE,aAAa,EAAE;MAAC,CAAE,EAChJ;QAAED,EAAE,EAAE,GAAG;QAAEpE,YAAY,EAAE,sBAAsB;QAAEC,gBAAgB,EAAE,EAAE;QAAEC,oBAAoB,EAAE,IAAI;QAAEC,oBAAoB,EAAE,EAAE;QAAEkE,aAAa,EAAE;MAAC,CAAE,CAChJ;MACDtC,YAAY,EAAE,CACZ;QAAEqC,EAAE,EAAE,GAAG;QAAE3E,gBAAgB,EAAE,2BAA2B;QAAEa,UAAU,EAAE,GAAG;QAAE0D,sBAAsB,EAAE,IAAI;QAAEzD,WAAW,EAAE,GAAG;QAAEC,cAAc,EAAE;MAAE,CAAE,EAC/I;QAAE4D,EAAE,EAAE,GAAG;QAAE3E,gBAAgB,EAAE,wBAAwB;QAAEa,UAAU,EAAE,GAAG;QAAE0D,sBAAsB,EAAE,IAAI;QAAEzD,WAAW,EAAE,GAAG;QAAEC,cAAc,EAAE;MAAE,CAAE,EAC5I;QAAE4D,EAAE,EAAE,GAAG;QAAE3E,gBAAgB,EAAE,2BAA2B;QAAEa,UAAU,EAAE,GAAG;QAAE0D,sBAAsB,EAAE,IAAI;QAAEzD,WAAW,EAAE,GAAG;QAAEC,cAAc,EAAE;MAAE,CAAE,CAChJ;MACD8D,aAAa,EAAE;KAChB;EACH;EAEQvB,oBAAoBA,CAAA;IAC1B,IAAI,CAACf,gBAAgB,GAAG,CACtB;MACEuC,IAAI,EAAE,cAAc;MACpBjF,WAAW,EAAE,2CAA2C;MACxDqB,SAAS,EAAE,IAAI6D,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDpG,IAAI,EAAE,YAAY;MAClBH,KAAK,EAAE;KACR,EACD;MACEqG,IAAI,EAAE,qBAAqB;MAC3BjF,WAAW,EAAE,kDAAkD;MAC/DqB,SAAS,EAAE,IAAI6D,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDpG,IAAI,EAAE,cAAc;MACpBH,KAAK,EAAE;KACR,EACD;MACEqG,IAAI,EAAE,gBAAgB;MACtBjF,WAAW,EAAE,0CAA0C;MACvDqB,SAAS,EAAE,IAAI6D,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDpG,IAAI,EAAE,OAAO;MACbH,KAAK,EAAE;KACR,EACD;MACEqG,IAAI,EAAE,iBAAiB;MACvBjF,WAAW,EAAE,4CAA4C;MACzDqB,SAAS,EAAE,IAAI6D,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDpG,IAAI,EAAE,eAAe;MACrBH,KAAK,EAAE;KACR,EACD;MACEqG,IAAI,EAAE,YAAY;MAClBjF,WAAW,EAAE,2CAA2C;MACxDqB,SAAS,EAAE,IAAI6D,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDpG,IAAI,EAAE,iBAAiB;MACvBH,KAAK,EAAE;KACR,CACF;EACH;EAEQ4F,YAAYA,CAACY,GAAW;IAC9B,IAAIA,GAAG,IAAI,OAAO,EAAE;MAClB,OAAO,CAACA,GAAG,GAAG,OAAO,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;KACxC,MAAM,IAAID,GAAG,IAAI,IAAI,EAAE;MACtB,OAAO,CAACA,GAAG,GAAG,IAAI,EAAEC,OAAO,CAAC,CAAC,CAAC,GAAG,GAAG;;IAEtC,OAAOD,GAAG,CAACb,QAAQ,EAAE;EACvB;EAEAzE,UAAUA,CAACC,KAAa;IACtB,IAAI,CAAC+C,MAAM,CAACQ,QAAQ,CAAC,CAACvD,KAAK,CAAC,CAAC;EAC/B;EAEAiC,WAAWA,CAAA;IACT,MAAMsD,IAAI,GAAG,IAAIJ,IAAI,EAAE,CAACK,QAAQ,EAAE;IAClC,IAAID,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,SAAS;EAClB;;;uBAhOW3C,uBAAuB,EAAArE,EAAA,CAAAkH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApH,EAAA,CAAAkH,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvBjD,uBAAuB;MAAAkD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVpC7H,EAAA,CAAAgD,UAAA,IAAA+E,sCAAA,kBAAoD;;;UAAZ/H,EAAA,CAAA6D,UAAA,SAAAiE,GAAA,CAAAnE,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}