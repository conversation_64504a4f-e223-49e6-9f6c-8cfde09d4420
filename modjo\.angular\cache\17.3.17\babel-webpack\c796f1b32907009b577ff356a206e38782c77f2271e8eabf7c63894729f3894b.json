{"ast": null, "code": "import { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\nexport function windowToggle(openings, closingSelector) {\n  return operate((source, subscriber) => {\n    const windows = [];\n    const handleError = err => {\n      while (0 < windows.length) {\n        windows.shift().error(err);\n      }\n      subscriber.error(err);\n    };\n    innerFrom(openings).subscribe(createOperatorSubscriber(subscriber, openValue => {\n      const window = new Subject();\n      windows.push(window);\n      const closingSubscription = new Subscription();\n      const closeWindow = () => {\n        arrRemove(windows, window);\n        window.complete();\n        closingSubscription.unsubscribe();\n      };\n      let closingNotifier;\n      try {\n        closingNotifier = innerFrom(closingSelector(openValue));\n      } catch (err) {\n        handleError(err);\n        return;\n      }\n      subscriber.next(window.asObservable());\n      closingSubscription.add(closingNotifier.subscribe(createOperatorSubscriber(subscriber, closeWindow, noop, handleError)));\n    }, noop));\n    source.subscribe(createOperatorSubscriber(subscriber, value => {\n      const windowsCopy = windows.slice();\n      for (const window of windowsCopy) {\n        window.next(value);\n      }\n    }, () => {\n      while (0 < windows.length) {\n        windows.shift().complete();\n      }\n      subscriber.complete();\n    }, handleError, () => {\n      while (0 < windows.length) {\n        windows.shift().unsubscribe();\n      }\n    }));\n  });\n}", "map": {"version": 3, "names": ["Subject", "Subscription", "operate", "innerFrom", "createOperatorSubscriber", "noop", "arr<PERSON><PERSON><PERSON>", "windowToggle", "openings", "closingSelector", "source", "subscriber", "windows", "handleError", "err", "length", "shift", "error", "subscribe", "openValue", "window", "push", "closingSubscription", "closeWindow", "complete", "unsubscribe", "closingNotifier", "next", "asObservable", "add", "value", "windowsCopy", "slice"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/rxjs/dist/esm/internal/operators/windowToggle.js"], "sourcesContent": ["import { Subject } from '../Subject';\nimport { Subscription } from '../Subscription';\nimport { operate } from '../util/lift';\nimport { innerFrom } from '../observable/innerFrom';\nimport { createOperatorSubscriber } from './OperatorSubscriber';\nimport { noop } from '../util/noop';\nimport { arrRemove } from '../util/arrRemove';\nexport function windowToggle(openings, closingSelector) {\n    return operate((source, subscriber) => {\n        const windows = [];\n        const handleError = (err) => {\n            while (0 < windows.length) {\n                windows.shift().error(err);\n            }\n            subscriber.error(err);\n        };\n        innerFrom(openings).subscribe(createOperatorSubscriber(subscriber, (openValue) => {\n            const window = new Subject();\n            windows.push(window);\n            const closingSubscription = new Subscription();\n            const closeWindow = () => {\n                arrRemove(windows, window);\n                window.complete();\n                closingSubscription.unsubscribe();\n            };\n            let closingNotifier;\n            try {\n                closingNotifier = innerFrom(closingSelector(openValue));\n            }\n            catch (err) {\n                handleError(err);\n                return;\n            }\n            subscriber.next(window.asObservable());\n            closingSubscription.add(closingNotifier.subscribe(createOperatorSubscriber(subscriber, closeWindow, noop, handleError)));\n        }, noop));\n        source.subscribe(createOperatorSubscriber(subscriber, (value) => {\n            const windowsCopy = windows.slice();\n            for (const window of windowsCopy) {\n                window.next(value);\n            }\n        }, () => {\n            while (0 < windows.length) {\n                windows.shift().complete();\n            }\n            subscriber.complete();\n        }, handleError, () => {\n            while (0 < windows.length) {\n                windows.shift().unsubscribe();\n            }\n        }));\n    });\n}\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,YAAY;AACpC,SAASC,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,OAAO,QAAQ,cAAc;AACtC,SAASC,SAAS,QAAQ,yBAAyB;AACnD,SAASC,wBAAwB,QAAQ,sBAAsB;AAC/D,SAASC,IAAI,QAAQ,cAAc;AACnC,SAASC,SAAS,QAAQ,mBAAmB;AAC7C,OAAO,SAASC,YAAYA,CAACC,QAAQ,EAAEC,eAAe,EAAE;EACpD,OAAOP,OAAO,CAAC,CAACQ,MAAM,EAAEC,UAAU,KAAK;IACnC,MAAMC,OAAO,GAAG,EAAE;IAClB,MAAMC,WAAW,GAAIC,GAAG,IAAK;MACzB,OAAO,CAAC,GAAGF,OAAO,CAACG,MAAM,EAAE;QACvBH,OAAO,CAACI,KAAK,CAAC,CAAC,CAACC,KAAK,CAACH,GAAG,CAAC;MAC9B;MACAH,UAAU,CAACM,KAAK,CAACH,GAAG,CAAC;IACzB,CAAC;IACDX,SAAS,CAACK,QAAQ,CAAC,CAACU,SAAS,CAACd,wBAAwB,CAACO,UAAU,EAAGQ,SAAS,IAAK;MAC9E,MAAMC,MAAM,GAAG,IAAIpB,OAAO,CAAC,CAAC;MAC5BY,OAAO,CAACS,IAAI,CAACD,MAAM,CAAC;MACpB,MAAME,mBAAmB,GAAG,IAAIrB,YAAY,CAAC,CAAC;MAC9C,MAAMsB,WAAW,GAAGA,CAAA,KAAM;QACtBjB,SAAS,CAACM,OAAO,EAAEQ,MAAM,CAAC;QAC1BA,MAAM,CAACI,QAAQ,CAAC,CAAC;QACjBF,mBAAmB,CAACG,WAAW,CAAC,CAAC;MACrC,CAAC;MACD,IAAIC,eAAe;MACnB,IAAI;QACAA,eAAe,GAAGvB,SAAS,CAACM,eAAe,CAACU,SAAS,CAAC,CAAC;MAC3D,CAAC,CACD,OAAOL,GAAG,EAAE;QACRD,WAAW,CAACC,GAAG,CAAC;QAChB;MACJ;MACAH,UAAU,CAACgB,IAAI,CAACP,MAAM,CAACQ,YAAY,CAAC,CAAC,CAAC;MACtCN,mBAAmB,CAACO,GAAG,CAACH,eAAe,CAACR,SAAS,CAACd,wBAAwB,CAACO,UAAU,EAAEY,WAAW,EAAElB,IAAI,EAAEQ,WAAW,CAAC,CAAC,CAAC;IAC5H,CAAC,EAAER,IAAI,CAAC,CAAC;IACTK,MAAM,CAACQ,SAAS,CAACd,wBAAwB,CAACO,UAAU,EAAGmB,KAAK,IAAK;MAC7D,MAAMC,WAAW,GAAGnB,OAAO,CAACoB,KAAK,CAAC,CAAC;MACnC,KAAK,MAAMZ,MAAM,IAAIW,WAAW,EAAE;QAC9BX,MAAM,CAACO,IAAI,CAACG,KAAK,CAAC;MACtB;IACJ,CAAC,EAAE,MAAM;MACL,OAAO,CAAC,GAAGlB,OAAO,CAACG,MAAM,EAAE;QACvBH,OAAO,CAACI,KAAK,CAAC,CAAC,CAACQ,QAAQ,CAAC,CAAC;MAC9B;MACAb,UAAU,CAACa,QAAQ,CAAC,CAAC;IACzB,CAAC,EAAEX,WAAW,EAAE,MAAM;MAClB,OAAO,CAAC,GAAGD,OAAO,CAACG,MAAM,EAAE;QACvBH,OAAO,CAACI,KAAK,CAAC,CAAC,CAACS,WAAW,CAAC,CAAC;MACjC;IACJ,CAAC,CAAC,CAAC;EACP,CAAC,CAAC;AACN", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}