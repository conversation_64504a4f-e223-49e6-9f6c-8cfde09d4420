{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Optional, NgModule } from '@angular/core';\nimport { Subject, of, Observable, from, merge } from 'rxjs';\nimport { observeOn, switchMap, map, shareReplay, first, switchMapTo, subscribeOn, filter } from 'rxjs/operators';\nimport * as i1 from '@angular/fire';\nimport { keepUnstableUntilFirst, VERSION } from '@angular/fire';\nimport { ɵcacheInstance, ɵfirebaseAppFactory, ɵlazySDKProxy, FIREBASE_OPTIONS, FIREBASE_APP_NAME, ɵapplyMixins } from '@angular/fire/compat';\nimport { isPlatformServer } from '@angular/common';\nimport * as i2 from '@angular/fire/app-check';\nimport firebase from 'firebase/compat/app';\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\n// Export a null object with the same keys as firebase/compat/auth, so Proxy can work with proxy-polyfill in Internet Explorer\nconst proxyPolyfillCompat = {\n  name: null,\n  config: null,\n  emulatorConfig: null,\n  app: null,\n  applyActionCode: null,\n  checkActionCode: null,\n  confirmPasswordReset: null,\n  createUserWithEmailAndPassword: null,\n  currentUser: null,\n  fetchSignInMethodsForEmail: null,\n  isSignInWithEmailLink: null,\n  getRedirectResult: null,\n  languageCode: null,\n  settings: null,\n  onAuthStateChanged: null,\n  onIdTokenChanged: null,\n  sendSignInLinkToEmail: null,\n  sendPasswordResetEmail: null,\n  setPersistence: null,\n  signInAndRetrieveDataWithCredential: null,\n  signInAnonymously: null,\n  signInWithCredential: null,\n  signInWithCustomToken: null,\n  signInWithEmailAndPassword: null,\n  signInWithPhoneNumber: null,\n  signInWithEmailLink: null,\n  signInWithPopup: null,\n  signInWithRedirect: null,\n  signOut: null,\n  tenantId: null,\n  updateCurrentUser: null,\n  useDeviceLanguage: null,\n  useEmulator: null,\n  verifyPasswordResetCode: null\n};\nconst USE_EMULATOR = new InjectionToken('angularfire2.auth.use-emulator');\nconst SETTINGS = new InjectionToken('angularfire2.auth.settings');\nconst TENANT_ID = new InjectionToken('angularfire2.auth.tenant-id');\nconst LANGUAGE_CODE = new InjectionToken('angularfire2.auth.langugage-code');\nconst USE_DEVICE_LANGUAGE = new InjectionToken('angularfire2.auth.use-device-language');\nconst PERSISTENCE = new InjectionToken('angularfire.auth.persistence');\nconst ɵauthFactory = (app, zone, useEmulator, tenantId, languageCode, useDeviceLanguage, settings, persistence) => ɵcacheInstance(`${app.name}.auth`, 'AngularFireAuth', app.name, () => {\n  const auth = zone.runOutsideAngular(() => app.auth());\n  if (useEmulator) {\n    auth.useEmulator(...useEmulator);\n  }\n  if (tenantId) {\n    auth.tenantId = tenantId;\n  }\n  auth.languageCode = languageCode;\n  if (useDeviceLanguage) {\n    auth.useDeviceLanguage();\n  }\n  if (settings) {\n    for (const [k, v] of Object.entries(settings)) {\n      auth.settings[k] = v;\n    }\n  }\n  if (persistence) {\n    auth.setPersistence(persistence);\n  }\n  return auth;\n}, [useEmulator, tenantId, languageCode, useDeviceLanguage, settings, persistence]);\nclass AngularFireAuth {\n  constructor(options, name,\n  // tslint:disable-next-line:ban-types\n  platformId, zone, schedulers, useEmulator,\n  // can't use the tuple here\n  settings,\n  // can't use firebase.auth.AuthSettings here\n  tenantId, languageCode, useDeviceLanguage, persistence, _appCheckInstances) {\n    const logins = new Subject();\n    const auth = of(undefined).pipe(observeOn(schedulers.outsideAngular), switchMap(() => zone.runOutsideAngular(() => import('firebase/compat/auth'))), map(() => ɵfirebaseAppFactory(options, zone, name)), map(app => ɵauthFactory(app, zone, useEmulator, tenantId, languageCode, useDeviceLanguage, settings, persistence)), shareReplay({\n      bufferSize: 1,\n      refCount: false\n    }));\n    if (isPlatformServer(platformId)) {\n      this.authState = this.user = this.idToken = this.idTokenResult = this.credential = of(null);\n    } else {\n      // HACK, as we're exporting auth.Auth, rather than auth, developers importing firebase.auth\n      //       (e.g, `import { auth } from 'firebase/compat/app'`) are getting an undefined auth object unexpectedly\n      //       as we're completely lazy. Let's eagerly load the Auth SDK here.\n      //       There could potentially be race conditions still... but this greatly decreases the odds while\n      //       we reevaluate the API.\n      const _ = auth.pipe(first()).subscribe();\n      const redirectResult = auth.pipe(switchMap(auth => auth.getRedirectResult().then(it => it, () => null)), keepUnstableUntilFirst, shareReplay({\n        bufferSize: 1,\n        refCount: false\n      }));\n      const authStateChanged = auth.pipe(switchMap(auth => new Observable(sub => ({\n        unsubscribe: zone.runOutsideAngular(() => auth.onAuthStateChanged(next => sub.next(next), err => sub.error(err), () => sub.complete()))\n      }))));\n      const idTokenChanged = auth.pipe(switchMap(auth => new Observable(sub => ({\n        unsubscribe: zone.runOutsideAngular(() => auth.onIdTokenChanged(next => sub.next(next), err => sub.error(err), () => sub.complete()))\n      }))));\n      this.authState = redirectResult.pipe(switchMapTo(authStateChanged), subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n      this.user = redirectResult.pipe(switchMapTo(idTokenChanged), subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n      this.idToken = this.user.pipe(switchMap(user => user ? from(user.getIdToken()) : of(null)));\n      this.idTokenResult = this.user.pipe(switchMap(user => user ? from(user.getIdTokenResult()) : of(null)));\n      this.credential = merge(redirectResult, logins,\n      // pipe in null authState to make credential zipable, just a weird devexp if\n      // authState and user go null to still have a credential\n      this.authState.pipe(filter(it => !it))).pipe(\n      // handle the { user: { } } when a user is already logged in, rather have null\n      // TODO handle the type corcersion better\n      map(credential => (credential === null || credential === void 0 ? void 0 : credential.user) ? credential : null), subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n    }\n    return ɵlazySDKProxy(this, auth, zone, {\n      spy: {\n        apply: (name, _, val) => {\n          // If they call a signIn or createUser function listen into the promise\n          // this will give us the user credential, push onto the logins Subject\n          // to be consumed in .credential\n          if (name.startsWith('signIn') || name.startsWith('createUser')) {\n            // TODO fix the types, the trouble is UserCredential has everything optional\n            val.then(user => logins.next(user));\n          }\n        }\n      }\n    });\n  }\n}\nAngularFireAuth.ɵfac = function AngularFireAuth_Factory(t) {\n  return new (t || AngularFireAuth)(i0.ɵɵinject(FIREBASE_OPTIONS), i0.ɵɵinject(FIREBASE_APP_NAME, 8), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.ɵAngularFireSchedulers), i0.ɵɵinject(USE_EMULATOR, 8), i0.ɵɵinject(SETTINGS, 8), i0.ɵɵinject(TENANT_ID, 8), i0.ɵɵinject(LANGUAGE_CODE, 8), i0.ɵɵinject(USE_DEVICE_LANGUAGE, 8), i0.ɵɵinject(PERSISTENCE, 8), i0.ɵɵinject(i2.AppCheckInstances, 8));\n};\nAngularFireAuth.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: AngularFireAuth,\n  factory: AngularFireAuth.ɵfac,\n  providedIn: 'any'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularFireAuth, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'any'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [FIREBASE_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [FIREBASE_APP_NAME]\n      }]\n    }, {\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1.ɵAngularFireSchedulers\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [USE_EMULATOR]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [SETTINGS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [TENANT_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [LANGUAGE_CODE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [USE_DEVICE_LANGUAGE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [PERSISTENCE]\n      }]\n    }, {\n      type: i2.AppCheckInstances,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\nɵapplyMixins(AngularFireAuth, [proxyPolyfillCompat]);\nclass AngularFireAuthModule {\n  constructor() {\n    firebase.registerVersion('angularfire', VERSION.full, 'auth-compat');\n  }\n}\nAngularFireAuthModule.ɵfac = function AngularFireAuthModule_Factory(t) {\n  return new (t || AngularFireAuthModule)();\n};\nAngularFireAuthModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: AngularFireAuthModule\n});\nAngularFireAuthModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [AngularFireAuth]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularFireAuthModule, [{\n    type: NgModule,\n    args: [{\n      providers: [AngularFireAuth]\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularFireAuth, AngularFireAuthModule, LANGUAGE_CODE, PERSISTENCE, SETTINGS, TENANT_ID, USE_DEVICE_LANGUAGE, USE_EMULATOR, ɵauthFactory };", "map": {"version": 3, "names": ["i0", "InjectionToken", "PLATFORM_ID", "Injectable", "Inject", "Optional", "NgModule", "Subject", "of", "Observable", "from", "merge", "observeOn", "switchMap", "map", "shareReplay", "first", "switchMapTo", "subscribeOn", "filter", "i1", "keepUnstableUntilFirst", "VERSION", "ɵcacheInstance", "ɵfirebaseAppFactory", "ɵlazySDKProxy", "FIREBASE_OPTIONS", "FIREBASE_APP_NAME", "ɵapplyMixins", "isPlatformServer", "i2", "firebase", "proxyPolyfillCompat", "name", "config", "emulatorConfig", "app", "applyActionCode", "checkActionCode", "confirmPasswordReset", "createUserWithEmailAndPassword", "currentUser", "fetchSignInMethodsForEmail", "isSignInWithEmailLink", "getRedirectResult", "languageCode", "settings", "onAuthStateChanged", "onIdTokenChanged", "sendSignInLinkToEmail", "sendPasswordResetEmail", "setPersistence", "signInAndRetrieveDataWithCredential", "signInAnonymously", "signInWithCredential", "signInWithCustomToken", "signInWithEmailAndPassword", "signInWithPhoneNumber", "signInWithEmailLink", "signInWithPopup", "signInWithRedirect", "signOut", "tenantId", "updateCurrentUser", "useDeviceLanguage", "useEmulator", "verifyPasswordResetCode", "USE_EMULATOR", "SETTINGS", "TENANT_ID", "LANGUAGE_CODE", "USE_DEVICE_LANGUAGE", "PERSISTENCE", "ɵauthFactory", "zone", "persistence", "auth", "runOutsideAngular", "k", "v", "Object", "entries", "AngularFireAuth", "constructor", "options", "platformId", "schedulers", "_appCheckInstances", "logins", "undefined", "pipe", "outsideAngular", "bufferSize", "refCount", "authState", "user", "idToken", "idTokenResult", "credential", "_", "subscribe", "redirectResult", "then", "it", "authStateChanged", "sub", "unsubscribe", "next", "err", "error", "complete", "idTokenChanged", "insideAngular", "getIdToken", "getIdTokenResult", "spy", "apply", "val", "startsWith", "ɵfac", "AngularFireAuth_Factory", "t", "ɵɵinject", "NgZone", "ɵAngularFireSchedulers", "AppCheckInstances", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "decorators", "AngularFireAuthModule", "registerVersion", "full", "AngularFireAuthModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "providers"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/fire/fesm2015/angular-fire-compat-auth.js"], "sourcesContent": ["\nimport * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Optional, NgModule } from '@angular/core';\nimport { Subject, of, Observable, from, merge } from 'rxjs';\nimport { observeOn, switchMap, map, shareReplay, first, switchMapTo, subscribeOn, filter } from 'rxjs/operators';\nimport * as i1 from '@angular/fire';\nimport { keepUnstableUntilFirst, VERSION } from '@angular/fire';\nimport { ɵcacheInstance, ɵfirebaseAppFactory, ɵlazySDKProxy, FIREBASE_OPTIONS, FIREBASE_APP_NAME, ɵapplyMixins } from '@angular/fire/compat';\nimport { isPlatformServer } from '@angular/common';\nimport * as i2 from '@angular/fire/app-check';\nimport firebase from 'firebase/compat/app';\n\n// DO NOT MODIFY, this file is autogenerated by tools/build.ts\n// Export a null object with the same keys as firebase/compat/auth, so Proxy can work with proxy-polyfill in Internet Explorer\nconst proxyPolyfillCompat = {\n    name: null,\n    config: null,\n    emulatorConfig: null,\n    app: null,\n    applyActionCode: null,\n    checkActionCode: null,\n    confirmPasswordReset: null,\n    createUserWithEmailAndPassword: null,\n    currentUser: null,\n    fetchSignInMethodsForEmail: null,\n    isSignInWithEmailLink: null,\n    getRedirectResult: null,\n    languageCode: null,\n    settings: null,\n    onAuthStateChanged: null,\n    onIdTokenChanged: null,\n    sendSignInLinkToEmail: null,\n    sendPasswordResetEmail: null,\n    setPersistence: null,\n    signInAndRetrieveDataWithCredential: null,\n    signInAnonymously: null,\n    signInWithCredential: null,\n    signInWithCustomToken: null,\n    signInWithEmailAndPassword: null,\n    signInWithPhoneNumber: null,\n    signInWithEmailLink: null,\n    signInWithPopup: null,\n    signInWithRedirect: null,\n    signOut: null,\n    tenantId: null,\n    updateCurrentUser: null,\n    useDeviceLanguage: null,\n    useEmulator: null,\n    verifyPasswordResetCode: null,\n};\n\nconst USE_EMULATOR = new InjectionToken('angularfire2.auth.use-emulator');\nconst SETTINGS = new InjectionToken('angularfire2.auth.settings');\nconst TENANT_ID = new InjectionToken('angularfire2.auth.tenant-id');\nconst LANGUAGE_CODE = new InjectionToken('angularfire2.auth.langugage-code');\nconst USE_DEVICE_LANGUAGE = new InjectionToken('angularfire2.auth.use-device-language');\nconst PERSISTENCE = new InjectionToken('angularfire.auth.persistence');\nconst ɵauthFactory = (app, zone, useEmulator, tenantId, languageCode, useDeviceLanguage, settings, persistence) => ɵcacheInstance(`${app.name}.auth`, 'AngularFireAuth', app.name, () => {\n    const auth = zone.runOutsideAngular(() => app.auth());\n    if (useEmulator) {\n        auth.useEmulator(...useEmulator);\n    }\n    if (tenantId) {\n        auth.tenantId = tenantId;\n    }\n    auth.languageCode = languageCode;\n    if (useDeviceLanguage) {\n        auth.useDeviceLanguage();\n    }\n    if (settings) {\n        for (const [k, v] of Object.entries(settings)) {\n            auth.settings[k] = v;\n        }\n    }\n    if (persistence) {\n        auth.setPersistence(persistence);\n    }\n    return auth;\n}, [useEmulator, tenantId, languageCode, useDeviceLanguage, settings, persistence]);\nclass AngularFireAuth {\n    constructor(options, name, \n    // tslint:disable-next-line:ban-types\n    platformId, zone, schedulers, useEmulator, // can't use the tuple here\n    settings, // can't use firebase.auth.AuthSettings here\n    tenantId, languageCode, useDeviceLanguage, persistence, _appCheckInstances) {\n        const logins = new Subject();\n        const auth = of(undefined).pipe(observeOn(schedulers.outsideAngular), switchMap(() => zone.runOutsideAngular(() => import('firebase/compat/auth'))), map(() => ɵfirebaseAppFactory(options, zone, name)), map(app => ɵauthFactory(app, zone, useEmulator, tenantId, languageCode, useDeviceLanguage, settings, persistence)), shareReplay({ bufferSize: 1, refCount: false }));\n        if (isPlatformServer(platformId)) {\n            this.authState = this.user = this.idToken = this.idTokenResult = this.credential = of(null);\n        }\n        else {\n            // HACK, as we're exporting auth.Auth, rather than auth, developers importing firebase.auth\n            //       (e.g, `import { auth } from 'firebase/compat/app'`) are getting an undefined auth object unexpectedly\n            //       as we're completely lazy. Let's eagerly load the Auth SDK here.\n            //       There could potentially be race conditions still... but this greatly decreases the odds while\n            //       we reevaluate the API.\n            const _ = auth.pipe(first()).subscribe();\n            const redirectResult = auth.pipe(switchMap(auth => auth.getRedirectResult().then(it => it, () => null)), keepUnstableUntilFirst, shareReplay({ bufferSize: 1, refCount: false }));\n            const authStateChanged = auth.pipe(switchMap(auth => new Observable(sub => ({ unsubscribe: zone.runOutsideAngular(() => auth.onAuthStateChanged(next => sub.next(next), err => sub.error(err), () => sub.complete())) }))));\n            const idTokenChanged = auth.pipe(switchMap(auth => new Observable(sub => ({ unsubscribe: zone.runOutsideAngular(() => auth.onIdTokenChanged(next => sub.next(next), err => sub.error(err), () => sub.complete())) }))));\n            this.authState = redirectResult.pipe(switchMapTo(authStateChanged), subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n            this.user = redirectResult.pipe(switchMapTo(idTokenChanged), subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n            this.idToken = this.user.pipe(switchMap(user => user ? from(user.getIdToken()) : of(null)));\n            this.idTokenResult = this.user.pipe(switchMap(user => user ? from(user.getIdTokenResult()) : of(null)));\n            this.credential = merge(redirectResult, logins, \n            // pipe in null authState to make credential zipable, just a weird devexp if\n            // authState and user go null to still have a credential\n            this.authState.pipe(filter(it => !it))).pipe(\n            // handle the { user: { } } when a user is already logged in, rather have null\n            // TODO handle the type corcersion better\n            map(credential => (credential === null || credential === void 0 ? void 0 : credential.user) ? credential : null), subscribeOn(schedulers.outsideAngular), observeOn(schedulers.insideAngular));\n        }\n        return ɵlazySDKProxy(this, auth, zone, { spy: {\n                apply: (name, _, val) => {\n                    // If they call a signIn or createUser function listen into the promise\n                    // this will give us the user credential, push onto the logins Subject\n                    // to be consumed in .credential\n                    if (name.startsWith('signIn') || name.startsWith('createUser')) {\n                        // TODO fix the types, the trouble is UserCredential has everything optional\n                        val.then((user) => logins.next(user));\n                    }\n                }\n            } });\n    }\n}\nAngularFireAuth.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFireAuth, deps: [{ token: FIREBASE_OPTIONS }, { token: FIREBASE_APP_NAME, optional: true }, { token: PLATFORM_ID }, { token: i0.NgZone }, { token: i1.ɵAngularFireSchedulers }, { token: USE_EMULATOR, optional: true }, { token: SETTINGS, optional: true }, { token: TENANT_ID, optional: true }, { token: LANGUAGE_CODE, optional: true }, { token: USE_DEVICE_LANGUAGE, optional: true }, { token: PERSISTENCE, optional: true }, { token: i2.AppCheckInstances, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nAngularFireAuth.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFireAuth, providedIn: 'any' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFireAuth, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'any'\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [FIREBASE_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FIREBASE_APP_NAME]\n                }] }, { type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.NgZone }, { type: i1.ɵAngularFireSchedulers }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [USE_EMULATOR]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [SETTINGS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [TENANT_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LANGUAGE_CODE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [USE_DEVICE_LANGUAGE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [PERSISTENCE]\n                }] }, { type: i2.AppCheckInstances, decorators: [{\n                    type: Optional\n                }] }]; } });\nɵapplyMixins(AngularFireAuth, [proxyPolyfillCompat]);\n\nclass AngularFireAuthModule {\n    constructor() {\n        firebase.registerVersion('angularfire', VERSION.full, 'auth-compat');\n    }\n}\nAngularFireAuthModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFireAuthModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nAngularFireAuthModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFireAuthModule });\nAngularFireAuthModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFireAuthModule, providers: [AngularFireAuth] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFireAuthModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [AngularFireAuth]\n                }]\n        }], ctorParameters: function () { return []; } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularFireAuth, AngularFireAuthModule, LANGUAGE_CODE, PERSISTENCE, SETTINGS, TENANT_ID, USE_DEVICE_LANGUAGE, USE_EMULATOR, ɵauthFactory };\n"], "mappings": "AACA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AACnG,SAASC,OAAO,EAAEC,EAAE,EAAEC,UAAU,EAAEC,IAAI,EAAEC,KAAK,QAAQ,MAAM;AAC3D,SAASC,SAAS,EAAEC,SAAS,EAAEC,GAAG,EAAEC,WAAW,EAAEC,KAAK,EAAEC,WAAW,EAAEC,WAAW,EAAEC,MAAM,QAAQ,gBAAgB;AAChH,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,sBAAsB,EAAEC,OAAO,QAAQ,eAAe;AAC/D,SAASC,cAAc,EAAEC,mBAAmB,EAAEC,aAAa,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,YAAY,QAAQ,sBAAsB;AAC5I,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAO,KAAKC,EAAE,MAAM,yBAAyB;AAC7C,OAAOC,QAAQ,MAAM,qBAAqB;;AAE1C;AACA;AACA,MAAMC,mBAAmB,GAAG;EACxBC,IAAI,EAAE,IAAI;EACVC,MAAM,EAAE,IAAI;EACZC,cAAc,EAAE,IAAI;EACpBC,GAAG,EAAE,IAAI;EACTC,eAAe,EAAE,IAAI;EACrBC,eAAe,EAAE,IAAI;EACrBC,oBAAoB,EAAE,IAAI;EAC1BC,8BAA8B,EAAE,IAAI;EACpCC,WAAW,EAAE,IAAI;EACjBC,0BAA0B,EAAE,IAAI;EAChCC,qBAAqB,EAAE,IAAI;EAC3BC,iBAAiB,EAAE,IAAI;EACvBC,YAAY,EAAE,IAAI;EAClBC,QAAQ,EAAE,IAAI;EACdC,kBAAkB,EAAE,IAAI;EACxBC,gBAAgB,EAAE,IAAI;EACtBC,qBAAqB,EAAE,IAAI;EAC3BC,sBAAsB,EAAE,IAAI;EAC5BC,cAAc,EAAE,IAAI;EACpBC,mCAAmC,EAAE,IAAI;EACzCC,iBAAiB,EAAE,IAAI;EACvBC,oBAAoB,EAAE,IAAI;EAC1BC,qBAAqB,EAAE,IAAI;EAC3BC,0BAA0B,EAAE,IAAI;EAChCC,qBAAqB,EAAE,IAAI;EAC3BC,mBAAmB,EAAE,IAAI;EACzBC,eAAe,EAAE,IAAI;EACrBC,kBAAkB,EAAE,IAAI;EACxBC,OAAO,EAAE,IAAI;EACbC,QAAQ,EAAE,IAAI;EACdC,iBAAiB,EAAE,IAAI;EACvBC,iBAAiB,EAAE,IAAI;EACvBC,WAAW,EAAE,IAAI;EACjBC,uBAAuB,EAAE;AAC7B,CAAC;AAED,MAAMC,YAAY,GAAG,IAAIlE,cAAc,CAAC,gCAAgC,CAAC;AACzE,MAAMmE,QAAQ,GAAG,IAAInE,cAAc,CAAC,4BAA4B,CAAC;AACjE,MAAMoE,SAAS,GAAG,IAAIpE,cAAc,CAAC,6BAA6B,CAAC;AACnE,MAAMqE,aAAa,GAAG,IAAIrE,cAAc,CAAC,kCAAkC,CAAC;AAC5E,MAAMsE,mBAAmB,GAAG,IAAItE,cAAc,CAAC,uCAAuC,CAAC;AACvF,MAAMuE,WAAW,GAAG,IAAIvE,cAAc,CAAC,8BAA8B,CAAC;AACtE,MAAMwE,YAAY,GAAGA,CAACrC,GAAG,EAAEsC,IAAI,EAAET,WAAW,EAAEH,QAAQ,EAAEjB,YAAY,EAAEmB,iBAAiB,EAAElB,QAAQ,EAAE6B,WAAW,KAAKpD,cAAc,CAAC,GAAGa,GAAG,CAACH,IAAI,OAAO,EAAE,iBAAiB,EAAEG,GAAG,CAACH,IAAI,EAAE,MAAM;EACrL,MAAM2C,IAAI,GAAGF,IAAI,CAACG,iBAAiB,CAAC,MAAMzC,GAAG,CAACwC,IAAI,CAAC,CAAC,CAAC;EACrD,IAAIX,WAAW,EAAE;IACbW,IAAI,CAACX,WAAW,CAAC,GAAGA,WAAW,CAAC;EACpC;EACA,IAAIH,QAAQ,EAAE;IACVc,IAAI,CAACd,QAAQ,GAAGA,QAAQ;EAC5B;EACAc,IAAI,CAAC/B,YAAY,GAAGA,YAAY;EAChC,IAAImB,iBAAiB,EAAE;IACnBY,IAAI,CAACZ,iBAAiB,CAAC,CAAC;EAC5B;EACA,IAAIlB,QAAQ,EAAE;IACV,KAAK,MAAM,CAACgC,CAAC,EAAEC,CAAC,CAAC,IAAIC,MAAM,CAACC,OAAO,CAACnC,QAAQ,CAAC,EAAE;MAC3C8B,IAAI,CAAC9B,QAAQ,CAACgC,CAAC,CAAC,GAAGC,CAAC;IACxB;EACJ;EACA,IAAIJ,WAAW,EAAE;IACbC,IAAI,CAACzB,cAAc,CAACwB,WAAW,CAAC;EACpC;EACA,OAAOC,IAAI;AACf,CAAC,EAAE,CAACX,WAAW,EAAEH,QAAQ,EAAEjB,YAAY,EAAEmB,iBAAiB,EAAElB,QAAQ,EAAE6B,WAAW,CAAC,CAAC;AACnF,MAAMO,eAAe,CAAC;EAClBC,WAAWA,CAACC,OAAO,EAAEnD,IAAI;EACzB;EACAoD,UAAU,EAAEX,IAAI,EAAEY,UAAU,EAAErB,WAAW;EAAE;EAC3CnB,QAAQ;EAAE;EACVgB,QAAQ,EAAEjB,YAAY,EAAEmB,iBAAiB,EAAEW,WAAW,EAAEY,kBAAkB,EAAE;IACxE,MAAMC,MAAM,GAAG,IAAIjF,OAAO,CAAC,CAAC;IAC5B,MAAMqE,IAAI,GAAGpE,EAAE,CAACiF,SAAS,CAAC,CAACC,IAAI,CAAC9E,SAAS,CAAC0E,UAAU,CAACK,cAAc,CAAC,EAAE9E,SAAS,CAAC,MAAM6D,IAAI,CAACG,iBAAiB,CAAC,MAAM,MAAM,CAAC,sBAAsB,CAAC,CAAC,CAAC,EAAE/D,GAAG,CAAC,MAAMU,mBAAmB,CAAC4D,OAAO,EAAEV,IAAI,EAAEzC,IAAI,CAAC,CAAC,EAAEnB,GAAG,CAACsB,GAAG,IAAIqC,YAAY,CAACrC,GAAG,EAAEsC,IAAI,EAAET,WAAW,EAAEH,QAAQ,EAAEjB,YAAY,EAAEmB,iBAAiB,EAAElB,QAAQ,EAAE6B,WAAW,CAAC,CAAC,EAAE5D,WAAW,CAAC;MAAE6E,UAAU,EAAE,CAAC;MAAEC,QAAQ,EAAE;IAAM,CAAC,CAAC,CAAC;IAC9W,IAAIhE,gBAAgB,CAACwD,UAAU,CAAC,EAAE;MAC9B,IAAI,CAACS,SAAS,GAAG,IAAI,CAACC,IAAI,GAAG,IAAI,CAACC,OAAO,GAAG,IAAI,CAACC,aAAa,GAAG,IAAI,CAACC,UAAU,GAAG1F,EAAE,CAAC,IAAI,CAAC;IAC/F,CAAC,MACI;MACD;MACA;MACA;MACA;MACA;MACA,MAAM2F,CAAC,GAAGvB,IAAI,CAACc,IAAI,CAAC1E,KAAK,CAAC,CAAC,CAAC,CAACoF,SAAS,CAAC,CAAC;MACxC,MAAMC,cAAc,GAAGzB,IAAI,CAACc,IAAI,CAAC7E,SAAS,CAAC+D,IAAI,IAAIA,IAAI,CAAChC,iBAAiB,CAAC,CAAC,CAAC0D,IAAI,CAACC,EAAE,IAAIA,EAAE,EAAE,MAAM,IAAI,CAAC,CAAC,EAAElF,sBAAsB,EAAEN,WAAW,CAAC;QAAE6E,UAAU,EAAE,CAAC;QAAEC,QAAQ,EAAE;MAAM,CAAC,CAAC,CAAC;MACjL,MAAMW,gBAAgB,GAAG5B,IAAI,CAACc,IAAI,CAAC7E,SAAS,CAAC+D,IAAI,IAAI,IAAInE,UAAU,CAACgG,GAAG,KAAK;QAAEC,WAAW,EAAEhC,IAAI,CAACG,iBAAiB,CAAC,MAAMD,IAAI,CAAC7B,kBAAkB,CAAC4D,IAAI,IAAIF,GAAG,CAACE,IAAI,CAACA,IAAI,CAAC,EAAEC,GAAG,IAAIH,GAAG,CAACI,KAAK,CAACD,GAAG,CAAC,EAAE,MAAMH,GAAG,CAACK,QAAQ,CAAC,CAAC,CAAC;MAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3N,MAAMC,cAAc,GAAGnC,IAAI,CAACc,IAAI,CAAC7E,SAAS,CAAC+D,IAAI,IAAI,IAAInE,UAAU,CAACgG,GAAG,KAAK;QAAEC,WAAW,EAAEhC,IAAI,CAACG,iBAAiB,CAAC,MAAMD,IAAI,CAAC5B,gBAAgB,CAAC2D,IAAI,IAAIF,GAAG,CAACE,IAAI,CAACA,IAAI,CAAC,EAAEC,GAAG,IAAIH,GAAG,CAACI,KAAK,CAACD,GAAG,CAAC,EAAE,MAAMH,GAAG,CAACK,QAAQ,CAAC,CAAC,CAAC;MAAE,CAAC,CAAC,CAAC,CAAC,CAAC;MACvN,IAAI,CAAChB,SAAS,GAAGO,cAAc,CAACX,IAAI,CAACzE,WAAW,CAACuF,gBAAgB,CAAC,EAAEtF,WAAW,CAACoE,UAAU,CAACK,cAAc,CAAC,EAAE/E,SAAS,CAAC0E,UAAU,CAAC0B,aAAa,CAAC,CAAC;MAChJ,IAAI,CAACjB,IAAI,GAAGM,cAAc,CAACX,IAAI,CAACzE,WAAW,CAAC8F,cAAc,CAAC,EAAE7F,WAAW,CAACoE,UAAU,CAACK,cAAc,CAAC,EAAE/E,SAAS,CAAC0E,UAAU,CAAC0B,aAAa,CAAC,CAAC;MACzI,IAAI,CAAChB,OAAO,GAAG,IAAI,CAACD,IAAI,CAACL,IAAI,CAAC7E,SAAS,CAACkF,IAAI,IAAIA,IAAI,GAAGrF,IAAI,CAACqF,IAAI,CAACkB,UAAU,CAAC,CAAC,CAAC,GAAGzG,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;MAC3F,IAAI,CAACyF,aAAa,GAAG,IAAI,CAACF,IAAI,CAACL,IAAI,CAAC7E,SAAS,CAACkF,IAAI,IAAIA,IAAI,GAAGrF,IAAI,CAACqF,IAAI,CAACmB,gBAAgB,CAAC,CAAC,CAAC,GAAG1G,EAAE,CAAC,IAAI,CAAC,CAAC,CAAC;MACvG,IAAI,CAAC0F,UAAU,GAAGvF,KAAK,CAAC0F,cAAc,EAAEb,MAAM;MAC9C;MACA;MACA,IAAI,CAACM,SAAS,CAACJ,IAAI,CAACvE,MAAM,CAACoF,EAAE,IAAI,CAACA,EAAE,CAAC,CAAC,CAAC,CAACb,IAAI;MAC5C;MACA;MACA5E,GAAG,CAACoF,UAAU,IAAI,CAACA,UAAU,KAAK,IAAI,IAAIA,UAAU,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,UAAU,CAACH,IAAI,IAAIG,UAAU,GAAG,IAAI,CAAC,EAAEhF,WAAW,CAACoE,UAAU,CAACK,cAAc,CAAC,EAAE/E,SAAS,CAAC0E,UAAU,CAAC0B,aAAa,CAAC,CAAC;IAClM;IACA,OAAOvF,aAAa,CAAC,IAAI,EAAEmD,IAAI,EAAEF,IAAI,EAAE;MAAEyC,GAAG,EAAE;QACtCC,KAAK,EAAEA,CAACnF,IAAI,EAAEkE,CAAC,EAAEkB,GAAG,KAAK;UACrB;UACA;UACA;UACA,IAAIpF,IAAI,CAACqF,UAAU,CAAC,QAAQ,CAAC,IAAIrF,IAAI,CAACqF,UAAU,CAAC,YAAY,CAAC,EAAE;YAC5D;YACAD,GAAG,CAACf,IAAI,CAAEP,IAAI,IAAKP,MAAM,CAACmB,IAAI,CAACZ,IAAI,CAAC,CAAC;UACzC;QACJ;MACJ;IAAE,CAAC,CAAC;EACZ;AACJ;AACAb,eAAe,CAACqC,IAAI,YAAAC,wBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFvC,eAAe,EAAzBlF,EAAE,CAAA0H,QAAA,CAAyChG,gBAAgB,GAA3D1B,EAAE,CAAA0H,QAAA,CAAsE/F,iBAAiB,MAAzF3B,EAAE,CAAA0H,QAAA,CAAoHxH,WAAW,GAAjIF,EAAE,CAAA0H,QAAA,CAA4I1H,EAAE,CAAC2H,MAAM,GAAvJ3H,EAAE,CAAA0H,QAAA,CAAkKtG,EAAE,CAACwG,sBAAsB,GAA7L5H,EAAE,CAAA0H,QAAA,CAAwMvD,YAAY,MAAtNnE,EAAE,CAAA0H,QAAA,CAAiPtD,QAAQ,MAA3PpE,EAAE,CAAA0H,QAAA,CAAsRrD,SAAS,MAAjSrE,EAAE,CAAA0H,QAAA,CAA4TpD,aAAa,MAA3UtE,EAAE,CAAA0H,QAAA,CAAsWnD,mBAAmB,MAA3XvE,EAAE,CAAA0H,QAAA,CAAsZlD,WAAW,MAAnaxE,EAAE,CAAA0H,QAAA,CAA8b5F,EAAE,CAAC+F,iBAAiB;AAAA,CAA6D;AACnnB3C,eAAe,CAAC4C,KAAK,kBAD6E9H,EAAE,CAAA+H,kBAAA;EAAAC,KAAA,EACY9C,eAAe;EAAA+C,OAAA,EAAf/C,eAAe,CAAAqC,IAAA;EAAAW,UAAA,EAAc;AAAK,EAAG;AACrJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFkGnI,EAAE,CAAAoI,iBAAA,CAETlD,eAAe,EAAc,CAAC;IAC7GmD,IAAI,EAAElI,UAAU;IAChBmI,IAAI,EAAE,CAAC;MACCJ,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAEG,IAAI,EAAE5C,SAAS;MAAE8C,UAAU,EAAE,CAAC;QAC9DF,IAAI,EAAEjI,MAAM;QACZkI,IAAI,EAAE,CAAC5G,gBAAgB;MAC3B,CAAC;IAAE,CAAC,EAAE;MAAE2G,IAAI,EAAE5C,SAAS;MAAE8C,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAEhI;MACV,CAAC,EAAE;QACCgI,IAAI,EAAEjI,MAAM;QACZkI,IAAI,EAAE,CAAC3G,iBAAiB;MAC5B,CAAC;IAAE,CAAC,EAAE;MAAE0G,IAAI,EAAErD,MAAM;MAAEuD,UAAU,EAAE,CAAC;QAC/BF,IAAI,EAAEjI,MAAM;QACZkI,IAAI,EAAE,CAACpI,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAEmI,IAAI,EAAErI,EAAE,CAAC2H;IAAO,CAAC,EAAE;MAAEU,IAAI,EAAEjH,EAAE,CAACwG;IAAuB,CAAC,EAAE;MAAES,IAAI,EAAE5C,SAAS;MAAE8C,UAAU,EAAE,CAAC;QAC5FF,IAAI,EAAEhI;MACV,CAAC,EAAE;QACCgI,IAAI,EAAEjI,MAAM;QACZkI,IAAI,EAAE,CAACnE,YAAY;MACvB,CAAC;IAAE,CAAC,EAAE;MAAEkE,IAAI,EAAE5C,SAAS;MAAE8C,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAEhI;MACV,CAAC,EAAE;QACCgI,IAAI,EAAEjI,MAAM;QACZkI,IAAI,EAAE,CAAClE,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEiE,IAAI,EAAE5C,SAAS;MAAE8C,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAEhI;MACV,CAAC,EAAE;QACCgI,IAAI,EAAEjI,MAAM;QACZkI,IAAI,EAAE,CAACjE,SAAS;MACpB,CAAC;IAAE,CAAC,EAAE;MAAEgE,IAAI,EAAE5C,SAAS;MAAE8C,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAEhI;MACV,CAAC,EAAE;QACCgI,IAAI,EAAEjI,MAAM;QACZkI,IAAI,EAAE,CAAChE,aAAa;MACxB,CAAC;IAAE,CAAC,EAAE;MAAE+D,IAAI,EAAE5C,SAAS;MAAE8C,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAEhI;MACV,CAAC,EAAE;QACCgI,IAAI,EAAEjI,MAAM;QACZkI,IAAI,EAAE,CAAC/D,mBAAmB;MAC9B,CAAC;IAAE,CAAC,EAAE;MAAE8D,IAAI,EAAE5C,SAAS;MAAE8C,UAAU,EAAE,CAAC;QAClCF,IAAI,EAAEhI;MACV,CAAC,EAAE;QACCgI,IAAI,EAAEjI,MAAM;QACZkI,IAAI,EAAE,CAAC9D,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAE6D,IAAI,EAAEvG,EAAE,CAAC+F,iBAAiB;MAAEU,UAAU,EAAE,CAAC;QAC7CF,IAAI,EAAEhI;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AACxBuB,YAAY,CAACsD,eAAe,EAAE,CAAClD,mBAAmB,CAAC,CAAC;AAEpD,MAAMwG,qBAAqB,CAAC;EACxBrD,WAAWA,CAAA,EAAG;IACVpD,QAAQ,CAAC0G,eAAe,CAAC,aAAa,EAAEnH,OAAO,CAACoH,IAAI,EAAE,aAAa,CAAC;EACxE;AACJ;AACAF,qBAAqB,CAACjB,IAAI,YAAAoB,8BAAAlB,CAAA;EAAA,YAAAA,CAAA,IAAwFe,qBAAqB;AAAA,CAAkD;AACzLA,qBAAqB,CAACI,IAAI,kBA3DwE5I,EAAE,CAAA6I,gBAAA;EAAAR,IAAA,EA2DeG;AAAqB,EAAG;AAC3IA,qBAAqB,CAACM,IAAI,kBA5DwE9I,EAAE,CAAA+I,gBAAA;EAAAC,SAAA,EA4DiD,CAAC9D,eAAe;AAAC,EAAG;AACzK;EAAA,QAAAiD,SAAA,oBAAAA,SAAA,KA7DkGnI,EAAE,CAAAoI,iBAAA,CA6DTI,qBAAqB,EAAc,CAAC;IACnHH,IAAI,EAAE/H,QAAQ;IACdgI,IAAI,EAAE,CAAC;MACCU,SAAS,EAAE,CAAC9D,eAAe;IAC/B,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;;AAEtD;AACA;AACA;;AAEA,SAASA,eAAe,EAAEsD,qBAAqB,EAAElE,aAAa,EAAEE,WAAW,EAAEJ,QAAQ,EAAEC,SAAS,EAAEE,mBAAmB,EAAEJ,YAAY,EAAEM,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}