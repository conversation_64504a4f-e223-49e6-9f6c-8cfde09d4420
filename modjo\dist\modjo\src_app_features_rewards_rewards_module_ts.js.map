{"version": 3, "file": "src_app_features_rewards_rewards_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;AACuD;AACsD;;AAKvG,MAAOK,cAAc;EASzBC,YAAA;IARQ,KAAAC,cAAc,GAAG,IAAIN,iDAAe,CAAW,EAAE,CAAC;IAClD,KAAAO,eAAe,GAAG,IAAIP,iDAAe,CAAY,EAAE,CAAC;IACpD,KAAAQ,gBAAgB,GAAG,IAAIR,iDAAe,CAAmB,EAAE,CAAC;IAE7D,KAAAS,QAAQ,GAAG,IAAI,CAACH,cAAc,CAACI,YAAY,EAAE;IAC7C,KAAAC,SAAS,GAAG,IAAI,CAACJ,eAAe,CAACG,YAAY,EAAE;IAC/C,KAAAE,UAAU,GAAG,IAAI,CAACJ,gBAAgB,CAACE,YAAY,EAAE;IAGtD,IAAI,CAACG,oBAAoB,EAAE;EAC7B;EAEQA,oBAAoBA,CAAA;IAC1B;IACA,MAAMC,cAAc,GAAc,CAChC;MACEC,EAAE,EAAE,UAAU;MACdC,IAAI,EAAE,uBAAuB;MAC7BC,WAAW,EAAE,uCAAuC;MACpDC,QAAQ,EAAEhB,oDAAe,CAACiB,IAAI;MAC9BC,OAAO,EAAE,kCAAkC;MAC3CC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE,wBAAwB;MAC/BC,QAAQ,EAAE;QAAEC,QAAQ,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAO,CAAE;MACnDC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;MAC/BC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;KACjC,EACD;MACEf,EAAE,EAAE,UAAU;MACdC,IAAI,EAAE,sBAAsB;MAC5BC,WAAW,EAAE,kDAAkD;MAC/DC,QAAQ,EAAEhB,oDAAe,CAAC8B,UAAU;MACpCZ,OAAO,EAAE,2BAA2B;MACpCC,IAAI,EAAE,QAAQ;MACdC,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE,kBAAkB;MACzBC,QAAQ,EAAE;QAAEC,QAAQ,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAO,CAAE;MACnDC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,CAAC,SAAS,EAAE,SAAS,CAAC;MAC/BC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;KACjC,EACD;MACEf,EAAE,EAAE,UAAU;MACdC,IAAI,EAAE,sBAAsB;MAC5BC,WAAW,EAAE,8BAA8B;MAC3CC,QAAQ,EAAEhB,oDAAe,CAAC+B,MAAM;MAChCb,OAAO,EAAE,gCAAgC;MACzCC,IAAI,EAAE,UAAU;MAChBC,KAAK,EAAE,UAAU;MACjBC,KAAK,EAAE,iCAAiC;MACxCC,QAAQ,EAAE;QAAEC,QAAQ,EAAE,OAAO;QAAEC,SAAS,EAAE;MAAO,CAAE;MACnDC,QAAQ,EAAE,IAAI;MACdC,OAAO,EAAE,CAAC,SAAS,CAAC;MACpBC,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY;KACjC,CACF;IAED;IACA,MAAMI,aAAa,GAAa,CAC9B;MACEnB,EAAE,EAAE,SAAS;MACboB,KAAK,EAAE,cAAc;MACrBlB,WAAW,EAAE,uCAAuC;MACpDmB,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,UAAU;MACrBC,WAAW,EAAE,uBAAuB;MACpCpB,QAAQ,EAAEjB,mDAAc,CAACsC,IAAI;MAC7BC,QAAQ,EAAE,2BAA2B;MACrCb,QAAQ,EAAE,IAAI;MACdc,iBAAiB,EAAE,EAAE;MACrBC,UAAU,EAAE,IAAIZ,IAAI,CAAC,YAAY,CAAC;MAClCa,KAAK,EAAE,2CAA2C;MAClDd,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;MACjCT,IAAI,EAAE;KACP,EACD;MACEN,EAAE,EAAE,SAAS;MACboB,KAAK,EAAE,oBAAoB;MAC3BlB,WAAW,EAAE,0CAA0C;MACvDmB,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,UAAU;MACrBC,WAAW,EAAE,uBAAuB;MACpCpB,QAAQ,EAAEjB,mDAAc,CAACsC,IAAI;MAC7BC,QAAQ,EAAE,2BAA2B;MACrCb,QAAQ,EAAE,IAAI;MACdc,iBAAiB,EAAE,EAAE;MACrBC,UAAU,EAAE,IAAIZ,IAAI,CAAC,YAAY,CAAC;MAClCa,KAAK,EAAE,qBAAqB;MAC5Bd,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;MACjCT,IAAI,EAAE;KACP,EACD;MACEN,EAAE,EAAE,SAAS;MACboB,KAAK,EAAE,iBAAiB;MACxBlB,WAAW,EAAE,sDAAsD;MACnEmB,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,UAAU;MACrBC,WAAW,EAAE,sBAAsB;MACnCpB,QAAQ,EAAEjB,mDAAc,CAACsC,IAAI;MAC7BC,QAAQ,EAAE,8BAA8B;MACxCb,QAAQ,EAAE,IAAI;MACdc,iBAAiB,EAAE,EAAE;MACrBC,UAAU,EAAE,IAAIZ,IAAI,CAAC,YAAY,CAAC;MAClCa,KAAK,EAAE,qCAAqC;MAC5Cd,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;MACjCT,IAAI,EAAE;KACP,EACD;MACEN,EAAE,EAAE,SAAS;MACboB,KAAK,EAAE,eAAe;MACtBlB,WAAW,EAAE,yCAAyC;MACtDmB,cAAc,EAAE,EAAE;MAClBC,SAAS,EAAE,UAAU;MACrBC,WAAW,EAAE,sBAAsB;MACnCpB,QAAQ,EAAEjB,mDAAc,CAACsC,IAAI;MAC7BC,QAAQ,EAAE,6BAA6B;MACvCb,QAAQ,EAAE,IAAI;MACdc,iBAAiB,EAAE,EAAE;MACrBC,UAAU,EAAE,IAAIZ,IAAI,CAAC,YAAY,CAAC;MAClCa,KAAK,EAAE,6CAA6C;MACpDd,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;MACjCT,IAAI,EAAE;KACP,EACD;MACEN,EAAE,EAAE,SAAS;MACboB,KAAK,EAAE,eAAe;MACtBlB,WAAW,EAAE,qCAAqC;MAClDmB,cAAc,EAAE,GAAG;MACnBC,SAAS,EAAE,UAAU;MACrBC,WAAW,EAAE,sBAAsB;MACnCpB,QAAQ,EAAEjB,mDAAc,CAAC2C,SAAS;MAClCJ,QAAQ,EAAE,yBAAyB;MACnCb,QAAQ,EAAE,IAAI;MACdc,iBAAiB,EAAE,EAAE;MACrBC,UAAU,EAAE,IAAIZ,IAAI,CAAC,YAAY,CAAC;MAClCa,KAAK,EAAE,4BAA4B;MACnCd,SAAS,EAAE,IAAIC,IAAI,CAAC,YAAY,CAAC;MACjCC,SAAS,EAAE,IAAID,IAAI,CAAC,YAAY,CAAC;MACjCT,IAAI,EAAE;KACP,CACF;IAED,IAAI,CAACd,eAAe,CAACsC,IAAI,CAAC/B,cAAc,CAAC;IACzC,IAAI,CAACR,cAAc,CAACuC,IAAI,CAACX,aAAa,CAAC;IACvC,IAAI,CAAC1B,gBAAgB,CAACqC,IAAI,CAAC,EAAE,CAAC;EAChC;EAEAC,UAAUA,CAAA;IACR,OAAO,IAAI,CAACrC,QAAQ;EACtB;EAEAsC,gBAAgBA,CAAC1B,IAA2B;IAC1C,OAAO,IAAItB,4CAAU,CAACiD,QAAQ,IAAG;MAC/B,IAAI,CAACvC,QAAQ,CAACwC,SAAS,CAACrB,OAAO,IAAG;QAChC,MAAMsB,QAAQ,GAAGtB,OAAO,CAACuB,MAAM,CAACC,MAAM,IACpCA,MAAM,CAAC/B,IAAI,KAAKA,IAAI,IAAI+B,MAAM,CAAC/B,IAAI,KAAK,MAAM,CAC/C;QACD2B,QAAQ,CAACH,IAAI,CAACK,QAAQ,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAG,oBAAoBA,CAACnC,QAAwB;IAC3C,OAAO,IAAInB,4CAAU,CAACiD,QAAQ,IAAG;MAC/B,IAAI,CAACvC,QAAQ,CAACwC,SAAS,CAACrB,OAAO,IAAG;QAChC,MAAMsB,QAAQ,GAAGtB,OAAO,CAACuB,MAAM,CAACC,MAAM,IAAIA,MAAM,CAAClC,QAAQ,KAAKA,QAAQ,CAAC;QACvE8B,QAAQ,CAACH,IAAI,CAACK,QAAQ,CAAC;MACzB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAI,aAAaA,CAACvC,EAAU;IACtB,OAAO,IAAIhB,4CAAU,CAACiD,QAAQ,IAAG;MAC/B,IAAI,CAACvC,QAAQ,CAACwC,SAAS,CAACrB,OAAO,IAAG;QAChC,MAAMwB,MAAM,GAAGxB,OAAO,CAAC2B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzC,EAAE,KAAKA,EAAE,CAAC,IAAI,IAAI;QACrDiC,QAAQ,CAACH,IAAI,CAACO,MAAM,CAAC;MACvB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEAK,WAAWA,CAAA;IACT,OAAO,IAAI,CAAC9C,SAAS;EACvB;EAEA+C,cAAcA,CAAC3C,EAAU;IACvB,OAAO,IAAIhB,4CAAU,CAACiD,QAAQ,IAAG;MAC/B,IAAI,CAACrC,SAAS,CAACsC,SAAS,CAACU,QAAQ,IAAG;QAClC,MAAMC,OAAO,GAAGD,QAAQ,CAACJ,IAAI,CAACM,CAAC,IAAIA,CAAC,CAAC9C,EAAE,KAAKA,EAAE,CAAC,IAAI,IAAI;QACvDiC,QAAQ,CAACH,IAAI,CAACe,OAAO,CAAC;MACxB,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEME,cAAcA,CAACC,QAAgB,EAAEC,MAAc;IAAA,IAAAC,KAAA;IAAA,OAAAC,wJAAA;MACnD,MAAMtC,OAAO,GAAGqC,KAAI,CAAC3D,cAAc,CAAC6D,KAAK;MACzC,MAAMf,MAAM,GAAGxB,OAAO,CAAC2B,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACzC,EAAE,KAAKgD,QAAQ,CAAC;MAEnD,IAAI,CAACX,MAAM,EAAE;QACX,MAAM,IAAIgB,KAAK,CAAC,wBAAwB,CAAC;;MAG3C,IAAI,CAAChB,MAAM,CAACzB,QAAQ,EAAE;QACpB,MAAM,IAAIyC,KAAK,CAAC,2BAA2B,CAAC;;MAG9C,IAAIhB,MAAM,CAACX,iBAAiB,IAAIW,MAAM,CAACX,iBAAiB,IAAI,CAAC,EAAE;QAC7D,MAAM,IAAI2B,KAAK,CAAC,cAAc,CAAC;;MAGjC;MACA,MAAMC,QAAQ,GAAmB;QAC/BtD,EAAE,EAAEkD,KAAI,CAACK,kBAAkB,EAAE;QAC7BN,MAAM;QACND,QAAQ;QACRQ,WAAW,EAAEnB,MAAM,CAAChB,cAAc;QAClCoC,MAAM,EAAErE,mDAAc,CAACsE,SAAS;QAChCC,YAAY,EAAET,KAAI,CAACU,oBAAoB,EAAE;QACzCC,WAAW,EAAE,IAAI9C,IAAI,EAAE;QACvBY,UAAU,EAAEU,MAAM,CAACV,UAAU,IAAI,IAAIZ,IAAI,CAACA,IAAI,CAAC+C,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;OAClF;MAED;MACA,IAAIzB,MAAM,CAACX,iBAAiB,EAAE;QAC5BW,MAAM,CAACX,iBAAiB,EAAE;QAC1BwB,KAAI,CAAC3D,cAAc,CAACuC,IAAI,CAAC,CAAC,GAAGjB,OAAO,CAAC,CAAC;;MAGxC;MACA,MAAMkD,SAAS,GAAGb,KAAI,CAACzD,gBAAgB,CAAC2D,KAAK;MAC7CF,KAAI,CAACzD,gBAAgB,CAACqC,IAAI,CAAC,CAAC,GAAGiC,SAAS,EAAET,QAAQ,CAAC,CAAC;MAEpD,OAAOA,QAAQ;IAAC;EAClB;EAEAU,gBAAgBA,CAACf,MAAc;IAC7B,OAAO,IAAIjE,4CAAU,CAACiD,QAAQ,IAAG;MAC/B,IAAI,CAACpC,UAAU,CAACqC,SAAS,CAAC6B,SAAS,IAAG;QACpC,MAAME,aAAa,GAAGF,SAAS,CAAC3B,MAAM,CAAC8B,CAAC,IAAIA,CAAC,CAACjB,MAAM,KAAKA,MAAM,CAAC;QAChEhB,QAAQ,CAACH,IAAI,CAACmC,aAAa,CAAC;MAC9B,CAAC,CAAC;IACJ,CAAC,CAAC;EACJ;EAEQV,kBAAkBA,CAAA;IACxB,OAAO,KAAK,GAAGxC,IAAI,CAAC+C,GAAG,EAAE,CAACK,QAAQ,EAAE,GAAGC,IAAI,CAACC,MAAM,EAAE,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC;EAChF;EAEQV,oBAAoBA,CAAA;IAC1B,OAAOQ,IAAI,CAACC,MAAM,EAAE,CAACF,QAAQ,CAAC,EAAE,CAAC,CAACG,MAAM,CAAC,CAAC,EAAE,CAAC,CAAC,CAACC,WAAW,EAAE;EAC9D;EAEA;EACAC,aAAaA,CAAA;IACX,OAAOC,MAAM,CAACC,MAAM,CAACxF,mDAAc,CAAC;EACtC;EAEAyF,sBAAsBA,CAACxE,QAAwB;IAC7C,MAAMyE,KAAK,GAAwC;MACjD,CAAC1F,mDAAc,CAACsC,IAAI,GAAG,cAAc;MACrC,CAACtC,mDAAc,CAAC2F,QAAQ,GAAG,UAAU;MACrC,CAAC3F,mDAAc,CAAC4F,aAAa,GAAG,gBAAgB;MAChD,CAAC5F,mDAAc,CAAC6F,QAAQ,GAAG,UAAU;MACrC,CAAC7F,mDAAc,CAAC8F,MAAM,GAAG,OAAO;MAChC,CAAC9F,mDAAc,CAAC2C,SAAS,GAAG,WAAW;MACvC,CAAC3C,mDAAc,CAAC+F,SAAS,GAAG,WAAW;MACvC,CAAC/F,mDAAc,CAACgG,KAAK,GAAG;KACzB;IACD,OAAON,KAAK,CAACzE,QAAQ,CAAC;EACxB;;;uBAtRWd,cAAc;IAAA;EAAA;;;aAAdA,cAAc;MAAA8F,OAAA,EAAd9F,cAAc,CAAA+F,IAAA;MAAAC,UAAA,EAFb;IAAM;EAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACF2D;AAEpC;;;;;;;;;;;;;;ICQ/BE,4DALV,aAA2E,aACjD,kBACM,uBACR,aACU,mBAC4B;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAW;IAEnEA,4DADF,cAAuB,SACjB;IAAAA,oDAAA,GAAsB;IAAAA,0DAAA,EAAK;IAC/BA,4DAAA,SAAG;IAAAA,oDAAA,4BAAe;IAI1BA,0DAJ0B,EAAI,EAClB,EACF,EACW,EACV;IAKLA,4DAHN,mBAA4B,wBACR,cACU,oBAC4B;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAElEA,4DADF,eAAuB,UACjB;IAAAA,oDAAA,IAAoC;IAAAA,0DAAA,EAAK;IAC7CA,4DAAA,SAAG;IAAAA,oDAAA,iCAAe;IAI1BA,0DAJ0B,EAAI,EAClB,EACF,EACW,EACV;IAKLA,4DAHN,mBAA4B,wBACR,cACU,oBAC4B;IAAAA,oDAAA,oBAAY;IAAAA,0DAAA,EAAW;IAEzEA,4DADF,eAAuB,UACjB;IAAAA,oDAAA,IAAsE;IAAAA,0DAAA,EAAK;IAC/EA,4DAAA,SAAG;IAAAA,oDAAA,mBAAW;IAItBA,0DAJsB,EAAI,EACd,EACF,EACW,EACV;IAKLA,4DAHN,mBAA4B,wBACR,cACU,oBAC4B;IAAAA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAW;IAErEA,4DADF,eAAuB,UACjB;IAAAA,oDAAA,IAAiE;IAAAA,0DAAA,EAAK;IAC1EA,4DAAA,SAAG;IAAAA,oDAAA,sBAAS;IAMxBA,0DANwB,EAAI,EACZ,EACF,EACW,EACV,EACP,EACF;;;;;IA3CUA,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAM,YAAA,CAAAC,MAAA,CAAsB;IAYtBP,uDAAA,IAAoC;IAApCA,+DAAA,CAAAQ,MAAA,CAAAC,mBAAA,CAAAH,YAAA,EAAoC;IAYpCN,uDAAA,IAAsE;IAAtEA,+DAAA,CAAAQ,MAAA,CAAAE,oBAAA,CAAAJ,YAAA,EAAAE,MAAA,CAAA3G,cAAA,CAAAsE,SAAA,EAAAoC,MAAA,CAAsE;IAYtEP,uDAAA,IAAiE;IAAjEA,+DAAA,CAAAQ,MAAA,CAAAE,oBAAA,CAAAJ,YAAA,EAAAE,MAAA,CAAA3G,cAAA,CAAA8G,IAAA,EAAAJ,MAAA,CAAiE;;;;;;IAwCnEP,4DAFJ,cAAyD,cAC5B,eACf;IAAAA,oDAAA,cAAO;IAAAA,0DAAA,EAAW;IAE1BA,4DADF,cAAuB,YACd;IAAAA,oDAAA,0BAAc;IAAAA,0DAAA,EAAQ;IAE3BA,4DADF,cAAwB,eACH;IAAAA,oDAAA,GAA2B;IAAAA,0DAAA,EAAO;IACrDA,4DAAA,kBAEoC;IAD5BA,wDAAA,mBAAAa,yFAAA;MAAAb,2DAAA,CAAAe,GAAA;MAAA,MAAAC,WAAA,GAAAhB,2DAAA,GAAAkB,SAAA;MAAA,MAAAV,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAY,gBAAA,CAAAJ,WAAA,CAAA5C,YAAA,CAAuC;IAAA,EAAC;IAEvD4B,4DAAA,gBAAU;IAAAA,oDAAA,oBAAY;IAKhCA,0DALgC,EAAW,EAC1B,EACL,EACF,EACF,EACF;;;;IATqBA,uDAAA,GAA2B;IAA3BA,+DAAA,CAAAgB,WAAA,CAAA5C,YAAA,CAA2B;;;;;IAsBpD4B,4DADF,cAAoD,eACoB;IACpEA,oDAAA,GACF;IAAAA,0DAAA,EAAW;IAETA,4DADF,UAAK,YACI;IAAAA,oDAAA,sBAAe;IAAAA,0DAAA,EAAQ;IAC9BA,4DAAA,WAA4C;IAC1CA,oDAAA,GACF;IAEJA,0DAFI,EAAO,EACH,EACF;;;;;IATMA,uDAAA,EAA2D;IAA3DA,yDAAA,UAAAQ,MAAA,CAAAc,SAAA,CAAAN,WAAA,0BAA2D;IACnEhB,uDAAA,EACF;IADEA,gEAAA,MAAAQ,MAAA,CAAAc,SAAA,CAAAN,WAAA,0CACF;IAGQhB,uDAAA,GAAqC;IAArCA,yDAAA,YAAAQ,MAAA,CAAAc,SAAA,CAAAN,WAAA,EAAqC;IACzChB,uDAAA,EACF;IADEA,gEAAA,MAAAQ,MAAA,CAAAiB,UAAA,CAAAT,WAAA,CAAA5E,UAAA,OACF;;;;;IAMF4D,4DADF,cAAgD,mBACZ;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAW;IAEnDA,4DADF,UAAK,YACI;IAAAA,oDAAA,sBAAU;IAAAA,0DAAA,EAAQ;IACzBA,4DAAA,WAAM;IAAAA,oDAAA,GAAiC;IAE3CA,0DAF2C,EAAO,EAC1C,EACF;;;;;IAFIA,uDAAA,GAAiC;IAAjCA,+DAAA,CAAAQ,MAAA,CAAAiB,UAAA,CAAAT,WAAA,CAAAU,MAAA,EAAiC;;;;;IAmBzC1B,4DAHN,uBAAmD,cACnB,cACI,eACpB;IAAAA,oDAAA,WAAI;IAAAA,0DAAA,EAAW;IACzBA,4DAAA,WAAM;IAAAA,oDAAA,+EAA8D;IAG1EA,0DAH0E,EAAO,EACvE,EACF,EACW;;;;;IArFbA,4DARR,mBAEoD,sBAGjC,cACc,cACC,SACtB;IAAAA,oDAAA,yCAAmB;IAAAA,0DAAA,EAAK;IAE1BA,4DADF,mBAA6D,eACjD;IAAAA,oDAAA,GAAoC;IAAAA,0DAAA,EAAW;IACzDA,oDAAA,GACF;IACFA,0DADE,EAAW,EACP;IAGJA,4DADF,eAA6B,gBACjB;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,YAAM;IAAAA,oDAAA,IAA8B;IAG1CA,0DAH0C,EAAO,EACvC,EACF,EACU;IAIhBA,4DADF,wBAAkB,eACc;IAE5BA,wDAAA,KAAA4B,+DAAA,mBAAyD;IAmBvD5B,4DADF,eAAwB,gBACZ;IAAAA,oDAAA,gBAAQ;IAAAA,0DAAA,EAAW;IAE3BA,4DADF,WAAK,aACI;IAAAA,oDAAA,2BAAc;IAAAA,0DAAA,EAAQ;IAC7BA,4DAAA,YAAM;IAAAA,oDAAA,IAAsC;IAEhDA,0DAFgD,EAAO,EAC/C,EACF;IAgBNA,wDAbA,KAAA6B,+DAAA,kBAAoD,KAAAC,+DAAA,kBAaJ;IAU9C9B,4DADF,eAAwB,gBACZ;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAExBA,4DADF,WAAK,aACI;IAAAA,oDAAA,kBAAU;IAAAA,0DAAA,EAAQ;IACzBA,4DAAA,YAAM;IAAAA,oDAAA,wBAAgB;IAI9BA,0DAJ8B,EAAO,EACzB,EACF,EACF,EACW;IAGnBA,wDAAA,KAAA+B,4EAAA,+BAAmD;IAQrD/B,0DAAA,EAAW;;;;;;IA5FDA,yDAAA,oBAAAgC,IAAA,aAAyC;IAOjChC,uDAAA,GAAyC;IAAzCA,wDAAA,UAAAQ,MAAA,CAAA0B,cAAA,CAAAlB,WAAA,CAAA9C,MAAA,EAAyC;IACvC8B,uDAAA,GAAoC;IAApCA,+DAAA,CAAAQ,MAAA,CAAA2B,aAAA,CAAAnB,WAAA,CAAA9C,MAAA,EAAoC;IAC9C8B,uDAAA,EACF;IADEA,gEAAA,MAAAQ,MAAA,CAAA4B,cAAA,CAAApB,WAAA,CAAA9C,MAAA,OACF;IAKM8B,uDAAA,GAA8B;IAA9BA,gEAAA,KAAAgB,WAAA,CAAA/C,WAAA,SAA8B;IASb+B,uDAAA,GAA8B;IAA9BA,wDAAA,SAAAQ,MAAA,CAAA6B,cAAA,CAAArB,WAAA,EAA8B;IAsB7ChB,uDAAA,GAAsC;IAAtCA,+DAAA,CAAAQ,MAAA,CAAAiB,UAAA,CAAAT,WAAA,CAAA1C,WAAA,EAAsC;IAKvB0B,uDAAA,EAAyB;IAAzBA,wDAAA,SAAAgB,WAAA,CAAA5E,UAAA,CAAyB;IAazB4D,uDAAA,EAAqB;IAArBA,wDAAA,SAAAgB,WAAA,CAAAU,MAAA,CAAqB;IAoB/B1B,uDAAA,GAA8B;IAA9BA,wDAAA,SAAAQ,MAAA,CAAA6B,cAAA,CAAArB,WAAA,EAA8B;;;;;IAvFrDhB,4DAAA,cAA2E;IACzEA,wDAAA,IAAAsC,wDAAA,yBAEoD;IA6FtDtC,0DAAA,EAAM;;;;IA/F2BA,uDAAA,EAAc;IAAdA,wDAAA,YAAAuC,YAAA,CAAc;;;;;IAuGrCvC,4DAJR,cAAyB,mBACM,uBACT,cACW,mBACI;IAAAA,oDAAA,aAAM;IAAAA,0DAAA,EAAW;IAC9CA,4DAAA,SAAI;IAAAA,oDAAA,wCAA4B;IAAAA,0DAAA,EAAK;IACrCA,4DAAA,QAAG;IAAAA,oDAAA,wIAA6G;IAAAA,0DAAA,EAAI;IAElHA,4DADF,kBAAgE,gBACpD;IAAAA,oDAAA,qBAAa;IAAAA,0DAAA,EAAW;IAClCA,oDAAA,6CACF;IAIRA,0DAJQ,EAAS,EACL,EACW,EACV,EACP;;;;;IAnHVA,4DAAA,cAAuE;IAoGrEA,wDAnGA,IAAAwC,6CAAA,kBAA2E,IAAAC,qDAAA,iCAAAzC,oEAAA,CAmGjD;IAiB5BA,0DAAA,EAAM;;;;;IApHyBA,uDAAA,EAA4B;IAAAA,wDAA5B,SAAAuC,YAAA,CAAAhC,MAAA,KAA4B,aAAAoC,cAAA,CAAgB;;;;;IA0HrE3C,4DAHN,cAA6D,mBAC5B,uBACX,cACa;IAC3BA,uDAAA,sBAAyC;IACzCA,4DAAA,SAAI;IAAAA,oDAAA,oCAA6B;IAIzCA,0DAJyC,EAAK,EAClC,EACW,EACV,EACP;;;ADlLF,MAAO6C,wBAAwB;EAMnC9I,YACU+I,cAA8B,EAC9BC,WAAwB;IADxB,KAAAD,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IANrB,KAAAC,WAAW,GAAgB,IAAI;IAE/B,KAAAnJ,cAAc,GAAGA,wDAAc,CAAC,CAAC;IAM/B,IAAI,CAACS,UAAU,GAAG,IAAI,CAACyI,WAAW,CAACE,YAAY,CAACC,IAAI,CAClDnD,yDAAS,CAACoD,IAAI,IAAG;MACf,IAAIA,IAAI,EAAE;QACR,OAAO,IAAI,CAACL,cAAc,CAACrE,gBAAgB,CAAC0E,IAAI,CAACC,GAAG,CAAC;;MAEvD,OAAO,EAAE;IACX,CAAC,CAAC,CACH;EACH;EAEAC,QAAQA,CAAA;IACN,IAAI,CAACN,WAAW,CAACE,YAAY,CAACtG,SAAS,CAACwG,IAAI,IAAG;MAC7C,IAAI,CAACH,WAAW,GAAGG,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAhB,aAAaA,CAACjE,MAAsB;IAClC,QAAQA,MAAM;MACZ,KAAKrE,wDAAc,CAACyJ,OAAO;QACzB,OAAO,UAAU;MACnB,KAAKzJ,wDAAc,CAACsE,SAAS;QAC3B,OAAO,cAAc;MACvB,KAAKtE,wDAAc,CAAC8G,IAAI;QACtB,OAAO,UAAU;MACnB,KAAK9G,wDAAc,CAAC0J,OAAO;QACzB,OAAO,SAAS;MAClB,KAAK1J,wDAAc,CAAC2J,SAAS;QAC3B,OAAO,QAAQ;MACjB;QACE,OAAO,MAAM;;EAEnB;EAEAtB,cAAcA,CAAChE,MAAsB;IACnC,QAAQA,MAAM;MACZ,KAAKrE,wDAAc,CAACyJ,OAAO;QACzB,OAAO,QAAQ;MACjB,KAAKzJ,wDAAc,CAACsE,SAAS;QAC3B,OAAO,SAAS;MAClB,KAAKtE,wDAAc,CAAC8G,IAAI;QACtB,OAAO,SAAS;MAClB,KAAK9G,wDAAc,CAAC0J,OAAO;QACzB,OAAO,MAAM;MACf,KAAK1J,wDAAc,CAAC2J,SAAS;QAC3B,OAAO,MAAM;MACf;QACE,OAAO,OAAO;;EAEpB;EAEApB,cAAcA,CAAClE,MAAsB;IACnC,QAAQA,MAAM;MACZ,KAAKrE,wDAAc,CAACyJ,OAAO;QACzB,OAAO,YAAY;MACrB,KAAKzJ,wDAAc,CAACsE,SAAS;QAC3B,OAAO,UAAU;MACnB,KAAKtE,wDAAc,CAAC8G,IAAI;QACtB,OAAO,SAAS;MAClB,KAAK9G,wDAAc,CAAC0J,OAAO;QACzB,OAAO,QAAQ;MACjB,KAAK1J,wDAAc,CAAC2J,SAAS;QAC3B,OAAO,QAAQ;MACjB;QACE,OAAO,SAAS;;EAEtB;EAEA/B,UAAUA,CAACgC,IAAU;IACnB,OAAO,IAAIjI,IAAI,CAACiI,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MAChDC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,OAAO;MACdC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEAzC,SAASA,CAACvD,QAAwB;IAChC,IAAI,CAACA,QAAQ,CAAC3B,UAAU,EAAE,OAAO,KAAK;IACtC,OAAO,IAAIZ,IAAI,EAAE,GAAG,IAAIA,IAAI,CAACuC,QAAQ,CAAC3B,UAAU,CAAC;EACnD;EAEAiG,cAAcA,CAACtE,QAAwB;IACrC,OAAOA,QAAQ,CAACG,MAAM,KAAKrE,wDAAc,CAACsE,SAAS,IAAI,CAAC,IAAI,CAACmD,SAAS,CAACvD,QAAQ,CAAC;EAClF;EAEAqD,gBAAgBA,CAAC4C,IAAY;IAC3BC,SAAS,CAACC,SAAS,CAACC,SAAS,CAACH,IAAI,CAAC,CAACI,IAAI,CAAC,MAAK;MAC5C;IAAA,CACD,CAAC;EACJ;EAEA3D,mBAAmBA,CAACjC,SAA2B;IAC7C,OAAOA,SAAS,CAAC6F,MAAM,CAAC,CAACC,KAAK,EAAEvG,QAAQ,KAAKuG,KAAK,GAAGvG,QAAQ,CAACE,WAAW,EAAE,CAAC,CAAC;EAC/E;EAEAyC,oBAAoBA,CAAClC,SAA2B,EAAEN,MAAsB;IACtE,OAAOM,SAAS,CAAC3B,MAAM,CAACkB,QAAQ,IAAIA,QAAQ,CAACG,MAAM,KAAKA,MAAM,CAAC;EACjE;;;uBA5GW2E,wBAAwB,EAAA7C,+DAAA,CAAAwE,0EAAA,GAAAxE,+DAAA,CAAAyE,oEAAA;IAAA;EAAA;;;YAAxB5B,wBAAwB;MAAA8B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,kCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCTjCjF,4DAHJ,aAAwC,aAED,SAC/B;UAAAA,oDAAA,gDAA0B;UAAAA,0DAAA,EAAK;UACnCA,4DAAA,QAAG;UAAAA,oDAAA,yDAAmC;UACxCA,0DADwC,EAAI,EACtC;UAGNA,wDAAA,IAAAmF,uCAAA,kBAA2E;;UAqD3EnF,wDAAA,IAAAoF,uCAAA,iBAAuE;;UAwHvEpF,wDAAA,KAAAqF,wCAAA,iBAA6D;;UAU/DrF,0DAAA,EAAM;;;UAvLgCA,uDAAA,GAAyB;UAAzBA,wDAAA,SAAAA,yDAAA,OAAAkF,GAAA,CAAA5K,UAAA,EAAyB;UAqD7B0F,uDAAA,GAAyB;UAAzBA,wDAAA,SAAAA,yDAAA,OAAAkF,GAAA,CAAA5K,UAAA,EAAyB;UAwHzB0F,uDAAA,GAA2B;UAA3BA,wDAAA,UAAAA,yDAAA,QAAAkF,GAAA,CAAA5K,UAAA,EAA2B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC9Kb;;;;;;;;;;;;;;;ICuBtC0F,4DAJF,mBAGkB,eACN;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAW;IAC7BA,oDAAA,4BACF;IAAAA,0DAAA,EAAW;;;;;IAiDTA,4DADF,cAAqD,eACzC;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,WAAM;IAAAA,oDAAA,GAAmD;IAC3DA,0DAD2D,EAAO,EAC5D;;;;;IADEA,uDAAA,GAAmD;IAAnDA,gEAAA,qBAAAQ,MAAA,CAAAiB,UAAA,CAAA8D,SAAA,CAAAnJ,UAAA,MAAmD;;;;;;IAoBrD4D,4DADF,cAAgD,eACpC;IAAAA,oDAAA,kBAAW;IAAAA,0DAAA,EAAW;IAChCA,4DAAA,WAAM;IAAAA,oDAAA,GAAqB;IAAAA,0DAAA,EAAO;IAClCA,4DAAA,iBAA2F;IAAnEA,wDAAA,mBAAAwF,gFAAA;MAAAxF,2DAAA,CAAAyF,GAAA;MAAA,MAAAC,UAAA,GAAA1F,2DAAA,GAAA2F,IAAA;MAAA,MAAAnF,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAoF,aAAA,CAAAF,UAAA,CAAsB;IAAA,EAAC;IACtD1F,4DAAA,eAAU;IAAAA,oDAAA,iBAAU;IAExBA,0DAFwB,EAAW,EACxB,EACL;;;;IAJEA,uDAAA,GAAqB;IAArBA,+DAAA,CAAA0F,UAAA,CAAA5K,OAAA,CAAqB;;;;;;IAO3BkF,4DADF,cAA8C,eAClC;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,WAAM;IAAAA,oDAAA,GAAmB;IAAAA,0DAAA,EAAO;IAChCA,4DAAA,iBAA+E;IAAvDA,wDAAA,mBAAA6F,gFAAA;MAAA7F,2DAAA,CAAA8F,GAAA;MAAA,MAAAJ,UAAA,GAAA1F,2DAAA,GAAA2F,IAAA;MAAA,MAAAnF,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAuF,cAAA,CAAAL,UAAA,CAAuB;IAAA,EAAC;IACvD1F,4DAAA,eAAU;IAAAA,oDAAA,WAAI;IAElBA,0DAFkB,EAAW,EAClB,EACL;;;;IAJEA,uDAAA,GAAmB;IAAnBA,+DAAA,CAAA0F,UAAA,CAAA1K,KAAA,CAAmB;;;;;;IAOzBgF,4DADF,cAA8C,eAClC;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,WAAM;IAAAA,oDAAA,GAAmB;IAAAA,0DAAA,EAAO;IAChCA,4DAAA,iBAAwF;IAAhEA,wDAAA,mBAAAgG,gFAAA;MAAAhG,2DAAA,CAAAiG,GAAA;MAAA,MAAAP,UAAA,GAAA1F,2DAAA,GAAA2F,IAAA;MAAA,MAAAnF,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAuF,cAAA,CAAAL,UAAA,CAAuB;IAAA,EAAC;IACvD1F,4DAAA,eAAU;IAAAA,oDAAA,WAAI;IAElBA,0DAFkB,EAAW,EAClB,EACL;;;;IAJEA,uDAAA,GAAmB;IAAnBA,+DAAA,CAAA0F,UAAA,CAAAzK,KAAA,CAAmB;;;;;IA5B7B+E,4DAHN,mBAAkE,sBAC/C,qBACC,eACJ;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAW;IAC7BA,oDAAA,oCACF;IACFA,0DADE,EAAiB,EACD;IAGdA,4DAFJ,uBAAkB,cACU,SACpB;IAAAA,oDAAA,GAAkB;IAAAA,0DAAA,EAAK;IAC3BA,4DAAA,SAAG;IAAAA,oDAAA,IAAyB;IAAAA,0DAAA,EAAI;IAEhCA,4DAAA,eAA6B;IAiB3BA,wDAhBA,KAAAkG,uDAAA,kBAAgD,KAAAC,uDAAA,kBAQF,KAAAC,uDAAA,kBAQA;IAUtDpG,0DAHM,EAAM,EACF,EACW,EACV;;;;IA9BDA,uDAAA,GAAkB;IAAlBA,+DAAA,CAAA0F,UAAA,CAAAhL,IAAA,CAAkB;IACnBsF,uDAAA,GAAyB;IAAzBA,+DAAA,CAAA0F,UAAA,CAAA/K,WAAA,CAAyB;IAGDqF,uDAAA,GAAqB;IAArBA,wDAAA,SAAA0F,UAAA,CAAA5K,OAAA,CAAqB;IAQrBkF,uDAAA,EAAmB;IAAnBA,wDAAA,SAAA0F,UAAA,CAAA1K,KAAA,CAAmB;IAQnBgF,uDAAA,EAAmB;IAAnBA,wDAAA,SAAA0F,UAAA,CAAAzK,KAAA,CAAmB;;;;;IAyB5C+E,4DADF,cAAoD,eACxC;IAAAA,oDAAA,oBAAa;IAAAA,0DAAA,EAAW;IAEhCA,4DADF,UAAK,SACC;IAAAA,oDAAA,8BAAa;IAAAA,0DAAA,EAAK;IACtBA,4DAAA,YAAwB;IAAAA,oDAAA,GAAuD;IAEnFA,0DAFmF,EAAI,EAC/E,EACF;;;;;IAFsBA,uDAAA,GAAuD;IAAvDA,gEAAA,KAAAQ,MAAA,CAAAwC,WAAA,CAAAqD,MAAA,GAAAd,SAAA,CAAAzJ,cAAA,YAAuD;;;;;IAKjFkE,4DADF,cAAsD,eAC1C;IAAAA,oDAAA,iBAAU;IAAAA,0DAAA,EAAW;IAE7BA,4DADF,UAAK,SACC;IAAAA,oDAAA,8BAAkB;IAAAA,0DAAA,EAAK;IAC3BA,4DAAA,YAA+B;IAAAA,oDAAA,GAAuD;IAE1FA,0DAF0F,EAAI,EACtF,EACF;;;;;IAF6BA,uDAAA,GAAuD;IAAvDA,gEAAA,KAAAuF,SAAA,CAAAzJ,cAAA,GAAA0E,MAAA,CAAAwC,WAAA,CAAAqD,MAAA,YAAuD;;;;;IAnBxFrG,4DAJR,mBAAqE,uBACjD,cACW,cACG,eAChB;IAAAA,oDAAA,6BAAsB;IAAAA,0DAAA,EAAW;IAEzCA,4DADF,UAAK,SACC;IAAAA,oDAAA,yBAAkB;IAAAA,0DAAA,EAAK;IAC3BA,4DAAA,YAAwB;IAAAA,oDAAA,IAA+B;IAE3DA,0DAF2D,EAAI,EACvD,EACF;IAUNA,wDARA,KAAAsG,uDAAA,kBAAoD,KAAAC,uDAAA,kBAQE;IAS5DvG,0DAFI,EAAM,EACW,EACV;;;;;IArBuBA,uDAAA,IAA+B;IAA/BA,gEAAA,KAAAQ,MAAA,CAAAwC,WAAA,CAAAqD,MAAA,YAA+B;IAIhCrG,uDAAA,EAAuB;IAAvBA,wDAAA,SAAAQ,MAAA,CAAAgG,SAAA,CAAAjB,SAAA,EAAuB;IAQtBvF,uDAAA,EAAwB;IAAxBA,wDAAA,UAAAQ,MAAA,CAAAgG,SAAA,CAAAjB,SAAA,EAAwB;;;;;IAoBtDvF,uDAAA,sBAA8D;;;;;IAC9DA,4DAAA,eAAgC;IAAAA,oDAAA,GAA2C;IAAAA,0DAAA,EAAW;;;;;IAAtDA,uDAAA,EAA2C;IAA3CA,+DAAA,CAAAQ,MAAA,CAAAgG,SAAA,CAAAjB,SAAA,sBAA2C;;;;;IAC3EvF,4DAAA,WAA4B;IAC1BA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;;IADLA,uDAAA,EACF;IADEA,gEAAA,MAAAQ,MAAA,CAAAgG,SAAA,CAAAjB,SAAA,4DACF;;;;;IAOAvF,4DAJF,iBAGmC,eACvB;IAAAA,oDAAA,sBAAe;IAAAA,0DAAA,EAAW;IACpCA,oDAAA,0BACF;IAAAA,0DAAA,EAAS;;;;;IAITA,4DADF,YAAmD,eACvC;IAAAA,oDAAA,WAAI;IAAAA,0DAAA,EAAW;IACzBA,oDAAA,kFACF;IAAAA,0DAAA,EAAI;;;;;;IAzLJA,4DAHJ,aAAuE,aAE1C,gBACsC;IAAvCA,wDAAA,mBAAAyG,6DAAA;MAAAzG,2DAAA,CAAA0G,GAAA;MAAA,MAAAlG,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAmG,MAAA,EAAQ;IAAA,EAAC;IACxC3G,4DAAA,eAAU;IAAAA,oDAAA,iBAAU;IACtBA,0DADsB,EAAW,EACxB;IACTA,4DAAA,SAAI;IAAAA,oDAAA,yCAAwB;IAAAA,0DAAA,EAAK;IACjCA,4DAAA,gBAA2E;IAAnDA,wDAAA,mBAAA4G,6DAAA;MAAA,MAAArB,SAAA,GAAAvF,2DAAA,CAAA0G,GAAA,EAAAf,IAAA;MAAA,MAAAnF,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAqG,WAAA,CAAAtB,SAAA,CAAmB;IAAA,EAAC;IACnDvF,4DAAA,eAAU;IAAAA,oDAAA,YAAK;IAEnBA,0DAFmB,EAAW,EACnB,EACL;IAIJA,4DADF,cAAkC,cACR;IACtBA,uDAAA,cAE+D;IAK3DA,4DAFJ,cAAyB,oBACsC,gBACjD;IAAAA,oDAAA,IAAsC;IAAAA,0DAAA,EAAW;IAC3DA,oDAAA,IACF;IAAAA,0DAAA,EAAW;IAEXA,wDAAA,KAAA8G,gDAAA,uBAGkB;IAKtB9G,0DADE,EAAM,EACF;IAIFA,4DAFJ,eAA0B,eACU,UAC5B;IAAAA,oDAAA,IAAkB;IAAAA,0DAAA,EAAK;IAEzBA,4DADF,eAA0B,gBACd;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,YAAM;IAAAA,oDAAA,IAAkC;IAE5CA,0DAF4C,EAAO,EAC3C,EACF;IAENA,4DAAA,aAA8B;IAAAA,oDAAA,IAAwB;IAAAA,0DAAA,EAAI;IAItDA,4DAFJ,eAAyB,eACA,gBACX;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,YAAM;IAAAA,oDAAA,IAAwB;IAChCA,0DADgC,EAAO,EACjC;IAGJA,4DADF,eAAuB,gBACX;IAAAA,oDAAA,mBAAW;IAAAA,0DAAA,EAAW;IAChCA,4DAAA,YAAM;IAAAA,oDAAA,IAAiB;IACzBA,0DADyB,EAAO,EAC1B;IAGJA,4DADF,eAAuB,oBACqC;IACxDA,oDAAA,IACF;IAIRA,0DAJQ,EAAW,EACP,EACF,EACF,EACF;IAQEA,4DALR,eAAqC,oBAEL,uBACX,sBACC,gBACJ;IAAAA,oDAAA,mBAAW;IAAAA,0DAAA,EAAW;IAChCA,oDAAA,kCACF;IACFA,0DADE,EAAiB,EACD;IAEhBA,4DADF,wBAAkB,SACb;IAAAA,oDAAA,IAAsD;IAAAA,0DAAA,EAAI;IAE7DA,wDAAA,KAAA+G,2CAAA,kBAAqD;IAKzD/G,0DADE,EAAmB,EACV;IAGXA,wDAAA,KAAAgH,gDAAA,wBAAkE;;IA0ClEhH,wDAAA,KAAAiH,gDAAA,wBAAqE;IA6BvEjH,0DAAA,EAAM;IAKFA,4DAFJ,eAA4B,eACE,kBAKM;IADxBA,wDAAA,mBAAAkH,8DAAA;MAAA,MAAA3B,SAAA,GAAAvF,2DAAA,CAAA0G,GAAA,EAAAf,IAAA;MAAA,MAAAnF,MAAA,GAAAR,2DAAA;MAAA,OAAAA,yDAAA,CAASQ,MAAA,CAAAhD,cAAA,CAAA+H,SAAA,CAAsB;IAAA,EAAC;IAItCvF,wDAFA,KAAAmH,mDAAA,0BAAgD,KAAAC,gDAAA,uBAChB,KAAAC,4CAAA,mBACJ;IAG9BrH,0DAAA,EAAS;IAETA,wDAAA,KAAAsH,8CAAA,qBAGmC;IAIrCtH,0DAAA,EAAM;IAENA,wDAAA,KAAAuH,yCAAA,gBAAmD;IAKvDvH,0DADE,EAAM,EACF;;;;;IA/KKA,uDAAA,IAAiE;IACjEA,wDADA,QAAAuF,SAAA,CAAArJ,QAAA,4CAAA8D,2DAAA,CAAiE,QAAAuF,SAAA,CAAA1J,KAAA,CAC7C;IAKSmE,uDAAA,GAAmB;IAAnBA,wDAAA,oBAAmB;IACvCA,uDAAA,GAAsC;IAAtCA,+DAAA,CAAAQ,MAAA,CAAAiH,eAAA,CAAAlC,SAAA,CAAA3K,QAAA,EAAsC;IAChDoF,uDAAA,EACF;IADEA,gEAAA,MAAAQ,MAAA,CAAAkH,gBAAA,CAAAnC,SAAA,CAAA3K,QAAA,OACF;IAEWoF,uDAAA,EAA4B;IAA5BA,wDAAA,SAAAQ,MAAA,CAAAmH,cAAA,CAAApC,SAAA,EAA4B;IAYnCvF,uDAAA,GAAkB;IAAlBA,+DAAA,CAAAuF,SAAA,CAAA1J,KAAA,CAAkB;IAGdmE,uDAAA,GAAkC;IAAlCA,gEAAA,KAAAuF,SAAA,CAAAzJ,cAAA,YAAkC;IAIdkE,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAuF,SAAA,CAAA5K,WAAA,CAAwB;IAK5CqF,uDAAA,GAAwB;IAAxBA,+DAAA,CAAAuF,SAAA,CAAAvJ,WAAA,CAAwB;IAKxBgE,uDAAA,GAAiB;IAAjBA,+DAAA,CAAAuF,SAAA,CAAAxK,IAAA,CAAiB;IAIbiF,uDAAA,GAAsC;IAAtCA,wDAAA,UAAAQ,MAAA,CAAAoH,oBAAA,CAAArC,SAAA,EAAsC;IAC9CvF,uDAAA,EACF;IADEA,gEAAA,MAAAQ,MAAA,CAAAqH,mBAAA,CAAAtC,SAAA,OACF;IAiBCvF,uDAAA,IAAsD;IAAtDA,+DAAA,CAAAuF,SAAA,CAAAlJ,KAAA,0CAAsD;IAE7B2D,uDAAA,EAAuB;IAAvBA,wDAAA,SAAAuF,SAAA,CAAAnJ,UAAA,CAAuB;IAQxB4D,uDAAA,EAAuB;IAAvBA,wDAAA,SAAAA,yDAAA,SAAAQ,MAAA,CAAAsH,QAAA,EAAuB;IA0CJ9H,uDAAA,GAAiB;IAAjBA,wDAAA,SAAAQ,MAAA,CAAAwC,WAAA,CAAiB;IAmCzDhD,uDAAA,GAAiD;IACjDA,wDADA,UAAAQ,MAAA,CAAAgG,SAAA,CAAAjB,SAAA,wBAAiD,cAAA/E,MAAA,CAAAgG,SAAA,CAAAjB,SAAA,MAAAA,SAAA,CAAApJ,iBAAA,IAAAqE,MAAA,CAAAuH,YAAA,CAC2B;IAGtD/H,uDAAA,EAAkB;IAAlBA,wDAAA,SAAAQ,MAAA,CAAAuH,YAAA,CAAkB;IACnC/H,uDAAA,EAAmB;IAAnBA,wDAAA,UAAAQ,MAAA,CAAAuH,YAAA,CAAmB;IACvB/H,uDAAA,EAAmB;IAAnBA,wDAAA,UAAAQ,MAAA,CAAAuH,YAAA,CAAmB;IAQnB/H,uDAAA,EAAwB;IAAxBA,wDAAA,UAAAQ,MAAA,CAAAgG,SAAA,CAAAjB,SAAA,EAAwB;IAMTvF,uDAAA,EAAuB;IAAvBA,wDAAA,SAAAQ,MAAA,CAAAgG,SAAA,CAAAjB,SAAA,EAAuB;;;;;IAW/CvF,4DAHN,cAA0D,mBACzB,uBACX,cACa;IAC3BA,uDAAA,sBAAyC;IACzCA,4DAAA,SAAI;IAAAA,oDAAA,qCAAyB;IAIrCA,0DAJqC,EAAK,EAC9B,EACW,EACV,EACP;;;AD5LA,MAAOgI,qBAAqB;EAMhCjO,YACUkO,KAAqB,EACrBC,MAAc,EACdpF,cAA8B,EAC9BC,WAAwB,EACxBoF,QAAqB;IAJrB,KAAAF,KAAK,GAALA,KAAK;IACL,KAAAC,MAAM,GAANA,MAAM;IACN,KAAApF,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAoF,QAAQ,GAARA,QAAQ;IARlB,KAAAnF,WAAW,GAAgB,IAAI;IAC/B,KAAA+E,YAAY,GAAG,KAAK;IASlB,IAAI,CAACK,OAAO,GAAG,IAAI,CAACH,KAAK,CAACI,MAAM,CAACnF,IAAI,CACnCnD,yDAAS,CAACsI,MAAM,IAAI,IAAI,CAACvF,cAAc,CAAC9F,aAAa,CAACqL,MAAM,CAAC,IAAI,CAAC,CAAC,CAAC,CACrE;IAED,IAAI,CAACP,QAAQ,GAAG,IAAI,CAACM,OAAO,CAAClF,IAAI,CAC/BnD,yDAAS,CAACjD,MAAM,IACdA,MAAM,GAAG,IAAI,CAACgG,cAAc,CAAC1F,cAAc,CAACN,MAAM,CAACf,SAAS,CAAC,GAAG,CAAC,IAAI,CAAC,CACvE,CACF;EACH;EAEAsH,QAAQA,CAAA;IACN,IAAI,CAACN,WAAW,CAACE,YAAY,CAACtG,SAAS,CAACwG,IAAI,IAAG;MAC7C,IAAI,CAACH,WAAW,GAAGG,IAAI;IACzB,CAAC,CAAC;EACJ;EAEAqD,SAASA,CAAC1J,MAAc;IACtB,OAAO,IAAI,CAACkG,WAAW,GAAG,IAAI,CAACA,WAAW,CAACqD,MAAM,IAAIvJ,MAAM,CAAChB,cAAc,GAAG,KAAK;EACpF;EAEM0B,cAAcA,CAACV,MAAc;IAAA,IAAAa,KAAA;IAAA,OAAAC,wJAAA;MACjC,IAAI,CAACD,KAAI,CAACqF,WAAW,EAAE;QACrBrF,KAAI,CAACwK,QAAQ,CAACG,IAAI,CAAC,uDAAuD,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACzG;;MAGF,IAAI,CAAC5K,KAAI,CAAC6I,SAAS,CAAC1J,MAAM,CAAC,EAAE;QAC3Ba,KAAI,CAACwK,QAAQ,CAACG,IAAI,CAAC,2CAA2C,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC7F;;MAGF,IAAIzL,MAAM,CAACX,iBAAiB,KAAKqM,SAAS,IAAI1L,MAAM,CAACX,iBAAiB,IAAI,CAAC,EAAE;QAC3EwB,KAAI,CAACwK,QAAQ,CAACG,IAAI,CAAC,yCAAyC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC3F;;MAGF5K,KAAI,CAACoK,YAAY,GAAG,IAAI;MAExB,IAAI;QACF,MAAMhK,QAAQ,SAASJ,KAAI,CAACmF,cAAc,CAACtF,cAAc,CAACV,MAAM,CAACrC,EAAE,EAAEkD,KAAI,CAACqF,WAAW,CAACI,GAAG,CAAC;QAE1FzF,KAAI,CAACwK,QAAQ,CAACG,IAAI,CAChB,oDAAoDvK,QAAQ,CAACK,YAAY,EAAE,EAC3E,QAAQ,EACR;UAAEmK,QAAQ,EAAE;QAAI,CAAE,CACnB;QAED;QAEA;QACA5K,KAAI,CAACuK,MAAM,CAACO,QAAQ,CAAC,CAAC,UAAU,CAAC,EAAE;UAAEC,QAAQ,EAAE;QAAW,CAAE,CAAC;OAE9D,CAAC,OAAOC,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;QACvChL,KAAI,CAACwK,QAAQ,CAACG,IAAI,CAACK,KAAK,CAACE,OAAO,IAAI,2BAA2B,EAAE,QAAQ,EAAE;UAAEN,QAAQ,EAAE;QAAI,CAAE,CAAC;OAC/F,SAAS;QACR5K,KAAI,CAACoK,YAAY,GAAG,KAAK;;IAC1B;EACH;EAEApB,MAAMA,CAAA;IACJ,IAAI,CAACuB,MAAM,CAACO,QAAQ,CAAC,CAAC,UAAU,CAAC,CAAC;EACpC;EAEAhB,eAAeA,CAAC7M,QAAgB;IAC9B,MAAMkO,OAAO,GAA8B;MACzC,MAAM,EAAE,YAAY;MACpB,UAAU,EAAE,cAAc;MAC1B,eAAe,EAAE,OAAO;MACxB,UAAU,EAAE,OAAO;MACnB,QAAQ,EAAE,gBAAgB;MAC1B,WAAW,EAAE,QAAQ;MACrB,WAAW,EAAE,gBAAgB;MAC7B,OAAO,EAAE;KACV;IACD,OAAOA,OAAO,CAAClO,QAAQ,CAAC,IAAI,UAAU;EACxC;EAEA8M,gBAAgBA,CAAC9M,QAAgB;IAC/B,MAAMmO,QAAQ,GAA8B;MAC1C,MAAM,EAAE,cAAc;MACtB,UAAU,EAAE,UAAU;MACtB,eAAe,EAAE,gBAAgB;MACjC,UAAU,EAAE,UAAU;MACtB,QAAQ,EAAE,OAAO;MACjB,WAAW,EAAE,WAAW;MACxB,WAAW,EAAE,WAAW;MACxB,OAAO,EAAE;KACV;IACD,OAAOA,QAAQ,CAACnO,QAAQ,CAAC,IAAIA,QAAQ;EACvC;EAEA6G,UAAUA,CAACgC,IAAU;IACnB,OAAO,IAAIjI,IAAI,CAACiI,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MAChDC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE;KACP,CAAC;EACJ;EAEA8D,cAAcA,CAAC7K,MAAc;IAC3B,IAAI,CAACA,MAAM,CAACV,UAAU,EAAE,OAAO,KAAK;IAEpC,MAAMmC,GAAG,GAAG,IAAI/C,IAAI,EAAE;IACtB,MAAMY,UAAU,GAAG,IAAIZ,IAAI,CAACsB,MAAM,CAACV,UAAU,CAAC;IAC9C,MAAM4M,eAAe,GAAGnK,IAAI,CAACoK,IAAI,CAAC,CAAC7M,UAAU,CAAC8M,OAAO,EAAE,GAAG3K,GAAG,CAAC2K,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEjG,OAAOF,eAAe,IAAI,CAAC,IAAIA,eAAe,GAAG,CAAC;EACpD;EAEAnB,mBAAmBA,CAAC/K,MAAc;IAChC,IAAI,CAACA,MAAM,CAACX,iBAAiB,EAAE;MAC7B,OAAO,YAAY;;IAGrB,IAAIW,MAAM,CAACX,iBAAiB,IAAI,CAAC,EAAE;MACjC,OAAO,QAAQ;;IAGjB,IAAIW,MAAM,CAACX,iBAAiB,IAAI,CAAC,EAAE;MACjC,OAAO,YAAYW,MAAM,CAACX,iBAAiB,gBAAgB;;IAG7D,OAAO,GAAGW,MAAM,CAACX,iBAAiB,gBAAgB;EACpD;EAEAyL,oBAAoBA,CAAC9K,MAAc;IACjC,IAAI,CAACA,MAAM,CAACX,iBAAiB,IAAIW,MAAM,CAACX,iBAAiB,GAAG,CAAC,EAAE;MAC7D,OAAO,SAAS;;IAGlB,IAAIW,MAAM,CAACX,iBAAiB,IAAI,CAAC,EAAE;MACjC,OAAO,MAAM;;IAGf,OAAO,QAAQ;EACjB;EAEA0K,WAAWA,CAAC/J,MAAc;IACxB,IAAImH,SAAS,CAACkF,KAAK,EAAE;MACnBlF,SAAS,CAACkF,KAAK,CAAC;QACdtN,KAAK,EAAE,qBAAqBiB,MAAM,CAACjB,KAAK,EAAE;QAC1CuN,IAAI,EAAE,yCAAyCtM,MAAM,CAACnC,WAAW,EAAE;QACnE0O,GAAG,EAAEC,MAAM,CAACpO,QAAQ,CAACqO;OACtB,CAAC,CAACC,KAAK,CAACZ,OAAO,CAACD,KAAK,CAAC;KACxB,MAAM;MACL;MACA1E,SAAS,CAACC,SAAS,CAACC,SAAS,CAACmF,MAAM,CAACpO,QAAQ,CAACqO,IAAI,CAAC,CAACnF,IAAI,CAAC,MAAK;QAC5D,IAAI,CAAC+D,QAAQ,CAACG,IAAI,CAAC,mCAAmC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACvF,CAAC,CAAC;;EAEN;EAEAxC,cAAcA,CAACzI,OAAgB;IAC7B,IAAIA,OAAO,CAACtC,KAAK,EAAE;MACjBsO,MAAM,CAAChB,IAAI,CAAC,OAAOhL,OAAO,CAACtC,KAAK,EAAE,EAAE,QAAQ,CAAC;KAC9C,MAAM,IAAIsC,OAAO,CAACrC,KAAK,EAAE;MACxBqO,MAAM,CAAChB,IAAI,CAAC,UAAUhL,OAAO,CAACrC,KAAK,EAAE,EAAE,QAAQ,CAAC;;EAEpD;EAEA2K,aAAaA,CAACtI,OAAgB;IAC5B,IAAIA,OAAO,CAACpC,QAAQ,EAAE;MACpB,MAAMmO,GAAG,GAAG,sDAAsD/L,OAAO,CAACpC,QAAQ,CAACC,QAAQ,IAAImC,OAAO,CAACpC,QAAQ,CAACE,SAAS,EAAE;MAC3HkO,MAAM,CAAChB,IAAI,CAACe,GAAG,EAAE,QAAQ,CAAC;KAC3B,MAAM,IAAI/L,OAAO,CAACxC,OAAO,EAAE;MAC1B,MAAMuO,GAAG,GAAG,mDAAmDI,kBAAkB,CAACnM,OAAO,CAACxC,OAAO,CAAC,EAAE;MACpGwO,MAAM,CAAChB,IAAI,CAACe,GAAG,EAAE,QAAQ,CAAC;;EAE9B;;;uBAvLWrB,qBAAqB,EAAAhI,+DAAA,CAAAwE,2DAAA,GAAAxE,+DAAA,CAAAwE,mDAAA,GAAAxE,+DAAA,CAAAyE,0EAAA,GAAAzE,+DAAA,CAAA4J,oEAAA,GAAA5J,+DAAA,CAAA6J,oEAAA;IAAA;EAAA;;;YAArB7B,qBAAqB;MAAArD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAgF,+BAAA9E,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCdlCjF,wDAAA,IAAAgK,oCAAA,mBAAuE;;UAiMvEhK,wDAAA,IAAAiK,oCAAA,iBAA0D;;;;UAjMpBjK,wDAAA,SAAAA,yDAAA,OAAAkF,GAAA,CAAAkD,OAAA,EAAsB;UAiM5BpI,uDAAA,GAAwB;UAAxBA,wDAAA,UAAAA,yDAAA,OAAAkF,GAAA,CAAAkD,OAAA,EAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AC3Le;AACtB;AACD;AACH;;;;;;;;;;;;;;;;;;;;ICFvCpI,4DADF,cAA6C,eACjC;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,WAAM;IAAAA,oDAAA,GAA2C;IACnDA,0DADmD,EAAO,EACpD;;;;IADEA,uDAAA,GAA2C;IAA3CA,gEAAA,KAAAsK,MAAA,CAAAtH,WAAA,CAAAqD,MAAA,wBAA2C;;;;;IAqBvCrG,4DADF,qBAAyE,eAC7D;IAAAA,oDAAA,GAAmB;IAAAA,0DAAA,EAAW;IACxCA,oDAAA,GACF;IAAAA,0DAAA,EAAa;;;;IAHmCA,wDAAA,UAAAuK,WAAA,CAAA1M,KAAA,CAAwB;IAC5DmC,uDAAA,GAAmB;IAAnBA,+DAAA,CAAAuK,WAAA,CAAAC,IAAA,CAAmB;IAC7BxK,uDAAA,EACF;IADEA,gEAAA,MAAAuK,WAAA,CAAAE,KAAA,MACF;;;;;IAQAzK,4DAAA,qBAA6D;IAC3DA,oDAAA,GACF;IAAAA,0DAAA,EAAa;;;;IAF2BA,wDAAA,UAAA0K,OAAA,CAAA7M,KAAA,CAAoB;IAC1DmC,uDAAA,EACF;IADEA,gEAAA,MAAA0K,OAAA,CAAAD,KAAA,MACF;;;;;IAsCFzK,4DAJF,mBAGkB,eACN;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAW;IAC7BA,oDAAA,4BACF;IAAAA,0DAAA,EAAW;;;;;IA6BXA,4DAAA,eAAoD;IAClDA,oDAAA,GACF;IAAAA,0DAAA,EAAO;;;;;IADLA,uDAAA,EACF;IADEA,gEAAA,sBAAAsK,MAAA,CAAAK,gBAAA,CAAAC,SAAA,CAAAxO,UAAA,OACF;;;;;;IAvDN4D,4DAAA,mBAG8C;IAApCA,wDAAA,mBAAA6K,0EAAA;MAAA,MAAAD,SAAA,GAAA5K,2DAAA,CAAAyF,GAAA,EAAAvE,SAAA;MAAA,MAAAoJ,MAAA,GAAAtK,2DAAA;MAAA,OAAAA,yDAAA,CAASsK,MAAA,CAAAQ,iBAAA,CAAAF,SAAA,CAAyB;IAAA,EAAC;IAG3C5K,4DAAA,cAA0B;IACxBA,uDAAA,cAE+D;IAK3DA,4DAFJ,cAA2B,mBACoC,eACjD;IAAAA,oDAAA,GAAsC;IAAAA,0DAAA,EAAW;IAC3DA,oDAAA,GACF;IAAAA,0DAAA,EAAW;IAEXA,wDAAA,IAAA+K,0DAAA,uBAGkB;IAKtB/K,0DADE,EAAM,EACF;IAKFA,4DAFJ,uBAAkB,eACW,UACrB;IAAAA,oDAAA,IAAkB;IAAAA,0DAAA,EAAK;IAEzBA,4DADF,eAA6B,gBACjB;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,YAAM;IAAAA,oDAAA,IAA2B;IAErCA,0DAFqC,EAAO,EACpC,EACF;IAENA,4DAAA,aAA8B;IAAAA,oDAAA,IAAwB;IAAAA,0DAAA,EAAI;IAGxDA,4DADF,eAA4B,gBAChB;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,YAAM;IAAAA,oDAAA,IAAwB;IAAAA,0DAAA,EAAO;IACrCA,4DAAA,oBAAwD;IACtDA,oDAAA,IACF;IACFA,0DADE,EAAW,EACP;IAGJA,4DADF,eAAiC,oBAC2B;IACxDA,oDAAA,IACF;IAAAA,0DAAA,EAAW;IAEXA,wDAAA,KAAAgL,uDAAA,mBAAoD;IAIxDhL,0DADE,EAAM,EACW;IAKfA,4DAFJ,wBAAkB,eACY,kBAKuB;IADzCA,wDAAA,mBAAAiL,yEAAAC,MAAA;MAAA,MAAAN,SAAA,GAAA5K,2DAAA,CAAAyF,GAAA,EAAAvE,SAAA;MAAA,MAAAoJ,MAAA,GAAAtK,2DAAA;MAASsK,MAAA,CAAA9M,cAAA,CAAAoN,SAAA,CAAsB;MAAA,OAAA5K,yDAAA,CAAEkL,MAAA,CAAAC,eAAA,EAAwB;IAAA,EAAC;IAEhEnL,4DAAA,gBAAU;IAAAA,oDAAA,IAA2C;IAAAA,0DAAA,EAAW;IAChEA,oDAAA,IACF;IAAAA,0DAAA,EAAS;IAETA,4DAAA,kBAGgD;IADxCA,wDAAA,mBAAAoL,yEAAAF,MAAA;MAAA,MAAAN,SAAA,GAAA5K,2DAAA,CAAAyF,GAAA,EAAAvE,SAAA;MAAA,MAAAoJ,MAAA,GAAAtK,2DAAA;MAASsK,MAAA,CAAAQ,iBAAA,CAAAF,SAAA,CAAyB;MAAA,OAAA5K,yDAAA,CAAEkL,MAAA,CAAAC,eAAA,EAAwB;IAAA,EAAC;IAEnEnL,4DAAA,gBAAU;IAAAA,oDAAA,YAAI;IAAAA,0DAAA,EAAW;IACzBA,oDAAA,sBACF;IAAAA,0DAAA,EAAS;IAETA,4DAAA,kBAI6B;IAFrBA,wDAAA,mBAAAqL,yEAAAH,MAAA;MAAA,MAAAN,SAAA,GAAA5K,2DAAA,CAAAyF,GAAA,EAAAvE,SAAA;MAAA,MAAAoJ,MAAA,GAAAtK,2DAAA;MAASsK,MAAA,CAAAgB,aAAA,CAAAV,SAAA,CAAqB;MAAA,OAAA5K,yDAAA,CAAEkL,MAAA,CAAAC,eAAA,EAAwB;IAAA,EAAC;IAG/DnL,4DAAA,gBAAU;IAAAA,oDAAA,IAA2D;IAI7EA,0DAJ6E,EAAW,EACzE,EACL,EACW,EACV;;;;;;IAtFDA,yDAAA,oBAAAuL,IAAA,aAAyC;IAK1CvL,uDAAA,GAAiE;IACjEA,wDADA,QAAA4K,SAAA,CAAA1O,QAAA,4CAAA8D,2DAAA,CAAiE,QAAA4K,SAAA,CAAA/O,KAAA,CAC7C;IAKSmE,uDAAA,GAAmB;IAAnBA,wDAAA,oBAAmB;IACvCA,uDAAA,GAAsC;IAAtCA,+DAAA,CAAAsK,MAAA,CAAA7C,eAAA,CAAAmD,SAAA,CAAAhQ,QAAA,EAAsC;IAChDoF,uDAAA,EACF;IADEA,gEAAA,MAAAsK,MAAA,CAAA5C,gBAAA,CAAAkD,SAAA,CAAAhQ,QAAA,OACF;IAEWoF,uDAAA,EAA4B;IAA5BA,wDAAA,SAAAsK,MAAA,CAAA3C,cAAA,CAAAiD,SAAA,EAA4B;IAanC5K,uDAAA,GAAkB;IAAlBA,+DAAA,CAAA4K,SAAA,CAAA/O,KAAA,CAAkB;IAGdmE,uDAAA,GAA2B;IAA3BA,+DAAA,CAAA4K,SAAA,CAAA9O,cAAA,CAA2B;IAIPkE,uDAAA,GAAwB;IAAxBA,+DAAA,CAAA4K,SAAA,CAAAjQ,WAAA,CAAwB;IAI9CqF,uDAAA,GAAwB;IAAxBA,+DAAA,CAAA4K,SAAA,CAAA5O,WAAA,CAAwB;IACFgE,uDAAA,EAAkB;IAAlBA,wDAAA,mBAAkB;IAC5CA,uDAAA,EACF;IADEA,gEAAA,MAAA4K,SAAA,CAAA7P,IAAA,MACF;IAIUiF,uDAAA,GAAsC;IAAtCA,wDAAA,UAAAsK,MAAA,CAAA1C,oBAAA,CAAAgD,SAAA,EAAsC;IAC9C5K,uDAAA,EACF;IADEA,gEAAA,MAAAsK,MAAA,CAAAzC,mBAAA,CAAA+C,SAAA,OACF;IAEO5K,uDAAA,EAAuB;IAAvBA,wDAAA,SAAA4K,SAAA,CAAAxO,UAAA,CAAuB;IAUtB4D,uDAAA,GAAiD;IACjDA,wDADA,UAAAsK,MAAA,CAAA9D,SAAA,CAAAoE,SAAA,wBAAiD,cAAAN,MAAA,CAAA9D,SAAA,CAAAoE,SAAA,MAAAA,SAAA,CAAAzO,iBAAA,CACW;IAGxD6D,uDAAA,GAA2C;IAA3CA,+DAAA,CAAAsK,MAAA,CAAA9D,SAAA,CAAAoE,SAAA,sBAA2C;IACrD5K,uDAAA,EACF;IADEA,gEAAA,MAAAsK,MAAA,CAAA9D,SAAA,CAAAoE,SAAA,iDACF;IAeY5K,uDAAA,GAA2D;IAA3DA,+DAAA,CAAAsK,MAAA,CAAAkB,YAAA,CAAAZ,SAAA,mCAA2D;;;;;IArF/E5K,4DAAA,cAAsE;IACpEA,wDAAA,IAAAyL,+CAAA,yBAG8C;IAsFhDzL,0DAAA,EAAM;;;;IAzFyBA,uDAAA,EAAY;IAAZA,wDAAA,YAAA0L,UAAA,CAAY;;;;;;IAgGnC1L,4DAJR,cAA0E,mBAC3C,uBACT,cACW,mBACI;IAAAA,oDAAA,oBAAa;IAAAA,0DAAA,EAAW;IACrDA,4DAAA,SAAI;IAAAA,oDAAA,0CAAyB;IAAAA,0DAAA,EAAK;IAClCA,4DAAA,QAAG;IAAAA,oDAAA,8GAA6F;IAAAA,0DAAA,EAAI;IACpGA,4DAAA,kBAAmE;IAAzBA,wDAAA,mBAAA2L,8DAAA;MAAA3L,2DAAA,CAAA4L,GAAA;MAAA,MAAAtB,MAAA,GAAAtK,2DAAA;MAAA,OAAAA,yDAAA,CAASsK,MAAA,CAAAuB,YAAA,EAAc;IAAA,EAAC;IAChE7L,4DAAA,gBAAU;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAW;IAC5BA,oDAAA,wCACF;IAIRA,0DAJQ,EAAS,EACL,EACW,EACV,EACP;;;ADnJF,MAAO8L,oBAAoB;EA4B/B/R,YACU+I,cAA8B,EAC9BC,WAAwB,EACxBmF,MAAc,EACd6D,MAAiB,EACjB5D,QAAqB;IAJrB,KAAArF,cAAc,GAAdA,cAAc;IACd,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAmF,MAAM,GAANA,MAAM;IACN,KAAA6D,MAAM,GAANA,MAAM;IACN,KAAA5D,QAAQ,GAARA,QAAQ;IA9BlB,KAAAnF,WAAW,GAAgB,IAAI;IAE/B;IACA,KAAAgJ,cAAc,GAAG,IAAI3B,uDAAW,CAAC,KAAK,CAAC;IACvC,KAAA4B,UAAU,GAAG,IAAI5B,uDAAW,CAAC,KAAK,CAAC;IACnC,KAAA6B,YAAY,GAAG,IAAI7B,uDAAW,CAAC,EAAE,CAAC;IAElC,KAAA8B,UAAU,GAAG,CACX;MAAEtO,KAAK,EAAE,KAAK;MAAE4M,KAAK,EAAE,uBAAuB;MAAED,IAAI,EAAE;IAAU,CAAE,EAClE;MAAE3M,KAAK,EAAElE,wDAAc,CAACsC,IAAI;MAAEwO,KAAK,EAAE,cAAc;MAAED,IAAI,EAAE;IAAY,CAAE,EACzE;MAAE3M,KAAK,EAAElE,wDAAc,CAAC2F,QAAQ;MAAEmL,KAAK,EAAE,UAAU;MAAED,IAAI,EAAE;IAAc,CAAE,EAC3E;MAAE3M,KAAK,EAAElE,wDAAc,CAAC4F,aAAa;MAAEkL,KAAK,EAAE,gBAAgB;MAAED,IAAI,EAAE;IAAO,CAAE,EAC/E;MAAE3M,KAAK,EAAElE,wDAAc,CAAC6F,QAAQ;MAAEiL,KAAK,EAAE,UAAU;MAAED,IAAI,EAAE;IAAO,CAAE,EACpE;MAAE3M,KAAK,EAAElE,wDAAc,CAAC8F,MAAM;MAAEgL,KAAK,EAAE,OAAO;MAAED,IAAI,EAAE;IAAgB,CAAE,EACxE;MAAE3M,KAAK,EAAElE,wDAAc,CAAC2C,SAAS;MAAEmO,KAAK,EAAE,WAAW;MAAED,IAAI,EAAE;IAAQ,CAAE,EACvE;MAAE3M,KAAK,EAAElE,wDAAc,CAAC+F,SAAS;MAAE+K,KAAK,EAAE,WAAW;MAAED,IAAI,EAAE;IAAgB,CAAE,EAC/E;MAAE3M,KAAK,EAAElE,wDAAc,CAACgG,KAAK;MAAE8K,KAAK,EAAE,OAAO;MAAED,IAAI,EAAE;IAAY,CAAE,CACpE;IAED,KAAA4B,MAAM,GAAG,CACP;MAAEvO,KAAK,EAAE,KAAK;MAAE4M,KAAK,EAAE;IAAmB,CAAE,EAC5C;MAAE5M,KAAK,EAAE,UAAU;MAAE4M,KAAK,EAAE;IAAU,CAAE,EACxC;MAAE5M,KAAK,EAAE,QAAQ;MAAE4M,KAAK,EAAE;IAAQ,CAAE,CACrC;IASC,IAAI,CAACtQ,QAAQ,GAAG,IAAI,CAAC2I,cAAc,CAACtG,UAAU,EAAE;EAClD;EAEA6G,QAAQA,CAAA;IACN,IAAI,CAACN,WAAW,CAACE,YAAY,CAACtG,SAAS,CAACwG,IAAI,IAAG;MAC7C,IAAI,CAACH,WAAW,GAAGG,IAAI;MACvB,IAAIA,IAAI,EAAE;QACR,IAAI,CAAC8I,UAAU,CAACI,QAAQ,CAAClJ,IAAI,CAACpI,IAAI,CAAC;;IAEvC,CAAC,CAAC;IAEF,IAAI,CAACuR,YAAY,EAAE;EACrB;EAEQA,YAAYA,CAAA;IAClB,IAAI,CAACC,gBAAgB,GAAGrC,mDAAa,CAAC,CACpC,IAAI,CAAC/P,QAAQ,EACb,IAAI,CAAC6R,cAAc,CAACQ,YAAY,CAACtJ,IAAI,CAACkH,yDAAS,CAAC,KAAK,CAAC,CAAC,EACvD,IAAI,CAAC6B,UAAU,CAACO,YAAY,CAACtJ,IAAI,CAACkH,yDAAS,CAAC,KAAK,CAAC,CAAC,EACnD,IAAI,CAAC8B,YAAY,CAACM,YAAY,CAACtJ,IAAI,CAACkH,yDAAS,CAAC,EAAE,CAAC,CAAC,CACnD,CAAC,CAAClH,IAAI,CACLiH,mDAAG,CAAC,CAAC,CAAC7O,OAAO,EAAEV,QAAQ,EAAEG,IAAI,EAAE0R,MAAM,CAAC,KAAI;MACxC,OAAOnR,OAAO,CAACuB,MAAM,CAACC,MAAM,IAAG;QAC7B;QACA,IAAIlC,QAAQ,KAAK,KAAK,IAAIkC,MAAM,CAAClC,QAAQ,KAAKA,QAAQ,EAAE;UACtD,OAAO,KAAK;;QAGd;QACA,IAAIG,IAAI,KAAK,KAAK,IAAI+B,MAAM,CAAC/B,IAAI,KAAKA,IAAI,IAAI+B,MAAM,CAAC/B,IAAI,KAAK,MAAM,EAAE;UACpE,OAAO,KAAK;;QAGd;QACA,IAAI0R,MAAM,IAAI,CAAC3P,MAAM,CAACjB,KAAK,CAAC6Q,WAAW,EAAE,CAACC,QAAQ,CAACF,MAAM,CAACC,WAAW,EAAE,CAAC,IACpE,CAAC5P,MAAM,CAACnC,WAAW,CAAC+R,WAAW,EAAE,CAACC,QAAQ,CAACF,MAAM,CAACC,WAAW,EAAE,CAAC,IAChE,CAAC5P,MAAM,CAACd,WAAW,CAAC0Q,WAAW,EAAE,CAACC,QAAQ,CAACF,MAAM,CAACC,WAAW,EAAE,CAAC,EAAE;UACpE,OAAO,KAAK;;QAGd,OAAO5P,MAAM,CAACzB,QAAQ;MACxB,CAAC,CAAC;IACJ,CAAC,CAAC,CACH;EACH;EAEAoM,eAAeA,CAAC7M,QAAwB;IACtC,MAAMgS,YAAY,GAAG,IAAI,CAACT,UAAU,CAAClP,IAAI,CAAC4P,CAAC,IAAIA,CAAC,CAAChP,KAAK,KAAKjD,QAAQ,CAAC;IACpE,OAAOgS,YAAY,EAAEpC,IAAI,IAAI,UAAU;EACzC;EAEA9C,gBAAgBA,CAAC9M,QAAwB;IACvC,MAAMgS,YAAY,GAAG,IAAI,CAACT,UAAU,CAAClP,IAAI,CAAC4P,CAAC,IAAIA,CAAC,CAAChP,KAAK,KAAKjD,QAAQ,CAAC;IACpE,OAAOgS,YAAY,EAAEnC,KAAK,IAAI7P,QAAQ;EACxC;EAEA4L,SAASA,CAAC1J,MAAc;IACtB,OAAO,IAAI,CAACkG,WAAW,GAAG,IAAI,CAACA,WAAW,CAACqD,MAAM,IAAIvJ,MAAM,CAAChB,cAAc,GAAG,KAAK;EACpF;EAEM0B,cAAcA,CAACV,MAAc;IAAA,IAAAa,KAAA;IAAA,OAAAC,wJAAA;MACjC,IAAI,CAACD,KAAI,CAACqF,WAAW,EAAE;QACrBrF,KAAI,CAACwK,QAAQ,CAACG,IAAI,CAAC,uDAAuD,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACzG;;MAGF,IAAI,CAAC5K,KAAI,CAAC6I,SAAS,CAAC1J,MAAM,CAAC,EAAE;QAC3Ba,KAAI,CAACwK,QAAQ,CAACG,IAAI,CAAC,2CAA2C,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QAC7F;;MAGF,IAAI;QACF,MAAMxK,QAAQ,SAASJ,KAAI,CAACmF,cAAc,CAACtF,cAAc,CAACV,MAAM,CAACrC,EAAE,EAAEkD,KAAI,CAACqF,WAAW,CAACI,GAAG,CAAC;QAC1FzF,KAAI,CAACwK,QAAQ,CAACG,IAAI,CAChB,0CAA0CvK,QAAQ,CAACK,YAAY,EAAE,EACjE,QAAQ,EACR;UAAEmK,QAAQ,EAAE;QAAI,CAAE,CACnB;QAED;OAED,CAAC,OAAOI,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,iBAAiB,EAAEA,KAAK,CAAC;QACvChL,KAAI,CAACwK,QAAQ,CAACG,IAAI,CAACK,KAAK,CAACE,OAAO,IAAI,2BAA2B,EAAE,QAAQ,EAAE;UAAEN,QAAQ,EAAE;QAAI,CAAE,CAAC;;IAC/F;EACH;EAEAuC,iBAAiBA,CAAChO,MAAc;IAC9B,IAAI,CAACoL,MAAM,CAACO,QAAQ,CAAC,CAAC,UAAU,EAAE3L,MAAM,CAACrC,EAAE,CAAC,CAAC;EAC/C;EAEAoN,mBAAmBA,CAAC/K,MAAc;IAChC,IAAI,CAACA,MAAM,CAACX,iBAAiB,EAAE;MAC7B,OAAO,YAAY;;IAGrB,IAAIW,MAAM,CAACX,iBAAiB,IAAI,CAAC,EAAE;MACjC,OAAO,QAAQ;;IAGjB,IAAIW,MAAM,CAACX,iBAAiB,IAAI,CAAC,EAAE;MACjC,OAAO,YAAYW,MAAM,CAACX,iBAAiB,gBAAgB;;IAG7D,OAAO,YAAY;EACrB;EAEAyL,oBAAoBA,CAAC9K,MAAc;IACjC,IAAI,CAACA,MAAM,CAACX,iBAAiB,IAAIW,MAAM,CAACX,iBAAiB,GAAG,CAAC,EAAE;MAC7D,OAAO,SAAS;;IAGlB,IAAIW,MAAM,CAACX,iBAAiB,IAAI,CAAC,EAAE;MACjC,OAAO,MAAM;;IAGf,OAAO,QAAQ;EACjB;EAEAwL,cAAcA,CAAC7K,MAAc;IAC3B,IAAI,CAACA,MAAM,CAACV,UAAU,EAAE,OAAO,KAAK;IAEpC,MAAMmC,GAAG,GAAG,IAAI/C,IAAI,EAAE;IACtB,MAAMY,UAAU,GAAG,IAAIZ,IAAI,CAACsB,MAAM,CAACV,UAAU,CAAC;IAC9C,MAAM4M,eAAe,GAAGnK,IAAI,CAACoK,IAAI,CAAC,CAAC7M,UAAU,CAAC8M,OAAO,EAAE,GAAG3K,GAAG,CAAC2K,OAAO,EAAE,KAAK,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAEjG,OAAOF,eAAe,IAAI,CAAC,IAAIA,eAAe,GAAG,CAAC;EACpD;EAEA2B,gBAAgBA,CAAClH,IAAU;IACzB,OAAO,IAAIjI,IAAI,CAACiI,IAAI,CAAC,CAACC,kBAAkB,CAAC,OAAO,EAAE;MAChDC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE;KACP,CAAC;EACJ;EAEAgI,YAAYA,CAAA;IACV,IAAI,CAACG,cAAc,CAACK,QAAQ,CAAC,KAAK,CAAC;IACnC,IAAI,CAACJ,UAAU,CAACI,QAAQ,CAAC,IAAI,CAACrJ,WAAW,EAAEjI,IAAI,IAAI,KAAK,CAAC;IACzD,IAAI,CAACmR,YAAY,CAACG,QAAQ,CAAC,EAAE,CAAC;EAChC;EAEAf,aAAaA,CAACxO,MAAc;IAC1B;IACA8L,OAAO,CAACkE,GAAG,CAAC,oBAAoB,EAAEhQ,MAAM,CAACjB,KAAK,CAAC;IAC/C,IAAI,CAACsM,QAAQ,CAACG,IAAI,CAAC,qBAAqB,EAAE,QAAQ,EAAE;MAAEC,QAAQ,EAAE;IAAI,CAAE,CAAC;EACzE;EAEAiD,YAAYA,CAAC1O,MAAc;IACzB;IACA,OAAO,KAAK,CAAC,CAAC;EAChB;EAEA+J,WAAWA,CAAC/J,MAAc;IACxB,IAAImH,SAAS,CAACkF,KAAK,EAAE;MACnBlF,SAAS,CAACkF,KAAK,CAAC;QACdtN,KAAK,EAAE,qBAAqBiB,MAAM,CAACjB,KAAK,EAAE;QAC1CuN,IAAI,EAAE,yCAAyCtM,MAAM,CAACnC,WAAW,EAAE;QACnE0O,GAAG,EAAE,GAAGC,MAAM,CAACpO,QAAQ,CAAC6R,MAAM,YAAYjQ,MAAM,CAACrC,EAAE;OACpD,CAAC,CAAC+O,KAAK,CAACZ,OAAO,CAACD,KAAK,CAAC;KACxB,MAAM;MACL;MACA,MAAMU,GAAG,GAAG,GAAGC,MAAM,CAACpO,QAAQ,CAAC6R,MAAM,YAAYjQ,MAAM,CAACrC,EAAE,EAAE;MAC5DwJ,SAAS,CAACC,SAAS,CAACC,SAAS,CAACkF,GAAG,CAAC,CAACjF,IAAI,CAAC,MAAK;QAC3C,IAAI,CAAC+D,QAAQ,CAACG,IAAI,CAAC,mCAAmC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;MACvF,CAAC,CAAC;;EAEN;;;uBA3MWuD,oBAAoB,EAAA9L,+DAAA,CAAAwE,0EAAA,GAAAxE,+DAAA,CAAAyE,oEAAA,GAAAzE,+DAAA,CAAA4J,mDAAA,GAAA5J,+DAAA,CAAA6J,gEAAA,GAAA7J,+DAAA,CAAAiN,qEAAA;IAAA;EAAA;;;YAApBnB,oBAAoB;MAAAnH,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAmI,8BAAAjI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCb7BjF,4DAHJ,aAA+B,aAEQ,SAC/B;UAAAA,oDAAA,oCAAc;UAAAA,0DAAA,EAAK;UACvBA,4DAAA,QAAG;UAAAA,oDAAA,sEAAqD;UAAAA,0DAAA,EAAI;UAE5DA,wDAAA,IAAAmN,mCAAA,iBAA6C;UAI/CnN,0DAAA,EAAM;UASIA,4DANV,aAAqC,kBACJ,uBACX,cACU,yBAEkC,iBAC7C;UAAAA,oDAAA,kBAAU;UAAAA,0DAAA,EAAY;UACjCA,uDAAA,gBAA2F;UAC3FA,4DAAA,mBAAoB;UAAAA,oDAAA,cAAM;UAC5BA,0DAD4B,EAAW,EACtB;UAIfA,4DADF,yBAAqC,iBACxB;UAAAA,oDAAA,sBAAS;UAAAA,0DAAA,EAAY;UAChCA,4DAAA,sBAA2C;UACzCA,wDAAA,KAAAoN,2CAAA,yBAAyE;UAK7EpN,0DADE,EAAa,EACE;UAIfA,4DADF,yBAAqC,iBACxB;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAY;UAC5BA,4DAAA,sBAAuC;UACrCA,wDAAA,KAAAqN,2CAAA,yBAA6D;UAIjErN,0DADE,EAAa,EACE;UAGjBA,4DAAA,kBAA8E;UAAnDA,wDAAA,mBAAAsN,uDAAA;YAAA,OAASpI,GAAA,CAAA2G,YAAA,EAAc;UAAA,EAAC;UACjD7L,4DAAA,gBAAU;UAAAA,oDAAA,aAAK;UAAAA,0DAAA,EAAW;UAC1BA,oDAAA,iBACF;UAIRA,0DAJQ,EAAS,EACL,EACW,EACV,EACP;UAGNA,wDAAA,KAAAuN,oCAAA,kBAAsE;;UA6FtEvN,wDAAA,KAAAwN,oCAAA,mBAA0E;;UAsBxExN,4DALF,kBAIoD,gBACxC;UAAAA,oDAAA,uBAAe;UAE7BA,0DAF6B,EAAW,EAC7B,EACL;;;;UAvKwBA,uDAAA,GAAiB;UAAjBA,wDAAA,SAAAkF,GAAA,CAAAlC,WAAA,CAAiB;UAcnBhD,uDAAA,GAA4B;UAA5BA,wDAAA,gBAAAkF,GAAA,CAAAgH,YAAA,CAA4B;UAOhClM,uDAAA,GAA8B;UAA9BA,wDAAA,gBAAAkF,GAAA,CAAA8G,cAAA,CAA8B;UACPhM,uDAAA,EAAa;UAAbA,wDAAA,YAAAkF,GAAA,CAAAiH,UAAA,CAAa;UAUpCnM,uDAAA,GAA0B;UAA1BA,wDAAA,gBAAAkF,GAAA,CAAA+G,UAAA,CAA0B;UACPjM,uDAAA,EAAS;UAATA,wDAAA,YAAAkF,GAAA,CAAAkH,MAAA,CAAS;UAiBvBpM,uDAAA,GAA+B;UAA/BA,wDAAA,SAAAA,yDAAA,QAAAkF,GAAA,CAAAqH,gBAAA,EAA+B;UA6FhCvM,uDAAA,GAA8C;UAA9CA,wDAAA,WAAAyN,OAAA,GAAAzN,yDAAA,SAAAkF,GAAA,CAAAqH,gBAAA,oBAAAkB,OAAA,CAAAlN,MAAA,QAA8C;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACpJ3B;AACA;AAE/C;AACuD;AACI;AACJ;AACE;AACF;AACI;AACK;AACc;AACrB;AACI;AACN;AACW;AACT;AACE;AACN;AAEmC;AACG;AACS;AACnD;;;AA8B3C,MAAOqO,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBArBtBlB,yDAAY,EACZgB,+DAAmB,EACnBf,yDAAY,CAACkB,QAAQ,CAACF,0DAAa,CAAC;MAEpC;MACAf,iEAAa,EACbC,qEAAe,EACfC,kEAAa,EACbC,oEAAc,EACdC,kEAAa,EACbC,sEAAe,EACfC,2EAAiB,EACjBC,yFAAwB,EACxBC,oEAAc,EACdC,wEAAgB,EAChBC,kEAAa,EACbC,6EAAkB,EAClBC,oEAAc,EACdC,sEAAe;IAAA;EAAA;;;sHAGNG,aAAa;IAAAE,YAAA,GA1BtBhD,iGAAoB,EACpB9D,oGAAqB,EACrBnF,6GAAwB;IAAAkM,OAAA,GAGxBrB,yDAAY,EACZgB,+DAAmB,EAAAlK,yDAAA;IAGnB;IACAoJ,iEAAa,EACbC,qEAAe,EACfC,kEAAa,EACbC,oEAAc,EACdC,kEAAa,EACbC,sEAAe,EACfC,2EAAiB,EACjBC,yFAAwB,EACxBC,oEAAc,EACdC,wEAAgB,EAChBC,kEAAa,EACbC,6EAAkB,EAClBC,oEAAc,EACdC,sEAAe;EAAA;AAAA;;;;;;;;;;;;;;;;AClDqE;AACG;AAEpF,MAAME,aAAa,GAAW,CACnC;EACEK,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEnD,iGAAoBA;CAChC,EACD;EACEkD,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEjH,oGAAqBA;CACjC,CACF;;;;;;;;;;;;;;;;;;;ACbmC;AACuM;AAClL;AACjB;AAC6B;AAC1B;AAE3C,IAAIoI,MAAM,GAAG,CAAC;AACd,MAAMC,mBAAmB,GAAG,mBAAmB;AAC/C;AACA,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;AAC3B;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,CAAC;EACvB;IAAS,IAAI,CAAC3Q,IAAI,YAAA4Q,6BAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFF,oBAAoB;IAAA,CAAmD;EAAE;EACnL;IAAS,IAAI,CAACG,IAAI,kBAD8E3Q,+DAAE;MAAA6Q,IAAA,EACJL,oBAAoB;MAAA7L,SAAA;MAAAmM,UAAA;MAAAC,QAAA,GADlB/Q,iEAAE;MAAA4E,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAAkM,8BAAAhM,EAAA,EAAAC,GAAA;MAAAgM,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EACyxG;EAAE;AACj4G;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGrR,+DAAE,CAGXwQ,oBAAoB,EAAc,CAAC;IAClHK,IAAI,EAAE3B,oDAAS;IACfqC,IAAI,EAAE,CAAC;MAAET,UAAU,EAAE,IAAI;MAAEK,aAAa,EAAEhC,4DAAiB,CAACqC,IAAI;MAAEzM,QAAQ,EAAE,EAAE;MAAEqM,eAAe,EAAEhC,kEAAuB,CAACqC,MAAM;MAAEP,MAAM,EAAE,CAAC,kkGAAkkG;IAAE,CAAC;EACntG,CAAC,CAAC;AAAA;AACV;AACA,MAAMQ,QAAQ,CAAC;EACX;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM;EACtB;EACA,IAAID,KAAKA,CAAC9T,KAAK,EAAE;IACb,IAAI,CAACgU,SAAS,CAAChU,KAAK,CAAC;IACrB,IAAI,CAAC+T,MAAM,GAAG/T,KAAK;EACvB;EACA;EACA,IAAIiU,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACE,UAAU,EAAE;IACpB,IAAI,CAACC,sBAAsB,CAACD,UAAU,CAAC;EAC3C;EACA;EACA,IAAIrX,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAACuX,YAAY;EAC5B;EACA,IAAIvX,WAAWA,CAACwX,cAAc,EAAE;IAC5B,IAAI,CAACC,kBAAkB,CAACD,cAAc,CAAC;EAC3C;EACApY,WAAWA,CAACsY,OAAO,EAAEC,WAAW,EAAEC,cAAc,EAAEC,SAAS,EAAEC,cAAc,EAAE;IACzE,IAAI,CAACJ,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACb,MAAM,GAAG,SAAS;IACvB;IACA,IAAI,CAACc,OAAO,GAAG,IAAI;IACnB;AACR;AACA;AACA;IACQ,IAAI,CAACC,QAAQ,GAAG,aAAa;IAC7B;IACA,IAAI,CAACC,IAAI,GAAG,QAAQ;IACpB;IACA,IAAI,CAACC,GAAG,GAAGzC,MAAM,EAAE;IACnB;IACA,IAAI,CAAC0C,cAAc,GAAG,KAAK;IAC3B;IACA,IAAI,CAACC,qBAAqB,GAAG1D,qDAAM,CAACY,mEAAoB,CAAC;IACzD,IAAI,CAAC+C,SAAS,GAAG3D,qDAAM,CAACc,qDAAQ,CAAC;IACjC,MAAM8C,MAAM,GAAG5D,qDAAM,CAACC,yDAAc,CAAC;IACrC,IAAI,CAACgB,SAAS,CAAC4C,GAAG,CAACD,MAAM,CAAC,EAAE;MACxB3C,SAAS,CAAC6C,GAAG,CAACF,MAAM,CAAC;MACrB,MAAMG,YAAY,GAAG7D,8DAAe,CAACiB,oBAAoB,EAAE;QACvD6C,mBAAmB,EAAEhE,qDAAM,CAACG,8DAAmB;MACnD,CAAC,CAAC;MACFyD,MAAM,CAACK,SAAS,CAAC,MAAM;QACnBhD,SAAS,CAACiD,MAAM,CAACN,MAAM,CAAC;QACxB,IAAI3C,SAAS,CAACsC,IAAI,KAAK,CAAC,EAAE;UACtBQ,YAAY,CAACI,OAAO,CAAC,CAAC;QAC1B;MACJ,CAAC,CAAC;IACN;IACA,IAAI,OAAOnC,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MAC/C,MAAMoC,aAAa,GAAGnB,WAAW,CAACmB,aAAa;MAC/C,IAAIA,aAAa,CAACC,QAAQ,KAAKD,aAAa,CAACE,YAAY,EAAE;QACvD,MAAM7V,KAAK,CAAC,+CAA+C,CAAC;MAChE;MACA,MAAM8V,cAAc,GAAG,UAAU;MACjC;MACA;MACA;MACA,IAAIH,aAAa,CAACI,OAAO,CAACnH,WAAW,CAAC,CAAC,KAAKkH,cAAc,IACtDH,aAAa,CAACK,YAAY,CAAC,aAAa,CAAC,KAAK,MAAM,EAAE;QACtDlL,OAAO,CAACmL,IAAI,CAAC,wDAAwD,GACjE,gGAAgG,GAChG,KAAKN,aAAa,CAACO,SAAS,EAAE,CAAC;MACvC;IACJ;EACJ;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACtB,QAAQ,CAACuB,OAAO,CAAC,OAAO,CAAC,KAAK,CAAC,CAAC;EAChD;EACA;EACAC,OAAOA,CAAA,EAAG;IACN,OAAO,IAAI,CAACxB,QAAQ,CAACuB,OAAO,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC;EACjD;EACA;AACJ;AACA;AACA;EACIE,eAAeA,CAAA,EAAG;IACd,OAAO,IAAI,CAACC,aAAa;EAC7B;EACAhR,QAAQA,CAAA,EAAG;IACP;IACA;IACA;IACA,IAAI,CAACiR,oBAAoB,CAAC,CAAC;IAC3B,IAAI,IAAI,CAACxC,OAAO,IAAI,CAAC,IAAI,CAACuC,aAAa,EAAE;MACrC,IAAI,CAACA,aAAa,GAAG,IAAI,CAACE,mBAAmB,CAAC,CAAC;MAC/C,IAAI,CAACtC,sBAAsB,CAAC,IAAI,CAACH,OAAO,CAAC;IAC7C;IACA,IAAI,CAACgB,cAAc,GAAG,IAAI;EAC9B;EACA0B,WAAWA,CAAA,EAAG;IACV;IACA;IACA,IAAI,IAAI,CAAChC,SAAS,CAACiC,WAAW,EAAE;MAC5B,IAAI,CAACjC,SAAS,CAACiC,WAAW,CAAC,IAAI,CAACJ,aAAa,CAAC;MAC9C,IAAI,CAACK,uBAAuB,EAAEC,MAAM,CAAC,CAAC;IAC1C;IACA,IAAI,CAACpC,cAAc,CAACqC,iBAAiB,CAAC,IAAI,CAACtC,WAAW,CAACmB,aAAa,EAAE,IAAI,CAAC9Y,WAAW,CAAC;EAC3F;EACA;EACAka,kBAAkBA,CAAA,EAAG;IACjB;IACA,OAAO,IAAI,CAAC9B,qBAAqB,CAAC+B,WAAW,CAAC,IAAI,CAACxC,WAAW,CAACmB,aAAa,EAAE;MAC1EsB,gBAAgB,EAAE;IACtB,CAAC,CAAC;EACN;EACA;EACAR,mBAAmBA,CAAA,EAAG;IAClB,MAAMS,YAAY,GAAG,IAAI,CAACxC,SAAS,CAACyC,aAAa,CAAC,MAAM,CAAC;IACzD,MAAMC,WAAW,GAAG,kBAAkB;IACtCF,YAAY,CAACG,YAAY,CAAC,IAAI,EAAE,qBAAqB,IAAI,CAACtC,GAAG,EAAE,CAAC;IAChE;IACA;IACAmC,YAAY,CAACG,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IAChDH,YAAY,CAACI,SAAS,CAACjC,GAAG,CAAC9C,mBAAmB,CAAC;IAC/C,IAAI,IAAI,CAACoC,cAAc,KAAK,gBAAgB,EAAE;MAC1CuC,YAAY,CAACI,SAAS,CAACjC,GAAG,CAAC,yBAAyB,CAAC;IACzD;IACA,IAAI,CAACb,WAAW,CAACmB,aAAa,CAAC4B,WAAW,CAACL,YAAY,CAAC;IACxD;IACA,IAAI,OAAOM,qBAAqB,KAAK,UAAU,IAAI,IAAI,CAAC7C,cAAc,KAAK,gBAAgB,EAAE;MACzF,IAAI,CAACJ,OAAO,CAACkD,iBAAiB,CAAC,MAAM;QACjCD,qBAAqB,CAAC,MAAM;UACxBN,YAAY,CAACI,SAAS,CAACjC,GAAG,CAAC+B,WAAW,CAAC;QAC3C,CAAC,CAAC;MACN,CAAC,CAAC;IACN,CAAC,MACI;MACDF,YAAY,CAACI,SAAS,CAACjC,GAAG,CAAC+B,WAAW,CAAC;IAC3C;IACA,OAAOF,YAAY;EACvB;EACA;EACA/C,sBAAsBA,CAACD,UAAU,EAAE;IAC/B,MAAMwD,oBAAoB,GAAG,GAAGxD,UAAU,IAAI,EAAE,EAAE,CAACyD,IAAI,CAAC,CAAC;IACzD;IACA;IACA;IACA,IAAI,IAAI,CAAC3C,cAAc,IAAI0C,oBAAoB,IAAI,CAAC,IAAI,CAACnB,aAAa,EAAE;MACpE,IAAI,CAACA,aAAa,GAAG,IAAI,CAACE,mBAAmB,CAAC,CAAC;IACnD;IACA,IAAI,IAAI,CAACF,aAAa,EAAE;MACpB,IAAI,CAACA,aAAa,CAACqB,WAAW,GAAGF,oBAAoB;IACzD;IACA,IAAI,CAACzD,QAAQ,GAAGyD,oBAAoB;EACxC;EACA;EACApD,kBAAkBA,CAACD,cAAc,EAAE;IAC/B;IACA,IAAI,CAACI,cAAc,CAACqC,iBAAiB,CAAC,IAAI,CAACtC,WAAW,CAACmB,aAAa,EAAE,IAAI,CAAC9Y,WAAW,CAAC;IACvF;IACA;IACA;IACA;IACA;IACA;IACA,IAAI,CAACwX,cAAc,IAAI,IAAI,CAAC0C,kBAAkB,CAAC,CAAC,EAAE;MAC9C,IAAI,CAACc,wBAAwB,CAAC,CAAC;IACnC;IACA,IAAI,CAACzD,YAAY,GAAGC,cAAc;IAClC;IACA;IACA,IAAI,IAAI,CAAC0C,kBAAkB,CAAC,CAAC,EAAE;MAC3B,IAAI,CAACtC,cAAc,CAACqD,QAAQ,CAAC,IAAI,CAACtD,WAAW,CAACmB,aAAa,EAAEtB,cAAc,CAAC;IAChF,CAAC,MACI;MACD,IAAI,CAAC0D,wBAAwB,CAAC,CAAC;IACnC;EACJ;EACAA,wBAAwBA,CAAA,EAAG;IACvB;IACA,IAAI,CAAC,IAAI,CAACnB,uBAAuB,EAAE;MAC/B,IAAI,CAACA,uBAAuB,GAAG,IAAI,CAAC1B,SAAS,CAACiC,aAAa,CAAC,MAAM,CAAC;MACnE,IAAI,CAACP,uBAAuB,CAACU,SAAS,CAACjC,GAAG,CAAC,qBAAqB,CAAC;IACrE;IACA,IAAI,CAACuB,uBAAuB,CAACgB,WAAW,GAAG,IAAI,CAAC/a,WAAW;IAC3D,IAAI,CAAC0Z,aAAa,EAAEgB,WAAW,CAAC,IAAI,CAACX,uBAAuB,CAAC;EACjE;EACAiB,wBAAwBA,CAAA,EAAG;IACvB,IAAI,CAACjB,uBAAuB,EAAEC,MAAM,CAAC,CAAC;IACtC,IAAI,CAACD,uBAAuB,GAAGlM,SAAS;EAC5C;EACA;EACAqJ,SAASA,CAACiE,YAAY,EAAE;IACpB,MAAMV,SAAS,GAAG,IAAI,CAAC9C,WAAW,CAACmB,aAAa,CAAC2B,SAAS;IAC1DA,SAAS,CAACT,MAAM,CAAC,aAAa,IAAI,CAAC/C,MAAM,EAAE,CAAC;IAC5C,IAAIkE,YAAY,EAAE;MACdV,SAAS,CAACjC,GAAG,CAAC,aAAa2C,YAAY,EAAE,CAAC;IAC9C;EACJ;EACA;EACAxB,oBAAoBA,CAAA,EAAG;IACnB;IACA;IACA,MAAMyB,MAAM,GAAG,IAAI,CAACzD,WAAW,CAACmB,aAAa,CAACuC,gBAAgB,CAAC,aAAa3F,mBAAmB,EAAE,CAAC;IAClG,KAAK,MAAM2E,YAAY,IAAIiB,KAAK,CAACC,IAAI,CAACH,MAAM,CAAC,EAAE;MAC3C,IAAIf,YAAY,KAAK,IAAI,CAACX,aAAa,EAAE;QACrCW,YAAY,CAACL,MAAM,CAAC,CAAC;MACzB;IACJ;EACJ;EACA;IAAS,IAAI,CAAC9U,IAAI,YAAAsW,iBAAAzF,CAAA;MAAA,YAAAA,CAAA,IAAwFgB,QAAQ,EA7NlB1R,+DAAE,CA6NkCA,iDAAS,GA7N7CA,+DAAE,CA6NwDA,qDAAa,GA7NvEA,+DAAE,CA6NkFwE,4DAAgB,GA7NpGxE,+DAAE,CA6N+GA,oDAAY,GA7N7HA,+DAAE,CA6NwIyP,gEAAqB;IAAA,CAA4D;EAAE;EAC7T;IAAS,IAAI,CAAC+G,IAAI,kBA9N8ExW,+DAAE;MAAA6Q,IAAA,EA8NJa,QAAQ;MAAA/M,SAAA;MAAA+R,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,sBAAA5R,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA9NNjF,yDAAE,sBAAAkF,GAAA,CAAAwN,OA8NG,CAAC,oBAARxN,GAAA,CAAA+O,OAAA,CAAQ,CAAD,CAAC,qBAAP/O,GAAA,CAAA+O,OAAA,CAAQ,CAAF,CAAC,sBAAP/O,GAAA,CAAAiP,OAAA,CAAQ,CAAF,CAAC,oBAARjP,GAAA,CAAAiP,OAAA,CAAQ,CAAD,CAAC,oBAAAjP,GAAA,CAAA0N,IAAA,KAAC,OAAF,CAAC,qBAAA1N,GAAA,CAAA0N,IAAA,KAAC,QAAF,CAAC,oBAAA1N,GAAA,CAAA0N,IAAA,KAAC,OAAF,CAAC,qBAAA1N,GAAA,CAAA4R,MAAA,KAAA5R,GAAA,CAAA4M,OAAD,CAAC,uBAAA5M,GAAA,CAAA6R,QAAD,CAAC;QAAA;MAAA;MAAAC,MAAA;QAAArF,KAAA,GA9NN3R,0DAAE,CAAAwR,IAAA;QAAAkB,OAAA,GAAF1S,0DAAE,CAAAkX,0BAAA,gCA8NuIxH,2DAAgB;QAAAqH,QAAA,GA9NzJ/W,0DAAE,CAAAkX,0BAAA,kCA8NqMxH,2DAAgB;QAAAiD,QAAA,GA9NvN3S,0DAAE,CAAAwR,IAAA;QAAAM,OAAA,GAAF9R,0DAAE,CAAAwR,IAAA;QAAA7W,WAAA,GAAFqF,0DAAE,CAAAwR,IAAA;QAAAoB,IAAA,GAAF5S,0DAAE,CAAAwR,IAAA;QAAAsF,MAAA,GAAF9W,0DAAE,CAAAkX,0BAAA,8BA8NgaxH,2DAAgB;MAAA;MAAAoB,UAAA;MAAAC,QAAA,GA9Nlb/Q,sEAAE;IAAA,EA8Nm6B;EAAE;AAC3gC;AACA;EAAA,QAAAqR,SAAA,oBAAAA,SAAA,KAhOoGrR,+DAAE,CAgOX0R,QAAQ,EAAc,CAAC;IACtGb,IAAI,EAAElB,oDAAS;IACf4B,IAAI,EAAE,CAAC;MACC6F,QAAQ,EAAE,YAAY;MACtBC,IAAI,EAAE;QACF,OAAO,EAAE,WAAW;QACpB,2BAA2B,EAAE,SAAS;QACtC,yBAAyB,EAAE,WAAW;QACtC,yBAAyB,EAAE,YAAY;QACvC,0BAA0B,EAAE,YAAY;QACxC,yBAAyB,EAAE,WAAW;QACtC,yBAAyB,EAAE,kBAAkB;QAC7C,0BAA0B,EAAE,mBAAmB;QAC/C,yBAAyB,EAAE,kBAAkB;QAC7C,0BAA0B,EAAE,oBAAoB;QAChD,4BAA4B,EAAE;MAClC,CAAC;MACDvG,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAED,IAAI,EAAE7Q,iDAASoW;EAAC,CAAC,EAAE;IAAEvF,IAAI,EAAE7Q,qDAAaqW;EAAC,CAAC,EAAE;IAAExF,IAAI,EAAErM,4DAAgB8R;EAAC,CAAC,EAAE;IAAEzF,IAAI,EAAE7Q,oDAAYuW;EAAC,CAAC,EAAE;IAAE1F,IAAI,EAAErI,SAAS;IAAE8O,UAAU,EAAE,CAAC;MACjJzG,IAAI,EAAEjB,mDAAQA;IAClB,CAAC,EAAE;MACCiB,IAAI,EAAEhB,iDAAM;MACZ0B,IAAI,EAAE,CAAC9B,gEAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEkC,KAAK,EAAE,CAAC;MACjCd,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC,eAAe;IAC1B,CAAC,CAAC;IAAEmB,OAAO,EAAE,CAAC;MACV7B,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC;QAAEgG,KAAK,EAAE,iBAAiB;QAAEC,SAAS,EAAE9H,2DAAgBA;MAAC,CAAC;IACpE,CAAC,CAAC;IAAEqH,QAAQ,EAAE,CAAC;MACXlG,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC;QAAEgG,KAAK,EAAE,kBAAkB;QAAEC,SAAS,EAAE9H,2DAAgBA;MAAC,CAAC;IACrE,CAAC,CAAC;IAAEiD,QAAQ,EAAE,CAAC;MACX9B,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC,kBAAkB;IAC7B,CAAC,CAAC;IAAEO,OAAO,EAAE,CAAC;MACVjB,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC,UAAU;IACrB,CAAC,CAAC;IAAE5W,WAAW,EAAE,CAAC;MACdkW,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAEqB,IAAI,EAAE,CAAC;MACP/B,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC,cAAc;IACzB,CAAC,CAAC;IAAEuF,MAAM,EAAE,CAAC;MACTjG,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC;QAAEgG,KAAK,EAAE,gBAAgB;QAAEC,SAAS,EAAE9H,2DAAgBA;MAAC,CAAC;IACnE,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMtB,cAAc,CAAC;EACjB;IAAS,IAAI,CAACvO,IAAI,YAAA4X,uBAAA/G,CAAA;MAAA,YAAAA,CAAA,IAAwFtC,cAAc;IAAA,CAAkD;EAAE;EAC5K;IAAS,IAAI,CAACsJ,IAAI,kBApR8E1X,8DAAE;MAAA6Q,IAAA,EAoRSzC;IAAc,EAAiH;EAAE;EAC5O;IAAS,IAAI,CAACwJ,IAAI,kBArR8E5X,8DAAE;MAAA+O,OAAA,GAqRmCmB,yDAAU,EAAEF,mEAAe,EAAEA,mEAAe;IAAA,EAAI;EAAE;AAC3L;AACA;EAAA,QAAAqB,SAAA,oBAAAA,SAAA,KAvRoGrR,+DAAE,CAuRXoO,cAAc,EAAc,CAAC;IAC5GyC,IAAI,EAAEd,mDAAQ;IACdwB,IAAI,EAAE,CAAC;MACC;MACA;MACAxC,OAAO,EAAE,CAACmB,yDAAU,EAAEF,mEAAe,EAAE0B,QAAQ,EAAElB,oBAAoB,CAAC;MACtEsH,OAAO,EAAE,CAACpG,QAAQ,EAAE1B,mEAAe;IACvC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACnTiD;AACmC;AACrB;AAC3B;AACsK;AACxI;AACtB;AAC4B;AAChC;AACO;AACP;AACG;AACmB;AACR;AACvB;AACkD;AACpB;AACJ;;AAEzD;AAAA,MAAAmJ,GAAA;AACA,MAAMC,kBAAkB,GAAG,EAAE;AAC7B;AACA;AACA;AACA;AACA,SAASC,iCAAiCA,CAAC1G,QAAQ,EAAE;EACjD,OAAO7U,KAAK,CAAC,qBAAqB6U,QAAQ,eAAe,CAAC;AAC9D;AACA;AACA,MAAM2G,2BAA2B,GAAG,IAAIjB,yDAAc,CAAC,6BAA6B,EAAE;EAClFvY,UAAU,EAAE,MAAM;EAClBF,OAAO,EAAEA,CAAA,KAAM;IACX,MAAM2Z,OAAO,GAAGlK,qDAAM,CAACoJ,yDAAO,CAAC;IAC/B,OAAO,MAAMc,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC;MAAEC,cAAc,EAAEN;IAAmB,CAAC,CAAC;EAC5F;AACJ,CAAC,CAAC;AACF;AACA,SAASO,mCAAmCA,CAACJ,OAAO,EAAE;EAClD,OAAO,MAAMA,OAAO,CAACC,gBAAgB,CAACC,UAAU,CAAC;IAAEC,cAAc,EAAEN;EAAmB,CAAC,CAAC;AAC5F;AACA;AACA,MAAMQ,4CAA4C,GAAG;EACjDC,OAAO,EAAEP,2BAA2B;EACpCQ,IAAI,EAAE,CAACrB,yDAAO,CAAC;EACfsB,UAAU,EAAEJ;AAChB,CAAC;AACD;AACA,SAASK,mCAAmCA,CAAA,EAAG;EAC3C,OAAO;IACHC,SAAS,EAAE,CAAC;IACZC,SAAS,EAAE,CAAC;IACZC,iBAAiB,EAAE;EACvB,CAAC;AACL;AACA;AACA,MAAMC,2BAA2B,GAAG,IAAI/B,yDAAc,CAAC,6BAA6B,EAAE;EAClFvY,UAAU,EAAE,MAAM;EAClBF,OAAO,EAAEoa;AACb,CAAC,CAAC;AACF;AACA;AACA;AACA;AACA;AACA,MAAMK,mBAAmB,GAAG,uBAAuB;AACnD,MAAMC,WAAW,GAAG,eAAe;AACnC;AACA,MAAMC,sBAAsB,GAAG/B,sFAA+B,CAAC;EAAEgC,OAAO,EAAE;AAAK,CAAC,CAAC;AACjF;AACA;AACA,MAAMC,8BAA8B,GAAG,CAAC;AACxC,MAAMC,oBAAoB,GAAG,CAAC;AAC9B,MAAMC,UAAU,GAAG,EAAE;AACrB,MAAMC,SAAS,GAAG,GAAG;AACrB;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACb;EACA,IAAIlI,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACmI,SAAS;EACzB;EACA,IAAInI,QAAQA,CAAC9U,KAAK,EAAE;IAChB,IAAIA,KAAK,KAAK,IAAI,CAACid,SAAS,EAAE;MAC1B,IAAI,CAACA,SAAS,GAAGjd,KAAK;MACtB,IAAI,IAAI,CAACkd,WAAW,EAAE;QAClB,IAAI,CAACC,eAAe,CAAC,IAAI,CAACD,WAAW,CAAC;QACtC,IAAI,CAACE,gBAAgB,EAAEC,IAAI,CAAC,CAAC,CAAC;QAC9B,IAAI,CAACH,WAAW,CAACI,cAAc,CAAC,CAAC;MACrC;IACJ;EACJ;EACA;AACJ;AACA;AACA;EACI,IAAIC,gBAAgBA,CAAA,EAAG;IACnB,OAAO,IAAI,CAACC,iBAAiB;EACjC;EACA,IAAID,gBAAgBA,CAACvd,KAAK,EAAE;IACxB,IAAI,CAACwd,iBAAiB,GAAGpD,4EAAqB,CAACpa,KAAK,CAAC;IACrD,IAAI,CAACyd,OAAO,CAAC,CAAC;IACd,IAAI,CAACP,WAAW,GAAG,IAAI;EAC3B;EACA;EACA,IAAIhE,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACwE,SAAS;EACzB;EACA,IAAIxE,QAAQA,CAAClZ,KAAK,EAAE;IAChB,IAAI,CAAC0d,SAAS,GAAGtD,4EAAqB,CAACpa,KAAK,CAAC;IAC7C;IACA,IAAI,IAAI,CAAC0d,SAAS,EAAE;MAChB,IAAI,CAACC,IAAI,CAAC,CAAC,CAAC;IAChB,CAAC,MACI;MACD,IAAI,CAACC,gCAAgC,CAAC,CAAC;IAC3C;EACJ;EACA;EACA,IAAIxB,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACyB,UAAU;EAC1B;EACA,IAAIzB,SAASA,CAACpc,KAAK,EAAE;IACjB,IAAI,CAAC6d,UAAU,GAAGxD,2EAAoB,CAACra,KAAK,CAAC;EACjD;EACA;EACA,IAAIqc,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACyB,UAAU;EAC1B;EACA,IAAIzB,SAASA,CAACrc,KAAK,EAAE;IACjB,IAAI,CAAC8d,UAAU,GAAGzD,2EAAoB,CAACra,KAAK,CAAC;IAC7C,IAAI,IAAI,CAACod,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACW,oBAAoB,GAAG,IAAI,CAACD,UAAU;IAChE;EACJ;EACA;EACA,IAAI9S,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACgT,QAAQ;EACxB;EACA,IAAIhT,OAAOA,CAAChL,KAAK,EAAE;IACf,IAAI,CAAC0U,cAAc,CAACqC,iBAAiB,CAAC,IAAI,CAACtC,WAAW,CAACmB,aAAa,EAAE,IAAI,CAACoI,QAAQ,EAAE,SAAS,CAAC;IAC/F;IACA;IACA;IACA,IAAI,CAACA,QAAQ,GAAGhe,KAAK,IAAI,IAAI,GAAGie,MAAM,CAACje,KAAK,CAAC,CAAC4X,IAAI,CAAC,CAAC,GAAG,EAAE;IACzD,IAAI,CAAC,IAAI,CAACoG,QAAQ,IAAI,IAAI,CAACE,iBAAiB,CAAC,CAAC,EAAE;MAC5C,IAAI,CAACP,IAAI,CAAC,CAAC,CAAC;IAChB,CAAC,MACI;MACD,IAAI,CAACC,gCAAgC,CAAC,CAAC;MACvC,IAAI,CAACO,qBAAqB,CAAC,CAAC;MAC5B,IAAI,CAAC3J,OAAO,CAACkD,iBAAiB,CAAC,MAAM;QACjC;QACA;QACA;QACA;QACA0G,OAAO,CAACC,OAAO,CAAC,CAAC,CAAC9X,IAAI,CAAC,MAAM;UACzB,IAAI,CAACmO,cAAc,CAACqD,QAAQ,CAAC,IAAI,CAACtD,WAAW,CAACmB,aAAa,EAAE,IAAI,CAAC5K,OAAO,EAAE,SAAS,CAAC;QACzF,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA;EACA,IAAIsT,YAAYA,CAAA,EAAG;IACf,OAAO,IAAI,CAACC,aAAa;EAC7B;EACA,IAAID,YAAYA,CAACte,KAAK,EAAE;IACpB,IAAI,CAACue,aAAa,GAAGve,KAAK;IAC1B,IAAI,IAAI,CAACod,gBAAgB,EAAE;MACvB,IAAI,CAACoB,gBAAgB,CAAC,IAAI,CAACD,aAAa,CAAC;IAC7C;EACJ;EACAriB,WAAWA,CAACuiB,QAAQ,EAAEhK,WAAW,EAAEiK,iBAAiB,EAAEC,iBAAiB,EAAEnK,OAAO,EAAEoK,SAAS,EAAElK,cAAc,EAAEmK,aAAa,EAAEC,cAAc,EAAEC,IAAI,EAAEC,eAAe,EAAE7J,SAAS,EAAE;IAC1K,IAAI,CAACsJ,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAAChK,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACiK,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACC,iBAAiB,GAAGA,iBAAiB;IAC1C,IAAI,CAACnK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACoK,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAAClK,cAAc,GAAGA,cAAc;IACpC,IAAI,CAACmK,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACE,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAAC/B,SAAS,GAAG,OAAO;IACxB,IAAI,CAACO,iBAAiB,GAAG,KAAK;IAC9B,IAAI,CAACE,SAAS,GAAG,KAAK;IACtB,IAAI,CAACuB,gBAAgB,GAAG,KAAK;IAC7B,IAAI,CAACC,6BAA6B,GAAG,KAAK;IAC1C,IAAI,CAACC,iBAAiB,GAAGC,gBAAgB;IACzC,IAAI,CAACC,eAAe,GAAG,CAAC;IACxB,IAAI,CAACC,eAAe,GAAG,SAAS;IAChC;AACR;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACQ,IAAI,CAACC,aAAa,GAAG,MAAM;IAC3B,IAAI,CAACvB,QAAQ,GAAG,EAAE;IAClB;IACA,IAAI,CAACwB,iBAAiB,GAAG,EAAE;IAC3B;IACA,IAAI,CAACC,UAAU,GAAG,IAAI1E,yCAAO,CAAC,CAAC;IAC/B,IAAI,CAAC2E,eAAe,GAAGZ,cAAc;IACrC,IAAI,CAAC3J,SAAS,GAAGA,SAAS;IAC1B,IAAI6J,eAAe,EAAE;MACjB,IAAI,CAACnB,UAAU,GAAGmB,eAAe,CAAC5C,SAAS;MAC3C,IAAI,CAAC0B,UAAU,GAAGkB,eAAe,CAAC3C,SAAS;MAC3C,IAAI2C,eAAe,CAAClK,QAAQ,EAAE;QAC1B,IAAI,CAACA,QAAQ,GAAGkK,eAAe,CAAClK,QAAQ;MAC5C;MACA,IAAIkK,eAAe,CAACzB,gBAAgB,EAAE;QAClC,IAAI,CAACA,gBAAgB,GAAGyB,eAAe,CAACzB,gBAAgB;MAC5D;MACA,IAAIyB,eAAe,CAACO,aAAa,EAAE;QAC/B,IAAI,CAACA,aAAa,GAAGP,eAAe,CAACO,aAAa;MACtD;IACJ;IACAR,IAAI,CAACY,MAAM,CAACta,IAAI,CAAC6U,yDAAS,CAAC,IAAI,CAACuF,UAAU,CAAC,CAAC,CAAC3gB,SAAS,CAAC,MAAM;MACzD,IAAI,IAAI,CAACoe,WAAW,EAAE;QAClB,IAAI,CAACC,eAAe,CAAC,IAAI,CAACD,WAAW,CAAC;MAC1C;IACJ,CAAC,CAAC;IACF,IAAI,CAACmC,eAAe,GAAGzC,8BAA8B;EACzD;EACAgD,eAAeA,CAAA,EAAG;IACd;IACA,IAAI,CAACX,gBAAgB,GAAG,IAAI;IAC5B,IAAI,CAACrB,gCAAgC,CAAC,CAAC;IACvC,IAAI,CAACiB,aAAa,CACbgB,OAAO,CAAC,IAAI,CAACpL,WAAW,CAAC,CACzBpP,IAAI,CAAC6U,yDAAS,CAAC,IAAI,CAACuF,UAAU,CAAC,CAAC,CAChC3gB,SAAS,CAACoQ,MAAM,IAAI;MACrB;MACA,IAAI,CAACA,MAAM,EAAE;QACT,IAAI,CAACsF,OAAO,CAACsL,GAAG,CAAC,MAAM,IAAI,CAACnC,IAAI,CAAC,CAAC,CAAC,CAAC;MACxC,CAAC,MACI,IAAIzO,MAAM,KAAK,UAAU,EAAE;QAC5B,IAAI,CAACsF,OAAO,CAACsL,GAAG,CAAC,MAAM,IAAI,CAACzC,IAAI,CAAC,CAAC,CAAC;MACvC;IACJ,CAAC,CAAC;EACN;EACA;AACJ;AACA;EACI1G,WAAWA,CAAA,EAAG;IACV,MAAMf,aAAa,GAAG,IAAI,CAACnB,WAAW,CAACmB,aAAa;IACpDmK,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;IACrC,IAAI,IAAI,CAAC9C,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC+C,OAAO,CAAC,CAAC;MAC1B,IAAI,CAAC7C,gBAAgB,GAAG,IAAI;IAChC;IACA;IACA,IAAI,CAACoC,iBAAiB,CAACU,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,QAAQ,CAAC,KAAK;MAClDxK,aAAa,CAACyK,mBAAmB,CAACF,KAAK,EAAEC,QAAQ,EAAE1D,sBAAsB,CAAC;IAC9E,CAAC,CAAC;IACF,IAAI,CAAC8C,iBAAiB,CAAC9c,MAAM,GAAG,CAAC;IACjC,IAAI,CAAC+c,UAAU,CAAC/gB,IAAI,CAAC,CAAC;IACtB,IAAI,CAAC+gB,UAAU,CAACa,QAAQ,CAAC,CAAC;IAC1B,IAAI,CAAC5L,cAAc,CAACqC,iBAAiB,CAACnB,aAAa,EAAE,IAAI,CAAC5K,OAAO,EAAE,SAAS,CAAC;IAC7E,IAAI,CAAC6T,aAAa,CAAC0B,cAAc,CAAC3K,aAAa,CAAC;EACpD;EACA;EACAyH,IAAIA,CAACmD,KAAK,GAAG,IAAI,CAACpE,SAAS,EAAElN,MAAM,EAAE;IACjC,IAAI,IAAI,CAACgK,QAAQ,IAAI,CAAC,IAAI,CAAClO,OAAO,IAAI,IAAI,CAACkT,iBAAiB,CAAC,CAAC,EAAE;MAC5D,IAAI,CAACd,gBAAgB,EAAEqD,wBAAwB,CAAC,CAAC;MACjD;IACJ;IACA,MAAMC,UAAU,GAAG,IAAI,CAACC,cAAc,CAACzR,MAAM,CAAC;IAC9C,IAAI,CAACuO,OAAO,CAAC,CAAC;IACd,IAAI,CAACmD,OAAO,GACR,IAAI,CAACA,OAAO,IAAI,IAAI9F,gEAAe,CAAC,IAAI,CAACqE,iBAAiB,EAAE,IAAI,CAACR,iBAAiB,CAAC;IACvF,MAAMkC,QAAQ,GAAI,IAAI,CAACzD,gBAAgB,GAAGsD,UAAU,CAACI,MAAM,CAAC,IAAI,CAACF,OAAO,CAAC,CAACC,QAAS;IACnFA,QAAQ,CAACE,eAAe,GAAG,IAAI,CAACtM,WAAW,CAACmB,aAAa;IACzDiL,QAAQ,CAAC9C,oBAAoB,GAAG,IAAI,CAACD,UAAU;IAC/C+C,QAAQ,CACHG,WAAW,CAAC,CAAC,CACb3b,IAAI,CAAC6U,yDAAS,CAAC,IAAI,CAACuF,UAAU,CAAC,CAAC,CAChC3gB,SAAS,CAAC,MAAM,IAAI,CAAC2e,OAAO,CAAC,CAAC,CAAC;IACpC,IAAI,CAACe,gBAAgB,CAAC,IAAI,CAACD,aAAa,CAAC;IACzC,IAAI,CAACJ,qBAAqB,CAAC,CAAC;IAC5B0C,QAAQ,CAACxD,IAAI,CAACmD,KAAK,CAAC;EACxB;EACA;EACA7C,IAAIA,CAAC6C,KAAK,GAAG,IAAI,CAACnE,SAAS,EAAE;IACzB,MAAMwE,QAAQ,GAAG,IAAI,CAACzD,gBAAgB;IACtC,IAAIyD,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACI,SAAS,CAAC,CAAC,EAAE;QACtBJ,QAAQ,CAAClD,IAAI,CAAC6C,KAAK,CAAC;MACxB,CAAC,MACI;QACDK,QAAQ,CAACJ,wBAAwB,CAAC,CAAC;QACnC,IAAI,CAAChD,OAAO,CAAC,CAAC;MAClB;IACJ;EACJ;EACA;EACAyD,MAAMA,CAAChS,MAAM,EAAE;IACX,IAAI,CAACgP,iBAAiB,CAAC,CAAC,GAAG,IAAI,CAACP,IAAI,CAAC,CAAC,GAAG,IAAI,CAACN,IAAI,CAAC1S,SAAS,EAAEuE,MAAM,CAAC;EACzE;EACA;EACAgP,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,CAAC,IAAI,CAACd,gBAAgB,IAAI,IAAI,CAACA,gBAAgB,CAAC6D,SAAS,CAAC,CAAC;EACvE;EACA;EACAN,cAAcA,CAACzR,MAAM,EAAE;IACnB,IAAI,IAAI,CAACgO,WAAW,EAAE;MAClB,MAAMiE,gBAAgB,GAAG,IAAI,CAACjE,WAAW,CAACkE,SAAS,CAAC,CAAC,CAChDC,gBAAgB;MACrB,IAAI,CAAC,CAAC,IAAI,CAAC9D,gBAAgB,IAAI,CAACrO,MAAM,KAAKiS,gBAAgB,CAACG,OAAO,YAAY9I,qDAAU,EAAE;QACvF,OAAO,IAAI,CAAC0E,WAAW;MAC3B;MACA,IAAI,CAACO,OAAO,CAAC,CAAC;IAClB;IACA,MAAM8D,mBAAmB,GAAG,IAAI,CAAC7C,iBAAiB,CAAC8C,2BAA2B,CAAC,IAAI,CAAC/M,WAAW,CAAC;IAChG;IACA,MAAMgN,QAAQ,GAAG,IAAI,CAAChD,QAAQ,CACzB3J,QAAQ,CAAC,CAAC,CACV4M,mBAAmB,CAAC,IAAI,CAACnE,gBAAgB,GAAGrO,MAAM,IAAI,IAAI,CAACuF,WAAW,GAAG,IAAI,CAACA,WAAW,CAAC,CAC1FkN,qBAAqB,CAAC,IAAI,IAAI,CAACrC,eAAe,UAAU,CAAC,CACzDsC,sBAAsB,CAAC,KAAK,CAAC,CAC7BC,kBAAkB,CAAC,IAAI,CAACxC,eAAe,CAAC,CACxCyC,wBAAwB,CAACP,mBAAmB,CAAC;IAClDE,QAAQ,CAACM,eAAe,CAAC1c,IAAI,CAAC6U,yDAAS,CAAC,IAAI,CAACuF,UAAU,CAAC,CAAC,CAAC3gB,SAAS,CAAC6gB,MAAM,IAAI;MAC1E,IAAI,CAACqC,2BAA2B,CAACrC,MAAM,CAACsC,cAAc,CAAC;MACvD,IAAI,IAAI,CAAC7E,gBAAgB,EAAE;QACvB,IAAIuC,MAAM,CAACuC,wBAAwB,CAACC,gBAAgB,IAAI,IAAI,CAAC/E,gBAAgB,CAAC6D,SAAS,CAAC,CAAC,EAAE;UACvF;UACA;UACA,IAAI,CAACzM,OAAO,CAACsL,GAAG,CAAC,MAAM,IAAI,CAACnC,IAAI,CAAC,CAAC,CAAC,CAAC;QACxC;MACJ;IACJ,CAAC,CAAC;IACF,IAAI,CAACT,WAAW,GAAG,IAAI,CAACuB,QAAQ,CAAC2D,MAAM,CAAC;MACpCC,SAAS,EAAE,IAAI,CAACtD,IAAI;MACpBsC,gBAAgB,EAAEI,QAAQ;MAC1Ba,UAAU,EAAE,GAAG,IAAI,CAAChD,eAAe,IAAI7C,WAAW,EAAE;MACpDqC,cAAc,EAAE,IAAI,CAACY,eAAe,CAAC;IACzC,CAAC,CAAC;IACF,IAAI,CAACvC,eAAe,CAAC,IAAI,CAACD,WAAW,CAAC;IACtC,IAAI,CAACA,WAAW,CACXqF,WAAW,CAAC,CAAC,CACbld,IAAI,CAAC6U,yDAAS,CAAC,IAAI,CAACuF,UAAU,CAAC,CAAC,CAChC3gB,SAAS,CAAC,MAAM,IAAI,CAAC2e,OAAO,CAAC,CAAC,CAAC;IACpC,IAAI,CAACP,WAAW,CACXsF,oBAAoB,CAAC,CAAC,CACtBnd,IAAI,CAAC6U,yDAAS,CAAC,IAAI,CAACuF,UAAU,CAAC,CAAC,CAChC3gB,SAAS,CAAC,MAAM,IAAI,CAACse,gBAAgB,EAAEqF,sBAAsB,CAAC,CAAC,CAAC;IACrE,IAAI,CAACvF,WAAW,CACXwF,aAAa,CAAC,CAAC,CACfrd,IAAI,CAAC6U,yDAAS,CAAC,IAAI,CAACuF,UAAU,CAAC,CAAC,CAChC3gB,SAAS,CAACqhB,KAAK,IAAI;MACpB,IAAI,IAAI,CAACjC,iBAAiB,CAAC,CAAC,IAAIiC,KAAK,CAACwC,OAAO,KAAKrI,yDAAM,IAAI,CAACC,qEAAc,CAAC4F,KAAK,CAAC,EAAE;QAChFA,KAAK,CAACyC,cAAc,CAAC,CAAC;QACtBzC,KAAK,CAAC7S,eAAe,CAAC,CAAC;QACvB,IAAI,CAACkH,OAAO,CAACsL,GAAG,CAAC,MAAM,IAAI,CAACnC,IAAI,CAAC,CAAC,CAAC,CAAC;MACxC;IACJ,CAAC,CAAC;IACF,IAAI,IAAI,CAACqB,eAAe,EAAE6D,2BAA2B,EAAE;MACnD,IAAI,CAAC3F,WAAW,CAAC4F,aAAa,CAAC,GAAG,IAAI,CAACxD,eAAe,gCAAgC,CAAC;IAC3F;IACA,OAAO,IAAI,CAACpC,WAAW;EAC3B;EACA;EACAO,OAAOA,CAAA,EAAG;IACN,IAAI,IAAI,CAACP,WAAW,IAAI,IAAI,CAACA,WAAW,CAAC6F,WAAW,CAAC,CAAC,EAAE;MACpD,IAAI,CAAC7F,WAAW,CAAC8F,MAAM,CAAC,CAAC;IAC7B;IACA,IAAI,CAAC5F,gBAAgB,GAAG,IAAI;EAChC;EACA;EACAD,eAAeA,CAACuD,UAAU,EAAE;IACxB,MAAM5L,QAAQ,GAAG4L,UAAU,CAACU,SAAS,CAAC,CAAC,CAACC,gBAAgB;IACxD,MAAMnS,MAAM,GAAG,IAAI,CAAC+T,UAAU,CAAC,CAAC;IAChC,MAAMvH,OAAO,GAAG,IAAI,CAACwH,mBAAmB,CAAC,CAAC;IAC1CpO,QAAQ,CAACqO,aAAa,CAAC,CACnB,IAAI,CAACC,UAAU,CAAC;MAAE,GAAGlU,MAAM,CAACmU,IAAI;MAAE,GAAG3H,OAAO,CAAC2H;IAAK,CAAC,CAAC,EACpD,IAAI,CAACD,UAAU,CAAC;MAAE,GAAGlU,MAAM,CAACoU,QAAQ;MAAE,GAAG5H,OAAO,CAAC4H;IAAS,CAAC,CAAC,CAC/D,CAAC;EACN;EACA;EACAF,UAAUA,CAACtO,QAAQ,EAAE;IACjB,MAAMyO,MAAM,GAAG1G,oBAAoB;IACnC,MAAM2G,KAAK,GAAG,CAAC,IAAI,CAACzE,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC/e,KAAK,IAAI,KAAK;IACpD,IAAI8U,QAAQ,CAAC2O,OAAO,KAAK,KAAK,EAAE;MAC5B3O,QAAQ,CAAC4O,OAAO,GAAG,CAACH,MAAM;IAC9B,CAAC,MACI,IAAIzO,QAAQ,CAAC2O,OAAO,KAAK,QAAQ,EAAE;MACpC3O,QAAQ,CAAC4O,OAAO,GAAGH,MAAM;IAC7B,CAAC,MACI,IAAIzO,QAAQ,CAAC6O,OAAO,KAAK,OAAO,EAAE;MACnC7O,QAAQ,CAAC8O,OAAO,GAAGJ,KAAK,GAAG,CAACD,MAAM,GAAGA,MAAM;IAC/C,CAAC,MACI,IAAIzO,QAAQ,CAAC6O,OAAO,KAAK,KAAK,EAAE;MACjC7O,QAAQ,CAAC8O,OAAO,GAAGJ,KAAK,GAAGD,MAAM,GAAG,CAACA,MAAM;IAC/C;IACA,OAAOzO,QAAQ;EACnB;EACA;AACJ;AACA;AACA;EACImO,UAAUA,CAAA,EAAG;IACT,MAAMO,KAAK,GAAG,CAAC,IAAI,CAACzE,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC/e,KAAK,IAAI,KAAK;IACpD,MAAM8U,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAI+O,cAAc;IAClB,IAAI/O,QAAQ,IAAI,OAAO,IAAIA,QAAQ,IAAI,OAAO,EAAE;MAC5C+O,cAAc,GAAG;QAAEF,OAAO,EAAE,QAAQ;QAAEF,OAAO,EAAE3O,QAAQ,IAAI,OAAO,GAAG,KAAK,GAAG;MAAS,CAAC;IAC3F,CAAC,MACI,IAAIA,QAAQ,IAAI,QAAQ,IACxBA,QAAQ,IAAI,MAAM,IAAI0O,KAAM,IAC5B1O,QAAQ,IAAI,OAAO,IAAI,CAAC0O,KAAM,EAAE;MACjCK,cAAc,GAAG;QAAEF,OAAO,EAAE,OAAO;QAAEF,OAAO,EAAE;MAAS,CAAC;IAC5D,CAAC,MACI,IAAI3O,QAAQ,IAAI,OAAO,IACvBA,QAAQ,IAAI,OAAO,IAAI0O,KAAM,IAC7B1O,QAAQ,IAAI,MAAM,IAAI,CAAC0O,KAAM,EAAE;MAChCK,cAAc,GAAG;QAAEF,OAAO,EAAE,KAAK;QAAEF,OAAO,EAAE;MAAS,CAAC;IAC1D,CAAC,MACI,IAAI,OAAOjQ,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAMgI,iCAAiC,CAAC1G,QAAQ,CAAC;IACrD;IACA,MAAM;MAAEgP,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACC,eAAe,CAACH,cAAc,CAACF,OAAO,EAAEE,cAAc,CAACJ,OAAO,CAAC;IACrF,OAAO;MACHJ,IAAI,EAAEQ,cAAc;MACpBP,QAAQ,EAAE;QAAEK,OAAO,EAAEG,CAAC;QAAEL,OAAO,EAAEM;MAAE;IACvC,CAAC;EACL;EACA;EACAb,mBAAmBA,CAAA,EAAG;IAClB,MAAMM,KAAK,GAAG,CAAC,IAAI,CAACzE,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC/e,KAAK,IAAI,KAAK;IACpD,MAAM8U,QAAQ,GAAG,IAAI,CAACA,QAAQ;IAC9B,IAAImP,eAAe;IACnB,IAAInP,QAAQ,IAAI,OAAO,EAAE;MACrBmP,eAAe,GAAG;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAChE,CAAC,MACI,IAAIrP,QAAQ,IAAI,OAAO,EAAE;MAC1BmP,eAAe,GAAG;QAAEC,QAAQ,EAAE,QAAQ;QAAEC,QAAQ,EAAE;MAAM,CAAC;IAC7D,CAAC,MACI,IAAIrP,QAAQ,IAAI,QAAQ,IACxBA,QAAQ,IAAI,MAAM,IAAI0O,KAAM,IAC5B1O,QAAQ,IAAI,OAAO,IAAI,CAAC0O,KAAM,EAAE;MACjCS,eAAe,GAAG;QAAEC,QAAQ,EAAE,KAAK;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAC7D,CAAC,MACI,IAAIrP,QAAQ,IAAI,OAAO,IACvBA,QAAQ,IAAI,OAAO,IAAI0O,KAAM,IAC7B1O,QAAQ,IAAI,MAAM,IAAI,CAAC0O,KAAM,EAAE;MAChCS,eAAe,GAAG;QAAEC,QAAQ,EAAE,OAAO;QAAEC,QAAQ,EAAE;MAAS,CAAC;IAC/D,CAAC,MACI,IAAI,OAAO3Q,SAAS,KAAK,WAAW,IAAIA,SAAS,EAAE;MACpD,MAAMgI,iCAAiC,CAAC1G,QAAQ,CAAC;IACrD;IACA,MAAM;MAAEgP,CAAC;MAAEC;IAAE,CAAC,GAAG,IAAI,CAACC,eAAe,CAACC,eAAe,CAACC,QAAQ,EAAED,eAAe,CAACE,QAAQ,CAAC;IACzF,OAAO;MACHd,IAAI,EAAEY,eAAe;MACrBX,QAAQ,EAAE;QAAEY,QAAQ,EAAEJ,CAAC;QAAEK,QAAQ,EAAEJ;MAAE;IACzC,CAAC;EACL;EACA;EACA5F,qBAAqBA,CAAA,EAAG;IACpB;IACA;IACA,IAAI,IAAI,CAACf,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACpS,OAAO,GAAG,IAAI,CAACA,OAAO;MAC5C,IAAI,CAACoS,gBAAgB,CAACgH,aAAa,CAAC,CAAC;MACrC,IAAI,CAAC5P,OAAO,CAAC6P,gBAAgB,CAAChf,IAAI,CAAC8U,oDAAI,CAAC,CAAC,CAAC,EAAED,yDAAS,CAAC,IAAI,CAACuF,UAAU,CAAC,CAAC,CAAC3gB,SAAS,CAAC,MAAM;QACpF,IAAI,IAAI,CAACse,gBAAgB,EAAE;UACvB,IAAI,CAACF,WAAW,CAACI,cAAc,CAAC,CAAC;QACrC;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACAkB,gBAAgBA,CAACF,YAAY,EAAE;IAC3B,IAAI,IAAI,CAAClB,gBAAgB,EAAE;MACvB,IAAI,CAACA,gBAAgB,CAACkB,YAAY,GAAGA,YAAY;MACjD,IAAI,CAAClB,gBAAgB,CAACgH,aAAa,CAAC,CAAC;IACzC;EACJ;EACA;EACAJ,eAAeA,CAACF,CAAC,EAAEC,CAAC,EAAE;IAClB,IAAI,IAAI,CAACjP,QAAQ,KAAK,OAAO,IAAI,IAAI,CAACA,QAAQ,KAAK,OAAO,EAAE;MACxD,IAAIiP,CAAC,KAAK,KAAK,EAAE;QACbA,CAAC,GAAG,QAAQ;MAChB,CAAC,MACI,IAAIA,CAAC,KAAK,QAAQ,EAAE;QACrBA,CAAC,GAAG,KAAK;MACb;IACJ,CAAC,MACI;MACD,IAAID,CAAC,KAAK,KAAK,EAAE;QACbA,CAAC,GAAG,OAAO;MACf,CAAC,MACI,IAAIA,CAAC,KAAK,OAAO,EAAE;QACpBA,CAAC,GAAG,KAAK;MACb;IACJ;IACA,OAAO;MAAEA,CAAC;MAAEC;IAAE,CAAC;EACnB;EACA;EACA/B,2BAA2BA,CAACC,cAAc,EAAE;IACxC,MAAM;MAAEkC,QAAQ;MAAER,OAAO;MAAEF;IAAQ,CAAC,GAAGxB,cAAc;IACrD,IAAIqC,WAAW;IACf;IACA;IACA,IAAIH,QAAQ,KAAK,QAAQ,EAAE;MACvB;MACA;MACA;MACA,IAAI,IAAI,CAACpF,IAAI,IAAI,IAAI,CAACA,IAAI,CAAC/e,KAAK,KAAK,KAAK,EAAE;QACxCskB,WAAW,GAAGX,OAAO,KAAK,KAAK,GAAG,MAAM,GAAG,OAAO;MACtD,CAAC,MACI;QACDW,WAAW,GAAGX,OAAO,KAAK,OAAO,GAAG,MAAM,GAAG,OAAO;MACxD;IACJ,CAAC,MACI;MACDW,WAAW,GAAGH,QAAQ,KAAK,QAAQ,IAAIV,OAAO,KAAK,KAAK,GAAG,OAAO,GAAG,OAAO;IAChF;IACA,IAAIa,WAAW,KAAK,IAAI,CAACC,gBAAgB,EAAE;MACvC,MAAM7D,UAAU,GAAG,IAAI,CAACxD,WAAW;MACnC,IAAIwD,UAAU,EAAE;QACZ,MAAM8D,WAAW,GAAG,GAAG,IAAI,CAAClF,eAAe,IAAI7C,WAAW,GAAG;QAC7DiE,UAAU,CAAC+D,gBAAgB,CAACD,WAAW,GAAG,IAAI,CAACD,gBAAgB,CAAC;QAChE7D,UAAU,CAACoC,aAAa,CAAC0B,WAAW,GAAGF,WAAW,CAAC;MACvD;MACA,IAAI,CAACC,gBAAgB,GAAGD,WAAW;IACvC;EACJ;EACA;EACA1G,gCAAgCA,CAAA,EAAG;IAC/B;IACA,IAAI,IAAI,CAACF,SAAS,IACd,CAAC,IAAI,CAAC1S,OAAO,IACb,CAAC,IAAI,CAACiU,gBAAgB,IACtB,IAAI,CAACO,iBAAiB,CAAC9c,MAAM,EAAE;MAC/B;IACJ;IACA;IACA;IACA,IAAI,IAAI,CAACgiB,4BAA4B,CAAC,CAAC,EAAE;MACrC,IAAI,CAAClF,iBAAiB,CAACmF,IAAI,CAAC,CACxB,YAAY,EACZxE,KAAK,IAAI;QACL,IAAI,CAACyE,+BAA+B,CAAC,CAAC;QACtC,IAAIC,KAAK,GAAGla,SAAS;QACrB,IAAIwV,KAAK,CAAC2D,CAAC,KAAKnZ,SAAS,IAAIwV,KAAK,CAAC4D,CAAC,KAAKpZ,SAAS,EAAE;UAChDka,KAAK,GAAG1E,KAAK;QACjB;QACA,IAAI,CAAC9C,IAAI,CAAC1S,SAAS,EAAEka,KAAK,CAAC;MAC/B,CAAC,CACJ,CAAC;IACN,CAAC,MACI,IAAI,IAAI,CAACtF,aAAa,KAAK,KAAK,EAAE;MACnC,IAAI,CAACuF,iCAAiC,CAAC,CAAC;MACxC,IAAI,CAACtF,iBAAiB,CAACmF,IAAI,CAAC,CACxB,YAAY,EACZxE,KAAK,IAAI;QACL,MAAM4E,KAAK,GAAG5E,KAAK,CAAC6E,aAAa,GAAG,CAAC,CAAC;QACtC,MAAM9V,MAAM,GAAG6V,KAAK,GAAG;UAAEjB,CAAC,EAAEiB,KAAK,CAACE,OAAO;UAAElB,CAAC,EAAEgB,KAAK,CAACG;QAAQ,CAAC,GAAGva,SAAS;QACzE;QACA;QACA,IAAI,CAACia,+BAA+B,CAAC,CAAC;QACtC7E,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;QACrC,MAAMmF,uBAAuB,GAAG,GAAG;QACnC,IAAI,CAACnF,kBAAkB,GAAGoF,UAAU,CAAC,MAAM,IAAI,CAAC/H,IAAI,CAAC1S,SAAS,EAAEuE,MAAM,CAAC,EAAE,IAAI,CAAC8P,eAAe,CAACqG,uBAAuB,IAAIF,uBAAuB,CAAC;MACrJ,CAAC,CACJ,CAAC;IACN;IACA,IAAI,CAACG,aAAa,CAAC,IAAI,CAAC9F,iBAAiB,CAAC;EAC9C;EACAoF,+BAA+BA,CAAA,EAAG;IAC9B,IAAI,IAAI,CAAC1F,6BAA6B,EAAE;MACpC;IACJ;IACA,IAAI,CAACA,6BAA6B,GAAG,IAAI;IACzC,MAAMqG,aAAa,GAAG,EAAE;IACxB,IAAI,IAAI,CAACb,4BAA4B,CAAC,CAAC,EAAE;MACrCa,aAAa,CAACZ,IAAI,CAAC,CACf,YAAY,EACZxE,KAAK,IAAI;QACL,MAAMqF,SAAS,GAAGrF,KAAK,CAACsF,aAAa;QACrC,IAAI,CAACD,SAAS,IAAI,CAAC,IAAI,CAACtI,WAAW,EAAEwI,cAAc,CAACC,QAAQ,CAACH,SAAS,CAAC,EAAE;UACrE,IAAI,CAAC7H,IAAI,CAAC,CAAC;QACf;MACJ,CAAC,CACJ,EAAE,CAAC,OAAO,EAAEwC,KAAK,IAAI,IAAI,CAACyF,cAAc,CAACzF,KAAK,CAAC,CAAC,CAAC;IACtD,CAAC,MACI,IAAI,IAAI,CAACZ,aAAa,KAAK,KAAK,EAAE;MACnC,IAAI,CAACuF,iCAAiC,CAAC,CAAC;MACxC,MAAMe,gBAAgB,GAAGA,CAAA,KAAM;QAC3B9F,YAAY,CAAC,IAAI,CAACC,kBAAkB,CAAC;QACrC,IAAI,CAACrC,IAAI,CAAC,IAAI,CAACqB,eAAe,CAAC1C,iBAAiB,CAAC;MACrD,CAAC;MACDiJ,aAAa,CAACZ,IAAI,CAAC,CAAC,UAAU,EAAEkB,gBAAgB,CAAC,EAAE,CAAC,aAAa,EAAEA,gBAAgB,CAAC,CAAC;IACzF;IACA,IAAI,CAACP,aAAa,CAACC,aAAa,CAAC;IACjC,IAAI,CAAC/F,iBAAiB,CAACmF,IAAI,CAAC,GAAGY,aAAa,CAAC;EACjD;EACAD,aAAaA,CAACQ,SAAS,EAAE;IACrBA,SAAS,CAAC5F,OAAO,CAAC,CAAC,CAACC,KAAK,EAAEC,QAAQ,CAAC,KAAK;MACrC,IAAI,CAAC3L,WAAW,CAACmB,aAAa,CAACmQ,gBAAgB,CAAC5F,KAAK,EAAEC,QAAQ,EAAE1D,sBAAsB,CAAC;IAC5F,CAAC,CAAC;EACN;EACAgI,4BAA4BA,CAAA,EAAG;IAC3B,OAAO,CAAC,IAAI,CAAC9F,SAAS,CAACoH,GAAG,IAAI,CAAC,IAAI,CAACpH,SAAS,CAACqH,OAAO;EACzD;EACA;EACAL,cAAcA,CAACzF,KAAK,EAAE;IAClB,IAAI,IAAI,CAACjC,iBAAiB,CAAC,CAAC,EAAE;MAC1B,MAAMgI,mBAAmB,GAAG,IAAI,CAAC/Q,SAAS,CAACgR,gBAAgB,CAAChG,KAAK,CAAC8E,OAAO,EAAE9E,KAAK,CAAC+E,OAAO,CAAC;MACzF,MAAMkB,OAAO,GAAG,IAAI,CAAC3R,WAAW,CAACmB,aAAa;MAC9C;MACA;MACA;MACA;MACA,IAAIsQ,mBAAmB,KAAKE,OAAO,IAAI,CAACA,OAAO,CAACT,QAAQ,CAACO,mBAAmB,CAAC,EAAE;QAC3E,IAAI,CAACvI,IAAI,CAAC,CAAC;MACf;IACJ;EACJ;EACA;EACAmH,iCAAiCA,CAAA,EAAG;IAChC,MAAMuB,QAAQ,GAAG,IAAI,CAAC9G,aAAa;IACnC,IAAI8G,QAAQ,KAAK,KAAK,EAAE;MACpB,MAAMD,OAAO,GAAG,IAAI,CAAC3R,WAAW,CAACmB,aAAa;MAC9C,MAAMsF,KAAK,GAAGkL,OAAO,CAAClL,KAAK;MAC3B;MACA;MACA,IAAImL,QAAQ,KAAK,IAAI,IAAKD,OAAO,CAACE,QAAQ,KAAK,OAAO,IAAIF,OAAO,CAACE,QAAQ,KAAK,UAAW,EAAE;QACxFpL,KAAK,CAACqL,UAAU,GACZrL,KAAK,CAACsL,YAAY,GACdtL,KAAK,CAACuL,gBAAgB,GAClBvL,KAAK,CAACwL,aAAa,GACf,MAAM;MAC1B;MACA;MACA;MACA,IAAIL,QAAQ,KAAK,IAAI,IAAI,CAACD,OAAO,CAACO,SAAS,EAAE;QACzCzL,KAAK,CAAC0L,cAAc,GAAG,MAAM;MACjC;MACA1L,KAAK,CAAC2L,WAAW,GAAG,MAAM;MAC1B3L,KAAK,CAAC4L,uBAAuB,GAAG,aAAa;IACjD;EACJ;EACA;IAAS,IAAI,CAAC9kB,IAAI,YAAA+kB,mBAAAlU,CAAA;MAAA,YAAAA,CAAA,IAAwFmK,UAAU,EAApB7a,+DAAE,CAAoCwE,yDAAU,GAAhDxE,+DAAE,CAA2DA,qDAAa,GAA1EA,+DAAE,CAAqFwE,kEAAmB,GAA1GxE,+DAAE,CAAqHA,2DAAmB,GAA1IA,+DAAE,CAAqJA,iDAAS,GAAhKA,+DAAE,CAA2KyE,2DAAW,GAAxLzE,+DAAE,CAAmM4J,6DAAgB,GAArN5J,+DAAE,CAAgO4J,4DAAe,GAAjP5J,+DAAE,CAA4PsZ,2BAA2B,GAAzRtZ,+DAAE,CAAoS6J,8DAAiB,GAAvT7J,+DAAE,CAAkUoa,2BAA2B,MAA/Vpa,+DAAE,CAA0XmQ,sDAAQ;IAAA,CAA4C;EAAE;EAClhB;IAAS,IAAI,CAACqG,IAAI,kBAD8ExW,+DAAE;MAAA6Q,IAAA,EACJgK,UAAU;MAAAlW,SAAA;MAAA+R,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAsO,wBAAAjgB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADRjF,yDAAE,6BAAAkF,GAAA,CAAA6R,QACK,CAAC;QAAA;MAAA;MAAAC,MAAA;QAAArE,QAAA,GADR3S,0DAAE,CAAAwR,IAAA;QAAA4J,gBAAA,GAAFpb,0DAAE,CAAAwR,IAAA;QAAAuF,QAAA,GAAF/W,0DAAE,CAAAwR,IAAA;QAAAyI,SAAA,GAAFja,0DAAE,CAAAwR,IAAA;QAAA0I,SAAA,GAAFla,0DAAE,CAAAwR,IAAA;QAAA4L,aAAA,GAAFpd,0DAAE,CAAAwR,IAAA;QAAA3I,OAAA,GAAF7I,0DAAE,CAAAwR,IAAA;QAAA2K,YAAA,GAAFnc,0DAAE,CAAAwR,IAAA;MAAA;MAAA2T,QAAA;MAAArU,UAAA;IAAA,EACsnB;EAAE;AAC9tB;AACA;EAAA,QAAAO,SAAA,oBAAAA,SAAA,KAHoGrR,+DAAE,CAGX6a,UAAU,EAAc,CAAC;IACxGhK,IAAI,EAAElB,oDAAS;IACf4B,IAAI,EAAE,CAAC;MACC6F,QAAQ,EAAE,cAAc;MACxB+N,QAAQ,EAAE,YAAY;MACtB9N,IAAI,EAAE;QACF,OAAO,EAAE,yBAAyB;QAClC,kCAAkC,EAAE;MACxC,CAAC;MACDvG,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAED,IAAI,EAAErM,yDAAUiU;EAAC,CAAC,EAAE;IAAE5H,IAAI,EAAE7Q,qDAAaqW;EAAC,CAAC,EAAE;IAAExF,IAAI,EAAErM,kEAAmBqgB;EAAC,CAAC,EAAE;IAAEhU,IAAI,EAAE7Q,2DAAmB8kB;EAAC,CAAC,EAAE;IAAEjU,IAAI,EAAE7Q,iDAASoW;EAAC,CAAC,EAAE;IAAEvF,IAAI,EAAEpM,2DAAWsgB;EAAC,CAAC,EAAE;IAAElU,IAAI,EAAEjH,6DAAgB0M;EAAC,CAAC,EAAE;IAAEzF,IAAI,EAAEjH,4DAAeob;EAAC,CAAC,EAAE;IAAEnU,IAAI,EAAErI,SAAS;IAAE8O,UAAU,EAAE,CAAC;MAC/PzG,IAAI,EAAEhB,iDAAM;MACZ0B,IAAI,EAAE,CAAC+H,2BAA2B;IACtC,CAAC;EAAE,CAAC,EAAE;IAAEzI,IAAI,EAAEhH,8DAAiBob;EAAC,CAAC,EAAE;IAAEpU,IAAI,EAAErI,SAAS;IAAE8O,UAAU,EAAE,CAAC;MAC/DzG,IAAI,EAAEjB,mDAAQA;IAClB,CAAC,EAAE;MACCiB,IAAI,EAAEhB,iDAAM;MACZ0B,IAAI,EAAE,CAAC6I,2BAA2B;IACtC,CAAC;EAAE,CAAC,EAAE;IAAEvJ,IAAI,EAAErI,SAAS;IAAE8O,UAAU,EAAE,CAAC;MAClCzG,IAAI,EAAEhB,iDAAM;MACZ0B,IAAI,EAAE,CAACpB,sDAAQ;IACnB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEwC,QAAQ,EAAE,CAAC;MACpC9B,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE6J,gBAAgB,EAAE,CAAC;MACnBvK,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC,4BAA4B;IACvC,CAAC,CAAC;IAAEwF,QAAQ,EAAE,CAAC;MACXlG,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC,oBAAoB;IAC/B,CAAC,CAAC;IAAE0I,SAAS,EAAE,CAAC;MACZpJ,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAE2I,SAAS,EAAE,CAAC;MACZrJ,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC,qBAAqB;IAChC,CAAC,CAAC;IAAE6L,aAAa,EAAE,CAAC;MAChBvM,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC,yBAAyB;IACpC,CAAC,CAAC;IAAE1I,OAAO,EAAE,CAAC;MACVgI,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC,YAAY;IACvB,CAAC,CAAC;IAAE4K,YAAY,EAAE,CAAC;MACftL,IAAI,EAAEf,gDAAK;MACXyB,IAAI,EAAE,CAAC,iBAAiB;IAC5B,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAM0L,gBAAgB,CAAC;EACnBljB,WAAWA,CAACqrB,kBAAkB,EAAE9S,WAAW,EAAE+S,aAAa,EAAE;IACxD,IAAI,CAACD,kBAAkB,GAAGA,kBAAkB;IAC5C,IAAI,CAAC9S,WAAW,GAAGA,WAAW;IAC9B;IACA,IAAI,CAACgT,YAAY,GAAG,KAAK;IACzB;IACA,IAAI,CAACC,mBAAmB,GAAG,KAAK;IAChC;IACA,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB;IACA,IAAI,CAACC,OAAO,GAAG,IAAI7M,yCAAO,CAAC,CAAC;IAC5B;IACA,IAAI,CAAC8M,cAAc,GAAG,sBAAsB;IAC5C;IACA,IAAI,CAACC,cAAc,GAAG,sBAAsB;IAC5C,IAAI,CAACC,mBAAmB,GAAGP,aAAa,KAAK,gBAAgB;EACjE;EACA;AACJ;AACA;AACA;EACInK,IAAIA,CAACmD,KAAK,EAAE;IACR;IACA,IAAI,IAAI,CAACwH,cAAc,IAAI,IAAI,EAAE;MAC7BjI,YAAY,CAAC,IAAI,CAACiI,cAAc,CAAC;IACrC;IACA,IAAI,CAACC,cAAc,GAAG7C,UAAU,CAAC,MAAM;MACnC,IAAI,CAAC8C,iBAAiB,CAAC,IAAI,CAAC;MAC5B,IAAI,CAACD,cAAc,GAAGtd,SAAS;IACnC,CAAC,EAAE6V,KAAK,CAAC;EACb;EACA;AACJ;AACA;AACA;EACI7C,IAAIA,CAAC6C,KAAK,EAAE;IACR;IACA,IAAI,IAAI,CAACyH,cAAc,IAAI,IAAI,EAAE;MAC7BlI,YAAY,CAAC,IAAI,CAACkI,cAAc,CAAC;IACrC;IACA,IAAI,CAACD,cAAc,GAAG5C,UAAU,CAAC,MAAM;MACnC,IAAI,CAAC8C,iBAAiB,CAAC,KAAK,CAAC;MAC7B,IAAI,CAACF,cAAc,GAAGrd,SAAS;IACnC,CAAC,EAAE6V,KAAK,CAAC;EACb;EACA;EACAQ,WAAWA,CAAA,EAAG;IACV,OAAO,IAAI,CAAC4G,OAAO;EACvB;EACA;EACA3G,SAASA,CAAA,EAAG;IACR,OAAO,IAAI,CAAC0G,UAAU;EAC1B;EACAhR,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC8J,wBAAwB,CAAC,CAAC;IAC/B,IAAI,CAACmH,OAAO,CAACtH,QAAQ,CAAC,CAAC;IACvB,IAAI,CAACS,eAAe,GAAG,IAAI;EAC/B;EACA;AACJ;AACA;AACA;AACA;EACI0B,sBAAsBA,CAAA,EAAG;IACrB,IAAI,IAAI,CAACiF,mBAAmB,EAAE;MAC1B,IAAI,CAAC/J,IAAI,CAAC,CAAC,CAAC;IAChB;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIyG,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACmD,kBAAkB,CAACY,YAAY,CAAC,CAAC;EAC1C;EACAC,iBAAiBA,CAAC;IAAE3C;EAAc,CAAC,EAAE;IACjC,IAAI,CAACA,aAAa,IAAI,CAAC,IAAI,CAAC1E,eAAe,CAAC4E,QAAQ,CAACF,aAAa,CAAC,EAAE;MACjE,IAAI,IAAI,CAACxE,SAAS,CAAC,CAAC,EAAE;QAClB,IAAI,CAACtD,IAAI,CAAC,IAAI,CAACI,oBAAoB,CAAC;MACxC,CAAC,MACI;QACD,IAAI,CAACsK,kBAAkB,CAAC,KAAK,CAAC;MAClC;IACJ;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIC,OAAOA,CAAA,EAAG;IACN,IAAI,CAACb,YAAY,GAAG,IAAI,CAACc,mBAAmB,CAAC,CAAC;IAC9C,IAAI,CAACnE,aAAa,CAAC,CAAC;EACxB;EACA;EACAmE,mBAAmBA,CAAA,EAAG;IAClB,MAAMC,IAAI,GAAG,IAAI,CAAC/T,WAAW,CAACmB,aAAa,CAAC6S,qBAAqB,CAAC,CAAC;IACnE,OAAOD,IAAI,CAACE,MAAM,GAAG5L,UAAU,IAAI0L,IAAI,CAACG,KAAK,IAAI5L,SAAS;EAC9D;EACA;EACA6L,mBAAmBA,CAAC;IAAEC;EAAc,CAAC,EAAE;IACnC,IAAIA,aAAa,KAAK,IAAI,CAAChB,cAAc,IAAIgB,aAAa,KAAK,IAAI,CAACf,cAAc,EAAE;MAChF,IAAI,CAACO,kBAAkB,CAACQ,aAAa,KAAK,IAAI,CAAChB,cAAc,CAAC;IAClE;EACJ;EACA;EACApH,wBAAwBA,CAAA,EAAG;IACvB,IAAI,IAAI,CAACwH,cAAc,IAAI,IAAI,EAAE;MAC7BlI,YAAY,CAAC,IAAI,CAACkI,cAAc,CAAC;IACrC;IACA,IAAI,IAAI,CAACD,cAAc,IAAI,IAAI,EAAE;MAC7BjI,YAAY,CAAC,IAAI,CAACiI,cAAc,CAAC;IACrC;IACA,IAAI,CAACC,cAAc,GAAG,IAAI,CAACD,cAAc,GAAGrd,SAAS;EACzD;EACA;EACA0d,kBAAkBA,CAACS,SAAS,EAAE;IAC1B,IAAIA,SAAS,EAAE;MACX,IAAI,CAACpB,mBAAmB,GAAG,IAAI;IACnC,CAAC,MACI,IAAI,CAAC,IAAI,CAACzG,SAAS,CAAC,CAAC,EAAE;MACxB,IAAI,CAAC2G,OAAO,CAAClpB,IAAI,CAAC,CAAC;IACvB;EACJ;EACA;EACAwpB,iBAAiBA,CAACjH,SAAS,EAAE;IACzB;IACA;IACA;IACA,MAAM8H,OAAO,GAAG,IAAI,CAACC,QAAQ,CAACpT,aAAa;IAC3C,MAAMqT,SAAS,GAAG,IAAI,CAACpB,cAAc;IACrC,MAAMqB,SAAS,GAAG,IAAI,CAACpB,cAAc;IACrCiB,OAAO,CAACxR,SAAS,CAACT,MAAM,CAACmK,SAAS,GAAGiI,SAAS,GAAGD,SAAS,CAAC;IAC3DF,OAAO,CAACxR,SAAS,CAACjC,GAAG,CAAC2L,SAAS,GAAGgI,SAAS,GAAGC,SAAS,CAAC;IACxD,IAAI,IAAI,CAACvB,UAAU,KAAK1G,SAAS,EAAE;MAC/B,IAAI,CAAC0G,UAAU,GAAG1G,SAAS;MAC3B,IAAI,CAACsG,kBAAkB,CAACY,YAAY,CAAC,CAAC;IAC1C;IACA;IACA;IACA,IAAIlH,SAAS,IAAI,CAAC,IAAI,CAAC8G,mBAAmB,IAAI,OAAOoB,gBAAgB,KAAK,UAAU,EAAE;MAClF,MAAM9V,MAAM,GAAG8V,gBAAgB,CAACJ,OAAO,CAAC;MACxC;MACA,IAAI1V,MAAM,CAAC+V,gBAAgB,CAAC,oBAAoB,CAAC,KAAK,IAAI,IACtD/V,MAAM,CAAC+V,gBAAgB,CAAC,gBAAgB,CAAC,KAAK,MAAM,EAAE;QACtD,IAAI,CAACrB,mBAAmB,GAAG,IAAI;MACnC;IACJ;IACA,IAAI9G,SAAS,EAAE;MACX,IAAI,CAACqH,OAAO,CAAC,CAAC;IAClB;IACA,IAAI,IAAI,CAACP,mBAAmB,EAAE;MAC1BgB,OAAO,CAACxR,SAAS,CAACjC,GAAG,CAAC,yBAAyB,CAAC;MAChD,IAAI,CAAC+S,kBAAkB,CAACpH,SAAS,CAAC;IACtC;EACJ;EACA;IAAS,IAAI,CAACjf,IAAI,YAAAqnB,yBAAAxW,CAAA;MAAA,YAAAA,CAAA,IAAwFuM,gBAAgB,EApN1Bjd,+DAAE,CAoN0CA,4DAAoB,GApNhEA,+DAAE,CAoN2EA,qDAAa,GApN1FA,+DAAE,CAoNqGyP,gEAAqB;IAAA,CAA4D;EAAE;EAC1R;IAAS,IAAI,CAACkB,IAAI,kBArN8E3Q,+DAAE;MAAA6Q,IAAA,EAqNJoM,gBAAgB;MAAAtY,SAAA;MAAAyiB,SAAA,WAAAC,uBAAApiB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArNdjF,yDAAE,CAAAmZ,GAAA;QAAA;QAAA,IAAAlU,EAAA;UAAA,IAAAsiB,EAAA;UAAFvnB,4DAAE,CAAAunB,EAAA,GAAFvnB,yDAAE,QAAAkF,GAAA,CAAA2hB,QAAA,GAAAU,EAAA,CAAAG,KAAA;QAAA;MAAA;MAAAhR,SAAA,kBAqN0G,MAAM;MAAAC,QAAA;MAAAC,YAAA,WAAA+Q,8BAAA1iB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArNlHjF,wDAAE,wBAAA4nB,+CAAA1c,MAAA;YAAA,OAqNJhG,GAAA,CAAA+gB,iBAAA,CAAA/a,MAAwB,CAAC;UAAA,CAAV,CAAC;QAAA;QAAA,IAAAjG,EAAA;UArNdjF,yDAAE,SAqNJkF,GAAA,CAAA4Z,SAAA,CAAU,CAAC,GAAG,CAAC,GAAG,IAAH,CAAC;QAAA;MAAA;MAAAhO,UAAA;MAAAC,QAAA,GArNd/Q,iEAAE;MAAA4E,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA8iB,0BAAA5iB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAA,MAAAyB,GAAA,GAAF1G,8DAAE;UAAFA,4DAAE,eAqNmkB,CAAC;UArNtkBA,wDAAE,0BAAA+nB,sDAAA7c,MAAA;YAAFlL,2DAAE,CAAA0G,GAAA;YAAA,OAAF1G,yDAAE,CAqNmfkF,GAAA,CAAAuhB,mBAAA,CAAAvb,MAA0B,CAAC;UAAA,CAAC,CAAC;UArNlhBlL,4DAAE,YAqN0oB,CAAC;UArN7oBA,oDAAE,EAqNqpB,CAAC;UArNxpBA,0DAAE,CAqN2pB,CAAC,CAAO,CAAC;QAAA;QAAA,IAAAiF,EAAA;UArNtqBjF,yDAAE,2BAAAkF,GAAA,CAAAogB,YAqNkkB,CAAC;UArNrkBtlB,wDAAE,YAAAkF,GAAA,CAAAiX,YAqN6d,CAAC;UArNhenc,uDAAE,EAqNqpB,CAAC;UArNxpBA,+DAAE,CAAAkF,GAAA,CAAA2D,OAqNqpB,CAAC;QAAA;MAAA;MAAAmf,YAAA,GAAsqJzP,qDAAO;MAAArH,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAyJ;EAAE;AACpkL;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAvNoGrR,+DAAE,CAuNXid,gBAAgB,EAAc,CAAC;IAC9GpM,IAAI,EAAE3B,oDAAS;IACfqC,IAAI,EAAE,CAAC;MAAE6F,QAAQ,EAAE,uBAAuB;MAAEjG,aAAa,EAAEhC,4DAAiB,CAACqC,IAAI;MAAEJ,eAAe,EAAEhC,kEAAuB,CAACqC,MAAM;MAAE4F,IAAI,EAAE;QAC9H;QACA;QACA,cAAc,EAAE,wBAAwB;QACxC,cAAc,EAAE,2BAA2B;QAC3C,aAAa,EAAE;MACnB,CAAC;MAAEvG,UAAU,EAAE,IAAI;MAAE/B,OAAO,EAAE,CAACwJ,qDAAO,CAAC;MAAExT,QAAQ,EAAE,oTAAoT;MAAEmM,MAAM,EAAE,CAAC,6lJAA6lJ;IAAE,CAAC;EAC99J,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEL,IAAI,EAAE7Q,4DAAoBmnB;EAAC,CAAC,EAAE;IAAEtW,IAAI,EAAE7Q,qDAAaqW;EAAC,CAAC,EAAE;IAAExF,IAAI,EAAErI,SAAS;IAAE8O,UAAU,EAAE,CAAC;MACxGzG,IAAI,EAAEjB,mDAAQA;IAClB,CAAC,EAAE;MACCiB,IAAI,EAAEhB,iDAAM;MACZ0B,IAAI,EAAE,CAAC9B,gEAAqB;IAChC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEoX,QAAQ,EAAE,CAAC;MACpChW,IAAI,EAAEyH,oDAAS;MACf/G,IAAI,EAAE,CAAC,SAAS,EAAE;QACV;QACA;QACA0W,MAAM,EAAE;MACZ,CAAC;IACT,CAAC;EAAE,CAAC;AAAA;;AAEhB;AACA;AACA;AACA;AACA,MAAMC,oBAAoB,GAAG;EACzB;EACAC,YAAY,EAAEtP,6DAAO,CAAC,OAAO,EAAE;EAC3B;EACA;EACAC,2DAAK,CAAC,uBAAuB,EAAEC,2DAAK,CAAC;IAAEqP,OAAO,EAAE,CAAC;IAAE5Q,SAAS,EAAE;EAAa,CAAC,CAAC,CAAC,EAC9EsB,2DAAK,CAAC,SAAS,EAAEC,2DAAK,CAAC;IAAEvB,SAAS,EAAE;EAAW,CAAC,CAAC,CAAC,EAClDwB,gEAAU,CAAC,cAAc,EAAEC,6DAAO,CAAC,kCAAkC,CAAC,CAAC,EACvED,gEAAU,CAAC,aAAa,EAAEC,6DAAO,CAAC,iCAAiC,CAAC,CAAC,CACxE;AACL,CAAC;AAED,MAAM5K,gBAAgB,CAAC;EACnB;IAAS,IAAI,CAACxO,IAAI,YAAAwoB,yBAAA3X,CAAA;MAAA,YAAAA,CAAA,IAAwFrC,gBAAgB;IAAA,CAAkD;EAAE;EAC9K;IAAS,IAAI,CAACqJ,IAAI,kBAhQ8E1X,8DAAE;MAAA6Q,IAAA,EAgQSxC;IAAgB,EAAqL;EAAE;EAClT;IAAS,IAAI,CAACuJ,IAAI,kBAjQ8E5X,8DAAE;MAAAsoB,SAAA,EAiQsC,CAAC1O,4CAA4C,CAAC;MAAA7K,OAAA,GAAYmB,0DAAU,EAAExC,0DAAY,EAAEgL,+DAAa,EAAE1I,oEAAe,EAAEA,oEAAe,EAAEkJ,qEAAmB;IAAA,EAAI;EAAE;AAC1S;AACA;EAAA,QAAA7H,SAAA,oBAAAA,SAAA,KAnQoGrR,+DAAE,CAmQXqO,gBAAgB,EAAc,CAAC;IAC9GwC,IAAI,EAAEd,mDAAQ;IACdwB,IAAI,EAAE,CAAC;MACCxC,OAAO,EAAE,CAACmB,0DAAU,EAAExC,0DAAY,EAAEgL,+DAAa,EAAE1I,oEAAe,EAAE6K,UAAU,EAAEoC,gBAAgB,CAAC;MACjGnF,OAAO,EAAE,CAAC+C,UAAU,EAAEoC,gBAAgB,EAAEjN,oEAAe,EAAEkJ,qEAAmB,CAAC;MAC7EoP,SAAS,EAAE,CAAC1O,4CAA4C;IAC5D,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA", "sources": ["./src/app/core/services/rewards.service.ts", "./src/app/features/rewards/components/exchange-history/exchange-history.component.ts", "./src/app/features/rewards/components/exchange-history/exchange-history.component.html", "./src/app/features/rewards/components/reward-detail/reward-detail.component.ts", "./src/app/features/rewards/components/reward-detail/reward-detail.component.html", "./src/app/features/rewards/components/rewards-list/rewards-list.component.ts", "./src/app/features/rewards/components/rewards-list/rewards-list.component.html", "./src/app/features/rewards/rewards.module.ts", "./src/app/features/rewards/rewards.routes.ts", "./node_modules/@angular/material/fesm2022/badge.mjs", "./node_modules/@angular/material/fesm2022/tooltip.mjs"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { Observable, of, BehaviorSubject } from 'rxjs';\nimport { <PERSON><PERSON>, Partner, RewardExchange, RewardCategory, PartnerCategory, ExchangeStatus } from '../models';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class RewardsService {\n  private rewardsSubject = new BehaviorSubject<Reward[]>([]);\n  private partnersSubject = new BehaviorSubject<Partner[]>([]);\n  private exchangesSubject = new BehaviorSubject<RewardExchange[]>([]);\n\n  public rewards$ = this.rewardsSubject.asObservable();\n  public partners$ = this.partnersSubject.asObservable();\n  public exchanges$ = this.exchangesSubject.asObservable();\n\n  constructor() {\n    this.initializeSampleData();\n  }\n\n  private initializeSampleData(): void {\n    // Sample partners\n    const samplePartners: Partner[] = [\n      {\n        id: 'partner1',\n        name: 'Café Central Monastir',\n        description: 'Café traditionnel au cœur de Monastir',\n        category: PartnerCategory.CAFE,\n        address: 'Avenue Habib Bourguiba, Monastir',\n        city: 'Monastir',\n        phone: '73123456',\n        email: '<EMAIL>',\n        location: { latitude: 35.7643, longitude: 10.8113 },\n        isActive: true,\n        rewards: ['reward1', 'reward2'],\n        createdAt: new Date('2024-01-01'),\n        updatedAt: new Date('2024-01-01')\n      },\n      {\n        id: 'partner2',\n        name: 'Restaurant La Marina',\n        description: 'Restaurant de fruits de mer avec vue sur le port',\n        category: PartnerCategory.RESTAURANT,\n        address: 'Port de Plaisance, Sousse',\n        city: 'Sousse',\n        phone: '73654321',\n        email: '<EMAIL>',\n        location: { latitude: 35.8256, longitude: 10.6411 },\n        isActive: true,\n        rewards: ['reward3', 'reward4'],\n        createdAt: new Date('2024-01-01'),\n        updatedAt: new Date('2024-01-01')\n      },\n      {\n        id: 'partner3',\n        name: 'Librairie Culturelle',\n        description: 'Librairie et centre culturel',\n        category: PartnerCategory.RETAIL,\n        address: 'Rue de la République, Monastir',\n        city: 'Monastir',\n        phone: '73789012',\n        email: '<EMAIL>',\n        location: { latitude: 35.7676, longitude: 10.8267 },\n        isActive: true,\n        rewards: ['reward5'],\n        createdAt: new Date('2024-01-01'),\n        updatedAt: new Date('2024-01-01')\n      }\n    ];\n\n    // Sample rewards\n    const sampleRewards: Reward[] = [\n      {\n        id: 'reward1',\n        title: 'Café gratuit',\n        description: 'Un café expresso ou cappuccino offert',\n        pointsRequired: 10,\n        partnerId: 'partner1',\n        partnerName: 'Café Central Monastir',\n        category: RewardCategory.FOOD,\n        imageUrl: 'assets/rewards/coffee.jpg',\n        isActive: true,\n        availableQuantity: 50,\n        validUntil: new Date('2024-12-31'),\n        terms: 'Valable du lundi au vendredi, de 8h à 18h',\n        createdAt: new Date('2024-01-01'),\n        updatedAt: new Date('2024-01-01'),\n        city: 'Monastir'\n      },\n      {\n        id: 'reward2',\n        title: 'Pâtisserie offerte',\n        description: 'Une pâtisserie tunisienne traditionnelle',\n        pointsRequired: 15,\n        partnerId: 'partner1',\n        partnerName: 'Café Central Monastir',\n        category: RewardCategory.FOOD,\n        imageUrl: 'assets/rewards/pastry.jpg',\n        isActive: true,\n        availableQuantity: 30,\n        validUntil: new Date('2024-12-31'),\n        terms: 'Selon disponibilité',\n        createdAt: new Date('2024-01-01'),\n        updatedAt: new Date('2024-01-01'),\n        city: 'Monastir'\n      },\n      {\n        id: 'reward3',\n        title: 'Entrée gratuite',\n        description: 'Une entrée offerte pour tout plat principal commandé',\n        pointsRequired: 25,\n        partnerId: 'partner2',\n        partnerName: 'Restaurant La Marina',\n        category: RewardCategory.FOOD,\n        imageUrl: 'assets/rewards/appetizer.jpg',\n        isActive: true,\n        availableQuantity: 20,\n        validUntil: new Date('2024-12-31'),\n        terms: 'Non cumulable avec d\\'autres offres',\n        createdAt: new Date('2024-01-01'),\n        updatedAt: new Date('2024-01-01'),\n        city: 'Sousse'\n      },\n      {\n        id: 'reward4',\n        title: 'Réduction 20%',\n        description: '20% de réduction sur l\\'addition totale',\n        pointsRequired: 50,\n        partnerId: 'partner2',\n        partnerName: 'Restaurant La Marina',\n        category: RewardCategory.FOOD,\n        imageUrl: 'assets/rewards/discount.jpg',\n        isActive: true,\n        availableQuantity: 15,\n        validUntil: new Date('2024-12-31'),\n        terms: 'Valable le soir uniquement, sur réservation',\n        createdAt: new Date('2024-01-01'),\n        updatedAt: new Date('2024-01-01'),\n        city: 'Sousse'\n      },\n      {\n        id: 'reward5',\n        title: 'Livre gratuit',\n        description: 'Un livre de votre choix (max 30 DT)',\n        pointsRequired: 100,\n        partnerId: 'partner3',\n        partnerName: 'Librairie Culturelle',\n        category: RewardCategory.EDUCATION,\n        imageUrl: 'assets/rewards/book.jpg',\n        isActive: true,\n        availableQuantity: 10,\n        validUntil: new Date('2024-12-31'),\n        terms: 'Livres en stock uniquement',\n        createdAt: new Date('2024-01-01'),\n        updatedAt: new Date('2024-01-01'),\n        city: 'Monastir'\n      }\n    ];\n\n    this.partnersSubject.next(samplePartners);\n    this.rewardsSubject.next(sampleRewards);\n    this.exchangesSubject.next([]);\n  }\n\n  getRewards(): Observable<Reward[]> {\n    return this.rewards$;\n  }\n\n  getRewardsByCity(city: 'Monastir' | 'Sousse'): Observable<Reward[]> {\n    return new Observable(observer => {\n      this.rewards$.subscribe(rewards => {\n        const filtered = rewards.filter(reward => \n          reward.city === city || reward.city === 'Both'\n        );\n        observer.next(filtered);\n      });\n    });\n  }\n\n  getRewardsByCategory(category: RewardCategory): Observable<Reward[]> {\n    return new Observable(observer => {\n      this.rewards$.subscribe(rewards => {\n        const filtered = rewards.filter(reward => reward.category === category);\n        observer.next(filtered);\n      });\n    });\n  }\n\n  getRewardById(id: string): Observable<Reward | null> {\n    return new Observable(observer => {\n      this.rewards$.subscribe(rewards => {\n        const reward = rewards.find(r => r.id === id) || null;\n        observer.next(reward);\n      });\n    });\n  }\n\n  getPartners(): Observable<Partner[]> {\n    return this.partners$;\n  }\n\n  getPartnerById(id: string): Observable<Partner | null> {\n    return new Observable(observer => {\n      this.partners$.subscribe(partners => {\n        const partner = partners.find(p => p.id === id) || null;\n        observer.next(partner);\n      });\n    });\n  }\n\n  async exchangeReward(rewardId: string, userId: string): Promise<RewardExchange> {\n    const rewards = this.rewardsSubject.value;\n    const reward = rewards.find(r => r.id === rewardId);\n    \n    if (!reward) {\n      throw new Error('Récompense non trouvée');\n    }\n\n    if (!reward.isActive) {\n      throw new Error('Récompense non disponible');\n    }\n\n    if (reward.availableQuantity && reward.availableQuantity <= 0) {\n      throw new Error('Stock épuisé');\n    }\n\n    // Create exchange record\n    const exchange: RewardExchange = {\n      id: this.generateExchangeId(),\n      userId,\n      rewardId,\n      pointsSpent: reward.pointsRequired,\n      status: ExchangeStatus.CONFIRMED,\n      exchangeCode: this.generateExchangeCode(),\n      exchangedAt: new Date(),\n      validUntil: reward.validUntil || new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30 days\n    };\n\n    // Update available quantity\n    if (reward.availableQuantity) {\n      reward.availableQuantity--;\n      this.rewardsSubject.next([...rewards]);\n    }\n\n    // Add to exchanges\n    const exchanges = this.exchangesSubject.value;\n    this.exchangesSubject.next([...exchanges, exchange]);\n\n    return exchange;\n  }\n\n  getUserExchanges(userId: string): Observable<RewardExchange[]> {\n    return new Observable(observer => {\n      this.exchanges$.subscribe(exchanges => {\n        const userExchanges = exchanges.filter(e => e.userId === userId);\n        observer.next(userExchanges);\n      });\n    });\n  }\n\n  private generateExchangeId(): string {\n    return 'ex_' + Date.now().toString() + Math.random().toString(36).substr(2, 9);\n  }\n\n  private generateExchangeCode(): string {\n    return Math.random().toString(36).substr(2, 8).toUpperCase();\n  }\n\n  // Get categories for filtering\n  getCategories(): RewardCategory[] {\n    return Object.values(RewardCategory);\n  }\n\n  getCategoryDisplayName(category: RewardCategory): string {\n    const names: { [key in RewardCategory]: string } = {\n      [RewardCategory.FOOD]: 'Restauration',\n      [RewardCategory.SHOPPING]: 'Shopping',\n      [RewardCategory.ENTERTAINMENT]: 'Divertissement',\n      [RewardCategory.SERVICES]: 'Services',\n      [RewardCategory.HEALTH]: 'Santé',\n      [RewardCategory.EDUCATION]: 'Éducation',\n      [RewardCategory.TRANSPORT]: 'Transport',\n      [RewardCategory.OTHER]: 'Autre'\n    };\n    return names[category];\n  }\n}\n", "import { Component, OnInit } from '@angular/core';\nimport { RewardsService } from '../../../../core/services/rewards.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { RewardExchange, ExchangeStatus, User } from '../../../../core/models';\nimport { Observable } from 'rxjs';\nimport { switchMap } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-exchange-history',\n  templateUrl: './exchange-history.component.html',\n  styleUrls: ['./exchange-history.component.css']\n})\nexport class ExchangeHistoryComponent implements OnInit {\n  exchanges$: Observable<RewardExchange[]>;\n  currentUser: User | null = null;\n  \n  ExchangeStatus = ExchangeStatus; // Make enum available in template\n\n  constructor(\n    private rewardsService: RewardsService,\n    private authService: AuthService\n  ) {\n    this.exchanges$ = this.authService.currentUser$.pipe(\n      switchMap(user => {\n        if (user) {\n          return this.rewardsService.getUserExchanges(user.uid);\n        }\n        return [];\n      })\n    );\n  }\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  getStatusIcon(status: ExchangeStatus): string {\n    switch (status) {\n      case ExchangeStatus.PENDING:\n        return 'schedule';\n      case ExchangeStatus.CONFIRMED:\n        return 'check_circle';\n      case ExchangeStatus.USED:\n        return 'done_all';\n      case ExchangeStatus.EXPIRED:\n        return 'expired';\n      case ExchangeStatus.CANCELLED:\n        return 'cancel';\n      default:\n        return 'help';\n    }\n  }\n\n  getStatusColor(status: ExchangeStatus): string {\n    switch (status) {\n      case ExchangeStatus.PENDING:\n        return 'accent';\n      case ExchangeStatus.CONFIRMED:\n        return 'primary';\n      case ExchangeStatus.USED:\n        return 'primary';\n      case ExchangeStatus.EXPIRED:\n        return 'warn';\n      case ExchangeStatus.CANCELLED:\n        return 'warn';\n      default:\n        return 'basic';\n    }\n  }\n\n  getStatusLabel(status: ExchangeStatus): string {\n    switch (status) {\n      case ExchangeStatus.PENDING:\n        return 'En attente';\n      case ExchangeStatus.CONFIRMED:\n        return 'Confirmé';\n      case ExchangeStatus.USED:\n        return 'Utilisé';\n      case ExchangeStatus.EXPIRED:\n        return 'Expiré';\n      case ExchangeStatus.CANCELLED:\n        return 'Annulé';\n      default:\n        return 'Inconnu';\n    }\n  }\n\n  formatDate(date: Date): string {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      day: '2-digit',\n      month: 'short',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  isExpired(exchange: RewardExchange): boolean {\n    if (!exchange.validUntil) return false;\n    return new Date() > new Date(exchange.validUntil);\n  }\n\n  canUseExchange(exchange: RewardExchange): boolean {\n    return exchange.status === ExchangeStatus.CONFIRMED && !this.isExpired(exchange);\n  }\n\n  copyExchangeCode(code: string): void {\n    navigator.clipboard.writeText(code).then(() => {\n      // Could show a snackbar here\n    });\n  }\n\n  getTotalPointsSpent(exchanges: RewardExchange[]): number {\n    return exchanges.reduce((total, exchange) => total + exchange.pointsSpent, 0);\n  }\n\n  getExchangesByStatus(exchanges: RewardExchange[], status: ExchangeStatus): RewardExchange[] {\n    return exchanges.filter(exchange => exchange.status === status);\n  }\n}\n", "<div class=\"exchange-history-container\">\n  <!-- Header -->\n  <div class=\"history-header slide-up\">\n    <h1>📋 Historique des échanges</h1>\n    <p>Consultez vos récompenses échangées</p>\n  </div>\n\n  <!-- Statistics -->\n  <div class=\"stats-section fade-in\" *ngIf=\"exchanges$ | async as exchanges\">\n    <div class=\"stats-grid\">\n      <mat-card class=\"stat-card\">\n        <mat-card-content>\n          <div class=\"stat-content\">\n            <mat-icon class=\"stat-icon\" style=\"color: #4caf50;\">redeem</mat-icon>\n            <div class=\"stat-info\">\n              <h3>{{ exchanges.length }}</h3>\n              <p>Échanges totaux</p>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"stat-card\">\n        <mat-card-content>\n          <div class=\"stat-content\">\n            <mat-icon class=\"stat-icon\" style=\"color: #ff9800;\">stars</mat-icon>\n            <div class=\"stat-info\">\n              <h3>{{ getTotalPointsSpent(exchanges) }}</h3>\n              <p>Points dépensés</p>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"stat-card\">\n        <mat-card-content>\n          <div class=\"stat-content\">\n            <mat-icon class=\"stat-icon\" style=\"color: #2196f3;\">check_circle</mat-icon>\n            <div class=\"stat-info\">\n              <h3>{{ getExchangesByStatus(exchanges, ExchangeStatus.CONFIRMED).length }}</h3>\n              <p>Disponibles</p>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"stat-card\">\n        <mat-card-content>\n          <div class=\"stat-content\">\n            <mat-icon class=\"stat-icon\" style=\"color: #9c27b0;\">done_all</mat-icon>\n            <div class=\"stat-info\">\n              <h3>{{ getExchangesByStatus(exchanges, ExchangeStatus.USED).length }}</h3>\n              <p>Utilisées</p>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Exchanges list -->\n  <div class=\"exchanges-section\" *ngIf=\"exchanges$ | async as exchanges\">\n    <div class=\"exchanges-list\" *ngIf=\"exchanges.length > 0; else noExchanges\">\n      <mat-card *ngFor=\"let exchange of exchanges; let i = index\" \n                class=\"exchange-card floating-card\"\n                [style.animation-delay]=\"(i * 0.1) + 's'\">\n        \n        <!-- Exchange header -->\n        <mat-card-header>\n          <div class=\"exchange-header\">\n            <div class=\"exchange-title\">\n              <h3>Récompense échangée</h3>\n              <mat-chip [color]=\"getStatusColor(exchange.status)\" selected>\n                <mat-icon>{{ getStatusIcon(exchange.status) }}</mat-icon>\n                {{ getStatusLabel(exchange.status) }}\n              </mat-chip>\n            </div>\n            \n            <div class=\"exchange-points\">\n              <mat-icon>stars</mat-icon>\n              <span>{{ exchange.pointsSpent }} pts</span>\n            </div>\n          </div>\n        </mat-card-header>\n\n        <!-- Exchange content -->\n        <mat-card-content>\n          <div class=\"exchange-details\">\n            <!-- Exchange code -->\n            <div class=\"detail-row\" *ngIf=\"canUseExchange(exchange)\">\n              <div class=\"exchange-code\">\n                <mat-icon>qr_code</mat-icon>\n                <div class=\"code-info\">\n                  <label>Code d'échange</label>\n                  <div class=\"code-value\">\n                    <span class=\"code\">{{ exchange.exchangeCode }}</span>\n                    <button mat-icon-button \n                            (click)=\"copyExchangeCode(exchange.exchangeCode)\"\n                            matTooltip=\"Copier le code\">\n                      <mat-icon>content_copy</mat-icon>\n                    </button>\n                  </div>\n                </div>\n              </div>\n            </div>\n\n            <!-- Exchange date -->\n            <div class=\"detail-row\">\n              <mat-icon>schedule</mat-icon>\n              <div>\n                <label>Date d'échange</label>\n                <span>{{ formatDate(exchange.exchangedAt) }}</span>\n              </div>\n            </div>\n\n            <!-- Valid until -->\n            <div class=\"detail-row\" *ngIf=\"exchange.validUntil\">\n              <mat-icon [style.color]=\"isExpired(exchange) ? '#f44336' : '#4caf50'\">\n                {{ isExpired(exchange) ? 'event_busy' : 'event_available' }}\n              </mat-icon>\n              <div>\n                <label>Valide jusqu'au</label>\n                <span [class.expired]=\"isExpired(exchange)\">\n                  {{ formatDate(exchange.validUntil) }}\n                </span>\n              </div>\n            </div>\n\n            <!-- Used date -->\n            <div class=\"detail-row\" *ngIf=\"exchange.usedAt\">\n              <mat-icon style=\"color: #9c27b0;\">done_all</mat-icon>\n              <div>\n                <label>Utilisé le</label>\n                <span>{{ formatDate(exchange.usedAt) }}</span>\n              </div>\n            </div>\n\n            <!-- Partner info -->\n            <div class=\"detail-row\">\n              <mat-icon>store</mat-icon>\n              <div>\n                <label>Partenaire</label>\n                <span>Partenaire local</span>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n\n        <!-- Exchange actions -->\n        <mat-card-actions *ngIf=\"canUseExchange(exchange)\">\n          <div class=\"exchange-actions\">\n            <div class=\"usage-instructions\">\n              <mat-icon>info</mat-icon>\n              <span>Présentez ce code au partenaire pour utiliser votre récompense</span>\n            </div>\n          </div>\n        </mat-card-actions>\n      </mat-card>\n    </div>\n\n    <!-- Empty state -->\n    <ng-template #noExchanges>\n      <div class=\"empty-state\">\n        <mat-card class=\"empty-card\">\n          <mat-card-content>\n            <div class=\"empty-content\">\n              <mat-icon class=\"empty-icon\">redeem</mat-icon>\n              <h3>Aucun échange pour le moment</h3>\n              <p>Vous n'avez pas encore échangé de récompenses. Explorez notre catalogue pour découvrir des offres exclusives!</p>\n              <button mat-raised-button color=\"primary\" routerLink=\"/rewards\">\n                <mat-icon>card_giftcard</mat-icon>\n                Découvrir les récompenses\n              </button>\n            </div>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </ng-template>\n  </div>\n\n  <!-- Loading state -->\n  <div class=\"loading-container\" *ngIf=\"!(exchanges$ | async)\">\n    <mat-card class=\"loading-card\">\n      <mat-card-content>\n        <div class=\"loading-content\">\n          <mat-spinner diameter=\"48\"></mat-spinner>\n          <h3>Chargement de l'historique...</h3>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { ActivatedRoute, Router } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { RewardsService } from '../../../../core/services/rewards.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { Reward, Partner, User } from '../../../../core/models';\nimport { Observable, combineLatest } from 'rxjs';\nimport { map, switchMap } from 'rxjs/operators';\n\n@Component({\n  selector: 'app-reward-detail',\n  templateUrl: './reward-detail.component.html',\n  styleUrls: ['./reward-detail.component.css']\n})\nexport class RewardDetailComponent implements OnInit {\n  reward$: Observable<Reward | null>;\n  partner$: Observable<Partner | null>;\n  currentUser: User | null = null;\n  isExchanging = false;\n\n  constructor(\n    private route: ActivatedRoute,\n    private router: Router,\n    private rewardsService: RewardsService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar\n  ) {\n    this.reward$ = this.route.params.pipe(\n      switchMap(params => this.rewardsService.getRewardById(params['id']))\n    );\n\n    this.partner$ = this.reward$.pipe(\n      switchMap(reward => \n        reward ? this.rewardsService.getPartnerById(reward.partnerId) : [null]\n      )\n    );\n  }\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n  }\n\n  canAfford(reward: Reward): boolean {\n    return this.currentUser ? this.currentUser.points >= reward.pointsRequired : false;\n  }\n\n  async exchangeReward(reward: Reward): Promise<void> {\n    if (!this.currentUser) {\n      this.snackBar.open('Vous devez être connecté pour échanger une récompense', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    if (!this.canAfford(reward)) {\n      this.snackBar.open('Points insuffisants pour cette récompense', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    if (reward.availableQuantity !== undefined && reward.availableQuantity <= 0) {\n      this.snackBar.open('Cette récompense n\\'est plus disponible', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    this.isExchanging = true;\n\n    try {\n      const exchange = await this.rewardsService.exchangeReward(reward.id, this.currentUser.uid);\n      \n      this.snackBar.open(\n        `Récompense échangée avec succès! Code d'échange: ${exchange.exchangeCode}`, \n        'Fermer', \n        { duration: 8000 }\n      );\n      \n      // Note: In a real app, you would refresh user data here\n      \n      // Navigate to exchange history or profile\n      this.router.navigate(['/profile'], { fragment: 'exchanges' });\n      \n    } catch (error: any) {\n      console.error('Exchange error:', error);\n      this.snackBar.open(error.message || 'Erreur lors de l\\'échange', 'Fermer', { duration: 3000 });\n    } finally {\n      this.isExchanging = false;\n    }\n  }\n\n  goBack(): void {\n    this.router.navigate(['/rewards']);\n  }\n\n  getCategoryIcon(category: string): string {\n    const iconMap: { [key: string]: string } = {\n      'FOOD': 'restaurant',\n      'SHOPPING': 'shopping_bag',\n      'ENTERTAINMENT': 'movie',\n      'SERVICES': 'build',\n      'HEALTH': 'local_hospital',\n      'EDUCATION': 'school',\n      'TRANSPORT': 'directions_car',\n      'OTHER': 'more_horiz'\n    };\n    return iconMap[category] || 'category';\n  }\n\n  getCategoryLabel(category: string): string {\n    const labelMap: { [key: string]: string } = {\n      'FOOD': 'Restauration',\n      'SHOPPING': 'Shopping',\n      'ENTERTAINMENT': 'Divertissement',\n      'SERVICES': 'Services',\n      'HEALTH': 'Santé',\n      'EDUCATION': 'Éducation',\n      'TRANSPORT': 'Transport',\n      'OTHER': 'Autre'\n    };\n    return labelMap[category] || category;\n  }\n\n  formatDate(date: Date): string {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      day: '2-digit',\n      month: 'long',\n      year: 'numeric'\n    });\n  }\n\n  isExpiringSoon(reward: Reward): boolean {\n    if (!reward.validUntil) return false;\n    \n    const now = new Date();\n    const validUntil = new Date(reward.validUntil);\n    const daysUntilExpiry = Math.ceil((validUntil.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));\n    \n    return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n  }\n\n  getAvailabilityText(reward: Reward): string {\n    if (!reward.availableQuantity) {\n      return 'Disponible';\n    }\n    \n    if (reward.availableQuantity <= 0) {\n      return 'Épuisé';\n    }\n    \n    if (reward.availableQuantity <= 5) {\n      return `Plus que ${reward.availableQuantity} disponible(s)`;\n    }\n    \n    return `${reward.availableQuantity} disponible(s)`;\n  }\n\n  getAvailabilityColor(reward: Reward): string {\n    if (!reward.availableQuantity || reward.availableQuantity > 5) {\n      return 'primary';\n    }\n    \n    if (reward.availableQuantity <= 0) {\n      return 'warn';\n    }\n    \n    return 'accent';\n  }\n\n  shareReward(reward: Reward): void {\n    if (navigator.share) {\n      navigator.share({\n        title: `Récompense Modjo: ${reward.title}`,\n        text: `Découvrez cette récompense sur Modjo: ${reward.description}`,\n        url: window.location.href\n      }).catch(console.error);\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href).then(() => {\n        this.snackBar.open('Lien copié dans le presse-papiers', 'Fermer', { duration: 2000 });\n      });\n    }\n  }\n\n  contactPartner(partner: Partner): void {\n    if (partner.phone) {\n      window.open(`tel:${partner.phone}`, '_blank');\n    } else if (partner.email) {\n      window.open(`mailto:${partner.email}`, '_blank');\n    }\n  }\n\n  getDirections(partner: Partner): void {\n    if (partner.location) {\n      const url = `https://www.google.com/maps/dir/?api=1&destination=${partner.location.latitude},${partner.location.longitude}`;\n      window.open(url, '_blank');\n    } else if (partner.address) {\n      const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(partner.address)}`;\n      window.open(url, '_blank');\n    }\n  }\n}\n", "<div class=\"reward-detail-container\" *ngIf=\"reward$ | async as reward\">\n  <!-- Header with back button -->\n  <div class=\"detail-header\">\n    <button mat-icon-button (click)=\"goBack()\" class=\"back-button\">\n      <mat-icon>arrow_back</mat-icon>\n    </button>\n    <h1>Détails de la récompense</h1>\n    <button mat-icon-button (click)=\"shareReward(reward)\" class=\"share-button\">\n      <mat-icon>share</mat-icon>\n    </button>\n  </div>\n\n  <!-- Reward image and basic info -->\n  <div class=\"reward-hero slide-up\">\n    <div class=\"hero-image\">\n      <img [src]=\"reward.imageUrl || 'assets/images/reward-placeholder.jpg'\" \n           [alt]=\"reward.title\"\n           onerror=\"this.src='assets/images/reward-placeholder.jpg'\">\n      \n      <!-- Badges overlay -->\n      <div class=\"hero-badges\">\n        <mat-chip class=\"category-chip\" [color]=\"'primary'\" selected>\n          <mat-icon>{{ getCategoryIcon(reward.category) }}</mat-icon>\n          {{ getCategoryLabel(reward.category) }}\n        </mat-chip>\n        \n        <mat-chip *ngIf=\"isExpiringSoon(reward)\" \n                 class=\"expiry-chip\" \n                 color=\"warn\" \n                 selected>\n          <mat-icon>schedule</mat-icon>\n          Expire bientôt\n        </mat-chip>\n      </div>\n    </div>\n\n    <div class=\"hero-content\">\n      <div class=\"reward-title-section\">\n        <h2>{{ reward.title }}</h2>\n        <div class=\"points-badge\">\n          <mat-icon>stars</mat-icon>\n          <span>{{ reward.pointsRequired }} points</span>\n        </div>\n      </div>\n      \n      <p class=\"reward-description\">{{ reward.description }}</p>\n      \n      <div class=\"reward-meta\">\n        <div class=\"meta-item\">\n          <mat-icon>store</mat-icon>\n          <span>{{ reward.partnerName }}</span>\n        </div>\n        \n        <div class=\"meta-item\">\n          <mat-icon>location_on</mat-icon>\n          <span>{{ reward.city }}</span>\n        </div>\n        \n        <div class=\"meta-item\">\n          <mat-chip [color]=\"getAvailabilityColor(reward)\" selected>\n            {{ getAvailabilityText(reward) }}\n          </mat-chip>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Detailed information -->\n  <div class=\"detail-sections fade-in\">\n    <!-- Terms and conditions -->\n    <mat-card class=\"detail-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>description</mat-icon>\n          Conditions d'utilisation\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <p>{{ reward.terms || 'Aucune condition particulière.' }}</p>\n        \n        <div class=\"validity-info\" *ngIf=\"reward.validUntil\">\n          <mat-icon>event</mat-icon>\n          <span>Valide jusqu'au {{ formatDate(reward.validUntil) }}</span>\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- Partner information -->\n    <mat-card class=\"detail-card\" *ngIf=\"partner$ | async as partner\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>business</mat-icon>\n          À propos du partenaire\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"partner-info\">\n          <h4>{{ partner.name }}</h4>\n          <p>{{ partner.description }}</p>\n          \n          <div class=\"partner-details\">\n            <div class=\"detail-row\" *ngIf=\"partner.address\">\n              <mat-icon>location_on</mat-icon>\n              <span>{{ partner.address }}</span>\n              <button mat-icon-button (click)=\"getDirections(partner)\" matTooltip=\"Obtenir l'itinéraire\">\n                <mat-icon>directions</mat-icon>\n              </button>\n            </div>\n            \n            <div class=\"detail-row\" *ngIf=\"partner.phone\">\n              <mat-icon>phone</mat-icon>\n              <span>{{ partner.phone }}</span>\n              <button mat-icon-button (click)=\"contactPartner(partner)\" matTooltip=\"Appeler\">\n                <mat-icon>call</mat-icon>\n              </button>\n            </div>\n            \n            <div class=\"detail-row\" *ngIf=\"partner.email\">\n              <mat-icon>email</mat-icon>\n              <span>{{ partner.email }}</span>\n              <button mat-icon-button (click)=\"contactPartner(partner)\" matTooltip=\"Envoyer un email\">\n                <mat-icon>mail</mat-icon>\n              </button>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n\n    <!-- User points status -->\n    <mat-card class=\"detail-card points-status-card\" *ngIf=\"currentUser\">\n      <mat-card-content>\n        <div class=\"points-status\">\n          <div class=\"current-points\">\n            <mat-icon>account_balance_wallet</mat-icon>\n            <div>\n              <h4>Vos points actuels</h4>\n              <p class=\"points-value\">{{ currentUser.points }} points</p>\n            </div>\n          </div>\n          \n          <div class=\"points-after\" *ngIf=\"canAfford(reward)\">\n            <mat-icon>trending_down</mat-icon>\n            <div>\n              <h4>Après échange</h4>\n              <p class=\"points-value\">{{ currentUser.points - reward.pointsRequired }} points</p>\n            </div>\n          </div>\n          \n          <div class=\"points-needed\" *ngIf=\"!canAfford(reward)\">\n            <mat-icon>add_circle</mat-icon>\n            <div>\n              <h4>Points nécessaires</h4>\n              <p class=\"points-value needed\">{{ reward.pointsRequired - currentUser.points }} points</p>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Action buttons -->\n  <div class=\"action-section\">\n    <div class=\"action-buttons\">\n      <button mat-raised-button \n              [color]=\"canAfford(reward) ? 'primary' : 'basic'\"\n              [disabled]=\"!canAfford(reward) || !reward.availableQuantity || isExchanging\"\n              (click)=\"exchangeReward(reward)\"\n              class=\"exchange-button\">\n        <mat-spinner diameter=\"24\" *ngIf=\"isExchanging\"></mat-spinner>\n        <mat-icon *ngIf=\"!isExchanging\">{{ canAfford(reward) ? 'redeem' : 'lock' }}</mat-icon>\n        <span *ngIf=\"!isExchanging\">\n          {{ canAfford(reward) ? 'Échanger maintenant' : 'Points insuffisants' }}\n        </span>\n      </button>\n      \n      <button mat-stroked-button \n              routerLink=\"/qr-scanner\" \n              class=\"scan-button\"\n              *ngIf=\"!canAfford(reward)\">\n        <mat-icon>qr_code_scanner</mat-icon>\n        Gagner des points\n      </button>\n    </div>\n    \n    <p class=\"exchange-note\" *ngIf=\"canAfford(reward)\">\n      <mat-icon>info</mat-icon>\n      Vous recevrez un code d'échange à présenter au partenaire.\n    </p>\n  </div>\n</div>\n\n<!-- Loading state -->\n<div class=\"loading-container\" *ngIf=\"!(reward$ | async)\">\n  <mat-card class=\"loading-card\">\n    <mat-card-content>\n      <div class=\"loading-content\">\n        <mat-spinner diameter=\"48\"></mat-spinner>\n        <h3>Chargement des détails...</h3>\n      </div>\n    </mat-card-content>\n  </mat-card>\n</div>\n", "import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { RewardsService } from '../../../../core/services/rewards.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { Reward, RewardCategory, User } from '../../../../core/models';\nimport { Observable, combineLatest } from 'rxjs';\nimport { map, startWith } from 'rxjs/operators';\nimport { FormControl } from '@angular/forms';\n\n@Component({\n  selector: 'app-rewards-list',\n  templateUrl: './rewards-list.component.html',\n  styleUrls: ['./rewards-list.component.css']\n})\nexport class RewardsListComponent implements OnInit {\n  rewards$: Observable<Reward[]>;\n  filteredRewards$!: Observable<Reward[]>;\n  currentUser: User | null = null;\n  \n  // Filters\n  categoryFilter = new FormControl('all');\n  cityFilter = new FormControl('all');\n  searchFilter = new FormControl('');\n  \n  categories = [\n    { value: 'all', label: 'Toutes les catégories', icon: 'category' },\n    { value: RewardCategory.FOOD, label: 'Restauration', icon: 'restaurant' },\n    { value: RewardCategory.SHOPPING, label: 'Shopping', icon: 'shopping_bag' },\n    { value: RewardCategory.ENTERTAINMENT, label: 'Divertissement', icon: 'movie' },\n    { value: RewardCategory.SERVICES, label: 'Services', icon: 'build' },\n    { value: RewardCategory.HEALTH, label: 'Santé', icon: 'local_hospital' },\n    { value: RewardCategory.EDUCATION, label: 'Éducation', icon: 'school' },\n    { value: RewardCategory.TRANSPORT, label: 'Transport', icon: 'directions_car' },\n    { value: RewardCategory.OTHER, label: 'Autre', icon: 'more_horiz' }\n  ];\n\n  cities = [\n    { value: 'all', label: 'Toutes les villes' },\n    { value: 'Monastir', label: 'Monastir' },\n    { value: 'Sousse', label: 'Sousse' }\n  ];\n\n  constructor(\n    private rewardsService: RewardsService,\n    private authService: AuthService,\n    private router: Router,\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar\n  ) {\n    this.rewards$ = this.rewardsService.getRewards();\n  }\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n      if (user) {\n        this.cityFilter.setValue(user.city);\n      }\n    });\n\n    this.setupFilters();\n  }\n\n  private setupFilters(): void {\n    this.filteredRewards$ = combineLatest([\n      this.rewards$,\n      this.categoryFilter.valueChanges.pipe(startWith('all')),\n      this.cityFilter.valueChanges.pipe(startWith('all')),\n      this.searchFilter.valueChanges.pipe(startWith(''))\n    ]).pipe(\n      map(([rewards, category, city, search]) => {\n        return rewards.filter(reward => {\n          // Category filter\n          if (category !== 'all' && reward.category !== category) {\n            return false;\n          }\n          \n          // City filter\n          if (city !== 'all' && reward.city !== city && reward.city !== 'Both') {\n            return false;\n          }\n          \n          // Search filter\n          if (search && !reward.title.toLowerCase().includes(search.toLowerCase()) &&\n              !reward.description.toLowerCase().includes(search.toLowerCase()) &&\n              !reward.partnerName.toLowerCase().includes(search.toLowerCase())) {\n            return false;\n          }\n          \n          return reward.isActive;\n        });\n      })\n    );\n  }\n\n  getCategoryIcon(category: RewardCategory): string {\n    const categoryData = this.categories.find(c => c.value === category);\n    return categoryData?.icon || 'category';\n  }\n\n  getCategoryLabel(category: RewardCategory): string {\n    const categoryData = this.categories.find(c => c.value === category);\n    return categoryData?.label || category;\n  }\n\n  canAfford(reward: Reward): boolean {\n    return this.currentUser ? this.currentUser.points >= reward.pointsRequired : false;\n  }\n\n  async exchangeReward(reward: Reward): Promise<void> {\n    if (!this.currentUser) {\n      this.snackBar.open('Vous devez être connecté pour échanger une récompense', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    if (!this.canAfford(reward)) {\n      this.snackBar.open('Points insuffisants pour cette récompense', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    try {\n      const exchange = await this.rewardsService.exchangeReward(reward.id, this.currentUser.uid);\n      this.snackBar.open(\n        `Récompense échangée avec succès! Code: ${exchange.exchangeCode}`, \n        'Fermer', \n        { duration: 5000 }\n      );\n      \n      // Note: In a real app, you would refresh user data here\n      \n    } catch (error: any) {\n      console.error('Exchange error:', error);\n      this.snackBar.open(error.message || 'Erreur lors de l\\'échange', 'Fermer', { duration: 3000 });\n    }\n  }\n\n  viewRewardDetails(reward: Reward): void {\n    this.router.navigate(['/rewards', reward.id]);\n  }\n\n  getAvailabilityText(reward: Reward): string {\n    if (!reward.availableQuantity) {\n      return 'Disponible';\n    }\n    \n    if (reward.availableQuantity <= 0) {\n      return 'Épuisé';\n    }\n    \n    if (reward.availableQuantity <= 5) {\n      return `Plus que ${reward.availableQuantity} disponible(s)`;\n    }\n    \n    return 'Disponible';\n  }\n\n  getAvailabilityColor(reward: Reward): string {\n    if (!reward.availableQuantity || reward.availableQuantity > 5) {\n      return 'primary';\n    }\n    \n    if (reward.availableQuantity <= 0) {\n      return 'warn';\n    }\n    \n    return 'accent';\n  }\n\n  isExpiringSoon(reward: Reward): boolean {\n    if (!reward.validUntil) return false;\n    \n    const now = new Date();\n    const validUntil = new Date(reward.validUntil);\n    const daysUntilExpiry = Math.ceil((validUntil.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));\n    \n    return daysUntilExpiry <= 7 && daysUntilExpiry > 0;\n  }\n\n  formatExpiryDate(date: Date): string {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric'\n    });\n  }\n\n  clearFilters(): void {\n    this.categoryFilter.setValue('all');\n    this.cityFilter.setValue(this.currentUser?.city || 'all');\n    this.searchFilter.setValue('');\n  }\n\n  addToWishlist(reward: Reward): void {\n    // In a real app, this would save to user's wishlist\n    console.log('Added to wishlist:', reward.title);\n    this.snackBar.open('Ajouté aux favoris!', 'Fermer', { duration: 2000 });\n  }\n\n  isInWishlist(reward: Reward): boolean {\n    // In a real app, this would check user's wishlist\n    return false; // Mock implementation\n  }\n\n  shareReward(reward: Reward): void {\n    if (navigator.share) {\n      navigator.share({\n        title: `Récompense Modjo: ${reward.title}`,\n        text: `Découvrez cette récompense sur Modjo: ${reward.description}`,\n        url: `${window.location.origin}/rewards/${reward.id}`\n      }).catch(console.error);\n    } else {\n      // Fallback: copy to clipboard\n      const url = `${window.location.origin}/rewards/${reward.id}`;\n      navigator.clipboard.writeText(url).then(() => {\n        this.snackBar.open('Lien copié dans le presse-papiers', 'Fermer', { duration: 2000 });\n      });\n    }\n  }\n}\n", "<div class=\"rewards-container\">\n  <!-- Header -->\n  <div class=\"rewards-header slide-up\">\n    <h1>🎁 Récompenses</h1>\n    <p>Échangez vos points contre des récompenses exclusives</p>\n    \n    <div class=\"user-points\" *ngIf=\"currentUser\">\n      <mat-icon>stars</mat-icon>\n      <span>{{ currentUser.points }} points disponibles</span>\n    </div>\n  </div>\n\n  <!-- Filters -->\n  <div class=\"filters-section fade-in\">\n    <mat-card class=\"filters-card\">\n      <mat-card-content>\n        <div class=\"filters-grid\">\n          <!-- Search -->\n          <mat-form-field appearance=\"outline\" class=\"search-field\">\n            <mat-label>Rechercher</mat-label>\n            <input matInput [formControl]=\"searchFilter\" placeholder=\"Nom, description, partenaire...\">\n            <mat-icon matSuffix>search</mat-icon>\n          </mat-form-field>\n\n          <!-- Category filter -->\n          <mat-form-field appearance=\"outline\">\n            <mat-label>Catégorie</mat-label>\n            <mat-select [formControl]=\"categoryFilter\">\n              <mat-option *ngFor=\"let category of categories\" [value]=\"category.value\">\n                <mat-icon>{{ category.icon }}</mat-icon>\n                {{ category.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <!-- City filter -->\n          <mat-form-field appearance=\"outline\">\n            <mat-label>Ville</mat-label>\n            <mat-select [formControl]=\"cityFilter\">\n              <mat-option *ngFor=\"let city of cities\" [value]=\"city.value\">\n                {{ city.label }}\n              </mat-option>\n            </mat-select>\n          </mat-form-field>\n\n          <!-- Clear filters -->\n          <button mat-stroked-button (click)=\"clearFilters()\" class=\"clear-filters-btn\">\n            <mat-icon>clear</mat-icon>\n            Effacer\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Rewards grid -->\n  <div class=\"rewards-grid\" *ngIf=\"filteredRewards$ | async as rewards\">\n    <mat-card *ngFor=\"let reward of rewards; let i = index\" \n              class=\"reward-card floating-card\"\n              [style.animation-delay]=\"(i * 0.1) + 's'\"\n              (click)=\"viewRewardDetails(reward)\">\n      \n      <!-- Reward image -->\n      <div class=\"reward-image\">\n        <img [src]=\"reward.imageUrl || 'assets/images/reward-placeholder.jpg'\" \n             [alt]=\"reward.title\"\n             onerror=\"this.src='assets/images/reward-placeholder.jpg'\">\n        \n        <!-- Badges -->\n        <div class=\"reward-badges\">\n          <mat-chip class=\"category-chip\" [color]=\"'primary'\" selected>\n            <mat-icon>{{ getCategoryIcon(reward.category) }}</mat-icon>\n            {{ getCategoryLabel(reward.category) }}\n          </mat-chip>\n          \n          <mat-chip *ngIf=\"isExpiringSoon(reward)\" \n                   class=\"expiry-chip\" \n                   color=\"warn\" \n                   selected>\n            <mat-icon>schedule</mat-icon>\n            Expire bientôt\n          </mat-chip>\n        </div>\n      </div>\n\n      <!-- Reward content -->\n      <mat-card-content>\n        <div class=\"reward-header\">\n          <h3>{{ reward.title }}</h3>\n          <div class=\"points-required\">\n            <mat-icon>stars</mat-icon>\n            <span>{{ reward.pointsRequired }}</span>\n          </div>\n        </div>\n\n        <p class=\"reward-description\">{{ reward.description }}</p>\n        \n        <div class=\"reward-partner\">\n          <mat-icon>store</mat-icon>\n          <span>{{ reward.partnerName }}</span>\n          <mat-chip class=\"city-chip\" [color]=\"'accent'\" selected>\n            {{ reward.city }}\n          </mat-chip>\n        </div>\n\n        <div class=\"reward-availability\">\n          <mat-chip [color]=\"getAvailabilityColor(reward)\" selected>\n            {{ getAvailabilityText(reward) }}\n          </mat-chip>\n          \n          <span *ngIf=\"reward.validUntil\" class=\"expiry-date\">\n            Valide jusqu'au {{ formatExpiryDate(reward.validUntil) }}\n          </span>\n        </div>\n      </mat-card-content>\n\n      <!-- Reward actions -->\n      <mat-card-actions>\n        <div class=\"reward-actions\">\n          <button mat-raised-button\n                  [color]=\"canAfford(reward) ? 'primary' : 'basic'\"\n                  [disabled]=\"!canAfford(reward) || !reward.availableQuantity\"\n                  (click)=\"exchangeReward(reward); $event.stopPropagation()\"\n                  class=\"exchange-btn enhanced-action-btn\">\n            <mat-icon>{{ canAfford(reward) ? 'redeem' : 'lock' }}</mat-icon>\n            {{ canAfford(reward) ? 'Échanger' : 'Points insuffisants' }}\n          </button>\n\n          <button mat-stroked-button\n                  color=\"accent\"\n                  (click)=\"viewRewardDetails(reward); $event.stopPropagation()\"\n                  class=\"details-btn enhanced-action-btn\">\n            <mat-icon>info</mat-icon>\n            Détails\n          </button>\n\n          <button mat-icon-button\n                  color=\"primary\"\n                  (click)=\"addToWishlist(reward); $event.stopPropagation()\"\n                  matTooltip=\"Ajouter aux favoris\"\n                  class=\"wishlist-btn\">\n            <mat-icon>{{ isInWishlist(reward) ? 'favorite' : 'favorite_border' }}</mat-icon>\n          </button>\n        </div>\n      </mat-card-actions>\n    </mat-card>\n  </div>\n\n  <!-- Empty state -->\n  <div class=\"empty-state\" *ngIf=\"(filteredRewards$ | async)?.length === 0\">\n    <mat-card class=\"empty-card\">\n      <mat-card-content>\n        <div class=\"empty-content\">\n          <mat-icon class=\"empty-icon\">card_giftcard</mat-icon>\n          <h3>Aucune récompense trouvée</h3>\n          <p>Essayez de modifier vos filtres ou revenez plus tard pour découvrir de nouvelles récompenses.</p>\n          <button mat-raised-button color=\"primary\" (click)=\"clearFilters()\">\n            <mat-icon>refresh</mat-icon>\n            Réinitialiser les filtres\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Floating action button -->\n  <button mat-fab \n          class=\"floating-fab\" \n          color=\"primary\"\n          routerLink=\"/qr-scanner\"\n          matTooltip=\"Scanner pour gagner des points\">\n    <mat-icon>qr_code_scanner</mat-icon>\n  </button>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatChipsModule } from '@angular/material/chips';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatDialogModule } from '@angular/material/dialog';\nimport { MatSnackBarModule } from '@angular/material/snack-bar';\nimport { MatProgressSpinnerModule } from '@angular/material/progress-spinner';\nimport { MatBadgeModule } from '@angular/material/badge';\nimport { MatTooltipModule } from '@angular/material/tooltip';\nimport { MatMenuModule } from '@angular/material/menu';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { ReactiveFormsModule } from '@angular/forms';\n\nimport { RewardsListComponent } from './components/rewards-list/rewards-list.component';\nimport { RewardDetailComponent } from './components/reward-detail/reward-detail.component';\nimport { ExchangeHistoryComponent } from './components/exchange-history/exchange-history.component';\nimport { rewardsRoutes } from './rewards.routes';\n\n@NgModule({\n  declarations: [\n    RewardsListComponent,\n    RewardDetailComponent,\n    ExchangeHistoryComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule.forChild(rewardsRoutes),\n\n    // Material modules\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatChipsModule,\n    MatTabsModule,\n    MatDialogModule,\n    MatSnackBarModule,\n    MatProgressSpinnerModule,\n    MatBadgeModule,\n    MatTooltipModule,\n    MatMenuModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule\n  ]\n})\nexport class RewardsModule { }\n", "import { Routes } from '@angular/router';\nimport { RewardsListComponent } from './components/rewards-list/rewards-list.component';\nimport { RewardDetailComponent } from './components/reward-detail/reward-detail.component';\n\nexport const rewardsRoutes: Routes = [\n  {\n    path: '',\n    component: RewardsListComponent\n  },\n  {\n    path: ':id',\n    component: RewardDetailComponent\n  }\n];\n", "import * as i0 from '@angular/core';\nimport { Component, ViewEncapsulation, ChangeDetectionStrategy, inject, ApplicationRef, createComponent, EnvironmentInjector, ANIMATION_MODULE_TYPE, booleanAttribute, Directive, Optional, Inject, Input, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport * as i1 from '@angular/cdk/a11y';\nimport { InteractivityChecker, A11yModule } from '@angular/cdk/a11y';\nimport { DOCUMENT } from '@angular/common';\n\nlet nextId = 0;\nconst BADGE_CONTENT_CLASS = 'mat-badge-content';\n/** Keeps track of the apps currently containing badges. */\nconst badgeApps = new Set();\n/**\n * Component used to load the structural styles of the badge.\n * @docs-private\n */\nclass _MatBadgeStyleLoader {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatBadgeStyleLoader, deps: [], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: _MatBadgeStyleLoader, isStandalone: true, selector: \"ng-component\", ngImport: i0, template: '', isInline: true, styles: [\".mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;box-sizing:border-box;pointer-events:none;background-color:var(--mat-badge-background-color);color:var(--mat-badge-text-color);font-family:var(--mat-badge-text-font);font-weight:var(--mat-badge-text-weight);border-radius:var(--mat-badge-container-shape)}.cdk-high-contrast-active .mat-badge-content{outline:solid 1px;border-radius:0}.mat-badge-above .mat-badge-content{bottom:100%}.mat-badge-below .mat-badge-content{top:100%}.mat-badge-before .mat-badge-content{right:100%}[dir=rtl] .mat-badge-before .mat-badge-content{right:auto;left:100%}.mat-badge-after .mat-badge-content{left:100%}[dir=rtl] .mat-badge-after .mat-badge-content{left:auto;right:100%}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color);color:var(--mat-badge-disabled-state-text-color)}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:var(--mat-badge-legacy-small-size-container-size, unset);height:var(--mat-badge-legacy-small-size-container-size, unset);min-width:var(--mat-badge-small-size-container-size, unset);min-height:var(--mat-badge-small-size-container-size, unset);line-height:var(--mat-badge-legacy-small-size-container-size, var(--mat-badge-small-size-container-size));padding:var(--mat-badge-small-size-container-padding);font-size:var(--mat-badge-small-size-text-size);margin:var(--mat-badge-small-size-container-offset)}.mat-badge-small.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-small-size-container-overlap-offset)}.mat-badge-medium .mat-badge-content{width:var(--mat-badge-legacy-container-size, unset);height:var(--mat-badge-legacy-container-size, unset);min-width:var(--mat-badge-container-size, unset);min-height:var(--mat-badge-container-size, unset);line-height:var(--mat-badge-legacy-container-size, var(--mat-badge-container-size));padding:var(--mat-badge-container-padding);font-size:var(--mat-badge-text-size);margin:var(--mat-badge-container-offset)}.mat-badge-medium.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-container-overlap-offset)}.mat-badge-large .mat-badge-content{width:var(--mat-badge-legacy-large-size-container-size, unset);height:var(--mat-badge-legacy-large-size-container-size, unset);min-width:var(--mat-badge-large-size-container-size, unset);min-height:var(--mat-badge-large-size-container-size, unset);line-height:var(--mat-badge-legacy-large-size-container-size, var(--mat-badge-large-size-container-size));padding:var(--mat-badge-large-size-container-padding);font-size:var(--mat-badge-large-size-text-size);margin:var(--mat-badge-large-size-container-offset)}.mat-badge-large.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-large-size-container-overlap-offset)}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: _MatBadgeStyleLoader, decorators: [{\n            type: Component,\n            args: [{ standalone: true, encapsulation: ViewEncapsulation.None, template: '', changeDetection: ChangeDetectionStrategy.OnPush, styles: [\".mat-badge{position:relative}.mat-badge.mat-badge{overflow:visible}.mat-badge-content{position:absolute;text-align:center;display:inline-block;transition:transform 200ms ease-in-out;transform:scale(0.6);overflow:hidden;white-space:nowrap;text-overflow:ellipsis;box-sizing:border-box;pointer-events:none;background-color:var(--mat-badge-background-color);color:var(--mat-badge-text-color);font-family:var(--mat-badge-text-font);font-weight:var(--mat-badge-text-weight);border-radius:var(--mat-badge-container-shape)}.cdk-high-contrast-active .mat-badge-content{outline:solid 1px;border-radius:0}.mat-badge-above .mat-badge-content{bottom:100%}.mat-badge-below .mat-badge-content{top:100%}.mat-badge-before .mat-badge-content{right:100%}[dir=rtl] .mat-badge-before .mat-badge-content{right:auto;left:100%}.mat-badge-after .mat-badge-content{left:100%}[dir=rtl] .mat-badge-after .mat-badge-content{left:auto;right:100%}.mat-badge-disabled .mat-badge-content{background-color:var(--mat-badge-disabled-state-background-color);color:var(--mat-badge-disabled-state-text-color)}.mat-badge-hidden .mat-badge-content{display:none}.ng-animate-disabled .mat-badge-content,.mat-badge-content._mat-animation-noopable{transition:none}.mat-badge-content.mat-badge-active{transform:none}.mat-badge-small .mat-badge-content{width:var(--mat-badge-legacy-small-size-container-size, unset);height:var(--mat-badge-legacy-small-size-container-size, unset);min-width:var(--mat-badge-small-size-container-size, unset);min-height:var(--mat-badge-small-size-container-size, unset);line-height:var(--mat-badge-legacy-small-size-container-size, var(--mat-badge-small-size-container-size));padding:var(--mat-badge-small-size-container-padding);font-size:var(--mat-badge-small-size-text-size);margin:var(--mat-badge-small-size-container-offset)}.mat-badge-small.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-small-size-container-overlap-offset)}.mat-badge-medium .mat-badge-content{width:var(--mat-badge-legacy-container-size, unset);height:var(--mat-badge-legacy-container-size, unset);min-width:var(--mat-badge-container-size, unset);min-height:var(--mat-badge-container-size, unset);line-height:var(--mat-badge-legacy-container-size, var(--mat-badge-container-size));padding:var(--mat-badge-container-padding);font-size:var(--mat-badge-text-size);margin:var(--mat-badge-container-offset)}.mat-badge-medium.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-container-overlap-offset)}.mat-badge-large .mat-badge-content{width:var(--mat-badge-legacy-large-size-container-size, unset);height:var(--mat-badge-legacy-large-size-container-size, unset);min-width:var(--mat-badge-large-size-container-size, unset);min-height:var(--mat-badge-large-size-container-size, unset);line-height:var(--mat-badge-legacy-large-size-container-size, var(--mat-badge-large-size-container-size));padding:var(--mat-badge-large-size-container-padding);font-size:var(--mat-badge-large-size-text-size);margin:var(--mat-badge-large-size-container-offset)}.mat-badge-large.mat-badge-overlap .mat-badge-content{margin:var(--mat-badge-large-size-container-overlap-offset)}\"] }]\n        }] });\n/** Directive to display a text badge. */\nclass MatBadge {\n    /** The color of the badge. Can be `primary`, `accent`, or `warn`. */\n    get color() {\n        return this._color;\n    }\n    set color(value) {\n        this._setColor(value);\n        this._color = value;\n    }\n    /** The content for the badge */\n    get content() {\n        return this._content;\n    }\n    set content(newContent) {\n        this._updateRenderedContent(newContent);\n    }\n    /** Message used to describe the decorated element via aria-describedby */\n    get description() {\n        return this._description;\n    }\n    set description(newDescription) {\n        this._updateDescription(newDescription);\n    }\n    constructor(_ngZone, _elementRef, _ariaDescriber, _renderer, _animationMode) {\n        this._ngZone = _ngZone;\n        this._elementRef = _elementRef;\n        this._ariaDescriber = _ariaDescriber;\n        this._renderer = _renderer;\n        this._animationMode = _animationMode;\n        this._color = 'primary';\n        /** Whether the badge should overlap its contents or not */\n        this.overlap = true;\n        /**\n         * Position the badge should reside.\n         * Accepts any combination of 'above'|'below' and 'before'|'after'\n         */\n        this.position = 'above after';\n        /** Size of the badge. Can be 'small', 'medium', or 'large'. */\n        this.size = 'medium';\n        /** Unique id for the badge */\n        this._id = nextId++;\n        /** Whether the OnInit lifecycle hook has run yet */\n        this._isInitialized = false;\n        /** InteractivityChecker to determine if the badge host is focusable. */\n        this._interactivityChecker = inject(InteractivityChecker);\n        this._document = inject(DOCUMENT);\n        const appRef = inject(ApplicationRef);\n        if (!badgeApps.has(appRef)) {\n            badgeApps.add(appRef);\n            const componentRef = createComponent(_MatBadgeStyleLoader, {\n                environmentInjector: inject(EnvironmentInjector),\n            });\n            appRef.onDestroy(() => {\n                badgeApps.delete(appRef);\n                if (badgeApps.size === 0) {\n                    componentRef.destroy();\n                }\n            });\n        }\n        if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            const nativeElement = _elementRef.nativeElement;\n            if (nativeElement.nodeType !== nativeElement.ELEMENT_NODE) {\n                throw Error('matBadge must be attached to an element node.');\n            }\n            const matIconTagName = 'mat-icon';\n            // Heads-up for developers to avoid putting matBadge on <mat-icon>\n            // as it is aria-hidden by default docs mention this at:\n            // https://material.angular.io/components/badge/overview#accessibility\n            if (nativeElement.tagName.toLowerCase() === matIconTagName &&\n                nativeElement.getAttribute('aria-hidden') === 'true') {\n                console.warn(`Detected a matBadge on an \"aria-hidden\" \"<mat-icon>\". ` +\n                    `Consider setting aria-hidden=\"false\" in order to surface the information assistive technology.` +\n                    `\\n${nativeElement.outerHTML}`);\n            }\n        }\n    }\n    /** Whether the badge is above the host or not */\n    isAbove() {\n        return this.position.indexOf('below') === -1;\n    }\n    /** Whether the badge is after the host or not */\n    isAfter() {\n        return this.position.indexOf('before') === -1;\n    }\n    /**\n     * Gets the element into which the badge's content is being rendered. Undefined if the element\n     * hasn't been created (e.g. if the badge doesn't have content).\n     */\n    getBadgeElement() {\n        return this._badgeElement;\n    }\n    ngOnInit() {\n        // We may have server-side rendered badge that we need to clear.\n        // We need to do this in ngOnInit because the full content of the component\n        // on which the badge is attached won't necessarily be in the DOM until this point.\n        this._clearExistingBadges();\n        if (this.content && !this._badgeElement) {\n            this._badgeElement = this._createBadgeElement();\n            this._updateRenderedContent(this.content);\n        }\n        this._isInitialized = true;\n    }\n    ngOnDestroy() {\n        // ViewEngine only: when creating a badge through the Renderer, Angular remembers its index.\n        // We have to destroy it ourselves, otherwise it'll be retained in memory.\n        if (this._renderer.destroyNode) {\n            this._renderer.destroyNode(this._badgeElement);\n            this._inlineBadgeDescription?.remove();\n        }\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n    }\n    /** Gets whether the badge's host element is interactive. */\n    _isHostInteractive() {\n        // Ignore visibility since it requires an expensive style caluclation.\n        return this._interactivityChecker.isFocusable(this._elementRef.nativeElement, {\n            ignoreVisibility: true,\n        });\n    }\n    /** Creates the badge element */\n    _createBadgeElement() {\n        const badgeElement = this._renderer.createElement('span');\n        const activeClass = 'mat-badge-active';\n        badgeElement.setAttribute('id', `mat-badge-content-${this._id}`);\n        // The badge is aria-hidden because we don't want it to appear in the page's navigation\n        // flow. Instead, we use the badge to describe the decorated element with aria-describedby.\n        badgeElement.setAttribute('aria-hidden', 'true');\n        badgeElement.classList.add(BADGE_CONTENT_CLASS);\n        if (this._animationMode === 'NoopAnimations') {\n            badgeElement.classList.add('_mat-animation-noopable');\n        }\n        this._elementRef.nativeElement.appendChild(badgeElement);\n        // animate in after insertion\n        if (typeof requestAnimationFrame === 'function' && this._animationMode !== 'NoopAnimations') {\n            this._ngZone.runOutsideAngular(() => {\n                requestAnimationFrame(() => {\n                    badgeElement.classList.add(activeClass);\n                });\n            });\n        }\n        else {\n            badgeElement.classList.add(activeClass);\n        }\n        return badgeElement;\n    }\n    /** Update the text content of the badge element in the DOM, creating the element if necessary. */\n    _updateRenderedContent(newContent) {\n        const newContentNormalized = `${newContent ?? ''}`.trim();\n        // Don't create the badge element if the directive isn't initialized because we want to\n        // append the badge element to the *end* of the host element's content for backwards\n        // compatibility.\n        if (this._isInitialized && newContentNormalized && !this._badgeElement) {\n            this._badgeElement = this._createBadgeElement();\n        }\n        if (this._badgeElement) {\n            this._badgeElement.textContent = newContentNormalized;\n        }\n        this._content = newContentNormalized;\n    }\n    /** Updates the host element's aria description via AriaDescriber. */\n    _updateDescription(newDescription) {\n        // Always start by removing the aria-describedby; we will add a new one if necessary.\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this.description);\n        // NOTE: We only check whether the host is interactive here, which happens during\n        // when then badge content changes. It is possible that the host changes\n        // interactivity status separate from one of these. However, watching the interactivity\n        // status of the host would require a `MutationObserver`, which is likely more code + overhead\n        // than it's worth; from usages inside Google, we see that the vats majority of badges either\n        // never change interactivity, or also set `matBadgeHidden` based on the same condition.\n        if (!newDescription || this._isHostInteractive()) {\n            this._removeInlineDescription();\n        }\n        this._description = newDescription;\n        // We don't add `aria-describedby` for non-interactive hosts elements because we\n        // instead insert the description inline.\n        if (this._isHostInteractive()) {\n            this._ariaDescriber.describe(this._elementRef.nativeElement, newDescription);\n        }\n        else {\n            this._updateInlineDescription();\n        }\n    }\n    _updateInlineDescription() {\n        // Create the inline description element if it doesn't exist\n        if (!this._inlineBadgeDescription) {\n            this._inlineBadgeDescription = this._document.createElement('span');\n            this._inlineBadgeDescription.classList.add('cdk-visually-hidden');\n        }\n        this._inlineBadgeDescription.textContent = this.description;\n        this._badgeElement?.appendChild(this._inlineBadgeDescription);\n    }\n    _removeInlineDescription() {\n        this._inlineBadgeDescription?.remove();\n        this._inlineBadgeDescription = undefined;\n    }\n    /** Adds css theme class given the color to the component host */\n    _setColor(colorPalette) {\n        const classList = this._elementRef.nativeElement.classList;\n        classList.remove(`mat-badge-${this._color}`);\n        if (colorPalette) {\n            classList.add(`mat-badge-${colorPalette}`);\n        }\n    }\n    /** Clears any existing badges that might be left over from server-side rendering. */\n    _clearExistingBadges() {\n        // Only check direct children of this host element in order to avoid deleting\n        // any badges that might exist in descendant elements.\n        const badges = this._elementRef.nativeElement.querySelectorAll(`:scope > .${BADGE_CONTENT_CLASS}`);\n        for (const badgeElement of Array.from(badges)) {\n            if (badgeElement !== this._badgeElement) {\n                badgeElement.remove();\n            }\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatBadge, deps: [{ token: i0.NgZone }, { token: i0.ElementRef }, { token: i1.AriaDescriber }, { token: i0.Renderer2 }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatBadge, isStandalone: true, selector: \"[matBadge]\", inputs: { color: [\"matBadgeColor\", \"color\"], overlap: [\"matBadgeOverlap\", \"overlap\", booleanAttribute], disabled: [\"matBadgeDisabled\", \"disabled\", booleanAttribute], position: [\"matBadgePosition\", \"position\"], content: [\"matBadge\", \"content\"], description: [\"matBadgeDescription\", \"description\"], size: [\"matBadgeSize\", \"size\"], hidden: [\"matBadgeHidden\", \"hidden\", booleanAttribute] }, host: { properties: { \"class.mat-badge-overlap\": \"overlap\", \"class.mat-badge-above\": \"isAbove()\", \"class.mat-badge-below\": \"!isAbove()\", \"class.mat-badge-before\": \"!isAfter()\", \"class.mat-badge-after\": \"isAfter()\", \"class.mat-badge-small\": \"size === \\\"small\\\"\", \"class.mat-badge-medium\": \"size === \\\"medium\\\"\", \"class.mat-badge-large\": \"size === \\\"large\\\"\", \"class.mat-badge-hidden\": \"hidden || !content\", \"class.mat-badge-disabled\": \"disabled\" }, classAttribute: \"mat-badge\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatBadge, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matBadge]',\n                    host: {\n                        'class': 'mat-badge',\n                        '[class.mat-badge-overlap]': 'overlap',\n                        '[class.mat-badge-above]': 'isAbove()',\n                        '[class.mat-badge-below]': '!isAbove()',\n                        '[class.mat-badge-before]': '!isAfter()',\n                        '[class.mat-badge-after]': 'isAfter()',\n                        '[class.mat-badge-small]': 'size === \"small\"',\n                        '[class.mat-badge-medium]': 'size === \"medium\"',\n                        '[class.mat-badge-large]': 'size === \"large\"',\n                        '[class.mat-badge-hidden]': 'hidden || !content',\n                        '[class.mat-badge-disabled]': 'disabled',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i0.NgZone }, { type: i0.ElementRef }, { type: i1.AriaDescriber }, { type: i0.Renderer2 }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { color: [{\n                type: Input,\n                args: ['matBadgeColor']\n            }], overlap: [{\n                type: Input,\n                args: [{ alias: 'matBadgeOverlap', transform: booleanAttribute }]\n            }], disabled: [{\n                type: Input,\n                args: [{ alias: 'matBadgeDisabled', transform: booleanAttribute }]\n            }], position: [{\n                type: Input,\n                args: ['matBadgePosition']\n            }], content: [{\n                type: Input,\n                args: ['matBadge']\n            }], description: [{\n                type: Input,\n                args: ['matBadgeDescription']\n            }], size: [{\n                type: Input,\n                args: ['matBadgeSize']\n            }], hidden: [{\n                type: Input,\n                args: [{ alias: 'matBadgeHidden', transform: booleanAttribute }]\n            }] } });\n\nclass MatBadgeModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatBadgeModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatBadgeModule, imports: [A11yModule, MatCommonModule, MatBadge, _MatBadgeStyleLoader], exports: [MatBadge, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatBadgeModule, imports: [A11yModule, MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatBadgeModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    // Note: we _shouldn't_ have to import `_MatBadgeStyleLoader`,\n                    // but it seems to be necessary for tests.\n                    imports: [A11yModule, MatCommonModule, MatBadge, _MatBadgeStyleLoader],\n                    exports: [MatBadge, MatCommonModule],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatBadge, MatBadgeModule };\n", "import { takeUntil, take } from 'rxjs/operators';\nimport { coerceBooleanProperty, coerceNumberProperty } from '@angular/cdk/coercion';\nimport { ESCAPE, hasModifierKey } from '@angular/cdk/keycodes';\nimport * as i0 from '@angular/core';\nimport { InjectionToken, inject, ElementRef, Directive, Inject, Optional, Input, ANIMATION_MODULE_TYPE, Component, ViewEncapsulation, ChangeDetectionStrategy, ViewChild, NgModule } from '@angular/core';\nimport { DOCUMENT, NgClass, CommonModule } from '@angular/common';\nimport * as i2 from '@angular/cdk/platform';\nimport { normalizePassiveListenerOptions } from '@angular/cdk/platform';\nimport * as i3 from '@angular/cdk/a11y';\nimport { A11yModule } from '@angular/cdk/a11y';\nimport * as i4 from '@angular/cdk/bidi';\nimport * as i1 from '@angular/cdk/overlay';\nimport { Overlay, OverlayModule } from '@angular/cdk/overlay';\nimport { ComponentPortal } from '@angular/cdk/portal';\nimport { Subject } from 'rxjs';\nimport { trigger, state, style, transition, animate } from '@angular/animations';\nimport { CdkScrollableModule } from '@angular/cdk/scrolling';\nimport { MatCommonModule } from '@angular/material/core';\n\n/** Time in ms to throttle repositioning after scroll events. */\nconst SCROLL_THROTTLE_MS = 20;\n/**\n * Creates an error to be thrown if the user supplied an invalid tooltip position.\n * @docs-private\n */\nfunction getMatTooltipInvalidPositionError(position) {\n    return Error(`Tooltip position \"${position}\" is invalid.`);\n}\n/** Injection token that determines the scroll handling while a tooltip is visible. */\nconst MAT_TOOLTIP_SCROLL_STRATEGY = new InjectionToken('mat-tooltip-scroll-strategy', {\n    providedIn: 'root',\n    factory: () => {\n        const overlay = inject(Overlay);\n        return () => overlay.scrollStrategies.reposition({ scrollThrottle: SCROLL_THROTTLE_MS });\n    },\n});\n/** @docs-private */\nfunction MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY(overlay) {\n    return () => overlay.scrollStrategies.reposition({ scrollThrottle: SCROLL_THROTTLE_MS });\n}\n/** @docs-private */\nconst MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER = {\n    provide: MAT_TOOLTIP_SCROLL_STRATEGY,\n    deps: [Overlay],\n    useFactory: MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY,\n};\n/** @docs-private */\nfunction MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY() {\n    return {\n        showDelay: 0,\n        hideDelay: 0,\n        touchendHideDelay: 1500,\n    };\n}\n/** Injection token to be used to override the default options for `matTooltip`. */\nconst MAT_TOOLTIP_DEFAULT_OPTIONS = new InjectionToken('mat-tooltip-default-options', {\n    providedIn: 'root',\n    factory: MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY,\n});\n/**\n * CSS class that will be attached to the overlay panel.\n * @deprecated\n * @breaking-change 13.0.0 remove this variable\n */\nconst TOOLTIP_PANEL_CLASS = 'mat-mdc-tooltip-panel';\nconst PANEL_CLASS = 'tooltip-panel';\n/** Options used to bind passive event listeners. */\nconst passiveListenerOptions = normalizePassiveListenerOptions({ passive: true });\n// These constants were taken from MDC's `numbers` object. We can't import them from MDC,\n// because they have some top-level references to `window` which break during SSR.\nconst MIN_VIEWPORT_TOOLTIP_THRESHOLD = 8;\nconst UNBOUNDED_ANCHOR_GAP = 8;\nconst MIN_HEIGHT = 24;\nconst MAX_WIDTH = 200;\n/**\n * Directive that attaches a material design tooltip to the host element. Animates the showing and\n * hiding of a tooltip provided position (defaults to below the element).\n *\n * https://material.io/design/components/tooltips.html\n */\nclass MatTooltip {\n    /** Allows the user to define the position of the tooltip relative to the parent element */\n    get position() {\n        return this._position;\n    }\n    set position(value) {\n        if (value !== this._position) {\n            this._position = value;\n            if (this._overlayRef) {\n                this._updatePosition(this._overlayRef);\n                this._tooltipInstance?.show(0);\n                this._overlayRef.updatePosition();\n            }\n        }\n    }\n    /**\n     * Whether tooltip should be relative to the click or touch origin\n     * instead of outside the element bounding box.\n     */\n    get positionAtOrigin() {\n        return this._positionAtOrigin;\n    }\n    set positionAtOrigin(value) {\n        this._positionAtOrigin = coerceBooleanProperty(value);\n        this._detach();\n        this._overlayRef = null;\n    }\n    /** Disables the display of the tooltip. */\n    get disabled() {\n        return this._disabled;\n    }\n    set disabled(value) {\n        this._disabled = coerceBooleanProperty(value);\n        // If tooltip is disabled, hide immediately.\n        if (this._disabled) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n        }\n    }\n    /** The default delay in ms before showing the tooltip after show is called */\n    get showDelay() {\n        return this._showDelay;\n    }\n    set showDelay(value) {\n        this._showDelay = coerceNumberProperty(value);\n    }\n    /** The default delay in ms before hiding the tooltip after hide is called */\n    get hideDelay() {\n        return this._hideDelay;\n    }\n    set hideDelay(value) {\n        this._hideDelay = coerceNumberProperty(value);\n        if (this._tooltipInstance) {\n            this._tooltipInstance._mouseLeaveHideDelay = this._hideDelay;\n        }\n    }\n    /** The message to be displayed in the tooltip */\n    get message() {\n        return this._message;\n    }\n    set message(value) {\n        this._ariaDescriber.removeDescription(this._elementRef.nativeElement, this._message, 'tooltip');\n        // If the message is not a string (e.g. number), convert it to a string and trim it.\n        // Must convert with `String(value)`, not `${value}`, otherwise Closure Compiler optimises\n        // away the string-conversion: https://github.com/angular/components/issues/20684\n        this._message = value != null ? String(value).trim() : '';\n        if (!this._message && this._isTooltipVisible()) {\n            this.hide(0);\n        }\n        else {\n            this._setupPointerEnterEventsIfNeeded();\n            this._updateTooltipMessage();\n            this._ngZone.runOutsideAngular(() => {\n                // The `AriaDescriber` has some functionality that avoids adding a description if it's the\n                // same as the `aria-label` of an element, however we can't know whether the tooltip trigger\n                // has a data-bound `aria-label` or when it'll be set for the first time. We can avoid the\n                // issue by deferring the description by a tick so Angular has time to set the `aria-label`.\n                Promise.resolve().then(() => {\n                    this._ariaDescriber.describe(this._elementRef.nativeElement, this.message, 'tooltip');\n                });\n            });\n        }\n    }\n    /** Classes to be passed to the tooltip. Supports the same syntax as `ngClass`. */\n    get tooltipClass() {\n        return this._tooltipClass;\n    }\n    set tooltipClass(value) {\n        this._tooltipClass = value;\n        if (this._tooltipInstance) {\n            this._setTooltipClass(this._tooltipClass);\n        }\n    }\n    constructor(_overlay, _elementRef, _scrollDispatcher, _viewContainerRef, _ngZone, _platform, _ariaDescriber, _focusMonitor, scrollStrategy, _dir, _defaultOptions, _document) {\n        this._overlay = _overlay;\n        this._elementRef = _elementRef;\n        this._scrollDispatcher = _scrollDispatcher;\n        this._viewContainerRef = _viewContainerRef;\n        this._ngZone = _ngZone;\n        this._platform = _platform;\n        this._ariaDescriber = _ariaDescriber;\n        this._focusMonitor = _focusMonitor;\n        this._dir = _dir;\n        this._defaultOptions = _defaultOptions;\n        this._position = 'below';\n        this._positionAtOrigin = false;\n        this._disabled = false;\n        this._viewInitialized = false;\n        this._pointerExitEventsInitialized = false;\n        this._tooltipComponent = TooltipComponent;\n        this._viewportMargin = 8;\n        this._cssClassPrefix = 'mat-mdc';\n        /**\n         * How touch gestures should be handled by the tooltip. On touch devices the tooltip directive\n         * uses a long press gesture to show and hide, however it can conflict with the native browser\n         * gestures. To work around the conflict, Angular Material disables native gestures on the\n         * trigger, but that might not be desirable on particular elements (e.g. inputs and draggable\n         * elements). The different values for this option configure the touch event handling as follows:\n         * - `auto` - Enables touch gestures for all elements, but tries to avoid conflicts with native\n         *   browser gestures on particular elements. In particular, it allows text selection on inputs\n         *   and textareas, and preserves the native browser dragging on elements marked as `draggable`.\n         * - `on` - Enables touch gestures for all elements and disables native\n         *   browser gestures with no exceptions.\n         * - `off` - Disables touch gestures. Note that this will prevent the tooltip from\n         *   showing on touch devices.\n         */\n        this.touchGestures = 'auto';\n        this._message = '';\n        /** Manually-bound passive event listeners. */\n        this._passiveListeners = [];\n        /** Emits when the component is destroyed. */\n        this._destroyed = new Subject();\n        this._scrollStrategy = scrollStrategy;\n        this._document = _document;\n        if (_defaultOptions) {\n            this._showDelay = _defaultOptions.showDelay;\n            this._hideDelay = _defaultOptions.hideDelay;\n            if (_defaultOptions.position) {\n                this.position = _defaultOptions.position;\n            }\n            if (_defaultOptions.positionAtOrigin) {\n                this.positionAtOrigin = _defaultOptions.positionAtOrigin;\n            }\n            if (_defaultOptions.touchGestures) {\n                this.touchGestures = _defaultOptions.touchGestures;\n            }\n        }\n        _dir.change.pipe(takeUntil(this._destroyed)).subscribe(() => {\n            if (this._overlayRef) {\n                this._updatePosition(this._overlayRef);\n            }\n        });\n        this._viewportMargin = MIN_VIEWPORT_TOOLTIP_THRESHOLD;\n    }\n    ngAfterViewInit() {\n        // This needs to happen after view init so the initial values for all inputs have been set.\n        this._viewInitialized = true;\n        this._setupPointerEnterEventsIfNeeded();\n        this._focusMonitor\n            .monitor(this._elementRef)\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(origin => {\n            // Note that the focus monitor runs outside the Angular zone.\n            if (!origin) {\n                this._ngZone.run(() => this.hide(0));\n            }\n            else if (origin === 'keyboard') {\n                this._ngZone.run(() => this.show());\n            }\n        });\n    }\n    /**\n     * Dispose the tooltip when destroyed.\n     */\n    ngOnDestroy() {\n        const nativeElement = this._elementRef.nativeElement;\n        clearTimeout(this._touchstartTimeout);\n        if (this._overlayRef) {\n            this._overlayRef.dispose();\n            this._tooltipInstance = null;\n        }\n        // Clean up the event listeners set in the constructor\n        this._passiveListeners.forEach(([event, listener]) => {\n            nativeElement.removeEventListener(event, listener, passiveListenerOptions);\n        });\n        this._passiveListeners.length = 0;\n        this._destroyed.next();\n        this._destroyed.complete();\n        this._ariaDescriber.removeDescription(nativeElement, this.message, 'tooltip');\n        this._focusMonitor.stopMonitoring(nativeElement);\n    }\n    /** Shows the tooltip after the delay in ms, defaults to tooltip-delay-show or 0ms if no input */\n    show(delay = this.showDelay, origin) {\n        if (this.disabled || !this.message || this._isTooltipVisible()) {\n            this._tooltipInstance?._cancelPendingAnimations();\n            return;\n        }\n        const overlayRef = this._createOverlay(origin);\n        this._detach();\n        this._portal =\n            this._portal || new ComponentPortal(this._tooltipComponent, this._viewContainerRef);\n        const instance = (this._tooltipInstance = overlayRef.attach(this._portal).instance);\n        instance._triggerElement = this._elementRef.nativeElement;\n        instance._mouseLeaveHideDelay = this._hideDelay;\n        instance\n            .afterHidden()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._setTooltipClass(this._tooltipClass);\n        this._updateTooltipMessage();\n        instance.show(delay);\n    }\n    /** Hides the tooltip after the delay in ms, defaults to tooltip-delay-hide or 0ms if no input */\n    hide(delay = this.hideDelay) {\n        const instance = this._tooltipInstance;\n        if (instance) {\n            if (instance.isVisible()) {\n                instance.hide(delay);\n            }\n            else {\n                instance._cancelPendingAnimations();\n                this._detach();\n            }\n        }\n    }\n    /** Shows/hides the tooltip */\n    toggle(origin) {\n        this._isTooltipVisible() ? this.hide() : this.show(undefined, origin);\n    }\n    /** Returns true if the tooltip is currently visible to the user */\n    _isTooltipVisible() {\n        return !!this._tooltipInstance && this._tooltipInstance.isVisible();\n    }\n    /** Create the overlay config and position strategy */\n    _createOverlay(origin) {\n        if (this._overlayRef) {\n            const existingStrategy = this._overlayRef.getConfig()\n                .positionStrategy;\n            if ((!this.positionAtOrigin || !origin) && existingStrategy._origin instanceof ElementRef) {\n                return this._overlayRef;\n            }\n            this._detach();\n        }\n        const scrollableAncestors = this._scrollDispatcher.getAncestorScrollContainers(this._elementRef);\n        // Create connected position strategy that listens for scroll events to reposition.\n        const strategy = this._overlay\n            .position()\n            .flexibleConnectedTo(this.positionAtOrigin ? origin || this._elementRef : this._elementRef)\n            .withTransformOriginOn(`.${this._cssClassPrefix}-tooltip`)\n            .withFlexibleDimensions(false)\n            .withViewportMargin(this._viewportMargin)\n            .withScrollableContainers(scrollableAncestors);\n        strategy.positionChanges.pipe(takeUntil(this._destroyed)).subscribe(change => {\n            this._updateCurrentPositionClass(change.connectionPair);\n            if (this._tooltipInstance) {\n                if (change.scrollableViewProperties.isOverlayClipped && this._tooltipInstance.isVisible()) {\n                    // After position changes occur and the overlay is clipped by\n                    // a parent scrollable then close the tooltip.\n                    this._ngZone.run(() => this.hide(0));\n                }\n            }\n        });\n        this._overlayRef = this._overlay.create({\n            direction: this._dir,\n            positionStrategy: strategy,\n            panelClass: `${this._cssClassPrefix}-${PANEL_CLASS}`,\n            scrollStrategy: this._scrollStrategy(),\n        });\n        this._updatePosition(this._overlayRef);\n        this._overlayRef\n            .detachments()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._detach());\n        this._overlayRef\n            .outsidePointerEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(() => this._tooltipInstance?._handleBodyInteraction());\n        this._overlayRef\n            .keydownEvents()\n            .pipe(takeUntil(this._destroyed))\n            .subscribe(event => {\n            if (this._isTooltipVisible() && event.keyCode === ESCAPE && !hasModifierKey(event)) {\n                event.preventDefault();\n                event.stopPropagation();\n                this._ngZone.run(() => this.hide(0));\n            }\n        });\n        if (this._defaultOptions?.disableTooltipInteractivity) {\n            this._overlayRef.addPanelClass(`${this._cssClassPrefix}-tooltip-panel-non-interactive`);\n        }\n        return this._overlayRef;\n    }\n    /** Detaches the currently-attached tooltip. */\n    _detach() {\n        if (this._overlayRef && this._overlayRef.hasAttached()) {\n            this._overlayRef.detach();\n        }\n        this._tooltipInstance = null;\n    }\n    /** Updates the position of the current tooltip. */\n    _updatePosition(overlayRef) {\n        const position = overlayRef.getConfig().positionStrategy;\n        const origin = this._getOrigin();\n        const overlay = this._getOverlayPosition();\n        position.withPositions([\n            this._addOffset({ ...origin.main, ...overlay.main }),\n            this._addOffset({ ...origin.fallback, ...overlay.fallback }),\n        ]);\n    }\n    /** Adds the configured offset to a position. Used as a hook for child classes. */\n    _addOffset(position) {\n        const offset = UNBOUNDED_ANCHOR_GAP;\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        if (position.originY === 'top') {\n            position.offsetY = -offset;\n        }\n        else if (position.originY === 'bottom') {\n            position.offsetY = offset;\n        }\n        else if (position.originX === 'start') {\n            position.offsetX = isLtr ? -offset : offset;\n        }\n        else if (position.originX === 'end') {\n            position.offsetX = isLtr ? offset : -offset;\n        }\n        return position;\n    }\n    /**\n     * Returns the origin position and a fallback position based on the user's position preference.\n     * The fallback position is the inverse of the origin (e.g. `'below' -> 'above'`).\n     */\n    _getOrigin() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let originPosition;\n        if (position == 'above' || position == 'below') {\n            originPosition = { originX: 'center', originY: position == 'above' ? 'top' : 'bottom' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            originPosition = { originX: 'start', originY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            originPosition = { originX: 'end', originY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(originPosition.originX, originPosition.originY);\n        return {\n            main: originPosition,\n            fallback: { originX: x, originY: y },\n        };\n    }\n    /** Returns the overlay position and a fallback position based on the user's preference */\n    _getOverlayPosition() {\n        const isLtr = !this._dir || this._dir.value == 'ltr';\n        const position = this.position;\n        let overlayPosition;\n        if (position == 'above') {\n            overlayPosition = { overlayX: 'center', overlayY: 'bottom' };\n        }\n        else if (position == 'below') {\n            overlayPosition = { overlayX: 'center', overlayY: 'top' };\n        }\n        else if (position == 'before' ||\n            (position == 'left' && isLtr) ||\n            (position == 'right' && !isLtr)) {\n            overlayPosition = { overlayX: 'end', overlayY: 'center' };\n        }\n        else if (position == 'after' ||\n            (position == 'right' && isLtr) ||\n            (position == 'left' && !isLtr)) {\n            overlayPosition = { overlayX: 'start', overlayY: 'center' };\n        }\n        else if (typeof ngDevMode === 'undefined' || ngDevMode) {\n            throw getMatTooltipInvalidPositionError(position);\n        }\n        const { x, y } = this._invertPosition(overlayPosition.overlayX, overlayPosition.overlayY);\n        return {\n            main: overlayPosition,\n            fallback: { overlayX: x, overlayY: y },\n        };\n    }\n    /** Updates the tooltip message and repositions the overlay according to the new message length */\n    _updateTooltipMessage() {\n        // Must wait for the message to be painted to the tooltip so that the overlay can properly\n        // calculate the correct positioning based on the size of the text.\n        if (this._tooltipInstance) {\n            this._tooltipInstance.message = this.message;\n            this._tooltipInstance._markForCheck();\n            this._ngZone.onMicrotaskEmpty.pipe(take(1), takeUntil(this._destroyed)).subscribe(() => {\n                if (this._tooltipInstance) {\n                    this._overlayRef.updatePosition();\n                }\n            });\n        }\n    }\n    /** Updates the tooltip class */\n    _setTooltipClass(tooltipClass) {\n        if (this._tooltipInstance) {\n            this._tooltipInstance.tooltipClass = tooltipClass;\n            this._tooltipInstance._markForCheck();\n        }\n    }\n    /** Inverts an overlay position. */\n    _invertPosition(x, y) {\n        if (this.position === 'above' || this.position === 'below') {\n            if (y === 'top') {\n                y = 'bottom';\n            }\n            else if (y === 'bottom') {\n                y = 'top';\n            }\n        }\n        else {\n            if (x === 'end') {\n                x = 'start';\n            }\n            else if (x === 'start') {\n                x = 'end';\n            }\n        }\n        return { x, y };\n    }\n    /** Updates the class on the overlay panel based on the current position of the tooltip. */\n    _updateCurrentPositionClass(connectionPair) {\n        const { overlayY, originX, originY } = connectionPair;\n        let newPosition;\n        // If the overlay is in the middle along the Y axis,\n        // it means that it's either before or after.\n        if (overlayY === 'center') {\n            // Note that since this information is used for styling, we want to\n            // resolve `start` and `end` to their real values, otherwise consumers\n            // would have to remember to do it themselves on each consumption.\n            if (this._dir && this._dir.value === 'rtl') {\n                newPosition = originX === 'end' ? 'left' : 'right';\n            }\n            else {\n                newPosition = originX === 'start' ? 'left' : 'right';\n            }\n        }\n        else {\n            newPosition = overlayY === 'bottom' && originY === 'top' ? 'above' : 'below';\n        }\n        if (newPosition !== this._currentPosition) {\n            const overlayRef = this._overlayRef;\n            if (overlayRef) {\n                const classPrefix = `${this._cssClassPrefix}-${PANEL_CLASS}-`;\n                overlayRef.removePanelClass(classPrefix + this._currentPosition);\n                overlayRef.addPanelClass(classPrefix + newPosition);\n            }\n            this._currentPosition = newPosition;\n        }\n    }\n    /** Binds the pointer events to the tooltip trigger. */\n    _setupPointerEnterEventsIfNeeded() {\n        // Optimization: Defer hooking up events if there's no message or the tooltip is disabled.\n        if (this._disabled ||\n            !this.message ||\n            !this._viewInitialized ||\n            this._passiveListeners.length) {\n            return;\n        }\n        // The mouse events shouldn't be bound on mobile devices, because they can prevent the\n        // first tap from firing its click event or can cause the tooltip to open for clicks.\n        if (this._platformSupportsMouseEvents()) {\n            this._passiveListeners.push([\n                'mouseenter',\n                event => {\n                    this._setupPointerExitEventsIfNeeded();\n                    let point = undefined;\n                    if (event.x !== undefined && event.y !== undefined) {\n                        point = event;\n                    }\n                    this.show(undefined, point);\n                },\n            ]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            this._passiveListeners.push([\n                'touchstart',\n                event => {\n                    const touch = event.targetTouches?.[0];\n                    const origin = touch ? { x: touch.clientX, y: touch.clientY } : undefined;\n                    // Note that it's important that we don't `preventDefault` here,\n                    // because it can prevent click events from firing on the element.\n                    this._setupPointerExitEventsIfNeeded();\n                    clearTimeout(this._touchstartTimeout);\n                    const DEFAULT_LONGPRESS_DELAY = 500;\n                    this._touchstartTimeout = setTimeout(() => this.show(undefined, origin), this._defaultOptions.touchLongPressShowDelay ?? DEFAULT_LONGPRESS_DELAY);\n                },\n            ]);\n        }\n        this._addListeners(this._passiveListeners);\n    }\n    _setupPointerExitEventsIfNeeded() {\n        if (this._pointerExitEventsInitialized) {\n            return;\n        }\n        this._pointerExitEventsInitialized = true;\n        const exitListeners = [];\n        if (this._platformSupportsMouseEvents()) {\n            exitListeners.push([\n                'mouseleave',\n                event => {\n                    const newTarget = event.relatedTarget;\n                    if (!newTarget || !this._overlayRef?.overlayElement.contains(newTarget)) {\n                        this.hide();\n                    }\n                },\n            ], ['wheel', event => this._wheelListener(event)]);\n        }\n        else if (this.touchGestures !== 'off') {\n            this._disableNativeGesturesIfNecessary();\n            const touchendListener = () => {\n                clearTimeout(this._touchstartTimeout);\n                this.hide(this._defaultOptions.touchendHideDelay);\n            };\n            exitListeners.push(['touchend', touchendListener], ['touchcancel', touchendListener]);\n        }\n        this._addListeners(exitListeners);\n        this._passiveListeners.push(...exitListeners);\n    }\n    _addListeners(listeners) {\n        listeners.forEach(([event, listener]) => {\n            this._elementRef.nativeElement.addEventListener(event, listener, passiveListenerOptions);\n        });\n    }\n    _platformSupportsMouseEvents() {\n        return !this._platform.IOS && !this._platform.ANDROID;\n    }\n    /** Listener for the `wheel` event on the element. */\n    _wheelListener(event) {\n        if (this._isTooltipVisible()) {\n            const elementUnderPointer = this._document.elementFromPoint(event.clientX, event.clientY);\n            const element = this._elementRef.nativeElement;\n            // On non-touch devices we depend on the `mouseleave` event to close the tooltip, but it\n            // won't fire if the user scrolls away using the wheel without moving their cursor. We\n            // work around it by finding the element under the user's cursor and closing the tooltip\n            // if it's not the trigger.\n            if (elementUnderPointer !== element && !element.contains(elementUnderPointer)) {\n                this.hide();\n            }\n        }\n    }\n    /** Disables the native browser gestures, based on how the tooltip has been configured. */\n    _disableNativeGesturesIfNecessary() {\n        const gestures = this.touchGestures;\n        if (gestures !== 'off') {\n            const element = this._elementRef.nativeElement;\n            const style = element.style;\n            // If gestures are set to `auto`, we don't disable text selection on inputs and\n            // textareas, because it prevents the user from typing into them on iOS Safari.\n            if (gestures === 'on' || (element.nodeName !== 'INPUT' && element.nodeName !== 'TEXTAREA')) {\n                style.userSelect =\n                    style.msUserSelect =\n                        style.webkitUserSelect =\n                            style.MozUserSelect =\n                                'none';\n            }\n            // If we have `auto` gestures and the element uses native HTML dragging,\n            // we don't set `-webkit-user-drag` because it prevents the native behavior.\n            if (gestures === 'on' || !element.draggable) {\n                style.webkitUserDrag = 'none';\n            }\n            style.touchAction = 'none';\n            style.webkitTapHighlightColor = 'transparent';\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTooltip, deps: [{ token: i1.Overlay }, { token: i0.ElementRef }, { token: i1.ScrollDispatcher }, { token: i0.ViewContainerRef }, { token: i0.NgZone }, { token: i2.Platform }, { token: i3.AriaDescriber }, { token: i3.FocusMonitor }, { token: MAT_TOOLTIP_SCROLL_STRATEGY }, { token: i4.Directionality }, { token: MAT_TOOLTIP_DEFAULT_OPTIONS, optional: true }, { token: DOCUMENT }], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatTooltip, isStandalone: true, selector: \"[matTooltip]\", inputs: { position: [\"matTooltipPosition\", \"position\"], positionAtOrigin: [\"matTooltipPositionAtOrigin\", \"positionAtOrigin\"], disabled: [\"matTooltipDisabled\", \"disabled\"], showDelay: [\"matTooltipShowDelay\", \"showDelay\"], hideDelay: [\"matTooltipHideDelay\", \"hideDelay\"], touchGestures: [\"matTooltipTouchGestures\", \"touchGestures\"], message: [\"matTooltip\", \"message\"], tooltipClass: [\"matTooltipClass\", \"tooltipClass\"] }, host: { properties: { \"class.mat-mdc-tooltip-disabled\": \"disabled\" }, classAttribute: \"mat-mdc-tooltip-trigger\" }, exportAs: [\"matTooltip\"], ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTooltip, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[matTooltip]',\n                    exportAs: 'matTooltip',\n                    host: {\n                        'class': 'mat-mdc-tooltip-trigger',\n                        '[class.mat-mdc-tooltip-disabled]': 'disabled',\n                    },\n                    standalone: true,\n                }]\n        }], ctorParameters: () => [{ type: i1.Overlay }, { type: i0.ElementRef }, { type: i1.ScrollDispatcher }, { type: i0.ViewContainerRef }, { type: i0.NgZone }, { type: i2.Platform }, { type: i3.AriaDescriber }, { type: i3.FocusMonitor }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_TOOLTIP_SCROLL_STRATEGY]\n                }] }, { type: i4.Directionality }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_TOOLTIP_DEFAULT_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }], propDecorators: { position: [{\n                type: Input,\n                args: ['matTooltipPosition']\n            }], positionAtOrigin: [{\n                type: Input,\n                args: ['matTooltipPositionAtOrigin']\n            }], disabled: [{\n                type: Input,\n                args: ['matTooltipDisabled']\n            }], showDelay: [{\n                type: Input,\n                args: ['matTooltipShowDelay']\n            }], hideDelay: [{\n                type: Input,\n                args: ['matTooltipHideDelay']\n            }], touchGestures: [{\n                type: Input,\n                args: ['matTooltipTouchGestures']\n            }], message: [{\n                type: Input,\n                args: ['matTooltip']\n            }], tooltipClass: [{\n                type: Input,\n                args: ['matTooltipClass']\n            }] } });\n/**\n * Internal component that wraps the tooltip's content.\n * @docs-private\n */\nclass TooltipComponent {\n    constructor(_changeDetectorRef, _elementRef, animationMode) {\n        this._changeDetectorRef = _changeDetectorRef;\n        this._elementRef = _elementRef;\n        /* Whether the tooltip text overflows to multiple lines */\n        this._isMultiline = false;\n        /** Whether interactions on the page should close the tooltip */\n        this._closeOnInteraction = false;\n        /** Whether the tooltip is currently visible. */\n        this._isVisible = false;\n        /** Subject for notifying that the tooltip has been hidden from the view */\n        this._onHide = new Subject();\n        /** Name of the show animation and the class that toggles it. */\n        this._showAnimation = 'mat-mdc-tooltip-show';\n        /** Name of the hide animation and the class that toggles it. */\n        this._hideAnimation = 'mat-mdc-tooltip-hide';\n        this._animationsDisabled = animationMode === 'NoopAnimations';\n    }\n    /**\n     * Shows the tooltip with an animation originating from the provided origin\n     * @param delay Amount of milliseconds to the delay showing the tooltip.\n     */\n    show(delay) {\n        // Cancel the delayed hide if it is scheduled\n        if (this._hideTimeoutId != null) {\n            clearTimeout(this._hideTimeoutId);\n        }\n        this._showTimeoutId = setTimeout(() => {\n            this._toggleVisibility(true);\n            this._showTimeoutId = undefined;\n        }, delay);\n    }\n    /**\n     * Begins the animation to hide the tooltip after the provided delay in ms.\n     * @param delay Amount of milliseconds to delay showing the tooltip.\n     */\n    hide(delay) {\n        // Cancel the delayed show if it is scheduled\n        if (this._showTimeoutId != null) {\n            clearTimeout(this._showTimeoutId);\n        }\n        this._hideTimeoutId = setTimeout(() => {\n            this._toggleVisibility(false);\n            this._hideTimeoutId = undefined;\n        }, delay);\n    }\n    /** Returns an observable that notifies when the tooltip has been hidden from view. */\n    afterHidden() {\n        return this._onHide;\n    }\n    /** Whether the tooltip is being displayed. */\n    isVisible() {\n        return this._isVisible;\n    }\n    ngOnDestroy() {\n        this._cancelPendingAnimations();\n        this._onHide.complete();\n        this._triggerElement = null;\n    }\n    /**\n     * Interactions on the HTML body should close the tooltip immediately as defined in the\n     * material design spec.\n     * https://material.io/design/components/tooltips.html#behavior\n     */\n    _handleBodyInteraction() {\n        if (this._closeOnInteraction) {\n            this.hide(0);\n        }\n    }\n    /**\n     * Marks that the tooltip needs to be checked in the next change detection run.\n     * Mainly used for rendering the initial text before positioning a tooltip, which\n     * can be problematic in components with OnPush change detection.\n     */\n    _markForCheck() {\n        this._changeDetectorRef.markForCheck();\n    }\n    _handleMouseLeave({ relatedTarget }) {\n        if (!relatedTarget || !this._triggerElement.contains(relatedTarget)) {\n            if (this.isVisible()) {\n                this.hide(this._mouseLeaveHideDelay);\n            }\n            else {\n                this._finalizeAnimation(false);\n            }\n        }\n    }\n    /**\n     * Callback for when the timeout in this.show() gets completed.\n     * This method is only needed by the mdc-tooltip, and so it is only implemented\n     * in the mdc-tooltip, not here.\n     */\n    _onShow() {\n        this._isMultiline = this._isTooltipMultiline();\n        this._markForCheck();\n    }\n    /** Whether the tooltip text has overflown to the next line */\n    _isTooltipMultiline() {\n        const rect = this._elementRef.nativeElement.getBoundingClientRect();\n        return rect.height > MIN_HEIGHT && rect.width >= MAX_WIDTH;\n    }\n    /** Event listener dispatched when an animation on the tooltip finishes. */\n    _handleAnimationEnd({ animationName }) {\n        if (animationName === this._showAnimation || animationName === this._hideAnimation) {\n            this._finalizeAnimation(animationName === this._showAnimation);\n        }\n    }\n    /** Cancels any pending animation sequences. */\n    _cancelPendingAnimations() {\n        if (this._showTimeoutId != null) {\n            clearTimeout(this._showTimeoutId);\n        }\n        if (this._hideTimeoutId != null) {\n            clearTimeout(this._hideTimeoutId);\n        }\n        this._showTimeoutId = this._hideTimeoutId = undefined;\n    }\n    /** Handles the cleanup after an animation has finished. */\n    _finalizeAnimation(toVisible) {\n        if (toVisible) {\n            this._closeOnInteraction = true;\n        }\n        else if (!this.isVisible()) {\n            this._onHide.next();\n        }\n    }\n    /** Toggles the visibility of the tooltip element. */\n    _toggleVisibility(isVisible) {\n        // We set the classes directly here ourselves so that toggling the tooltip state\n        // isn't bound by change detection. This allows us to hide it even if the\n        // view ref has been detached from the CD tree.\n        const tooltip = this._tooltip.nativeElement;\n        const showClass = this._showAnimation;\n        const hideClass = this._hideAnimation;\n        tooltip.classList.remove(isVisible ? hideClass : showClass);\n        tooltip.classList.add(isVisible ? showClass : hideClass);\n        if (this._isVisible !== isVisible) {\n            this._isVisible = isVisible;\n            this._changeDetectorRef.markForCheck();\n        }\n        // It's common for internal apps to disable animations using `* { animation: none !important }`\n        // which can break the opening sequence. Try to detect such cases and work around them.\n        if (isVisible && !this._animationsDisabled && typeof getComputedStyle === 'function') {\n            const styles = getComputedStyle(tooltip);\n            // Use `getPropertyValue` to avoid issues with property renaming.\n            if (styles.getPropertyValue('animation-duration') === '0s' ||\n                styles.getPropertyValue('animation-name') === 'none') {\n                this._animationsDisabled = true;\n            }\n        }\n        if (isVisible) {\n            this._onShow();\n        }\n        if (this._animationsDisabled) {\n            tooltip.classList.add('_mat-animation-noopable');\n            this._finalizeAnimation(isVisible);\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: TooltipComponent, deps: [{ token: i0.ChangeDetectorRef }, { token: i0.ElementRef }, { token: ANIMATION_MODULE_TYPE, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: TooltipComponent, isStandalone: true, selector: \"mat-tooltip-component\", host: { attributes: { \"aria-hidden\": \"true\" }, listeners: { \"mouseleave\": \"_handleMouseLeave($event)\" }, properties: { \"style.zoom\": \"isVisible() ? 1 : null\" } }, viewQueries: [{ propertyName: \"_tooltip\", first: true, predicate: [\"tooltip\"], descendants: true, static: true }], ngImport: i0, template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mdc-tooltip--shown mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mdc-tooltip__surface mdc-tooltip__surface-animation\\\">{{message}}</div>\\n</div>\\n\", styles: [\".mdc-tooltip__surface{word-break:break-all;word-break:var(--mdc-tooltip-word-break, normal);overflow-wrap:anywhere}.mdc-tooltip--showing-transition .mdc-tooltip__surface-animation{transition:opacity 150ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-tooltip--hide-transition .mdc-tooltip__surface-animation{transition:opacity 75ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-tooltip{position:fixed;display:none;z-index:9}.mdc-tooltip-wrapper--rich{position:relative}.mdc-tooltip--shown,.mdc-tooltip--showing,.mdc-tooltip--hide{display:inline-flex}.mdc-tooltip--shown.mdc-tooltip--rich,.mdc-tooltip--showing.mdc-tooltip--rich,.mdc-tooltip--hide.mdc-tooltip--rich{display:inline-block;left:-320px;position:absolute}.mdc-tooltip__surface{line-height:16px;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center}.mdc-tooltip__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-tooltip__surface::before{border-color:CanvasText}}.mdc-tooltip--rich .mdc-tooltip__surface{align-items:flex-start;display:flex;flex-direction:column;min-height:24px;min-width:40px;max-width:320px;position:relative}.mdc-tooltip--multiline .mdc-tooltip__surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mdc-tooltip__surface,.mdc-tooltip--multiline .mdc-tooltip__surface[dir=rtl]{text-align:right}.mdc-tooltip__surface .mdc-tooltip__title{margin:0 8px}.mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(200px - 2*8px);margin:8px;text-align:left}[dir=rtl] .mdc-tooltip__surface .mdc-tooltip__content,.mdc-tooltip__surface .mdc-tooltip__content[dir=rtl]{text-align:right}.mdc-tooltip--rich .mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(320px - 2*8px);align-self:stretch}.mdc-tooltip__surface .mdc-tooltip__content-link{text-decoration:none}.mdc-tooltip--rich-actions,.mdc-tooltip__content,.mdc-tooltip__title{z-index:1}.mdc-tooltip__surface-animation{opacity:0;transform:scale(0.8);will-change:transform,opacity}.mdc-tooltip--shown .mdc-tooltip__surface-animation{transform:scale(1);opacity:1}.mdc-tooltip--hide .mdc-tooltip__surface-animation{transform:scale(1)}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{position:absolute;height:24px;width:24px;transform:rotate(35deg) skewY(20deg) scaleX(0.9396926208)}.mdc-tooltip__caret-surface-top .mdc-elevation-overlay,.mdc-tooltip__caret-surface-bottom .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-tooltip__caret-surface-bottom{box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);outline:1px solid rgba(0,0,0,0);z-index:-1}@media screen and (forced-colors: active){.mdc-tooltip__caret-surface-bottom{outline-color:CanvasText}}.mat-mdc-tooltip .mdc-tooltip__surface{background-color:var(--mdc-plain-tooltip-container-color)}.mat-mdc-tooltip .mdc-tooltip__surface{border-radius:var(--mdc-plain-tooltip-container-shape)}.mat-mdc-tooltip .mdc-tooltip__caret-surface-top,.mat-mdc-tooltip .mdc-tooltip__caret-surface-bottom{border-radius:var(--mdc-plain-tooltip-container-shape)}.mat-mdc-tooltip .mdc-tooltip__surface{color:var(--mdc-plain-tooltip-supporting-text-color)}.mat-mdc-tooltip .mdc-tooltip__surface{font-family:var(--mdc-plain-tooltip-supporting-text-font);line-height:var(--mdc-plain-tooltip-supporting-text-line-height);font-size:var(--mdc-plain-tooltip-supporting-text-size);font-weight:var(--mdc-plain-tooltip-supporting-text-weight);letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking)}.mat-mdc-tooltip{position:relative;transform:scale(0)}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"], dependencies: [{ kind: \"directive\", type: NgClass, selector: \"[ngClass]\", inputs: [\"class\", \"ngClass\"] }], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: TooltipComponent, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-tooltip-component', encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, host: {\n                        // Forces the element to have a layout in IE and Edge. This fixes issues where the element\n                        // won't be rendered if the animations are disabled or there is no web animations polyfill.\n                        '[style.zoom]': 'isVisible() ? 1 : null',\n                        '(mouseleave)': '_handleMouseLeave($event)',\n                        'aria-hidden': 'true',\n                    }, standalone: true, imports: [NgClass], template: \"<div\\n  #tooltip\\n  class=\\\"mdc-tooltip mdc-tooltip--shown mat-mdc-tooltip\\\"\\n  [ngClass]=\\\"tooltipClass\\\"\\n  (animationend)=\\\"_handleAnimationEnd($event)\\\"\\n  [class.mdc-tooltip--multiline]=\\\"_isMultiline\\\">\\n  <div class=\\\"mdc-tooltip__surface mdc-tooltip__surface-animation\\\">{{message}}</div>\\n</div>\\n\", styles: [\".mdc-tooltip__surface{word-break:break-all;word-break:var(--mdc-tooltip-word-break, normal);overflow-wrap:anywhere}.mdc-tooltip--showing-transition .mdc-tooltip__surface-animation{transition:opacity 150ms 0ms cubic-bezier(0, 0, 0.2, 1),transform 150ms 0ms cubic-bezier(0, 0, 0.2, 1)}.mdc-tooltip--hide-transition .mdc-tooltip__surface-animation{transition:opacity 75ms 0ms cubic-bezier(0.4, 0, 1, 1)}.mdc-tooltip{position:fixed;display:none;z-index:9}.mdc-tooltip-wrapper--rich{position:relative}.mdc-tooltip--shown,.mdc-tooltip--showing,.mdc-tooltip--hide{display:inline-flex}.mdc-tooltip--shown.mdc-tooltip--rich,.mdc-tooltip--showing.mdc-tooltip--rich,.mdc-tooltip--hide.mdc-tooltip--rich{display:inline-block;left:-320px;position:absolute}.mdc-tooltip__surface{line-height:16px;padding:4px 8px;min-width:40px;max-width:200px;min-height:24px;max-height:40vh;box-sizing:border-box;overflow:hidden;text-align:center}.mdc-tooltip__surface::before{position:absolute;box-sizing:border-box;width:100%;height:100%;top:0;left:0;border:1px solid rgba(0,0,0,0);border-radius:inherit;content:\\\"\\\";pointer-events:none}@media screen and (forced-colors: active){.mdc-tooltip__surface::before{border-color:CanvasText}}.mdc-tooltip--rich .mdc-tooltip__surface{align-items:flex-start;display:flex;flex-direction:column;min-height:24px;min-width:40px;max-width:320px;position:relative}.mdc-tooltip--multiline .mdc-tooltip__surface{text-align:left}[dir=rtl] .mdc-tooltip--multiline .mdc-tooltip__surface,.mdc-tooltip--multiline .mdc-tooltip__surface[dir=rtl]{text-align:right}.mdc-tooltip__surface .mdc-tooltip__title{margin:0 8px}.mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(200px - 2*8px);margin:8px;text-align:left}[dir=rtl] .mdc-tooltip__surface .mdc-tooltip__content,.mdc-tooltip__surface .mdc-tooltip__content[dir=rtl]{text-align:right}.mdc-tooltip--rich .mdc-tooltip__surface .mdc-tooltip__content{max-width:calc(320px - 2*8px);align-self:stretch}.mdc-tooltip__surface .mdc-tooltip__content-link{text-decoration:none}.mdc-tooltip--rich-actions,.mdc-tooltip__content,.mdc-tooltip__title{z-index:1}.mdc-tooltip__surface-animation{opacity:0;transform:scale(0.8);will-change:transform,opacity}.mdc-tooltip--shown .mdc-tooltip__surface-animation{transform:scale(1);opacity:1}.mdc-tooltip--hide .mdc-tooltip__surface-animation{transform:scale(1)}.mdc-tooltip__caret-surface-top,.mdc-tooltip__caret-surface-bottom{position:absolute;height:24px;width:24px;transform:rotate(35deg) skewY(20deg) scaleX(0.9396926208)}.mdc-tooltip__caret-surface-top .mdc-elevation-overlay,.mdc-tooltip__caret-surface-bottom .mdc-elevation-overlay{width:100%;height:100%;top:0;left:0}.mdc-tooltip__caret-surface-bottom{box-shadow:0px 3px 1px -2px rgba(0, 0, 0, 0.2), 0px 2px 2px 0px rgba(0, 0, 0, 0.14), 0px 1px 5px 0px rgba(0, 0, 0, 0.12);outline:1px solid rgba(0,0,0,0);z-index:-1}@media screen and (forced-colors: active){.mdc-tooltip__caret-surface-bottom{outline-color:CanvasText}}.mat-mdc-tooltip .mdc-tooltip__surface{background-color:var(--mdc-plain-tooltip-container-color)}.mat-mdc-tooltip .mdc-tooltip__surface{border-radius:var(--mdc-plain-tooltip-container-shape)}.mat-mdc-tooltip .mdc-tooltip__caret-surface-top,.mat-mdc-tooltip .mdc-tooltip__caret-surface-bottom{border-radius:var(--mdc-plain-tooltip-container-shape)}.mat-mdc-tooltip .mdc-tooltip__surface{color:var(--mdc-plain-tooltip-supporting-text-color)}.mat-mdc-tooltip .mdc-tooltip__surface{font-family:var(--mdc-plain-tooltip-supporting-text-font);line-height:var(--mdc-plain-tooltip-supporting-text-line-height);font-size:var(--mdc-plain-tooltip-supporting-text-size);font-weight:var(--mdc-plain-tooltip-supporting-text-weight);letter-spacing:var(--mdc-plain-tooltip-supporting-text-tracking)}.mat-mdc-tooltip{position:relative;transform:scale(0)}.mat-mdc-tooltip::before{content:\\\"\\\";top:0;right:0;bottom:0;left:0;z-index:-1;position:absolute}.mat-mdc-tooltip-panel-below .mat-mdc-tooltip::before{top:-8px}.mat-mdc-tooltip-panel-above .mat-mdc-tooltip::before{bottom:-8px}.mat-mdc-tooltip-panel-right .mat-mdc-tooltip::before{left:-8px}.mat-mdc-tooltip-panel-left .mat-mdc-tooltip::before{right:-8px}.mat-mdc-tooltip._mat-animation-noopable{animation:none;transform:scale(1)}.mat-mdc-tooltip-panel.mat-mdc-tooltip-panel-non-interactive{pointer-events:none}@keyframes mat-mdc-tooltip-show{0%{opacity:0;transform:scale(0.8)}100%{opacity:1;transform:scale(1)}}@keyframes mat-mdc-tooltip-hide{0%{opacity:1;transform:scale(1)}100%{opacity:0;transform:scale(0.8)}}.mat-mdc-tooltip-show{animation:mat-mdc-tooltip-show 150ms cubic-bezier(0, 0, 0.2, 1) forwards}.mat-mdc-tooltip-hide{animation:mat-mdc-tooltip-hide 75ms cubic-bezier(0.4, 0, 1, 1) forwards}\"] }]\n        }], ctorParameters: () => [{ type: i0.ChangeDetectorRef }, { type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ANIMATION_MODULE_TYPE]\n                }] }], propDecorators: { _tooltip: [{\n                type: ViewChild,\n                args: ['tooltip', {\n                        // Use a static query here since we interact directly with\n                        // the DOM which can happen before `ngAfterViewInit`.\n                        static: true,\n                    }]\n            }] } });\n\n/**\n * Animations used by MatTooltip.\n * @docs-private\n */\nconst matTooltipAnimations = {\n    /** Animation that transitions a tooltip in and out. */\n    tooltipState: trigger('state', [\n        // TODO(crisbeto): these values are based on MDC's CSS.\n        // We should be able to use their styles directly once we land #19432.\n        state('initial, void, hidden', style({ opacity: 0, transform: 'scale(0.8)' })),\n        state('visible', style({ transform: 'scale(1)' })),\n        transition('* => visible', animate('150ms cubic-bezier(0, 0, 0.2, 1)')),\n        transition('* => hidden', animate('75ms cubic-bezier(0.4, 0, 1, 1)')),\n    ]),\n};\n\nclass MatTooltipModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTooltipModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTooltipModule, imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent], exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTooltipModule, providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER], imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatCommonModule, CdkScrollableModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatTooltipModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [A11yModule, CommonModule, OverlayModule, MatCommonModule, MatTooltip, TooltipComponent],\n                    exports: [MatTooltip, TooltipComponent, MatCommonModule, CdkScrollableModule],\n                    providers: [MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MAT_TOOLTIP_DEFAULT_OPTIONS, MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY, MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER, MatTooltip, MatTooltipModule, SCROLL_THROTTLE_MS, TOOLTIP_PANEL_CLASS, TooltipComponent, getMatTooltipInvalidPositionError, matTooltipAnimations };\n"], "names": ["Observable", "BehaviorSubject", "RewardCategory", "PartnerCategory", "ExchangeStatus", "RewardsService", "constructor", "rewardsSubject", "partnersSubject", "exchangesSubject", "rewards$", "asObservable", "partners$", "exchanges$", "initializeSampleData", "samplePartners", "id", "name", "description", "category", "CAFE", "address", "city", "phone", "email", "location", "latitude", "longitude", "isActive", "rewards", "createdAt", "Date", "updatedAt", "RESTAURANT", "RETAIL", "sampleRewards", "title", "pointsRequired", "partnerId", "partner<PERSON>ame", "FOOD", "imageUrl", "availableQuantity", "validUntil", "terms", "EDUCATION", "next", "getRewards", "getRewardsByCity", "observer", "subscribe", "filtered", "filter", "reward", "getRewardsByCategory", "getRewardById", "find", "r", "getPartners", "getPartnerById", "partners", "partner", "p", "exchangeReward", "rewardId", "userId", "_this", "_asyncToGenerator", "value", "Error", "exchange", "generateExchangeId", "pointsSpent", "status", "CONFIRMED", "exchangeCode", "generateExchangeCode", "exchangedAt", "now", "exchanges", "getUserExchanges", "userExchanges", "e", "toString", "Math", "random", "substr", "toUpperCase", "getCategories", "Object", "values", "getCategoryDisplayName", "names", "SHOPPING", "ENTERTAINMENT", "SERVICES", "HEALTH", "TRANSPORT", "OTHER", "factory", "ɵfac", "providedIn", "switchMap", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "exchanges_r1", "length", "ctx_r1", "getTotalPointsSpent", "getExchangesByStatus", "USED", "ɵɵlistener", "ExchangeHistoryComponent_div_8_div_1_mat_card_1_div_17_Template_button_click_10_listener", "ɵɵrestoreView", "_r3", "exchange_r4", "ɵɵnextContext", "$implicit", "ɵɵresetView", "copyExchangeCode", "ɵɵstyleProp", "isExpired", "ɵɵtextInterpolate1", "ɵɵclassProp", "formatDate", "usedAt", "ɵɵtemplate", "ExchangeHistoryComponent_div_8_div_1_mat_card_1_div_17_Template", "ExchangeHistoryComponent_div_8_div_1_mat_card_1_div_26_Template", "ExchangeHistoryComponent_div_8_div_1_mat_card_1_div_27_Template", "ExchangeHistoryComponent_div_8_div_1_mat_card_1_mat_card_actions_36_Template", "i_r5", "ɵɵproperty", "getStatusColor", "getStatusIcon", "getStatusLabel", "canUseExchange", "ExchangeHistoryComponent_div_8_div_1_mat_card_1_Template", "exchanges_r6", "ExchangeHistoryComponent_div_8_div_1_Template", "ExchangeHistoryComponent_div_8_ng_template_2_Template", "ɵɵtemplateRefExtractor", "noExchanges_r7", "ɵɵelement", "ExchangeHistoryComponent", "rewardsService", "authService", "currentUser", "currentUser$", "pipe", "user", "uid", "ngOnInit", "PENDING", "EXPIRED", "CANCELLED", "date", "toLocaleDateString", "day", "month", "year", "hour", "minute", "code", "navigator", "clipboard", "writeText", "then", "reduce", "total", "ɵɵdirectiveInject", "i1", "i2", "AuthService", "selectors", "decls", "vars", "consts", "template", "ExchangeHistoryComponent_Template", "rf", "ctx", "ExchangeHistoryComponent_div_6_Template", "ExchangeHistoryComponent_div_8_Template", "ExchangeHistoryComponent_div_10_Template", "ɵɵpipeBind1", "reward_r3", "RewardDetailComponent_div_0_mat_card_55_div_13_Template_button_click_5_listener", "_r4", "partner_r5", "ngIf", "getDirections", "RewardDetailComponent_div_0_mat_card_55_div_14_Template_button_click_5_listener", "_r6", "<PERSON><PERSON><PERSON><PERSON>", "RewardDetailComponent_div_0_mat_card_55_div_15_Template_button_click_5_listener", "_r7", "RewardDetailComponent_div_0_mat_card_55_div_13_Template", "RewardDetailComponent_div_0_mat_card_55_div_14_Template", "RewardDetailComponent_div_0_mat_card_55_div_15_Template", "points", "RewardDetailComponent_div_0_mat_card_57_div_11_Template", "RewardDetailComponent_div_0_mat_card_57_div_12_Template", "can<PERSON>fford", "RewardDetailComponent_div_0_Template_button_click_2_listener", "_r1", "goBack", "RewardDetailComponent_div_0_Template_button_click_7_listener", "shareReward", "RewardDetailComponent_div_0_mat_chip_18_Template", "RewardDetailComponent_div_0_div_54_Template", "RewardDetailComponent_div_0_mat_card_55_Template", "RewardDetailComponent_div_0_mat_card_57_Template", "RewardDetailComponent_div_0_Template_button_click_60_listener", "RewardDetailComponent_div_0_mat_spinner_61_Template", "RewardDetailComponent_div_0_mat_icon_62_Template", "RewardDetailComponent_div_0_span_63_Template", "RewardDetailComponent_div_0_button_64_Template", "RewardDetailComponent_div_0_p_65_Template", "ɵɵsanitizeUrl", "getCategoryIcon", "getCategoryLabel", "isExpiringSoon", "getAvailabilityColor", "getAvailabilityText", "partner$", "isExchanging", "RewardDetailComponent", "route", "router", "snackBar", "reward$", "params", "open", "duration", "undefined", "navigate", "fragment", "error", "console", "message", "iconMap", "labelMap", "daysUntilExpiry", "ceil", "getTime", "share", "text", "url", "window", "href", "catch", "encodeURIComponent", "ActivatedRoute", "Router", "i3", "i4", "MatSnackBar", "RewardDetailComponent_Template", "RewardDetailComponent_div_0_Template", "RewardDetailComponent_div_2_Template", "combineLatest", "map", "startWith", "FormControl", "ctx_r0", "category_r2", "icon", "label", "city_r3", "formatExpiryDate", "reward_r5", "RewardsListComponent_div_31_mat_card_1_Template_mat_card_click_0_listener", "viewRewardDetails", "RewardsListComponent_div_31_mat_card_1_mat_chip_8_Template", "RewardsListComponent_div_31_mat_card_1_span_30_Template", "RewardsListComponent_div_31_mat_card_1_Template_button_click_33_listener", "$event", "stopPropagation", "RewardsListComponent_div_31_mat_card_1_Template_button_click_37_listener", "RewardsListComponent_div_31_mat_card_1_Template_button_click_41_listener", "addToWishlist", "i_r6", "isInWishlist", "RewardsListComponent_div_31_mat_card_1_Template", "rewards_r7", "RewardsListComponent_div_33_Template_button_click_10_listener", "_r8", "clearFilters", "RewardsListComponent", "dialog", "categoryFilter", "cityFilter", "searchFilter", "categories", "cities", "setValue", "setupFilters", "filteredRewards$", "valueChanges", "search", "toLowerCase", "includes", "categoryData", "c", "log", "origin", "MatDialog", "i5", "RewardsListComponent_Template", "RewardsListComponent_div_6_Template", "RewardsListComponent_mat_option_21_Template", "RewardsListComponent_mat_option_26_Template", "RewardsListComponent_Template_button_click_27_listener", "RewardsListComponent_div_31_Template", "RewardsListComponent_div_33_Template", "tmp_7_0", "CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatChipsModule", "MatTabsModule", "MatDialogModule", "MatSnackBarModule", "MatProgressSpinnerModule", "MatBadgeModule", "MatTooltipModule", "MatMenuModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "ReactiveFormsModule", "rewardsRoutes", "RewardsModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "path", "component", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "inject", "ApplicationRef", "createComponent", "EnvironmentInjector", "ANIMATION_MODULE_TYPE", "booleanAttribute", "Directive", "Optional", "Inject", "Input", "NgModule", "MatCommonModule", "InteractivityChecker", "A11yModule", "DOCUMENT", "nextId", "BADGE_CONTENT_CLASS", "badgeApps", "Set", "_MatBadgeStyleLoader", "_MatBadgeStyleLoader_Factory", "t", "ɵcmp", "ɵɵdefineComponent", "type", "standalone", "features", "ɵɵStandaloneFeature", "_MatBadgeStyleLoader_Template", "styles", "encapsulation", "changeDetection", "ngDevMode", "ɵsetClassMetadata", "args", "None", "OnPush", "MatBadge", "color", "_color", "_setColor", "content", "_content", "newContent", "_updateR<PERSON><PERSON><PERSON><PERSON>nt", "_description", "newDescription", "_updateDescription", "_ngZone", "_elementRef", "_ariaDescriber", "_renderer", "_animationMode", "overlap", "position", "size", "_id", "_isInitialized", "_interactivityC<PERSON>cker", "_document", "appRef", "has", "add", "componentRef", "environmentInjector", "onDestroy", "delete", "destroy", "nativeElement", "nodeType", "ELEMENT_NODE", "matIconTagName", "tagName", "getAttribute", "warn", "outerHTML", "isAbove", "indexOf", "isAfter", "getBadgeElement", "_badgeElement", "_clearExistingBadges", "_createBadgeElement", "ngOnDestroy", "destroyNode", "_inlineBadgeDescription", "remove", "removeDescription", "_isHostInteractive", "isFocusable", "ignoreVisibility", "badgeElement", "createElement", "activeClass", "setAttribute", "classList", "append<PERSON><PERSON><PERSON>", "requestAnimationFrame", "runOutsideAngular", "newContentNormalized", "trim", "textContent", "_removeInlineDescription", "describe", "_updateInlineDescription", "colorPalette", "badges", "querySelectorAll", "Array", "from", "MatBadge_Factory", "NgZone", "ElementRef", "AriaDescriber", "Renderer2", "ɵdir", "ɵɵdefineDirective", "hostAttrs", "hostVars", "hostBindings", "MatBadge_HostBindings", "hidden", "disabled", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "ɵɵInputTransformsFeature", "selector", "host", "decorators", "alias", "transform", "MatBadgeModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "exports", "takeUntil", "take", "coerceBooleanProperty", "coerceNumberProperty", "ESCAPE", "hasModifierKey", "InjectionToken", "ViewChild", "Ng<PERSON><PERSON>", "normalizePassiveListenerOptions", "Overlay", "OverlayModule", "ComponentPortal", "Subject", "trigger", "state", "style", "transition", "animate", "CdkScrollableModule", "_c0", "SCROLL_THROTTLE_MS", "getMatTooltipInvalidPositionError", "MAT_TOOLTIP_SCROLL_STRATEGY", "overlay", "scrollStrategies", "reposition", "scrollThrottle", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY", "MAT_TOOLTIP_SCROLL_STRATEGY_FACTORY_PROVIDER", "provide", "deps", "useFactory", "MAT_TOOLTIP_DEFAULT_OPTIONS_FACTORY", "showDelay", "<PERSON><PERSON><PERSON><PERSON>", "touchendHideDelay", "MAT_TOOLTIP_DEFAULT_OPTIONS", "TOOLTIP_PANEL_CLASS", "PANEL_CLASS", "passiveListenerOptions", "passive", "MIN_VIEWPORT_TOOLTIP_THRESHOLD", "UNBOUNDED_ANCHOR_GAP", "MIN_HEIGHT", "MAX_WIDTH", "MatTooltip", "_position", "_overlayRef", "_updatePosition", "_tooltipInstance", "show", "updatePosition", "positionAt<PERSON><PERSON><PERSON>", "_position<PERSON><PERSON><PERSON><PERSON><PERSON>", "_detach", "_disabled", "hide", "_setupPointerEnterEventsIfNeeded", "_showDelay", "_hideDelay", "_mouseLeaveHideDelay", "_message", "String", "_isTooltipVisible", "_updateTooltipMessage", "Promise", "resolve", "tooltipClass", "_tooltipClass", "_setTooltipClass", "_overlay", "_scrollDispatcher", "_viewContainerRef", "_platform", "_focusMonitor", "scrollStrategy", "_dir", "_defaultOptions", "_viewInitialized", "_pointerExitEventsInitialized", "_tooltipComponent", "TooltipComponent", "_viewportMargin", "_cssClassPrefix", "touchGestures", "_passiveListeners", "_destroyed", "_scrollStrategy", "change", "ngAfterViewInit", "monitor", "run", "clearTimeout", "_touchstartTimeout", "dispose", "for<PERSON>ach", "event", "listener", "removeEventListener", "complete", "stopMonitoring", "delay", "_cancelPendingAnimations", "overlayRef", "_createOverlay", "_portal", "instance", "attach", "_triggerElement", "afterHidden", "isVisible", "toggle", "existingStrategy", "getConfig", "positionStrategy", "_origin", "scrollableAncestors", "getAncestorScrollContainers", "strategy", "flexibleConnectedTo", "withTransformOriginOn", "withFlexibleDimensions", "withViewportMargin", "withScrollableContainers", "position<PERSON><PERSON>es", "_updateCurrentPositionClass", "connectionPair", "scrollableViewProperties", "isOverlayClipped", "create", "direction", "panelClass", "detachments", "outsidePointerEvents", "_handleBodyInteraction", "keydownEvents", "keyCode", "preventDefault", "disableTooltipInteractivity", "addPanelClass", "has<PERSON>tta<PERSON>", "detach", "_get<PERSON><PERSON>in", "_getOverlayPosition", "withPositions", "_addOffset", "main", "fallback", "offset", "isLtr", "originY", "offsetY", "originX", "offsetX", "originPosition", "x", "y", "_invertPosition", "overlayPosition", "overlayX", "overlayY", "_mark<PERSON><PERSON><PERSON><PERSON><PERSON>", "onMicrotaskEmpty", "newPosition", "_currentPosition", "classPrefix", "removePanelClass", "_platformSupportsMouseEvents", "push", "_setupPointerExitEventsIfNeeded", "point", "_disableNativeGesturesIfNecessary", "touch", "targetTouches", "clientX", "clientY", "DEFAULT_LONGPRESS_DELAY", "setTimeout", "touchLongPressShowDelay", "_addListeners", "exitListeners", "newTarget", "relatedTarget", "overlayElement", "contains", "_wheelListener", "touchendListener", "listeners", "addEventListener", "IOS", "ANDROID", "elementUnderPointer", "elementFromPoint", "element", "gestures", "nodeName", "userSelect", "msUserSelect", "webkitUserSelect", "MozUserSelect", "draggable", "webkitUserDrag", "touchAction", "webkitTapHighlightColor", "MatTooltip_Factory", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "ViewContainerRef", "Platform", "FocusMonitor", "Directionality", "MatTooltip_HostBindings", "exportAs", "_changeDetectorRef", "animationMode", "_isMultiline", "_closeOnInteraction", "_isVisible", "_onHide", "_showAnimation", "_hideAnimation", "_animationsDisabled", "_hideTimeoutId", "_showTimeoutId", "_toggleVisibility", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "_handleMouseLeave", "_finalizeAnimation", "_onShow", "_isTooltipMultiline", "rect", "getBoundingClientRect", "height", "width", "_handleAnimationEnd", "animationName", "toVisible", "tooltip", "_tooltip", "showClass", "hideClass", "getComputedStyle", "getPropertyValue", "TooltipComponent_Factory", "ChangeDetectorRef", "viewQuery", "TooltipComponent_Query", "ɵɵviewQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "first", "TooltipComponent_HostBindings", "TooltipComponent_mouseleave_HostBindingHandler", "TooltipComponent_Template", "ɵɵgetCurrentView", "TooltipComponent_Template_div_animationend_0_listener", "dependencies", "static", "matTooltipAnimations", "tooltipState", "opacity", "MatTooltipModule_Factory", "providers"], "sourceRoot": "webpack:///", "x_google_ignoreList": [9, 10]}