export interface Reward {
  id: string;
  title: string;
  description: string;
  pointsCost: number;
  partnerId: string;
  partnerName: string;
  imageUrl?: string;
  available: boolean;
  expiryDate?: Date;
  createdAt: Date;
  updatedAt: Date;
}

export interface RewardRedemption {
  id: string;
  userId: string;
  rewardId: string;
  reward: Reward;
  redeemedAt: Date;
  status: 'pending' | 'completed' | 'cancelled';
}
