.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(103, 126, 234, 0.3);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.welcome-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.welcome-content {
  position: relative;
  z-index: 1;
}

.welcome-content h1 {
  margin: 0 0 12px 0;
  font-size: 2.5rem;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background: linear-gradient(45deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 12px;
  margin: 0;
  opacity: 0.95;
  font-size: 1.1rem;
  font-weight: 500;
}

.user-info mat-icon {
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));
}

.points-badge {
  display: flex;
  align-items: center;
  gap: 12px;
  background: rgba(255, 255, 255, 0.25);
  padding: 16px 24px;
  border-radius: 32px;
  font-size: 1.2rem;
  font-weight: 700;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  z-index: 1;
}

.points-badge:hover {
  transform: scale(1.05);
  background: rgba(255, 255, 255, 0.35);
}

.points-badge mat-icon {
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
  color: #ffd700;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.3));
  animation: pulse 2s infinite;
}

.stats-section,
.actions-section,
.activity-section {
  margin-bottom: 48px;
}

.section-title {
  margin: 0 0 32px 0;
  color: #2d3748;
  font-size: 2rem;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
  text-align: center;
  position: relative;
  padding-bottom: 16px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
}

.counter-animation {
  animation: countUp 1s ease-out;
}

@keyframes countUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.8);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 24px;
  margin-bottom: 40px;
}

.stat-card {
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
  opacity: 0.8;
}

.stat-card:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-card:nth-child(1)::before {
  background: linear-gradient(90deg, #ffd700, #ffed4e);
}

.stat-card:nth-child(2)::before {
  background: linear-gradient(90deg, #4facfe, #00f2fe);
}

.stat-card:nth-child(3)::before {
  background: linear-gradient(90deg, #43e97b, #38f9d7);
}

.stat-card:nth-child(4)::before {
  background: linear-gradient(90deg, #fa709a, #fee140);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
  position: relative;
  z-index: 1;
}

.stat-icon {
  font-size: 3rem;
  background: linear-gradient(135deg, rgba(103, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 16px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-icon mat-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.stat-info {
  flex: 1;
}

.stat-info h3 {
  margin: 0 0 8px 0;
  font-size: 2.2rem;
  font-weight: 700;
  color: #2d3748;
  font-family: 'Poppins', sans-serif;
  background: linear-gradient(135deg, #2d3748, #4a5568);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.stat-info p {
  margin: 0;
  color: #718096;
  font-size: 1rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.action-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.action-content {
  text-align: center;
  padding: 16px;
}

.action-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.action-icon mat-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
}

.action-content h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 500;
}

.action-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.activity-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  flex-shrink: 0;
}

.activity-icon mat-icon {
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
}

.activity-earned mat-icon { color: #4caf50; }
.activity-spent mat-icon { color: #f44336; }
.activity-transferred mat-icon { color: #2196f3; }
.activity-validated mat-icon { color: #ff9800; }
.activity-bonus mat-icon { color: #9c27b0; }

.activity-info {
  flex: 1;
}

.activity-description {
  margin: 0 0 4px 0;
  color: #333;
  font-weight: 500;
}

.activity-date {
  margin: 0;
  color: #666;
  font-size: 0.8rem;
}

.activity-points {
  font-weight: 600;
  font-size: 1rem;
}

.activity-points.positive {
  color: #4caf50;
}

.activity-points.negative {
  color: #f44336;
}

.empty-state {
  margin-top: 32px;
}

.empty-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-content {
  text-align: center;
  padding: 32px;
}

.empty-icon {
  font-size: 4rem;
  color: #ccc;
  margin-bottom: 16px;
}

.empty-content h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.5rem;
}

.empty-content p {
  margin: 0 0 24px 0;
  color: #666;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .welcome-content h1 {
    font-size: 1.5rem;
  }
  
  .stats-grid,
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .activity-points {
    align-self: flex-end;
  }
}
