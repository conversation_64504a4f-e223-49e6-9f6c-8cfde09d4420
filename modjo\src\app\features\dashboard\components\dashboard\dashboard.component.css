.dashboard-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 16px;
}

.welcome-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding: 24px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 16px;
  color: white;
}

.welcome-content h1 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 600;
}

.user-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin: 0;
  opacity: 0.9;
}

.points-badge {
  display: flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 12px 20px;
  border-radius: 24px;
  font-size: 1.1rem;
  font-weight: 500;
}

.stats-section,
.actions-section,
.activity-section {
  margin-bottom: 32px;
}

.stats-section h2,
.actions-section h2,
.activity-section h2 {
  margin: 0 0 16px 0;
  color: #333;
  font-size: 1.5rem;
  font-weight: 500;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.stat-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 16px;
}

.stat-icon {
  font-size: 2rem;
}

.stat-icon mat-icon {
  font-size: 2rem;
  width: 2rem;
  height: 2rem;
}

.stat-info h3 {
  margin: 0 0 4px 0;
  font-size: 1.8rem;
  font-weight: 600;
  color: #333;
}

.stat-info p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 16px;
}

.action-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.action-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.15);
}

.action-content {
  text-align: center;
  padding: 16px;
}

.action-icon {
  font-size: 3rem;
  margin-bottom: 16px;
}

.action-icon mat-icon {
  font-size: 3rem;
  width: 3rem;
  height: 3rem;
}

.action-content h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.2rem;
  font-weight: 500;
}

.action-content p {
  margin: 0;
  color: #666;
  font-size: 0.9rem;
}

.activity-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.activity-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid #eee;
}

.activity-item:last-child {
  border-bottom: none;
}

.activity-icon {
  flex-shrink: 0;
}

.activity-icon mat-icon {
  font-size: 1.5rem;
  width: 1.5rem;
  height: 1.5rem;
}

.activity-earned mat-icon { color: #4caf50; }
.activity-spent mat-icon { color: #f44336; }
.activity-transferred mat-icon { color: #2196f3; }
.activity-validated mat-icon { color: #ff9800; }
.activity-bonus mat-icon { color: #9c27b0; }

.activity-info {
  flex: 1;
}

.activity-description {
  margin: 0 0 4px 0;
  color: #333;
  font-weight: 500;
}

.activity-date {
  margin: 0;
  color: #666;
  font-size: 0.8rem;
}

.activity-points {
  font-weight: 600;
  font-size: 1rem;
}

.activity-points.positive {
  color: #4caf50;
}

.activity-points.negative {
  color: #f44336;
}

.empty-state {
  margin-top: 32px;
}

.empty-card {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.empty-content {
  text-align: center;
  padding: 32px;
}

.empty-icon {
  font-size: 4rem;
  color: #ccc;
  margin-bottom: 16px;
}

.empty-content h3 {
  margin: 0 0 8px 0;
  color: #333;
  font-size: 1.5rem;
}

.empty-content p {
  margin: 0 0 24px 0;
  color: #666;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .welcome-section {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }
  
  .welcome-content h1 {
    font-size: 1.5rem;
  }
  
  .stats-grid,
  .actions-grid {
    grid-template-columns: 1fr;
  }
  
  .activity-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .activity-points {
    align-self: flex-end;
  }
}
