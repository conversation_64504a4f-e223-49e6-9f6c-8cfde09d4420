/* Dashboard Élève - ModJob */
.dashboard-container {
  padding: 20px;
  max-width: 1200px;
  margin: 0 auto;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  min-height: 100vh;
  font-family: '<PERSON><PERSON>', sans-serif;
}

/* En-tête */
.dashboard-header {
  background: white;
  border-radius: 16px;
  padding: 24px;
  margin-bottom: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.user-welcome {
  display: flex;
  align-items: center;
  gap: 16px;
}

.user-avatar {
  width: 56px;
  height: 56px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
}

.user-avatar mat-icon {
  font-size: 1.8rem;
}

.user-info h2 {
  margin: 0 0 4px 0;
  color: #2d3748;
  font-weight: 600;
  font-size: 1.3rem;
}

.user-role {
  color: #718096;
  margin: 0;
  font-size: 0.9rem;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.points-card {
  display: flex;
  align-items: center;
  gap: 12px;
  background: linear-gradient(135deg, #FFD700, #FFA500);
  color: white;
  padding: 12px 20px;
  border-radius: 20px;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.points-icon {
  font-size: 1.5rem !important;
}

.points-info {
  display: flex;
  flex-direction: column;
}

.points-value {
  font-size: 1.4rem;
  font-weight: 700;
  line-height: 1;
}

.points-label {
  font-size: 0.8rem;
  opacity: 0.9;
}

.notifications-btn {
  background: #f7fafc;
  color: #4a5568;
}

/* Scanner QR */
.qr-scan-section {
  margin-bottom: 24px;
}

.qr-scan-card {
  background: linear-gradient(135deg, #4CAF50, #45a049);
  color: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(76, 175, 80, 0.3);
}

.qr-scan-content {
  display: flex;
  align-items: center;
  gap: 24px;
}

.qr-scan-icon {
  width: 80px;
  height: 80px;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.qr-scan-icon mat-icon {
  font-size: 2.5rem !important;
}

.qr-scan-text {
  flex: 1;
}

.qr-scan-text h3 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: 600;
}

.qr-scan-text p {
  margin: 0;
  opacity: 0.9;
}

.qr-scan-btn {
  background: white;
  color: #4CAF50;
  font-weight: 600;
  padding: 12px 24px;
  border-radius: 12px;
}

/* Progression */
.progress-section {
  margin-bottom: 24px;
}

.progress-card {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.progress-card h3 {
  margin: 0 0 20px 0;
  color: #2d3748;
  font-weight: 600;
  font-size: 1.2rem;
}

.progress-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16px;
}

.next-reward {
  display: flex;
  align-items: center;
  gap: 12px;
}

.reward-icon {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  object-fit: cover;
}

.reward-details h4 {
  margin: 0 0 4px 0;
  color: #2d3748;
  font-weight: 600;
}

.reward-details p {
  margin: 0;
  color: #718096;
  font-size: 0.9rem;
}

.points-needed {
  text-align: right;
}

.points-remaining {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #667eea;
}

.points-label {
  font-size: 0.8rem;
  color: #718096;
}

.progress-bar {
  width: 100%;
  height: 12px;
  background: #e2e8f0;
  border-radius: 6px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 6px;
  transition: width 0.3s ease;
}

.progress-text {
  text-align: center;
  font-size: 0.9rem;
  color: #718096;
  margin-bottom: 12px;
}

.motivation-message {
  text-align: center;
  padding: 12px;
  background: #f7fafc;
  border-radius: 8px;
}

.motivation-message p {
  margin: 0;
  color: #4a5568;
  font-weight: 500;
}

/* Récompenses */
.rewards-section {
  margin-bottom: 32px;
}

.section-subtitle {
  display: flex;
  align-items: center;
  gap: 8px;
  color: white;
  font-size: 1.2rem;
  font-weight: 600;
  margin-bottom: 16px;
}

.rewards-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.reward-card {
  background: white;
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
  transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.reward-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.reward-card.affordable {
  border: 2px solid #4CAF50;
}

.reward-image {
  position: relative;
  height: 150px;
  overflow: hidden;
}

.reward-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.reward-badge {
  position: absolute;
  top: 8px;
  right: 8px;
  background: #4CAF50;
  color: white;
  border-radius: 50%;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reward-content {
  padding: 16px;
}

.reward-card h4 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-weight: 600;
}

.reward-description {
  color: #718096;
  font-size: 0.9rem;
  margin-bottom: 12px;
}

.reward-partner,
.reward-points {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 0.9rem;
  color: #4a5568;
}

.exchange-btn {
  width: 100%;
  margin-top: 12px;
}

/* Floating QR Button */
.floating-qr-btn {
  position: fixed;
  bottom: 24px;
  right: 24px;
  z-index: 1000;
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

/* Responsive */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .dashboard-header {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .qr-scan-content {
    flex-direction: column;
    text-align: center;
  }

  .progress-info {
    flex-direction: column;
    gap: 16px;
    text-align: center;
  }

  .rewards-grid {
    grid-template-columns: 1fr;
  }
}







/* Partners Section */
.partners-section {
  margin-bottom: 48px;
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.partner-card {
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.partner-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.partner-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 20px 0;
}

.partner-logo {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  object-fit: cover;
  background: #f7fafc;
}

.partner-info h4 {
  margin: 0 0 4px 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
}

.partner-category {
  margin: 0 0 8px 0;
  color: #718096;
  font-size: 0.9rem;
}

.partner-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: #718096;
}

.star-icon {
  font-size: 1rem !important;
  width: 1rem !important;
  height: 1rem !important;
  color: #ffd700;
}

.partner-offer {
  margin: 0 0 16px 0;
  color: #2d3748;
  font-weight: 500;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  padding: 12px;
  border-radius: 8px;
  font-size: 0.9rem;
}

.partner-actions {
  display: flex;
  gap: 8px;
}

.partner-btn {
  flex: 1;
  height: 36px;
  font-size: 0.8rem;
}

/* Actions Hub */
.actions-hub {
  margin-bottom: 48px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.action-category {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-title {
  margin: 0 0 20px 0;
  color: #2d3748;
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

.category-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-btn span {
  flex: 1;
  text-align: left;
  margin-left: 12px;
  font-weight: 500;
}

.action-points {
  background: rgba(255, 255, 255, 0.9);
  color: #2d3748;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Challenges Section */
.challenges-section {
  margin-bottom: 48px;
}

.challenges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

.challenge-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.challenge-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.challenge-header {
  padding: 24px;
  text-align: center;
  color: white;
  position: relative;
}

.challenge-icon {
  font-size: 3rem !important;
  width: 3rem !important;
  height: 3rem !important;
  margin-bottom: 12px;
}

.challenge-header h4 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.challenge-description {
  margin: 0 0 20px 0;
  color: #4a5568;
  line-height: 1.5;
}

.challenge-progress {
  margin-bottom: 16px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 1s ease-in-out;
}

.progress-text {
  text-align: center;
  font-size: 0.9rem;
  color: #4a5568;
  font-weight: 600;
}

.challenge-reward {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding: 12px;
  background: #f7fafc;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #2d3748;
}

/* City Comparison Section */
.comparison-section {
  margin-bottom: 48px;
}

.comparison-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.comparison-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
  padding: 0 20px;
}

.city-score {
  text-align: center;
  flex: 1;
}

.city-score.monastir h3 {
  color: #667eea;
}

.city-score.sousse h3 {
  color: #f093fb;
}

.city-score h3 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.city-score .score {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.city-score.sousse .score {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.city-score p {
  margin: 0;
  color: #718096;
  font-size: 0.9rem;
}

.vs-divider {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 0 20px;
}

.vs-divider mat-icon {
  font-size: 2rem !important;
  width: 2rem !important;
  height: 2rem !important;
  color: #667eea;
}

.vs-divider span {
  font-weight: 700;
  color: #2d3748;
  font-size: 1.2rem;
}

.comparison-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-comparison {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.9rem;
  text-align: center;
}

.stat-bars {
  display: flex;
  gap: 8px;
  align-items: center;
}

.stat-bar {
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 60px;
  transition: all 0.3s ease;
}

.stat-bar.monastir {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-bar.sousse {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .hero-section {
    flex-direction: column;
    gap: 24px;
    text-align: center;
    padding: 40px 24px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-stats {
    justify-content: center;
    gap: 20px;
  }

  .impact-grid,
  .partners-grid,
  .actions-grid,
  .challenges-grid {
    grid-template-columns: 1fr;
  }

  .events-scroll {
    padding-left: 16px;
  }

  .comparison-header {
    flex-direction: column;
    gap: 20px;
  }

  .vs-divider {
    order: -1;
  }

  .stat-bars {
    flex-direction: column;
    gap: 4px;
  }

  .stat-bar {
    width: 100%;
    justify-content: space-between;
    padding: 0 12px;
  }
}

@media (max-width: 480px) {
  .hero-stats {
    flex-direction: column;
    gap: 16px;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .action-btn {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .action-btn span {
    margin-left: 0;
  }
}
