/* Dashboard Container */
.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

/* Hero Section */
.hero-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 40px;
  padding: 60px 40px;
  border-radius: 32px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.hero-section.city-monastir {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.hero-section.city-sousse {
  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.city-badge {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  background: rgba(255, 255, 255, 0.2);
  padding: 8px 16px;
  border-radius: 20px;
  font-size: 0.9rem;
  font-weight: 600;
  margin-bottom: 16px;
  backdrop-filter: blur(10px);
}

.hero-title {
  margin: 0 0 12px 0;
  font-size: 3rem;
  font-weight: 800;
  font-family: 'Poppins', sans-serif;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  background: linear-gradient(45deg, #ffffff, #f0f0f0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.hero-subtitle {
  margin: 0 0 32px 0;
  font-size: 1.2rem;
  opacity: 0.9;
  font-weight: 400;
}

.hero-stats {
  display: flex;
  gap: 32px;
}

.hero-stat {
  text-align: center;
}

.stat-number {
  display: block;
  font-size: 2.5rem;
  font-weight: 700;
  line-height: 1;
  margin-bottom: 4px;
}

.stat-label {
  font-size: 0.9rem;
  opacity: 0.8;
  text-transform: uppercase;
  letter-spacing: 1px;
}

.hero-visual {
  position: relative;
  z-index: 1;
}

.city-illustration {
  width: 120px;
  height: 120px;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  backdrop-filter: blur(10px);
  border: 2px solid rgba(255, 255, 255, 0.2);
}

.city-icon {
  font-size: 4rem !important;
  width: 4rem !important;
  height: 4rem !important;
  color: rgba(255, 255, 255, 0.9);
}

/* Impact Section */
.impact-section {
  margin-bottom: 48px;
}

.section-title {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  margin: 0 0 32px 0;
  color: #2d3748;
  font-size: 2rem;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
  text-align: center;
  position: relative;
  padding-bottom: 16px;
}

.section-title::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 80px;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 2px;
}

.impact-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.impact-card {
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.impact-card:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.impact-card h3 {
  margin: 0 0 20px 0;
  color: #2d3748;
  font-size: 1.3rem;
  font-weight: 600;
  text-align: center;
}

/* City Progress Card */
.city-progress .progress-ring {
  position: relative;
  margin: 0 auto 24px;
  width: 120px;
  height: 120px;
}

.progress-svg {
  transform: rotate(-90deg);
}

.progress-bg {
  fill: none;
  stroke: #e2e8f0;
  stroke-width: 8;
}

.progress-fill {
  fill: none;
  stroke: #667eea;
  stroke-width: 8;
  stroke-linecap: round;
  transition: stroke-dasharray 1s ease-in-out;
}

.progress-text {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}

.progress-value {
  display: block;
  font-size: 1.5rem;
  font-weight: 700;
  color: #2d3748;
}

.progress-label {
  font-size: 0.8rem;
  color: #718096;
}

.progress-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  padding: 8px 0;
  border-bottom: 1px solid #e2e8f0;
}

.detail-item:last-child {
  border-bottom: none;
}

/* Community Feed Card */
.community-feed .feed-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.feed-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  background: #f7fafc;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.feed-item:hover {
  background: #edf2f7;
  transform: translateX(4px);
}

.feed-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  background: linear-gradient(135deg, #667eea, #764ba2);
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  flex-shrink: 0;
}

.feed-content {
  flex: 1;
}

.feed-text {
  margin: 0 0 4px 0;
  font-size: 0.9rem;
  color: #2d3748;
  font-weight: 500;
}

.feed-time {
  font-size: 0.8rem;
  color: #718096;
}

.feed-points {
  background: linear-gradient(135deg, #48bb78, #38a169);
  color: white;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
}

.view-all-btn {
  width: 100%;
  margin-top: 8px;
}

/* Leaderboard Card */
.leaderboard .leaderboard-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.leader-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: 12px;
  transition: all 0.2s ease;
}

.leader-item:hover {
  background: #f7fafc;
}

.leader-item.current-user {
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border: 2px solid rgba(102, 126, 234, 0.3);
}

.leader-rank {
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  border-radius: 50%;
  background: #e2e8f0;
  color: #4a5568;
}

.trophy-icon {
  font-size: 1.5rem !important;
  width: 1.5rem !important;
  height: 1.5rem !important;
}

.rank-number {
  font-size: 0.9rem;
}

.leader-info {
  flex: 1;
}

.leader-name {
  display: block;
  font-weight: 600;
  color: #2d3748;
  font-size: 0.9rem;
}

.leader-city {
  font-size: 0.8rem;
  color: #718096;
}

.leader-points {
  font-weight: 700;
  color: #667eea;
  font-size: 0.9rem;
}

/* Events Section */
.events-section {
  margin-bottom: 48px;
}

.events-carousel {
  overflow: hidden;
}

.events-scroll {
  display: flex;
  gap: 24px;
  overflow-x: auto;
  padding-bottom: 16px;
  scroll-behavior: smooth;
}

.events-scroll::-webkit-scrollbar {
  height: 8px;
}

.events-scroll::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

.events-scroll::-webkit-scrollbar-thumb {
  background: #667eea;
  border-radius: 4px;
}

.event-card {
  min-width: 320px;
  border-radius: 16px;
  overflow: hidden;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.event-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.event-image {
  height: 160px;
  background-size: cover;
  background-position: center;
  position: relative;
  background-color: #e2e8f0;
}

.event-badge {
  position: absolute;
  top: 12px;
  right: 12px;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 0.8rem;
  font-weight: 600;
  color: white;
  text-transform: uppercase;
}

.badge-environnement {
  background: linear-gradient(135deg, #48bb78, #38a169);
}

.badge-culture {
  background: linear-gradient(135deg, #ed8936, #dd6b20);
}

.badge-éducation {
  background: linear-gradient(135deg, #4299e1, #3182ce);
}

.event-card h4 {
  margin: 0 0 8px 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
}

.event-description {
  margin: 0 0 16px 0;
  color: #718096;
  font-size: 0.9rem;
  line-height: 1.4;
}

.event-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 16px;
}

.event-detail {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.8rem;
  color: #4a5568;
}

.event-detail mat-icon {
  font-size: 1rem !important;
  width: 1rem !important;
  height: 1rem !important;
  color: #667eea;
}

.event-join-btn {
  width: 100%;
  height: 36px;
  font-size: 0.9rem;
}

/* Partners Section */
.partners-section {
  margin-bottom: 48px;
}

.partners-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.partner-card {
  border-radius: 16px;
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.partner-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.partner-header {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px 20px 0;
}

.partner-logo {
  width: 60px;
  height: 60px;
  border-radius: 12px;
  object-fit: cover;
  background: #f7fafc;
}

.partner-info h4 {
  margin: 0 0 4px 0;
  color: #2d3748;
  font-size: 1.1rem;
  font-weight: 600;
}

.partner-category {
  margin: 0 0 8px 0;
  color: #718096;
  font-size: 0.9rem;
}

.partner-rating {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 0.8rem;
  color: #718096;
}

.star-icon {
  font-size: 1rem !important;
  width: 1rem !important;
  height: 1rem !important;
  color: #ffd700;
}

.partner-offer {
  margin: 0 0 16px 0;
  color: #2d3748;
  font-weight: 500;
  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  padding: 12px;
  border-radius: 8px;
  font-size: 0.9rem;
}

.partner-actions {
  display: flex;
  gap: 8px;
}

.partner-btn {
  flex: 1;
  height: 36px;
  font-size: 0.8rem;
}

/* Actions Hub */
.actions-hub {
  margin-bottom: 48px;
}

.actions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 32px;
}

.action-category {
  background: white;
  border-radius: 16px;
  padding: 24px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.category-title {
  margin: 0 0 20px 0;
  color: #2d3748;
  font-size: 1.2rem;
  font-weight: 600;
  text-align: center;
  padding-bottom: 12px;
  border-bottom: 2px solid #e2e8f0;
}

.category-actions {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.action-btn {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  border-radius: 12px;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.action-btn:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.action-btn span {
  flex: 1;
  text-align: left;
  margin-left: 12px;
  font-weight: 500;
}

.action-points {
  background: rgba(255, 255, 255, 0.9);
  color: #2d3748;
  padding: 4px 8px;
  border-radius: 8px;
  font-size: 0.8rem;
  font-weight: 600;
}

/* Challenges Section */
.challenges-section {
  margin-bottom: 48px;
}

.challenges-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
  gap: 24px;
}

.challenge-card {
  border-radius: 16px;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.challenge-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.challenge-header {
  padding: 24px;
  text-align: center;
  color: white;
  position: relative;
}

.challenge-icon {
  font-size: 3rem !important;
  width: 3rem !important;
  height: 3rem !important;
  margin-bottom: 12px;
}

.challenge-header h4 {
  margin: 0;
  font-size: 1.3rem;
  font-weight: 600;
}

.challenge-description {
  margin: 0 0 20px 0;
  color: #4a5568;
  line-height: 1.5;
}

.challenge-progress {
  margin-bottom: 16px;
}

.progress-bar {
  width: 100%;
  height: 8px;
  background: #e2e8f0;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 8px;
}

.progress-fill {
  height: 100%;
  background: linear-gradient(90deg, #667eea, #764ba2);
  border-radius: 4px;
  transition: width 1s ease-in-out;
}

.progress-text {
  text-align: center;
  font-size: 0.9rem;
  color: #4a5568;
  font-weight: 600;
}

.challenge-reward {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
  padding: 12px;
  background: #f7fafc;
  border-radius: 8px;
  font-size: 0.9rem;
  color: #2d3748;
}

/* City Comparison Section */
.comparison-section {
  margin-bottom: 48px;
}

.comparison-card {
  border-radius: 16px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.comparison-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 32px;
  padding: 0 20px;
}

.city-score {
  text-align: center;
  flex: 1;
}

.city-score.monastir h3 {
  color: #667eea;
}

.city-score.sousse h3 {
  color: #f093fb;
}

.city-score h3 {
  margin: 0 0 8px 0;
  font-size: 1.5rem;
  font-weight: 700;
}

.city-score .score {
  font-size: 3rem;
  font-weight: 800;
  margin-bottom: 8px;
  background: linear-gradient(135deg, #667eea, #764ba2);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.city-score.sousse .score {
  background: linear-gradient(135deg, #f093fb, #f5576c);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.city-score p {
  margin: 0;
  color: #718096;
  font-size: 0.9rem;
}

.vs-divider {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 0 20px;
}

.vs-divider mat-icon {
  font-size: 2rem !important;
  width: 2rem !important;
  height: 2rem !important;
  color: #667eea;
}

.vs-divider span {
  font-weight: 700;
  color: #2d3748;
  font-size: 1.2rem;
}

.comparison-stats {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.stat-comparison {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.stat-name {
  font-weight: 600;
  color: #2d3748;
  font-size: 0.9rem;
  text-align: center;
}

.stat-bars {
  display: flex;
  gap: 8px;
  align-items: center;
}

.stat-bar {
  height: 24px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: white;
  font-size: 0.8rem;
  font-weight: 600;
  min-width: 60px;
  transition: all 0.3s ease;
}

.stat-bar.monastir {
  background: linear-gradient(135deg, #667eea, #764ba2);
}

.stat-bar.sousse {
  background: linear-gradient(135deg, #f093fb, #f5576c);
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

/* Mobile Responsive */
@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }

  .hero-section {
    flex-direction: column;
    gap: 24px;
    text-align: center;
    padding: 40px 24px;
  }

  .hero-title {
    font-size: 2rem;
  }

  .hero-stats {
    justify-content: center;
    gap: 20px;
  }

  .impact-grid,
  .partners-grid,
  .actions-grid,
  .challenges-grid {
    grid-template-columns: 1fr;
  }

  .events-scroll {
    padding-left: 16px;
  }

  .comparison-header {
    flex-direction: column;
    gap: 20px;
  }

  .vs-divider {
    order: -1;
  }

  .stat-bars {
    flex-direction: column;
    gap: 4px;
  }

  .stat-bar {
    width: 100%;
    justify-content: space-between;
    padding: 0 12px;
  }
}

@media (max-width: 480px) {
  .hero-stats {
    flex-direction: column;
    gap: 16px;
  }

  .section-title {
    font-size: 1.5rem;
  }

  .action-btn {
    flex-direction: column;
    gap: 8px;
    text-align: center;
  }

  .action-btn span {
    margin-left: 0;
  }
}
