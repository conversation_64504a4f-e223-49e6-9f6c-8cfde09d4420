{"ast": null, "code": "import { createErrorClass } from './createErrorClass';\nexport const ArgumentOutOfRangeError = createErrorClass(_super => function ArgumentOutOfRangeErrorImpl() {\n  _super(this);\n  this.name = 'ArgumentOutOfRangeError';\n  this.message = 'argument out of range';\n});", "map": {"version": 3, "names": ["createErrorClass", "ArgumentOutOfRangeError", "_super", "ArgumentOutOfRangeErrorImpl", "name", "message"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/fire/node_modules/rxjs/dist/esm/internal/util/ArgumentOutOfRangeError.js"], "sourcesContent": ["import { createErrorClass } from './createErrorClass';\nexport const ArgumentOutOfRangeError = createErrorClass((_super) => function ArgumentOutOfRangeErrorImpl() {\n    _super(this);\n    this.name = 'ArgumentOutOfRangeError';\n    this.message = 'argument out of range';\n});\n"], "mappings": "AAAA,SAASA,gBAAgB,QAAQ,oBAAoB;AACrD,OAAO,MAAMC,uBAAuB,GAAGD,gBAAgB,CAAEE,MAAM,IAAK,SAASC,2BAA2BA,CAAA,EAAG;EACvGD,MAAM,CAAC,IAAI,CAAC;EACZ,IAAI,CAACE,IAAI,GAAG,yBAAyB;EACrC,IAAI,CAACC,OAAO,GAAG,uBAAuB;AAC1C,CAAC,CAAC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}