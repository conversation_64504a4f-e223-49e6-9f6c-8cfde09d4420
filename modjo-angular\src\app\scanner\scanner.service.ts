import { Injectable } from '@angular/core';
import { Observable, of } from 'rxjs';
import { UserService } from '../user/user.service';
import { UserProfile } from '../models/user.model';
import { PointType } from '../models/point.model';

@Injectable({
  providedIn: 'root'
})
export class ScannerService {
  constructor(private userService: UserService) {}

  // Parse KnowMe QR code URL
  parseKnowMeUrl(url: string): string | null {
    try {
      // Extract KnowMe ID from URL
      // This is a simplified example - actual implementation would depend on KnowMe URL format
      const urlObj = new URL(url);
      if (urlObj.hostname.includes('knowme')) {
        // Extract ID from path or query parameter
        const pathParts = urlObj.pathname.split('/');
        if (pathParts.length > 1) {
          return pathParts[pathParts.length - 1];
        }
        
        // Try to get from query parameter
        const id = urlObj.searchParams.get('id');
        if (id) {
          return id;
        }
      }
      return null;
    } catch (error) {
      console.error('Error parsing KnowMe URL:', error);
      return null;
    }
  }

  // Get user profile from KnowMe ID
  getUserFromKnowMeId(knowMeId: string): Observable<UserProfile | null> {
    return this.userService.getUserByKnowMeId(knowMeId);
  }

  // Award points to a provider
  async awardPoints(
    userId: string,
    providerId: string,
    points: number,
    description: string
  ): Promise<void> {
    await this.userService.addPoints(
      providerId,
      userId,
      points,
      description,
      PointType.SCAN
    );
  }

  // Mock function to get KnowMe profile data
  // In a real implementation, this would call the KnowMe API
  getKnowMeProfileData(knowMeId: string): Observable<any> {
    // Mock data
    return of({
      id: knowMeId,
      name: 'John Doe',
      profession: 'Coiffeur',
      location: 'Monastir',
      skills: ['Coupe homme', 'Barbe', 'Coloration'],
      rating: 4.5,
      reviews: 27
    });
  }
}
