import { Component, OnInit } from '@angular/core';
import { ActivatedRoute, Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { RewardsService } from '../../../../core/services/rewards.service';
import { AuthService } from '../../../../core/services/auth.service';
import { Reward, Partner, User } from '../../../../core/models';
import { Observable, combineLatest } from 'rxjs';
import { map, switchMap } from 'rxjs/operators';

@Component({
  selector: 'app-reward-detail',
  templateUrl: './reward-detail.component.html',
  styleUrls: ['./reward-detail.component.css']
})
export class RewardDetailComponent implements OnInit {
  reward$: Observable<Reward | null>;
  partner$: Observable<Partner | null>;
  currentUser: User | null = null;
  isExchanging = false;

  constructor(
    private route: ActivatedRoute,
    private router: Router,
    private rewardsService: RewardsService,
    private authService: AuthService,
    private snackBar: MatSnackBar
  ) {
    this.reward$ = this.route.params.pipe(
      switchMap(params => this.rewardsService.getRewardById(params['id']))
    );

    this.partner$ = this.reward$.pipe(
      switchMap(reward => 
        reward ? this.rewardsService.getPartnerById(reward.partnerId) : [null]
      )
    );
  }

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.currentUser = user;
    });
  }

  canAfford(reward: Reward): boolean {
    return this.currentUser ? this.currentUser.points >= reward.pointsRequired : false;
  }

  async exchangeReward(reward: Reward): Promise<void> {
    if (!this.currentUser) {
      this.snackBar.open('Vous devez être connecté pour échanger une récompense', 'Fermer', { duration: 3000 });
      return;
    }

    if (!this.canAfford(reward)) {
      this.snackBar.open('Points insuffisants pour cette récompense', 'Fermer', { duration: 3000 });
      return;
    }

    if (reward.availableQuantity !== undefined && reward.availableQuantity <= 0) {
      this.snackBar.open('Cette récompense n\'est plus disponible', 'Fermer', { duration: 3000 });
      return;
    }

    this.isExchanging = true;

    try {
      const exchange = await this.rewardsService.exchangeReward(reward.id, this.currentUser.uid);
      
      this.snackBar.open(
        `Récompense échangée avec succès! Code d'échange: ${exchange.exchangeCode}`, 
        'Fermer', 
        { duration: 8000 }
      );
      
      // Note: In a real app, you would refresh user data here
      
      // Navigate to exchange history or profile
      this.router.navigate(['/profile'], { fragment: 'exchanges' });
      
    } catch (error: any) {
      console.error('Exchange error:', error);
      this.snackBar.open(error.message || 'Erreur lors de l\'échange', 'Fermer', { duration: 3000 });
    } finally {
      this.isExchanging = false;
    }
  }

  goBack(): void {
    this.router.navigate(['/rewards']);
  }

  getCategoryIcon(category: string): string {
    const iconMap: { [key: string]: string } = {
      'FOOD': 'restaurant',
      'SHOPPING': 'shopping_bag',
      'ENTERTAINMENT': 'movie',
      'SERVICES': 'build',
      'HEALTH': 'local_hospital',
      'EDUCATION': 'school',
      'TRANSPORT': 'directions_car',
      'OTHER': 'more_horiz'
    };
    return iconMap[category] || 'category';
  }

  getCategoryLabel(category: string): string {
    const labelMap: { [key: string]: string } = {
      'FOOD': 'Restauration',
      'SHOPPING': 'Shopping',
      'ENTERTAINMENT': 'Divertissement',
      'SERVICES': 'Services',
      'HEALTH': 'Santé',
      'EDUCATION': 'Éducation',
      'TRANSPORT': 'Transport',
      'OTHER': 'Autre'
    };
    return labelMap[category] || category;
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: 'long',
      year: 'numeric'
    });
  }

  isExpiringSoon(reward: Reward): boolean {
    if (!reward.validUntil) return false;
    
    const now = new Date();
    const validUntil = new Date(reward.validUntil);
    const daysUntilExpiry = Math.ceil((validUntil.getTime() - now.getTime()) / (1000 * 60 * 60 * 24));
    
    return daysUntilExpiry <= 7 && daysUntilExpiry > 0;
  }

  getAvailabilityText(reward: Reward): string {
    if (!reward.availableQuantity) {
      return 'Disponible';
    }
    
    if (reward.availableQuantity <= 0) {
      return 'Épuisé';
    }
    
    if (reward.availableQuantity <= 5) {
      return `Plus que ${reward.availableQuantity} disponible(s)`;
    }
    
    return `${reward.availableQuantity} disponible(s)`;
  }

  getAvailabilityColor(reward: Reward): string {
    if (!reward.availableQuantity || reward.availableQuantity > 5) {
      return 'primary';
    }
    
    if (reward.availableQuantity <= 0) {
      return 'warn';
    }
    
    return 'accent';
  }

  shareReward(reward: Reward): void {
    if (navigator.share) {
      navigator.share({
        title: `Récompense Modjo: ${reward.title}`,
        text: `Découvrez cette récompense sur Modjo: ${reward.description}`,
        url: window.location.href
      }).catch(console.error);
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href).then(() => {
        this.snackBar.open('Lien copié dans le presse-papiers', 'Fermer', { duration: 2000 });
      });
    }
  }

  contactPartner(partner: Partner): void {
    if (partner.phone) {
      window.open(`tel:${partner.phone}`, '_blank');
    } else if (partner.email) {
      window.open(`mailto:${partner.email}`, '_blank');
    }
  }

  getDirections(partner: Partner): void {
    if (partner.location) {
      const url = `https://www.google.com/maps/dir/?api=1&destination=${partner.location.latitude},${partner.location.longitude}`;
      window.open(url, '_blank');
    } else if (partner.address) {
      const url = `https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(partner.address)}`;
      window.open(url, '_blank');
    }
  }
}
