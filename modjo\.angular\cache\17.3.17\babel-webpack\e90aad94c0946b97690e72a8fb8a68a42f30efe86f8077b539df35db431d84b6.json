{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../../core/services/auth.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/common\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/form-field\";\nimport * as i8 from \"@angular/material/input\";\nimport * as i9 from \"@angular/material/button\";\nimport * as i10 from \"@angular/material/icon\";\nimport * as i11 from \"@angular/material/progress-spinner\";\nimport * as i12 from \"@angular/material/divider\";\nfunction LoginComponent_mat_spinner_30_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 20);\n  }\n}\nfunction LoginComponent_span_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\")(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Se connecter \");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class LoginComponent {\n  constructor(fb, authService, router, route, snackBar) {\n    this.fb = fb;\n    this.authService = authService;\n    this.router = router;\n    this.route = route;\n    this.snackBar = snackBar;\n    this.isLoading = false;\n    this.hidePassword = true;\n    this.returnUrl = '/dashboard';\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n  ngOnInit() {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate([this.returnUrl]);\n    }\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.loginForm.invalid) {\n        _this.markFormGroupTouched();\n        return;\n      }\n      _this.isLoading = true;\n      const {\n        email,\n        password\n      } = _this.loginForm.value;\n      try {\n        yield _this.authService.login(email, password);\n        _this.snackBar.open('Connexion réussie!', 'Fermer', {\n          duration: 3000\n        });\n        _this.router.navigate([_this.returnUrl]);\n      } catch (error) {\n        console.error('Login error:', error);\n        _this.snackBar.open(_this.getErrorMessage(error), 'Fermer', {\n          duration: 5000\n        });\n      } finally {\n        _this.isLoading = false;\n      }\n    })();\n  }\n  getErrorMessage(error) {\n    if (error.code) {\n      switch (error.code) {\n        case 'auth/user-not-found':\n          return 'Aucun utilisateur trouvé avec cette adresse email.';\n        case 'auth/wrong-password':\n          return 'Mot de passe incorrect.';\n        case 'auth/invalid-email':\n          return 'Adresse email invalide.';\n        case 'auth/user-disabled':\n          return 'Ce compte a été désactivé.';\n        case 'auth/too-many-requests':\n          return 'Trop de tentatives. Veuillez réessayer plus tard.';\n        default:\n          return 'Erreur de connexion. Veuillez réessayer.';\n      }\n    }\n    return 'Erreur de connexion. Veuillez réessayer.';\n  }\n  markFormGroupTouched() {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getFieldError(fieldName) {\n    const field = this.loginForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) {\n        return `${fieldName === 'email' ? 'Email' : 'Mot de passe'} requis`;\n      }\n      if (field.errors['email']) {\n        return 'Format email invalide';\n      }\n      if (field.errors['minlength']) {\n        return 'Mot de passe trop court (min. 6 caractères)';\n      }\n    }\n    return '';\n  }\n  // Demo login methods\n  loginAsUser() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      _this2.loginForm.patchValue({\n        email: '<EMAIL>',\n        password: 'password123'\n      });\n      yield _this2.onSubmit();\n    })();\n  }\n  loginAsValidator() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.loginForm.patchValue({\n        email: '<EMAIL>',\n        password: 'password123'\n      });\n      yield _this3.onSubmit();\n    })();\n  }\n  loginAsAdmin() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      _this4.loginForm.patchValue({\n        email: '<EMAIL>',\n        password: 'password123'\n      });\n      yield _this4.onSubmit();\n    })();\n  }\n  static {\n    this.ɵfac = function LoginComponent_Factory(t) {\n      return new (t || LoginComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.AuthService), i0.ɵɵdirectiveInject(i3.Router), i0.ɵɵdirectiveInject(i3.ActivatedRoute), i0.ɵɵdirectiveInject(i4.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: LoginComponent,\n      selectors: [[\"app-login\"]],\n      decls: 54,\n      vars: 10,\n      consts: [[1, \"login-container\"], [1, \"login-card\"], [1, \"login-header\"], [1, \"logo\"], [1, \"login-form\", 3, \"ngSubmit\", \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"matInput\", \"\", \"type\", \"email\", \"formControlName\", \"email\", \"placeholder\", \"<EMAIL>\"], [\"matSuffix\", \"\"], [\"matInput\", \"\", \"formControlName\", \"password\", \"placeholder\", \"Votre mot de passe\", 3, \"type\"], [\"mat-icon-button\", \"\", \"matSuffix\", \"\", \"type\", \"button\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"type\", \"submit\", 1, \"full-width\", \"login-button\", 3, \"disabled\"], [\"diameter\", \"24\", \"color\", \"accent\", 4, \"ngIf\"], [4, \"ngIf\"], [1, \"register-link\"], [\"routerLink\", \"/auth/register\", 1, \"link\"], [1, \"demo-section\"], [1, \"demo-buttons\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"demo-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"accent\", 1, \"demo-button\", 3, \"click\"], [\"mat-stroked-button\", \"\", \"color\", \"warn\", 1, \"demo-button\", 3, \"click\"], [\"diameter\", \"24\", \"color\", \"accent\"]],\n      template: function LoginComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"mat-card\", 1)(2, \"mat-card-header\")(3, \"div\", 2)(4, \"div\", 3);\n          i0.ɵɵtext(5, \"\\uD83C\\uDF1F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"h1\");\n          i0.ɵɵtext(7, \"Modjo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(8, \"p\");\n          i0.ɵɵtext(9, \"Connectez-vous \\u00E0 votre compte\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(10, \"mat-card-content\")(11, \"form\", 4);\n          i0.ɵɵlistener(\"ngSubmit\", function LoginComponent_Template_form_ngSubmit_11_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵelementStart(12, \"mat-form-field\", 5)(13, \"mat-label\");\n          i0.ɵɵtext(14, \"Adresse email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(15, \"input\", 6);\n          i0.ɵɵelementStart(16, \"mat-icon\", 7);\n          i0.ɵɵtext(17, \"email\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"mat-error\");\n          i0.ɵɵtext(19);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(20, \"mat-form-field\", 5)(21, \"mat-label\");\n          i0.ɵɵtext(22, \"Mot de passe\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(23, \"input\", 8);\n          i0.ɵɵelementStart(24, \"button\", 9);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_24_listener() {\n            return ctx.hidePassword = !ctx.hidePassword;\n          });\n          i0.ɵɵelementStart(25, \"mat-icon\");\n          i0.ɵɵtext(26);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"mat-error\");\n          i0.ɵɵtext(28);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(29, \"button\", 10);\n          i0.ɵɵtemplate(30, LoginComponent_mat_spinner_30_Template, 1, 0, \"mat-spinner\", 11)(31, LoginComponent_span_31_Template, 4, 0, \"span\", 12);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\", 13)(33, \"p\");\n          i0.ɵɵtext(34, \"Pas encore de compte? \");\n          i0.ɵɵelementStart(35, \"a\", 14);\n          i0.ɵɵtext(36, \"Cr\\u00E9er un compte\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(37, \"div\", 15);\n          i0.ɵɵelement(38, \"mat-divider\");\n          i0.ɵɵelementStart(39, \"h3\");\n          i0.ɵɵtext(40, \"Connexion d\\u00E9mo\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(41, \"div\", 16)(42, \"button\", 17);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_42_listener() {\n            return ctx.loginAsUser();\n          });\n          i0.ɵɵelementStart(43, \"mat-icon\");\n          i0.ɵɵtext(44, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(45, \" Utilisateur \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(46, \"button\", 18);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_46_listener() {\n            return ctx.loginAsValidator();\n          });\n          i0.ɵɵelementStart(47, \"mat-icon\");\n          i0.ɵɵtext(48, \"verified\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(49, \" Validateur \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(50, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function LoginComponent_Template_button_click_50_listener() {\n            return ctx.loginAsAdmin();\n          });\n          i0.ɵɵelementStart(51, \"mat-icon\");\n          i0.ɵɵtext(52, \"admin_panel_settings\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(53, \" Admin \");\n          i0.ɵɵelementEnd()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(11);\n          i0.ɵɵproperty(\"formGroup\", ctx.loginForm);\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"email\"));\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"type\", ctx.hidePassword ? \"password\" : \"text\");\n          i0.ɵɵadvance();\n          i0.ɵɵattribute(\"aria-label\", \"Hide password\")(\"aria-pressed\", ctx.hidePassword);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.hidePassword ? \"visibility_off\" : \"visibility\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"password\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i5.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i3.RouterLink, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i7.MatFormField, i7.MatLabel, i7.MatError, i7.MatSuffix, i8.MatInput, i9.MatButton, i9.MatIconButton, i10.MatIcon, i11.MatProgressSpinner, i12.MatDivider],\n      styles: [\".login-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 100vh;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.login-container[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\\n    radial-gradient(circle at 40% 40%, rgba(255, 255, 255, 0.05) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n.login-card[_ngcontent-%COMP%] {\\n  width: 100%;\\n  max-width: 450px;\\n  padding: 40px;\\n  border-radius: 24px;\\n  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  position: relative;\\n  z-index: 1;\\n  animation: slideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.login-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 40px;\\n  animation: fadeIn 1s cubic-bezier(0.4, 0, 0.2, 1) 0.3s both;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  width: 80px;\\n  height: 80px;\\n  margin-bottom: 24px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  border-radius: 20px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 2rem;\\n  color: white;\\n  margin-left: auto;\\n  margin-right: auto;\\n  box-shadow: 0 10px 30px rgba(103, 126, 234, 0.3);\\n  animation: bounceIn 1s cubic-bezier(0.68, -0.55, 0.265, 1.55) 0.5s both;\\n}\\n\\n.login-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  color: #2d3748;\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  font-family: 'Poppins', sans-serif;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.login-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #718096;\\n  font-size: 1.1rem;\\n  font-weight: 500;\\n}\\n\\n.login-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 24px;\\n  animation: slideUp 0.8s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.login-button[_ngcontent-%COMP%] {\\n  height: 56px;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  margin-top: 16px;\\n  border-radius: 16px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  box-shadow: 0 8px 25px rgba(103, 126, 234, 0.4);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  text-transform: none;\\n  letter-spacing: 0.5px;\\n}\\n\\n.login-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 12px 35px rgba(103, 126, 234, 0.6);\\n}\\n\\n.login-button[_ngcontent-%COMP%]:disabled {\\n  opacity: 0.7;\\n  transform: none;\\n  box-shadow: 0 4px 15px rgba(103, 126, 234, 0.2);\\n}\\n\\n.register-link[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-top: 16px;\\n}\\n\\n.register-link[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.link[_ngcontent-%COMP%] {\\n  color: #667eea;\\n  text-decoration: none;\\n  font-weight: 500;\\n}\\n\\n.link[_ngcontent-%COMP%]:hover {\\n  text-decoration: underline;\\n}\\n\\n.demo-section[_ngcontent-%COMP%] {\\n  margin-top: 40px;\\n  text-align: center;\\n  animation: fadeIn 1s cubic-bezier(0.4, 0, 0.2, 1) 0.8s both;\\n}\\n\\n.demo-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 24px 0;\\n  color: #4a5568;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n  font-family: 'Poppins', sans-serif;\\n  position: relative;\\n}\\n\\n.demo-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before, .demo-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  top: 50%;\\n  width: 60px;\\n  height: 1px;\\n  background: linear-gradient(90deg, transparent, #cbd5e0, transparent);\\n}\\n\\n.demo-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::before {\\n  left: -80px;\\n}\\n\\n.demo-section[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%]::after {\\n  right: -80px;\\n}\\n\\n.demo-buttons[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.demo-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  height: 48px;\\n  font-size: 1rem;\\n  font-weight: 500;\\n  border-radius: 12px;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  background: rgba(255, 255, 255, 0.8);\\n  border: 2px solid rgba(103, 126, 234, 0.2);\\n}\\n\\n.demo-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);\\n  border-color: rgba(103, 126, 234, 0.4);\\n  background: rgba(255, 255, 255, 1);\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .login-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  \\n  .login-card[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  \\n  .logo[_ngcontent-%COMP%] {\\n    width: 48px;\\n    height: 48px;\\n  }\\n  \\n  .login-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "i0", "ɵɵelement", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "LoginComponent", "constructor", "fb", "authService", "router", "route", "snackBar", "isLoading", "hidePassword", "returnUrl", "loginForm", "group", "email", "required", "password", "<PERSON><PERSON><PERSON><PERSON>", "ngOnInit", "snapshot", "queryParams", "isAuthenticated", "navigate", "onSubmit", "_this", "_asyncToGenerator", "invalid", "markFormGroupTouched", "value", "login", "open", "duration", "error", "console", "getErrorMessage", "code", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "getFieldError", "fieldName", "field", "errors", "touched", "loginAsUser", "_this2", "patchValue", "loginAsValidator", "_this3", "loginAsAdmin", "_this4", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "AuthService", "i3", "Router", "ActivatedRoute", "i4", "MatSnackBar", "selectors", "decls", "vars", "consts", "template", "LoginComponent_Template", "rf", "ctx", "ɵɵlistener", "LoginComponent_Template_form_ngSubmit_11_listener", "LoginComponent_Template_button_click_24_listener", "ɵɵtemplate", "LoginComponent_mat_spinner_30_Template", "LoginComponent_span_31_Template", "LoginComponent_Template_button_click_42_listener", "LoginComponent_Template_button_click_46_listener", "LoginComponent_Template_button_click_50_listener", "ɵɵadvance", "ɵɵproperty", "ɵɵtextInterpolate"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\auth\\components\\login\\login.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\auth\\components\\login\\login.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { FormBuilder, FormGroup, Validators } from '@angular/forms';\nimport { Router, ActivatedRoute } from '@angular/router';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { AuthService } from '../../../../core/services/auth.service';\n\n@Component({\n  selector: 'app-login',\n  templateUrl: './login.component.html',\n  styleUrls: ['./login.component.css']\n})\nexport class LoginComponent implements OnInit {\n  loginForm: FormGroup;\n  isLoading = false;\n  hidePassword = true;\n  returnUrl = '/dashboard';\n\n  constructor(\n    private fb: FormBuilder,\n    private authService: AuthService,\n    private router: Router,\n    private route: ActivatedRoute,\n    private snackBar: MatSnackBar\n  ) {\n    this.loginForm = this.fb.group({\n      email: ['', [Validators.required, Validators.email]],\n      password: ['', [Validators.required, Validators.minLength(6)]]\n    });\n  }\n\n  ngOnInit(): void {\n    // Get return URL from route parameters or default to dashboard\n    this.returnUrl = this.route.snapshot.queryParams['returnUrl'] || '/dashboard';\n    \n    // Redirect if already authenticated\n    if (this.authService.isAuthenticated()) {\n      this.router.navigate([this.returnUrl]);\n    }\n  }\n\n  async onSubmit(): Promise<void> {\n    if (this.loginForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.isLoading = true;\n    const { email, password } = this.loginForm.value;\n\n    try {\n      await this.authService.login(email, password);\n      this.snackBar.open('Connexion réussie!', 'Fermer', { duration: 3000 });\n      this.router.navigate([this.returnUrl]);\n    } catch (error: any) {\n      console.error('Login error:', error);\n      this.snackBar.open(\n        this.getErrorMessage(error), \n        'Fermer', \n        { duration: 5000 }\n      );\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  private getErrorMessage(error: any): string {\n    if (error.code) {\n      switch (error.code) {\n        case 'auth/user-not-found':\n          return 'Aucun utilisateur trouvé avec cette adresse email.';\n        case 'auth/wrong-password':\n          return 'Mot de passe incorrect.';\n        case 'auth/invalid-email':\n          return 'Adresse email invalide.';\n        case 'auth/user-disabled':\n          return 'Ce compte a été désactivé.';\n        case 'auth/too-many-requests':\n          return 'Trop de tentatives. Veuillez réessayer plus tard.';\n        default:\n          return 'Erreur de connexion. Veuillez réessayer.';\n      }\n    }\n    return 'Erreur de connexion. Veuillez réessayer.';\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.loginForm.controls).forEach(key => {\n      const control = this.loginForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.loginForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) {\n        return `${fieldName === 'email' ? 'Email' : 'Mot de passe'} requis`;\n      }\n      if (field.errors['email']) {\n        return 'Format email invalide';\n      }\n      if (field.errors['minlength']) {\n        return 'Mot de passe trop court (min. 6 caractères)';\n      }\n    }\n    return '';\n  }\n\n  // Demo login methods\n  async loginAsUser(): Promise<void> {\n    this.loginForm.patchValue({\n      email: '<EMAIL>',\n      password: 'password123'\n    });\n    await this.onSubmit();\n  }\n\n  async loginAsValidator(): Promise<void> {\n    this.loginForm.patchValue({\n      email: '<EMAIL>',\n      password: 'password123'\n    });\n    await this.onSubmit();\n  }\n\n  async loginAsAdmin(): Promise<void> {\n    this.loginForm.patchValue({\n      email: '<EMAIL>',\n      password: 'password123'\n    });\n    await this.onSubmit();\n  }\n}\n", "<div class=\"login-container\">\n  <mat-card class=\"login-card\">\n    <mat-card-header>\n      <div class=\"login-header\">\n        <div class=\"logo\">🌟</div>\n        <h1>Modjo</h1>\n        <p>Connectez-vous à votre compte</p>\n      </div>\n    </mat-card-header>\n\n    <mat-card-content>\n      <form [formGroup]=\"loginForm\" (ngSubmit)=\"onSubmit()\" class=\"login-form\">\n        <!-- Email field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Adresse email</mat-label>\n          <input matInput \n                 type=\"email\" \n                 formControlName=\"email\"\n                 placeholder=\"<EMAIL>\">\n          <mat-icon matSuffix>email</mat-icon>\n          <mat-error>{{ getFieldError('email') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Password field -->\n        <mat-form-field appearance=\"outline\" class=\"full-width\">\n          <mat-label>Mot de passe</mat-label>\n          <input matInput \n                 [type]=\"hidePassword ? 'password' : 'text'\"\n                 formControlName=\"password\"\n                 placeholder=\"Votre mot de passe\">\n          <button mat-icon-button \n                  matSuffix \n                  type=\"button\"\n                  (click)=\"hidePassword = !hidePassword\"\n                  [attr.aria-label]=\"'Hide password'\"\n                  [attr.aria-pressed]=\"hidePassword\">\n            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>\n          </button>\n          <mat-error>{{ getFieldError('password') }}</mat-error>\n        </mat-form-field>\n\n        <!-- Submit button -->\n        <button mat-raised-button\n                color=\"primary\"\n                type=\"submit\"\n                class=\"full-width login-button\"\n                [disabled]=\"isLoading\">\n          <mat-spinner diameter=\"24\" color=\"accent\" *ngIf=\"isLoading\"></mat-spinner>\n          <span *ngIf=\"!isLoading\">\n            <mat-icon>login</mat-icon>\n            Se connecter\n          </span>\n        </button>\n\n        <!-- Register link -->\n        <div class=\"register-link\">\n          <p>Pas encore de compte? \n            <a routerLink=\"/auth/register\" class=\"link\">Créer un compte</a>\n          </p>\n        </div>\n      </form>\n\n      <!-- Demo login buttons -->\n      <div class=\"demo-section\">\n        <mat-divider></mat-divider>\n        <h3>Connexion démo</h3>\n        <div class=\"demo-buttons\">\n          <button mat-stroked-button \n                  color=\"primary\" \n                  (click)=\"loginAsUser()\"\n                  class=\"demo-button\">\n            <mat-icon>person</mat-icon>\n            Utilisateur\n          </button>\n          \n          <button mat-stroked-button \n                  color=\"accent\" \n                  (click)=\"loginAsValidator()\"\n                  class=\"demo-button\">\n            <mat-icon>verified</mat-icon>\n            Validateur\n          </button>\n          \n          <button mat-stroked-button \n                  color=\"warn\" \n                  (click)=\"loginAsAdmin()\"\n                  class=\"demo-button\">\n            <mat-icon>admin_panel_settings</mat-icon>\n            Admin\n          </button>\n        </div>\n      </div>\n    </mat-card-content>\n  </mat-card>\n</div>\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;;;;;;;;;;;;;;;;IC8CzDC,EAAA,CAAAC,SAAA,sBAA0E;;;;;IAExED,EADF,CAAAE,cAAA,WAAyB,eACb;IAAAF,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,qBACF;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;ADxCjB,OAAM,MAAOC,cAAc;EAMzBC,YACUC,EAAe,EACfC,WAAwB,EACxBC,MAAc,EACdC,KAAqB,EACrBC,QAAqB;IAJrB,KAAAJ,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,KAAK,GAALA,KAAK;IACL,KAAAC,QAAQ,GAARA,QAAQ;IATlB,KAAAC,SAAS,GAAG,KAAK;IACjB,KAAAC,YAAY,GAAG,IAAI;IACnB,KAAAC,SAAS,GAAG,YAAY;IAStB,IAAI,CAACC,SAAS,GAAG,IAAI,CAACR,EAAE,CAACS,KAAK,CAAC;MAC7BC,KAAK,EAAE,CAAC,EAAE,EAAE,CAAClB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACkB,KAAK,CAAC,CAAC;MACpDE,QAAQ,EAAE,CAAC,EAAE,EAAE,CAACpB,UAAU,CAACmB,QAAQ,EAAEnB,UAAU,CAACqB,SAAS,CAAC,CAAC,CAAC,CAAC;KAC9D,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACP,SAAS,GAAG,IAAI,CAACJ,KAAK,CAACY,QAAQ,CAACC,WAAW,CAAC,WAAW,CAAC,IAAI,YAAY;IAE7E;IACA,IAAI,IAAI,CAACf,WAAW,CAACgB,eAAe,EAAE,EAAE;MACtC,IAAI,CAACf,MAAM,CAACgB,QAAQ,CAAC,CAAC,IAAI,CAACX,SAAS,CAAC,CAAC;;EAE1C;EAEMY,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,IAAID,KAAI,CAACZ,SAAS,CAACc,OAAO,EAAE;QAC1BF,KAAI,CAACG,oBAAoB,EAAE;QAC3B;;MAGFH,KAAI,CAACf,SAAS,GAAG,IAAI;MACrB,MAAM;QAAEK,KAAK;QAAEE;MAAQ,CAAE,GAAGQ,KAAI,CAACZ,SAAS,CAACgB,KAAK;MAEhD,IAAI;QACF,MAAMJ,KAAI,CAACnB,WAAW,CAACwB,KAAK,CAACf,KAAK,EAAEE,QAAQ,CAAC;QAC7CQ,KAAI,CAAChB,QAAQ,CAACsB,IAAI,CAAC,oBAAoB,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACtEP,KAAI,CAAClB,MAAM,CAACgB,QAAQ,CAAC,CAACE,KAAI,CAACb,SAAS,CAAC,CAAC;OACvC,CAAC,OAAOqB,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,cAAc,EAAEA,KAAK,CAAC;QACpCR,KAAI,CAAChB,QAAQ,CAACsB,IAAI,CAChBN,KAAI,CAACU,eAAe,CAACF,KAAK,CAAC,EAC3B,QAAQ,EACR;UAAED,QAAQ,EAAE;QAAI,CAAE,CACnB;OACF,SAAS;QACRP,KAAI,CAACf,SAAS,GAAG,KAAK;;IACvB;EACH;EAEQyB,eAAeA,CAACF,KAAU;IAChC,IAAIA,KAAK,CAACG,IAAI,EAAE;MACd,QAAQH,KAAK,CAACG,IAAI;QAChB,KAAK,qBAAqB;UACxB,OAAO,oDAAoD;QAC7D,KAAK,qBAAqB;UACxB,OAAO,yBAAyB;QAClC,KAAK,oBAAoB;UACvB,OAAO,yBAAyB;QAClC,KAAK,oBAAoB;UACvB,OAAO,4BAA4B;QACrC,KAAK,wBAAwB;UAC3B,OAAO,mDAAmD;QAC5D;UACE,OAAO,0CAA0C;;;IAGvD,OAAO,0CAA0C;EACnD;EAEQR,oBAAoBA,CAAA;IAC1BS,MAAM,CAACC,IAAI,CAAC,IAAI,CAACzB,SAAS,CAAC0B,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACjD,MAAMC,OAAO,GAAG,IAAI,CAAC7B,SAAS,CAAC8B,GAAG,CAACF,GAAG,CAAC;MACvCC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAACC,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAAClC,SAAS,CAAC8B,GAAG,CAACG,SAAS,CAAC;IAC3C,IAAIC,KAAK,EAAEC,MAAM,IAAID,KAAK,CAACE,OAAO,EAAE;MAClC,IAAIF,KAAK,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,GAAGF,SAAS,KAAK,OAAO,GAAG,OAAO,GAAG,cAAc,SAAS;;MAErE,IAAIC,KAAK,CAACC,MAAM,CAAC,OAAO,CAAC,EAAE;QACzB,OAAO,uBAAuB;;MAEhC,IAAID,KAAK,CAACC,MAAM,CAAC,WAAW,CAAC,EAAE;QAC7B,OAAO,6CAA6C;;;IAGxD,OAAO,EAAE;EACX;EAEA;EACME,WAAWA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAAzB,iBAAA;MACfyB,MAAI,CAACtC,SAAS,CAACuC,UAAU,CAAC;QACxBrC,KAAK,EAAE,eAAe;QACtBE,QAAQ,EAAE;OACX,CAAC;MACF,MAAMkC,MAAI,CAAC3B,QAAQ,EAAE;IAAC;EACxB;EAEM6B,gBAAgBA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA5B,iBAAA;MACpB4B,MAAI,CAACzC,SAAS,CAACuC,UAAU,CAAC;QACxBrC,KAAK,EAAE,oBAAoB;QAC3BE,QAAQ,EAAE;OACX,CAAC;MACF,MAAMqC,MAAI,CAAC9B,QAAQ,EAAE;IAAC;EACxB;EAEM+B,YAAYA,CAAA;IAAA,IAAAC,MAAA;IAAA,OAAA9B,iBAAA;MAChB8B,MAAI,CAAC3C,SAAS,CAACuC,UAAU,CAAC;QACxBrC,KAAK,EAAE,gBAAgB;QACvBE,QAAQ,EAAE;OACX,CAAC;MACF,MAAMuC,MAAI,CAAChC,QAAQ,EAAE;IAAC;EACxB;;;uBAxHWrB,cAAc,EAAAL,EAAA,CAAA2D,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA7D,EAAA,CAAA2D,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAA/D,EAAA,CAAA2D,iBAAA,CAAAK,EAAA,CAAAC,MAAA,GAAAjE,EAAA,CAAA2D,iBAAA,CAAAK,EAAA,CAAAE,cAAA,GAAAlE,EAAA,CAAA2D,iBAAA,CAAAQ,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAd/D,cAAc;MAAAgE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,wBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCPnB3E,EAJR,CAAAE,cAAA,aAA6B,kBACE,sBACV,aACW,aACN;UAAAF,EAAA,CAAAG,MAAA,mBAAE;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAC1BJ,EAAA,CAAAE,cAAA,SAAI;UAAAF,EAAA,CAAAG,MAAA,YAAK;UAAAH,EAAA,CAAAI,YAAA,EAAK;UACdJ,EAAA,CAAAE,cAAA,QAAG;UAAAF,EAAA,CAAAG,MAAA,yCAA6B;UAEpCH,EAFoC,CAAAI,YAAA,EAAI,EAChC,EACU;UAGhBJ,EADF,CAAAE,cAAA,wBAAkB,eACyD;UAA3CF,EAAA,CAAA6E,UAAA,sBAAAC,kDAAA;YAAA,OAAYF,GAAA,CAAAlD,QAAA,EAAU;UAAA,EAAC;UAGjD1B,EADF,CAAAE,cAAA,yBAAwD,iBAC3C;UAAAF,EAAA,CAAAG,MAAA,qBAAa;UAAAH,EAAA,CAAAI,YAAA,EAAY;UACpCJ,EAAA,CAAAC,SAAA,gBAGqC;UACrCD,EAAA,CAAAE,cAAA,mBAAoB;UAAAF,EAAA,CAAAG,MAAA,aAAK;UAAAH,EAAA,CAAAI,YAAA,EAAW;UACpCJ,EAAA,CAAAE,cAAA,iBAAW;UAAAF,EAAA,CAAAG,MAAA,IAA4B;UACzCH,EADyC,CAAAI,YAAA,EAAY,EACpC;UAIfJ,EADF,CAAAE,cAAA,yBAAwD,iBAC3C;UAAAF,EAAA,CAAAG,MAAA,oBAAY;UAAAH,EAAA,CAAAI,YAAA,EAAY;UACnCJ,EAAA,CAAAC,SAAA,gBAGwC;UACxCD,EAAA,CAAAE,cAAA,iBAK2C;UAFnCF,EAAA,CAAA6E,UAAA,mBAAAE,iDAAA;YAAA,OAAAH,GAAA,CAAA/D,YAAA,IAAA+D,GAAA,CAAA/D,YAAA;UAAA,EAAsC;UAG5Cb,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,IAAoD;UAChEH,EADgE,CAAAI,YAAA,EAAW,EAClE;UACTJ,EAAA,CAAAE,cAAA,iBAAW;UAAAF,EAAA,CAAAG,MAAA,IAA+B;UAC5CH,EAD4C,CAAAI,YAAA,EAAY,EACvC;UAGjBJ,EAAA,CAAAE,cAAA,kBAI+B;UAE7BF,EADA,CAAAgF,UAAA,KAAAC,sCAAA,0BAA4D,KAAAC,+BAAA,mBACnC;UAI3BlF,EAAA,CAAAI,YAAA,EAAS;UAIPJ,EADF,CAAAE,cAAA,eAA2B,SACtB;UAAAF,EAAA,CAAAG,MAAA,8BACD;UAAAH,EAAA,CAAAE,cAAA,aAA4C;UAAAF,EAAA,CAAAG,MAAA,4BAAe;UAGjEH,EAHiE,CAAAI,YAAA,EAAI,EAC7D,EACA,EACD;UAGPJ,EAAA,CAAAE,cAAA,eAA0B;UACxBF,EAAA,CAAAC,SAAA,mBAA2B;UAC3BD,EAAA,CAAAE,cAAA,UAAI;UAAAF,EAAA,CAAAG,MAAA,2BAAc;UAAAH,EAAA,CAAAI,YAAA,EAAK;UAErBJ,EADF,CAAAE,cAAA,eAA0B,kBAII;UADpBF,EAAA,CAAA6E,UAAA,mBAAAM,iDAAA;YAAA,OAASP,GAAA,CAAAxB,WAAA,EAAa;UAAA,EAAC;UAE7BpD,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAC3BJ,EAAA,CAAAG,MAAA,qBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAETJ,EAAA,CAAAE,cAAA,kBAG4B;UADpBF,EAAA,CAAA6E,UAAA,mBAAAO,iDAAA;YAAA,OAASR,GAAA,CAAArB,gBAAA,EAAkB;UAAA,EAAC;UAElCvD,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,gBAAQ;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAC7BJ,EAAA,CAAAG,MAAA,oBACF;UAAAH,EAAA,CAAAI,YAAA,EAAS;UAETJ,EAAA,CAAAE,cAAA,kBAG4B;UADpBF,EAAA,CAAA6E,UAAA,mBAAAQ,iDAAA;YAAA,OAAST,GAAA,CAAAnB,YAAA,EAAc;UAAA,EAAC;UAE9BzD,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,4BAAoB;UAAAH,EAAA,CAAAI,YAAA,EAAW;UACzCJ,EAAA,CAAAG,MAAA,eACF;UAKVH,EALU,CAAAI,YAAA,EAAS,EACL,EACF,EACW,EACV,EACP;;;UAnFMJ,EAAA,CAAAsF,SAAA,IAAuB;UAAvBtF,EAAA,CAAAuF,UAAA,cAAAX,GAAA,CAAA7D,SAAA,CAAuB;UASdf,EAAA,CAAAsF,SAAA,GAA4B;UAA5BtF,EAAA,CAAAwF,iBAAA,CAAAZ,GAAA,CAAA7B,aAAA,UAA4B;UAOhC/C,EAAA,CAAAsF,SAAA,GAA2C;UAA3CtF,EAAA,CAAAuF,UAAA,SAAAX,GAAA,CAAA/D,YAAA,uBAA2C;UAO1Cb,EAAA,CAAAsF,SAAA,EAAmC;;UAE/BtF,EAAA,CAAAsF,SAAA,GAAoD;UAApDtF,EAAA,CAAAwF,iBAAA,CAAAZ,GAAA,CAAA/D,YAAA,mCAAoD;UAErDb,EAAA,CAAAsF,SAAA,GAA+B;UAA/BtF,EAAA,CAAAwF,iBAAA,CAAAZ,GAAA,CAAA7B,aAAA,aAA+B;UAQpC/C,EAAA,CAAAsF,SAAA,EAAsB;UAAtBtF,EAAA,CAAAuF,UAAA,aAAAX,GAAA,CAAAhE,SAAA,CAAsB;UACeZ,EAAA,CAAAsF,SAAA,EAAe;UAAftF,EAAA,CAAAuF,UAAA,SAAAX,GAAA,CAAAhE,SAAA,CAAe;UACnDZ,EAAA,CAAAsF,SAAA,EAAgB;UAAhBtF,EAAA,CAAAuF,UAAA,UAAAX,GAAA,CAAAhE,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}