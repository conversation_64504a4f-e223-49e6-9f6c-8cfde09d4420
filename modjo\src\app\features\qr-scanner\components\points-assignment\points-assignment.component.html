<div class="assignment-dialog">
  <div class="dialog-header">
    <h2 mat-dialog-title>
      <mat-icon [color]="'primary'">{{ getScanTypeIcon() }}</mat-icon>
      Attribution de points
    </h2>
    <p class="scan-type">{{ getScanTypeLabel() }}</p>
  </div>

  <mat-dialog-content>
    <form [formGroup]="assignmentForm" class="assignment-form">
      <!-- Points selection -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Nombre de points</mat-label>
        <mat-select formControlName="points">
          <mat-option *ngFor="let option of pointsOptions" [value]="option.value">
            <div class="points-option">
              <span class="points-value">{{ option.label }}</span>
              <span class="points-description">{{ option.description }}</span>
            </div>
          </mat-option>
        </mat-select>
        <mat-error>{{ getFieldError('points') }}</mat-error>
      </mat-form-field>

      <!-- Description -->
      <mat-form-field appearance="outline" class="full-width">
        <mat-label>Description</mat-label>
        <textarea matInput 
                  formControlName="description"
                  rows="3"
                  placeholder="Décrivez l'action ou la raison de l'attribution">
        </textarea>
        <mat-error>{{ getFieldError('description') }}</mat-error>
      </mat-form-field>

      <!-- Target user (if applicable) -->
      <mat-form-field appearance="outline" class="full-width" 
                      *ngIf="data.qrData?.type === 'user_transfer'">
        <mat-label>Utilisateur cible</mat-label>
        <input matInput 
               formControlName="targetUserId"
               readonly
               placeholder="ID de l'utilisateur">
        <mat-icon matSuffix>person</mat-icon>
      </mat-form-field>

      <!-- Scan details -->
      <div class="scan-details">
        <h4>Détails du scan</h4>
        <div class="detail-item">
          <mat-icon>qr_code</mat-icon>
          <span>Type: {{ getScanTypeLabel() }}</span>
        </div>
        <div class="detail-item" *ngIf="data.qrData?.timestamp">
          <mat-icon>schedule</mat-icon>
          <span>Scanné: {{ data.qrData.timestamp | date:'short' }}</span>
        </div>
        <div class="detail-item" *ngIf="data.qrData?.action">
          <mat-icon>task_alt</mat-icon>
          <span>Action: {{ data.qrData.action }}</span>
        </div>
      </div>

      <!-- Points preview -->
      <div class="points-preview">
        <div class="preview-content">
          <mat-icon class="preview-icon">stars</mat-icon>
          <div class="preview-text">
            <h3>{{ assignmentForm.get('points')?.value || 0 }} point(s)</h3>
            <p>seront attribués à {{ data.currentUser?.name }}</p>
          </div>
        </div>
      </div>
    </form>
  </mat-dialog-content>

  <mat-dialog-actions align="end">
    <button mat-button (click)="onCancel()" [disabled]="isLoading">
      Annuler
    </button>
    <button mat-raised-button 
            color="primary" 
            (click)="onSubmit()"
            [disabled]="isLoading || assignmentForm.invalid">
      <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
      <span *ngIf="!isLoading">Attribuer les points</span>
    </button>
  </mat-dialog-actions>
</div>
