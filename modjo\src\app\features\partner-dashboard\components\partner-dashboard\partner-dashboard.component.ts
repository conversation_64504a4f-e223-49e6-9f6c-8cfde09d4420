import { Component, OnInit } from '@angular/core';
import { AuthService } from '../../../../core/services/auth.service';
import { User, UserRole } from '../../../../core/models';

@Component({
  selector: 'app-partner-dashboard',
  template: `
    <div class="partner-dashboard-container" *ngIf="user">
      <div class="partner-header">
        <h1>🧑‍🍳 Dashboard Partenaire</h1>
        <p>{{ getGreeting() }}, {{ user.name }}!</p>
        <p class="subtitle">G<PERSON>rez vos récompenses et suivez vos performances</p>
      </div>
      
      <div class="partner-features">
        <mat-card class="feature-card">
          <mat-card-content>
            <mat-icon>card_giftcard</mat-icon>
            <h3>Ajouter des Récompenses</h3>
            <p>Créez et gérez vos offres (valeur, stock, conditions)</p>
            <button mat-raised-button color="primary">G<PERSON><PERSON> les Récompenses</button>
          </mat-card-content>
        </mat-card>
        
        <mat-card class="feature-card">
          <mat-card-content>
            <mat-icon>analytics</mat-icon>
            <h3>Statistiques d'Utilisation</h3>
            <p>Suivez combien de fois vos récompenses sont échangées</p>
            <button mat-raised-button color="accent">Voir les Stats</button>
          </mat-card-content>
        </mat-card>
        
        <mat-card class="feature-card">
          <mat-card-content>
            <mat-icon>edit</mat-icon>
            <h3>Modifier les Offres</h3>
            <p>Modifiez ou supprimez vos récompenses existantes</p>
            <button mat-raised-button color="warn">Modifier les Offres</button>
          </mat-card-content>
        </mat-card>
      </div>
    </div>
  `,
  styles: [`
    .partner-dashboard-container { padding: 20px; }
    .partner-header { text-align: center; margin-bottom: 40px; }
    .partner-header h1 { color: #2d3748; font-size: 2.5rem; margin-bottom: 16px; }
    .subtitle { color: #718096; font-size: 1.1rem; }
    .partner-features { display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; }
    .feature-card { text-align: center; padding: 24px; }
    .feature-card mat-icon { font-size: 3rem; color: #667eea; margin-bottom: 16px; }
    .feature-card h3 { color: #2d3748; margin-bottom: 12px; }
    .feature-card p { color: #718096; margin-bottom: 20px; }
  `]
})
export class PartnerDashboardComponent implements OnInit {
  user: User | null = null;

  constructor(private authService: AuthService) {}

  ngOnInit(): void {
    this.authService.currentUser$.subscribe(user => {
      this.user = user;
    });
  }

  getGreeting(): string {
    const hour = new Date().getHours();
    if (hour < 12) return 'Bonjour';
    if (hour < 18) return 'Bon après-midi';
    return 'Bonsoir';
  }
}
