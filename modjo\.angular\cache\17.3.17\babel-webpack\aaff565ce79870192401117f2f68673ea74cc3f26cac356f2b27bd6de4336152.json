{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { SecurityContext, Injectable, Optional, Inject, Skip<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, InjectionToken, inject, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Input, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport { DOCUMENT } from '@angular/common';\nimport { of, throwError, forkJoin, Subscription } from 'rxjs';\nimport { tap, map, catchError, finalize, share, take } from 'rxjs/operators';\nimport * as i1 from '@angular/common/http';\nimport { HttpClient } from '@angular/common/http';\nimport * as i2 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\n\n/**\n * The Trusted Types policy, or null if Trusted Types are not\n * enabled/supported, or undefined if the policy has not been created yet.\n */\nconst _c0 = [\"*\"];\nlet policy;\n/**\n * Returns the Trusted Types policy, or null if Trusted Types are not\n * enabled/supported. The first call to this function will create the policy.\n */\nfunction getPolicy() {\n  if (policy === undefined) {\n    policy = null;\n    if (typeof window !== 'undefined') {\n      const ttWindow = window;\n      if (ttWindow.trustedTypes !== undefined) {\n        policy = ttWindow.trustedTypes.createPolicy('angular#components', {\n          createHTML: s => s\n        });\n      }\n    }\n  }\n  return policy;\n}\n/**\n * Unsafely promote a string to a TrustedHTML, falling back to strings when\n * Trusted Types are not available.\n * @security This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will be interpreted as HTML by a browser, e.g. when assigning to\n * element.innerHTML.\n */\nfunction trustedHTMLFromString(html) {\n  return getPolicy()?.createHTML(html) || html;\n}\n\n/**\n * Returns an exception to be thrown in the case when attempting to\n * load an icon with a name that cannot be found.\n * @docs-private\n */\nfunction getMatIconNameNotFoundError(iconName) {\n  return Error(`Unable to find icon with the name \"${iconName}\"`);\n}\n/**\n * Returns an exception to be thrown when the consumer attempts to use\n * `<mat-icon>` without including @angular/common/http.\n * @docs-private\n */\nfunction getMatIconNoHttpProviderError() {\n  return Error('Could not find HttpClient provider for use with Angular Material icons. ' + 'Please include the HttpClientModule from @angular/common/http in your ' + 'app imports.');\n}\n/**\n * Returns an exception to be thrown when a URL couldn't be sanitized.\n * @param url URL that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeUrlError(url) {\n  return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL ` + `via Angular's DomSanitizer. Attempted URL was \"${url}\".`);\n}\n/**\n * Returns an exception to be thrown when a HTML string couldn't be sanitized.\n * @param literal HTML that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeLiteralError(literal) {\n  return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by ` + `Angular's DomSanitizer. Attempted literal was \"${literal}\".`);\n}\n/**\n * Configuration for an icon, including the URL and possibly the cached SVG element.\n * @docs-private\n */\nclass SvgIconConfig {\n  constructor(url, svgText, options) {\n    this.url = url;\n    this.svgText = svgText;\n    this.options = options;\n  }\n}\n/**\n * Service to register and display icons used by the `<mat-icon>` component.\n * - Registers icon URLs by namespace and name.\n * - Registers icon set URLs by namespace.\n * - Registers aliases for CSS classes, for use with icon fonts.\n * - Loads icons from URLs and extracts individual icons from icon sets.\n */\nclass MatIconRegistry {\n  constructor(_httpClient, _sanitizer, document, _errorHandler) {\n    this._httpClient = _httpClient;\n    this._sanitizer = _sanitizer;\n    this._errorHandler = _errorHandler;\n    /**\n     * URLs and cached SVG elements for individual icons. Keys are of the format \"[namespace]:[icon]\".\n     */\n    this._svgIconConfigs = new Map();\n    /**\n     * SvgIconConfig objects and cached SVG elements for icon sets, keyed by namespace.\n     * Multiple icon sets can be registered under the same namespace.\n     */\n    this._iconSetConfigs = new Map();\n    /** Cache for icons loaded by direct URLs. */\n    this._cachedIconsByUrl = new Map();\n    /** In-progress icon fetches. Used to coalesce multiple requests to the same URL. */\n    this._inProgressUrlFetches = new Map();\n    /** Map from font identifiers to their CSS class names. Used for icon fonts. */\n    this._fontCssClassesByAlias = new Map();\n    /** Registered icon resolver functions. */\n    this._resolvers = [];\n    /**\n     * The CSS classes to apply when an `<mat-icon>` component has no icon name, url, or font\n     * specified. The default 'material-icons' value assumes that the material icon font has been\n     * loaded as described at https://google.github.io/material-design-icons/#icon-font-for-the-web\n     */\n    this._defaultFontSetClass = ['material-icons', 'mat-ligature-font'];\n    this._document = document;\n  }\n  /**\n   * Registers an icon by URL in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n  addSvgIcon(iconName, url, options) {\n    return this.addSvgIconInNamespace('', iconName, url, options);\n  }\n  /**\n   * Registers an icon using an HTML string in the default namespace.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n  addSvgIconLiteral(iconName, literal, options) {\n    return this.addSvgIconLiteralInNamespace('', iconName, literal, options);\n  }\n  /**\n   * Registers an icon by URL in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param url\n   */\n  addSvgIconInNamespace(namespace, iconName, url, options) {\n    return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig(url, null, options));\n  }\n  /**\n   * Registers an icon resolver function with the registry. The function will be invoked with the\n   * name and namespace of an icon when the registry tries to resolve the URL from which to fetch\n   * the icon. The resolver is expected to return a `SafeResourceUrl` that points to the icon,\n   * an object with the icon URL and icon options, or `null` if the icon is not supported. Resolvers\n   * will be invoked in the order in which they have been registered.\n   * @param resolver Resolver function to be registered.\n   */\n  addSvgIconResolver(resolver) {\n    this._resolvers.push(resolver);\n    return this;\n  }\n  /**\n   * Registers an icon using an HTML string in the specified namespace.\n   * @param namespace Namespace in which the icon should be registered.\n   * @param iconName Name under which the icon should be registered.\n   * @param literal SVG source of the icon.\n   */\n  addSvgIconLiteralInNamespace(namespace, iconName, literal, options) {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n    // TODO: add an ngDevMode check\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    }\n    // Security: The literal is passed in as SafeHtml, and is thus trusted.\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig('', trustedLiteral, options));\n  }\n  /**\n   * Registers an icon set by URL in the default namespace.\n   * @param url\n   */\n  addSvgIconSet(url, options) {\n    return this.addSvgIconSetInNamespace('', url, options);\n  }\n  /**\n   * Registers an icon set using an HTML string in the default namespace.\n   * @param literal SVG source of the icon set.\n   */\n  addSvgIconSetLiteral(literal, options) {\n    return this.addSvgIconSetLiteralInNamespace('', literal, options);\n  }\n  /**\n   * Registers an icon set by URL in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param url\n   */\n  addSvgIconSetInNamespace(namespace, url, options) {\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig(url, null, options));\n  }\n  /**\n   * Registers an icon set using an HTML string in the specified namespace.\n   * @param namespace Namespace in which to register the icon set.\n   * @param literal SVG source of the icon set.\n   */\n  addSvgIconSetLiteralInNamespace(namespace, literal, options) {\n    const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n    if (!cleanLiteral) {\n      throw getMatIconFailedToSanitizeLiteralError(literal);\n    }\n    // Security: The literal is passed in as SafeHtml, and is thus trusted.\n    const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n    return this._addSvgIconSetConfig(namespace, new SvgIconConfig('', trustedLiteral, options));\n  }\n  /**\n   * Defines an alias for CSS class names to be used for icon fonts. Creating an matIcon\n   * component with the alias as the fontSet input will cause the class name to be applied\n   * to the `<mat-icon>` element.\n   *\n   * If the registered font is a ligature font, then don't forget to also include the special\n   * class `mat-ligature-font` to allow the usage via attribute. So register like this:\n   *\n   * ```ts\n   * iconRegistry.registerFontClassAlias('f1', 'font1 mat-ligature-font');\n   * ```\n   *\n   * And use like this:\n   *\n   * ```html\n   * <mat-icon fontSet=\"f1\" fontIcon=\"home\"></mat-icon>\n   * ```\n   *\n   * @param alias Alias for the font.\n   * @param classNames Class names override to be used instead of the alias.\n   */\n  registerFontClassAlias(alias, classNames = alias) {\n    this._fontCssClassesByAlias.set(alias, classNames);\n    return this;\n  }\n  /**\n   * Returns the CSS class name associated with the alias by a previous call to\n   * registerFontClassAlias. If no CSS class has been associated, returns the alias unmodified.\n   */\n  classNameForFontAlias(alias) {\n    return this._fontCssClassesByAlias.get(alias) || alias;\n  }\n  /**\n   * Sets the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n  setDefaultFontSetClass(...classNames) {\n    this._defaultFontSetClass = classNames;\n    return this;\n  }\n  /**\n   * Returns the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n   * have a fontSet input value, and is not loading an icon by name or URL.\n   */\n  getDefaultFontSetClass() {\n    return this._defaultFontSetClass;\n  }\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) from the given URL.\n   * The response from the URL may be cached so this will not always cause an HTTP request, but\n   * the produced element will always be a new copy of the originally fetched icon. (That is,\n   * it will not contain any modifications made to elements previously returned).\n   *\n   * @param safeUrl URL from which to fetch the SVG icon.\n   */\n  getSvgIconFromUrl(safeUrl) {\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    }\n    const cachedIcon = this._cachedIconsByUrl.get(url);\n    if (cachedIcon) {\n      return of(cloneSvg(cachedIcon));\n    }\n    return this._loadSvgIconFromConfig(new SvgIconConfig(safeUrl, null)).pipe(tap(svg => this._cachedIconsByUrl.set(url, svg)), map(svg => cloneSvg(svg)));\n  }\n  /**\n   * Returns an Observable that produces the icon (as an `<svg>` DOM element) with the given name\n   * and namespace. The icon must have been previously registered with addIcon or addIconSet;\n   * if not, the Observable will throw an error.\n   *\n   * @param name Name of the icon to be retrieved.\n   * @param namespace Namespace in which to look for the icon.\n   */\n  getNamedSvgIcon(name, namespace = '') {\n    const key = iconKey(namespace, name);\n    let config = this._svgIconConfigs.get(key);\n    // Return (copy of) cached icon if possible.\n    if (config) {\n      return this._getSvgFromConfig(config);\n    }\n    // Otherwise try to resolve the config from one of the resolver functions.\n    config = this._getIconConfigFromResolvers(namespace, name);\n    if (config) {\n      this._svgIconConfigs.set(key, config);\n      return this._getSvgFromConfig(config);\n    }\n    // See if we have any icon sets registered for the namespace.\n    const iconSetConfigs = this._iconSetConfigs.get(namespace);\n    if (iconSetConfigs) {\n      return this._getSvgFromIconSetConfigs(name, iconSetConfigs);\n    }\n    return throwError(getMatIconNameNotFoundError(key));\n  }\n  ngOnDestroy() {\n    this._resolvers = [];\n    this._svgIconConfigs.clear();\n    this._iconSetConfigs.clear();\n    this._cachedIconsByUrl.clear();\n  }\n  /**\n   * Returns the cached icon for a SvgIconConfig if available, or fetches it from its URL if not.\n   */\n  _getSvgFromConfig(config) {\n    if (config.svgText) {\n      // We already have the SVG element for this icon, return a copy.\n      return of(cloneSvg(this._svgElementFromConfig(config)));\n    } else {\n      // Fetch the icon from the config's URL, cache it, and return a copy.\n      return this._loadSvgIconFromConfig(config).pipe(map(svg => cloneSvg(svg)));\n    }\n  }\n  /**\n   * Attempts to find an icon with the specified name in any of the SVG icon sets.\n   * First searches the available cached icons for a nested element with a matching name, and\n   * if found copies the element to a new `<svg>` element. If not found, fetches all icon sets\n   * that have not been cached, and searches again after all fetches are completed.\n   * The returned Observable produces the SVG element if possible, and throws\n   * an error if no icon with the specified name can be found.\n   */\n  _getSvgFromIconSetConfigs(name, iconSetConfigs) {\n    // For all the icon set SVG elements we've fetched, see if any contain an icon with the\n    // requested name.\n    const namedIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n    if (namedIcon) {\n      // We could cache namedIcon in _svgIconConfigs, but since we have to make a copy every\n      // time anyway, there's probably not much advantage compared to just always extracting\n      // it from the icon set.\n      return of(namedIcon);\n    }\n    // Not found in any cached icon sets. If there are icon sets with URLs that we haven't\n    // fetched, fetch them now and look for iconName in the results.\n    const iconSetFetchRequests = iconSetConfigs.filter(iconSetConfig => !iconSetConfig.svgText).map(iconSetConfig => {\n      return this._loadSvgIconSetFromConfig(iconSetConfig).pipe(catchError(err => {\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, iconSetConfig.url);\n        // Swallow errors fetching individual URLs so the\n        // combined Observable won't necessarily fail.\n        const errorMessage = `Loading icon set URL: ${url} failed: ${err.message}`;\n        this._errorHandler.handleError(new Error(errorMessage));\n        return of(null);\n      }));\n    });\n    // Fetch all the icon set URLs. When the requests complete, every IconSet should have a\n    // cached SVG element (unless the request failed), and we can check again for the icon.\n    return forkJoin(iconSetFetchRequests).pipe(map(() => {\n      const foundIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n      // TODO: add an ngDevMode check\n      if (!foundIcon) {\n        throw getMatIconNameNotFoundError(name);\n      }\n      return foundIcon;\n    }));\n  }\n  /**\n   * Searches the cached SVG elements for the given icon sets for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n  _extractIconWithNameFromAnySet(iconName, iconSetConfigs) {\n    // Iterate backwards, so icon sets added later have precedence.\n    for (let i = iconSetConfigs.length - 1; i >= 0; i--) {\n      const config = iconSetConfigs[i];\n      // Parsing the icon set's text into an SVG element can be expensive. We can avoid some of\n      // the parsing by doing a quick check using `indexOf` to see if there's any chance for the\n      // icon to be in the set. This won't be 100% accurate, but it should help us avoid at least\n      // some of the parsing.\n      if (config.svgText && config.svgText.toString().indexOf(iconName) > -1) {\n        const svg = this._svgElementFromConfig(config);\n        const foundIcon = this._extractSvgIconFromSet(svg, iconName, config.options);\n        if (foundIcon) {\n          return foundIcon;\n        }\n      }\n    }\n    return null;\n  }\n  /**\n   * Loads the content of the icon URL specified in the SvgIconConfig and creates an SVG element\n   * from it.\n   */\n  _loadSvgIconFromConfig(config) {\n    return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText), map(() => this._svgElementFromConfig(config)));\n  }\n  /**\n   * Loads the content of the icon set URL specified in the\n   * SvgIconConfig and attaches it to the config.\n   */\n  _loadSvgIconSetFromConfig(config) {\n    if (config.svgText) {\n      return of(null);\n    }\n    return this._fetchIcon(config).pipe(tap(svgText => config.svgText = svgText));\n  }\n  /**\n   * Searches the cached element of the given SvgIconConfig for a nested icon element whose \"id\"\n   * tag matches the specified name. If found, copies the nested element to a new SVG element and\n   * returns it. Returns null if no matching element is found.\n   */\n  _extractSvgIconFromSet(iconSet, iconName, options) {\n    // Use the `id=\"iconName\"` syntax in order to escape special\n    // characters in the ID (versus using the #iconName syntax).\n    const iconSource = iconSet.querySelector(`[id=\"${iconName}\"]`);\n    if (!iconSource) {\n      return null;\n    }\n    // Clone the element and remove the ID to prevent multiple elements from being added\n    // to the page with the same ID.\n    const iconElement = iconSource.cloneNode(true);\n    iconElement.removeAttribute('id');\n    // If the icon node is itself an <svg> node, clone and return it directly. If not, set it as\n    // the content of a new <svg> node.\n    if (iconElement.nodeName.toLowerCase() === 'svg') {\n      return this._setSvgAttributes(iconElement, options);\n    }\n    // If the node is a <symbol>, it won't be rendered so we have to convert it into <svg>. Note\n    // that the same could be achieved by referring to it via <use href=\"#id\">, however the <use>\n    // tag is problematic on Firefox, because it needs to include the current page path.\n    if (iconElement.nodeName.toLowerCase() === 'symbol') {\n      return this._setSvgAttributes(this._toSvgElement(iconElement), options);\n    }\n    // createElement('SVG') doesn't work as expected; the DOM ends up with\n    // the correct nodes, but the SVG content doesn't render. Instead we\n    // have to create an empty SVG node using innerHTML and append its content.\n    // Elements created using DOMParser.parseFromString have the same problem.\n    // http://stackoverflow.com/questions/23003278/svg-innerhtml-in-firefox-can-not-display\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n    // Clone the node so we don't remove it from the parent icon set element.\n    svg.appendChild(iconElement);\n    return this._setSvgAttributes(svg, options);\n  }\n  /**\n   * Creates a DOM element from the given SVG string.\n   */\n  _svgElementFromString(str) {\n    const div = this._document.createElement('DIV');\n    div.innerHTML = str;\n    const svg = div.querySelector('svg');\n    // TODO: add an ngDevMode check\n    if (!svg) {\n      throw Error('<svg> tag not found');\n    }\n    return svg;\n  }\n  /**\n   * Converts an element into an SVG node by cloning all of its children.\n   */\n  _toSvgElement(element) {\n    const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n    const attributes = element.attributes;\n    // Copy over all the attributes from the `symbol` to the new SVG, except the id.\n    for (let i = 0; i < attributes.length; i++) {\n      const {\n        name,\n        value\n      } = attributes[i];\n      if (name !== 'id') {\n        svg.setAttribute(name, value);\n      }\n    }\n    for (let i = 0; i < element.childNodes.length; i++) {\n      if (element.childNodes[i].nodeType === this._document.ELEMENT_NODE) {\n        svg.appendChild(element.childNodes[i].cloneNode(true));\n      }\n    }\n    return svg;\n  }\n  /**\n   * Sets the default attributes for an SVG element to be used as an icon.\n   */\n  _setSvgAttributes(svg, options) {\n    svg.setAttribute('fit', '');\n    svg.setAttribute('height', '100%');\n    svg.setAttribute('width', '100%');\n    svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n    svg.setAttribute('focusable', 'false'); // Disable IE11 default behavior to make SVGs focusable.\n    if (options && options.viewBox) {\n      svg.setAttribute('viewBox', options.viewBox);\n    }\n    return svg;\n  }\n  /**\n   * Returns an Observable which produces the string contents of the given icon. Results may be\n   * cached, so future calls with the same URL may not cause another HTTP request.\n   */\n  _fetchIcon(iconConfig) {\n    const {\n      url: safeUrl,\n      options\n    } = iconConfig;\n    const withCredentials = options?.withCredentials ?? false;\n    if (!this._httpClient) {\n      throw getMatIconNoHttpProviderError();\n    }\n    // TODO: add an ngDevMode check\n    if (safeUrl == null) {\n      throw Error(`Cannot fetch icon from URL \"${safeUrl}\".`);\n    }\n    const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n    // TODO: add an ngDevMode check\n    if (!url) {\n      throw getMatIconFailedToSanitizeUrlError(safeUrl);\n    }\n    // Store in-progress fetches to avoid sending a duplicate request for a URL when there is\n    // already a request in progress for that URL. It's necessary to call share() on the\n    // Observable returned by http.get() so that multiple subscribers don't cause multiple XHRs.\n    const inProgressFetch = this._inProgressUrlFetches.get(url);\n    if (inProgressFetch) {\n      return inProgressFetch;\n    }\n    const req = this._httpClient.get(url, {\n      responseType: 'text',\n      withCredentials\n    }).pipe(map(svg => {\n      // Security: This SVG is fetched from a SafeResourceUrl, and is thus\n      // trusted HTML.\n      return trustedHTMLFromString(svg);\n    }), finalize(() => this._inProgressUrlFetches.delete(url)), share());\n    this._inProgressUrlFetches.set(url, req);\n    return req;\n  }\n  /**\n   * Registers an icon config by name in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param iconName Name under which to register the config.\n   * @param config Config to be registered.\n   */\n  _addSvgIconConfig(namespace, iconName, config) {\n    this._svgIconConfigs.set(iconKey(namespace, iconName), config);\n    return this;\n  }\n  /**\n   * Registers an icon set config in the specified namespace.\n   * @param namespace Namespace in which to register the icon config.\n   * @param config Config to be registered.\n   */\n  _addSvgIconSetConfig(namespace, config) {\n    const configNamespace = this._iconSetConfigs.get(namespace);\n    if (configNamespace) {\n      configNamespace.push(config);\n    } else {\n      this._iconSetConfigs.set(namespace, [config]);\n    }\n    return this;\n  }\n  /** Parses a config's text into an SVG element. */\n  _svgElementFromConfig(config) {\n    if (!config.svgElement) {\n      const svg = this._svgElementFromString(config.svgText);\n      this._setSvgAttributes(svg, config.options);\n      config.svgElement = svg;\n    }\n    return config.svgElement;\n  }\n  /** Tries to create an icon config through the registered resolver functions. */\n  _getIconConfigFromResolvers(namespace, name) {\n    for (let i = 0; i < this._resolvers.length; i++) {\n      const result = this._resolvers[i](name, namespace);\n      if (result) {\n        return isSafeUrlWithOptions(result) ? new SvgIconConfig(result.url, null, result.options) : new SvgIconConfig(result, null);\n      }\n    }\n    return undefined;\n  }\n  static {\n    this.ɵfac = function MatIconRegistry_Factory(t) {\n      return new (t || MatIconRegistry)(i0.ɵɵinject(i1.HttpClient, 8), i0.ɵɵinject(i2.DomSanitizer), i0.ɵɵinject(DOCUMENT, 8), i0.ɵɵinject(i0.ErrorHandler));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: MatIconRegistry,\n      factory: MatIconRegistry.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconRegistry, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'root'\n    }]\n  }], () => [{\n    type: i1.HttpClient,\n    decorators: [{\n      type: Optional\n    }]\n  }, {\n    type: i2.DomSanitizer\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [DOCUMENT]\n    }]\n  }, {\n    type: i0.ErrorHandler\n  }], null);\n})();\n/** @docs-private */\nfunction ICON_REGISTRY_PROVIDER_FACTORY(parentRegistry, httpClient, sanitizer, errorHandler, document) {\n  return parentRegistry || new MatIconRegistry(httpClient, sanitizer, document, errorHandler);\n}\n/** @docs-private */\nconst ICON_REGISTRY_PROVIDER = {\n  // If there is already an MatIconRegistry available, use that. Otherwise, provide a new one.\n  provide: MatIconRegistry,\n  deps: [[new Optional(), new SkipSelf(), MatIconRegistry], [new Optional(), HttpClient], DomSanitizer, ErrorHandler, [new Optional(), DOCUMENT]],\n  useFactory: ICON_REGISTRY_PROVIDER_FACTORY\n};\n/** Clones an SVGElement while preserving type information. */\nfunction cloneSvg(svg) {\n  return svg.cloneNode(true);\n}\n/** Returns the cache key to use for an icon namespace and name. */\nfunction iconKey(namespace, name) {\n  return namespace + ':' + name;\n}\nfunction isSafeUrlWithOptions(value) {\n  return !!(value.url && value.options);\n}\n\n/** Injection token to be used to override the default options for `mat-icon`. */\nconst MAT_ICON_DEFAULT_OPTIONS = new InjectionToken('MAT_ICON_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatIcon`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_ICON_LOCATION = new InjectionToken('mat-icon-location', {\n  providedIn: 'root',\n  factory: MAT_ICON_LOCATION_FACTORY\n});\n/** @docs-private */\nfunction MAT_ICON_LOCATION_FACTORY() {\n  const _document = inject(DOCUMENT);\n  const _location = _document ? _document.location : null;\n  return {\n    // Note that this needs to be a function, rather than a property, because Angular\n    // will only resolve it once, but we want the current path on each call.\n    getPathname: () => _location ? _location.pathname + _location.search : ''\n  };\n}\n/** SVG attributes that accept a FuncIRI (e.g. `url(<something>)`). */\nconst funcIriAttributes = ['clip-path', 'color-profile', 'src', 'cursor', 'fill', 'filter', 'marker', 'marker-start', 'marker-mid', 'marker-end', 'mask', 'stroke'];\n/** Selector that can be used to find all elements that are using a `FuncIRI`. */\nconst funcIriAttributeSelector = funcIriAttributes.map(attr => `[${attr}]`).join(', ');\n/** Regex that can be used to extract the id out of a FuncIRI. */\nconst funcIriPattern = /^url\\(['\"]?#(.*?)['\"]?\\)$/;\n/**\n * Component to display an icon. It can be used in the following ways:\n *\n * - Specify the svgIcon input to load an SVG icon from a URL previously registered with the\n *   addSvgIcon, addSvgIconInNamespace, addSvgIconSet, or addSvgIconSetInNamespace methods of\n *   MatIconRegistry. If the svgIcon value contains a colon it is assumed to be in the format\n *   \"[namespace]:[name]\", if not the value will be the name of an icon in the default namespace.\n *   Examples:\n *     `<mat-icon svgIcon=\"left-arrow\"></mat-icon>\n *     <mat-icon svgIcon=\"animals:cat\"></mat-icon>`\n *\n * - Use a font ligature as an icon by putting the ligature text in the `fontIcon` attribute or the\n *   content of the `<mat-icon>` component. If you register a custom font class, don't forget to also\n *   include the special class `mat-ligature-font`. It is recommended to use the attribute alternative\n *   to prevent the ligature text to be selectable and to appear in search engine results.\n *   By default, the Material icons font is used as described at\n *   http://google.github.io/material-design-icons/#icon-font-for-the-web. You can specify an\n *   alternate font by setting the fontSet input to either the CSS class to apply to use the\n *   desired font, or to an alias previously registered with MatIconRegistry.registerFontClassAlias.\n *   Examples:\n *     `<mat-icon fontIcon=\"home\"></mat-icon>\n *     <mat-icon>home</mat-icon>\n *     <mat-icon fontSet=\"myfont\" fontIcon=\"sun\"></mat-icon>\n *     <mat-icon fontSet=\"myfont\">sun</mat-icon>`\n *\n * - Specify a font glyph to be included via CSS rules by setting the fontSet input to specify the\n *   font, and the fontIcon input to specify the icon. Typically the fontIcon will specify a\n *   CSS class which causes the glyph to be displayed via a :before selector, as in\n *   https://fortawesome.github.io/Font-Awesome/examples/\n *   Example:\n *     `<mat-icon fontSet=\"fa\" fontIcon=\"alarm\"></mat-icon>`\n */\nclass MatIcon {\n  /** Theme palette color of the icon. */\n  get color() {\n    return this._color || this._defaultColor;\n  }\n  set color(value) {\n    this._color = value;\n  }\n  /** Name of the icon in the SVG icon set. */\n  get svgIcon() {\n    return this._svgIcon;\n  }\n  set svgIcon(value) {\n    if (value !== this._svgIcon) {\n      if (value) {\n        this._updateSvgIcon(value);\n      } else if (this._svgIcon) {\n        this._clearSvgElement();\n      }\n      this._svgIcon = value;\n    }\n  }\n  /** Font set that the icon is a part of. */\n  get fontSet() {\n    return this._fontSet;\n  }\n  set fontSet(value) {\n    const newValue = this._cleanupFontValue(value);\n    if (newValue !== this._fontSet) {\n      this._fontSet = newValue;\n      this._updateFontIconClasses();\n    }\n  }\n  /** Name of an icon within a font set. */\n  get fontIcon() {\n    return this._fontIcon;\n  }\n  set fontIcon(value) {\n    const newValue = this._cleanupFontValue(value);\n    if (newValue !== this._fontIcon) {\n      this._fontIcon = newValue;\n      this._updateFontIconClasses();\n    }\n  }\n  constructor(_elementRef, _iconRegistry, ariaHidden, _location, _errorHandler, defaults) {\n    this._elementRef = _elementRef;\n    this._iconRegistry = _iconRegistry;\n    this._location = _location;\n    this._errorHandler = _errorHandler;\n    /**\n     * Whether the icon should be inlined, automatically sizing the icon to match the font size of\n     * the element the icon is contained in.\n     */\n    this.inline = false;\n    this._previousFontSetClass = [];\n    /** Subscription to the current in-progress SVG icon request. */\n    this._currentIconFetch = Subscription.EMPTY;\n    if (defaults) {\n      if (defaults.color) {\n        this.color = this._defaultColor = defaults.color;\n      }\n      if (defaults.fontSet) {\n        this.fontSet = defaults.fontSet;\n      }\n    }\n    // If the user has not explicitly set aria-hidden, mark the icon as hidden, as this is\n    // the right thing to do for the majority of icon use-cases.\n    if (!ariaHidden) {\n      _elementRef.nativeElement.setAttribute('aria-hidden', 'true');\n    }\n  }\n  /**\n   * Splits an svgIcon binding value into its icon set and icon name components.\n   * Returns a 2-element array of [(icon set), (icon name)].\n   * The separator for the two fields is ':'. If there is no separator, an empty\n   * string is returned for the icon set and the entire value is returned for\n   * the icon name. If the argument is falsy, returns an array of two empty strings.\n   * Throws an error if the name contains two or more ':' separators.\n   * Examples:\n   *   `'social:cake' -> ['social', 'cake']\n   *   'penguin' -> ['', 'penguin']\n   *   null -> ['', '']\n   *   'a:b:c' -> (throws Error)`\n   */\n  _splitIconName(iconName) {\n    if (!iconName) {\n      return ['', ''];\n    }\n    const parts = iconName.split(':');\n    switch (parts.length) {\n      case 1:\n        return ['', parts[0]];\n      // Use default namespace.\n      case 2:\n        return parts;\n      default:\n        throw Error(`Invalid icon name: \"${iconName}\"`);\n      // TODO: add an ngDevMode check\n    }\n  }\n  ngOnInit() {\n    // Update font classes because ngOnChanges won't be called if none of the inputs are present,\n    // e.g. <mat-icon>arrow</mat-icon> In this case we need to add a CSS class for the default font.\n    this._updateFontIconClasses();\n  }\n  ngAfterViewChecked() {\n    const cachedElements = this._elementsWithExternalReferences;\n    if (cachedElements && cachedElements.size) {\n      const newPath = this._location.getPathname();\n      // We need to check whether the URL has changed on each change detection since\n      // the browser doesn't have an API that will let us react on link clicks and\n      // we can't depend on the Angular router. The references need to be updated,\n      // because while most browsers don't care whether the URL is correct after\n      // the first render, Safari will break if the user navigates to a different\n      // page and the SVG isn't re-rendered.\n      if (newPath !== this._previousPath) {\n        this._previousPath = newPath;\n        this._prependPathToReferences(newPath);\n      }\n    }\n  }\n  ngOnDestroy() {\n    this._currentIconFetch.unsubscribe();\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    }\n  }\n  _usingFontIcon() {\n    return !this.svgIcon;\n  }\n  _setSvgElement(svg) {\n    this._clearSvgElement();\n    // Note: we do this fix here, rather than the icon registry, because the\n    // references have to point to the URL at the time that the icon was created.\n    const path = this._location.getPathname();\n    this._previousPath = path;\n    this._cacheChildrenWithExternalReferences(svg);\n    this._prependPathToReferences(path);\n    this._elementRef.nativeElement.appendChild(svg);\n  }\n  _clearSvgElement() {\n    const layoutElement = this._elementRef.nativeElement;\n    let childCount = layoutElement.childNodes.length;\n    if (this._elementsWithExternalReferences) {\n      this._elementsWithExternalReferences.clear();\n    }\n    // Remove existing non-element child nodes and SVGs, and add the new SVG element. Note that\n    // we can't use innerHTML, because IE will throw if the element has a data binding.\n    while (childCount--) {\n      const child = layoutElement.childNodes[childCount];\n      // 1 corresponds to Node.ELEMENT_NODE. We remove all non-element nodes in order to get rid\n      // of any loose text nodes, as well as any SVG elements in order to remove any old icons.\n      if (child.nodeType !== 1 || child.nodeName.toLowerCase() === 'svg') {\n        child.remove();\n      }\n    }\n  }\n  _updateFontIconClasses() {\n    if (!this._usingFontIcon()) {\n      return;\n    }\n    const elem = this._elementRef.nativeElement;\n    const fontSetClasses = (this.fontSet ? this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/) : this._iconRegistry.getDefaultFontSetClass()).filter(className => className.length > 0);\n    this._previousFontSetClass.forEach(className => elem.classList.remove(className));\n    fontSetClasses.forEach(className => elem.classList.add(className));\n    this._previousFontSetClass = fontSetClasses;\n    if (this.fontIcon !== this._previousFontIconClass && !fontSetClasses.includes('mat-ligature-font')) {\n      if (this._previousFontIconClass) {\n        elem.classList.remove(this._previousFontIconClass);\n      }\n      if (this.fontIcon) {\n        elem.classList.add(this.fontIcon);\n      }\n      this._previousFontIconClass = this.fontIcon;\n    }\n  }\n  /**\n   * Cleans up a value to be used as a fontIcon or fontSet.\n   * Since the value ends up being assigned as a CSS class, we\n   * have to trim the value and omit space-separated values.\n   */\n  _cleanupFontValue(value) {\n    return typeof value === 'string' ? value.trim().split(' ')[0] : value;\n  }\n  /**\n   * Prepends the current path to all elements that have an attribute pointing to a `FuncIRI`\n   * reference. This is required because WebKit browsers require references to be prefixed with\n   * the current path, if the page has a `base` tag.\n   */\n  _prependPathToReferences(path) {\n    const elements = this._elementsWithExternalReferences;\n    if (elements) {\n      elements.forEach((attrs, element) => {\n        attrs.forEach(attr => {\n          element.setAttribute(attr.name, `url('${path}#${attr.value}')`);\n        });\n      });\n    }\n  }\n  /**\n   * Caches the children of an SVG element that have `url()`\n   * references that we need to prefix with the current path.\n   */\n  _cacheChildrenWithExternalReferences(element) {\n    const elementsWithFuncIri = element.querySelectorAll(funcIriAttributeSelector);\n    const elements = this._elementsWithExternalReferences = this._elementsWithExternalReferences || new Map();\n    for (let i = 0; i < elementsWithFuncIri.length; i++) {\n      funcIriAttributes.forEach(attr => {\n        const elementWithReference = elementsWithFuncIri[i];\n        const value = elementWithReference.getAttribute(attr);\n        const match = value ? value.match(funcIriPattern) : null;\n        if (match) {\n          let attributes = elements.get(elementWithReference);\n          if (!attributes) {\n            attributes = [];\n            elements.set(elementWithReference, attributes);\n          }\n          attributes.push({\n            name: attr,\n            value: match[1]\n          });\n        }\n      });\n    }\n  }\n  /** Sets a new SVG icon with a particular name. */\n  _updateSvgIcon(rawName) {\n    this._svgNamespace = null;\n    this._svgName = null;\n    this._currentIconFetch.unsubscribe();\n    if (rawName) {\n      const [namespace, iconName] = this._splitIconName(rawName);\n      if (namespace) {\n        this._svgNamespace = namespace;\n      }\n      if (iconName) {\n        this._svgName = iconName;\n      }\n      this._currentIconFetch = this._iconRegistry.getNamedSvgIcon(iconName, namespace).pipe(take(1)).subscribe(svg => this._setSvgElement(svg), err => {\n        const errorMessage = `Error retrieving icon ${namespace}:${iconName}! ${err.message}`;\n        this._errorHandler.handleError(new Error(errorMessage));\n      });\n    }\n  }\n  static {\n    this.ɵfac = function MatIcon_Factory(t) {\n      return new (t || MatIcon)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MatIconRegistry), i0.ɵɵinjectAttribute('aria-hidden'), i0.ɵɵdirectiveInject(MAT_ICON_LOCATION), i0.ɵɵdirectiveInject(i0.ErrorHandler), i0.ɵɵdirectiveInject(MAT_ICON_DEFAULT_OPTIONS, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatIcon,\n      selectors: [[\"mat-icon\"]],\n      hostAttrs: [\"role\", \"img\", 1, \"mat-icon\", \"notranslate\"],\n      hostVars: 10,\n      hostBindings: function MatIcon_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"data-mat-icon-type\", ctx._usingFontIcon() ? \"font\" : \"svg\")(\"data-mat-icon-name\", ctx._svgName || ctx.fontIcon)(\"data-mat-icon-namespace\", ctx._svgNamespace || ctx.fontSet)(\"fontIcon\", ctx._usingFontIcon() ? ctx.fontIcon : null);\n          i0.ɵɵclassMap(ctx.color ? \"mat-\" + ctx.color : \"\");\n          i0.ɵɵclassProp(\"mat-icon-inline\", ctx.inline)(\"mat-icon-no-color\", ctx.color !== \"primary\" && ctx.color !== \"accent\" && ctx.color !== \"warn\");\n        }\n      },\n      inputs: {\n        color: \"color\",\n        inline: [i0.ɵɵInputFlags.HasDecoratorInputTransform, \"inline\", \"inline\", booleanAttribute],\n        svgIcon: \"svgIcon\",\n        fontSet: \"fontSet\",\n        fontIcon: \"fontIcon\"\n      },\n      exportAs: [\"matIcon\"],\n      standalone: true,\n      features: [i0.ɵɵInputTransformsFeature, i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 1,\n      vars: 0,\n      template: function MatIcon_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵprojection(0);\n        }\n      },\n      styles: [\"mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIcon, [{\n    type: Component,\n    args: [{\n      template: '<ng-content></ng-content>',\n      selector: 'mat-icon',\n      exportAs: 'matIcon',\n      host: {\n        'role': 'img',\n        'class': 'mat-icon notranslate',\n        '[class]': 'color ? \"mat-\" + color : \"\"',\n        '[attr.data-mat-icon-type]': '_usingFontIcon() ? \"font\" : \"svg\"',\n        '[attr.data-mat-icon-name]': '_svgName || fontIcon',\n        '[attr.data-mat-icon-namespace]': '_svgNamespace || fontSet',\n        '[attr.fontIcon]': '_usingFontIcon() ? fontIcon : null',\n        '[class.mat-icon-inline]': 'inline',\n        '[class.mat-icon-no-color]': 'color !== \"primary\" && color !== \"accent\" && color !== \"warn\"'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      styles: [\"mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: MatIconRegistry\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Attribute,\n      args: ['aria-hidden']\n    }]\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Inject,\n      args: [MAT_ICON_LOCATION]\n    }]\n  }, {\n    type: i0.ErrorHandler\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_ICON_DEFAULT_OPTIONS]\n    }]\n  }], {\n    color: [{\n      type: Input\n    }],\n    inline: [{\n      type: Input,\n      args: [{\n        transform: booleanAttribute\n      }]\n    }],\n    svgIcon: [{\n      type: Input\n    }],\n    fontSet: [{\n      type: Input\n    }],\n    fontIcon: [{\n      type: Input\n    }]\n  });\n})();\nclass MatIconModule {\n  static {\n    this.ɵfac = function MatIconModule_Factory(t) {\n      return new (t || MatIconModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatIconModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatCommonModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatIconModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatCommonModule, MatIcon],\n      exports: [MatIcon, MatCommonModule]\n    }]\n  }], null, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ICON_REGISTRY_PROVIDER, ICON_REGISTRY_PROVIDER_FACTORY, MAT_ICON_DEFAULT_OPTIONS, MAT_ICON_LOCATION, MAT_ICON_LOCATION_FACTORY, MatIcon, MatIconModule, MatIconRegistry, getMatIconFailedToSanitizeLiteralError, getMatIconFailedToSanitizeUrlError, getMatIconNameNotFoundError, getMatIconNoHttpProviderError };", "map": {"version": 3, "names": ["i0", "SecurityContext", "Injectable", "Optional", "Inject", "SkipSelf", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "InjectionToken", "inject", "booleanAttribute", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Attribute", "Input", "NgModule", "MatCommonModule", "DOCUMENT", "of", "throwError", "fork<PERSON><PERSON>n", "Subscription", "tap", "map", "catchError", "finalize", "share", "take", "i1", "HttpClient", "i2", "Dom<PERSON><PERSON><PERSON>zer", "_c0", "policy", "getPolicy", "undefined", "window", "ttWindow", "trustedTypes", "createPolicy", "createHTML", "s", "trustedHTMLFromString", "html", "getMatIconNameNotFoundError", "iconName", "Error", "getMatIconNoHttpProviderError", "getMatIconFailedToSanitizeUrlError", "url", "getMatIconFailedToSanitizeLiteralError", "literal", "SvgIconConfig", "constructor", "svgText", "options", "MatIconRegistry", "_httpClient", "_sanitizer", "document", "_error<PERSON><PERSON><PERSON>", "_svgIconConfigs", "Map", "_iconSetConfigs", "_cachedIconsByUrl", "_inProgressUrlFetches", "_fontCssClassesByAlias", "_resolvers", "_defaultFontSetClass", "_document", "addSvgIcon", "addSvgIconInNamespace", "addSvgIconLiteral", "addSvgIconLiteralInNamespace", "namespace", "_addSvgIconConfig", "addSvgIconResolver", "resolver", "push", "cleanLiteral", "sanitize", "HTML", "trustedLiteral", "addSvgIconSet", "addSvgIconSetInNamespace", "addSvgIconSetLiteral", "addSvgIconSetLiteralInNamespace", "_addSvgIconSetConfig", "registerFontClassAlias", "alias", "classNames", "set", "classNameForFontAlias", "get", "setDefaultFontSetClass", "getDefaultFontSetClass", "getSvgIconFromUrl", "safeUrl", "RESOURCE_URL", "cachedIcon", "cloneSvg", "_loadSvgIconFromConfig", "pipe", "svg", "getNamedSvgIcon", "name", "key", "<PERSON><PERSON><PERSON>", "config", "_getSvgFromConfig", "_getIconConfigFromResolvers", "iconSetConfigs", "_getSvgFromIconSetConfigs", "ngOnDestroy", "clear", "_svgElementFromConfig", "namedIcon", "_extractIconWithNameFromAnySet", "iconSetFetchRequests", "filter", "iconSetConfig", "_loadSvgIconSetFromConfig", "err", "errorMessage", "message", "handleError", "foundIcon", "i", "length", "toString", "indexOf", "_extractSvgIconFromSet", "_fetchIcon", "iconSet", "iconSource", "querySelector", "iconElement", "cloneNode", "removeAttribute", "nodeName", "toLowerCase", "_setSvgAttributes", "_toSvgElement", "_svgElementFromString", "append<PERSON><PERSON><PERSON>", "str", "div", "createElement", "innerHTML", "element", "attributes", "value", "setAttribute", "childNodes", "nodeType", "ELEMENT_NODE", "viewBox", "iconConfig", "withCredentials", "inProgressFetch", "req", "responseType", "delete", "configNamespace", "svgElement", "result", "isSafeUrlWithOptions", "ɵfac", "MatIconRegistry_Factory", "t", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "type", "args", "decorators", "ICON_REGISTRY_PROVIDER_FACTORY", "parentRegistry", "httpClient", "sanitizer", "<PERSON><PERSON><PERSON><PERSON>", "ICON_REGISTRY_PROVIDER", "provide", "deps", "useFactory", "MAT_ICON_DEFAULT_OPTIONS", "MAT_ICON_LOCATION", "MAT_ICON_LOCATION_FACTORY", "_location", "location", "getPathname", "pathname", "search", "funcIriAttributes", "funcIriAttributeSelector", "attr", "join", "funcIriPattern", "MatIcon", "color", "_color", "_defaultColor", "svgIcon", "_svgIcon", "_updateSvgIcon", "_clearSvgElement", "fontSet", "_fontSet", "newValue", "_cleanupFontValue", "_updateFontIconClasses", "fontIcon", "_fontIcon", "_elementRef", "_iconRegistry", "ariaHidden", "defaults", "inline", "_previousFontSetClass", "_currentIconFetch", "EMPTY", "nativeElement", "_splitIconName", "parts", "split", "ngOnInit", "ngAfterViewChecked", "cachedElements", "_elementsWithExternalReferences", "size", "newPath", "_previousPath", "_prependPathToReferences", "unsubscribe", "_usingFontIcon", "_setSvgElement", "path", "_cacheChildrenWithExternalReferences", "layoutElement", "childCount", "child", "remove", "elem", "fontSetClasses", "className", "for<PERSON>ach", "classList", "add", "_previousFontIconClass", "includes", "trim", "elements", "attrs", "elementsWithFuncIri", "querySelectorAll", "elementWithReference", "getAttribute", "match", "rawName", "_svgNamespace", "_svgName", "subscribe", "MatIcon_Factory", "ɵɵdirectiveInject", "ElementRef", "ɵɵinjectAttribute", "ɵcmp", "ɵɵdefineComponent", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatIcon_HostBindings", "rf", "ctx", "ɵɵattribute", "ɵɵclassMap", "ɵɵclassProp", "inputs", "ɵɵInputFlags", "HasDecoratorInputTransform", "exportAs", "standalone", "features", "ɵɵInputTransformsFeature", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "template", "MatIcon_Template", "ɵɵprojectionDef", "ɵɵprojection", "styles", "encapsulation", "changeDetection", "selector", "host", "None", "OnPush", "transform", "MatIconModule", "MatIconModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/material/fesm2022/icon.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { SecurityContext, Injectable, Optional, Inject, Ski<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, InjectionToken, inject, booleanAttribute, Component, ViewEncapsulation, ChangeDetectionStrategy, Attribute, Input, NgModule } from '@angular/core';\nimport { MatCommonModule } from '@angular/material/core';\nimport { DOCUMENT } from '@angular/common';\nimport { of, throwError, forkJoin, Subscription } from 'rxjs';\nimport { tap, map, catchError, finalize, share, take } from 'rxjs/operators';\nimport * as i1 from '@angular/common/http';\nimport { HttpClient } from '@angular/common/http';\nimport * as i2 from '@angular/platform-browser';\nimport { DomSanitizer } from '@angular/platform-browser';\n\n/**\n * The Trusted Types policy, or null if Trusted Types are not\n * enabled/supported, or undefined if the policy has not been created yet.\n */\nlet policy;\n/**\n * Returns the Trusted Types policy, or null if Trusted Types are not\n * enabled/supported. The first call to this function will create the policy.\n */\nfunction getPolicy() {\n    if (policy === undefined) {\n        policy = null;\n        if (typeof window !== 'undefined') {\n            const ttWindow = window;\n            if (ttWindow.trustedTypes !== undefined) {\n                policy = ttWindow.trustedTypes.createPolicy('angular#components', {\n                    createHTML: (s) => s,\n                });\n            }\n        }\n    }\n    return policy;\n}\n/**\n * Unsafely promote a string to a TrustedHTML, falling back to strings when\n * Trusted Types are not available.\n * @security This is a security-sensitive function; any use of this function\n * must go through security review. In particular, it must be assured that the\n * provided string will never cause an XSS vulnerability if used in a context\n * that will be interpreted as HTML by a browser, e.g. when assigning to\n * element.innerHTML.\n */\nfunction trustedHTMLFromString(html) {\n    return getPolicy()?.createHTML(html) || html;\n}\n\n/**\n * Returns an exception to be thrown in the case when attempting to\n * load an icon with a name that cannot be found.\n * @docs-private\n */\nfunction getMatIconNameNotFoundError(iconName) {\n    return Error(`Unable to find icon with the name \"${iconName}\"`);\n}\n/**\n * Returns an exception to be thrown when the consumer attempts to use\n * `<mat-icon>` without including @angular/common/http.\n * @docs-private\n */\nfunction getMatIconNoHttpProviderError() {\n    return Error('Could not find HttpClient provider for use with Angular Material icons. ' +\n        'Please include the HttpClientModule from @angular/common/http in your ' +\n        'app imports.');\n}\n/**\n * Returns an exception to be thrown when a URL couldn't be sanitized.\n * @param url URL that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeUrlError(url) {\n    return Error(`The URL provided to MatIconRegistry was not trusted as a resource URL ` +\n        `via Angular's DomSanitizer. Attempted URL was \"${url}\".`);\n}\n/**\n * Returns an exception to be thrown when a HTML string couldn't be sanitized.\n * @param literal HTML that was attempted to be sanitized.\n * @docs-private\n */\nfunction getMatIconFailedToSanitizeLiteralError(literal) {\n    return Error(`The literal provided to MatIconRegistry was not trusted as safe HTML by ` +\n        `Angular's DomSanitizer. Attempted literal was \"${literal}\".`);\n}\n/**\n * Configuration for an icon, including the URL and possibly the cached SVG element.\n * @docs-private\n */\nclass SvgIconConfig {\n    constructor(url, svgText, options) {\n        this.url = url;\n        this.svgText = svgText;\n        this.options = options;\n    }\n}\n/**\n * Service to register and display icons used by the `<mat-icon>` component.\n * - Registers icon URLs by namespace and name.\n * - Registers icon set URLs by namespace.\n * - Registers aliases for CSS classes, for use with icon fonts.\n * - Loads icons from URLs and extracts individual icons from icon sets.\n */\nclass MatIconRegistry {\n    constructor(_httpClient, _sanitizer, document, _errorHandler) {\n        this._httpClient = _httpClient;\n        this._sanitizer = _sanitizer;\n        this._errorHandler = _errorHandler;\n        /**\n         * URLs and cached SVG elements for individual icons. Keys are of the format \"[namespace]:[icon]\".\n         */\n        this._svgIconConfigs = new Map();\n        /**\n         * SvgIconConfig objects and cached SVG elements for icon sets, keyed by namespace.\n         * Multiple icon sets can be registered under the same namespace.\n         */\n        this._iconSetConfigs = new Map();\n        /** Cache for icons loaded by direct URLs. */\n        this._cachedIconsByUrl = new Map();\n        /** In-progress icon fetches. Used to coalesce multiple requests to the same URL. */\n        this._inProgressUrlFetches = new Map();\n        /** Map from font identifiers to their CSS class names. Used for icon fonts. */\n        this._fontCssClassesByAlias = new Map();\n        /** Registered icon resolver functions. */\n        this._resolvers = [];\n        /**\n         * The CSS classes to apply when an `<mat-icon>` component has no icon name, url, or font\n         * specified. The default 'material-icons' value assumes that the material icon font has been\n         * loaded as described at https://google.github.io/material-design-icons/#icon-font-for-the-web\n         */\n        this._defaultFontSetClass = ['material-icons', 'mat-ligature-font'];\n        this._document = document;\n    }\n    /**\n     * Registers an icon by URL in the default namespace.\n     * @param iconName Name under which the icon should be registered.\n     * @param url\n     */\n    addSvgIcon(iconName, url, options) {\n        return this.addSvgIconInNamespace('', iconName, url, options);\n    }\n    /**\n     * Registers an icon using an HTML string in the default namespace.\n     * @param iconName Name under which the icon should be registered.\n     * @param literal SVG source of the icon.\n     */\n    addSvgIconLiteral(iconName, literal, options) {\n        return this.addSvgIconLiteralInNamespace('', iconName, literal, options);\n    }\n    /**\n     * Registers an icon by URL in the specified namespace.\n     * @param namespace Namespace in which the icon should be registered.\n     * @param iconName Name under which the icon should be registered.\n     * @param url\n     */\n    addSvgIconInNamespace(namespace, iconName, url, options) {\n        return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig(url, null, options));\n    }\n    /**\n     * Registers an icon resolver function with the registry. The function will be invoked with the\n     * name and namespace of an icon when the registry tries to resolve the URL from which to fetch\n     * the icon. The resolver is expected to return a `SafeResourceUrl` that points to the icon,\n     * an object with the icon URL and icon options, or `null` if the icon is not supported. Resolvers\n     * will be invoked in the order in which they have been registered.\n     * @param resolver Resolver function to be registered.\n     */\n    addSvgIconResolver(resolver) {\n        this._resolvers.push(resolver);\n        return this;\n    }\n    /**\n     * Registers an icon using an HTML string in the specified namespace.\n     * @param namespace Namespace in which the icon should be registered.\n     * @param iconName Name under which the icon should be registered.\n     * @param literal SVG source of the icon.\n     */\n    addSvgIconLiteralInNamespace(namespace, iconName, literal, options) {\n        const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n        // TODO: add an ngDevMode check\n        if (!cleanLiteral) {\n            throw getMatIconFailedToSanitizeLiteralError(literal);\n        }\n        // Security: The literal is passed in as SafeHtml, and is thus trusted.\n        const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n        return this._addSvgIconConfig(namespace, iconName, new SvgIconConfig('', trustedLiteral, options));\n    }\n    /**\n     * Registers an icon set by URL in the default namespace.\n     * @param url\n     */\n    addSvgIconSet(url, options) {\n        return this.addSvgIconSetInNamespace('', url, options);\n    }\n    /**\n     * Registers an icon set using an HTML string in the default namespace.\n     * @param literal SVG source of the icon set.\n     */\n    addSvgIconSetLiteral(literal, options) {\n        return this.addSvgIconSetLiteralInNamespace('', literal, options);\n    }\n    /**\n     * Registers an icon set by URL in the specified namespace.\n     * @param namespace Namespace in which to register the icon set.\n     * @param url\n     */\n    addSvgIconSetInNamespace(namespace, url, options) {\n        return this._addSvgIconSetConfig(namespace, new SvgIconConfig(url, null, options));\n    }\n    /**\n     * Registers an icon set using an HTML string in the specified namespace.\n     * @param namespace Namespace in which to register the icon set.\n     * @param literal SVG source of the icon set.\n     */\n    addSvgIconSetLiteralInNamespace(namespace, literal, options) {\n        const cleanLiteral = this._sanitizer.sanitize(SecurityContext.HTML, literal);\n        if (!cleanLiteral) {\n            throw getMatIconFailedToSanitizeLiteralError(literal);\n        }\n        // Security: The literal is passed in as SafeHtml, and is thus trusted.\n        const trustedLiteral = trustedHTMLFromString(cleanLiteral);\n        return this._addSvgIconSetConfig(namespace, new SvgIconConfig('', trustedLiteral, options));\n    }\n    /**\n     * Defines an alias for CSS class names to be used for icon fonts. Creating an matIcon\n     * component with the alias as the fontSet input will cause the class name to be applied\n     * to the `<mat-icon>` element.\n     *\n     * If the registered font is a ligature font, then don't forget to also include the special\n     * class `mat-ligature-font` to allow the usage via attribute. So register like this:\n     *\n     * ```ts\n     * iconRegistry.registerFontClassAlias('f1', 'font1 mat-ligature-font');\n     * ```\n     *\n     * And use like this:\n     *\n     * ```html\n     * <mat-icon fontSet=\"f1\" fontIcon=\"home\"></mat-icon>\n     * ```\n     *\n     * @param alias Alias for the font.\n     * @param classNames Class names override to be used instead of the alias.\n     */\n    registerFontClassAlias(alias, classNames = alias) {\n        this._fontCssClassesByAlias.set(alias, classNames);\n        return this;\n    }\n    /**\n     * Returns the CSS class name associated with the alias by a previous call to\n     * registerFontClassAlias. If no CSS class has been associated, returns the alias unmodified.\n     */\n    classNameForFontAlias(alias) {\n        return this._fontCssClassesByAlias.get(alias) || alias;\n    }\n    /**\n     * Sets the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n     * have a fontSet input value, and is not loading an icon by name or URL.\n     */\n    setDefaultFontSetClass(...classNames) {\n        this._defaultFontSetClass = classNames;\n        return this;\n    }\n    /**\n     * Returns the CSS classes to be used for icon fonts when an `<mat-icon>` component does not\n     * have a fontSet input value, and is not loading an icon by name or URL.\n     */\n    getDefaultFontSetClass() {\n        return this._defaultFontSetClass;\n    }\n    /**\n     * Returns an Observable that produces the icon (as an `<svg>` DOM element) from the given URL.\n     * The response from the URL may be cached so this will not always cause an HTTP request, but\n     * the produced element will always be a new copy of the originally fetched icon. (That is,\n     * it will not contain any modifications made to elements previously returned).\n     *\n     * @param safeUrl URL from which to fetch the SVG icon.\n     */\n    getSvgIconFromUrl(safeUrl) {\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n        if (!url) {\n            throw getMatIconFailedToSanitizeUrlError(safeUrl);\n        }\n        const cachedIcon = this._cachedIconsByUrl.get(url);\n        if (cachedIcon) {\n            return of(cloneSvg(cachedIcon));\n        }\n        return this._loadSvgIconFromConfig(new SvgIconConfig(safeUrl, null)).pipe(tap(svg => this._cachedIconsByUrl.set(url, svg)), map(svg => cloneSvg(svg)));\n    }\n    /**\n     * Returns an Observable that produces the icon (as an `<svg>` DOM element) with the given name\n     * and namespace. The icon must have been previously registered with addIcon or addIconSet;\n     * if not, the Observable will throw an error.\n     *\n     * @param name Name of the icon to be retrieved.\n     * @param namespace Namespace in which to look for the icon.\n     */\n    getNamedSvgIcon(name, namespace = '') {\n        const key = iconKey(namespace, name);\n        let config = this._svgIconConfigs.get(key);\n        // Return (copy of) cached icon if possible.\n        if (config) {\n            return this._getSvgFromConfig(config);\n        }\n        // Otherwise try to resolve the config from one of the resolver functions.\n        config = this._getIconConfigFromResolvers(namespace, name);\n        if (config) {\n            this._svgIconConfigs.set(key, config);\n            return this._getSvgFromConfig(config);\n        }\n        // See if we have any icon sets registered for the namespace.\n        const iconSetConfigs = this._iconSetConfigs.get(namespace);\n        if (iconSetConfigs) {\n            return this._getSvgFromIconSetConfigs(name, iconSetConfigs);\n        }\n        return throwError(getMatIconNameNotFoundError(key));\n    }\n    ngOnDestroy() {\n        this._resolvers = [];\n        this._svgIconConfigs.clear();\n        this._iconSetConfigs.clear();\n        this._cachedIconsByUrl.clear();\n    }\n    /**\n     * Returns the cached icon for a SvgIconConfig if available, or fetches it from its URL if not.\n     */\n    _getSvgFromConfig(config) {\n        if (config.svgText) {\n            // We already have the SVG element for this icon, return a copy.\n            return of(cloneSvg(this._svgElementFromConfig(config)));\n        }\n        else {\n            // Fetch the icon from the config's URL, cache it, and return a copy.\n            return this._loadSvgIconFromConfig(config).pipe(map(svg => cloneSvg(svg)));\n        }\n    }\n    /**\n     * Attempts to find an icon with the specified name in any of the SVG icon sets.\n     * First searches the available cached icons for a nested element with a matching name, and\n     * if found copies the element to a new `<svg>` element. If not found, fetches all icon sets\n     * that have not been cached, and searches again after all fetches are completed.\n     * The returned Observable produces the SVG element if possible, and throws\n     * an error if no icon with the specified name can be found.\n     */\n    _getSvgFromIconSetConfigs(name, iconSetConfigs) {\n        // For all the icon set SVG elements we've fetched, see if any contain an icon with the\n        // requested name.\n        const namedIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n        if (namedIcon) {\n            // We could cache namedIcon in _svgIconConfigs, but since we have to make a copy every\n            // time anyway, there's probably not much advantage compared to just always extracting\n            // it from the icon set.\n            return of(namedIcon);\n        }\n        // Not found in any cached icon sets. If there are icon sets with URLs that we haven't\n        // fetched, fetch them now and look for iconName in the results.\n        const iconSetFetchRequests = iconSetConfigs\n            .filter(iconSetConfig => !iconSetConfig.svgText)\n            .map(iconSetConfig => {\n            return this._loadSvgIconSetFromConfig(iconSetConfig).pipe(catchError((err) => {\n                const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, iconSetConfig.url);\n                // Swallow errors fetching individual URLs so the\n                // combined Observable won't necessarily fail.\n                const errorMessage = `Loading icon set URL: ${url} failed: ${err.message}`;\n                this._errorHandler.handleError(new Error(errorMessage));\n                return of(null);\n            }));\n        });\n        // Fetch all the icon set URLs. When the requests complete, every IconSet should have a\n        // cached SVG element (unless the request failed), and we can check again for the icon.\n        return forkJoin(iconSetFetchRequests).pipe(map(() => {\n            const foundIcon = this._extractIconWithNameFromAnySet(name, iconSetConfigs);\n            // TODO: add an ngDevMode check\n            if (!foundIcon) {\n                throw getMatIconNameNotFoundError(name);\n            }\n            return foundIcon;\n        }));\n    }\n    /**\n     * Searches the cached SVG elements for the given icon sets for a nested icon element whose \"id\"\n     * tag matches the specified name. If found, copies the nested element to a new SVG element and\n     * returns it. Returns null if no matching element is found.\n     */\n    _extractIconWithNameFromAnySet(iconName, iconSetConfigs) {\n        // Iterate backwards, so icon sets added later have precedence.\n        for (let i = iconSetConfigs.length - 1; i >= 0; i--) {\n            const config = iconSetConfigs[i];\n            // Parsing the icon set's text into an SVG element can be expensive. We can avoid some of\n            // the parsing by doing a quick check using `indexOf` to see if there's any chance for the\n            // icon to be in the set. This won't be 100% accurate, but it should help us avoid at least\n            // some of the parsing.\n            if (config.svgText && config.svgText.toString().indexOf(iconName) > -1) {\n                const svg = this._svgElementFromConfig(config);\n                const foundIcon = this._extractSvgIconFromSet(svg, iconName, config.options);\n                if (foundIcon) {\n                    return foundIcon;\n                }\n            }\n        }\n        return null;\n    }\n    /**\n     * Loads the content of the icon URL specified in the SvgIconConfig and creates an SVG element\n     * from it.\n     */\n    _loadSvgIconFromConfig(config) {\n        return this._fetchIcon(config).pipe(tap(svgText => (config.svgText = svgText)), map(() => this._svgElementFromConfig(config)));\n    }\n    /**\n     * Loads the content of the icon set URL specified in the\n     * SvgIconConfig and attaches it to the config.\n     */\n    _loadSvgIconSetFromConfig(config) {\n        if (config.svgText) {\n            return of(null);\n        }\n        return this._fetchIcon(config).pipe(tap(svgText => (config.svgText = svgText)));\n    }\n    /**\n     * Searches the cached element of the given SvgIconConfig for a nested icon element whose \"id\"\n     * tag matches the specified name. If found, copies the nested element to a new SVG element and\n     * returns it. Returns null if no matching element is found.\n     */\n    _extractSvgIconFromSet(iconSet, iconName, options) {\n        // Use the `id=\"iconName\"` syntax in order to escape special\n        // characters in the ID (versus using the #iconName syntax).\n        const iconSource = iconSet.querySelector(`[id=\"${iconName}\"]`);\n        if (!iconSource) {\n            return null;\n        }\n        // Clone the element and remove the ID to prevent multiple elements from being added\n        // to the page with the same ID.\n        const iconElement = iconSource.cloneNode(true);\n        iconElement.removeAttribute('id');\n        // If the icon node is itself an <svg> node, clone and return it directly. If not, set it as\n        // the content of a new <svg> node.\n        if (iconElement.nodeName.toLowerCase() === 'svg') {\n            return this._setSvgAttributes(iconElement, options);\n        }\n        // If the node is a <symbol>, it won't be rendered so we have to convert it into <svg>. Note\n        // that the same could be achieved by referring to it via <use href=\"#id\">, however the <use>\n        // tag is problematic on Firefox, because it needs to include the current page path.\n        if (iconElement.nodeName.toLowerCase() === 'symbol') {\n            return this._setSvgAttributes(this._toSvgElement(iconElement), options);\n        }\n        // createElement('SVG') doesn't work as expected; the DOM ends up with\n        // the correct nodes, but the SVG content doesn't render. Instead we\n        // have to create an empty SVG node using innerHTML and append its content.\n        // Elements created using DOMParser.parseFromString have the same problem.\n        // http://stackoverflow.com/questions/23003278/svg-innerhtml-in-firefox-can-not-display\n        const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n        // Clone the node so we don't remove it from the parent icon set element.\n        svg.appendChild(iconElement);\n        return this._setSvgAttributes(svg, options);\n    }\n    /**\n     * Creates a DOM element from the given SVG string.\n     */\n    _svgElementFromString(str) {\n        const div = this._document.createElement('DIV');\n        div.innerHTML = str;\n        const svg = div.querySelector('svg');\n        // TODO: add an ngDevMode check\n        if (!svg) {\n            throw Error('<svg> tag not found');\n        }\n        return svg;\n    }\n    /**\n     * Converts an element into an SVG node by cloning all of its children.\n     */\n    _toSvgElement(element) {\n        const svg = this._svgElementFromString(trustedHTMLFromString('<svg></svg>'));\n        const attributes = element.attributes;\n        // Copy over all the attributes from the `symbol` to the new SVG, except the id.\n        for (let i = 0; i < attributes.length; i++) {\n            const { name, value } = attributes[i];\n            if (name !== 'id') {\n                svg.setAttribute(name, value);\n            }\n        }\n        for (let i = 0; i < element.childNodes.length; i++) {\n            if (element.childNodes[i].nodeType === this._document.ELEMENT_NODE) {\n                svg.appendChild(element.childNodes[i].cloneNode(true));\n            }\n        }\n        return svg;\n    }\n    /**\n     * Sets the default attributes for an SVG element to be used as an icon.\n     */\n    _setSvgAttributes(svg, options) {\n        svg.setAttribute('fit', '');\n        svg.setAttribute('height', '100%');\n        svg.setAttribute('width', '100%');\n        svg.setAttribute('preserveAspectRatio', 'xMidYMid meet');\n        svg.setAttribute('focusable', 'false'); // Disable IE11 default behavior to make SVGs focusable.\n        if (options && options.viewBox) {\n            svg.setAttribute('viewBox', options.viewBox);\n        }\n        return svg;\n    }\n    /**\n     * Returns an Observable which produces the string contents of the given icon. Results may be\n     * cached, so future calls with the same URL may not cause another HTTP request.\n     */\n    _fetchIcon(iconConfig) {\n        const { url: safeUrl, options } = iconConfig;\n        const withCredentials = options?.withCredentials ?? false;\n        if (!this._httpClient) {\n            throw getMatIconNoHttpProviderError();\n        }\n        // TODO: add an ngDevMode check\n        if (safeUrl == null) {\n            throw Error(`Cannot fetch icon from URL \"${safeUrl}\".`);\n        }\n        const url = this._sanitizer.sanitize(SecurityContext.RESOURCE_URL, safeUrl);\n        // TODO: add an ngDevMode check\n        if (!url) {\n            throw getMatIconFailedToSanitizeUrlError(safeUrl);\n        }\n        // Store in-progress fetches to avoid sending a duplicate request for a URL when there is\n        // already a request in progress for that URL. It's necessary to call share() on the\n        // Observable returned by http.get() so that multiple subscribers don't cause multiple XHRs.\n        const inProgressFetch = this._inProgressUrlFetches.get(url);\n        if (inProgressFetch) {\n            return inProgressFetch;\n        }\n        const req = this._httpClient.get(url, { responseType: 'text', withCredentials }).pipe(map(svg => {\n            // Security: This SVG is fetched from a SafeResourceUrl, and is thus\n            // trusted HTML.\n            return trustedHTMLFromString(svg);\n        }), finalize(() => this._inProgressUrlFetches.delete(url)), share());\n        this._inProgressUrlFetches.set(url, req);\n        return req;\n    }\n    /**\n     * Registers an icon config by name in the specified namespace.\n     * @param namespace Namespace in which to register the icon config.\n     * @param iconName Name under which to register the config.\n     * @param config Config to be registered.\n     */\n    _addSvgIconConfig(namespace, iconName, config) {\n        this._svgIconConfigs.set(iconKey(namespace, iconName), config);\n        return this;\n    }\n    /**\n     * Registers an icon set config in the specified namespace.\n     * @param namespace Namespace in which to register the icon config.\n     * @param config Config to be registered.\n     */\n    _addSvgIconSetConfig(namespace, config) {\n        const configNamespace = this._iconSetConfigs.get(namespace);\n        if (configNamespace) {\n            configNamespace.push(config);\n        }\n        else {\n            this._iconSetConfigs.set(namespace, [config]);\n        }\n        return this;\n    }\n    /** Parses a config's text into an SVG element. */\n    _svgElementFromConfig(config) {\n        if (!config.svgElement) {\n            const svg = this._svgElementFromString(config.svgText);\n            this._setSvgAttributes(svg, config.options);\n            config.svgElement = svg;\n        }\n        return config.svgElement;\n    }\n    /** Tries to create an icon config through the registered resolver functions. */\n    _getIconConfigFromResolvers(namespace, name) {\n        for (let i = 0; i < this._resolvers.length; i++) {\n            const result = this._resolvers[i](name, namespace);\n            if (result) {\n                return isSafeUrlWithOptions(result)\n                    ? new SvgIconConfig(result.url, null, result.options)\n                    : new SvgIconConfig(result, null);\n            }\n        }\n        return undefined;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatIconRegistry, deps: [{ token: i1.HttpClient, optional: true }, { token: i2.DomSanitizer }, { token: DOCUMENT, optional: true }, { token: i0.ErrorHandler }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatIconRegistry, providedIn: 'root' }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatIconRegistry, decorators: [{\n            type: Injectable,\n            args: [{ providedIn: 'root' }]\n        }], ctorParameters: () => [{ type: i1.HttpClient, decorators: [{\n                    type: Optional\n                }] }, { type: i2.DomSanitizer }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [DOCUMENT]\n                }] }, { type: i0.ErrorHandler }] });\n/** @docs-private */\nfunction ICON_REGISTRY_PROVIDER_FACTORY(parentRegistry, httpClient, sanitizer, errorHandler, document) {\n    return parentRegistry || new MatIconRegistry(httpClient, sanitizer, document, errorHandler);\n}\n/** @docs-private */\nconst ICON_REGISTRY_PROVIDER = {\n    // If there is already an MatIconRegistry available, use that. Otherwise, provide a new one.\n    provide: MatIconRegistry,\n    deps: [\n        [new Optional(), new SkipSelf(), MatIconRegistry],\n        [new Optional(), HttpClient],\n        DomSanitizer,\n        ErrorHandler,\n        [new Optional(), DOCUMENT],\n    ],\n    useFactory: ICON_REGISTRY_PROVIDER_FACTORY,\n};\n/** Clones an SVGElement while preserving type information. */\nfunction cloneSvg(svg) {\n    return svg.cloneNode(true);\n}\n/** Returns the cache key to use for an icon namespace and name. */\nfunction iconKey(namespace, name) {\n    return namespace + ':' + name;\n}\nfunction isSafeUrlWithOptions(value) {\n    return !!(value.url && value.options);\n}\n\n/** Injection token to be used to override the default options for `mat-icon`. */\nconst MAT_ICON_DEFAULT_OPTIONS = new InjectionToken('MAT_ICON_DEFAULT_OPTIONS');\n/**\n * Injection token used to provide the current location to `MatIcon`.\n * Used to handle server-side rendering and to stub out during unit tests.\n * @docs-private\n */\nconst MAT_ICON_LOCATION = new InjectionToken('mat-icon-location', {\n    providedIn: 'root',\n    factory: MAT_ICON_LOCATION_FACTORY,\n});\n/** @docs-private */\nfunction MAT_ICON_LOCATION_FACTORY() {\n    const _document = inject(DOCUMENT);\n    const _location = _document ? _document.location : null;\n    return {\n        // Note that this needs to be a function, rather than a property, because Angular\n        // will only resolve it once, but we want the current path on each call.\n        getPathname: () => (_location ? _location.pathname + _location.search : ''),\n    };\n}\n/** SVG attributes that accept a FuncIRI (e.g. `url(<something>)`). */\nconst funcIriAttributes = [\n    'clip-path',\n    'color-profile',\n    'src',\n    'cursor',\n    'fill',\n    'filter',\n    'marker',\n    'marker-start',\n    'marker-mid',\n    'marker-end',\n    'mask',\n    'stroke',\n];\n/** Selector that can be used to find all elements that are using a `FuncIRI`. */\nconst funcIriAttributeSelector = funcIriAttributes.map(attr => `[${attr}]`).join(', ');\n/** Regex that can be used to extract the id out of a FuncIRI. */\nconst funcIriPattern = /^url\\(['\"]?#(.*?)['\"]?\\)$/;\n/**\n * Component to display an icon. It can be used in the following ways:\n *\n * - Specify the svgIcon input to load an SVG icon from a URL previously registered with the\n *   addSvgIcon, addSvgIconInNamespace, addSvgIconSet, or addSvgIconSetInNamespace methods of\n *   MatIconRegistry. If the svgIcon value contains a colon it is assumed to be in the format\n *   \"[namespace]:[name]\", if not the value will be the name of an icon in the default namespace.\n *   Examples:\n *     `<mat-icon svgIcon=\"left-arrow\"></mat-icon>\n *     <mat-icon svgIcon=\"animals:cat\"></mat-icon>`\n *\n * - Use a font ligature as an icon by putting the ligature text in the `fontIcon` attribute or the\n *   content of the `<mat-icon>` component. If you register a custom font class, don't forget to also\n *   include the special class `mat-ligature-font`. It is recommended to use the attribute alternative\n *   to prevent the ligature text to be selectable and to appear in search engine results.\n *   By default, the Material icons font is used as described at\n *   http://google.github.io/material-design-icons/#icon-font-for-the-web. You can specify an\n *   alternate font by setting the fontSet input to either the CSS class to apply to use the\n *   desired font, or to an alias previously registered with MatIconRegistry.registerFontClassAlias.\n *   Examples:\n *     `<mat-icon fontIcon=\"home\"></mat-icon>\n *     <mat-icon>home</mat-icon>\n *     <mat-icon fontSet=\"myfont\" fontIcon=\"sun\"></mat-icon>\n *     <mat-icon fontSet=\"myfont\">sun</mat-icon>`\n *\n * - Specify a font glyph to be included via CSS rules by setting the fontSet input to specify the\n *   font, and the fontIcon input to specify the icon. Typically the fontIcon will specify a\n *   CSS class which causes the glyph to be displayed via a :before selector, as in\n *   https://fortawesome.github.io/Font-Awesome/examples/\n *   Example:\n *     `<mat-icon fontSet=\"fa\" fontIcon=\"alarm\"></mat-icon>`\n */\nclass MatIcon {\n    /** Theme palette color of the icon. */\n    get color() {\n        return this._color || this._defaultColor;\n    }\n    set color(value) {\n        this._color = value;\n    }\n    /** Name of the icon in the SVG icon set. */\n    get svgIcon() {\n        return this._svgIcon;\n    }\n    set svgIcon(value) {\n        if (value !== this._svgIcon) {\n            if (value) {\n                this._updateSvgIcon(value);\n            }\n            else if (this._svgIcon) {\n                this._clearSvgElement();\n            }\n            this._svgIcon = value;\n        }\n    }\n    /** Font set that the icon is a part of. */\n    get fontSet() {\n        return this._fontSet;\n    }\n    set fontSet(value) {\n        const newValue = this._cleanupFontValue(value);\n        if (newValue !== this._fontSet) {\n            this._fontSet = newValue;\n            this._updateFontIconClasses();\n        }\n    }\n    /** Name of an icon within a font set. */\n    get fontIcon() {\n        return this._fontIcon;\n    }\n    set fontIcon(value) {\n        const newValue = this._cleanupFontValue(value);\n        if (newValue !== this._fontIcon) {\n            this._fontIcon = newValue;\n            this._updateFontIconClasses();\n        }\n    }\n    constructor(_elementRef, _iconRegistry, ariaHidden, _location, _errorHandler, defaults) {\n        this._elementRef = _elementRef;\n        this._iconRegistry = _iconRegistry;\n        this._location = _location;\n        this._errorHandler = _errorHandler;\n        /**\n         * Whether the icon should be inlined, automatically sizing the icon to match the font size of\n         * the element the icon is contained in.\n         */\n        this.inline = false;\n        this._previousFontSetClass = [];\n        /** Subscription to the current in-progress SVG icon request. */\n        this._currentIconFetch = Subscription.EMPTY;\n        if (defaults) {\n            if (defaults.color) {\n                this.color = this._defaultColor = defaults.color;\n            }\n            if (defaults.fontSet) {\n                this.fontSet = defaults.fontSet;\n            }\n        }\n        // If the user has not explicitly set aria-hidden, mark the icon as hidden, as this is\n        // the right thing to do for the majority of icon use-cases.\n        if (!ariaHidden) {\n            _elementRef.nativeElement.setAttribute('aria-hidden', 'true');\n        }\n    }\n    /**\n     * Splits an svgIcon binding value into its icon set and icon name components.\n     * Returns a 2-element array of [(icon set), (icon name)].\n     * The separator for the two fields is ':'. If there is no separator, an empty\n     * string is returned for the icon set and the entire value is returned for\n     * the icon name. If the argument is falsy, returns an array of two empty strings.\n     * Throws an error if the name contains two or more ':' separators.\n     * Examples:\n     *   `'social:cake' -> ['social', 'cake']\n     *   'penguin' -> ['', 'penguin']\n     *   null -> ['', '']\n     *   'a:b:c' -> (throws Error)`\n     */\n    _splitIconName(iconName) {\n        if (!iconName) {\n            return ['', ''];\n        }\n        const parts = iconName.split(':');\n        switch (parts.length) {\n            case 1:\n                return ['', parts[0]]; // Use default namespace.\n            case 2:\n                return parts;\n            default:\n                throw Error(`Invalid icon name: \"${iconName}\"`); // TODO: add an ngDevMode check\n        }\n    }\n    ngOnInit() {\n        // Update font classes because ngOnChanges won't be called if none of the inputs are present,\n        // e.g. <mat-icon>arrow</mat-icon> In this case we need to add a CSS class for the default font.\n        this._updateFontIconClasses();\n    }\n    ngAfterViewChecked() {\n        const cachedElements = this._elementsWithExternalReferences;\n        if (cachedElements && cachedElements.size) {\n            const newPath = this._location.getPathname();\n            // We need to check whether the URL has changed on each change detection since\n            // the browser doesn't have an API that will let us react on link clicks and\n            // we can't depend on the Angular router. The references need to be updated,\n            // because while most browsers don't care whether the URL is correct after\n            // the first render, Safari will break if the user navigates to a different\n            // page and the SVG isn't re-rendered.\n            if (newPath !== this._previousPath) {\n                this._previousPath = newPath;\n                this._prependPathToReferences(newPath);\n            }\n        }\n    }\n    ngOnDestroy() {\n        this._currentIconFetch.unsubscribe();\n        if (this._elementsWithExternalReferences) {\n            this._elementsWithExternalReferences.clear();\n        }\n    }\n    _usingFontIcon() {\n        return !this.svgIcon;\n    }\n    _setSvgElement(svg) {\n        this._clearSvgElement();\n        // Note: we do this fix here, rather than the icon registry, because the\n        // references have to point to the URL at the time that the icon was created.\n        const path = this._location.getPathname();\n        this._previousPath = path;\n        this._cacheChildrenWithExternalReferences(svg);\n        this._prependPathToReferences(path);\n        this._elementRef.nativeElement.appendChild(svg);\n    }\n    _clearSvgElement() {\n        const layoutElement = this._elementRef.nativeElement;\n        let childCount = layoutElement.childNodes.length;\n        if (this._elementsWithExternalReferences) {\n            this._elementsWithExternalReferences.clear();\n        }\n        // Remove existing non-element child nodes and SVGs, and add the new SVG element. Note that\n        // we can't use innerHTML, because IE will throw if the element has a data binding.\n        while (childCount--) {\n            const child = layoutElement.childNodes[childCount];\n            // 1 corresponds to Node.ELEMENT_NODE. We remove all non-element nodes in order to get rid\n            // of any loose text nodes, as well as any SVG elements in order to remove any old icons.\n            if (child.nodeType !== 1 || child.nodeName.toLowerCase() === 'svg') {\n                child.remove();\n            }\n        }\n    }\n    _updateFontIconClasses() {\n        if (!this._usingFontIcon()) {\n            return;\n        }\n        const elem = this._elementRef.nativeElement;\n        const fontSetClasses = (this.fontSet\n            ? this._iconRegistry.classNameForFontAlias(this.fontSet).split(/ +/)\n            : this._iconRegistry.getDefaultFontSetClass()).filter(className => className.length > 0);\n        this._previousFontSetClass.forEach(className => elem.classList.remove(className));\n        fontSetClasses.forEach(className => elem.classList.add(className));\n        this._previousFontSetClass = fontSetClasses;\n        if (this.fontIcon !== this._previousFontIconClass &&\n            !fontSetClasses.includes('mat-ligature-font')) {\n            if (this._previousFontIconClass) {\n                elem.classList.remove(this._previousFontIconClass);\n            }\n            if (this.fontIcon) {\n                elem.classList.add(this.fontIcon);\n            }\n            this._previousFontIconClass = this.fontIcon;\n        }\n    }\n    /**\n     * Cleans up a value to be used as a fontIcon or fontSet.\n     * Since the value ends up being assigned as a CSS class, we\n     * have to trim the value and omit space-separated values.\n     */\n    _cleanupFontValue(value) {\n        return typeof value === 'string' ? value.trim().split(' ')[0] : value;\n    }\n    /**\n     * Prepends the current path to all elements that have an attribute pointing to a `FuncIRI`\n     * reference. This is required because WebKit browsers require references to be prefixed with\n     * the current path, if the page has a `base` tag.\n     */\n    _prependPathToReferences(path) {\n        const elements = this._elementsWithExternalReferences;\n        if (elements) {\n            elements.forEach((attrs, element) => {\n                attrs.forEach(attr => {\n                    element.setAttribute(attr.name, `url('${path}#${attr.value}')`);\n                });\n            });\n        }\n    }\n    /**\n     * Caches the children of an SVG element that have `url()`\n     * references that we need to prefix with the current path.\n     */\n    _cacheChildrenWithExternalReferences(element) {\n        const elementsWithFuncIri = element.querySelectorAll(funcIriAttributeSelector);\n        const elements = (this._elementsWithExternalReferences =\n            this._elementsWithExternalReferences || new Map());\n        for (let i = 0; i < elementsWithFuncIri.length; i++) {\n            funcIriAttributes.forEach(attr => {\n                const elementWithReference = elementsWithFuncIri[i];\n                const value = elementWithReference.getAttribute(attr);\n                const match = value ? value.match(funcIriPattern) : null;\n                if (match) {\n                    let attributes = elements.get(elementWithReference);\n                    if (!attributes) {\n                        attributes = [];\n                        elements.set(elementWithReference, attributes);\n                    }\n                    attributes.push({ name: attr, value: match[1] });\n                }\n            });\n        }\n    }\n    /** Sets a new SVG icon with a particular name. */\n    _updateSvgIcon(rawName) {\n        this._svgNamespace = null;\n        this._svgName = null;\n        this._currentIconFetch.unsubscribe();\n        if (rawName) {\n            const [namespace, iconName] = this._splitIconName(rawName);\n            if (namespace) {\n                this._svgNamespace = namespace;\n            }\n            if (iconName) {\n                this._svgName = iconName;\n            }\n            this._currentIconFetch = this._iconRegistry\n                .getNamedSvgIcon(iconName, namespace)\n                .pipe(take(1))\n                .subscribe(svg => this._setSvgElement(svg), (err) => {\n                const errorMessage = `Error retrieving icon ${namespace}:${iconName}! ${err.message}`;\n                this._errorHandler.handleError(new Error(errorMessage));\n            });\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatIcon, deps: [{ token: i0.ElementRef }, { token: MatIconRegistry }, { token: 'aria-hidden', attribute: true }, { token: MAT_ICON_LOCATION }, { token: i0.ErrorHandler }, { token: MAT_ICON_DEFAULT_OPTIONS, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"16.1.0\", version: \"17.2.0\", type: MatIcon, isStandalone: true, selector: \"mat-icon\", inputs: { color: \"color\", inline: [\"inline\", \"inline\", booleanAttribute], svgIcon: \"svgIcon\", fontSet: \"fontSet\", fontIcon: \"fontIcon\" }, host: { attributes: { \"role\": \"img\" }, properties: { \"class\": \"color ? \\\"mat-\\\" + color : \\\"\\\"\", \"attr.data-mat-icon-type\": \"_usingFontIcon() ? \\\"font\\\" : \\\"svg\\\"\", \"attr.data-mat-icon-name\": \"_svgName || fontIcon\", \"attr.data-mat-icon-namespace\": \"_svgNamespace || fontSet\", \"attr.fontIcon\": \"_usingFontIcon() ? fontIcon : null\", \"class.mat-icon-inline\": \"inline\", \"class.mat-icon-no-color\": \"color !== \\\"primary\\\" && color !== \\\"accent\\\" && color !== \\\"warn\\\"\" }, classAttribute: \"mat-icon notranslate\" }, exportAs: [\"matIcon\"], ngImport: i0, template: '<ng-content></ng-content>', isInline: true, styles: [\"mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatIcon, decorators: [{\n            type: Component,\n            args: [{ template: '<ng-content></ng-content>', selector: 'mat-icon', exportAs: 'matIcon', host: {\n                        'role': 'img',\n                        'class': 'mat-icon notranslate',\n                        '[class]': 'color ? \"mat-\" + color : \"\"',\n                        '[attr.data-mat-icon-type]': '_usingFontIcon() ? \"font\" : \"svg\"',\n                        '[attr.data-mat-icon-name]': '_svgName || fontIcon',\n                        '[attr.data-mat-icon-namespace]': '_svgNamespace || fontSet',\n                        '[attr.fontIcon]': '_usingFontIcon() ? fontIcon : null',\n                        '[class.mat-icon-inline]': 'inline',\n                        '[class.mat-icon-no-color]': 'color !== \"primary\" && color !== \"accent\" && color !== \"warn\"',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, standalone: true, styles: [\"mat-icon,mat-icon.mat-primary,mat-icon.mat-accent,mat-icon.mat-warn{color:var(--mat-icon-color)}.mat-icon{-webkit-user-select:none;user-select:none;background-repeat:no-repeat;display:inline-block;fill:currentColor;height:24px;width:24px;overflow:hidden}.mat-icon.mat-icon-inline{font-size:inherit;height:inherit;line-height:inherit;width:inherit}.mat-icon.mat-ligature-font[fontIcon]::before{content:attr(fontIcon)}[dir=rtl] .mat-icon-rtl-mirror{transform:scale(-1, 1)}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon{display:block}.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-prefix .mat-icon-button .mat-icon,.mat-form-field:not(.mat-form-field-appearance-legacy) .mat-form-field-suffix .mat-icon-button .mat-icon{margin:auto}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: MatIconRegistry }, { type: undefined, decorators: [{\n                    type: Attribute,\n                    args: ['aria-hidden']\n                }] }, { type: undefined, decorators: [{\n                    type: Inject,\n                    args: [MAT_ICON_LOCATION]\n                }] }, { type: i0.ErrorHandler }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_ICON_DEFAULT_OPTIONS]\n                }] }], propDecorators: { color: [{\n                type: Input\n            }], inline: [{\n                type: Input,\n                args: [{ transform: booleanAttribute }]\n            }], svgIcon: [{\n                type: Input\n            }], fontSet: [{\n                type: Input\n            }], fontIcon: [{\n                type: Input\n            }] } });\n\nclass MatIconModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatIconModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatIconModule, imports: [MatCommonModule, MatIcon], exports: [MatIcon, MatCommonModule] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatIconModule, imports: [MatCommonModule, MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatIconModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [MatCommonModule, MatIcon],\n                    exports: [MatIcon, MatCommonModule],\n                }]\n        }] });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ICON_REGISTRY_PROVIDER, ICON_REGISTRY_PROVIDER_FACTORY, MAT_ICON_DEFAULT_OPTIONS, MAT_ICON_LOCATION, MAT_ICON_LOCATION_FACTORY, MatIcon, MatIconModule, MatIconRegistry, getMatIconFailedToSanitizeLiteralError, getMatIconFailedToSanitizeUrlError, getMatIconNameNotFoundError, getMatIconNoHttpProviderError };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,eAAe,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,YAAY,EAAEC,cAAc,EAAEC,MAAM,EAAEC,gBAAgB,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,SAAS,EAAEC,KAAK,EAAEC,QAAQ,QAAQ,eAAe;AAClO,SAASC,eAAe,QAAQ,wBAAwB;AACxD,SAASC,QAAQ,QAAQ,iBAAiB;AAC1C,SAASC,EAAE,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,YAAY,QAAQ,MAAM;AAC7D,SAASC,GAAG,EAAEC,GAAG,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,KAAK,EAAEC,IAAI,QAAQ,gBAAgB;AAC5E,OAAO,KAAKC,EAAE,MAAM,sBAAsB;AAC1C,SAASC,UAAU,QAAQ,sBAAsB;AACjD,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,YAAY,QAAQ,2BAA2B;;AAExD;AACA;AACA;AACA;AAHA,MAAAC,GAAA;AAIA,IAAIC,MAAM;AACV;AACA;AACA;AACA;AACA,SAASC,SAASA,CAAA,EAAG;EACjB,IAAID,MAAM,KAAKE,SAAS,EAAE;IACtBF,MAAM,GAAG,IAAI;IACb,IAAI,OAAOG,MAAM,KAAK,WAAW,EAAE;MAC/B,MAAMC,QAAQ,GAAGD,MAAM;MACvB,IAAIC,QAAQ,CAACC,YAAY,KAAKH,SAAS,EAAE;QACrCF,MAAM,GAAGI,QAAQ,CAACC,YAAY,CAACC,YAAY,CAAC,oBAAoB,EAAE;UAC9DC,UAAU,EAAGC,CAAC,IAAKA;QACvB,CAAC,CAAC;MACN;IACJ;EACJ;EACA,OAAOR,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,qBAAqBA,CAACC,IAAI,EAAE;EACjC,OAAOT,SAAS,CAAC,CAAC,EAAEM,UAAU,CAACG,IAAI,CAAC,IAAIA,IAAI;AAChD;;AAEA;AACA;AACA;AACA;AACA;AACA,SAASC,2BAA2BA,CAACC,QAAQ,EAAE;EAC3C,OAAOC,KAAK,CAAC,sCAAsCD,QAAQ,GAAG,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,6BAA6BA,CAAA,EAAG;EACrC,OAAOD,KAAK,CAAC,0EAA0E,GACnF,wEAAwE,GACxE,cAAc,CAAC;AACvB;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,kCAAkCA,CAACC,GAAG,EAAE;EAC7C,OAAOH,KAAK,CAAC,wEAAwE,GACjF,kDAAkDG,GAAG,IAAI,CAAC;AAClE;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,sCAAsCA,CAACC,OAAO,EAAE;EACrD,OAAOL,KAAK,CAAC,0EAA0E,GACnF,kDAAkDK,OAAO,IAAI,CAAC;AACtE;AACA;AACA;AACA;AACA;AACA,MAAMC,aAAa,CAAC;EAChBC,WAAWA,CAACJ,GAAG,EAAEK,OAAO,EAAEC,OAAO,EAAE;IAC/B,IAAI,CAACN,GAAG,GAAGA,GAAG;IACd,IAAI,CAACK,OAAO,GAAGA,OAAO;IACtB,IAAI,CAACC,OAAO,GAAGA,OAAO;EAC1B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,CAAC;EAClBH,WAAWA,CAACI,WAAW,EAAEC,UAAU,EAAEC,QAAQ,EAAEC,aAAa,EAAE;IAC1D,IAAI,CAACH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACE,aAAa,GAAGA,aAAa;IAClC;AACR;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAIC,GAAG,CAAC,CAAC;IAChC;AACR;AACA;AACA;IACQ,IAAI,CAACC,eAAe,GAAG,IAAID,GAAG,CAAC,CAAC;IAChC;IACA,IAAI,CAACE,iBAAiB,GAAG,IAAIF,GAAG,CAAC,CAAC;IAClC;IACA,IAAI,CAACG,qBAAqB,GAAG,IAAIH,GAAG,CAAC,CAAC;IACtC;IACA,IAAI,CAACI,sBAAsB,GAAG,IAAIJ,GAAG,CAAC,CAAC;IACvC;IACA,IAAI,CAACK,UAAU,GAAG,EAAE;IACpB;AACR;AACA;AACA;AACA;IACQ,IAAI,CAACC,oBAAoB,GAAG,CAAC,gBAAgB,EAAE,mBAAmB,CAAC;IACnE,IAAI,CAACC,SAAS,GAAGV,QAAQ;EAC7B;EACA;AACJ;AACA;AACA;AACA;EACIW,UAAUA,CAACzB,QAAQ,EAAEI,GAAG,EAAEM,OAAO,EAAE;IAC/B,OAAO,IAAI,CAACgB,qBAAqB,CAAC,EAAE,EAAE1B,QAAQ,EAAEI,GAAG,EAAEM,OAAO,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACIiB,iBAAiBA,CAAC3B,QAAQ,EAAEM,OAAO,EAAEI,OAAO,EAAE;IAC1C,OAAO,IAAI,CAACkB,4BAA4B,CAAC,EAAE,EAAE5B,QAAQ,EAAEM,OAAO,EAAEI,OAAO,CAAC;EAC5E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIgB,qBAAqBA,CAACG,SAAS,EAAE7B,QAAQ,EAAEI,GAAG,EAAEM,OAAO,EAAE;IACrD,OAAO,IAAI,CAACoB,iBAAiB,CAACD,SAAS,EAAE7B,QAAQ,EAAE,IAAIO,aAAa,CAACH,GAAG,EAAE,IAAI,EAAEM,OAAO,CAAC,CAAC;EAC7F;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIqB,kBAAkBA,CAACC,QAAQ,EAAE;IACzB,IAAI,CAACV,UAAU,CAACW,IAAI,CAACD,QAAQ,CAAC;IAC9B,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;AACA;EACIJ,4BAA4BA,CAACC,SAAS,EAAE7B,QAAQ,EAAEM,OAAO,EAAEI,OAAO,EAAE;IAChE,MAAMwB,YAAY,GAAG,IAAI,CAACrB,UAAU,CAACsB,QAAQ,CAAC/E,eAAe,CAACgF,IAAI,EAAE9B,OAAO,CAAC;IAC5E;IACA,IAAI,CAAC4B,YAAY,EAAE;MACf,MAAM7B,sCAAsC,CAACC,OAAO,CAAC;IACzD;IACA;IACA,MAAM+B,cAAc,GAAGxC,qBAAqB,CAACqC,YAAY,CAAC;IAC1D,OAAO,IAAI,CAACJ,iBAAiB,CAACD,SAAS,EAAE7B,QAAQ,EAAE,IAAIO,aAAa,CAAC,EAAE,EAAE8B,cAAc,EAAE3B,OAAO,CAAC,CAAC;EACtG;EACA;AACJ;AACA;AACA;EACI4B,aAAaA,CAAClC,GAAG,EAAEM,OAAO,EAAE;IACxB,OAAO,IAAI,CAAC6B,wBAAwB,CAAC,EAAE,EAAEnC,GAAG,EAAEM,OAAO,CAAC;EAC1D;EACA;AACJ;AACA;AACA;EACI8B,oBAAoBA,CAAClC,OAAO,EAAEI,OAAO,EAAE;IACnC,OAAO,IAAI,CAAC+B,+BAA+B,CAAC,EAAE,EAAEnC,OAAO,EAAEI,OAAO,CAAC;EACrE;EACA;AACJ;AACA;AACA;AACA;EACI6B,wBAAwBA,CAACV,SAAS,EAAEzB,GAAG,EAAEM,OAAO,EAAE;IAC9C,OAAO,IAAI,CAACgC,oBAAoB,CAACb,SAAS,EAAE,IAAItB,aAAa,CAACH,GAAG,EAAE,IAAI,EAAEM,OAAO,CAAC,CAAC;EACtF;EACA;AACJ;AACA;AACA;AACA;EACI+B,+BAA+BA,CAACZ,SAAS,EAAEvB,OAAO,EAAEI,OAAO,EAAE;IACzD,MAAMwB,YAAY,GAAG,IAAI,CAACrB,UAAU,CAACsB,QAAQ,CAAC/E,eAAe,CAACgF,IAAI,EAAE9B,OAAO,CAAC;IAC5E,IAAI,CAAC4B,YAAY,EAAE;MACf,MAAM7B,sCAAsC,CAACC,OAAO,CAAC;IACzD;IACA;IACA,MAAM+B,cAAc,GAAGxC,qBAAqB,CAACqC,YAAY,CAAC;IAC1D,OAAO,IAAI,CAACQ,oBAAoB,CAACb,SAAS,EAAE,IAAItB,aAAa,CAAC,EAAE,EAAE8B,cAAc,EAAE3B,OAAO,CAAC,CAAC;EAC/F;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiC,sBAAsBA,CAACC,KAAK,EAAEC,UAAU,GAAGD,KAAK,EAAE;IAC9C,IAAI,CAACvB,sBAAsB,CAACyB,GAAG,CAACF,KAAK,EAAEC,UAAU,CAAC;IAClD,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIE,qBAAqBA,CAACH,KAAK,EAAE;IACzB,OAAO,IAAI,CAACvB,sBAAsB,CAAC2B,GAAG,CAACJ,KAAK,CAAC,IAAIA,KAAK;EAC1D;EACA;AACJ;AACA;AACA;EACIK,sBAAsBA,CAAC,GAAGJ,UAAU,EAAE;IAClC,IAAI,CAACtB,oBAAoB,GAAGsB,UAAU;IACtC,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIK,sBAAsBA,CAAA,EAAG;IACrB,OAAO,IAAI,CAAC3B,oBAAoB;EACpC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACI4B,iBAAiBA,CAACC,OAAO,EAAE;IACvB,MAAMhD,GAAG,GAAG,IAAI,CAACS,UAAU,CAACsB,QAAQ,CAAC/E,eAAe,CAACiG,YAAY,EAAED,OAAO,CAAC;IAC3E,IAAI,CAAChD,GAAG,EAAE;MACN,MAAMD,kCAAkC,CAACiD,OAAO,CAAC;IACrD;IACA,MAAME,UAAU,GAAG,IAAI,CAACnC,iBAAiB,CAAC6B,GAAG,CAAC5C,GAAG,CAAC;IAClD,IAAIkD,UAAU,EAAE;MACZ,OAAOjF,EAAE,CAACkF,QAAQ,CAACD,UAAU,CAAC,CAAC;IACnC;IACA,OAAO,IAAI,CAACE,sBAAsB,CAAC,IAAIjD,aAAa,CAAC6C,OAAO,EAAE,IAAI,CAAC,CAAC,CAACK,IAAI,CAAChF,GAAG,CAACiF,GAAG,IAAI,IAAI,CAACvC,iBAAiB,CAAC2B,GAAG,CAAC1C,GAAG,EAAEsD,GAAG,CAAC,CAAC,EAAEhF,GAAG,CAACgF,GAAG,IAAIH,QAAQ,CAACG,GAAG,CAAC,CAAC,CAAC;EAC1J;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACC,IAAI,EAAE/B,SAAS,GAAG,EAAE,EAAE;IAClC,MAAMgC,GAAG,GAAGC,OAAO,CAACjC,SAAS,EAAE+B,IAAI,CAAC;IACpC,IAAIG,MAAM,GAAG,IAAI,CAAC/C,eAAe,CAACgC,GAAG,CAACa,GAAG,CAAC;IAC1C;IACA,IAAIE,MAAM,EAAE;MACR,OAAO,IAAI,CAACC,iBAAiB,CAACD,MAAM,CAAC;IACzC;IACA;IACAA,MAAM,GAAG,IAAI,CAACE,2BAA2B,CAACpC,SAAS,EAAE+B,IAAI,CAAC;IAC1D,IAAIG,MAAM,EAAE;MACR,IAAI,CAAC/C,eAAe,CAAC8B,GAAG,CAACe,GAAG,EAAEE,MAAM,CAAC;MACrC,OAAO,IAAI,CAACC,iBAAiB,CAACD,MAAM,CAAC;IACzC;IACA;IACA,MAAMG,cAAc,GAAG,IAAI,CAAChD,eAAe,CAAC8B,GAAG,CAACnB,SAAS,CAAC;IAC1D,IAAIqC,cAAc,EAAE;MAChB,OAAO,IAAI,CAACC,yBAAyB,CAACP,IAAI,EAAEM,cAAc,CAAC;IAC/D;IACA,OAAO5F,UAAU,CAACyB,2BAA2B,CAAC8D,GAAG,CAAC,CAAC;EACvD;EACAO,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC9C,UAAU,GAAG,EAAE;IACpB,IAAI,CAACN,eAAe,CAACqD,KAAK,CAAC,CAAC;IAC5B,IAAI,CAACnD,eAAe,CAACmD,KAAK,CAAC,CAAC;IAC5B,IAAI,CAAClD,iBAAiB,CAACkD,KAAK,CAAC,CAAC;EAClC;EACA;AACJ;AACA;EACIL,iBAAiBA,CAACD,MAAM,EAAE;IACtB,IAAIA,MAAM,CAACtD,OAAO,EAAE;MAChB;MACA,OAAOpC,EAAE,CAACkF,QAAQ,CAAC,IAAI,CAACe,qBAAqB,CAACP,MAAM,CAAC,CAAC,CAAC;IAC3D,CAAC,MACI;MACD;MACA,OAAO,IAAI,CAACP,sBAAsB,CAACO,MAAM,CAAC,CAACN,IAAI,CAAC/E,GAAG,CAACgF,GAAG,IAAIH,QAAQ,CAACG,GAAG,CAAC,CAAC,CAAC;IAC9E;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIS,yBAAyBA,CAACP,IAAI,EAAEM,cAAc,EAAE;IAC5C;IACA;IACA,MAAMK,SAAS,GAAG,IAAI,CAACC,8BAA8B,CAACZ,IAAI,EAAEM,cAAc,CAAC;IAC3E,IAAIK,SAAS,EAAE;MACX;MACA;MACA;MACA,OAAOlG,EAAE,CAACkG,SAAS,CAAC;IACxB;IACA;IACA;IACA,MAAME,oBAAoB,GAAGP,cAAc,CACtCQ,MAAM,CAACC,aAAa,IAAI,CAACA,aAAa,CAAClE,OAAO,CAAC,CAC/C/B,GAAG,CAACiG,aAAa,IAAI;MACtB,OAAO,IAAI,CAACC,yBAAyB,CAACD,aAAa,CAAC,CAAClB,IAAI,CAAC9E,UAAU,CAAEkG,GAAG,IAAK;QAC1E,MAAMzE,GAAG,GAAG,IAAI,CAACS,UAAU,CAACsB,QAAQ,CAAC/E,eAAe,CAACiG,YAAY,EAAEsB,aAAa,CAACvE,GAAG,CAAC;QACrF;QACA;QACA,MAAM0E,YAAY,GAAG,yBAAyB1E,GAAG,YAAYyE,GAAG,CAACE,OAAO,EAAE;QAC1E,IAAI,CAAChE,aAAa,CAACiE,WAAW,CAAC,IAAI/E,KAAK,CAAC6E,YAAY,CAAC,CAAC;QACvD,OAAOzG,EAAE,CAAC,IAAI,CAAC;MACnB,CAAC,CAAC,CAAC;IACP,CAAC,CAAC;IACF;IACA;IACA,OAAOE,QAAQ,CAACkG,oBAAoB,CAAC,CAAChB,IAAI,CAAC/E,GAAG,CAAC,MAAM;MACjD,MAAMuG,SAAS,GAAG,IAAI,CAACT,8BAA8B,CAACZ,IAAI,EAAEM,cAAc,CAAC;MAC3E;MACA,IAAI,CAACe,SAAS,EAAE;QACZ,MAAMlF,2BAA2B,CAAC6D,IAAI,CAAC;MAC3C;MACA,OAAOqB,SAAS;IACpB,CAAC,CAAC,CAAC;EACP;EACA;AACJ;AACA;AACA;AACA;EACIT,8BAA8BA,CAACxE,QAAQ,EAAEkE,cAAc,EAAE;IACrD;IACA,KAAK,IAAIgB,CAAC,GAAGhB,cAAc,CAACiB,MAAM,GAAG,CAAC,EAAED,CAAC,IAAI,CAAC,EAAEA,CAAC,EAAE,EAAE;MACjD,MAAMnB,MAAM,GAAGG,cAAc,CAACgB,CAAC,CAAC;MAChC;MACA;MACA;MACA;MACA,IAAInB,MAAM,CAACtD,OAAO,IAAIsD,MAAM,CAACtD,OAAO,CAAC2E,QAAQ,CAAC,CAAC,CAACC,OAAO,CAACrF,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;QACpE,MAAM0D,GAAG,GAAG,IAAI,CAACY,qBAAqB,CAACP,MAAM,CAAC;QAC9C,MAAMkB,SAAS,GAAG,IAAI,CAACK,sBAAsB,CAAC5B,GAAG,EAAE1D,QAAQ,EAAE+D,MAAM,CAACrD,OAAO,CAAC;QAC5E,IAAIuE,SAAS,EAAE;UACX,OAAOA,SAAS;QACpB;MACJ;IACJ;IACA,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;EACIzB,sBAAsBA,CAACO,MAAM,EAAE;IAC3B,OAAO,IAAI,CAACwB,UAAU,CAACxB,MAAM,CAAC,CAACN,IAAI,CAAChF,GAAG,CAACgC,OAAO,IAAKsD,MAAM,CAACtD,OAAO,GAAGA,OAAQ,CAAC,EAAE/B,GAAG,CAAC,MAAM,IAAI,CAAC4F,qBAAqB,CAACP,MAAM,CAAC,CAAC,CAAC;EAClI;EACA;AACJ;AACA;AACA;EACIa,yBAAyBA,CAACb,MAAM,EAAE;IAC9B,IAAIA,MAAM,CAACtD,OAAO,EAAE;MAChB,OAAOpC,EAAE,CAAC,IAAI,CAAC;IACnB;IACA,OAAO,IAAI,CAACkH,UAAU,CAACxB,MAAM,CAAC,CAACN,IAAI,CAAChF,GAAG,CAACgC,OAAO,IAAKsD,MAAM,CAACtD,OAAO,GAAGA,OAAQ,CAAC,CAAC;EACnF;EACA;AACJ;AACA;AACA;AACA;EACI6E,sBAAsBA,CAACE,OAAO,EAAExF,QAAQ,EAAEU,OAAO,EAAE;IAC/C;IACA;IACA,MAAM+E,UAAU,GAAGD,OAAO,CAACE,aAAa,CAAC,QAAQ1F,QAAQ,IAAI,CAAC;IAC9D,IAAI,CAACyF,UAAU,EAAE;MACb,OAAO,IAAI;IACf;IACA;IACA;IACA,MAAME,WAAW,GAAGF,UAAU,CAACG,SAAS,CAAC,IAAI,CAAC;IAC9CD,WAAW,CAACE,eAAe,CAAC,IAAI,CAAC;IACjC;IACA;IACA,IAAIF,WAAW,CAACG,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;MAC9C,OAAO,IAAI,CAACC,iBAAiB,CAACL,WAAW,EAAEjF,OAAO,CAAC;IACvD;IACA;IACA;IACA;IACA,IAAIiF,WAAW,CAACG,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,QAAQ,EAAE;MACjD,OAAO,IAAI,CAACC,iBAAiB,CAAC,IAAI,CAACC,aAAa,CAACN,WAAW,CAAC,EAAEjF,OAAO,CAAC;IAC3E;IACA;IACA;IACA;IACA;IACA;IACA,MAAMgD,GAAG,GAAG,IAAI,CAACwC,qBAAqB,CAACrG,qBAAqB,CAAC,aAAa,CAAC,CAAC;IAC5E;IACA6D,GAAG,CAACyC,WAAW,CAACR,WAAW,CAAC;IAC5B,OAAO,IAAI,CAACK,iBAAiB,CAACtC,GAAG,EAAEhD,OAAO,CAAC;EAC/C;EACA;AACJ;AACA;EACIwF,qBAAqBA,CAACE,GAAG,EAAE;IACvB,MAAMC,GAAG,GAAG,IAAI,CAAC7E,SAAS,CAAC8E,aAAa,CAAC,KAAK,CAAC;IAC/CD,GAAG,CAACE,SAAS,GAAGH,GAAG;IACnB,MAAM1C,GAAG,GAAG2C,GAAG,CAACX,aAAa,CAAC,KAAK,CAAC;IACpC;IACA,IAAI,CAAChC,GAAG,EAAE;MACN,MAAMzD,KAAK,CAAC,qBAAqB,CAAC;IACtC;IACA,OAAOyD,GAAG;EACd;EACA;AACJ;AACA;EACIuC,aAAaA,CAACO,OAAO,EAAE;IACnB,MAAM9C,GAAG,GAAG,IAAI,CAACwC,qBAAqB,CAACrG,qBAAqB,CAAC,aAAa,CAAC,CAAC;IAC5E,MAAM4G,UAAU,GAAGD,OAAO,CAACC,UAAU;IACrC;IACA,KAAK,IAAIvB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGuB,UAAU,CAACtB,MAAM,EAAED,CAAC,EAAE,EAAE;MACxC,MAAM;QAAEtB,IAAI;QAAE8C;MAAM,CAAC,GAAGD,UAAU,CAACvB,CAAC,CAAC;MACrC,IAAItB,IAAI,KAAK,IAAI,EAAE;QACfF,GAAG,CAACiD,YAAY,CAAC/C,IAAI,EAAE8C,KAAK,CAAC;MACjC;IACJ;IACA,KAAK,IAAIxB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGsB,OAAO,CAACI,UAAU,CAACzB,MAAM,EAAED,CAAC,EAAE,EAAE;MAChD,IAAIsB,OAAO,CAACI,UAAU,CAAC1B,CAAC,CAAC,CAAC2B,QAAQ,KAAK,IAAI,CAACrF,SAAS,CAACsF,YAAY,EAAE;QAChEpD,GAAG,CAACyC,WAAW,CAACK,OAAO,CAACI,UAAU,CAAC1B,CAAC,CAAC,CAACU,SAAS,CAAC,IAAI,CAAC,CAAC;MAC1D;IACJ;IACA,OAAOlC,GAAG;EACd;EACA;AACJ;AACA;EACIsC,iBAAiBA,CAACtC,GAAG,EAAEhD,OAAO,EAAE;IAC5BgD,GAAG,CAACiD,YAAY,CAAC,KAAK,EAAE,EAAE,CAAC;IAC3BjD,GAAG,CAACiD,YAAY,CAAC,QAAQ,EAAE,MAAM,CAAC;IAClCjD,GAAG,CAACiD,YAAY,CAAC,OAAO,EAAE,MAAM,CAAC;IACjCjD,GAAG,CAACiD,YAAY,CAAC,qBAAqB,EAAE,eAAe,CAAC;IACxDjD,GAAG,CAACiD,YAAY,CAAC,WAAW,EAAE,OAAO,CAAC,CAAC,CAAC;IACxC,IAAIjG,OAAO,IAAIA,OAAO,CAACqG,OAAO,EAAE;MAC5BrD,GAAG,CAACiD,YAAY,CAAC,SAAS,EAAEjG,OAAO,CAACqG,OAAO,CAAC;IAChD;IACA,OAAOrD,GAAG;EACd;EACA;AACJ;AACA;AACA;EACI6B,UAAUA,CAACyB,UAAU,EAAE;IACnB,MAAM;MAAE5G,GAAG,EAAEgD,OAAO;MAAE1C;IAAQ,CAAC,GAAGsG,UAAU;IAC5C,MAAMC,eAAe,GAAGvG,OAAO,EAAEuG,eAAe,IAAI,KAAK;IACzD,IAAI,CAAC,IAAI,CAACrG,WAAW,EAAE;MACnB,MAAMV,6BAA6B,CAAC,CAAC;IACzC;IACA;IACA,IAAIkD,OAAO,IAAI,IAAI,EAAE;MACjB,MAAMnD,KAAK,CAAC,+BAA+BmD,OAAO,IAAI,CAAC;IAC3D;IACA,MAAMhD,GAAG,GAAG,IAAI,CAACS,UAAU,CAACsB,QAAQ,CAAC/E,eAAe,CAACiG,YAAY,EAAED,OAAO,CAAC;IAC3E;IACA,IAAI,CAAChD,GAAG,EAAE;MACN,MAAMD,kCAAkC,CAACiD,OAAO,CAAC;IACrD;IACA;IACA;IACA;IACA,MAAM8D,eAAe,GAAG,IAAI,CAAC9F,qBAAqB,CAAC4B,GAAG,CAAC5C,GAAG,CAAC;IAC3D,IAAI8G,eAAe,EAAE;MACjB,OAAOA,eAAe;IAC1B;IACA,MAAMC,GAAG,GAAG,IAAI,CAACvG,WAAW,CAACoC,GAAG,CAAC5C,GAAG,EAAE;MAAEgH,YAAY,EAAE,MAAM;MAAEH;IAAgB,CAAC,CAAC,CAACxD,IAAI,CAAC/E,GAAG,CAACgF,GAAG,IAAI;MAC7F;MACA;MACA,OAAO7D,qBAAqB,CAAC6D,GAAG,CAAC;IACrC,CAAC,CAAC,EAAE9E,QAAQ,CAAC,MAAM,IAAI,CAACwC,qBAAqB,CAACiG,MAAM,CAACjH,GAAG,CAAC,CAAC,EAAEvB,KAAK,CAAC,CAAC,CAAC;IACpE,IAAI,CAACuC,qBAAqB,CAAC0B,GAAG,CAAC1C,GAAG,EAAE+G,GAAG,CAAC;IACxC,OAAOA,GAAG;EACd;EACA;AACJ;AACA;AACA;AACA;AACA;EACIrF,iBAAiBA,CAACD,SAAS,EAAE7B,QAAQ,EAAE+D,MAAM,EAAE;IAC3C,IAAI,CAAC/C,eAAe,CAAC8B,GAAG,CAACgB,OAAO,CAACjC,SAAS,EAAE7B,QAAQ,CAAC,EAAE+D,MAAM,CAAC;IAC9D,OAAO,IAAI;EACf;EACA;AACJ;AACA;AACA;AACA;EACIrB,oBAAoBA,CAACb,SAAS,EAAEkC,MAAM,EAAE;IACpC,MAAMuD,eAAe,GAAG,IAAI,CAACpG,eAAe,CAAC8B,GAAG,CAACnB,SAAS,CAAC;IAC3D,IAAIyF,eAAe,EAAE;MACjBA,eAAe,CAACrF,IAAI,CAAC8B,MAAM,CAAC;IAChC,CAAC,MACI;MACD,IAAI,CAAC7C,eAAe,CAAC4B,GAAG,CAACjB,SAAS,EAAE,CAACkC,MAAM,CAAC,CAAC;IACjD;IACA,OAAO,IAAI;EACf;EACA;EACAO,qBAAqBA,CAACP,MAAM,EAAE;IAC1B,IAAI,CAACA,MAAM,CAACwD,UAAU,EAAE;MACpB,MAAM7D,GAAG,GAAG,IAAI,CAACwC,qBAAqB,CAACnC,MAAM,CAACtD,OAAO,CAAC;MACtD,IAAI,CAACuF,iBAAiB,CAACtC,GAAG,EAAEK,MAAM,CAACrD,OAAO,CAAC;MAC3CqD,MAAM,CAACwD,UAAU,GAAG7D,GAAG;IAC3B;IACA,OAAOK,MAAM,CAACwD,UAAU;EAC5B;EACA;EACAtD,2BAA2BA,CAACpC,SAAS,EAAE+B,IAAI,EAAE;IACzC,KAAK,IAAIsB,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAAC5D,UAAU,CAAC6D,MAAM,EAAED,CAAC,EAAE,EAAE;MAC7C,MAAMsC,MAAM,GAAG,IAAI,CAAClG,UAAU,CAAC4D,CAAC,CAAC,CAACtB,IAAI,EAAE/B,SAAS,CAAC;MAClD,IAAI2F,MAAM,EAAE;QACR,OAAOC,oBAAoB,CAACD,MAAM,CAAC,GAC7B,IAAIjH,aAAa,CAACiH,MAAM,CAACpH,GAAG,EAAE,IAAI,EAAEoH,MAAM,CAAC9G,OAAO,CAAC,GACnD,IAAIH,aAAa,CAACiH,MAAM,EAAE,IAAI,CAAC;MACzC;IACJ;IACA,OAAOlI,SAAS;EACpB;EACA;IAAS,IAAI,CAACoI,IAAI,YAAAC,wBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFjH,eAAe,EAAzBxD,EAAE,CAAA0K,QAAA,CAAyC9I,EAAE,CAACC,UAAU,MAAxD7B,EAAE,CAAA0K,QAAA,CAAmF5I,EAAE,CAACC,YAAY,GAApG/B,EAAE,CAAA0K,QAAA,CAA+GzJ,QAAQ,MAAzHjB,EAAE,CAAA0K,QAAA,CAAoJ1K,EAAE,CAACM,YAAY;IAAA,CAA6C;EAAE;EACpT;IAAS,IAAI,CAACqK,KAAK,kBAD6E3K,EAAE,CAAA4K,kBAAA;MAAAC,KAAA,EACYrH,eAAe;MAAAsH,OAAA,EAAftH,eAAe,CAAA+G,IAAA;MAAAQ,UAAA,EAAc;IAAM,EAAG;EAAE;AAC1J;AACA;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAHoGhL,EAAE,CAAAiL,iBAAA,CAGXzH,eAAe,EAAc,CAAC;IAC7G0H,IAAI,EAAEhL,UAAU;IAChBiL,IAAI,EAAE,CAAC;MAAEJ,UAAU,EAAE;IAAO,CAAC;EACjC,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEG,IAAI,EAAEtJ,EAAE,CAACC,UAAU;IAAEuJ,UAAU,EAAE,CAAC;MACnDF,IAAI,EAAE/K;IACV,CAAC;EAAE,CAAC,EAAE;IAAE+K,IAAI,EAAEpJ,EAAE,CAACC;EAAa,CAAC,EAAE;IAAEmJ,IAAI,EAAE/I,SAAS;IAAEiJ,UAAU,EAAE,CAAC;MAC7DF,IAAI,EAAE/K;IACV,CAAC,EAAE;MACC+K,IAAI,EAAE9K,MAAM;MACZ+K,IAAI,EAAE,CAAClK,QAAQ;IACnB,CAAC;EAAE,CAAC,EAAE;IAAEiK,IAAI,EAAElL,EAAE,CAACM;EAAa,CAAC,CAAC;AAAA;AAChD;AACA,SAAS+K,8BAA8BA,CAACC,cAAc,EAAEC,UAAU,EAAEC,SAAS,EAAEC,YAAY,EAAE9H,QAAQ,EAAE;EACnG,OAAO2H,cAAc,IAAI,IAAI9H,eAAe,CAAC+H,UAAU,EAAEC,SAAS,EAAE7H,QAAQ,EAAE8H,YAAY,CAAC;AAC/F;AACA;AACA,MAAMC,sBAAsB,GAAG;EAC3B;EACAC,OAAO,EAAEnI,eAAe;EACxBoI,IAAI,EAAE,CACF,CAAC,IAAIzL,QAAQ,CAAC,CAAC,EAAE,IAAIE,QAAQ,CAAC,CAAC,EAAEmD,eAAe,CAAC,EACjD,CAAC,IAAIrD,QAAQ,CAAC,CAAC,EAAE0B,UAAU,CAAC,EAC5BE,YAAY,EACZzB,YAAY,EACZ,CAAC,IAAIH,QAAQ,CAAC,CAAC,EAAEc,QAAQ,CAAC,CAC7B;EACD4K,UAAU,EAAER;AAChB,CAAC;AACD;AACA,SAASjF,QAAQA,CAACG,GAAG,EAAE;EACnB,OAAOA,GAAG,CAACkC,SAAS,CAAC,IAAI,CAAC;AAC9B;AACA;AACA,SAAS9B,OAAOA,CAACjC,SAAS,EAAE+B,IAAI,EAAE;EAC9B,OAAO/B,SAAS,GAAG,GAAG,GAAG+B,IAAI;AACjC;AACA,SAAS6D,oBAAoBA,CAACf,KAAK,EAAE;EACjC,OAAO,CAAC,EAAEA,KAAK,CAACtG,GAAG,IAAIsG,KAAK,CAAChG,OAAO,CAAC;AACzC;;AAEA;AACA,MAAMuI,wBAAwB,GAAG,IAAIvL,cAAc,CAAC,0BAA0B,CAAC;AAC/E;AACA;AACA;AACA;AACA;AACA,MAAMwL,iBAAiB,GAAG,IAAIxL,cAAc,CAAC,mBAAmB,EAAE;EAC9DwK,UAAU,EAAE,MAAM;EAClBD,OAAO,EAAEkB;AACb,CAAC,CAAC;AACF;AACA,SAASA,yBAAyBA,CAAA,EAAG;EACjC,MAAM3H,SAAS,GAAG7D,MAAM,CAACS,QAAQ,CAAC;EAClC,MAAMgL,SAAS,GAAG5H,SAAS,GAAGA,SAAS,CAAC6H,QAAQ,GAAG,IAAI;EACvD,OAAO;IACH;IACA;IACAC,WAAW,EAAEA,CAAA,KAAOF,SAAS,GAAGA,SAAS,CAACG,QAAQ,GAAGH,SAAS,CAACI,MAAM,GAAG;EAC5E,CAAC;AACL;AACA;AACA,MAAMC,iBAAiB,GAAG,CACtB,WAAW,EACX,eAAe,EACf,KAAK,EACL,QAAQ,EACR,MAAM,EACN,QAAQ,EACR,QAAQ,EACR,cAAc,EACd,YAAY,EACZ,YAAY,EACZ,MAAM,EACN,QAAQ,CACX;AACD;AACA,MAAMC,wBAAwB,GAAGD,iBAAiB,CAAC/K,GAAG,CAACiL,IAAI,IAAI,IAAIA,IAAI,GAAG,CAAC,CAACC,IAAI,CAAC,IAAI,CAAC;AACtF;AACA,MAAMC,cAAc,GAAG,2BAA2B;AAClD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,OAAO,CAAC;EACV;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACC,MAAM,IAAI,IAAI,CAACC,aAAa;EAC5C;EACA,IAAIF,KAAKA,CAACrD,KAAK,EAAE;IACb,IAAI,CAACsD,MAAM,GAAGtD,KAAK;EACvB;EACA;EACA,IAAIwD,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAACxD,KAAK,EAAE;IACf,IAAIA,KAAK,KAAK,IAAI,CAACyD,QAAQ,EAAE;MACzB,IAAIzD,KAAK,EAAE;QACP,IAAI,CAAC0D,cAAc,CAAC1D,KAAK,CAAC;MAC9B,CAAC,MACI,IAAI,IAAI,CAACyD,QAAQ,EAAE;QACpB,IAAI,CAACE,gBAAgB,CAAC,CAAC;MAC3B;MACA,IAAI,CAACF,QAAQ,GAAGzD,KAAK;IACzB;EACJ;EACA;EACA,IAAI4D,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACC,QAAQ;EACxB;EACA,IAAID,OAAOA,CAAC5D,KAAK,EAAE;IACf,MAAM8D,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAAC/D,KAAK,CAAC;IAC9C,IAAI8D,QAAQ,KAAK,IAAI,CAACD,QAAQ,EAAE;MAC5B,IAAI,CAACA,QAAQ,GAAGC,QAAQ;MACxB,IAAI,CAACE,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACC,SAAS;EACzB;EACA,IAAID,QAAQA,CAACjE,KAAK,EAAE;IAChB,MAAM8D,QAAQ,GAAG,IAAI,CAACC,iBAAiB,CAAC/D,KAAK,CAAC;IAC9C,IAAI8D,QAAQ,KAAK,IAAI,CAACI,SAAS,EAAE;MAC7B,IAAI,CAACA,SAAS,GAAGJ,QAAQ;MACzB,IAAI,CAACE,sBAAsB,CAAC,CAAC;IACjC;EACJ;EACAlK,WAAWA,CAACqK,WAAW,EAAEC,aAAa,EAAEC,UAAU,EAAE3B,SAAS,EAAErI,aAAa,EAAEiK,QAAQ,EAAE;IACpF,IAAI,CAACH,WAAW,GAAGA,WAAW;IAC9B,IAAI,CAACC,aAAa,GAAGA,aAAa;IAClC,IAAI,CAAC1B,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACrI,aAAa,GAAGA,aAAa;IAClC;AACR;AACA;AACA;IACQ,IAAI,CAACkK,MAAM,GAAG,KAAK;IACnB,IAAI,CAACC,qBAAqB,GAAG,EAAE;IAC/B;IACA,IAAI,CAACC,iBAAiB,GAAG3M,YAAY,CAAC4M,KAAK;IAC3C,IAAIJ,QAAQ,EAAE;MACV,IAAIA,QAAQ,CAACjB,KAAK,EAAE;QAChB,IAAI,CAACA,KAAK,GAAG,IAAI,CAACE,aAAa,GAAGe,QAAQ,CAACjB,KAAK;MACpD;MACA,IAAIiB,QAAQ,CAACV,OAAO,EAAE;QAClB,IAAI,CAACA,OAAO,GAAGU,QAAQ,CAACV,OAAO;MACnC;IACJ;IACA;IACA;IACA,IAAI,CAACS,UAAU,EAAE;MACbF,WAAW,CAACQ,aAAa,CAAC1E,YAAY,CAAC,aAAa,EAAE,MAAM,CAAC;IACjE;EACJ;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI2E,cAAcA,CAACtL,QAAQ,EAAE;IACrB,IAAI,CAACA,QAAQ,EAAE;MACX,OAAO,CAAC,EAAE,EAAE,EAAE,CAAC;IACnB;IACA,MAAMuL,KAAK,GAAGvL,QAAQ,CAACwL,KAAK,CAAC,GAAG,CAAC;IACjC,QAAQD,KAAK,CAACpG,MAAM;MAChB,KAAK,CAAC;QACF,OAAO,CAAC,EAAE,EAAEoG,KAAK,CAAC,CAAC,CAAC,CAAC;MAAE;MAC3B,KAAK,CAAC;QACF,OAAOA,KAAK;MAChB;QACI,MAAMtL,KAAK,CAAC,uBAAuBD,QAAQ,GAAG,CAAC;MAAE;IACzD;EACJ;EACAyL,QAAQA,CAAA,EAAG;IACP;IACA;IACA,IAAI,CAACf,sBAAsB,CAAC,CAAC;EACjC;EACAgB,kBAAkBA,CAAA,EAAG;IACjB,MAAMC,cAAc,GAAG,IAAI,CAACC,+BAA+B;IAC3D,IAAID,cAAc,IAAIA,cAAc,CAACE,IAAI,EAAE;MACvC,MAAMC,OAAO,GAAG,IAAI,CAAC1C,SAAS,CAACE,WAAW,CAAC,CAAC;MAC5C;MACA;MACA;MACA;MACA;MACA;MACA,IAAIwC,OAAO,KAAK,IAAI,CAACC,aAAa,EAAE;QAChC,IAAI,CAACA,aAAa,GAAGD,OAAO;QAC5B,IAAI,CAACE,wBAAwB,CAACF,OAAO,CAAC;MAC1C;IACJ;EACJ;EACA1H,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC+G,iBAAiB,CAACc,WAAW,CAAC,CAAC;IACpC,IAAI,IAAI,CAACL,+BAA+B,EAAE;MACtC,IAAI,CAACA,+BAA+B,CAACvH,KAAK,CAAC,CAAC;IAChD;EACJ;EACA6H,cAAcA,CAAA,EAAG;IACb,OAAO,CAAC,IAAI,CAAChC,OAAO;EACxB;EACAiC,cAAcA,CAACzI,GAAG,EAAE;IAChB,IAAI,CAAC2G,gBAAgB,CAAC,CAAC;IACvB;IACA;IACA,MAAM+B,IAAI,GAAG,IAAI,CAAChD,SAAS,CAACE,WAAW,CAAC,CAAC;IACzC,IAAI,CAACyC,aAAa,GAAGK,IAAI;IACzB,IAAI,CAACC,oCAAoC,CAAC3I,GAAG,CAAC;IAC9C,IAAI,CAACsI,wBAAwB,CAACI,IAAI,CAAC;IACnC,IAAI,CAACvB,WAAW,CAACQ,aAAa,CAAClF,WAAW,CAACzC,GAAG,CAAC;EACnD;EACA2G,gBAAgBA,CAAA,EAAG;IACf,MAAMiC,aAAa,GAAG,IAAI,CAACzB,WAAW,CAACQ,aAAa;IACpD,IAAIkB,UAAU,GAAGD,aAAa,CAAC1F,UAAU,CAACzB,MAAM;IAChD,IAAI,IAAI,CAACyG,+BAA+B,EAAE;MACtC,IAAI,CAACA,+BAA+B,CAACvH,KAAK,CAAC,CAAC;IAChD;IACA;IACA;IACA,OAAOkI,UAAU,EAAE,EAAE;MACjB,MAAMC,KAAK,GAAGF,aAAa,CAAC1F,UAAU,CAAC2F,UAAU,CAAC;MAClD;MACA;MACA,IAAIC,KAAK,CAAC3F,QAAQ,KAAK,CAAC,IAAI2F,KAAK,CAAC1G,QAAQ,CAACC,WAAW,CAAC,CAAC,KAAK,KAAK,EAAE;QAChEyG,KAAK,CAACC,MAAM,CAAC,CAAC;MAClB;IACJ;EACJ;EACA/B,sBAAsBA,CAAA,EAAG;IACrB,IAAI,CAAC,IAAI,CAACwB,cAAc,CAAC,CAAC,EAAE;MACxB;IACJ;IACA,MAAMQ,IAAI,GAAG,IAAI,CAAC7B,WAAW,CAACQ,aAAa;IAC3C,MAAMsB,cAAc,GAAG,CAAC,IAAI,CAACrC,OAAO,GAC9B,IAAI,CAACQ,aAAa,CAAC/H,qBAAqB,CAAC,IAAI,CAACuH,OAAO,CAAC,CAACkB,KAAK,CAAC,IAAI,CAAC,GAClE,IAAI,CAACV,aAAa,CAAC5H,sBAAsB,CAAC,CAAC,EAAEwB,MAAM,CAACkI,SAAS,IAAIA,SAAS,CAACzH,MAAM,GAAG,CAAC,CAAC;IAC5F,IAAI,CAAC+F,qBAAqB,CAAC2B,OAAO,CAACD,SAAS,IAAIF,IAAI,CAACI,SAAS,CAACL,MAAM,CAACG,SAAS,CAAC,CAAC;IACjFD,cAAc,CAACE,OAAO,CAACD,SAAS,IAAIF,IAAI,CAACI,SAAS,CAACC,GAAG,CAACH,SAAS,CAAC,CAAC;IAClE,IAAI,CAAC1B,qBAAqB,GAAGyB,cAAc;IAC3C,IAAI,IAAI,CAAChC,QAAQ,KAAK,IAAI,CAACqC,sBAAsB,IAC7C,CAACL,cAAc,CAACM,QAAQ,CAAC,mBAAmB,CAAC,EAAE;MAC/C,IAAI,IAAI,CAACD,sBAAsB,EAAE;QAC7BN,IAAI,CAACI,SAAS,CAACL,MAAM,CAAC,IAAI,CAACO,sBAAsB,CAAC;MACtD;MACA,IAAI,IAAI,CAACrC,QAAQ,EAAE;QACf+B,IAAI,CAACI,SAAS,CAACC,GAAG,CAAC,IAAI,CAACpC,QAAQ,CAAC;MACrC;MACA,IAAI,CAACqC,sBAAsB,GAAG,IAAI,CAACrC,QAAQ;IAC/C;EACJ;EACA;AACJ;AACA;AACA;AACA;EACIF,iBAAiBA,CAAC/D,KAAK,EAAE;IACrB,OAAO,OAAOA,KAAK,KAAK,QAAQ,GAAGA,KAAK,CAACwG,IAAI,CAAC,CAAC,CAAC1B,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG9E,KAAK;EACzE;EACA;AACJ;AACA;AACA;AACA;EACIsF,wBAAwBA,CAACI,IAAI,EAAE;IAC3B,MAAMe,QAAQ,GAAG,IAAI,CAACvB,+BAA+B;IACrD,IAAIuB,QAAQ,EAAE;MACVA,QAAQ,CAACN,OAAO,CAAC,CAACO,KAAK,EAAE5G,OAAO,KAAK;QACjC4G,KAAK,CAACP,OAAO,CAAClD,IAAI,IAAI;UAClBnD,OAAO,CAACG,YAAY,CAACgD,IAAI,CAAC/F,IAAI,EAAE,QAAQwI,IAAI,IAAIzC,IAAI,CAACjD,KAAK,IAAI,CAAC;QACnE,CAAC,CAAC;MACN,CAAC,CAAC;IACN;EACJ;EACA;AACJ;AACA;AACA;EACI2F,oCAAoCA,CAAC7F,OAAO,EAAE;IAC1C,MAAM6G,mBAAmB,GAAG7G,OAAO,CAAC8G,gBAAgB,CAAC5D,wBAAwB,CAAC;IAC9E,MAAMyD,QAAQ,GAAI,IAAI,CAACvB,+BAA+B,GAClD,IAAI,CAACA,+BAA+B,IAAI,IAAI3K,GAAG,CAAC,CAAE;IACtD,KAAK,IAAIiE,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGmI,mBAAmB,CAAClI,MAAM,EAAED,CAAC,EAAE,EAAE;MACjDuE,iBAAiB,CAACoD,OAAO,CAAClD,IAAI,IAAI;QAC9B,MAAM4D,oBAAoB,GAAGF,mBAAmB,CAACnI,CAAC,CAAC;QACnD,MAAMwB,KAAK,GAAG6G,oBAAoB,CAACC,YAAY,CAAC7D,IAAI,CAAC;QACrD,MAAM8D,KAAK,GAAG/G,KAAK,GAAGA,KAAK,CAAC+G,KAAK,CAAC5D,cAAc,CAAC,GAAG,IAAI;QACxD,IAAI4D,KAAK,EAAE;UACP,IAAIhH,UAAU,GAAG0G,QAAQ,CAACnK,GAAG,CAACuK,oBAAoB,CAAC;UACnD,IAAI,CAAC9G,UAAU,EAAE;YACbA,UAAU,GAAG,EAAE;YACf0G,QAAQ,CAACrK,GAAG,CAACyK,oBAAoB,EAAE9G,UAAU,CAAC;UAClD;UACAA,UAAU,CAACxE,IAAI,CAAC;YAAE2B,IAAI,EAAE+F,IAAI;YAAEjD,KAAK,EAAE+G,KAAK,CAAC,CAAC;UAAE,CAAC,CAAC;QACpD;MACJ,CAAC,CAAC;IACN;EACJ;EACA;EACArD,cAAcA,CAACsD,OAAO,EAAE;IACpB,IAAI,CAACC,aAAa,GAAG,IAAI;IACzB,IAAI,CAACC,QAAQ,GAAG,IAAI;IACpB,IAAI,CAACzC,iBAAiB,CAACc,WAAW,CAAC,CAAC;IACpC,IAAIyB,OAAO,EAAE;MACT,MAAM,CAAC7L,SAAS,EAAE7B,QAAQ,CAAC,GAAG,IAAI,CAACsL,cAAc,CAACoC,OAAO,CAAC;MAC1D,IAAI7L,SAAS,EAAE;QACX,IAAI,CAAC8L,aAAa,GAAG9L,SAAS;MAClC;MACA,IAAI7B,QAAQ,EAAE;QACV,IAAI,CAAC4N,QAAQ,GAAG5N,QAAQ;MAC5B;MACA,IAAI,CAACmL,iBAAiB,GAAG,IAAI,CAACL,aAAa,CACtCnH,eAAe,CAAC3D,QAAQ,EAAE6B,SAAS,CAAC,CACpC4B,IAAI,CAAC3E,IAAI,CAAC,CAAC,CAAC,CAAC,CACb+O,SAAS,CAACnK,GAAG,IAAI,IAAI,CAACyI,cAAc,CAACzI,GAAG,CAAC,EAAGmB,GAAG,IAAK;QACrD,MAAMC,YAAY,GAAG,yBAAyBjD,SAAS,IAAI7B,QAAQ,KAAK6E,GAAG,CAACE,OAAO,EAAE;QACrF,IAAI,CAAChE,aAAa,CAACiE,WAAW,CAAC,IAAI/E,KAAK,CAAC6E,YAAY,CAAC,CAAC;MAC3D,CAAC,CAAC;IACN;EACJ;EACA;IAAS,IAAI,CAAC4C,IAAI,YAAAoG,gBAAAlG,CAAA;MAAA,YAAAA,CAAA,IAAwFkC,OAAO,EA1WjB3M,EAAE,CAAA4Q,iBAAA,CA0WiC5Q,EAAE,CAAC6Q,UAAU,GA1WhD7Q,EAAE,CAAA4Q,iBAAA,CA0W2DpN,eAAe,GA1W5ExD,EAAE,CAAA8Q,iBAAA,CA0WuF,aAAa,GA1WtG9Q,EAAE,CAAA4Q,iBAAA,CA0WkI7E,iBAAiB,GA1WrJ/L,EAAE,CAAA4Q,iBAAA,CA0WgK5Q,EAAE,CAACM,YAAY,GA1WjLN,EAAE,CAAA4Q,iBAAA,CA0W4L9E,wBAAwB;IAAA,CAA4D;EAAE;EACpX;IAAS,IAAI,CAACiF,IAAI,kBA3W8E/Q,EAAE,CAAAgR,iBAAA;MAAA9F,IAAA,EA2WJyB,OAAO;MAAAsE,SAAA;MAAAC,SAAA,WAAoN,KAAK;MAAAC,QAAA;MAAAC,YAAA,WAAAC,qBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UA3W9NtR,EAAE,CAAAwR,WAAA,uBA2WJD,GAAA,CAAAxC,cAAA,CAAe,CAAC,GAAG,MAAM,GAAG,KAAK,wBAAAwC,GAAA,CAAAd,QAAA,IAAAc,GAAA,CAAA/D,QAAA,6BAAA+D,GAAA,CAAAf,aAAA,IAAAe,GAAA,CAAApE,OAAA,cAAjCoE,GAAA,CAAAxC,cAAA,CAAe,CAAC,GAAAwC,GAAA,CAAA/D,QAAA,GAAc,IAAI;UA3WhCxN,EAAE,CAAAyR,UAAA,CAAAF,GAAA,CAAA3E,KAAA,GA2WI,MAAM,GAAA2E,GAAA,CAAA3E,KAAA,GAAW,EAAnB,CAAC;UA3WL5M,EAAE,CAAA0R,WAAA,oBAAAH,GAAA,CAAAzD,MA2WE,CAAC,sBAAAyD,GAAA,CAAA3E,KAAA,KAAG,SAAS,IAAA2E,GAAA,CAAA3E,KAAA,KAAc,QAAQ,IAAA2E,GAAA,CAAA3E,KAAA,KAAc,MAAjD,CAAC;QAAA;MAAA;MAAA+E,MAAA;QAAA/E,KAAA;QAAAkB,MAAA,GA3WL9N,EAAE,CAAA4R,YAAA,CAAAC,0BAAA,sBA2WsGpR,gBAAgB;QAAAsM,OAAA;QAAAI,OAAA;QAAAK,QAAA;MAAA;MAAAsE,QAAA;MAAAC,UAAA;MAAAC,QAAA,GA3WxHhS,EAAE,CAAAiS,wBAAA,EAAFjS,EAAE,CAAAkS,mBAAA;MAAAC,kBAAA,EAAAnQ,GAAA;MAAAoQ,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,iBAAAjB,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFtR,EAAE,CAAAwS,eAAA;UAAFxS,EAAE,CAAAyS,YAAA,EA2W6vB,CAAC;QAAA;MAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAAk/B;EAAE;AACx1D;AACA;EAAA,QAAA5H,SAAA,oBAAAA,SAAA,KA7WoGhL,EAAE,CAAAiL,iBAAA,CA6WX0B,OAAO,EAAc,CAAC;IACrGzB,IAAI,EAAExK,SAAS;IACfyK,IAAI,EAAE,CAAC;MAAEmH,QAAQ,EAAE,2BAA2B;MAAEO,QAAQ,EAAE,UAAU;MAAEf,QAAQ,EAAE,SAAS;MAAEgB,IAAI,EAAE;QACrF,MAAM,EAAE,KAAK;QACb,OAAO,EAAE,sBAAsB;QAC/B,SAAS,EAAE,6BAA6B;QACxC,2BAA2B,EAAE,mCAAmC;QAChE,2BAA2B,EAAE,sBAAsB;QACnD,gCAAgC,EAAE,0BAA0B;QAC5D,iBAAiB,EAAE,oCAAoC;QACvD,yBAAyB,EAAE,QAAQ;QACnC,2BAA2B,EAAE;MACjC,CAAC;MAAEH,aAAa,EAAEhS,iBAAiB,CAACoS,IAAI;MAAEH,eAAe,EAAEhS,uBAAuB,CAACoS,MAAM;MAAEjB,UAAU,EAAE,IAAI;MAAEW,MAAM,EAAE,CAAC,o3BAAo3B;IAAE,CAAC;EACz/B,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAExH,IAAI,EAAElL,EAAE,CAAC6Q;EAAW,CAAC,EAAE;IAAE3F,IAAI,EAAE1H;EAAgB,CAAC,EAAE;IAAE0H,IAAI,EAAE/I,SAAS;IAAEiJ,UAAU,EAAE,CAAC;MACnGF,IAAI,EAAErK,SAAS;MACfsK,IAAI,EAAE,CAAC,aAAa;IACxB,CAAC;EAAE,CAAC,EAAE;IAAED,IAAI,EAAE/I,SAAS;IAAEiJ,UAAU,EAAE,CAAC;MAClCF,IAAI,EAAE9K,MAAM;MACZ+K,IAAI,EAAE,CAACY,iBAAiB;IAC5B,CAAC;EAAE,CAAC,EAAE;IAAEb,IAAI,EAAElL,EAAE,CAACM;EAAa,CAAC,EAAE;IAAE4K,IAAI,EAAE/I,SAAS;IAAEiJ,UAAU,EAAE,CAAC;MAC7DF,IAAI,EAAE/K;IACV,CAAC,EAAE;MACC+K,IAAI,EAAE9K,MAAM;MACZ+K,IAAI,EAAE,CAACW,wBAAwB;IACnC,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEc,KAAK,EAAE,CAAC;MACjC1B,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAEgN,MAAM,EAAE,CAAC;MACT5C,IAAI,EAAEpK,KAAK;MACXqK,IAAI,EAAE,CAAC;QAAE8H,SAAS,EAAExS;MAAiB,CAAC;IAC1C,CAAC,CAAC;IAAEsM,OAAO,EAAE,CAAC;MACV7B,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAEqM,OAAO,EAAE,CAAC;MACVjC,IAAI,EAAEpK;IACV,CAAC,CAAC;IAAE0M,QAAQ,EAAE,CAAC;MACXtC,IAAI,EAAEpK;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAMoS,aAAa,CAAC;EAChB;IAAS,IAAI,CAAC3I,IAAI,YAAA4I,sBAAA1I,CAAA;MAAA,YAAAA,CAAA,IAAwFyI,aAAa;IAAA,CAAkD;EAAE;EAC3K;IAAS,IAAI,CAACE,IAAI,kBApZ8EpT,EAAE,CAAAqT,gBAAA;MAAAnI,IAAA,EAoZSgI;IAAa,EAA6E;EAAE;EACvM;IAAS,IAAI,CAACI,IAAI,kBArZ8EtT,EAAE,CAAAuT,gBAAA;MAAAC,OAAA,GAqZkCxS,eAAe,EAAEA,eAAe;IAAA,EAAI;EAAE;AAC9K;AACA;EAAA,QAAAgK,SAAA,oBAAAA,SAAA,KAvZoGhL,EAAE,CAAAiL,iBAAA,CAuZXiI,aAAa,EAAc,CAAC;IAC3GhI,IAAI,EAAEnK,QAAQ;IACdoK,IAAI,EAAE,CAAC;MACCqI,OAAO,EAAE,CAACxS,eAAe,EAAE2L,OAAO,CAAC;MACnC8G,OAAO,EAAE,CAAC9G,OAAO,EAAE3L,eAAe;IACtC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;;AAEA,SAAS0K,sBAAsB,EAAEL,8BAA8B,EAAES,wBAAwB,EAAEC,iBAAiB,EAAEC,yBAAyB,EAAEW,OAAO,EAAEuG,aAAa,EAAE1P,eAAe,EAAEN,sCAAsC,EAAEF,kCAAkC,EAAEJ,2BAA2B,EAAEG,6BAA6B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}