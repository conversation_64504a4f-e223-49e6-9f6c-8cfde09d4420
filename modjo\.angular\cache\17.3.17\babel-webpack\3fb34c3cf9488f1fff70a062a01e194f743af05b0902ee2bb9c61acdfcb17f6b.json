{"ast": null, "code": "import { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatGridListModule } from '@angular/material/grid-list';\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { dashboardRoutes } from './dashboard.routes';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class DashboardModule {\n  static {\n    this.ɵfac = function DashboardModule_Factory(t) {\n      return new (t || DashboardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: DashboardModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [CommonModule, RouterModule.forChild(dashboardRoutes),\n      // Material modules\n      MatCardModule, MatButtonModule, MatIconModule, MatGridListModule]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(DashboardModule, {\n    declarations: [DashboardComponent],\n    imports: [CommonModule, i1.RouterModule,\n    // Material modules\n    MatCardModule, MatButtonModule, MatIconModule, MatGridListModule]\n  });\n})();", "map": {"version": 3, "names": ["CommonModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatGridListModule", "DashboardComponent", "dashboardRoutes", "DashboardModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { RouterModule } from '@angular/router';\n\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatGridListModule } from '@angular/material/grid-list';\n\nimport { DashboardComponent } from './components/dashboard/dashboard.component';\nimport { dashboardRoutes } from './dashboard.routes';\n\n@NgModule({\n  declarations: [\n    DashboardComponent\n  ],\n  imports: [\n    CommonModule,\n    RouterModule.forChild(dashboardRoutes),\n    \n    // Material modules\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatGridListModule\n  ]\n})\nexport class DashboardModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAC9C,SAASC,YAAY,QAAQ,iBAAiB;AAE9C;AACA,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,aAAa,QAAQ,wBAAwB;AACtD,SAASC,iBAAiB,QAAQ,6BAA6B;AAE/D,SAASC,kBAAkB,QAAQ,4CAA4C;AAC/E,SAASC,eAAe,QAAQ,oBAAoB;;;AAiBpD,OAAM,MAAOC,eAAe;;;uBAAfA,eAAe;IAAA;EAAA;;;YAAfA;IAAe;EAAA;;;gBAVxBR,YAAY,EACZC,YAAY,CAACQ,QAAQ,CAACF,eAAe,CAAC;MAEtC;MACAL,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,iBAAiB;IAAA;EAAA;;;2EAGRG,eAAe;IAAAE,YAAA,GAbxBJ,kBAAkB;IAAAK,OAAA,GAGlBX,YAAY,EAAAY,EAAA,CAAAX,YAAA;IAGZ;IACAC,aAAa,EACbC,eAAe,EACfC,aAAa,EACbC,iBAAiB;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}