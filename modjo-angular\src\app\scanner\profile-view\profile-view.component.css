.profile-container {
  max-width: 600px;
  margin: 0 auto;
  padding: 20px;
}

.back-button {
  display: flex;
  align-items: center;
  background: none;
  border: none;
  color: #4a90e2;
  font-size: 16px;
  cursor: pointer;
  padding: 0;
  margin-bottom: 20px;
}

.back-button span {
  margin-right: 5px;
  font-size: 20px;
}

.loading, .error {
  text-align: center;
  padding: 30px;
  background-color: #f5f5f5;
  border-radius: 8px;
}

.error button {
  margin-top: 15px;
  padding: 8px 16px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.profile-content {
  background-color: white;
  border-radius: 8px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.profile-header {
  display: flex;
  padding: 20px;
  background-color: #f9f9f9;
  border-bottom: 1px solid #eee;
}

.profile-image {
  width: 80px;
  height: 80px;
  border-radius: 50%;
  overflow: hidden;
  margin-right: 20px;
  flex-shrink: 0;
}

.profile-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.profile-info {
  flex: 1;
}

.profile-info h2 {
  margin: 0 0 5px 0;
  font-size: 22px;
  color: #333;
}

.profession {
  font-size: 16px;
  color: #666;
  margin: 0 0 5px 0;
}

.location {
  font-size: 14px;
  color: #888;
  margin: 0 0 10px 0;
}

.rating {
  display: flex;
  align-items: center;
}

.stars {
  color: #ffc107;
  font-size: 18px;
  margin-right: 5px;
}

.reviews {
  font-size: 14px;
  color: #888;
}

.profile-details {
  padding: 20px;
  border-bottom: 1px solid #eee;
}

.profile-details h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.skills {
  display: flex;
  flex-wrap: wrap;
  gap: 10px;
}

.skill-tag {
  padding: 5px 10px;
  background-color: #e3f2fd;
  color: #1976d2;
  border-radius: 20px;
  font-size: 14px;
}

.knowme-link {
  padding: 15px 20px;
  text-align: center;
  border-bottom: 1px solid #eee;
}

.knowme-link a {
  color: #4a90e2;
  text-decoration: none;
}

.knowme-link a:hover {
  text-decoration: underline;
}

.award-points-section {
  padding: 20px;
}

.award-points-section h3 {
  margin-top: 0;
  margin-bottom: 15px;
  font-size: 18px;
  color: #333;
}

.success-message {
  padding: 15px;
  background-color: #e8f5e9;
  color: #2e7d32;
  border-radius: 4px;
  margin-bottom: 15px;
}

.error-message {
  padding: 15px;
  background-color: #ffebee;
  color: #d32f2f;
  border-radius: 4px;
  margin-bottom: 15px;
}

.form-group {
  margin-bottom: 20px;
}

.form-group label {
  display: block;
  margin-bottom: 8px;
  font-weight: 500;
  color: #333;
}

.points-selector {
  display: flex;
  gap: 10px;
}

.points-selector button {
  flex: 1;
  padding: 10px;
  background-color: #f5f5f5;
  border: 1px solid #ddd;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s;
}

.points-selector button.selected {
  background-color: #4a90e2;
  color: white;
  border-color: #4a90e2;
}

input {
  width: 100%;
  padding: 12px;
  border: 1px solid #ddd;
  border-radius: 4px;
  font-size: 16px;
}

button[type="submit"] {
  width: 100%;
  padding: 12px;
  background-color: #4a90e2;
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 16px;
  cursor: pointer;
  transition: background-color 0.3s;
}

button[type="submit"]:hover {
  background-color: #3a7bc8;
}

button[type="submit"]:disabled {
  background-color: #a0c3e8;
  cursor: not-allowed;
}
