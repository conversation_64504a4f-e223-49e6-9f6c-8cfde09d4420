{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"../../../../core/services/user.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/button\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/tabs\";\nimport * as i9 from \"@angular/material/chips\";\nfunction ProfileComponent_div_0_mat_card_36_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 19)(1, \"mat-card-content\")(2, \"div\", 20)(3, \"mat-icon\", 35);\n    i0.ɵɵtext(4, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 22)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Points gagn\\u00E9s\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.userStats.totalEarned || 0);\n  }\n}\nfunction ProfileComponent_div_0_mat_card_37_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 19)(1, \"mat-card-content\")(2, \"div\", 20)(3, \"mat-icon\", 36);\n    i0.ɵɵtext(4, \"shopping_cart\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 22)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Points d\\u00E9pens\\u00E9s\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.userStats.totalSpent || 0);\n  }\n}\nfunction ProfileComponent_div_0_mat_card_38_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 19)(1, \"mat-card-content\")(2, \"div\", 20)(3, \"mat-icon\", 37);\n    i0.ɵɵtext(4, \"timeline\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 22)(6, \"h3\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Transactions\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.userStats.transactionCount || 0);\n  }\n}\nfunction ProfileComponent_div_0_div_55_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"phone\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\", 28);\n    i0.ɵɵtext(4, \"T\\u00E9l\\u00E9phone:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 29);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r1.user.phone);\n  }\n}\nfunction ProfileComponent_div_0_div_81_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 40)(1, \"div\", 41)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 42)(5, \"p\", 43);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 44);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 45);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const transaction_r3 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵclassMap(\"activity-\" + transaction_r3.type);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getTransactionIcon(transaction_r3.type), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(transaction_r3.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDate(transaction_r3.timestamp));\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(transaction_r3.points > 0 ? \"positive\" : \"negative\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate2(\" \", transaction_r3.points > 0 ? \"+\" : \"\", \"\", transaction_r3.points, \" pts \");\n  }\n}\nfunction ProfileComponent_div_0_div_81_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 38);\n    i0.ɵɵtemplate(1, ProfileComponent_div_0_div_81_div_1_Template, 11, 9, \"div\", 39);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.recentTransactions);\n  }\n}\nfunction ProfileComponent_div_0_ng_template_82_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"mat-icon\", 47);\n    i0.ɵɵtext(2, \"inbox\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"h3\");\n    i0.ɵɵtext(4, \"Aucune activit\\u00E9 r\\u00E9cente\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\");\n    i0.ɵɵtext(6, \"Commencez \\u00E0 scanner des QR codes pour voir votre activit\\u00E9 ici.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"button\", 48)(8, \"mat-icon\");\n    i0.ɵɵtext(9, \"qr_code_scanner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(10, \" Scanner un QR Code \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction ProfileComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"mat-icon\", 6);\n    i0.ɵɵtext(4, \"account_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(5, \"div\", 7)(6, \"h1\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\", 8);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 9)(11, \"mat-chip-set\")(12, \"mat-chip\", 10);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(14, \"mat-chip\", 11);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(16, \"div\", 12)(17, \"button\", 13);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_0_Template_button_click_17_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.editProfile());\n    });\n    i0.ɵɵelementStart(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"edit\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(20, \" Modifier \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(21, \"mat-tab-group\", 14)(22, \"mat-tab\", 15)(23, \"div\", 16)(24, \"div\", 17)(25, \"div\", 18)(26, \"mat-card\", 19)(27, \"mat-card-content\")(28, \"div\", 20)(29, \"mat-icon\", 21);\n    i0.ɵɵtext(30, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(31, \"div\", 22)(32, \"h3\");\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"p\");\n    i0.ɵɵtext(35, \"Points totaux\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵtemplate(36, ProfileComponent_div_0_mat_card_36_Template, 10, 1, \"mat-card\", 23)(37, ProfileComponent_div_0_mat_card_37_Template, 10, 1, \"mat-card\", 23)(38, ProfileComponent_div_0_mat_card_38_Template, 10, 1, \"mat-card\", 23);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(39, \"div\", 24)(40, \"mat-card\", 25)(41, \"mat-card-header\")(42, \"mat-card-title\")(43, \"mat-icon\");\n    i0.ɵɵtext(44, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(45, \" Informations personnelles \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(46, \"mat-card-content\")(47, \"div\", 26)(48, \"div\", 27)(49, \"mat-icon\");\n    i0.ɵɵtext(50, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(51, \"span\", 28);\n    i0.ɵɵtext(52, \"Ville:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(53, \"span\", 29);\n    i0.ɵɵtext(54);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(55, ProfileComponent_div_0_div_55_Template, 7, 1, \"div\", 30);\n    i0.ɵɵelementStart(56, \"div\", 27)(57, \"mat-icon\");\n    i0.ɵɵtext(58, \"calendar_today\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(59, \"span\", 28);\n    i0.ɵɵtext(60, \"Membre depuis:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"span\", 29);\n    i0.ɵɵtext(62);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(63, \"div\", 27)(64, \"mat-icon\");\n    i0.ɵɵtext(65, \"update\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"span\", 28);\n    i0.ɵɵtext(67, \"Derni\\u00E8re activit\\u00E9:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"span\", 29);\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd()()()()()()()();\n    i0.ɵɵelementStart(70, \"mat-tab\", 31)(71, \"div\", 16)(72, \"mat-card\", 32)(73, \"mat-card-header\")(74, \"mat-card-title\")(75, \"mat-icon\");\n    i0.ɵɵtext(76, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(77, \" Derni\\u00E8res transactions \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(78, \"button\", 33);\n    i0.ɵɵlistener(\"click\", function ProfileComponent_div_0_Template_button_click_78_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.viewAllTransactions());\n    });\n    i0.ɵɵtext(79, \" Voir tout \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(80, \"mat-card-content\");\n    i0.ɵɵtemplate(81, ProfileComponent_div_0_div_81_Template, 2, 1, \"div\", 34)(82, ProfileComponent_div_0_ng_template_82_Template, 11, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const noActivity_r4 = i0.ɵɵreference(83);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.user.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.user.email);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"color\", ctx_r1.getRoleBadgeColor(ctx_r1.user.role));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getRoleDisplayName(ctx_r1.user.role), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.getActivityLevelColor());\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getActivityLevel(), \" \");\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate(ctx_r1.user.points);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userStats);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userStats);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.userStats);\n    i0.ɵɵadvance(16);\n    i0.ɵɵtextInterpolate(ctx_r1.user.city);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.user.phone);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.getMemberSince());\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate(ctx_r1.formatDate(ctx_r1.user.updatedAt));\n    i0.ɵɵadvance(12);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.recentTransactions.length > 0)(\"ngIfElse\", noActivity_r4);\n  }\n}\nfunction ProfileComponent_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 49)(1, \"mat-card\", 50)(2, \"mat-card-content\")(3, \"div\", 51)(4, \"mat-icon\", 52);\n    i0.ɵɵtext(5, \"account_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"Chargement du profil...\");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nexport class ProfileComponent {\n  constructor(authService, userService, router) {\n    this.authService = authService;\n    this.userService = userService;\n    this.router = router;\n    this.user = null;\n    this.userStats = null;\n    this.recentTransactions = [];\n    this.isLoading = true;\n  }\n  ngOnInit() {\n    this.loadUserData();\n  }\n  loadUserData() {\n    var _this = this;\n    this.authService.currentUser$.subscribe(/*#__PURE__*/function () {\n      var _ref = _asyncToGenerator(function* (user) {\n        if (user) {\n          _this.user = user;\n          yield _this.loadUserStats();\n          yield _this.loadRecentTransactions();\n        }\n        _this.isLoading = false;\n      });\n      return function (_x) {\n        return _ref.apply(this, arguments);\n      };\n    }());\n  }\n  loadUserStats() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.user) return;\n      try {\n        _this2.userService.getUserStats(_this2.user.uid).subscribe(stats => {\n          _this2.userStats = stats;\n        });\n      } catch (error) {\n        console.error('Error loading user stats:', error);\n      }\n    })();\n  }\n  loadRecentTransactions() {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this3.user) return;\n      try {\n        _this3.userService.getUserTransactions(_this3.user.uid, 10).subscribe(transactions => {\n          _this3.recentTransactions = transactions;\n        });\n      } catch (error) {\n        console.error('Error loading transactions:', error);\n      }\n    })();\n  }\n  editProfile() {\n    this.router.navigate(['/profile/edit']);\n  }\n  getRoleDisplayName(role) {\n    const roleNames = {\n      'user': 'Utilisateur',\n      'provider': 'Partenaire',\n      'validator': 'Validateur',\n      'admin': 'Administrateur'\n    };\n    return roleNames[role] || role;\n  }\n  getRoleBadgeColor(role) {\n    const colors = {\n      'user': 'primary',\n      'provider': 'accent',\n      'validator': 'warn',\n      'admin': 'warn'\n    };\n    return colors[role] || 'primary';\n  }\n  getTransactionIcon(type) {\n    const iconMap = {\n      'earned': 'add_circle',\n      'spent': 'remove_circle',\n      'transferred': 'swap_horiz',\n      'validated': 'verified',\n      'bonus': 'star'\n    };\n    return iconMap[type] || 'circle';\n  }\n  getTransactionColor(type) {\n    const colorMap = {\n      'earned': 'success',\n      'spent': 'warn',\n      'transferred': 'primary',\n      'validated': 'accent',\n      'bonus': 'accent'\n    };\n    return colorMap[type] || 'primary';\n  }\n  formatDate(date) {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n  getMemberSince() {\n    if (!this.user?.createdAt) return '';\n    const createdDate = new Date(this.user.createdAt);\n    return createdDate.toLocaleDateString('fr-FR', {\n      month: 'long',\n      year: 'numeric'\n    });\n  }\n  getActivityLevel() {\n    if (!this.userStats) return 'Nouveau';\n    const transactionCount = this.userStats.transactionCount || 0;\n    if (transactionCount >= 50) return 'Expert';\n    if (transactionCount >= 20) return 'Actif';\n    if (transactionCount >= 5) return 'Régulier';\n    return 'Débutant';\n  }\n  getActivityLevelColor() {\n    const level = this.getActivityLevel();\n    const colors = {\n      'Expert': '#4caf50',\n      'Actif': '#2196f3',\n      'Régulier': '#ff9800',\n      'Débutant': '#9e9e9e',\n      'Nouveau': '#9e9e9e'\n    };\n    return colors[level] || '#9e9e9e';\n  }\n  viewAllTransactions() {\n    // Navigate to full transaction history\n    this.router.navigate(['/profile'], {\n      fragment: 'history'\n    });\n  }\n  shareProfile() {\n    if (navigator.share) {\n      navigator.share({\n        title: `Profil Modjo de ${this.user?.name}`,\n        text: `Découvrez mon profil sur Modjo - ${this.user?.points} points!`,\n        url: window.location.href\n      }).catch(console.error);\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href).then(() => {\n        console.log('Lien copié dans le presse-papiers');\n      });\n    }\n  }\n  downloadQRCode() {\n    // Generate and download user QR code\n    console.log('Téléchargement du QR code utilisateur...');\n    // This would generate a QR code with user ID for scanning\n  }\n  exportData() {\n    // Export user data\n    console.log('Export des données utilisateur...');\n    // This would export user transaction history and stats\n  }\n  static {\n    this.ɵfac = function ProfileComponent_Factory(t) {\n      return new (t || ProfileComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProfileComponent,\n      selectors: [[\"app-profile\"]],\n      decls: 2,\n      vars: 2,\n      consts: [[\"noActivity\", \"\"], [\"class\", \"profile-container\", 4, \"ngIf\"], [\"class\", \"loading-container\", 4, \"ngIf\"], [1, \"profile-container\"], [1, \"profile-header\"], [1, \"profile-avatar\"], [1, \"avatar-icon\"], [1, \"profile-info\"], [1, \"user-email\"], [1, \"user-badges\"], [\"selected\", \"\", 3, \"color\"], [\"selected\", \"\"], [1, \"profile-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"profile-tabs\"], [\"label\", \"Aper\\u00E7u\"], [1, \"tab-content\"], [1, \"stats-section\"], [1, \"stats-grid\"], [1, \"stat-card\"], [1, \"stat-content\"], [1, \"stat-icon\", 2, \"color\", \"#ffd700\"], [1, \"stat-info\"], [\"class\", \"stat-card\", 4, \"ngIf\"], [1, \"details-section\"], [1, \"details-card\"], [1, \"details-list\"], [1, \"detail-item\"], [1, \"detail-label\"], [1, \"detail-value\"], [\"class\", \"detail-item\", 4, \"ngIf\"], [\"label\", \"Activit\\u00E9 r\\u00E9cente\"], [1, \"activity-card\"], [\"mat-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"class\", \"activity-list\", 4, \"ngIf\", \"ngIfElse\"], [1, \"stat-icon\", 2, \"color\", \"#4caf50\"], [1, \"stat-icon\", 2, \"color\", \"#ff9800\"], [1, \"stat-icon\", 2, \"color\", \"#2196f3\"], [1, \"activity-list\"], [\"class\", \"activity-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"activity-item\"], [1, \"activity-icon\"], [1, \"activity-info\"], [1, \"activity-description\"], [1, \"activity-date\"], [1, \"activity-points\"], [1, \"no-activity\"], [1, \"no-activity-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"routerLink\", \"/qr-scanner\"], [1, \"loading-container\"], [1, \"loading-card\"], [1, \"loading-content\"], [1, \"loading-icon\"]],\n      template: function ProfileComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ProfileComponent_div_0_Template, 84, 17, \"div\", 1)(1, ProfileComponent_div_1_Template, 8, 0, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading && ctx.user);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i3.RouterLink, i5.MatCard, i5.MatCardContent, i5.MatCardHeader, i5.MatCardTitle, i6.MatButton, i7.MatIcon, i8.MatTab, i8.MatTabGroup, i9.MatChip, i9.MatChipSet],\n      styles: [\".profile-container[_ngcontent-%COMP%] {\\n  max-width: 1000px;\\n  margin: 0 auto;\\n  padding: 16px;\\n}\\n\\n.profile-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 24px;\\n  margin-bottom: 32px;\\n  padding: 24px;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 16px;\\n  color: white;\\n}\\n\\n.profile-avatar[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n.avatar-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  width: 4rem;\\n  height: 4rem;\\n  background: rgba(255, 255, 255, 0.2);\\n  border-radius: 50%;\\n  padding: 8px;\\n}\\n\\n.profile-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.profile-info[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 2rem;\\n  font-weight: 600;\\n}\\n\\n.user-email[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  opacity: 0.9;\\n  font-size: 1rem;\\n}\\n\\n.user-badges[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.profile-actions[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n.profile-tabs[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.tab-content[_ngcontent-%COMP%] {\\n  padding: 24px;\\n}\\n\\n.stats-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));\\n  gap: 16px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.stat-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  width: 2.5rem;\\n  height: 2.5rem;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 2rem;\\n  font-weight: 600;\\n  color: #333;\\n}\\n\\n.stat-info[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.details-section[_ngcontent-%COMP%] {\\n  margin-bottom: 32px;\\n}\\n\\n.details-card[_ngcontent-%COMP%], .activity-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.details-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n}\\n\\n.detail-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #666;\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n}\\n\\n.detail-label[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n  min-width: 120px;\\n}\\n\\n.detail-value[_ngcontent-%COMP%] {\\n  color: #666;\\n}\\n\\n.activity-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.activity-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 12px 0;\\n  border-bottom: 1px solid #eee;\\n}\\n\\n.activity-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n.activity-icon[_ngcontent-%COMP%] {\\n  flex-shrink: 0;\\n}\\n\\n.activity-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.activity-earned[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] { color: #4caf50; }\\n.activity-spent[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] { color: #f44336; }\\n.activity-transferred[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] { color: #2196f3; }\\n.activity-validated[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] { color: #ff9800; }\\n.activity-bonus[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] { color: #9c27b0; }\\n\\n.activity-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.activity-description[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #333;\\n  font-weight: 500;\\n}\\n\\n.activity-date[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.8rem;\\n}\\n\\n.activity-points[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 1rem;\\n}\\n\\n.activity-points.positive[_ngcontent-%COMP%] {\\n  color: #4caf50;\\n}\\n\\n.activity-points.negative[_ngcontent-%COMP%] {\\n  color: #f44336;\\n}\\n\\n.no-activity[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 32px;\\n}\\n\\n.no-activity-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #ccc;\\n  margin-bottom: 16px;\\n}\\n\\n.no-activity[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n\\n.no-activity[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: #666;\\n}\\n\\n.loading-container[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  align-items: center;\\n  min-height: 400px;\\n}\\n\\n.loading-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.loading-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 32px;\\n}\\n\\n.loading-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  color: #ccc;\\n  margin-bottom: 16px;\\n}\\n\\n.loading-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #333;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .profile-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 16px;\\n  }\\n  \\n  .profile-info[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  \\n  .stats-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: repeat(2, 1fr);\\n  }\\n  \\n  .stat-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 8px;\\n  }\\n  \\n  .detail-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 4px;\\n  }\\n  \\n  .detail-label[_ngcontent-%COMP%] {\\n    min-width: auto;\\n  }\\n  \\n  .activity-item[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 8px;\\n  }\\n  \\n  .activity-points[_ngcontent-%COMP%] {\\n    align-self: flex-end;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "userStats", "totalEarned", "totalSpent", "transactionCount", "user", "phone", "ɵɵclassMap", "transaction_r3", "type", "ɵɵtextInterpolate1", "getTransactionIcon", "description", "formatDate", "timestamp", "points", "ɵɵtextInterpolate2", "ɵɵtemplate", "ProfileComponent_div_0_div_81_div_1_Template", "ɵɵproperty", "recentTransactions", "ɵɵlistener", "ProfileComponent_div_0_Template_button_click_17_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "editProfile", "ProfileComponent_div_0_mat_card_36_Template", "ProfileComponent_div_0_mat_card_37_Template", "ProfileComponent_div_0_mat_card_38_Template", "ProfileComponent_div_0_div_55_Template", "ProfileComponent_div_0_Template_button_click_78_listener", "viewAllTransactions", "ProfileComponent_div_0_div_81_Template", "ProfileComponent_div_0_ng_template_82_Template", "ɵɵtemplateRefExtractor", "name", "email", "getRoleBadgeColor", "role", "getRoleDisplayName", "ɵɵstyleProp", "getActivityLevelColor", "getActivityLevel", "city", "getMemberSince", "updatedAt", "length", "noActivity_r4", "ProfileComponent", "constructor", "authService", "userService", "router", "isLoading", "ngOnInit", "loadUserData", "_this", "currentUser$", "subscribe", "_ref", "_asyncToGenerator", "loadUserStats", "loadRecentTransactions", "_x", "apply", "arguments", "_this2", "getUserStats", "uid", "stats", "error", "console", "_this3", "getUserTransactions", "transactions", "navigate", "roleNames", "colors", "iconMap", "getTransactionColor", "colorMap", "date", "Date", "toLocaleDateString", "day", "month", "year", "hour", "minute", "createdAt", "createdDate", "level", "fragment", "shareProfile", "navigator", "share", "title", "text", "url", "window", "location", "href", "catch", "clipboard", "writeText", "then", "log", "downloadQRCode", "exportData", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "UserService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_div_0_Template", "ProfileComponent_div_1_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\profile\\components\\profile\\profile.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\profile\\components\\profile\\profile.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { UserService } from '../../../../core/services/user.service';\nimport { User, PointsTransaction } from '../../../../core/models';\n\n@Component({\n  selector: 'app-profile',\n  templateUrl: './profile.component.html',\n  styleUrls: ['./profile.component.css']\n})\nexport class ProfileComponent implements OnInit {\n  user: User | null = null;\n  userStats: any = null;\n  recentTransactions: PointsTransaction[] = [];\n  isLoading = true;\n\n  constructor(\n    private authService: AuthService,\n    private userService: UserService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadUserData();\n  }\n\n  private loadUserData(): void {\n    this.authService.currentUser$.subscribe(async user => {\n      if (user) {\n        this.user = user;\n        await this.loadUserStats();\n        await this.loadRecentTransactions();\n      }\n      this.isLoading = false;\n    });\n  }\n\n  private async loadUserStats(): Promise<void> {\n    if (!this.user) return;\n    \n    try {\n      this.userService.getUserStats(this.user.uid).subscribe(stats => {\n        this.userStats = stats;\n      });\n    } catch (error) {\n      console.error('Error loading user stats:', error);\n    }\n  }\n\n  private async loadRecentTransactions(): Promise<void> {\n    if (!this.user) return;\n    \n    try {\n      this.userService.getUserTransactions(this.user.uid, 10).subscribe(transactions => {\n        this.recentTransactions = transactions;\n      });\n    } catch (error) {\n      console.error('Error loading transactions:', error);\n    }\n  }\n\n  editProfile(): void {\n    this.router.navigate(['/profile/edit']);\n  }\n\n  getRoleDisplayName(role: string): string {\n    const roleNames: { [key: string]: string } = {\n      'user': 'Utilisateur',\n      'provider': 'Partenaire',\n      'validator': 'Validateur',\n      'admin': 'Administrateur'\n    };\n    return roleNames[role] || role;\n  }\n\n  getRoleBadgeColor(role: string): string {\n    const colors: { [key: string]: string } = {\n      'user': 'primary',\n      'provider': 'accent',\n      'validator': 'warn',\n      'admin': 'warn'\n    };\n    return colors[role] || 'primary';\n  }\n\n  getTransactionIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'earned': 'add_circle',\n      'spent': 'remove_circle',\n      'transferred': 'swap_horiz',\n      'validated': 'verified',\n      'bonus': 'star'\n    };\n    return iconMap[type] || 'circle';\n  }\n\n  getTransactionColor(type: string): string {\n    const colorMap: { [key: string]: string } = {\n      'earned': 'success',\n      'spent': 'warn',\n      'transferred': 'primary',\n      'validated': 'accent',\n      'bonus': 'accent'\n    };\n    return colorMap[type] || 'primary';\n  }\n\n  formatDate(date: Date): string {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  getMemberSince(): string {\n    if (!this.user?.createdAt) return '';\n    \n    const createdDate = new Date(this.user.createdAt);\n    return createdDate.toLocaleDateString('fr-FR', {\n      month: 'long',\n      year: 'numeric'\n    });\n  }\n\n  getActivityLevel(): string {\n    if (!this.userStats) return 'Nouveau';\n    \n    const transactionCount = this.userStats.transactionCount || 0;\n    \n    if (transactionCount >= 50) return 'Expert';\n    if (transactionCount >= 20) return 'Actif';\n    if (transactionCount >= 5) return 'Régulier';\n    return 'Débutant';\n  }\n\n  getActivityLevelColor(): string {\n    const level = this.getActivityLevel();\n    const colors: { [key: string]: string } = {\n      'Expert': '#4caf50',\n      'Actif': '#2196f3',\n      'Régulier': '#ff9800',\n      'Débutant': '#9e9e9e',\n      'Nouveau': '#9e9e9e'\n    };\n    return colors[level] || '#9e9e9e';\n  }\n\n  viewAllTransactions(): void {\n    // Navigate to full transaction history\n    this.router.navigate(['/profile'], { fragment: 'history' });\n  }\n\n  shareProfile(): void {\n    if (navigator.share) {\n      navigator.share({\n        title: `Profil Modjo de ${this.user?.name}`,\n        text: `Découvrez mon profil sur Modjo - ${this.user?.points} points!`,\n        url: window.location.href\n      }).catch(console.error);\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href).then(() => {\n        console.log('Lien copié dans le presse-papiers');\n      });\n    }\n  }\n\n  downloadQRCode(): void {\n    // Generate and download user QR code\n    console.log('Téléchargement du QR code utilisateur...');\n    // This would generate a QR code with user ID for scanning\n  }\n\n  exportData(): void {\n    // Export user data\n    console.log('Export des données utilisateur...');\n    // This would export user transaction history and stats\n  }\n}\n", "<div class=\"profile-container\" *ngIf=\"!isLoading && user\">\n  <!-- Profile header -->\n  <div class=\"profile-header\">\n    <div class=\"profile-avatar\">\n      <mat-icon class=\"avatar-icon\">account_circle</mat-icon>\n    </div>\n    <div class=\"profile-info\">\n      <h1>{{ user.name }}</h1>\n      <p class=\"user-email\">{{ user.email }}</p>\n      <div class=\"user-badges\">\n        <mat-chip-set>\n          <mat-chip [color]=\"getRoleBadgeColor(user.role)\" selected>\n            {{ getRoleDisplayName(user.role) }}\n          </mat-chip>\n          <mat-chip [style.background-color]=\"getActivityLevelColor()\" selected>\n            {{ getActivityLevel() }}\n          </mat-chip>\n        </mat-chip-set>\n      </div>\n    </div>\n    <div class=\"profile-actions\">\n      <button mat-raised-button color=\"primary\" (click)=\"editProfile()\">\n        <mat-icon>edit</mat-icon>\n        Modifier\n      </button>\n    </div>\n  </div>\n\n  <!-- Profile tabs -->\n  <mat-tab-group class=\"profile-tabs\">\n    <!-- Overview tab -->\n    <mat-tab label=\"Aperçu\">\n      <div class=\"tab-content\">\n        <!-- Stats cards -->\n        <div class=\"stats-section\">\n          <div class=\"stats-grid\">\n            <mat-card class=\"stat-card\">\n              <mat-card-content>\n                <div class=\"stat-content\">\n                  <mat-icon class=\"stat-icon\" style=\"color: #ffd700;\">stars</mat-icon>\n                  <div class=\"stat-info\">\n                    <h3>{{ user.points }}</h3>\n                    <p>Points totaux</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <mat-card class=\"stat-card\" *ngIf=\"userStats\">\n              <mat-card-content>\n                <div class=\"stat-content\">\n                  <mat-icon class=\"stat-icon\" style=\"color: #4caf50;\">trending_up</mat-icon>\n                  <div class=\"stat-info\">\n                    <h3>{{ userStats.totalEarned || 0 }}</h3>\n                    <p>Points gagnés</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <mat-card class=\"stat-card\" *ngIf=\"userStats\">\n              <mat-card-content>\n                <div class=\"stat-content\">\n                  <mat-icon class=\"stat-icon\" style=\"color: #ff9800;\">shopping_cart</mat-icon>\n                  <div class=\"stat-info\">\n                    <h3>{{ userStats.totalSpent || 0 }}</h3>\n                    <p>Points dépensés</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <mat-card class=\"stat-card\" *ngIf=\"userStats\">\n              <mat-card-content>\n                <div class=\"stat-content\">\n                  <mat-icon class=\"stat-icon\" style=\"color: #2196f3;\">timeline</mat-icon>\n                  <div class=\"stat-info\">\n                    <h3>{{ userStats.transactionCount || 0 }}</h3>\n                    <p>Transactions</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </div>\n\n        <!-- User details -->\n        <div class=\"details-section\">\n          <mat-card class=\"details-card\">\n            <mat-card-header>\n              <mat-card-title>\n                <mat-icon>info</mat-icon>\n                Informations personnelles\n              </mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"details-list\">\n                <div class=\"detail-item\">\n                  <mat-icon>location_on</mat-icon>\n                  <span class=\"detail-label\">Ville:</span>\n                  <span class=\"detail-value\">{{ user.city }}</span>\n                </div>\n                <div class=\"detail-item\" *ngIf=\"user.phone\">\n                  <mat-icon>phone</mat-icon>\n                  <span class=\"detail-label\">Téléphone:</span>\n                  <span class=\"detail-value\">{{ user.phone }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <mat-icon>calendar_today</mat-icon>\n                  <span class=\"detail-label\">Membre depuis:</span>\n                  <span class=\"detail-value\">{{ getMemberSince() }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <mat-icon>update</mat-icon>\n                  <span class=\"detail-label\">Dernière activité:</span>\n                  <span class=\"detail-value\">{{ formatDate(user.updatedAt) }}</span>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n    </mat-tab>\n\n    <!-- Recent activity tab -->\n    <mat-tab label=\"Activité récente\">\n      <div class=\"tab-content\">\n        <mat-card class=\"activity-card\">\n          <mat-card-header>\n            <mat-card-title>\n              <mat-icon>history</mat-icon>\n              Dernières transactions\n            </mat-card-title>\n            <button mat-button color=\"primary\" (click)=\"viewAllTransactions()\">\n              Voir tout\n            </button>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"activity-list\" *ngIf=\"recentTransactions.length > 0; else noActivity\">\n              <div *ngFor=\"let transaction of recentTransactions\" class=\"activity-item\">\n                <div class=\"activity-icon\">\n                  <mat-icon [class]=\"'activity-' + transaction.type\">\n                    {{ getTransactionIcon(transaction.type) }}\n                  </mat-icon>\n                </div>\n                <div class=\"activity-info\">\n                  <p class=\"activity-description\">{{ transaction.description }}</p>\n                  <p class=\"activity-date\">{{ formatDate(transaction.timestamp) }}</p>\n                </div>\n                <div class=\"activity-points\" [class]=\"transaction.points > 0 ? 'positive' : 'negative'\">\n                  {{ transaction.points > 0 ? '+' : '' }}{{ transaction.points }} pts\n                </div>\n              </div>\n            </div>\n            \n            <ng-template #noActivity>\n              <div class=\"no-activity\">\n                <mat-icon class=\"no-activity-icon\">inbox</mat-icon>\n                <h3>Aucune activité récente</h3>\n                <p>Commencez à scanner des QR codes pour voir votre activité ici.</p>\n                <button mat-raised-button color=\"primary\" routerLink=\"/qr-scanner\">\n                  <mat-icon>qr_code_scanner</mat-icon>\n                  Scanner un QR Code\n                </button>\n              </div>\n            </ng-template>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </mat-tab>\n  </mat-tab-group>\n</div>\n\n<!-- Loading state -->\n<div class=\"loading-container\" *ngIf=\"isLoading\">\n  <mat-card class=\"loading-card\">\n    <mat-card-content>\n      <div class=\"loading-content\">\n        <mat-icon class=\"loading-icon\">account_circle</mat-icon>\n        <h3>Chargement du profil...</h3>\n      </div>\n    </mat-card-content>\n  </mat-card>\n</div>\n"], "mappings": ";;;;;;;;;;;;;ICmDkBA,EAHN,CAAAC,cAAA,mBAA8C,uBAC1B,cACU,mBAC4B;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAExEH,EADF,CAAAC,cAAA,cAAuB,SACjB;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,yBAAa;IAIxBF,EAJwB,CAAAG,YAAA,EAAI,EAChB,EACF,EACW,EACV;;;;IALCH,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,SAAA,CAAAC,WAAA,MAAgC;;;;;IAUtCR,EAHN,CAAAC,cAAA,mBAA8C,uBAC1B,cACU,mBAC4B;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE1EH,EADF,CAAAC,cAAA,cAAuB,SACjB;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,gCAAe;IAI1BF,EAJ0B,CAAAG,YAAA,EAAI,EAClB,EACF,EACW,EACV;;;;IALCH,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,SAAA,CAAAE,UAAA,MAA+B;;;;;IAUrCT,EAHN,CAAAC,cAAA,mBAA8C,uBAC1B,cACU,mBAC4B;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAErEH,EADF,CAAAC,cAAA,cAAuB,SACjB;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC9CH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAIvBF,EAJuB,CAAAG,YAAA,EAAI,EACf,EACF,EACW,EACV;;;;IALCH,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAC,SAAA,CAAAG,gBAAA,MAAqC;;;;;IA0B3CV,EADF,CAAAC,cAAA,cAA4C,eAChC;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,2BAAU;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC5CH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;;;;IADuBH,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAK,IAAA,CAAAC,KAAA,CAAgB;;;;;IAoC3CZ,EAFJ,CAAAC,cAAA,cAA0E,cAC7C,eAC0B;IACjDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,cAA2B,YACO;IAAAD,EAAA,CAAAE,MAAA,GAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACjEH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAE,MAAA,GAAuC;IAClEF,EADkE,CAAAG,YAAA,EAAI,EAChE;IACNH,EAAA,CAAAC,cAAA,cAAwF;IACtFD,EAAA,CAAAE,MAAA,IACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;;;;;IAXQH,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAa,UAAA,eAAAC,cAAA,CAAAC,IAAA,CAAwC;IAChDf,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAW,kBAAA,CAAAH,cAAA,CAAAC,IAAA,OACF;IAGgCf,EAAA,CAAAI,SAAA,GAA6B;IAA7BJ,EAAA,CAAAK,iBAAA,CAAAS,cAAA,CAAAI,WAAA,CAA6B;IACpClB,EAAA,CAAAI,SAAA,GAAuC;IAAvCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAa,UAAA,CAAAL,cAAA,CAAAM,SAAA,EAAuC;IAErCpB,EAAA,CAAAI,SAAA,EAA0D;IAA1DJ,EAAA,CAAAa,UAAA,CAAAC,cAAA,CAAAO,MAAA,+BAA0D;IACrFrB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAsB,kBAAA,MAAAR,cAAA,CAAAO,MAAA,qBAAAP,cAAA,CAAAO,MAAA,UACF;;;;;IAbJrB,EAAA,CAAAC,cAAA,cAAkF;IAChFD,EAAA,CAAAuB,UAAA,IAAAC,4CAAA,mBAA0E;IAc5ExB,EAAA,CAAAG,YAAA,EAAM;;;;IAdyBH,EAAA,CAAAI,SAAA,EAAqB;IAArBJ,EAAA,CAAAyB,UAAA,YAAAnB,MAAA,CAAAoB,kBAAA,CAAqB;;;;;IAkBhD1B,EADF,CAAAC,cAAA,cAAyB,mBACY;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,wCAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAChCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,+EAA8D;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEnEH,EADF,CAAAC,cAAA,iBAAmE,eACvD;IAAAD,EAAA,CAAAE,MAAA,sBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAE,MAAA,4BACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;;IAhKdH,EAJN,CAAAC,cAAA,aAA0D,aAE5B,aACE,kBACI;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAC9CF,EAD8C,CAAAG,YAAA,EAAW,EACnD;IAEJH,EADF,CAAAC,cAAA,aAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,WAAsB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGtCH,EAFJ,CAAAC,cAAA,cAAyB,oBACT,oBAC8C;IACxDD,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACXH,EAAA,CAAAC,cAAA,oBAAsE;IACpED,EAAA,CAAAE,MAAA,IACF;IAGNF,EAHM,CAAAG,YAAA,EAAW,EACE,EACX,EACF;IAEJH,EADF,CAAAC,cAAA,eAA6B,kBACuC;IAAxBD,EAAA,CAAA2B,UAAA,mBAAAC,yDAAA;MAAA5B,EAAA,CAAA6B,aAAA,CAAAC,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAAS1B,MAAA,CAAA2B,WAAA,EAAa;IAAA,EAAC;IAC/DjC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,kBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;IAaUH,EAVhB,CAAAC,cAAA,yBAAoC,mBAEV,eACG,eAEI,eACD,oBACM,wBACR,eACU,oBAC4B;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAElEH,EADF,CAAAC,cAAA,eAAuB,UACjB;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAIxBF,EAJwB,CAAAG,YAAA,EAAI,EAChB,EACF,EACW,EACV;IA0BXH,EAxBA,CAAAuB,UAAA,KAAAW,2CAAA,wBAA8C,KAAAC,2CAAA,wBAYA,KAAAC,2CAAA,wBAYA;IAYlDpC,EADE,CAAAG,YAAA,EAAM,EACF;IAOEH,EAJR,CAAAC,cAAA,eAA6B,oBACI,uBACZ,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,mCACF;IACFF,EADE,CAAAG,YAAA,EAAiB,EACD;IAIZH,EAHN,CAAAC,cAAA,wBAAkB,eACU,eACC,gBACb;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACxCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAe;IAC5CF,EAD4C,CAAAG,YAAA,EAAO,EAC7C;IACNH,EAAA,CAAAuB,UAAA,KAAAc,sCAAA,kBAA4C;IAM1CrC,EADF,CAAAC,cAAA,eAAyB,gBACb;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnCH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IACnDF,EADmD,CAAAG,YAAA,EAAO,EACpD;IAEJH,EADF,CAAAC,cAAA,eAAyB,gBACb;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,oCAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAOzEF,EAPyE,CAAAG,YAAA,EAAO,EAC9D,EACF,EACW,EACV,EACP,EACF,EACE;IAQAH,EALV,CAAAC,cAAA,mBAAkC,eACP,oBACS,uBACb,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,qCACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IACjBH,EAAA,CAAAC,cAAA,kBAAmE;IAAhCD,EAAA,CAAA2B,UAAA,mBAAAW,yDAAA;MAAAtC,EAAA,CAAA6B,aAAA,CAAAC,GAAA;MAAA,MAAAxB,MAAA,GAAAN,EAAA,CAAA+B,aAAA;MAAA,OAAA/B,EAAA,CAAAgC,WAAA,CAAS1B,MAAA,CAAAiC,mBAAA,EAAqB;IAAA,EAAC;IAChEvC,EAAA,CAAAE,MAAA,mBACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACO;IAClBH,EAAA,CAAAC,cAAA,wBAAkB;IAkBhBD,EAjBA,CAAAuB,UAAA,KAAAiB,sCAAA,kBAAkF,KAAAC,8CAAA,iCAAAzC,EAAA,CAAA0C,sBAAA,CAiBzD;IAgBrC1C,EALU,CAAAG,YAAA,EAAmB,EACV,EACP,EACE,EACI,EACZ;;;;;IApKIH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAK,IAAA,CAAAgC,IAAA,CAAe;IACG3C,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAK,IAAA,CAAAiC,KAAA,CAAgB;IAGxB5C,EAAA,CAAAI,SAAA,GAAsC;IAAtCJ,EAAA,CAAAyB,UAAA,UAAAnB,MAAA,CAAAuC,iBAAA,CAAAvC,MAAA,CAAAK,IAAA,CAAAmC,IAAA,EAAsC;IAC9C9C,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAAyC,kBAAA,CAAAzC,MAAA,CAAAK,IAAA,CAAAmC,IAAA,OACF;IACU9C,EAAA,CAAAI,SAAA,EAAkD;IAAlDJ,EAAA,CAAAgD,WAAA,qBAAA1C,MAAA,CAAA2C,qBAAA,GAAkD;IAC1DjD,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAgB,kBAAA,MAAAV,MAAA,CAAA4C,gBAAA,QACF;IAyBclD,EAAA,CAAAI,SAAA,IAAiB;IAAjBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAK,IAAA,CAAAU,MAAA,CAAiB;IAOArB,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAC,SAAA,CAAe;IAYfP,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAC,SAAA,CAAe;IAYfP,EAAA,CAAAI,SAAA,EAAe;IAAfJ,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAC,SAAA,CAAe;IA4BXP,EAAA,CAAAI,SAAA,IAAe;IAAfJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAK,IAAA,CAAAwC,IAAA,CAAe;IAElBnD,EAAA,CAAAI,SAAA,EAAgB;IAAhBJ,EAAA,CAAAyB,UAAA,SAAAnB,MAAA,CAAAK,IAAA,CAAAC,KAAA,CAAgB;IAQbZ,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAA8C,cAAA,GAAsB;IAKtBpD,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAK,iBAAA,CAAAC,MAAA,CAAAa,UAAA,CAAAb,MAAA,CAAAK,IAAA,CAAA0C,SAAA,EAAgC;IAuBrCrD,EAAA,CAAAI,SAAA,IAAqC;IAAAJ,EAArC,CAAAyB,UAAA,SAAAnB,MAAA,CAAAoB,kBAAA,CAAA4B,MAAA,KAAqC,aAAAC,aAAA,CAAe;;;;;IAwCpFvD,EAJR,CAAAC,cAAA,cAAiD,mBAChB,uBACX,cACa,mBACI;IAAAD,EAAA,CAAAE,MAAA,qBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACxDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,8BAAuB;IAInCF,EAJmC,CAAAG,YAAA,EAAK,EAC5B,EACW,EACV,EACP;;;AD5KN,OAAM,MAAOqD,gBAAgB;EAM3BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAAjD,IAAI,GAAgB,IAAI;IACxB,KAAAJ,SAAS,GAAQ,IAAI;IACrB,KAAAmB,kBAAkB,GAAwB,EAAE;IAC5C,KAAAmC,SAAS,GAAG,IAAI;EAMb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEQA,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAClB,IAAI,CAACN,WAAW,CAACO,YAAY,CAACC,SAAS;MAAA,IAAAC,IAAA,GAAAC,iBAAA,CAAC,WAAMzD,IAAI,EAAG;QACnD,IAAIA,IAAI,EAAE;UACRqD,KAAI,CAACrD,IAAI,GAAGA,IAAI;UAChB,MAAMqD,KAAI,CAACK,aAAa,EAAE;UAC1B,MAAML,KAAI,CAACM,sBAAsB,EAAE;;QAErCN,KAAI,CAACH,SAAS,GAAG,KAAK;MACxB,CAAC;MAAA,iBAAAU,EAAA;QAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC;EACJ;EAEcJ,aAAaA,CAAA;IAAA,IAAAK,MAAA;IAAA,OAAAN,iBAAA;MACzB,IAAI,CAACM,MAAI,CAAC/D,IAAI,EAAE;MAEhB,IAAI;QACF+D,MAAI,CAACf,WAAW,CAACgB,YAAY,CAACD,MAAI,CAAC/D,IAAI,CAACiE,GAAG,CAAC,CAACV,SAAS,CAACW,KAAK,IAAG;UAC7DH,MAAI,CAACnE,SAAS,GAAGsE,KAAK;QACxB,CAAC,CAAC;OACH,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAClD;EACH;EAEcR,sBAAsBA,CAAA;IAAA,IAAAU,MAAA;IAAA,OAAAZ,iBAAA;MAClC,IAAI,CAACY,MAAI,CAACrE,IAAI,EAAE;MAEhB,IAAI;QACFqE,MAAI,CAACrB,WAAW,CAACsB,mBAAmB,CAACD,MAAI,CAACrE,IAAI,CAACiE,GAAG,EAAE,EAAE,CAAC,CAACV,SAAS,CAACgB,YAAY,IAAG;UAC/EF,MAAI,CAACtD,kBAAkB,GAAGwD,YAAY;QACxC,CAAC,CAAC;OACH,CAAC,OAAOJ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;IACpD;EACH;EAEA7C,WAAWA,CAAA;IACT,IAAI,CAAC2B,MAAM,CAACuB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEApC,kBAAkBA,CAACD,IAAY;IAC7B,MAAMsC,SAAS,GAA8B;MAC3C,MAAM,EAAE,aAAa;MACrB,UAAU,EAAE,YAAY;MACxB,WAAW,EAAE,YAAY;MACzB,OAAO,EAAE;KACV;IACD,OAAOA,SAAS,CAACtC,IAAI,CAAC,IAAIA,IAAI;EAChC;EAEAD,iBAAiBA,CAACC,IAAY;IAC5B,MAAMuC,MAAM,GAA8B;MACxC,MAAM,EAAE,SAAS;MACjB,UAAU,EAAE,QAAQ;MACpB,WAAW,EAAE,MAAM;MACnB,OAAO,EAAE;KACV;IACD,OAAOA,MAAM,CAACvC,IAAI,CAAC,IAAI,SAAS;EAClC;EAEA7B,kBAAkBA,CAACF,IAAY;IAC7B,MAAMuE,OAAO,GAA8B;MACzC,QAAQ,EAAE,YAAY;MACtB,OAAO,EAAE,eAAe;MACxB,aAAa,EAAE,YAAY;MAC3B,WAAW,EAAE,UAAU;MACvB,OAAO,EAAE;KACV;IACD,OAAOA,OAAO,CAACvE,IAAI,CAAC,IAAI,QAAQ;EAClC;EAEAwE,mBAAmBA,CAACxE,IAAY;IAC9B,MAAMyE,QAAQ,GAA8B;MAC1C,QAAQ,EAAE,SAAS;MACnB,OAAO,EAAE,MAAM;MACf,aAAa,EAAE,SAAS;MACxB,WAAW,EAAE,QAAQ;MACrB,OAAO,EAAE;KACV;IACD,OAAOA,QAAQ,CAACzE,IAAI,CAAC,IAAI,SAAS;EACpC;EAEAI,UAAUA,CAACsE,IAAU;IACnB,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAChDC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA5C,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAACzC,IAAI,EAAEsF,SAAS,EAAE,OAAO,EAAE;IAEpC,MAAMC,WAAW,GAAG,IAAIR,IAAI,CAAC,IAAI,CAAC/E,IAAI,CAACsF,SAAS,CAAC;IACjD,OAAOC,WAAW,CAACP,kBAAkB,CAAC,OAAO,EAAE;MAC7CE,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE;KACP,CAAC;EACJ;EAEA5C,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC3C,SAAS,EAAE,OAAO,SAAS;IAErC,MAAMG,gBAAgB,GAAG,IAAI,CAACH,SAAS,CAACG,gBAAgB,IAAI,CAAC;IAE7D,IAAIA,gBAAgB,IAAI,EAAE,EAAE,OAAO,QAAQ;IAC3C,IAAIA,gBAAgB,IAAI,EAAE,EAAE,OAAO,OAAO;IAC1C,IAAIA,gBAAgB,IAAI,CAAC,EAAE,OAAO,UAAU;IAC5C,OAAO,UAAU;EACnB;EAEAuC,qBAAqBA,CAAA;IACnB,MAAMkD,KAAK,GAAG,IAAI,CAACjD,gBAAgB,EAAE;IACrC,MAAMmC,MAAM,GAA8B;MACxC,QAAQ,EAAE,SAAS;MACnB,OAAO,EAAE,SAAS;MAClB,UAAU,EAAE,SAAS;MACrB,UAAU,EAAE,SAAS;MACrB,SAAS,EAAE;KACZ;IACD,OAAOA,MAAM,CAACc,KAAK,CAAC,IAAI,SAAS;EACnC;EAEA5D,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACqB,MAAM,CAACuB,QAAQ,CAAC,CAAC,UAAU,CAAC,EAAE;MAAEiB,QAAQ,EAAE;IAAS,CAAE,CAAC;EAC7D;EAEAC,YAAYA,CAAA;IACV,IAAIC,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,mBAAmB,IAAI,CAAC7F,IAAI,EAAEgC,IAAI,EAAE;QAC3C8D,IAAI,EAAE,oCAAoC,IAAI,CAAC9F,IAAI,EAAEU,MAAM,UAAU;QACrEqF,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC;OACtB,CAAC,CAACC,KAAK,CAAC/B,OAAO,CAACD,KAAK,CAAC;KACxB,MAAM;MACL;MACAwB,SAAS,CAACS,SAAS,CAACC,SAAS,CAACL,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAACI,IAAI,CAAC,MAAK;QAC5DlC,OAAO,CAACmC,GAAG,CAAC,mCAAmC,CAAC;MAClD,CAAC,CAAC;;EAEN;EAEAC,cAAcA,CAAA;IACZ;IACApC,OAAO,CAACmC,GAAG,CAAC,0CAA0C,CAAC;IACvD;EACF;EAEAE,UAAUA,CAAA;IACR;IACArC,OAAO,CAACmC,GAAG,CAAC,mCAAmC,CAAC;IAChD;EACF;;;uBA1KW1D,gBAAgB,EAAAxD,EAAA,CAAAqH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvH,EAAA,CAAAqH,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAzH,EAAA,CAAAqH,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAhBnE,gBAAgB;MAAAoE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCmK7BlI,EA9KA,CAAAuB,UAAA,IAAA6G,+BAAA,mBAA0D,IAAAC,+BAAA,iBA8KT;;;UA9KjBrI,EAAA,CAAAyB,UAAA,UAAA0G,GAAA,CAAAtE,SAAA,IAAAsE,GAAA,CAAAxH,IAAA,CAAwB;UA8KxBX,EAAA,CAAAI,SAAA,EAAe;UAAfJ,EAAA,CAAAyB,UAAA,SAAA0G,GAAA,CAAAtE,SAAA,CAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}