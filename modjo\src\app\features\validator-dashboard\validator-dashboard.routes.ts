import { Routes } from '@angular/router';
import { ValidatorDashboardComponent } from './components/validator-dashboard/validator-dashboard.component';
import { ValidationListComponent } from './components/validation-list/validation-list.component';
import { ValidationHistoryComponent } from './components/validation-history/validation-history.component';

export const validatorDashboardRoutes: Routes = [
  {
    path: '',
    component: ValidatorDashboardComponent,
    children: [
      { path: '', redirectTo: 'pending', pathMatch: 'full' },
      { path: 'pending', component: ValidationListComponent },
      { path: 'history', component: ValidationHistoryComponent }
    ]
  }
];
