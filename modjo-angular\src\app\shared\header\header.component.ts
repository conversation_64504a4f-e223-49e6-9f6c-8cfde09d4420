import { Component, OnInit } from '@angular/core';
import { CommonModule } from '@angular/common';
import { RouterModule } from '@angular/router';
import { AuthService } from '../../auth/auth.service';
import { User, UserRole } from '../../models/user.model';

@Component({
  selector: 'app-header',
  standalone: true,
  imports: [CommonModule, RouterModule],
  templateUrl: './header.component.html',
  styleUrls: ['./header.component.css']
})
export class HeaderComponent implements OnInit {
  currentUser: User | null = null;
  isMenuOpen: boolean = false;
  
  // Role-specific properties
  isUser: boolean = false;
  isProvider: boolean = false;
  isValidator: boolean = false;
  isAdmin: boolean = false;

  constructor(private authService: AuthService) {}

  ngOnInit(): void {
    // Get current user
    this.currentUser = this.authService.getCurrentUser();
    
    // Set role flags
    if (this.currentUser) {
      this.isUser = this.currentUser.role === UserRole.USER;
      this.isProvider = this.currentUser.role === UserRole.PROVIDER;
      this.isValidator = this.currentUser.role === UserRole.VALIDATOR;
      this.isAdmin = this.currentUser.role === UserRole.ADMIN;
    }
  }

  toggleMenu(): void {
    this.isMenuOpen = !this.isMenuOpen;
  }

  async logout(): Promise<void> {
    await this.authService.signOut();
  }
}
