{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { BehaviorSubject } from 'rxjs';\nimport { QrCodeType } from '../models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./user.service\";\nimport * as i2 from \"./auth.service\";\nexport class QrService {\n  constructor(userService, authService) {\n    this.userService = userService;\n    this.authService = authService;\n    this.scanResultSubject = new BehaviorSubject(null);\n    this.scanResult$ = this.scanResultSubject.asObservable();\n  }\n  // Process scanned QR code\n  processScanResult(rawData, location) {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const scanResult = {\n        data: rawData,\n        format: 'QR_CODE',\n        timestamp: new Date(),\n        location\n      };\n      _this.scanResultSubject.next(scanResult);\n      return scanResult;\n    })();\n  }\n  // Parse QR code data\n  parseQrData(rawData) {\n    try {\n      // Try to parse as JSON first\n      const parsed = JSON.parse(rawData);\n      if (this.isValidQrCodeData(parsed)) {\n        return parsed;\n      }\n    } catch {\n      // If not JSON, try to parse as simple formats\n      return this.parseSimpleQrData(rawData);\n    }\n    return null;\n  }\n  // Generate QR code data for user transfers\n  generateUserTransferQr(userId, points) {\n    const qrData = {\n      type: QrCodeType.USER_TRANSFER,\n      userId,\n      points,\n      timestamp: Date.now(),\n      signature: this.generateSignature(userId, points)\n    };\n    return JSON.stringify(qrData);\n  }\n  // Generate QR code for validator actions\n  generateValidatorQr(validatorId, action, points) {\n    const qrData = {\n      type: QrCodeType.VALIDATOR_ACTION,\n      validatorId,\n      action,\n      points,\n      timestamp: Date.now(),\n      signature: this.generateSignature(validatorId, points, action)\n    };\n    return JSON.stringify(qrData);\n  }\n  // Handle different types of QR scans\n  handleQrScan(qrData) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const currentUser = _this2.authService.getCurrentUser();\n      if (!currentUser) {\n        throw new Error('User not authenticated');\n      }\n      switch (qrData.type) {\n        case QrCodeType.USER_TRANSFER:\n          return _this2.handleUserTransfer(qrData, currentUser);\n        case QrCodeType.VALIDATOR_ACTION:\n          return _this2.handleValidatorAction(qrData, currentUser);\n        case QrCodeType.PARTNER_REWARD:\n          return _this2.handlePartnerReward(qrData, currentUser);\n        case QrCodeType.SYSTEM_BONUS:\n          return _this2.handleSystemBonus(qrData, currentUser);\n        default:\n          throw new Error('Unknown QR code type');\n      }\n    })();\n  }\n  handleUserTransfer(qrData, currentUser) {\n    return _asyncToGenerator(function* () {\n      if (!qrData.userId) {\n        throw new Error('Invalid user transfer QR code');\n      }\n      // For demo purposes, we'll simulate a simple point transfer\n      const points = qrData.points || 1; // Default 1 point if not specified\n      return {\n        type: 'user_transfer',\n        targetUserId: qrData.userId,\n        points,\n        message: `Ready to transfer ${points} point(s) to user`\n      };\n    })();\n  }\n  handleValidatorAction(qrData, currentUser) {\n    return _asyncToGenerator(function* () {\n      if (currentUser.role !== 'validator') {\n        throw new Error('Only validators can process validator QR codes');\n      }\n      return {\n        type: 'validator_action',\n        action: qrData.action,\n        points: qrData.points || 10,\n        message: `Validation action: ${qrData.action}`\n      };\n    })();\n  }\n  handlePartnerReward(qrData, currentUser) {\n    return _asyncToGenerator(function* () {\n      return {\n        type: 'partner_reward',\n        points: qrData.points || 5,\n        message: 'Partner reward scanned'\n      };\n    })();\n  }\n  handleSystemBonus(qrData, currentUser) {\n    return _asyncToGenerator(function* () {\n      return {\n        type: 'system_bonus',\n        points: qrData.points || 3,\n        message: 'System bonus activated'\n      };\n    })();\n  }\n  // Validate QR code data structure\n  isValidQrCodeData(data) {\n    return data && typeof data.type === 'string' && Object.values(QrCodeType).includes(data.type) && typeof data.timestamp === 'number';\n  }\n  // Parse simple QR data formats (for demo/testing)\n  parseSimpleQrData(rawData) {\n    // Handle simple formats like \"user:123\" or \"points:5\"\n    if (rawData.startsWith('user:')) {\n      const userId = rawData.substring(5);\n      return {\n        type: QrCodeType.USER_TRANSFER,\n        userId,\n        timestamp: Date.now()\n      };\n    }\n    if (rawData.startsWith('validator:')) {\n      const parts = rawData.substring(10).split(':');\n      return {\n        type: QrCodeType.VALIDATOR_ACTION,\n        validatorId: parts[0],\n        action: parts[1] || 'community_service',\n        points: parseInt(parts[2]) || 10,\n        timestamp: Date.now()\n      };\n    }\n    // Default: treat as user ID\n    return {\n      type: QrCodeType.USER_TRANSFER,\n      userId: rawData,\n      timestamp: Date.now()\n    };\n  }\n  // Generate simple signature for QR codes (for demo purposes)\n  generateSignature(userId, points, action) {\n    const data = `${userId}-${points || 0}-${action || ''}-${Date.now()}`;\n    return btoa(data).substring(0, 16);\n  }\n  // Generate sample QR codes for testing\n  generateSampleQrCodes() {\n    return [this.generateUserTransferQr('user123', 1), this.generateUserTransferQr('user456', 3), this.generateValidatorQr('validator789', 'community_service', 10), JSON.stringify({\n      type: QrCodeType.PARTNER_REWARD,\n      points: 5,\n      timestamp: Date.now()\n    }), JSON.stringify({\n      type: QrCodeType.SYSTEM_BONUS,\n      points: 3,\n      timestamp: Date.now()\n    })];\n  }\n  // Clear scan result\n  clearScanResult() {\n    this.scanResultSubject.next(null);\n  }\n  static {\n    this.ɵfac = function QrService_Factory(t) {\n      return new (t || QrService)(i0.ɵɵinject(i1.UserService), i0.ɵɵinject(i2.AuthService));\n    };\n  }\n  static {\n    this.ɵprov = /*@__PURE__*/i0.ɵɵdefineInjectable({\n      token: QrService,\n      factory: QrService.ɵfac,\n      providedIn: 'root'\n    });\n  }\n}", "map": {"version": 3, "names": ["BehaviorSubject", "QrCodeType", "QrService", "constructor", "userService", "authService", "scanResultSubject", "scanResult$", "asObservable", "processScanResult", "rawData", "location", "_this", "_asyncToGenerator", "scanResult", "data", "format", "timestamp", "Date", "next", "parseQrData", "parsed", "JSON", "parse", "isValidQrCodeData", "parseSimpleQrData", "generateUserTransferQr", "userId", "points", "qrData", "type", "USER_TRANSFER", "now", "signature", "generateSignature", "stringify", "generateValidatorQr", "validatorId", "action", "VALIDATOR_ACTION", "handleQrScan", "_this2", "currentUser", "getCurrentUser", "Error", "handleUserTransfer", "handleValidatorAction", "PARTNER_REWARD", "handlePartnerReward", "SYSTEM_BONUS", "handleSystemBonus", "targetUserId", "message", "role", "Object", "values", "includes", "startsWith", "substring", "parts", "split", "parseInt", "btoa", "generateSampleQrCodes", "clearScanResult", "i0", "ɵɵinject", "i1", "UserService", "i2", "AuthService", "factory", "ɵfac", "providedIn"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\services\\qr.service.ts"], "sourcesContent": ["import { Injectable } from '@angular/core';\nimport { BehaviorSubject, Observable } from 'rxjs';\nimport { QrScanResult, QrCodeData, QrCodeType, User } from '../models';\nimport { UserService } from './user.service';\nimport { AuthService } from './auth.service';\n\n@Injectable({\n  providedIn: 'root'\n})\nexport class QrService {\n  private scanResultSubject = new BehaviorSubject<QrScanResult | null>(null);\n  public scanResult$ = this.scanResultSubject.asObservable();\n\n  constructor(\n    private userService: UserService,\n    private authService: AuthService\n  ) {}\n\n  // Process scanned QR code\n  async processScanResult(rawData: string, location?: any): Promise<QrScanResult> {\n    const scanResult: QrScanResult = {\n      data: rawData,\n      format: 'QR_CODE',\n      timestamp: new Date(),\n      location\n    };\n\n    this.scanResultSubject.next(scanResult);\n    return scanResult;\n  }\n\n  // Parse QR code data\n  parseQrData(rawData: string): QrCodeData | null {\n    try {\n      // Try to parse as JSON first\n      const parsed = JSON.parse(rawData);\n      if (this.isValidQrCodeData(parsed)) {\n        return parsed as QrCodeData;\n      }\n    } catch {\n      // If not JSON, try to parse as simple formats\n      return this.parseSimpleQrData(rawData);\n    }\n    return null;\n  }\n\n  // Generate QR code data for user transfers\n  generateUserTransferQr(userId: string, points?: number): string {\n    const qrData: QrCodeData = {\n      type: QrCodeType.USER_TRANSFER,\n      userId,\n      points,\n      timestamp: Date.now(),\n      signature: this.generateSignature(userId, points)\n    };\n    return JSON.stringify(qrData);\n  }\n\n  // Generate QR code for validator actions\n  generateValidatorQr(validatorId: string, action: string, points: number): string {\n    const qrData: QrCodeData = {\n      type: QrCodeType.VALIDATOR_ACTION,\n      validatorId,\n      action,\n      points,\n      timestamp: Date.now(),\n      signature: this.generateSignature(validatorId, points, action)\n    };\n    return JSON.stringify(qrData);\n  }\n\n  // Handle different types of QR scans\n  async handleQrScan(qrData: QrCodeData): Promise<any> {\n    const currentUser = this.authService.getCurrentUser();\n    if (!currentUser) {\n      throw new Error('User not authenticated');\n    }\n\n    switch (qrData.type) {\n      case QrCodeType.USER_TRANSFER:\n        return this.handleUserTransfer(qrData, currentUser);\n      \n      case QrCodeType.VALIDATOR_ACTION:\n        return this.handleValidatorAction(qrData, currentUser);\n      \n      case QrCodeType.PARTNER_REWARD:\n        return this.handlePartnerReward(qrData, currentUser);\n      \n      case QrCodeType.SYSTEM_BONUS:\n        return this.handleSystemBonus(qrData, currentUser);\n      \n      default:\n        throw new Error('Unknown QR code type');\n    }\n  }\n\n  private async handleUserTransfer(qrData: QrCodeData, currentUser: User): Promise<any> {\n    if (!qrData.userId) {\n      throw new Error('Invalid user transfer QR code');\n    }\n\n    // For demo purposes, we'll simulate a simple point transfer\n    const points = qrData.points || 1; // Default 1 point if not specified\n    \n    return {\n      type: 'user_transfer',\n      targetUserId: qrData.userId,\n      points,\n      message: `Ready to transfer ${points} point(s) to user`\n    };\n  }\n\n  private async handleValidatorAction(qrData: QrCodeData, currentUser: User): Promise<any> {\n    if (currentUser.role !== 'validator') {\n      throw new Error('Only validators can process validator QR codes');\n    }\n\n    return {\n      type: 'validator_action',\n      action: qrData.action,\n      points: qrData.points || 10,\n      message: `Validation action: ${qrData.action}`\n    };\n  }\n\n  private async handlePartnerReward(qrData: QrCodeData, currentUser: User): Promise<any> {\n    return {\n      type: 'partner_reward',\n      points: qrData.points || 5,\n      message: 'Partner reward scanned'\n    };\n  }\n\n  private async handleSystemBonus(qrData: QrCodeData, currentUser: User): Promise<any> {\n    return {\n      type: 'system_bonus',\n      points: qrData.points || 3,\n      message: 'System bonus activated'\n    };\n  }\n\n  // Validate QR code data structure\n  private isValidQrCodeData(data: any): boolean {\n    return data &&\n           typeof data.type === 'string' &&\n           Object.values(QrCodeType).includes(data.type) &&\n           typeof data.timestamp === 'number';\n  }\n\n  // Parse simple QR data formats (for demo/testing)\n  private parseSimpleQrData(rawData: string): QrCodeData | null {\n    // Handle simple formats like \"user:123\" or \"points:5\"\n    if (rawData.startsWith('user:')) {\n      const userId = rawData.substring(5);\n      return {\n        type: QrCodeType.USER_TRANSFER,\n        userId,\n        timestamp: Date.now()\n      };\n    }\n\n    if (rawData.startsWith('validator:')) {\n      const parts = rawData.substring(10).split(':');\n      return {\n        type: QrCodeType.VALIDATOR_ACTION,\n        validatorId: parts[0],\n        action: parts[1] || 'community_service',\n        points: parseInt(parts[2]) || 10,\n        timestamp: Date.now()\n      };\n    }\n\n    // Default: treat as user ID\n    return {\n      type: QrCodeType.USER_TRANSFER,\n      userId: rawData,\n      timestamp: Date.now()\n    };\n  }\n\n  // Generate simple signature for QR codes (for demo purposes)\n  private generateSignature(userId: string, points?: number, action?: string): string {\n    const data = `${userId}-${points || 0}-${action || ''}-${Date.now()}`;\n    return btoa(data).substring(0, 16);\n  }\n\n  // Generate sample QR codes for testing\n  generateSampleQrCodes(): string[] {\n    return [\n      this.generateUserTransferQr('user123', 1),\n      this.generateUserTransferQr('user456', 3),\n      this.generateValidatorQr('validator789', 'community_service', 10),\n      JSON.stringify({\n        type: QrCodeType.PARTNER_REWARD,\n        points: 5,\n        timestamp: Date.now()\n      }),\n      JSON.stringify({\n        type: QrCodeType.SYSTEM_BONUS,\n        points: 3,\n        timestamp: Date.now()\n      })\n    ];\n  }\n\n  // Clear scan result\n  clearScanResult(): void {\n    this.scanResultSubject.next(null);\n  }\n}\n"], "mappings": ";AACA,SAASA,eAAe,QAAoB,MAAM;AAClD,SAAmCC,UAAU,QAAc,WAAW;;;;AAOtE,OAAM,MAAOC,SAAS;EAIpBC,YACUC,WAAwB,EACxBC,WAAwB;IADxB,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IALb,KAAAC,iBAAiB,GAAG,IAAIN,eAAe,CAAsB,IAAI,CAAC;IACnE,KAAAO,WAAW,GAAG,IAAI,CAACD,iBAAiB,CAACE,YAAY,EAAE;EAKvD;EAEH;EACMC,iBAAiBA,CAACC,OAAe,EAAEC,QAAc;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACrD,MAAMC,UAAU,GAAiB;QAC/BC,IAAI,EAAEL,OAAO;QACbM,MAAM,EAAE,SAAS;QACjBC,SAAS,EAAE,IAAIC,IAAI,EAAE;QACrBP;OACD;MAEDC,KAAI,CAACN,iBAAiB,CAACa,IAAI,CAACL,UAAU,CAAC;MACvC,OAAOA,UAAU;IAAC;EACpB;EAEA;EACAM,WAAWA,CAACV,OAAe;IACzB,IAAI;MACF;MACA,MAAMW,MAAM,GAAGC,IAAI,CAACC,KAAK,CAACb,OAAO,CAAC;MAClC,IAAI,IAAI,CAACc,iBAAiB,CAACH,MAAM,CAAC,EAAE;QAClC,OAAOA,MAAoB;;KAE9B,CAAC,MAAM;MACN;MACA,OAAO,IAAI,CAACI,iBAAiB,CAACf,OAAO,CAAC;;IAExC,OAAO,IAAI;EACb;EAEA;EACAgB,sBAAsBA,CAACC,MAAc,EAAEC,MAAe;IACpD,MAAMC,MAAM,GAAe;MACzBC,IAAI,EAAE7B,UAAU,CAAC8B,aAAa;MAC9BJ,MAAM;MACNC,MAAM;MACNX,SAAS,EAAEC,IAAI,CAACc,GAAG,EAAE;MACrBC,SAAS,EAAE,IAAI,CAACC,iBAAiB,CAACP,MAAM,EAAEC,MAAM;KACjD;IACD,OAAON,IAAI,CAACa,SAAS,CAACN,MAAM,CAAC;EAC/B;EAEA;EACAO,mBAAmBA,CAACC,WAAmB,EAAEC,MAAc,EAAEV,MAAc;IACrE,MAAMC,MAAM,GAAe;MACzBC,IAAI,EAAE7B,UAAU,CAACsC,gBAAgB;MACjCF,WAAW;MACXC,MAAM;MACNV,MAAM;MACNX,SAAS,EAAEC,IAAI,CAACc,GAAG,EAAE;MACrBC,SAAS,EAAE,IAAI,CAACC,iBAAiB,CAACG,WAAW,EAAET,MAAM,EAAEU,MAAM;KAC9D;IACD,OAAOhB,IAAI,CAACa,SAAS,CAACN,MAAM,CAAC;EAC/B;EAEA;EACMW,YAAYA,CAACX,MAAkB;IAAA,IAAAY,MAAA;IAAA,OAAA5B,iBAAA;MACnC,MAAM6B,WAAW,GAAGD,MAAI,CAACpC,WAAW,CAACsC,cAAc,EAAE;MACrD,IAAI,CAACD,WAAW,EAAE;QAChB,MAAM,IAAIE,KAAK,CAAC,wBAAwB,CAAC;;MAG3C,QAAQf,MAAM,CAACC,IAAI;QACjB,KAAK7B,UAAU,CAAC8B,aAAa;UAC3B,OAAOU,MAAI,CAACI,kBAAkB,CAAChB,MAAM,EAAEa,WAAW,CAAC;QAErD,KAAKzC,UAAU,CAACsC,gBAAgB;UAC9B,OAAOE,MAAI,CAACK,qBAAqB,CAACjB,MAAM,EAAEa,WAAW,CAAC;QAExD,KAAKzC,UAAU,CAAC8C,cAAc;UAC5B,OAAON,MAAI,CAACO,mBAAmB,CAACnB,MAAM,EAAEa,WAAW,CAAC;QAEtD,KAAKzC,UAAU,CAACgD,YAAY;UAC1B,OAAOR,MAAI,CAACS,iBAAiB,CAACrB,MAAM,EAAEa,WAAW,CAAC;QAEpD;UACE,MAAM,IAAIE,KAAK,CAAC,sBAAsB,CAAC;;IAC1C;EACH;EAEcC,kBAAkBA,CAAChB,MAAkB,EAAEa,WAAiB;IAAA,OAAA7B,iBAAA;MACpE,IAAI,CAACgB,MAAM,CAACF,MAAM,EAAE;QAClB,MAAM,IAAIiB,KAAK,CAAC,+BAA+B,CAAC;;MAGlD;MACA,MAAMhB,MAAM,GAAGC,MAAM,CAACD,MAAM,IAAI,CAAC,CAAC,CAAC;MAEnC,OAAO;QACLE,IAAI,EAAE,eAAe;QACrBqB,YAAY,EAAEtB,MAAM,CAACF,MAAM;QAC3BC,MAAM;QACNwB,OAAO,EAAE,qBAAqBxB,MAAM;OACrC;IAAC;EACJ;EAEckB,qBAAqBA,CAACjB,MAAkB,EAAEa,WAAiB;IAAA,OAAA7B,iBAAA;MACvE,IAAI6B,WAAW,CAACW,IAAI,KAAK,WAAW,EAAE;QACpC,MAAM,IAAIT,KAAK,CAAC,gDAAgD,CAAC;;MAGnE,OAAO;QACLd,IAAI,EAAE,kBAAkB;QACxBQ,MAAM,EAAET,MAAM,CAACS,MAAM;QACrBV,MAAM,EAAEC,MAAM,CAACD,MAAM,IAAI,EAAE;QAC3BwB,OAAO,EAAE,sBAAsBvB,MAAM,CAACS,MAAM;OAC7C;IAAC;EACJ;EAEcU,mBAAmBA,CAACnB,MAAkB,EAAEa,WAAiB;IAAA,OAAA7B,iBAAA;MACrE,OAAO;QACLiB,IAAI,EAAE,gBAAgB;QACtBF,MAAM,EAAEC,MAAM,CAACD,MAAM,IAAI,CAAC;QAC1BwB,OAAO,EAAE;OACV;IAAC;EACJ;EAEcF,iBAAiBA,CAACrB,MAAkB,EAAEa,WAAiB;IAAA,OAAA7B,iBAAA;MACnE,OAAO;QACLiB,IAAI,EAAE,cAAc;QACpBF,MAAM,EAAEC,MAAM,CAACD,MAAM,IAAI,CAAC;QAC1BwB,OAAO,EAAE;OACV;IAAC;EACJ;EAEA;EACQ5B,iBAAiBA,CAACT,IAAS;IACjC,OAAOA,IAAI,IACJ,OAAOA,IAAI,CAACe,IAAI,KAAK,QAAQ,IAC7BwB,MAAM,CAACC,MAAM,CAACtD,UAAU,CAAC,CAACuD,QAAQ,CAACzC,IAAI,CAACe,IAAI,CAAC,IAC7C,OAAOf,IAAI,CAACE,SAAS,KAAK,QAAQ;EAC3C;EAEA;EACQQ,iBAAiBA,CAACf,OAAe;IACvC;IACA,IAAIA,OAAO,CAAC+C,UAAU,CAAC,OAAO,CAAC,EAAE;MAC/B,MAAM9B,MAAM,GAAGjB,OAAO,CAACgD,SAAS,CAAC,CAAC,CAAC;MACnC,OAAO;QACL5B,IAAI,EAAE7B,UAAU,CAAC8B,aAAa;QAC9BJ,MAAM;QACNV,SAAS,EAAEC,IAAI,CAACc,GAAG;OACpB;;IAGH,IAAItB,OAAO,CAAC+C,UAAU,CAAC,YAAY,CAAC,EAAE;MACpC,MAAME,KAAK,GAAGjD,OAAO,CAACgD,SAAS,CAAC,EAAE,CAAC,CAACE,KAAK,CAAC,GAAG,CAAC;MAC9C,OAAO;QACL9B,IAAI,EAAE7B,UAAU,CAACsC,gBAAgB;QACjCF,WAAW,EAAEsB,KAAK,CAAC,CAAC,CAAC;QACrBrB,MAAM,EAAEqB,KAAK,CAAC,CAAC,CAAC,IAAI,mBAAmB;QACvC/B,MAAM,EAAEiC,QAAQ,CAACF,KAAK,CAAC,CAAC,CAAC,CAAC,IAAI,EAAE;QAChC1C,SAAS,EAAEC,IAAI,CAACc,GAAG;OACpB;;IAGH;IACA,OAAO;MACLF,IAAI,EAAE7B,UAAU,CAAC8B,aAAa;MAC9BJ,MAAM,EAAEjB,OAAO;MACfO,SAAS,EAAEC,IAAI,CAACc,GAAG;KACpB;EACH;EAEA;EACQE,iBAAiBA,CAACP,MAAc,EAAEC,MAAe,EAAEU,MAAe;IACxE,MAAMvB,IAAI,GAAG,GAAGY,MAAM,IAAIC,MAAM,IAAI,CAAC,IAAIU,MAAM,IAAI,EAAE,IAAIpB,IAAI,CAACc,GAAG,EAAE,EAAE;IACrE,OAAO8B,IAAI,CAAC/C,IAAI,CAAC,CAAC2C,SAAS,CAAC,CAAC,EAAE,EAAE,CAAC;EACpC;EAEA;EACAK,qBAAqBA,CAAA;IACnB,OAAO,CACL,IAAI,CAACrC,sBAAsB,CAAC,SAAS,EAAE,CAAC,CAAC,EACzC,IAAI,CAACA,sBAAsB,CAAC,SAAS,EAAE,CAAC,CAAC,EACzC,IAAI,CAACU,mBAAmB,CAAC,cAAc,EAAE,mBAAmB,EAAE,EAAE,CAAC,EACjEd,IAAI,CAACa,SAAS,CAAC;MACbL,IAAI,EAAE7B,UAAU,CAAC8C,cAAc;MAC/BnB,MAAM,EAAE,CAAC;MACTX,SAAS,EAAEC,IAAI,CAACc,GAAG;KACpB,CAAC,EACFV,IAAI,CAACa,SAAS,CAAC;MACbL,IAAI,EAAE7B,UAAU,CAACgD,YAAY;MAC7BrB,MAAM,EAAE,CAAC;MACTX,SAAS,EAAEC,IAAI,CAACc,GAAG;KACpB,CAAC,CACH;EACH;EAEA;EACAgC,eAAeA,CAAA;IACb,IAAI,CAAC1D,iBAAiB,CAACa,IAAI,CAAC,IAAI,CAAC;EACnC;;;uBAvMWjB,SAAS,EAAA+D,EAAA,CAAAC,QAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAH,EAAA,CAAAC,QAAA,CAAAG,EAAA,CAAAC,WAAA;IAAA;EAAA;;;aAATpE,SAAS;MAAAqE,OAAA,EAATrE,SAAS,CAAAsE,IAAA;MAAAC,UAAA,EAFR;IAAM;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}