{"ast": null, "code": "import { UserRole } from '../../../../core/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../core/services/dashboard-router.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"../../../../shared/pipes/time-ago.pipe\";\nfunction DashboardComponent_div_0_div_67_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 57)(1, \"div\", 58)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 59)(5, \"p\", 60);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 61);\n    i0.ɵɵtext(8);\n    i0.ɵɵpipe(9, \"timeAgo\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(10, \"div\", 62);\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const activity_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r2.getActivityColor(activity_r2.type));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getActivityIcon(activity_r2.type), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(activity_r2.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(9, 6, activity_r2.timestamp));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"+\", activity_r2.points, \"\");\n  }\n}\nfunction DashboardComponent_div_0_div_75_mat_icon_2_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 71);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r4 = i0.ɵɵnextContext().index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r2.getTrophyColor(i_r4));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getTrophyIcon(i_r4), \" \");\n  }\n}\nfunction DashboardComponent_div_0_div_75_span_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 72);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const i_r4 = i0.ɵɵnextContext().index;\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(i_r4 + 1);\n  }\n}\nfunction DashboardComponent_div_0_div_75_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 63)(1, \"div\", 64);\n    i0.ɵɵtemplate(2, DashboardComponent_div_0_div_75_mat_icon_2_Template, 2, 3, \"mat-icon\", 65)(3, DashboardComponent_div_0_div_75_span_3_Template, 2, 1, \"span\", 66);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 67)(5, \"span\", 68);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"span\", 69);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(9, \"div\", 70);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const leader_r5 = ctx.$implicit;\n    const i_r4 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"current-user\", leader_r5.uid === ctx_r2.user.uid);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", i_r4 < 3);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", i_r4 >= 3);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(leader_r5.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(leader_r5.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(leader_r5.points);\n  }\n}\nfunction DashboardComponent_div_0_mat_card_85_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 73);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_mat_card_85_Template_mat_card_click_0_listener() {\n      const event_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.viewEvent(event_r7));\n    });\n    i0.ɵɵelementStart(1, \"div\", 74)(2, \"div\", 75);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 76);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 77)(10, \"div\", 78)(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\");\n    i0.ɵɵtext(14);\n    i0.ɵɵpipe(15, \"date\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(16, \"div\", 78)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\");\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(21, \"div\", 78)(22, \"mat-icon\");\n    i0.ɵɵtext(23, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(24, \"span\");\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(26, \"button\", 79);\n    i0.ɵɵtext(27, \" Participer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const event_r7 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-image\", \"url(\" + event_r7.image + \")\");\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"badge-\" + event_r7.category);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", event_r7.category, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(event_r7.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(event_r7.description);\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(15, 10, event_r7.date, \"short\"));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(event_r7.location);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", event_r7.points, \" points\");\n  }\n}\nfunction DashboardComponent_div_0_mat_card_92_mat_icon_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 90);\n    i0.ɵɵtext(1, \"star\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_0_mat_card_92_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 80);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_mat_card_92_Template_mat_card_click_0_listener() {\n      const partner_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.viewPartner(partner_r9));\n    });\n    i0.ɵɵelementStart(1, \"div\", 81);\n    i0.ɵɵelement(2, \"img\", 82);\n    i0.ɵɵelementStart(3, \"div\", 83)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 84);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 85);\n    i0.ɵɵtemplate(9, DashboardComponent_div_0_mat_card_92_mat_icon_9_Template, 2, 0, \"mat-icon\", 86);\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"mat-card-content\")(13, \"p\", 87);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 88)(16, \"button\", 89)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"qr_code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19, \" Scanner QR \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 89)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"directions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Itin\\u00E9raire \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const partner_r9 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", partner_r9.logo, i0.ɵɵsanitizeUrl)(\"alt\", partner_r9.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(partner_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(partner_r9.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.getStars(partner_r9.rating));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", partner_r9.reviews, \")\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(partner_r9.currentOffer);\n  }\n}\nfunction DashboardComponent_div_0_div_99_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 95);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_div_99_button_4_Template_button_click_0_listener() {\n      const action_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r2.executeAction(action_r11));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 96);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const action_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"color\", action_r11.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r11.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r11.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"+\", action_r11.points, \"\");\n  }\n}\nfunction DashboardComponent_div_0_div_99_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 91)(1, \"h3\", 92);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 93);\n    i0.ɵɵtemplate(4, DashboardComponent_div_0_div_99_button_4_Template, 7, 4, \"button\", 94);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r12.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", category_r12.actions);\n  }\n}\nfunction DashboardComponent_div_0_mat_card_106_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 97)(1, \"div\", 98)(2, \"mat-icon\", 99);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"p\", 100);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 101)(10, \"div\", 102);\n    i0.ɵɵelement(11, \"div\", 103);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 22);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 104)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"card_giftcard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"button\", 105);\n    i0.ɵɵtext(20, \" Participer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const challenge_r13 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", challenge_r13.gradient);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(challenge_r13.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(challenge_r13.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(challenge_r13.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", challenge_r13.progress + \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", challenge_r13.current, \"/\", challenge_r13.target, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(challenge_r13.reward);\n  }\n}\nfunction DashboardComponent_div_0_div_135_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"span\", 107);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 108)(4, \"div\", 109)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 110)(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const stat_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r14.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", stat_r14.monastir + \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r14.monastirValue);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", stat_r14.sousse + \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r14.sousseValue);\n  }\n}\nfunction DashboardComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"location_city\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"h1\", 5);\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"p\", 6);\n    i0.ɵɵtext(11, \"Ensemble, construisons une communaut\\u00E9 plus forte\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"div\", 7)(13, \"div\", 8)(14, \"span\", 9);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 10);\n    i0.ɵɵtext(17, \"Points\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(18, \"div\", 8)(19, \"span\", 9);\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(21, \"span\", 10);\n    i0.ɵɵtext(22, \"Rang\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 8)(24, \"span\", 9);\n    i0.ɵɵtext(25);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\", 10);\n    i0.ɵɵtext(27, \"Impact\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(28, \"div\", 11)(29, \"div\", 12)(30, \"mat-icon\", 13);\n    i0.ɵɵtext(31);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(32, \"div\", 14)(33, \"h2\", 15)(34, \"mat-icon\");\n    i0.ɵɵtext(35, \"trending_up\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(36, \" Impact Communautaire \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"div\", 16)(38, \"mat-card\", 17)(39, \"mat-card-content\")(40, \"h3\");\n    i0.ɵɵtext(41);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(42, \"div\", 18);\n    i0.ɵɵnamespaceSVG();\n    i0.ɵɵelementStart(43, \"svg\", 19);\n    i0.ɵɵelement(44, \"circle\", 20)(45, \"circle\", 21);\n    i0.ɵɵelementEnd();\n    i0.ɵɵnamespaceHTML();\n    i0.ɵɵelementStart(46, \"div\", 22)(47, \"span\", 23);\n    i0.ɵɵtext(48);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(49, \"span\", 24);\n    i0.ɵɵtext(50, \"Objectif mensuel\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(51, \"div\", 25)(52, \"div\", 26)(53, \"span\");\n    i0.ɵɵtext(54, \"Actions valid\\u00E9es\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(55, \"span\");\n    i0.ɵɵtext(56);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(57, \"div\", 26)(58, \"span\");\n    i0.ɵɵtext(59, \"Participants actifs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(60, \"span\");\n    i0.ɵɵtext(61);\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(62, \"mat-card\", 27)(63, \"mat-card-content\")(64, \"h3\");\n    i0.ɵɵtext(65, \"Activit\\u00E9 R\\u00E9cente\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(66, \"div\", 28);\n    i0.ɵɵtemplate(67, DashboardComponent_div_0_div_67_Template, 12, 8, \"div\", 29);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"button\", 30);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_Template_button_click_68_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.viewAllActivity());\n    });\n    i0.ɵɵtext(69, \" Voir tout \");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(70, \"mat-card\", 31)(71, \"mat-card-content\")(72, \"h3\");\n    i0.ɵɵtext(73, \"Top Contributeurs\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(74, \"div\", 32);\n    i0.ɵɵtemplate(75, DashboardComponent_div_0_div_75_Template, 11, 7, \"div\", 33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(76, \"button\", 34);\n    i0.ɵɵtext(77, \" Classement complet \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(78, \"div\", 35)(79, \"h2\", 15)(80, \"mat-icon\");\n    i0.ɵɵtext(81, \"event\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(82);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(83, \"div\", 36)(84, \"div\", 37);\n    i0.ɵɵtemplate(85, DashboardComponent_div_0_mat_card_85_Template, 28, 13, \"mat-card\", 38);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(86, \"div\", 39)(87, \"h2\", 15)(88, \"mat-icon\");\n    i0.ɵɵtext(89, \"store\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(90, \" Partenaires Locaux \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(91, \"div\", 40);\n    i0.ɵɵtemplate(92, DashboardComponent_div_0_mat_card_92_Template, 24, 7, \"mat-card\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(93, \"div\", 42)(94, \"h2\", 15)(95, \"mat-icon\");\n    i0.ɵɵtext(96, \"flash_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(97, \" Actions Rapides \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"div\", 43);\n    i0.ɵɵtemplate(99, DashboardComponent_div_0_div_99_Template, 5, 2, \"div\", 44);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(100, \"div\", 45)(101, \"h2\", 15)(102, \"mat-icon\");\n    i0.ɵɵtext(103, \"emoji_events\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(104, \" D\\u00E9fis Communautaires \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(105, \"div\", 46);\n    i0.ɵɵtemplate(106, DashboardComponent_div_0_mat_card_106_Template, 21, 10, \"mat-card\", 47);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(107, \"div\", 48)(108, \"h2\", 15)(109, \"mat-icon\");\n    i0.ɵɵtext(110, \"compare_arrows\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(111, \" Monastir vs Sousse \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(112, \"mat-card\", 49)(113, \"mat-card-content\")(114, \"div\", 50)(115, \"div\", 51)(116, \"h3\");\n    i0.ɵɵtext(117, \"Monastir\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(118, \"div\", 52);\n    i0.ɵɵtext(119);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(120, \"p\");\n    i0.ɵɵtext(121);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(122, \"div\", 53)(123, \"mat-icon\");\n    i0.ɵɵtext(124, \"sports\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(125, \"span\");\n    i0.ɵɵtext(126, \"VS\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(127, \"div\", 54)(128, \"h3\");\n    i0.ɵɵtext(129, \"Sousse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(130, \"div\", 52);\n    i0.ɵɵtext(131);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(132, \"p\");\n    i0.ɵɵtext(133);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(134, \"div\", 55);\n    i0.ɵɵtemplate(135, DashboardComponent_div_0_div_135_Template, 10, 7, \"div\", 56);\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵclassMap(\"city-\" + ctx_r2.user.city.toLowerCase());\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.user.city);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r2.getGreeting(), \", \", ctx_r2.user.name, \"!\");\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.user.points);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getCommunityRank());\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getImpactScore());\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate(ctx_r2.getCityIcon());\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate1(\"Progr\\u00E8s de \", ctx_r2.user.city, \"\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"stroke-dasharray\", ctx_r2.getCityProgress() + \" 314\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.getCityProgressPercent(), \"%\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r2.getCityStats().validatedActions);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(ctx_r2.getCityStats().activeUsers);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.communityFeed.slice(0, 4));\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.topContributors);\n    i0.ɵɵadvance(7);\n    i0.ɵɵtextInterpolate1(\" \\u00C9v\\u00E9nements \\u00E0 \", ctx_r2.user.city, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.localEvents);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.featuredPartners);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.actionCategories);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.activeChallenges);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r2.cityComparison.monastir.score);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.cityComparison.monastir.participants, \" participants\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r2.cityComparison.sousse.score);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r2.cityComparison.sousse.participants, \" participants\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.comparisonStats);\n  }\n}\nexport class DashboardComponent {\n  constructor(authService, router, dashboardRouter) {\n    this.authService = authService;\n    this.router = router;\n    this.dashboardRouter = dashboardRouter;\n    this.user = null;\n    // Community data\n    this.communityFeed = [];\n    this.topContributors = [];\n    this.localEvents = [];\n    this.featuredPartners = [];\n    this.activeChallenges = [];\n    // City comparison data\n    this.cityComparison = {\n      monastir: {\n        score: 0,\n        participants: 0\n      },\n      sousse: {\n        score: 0,\n        participants: 0\n      }\n    };\n    this.comparisonStats = [{\n      name: 'Actions validées',\n      monastir: 0,\n      sousse: 0,\n      monastirValue: 0,\n      sousseValue: 0\n    }, {\n      name: 'Événements organisés',\n      monastir: 0,\n      sousse: 0,\n      monastirValue: 0,\n      sousseValue: 0\n    }, {\n      name: 'Partenaires actifs',\n      monastir: 0,\n      sousse: 0,\n      monastirValue: 0,\n      sousseValue: 0\n    }];\n    this.actionCategories = [{\n      title: 'Environnement',\n      actions: [{\n        title: 'Nettoyage plage',\n        icon: 'waves',\n        color: 'primary',\n        points: 15\n      }, {\n        title: 'Plantation arbres',\n        icon: 'park',\n        color: 'primary',\n        points: 20\n      }, {\n        title: 'Recyclage',\n        icon: 'recycling',\n        color: 'primary',\n        points: 10\n      }]\n    }, {\n      title: 'Social',\n      actions: [{\n        title: 'Aide personnes âgées',\n        icon: 'elderly',\n        color: 'accent',\n        points: 25\n      }, {\n        title: 'Cours bénévoles',\n        icon: 'school',\n        color: 'accent',\n        points: 30\n      }, {\n        title: 'Distribution repas',\n        icon: 'restaurant',\n        color: 'accent',\n        points: 20\n      }]\n    }, {\n      title: 'Culture',\n      actions: [{\n        title: 'Guide touristique',\n        icon: 'tour',\n        color: 'warn',\n        points: 15\n      }, {\n        title: 'Animation enfants',\n        icon: 'child_care',\n        color: 'warn',\n        points: 18\n      }, {\n        title: 'Événement culturel',\n        icon: 'theater_comedy',\n        color: 'warn',\n        points: 25\n      }]\n    }];\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        // Rediriger vers le dashboard approprié selon le rôle\n        if (user.role !== UserRole.USER) {\n          this.dashboardRouter.navigateToUserDashboard(user);\n          return;\n        }\n        this.loadDashboardData();\n      }\n    });\n  }\n  loadDashboardData() {\n    this.loadCommunityFeed();\n    this.loadTopContributors();\n    this.loadLocalEvents();\n    this.loadFeaturedPartners();\n    this.loadActiveChallenges();\n    this.loadCityComparison();\n  }\n  loadCommunityFeed() {\n    // Simulate real community activity feed\n    this.communityFeed = [{\n      id: '1',\n      description: 'Ahmed a nettoyé la plage de Monastir',\n      points: 15,\n      type: 'environment',\n      timestamp: new Date(Date.now() - 1000 * 60 * 30),\n      userId: 'user1',\n      userName: 'Ahmed'\n    }, {\n      id: '2',\n      description: 'Fatma a aidé des personnes âgées à Sousse',\n      points: 25,\n      type: 'social',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60),\n      userId: 'user2',\n      userName: 'Fatma'\n    }, {\n      id: '3',\n      description: 'Mohamed a organisé un cours de français',\n      points: 30,\n      type: 'education',\n      timestamp: new Date(Date.now() - 1000 * 60 * 90),\n      userId: 'user3',\n      userName: 'Mohamed'\n    }, {\n      id: '4',\n      description: 'Leila a guidé des touristes au Ribat',\n      points: 15,\n      type: 'culture',\n      timestamp: new Date(Date.now() - 1000 * 60 * 120),\n      userId: 'user4',\n      userName: 'Leila'\n    }];\n  }\n  loadTopContributors() {\n    this.topContributors = [{\n      uid: 'top1',\n      name: 'Ahmed Ben Ali',\n      city: 'Monastir',\n      points: 1250,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }, {\n      uid: 'top2',\n      name: 'Fatma Trabelsi',\n      city: 'Sousse',\n      points: 1180,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }, {\n      uid: 'top3',\n      name: 'Mohamed Gharbi',\n      city: 'Monastir',\n      points: 1050,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }, {\n      uid: 'top4',\n      name: 'Leila Mansouri',\n      city: 'Sousse',\n      points: 980,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }, {\n      uid: 'top5',\n      name: 'Karim Bouazizi',\n      city: 'Monastir',\n      points: 920,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }];\n  }\n  loadLocalEvents() {\n    this.localEvents = [{\n      id: 'event1',\n      title: 'Nettoyage de la Plage de Monastir',\n      description: 'Rejoignez-nous pour nettoyer notre belle plage',\n      date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2),\n      location: 'Plage de Monastir',\n      category: 'Environnement',\n      points: 20,\n      image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=200&fit=crop',\n      participants: 45,\n      maxParticipants: 100\n    }, {\n      id: 'event2',\n      title: 'Festival Culturel de Sousse',\n      description: 'Célébrons notre patrimoine culturel ensemble',\n      date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5),\n      location: 'Médina de Sousse',\n      category: 'Culture',\n      points: 15,\n      image: 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=400&h=200&fit=crop',\n      participants: 120,\n      maxParticipants: 200\n    }, {\n      id: 'event3',\n      title: 'Cours de Français pour Réfugiés',\n      description: 'Aidez à enseigner le français aux nouveaux arrivants',\n      date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7),\n      location: 'Centre Communautaire',\n      category: 'Éducation',\n      points: 30,\n      image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=200&fit=crop',\n      participants: 15,\n      maxParticipants: 30\n    }];\n  }\n  loadFeaturedPartners() {\n    this.featuredPartners = [{\n      id: 'partner1',\n      name: 'Café des Nattes',\n      category: 'Restaurant',\n      logo: 'https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=100&h=100&fit=crop',\n      rating: 4.5,\n      reviews: 127,\n      currentOffer: '10% de réduction avec 50 points',\n      location: 'Médina de Monastir',\n      qrCode: 'PARTNER_CAFE_NATTES'\n    }, {\n      id: 'partner2',\n      name: 'Boutique Artisanat Sousse',\n      category: 'Artisanat',\n      logo: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop',\n      rating: 4.8,\n      reviews: 89,\n      currentOffer: 'Produit gratuit avec 100 points',\n      location: 'Médina de Sousse',\n      qrCode: 'PARTNER_ARTISANAT_SOUSSE'\n    }, {\n      id: 'partner3',\n      name: 'Hammam Traditionnel',\n      category: 'Bien-être',\n      logo: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=100&h=100&fit=crop',\n      rating: 4.3,\n      reviews: 156,\n      currentOffer: 'Séance gratuite avec 75 points',\n      location: 'Centre-ville Monastir',\n      qrCode: 'PARTNER_HAMMAM'\n    }];\n  }\n  loadActiveChallenges() {\n    this.activeChallenges = [{\n      id: 'challenge1',\n      title: 'Éco-Warrior',\n      description: 'Participez à 5 actions environnementales ce mois-ci',\n      icon: 'eco',\n      gradient: 'linear-gradient(135deg, #4CAF50, #8BC34A)',\n      progress: 60,\n      current: 3,\n      target: 5,\n      reward: 'Badge Éco-Warrior + 100 points bonus',\n      endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 15) // 15 days\n    }, {\n      id: 'challenge2',\n      title: 'Ambassadeur Culturel',\n      description: 'Guidez 10 touristes dans votre ville',\n      icon: 'tour',\n      gradient: 'linear-gradient(135deg, #FF9800, #FFC107)',\n      progress: 30,\n      current: 3,\n      target: 10,\n      reward: 'Badge Ambassadeur + Visite gratuite musée',\n      endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 20) // 20 days\n    }, {\n      id: 'challenge3',\n      title: 'Solidarité Communautaire',\n      description: 'Aidez 15 personnes dans le besoin',\n      icon: 'volunteer_activism',\n      gradient: 'linear-gradient(135deg, #E91E63, #F06292)',\n      progress: 80,\n      current: 12,\n      target: 15,\n      reward: 'Badge Solidarité + 200 points bonus',\n      endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 10) // 10 days\n    }];\n  }\n  loadCityComparison() {\n    // Simulate city competition data\n    this.cityComparison = {\n      monastir: {\n        score: 15420,\n        participants: 342\n      },\n      sousse: {\n        score: 16180,\n        participants: 389\n      }\n    };\n    this.comparisonStats = [{\n      name: 'Actions validées',\n      monastir: 65,\n      sousse: 78,\n      monastirValue: 1250,\n      sousseValue: 1520\n    }, {\n      name: 'Événements organisés',\n      monastir: 45,\n      sousse: 52,\n      monastirValue: 23,\n      sousseValue: 27\n    }, {\n      name: 'Partenaires actifs',\n      monastir: 80,\n      sousse: 70,\n      monastirValue: 16,\n      sousseValue: 14\n    }];\n  }\n  // UI Helper Methods\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n  getCityIcon() {\n    if (!this.user) return 'location_city';\n    return this.user.city === 'Monastir' ? 'account_balance' : 'museum';\n  }\n  getCommunityRank() {\n    if (!this.user) return 0;\n    const userIndex = this.topContributors.findIndex(u => u.uid === this.user.uid);\n    return userIndex >= 0 ? userIndex + 1 : this.topContributors.length + 1;\n  }\n  getImpactScore() {\n    if (!this.user) return 0;\n    // Calculate impact based on points and community actions\n    return Math.floor(this.user.points * 1.2 + (this.user.history?.length || 0) * 5);\n  }\n  getCityProgress() {\n    if (!this.user) return 0;\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    const monthlyTarget = 20000;\n    return Math.min(cityData.score / monthlyTarget * 314, 314); // 314 = 2π * 50 (circle circumference)\n  }\n  getCityProgressPercent() {\n    if (!this.user) return 0;\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    const monthlyTarget = 20000;\n    return Math.min(Math.round(cityData.score / monthlyTarget * 100), 100);\n  }\n  getCityStats() {\n    if (!this.user) return {\n      validatedActions: 0,\n      activeUsers: 0\n    };\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    return {\n      validatedActions: Math.floor(cityData.score / 15),\n      activeUsers: cityData.participants\n    };\n  }\n  getActivityColor(type) {\n    const colorMap = {\n      'environment': '#4CAF50',\n      'social': '#E91E63',\n      'education': '#2196F3',\n      'culture': '#FF9800',\n      'health': '#9C27B0'\n    };\n    return colorMap[type] || '#667eea';\n  }\n  getActivityIcon(type) {\n    const iconMap = {\n      'environment': 'eco',\n      'social': 'volunteer_activism',\n      'education': 'school',\n      'culture': 'theater_comedy',\n      'health': 'health_and_safety'\n    };\n    return iconMap[type] || 'circle';\n  }\n  getTrophyColor(index) {\n    const colors = ['#FFD700', '#C0C0C0', '#CD7F32']; // Gold, Silver, Bronze\n    return colors[index] || '#667eea';\n  }\n  getTrophyIcon(index) {\n    return index < 3 ? 'emoji_events' : 'star';\n  }\n  getStars(rating) {\n    return Array(Math.floor(rating)).fill(0);\n  }\n  // Action Methods\n  viewEvent(event) {\n    // Navigate to event details or open modal\n    console.log('Viewing event:', event);\n  }\n  viewPartner(partner) {\n    // Navigate to partner details or open modal\n    console.log('Viewing partner:', partner);\n  }\n  executeAction(action) {\n    // Execute the selected action\n    console.log('Executing action:', action);\n    // This would typically open a form or navigate to action details\n  }\n  viewAllActivity() {\n    this.router.navigate(['/community/activity']);\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.DashboardRouterService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"dashboard-container\", 4, \"ngIf\"], [1, \"dashboard-container\"], [1, \"hero-section\"], [1, \"hero-content\"], [1, \"city-badge\"], [1, \"hero-title\"], [1, \"hero-subtitle\"], [1, \"hero-stats\"], [1, \"hero-stat\"], [1, \"stat-number\"], [1, \"stat-label\"], [1, \"hero-visual\"], [1, \"city-illustration\"], [1, \"city-icon\"], [1, \"impact-section\"], [1, \"section-title\"], [1, \"impact-grid\"], [1, \"impact-card\", \"city-progress\"], [1, \"progress-ring\"], [\"width\", \"120\", \"height\", \"120\", 1, \"progress-svg\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", 1, \"progress-bg\"], [\"cx\", \"60\", \"cy\", \"60\", \"r\", \"50\", 1, \"progress-fill\"], [1, \"progress-text\"], [1, \"progress-value\"], [1, \"progress-label\"], [1, \"progress-details\"], [1, \"detail-item\"], [1, \"impact-card\", \"community-feed\"], [1, \"feed-list\"], [\"class\", \"feed-item\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", 1, \"view-all-btn\", 3, \"click\"], [1, \"impact-card\", \"leaderboard\"], [1, \"leaderboard-list\"], [\"class\", \"leader-item\", 3, \"current-user\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/leaderboard\", 1, \"view-all-btn\"], [1, \"events-section\"], [1, \"events-carousel\"], [1, \"events-scroll\"], [\"class\", \"event-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"partners-section\"], [1, \"partners-grid\"], [\"class\", \"partner-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"actions-hub\"], [1, \"actions-grid\"], [\"class\", \"action-category\", 4, \"ngFor\", \"ngForOf\"], [1, \"challenges-section\"], [1, \"challenges-grid\"], [\"class\", \"challenge-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"comparison-section\"], [1, \"comparison-card\"], [1, \"comparison-header\"], [1, \"city-score\", \"monastir\"], [1, \"score\"], [1, \"vs-divider\"], [1, \"city-score\", \"sousse\"], [1, \"comparison-stats\"], [\"class\", \"stat-comparison\", 4, \"ngFor\", \"ngForOf\"], [1, \"feed-item\"], [1, \"feed-avatar\"], [1, \"feed-content\"], [1, \"feed-text\"], [1, \"feed-time\"], [1, \"feed-points\"], [1, \"leader-item\"], [1, \"leader-rank\"], [\"class\", \"trophy-icon\", 3, \"color\", 4, \"ngIf\"], [\"class\", \"rank-number\", 4, \"ngIf\"], [1, \"leader-info\"], [1, \"leader-name\"], [1, \"leader-city\"], [1, \"leader-points\"], [1, \"trophy-icon\"], [1, \"rank-number\"], [1, \"event-card\", 3, \"click\"], [1, \"event-image\"], [1, \"event-badge\"], [1, \"event-description\"], [1, \"event-details\"], [1, \"event-detail\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"event-join-btn\"], [1, \"partner-card\", 3, \"click\"], [1, \"partner-header\"], [1, \"partner-logo\", 3, \"src\", \"alt\"], [1, \"partner-info\"], [1, \"partner-category\"], [1, \"partner-rating\"], [\"class\", \"star-icon\", 4, \"ngFor\", \"ngForOf\"], [1, \"partner-offer\"], [1, \"partner-actions\"], [\"mat-stroked-button\", \"\", 1, \"partner-btn\"], [1, \"star-icon\"], [1, \"action-category\"], [1, \"category-title\"], [1, \"category-actions\"], [\"mat-raised-button\", \"\", \"class\", \"action-btn\", 3, \"color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"mat-raised-button\", \"\", 1, \"action-btn\", 3, \"click\", \"color\"], [1, \"action-points\"], [1, \"challenge-card\"], [1, \"challenge-header\"], [1, \"challenge-icon\"], [1, \"challenge-description\"], [1, \"challenge-progress\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"challenge-reward\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 1, \"challenge-btn\"], [1, \"stat-comparison\"], [1, \"stat-name\"], [1, \"stat-bars\"], [1, \"stat-bar\", \"monastir\"], [1, \"stat-bar\", \"sousse\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, DashboardComponent_div_0_Template, 136, 27, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i2.RouterLink, i5.MatButton, i6.MatCard, i6.MatCardContent, i7.MatIcon, i4.DatePipe, i8.TimeAgoPipe],\n      styles: [\"\\n\\n.dashboard-container[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  animation: _ngcontent-%COMP%_fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  min-height: 100vh;\\n}\\n\\n\\n\\n.hero-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 40px;\\n  padding: 60px 40px;\\n  border-radius: 32px;\\n  color: white;\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.hero-section.city-monastir[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.hero-section.city-sousse[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n}\\n\\n.hero-section[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n.city-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.hero-title[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 3rem;\\n  font-weight: 800;\\n  font-family: 'Poppins', sans-serif;\\n  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\\n  background: linear-gradient(45deg, #ffffff, #f0f0f0);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.hero-subtitle[_ngcontent-%COMP%] {\\n  margin: 0 0 32px 0;\\n  font-size: 1.2rem;\\n  opacity: 0.9;\\n  font-weight: 400;\\n}\\n\\n.hero-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 32px;\\n}\\n\\n.hero-stat[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  line-height: 1;\\n  margin-bottom: 4px;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  opacity: 0.8;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n}\\n\\n.hero-visual[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.city-illustration[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 120px;\\n  background: rgba(255, 255, 255, 0.15);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.city-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem !important;\\n  width: 4rem !important;\\n  height: 4rem !important;\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n\\n\\n\\n.impact-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  margin: 0 0 32px 0;\\n  color: #2d3748;\\n  font-size: 2rem;\\n  font-weight: 700;\\n  font-family: 'Poppins', sans-serif;\\n  text-align: center;\\n  position: relative;\\n  padding-bottom: 16px;\\n}\\n\\n.section-title[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 80px;\\n  height: 4px;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  border-radius: 2px;\\n}\\n\\n.impact-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 24px;\\n}\\n\\n.impact-card[_ngcontent-%COMP%] {\\n  border-radius: 20px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.impact-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.impact-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #2d3748;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n\\n\\n\\n.city-progress[_ngcontent-%COMP%]   .progress-ring[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 auto 24px;\\n  width: 120px;\\n  height: 120px;\\n}\\n\\n.progress-svg[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n\\n.progress-bg[_ngcontent-%COMP%] {\\n  fill: none;\\n  stroke: #e2e8f0;\\n  stroke-width: 8;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  fill: none;\\n  stroke: #667eea;\\n  stroke-width: 8;\\n  stroke-linecap: round;\\n  transition: stroke-dasharray 1s ease-in-out;\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n}\\n\\n.progress-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #2d3748;\\n}\\n\\n.progress-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.progress-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  padding: 8px 0;\\n  border-bottom: 1px solid #e2e8f0;\\n}\\n\\n.detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n\\n\\n.community-feed[_ngcontent-%COMP%]   .feed-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n\\n.feed-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background: #f7fafc;\\n  border-radius: 12px;\\n  transition: all 0.2s ease;\\n}\\n\\n.feed-item[_ngcontent-%COMP%]:hover {\\n  background: #edf2f7;\\n  transform: translateX(4px);\\n}\\n\\n.feed-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  flex-shrink: 0;\\n}\\n\\n.feed-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.feed-text[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 0.9rem;\\n  color: #2d3748;\\n  font-weight: 500;\\n}\\n\\n.feed-time[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.feed-points[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78, #38a169);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n}\\n\\n.view-all-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-top: 8px;\\n}\\n\\n\\n\\n.leaderboard[_ngcontent-%COMP%]   .leaderboard-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.leader-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  border-radius: 12px;\\n  transition: all 0.2s ease;\\n}\\n\\n.leader-item[_ngcontent-%COMP%]:hover {\\n  background: #f7fafc;\\n}\\n\\n.leader-item.current-user[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\\n  border: 2px solid rgba(102, 126, 234, 0.3);\\n}\\n\\n.leader-rank[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 700;\\n  border-radius: 50%;\\n  background: #e2e8f0;\\n  color: #4a5568;\\n}\\n\\n.trophy-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem !important;\\n  width: 1.5rem !important;\\n  height: 1.5rem !important;\\n}\\n\\n.rank-number[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.leader-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.leader-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  color: #2d3748;\\n  font-size: 0.9rem;\\n}\\n\\n.leader-city[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.leader-points[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #667eea;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.events-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.events-carousel[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 24px;\\n  overflow-x: auto;\\n  padding-bottom: 16px;\\n  scroll-behavior: smooth;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 8px;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #667eea;\\n  border-radius: 4px;\\n}\\n\\n.event-card[_ngcontent-%COMP%] {\\n  min-width: 320px;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.event-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.event-image[_ngcontent-%COMP%] {\\n  height: 160px;\\n  background-size: cover;\\n  background-position: center;\\n  position: relative;\\n  background-color: #e2e8f0;\\n}\\n\\n.event-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  color: white;\\n  text-transform: uppercase;\\n}\\n\\n.badge-environnement[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78, #38a169);\\n}\\n\\n.badge-culture[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ed8936, #dd6b20);\\n}\\n\\n.badge-\\u00E9ducation[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4299e1, #3182ce);\\n}\\n\\n.event-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #2d3748;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.event-description[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n}\\n\\n.event-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.event-detail[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 0.8rem;\\n  color: #4a5568;\\n}\\n\\n.event-detail[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  width: 1rem !important;\\n  height: 1rem !important;\\n  color: #667eea;\\n}\\n\\n.event-join-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 36px;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.partners-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.partners-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 24px;\\n}\\n\\n.partner-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.partner-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.partner-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 20px 20px 0;\\n}\\n\\n.partner-logo[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 12px;\\n  object-fit: cover;\\n  background: #f7fafc;\\n}\\n\\n.partner-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #2d3748;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.partner-category[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n\\n.partner-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.star-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  width: 1rem !important;\\n  height: 1rem !important;\\n  color: #ffd700;\\n}\\n\\n.partner-offer[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  color: #2d3748;\\n  font-weight: 500;\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\\n  padding: 12px;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n}\\n\\n.partner-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.partner-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 36px;\\n  font-size: 0.8rem;\\n}\\n\\n\\n\\n.actions-hub[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 32px;\\n}\\n\\n.action-category[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.category-title[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #2d3748;\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  text-align: center;\\n  padding-bottom: 12px;\\n  border-bottom: 2px solid #e2e8f0;\\n}\\n\\n.category-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n  border-radius: 12px;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: left;\\n  margin-left: 12px;\\n  font-weight: 500;\\n}\\n\\n.action-points[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #2d3748;\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.challenges-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.challenges-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n  gap: 24px;\\n}\\n\\n.challenge-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n}\\n\\n.challenge-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.challenge-header[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  text-align: center;\\n  color: white;\\n  position: relative;\\n}\\n\\n.challenge-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem !important;\\n  width: 3rem !important;\\n  height: 3rem !important;\\n  margin-bottom: 12px;\\n}\\n\\n.challenge-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n}\\n\\n.challenge-description[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #4a5568;\\n  line-height: 1.5;\\n}\\n\\n.challenge-progress[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 8px;\\n  background: #e2e8f0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  margin-bottom: 8px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  border-radius: 4px;\\n  transition: width 1s ease-in-out;\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 0.9rem;\\n  color: #4a5568;\\n  font-weight: 600;\\n}\\n\\n.challenge-reward[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 20px;\\n  padding: 12px;\\n  background: #f7fafc;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  color: #2d3748;\\n}\\n\\n\\n\\n.comparison-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.comparison-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.comparison-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 32px;\\n  padding: 0 20px;\\n}\\n\\n.city-score[_ngcontent-%COMP%] {\\n  text-align: center;\\n  flex: 1;\\n}\\n\\n.city-score.monastir[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n\\n.city-score.sousse[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #f093fb;\\n}\\n\\n.city-score[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n}\\n\\n.city-score[_ngcontent-%COMP%]   .score[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  font-weight: 800;\\n  margin-bottom: 8px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.city-score.sousse[_ngcontent-%COMP%]   .score[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.city-score[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n\\n.vs-divider[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 0 20px;\\n}\\n\\n.vs-divider[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem !important;\\n  width: 2rem !important;\\n  height: 2rem !important;\\n  color: #667eea;\\n}\\n\\n.vs-divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #2d3748;\\n  font-size: 1.2rem;\\n}\\n\\n.comparison-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n\\n.stat-comparison[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.stat-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2d3748;\\n  font-size: 0.9rem;\\n  text-align: center;\\n}\\n\\n.stat-bars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  align-items: center;\\n}\\n\\n.stat-bar[_ngcontent-%COMP%] {\\n  height: 24px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  min-width: 60px;\\n  transition: all 0.3s ease;\\n}\\n\\n.stat-bar.monastir[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n}\\n\\n.stat-bar.sousse[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .hero-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 24px;\\n    text-align: center;\\n    padding: 40px 24px;\\n  }\\n\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .hero-stats[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    gap: 20px;\\n  }\\n\\n  .impact-grid[_ngcontent-%COMP%], .partners-grid[_ngcontent-%COMP%], .actions-grid[_ngcontent-%COMP%], .challenges-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .events-scroll[_ngcontent-%COMP%] {\\n    padding-left: 16px;\\n  }\\n\\n  .comparison-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n  }\\n\\n  .vs-divider[_ngcontent-%COMP%] {\\n    order: -1;\\n  }\\n\\n  .stat-bars[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n\\n  .stat-bar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n    padding: 0 12px;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .hero-stats[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .action-btn[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n    text-align: center;\\n  }\\n\\n  .action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["UserRole", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵstyleProp", "ctx_r2", "getActivityColor", "activity_r2", "type", "ɵɵtextInterpolate1", "getActivityIcon", "ɵɵtextInterpolate", "description", "ɵɵpipeBind1", "timestamp", "points", "getTrophyColor", "i_r4", "getTrophyIcon", "ɵɵtemplate", "DashboardComponent_div_0_div_75_mat_icon_2_Template", "DashboardComponent_div_0_div_75_span_3_Template", "ɵɵclassProp", "leader_r5", "uid", "user", "ɵɵproperty", "name", "city", "ɵɵlistener", "DashboardComponent_div_0_mat_card_85_Template_mat_card_click_0_listener", "event_r7", "ɵɵrestoreView", "_r6", "$implicit", "ɵɵnextContext", "ɵɵresetView", "viewEvent", "image", "ɵɵclassMap", "category", "title", "ɵɵpipeBind2", "date", "location", "DashboardComponent_div_0_mat_card_92_Template_mat_card_click_0_listener", "partner_r9", "_r8", "viewP<PERSON>ner", "ɵɵelement", "DashboardComponent_div_0_mat_card_92_mat_icon_9_Template", "logo", "ɵɵsanitizeUrl", "getStars", "rating", "reviews", "currentOffer", "DashboardComponent_div_0_div_99_button_4_Template_button_click_0_listener", "action_r11", "_r10", "executeAction", "color", "icon", "DashboardComponent_div_0_div_99_button_4_Template", "category_r12", "actions", "challenge_r13", "gradient", "progress", "ɵɵtextInterpolate2", "current", "target", "reward", "stat_r14", "monastir", "monastirValue", "sousse", "sousseValue", "DashboardComponent_div_0_div_67_Template", "DashboardComponent_div_0_Template_button_click_68_listener", "_r1", "viewAllActivity", "DashboardComponent_div_0_div_75_Template", "DashboardComponent_div_0_mat_card_85_Template", "DashboardComponent_div_0_mat_card_92_Template", "DashboardComponent_div_0_div_99_Template", "DashboardComponent_div_0_mat_card_106_Template", "DashboardComponent_div_0_div_135_Template", "toLowerCase", "getGreeting", "getCommunityRank", "getImpactScore", "getCityIcon", "getCityProgress", "getCityProgressPercent", "getCityStats", "validatedActions", "activeUsers", "communityFeed", "slice", "topContributors", "localEvents", "featuredPartners", "actionCategories", "activeChallenges", "cityComparison", "score", "participants", "comparisonStats", "DashboardComponent", "constructor", "authService", "router", "dashboardRouter", "ngOnInit", "currentUser$", "subscribe", "role", "USER", "navigateToUserDashboard", "loadDashboardData", "loadCommunityFeed", "loadTopContributors", "loadLocalEvents", "loadFeaturedPartners", "loadActiveChallenges", "loadCityComparison", "id", "Date", "now", "userId", "userName", "email", "createdAt", "updatedAt", "isActive", "history", "maxParticipants", "qrCode", "endDate", "hour", "getHours", "userIndex", "findIndex", "u", "length", "Math", "floor", "cityData", "monthlyTarget", "min", "round", "colorMap", "iconMap", "index", "colors", "Array", "fill", "event", "console", "log", "partner", "action", "navigate", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "DashboardRouterService", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { DashboardRouterService } from '../../../../core/services/dashboard-router.service';\nimport { User, UserRole } from '../../../../core/models';\n\ninterface CommunityActivity {\n  id: string;\n  description: string;\n  points: number;\n  type: string;\n  timestamp: Date;\n  userId: string;\n  userName: string;\n}\n\ninterface LocalEvent {\n  id: string;\n  title: string;\n  description: string;\n  date: Date;\n  location: string;\n  category: string;\n  points: number;\n  image: string;\n  participants: number;\n  maxParticipants: number;\n}\n\ninterface Partner {\n  id: string;\n  name: string;\n  category: string;\n  logo: string;\n  rating: number;\n  reviews: number;\n  currentOffer: string;\n  location: string;\n  qrCode: string;\n}\n\ninterface Challenge {\n  id: string;\n  title: string;\n  description: string;\n  icon: string;\n  gradient: string;\n  progress: number;\n  current: number;\n  target: number;\n  reward: string;\n  endDate: Date;\n}\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit {\n  user: User | null = null;\n\n  // Community data\n  communityFeed: CommunityActivity[] = [];\n  topContributors: User[] = [];\n  localEvents: LocalEvent[] = [];\n  featuredPartners: Partner[] = [];\n  activeChallenges: Challenge[] = [];\n\n  // City comparison data\n  cityComparison = {\n    monastir: { score: 0, participants: 0 },\n    sousse: { score: 0, participants: 0 }\n  };\n\n  comparisonStats = [\n    { name: 'Actions validées', monastir: 0, sousse: 0, monastirValue: 0, sousseValue: 0 },\n    { name: 'Événements organisés', monastir: 0, sousse: 0, monastirValue: 0, sousseValue: 0 },\n    { name: 'Partenaires actifs', monastir: 0, sousse: 0, monastirValue: 0, sousseValue: 0 }\n  ];\n\n  actionCategories = [\n    {\n      title: 'Environnement',\n      actions: [\n        { title: 'Nettoyage plage', icon: 'waves', color: 'primary', points: 15 },\n        { title: 'Plantation arbres', icon: 'park', color: 'primary', points: 20 },\n        { title: 'Recyclage', icon: 'recycling', color: 'primary', points: 10 }\n      ]\n    },\n    {\n      title: 'Social',\n      actions: [\n        { title: 'Aide personnes âgées', icon: 'elderly', color: 'accent', points: 25 },\n        { title: 'Cours bénévoles', icon: 'school', color: 'accent', points: 30 },\n        { title: 'Distribution repas', icon: 'restaurant', color: 'accent', points: 20 }\n      ]\n    },\n    {\n      title: 'Culture',\n      actions: [\n        { title: 'Guide touristique', icon: 'tour', color: 'warn', points: 15 },\n        { title: 'Animation enfants', icon: 'child_care', color: 'warn', points: 18 },\n        { title: 'Événement culturel', icon: 'theater_comedy', color: 'warn', points: 25 }\n      ]\n    }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private dashboardRouter: DashboardRouterService\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        // Rediriger vers le dashboard approprié selon le rôle\n        if (user.role !== UserRole.USER) {\n          this.dashboardRouter.navigateToUserDashboard(user);\n          return;\n        }\n        this.loadDashboardData();\n      }\n    });\n  }\n\n  private loadDashboardData(): void {\n    this.loadCommunityFeed();\n    this.loadTopContributors();\n    this.loadLocalEvents();\n    this.loadFeaturedPartners();\n    this.loadActiveChallenges();\n    this.loadCityComparison();\n  }\n\n  private loadCommunityFeed(): void {\n    // Simulate real community activity feed\n    this.communityFeed = [\n      {\n        id: '1',\n        description: 'Ahmed a nettoyé la plage de Monastir',\n        points: 15,\n        type: 'environment',\n        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 min ago\n        userId: 'user1',\n        userName: 'Ahmed'\n      },\n      {\n        id: '2',\n        description: 'Fatma a aidé des personnes âgées à Sousse',\n        points: 25,\n        type: 'social',\n        timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago\n        userId: 'user2',\n        userName: 'Fatma'\n      },\n      {\n        id: '3',\n        description: 'Mohamed a organisé un cours de français',\n        points: 30,\n        type: 'education',\n        timestamp: new Date(Date.now() - 1000 * 60 * 90), // 1.5 hours ago\n        userId: 'user3',\n        userName: 'Mohamed'\n      },\n      {\n        id: '4',\n        description: 'Leila a guidé des touristes au Ribat',\n        points: 15,\n        type: 'culture',\n        timestamp: new Date(Date.now() - 1000 * 60 * 120), // 2 hours ago\n        userId: 'user4',\n        userName: 'Leila'\n      }\n    ];\n  }\n\n  private loadTopContributors(): void {\n    this.topContributors = [\n      { uid: 'top1', name: 'Ahmed Ben Ali', city: 'Monastir', points: 1250, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },\n      { uid: 'top2', name: 'Fatma Trabelsi', city: 'Sousse', points: 1180, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },\n      { uid: 'top3', name: 'Mohamed Gharbi', city: 'Monastir', points: 1050, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },\n      { uid: 'top4', name: 'Leila Mansouri', city: 'Sousse', points: 980, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },\n      { uid: 'top5', name: 'Karim Bouazizi', city: 'Monastir', points: 920, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] }\n    ];\n  }\n\n  private loadLocalEvents(): void {\n    this.localEvents = [\n      {\n        id: 'event1',\n        title: 'Nettoyage de la Plage de Monastir',\n        description: 'Rejoignez-nous pour nettoyer notre belle plage',\n        date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2), // In 2 days\n        location: 'Plage de Monastir',\n        category: 'Environnement',\n        points: 20,\n        image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=200&fit=crop',\n        participants: 45,\n        maxParticipants: 100\n      },\n      {\n        id: 'event2',\n        title: 'Festival Culturel de Sousse',\n        description: 'Célébrons notre patrimoine culturel ensemble',\n        date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5), // In 5 days\n        location: 'Médina de Sousse',\n        category: 'Culture',\n        points: 15,\n        image: 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=400&h=200&fit=crop',\n        participants: 120,\n        maxParticipants: 200\n      },\n      {\n        id: 'event3',\n        title: 'Cours de Français pour Réfugiés',\n        description: 'Aidez à enseigner le français aux nouveaux arrivants',\n        date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7), // In 1 week\n        location: 'Centre Communautaire',\n        category: 'Éducation',\n        points: 30,\n        image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=200&fit=crop',\n        participants: 15,\n        maxParticipants: 30\n      }\n    ];\n  }\n\n  private loadFeaturedPartners(): void {\n    this.featuredPartners = [\n      {\n        id: 'partner1',\n        name: 'Café des Nattes',\n        category: 'Restaurant',\n        logo: 'https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=100&h=100&fit=crop',\n        rating: 4.5,\n        reviews: 127,\n        currentOffer: '10% de réduction avec 50 points',\n        location: 'Médina de Monastir',\n        qrCode: 'PARTNER_CAFE_NATTES'\n      },\n      {\n        id: 'partner2',\n        name: 'Boutique Artisanat Sousse',\n        category: 'Artisanat',\n        logo: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop',\n        rating: 4.8,\n        reviews: 89,\n        currentOffer: 'Produit gratuit avec 100 points',\n        location: 'Médina de Sousse',\n        qrCode: 'PARTNER_ARTISANAT_SOUSSE'\n      },\n      {\n        id: 'partner3',\n        name: 'Hammam Traditionnel',\n        category: 'Bien-être',\n        logo: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=100&h=100&fit=crop',\n        rating: 4.3,\n        reviews: 156,\n        currentOffer: 'Séance gratuite avec 75 points',\n        location: 'Centre-ville Monastir',\n        qrCode: 'PARTNER_HAMMAM'\n      }\n    ];\n  }\n\n  private loadActiveChallenges(): void {\n    this.activeChallenges = [\n      {\n        id: 'challenge1',\n        title: 'Éco-Warrior',\n        description: 'Participez à 5 actions environnementales ce mois-ci',\n        icon: 'eco',\n        gradient: 'linear-gradient(135deg, #4CAF50, #8BC34A)',\n        progress: 60,\n        current: 3,\n        target: 5,\n        reward: 'Badge Éco-Warrior + 100 points bonus',\n        endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 15) // 15 days\n      },\n      {\n        id: 'challenge2',\n        title: 'Ambassadeur Culturel',\n        description: 'Guidez 10 touristes dans votre ville',\n        icon: 'tour',\n        gradient: 'linear-gradient(135deg, #FF9800, #FFC107)',\n        progress: 30,\n        current: 3,\n        target: 10,\n        reward: 'Badge Ambassadeur + Visite gratuite musée',\n        endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 20) // 20 days\n      },\n      {\n        id: 'challenge3',\n        title: 'Solidarité Communautaire',\n        description: 'Aidez 15 personnes dans le besoin',\n        icon: 'volunteer_activism',\n        gradient: 'linear-gradient(135deg, #E91E63, #F06292)',\n        progress: 80,\n        current: 12,\n        target: 15,\n        reward: 'Badge Solidarité + 200 points bonus',\n        endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 10) // 10 days\n      }\n    ];\n  }\n\n  private loadCityComparison(): void {\n    // Simulate city competition data\n    this.cityComparison = {\n      monastir: { score: 15420, participants: 342 },\n      sousse: { score: 16180, participants: 389 }\n    };\n\n    this.comparisonStats = [\n      {\n        name: 'Actions validées',\n        monastir: 65,\n        sousse: 78,\n        monastirValue: 1250,\n        sousseValue: 1520\n      },\n      {\n        name: 'Événements organisés',\n        monastir: 45,\n        sousse: 52,\n        monastirValue: 23,\n        sousseValue: 27\n      },\n      {\n        name: 'Partenaires actifs',\n        monastir: 80,\n        sousse: 70,\n        monastirValue: 16,\n        sousseValue: 14\n      }\n    ];\n  }\n\n  // UI Helper Methods\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n\n  getCityIcon(): string {\n    if (!this.user) return 'location_city';\n    return this.user.city === 'Monastir' ? 'account_balance' : 'museum';\n  }\n\n  getCommunityRank(): number {\n    if (!this.user) return 0;\n    const userIndex = this.topContributors.findIndex(u => u.uid === this.user!.uid);\n    return userIndex >= 0 ? userIndex + 1 : this.topContributors.length + 1;\n  }\n\n  getImpactScore(): number {\n    if (!this.user) return 0;\n    // Calculate impact based on points and community actions\n    return Math.floor(this.user.points * 1.2 + (this.user.history?.length || 0) * 5);\n  }\n\n  getCityProgress(): number {\n    if (!this.user) return 0;\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    const monthlyTarget = 20000;\n    return Math.min((cityData.score / monthlyTarget) * 314, 314); // 314 = 2π * 50 (circle circumference)\n  }\n\n  getCityProgressPercent(): number {\n    if (!this.user) return 0;\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    const monthlyTarget = 20000;\n    return Math.min(Math.round((cityData.score / monthlyTarget) * 100), 100);\n  }\n\n  getCityStats() {\n    if (!this.user) return { validatedActions: 0, activeUsers: 0 };\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    return {\n      validatedActions: Math.floor(cityData.score / 15), // Estimate based on average points per action\n      activeUsers: cityData.participants\n    };\n  }\n\n  getActivityColor(type: string): string {\n    const colorMap: { [key: string]: string } = {\n      'environment': '#4CAF50',\n      'social': '#E91E63',\n      'education': '#2196F3',\n      'culture': '#FF9800',\n      'health': '#9C27B0'\n    };\n    return colorMap[type] || '#667eea';\n  }\n\n  getActivityIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'environment': 'eco',\n      'social': 'volunteer_activism',\n      'education': 'school',\n      'culture': 'theater_comedy',\n      'health': 'health_and_safety'\n    };\n    return iconMap[type] || 'circle';\n  }\n\n  getTrophyColor(index: number): string {\n    const colors = ['#FFD700', '#C0C0C0', '#CD7F32']; // Gold, Silver, Bronze\n    return colors[index] || '#667eea';\n  }\n\n  getTrophyIcon(index: number): string {\n    return index < 3 ? 'emoji_events' : 'star';\n  }\n\n  getStars(rating: number): number[] {\n    return Array(Math.floor(rating)).fill(0);\n  }\n\n  // Action Methods\n  viewEvent(event: LocalEvent): void {\n    // Navigate to event details or open modal\n    console.log('Viewing event:', event);\n  }\n\n  viewPartner(partner: Partner): void {\n    // Navigate to partner details or open modal\n    console.log('Viewing partner:', partner);\n  }\n\n  executeAction(action: any): void {\n    // Execute the selected action\n    console.log('Executing action:', action);\n    // This would typically open a form or navigate to action details\n  }\n\n  viewAllActivity(): void {\n    this.router.navigate(['/community/activity']);\n  }\n}\n", "<div class=\"dashboard-container\" *ngIf=\"user\">\n  <!-- Hero Section with City Pride -->\n  <div class=\"hero-section\" [class]=\"'city-' + user.city.toLowerCase()\">\n    <div class=\"hero-content\">\n      <div class=\"city-badge\">\n        <mat-icon>location_city</mat-icon>\n        <span>{{ user.city }}</span>\n      </div>\n      <h1 class=\"hero-title\">{{ getGreeting() }}, {{ user.name }}!</h1>\n      <p class=\"hero-subtitle\">Ensemble, construisons une communauté plus forte</p>\n      <div class=\"hero-stats\">\n        <div class=\"hero-stat\">\n          <span class=\"stat-number\">{{ user.points }}</span>\n          <span class=\"stat-label\">Points</span>\n        </div>\n        <div class=\"hero-stat\">\n          <span class=\"stat-number\">{{ getCommunityRank() }}</span>\n          <span class=\"stat-label\">Rang</span>\n        </div>\n        <div class=\"hero-stat\">\n          <span class=\"stat-number\">{{ getImpactScore() }}</span>\n          <span class=\"stat-label\">Impact</span>\n        </div>\n      </div>\n    </div>\n    <div class=\"hero-visual\">\n      <div class=\"city-illustration\">\n        <mat-icon class=\"city-icon\">{{ getCityIcon() }}</mat-icon>\n      </div>\n    </div>\n  </div>\n\n  <!-- Community Impact Dashboard -->\n  <div class=\"impact-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>trending_up</mat-icon>\n      Impact Communautaire\n    </h2>\n    <div class=\"impact-grid\">\n      <mat-card class=\"impact-card city-progress\">\n        <mat-card-content>\n          <h3>Progrès de {{ user.city }}</h3>\n          <div class=\"progress-ring\">\n            <svg class=\"progress-svg\" width=\"120\" height=\"120\">\n              <circle cx=\"60\" cy=\"60\" r=\"50\" class=\"progress-bg\"></circle>\n              <circle cx=\"60\" cy=\"60\" r=\"50\" class=\"progress-fill\"\n                      [style.stroke-dasharray]=\"getCityProgress() + ' 314'\"></circle>\n            </svg>\n            <div class=\"progress-text\">\n              <span class=\"progress-value\">{{ getCityProgressPercent() }}%</span>\n              <span class=\"progress-label\">Objectif mensuel</span>\n            </div>\n          </div>\n          <div class=\"progress-details\">\n            <div class=\"detail-item\">\n              <span>Actions validées</span>\n              <span>{{ getCityStats().validatedActions }}</span>\n            </div>\n            <div class=\"detail-item\">\n              <span>Participants actifs</span>\n              <span>{{ getCityStats().activeUsers }}</span>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"impact-card community-feed\">\n        <mat-card-content>\n          <h3>Activité Récente</h3>\n          <div class=\"feed-list\">\n            <div *ngFor=\"let activity of communityFeed.slice(0, 4)\" class=\"feed-item\">\n              <div class=\"feed-avatar\">\n                <mat-icon [style.color]=\"getActivityColor(activity.type)\">\n                  {{ getActivityIcon(activity.type) }}\n                </mat-icon>\n              </div>\n              <div class=\"feed-content\">\n                <p class=\"feed-text\">{{ activity.description }}</p>\n                <span class=\"feed-time\">{{ activity.timestamp | timeAgo }}</span>\n              </div>\n              <div class=\"feed-points\">+{{ activity.points }}</div>\n            </div>\n          </div>\n          <button mat-stroked-button class=\"view-all-btn\" (click)=\"viewAllActivity()\">\n            Voir tout\n          </button>\n        </mat-card-content>\n      </mat-card>\n\n      <mat-card class=\"impact-card leaderboard\">\n        <mat-card-content>\n          <h3>Top Contributeurs</h3>\n          <div class=\"leaderboard-list\">\n            <div *ngFor=\"let leader of topContributors; let i = index\"\n                 class=\"leader-item\" [class.current-user]=\"leader.uid === user.uid\">\n              <div class=\"leader-rank\">\n                <mat-icon *ngIf=\"i < 3\" class=\"trophy-icon\"\n                          [style.color]=\"getTrophyColor(i)\">\n                  {{ getTrophyIcon(i) }}\n                </mat-icon>\n                <span *ngIf=\"i >= 3\" class=\"rank-number\">{{ i + 1 }}</span>\n              </div>\n              <div class=\"leader-info\">\n                <span class=\"leader-name\">{{ leader.name }}</span>\n                <span class=\"leader-city\">{{ leader.city }}</span>\n              </div>\n              <div class=\"leader-points\">{{ leader.points }}</div>\n            </div>\n          </div>\n          <button mat-stroked-button class=\"view-all-btn\" routerLink=\"/leaderboard\">\n            Classement complet\n          </button>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Local Events & Opportunities -->\n  <div class=\"events-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>event</mat-icon>\n      Événements à {{ user.city }}\n    </h2>\n    <div class=\"events-carousel\">\n      <div class=\"events-scroll\">\n        <mat-card *ngFor=\"let event of localEvents\" class=\"event-card\" (click)=\"viewEvent(event)\">\n          <div class=\"event-image\" [style.background-image]=\"'url(' + event.image + ')'\">\n            <div class=\"event-badge\" [class]=\"'badge-' + event.category\">\n              {{ event.category }}\n            </div>\n          </div>\n          <mat-card-content>\n            <h4>{{ event.title }}</h4>\n            <p class=\"event-description\">{{ event.description }}</p>\n            <div class=\"event-details\">\n              <div class=\"event-detail\">\n                <mat-icon>schedule</mat-icon>\n                <span>{{ event.date | date:'short' }}</span>\n              </div>\n              <div class=\"event-detail\">\n                <mat-icon>location_on</mat-icon>\n                <span>{{ event.location }}</span>\n              </div>\n              <div class=\"event-detail\">\n                <mat-icon>stars</mat-icon>\n                <span>{{ event.points }} points</span>\n              </div>\n            </div>\n            <button mat-raised-button color=\"primary\" class=\"event-join-btn\">\n              Participer\n            </button>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  </div>\n\n  <!-- Partner Spotlight -->\n  <div class=\"partners-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>store</mat-icon>\n      Partenaires Locaux\n    </h2>\n    <div class=\"partners-grid\">\n      <mat-card *ngFor=\"let partner of featuredPartners\" class=\"partner-card\" (click)=\"viewPartner(partner)\">\n        <div class=\"partner-header\">\n          <img [src]=\"partner.logo\" [alt]=\"partner.name\" class=\"partner-logo\">\n          <div class=\"partner-info\">\n            <h4>{{ partner.name }}</h4>\n            <p class=\"partner-category\">{{ partner.category }}</p>\n            <div class=\"partner-rating\">\n              <mat-icon *ngFor=\"let star of getStars(partner.rating)\" class=\"star-icon\">star</mat-icon>\n              <span>({{ partner.reviews }})</span>\n            </div>\n          </div>\n        </div>\n        <mat-card-content>\n          <p class=\"partner-offer\">{{ partner.currentOffer }}</p>\n          <div class=\"partner-actions\">\n            <button mat-stroked-button class=\"partner-btn\">\n              <mat-icon>qr_code</mat-icon>\n              Scanner QR\n            </button>\n            <button mat-stroked-button class=\"partner-btn\">\n              <mat-icon>directions</mat-icon>\n              Itinéraire\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Quick Actions Hub -->\n  <div class=\"actions-hub\">\n    <h2 class=\"section-title\">\n      <mat-icon>flash_on</mat-icon>\n      Actions Rapides\n    </h2>\n    <div class=\"actions-grid\">\n      <div class=\"action-category\" *ngFor=\"let category of actionCategories\">\n        <h3 class=\"category-title\">{{ category.title }}</h3>\n        <div class=\"category-actions\">\n          <button *ngFor=\"let action of category.actions\"\n                  mat-raised-button\n                  [color]=\"action.color\"\n                  class=\"action-btn\"\n                  (click)=\"executeAction(action)\">\n            <mat-icon>{{ action.icon }}</mat-icon>\n            <span>{{ action.title }}</span>\n            <div class=\"action-points\">+{{ action.points }}</div>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Community Challenges -->\n  <div class=\"challenges-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>emoji_events</mat-icon>\n      Défis Communautaires\n    </h2>\n    <div class=\"challenges-grid\">\n      <mat-card *ngFor=\"let challenge of activeChallenges\" class=\"challenge-card\">\n        <div class=\"challenge-header\" [style.background]=\"challenge.gradient\">\n          <mat-icon class=\"challenge-icon\">{{ challenge.icon }}</mat-icon>\n          <h4>{{ challenge.title }}</h4>\n        </div>\n        <mat-card-content>\n          <p class=\"challenge-description\">{{ challenge.description }}</p>\n          <div class=\"challenge-progress\">\n            <div class=\"progress-bar\">\n              <div class=\"progress-fill\" [style.width]=\"challenge.progress + '%'\"></div>\n            </div>\n            <span class=\"progress-text\">{{ challenge.current }}/{{ challenge.target }}</span>\n          </div>\n          <div class=\"challenge-reward\">\n            <mat-icon>card_giftcard</mat-icon>\n            <span>{{ challenge.reward }}</span>\n          </div>\n          <button mat-raised-button color=\"accent\" class=\"challenge-btn\">\n            Participer\n          </button>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- City Comparison -->\n  <div class=\"comparison-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>compare_arrows</mat-icon>\n      Monastir vs Sousse\n    </h2>\n    <mat-card class=\"comparison-card\">\n      <mat-card-content>\n        <div class=\"comparison-header\">\n          <div class=\"city-score monastir\">\n            <h3>Monastir</h3>\n            <div class=\"score\">{{ cityComparison.monastir.score }}</div>\n            <p>{{ cityComparison.monastir.participants }} participants</p>\n          </div>\n          <div class=\"vs-divider\">\n            <mat-icon>sports</mat-icon>\n            <span>VS</span>\n          </div>\n          <div class=\"city-score sousse\">\n            <h3>Sousse</h3>\n            <div class=\"score\">{{ cityComparison.sousse.score }}</div>\n            <p>{{ cityComparison.sousse.participants }} participants</p>\n          </div>\n        </div>\n        <div class=\"comparison-stats\">\n          <div class=\"stat-comparison\" *ngFor=\"let stat of comparisonStats\">\n            <span class=\"stat-name\">{{ stat.name }}</span>\n            <div class=\"stat-bars\">\n              <div class=\"stat-bar monastir\" [style.width]=\"stat.monastir + '%'\">\n                <span>{{ stat.monastirValue }}</span>\n              </div>\n              <div class=\"stat-bar sousse\" [style.width]=\"stat.sousse + '%'\">\n                <span>{{ stat.sousseValue }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AAIA,SAAeA,QAAQ,QAAQ,yBAAyB;;;;;;;;;;;;ICoExCC,EAFJ,CAAAC,cAAA,cAA0E,cAC/C,eACmC;IACxDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,cAA0B,YACH;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACnDH,EAAA,CAAAC,cAAA,eAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAkC;;IAC5DF,EAD4D,CAAAG,YAAA,EAAO,EAC7D;IACNH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IACjDF,EADiD,CAAAG,YAAA,EAAM,EACjD;;;;;IATQH,EAAA,CAAAI,SAAA,GAA+C;IAA/CJ,EAAA,CAAAK,WAAA,UAAAC,MAAA,CAAAC,gBAAA,CAAAC,WAAA,CAAAC,IAAA,EAA+C;IACvDT,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAK,eAAA,CAAAH,WAAA,CAAAC,IAAA,OACF;IAGqBT,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAY,iBAAA,CAAAJ,WAAA,CAAAK,WAAA,CAA0B;IACvBb,EAAA,CAAAI,SAAA,GAAkC;IAAlCJ,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAAc,WAAA,OAAAN,WAAA,CAAAO,SAAA,EAAkC;IAEnCf,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAU,kBAAA,MAAAF,WAAA,CAAAQ,MAAA,KAAsB;;;;;IAgB7ChB,EAAA,CAAAC,cAAA,mBAC4C;IAC1CD,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;IAFDH,EAAA,CAAAK,WAAA,UAAAC,MAAA,CAAAW,cAAA,CAAAC,IAAA,EAAiC;IACzClB,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAU,kBAAA,MAAAJ,MAAA,CAAAa,aAAA,CAAAD,IAAA,OACF;;;;;IACAlB,EAAA,CAAAC,cAAA,eAAyC;IAAAD,EAAA,CAAAE,MAAA,GAAW;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAlBH,EAAA,CAAAI,SAAA,EAAW;IAAXJ,EAAA,CAAAY,iBAAA,CAAAM,IAAA,KAAW;;;;;IALtDlB,EAFF,CAAAC,cAAA,cACwE,cAC7C;IAKvBD,EAJA,CAAAoB,UAAA,IAAAC,mDAAA,uBAC4C,IAAAC,+CAAA,mBAGH;IAC3CtB,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,cAAyB,eACG;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,eAA0B;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;IACNH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAChDF,EADgD,CAAAG,YAAA,EAAM,EAChD;;;;;;IAbmBH,EAAA,CAAAuB,WAAA,iBAAAC,SAAA,CAAAC,GAAA,KAAAnB,MAAA,CAAAoB,IAAA,CAAAD,GAAA,CAA8C;IAExDzB,EAAA,CAAAI,SAAA,GAAW;IAAXJ,EAAA,CAAA2B,UAAA,SAAAT,IAAA,KAAW;IAIflB,EAAA,CAAAI,SAAA,EAAY;IAAZJ,EAAA,CAAA2B,UAAA,SAAAT,IAAA,MAAY;IAGOlB,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAY,iBAAA,CAAAY,SAAA,CAAAI,IAAA,CAAiB;IACjB5B,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAY,iBAAA,CAAAY,SAAA,CAAAK,IAAA,CAAiB;IAElB7B,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAY,iBAAA,CAAAY,SAAA,CAAAR,MAAA,CAAmB;;;;;;IAmBpDhB,EAAA,CAAAC,cAAA,mBAA0F;IAA3BD,EAAA,CAAA8B,UAAA,mBAAAC,wEAAA;MAAA,MAAAC,QAAA,GAAAhC,EAAA,CAAAiC,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAoC,aAAA;MAAA,OAAApC,EAAA,CAAAqC,WAAA,CAAS/B,MAAA,CAAAgC,SAAA,CAAAN,QAAA,CAAgB;IAAA,EAAC;IAErFhC,EADF,CAAAC,cAAA,cAA+E,cAChB;IAC3DD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAM,EACF;IAEJH,EADF,CAAAC,cAAA,uBAAkB,SACZ;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,YAA6B;IAAAD,EAAA,CAAAE,MAAA,GAAuB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGpDH,EAFJ,CAAAC,cAAA,cAA2B,eACC,gBACd;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA+B;;IACvCF,EADuC,CAAAG,YAAA,EAAO,EACxC;IAEJH,EADF,CAAAC,cAAA,eAA0B,gBACd;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAoB;IAC5BF,EAD4B,CAAAG,YAAA,EAAO,EAC7B;IAEJH,EADF,CAAAC,cAAA,eAA0B,gBACd;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAyB;IAEnCF,EAFmC,CAAAG,YAAA,EAAO,EAClC,EACF;IACNH,EAAA,CAAAC,cAAA,kBAAiE;IAC/DD,EAAA,CAAAE,MAAA,oBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;;;;IA1BgBH,EAAA,CAAAI,SAAA,EAAqD;IAArDJ,EAAA,CAAAK,WAAA,8BAAA2B,QAAA,CAAAO,KAAA,OAAqD;IACnDvC,EAAA,CAAAI,SAAA,EAAmC;IAAnCJ,EAAA,CAAAwC,UAAA,YAAAR,QAAA,CAAAS,QAAA,CAAmC;IAC1DzC,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAU,kBAAA,MAAAsB,QAAA,CAAAS,QAAA,MACF;IAGIzC,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAY,iBAAA,CAAAoB,QAAA,CAAAU,KAAA,CAAiB;IACQ1C,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAY,iBAAA,CAAAoB,QAAA,CAAAnB,WAAA,CAAuB;IAI1Cb,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAY,iBAAA,CAAAZ,EAAA,CAAA2C,WAAA,SAAAX,QAAA,CAAAY,IAAA,WAA+B;IAI/B5C,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAY,iBAAA,CAAAoB,QAAA,CAAAa,QAAA,CAAoB;IAIpB7C,EAAA,CAAAI,SAAA,GAAyB;IAAzBJ,EAAA,CAAAU,kBAAA,KAAAsB,QAAA,CAAAhB,MAAA,YAAyB;;;;;IA0BjChB,EAAA,CAAAC,cAAA,mBAA0E;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;IAPjGH,EAAA,CAAAC,cAAA,mBAAuG;IAA/BD,EAAA,CAAA8B,UAAA,mBAAAgB,wEAAA;MAAA,MAAAC,UAAA,GAAA/C,EAAA,CAAAiC,aAAA,CAAAe,GAAA,EAAAb,SAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAoC,aAAA;MAAA,OAAApC,EAAA,CAAAqC,WAAA,CAAS/B,MAAA,CAAA2C,WAAA,CAAAF,UAAA,CAAoB;IAAA,EAAC;IACpG/C,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAkD,SAAA,cAAoE;IAElElD,EADF,CAAAC,cAAA,cAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,YAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtDH,EAAA,CAAAC,cAAA,cAA4B;IAC1BD,EAAA,CAAAoB,UAAA,IAAA+B,wDAAA,uBAA0E;IAC1EnD,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAGnCF,EAHmC,CAAAG,YAAA,EAAO,EAChC,EACF,EACF;IAEJH,EADF,CAAAC,cAAA,wBAAkB,aACS;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGnDH,EAFJ,CAAAC,cAAA,eAA6B,kBACoB,gBACnC;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEPH,EADF,CAAAC,cAAA,kBAA+C,gBACnC;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,yBACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACW,EACV;;;;;IAvBFH,EAAA,CAAAI,SAAA,GAAoB;IAACJ,EAArB,CAAA2B,UAAA,QAAAoB,UAAA,CAAAK,IAAA,EAAApD,EAAA,CAAAqD,aAAA,CAAoB,QAAAN,UAAA,CAAAnB,IAAA,CAAqB;IAExC5B,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAY,iBAAA,CAAAmC,UAAA,CAAAnB,IAAA,CAAkB;IACM5B,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAY,iBAAA,CAAAmC,UAAA,CAAAN,QAAA,CAAsB;IAErBzC,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAAgD,QAAA,CAAAP,UAAA,CAAAQ,MAAA,EAA2B;IAChDvD,EAAA,CAAAI,SAAA,GAAuB;IAAvBJ,EAAA,CAAAU,kBAAA,MAAAqC,UAAA,CAAAS,OAAA,MAAuB;IAKRxD,EAAA,CAAAI,SAAA,GAA0B;IAA1BJ,EAAA,CAAAY,iBAAA,CAAAmC,UAAA,CAAAU,YAAA,CAA0B;;;;;;IA0BnDzD,EAAA,CAAAC,cAAA,iBAIwC;IAAhCD,EAAA,CAAA8B,UAAA,mBAAA4B,0EAAA;MAAA,MAAAC,UAAA,GAAA3D,EAAA,CAAAiC,aAAA,CAAA2B,IAAA,EAAAzB,SAAA;MAAA,MAAA7B,MAAA,GAAAN,EAAA,CAAAoC,aAAA;MAAA,OAAApC,EAAA,CAAAqC,WAAA,CAAS/B,MAAA,CAAAuD,aAAA,CAAAF,UAAA,CAAqB;IAAA,EAAC;IACrC3D,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/BH,EAAA,CAAAC,cAAA,cAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IACjDF,EADiD,CAAAG,YAAA,EAAM,EAC9C;;;;IANDH,EAAA,CAAA2B,UAAA,UAAAgC,UAAA,CAAAG,KAAA,CAAsB;IAGlB9D,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAY,iBAAA,CAAA+C,UAAA,CAAAI,IAAA,CAAiB;IACrB/D,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAY,iBAAA,CAAA+C,UAAA,CAAAjB,KAAA,CAAkB;IACG1C,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAU,kBAAA,MAAAiD,UAAA,CAAA3C,MAAA,KAAoB;;;;;IATnDhB,EADF,CAAAC,cAAA,cAAuE,aAC1C;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,cAA8B;IAC5BD,EAAA,CAAAoB,UAAA,IAAA4C,iDAAA,qBAIwC;IAM5ChE,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAZuBH,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAY,iBAAA,CAAAqD,YAAA,CAAAvB,KAAA,CAAoB;IAElB1C,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAA2B,UAAA,YAAAsC,YAAA,CAAAC,OAAA,CAAmB;;;;;IAuB9ClE,EAFJ,CAAAC,cAAA,mBAA4E,cACJ,mBACnC;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChEH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAC3BF,EAD2B,CAAAG,YAAA,EAAK,EAC1B;IAEJH,EADF,CAAAC,cAAA,uBAAkB,aACiB;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE9DH,EADF,CAAAC,cAAA,eAAgC,gBACJ;IACxBD,EAAA,CAAAkD,SAAA,gBAA0E;IAC5ElD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAA8C;IAC5EF,EAD4E,CAAAG,YAAA,EAAO,EAC7E;IAEJH,EADF,CAAAC,cAAA,gBAA8B,gBAClB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,mBAA+D;IAC7DD,EAAA,CAAAE,MAAA,oBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;;;;IApBqBH,EAAA,CAAAI,SAAA,EAAuC;IAAvCJ,EAAA,CAAAK,WAAA,eAAA8D,aAAA,CAAAC,QAAA,CAAuC;IAClCpE,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAAY,iBAAA,CAAAuD,aAAA,CAAAJ,IAAA,CAAoB;IACjD/D,EAAA,CAAAI,SAAA,GAAqB;IAArBJ,EAAA,CAAAY,iBAAA,CAAAuD,aAAA,CAAAzB,KAAA,CAAqB;IAGQ1C,EAAA,CAAAI,SAAA,GAA2B;IAA3BJ,EAAA,CAAAY,iBAAA,CAAAuD,aAAA,CAAAtD,WAAA,CAA2B;IAG7Bb,EAAA,CAAAI,SAAA,GAAwC;IAAxCJ,EAAA,CAAAK,WAAA,UAAA8D,aAAA,CAAAE,QAAA,OAAwC;IAEzCrE,EAAA,CAAAI,SAAA,GAA8C;IAA9CJ,EAAA,CAAAsE,kBAAA,KAAAH,aAAA,CAAAI,OAAA,OAAAJ,aAAA,CAAAK,MAAA,KAA8C;IAIpExE,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAY,iBAAA,CAAAuD,aAAA,CAAAM,MAAA,CAAsB;;;;;IAoC5BzE,EADF,CAAAC,cAAA,eAAkE,gBACxC;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG1CH,EAFJ,CAAAC,cAAA,eAAuB,eAC8C,WAC3D;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAChCF,EADgC,CAAAG,YAAA,EAAO,EACjC;IAEJH,EADF,CAAAC,cAAA,eAA+D,WACvD;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAGlCF,EAHkC,CAAAG,YAAA,EAAO,EAC/B,EACF,EACF;;;;IAToBH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAY,iBAAA,CAAA8D,QAAA,CAAA9C,IAAA,CAAe;IAEN5B,EAAA,CAAAI,SAAA,GAAmC;IAAnCJ,EAAA,CAAAK,WAAA,UAAAqE,QAAA,CAAAC,QAAA,OAAmC;IAC1D3E,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAY,iBAAA,CAAA8D,QAAA,CAAAE,aAAA,CAAwB;IAEH5E,EAAA,CAAAI,SAAA,EAAiC;IAAjCJ,EAAA,CAAAK,WAAA,UAAAqE,QAAA,CAAAG,MAAA,OAAiC;IACtD7E,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAY,iBAAA,CAAA8D,QAAA,CAAAI,WAAA,CAAsB;;;;;;IApRpC9E,EALR,CAAAC,cAAA,aAA8C,aAE0B,aAC1C,aACA,eACZ;IAAAD,EAAA,CAAAE,MAAA,oBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAe;IACvBF,EADuB,CAAAG,YAAA,EAAO,EACxB;IACNH,EAAA,CAAAC,cAAA,YAAuB;IAAAD,EAAA,CAAAE,MAAA,GAAqC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjEH,EAAA,CAAAC,cAAA,YAAyB;IAAAD,EAAA,CAAAE,MAAA,6DAAgD;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGzEH,EAFJ,CAAAC,cAAA,cAAwB,cACC,eACK;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAClDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IACjCF,EADiC,CAAAG,YAAA,EAAO,EAClC;IAEJH,EADF,CAAAC,cAAA,cAAuB,eACK;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACzDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAC/BF,EAD+B,CAAAG,YAAA,EAAO,EAChC;IAEJH,EADF,CAAAC,cAAA,cAAuB,eACK;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvDH,EAAA,CAAAC,cAAA,gBAAyB;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAGrCF,EAHqC,CAAAG,YAAA,EAAO,EAClC,EACF,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAAyB,eACQ,oBACD;IAAAD,EAAA,CAAAE,MAAA,IAAmB;IAGrDF,EAHqD,CAAAG,YAAA,EAAW,EACtD,EACF,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAA4B,cACA,gBACd;IAAAD,EAAA,CAAAE,MAAA,mBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,8BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAICH,EAHN,CAAAC,cAAA,eAAyB,oBACqB,wBACxB,UACZ;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACnCH,EAAA,CAAAC,cAAA,eAA2B;;IACzBD,EAAA,CAAAC,cAAA,eAAmD;IAEjDD,EADA,CAAAkD,SAAA,kBAA4D,kBAEW;IACzElD,EAAA,CAAAG,YAAA,EAAM;;IAEJH,EADF,CAAAC,cAAA,eAA2B,gBACI;IAAAD,EAAA,CAAAE,MAAA,IAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,gBAA6B;IAAAD,EAAA,CAAAE,MAAA,wBAAgB;IAEjDF,EAFiD,CAAAG,YAAA,EAAO,EAChD,EACF;IAGFH,EAFJ,CAAAC,cAAA,eAA8B,eACH,YACjB;IAAAD,EAAA,CAAAE,MAAA,6BAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAqC;IAC7CF,EAD6C,CAAAG,YAAA,EAAO,EAC9C;IAEJH,EADF,CAAAC,cAAA,eAAyB,YACjB;IAAAD,EAAA,CAAAE,MAAA,2BAAmB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAChCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAgC;IAI9CF,EAJ8C,CAAAG,YAAA,EAAO,EACzC,EACF,EACW,EACV;IAIPH,EAFJ,CAAAC,cAAA,oBAA6C,wBACzB,UACZ;IAAAD,EAAA,CAAAE,MAAA,kCAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACzBH,EAAA,CAAAC,cAAA,eAAuB;IACrBD,EAAA,CAAAoB,UAAA,KAAA2D,wCAAA,mBAA0E;IAY5E/E,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAA4E;IAA5BD,EAAA,CAAA8B,UAAA,mBAAAkD,2DAAA;MAAAhF,EAAA,CAAAiC,aAAA,CAAAgD,GAAA;MAAA,MAAA3E,MAAA,GAAAN,EAAA,CAAAoC,aAAA;MAAA,OAAApC,EAAA,CAAAqC,WAAA,CAAS/B,MAAA,CAAA4E,eAAA,EAAiB;IAAA,EAAC;IACzElF,EAAA,CAAAE,MAAA,mBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;IAIPH,EAFJ,CAAAC,cAAA,oBAA0C,wBACtB,UACZ;IAAAD,EAAA,CAAAE,MAAA,yBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAoB,UAAA,KAAA+D,wCAAA,mBACwE;IAc1EnF,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,kBAA0E;IACxED,EAAA,CAAAE,MAAA,4BACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACQ,EACV,EACP,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAA4B,cACA,gBACd;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAEHH,EADF,CAAAC,cAAA,eAA6B,eACA;IACzBD,EAAA,CAAAoB,UAAA,KAAAgE,6CAAA,yBAA0F;IA8BhGpF,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAA8B,cACF,gBACd;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,4BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAA2B;IACzBD,EAAA,CAAAoB,UAAA,KAAAiE,6CAAA,wBAAuG;IA2B3GrF,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAAyB,cACG,gBACd;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,yBACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAoB,UAAA,KAAAkE,wCAAA,kBAAuE;IAe3EtF,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,gBAAgC,eACJ,iBACd;IAAAD,EAAA,CAAAE,MAAA,qBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAoB,UAAA,MAAAmE,8CAAA,yBAA4E;IAuBhFvF,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,gBAAgC,eACJ,iBACd;IAAAD,EAAA,CAAAE,MAAA,uBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnCH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAKGH,EAJR,CAAAC,cAAA,qBAAkC,yBACd,gBACe,gBACI,WAC3B;IAAAD,EAAA,CAAAE,MAAA,iBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,KAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5DH,EAAA,CAAAC,cAAA,UAAG;IAAAD,EAAA,CAAAE,MAAA,KAAuD;IAC5DF,EAD4D,CAAAG,YAAA,EAAI,EAC1D;IAEJH,EADF,CAAAC,cAAA,gBAAwB,iBACZ;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAE,MAAA,WAAE;IACVF,EADU,CAAAG,YAAA,EAAO,EACX;IAEJH,EADF,CAAAC,cAAA,gBAA+B,WACzB;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,KAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1DH,EAAA,CAAAC,cAAA,UAAG;IAAAD,EAAA,CAAAE,MAAA,KAAqD;IAE5DF,EAF4D,CAAAG,YAAA,EAAI,EACxD,EACF;IACNH,EAAA,CAAAC,cAAA,gBAA8B;IAC5BD,EAAA,CAAAoB,UAAA,MAAAoE,yCAAA,mBAAkE;IAe5ExF,EAJQ,CAAAG,YAAA,EAAM,EACW,EACV,EACP,EACF;;;;IA/RsBH,EAAA,CAAAI,SAAA,EAA2C;IAA3CJ,EAAA,CAAAwC,UAAA,WAAAlC,MAAA,CAAAoB,IAAA,CAAAG,IAAA,CAAA4D,WAAA,GAA2C;IAIzDzF,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAY,iBAAA,CAAAN,MAAA,CAAAoB,IAAA,CAAAG,IAAA,CAAe;IAEA7B,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAsE,kBAAA,KAAAhE,MAAA,CAAAoF,WAAA,UAAApF,MAAA,CAAAoB,IAAA,CAAAE,IAAA,MAAqC;IAI9B5B,EAAA,CAAAI,SAAA,GAAiB;IAAjBJ,EAAA,CAAAY,iBAAA,CAAAN,MAAA,CAAAoB,IAAA,CAAAV,MAAA,CAAiB;IAIjBhB,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAY,iBAAA,CAAAN,MAAA,CAAAqF,gBAAA,GAAwB;IAIxB3F,EAAA,CAAAI,SAAA,GAAsB;IAAtBJ,EAAA,CAAAY,iBAAA,CAAAN,MAAA,CAAAsF,cAAA,GAAsB;IAOtB5F,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAAY,iBAAA,CAAAN,MAAA,CAAAuF,WAAA,GAAmB;IAczC7F,EAAA,CAAAI,SAAA,IAA0B;IAA1BJ,EAAA,CAAAU,kBAAA,qBAAAJ,MAAA,CAAAoB,IAAA,CAAAG,IAAA,KAA0B;IAKlB7B,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAK,WAAA,qBAAAC,MAAA,CAAAwF,eAAA,YAAqD;IAGhC9F,EAAA,CAAAI,SAAA,GAA+B;IAA/BJ,EAAA,CAAAU,kBAAA,KAAAJ,MAAA,CAAAyF,sBAAA,QAA+B;IAOtD/F,EAAA,CAAAI,SAAA,GAAqC;IAArCJ,EAAA,CAAAY,iBAAA,CAAAN,MAAA,CAAA0F,YAAA,GAAAC,gBAAA,CAAqC;IAIrCjG,EAAA,CAAAI,SAAA,GAAgC;IAAhCJ,EAAA,CAAAY,iBAAA,CAAAN,MAAA,CAAA0F,YAAA,GAAAE,WAAA,CAAgC;IAUdlG,EAAA,CAAAI,SAAA,GAA4B;IAA5BJ,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAA6F,aAAA,CAAAC,KAAA,OAA4B;IAuB9BpG,EAAA,CAAAI,SAAA,GAAoB;IAApBJ,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAA+F,eAAA,CAAoB;IA4BlDrG,EAAA,CAAAI,SAAA,GACF;IADEJ,EAAA,CAAAU,kBAAA,kCAAAJ,MAAA,CAAAoB,IAAA,CAAAG,IAAA,MACF;IAGgC7B,EAAA,CAAAI,SAAA,GAAc;IAAdJ,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAAgG,WAAA,CAAc;IAuCdtG,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAAiG,gBAAA,CAAmB;IAoCCvG,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAAkG,gBAAA,CAAmB;IAwBrCxG,EAAA,CAAAI,SAAA,GAAmB;IAAnBJ,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAAmG,gBAAA,CAAmB;IAoC1BzG,EAAA,CAAAI,SAAA,IAAmC;IAAnCJ,EAAA,CAAAY,iBAAA,CAAAN,MAAA,CAAAoG,cAAA,CAAA/B,QAAA,CAAAgC,KAAA,CAAmC;IACnD3G,EAAA,CAAAI,SAAA,GAAuD;IAAvDJ,EAAA,CAAAU,kBAAA,KAAAJ,MAAA,CAAAoG,cAAA,CAAA/B,QAAA,CAAAiC,YAAA,kBAAuD;IAQvC5G,EAAA,CAAAI,SAAA,IAAiC;IAAjCJ,EAAA,CAAAY,iBAAA,CAAAN,MAAA,CAAAoG,cAAA,CAAA7B,MAAA,CAAA8B,KAAA,CAAiC;IACjD3G,EAAA,CAAAI,SAAA,GAAqD;IAArDJ,EAAA,CAAAU,kBAAA,KAAAJ,MAAA,CAAAoG,cAAA,CAAA7B,MAAA,CAAA+B,YAAA,kBAAqD;IAIZ5G,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAA2B,UAAA,YAAArB,MAAA,CAAAuG,eAAA,CAAkB;;;ADvN1E,OAAM,MAAOC,kBAAkB;EAiD7BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,eAAuC;IAFvC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IAnDzB,KAAAxF,IAAI,GAAgB,IAAI;IAExB;IACA,KAAAyE,aAAa,GAAwB,EAAE;IACvC,KAAAE,eAAe,GAAW,EAAE;IAC5B,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAC,gBAAgB,GAAc,EAAE;IAChC,KAAAE,gBAAgB,GAAgB,EAAE;IAElC;IACA,KAAAC,cAAc,GAAG;MACf/B,QAAQ,EAAE;QAAEgC,KAAK,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAC,CAAE;MACvC/B,MAAM,EAAE;QAAE8B,KAAK,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAC;KACpC;IAED,KAAAC,eAAe,GAAG,CAChB;MAAEjF,IAAI,EAAE,kBAAkB;MAAE+C,QAAQ,EAAE,CAAC;MAAEE,MAAM,EAAE,CAAC;MAAED,aAAa,EAAE,CAAC;MAAEE,WAAW,EAAE;IAAC,CAAE,EACtF;MAAElD,IAAI,EAAE,sBAAsB;MAAE+C,QAAQ,EAAE,CAAC;MAAEE,MAAM,EAAE,CAAC;MAAED,aAAa,EAAE,CAAC;MAAEE,WAAW,EAAE;IAAC,CAAE,EAC1F;MAAElD,IAAI,EAAE,oBAAoB;MAAE+C,QAAQ,EAAE,CAAC;MAAEE,MAAM,EAAE,CAAC;MAAED,aAAa,EAAE,CAAC;MAAEE,WAAW,EAAE;IAAC,CAAE,CACzF;IAED,KAAA0B,gBAAgB,GAAG,CACjB;MACE9D,KAAK,EAAE,eAAe;MACtBwB,OAAO,EAAE,CACP;QAAExB,KAAK,EAAE,iBAAiB;QAAEqB,IAAI,EAAE,OAAO;QAAED,KAAK,EAAE,SAAS;QAAE9C,MAAM,EAAE;MAAE,CAAE,EACzE;QAAE0B,KAAK,EAAE,mBAAmB;QAAEqB,IAAI,EAAE,MAAM;QAAED,KAAK,EAAE,SAAS;QAAE9C,MAAM,EAAE;MAAE,CAAE,EAC1E;QAAE0B,KAAK,EAAE,WAAW;QAAEqB,IAAI,EAAE,WAAW;QAAED,KAAK,EAAE,SAAS;QAAE9C,MAAM,EAAE;MAAE,CAAE;KAE1E,EACD;MACE0B,KAAK,EAAE,QAAQ;MACfwB,OAAO,EAAE,CACP;QAAExB,KAAK,EAAE,sBAAsB;QAAEqB,IAAI,EAAE,SAAS;QAAED,KAAK,EAAE,QAAQ;QAAE9C,MAAM,EAAE;MAAE,CAAE,EAC/E;QAAE0B,KAAK,EAAE,iBAAiB;QAAEqB,IAAI,EAAE,QAAQ;QAAED,KAAK,EAAE,QAAQ;QAAE9C,MAAM,EAAE;MAAE,CAAE,EACzE;QAAE0B,KAAK,EAAE,oBAAoB;QAAEqB,IAAI,EAAE,YAAY;QAAED,KAAK,EAAE,QAAQ;QAAE9C,MAAM,EAAE;MAAE,CAAE;KAEnF,EACD;MACE0B,KAAK,EAAE,SAAS;MAChBwB,OAAO,EAAE,CACP;QAAExB,KAAK,EAAE,mBAAmB;QAAEqB,IAAI,EAAE,MAAM;QAAED,KAAK,EAAE,MAAM;QAAE9C,MAAM,EAAE;MAAE,CAAE,EACvE;QAAE0B,KAAK,EAAE,mBAAmB;QAAEqB,IAAI,EAAE,YAAY;QAAED,KAAK,EAAE,MAAM;QAAE9C,MAAM,EAAE;MAAE,CAAE,EAC7E;QAAE0B,KAAK,EAAE,oBAAoB;QAAEqB,IAAI,EAAE,gBAAgB;QAAED,KAAK,EAAE,MAAM;QAAE9C,MAAM,EAAE;MAAE,CAAE;KAErF,CACF;EAME;EAEHmG,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,CAACI,YAAY,CAACC,SAAS,CAAC3F,IAAI,IAAG;MAC7C,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAIA,IAAI,EAAE;QACR;QACA,IAAIA,IAAI,CAAC4F,IAAI,KAAKvH,QAAQ,CAACwH,IAAI,EAAE;UAC/B,IAAI,CAACL,eAAe,CAACM,uBAAuB,CAAC9F,IAAI,CAAC;UAClD;;QAEF,IAAI,CAAC+F,iBAAiB,EAAE;;IAE5B,CAAC,CAAC;EACJ;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQL,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAACvB,aAAa,GAAG,CACnB;MACE6B,EAAE,EAAE,GAAG;MACPnH,WAAW,EAAE,sCAAsC;MACnDG,MAAM,EAAE,EAAE;MACVP,IAAI,EAAE,aAAa;MACnBM,SAAS,EAAE,IAAIkH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE;KACX,EACD;MACEJ,EAAE,EAAE,GAAG;MACPnH,WAAW,EAAE,2CAA2C;MACxDG,MAAM,EAAE,EAAE;MACVP,IAAI,EAAE,QAAQ;MACdM,SAAS,EAAE,IAAIkH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE;KACX,EACD;MACEJ,EAAE,EAAE,GAAG;MACPnH,WAAW,EAAE,yCAAyC;MACtDG,MAAM,EAAE,EAAE;MACVP,IAAI,EAAE,WAAW;MACjBM,SAAS,EAAE,IAAIkH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE;KACX,EACD;MACEJ,EAAE,EAAE,GAAG;MACPnH,WAAW,EAAE,sCAAsC;MACnDG,MAAM,EAAE,EAAE;MACVP,IAAI,EAAE,SAAS;MACfM,SAAS,EAAE,IAAIkH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC;MACjDC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE;KACX,CACF;EACH;EAEQT,mBAAmBA,CAAA;IACzB,IAAI,CAACtB,eAAe,GAAG,CACrB;MAAE5E,GAAG,EAAE,MAAM;MAAEG,IAAI,EAAE,eAAe;MAAEC,IAAI,EAAE,UAAU;MAAEb,MAAM,EAAE,IAAI;MAAEqH,KAAK,EAAE,EAAE;MAAEf,IAAI,EAAEvH,QAAQ,CAACwH,IAAI;MAAEe,SAAS,EAAE,IAAIL,IAAI,EAAE;MAAEM,SAAS,EAAE,IAAIN,IAAI,EAAE;MAAEO,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,EACjL;MAAEhH,GAAG,EAAE,MAAM;MAAEG,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE,QAAQ;MAAEb,MAAM,EAAE,IAAI;MAAEqH,KAAK,EAAE,EAAE;MAAEf,IAAI,EAAEvH,QAAQ,CAACwH,IAAI;MAAEe,SAAS,EAAE,IAAIL,IAAI,EAAE;MAAEM,SAAS,EAAE,IAAIN,IAAI,EAAE;MAAEO,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,EAChL;MAAEhH,GAAG,EAAE,MAAM;MAAEG,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE,UAAU;MAAEb,MAAM,EAAE,IAAI;MAAEqH,KAAK,EAAE,EAAE;MAAEf,IAAI,EAAEvH,QAAQ,CAACwH,IAAI;MAAEe,SAAS,EAAE,IAAIL,IAAI,EAAE;MAAEM,SAAS,EAAE,IAAIN,IAAI,EAAE;MAAEO,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,EAClL;MAAEhH,GAAG,EAAE,MAAM;MAAEG,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE,QAAQ;MAAEb,MAAM,EAAE,GAAG;MAAEqH,KAAK,EAAE,EAAE;MAAEf,IAAI,EAAEvH,QAAQ,CAACwH,IAAI;MAAEe,SAAS,EAAE,IAAIL,IAAI,EAAE;MAAEM,SAAS,EAAE,IAAIN,IAAI,EAAE;MAAEO,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,EAC/K;MAAEhH,GAAG,EAAE,MAAM;MAAEG,IAAI,EAAE,gBAAgB;MAAEC,IAAI,EAAE,UAAU;MAAEb,MAAM,EAAE,GAAG;MAAEqH,KAAK,EAAE,EAAE;MAAEf,IAAI,EAAEvH,QAAQ,CAACwH,IAAI;MAAEe,SAAS,EAAE,IAAIL,IAAI,EAAE;MAAEM,SAAS,EAAE,IAAIN,IAAI,EAAE;MAAEO,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,CAClL;EACH;EAEQb,eAAeA,CAAA;IACrB,IAAI,CAACtB,WAAW,GAAG,CACjB;MACE0B,EAAE,EAAE,QAAQ;MACZtF,KAAK,EAAE,mCAAmC;MAC1C7B,WAAW,EAAE,gDAAgD;MAC7D+B,IAAI,EAAE,IAAIqF,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACpDrF,QAAQ,EAAE,mBAAmB;MAC7BJ,QAAQ,EAAE,eAAe;MACzBzB,MAAM,EAAE,EAAE;MACVuB,KAAK,EAAE,gFAAgF;MACvFqE,YAAY,EAAE,EAAE;MAChB8B,eAAe,EAAE;KAClB,EACD;MACEV,EAAE,EAAE,QAAQ;MACZtF,KAAK,EAAE,6BAA6B;MACpC7B,WAAW,EAAE,8CAA8C;MAC3D+B,IAAI,EAAE,IAAIqF,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACpDrF,QAAQ,EAAE,kBAAkB;MAC5BJ,QAAQ,EAAE,SAAS;MACnBzB,MAAM,EAAE,EAAE;MACVuB,KAAK,EAAE,mFAAmF;MAC1FqE,YAAY,EAAE,GAAG;MACjB8B,eAAe,EAAE;KAClB,EACD;MACEV,EAAE,EAAE,QAAQ;MACZtF,KAAK,EAAE,iCAAiC;MACxC7B,WAAW,EAAE,sDAAsD;MACnE+B,IAAI,EAAE,IAAIqF,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACpDrF,QAAQ,EAAE,sBAAsB;MAChCJ,QAAQ,EAAE,WAAW;MACrBzB,MAAM,EAAE,EAAE;MACVuB,KAAK,EAAE,mFAAmF;MAC1FqE,YAAY,EAAE,EAAE;MAChB8B,eAAe,EAAE;KAClB,CACF;EACH;EAEQb,oBAAoBA,CAAA;IAC1B,IAAI,CAACtB,gBAAgB,GAAG,CACtB;MACEyB,EAAE,EAAE,UAAU;MACdpG,IAAI,EAAE,iBAAiB;MACvBa,QAAQ,EAAE,YAAY;MACtBW,IAAI,EAAE,gFAAgF;MACtFG,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,GAAG;MACZC,YAAY,EAAE,iCAAiC;MAC/CZ,QAAQ,EAAE,oBAAoB;MAC9B8F,MAAM,EAAE;KACT,EACD;MACEX,EAAE,EAAE,UAAU;MACdpG,IAAI,EAAE,2BAA2B;MACjCa,QAAQ,EAAE,WAAW;MACrBW,IAAI,EAAE,mFAAmF;MACzFG,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,iCAAiC;MAC/CZ,QAAQ,EAAE,kBAAkB;MAC5B8F,MAAM,EAAE;KACT,EACD;MACEX,EAAE,EAAE,UAAU;MACdpG,IAAI,EAAE,qBAAqB;MAC3Ba,QAAQ,EAAE,WAAW;MACrBW,IAAI,EAAE,mFAAmF;MACzFG,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,GAAG;MACZC,YAAY,EAAE,gCAAgC;MAC9CZ,QAAQ,EAAE,uBAAuB;MACjC8F,MAAM,EAAE;KACT,CACF;EACH;EAEQb,oBAAoBA,CAAA;IAC1B,IAAI,CAACrB,gBAAgB,GAAG,CACtB;MACEuB,EAAE,EAAE,YAAY;MAChBtF,KAAK,EAAE,aAAa;MACpB7B,WAAW,EAAE,qDAAqD;MAClEkD,IAAI,EAAE,KAAK;MACXK,QAAQ,EAAE,2CAA2C;MACrDC,QAAQ,EAAE,EAAE;MACZE,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,sCAAsC;MAC9CmE,OAAO,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;KAC1D,EACD;MACEF,EAAE,EAAE,YAAY;MAChBtF,KAAK,EAAE,sBAAsB;MAC7B7B,WAAW,EAAE,sCAAsC;MACnDkD,IAAI,EAAE,MAAM;MACZK,QAAQ,EAAE,2CAA2C;MACrDC,QAAQ,EAAE,EAAE;MACZE,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,2CAA2C;MACnDmE,OAAO,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;KAC1D,EACD;MACEF,EAAE,EAAE,YAAY;MAChBtF,KAAK,EAAE,0BAA0B;MACjC7B,WAAW,EAAE,mCAAmC;MAChDkD,IAAI,EAAE,oBAAoB;MAC1BK,QAAQ,EAAE,2CAA2C;MACrDC,QAAQ,EAAE,EAAE;MACZE,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,qCAAqC;MAC7CmE,OAAO,EAAE,IAAIX,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;KAC1D,CACF;EACH;EAEQH,kBAAkBA,CAAA;IACxB;IACA,IAAI,CAACrB,cAAc,GAAG;MACpB/B,QAAQ,EAAE;QAAEgC,KAAK,EAAE,KAAK;QAAEC,YAAY,EAAE;MAAG,CAAE;MAC7C/B,MAAM,EAAE;QAAE8B,KAAK,EAAE,KAAK;QAAEC,YAAY,EAAE;MAAG;KAC1C;IAED,IAAI,CAACC,eAAe,GAAG,CACrB;MACEjF,IAAI,EAAE,kBAAkB;MACxB+C,QAAQ,EAAE,EAAE;MACZE,MAAM,EAAE,EAAE;MACVD,aAAa,EAAE,IAAI;MACnBE,WAAW,EAAE;KACd,EACD;MACElD,IAAI,EAAE,sBAAsB;MAC5B+C,QAAQ,EAAE,EAAE;MACZE,MAAM,EAAE,EAAE;MACVD,aAAa,EAAE,EAAE;MACjBE,WAAW,EAAE;KACd,EACD;MACElD,IAAI,EAAE,oBAAoB;MAC1B+C,QAAQ,EAAE,EAAE;MACZE,MAAM,EAAE,EAAE;MACVD,aAAa,EAAE,EAAE;MACjBE,WAAW,EAAE;KACd,CACF;EACH;EAEA;EACAY,WAAWA,CAAA;IACT,MAAMmD,IAAI,GAAG,IAAIZ,IAAI,EAAE,CAACa,QAAQ,EAAE;IAClC,IAAID,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,SAAS;EAClB;EAEAhD,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACnE,IAAI,EAAE,OAAO,eAAe;IACtC,OAAO,IAAI,CAACA,IAAI,CAACG,IAAI,KAAK,UAAU,GAAG,iBAAiB,GAAG,QAAQ;EACrE;EAEA8D,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACjE,IAAI,EAAE,OAAO,CAAC;IACxB,MAAMqH,SAAS,GAAG,IAAI,CAAC1C,eAAe,CAAC2C,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACxH,GAAG,KAAK,IAAI,CAACC,IAAK,CAACD,GAAG,CAAC;IAC/E,OAAOsH,SAAS,IAAI,CAAC,GAAGA,SAAS,GAAG,CAAC,GAAG,IAAI,CAAC1C,eAAe,CAAC6C,MAAM,GAAG,CAAC;EACzE;EAEAtD,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAClE,IAAI,EAAE,OAAO,CAAC;IACxB;IACA,OAAOyH,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC1H,IAAI,CAACV,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAACU,IAAI,CAAC+G,OAAO,EAAES,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;EAClF;EAEApD,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAACpE,IAAI,EAAE,OAAO,CAAC;IACxB,MAAM2H,QAAQ,GAAG,IAAI,CAAC3H,IAAI,CAACG,IAAI,KAAK,UAAU,GAAG,IAAI,CAAC6E,cAAc,CAAC/B,QAAQ,GAAG,IAAI,CAAC+B,cAAc,CAAC7B,MAAM;IAC1G,MAAMyE,aAAa,GAAG,KAAK;IAC3B,OAAOH,IAAI,CAACI,GAAG,CAAEF,QAAQ,CAAC1C,KAAK,GAAG2C,aAAa,GAAI,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;EAChE;EAEAvD,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACrE,IAAI,EAAE,OAAO,CAAC;IACxB,MAAM2H,QAAQ,GAAG,IAAI,CAAC3H,IAAI,CAACG,IAAI,KAAK,UAAU,GAAG,IAAI,CAAC6E,cAAc,CAAC/B,QAAQ,GAAG,IAAI,CAAC+B,cAAc,CAAC7B,MAAM;IAC1G,MAAMyE,aAAa,GAAG,KAAK;IAC3B,OAAOH,IAAI,CAACI,GAAG,CAACJ,IAAI,CAACK,KAAK,CAAEH,QAAQ,CAAC1C,KAAK,GAAG2C,aAAa,GAAI,GAAG,CAAC,EAAE,GAAG,CAAC;EAC1E;EAEAtD,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACtE,IAAI,EAAE,OAAO;MAAEuE,gBAAgB,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAC,CAAE;IAC9D,MAAMmD,QAAQ,GAAG,IAAI,CAAC3H,IAAI,CAACG,IAAI,KAAK,UAAU,GAAG,IAAI,CAAC6E,cAAc,CAAC/B,QAAQ,GAAG,IAAI,CAAC+B,cAAc,CAAC7B,MAAM;IAC1G,OAAO;MACLoB,gBAAgB,EAAEkD,IAAI,CAACC,KAAK,CAACC,QAAQ,CAAC1C,KAAK,GAAG,EAAE,CAAC;MACjDT,WAAW,EAAEmD,QAAQ,CAACzC;KACvB;EACH;EAEArG,gBAAgBA,CAACE,IAAY;IAC3B,MAAMgJ,QAAQ,GAA8B;MAC1C,aAAa,EAAE,SAAS;MACxB,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,SAAS;MACtB,SAAS,EAAE,SAAS;MACpB,QAAQ,EAAE;KACX;IACD,OAAOA,QAAQ,CAAChJ,IAAI,CAAC,IAAI,SAAS;EACpC;EAEAE,eAAeA,CAACF,IAAY;IAC1B,MAAMiJ,OAAO,GAA8B;MACzC,aAAa,EAAE,KAAK;MACpB,QAAQ,EAAE,oBAAoB;MAC9B,WAAW,EAAE,QAAQ;MACrB,SAAS,EAAE,gBAAgB;MAC3B,QAAQ,EAAE;KACX;IACD,OAAOA,OAAO,CAACjJ,IAAI,CAAC,IAAI,QAAQ;EAClC;EAEAQ,cAAcA,CAAC0I,KAAa;IAC1B,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;IAClD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC;EAEAxI,aAAaA,CAACwI,KAAa;IACzB,OAAOA,KAAK,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM;EAC5C;EAEArG,QAAQA,CAACC,MAAc;IACrB,OAAOsG,KAAK,CAACV,IAAI,CAACC,KAAK,CAAC7F,MAAM,CAAC,CAAC,CAACuG,IAAI,CAAC,CAAC,CAAC;EAC1C;EAEA;EACAxH,SAASA,CAACyH,KAAiB;IACzB;IACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEF,KAAK,CAAC;EACtC;EAEA9G,WAAWA,CAACiH,OAAgB;IAC1B;IACAF,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,OAAO,CAAC;EAC1C;EAEArG,aAAaA,CAACsG,MAAW;IACvB;IACAH,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEE,MAAM,CAAC;IACxC;EACF;EAEAjF,eAAeA,CAAA;IACb,IAAI,CAAC+B,MAAM,CAACmD,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC;EAC/C;;;uBAhYWtD,kBAAkB,EAAA9G,EAAA,CAAAqK,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvK,EAAA,CAAAqK,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAzK,EAAA,CAAAqK,iBAAA,CAAAK,EAAA,CAAAC,sBAAA;IAAA;EAAA;;;YAAlB7D,kBAAkB;MAAA8D,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC3D/BlL,EAAA,CAAAoB,UAAA,IAAAgK,iCAAA,oBAA8C;;;UAAZpL,EAAA,CAAA2B,UAAA,SAAAwJ,GAAA,CAAAzJ,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}