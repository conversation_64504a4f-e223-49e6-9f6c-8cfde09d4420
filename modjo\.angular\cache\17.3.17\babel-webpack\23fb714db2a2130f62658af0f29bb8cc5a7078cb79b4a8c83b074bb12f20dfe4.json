{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\nimport { AdminDashboardComponent } from './components/admin-dashboard/admin-dashboard.component';\nimport { UserManagementComponent } from './components/user-management/user-management.component';\nimport { SystemStatsComponent } from './components/system-stats/system-stats.component';\nimport { PartnerManagementComponent } from './components/partner-management/partner-management.component';\nimport { ProviderManagementComponent } from './components/provider-management/provider-management.component';\nimport { ValidatorManagementComponent } from './components/validator-management/validator-management.component';\nimport { SystemConfigComponent } from './components/system-config/system-config.component';\nimport { AuditLogComponent } from './components/audit-log/audit-log.component';\nimport { adminDashboardRoutes } from './admin-dashboard.routes';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class AdminDashboardModule {\n  static {\n    this.ɵfac = function AdminDashboardModule_Factory(t) {\n      return new (t || AdminDashboardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: AdminDashboardModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, RouterModule.forChild(adminDashboardRoutes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(AdminDashboardModule, {\n    declarations: [AdminDashboardComponent, UserManagementComponent, SystemStatsComponent, PartnerManagementComponent, ProviderManagementComponent, ValidatorManagementComponent, SystemConfigComponent, AuditLogComponent],\n    imports: [SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SharedModule", "AdminDashboardComponent", "UserManagementComponent", "SystemStatsComponent", "PartnerManagementComponent", "ProviderManagementComponent", "ValidatorManagementComponent", "SystemConfigComponent", "AuditLogComponent", "adminDashboardRoutes", "AdminDashboardModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\admin-dashboard\\admin-dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\nimport { SharedModule } from '../../shared/shared.module';\nimport { AdminDashboardComponent } from './components/admin-dashboard/admin-dashboard.component';\nimport { UserManagementComponent } from './components/user-management/user-management.component';\nimport { SystemStatsComponent } from './components/system-stats/system-stats.component';\nimport { PartnerManagementComponent } from './components/partner-management/partner-management.component';\nimport { ProviderManagementComponent } from './components/provider-management/provider-management.component';\nimport { ValidatorManagementComponent } from './components/validator-management/validator-management.component';\nimport { SystemConfigComponent } from './components/system-config/system-config.component';\nimport { AuditLogComponent } from './components/audit-log/audit-log.component';\nimport { adminDashboardRoutes } from './admin-dashboard.routes';\n\n@NgModule({\n  declarations: [\n    AdminDashboardComponent,\n    UserManagementComponent,\n    SystemStatsComponent,\n    PartnerManagementComponent,\n    ProviderManagementComponent,\n    ValidatorManagementComponent,\n    SystemConfigComponent,\n    AuditLogComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(adminDashboardRoutes)\n  ]\n})\nexport class AdminDashboardModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,0BAA0B,QAAQ,8DAA8D;AACzG,SAASC,2BAA2B,QAAQ,gEAAgE;AAC5G,SAASC,4BAA4B,QAAQ,kEAAkE;AAC/G,SAASC,qBAAqB,QAAQ,oDAAoD;AAC1F,SAASC,iBAAiB,QAAQ,4CAA4C;AAC9E,SAASC,oBAAoB,QAAQ,0BAA0B;;;AAkB/D,OAAM,MAAOC,oBAAoB;;;uBAApBA,oBAAoB;IAAA;EAAA;;;YAApBA;IAAoB;EAAA;;;gBAJ7BV,YAAY,EACZD,YAAY,CAACY,QAAQ,CAACF,oBAAoB,CAAC;IAAA;EAAA;;;2EAGlCC,oBAAoB;IAAAE,YAAA,GAd7BX,uBAAuB,EACvBC,uBAAuB,EACvBC,oBAAoB,EACpBC,0BAA0B,EAC1BC,2BAA2B,EAC3BC,4BAA4B,EAC5BC,qBAAqB,EACrBC,iBAAiB;IAAAK,OAAA,GAGjBb,YAAY,EAAAc,EAAA,CAAAf,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}