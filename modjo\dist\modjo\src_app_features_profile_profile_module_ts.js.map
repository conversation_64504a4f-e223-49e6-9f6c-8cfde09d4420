{"version": 3, "file": "src_app_features_profile_profile_module_ts.js", "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;IC6DkBA,4DAHN,mBAA8C,uBAC1B,cACU,mBAC4B;IAAAA,oDAAA,kBAAW;IAAAA,0DAAA,EAAW;IAExEA,4DADF,cAAuB,SACjB;IAAAA,oDAAA,GAAgC;IAAAA,0DAAA,EAAK;IACzCA,4DAAA,QAAG;IAAAA,oDAAA,yBAAa;IAIxBA,0DAJwB,EAAI,EAChB,EACF,EACW,EACV;;;;IALCA,uDAAA,GAAgC;IAAhCA,+DAAA,CAAAM,MAAA,CAAAC,SAAA,CAAAC,WAAA,MAAgC;;;;;IAUtCR,4DAHN,mBAA8C,uBAC1B,cACU,mBAC4B;IAAAA,oDAAA,oBAAa;IAAAA,0DAAA,EAAW;IAE1EA,4DADF,cAAuB,SACjB;IAAAA,oDAAA,GAA+B;IAAAA,0DAAA,EAAK;IACxCA,4DAAA,QAAG;IAAAA,oDAAA,gCAAe;IAI1BA,0DAJ0B,EAAI,EAClB,EACF,EACW,EACV;;;;IALCA,uDAAA,GAA+B;IAA/BA,+DAAA,CAAAM,MAAA,CAAAC,SAAA,CAAAE,UAAA,MAA+B;;;;;IAUrCT,4DAHN,mBAA8C,uBAC1B,cACU,mBAC4B;IAAAA,oDAAA,eAAQ;IAAAA,0DAAA,EAAW;IAErEA,4DADF,cAAuB,SACjB;IAAAA,oDAAA,GAAqC;IAAAA,0DAAA,EAAK;IAC9CA,4DAAA,QAAG;IAAAA,oDAAA,mBAAY;IAIvBA,0DAJuB,EAAI,EACf,EACF,EACW,EACV;;;;IALCA,uDAAA,GAAqC;IAArCA,+DAAA,CAAAM,MAAA,CAAAC,SAAA,CAAAG,gBAAA,MAAqC;;;;;IA0B3CV,4DADF,cAA4C,eAChC;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAW;IAC1BA,4DAAA,eAA2B;IAAAA,oDAAA,2BAAU;IAAAA,0DAAA,EAAO;IAC5CA,4DAAA,eAA2B;IAAAA,oDAAA,GAAgB;IAC7CA,0DAD6C,EAAO,EAC9C;;;;IADuBA,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAM,MAAA,CAAAK,IAAA,CAAAC,KAAA,CAAgB;;;;;IAoC3CZ,4DAFJ,cAA0E,cAC7C,eAC0B;IACjDA,oDAAA,GACF;IACFA,0DADE,EAAW,EACP;IAEJA,4DADF,cAA2B,YACO;IAAAA,oDAAA,GAA6B;IAAAA,0DAAA,EAAI;IACjEA,4DAAA,YAAyB;IAAAA,oDAAA,GAAuC;IAClEA,0DADkE,EAAI,EAChE;IACNA,4DAAA,cAAwF;IACtFA,oDAAA,IACF;IACFA,0DADE,EAAM,EACF;;;;;IAXQA,uDAAA,GAAwC;IAAxCA,wDAAA,eAAAc,cAAA,CAAAC,IAAA,CAAwC;IAChDf,uDAAA,EACF;IADEA,gEAAA,MAAAM,MAAA,CAAAW,kBAAA,CAAAH,cAAA,CAAAC,IAAA,OACF;IAGgCf,uDAAA,GAA6B;IAA7BA,+DAAA,CAAAc,cAAA,CAAAI,WAAA,CAA6B;IACpClB,uDAAA,GAAuC;IAAvCA,+DAAA,CAAAM,MAAA,CAAAa,UAAA,CAAAL,cAAA,CAAAM,SAAA,EAAuC;IAErCpB,uDAAA,EAA0D;IAA1DA,wDAAA,CAAAc,cAAA,CAAAO,MAAA,+BAA0D;IACrFrB,uDAAA,EACF;IADEA,gEAAA,MAAAc,cAAA,CAAAO,MAAA,qBAAAP,cAAA,CAAAO,MAAA,UACF;;;;;IAbJrB,4DAAA,cAAkF;IAChFA,wDAAA,IAAAwB,4CAAA,mBAA0E;IAc5ExB,0DAAA,EAAM;;;;IAdyBA,uDAAA,EAAqB;IAArBA,wDAAA,YAAAM,MAAA,CAAAoB,kBAAA,CAAqB;;;;;IAkBhD1B,4DADF,cAAyB,mBACY;IAAAA,oDAAA,YAAK;IAAAA,0DAAA,EAAW;IACnDA,4DAAA,SAAI;IAAAA,oDAAA,wCAAuB;IAAAA,0DAAA,EAAK;IAChCA,4DAAA,QAAG;IAAAA,oDAAA,+EAA8D;IAAAA,0DAAA,EAAI;IAEnEA,4DADF,iBAAmE,eACvD;IAAAA,oDAAA,sBAAe;IAAAA,0DAAA,EAAW;IACpCA,oDAAA,4BACF;IACFA,0DADE,EAAS,EACL;;;;;;IA1KdA,4DAJN,aAA0D,aAE5B,aACE,kBACI;IAAAA,oDAAA,qBAAc;IAC9CA,0DAD8C,EAAW,EACnD;IAEJA,4DADF,aAA0B,SACpB;IAAAA,oDAAA,GAAe;IAAAA,0DAAA,EAAK;IACxBA,4DAAA,WAAsB;IAAAA,oDAAA,GAAgB;IAAAA,0DAAA,EAAI;IAGtCA,4DAFJ,cAAyB,oBACT,oBAC8C;IACxDA,oDAAA,IACF;IAAAA,0DAAA,EAAW;IACXA,4DAAA,oBAAsE;IACpEA,oDAAA,IACF;IAGNA,0DAHM,EAAW,EACE,EACX,EACF;IAEJA,4DADF,eAA6B,kBAC0D;IAA3CA,wDAAA,mBAAA4B,yDAAA;MAAA5B,2DAAA,CAAA8B,GAAA;MAAA,MAAAxB,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAA2B,WAAA,EAAa;IAAA,EAAC;IAC/DjC,4DAAA,gBAAU;IAAAA,oDAAA,YAAI;IAAAA,0DAAA,EAAW;IACzBA,oDAAA,kBACF;IAAAA,0DAAA,EAAS;IAETA,4DAAA,kBAAsF;IAA5CA,wDAAA,mBAAAkC,yDAAA;MAAAlC,2DAAA,CAAA8B,GAAA;MAAA,MAAAxB,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAA6B,YAAA,EAAc;IAAA,EAAC;IAChEnC,4DAAA,gBAAU;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAC1BA,oDAAA,kBACF;IAAAA,0DAAA,EAAS;IAETA,4DAAA,kBAAyF;IAA9CA,wDAAA,mBAAAoC,yDAAA;MAAApC,2DAAA,CAAA8B,GAAA;MAAA,MAAAxB,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAA+B,cAAA,EAAgB;IAAA,EAAC;IACnErC,4DAAA,gBAAU;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAW;IAC5BA,oDAAA,qBACF;IAEJA,0DAFI,EAAS,EACL,EACF;IAaUA,4DAVhB,yBAAoC,mBAEV,eACG,eAEI,eACD,oBACM,wBACR,eACU,oBAC4B;IAAAA,oDAAA,aAAK;IAAAA,0DAAA,EAAW;IAElEA,4DADF,eAAuB,UACjB;IAAAA,oDAAA,IAAiB;IAAAA,0DAAA,EAAK;IAC1BA,4DAAA,SAAG;IAAAA,oDAAA,qBAAa;IAIxBA,0DAJwB,EAAI,EAChB,EACF,EACW,EACV;IA0BXA,wDAxBA,KAAAsC,2CAAA,wBAA8C,KAAAC,2CAAA,wBAYA,KAAAC,2CAAA,wBAYA;IAYlDxC,0DADE,EAAM,EACF;IAOEA,4DAJR,eAA6B,oBACI,uBACZ,sBACC,gBACJ;IAAAA,oDAAA,YAAI;IAAAA,0DAAA,EAAW;IACzBA,oDAAA,mCACF;IACFA,0DADE,EAAiB,EACD;IAIZA,4DAHN,wBAAkB,eACU,eACC,gBACb;IAAAA,oDAAA,mBAAW;IAAAA,0DAAA,EAAW;IAChCA,4DAAA,gBAA2B;IAAAA,oDAAA,cAAM;IAAAA,0DAAA,EAAO;IACxCA,4DAAA,gBAA2B;IAAAA,oDAAA,IAAe;IAC5CA,0DAD4C,EAAO,EAC7C;IACNA,wDAAA,KAAAyC,sCAAA,kBAA4C;IAM1CzC,4DADF,eAAyB,gBACb;IAAAA,oDAAA,sBAAc;IAAAA,0DAAA,EAAW;IACnCA,4DAAA,gBAA2B;IAAAA,oDAAA,sBAAc;IAAAA,0DAAA,EAAO;IAChDA,4DAAA,gBAA2B;IAAAA,oDAAA,IAAsB;IACnDA,0DADmD,EAAO,EACpD;IAEJA,4DADF,eAAyB,gBACb;IAAAA,oDAAA,cAAM;IAAAA,0DAAA,EAAW;IAC3BA,4DAAA,gBAA2B;IAAAA,oDAAA,oCAAkB;IAAAA,0DAAA,EAAO;IACpDA,4DAAA,gBAA2B;IAAAA,oDAAA,IAAgC;IAOzEA,0DAPyE,EAAO,EAC9D,EACF,EACW,EACV,EACP,EACF,EACE;IAQAA,4DALV,mBAAkC,eACP,oBACS,uBACb,sBACC,gBACJ;IAAAA,oDAAA,eAAO;IAAAA,0DAAA,EAAW;IAC5BA,oDAAA,qCACF;IAAAA,0DAAA,EAAiB;IACjBA,4DAAA,kBAAmE;IAAhCA,wDAAA,mBAAA0C,yDAAA;MAAA1C,2DAAA,CAAA8B,GAAA;MAAA,MAAAxB,MAAA,GAAAN,2DAAA;MAAA,OAAAA,yDAAA,CAASM,MAAA,CAAAqC,mBAAA,EAAqB;IAAA,EAAC;IAChE3C,oDAAA,mBACF;IACFA,0DADE,EAAS,EACO;IAClBA,4DAAA,wBAAkB;IAkBhBA,wDAjBA,KAAA4C,sCAAA,kBAAkF,KAAAC,8CAAA,iCAAA7C,oEAAA,CAiBzD;IAgBrCA,0DALU,EAAmB,EACV,EACP,EACE,EACI,EACZ;;;;;IA9KIA,uDAAA,GAAe;IAAfA,+DAAA,CAAAM,MAAA,CAAAK,IAAA,CAAAoC,IAAA,CAAe;IACG/C,uDAAA,GAAgB;IAAhBA,+DAAA,CAAAM,MAAA,CAAAK,IAAA,CAAAqC,KAAA,CAAgB;IAGxBhD,uDAAA,GAAsC;IAAtCA,wDAAA,UAAAM,MAAA,CAAA2C,iBAAA,CAAA3C,MAAA,CAAAK,IAAA,CAAAuC,IAAA,EAAsC;IAC9ClD,uDAAA,EACF;IADEA,gEAAA,MAAAM,MAAA,CAAA6C,kBAAA,CAAA7C,MAAA,CAAAK,IAAA,CAAAuC,IAAA,OACF;IACUlD,uDAAA,EAAkD;IAAlDA,yDAAA,qBAAAM,MAAA,CAAA+C,qBAAA,GAAkD;IAC1DrD,uDAAA,EACF;IADEA,gEAAA,MAAAM,MAAA,CAAAgD,gBAAA,QACF;IAmCctD,uDAAA,IAAiB;IAAjBA,+DAAA,CAAAM,MAAA,CAAAK,IAAA,CAAAU,MAAA,CAAiB;IAOArB,uDAAA,GAAe;IAAfA,wDAAA,SAAAM,MAAA,CAAAC,SAAA,CAAe;IAYfP,uDAAA,EAAe;IAAfA,wDAAA,SAAAM,MAAA,CAAAC,SAAA,CAAe;IAYfP,uDAAA,EAAe;IAAfA,wDAAA,SAAAM,MAAA,CAAAC,SAAA,CAAe;IA4BXP,uDAAA,IAAe;IAAfA,+DAAA,CAAAM,MAAA,CAAAK,IAAA,CAAA4C,IAAA,CAAe;IAElBvD,uDAAA,EAAgB;IAAhBA,wDAAA,SAAAM,MAAA,CAAAK,IAAA,CAAAC,KAAA,CAAgB;IAQbZ,uDAAA,GAAsB;IAAtBA,+DAAA,CAAAM,MAAA,CAAAkD,cAAA,GAAsB;IAKtBxD,uDAAA,GAAgC;IAAhCA,+DAAA,CAAAM,MAAA,CAAAa,UAAA,CAAAb,MAAA,CAAAK,IAAA,CAAA8C,SAAA,EAAgC;IAuBrCzD,uDAAA,IAAqC;IAAAA,wDAArC,SAAAM,MAAA,CAAAoB,kBAAA,CAAAgC,MAAA,KAAqC,aAAAC,aAAA,CAAe;;;;;IAwCpF3D,4DAJR,cAAiD,mBAChB,uBACX,cACa,mBACI;IAAAA,oDAAA,qBAAc;IAAAA,0DAAA,EAAW;IACxDA,4DAAA,SAAI;IAAAA,oDAAA,8BAAuB;IAInCA,0DAJmC,EAAK,EAC5B,EACW,EACV,EACP;;;ADtLA,MAAO4D,gBAAgB;EAM3BC,YACUC,WAAwB,EACxBC,WAAwB,EACxBC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IARhB,KAAArD,IAAI,GAAgB,IAAI;IACxB,KAAAJ,SAAS,GAAQ,IAAI;IACrB,KAAAmB,kBAAkB,GAAwB,EAAE;IAC5C,KAAAuC,SAAS,GAAG,IAAI;EAMb;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACC,YAAY,EAAE;EACrB;EAEQA,YAAYA,CAAA;IAAA,IAAAC,KAAA;IAClB,IAAI,CAACN,WAAW,CAACO,YAAY,CAACC,SAAS;MAAA,IAAAC,IAAA,GAAAC,wJAAA,CAAC,WAAM7D,IAAI,EAAG;QACnD,IAAIA,IAAI,EAAE;UACRyD,KAAI,CAACzD,IAAI,GAAGA,IAAI;UAChB,MAAMyD,KAAI,CAACK,aAAa,EAAE;UAC1B,MAAML,KAAI,CAACM,sBAAsB,EAAE;;QAErCN,KAAI,CAACH,SAAS,GAAG,KAAK;MACxB,CAAC;MAAA,iBAAAU,EAAA;QAAA,OAAAJ,IAAA,CAAAK,KAAA,OAAAC,SAAA;MAAA;IAAA,IAAC;EACJ;EAEcJ,aAAaA,CAAA;IAAA,IAAAK,MAAA;IAAA,OAAAN,wJAAA;MACzB,IAAI,CAACM,MAAI,CAACnE,IAAI,EAAE;MAEhB,IAAI;QACFmE,MAAI,CAACf,WAAW,CAACgB,YAAY,CAACD,MAAI,CAACnE,IAAI,CAACqE,GAAG,CAAC,CAACV,SAAS,CAACW,KAAK,IAAG;UAC7DH,MAAI,CAACvE,SAAS,GAAG0E,KAAK;QACxB,CAAC,CAAC;OACH,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;;IAClD;EACH;EAEcR,sBAAsBA,CAAA;IAAA,IAAAU,MAAA;IAAA,OAAAZ,wJAAA;MAClC,IAAI,CAACY,MAAI,CAACzE,IAAI,EAAE;MAEhB,IAAI;QACFyE,MAAI,CAACrB,WAAW,CAACsB,mBAAmB,CAACD,MAAI,CAACzE,IAAI,CAACqE,GAAG,EAAE,EAAE,CAAC,CAACV,SAAS,CAACgB,YAAY,IAAG;UAC/EF,MAAI,CAAC1D,kBAAkB,GAAG4D,YAAY;QACxC,CAAC,CAAC;OACH,CAAC,OAAOJ,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,6BAA6B,EAAEA,KAAK,CAAC;;IACpD;EACH;EAEAjD,WAAWA,CAAA;IACT,IAAI,CAAC+B,MAAM,CAACuB,QAAQ,CAAC,CAAC,eAAe,CAAC,CAAC;EACzC;EAEApC,kBAAkBA,CAACD,IAAY;IAC7B,MAAMsC,SAAS,GAA8B;MAC3C,MAAM,EAAE,aAAa;MACrB,UAAU,EAAE,YAAY;MACxB,WAAW,EAAE,YAAY;MACzB,OAAO,EAAE;KACV;IACD,OAAOA,SAAS,CAACtC,IAAI,CAAC,IAAIA,IAAI;EAChC;EAEAD,iBAAiBA,CAACC,IAAY;IAC5B,MAAMuC,MAAM,GAA8B;MACxC,MAAM,EAAE,SAAS;MACjB,UAAU,EAAE,QAAQ;MACpB,WAAW,EAAE,MAAM;MACnB,OAAO,EAAE;KACV;IACD,OAAOA,MAAM,CAACvC,IAAI,CAAC,IAAI,SAAS;EAClC;EAEAjC,kBAAkBA,CAACF,IAAY;IAC7B,MAAM2E,OAAO,GAA8B;MACzC,QAAQ,EAAE,YAAY;MACtB,OAAO,EAAE,eAAe;MACxB,aAAa,EAAE,YAAY;MAC3B,WAAW,EAAE,UAAU;MACvB,OAAO,EAAE;KACV;IACD,OAAOA,OAAO,CAAC3E,IAAI,CAAC,IAAI,QAAQ;EAClC;EAEA4E,mBAAmBA,CAAC5E,IAAY;IAC9B,MAAM6E,QAAQ,GAA8B;MAC1C,QAAQ,EAAE,SAAS;MACnB,OAAO,EAAE,MAAM;MACf,aAAa,EAAE,SAAS;MACxB,WAAW,EAAE,QAAQ;MACrB,OAAO,EAAE;KACV;IACD,OAAOA,QAAQ,CAAC7E,IAAI,CAAC,IAAI,SAAS;EACpC;EAEAI,UAAUA,CAAC0E,IAAU;IACnB,OAAO,IAAIC,IAAI,CAACD,IAAI,CAAC,CAACE,kBAAkB,CAAC,OAAO,EAAE;MAChDC,GAAG,EAAE,SAAS;MACdC,KAAK,EAAE,SAAS;MAChBC,IAAI,EAAE,SAAS;MACfC,IAAI,EAAE,SAAS;MACfC,MAAM,EAAE;KACT,CAAC;EACJ;EAEA5C,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC7C,IAAI,EAAE0F,SAAS,EAAE,OAAO,EAAE;IAEpC,MAAMC,WAAW,GAAG,IAAIR,IAAI,CAAC,IAAI,CAACnF,IAAI,CAAC0F,SAAS,CAAC;IACjD,OAAOC,WAAW,CAACP,kBAAkB,CAAC,OAAO,EAAE;MAC7CE,KAAK,EAAE,MAAM;MACbC,IAAI,EAAE;KACP,CAAC;EACJ;EAEA5C,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAAC/C,SAAS,EAAE,OAAO,SAAS;IAErC,MAAMG,gBAAgB,GAAG,IAAI,CAACH,SAAS,CAACG,gBAAgB,IAAI,CAAC;IAE7D,IAAIA,gBAAgB,IAAI,EAAE,EAAE,OAAO,QAAQ;IAC3C,IAAIA,gBAAgB,IAAI,EAAE,EAAE,OAAO,OAAO;IAC1C,IAAIA,gBAAgB,IAAI,CAAC,EAAE,OAAO,UAAU;IAC5C,OAAO,UAAU;EACnB;EAEA2C,qBAAqBA,CAAA;IACnB,MAAMkD,KAAK,GAAG,IAAI,CAACjD,gBAAgB,EAAE;IACrC,MAAMmC,MAAM,GAA8B;MACxC,QAAQ,EAAE,SAAS;MACnB,OAAO,EAAE,SAAS;MAClB,UAAU,EAAE,SAAS;MACrB,UAAU,EAAE,SAAS;MACrB,SAAS,EAAE;KACZ;IACD,OAAOA,MAAM,CAACc,KAAK,CAAC,IAAI,SAAS;EACnC;EAEA5D,mBAAmBA,CAAA;IACjB;IACA,IAAI,CAACqB,MAAM,CAACuB,QAAQ,CAAC,CAAC,UAAU,CAAC,EAAE;MAAEiB,QAAQ,EAAE;IAAS,CAAE,CAAC;EAC7D;EAEArE,YAAYA,CAAA;IACV,IAAIsE,SAAS,CAACC,KAAK,EAAE;MACnBD,SAAS,CAACC,KAAK,CAAC;QACdC,KAAK,EAAE,mBAAmB,IAAI,CAAChG,IAAI,EAAEoC,IAAI,EAAE;QAC3C6D,IAAI,EAAE,oCAAoC,IAAI,CAACjG,IAAI,EAAEU,MAAM,UAAU;QACrEwF,GAAG,EAAEC,MAAM,CAACC,QAAQ,CAACC;OACtB,CAAC,CAACC,KAAK,CAAC9B,OAAO,CAACD,KAAK,CAAC;KACxB,MAAM;MACL;MACAuB,SAAS,CAACS,SAAS,CAACC,SAAS,CAACL,MAAM,CAACC,QAAQ,CAACC,IAAI,CAAC,CAACI,IAAI,CAAC,MAAK;QAC5DjC,OAAO,CAACkC,GAAG,CAAC,mCAAmC,CAAC;MAClD,CAAC,CAAC;;EAEN;EAEAhF,cAAcA,CAAA;IACZ;IACA8C,OAAO,CAACkC,GAAG,CAAC,0CAA0C,CAAC;IACvD;EACF;EAEAC,UAAUA,CAAA;IACR;IACAnC,OAAO,CAACkC,GAAG,CAAC,mCAAmC,CAAC;IAChD;EACF;;;uBA1KWzD,gBAAgB,EAAA5D,+DAAA,CAAAwH,oEAAA,GAAAxH,+DAAA,CAAA0H,oEAAA,GAAA1H,+DAAA,CAAA4H,mDAAA;IAAA;EAAA;;;YAAhBhE,gBAAgB;MAAAkE,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,0BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC6K7BpI,wDAxLA,IAAAsI,+BAAA,mBAA0D,IAAAC,+BAAA,iBAwLT;;;UAxLjBvI,wDAAA,UAAAqI,GAAA,CAAApE,SAAA,IAAAoE,GAAA,CAAA1H,IAAA,CAAwB;UAwLxBX,uDAAA,EAAe;UAAfA,wDAAA,SAAAqI,GAAA,CAAApE,SAAA,CAAe;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;ACvLA;AACM;AACN;AAE/C;AACuD;AACI;AACJ;AACW;AACT;AACE;AACJ;AACA;AACM;AACJ;AAEiB;AAC1E;AACA;AACiD;;;AA0B3C,MAAOqF,aAAa;;;uBAAbA,aAAa;IAAA;EAAA;;;YAAbA;IAAa;EAAA;;;gBAjBtBd,yDAAY,EACZC,+DAAmB,EACnBC,yDAAY,CAACa,QAAQ,CAACF,0DAAa,CAAC;MAEpC;MACAV,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,4EAAkB,EAClBC,oEAAc,EACdC,sEAAe,EACfC,kEAAa,EACbC,kEAAa,EACbC,wEAAgB,EAChBC,oEAAc;IAAA;EAAA;;;sHAGLE,aAAa;IAAAE,YAAA,GAtBtB5F,mFAAgB;IAAA6F,OAAA,GAKhBjB,yDAAY,EACZC,+DAAmB,EAAAjB,yDAAA;IAGnB;IACAmB,iEAAa,EACbC,qEAAe,EACfC,iEAAa,EACbC,4EAAkB,EAClBC,oEAAc,EACdC,sEAAe,EACfC,kEAAa,EACbC,kEAAa,EACbC,wEAAgB,EAChBC,oEAAc;EAAA;AAAA;;;;;;;;;;;;;;;AC1CwD;AAC1E;AAEO,MAAMC,aAAa,GAAW,CACnC;EACEK,IAAI,EAAE,EAAE;EACRC,SAAS,EAAE/F,mFAAgBA;;AAE7B;AACA;AACA;AACA;AAAA,CACD", "sources": ["./src/app/features/profile/components/profile/profile.component.ts", "./src/app/features/profile/components/profile/profile.component.html", "./src/app/features/profile/profile.module.ts", "./src/app/features/profile/profile.routes.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { UserService } from '../../../../core/services/user.service';\nimport { User, PointsTransaction } from '../../../../core/models';\n\n@Component({\n  selector: 'app-profile',\n  templateUrl: './profile.component.html',\n  styleUrls: ['./profile.component.css']\n})\nexport class ProfileComponent implements OnInit {\n  user: User | null = null;\n  userStats: any = null;\n  recentTransactions: PointsTransaction[] = [];\n  isLoading = true;\n\n  constructor(\n    private authService: AuthService,\n    private userService: UserService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.loadUserData();\n  }\n\n  private loadUserData(): void {\n    this.authService.currentUser$.subscribe(async user => {\n      if (user) {\n        this.user = user;\n        await this.loadUserStats();\n        await this.loadRecentTransactions();\n      }\n      this.isLoading = false;\n    });\n  }\n\n  private async loadUserStats(): Promise<void> {\n    if (!this.user) return;\n    \n    try {\n      this.userService.getUserStats(this.user.uid).subscribe(stats => {\n        this.userStats = stats;\n      });\n    } catch (error) {\n      console.error('Error loading user stats:', error);\n    }\n  }\n\n  private async loadRecentTransactions(): Promise<void> {\n    if (!this.user) return;\n    \n    try {\n      this.userService.getUserTransactions(this.user.uid, 10).subscribe(transactions => {\n        this.recentTransactions = transactions;\n      });\n    } catch (error) {\n      console.error('Error loading transactions:', error);\n    }\n  }\n\n  editProfile(): void {\n    this.router.navigate(['/profile/edit']);\n  }\n\n  getRoleDisplayName(role: string): string {\n    const roleNames: { [key: string]: string } = {\n      'user': 'Utilisateur',\n      'provider': 'Partenaire',\n      'validator': 'Validateur',\n      'admin': 'Administrateur'\n    };\n    return roleNames[role] || role;\n  }\n\n  getRoleBadgeColor(role: string): string {\n    const colors: { [key: string]: string } = {\n      'user': 'primary',\n      'provider': 'accent',\n      'validator': 'warn',\n      'admin': 'warn'\n    };\n    return colors[role] || 'primary';\n  }\n\n  getTransactionIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'earned': 'add_circle',\n      'spent': 'remove_circle',\n      'transferred': 'swap_horiz',\n      'validated': 'verified',\n      'bonus': 'star'\n    };\n    return iconMap[type] || 'circle';\n  }\n\n  getTransactionColor(type: string): string {\n    const colorMap: { [key: string]: string } = {\n      'earned': 'success',\n      'spent': 'warn',\n      'transferred': 'primary',\n      'validated': 'accent',\n      'bonus': 'accent'\n    };\n    return colorMap[type] || 'primary';\n  }\n\n  formatDate(date: Date): string {\n    return new Date(date).toLocaleDateString('fr-FR', {\n      day: '2-digit',\n      month: '2-digit',\n      year: 'numeric',\n      hour: '2-digit',\n      minute: '2-digit'\n    });\n  }\n\n  getMemberSince(): string {\n    if (!this.user?.createdAt) return '';\n    \n    const createdDate = new Date(this.user.createdAt);\n    return createdDate.toLocaleDateString('fr-FR', {\n      month: 'long',\n      year: 'numeric'\n    });\n  }\n\n  getActivityLevel(): string {\n    if (!this.userStats) return 'Nouveau';\n    \n    const transactionCount = this.userStats.transactionCount || 0;\n    \n    if (transactionCount >= 50) return 'Expert';\n    if (transactionCount >= 20) return 'Actif';\n    if (transactionCount >= 5) return 'Régulier';\n    return 'Débutant';\n  }\n\n  getActivityLevelColor(): string {\n    const level = this.getActivityLevel();\n    const colors: { [key: string]: string } = {\n      'Expert': '#4caf50',\n      'Actif': '#2196f3',\n      'Régulier': '#ff9800',\n      'Débutant': '#9e9e9e',\n      'Nouveau': '#9e9e9e'\n    };\n    return colors[level] || '#9e9e9e';\n  }\n\n  viewAllTransactions(): void {\n    // Navigate to full transaction history\n    this.router.navigate(['/profile'], { fragment: 'history' });\n  }\n\n  shareProfile(): void {\n    if (navigator.share) {\n      navigator.share({\n        title: `Profil Modjo de ${this.user?.name}`,\n        text: `Découvrez mon profil sur Modjo - ${this.user?.points} points!`,\n        url: window.location.href\n      }).catch(console.error);\n    } else {\n      // Fallback: copy to clipboard\n      navigator.clipboard.writeText(window.location.href).then(() => {\n        console.log('Lien copié dans le presse-papiers');\n      });\n    }\n  }\n\n  downloadQRCode(): void {\n    // Generate and download user QR code\n    console.log('Téléchargement du QR code utilisateur...');\n    // This would generate a QR code with user ID for scanning\n  }\n\n  exportData(): void {\n    // Export user data\n    console.log('Export des données utilisateur...');\n    // This would export user transaction history and stats\n  }\n}\n", "<div class=\"profile-container\" *ngIf=\"!isLoading && user\">\n  <!-- Profile header -->\n  <div class=\"profile-header\">\n    <div class=\"profile-avatar\">\n      <mat-icon class=\"avatar-icon\">account_circle</mat-icon>\n    </div>\n    <div class=\"profile-info\">\n      <h1>{{ user.name }}</h1>\n      <p class=\"user-email\">{{ user.email }}</p>\n      <div class=\"user-badges\">\n        <mat-chip-set>\n          <mat-chip [color]=\"getRoleBadgeColor(user.role)\" selected>\n            {{ getRoleDisplayName(user.role) }}\n          </mat-chip>\n          <mat-chip [style.background-color]=\"getActivityLevelColor()\" selected>\n            {{ getActivityLevel() }}\n          </mat-chip>\n        </mat-chip-set>\n      </div>\n    </div>\n    <div class=\"profile-actions\">\n      <button mat-raised-button color=\"primary\" (click)=\"editProfile()\" class=\"action-btn\">\n        <mat-icon>edit</mat-icon>\n        Modifier\n      </button>\n\n      <button mat-stroked-button color=\"accent\" (click)=\"shareProfile()\" class=\"action-btn\">\n        <mat-icon>share</mat-icon>\n        Partager\n      </button>\n\n      <button mat-stroked-button color=\"primary\" (click)=\"downloadQRCode()\" class=\"action-btn\">\n        <mat-icon>qr_code</mat-icon>\n        Mon QR Code\n      </button>\n    </div>\n  </div>\n\n  <!-- Profile tabs -->\n  <mat-tab-group class=\"profile-tabs\">\n    <!-- Overview tab -->\n    <mat-tab label=\"Aperçu\">\n      <div class=\"tab-content\">\n        <!-- Stats cards -->\n        <div class=\"stats-section\">\n          <div class=\"stats-grid\">\n            <mat-card class=\"stat-card\">\n              <mat-card-content>\n                <div class=\"stat-content\">\n                  <mat-icon class=\"stat-icon\" style=\"color: #ffd700;\">stars</mat-icon>\n                  <div class=\"stat-info\">\n                    <h3>{{ user.points }}</h3>\n                    <p>Points totaux</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <mat-card class=\"stat-card\" *ngIf=\"userStats\">\n              <mat-card-content>\n                <div class=\"stat-content\">\n                  <mat-icon class=\"stat-icon\" style=\"color: #4caf50;\">trending_up</mat-icon>\n                  <div class=\"stat-info\">\n                    <h3>{{ userStats.totalEarned || 0 }}</h3>\n                    <p>Points gagnés</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <mat-card class=\"stat-card\" *ngIf=\"userStats\">\n              <mat-card-content>\n                <div class=\"stat-content\">\n                  <mat-icon class=\"stat-icon\" style=\"color: #ff9800;\">shopping_cart</mat-icon>\n                  <div class=\"stat-info\">\n                    <h3>{{ userStats.totalSpent || 0 }}</h3>\n                    <p>Points dépensés</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n\n            <mat-card class=\"stat-card\" *ngIf=\"userStats\">\n              <mat-card-content>\n                <div class=\"stat-content\">\n                  <mat-icon class=\"stat-icon\" style=\"color: #2196f3;\">timeline</mat-icon>\n                  <div class=\"stat-info\">\n                    <h3>{{ userStats.transactionCount || 0 }}</h3>\n                    <p>Transactions</p>\n                  </div>\n                </div>\n              </mat-card-content>\n            </mat-card>\n          </div>\n        </div>\n\n        <!-- User details -->\n        <div class=\"details-section\">\n          <mat-card class=\"details-card\">\n            <mat-card-header>\n              <mat-card-title>\n                <mat-icon>info</mat-icon>\n                Informations personnelles\n              </mat-card-title>\n            </mat-card-header>\n            <mat-card-content>\n              <div class=\"details-list\">\n                <div class=\"detail-item\">\n                  <mat-icon>location_on</mat-icon>\n                  <span class=\"detail-label\">Ville:</span>\n                  <span class=\"detail-value\">{{ user.city }}</span>\n                </div>\n                <div class=\"detail-item\" *ngIf=\"user.phone\">\n                  <mat-icon>phone</mat-icon>\n                  <span class=\"detail-label\">Téléphone:</span>\n                  <span class=\"detail-value\">{{ user.phone }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <mat-icon>calendar_today</mat-icon>\n                  <span class=\"detail-label\">Membre depuis:</span>\n                  <span class=\"detail-value\">{{ getMemberSince() }}</span>\n                </div>\n                <div class=\"detail-item\">\n                  <mat-icon>update</mat-icon>\n                  <span class=\"detail-label\">Dernière activité:</span>\n                  <span class=\"detail-value\">{{ formatDate(user.updatedAt) }}</span>\n                </div>\n              </div>\n            </mat-card-content>\n          </mat-card>\n        </div>\n      </div>\n    </mat-tab>\n\n    <!-- Recent activity tab -->\n    <mat-tab label=\"Activité récente\">\n      <div class=\"tab-content\">\n        <mat-card class=\"activity-card\">\n          <mat-card-header>\n            <mat-card-title>\n              <mat-icon>history</mat-icon>\n              Dernières transactions\n            </mat-card-title>\n            <button mat-button color=\"primary\" (click)=\"viewAllTransactions()\">\n              Voir tout\n            </button>\n          </mat-card-header>\n          <mat-card-content>\n            <div class=\"activity-list\" *ngIf=\"recentTransactions.length > 0; else noActivity\">\n              <div *ngFor=\"let transaction of recentTransactions\" class=\"activity-item\">\n                <div class=\"activity-icon\">\n                  <mat-icon [class]=\"'activity-' + transaction.type\">\n                    {{ getTransactionIcon(transaction.type) }}\n                  </mat-icon>\n                </div>\n                <div class=\"activity-info\">\n                  <p class=\"activity-description\">{{ transaction.description }}</p>\n                  <p class=\"activity-date\">{{ formatDate(transaction.timestamp) }}</p>\n                </div>\n                <div class=\"activity-points\" [class]=\"transaction.points > 0 ? 'positive' : 'negative'\">\n                  {{ transaction.points > 0 ? '+' : '' }}{{ transaction.points }} pts\n                </div>\n              </div>\n            </div>\n            \n            <ng-template #noActivity>\n              <div class=\"no-activity\">\n                <mat-icon class=\"no-activity-icon\">inbox</mat-icon>\n                <h3>Aucune activité récente</h3>\n                <p>Commencez à scanner des QR codes pour voir votre activité ici.</p>\n                <button mat-raised-button color=\"primary\" routerLink=\"/qr-scanner\">\n                  <mat-icon>qr_code_scanner</mat-icon>\n                  Scanner un QR Code\n                </button>\n              </div>\n            </ng-template>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </mat-tab>\n  </mat-tab-group>\n</div>\n\n<!-- Loading state -->\n<div class=\"loading-container\" *ngIf=\"isLoading\">\n  <mat-card class=\"loading-card\">\n    <mat-card-content>\n      <div class=\"loading-content\">\n        <mat-icon class=\"loading-icon\">account_circle</mat-icon>\n        <h3>Chargement du profil...</h3>\n      </div>\n    </mat-card-content>\n  </mat-card>\n</div>\n", "import { NgModule } from '@angular/core';\nimport { CommonModule } from '@angular/common';\nimport { ReactiveFormsModule } from '@angular/forms';\nimport { RouterModule } from '@angular/router';\n\n// Material modules\nimport { MatCardModule } from '@angular/material/card';\nimport { MatButtonModule } from '@angular/material/button';\nimport { MatIconModule } from '@angular/material/icon';\nimport { MatFormFieldModule } from '@angular/material/form-field';\nimport { MatInputModule } from '@angular/material/input';\nimport { MatSelectModule } from '@angular/material/select';\nimport { MatTabsModule } from '@angular/material/tabs';\nimport { MatListModule } from '@angular/material/list';\nimport { MatDividerModule } from '@angular/material/divider';\nimport { MatChipsModule } from '@angular/material/chips';\n\nimport { ProfileComponent } from './components/profile/profile.component';\n// import { ProfileEditComponent } from './components/profile-edit/profile-edit.component';\n// import { PointsHistoryComponent } from './components/points-history/points-history.component';\nimport { profileRoutes } from './profile.routes';\n\n@NgModule({\n  declarations: [\n    ProfileComponent,\n    // ProfileEditComponent,\n    // PointsHistoryComponent\n  ],\n  imports: [\n    CommonModule,\n    ReactiveFormsModule,\n    RouterModule.forChild(profileRoutes),\n    \n    // Material modules\n    MatCardModule,\n    MatButtonModule,\n    MatIconModule,\n    MatFormFieldModule,\n    MatInputModule,\n    MatSelectModule,\n    MatTabsModule,\n    MatListModule,\n    MatDividerModule,\n    MatChipsModule\n  ]\n})\nexport class ProfileModule { }\n", "import { Routes } from '@angular/router';\nimport { ProfileComponent } from './components/profile/profile.component';\n// import { ProfileEditComponent } from './components/profile-edit/profile-edit.component';\n\nexport const profileRoutes: Routes = [\n  {\n    path: '',\n    component: ProfileComponent\n  },\n  // {\n  //   path: 'edit',\n  //   component: ProfileEditComponent\n  // }\n];\n"], "names": ["i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate", "ctx_r1", "userStats", "totalEarned", "totalSpent", "transactionCount", "user", "phone", "ɵɵclassMap", "transaction_r3", "type", "ɵɵtextInterpolate1", "getTransactionIcon", "description", "formatDate", "timestamp", "points", "ɵɵtextInterpolate2", "ɵɵtemplate", "ProfileComponent_div_0_div_89_div_1_Template", "ɵɵproperty", "recentTransactions", "ɵɵlistener", "ProfileComponent_div_0_Template_button_click_17_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "editProfile", "ProfileComponent_div_0_Template_button_click_21_listener", "shareProfile", "ProfileComponent_div_0_Template_button_click_25_listener", "downloadQRCode", "ProfileComponent_div_0_mat_card_44_Template", "ProfileComponent_div_0_mat_card_45_Template", "ProfileComponent_div_0_mat_card_46_Template", "ProfileComponent_div_0_div_63_Template", "ProfileComponent_div_0_Template_button_click_86_listener", "viewAllTransactions", "ProfileComponent_div_0_div_89_Template", "ProfileComponent_div_0_ng_template_90_Template", "ɵɵtemplateRefExtractor", "name", "email", "getRoleBadgeColor", "role", "getRoleDisplayName", "ɵɵstyleProp", "getActivityLevelColor", "getActivityLevel", "city", "getMemberSince", "updatedAt", "length", "noActivity_r4", "ProfileComponent", "constructor", "authService", "userService", "router", "isLoading", "ngOnInit", "loadUserData", "_this", "currentUser$", "subscribe", "_ref", "_asyncToGenerator", "loadUserStats", "loadRecentTransactions", "_x", "apply", "arguments", "_this2", "getUserStats", "uid", "stats", "error", "console", "_this3", "getUserTransactions", "transactions", "navigate", "roleNames", "colors", "iconMap", "getTransactionColor", "colorMap", "date", "Date", "toLocaleDateString", "day", "month", "year", "hour", "minute", "createdAt", "createdDate", "level", "fragment", "navigator", "share", "title", "text", "url", "window", "location", "href", "catch", "clipboard", "writeText", "then", "log", "exportData", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "UserService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "ProfileComponent_Template", "rf", "ctx", "ProfileComponent_div_0_Template", "ProfileComponent_div_1_Template", "CommonModule", "ReactiveFormsModule", "RouterModule", "MatCardModule", "MatButtonModule", "MatIconModule", "MatFormFieldModule", "MatInputModule", "MatSelectModule", "MatTabsModule", "MatListModule", "MatDividerModule", "MatChipsModule", "profileRoutes", "ProfileModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "path", "component"], "sourceRoot": "webpack:///", "x_google_ignoreList": []}