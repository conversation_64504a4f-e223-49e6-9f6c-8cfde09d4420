{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Deferred } from '@firebase/util';\n\n/**\r\n * Component for service name T, e.g. `auth`, `auth-internal`\r\n */\nclass Component {\n  /**\r\n   *\r\n   * @param name The public service name, e.g. app, auth, firestore, database\r\n   * @param instanceFactory Service factory responsible for creating the public interface\r\n   * @param type whether the service provided by the component is public or private\r\n   */\n  constructor(name, instanceFactory, type) {\n    this.name = name;\n    this.instanceFactory = instanceFactory;\n    this.type = type;\n    this.multipleInstances = false;\n    /**\r\n     * Properties to be added to the service namespace\r\n     */\n    this.serviceProps = {};\n    this.instantiationMode = \"LAZY\" /* InstantiationMode.LAZY */;\n    this.onInstanceCreated = null;\n  }\n  setInstantiationMode(mode) {\n    this.instantiationMode = mode;\n    return this;\n  }\n  setMultipleInstances(multipleInstances) {\n    this.multipleInstances = multipleInstances;\n    return this;\n  }\n  setServiceProps(props) {\n    this.serviceProps = props;\n    return this;\n  }\n  setInstanceCreatedCallback(callback) {\n    this.onInstanceCreated = callback;\n    return this;\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\r\n * NameServiceMapping[T] is an alias for the type of the instance\r\n */\nclass Provider {\n  constructor(name, container) {\n    this.name = name;\n    this.container = container;\n    this.component = null;\n    this.instances = new Map();\n    this.instancesDeferred = new Map();\n    this.instancesOptions = new Map();\n    this.onInitCallbacks = new Map();\n  }\n  /**\r\n   * @param identifier A provider can provide mulitple instances of a service\r\n   * if this.component.multipleInstances is true.\r\n   */\n  get(identifier) {\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    if (!this.instancesDeferred.has(normalizedIdentifier)) {\n      const deferred = new Deferred();\n      this.instancesDeferred.set(normalizedIdentifier, deferred);\n      if (this.isInitialized(normalizedIdentifier) || this.shouldAutoInitialize()) {\n        // initialize the service if it can be auto-initialized\n        try {\n          const instance = this.getOrInitializeService({\n            instanceIdentifier: normalizedIdentifier\n          });\n          if (instance) {\n            deferred.resolve(instance);\n          }\n        } catch (e) {\n          // when the instance factory throws an exception during get(), it should not cause\n          // a fatal error. We just return the unresolved promise in this case.\n        }\n      }\n    }\n    return this.instancesDeferred.get(normalizedIdentifier).promise;\n  }\n  getImmediate(options) {\n    var _a;\n    // if multipleInstances is not supported, use the default name\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(options === null || options === void 0 ? void 0 : options.identifier);\n    const optional = (_a = options === null || options === void 0 ? void 0 : options.optional) !== null && _a !== void 0 ? _a : false;\n    if (this.isInitialized(normalizedIdentifier) || this.shouldAutoInitialize()) {\n      try {\n        return this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n      } catch (e) {\n        if (optional) {\n          return null;\n        } else {\n          throw e;\n        }\n      }\n    } else {\n      // In case a component is not initialized and should/can not be auto-initialized at the moment, return null if the optional flag is set, or throw\n      if (optional) {\n        return null;\n      } else {\n        throw Error(`Service ${this.name} is not available`);\n      }\n    }\n  }\n  getComponent() {\n    return this.component;\n  }\n  setComponent(component) {\n    if (component.name !== this.name) {\n      throw Error(`Mismatching Component ${component.name} for Provider ${this.name}.`);\n    }\n    if (this.component) {\n      throw Error(`Component for ${this.name} has already been provided`);\n    }\n    this.component = component;\n    // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\n    if (!this.shouldAutoInitialize()) {\n      return;\n    }\n    // if the service is eager, initialize the default instance\n    if (isComponentEager(component)) {\n      try {\n        this.getOrInitializeService({\n          instanceIdentifier: DEFAULT_ENTRY_NAME\n        });\n      } catch (e) {\n        // when the instance factory for an eager Component throws an exception during the eager\n        // initialization, it should not cause a fatal error.\n        // TODO: Investigate if we need to make it configurable, because some component may want to cause\n        // a fatal error in this case?\n      }\n    }\n    // Create service instances for the pending promises and resolve them\n    // NOTE: if this.multipleInstances is false, only the default instance will be created\n    // and all promises with resolve with it regardless of the identifier.\n    for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\n      const normalizedIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\n      try {\n        // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\n        const instance = this.getOrInitializeService({\n          instanceIdentifier: normalizedIdentifier\n        });\n        instanceDeferred.resolve(instance);\n      } catch (e) {\n        // when the instance factory throws an exception, it should not cause\n        // a fatal error. We just leave the promise unresolved.\n      }\n    }\n  }\n  clearInstance(identifier = DEFAULT_ENTRY_NAME) {\n    this.instancesDeferred.delete(identifier);\n    this.instancesOptions.delete(identifier);\n    this.instances.delete(identifier);\n  }\n  // app.delete() will call this method on every provider to delete the services\n  // TODO: should we mark the provider as deleted?\n  delete() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      const services = Array.from(_this.instances.values());\n      yield Promise.all([...services.filter(service => 'INTERNAL' in service) // legacy services\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      .map(service => service.INTERNAL.delete()), ...services.filter(service => '_delete' in service) // modularized services\n      // eslint-disable-next-line @typescript-eslint/no-explicit-any\n      .map(service => service._delete())]);\n    })();\n  }\n  isComponentSet() {\n    return this.component != null;\n  }\n  isInitialized(identifier = DEFAULT_ENTRY_NAME) {\n    return this.instances.has(identifier);\n  }\n  getOptions(identifier = DEFAULT_ENTRY_NAME) {\n    return this.instancesOptions.get(identifier) || {};\n  }\n  initialize(opts = {}) {\n    const {\n      options = {}\n    } = opts;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(opts.instanceIdentifier);\n    if (this.isInitialized(normalizedIdentifier)) {\n      throw Error(`${this.name}(${normalizedIdentifier}) has already been initialized`);\n    }\n    if (!this.isComponentSet()) {\n      throw Error(`Component ${this.name} has not been registered yet`);\n    }\n    const instance = this.getOrInitializeService({\n      instanceIdentifier: normalizedIdentifier,\n      options\n    });\n    // resolve any pending promise waiting for the service instance\n    for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\n      const normalizedDeferredIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\n      if (normalizedIdentifier === normalizedDeferredIdentifier) {\n        instanceDeferred.resolve(instance);\n      }\n    }\n    return instance;\n  }\n  /**\r\n   *\r\n   * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\r\n   * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\r\n   *\r\n   * @param identifier An optional instance identifier\r\n   * @returns a function to unregister the callback\r\n   */\n  onInit(callback, identifier) {\n    var _a;\n    const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\n    const existingCallbacks = (_a = this.onInitCallbacks.get(normalizedIdentifier)) !== null && _a !== void 0 ? _a : new Set();\n    existingCallbacks.add(callback);\n    this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\n    const existingInstance = this.instances.get(normalizedIdentifier);\n    if (existingInstance) {\n      callback(existingInstance, normalizedIdentifier);\n    }\n    return () => {\n      existingCallbacks.delete(callback);\n    };\n  }\n  /**\r\n   * Invoke onInit callbacks synchronously\r\n   * @param instance the service instance`\r\n   */\n  invokeOnInitCallbacks(instance, identifier) {\n    const callbacks = this.onInitCallbacks.get(identifier);\n    if (!callbacks) {\n      return;\n    }\n    for (const callback of callbacks) {\n      try {\n        callback(instance, identifier);\n      } catch (_a) {\n        // ignore errors in the onInit callback\n      }\n    }\n  }\n  getOrInitializeService({\n    instanceIdentifier,\n    options = {}\n  }) {\n    let instance = this.instances.get(instanceIdentifier);\n    if (!instance && this.component) {\n      instance = this.component.instanceFactory(this.container, {\n        instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\n        options\n      });\n      this.instances.set(instanceIdentifier, instance);\n      this.instancesOptions.set(instanceIdentifier, options);\n      /**\r\n       * Invoke onInit listeners.\r\n       * Note this.component.onInstanceCreated is different, which is used by the component creator,\r\n       * while onInit listeners are registered by consumers of the provider.\r\n       */\n      this.invokeOnInitCallbacks(instance, instanceIdentifier);\n      /**\r\n       * Order is important\r\n       * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\r\n       * makes `isInitialized()` return true.\r\n       */\n      if (this.component.onInstanceCreated) {\n        try {\n          this.component.onInstanceCreated(this.container, instanceIdentifier, instance);\n        } catch (_a) {\n          // ignore errors in the onInstanceCreatedCallback\n        }\n      }\n    }\n    return instance || null;\n  }\n  normalizeInstanceIdentifier(identifier = DEFAULT_ENTRY_NAME) {\n    if (this.component) {\n      return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\n    } else {\n      return identifier; // assume multiple instances are supported before the component is provided.\n    }\n  }\n  shouldAutoInitialize() {\n    return !!this.component && this.component.instantiationMode !== \"EXPLICIT\" /* InstantiationMode.EXPLICIT */;\n  }\n}\n// undefined should be passed to the service factory for the default instance\nfunction normalizeIdentifierForFactory(identifier) {\n  return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\n}\nfunction isComponentEager(component) {\n  return component.instantiationMode === \"EAGER\" /* InstantiationMode.EAGER */;\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\r\n */\nclass ComponentContainer {\n  constructor(name) {\n    this.name = name;\n    this.providers = new Map();\n  }\n  /**\r\n   *\r\n   * @param component Component being added\r\n   * @param overwrite When a component with the same name has already been registered,\r\n   * if overwrite is true: overwrite the existing component with the new component and create a new\r\n   * provider with the new component. It can be useful in tests where you want to use different mocks\r\n   * for different tests.\r\n   * if overwrite is false: throw an exception\r\n   */\n  addComponent(component) {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      throw new Error(`Component ${component.name} has already been registered with ${this.name}`);\n    }\n    provider.setComponent(component);\n  }\n  addOrOverwriteComponent(component) {\n    const provider = this.getProvider(component.name);\n    if (provider.isComponentSet()) {\n      // delete the existing provider from the container, so we can register the new component\n      this.providers.delete(component.name);\n    }\n    this.addComponent(component);\n  }\n  /**\r\n   * getProvider provides a type safe interface where it can only be called with a field name\r\n   * present in NameServiceMapping interface.\r\n   *\r\n   * Firebase SDKs providing services should extend NameServiceMapping interface to register\r\n   * themselves.\r\n   */\n  getProvider(name) {\n    if (this.providers.has(name)) {\n      return this.providers.get(name);\n    }\n    // create a Provider for a service that hasn't registered with Firebase\n    const provider = new Provider(name, this);\n    this.providers.set(name, provider);\n    return provider;\n  }\n  getProviders() {\n    return Array.from(this.providers.values());\n  }\n}\nexport { Component, ComponentContainer, Provider };", "map": {"version": 3, "names": ["Deferred", "Component", "constructor", "name", "instanceFactory", "type", "multipleInstances", "serviceProps", "instantiationMode", "onInstanceCreated", "setInstantiationMode", "mode", "setMultipleInstances", "setServiceProps", "props", "setInstanceCreatedCallback", "callback", "DEFAULT_ENTRY_NAME", "Provider", "container", "component", "instances", "Map", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "instancesOptions", "onInitCallbacks", "get", "identifier", "normalizedIdentifier", "normalizeInstanceIdentifier", "has", "deferred", "set", "isInitialized", "shouldAutoInitialize", "instance", "getOrInitializeService", "instanceIdentifier", "resolve", "e", "promise", "getImmediate", "options", "_a", "optional", "Error", "getComponent", "setComponent", "isComponentEager", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "entries", "clearInstance", "delete", "_this", "_asyncToGenerator", "services", "Array", "from", "values", "Promise", "all", "filter", "service", "map", "INTERNAL", "_delete", "isComponentSet", "getOptions", "initialize", "opts", "normalizedDeferredIdentifier", "onInit", "existingCallbacks", "Set", "add", "existingInstance", "invokeOnInitCallbacks", "callbacks", "normalizeIdentifierForFactory", "undefined", "ComponentContainer", "providers", "addComponent", "provider", "get<PERSON><PERSON><PERSON>", "addOrOverwriteComponent", "getProviders"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/fire/node_modules/@firebase/component/dist/esm/index.esm2017.js"], "sourcesContent": ["import { Deferred } from '@firebase/util';\n\n/**\r\n * Component for service name T, e.g. `auth`, `auth-internal`\r\n */\r\nclass Component {\r\n    /**\r\n     *\r\n     * @param name The public service name, e.g. app, auth, firestore, database\r\n     * @param instanceFactory Service factory responsible for creating the public interface\r\n     * @param type whether the service provided by the component is public or private\r\n     */\r\n    constructor(name, instanceFactory, type) {\r\n        this.name = name;\r\n        this.instanceFactory = instanceFactory;\r\n        this.type = type;\r\n        this.multipleInstances = false;\r\n        /**\r\n         * Properties to be added to the service namespace\r\n         */\r\n        this.serviceProps = {};\r\n        this.instantiationMode = \"LAZY\" /* InstantiationMode.LAZY */;\r\n        this.onInstanceCreated = null;\r\n    }\r\n    setInstantiationMode(mode) {\r\n        this.instantiationMode = mode;\r\n        return this;\r\n    }\r\n    setMultipleInstances(multipleInstances) {\r\n        this.multipleInstances = multipleInstances;\r\n        return this;\r\n    }\r\n    setServiceProps(props) {\r\n        this.serviceProps = props;\r\n        return this;\r\n    }\r\n    setInstanceCreatedCallback(callback) {\r\n        this.onInstanceCreated = callback;\r\n        return this;\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst DEFAULT_ENTRY_NAME = '[DEFAULT]';\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Provider for instance for service name T, e.g. 'auth', 'auth-internal'\r\n * NameServiceMapping[T] is an alias for the type of the instance\r\n */\r\nclass Provider {\r\n    constructor(name, container) {\r\n        this.name = name;\r\n        this.container = container;\r\n        this.component = null;\r\n        this.instances = new Map();\r\n        this.instancesDeferred = new Map();\r\n        this.instancesOptions = new Map();\r\n        this.onInitCallbacks = new Map();\r\n    }\r\n    /**\r\n     * @param identifier A provider can provide mulitple instances of a service\r\n     * if this.component.multipleInstances is true.\r\n     */\r\n    get(identifier) {\r\n        // if multipleInstances is not supported, use the default name\r\n        const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\r\n        if (!this.instancesDeferred.has(normalizedIdentifier)) {\r\n            const deferred = new Deferred();\r\n            this.instancesDeferred.set(normalizedIdentifier, deferred);\r\n            if (this.isInitialized(normalizedIdentifier) ||\r\n                this.shouldAutoInitialize()) {\r\n                // initialize the service if it can be auto-initialized\r\n                try {\r\n                    const instance = this.getOrInitializeService({\r\n                        instanceIdentifier: normalizedIdentifier\r\n                    });\r\n                    if (instance) {\r\n                        deferred.resolve(instance);\r\n                    }\r\n                }\r\n                catch (e) {\r\n                    // when the instance factory throws an exception during get(), it should not cause\r\n                    // a fatal error. We just return the unresolved promise in this case.\r\n                }\r\n            }\r\n        }\r\n        return this.instancesDeferred.get(normalizedIdentifier).promise;\r\n    }\r\n    getImmediate(options) {\r\n        var _a;\r\n        // if multipleInstances is not supported, use the default name\r\n        const normalizedIdentifier = this.normalizeInstanceIdentifier(options === null || options === void 0 ? void 0 : options.identifier);\r\n        const optional = (_a = options === null || options === void 0 ? void 0 : options.optional) !== null && _a !== void 0 ? _a : false;\r\n        if (this.isInitialized(normalizedIdentifier) ||\r\n            this.shouldAutoInitialize()) {\r\n            try {\r\n                return this.getOrInitializeService({\r\n                    instanceIdentifier: normalizedIdentifier\r\n                });\r\n            }\r\n            catch (e) {\r\n                if (optional) {\r\n                    return null;\r\n                }\r\n                else {\r\n                    throw e;\r\n                }\r\n            }\r\n        }\r\n        else {\r\n            // In case a component is not initialized and should/can not be auto-initialized at the moment, return null if the optional flag is set, or throw\r\n            if (optional) {\r\n                return null;\r\n            }\r\n            else {\r\n                throw Error(`Service ${this.name} is not available`);\r\n            }\r\n        }\r\n    }\r\n    getComponent() {\r\n        return this.component;\r\n    }\r\n    setComponent(component) {\r\n        if (component.name !== this.name) {\r\n            throw Error(`Mismatching Component ${component.name} for Provider ${this.name}.`);\r\n        }\r\n        if (this.component) {\r\n            throw Error(`Component for ${this.name} has already been provided`);\r\n        }\r\n        this.component = component;\r\n        // return early without attempting to initialize the component if the component requires explicit initialization (calling `Provider.initialize()`)\r\n        if (!this.shouldAutoInitialize()) {\r\n            return;\r\n        }\r\n        // if the service is eager, initialize the default instance\r\n        if (isComponentEager(component)) {\r\n            try {\r\n                this.getOrInitializeService({ instanceIdentifier: DEFAULT_ENTRY_NAME });\r\n            }\r\n            catch (e) {\r\n                // when the instance factory for an eager Component throws an exception during the eager\r\n                // initialization, it should not cause a fatal error.\r\n                // TODO: Investigate if we need to make it configurable, because some component may want to cause\r\n                // a fatal error in this case?\r\n            }\r\n        }\r\n        // Create service instances for the pending promises and resolve them\r\n        // NOTE: if this.multipleInstances is false, only the default instance will be created\r\n        // and all promises with resolve with it regardless of the identifier.\r\n        for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\r\n            const normalizedIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\r\n            try {\r\n                // `getOrInitializeService()` should always return a valid instance since a component is guaranteed. use ! to make typescript happy.\r\n                const instance = this.getOrInitializeService({\r\n                    instanceIdentifier: normalizedIdentifier\r\n                });\r\n                instanceDeferred.resolve(instance);\r\n            }\r\n            catch (e) {\r\n                // when the instance factory throws an exception, it should not cause\r\n                // a fatal error. We just leave the promise unresolved.\r\n            }\r\n        }\r\n    }\r\n    clearInstance(identifier = DEFAULT_ENTRY_NAME) {\r\n        this.instancesDeferred.delete(identifier);\r\n        this.instancesOptions.delete(identifier);\r\n        this.instances.delete(identifier);\r\n    }\r\n    // app.delete() will call this method on every provider to delete the services\r\n    // TODO: should we mark the provider as deleted?\r\n    async delete() {\r\n        const services = Array.from(this.instances.values());\r\n        await Promise.all([\r\n            ...services\r\n                .filter(service => 'INTERNAL' in service) // legacy services\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                .map(service => service.INTERNAL.delete()),\r\n            ...services\r\n                .filter(service => '_delete' in service) // modularized services\r\n                // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n                .map(service => service._delete())\r\n        ]);\r\n    }\r\n    isComponentSet() {\r\n        return this.component != null;\r\n    }\r\n    isInitialized(identifier = DEFAULT_ENTRY_NAME) {\r\n        return this.instances.has(identifier);\r\n    }\r\n    getOptions(identifier = DEFAULT_ENTRY_NAME) {\r\n        return this.instancesOptions.get(identifier) || {};\r\n    }\r\n    initialize(opts = {}) {\r\n        const { options = {} } = opts;\r\n        const normalizedIdentifier = this.normalizeInstanceIdentifier(opts.instanceIdentifier);\r\n        if (this.isInitialized(normalizedIdentifier)) {\r\n            throw Error(`${this.name}(${normalizedIdentifier}) has already been initialized`);\r\n        }\r\n        if (!this.isComponentSet()) {\r\n            throw Error(`Component ${this.name} has not been registered yet`);\r\n        }\r\n        const instance = this.getOrInitializeService({\r\n            instanceIdentifier: normalizedIdentifier,\r\n            options\r\n        });\r\n        // resolve any pending promise waiting for the service instance\r\n        for (const [instanceIdentifier, instanceDeferred] of this.instancesDeferred.entries()) {\r\n            const normalizedDeferredIdentifier = this.normalizeInstanceIdentifier(instanceIdentifier);\r\n            if (normalizedIdentifier === normalizedDeferredIdentifier) {\r\n                instanceDeferred.resolve(instance);\r\n            }\r\n        }\r\n        return instance;\r\n    }\r\n    /**\r\n     *\r\n     * @param callback - a function that will be invoked  after the provider has been initialized by calling provider.initialize().\r\n     * The function is invoked SYNCHRONOUSLY, so it should not execute any longrunning tasks in order to not block the program.\r\n     *\r\n     * @param identifier An optional instance identifier\r\n     * @returns a function to unregister the callback\r\n     */\r\n    onInit(callback, identifier) {\r\n        var _a;\r\n        const normalizedIdentifier = this.normalizeInstanceIdentifier(identifier);\r\n        const existingCallbacks = (_a = this.onInitCallbacks.get(normalizedIdentifier)) !== null && _a !== void 0 ? _a : new Set();\r\n        existingCallbacks.add(callback);\r\n        this.onInitCallbacks.set(normalizedIdentifier, existingCallbacks);\r\n        const existingInstance = this.instances.get(normalizedIdentifier);\r\n        if (existingInstance) {\r\n            callback(existingInstance, normalizedIdentifier);\r\n        }\r\n        return () => {\r\n            existingCallbacks.delete(callback);\r\n        };\r\n    }\r\n    /**\r\n     * Invoke onInit callbacks synchronously\r\n     * @param instance the service instance`\r\n     */\r\n    invokeOnInitCallbacks(instance, identifier) {\r\n        const callbacks = this.onInitCallbacks.get(identifier);\r\n        if (!callbacks) {\r\n            return;\r\n        }\r\n        for (const callback of callbacks) {\r\n            try {\r\n                callback(instance, identifier);\r\n            }\r\n            catch (_a) {\r\n                // ignore errors in the onInit callback\r\n            }\r\n        }\r\n    }\r\n    getOrInitializeService({ instanceIdentifier, options = {} }) {\r\n        let instance = this.instances.get(instanceIdentifier);\r\n        if (!instance && this.component) {\r\n            instance = this.component.instanceFactory(this.container, {\r\n                instanceIdentifier: normalizeIdentifierForFactory(instanceIdentifier),\r\n                options\r\n            });\r\n            this.instances.set(instanceIdentifier, instance);\r\n            this.instancesOptions.set(instanceIdentifier, options);\r\n            /**\r\n             * Invoke onInit listeners.\r\n             * Note this.component.onInstanceCreated is different, which is used by the component creator,\r\n             * while onInit listeners are registered by consumers of the provider.\r\n             */\r\n            this.invokeOnInitCallbacks(instance, instanceIdentifier);\r\n            /**\r\n             * Order is important\r\n             * onInstanceCreated() should be called after this.instances.set(instanceIdentifier, instance); which\r\n             * makes `isInitialized()` return true.\r\n             */\r\n            if (this.component.onInstanceCreated) {\r\n                try {\r\n                    this.component.onInstanceCreated(this.container, instanceIdentifier, instance);\r\n                }\r\n                catch (_a) {\r\n                    // ignore errors in the onInstanceCreatedCallback\r\n                }\r\n            }\r\n        }\r\n        return instance || null;\r\n    }\r\n    normalizeInstanceIdentifier(identifier = DEFAULT_ENTRY_NAME) {\r\n        if (this.component) {\r\n            return this.component.multipleInstances ? identifier : DEFAULT_ENTRY_NAME;\r\n        }\r\n        else {\r\n            return identifier; // assume multiple instances are supported before the component is provided.\r\n        }\r\n    }\r\n    shouldAutoInitialize() {\r\n        return (!!this.component &&\r\n            this.component.instantiationMode !== \"EXPLICIT\" /* InstantiationMode.EXPLICIT */);\r\n    }\r\n}\r\n// undefined should be passed to the service factory for the default instance\r\nfunction normalizeIdentifierForFactory(identifier) {\r\n    return identifier === DEFAULT_ENTRY_NAME ? undefined : identifier;\r\n}\r\nfunction isComponentEager(component) {\r\n    return component.instantiationMode === \"EAGER\" /* InstantiationMode.EAGER */;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2019 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * ComponentContainer that provides Providers for service name T, e.g. `auth`, `auth-internal`\r\n */\r\nclass ComponentContainer {\r\n    constructor(name) {\r\n        this.name = name;\r\n        this.providers = new Map();\r\n    }\r\n    /**\r\n     *\r\n     * @param component Component being added\r\n     * @param overwrite When a component with the same name has already been registered,\r\n     * if overwrite is true: overwrite the existing component with the new component and create a new\r\n     * provider with the new component. It can be useful in tests where you want to use different mocks\r\n     * for different tests.\r\n     * if overwrite is false: throw an exception\r\n     */\r\n    addComponent(component) {\r\n        const provider = this.getProvider(component.name);\r\n        if (provider.isComponentSet()) {\r\n            throw new Error(`Component ${component.name} has already been registered with ${this.name}`);\r\n        }\r\n        provider.setComponent(component);\r\n    }\r\n    addOrOverwriteComponent(component) {\r\n        const provider = this.getProvider(component.name);\r\n        if (provider.isComponentSet()) {\r\n            // delete the existing provider from the container, so we can register the new component\r\n            this.providers.delete(component.name);\r\n        }\r\n        this.addComponent(component);\r\n    }\r\n    /**\r\n     * getProvider provides a type safe interface where it can only be called with a field name\r\n     * present in NameServiceMapping interface.\r\n     *\r\n     * Firebase SDKs providing services should extend NameServiceMapping interface to register\r\n     * themselves.\r\n     */\r\n    getProvider(name) {\r\n        if (this.providers.has(name)) {\r\n            return this.providers.get(name);\r\n        }\r\n        // create a Provider for a service that hasn't registered with Firebase\r\n        const provider = new Provider(name, this);\r\n        this.providers.set(name, provider);\r\n        return provider;\r\n    }\r\n    getProviders() {\r\n        return Array.from(this.providers.values());\r\n    }\r\n}\n\nexport { Component, ComponentContainer, Provider };\n"], "mappings": ";AAAA,SAASA,QAAQ,QAAQ,gBAAgB;;AAEzC;AACA;AACA;AACA,MAAMC,SAAS,CAAC;EACZ;AACJ;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAACC,IAAI,EAAEC,eAAe,EAAEC,IAAI,EAAE;IACrC,IAAI,CAACF,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACC,iBAAiB,GAAG,KAAK;IAC9B;AACR;AACA;IACQ,IAAI,CAACC,YAAY,GAAG,CAAC,CAAC;IACtB,IAAI,CAACC,iBAAiB,GAAG,MAAM,CAAC;IAChC,IAAI,CAACC,iBAAiB,GAAG,IAAI;EACjC;EACAC,oBAAoBA,CAACC,IAAI,EAAE;IACvB,IAAI,CAACH,iBAAiB,GAAGG,IAAI;IAC7B,OAAO,IAAI;EACf;EACAC,oBAAoBA,CAACN,iBAAiB,EAAE;IACpC,IAAI,CAACA,iBAAiB,GAAGA,iBAAiB;IAC1C,OAAO,IAAI;EACf;EACAO,eAAeA,CAACC,KAAK,EAAE;IACnB,IAAI,CAACP,YAAY,GAAGO,KAAK;IACzB,OAAO,IAAI;EACf;EACAC,0BAA0BA,CAACC,QAAQ,EAAE;IACjC,IAAI,CAACP,iBAAiB,GAAGO,QAAQ;IACjC,OAAO,IAAI;EACf;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,kBAAkB,GAAG,WAAW;;AAEtC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,QAAQ,CAAC;EACXhB,WAAWA,CAACC,IAAI,EAAEgB,SAAS,EAAE;IACzB,IAAI,CAAChB,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACgB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,SAAS,GAAG,IAAI;IACrB,IAAI,CAACC,SAAS,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC1B,IAAI,CAACC,iBAAiB,GAAG,IAAID,GAAG,CAAC,CAAC;IAClC,IAAI,CAACE,gBAAgB,GAAG,IAAIF,GAAG,CAAC,CAAC;IACjC,IAAI,CAACG,eAAe,GAAG,IAAIH,GAAG,CAAC,CAAC;EACpC;EACA;AACJ;AACA;AACA;EACII,GAAGA,CAACC,UAAU,EAAE;IACZ;IACA,MAAMC,oBAAoB,GAAG,IAAI,CAACC,2BAA2B,CAACF,UAAU,CAAC;IACzE,IAAI,CAAC,IAAI,CAACJ,iBAAiB,CAACO,GAAG,CAACF,oBAAoB,CAAC,EAAE;MACnD,MAAMG,QAAQ,GAAG,IAAI/B,QAAQ,CAAC,CAAC;MAC/B,IAAI,CAACuB,iBAAiB,CAACS,GAAG,CAACJ,oBAAoB,EAAEG,QAAQ,CAAC;MAC1D,IAAI,IAAI,CAACE,aAAa,CAACL,oBAAoB,CAAC,IACxC,IAAI,CAACM,oBAAoB,CAAC,CAAC,EAAE;QAC7B;QACA,IAAI;UACA,MAAMC,QAAQ,GAAG,IAAI,CAACC,sBAAsB,CAAC;YACzCC,kBAAkB,EAAET;UACxB,CAAC,CAAC;UACF,IAAIO,QAAQ,EAAE;YACVJ,QAAQ,CAACO,OAAO,CAACH,QAAQ,CAAC;UAC9B;QACJ,CAAC,CACD,OAAOI,CAAC,EAAE;UACN;UACA;QAAA;MAER;IACJ;IACA,OAAO,IAAI,CAAChB,iBAAiB,CAACG,GAAG,CAACE,oBAAoB,CAAC,CAACY,OAAO;EACnE;EACAC,YAAYA,CAACC,OAAO,EAAE;IAClB,IAAIC,EAAE;IACN;IACA,MAAMf,oBAAoB,GAAG,IAAI,CAACC,2BAA2B,CAACa,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACf,UAAU,CAAC;IACnI,MAAMiB,QAAQ,GAAG,CAACD,EAAE,GAAGD,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACE,QAAQ,MAAM,IAAI,IAAID,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,KAAK;IACjI,IAAI,IAAI,CAACV,aAAa,CAACL,oBAAoB,CAAC,IACxC,IAAI,CAACM,oBAAoB,CAAC,CAAC,EAAE;MAC7B,IAAI;QACA,OAAO,IAAI,CAACE,sBAAsB,CAAC;UAC/BC,kBAAkB,EAAET;QACxB,CAAC,CAAC;MACN,CAAC,CACD,OAAOW,CAAC,EAAE;QACN,IAAIK,QAAQ,EAAE;UACV,OAAO,IAAI;QACf,CAAC,MACI;UACD,MAAML,CAAC;QACX;MACJ;IACJ,CAAC,MACI;MACD;MACA,IAAIK,QAAQ,EAAE;QACV,OAAO,IAAI;MACf,CAAC,MACI;QACD,MAAMC,KAAK,CAAC,WAAW,IAAI,CAAC1C,IAAI,mBAAmB,CAAC;MACxD;IACJ;EACJ;EACA2C,YAAYA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC1B,SAAS;EACzB;EACA2B,YAAYA,CAAC3B,SAAS,EAAE;IACpB,IAAIA,SAAS,CAACjB,IAAI,KAAK,IAAI,CAACA,IAAI,EAAE;MAC9B,MAAM0C,KAAK,CAAC,yBAAyBzB,SAAS,CAACjB,IAAI,iBAAiB,IAAI,CAACA,IAAI,GAAG,CAAC;IACrF;IACA,IAAI,IAAI,CAACiB,SAAS,EAAE;MAChB,MAAMyB,KAAK,CAAC,iBAAiB,IAAI,CAAC1C,IAAI,4BAA4B,CAAC;IACvE;IACA,IAAI,CAACiB,SAAS,GAAGA,SAAS;IAC1B;IACA,IAAI,CAAC,IAAI,CAACc,oBAAoB,CAAC,CAAC,EAAE;MAC9B;IACJ;IACA;IACA,IAAIc,gBAAgB,CAAC5B,SAAS,CAAC,EAAE;MAC7B,IAAI;QACA,IAAI,CAACgB,sBAAsB,CAAC;UAAEC,kBAAkB,EAAEpB;QAAmB,CAAC,CAAC;MAC3E,CAAC,CACD,OAAOsB,CAAC,EAAE;QACN;QACA;QACA;QACA;MAAA;IAER;IACA;IACA;IACA;IACA,KAAK,MAAM,CAACF,kBAAkB,EAAEY,gBAAgB,CAAC,IAAI,IAAI,CAAC1B,iBAAiB,CAAC2B,OAAO,CAAC,CAAC,EAAE;MACnF,MAAMtB,oBAAoB,GAAG,IAAI,CAACC,2BAA2B,CAACQ,kBAAkB,CAAC;MACjF,IAAI;QACA;QACA,MAAMF,QAAQ,GAAG,IAAI,CAACC,sBAAsB,CAAC;UACzCC,kBAAkB,EAAET;QACxB,CAAC,CAAC;QACFqB,gBAAgB,CAACX,OAAO,CAACH,QAAQ,CAAC;MACtC,CAAC,CACD,OAAOI,CAAC,EAAE;QACN;QACA;MAAA;IAER;EACJ;EACAY,aAAaA,CAACxB,UAAU,GAAGV,kBAAkB,EAAE;IAC3C,IAAI,CAACM,iBAAiB,CAAC6B,MAAM,CAACzB,UAAU,CAAC;IACzC,IAAI,CAACH,gBAAgB,CAAC4B,MAAM,CAACzB,UAAU,CAAC;IACxC,IAAI,CAACN,SAAS,CAAC+B,MAAM,CAACzB,UAAU,CAAC;EACrC;EACA;EACA;EACMyB,MAAMA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACX,MAAMC,QAAQ,GAAGC,KAAK,CAACC,IAAI,CAACJ,KAAI,CAAChC,SAAS,CAACqC,MAAM,CAAC,CAAC,CAAC;MACpD,MAAMC,OAAO,CAACC,GAAG,CAAC,CACd,GAAGL,QAAQ,CACNM,MAAM,CAACC,OAAO,IAAI,UAAU,IAAIA,OAAO,CAAC,CAAC;MAC1C;MAAA,CACCC,GAAG,CAACD,OAAO,IAAIA,OAAO,CAACE,QAAQ,CAACZ,MAAM,CAAC,CAAC,CAAC,EAC9C,GAAGG,QAAQ,CACNM,MAAM,CAACC,OAAO,IAAI,SAAS,IAAIA,OAAO,CAAC,CAAC;MACzC;MAAA,CACCC,GAAG,CAACD,OAAO,IAAIA,OAAO,CAACG,OAAO,CAAC,CAAC,CAAC,CACzC,CAAC;IAAC;EACP;EACAC,cAAcA,CAAA,EAAG;IACb,OAAO,IAAI,CAAC9C,SAAS,IAAI,IAAI;EACjC;EACAa,aAAaA,CAACN,UAAU,GAAGV,kBAAkB,EAAE;IAC3C,OAAO,IAAI,CAACI,SAAS,CAACS,GAAG,CAACH,UAAU,CAAC;EACzC;EACAwC,UAAUA,CAACxC,UAAU,GAAGV,kBAAkB,EAAE;IACxC,OAAO,IAAI,CAACO,gBAAgB,CAACE,GAAG,CAACC,UAAU,CAAC,IAAI,CAAC,CAAC;EACtD;EACAyC,UAAUA,CAACC,IAAI,GAAG,CAAC,CAAC,EAAE;IAClB,MAAM;MAAE3B,OAAO,GAAG,CAAC;IAAE,CAAC,GAAG2B,IAAI;IAC7B,MAAMzC,oBAAoB,GAAG,IAAI,CAACC,2BAA2B,CAACwC,IAAI,CAAChC,kBAAkB,CAAC;IACtF,IAAI,IAAI,CAACJ,aAAa,CAACL,oBAAoB,CAAC,EAAE;MAC1C,MAAMiB,KAAK,CAAC,GAAG,IAAI,CAAC1C,IAAI,IAAIyB,oBAAoB,gCAAgC,CAAC;IACrF;IACA,IAAI,CAAC,IAAI,CAACsC,cAAc,CAAC,CAAC,EAAE;MACxB,MAAMrB,KAAK,CAAC,aAAa,IAAI,CAAC1C,IAAI,8BAA8B,CAAC;IACrE;IACA,MAAMgC,QAAQ,GAAG,IAAI,CAACC,sBAAsB,CAAC;MACzCC,kBAAkB,EAAET,oBAAoB;MACxCc;IACJ,CAAC,CAAC;IACF;IACA,KAAK,MAAM,CAACL,kBAAkB,EAAEY,gBAAgB,CAAC,IAAI,IAAI,CAAC1B,iBAAiB,CAAC2B,OAAO,CAAC,CAAC,EAAE;MACnF,MAAMoB,4BAA4B,GAAG,IAAI,CAACzC,2BAA2B,CAACQ,kBAAkB,CAAC;MACzF,IAAIT,oBAAoB,KAAK0C,4BAA4B,EAAE;QACvDrB,gBAAgB,CAACX,OAAO,CAACH,QAAQ,CAAC;MACtC;IACJ;IACA,OAAOA,QAAQ;EACnB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;EACIoC,MAAMA,CAACvD,QAAQ,EAAEW,UAAU,EAAE;IACzB,IAAIgB,EAAE;IACN,MAAMf,oBAAoB,GAAG,IAAI,CAACC,2BAA2B,CAACF,UAAU,CAAC;IACzE,MAAM6C,iBAAiB,GAAG,CAAC7B,EAAE,GAAG,IAAI,CAAClB,eAAe,CAACC,GAAG,CAACE,oBAAoB,CAAC,MAAM,IAAI,IAAIe,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAG,IAAI8B,GAAG,CAAC,CAAC;IAC1HD,iBAAiB,CAACE,GAAG,CAAC1D,QAAQ,CAAC;IAC/B,IAAI,CAACS,eAAe,CAACO,GAAG,CAACJ,oBAAoB,EAAE4C,iBAAiB,CAAC;IACjE,MAAMG,gBAAgB,GAAG,IAAI,CAACtD,SAAS,CAACK,GAAG,CAACE,oBAAoB,CAAC;IACjE,IAAI+C,gBAAgB,EAAE;MAClB3D,QAAQ,CAAC2D,gBAAgB,EAAE/C,oBAAoB,CAAC;IACpD;IACA,OAAO,MAAM;MACT4C,iBAAiB,CAACpB,MAAM,CAACpC,QAAQ,CAAC;IACtC,CAAC;EACL;EACA;AACJ;AACA;AACA;EACI4D,qBAAqBA,CAACzC,QAAQ,EAAER,UAAU,EAAE;IACxC,MAAMkD,SAAS,GAAG,IAAI,CAACpD,eAAe,CAACC,GAAG,CAACC,UAAU,CAAC;IACtD,IAAI,CAACkD,SAAS,EAAE;MACZ;IACJ;IACA,KAAK,MAAM7D,QAAQ,IAAI6D,SAAS,EAAE;MAC9B,IAAI;QACA7D,QAAQ,CAACmB,QAAQ,EAAER,UAAU,CAAC;MAClC,CAAC,CACD,OAAOgB,EAAE,EAAE;QACP;MAAA;IAER;EACJ;EACAP,sBAAsBA,CAAC;IAAEC,kBAAkB;IAAEK,OAAO,GAAG,CAAC;EAAE,CAAC,EAAE;IACzD,IAAIP,QAAQ,GAAG,IAAI,CAACd,SAAS,CAACK,GAAG,CAACW,kBAAkB,CAAC;IACrD,IAAI,CAACF,QAAQ,IAAI,IAAI,CAACf,SAAS,EAAE;MAC7Be,QAAQ,GAAG,IAAI,CAACf,SAAS,CAAChB,eAAe,CAAC,IAAI,CAACe,SAAS,EAAE;QACtDkB,kBAAkB,EAAEyC,6BAA6B,CAACzC,kBAAkB,CAAC;QACrEK;MACJ,CAAC,CAAC;MACF,IAAI,CAACrB,SAAS,CAACW,GAAG,CAACK,kBAAkB,EAAEF,QAAQ,CAAC;MAChD,IAAI,CAACX,gBAAgB,CAACQ,GAAG,CAACK,kBAAkB,EAAEK,OAAO,CAAC;MACtD;AACZ;AACA;AACA;AACA;MACY,IAAI,CAACkC,qBAAqB,CAACzC,QAAQ,EAAEE,kBAAkB,CAAC;MACxD;AACZ;AACA;AACA;AACA;MACY,IAAI,IAAI,CAACjB,SAAS,CAACX,iBAAiB,EAAE;QAClC,IAAI;UACA,IAAI,CAACW,SAAS,CAACX,iBAAiB,CAAC,IAAI,CAACU,SAAS,EAAEkB,kBAAkB,EAAEF,QAAQ,CAAC;QAClF,CAAC,CACD,OAAOQ,EAAE,EAAE;UACP;QAAA;MAER;IACJ;IACA,OAAOR,QAAQ,IAAI,IAAI;EAC3B;EACAN,2BAA2BA,CAACF,UAAU,GAAGV,kBAAkB,EAAE;IACzD,IAAI,IAAI,CAACG,SAAS,EAAE;MAChB,OAAO,IAAI,CAACA,SAAS,CAACd,iBAAiB,GAAGqB,UAAU,GAAGV,kBAAkB;IAC7E,CAAC,MACI;MACD,OAAOU,UAAU,CAAC,CAAC;IACvB;EACJ;EACAO,oBAAoBA,CAAA,EAAG;IACnB,OAAQ,CAAC,CAAC,IAAI,CAACd,SAAS,IACpB,IAAI,CAACA,SAAS,CAACZ,iBAAiB,KAAK,UAAU,CAAC;EACxD;AACJ;AACA;AACA,SAASsE,6BAA6BA,CAACnD,UAAU,EAAE;EAC/C,OAAOA,UAAU,KAAKV,kBAAkB,GAAG8D,SAAS,GAAGpD,UAAU;AACrE;AACA,SAASqB,gBAAgBA,CAAC5B,SAAS,EAAE;EACjC,OAAOA,SAAS,CAACZ,iBAAiB,KAAK,OAAO,CAAC;AACnD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMwE,kBAAkB,CAAC;EACrB9E,WAAWA,CAACC,IAAI,EAAE;IACd,IAAI,CAACA,IAAI,GAAGA,IAAI;IAChB,IAAI,CAAC8E,SAAS,GAAG,IAAI3D,GAAG,CAAC,CAAC;EAC9B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI4D,YAAYA,CAAC9D,SAAS,EAAE;IACpB,MAAM+D,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAChE,SAAS,CAACjB,IAAI,CAAC;IACjD,IAAIgF,QAAQ,CAACjB,cAAc,CAAC,CAAC,EAAE;MAC3B,MAAM,IAAIrB,KAAK,CAAC,aAAazB,SAAS,CAACjB,IAAI,qCAAqC,IAAI,CAACA,IAAI,EAAE,CAAC;IAChG;IACAgF,QAAQ,CAACpC,YAAY,CAAC3B,SAAS,CAAC;EACpC;EACAiE,uBAAuBA,CAACjE,SAAS,EAAE;IAC/B,MAAM+D,QAAQ,GAAG,IAAI,CAACC,WAAW,CAAChE,SAAS,CAACjB,IAAI,CAAC;IACjD,IAAIgF,QAAQ,CAACjB,cAAc,CAAC,CAAC,EAAE;MAC3B;MACA,IAAI,CAACe,SAAS,CAAC7B,MAAM,CAAChC,SAAS,CAACjB,IAAI,CAAC;IACzC;IACA,IAAI,CAAC+E,YAAY,CAAC9D,SAAS,CAAC;EAChC;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIgE,WAAWA,CAACjF,IAAI,EAAE;IACd,IAAI,IAAI,CAAC8E,SAAS,CAACnD,GAAG,CAAC3B,IAAI,CAAC,EAAE;MAC1B,OAAO,IAAI,CAAC8E,SAAS,CAACvD,GAAG,CAACvB,IAAI,CAAC;IACnC;IACA;IACA,MAAMgF,QAAQ,GAAG,IAAIjE,QAAQ,CAACf,IAAI,EAAE,IAAI,CAAC;IACzC,IAAI,CAAC8E,SAAS,CAACjD,GAAG,CAAC7B,IAAI,EAAEgF,QAAQ,CAAC;IAClC,OAAOA,QAAQ;EACnB;EACAG,YAAYA,CAAA,EAAG;IACX,OAAO9B,KAAK,CAACC,IAAI,CAAC,IAAI,CAACwB,SAAS,CAACvB,MAAM,CAAC,CAAC,CAAC;EAC9C;AACJ;AAEA,SAASzD,SAAS,EAAE+E,kBAAkB,EAAE9D,QAAQ", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}