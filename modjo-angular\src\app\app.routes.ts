import { Routes } from '@angular/router';
import { AuthGuard } from './auth/auth.guard';
import { UserRole } from './models/user.model';

export const routes: Routes = [
  { path: '', redirectTo: '/home', pathMatch: 'full' },
  {
    path: 'home',
    loadComponent: () => import('./home/<USER>').then(m => m.HomeComponent)
  },
  {
    path: 'login',
    loadComponent: () => import('./auth/login/login.component').then(m => m.LoginComponent)
  },
  {
    path: 'register',
    loadComponent: () => import('./auth/register/register.component').then(m => m.RegisterComponent)
  },
  {
    path: 'profile',
    loadComponent: () => import('./user/profile/profile.component').then(m => m.ProfileComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'scanner',
    loadComponent: () => import('./scanner/scanner/scanner.component').then(m => m.ScannerComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'profile-view/:id',
    loadComponent: () => import('./scanner/profile-view/profile-view.component').then(m => m.ProfileViewComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'rewards',
    loadComponent: () => import('./rewards/rewards-list/rewards-list.component').then(m => m.RewardsListComponent),
    canActivate: [AuthGuard]
  },
  {
    path: 'validator-dashboard',
    loadComponent: () => import('./validation/validator-dashboard/validator-dashboard.component').then(m => m.ValidatorDashboardComponent),
    canActivate: [AuthGuard],
    data: { roles: [UserRole.VALIDATOR, UserRole.ADMIN] }
  },
  {
    path: 'admin',
    loadComponent: () => import('./admin/dashboard/dashboard.component').then(m => m.DashboardComponent),
    canActivate: [AuthGuard],
    data: { roles: [UserRole.ADMIN] }
  },
  { path: '**', redirectTo: '/home' }
];
