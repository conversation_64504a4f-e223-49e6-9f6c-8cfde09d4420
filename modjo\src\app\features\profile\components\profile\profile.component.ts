import { Component, OnInit } from '@angular/core';
import { Router } from '@angular/router';
import { AuthService } from '../../../../core/services/auth.service';
import { UserService } from '../../../../core/services/user.service';
import { User, PointsTransaction } from '../../../../core/models';

@Component({
  selector: 'app-profile',
  templateUrl: './profile.component.html',
  styleUrls: ['./profile.component.css']
})
export class ProfileComponent implements OnInit {
  user: User | null = null;
  userStats: any = null;
  recentTransactions: PointsTransaction[] = [];
  isLoading = true;

  constructor(
    private authService: AuthService,
    private userService: UserService,
    private router: Router
  ) {}

  ngOnInit(): void {
    this.loadUserData();
  }

  private loadUserData(): void {
    this.authService.currentUser$.subscribe(async user => {
      if (user) {
        this.user = user;
        await this.loadUserStats();
        await this.loadRecentTransactions();
      }
      this.isLoading = false;
    });
  }

  private async loadUserStats(): Promise<void> {
    if (!this.user) return;
    
    try {
      this.userService.getUserStats(this.user.uid).subscribe(stats => {
        this.userStats = stats;
      });
    } catch (error) {
      console.error('Error loading user stats:', error);
    }
  }

  private async loadRecentTransactions(): Promise<void> {
    if (!this.user) return;
    
    try {
      this.userService.getUserTransactions(this.user.uid, 10).subscribe(transactions => {
        this.recentTransactions = transactions;
      });
    } catch (error) {
      console.error('Error loading transactions:', error);
    }
  }

  editProfile(): void {
    this.router.navigate(['/profile/edit']);
  }

  getRoleDisplayName(role: string): string {
    const roleNames: { [key: string]: string } = {
      'user': 'Utilisateur',
      'provider': 'Partenaire',
      'validator': 'Validateur',
      'admin': 'Administrateur'
    };
    return roleNames[role] || role;
  }

  getRoleBadgeColor(role: string): string {
    const colors: { [key: string]: string } = {
      'user': 'primary',
      'provider': 'accent',
      'validator': 'warn',
      'admin': 'warn'
    };
    return colors[role] || 'primary';
  }

  getTransactionIcon(type: string): string {
    const iconMap: { [key: string]: string } = {
      'earned': 'add_circle',
      'spent': 'remove_circle',
      'transferred': 'swap_horiz',
      'validated': 'verified',
      'bonus': 'star'
    };
    return iconMap[type] || 'circle';
  }

  getTransactionColor(type: string): string {
    const colorMap: { [key: string]: string } = {
      'earned': 'success',
      'spent': 'warn',
      'transferred': 'primary',
      'validated': 'accent',
      'bonus': 'accent'
    };
    return colorMap[type] || 'primary';
  }

  formatDate(date: Date): string {
    return new Date(date).toLocaleDateString('fr-FR', {
      day: '2-digit',
      month: '2-digit',
      year: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  }

  getMemberSince(): string {
    if (!this.user?.createdAt) return '';
    
    const createdDate = new Date(this.user.createdAt);
    return createdDate.toLocaleDateString('fr-FR', {
      month: 'long',
      year: 'numeric'
    });
  }

  getActivityLevel(): string {
    if (!this.userStats) return 'Nouveau';
    
    const transactionCount = this.userStats.transactionCount || 0;
    
    if (transactionCount >= 50) return 'Expert';
    if (transactionCount >= 20) return 'Actif';
    if (transactionCount >= 5) return 'Régulier';
    return 'Débutant';
  }

  getActivityLevelColor(): string {
    const level = this.getActivityLevel();
    const colors: { [key: string]: string } = {
      'Expert': '#4caf50',
      'Actif': '#2196f3',
      'Régulier': '#ff9800',
      'Débutant': '#9e9e9e',
      'Nouveau': '#9e9e9e'
    };
    return colors[level] || '#9e9e9e';
  }

  viewAllTransactions(): void {
    // Navigate to full transaction history
    this.router.navigate(['/profile'], { fragment: 'history' });
  }

  shareProfile(): void {
    if (navigator.share) {
      navigator.share({
        title: `Profil Modjo de ${this.user?.name}`,
        text: `Découvrez mon profil sur Modjo - ${this.user?.points} points!`,
        url: window.location.href
      }).catch(console.error);
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href).then(() => {
        console.log('Lien copié dans le presse-papiers');
      });
    }
  }

  downloadQRCode(): void {
    // Generate and download user QR code
    console.log('Téléchargement du QR code utilisateur...');
    // This would generate a QR code with user ID for scanning
  }

  exportData(): void {
    // Export user data
    console.log('Export des données utilisateur...');
    // This would export user transaction history and stats
  }
}
