{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { Validators } from '@angular/forms';\nimport { MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { TransactionType } from '../../../../core/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/forms\";\nimport * as i2 from \"../../../../core/services/user.service\";\nimport * as i3 from \"../../../../core/services/auth.service\";\nimport * as i4 from \"@angular/material/snack-bar\";\nimport * as i5 from \"@angular/material/dialog\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/button\";\nimport * as i8 from \"@angular/material/icon\";\nimport * as i9 from \"@angular/material/form-field\";\nimport * as i10 from \"@angular/material/input\";\nimport * as i11 from \"@angular/material/select\";\nimport * as i12 from \"@angular/material/core\";\nimport * as i13 from \"@angular/material/progress-spinner\";\nfunction PointsAssignmentComponent_mat_option_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-option\", 23)(1, \"div\", 24)(2, \"span\", 25);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"span\", 26);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const option_r1 = ctx.$implicit;\n    i0.ɵɵproperty(\"value\", option_r1.value);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(option_r1.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(option_r1.description);\n  }\n}\nfunction PointsAssignmentComponent_mat_form_field_24_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-form-field\", 6)(1, \"mat-label\");\n    i0.ɵɵtext(2, \"Utilisateur cible\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelement(3, \"input\", 27);\n    i0.ɵɵelementStart(4, \"mat-icon\", 28);\n    i0.ɵɵtext(5, \"person\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction PointsAssignmentComponent_div_33_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵpipe(5, \"date\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Scann\\u00E9: \", i0.ɵɵpipeBind2(5, 1, ctx_r1.data.qrData.timestamp, \"short\"), \"\");\n  }\n}\nfunction PointsAssignmentComponent_div_34_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 12)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"task_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"Action: \", ctx_r1.data.qrData.action, \"\");\n  }\n}\nfunction PointsAssignmentComponent_mat_spinner_48_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelement(0, \"mat-spinner\", 29);\n  }\n}\nfunction PointsAssignmentComponent_span_49_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\");\n    i0.ɵɵtext(1, \"Attribuer les points\");\n    i0.ɵɵelementEnd();\n  }\n}\nexport class PointsAssignmentComponent {\n  constructor(fb, userService, authService, snackBar, dialogRef, data) {\n    this.fb = fb;\n    this.userService = userService;\n    this.authService = authService;\n    this.snackBar = snackBar;\n    this.dialogRef = dialogRef;\n    this.data = data;\n    this.isLoading = false;\n    this.pointsOptions = [{\n      value: 1,\n      label: '1 point',\n      description: 'Action simple'\n    }, {\n      value: 3,\n      label: '3 points',\n      description: 'Action modérée'\n    }, {\n      value: 5,\n      label: '5 points',\n      description: 'Action importante'\n    }, {\n      value: 10,\n      label: '10 points',\n      description: 'Action majeure'\n    }];\n    this.assignmentForm = this.fb.group({\n      points: [this.getDefaultPoints(), [Validators.required, Validators.min(1)]],\n      description: [this.getDefaultDescription(), Validators.required],\n      targetUserId: [this.getTargetUserId()]\n    });\n  }\n  ngOnInit() {\n    // Auto-fill form based on QR scan result\n    this.prefillForm();\n  }\n  getDefaultPoints() {\n    if (this.data.scanResult?.points) {\n      return this.data.scanResult.points;\n    }\n    switch (this.data.qrData?.type) {\n      case 'user_transfer':\n        return 1;\n      case 'validator_action':\n        return 10;\n      case 'partner_reward':\n        return 5;\n      case 'system_bonus':\n        return 3;\n      default:\n        return 1;\n    }\n  }\n  getDefaultDescription() {\n    if (this.data.scanResult?.message) {\n      return this.data.scanResult.message;\n    }\n    switch (this.data.qrData?.type) {\n      case 'user_transfer':\n        return 'Transfert de points entre utilisateurs';\n      case 'validator_action':\n        return `Action validée: ${this.data.qrData.action || 'activité communautaire'}`;\n      case 'partner_reward':\n        return 'Récompense partenaire scannée';\n      case 'system_bonus':\n        return 'Bonus système activé';\n      default:\n        return 'Points gagnés via QR code';\n    }\n  }\n  getTargetUserId() {\n    return this.data.qrData?.userId || this.data.currentUser?.uid || '';\n  }\n  prefillForm() {\n    // Additional logic to prefill form based on scan type\n    if (this.data.qrData?.type === 'user_transfer' && this.data.qrData.userId) {\n      // For user transfers, we might want to show the target user info\n      this.assignmentForm.patchValue({\n        description: `Transfert vers utilisateur ${this.data.qrData.userId}`\n      });\n    }\n  }\n  onSubmit() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      if (_this.assignmentForm.invalid) {\n        _this.markFormGroupTouched();\n        return;\n      }\n      _this.isLoading = true;\n      const formValue = _this.assignmentForm.value;\n      try {\n        const currentUser = _this.authService.getCurrentUser();\n        if (!currentUser) {\n          throw new Error('Utilisateur non connecté');\n        }\n        // Determine transaction type based on QR code type\n        const transactionType = _this.getTransactionType();\n        // Add points to the target user (usually current user)\n        const targetUserId = formValue.targetUserId || currentUser.uid;\n        yield _this.userService.addPointsToUser(targetUserId, formValue.points, transactionType, formValue.description, _this.data.qrData?.validatorId || currentUser.uid);\n        _this.snackBar.open(`${formValue.points} point(s) attribué(s) avec succès!`, 'Fermer', {\n          duration: 3000\n        });\n        _this.dialogRef.close(true);\n      } catch (error) {\n        console.error('Error assigning points:', error);\n        _this.snackBar.open('Erreur lors de l\\'attribution des points', 'Fermer', {\n          duration: 5000\n        });\n      } finally {\n        _this.isLoading = false;\n      }\n    })();\n  }\n  getTransactionType() {\n    switch (this.data.qrData?.type) {\n      case 'validator_action':\n        return TransactionType.VALIDATED;\n      case 'partner_reward':\n        return TransactionType.EARNED;\n      case 'system_bonus':\n        return TransactionType.BONUS;\n      case 'user_transfer':\n        return TransactionType.TRANSFERRED;\n      default:\n        return TransactionType.EARNED;\n    }\n  }\n  markFormGroupTouched() {\n    Object.keys(this.assignmentForm.controls).forEach(key => {\n      const control = this.assignmentForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n  getFieldError(fieldName) {\n    const field = this.assignmentForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) {\n        return 'Champ requis';\n      }\n      if (field.errors['min']) {\n        return 'Minimum 1 point';\n      }\n    }\n    return '';\n  }\n  onCancel() {\n    this.dialogRef.close(false);\n  }\n  getScanTypeLabel() {\n    const typeLabels = {\n      'user_transfer': 'Transfert utilisateur',\n      'validator_action': 'Action validateur',\n      'partner_reward': 'Récompense partenaire',\n      'system_bonus': 'Bonus système'\n    };\n    return typeLabels[this.data.qrData?.type || ''] || 'Scan QR';\n  }\n  getScanTypeIcon() {\n    const typeIcons = {\n      'user_transfer': 'swap_horiz',\n      'validator_action': 'verified',\n      'partner_reward': 'card_giftcard',\n      'system_bonus': 'star'\n    };\n    return typeIcons[this.data.qrData?.type || ''] || 'qr_code';\n  }\n  static {\n    this.ɵfac = function PointsAssignmentComponent_Factory(t) {\n      return new (t || PointsAssignmentComponent)(i0.ɵɵdirectiveInject(i1.FormBuilder), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatSnackBar), i0.ɵɵdirectiveInject(i5.MatDialogRef), i0.ɵɵdirectiveInject(MAT_DIALOG_DATA));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: PointsAssignmentComponent,\n      selectors: [[\"app-points-assignment\"]],\n      decls: 50,\n      vars: 17,\n      consts: [[1, \"assignment-dialog\"], [1, \"dialog-header\"], [\"mat-dialog-title\", \"\"], [3, \"color\"], [1, \"scan-type\"], [1, \"assignment-form\", 3, \"formGroup\"], [\"appearance\", \"outline\", 1, \"full-width\"], [\"formControlName\", \"points\"], [3, \"value\", 4, \"ngFor\", \"ngForOf\"], [\"matInput\", \"\", \"formControlName\", \"description\", \"rows\", \"3\", \"placeholder\", \"D\\u00E9crivez l'action ou la raison de l'attribution\"], [\"appearance\", \"outline\", \"class\", \"full-width\", 4, \"ngIf\"], [1, \"scan-details\"], [1, \"detail-item\"], [\"class\", \"detail-item\", 4, \"ngIf\"], [1, \"points-preview\"], [1, \"preview-content\"], [1, \"preview-icon\"], [1, \"preview-text\"], [\"align\", \"end\"], [\"mat-button\", \"\", 3, \"click\", \"disabled\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\", \"disabled\"], [\"diameter\", \"20\", 4, \"ngIf\"], [4, \"ngIf\"], [3, \"value\"], [1, \"points-option\"], [1, \"points-value\"], [1, \"points-description\"], [\"matInput\", \"\", \"formControlName\", \"targetUserId\", \"readonly\", \"\", \"placeholder\", \"ID de l'utilisateur\"], [\"matSuffix\", \"\"], [\"diameter\", \"20\"]],\n      template: function PointsAssignmentComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 0)(1, \"div\", 1)(2, \"h2\", 2)(3, \"mat-icon\", 3);\n          i0.ɵɵtext(4);\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(5, \" Attribution de points \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(6, \"p\", 4);\n          i0.ɵɵtext(7);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(8, \"mat-dialog-content\")(9, \"form\", 5)(10, \"mat-form-field\", 6)(11, \"mat-label\");\n          i0.ɵɵtext(12, \"Nombre de points\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(13, \"mat-select\", 7);\n          i0.ɵɵtemplate(14, PointsAssignmentComponent_mat_option_14_Template, 6, 3, \"mat-option\", 8);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(15, \"mat-error\");\n          i0.ɵɵtext(16);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(17, \"mat-form-field\", 6)(18, \"mat-label\");\n          i0.ɵɵtext(19, \"Description\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(20, \"textarea\", 9);\n          i0.ɵɵtext(21, \"        \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(22, \"mat-error\");\n          i0.ɵɵtext(23);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(24, PointsAssignmentComponent_mat_form_field_24_Template, 6, 0, \"mat-form-field\", 10);\n          i0.ɵɵelementStart(25, \"div\", 11)(26, \"h4\");\n          i0.ɵɵtext(27, \"D\\u00E9tails du scan\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(28, \"div\", 12)(29, \"mat-icon\");\n          i0.ɵɵtext(30, \"qr_code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(31, \"span\");\n          i0.ɵɵtext(32);\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(33, PointsAssignmentComponent_div_33_Template, 6, 4, \"div\", 13)(34, PointsAssignmentComponent_div_34_Template, 5, 1, \"div\", 13);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"div\", 14)(36, \"div\", 15)(37, \"mat-icon\", 16);\n          i0.ɵɵtext(38, \"stars\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(39, \"div\", 17)(40, \"h3\");\n          i0.ɵɵtext(41);\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(42, \"p\");\n          i0.ɵɵtext(43);\n          i0.ɵɵelementEnd()()()()()();\n          i0.ɵɵelementStart(44, \"mat-dialog-actions\", 18)(45, \"button\", 19);\n          i0.ɵɵlistener(\"click\", function PointsAssignmentComponent_Template_button_click_45_listener() {\n            return ctx.onCancel();\n          });\n          i0.ɵɵtext(46, \" Annuler \");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(47, \"button\", 20);\n          i0.ɵɵlistener(\"click\", function PointsAssignmentComponent_Template_button_click_47_listener() {\n            return ctx.onSubmit();\n          });\n          i0.ɵɵtemplate(48, PointsAssignmentComponent_mat_spinner_48_Template, 1, 0, \"mat-spinner\", 21)(49, PointsAssignmentComponent_span_49_Template, 2, 0, \"span\", 22);\n          i0.ɵɵelementEnd()()();\n        }\n        if (rf & 2) {\n          let tmp_11_0;\n          i0.ɵɵadvance(3);\n          i0.ɵɵproperty(\"color\", \"primary\");\n          i0.ɵɵadvance();\n          i0.ɵɵtextInterpolate(ctx.getScanTypeIcon());\n          i0.ɵɵadvance(3);\n          i0.ɵɵtextInterpolate(ctx.getScanTypeLabel());\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"formGroup\", ctx.assignmentForm);\n          i0.ɵɵadvance(5);\n          i0.ɵɵproperty(\"ngForOf\", ctx.pointsOptions);\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"points\"));\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate(ctx.getFieldError(\"description\"));\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", (ctx.data.qrData == null ? null : ctx.data.qrData.type) === \"user_transfer\");\n          i0.ɵɵadvance(8);\n          i0.ɵɵtextInterpolate1(\"Type: \", ctx.getScanTypeLabel(), \"\");\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.data.qrData == null ? null : ctx.data.qrData.timestamp);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.data.qrData == null ? null : ctx.data.qrData.action);\n          i0.ɵɵadvance(7);\n          i0.ɵɵtextInterpolate1(\"\", ((tmp_11_0 = ctx.assignmentForm.get(\"points\")) == null ? null : tmp_11_0.value) || 0, \" point(s)\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵtextInterpolate1(\"seront attribu\\u00E9s \\u00E0 \", ctx.data.currentUser == null ? null : ctx.data.currentUser.name, \"\");\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading);\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"disabled\", ctx.isLoading || ctx.assignmentForm.invalid);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", ctx.isLoading);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.isLoading);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i1.ɵNgNoValidate, i1.DefaultValueAccessor, i1.NgControlStatus, i1.NgControlStatusGroup, i1.FormGroupDirective, i1.FormControlName, i7.MatButton, i8.MatIcon, i9.MatFormField, i9.MatLabel, i9.MatError, i9.MatSuffix, i10.MatInput, i11.MatSelect, i12.MatOption, i5.MatDialogTitle, i5.MatDialogActions, i5.MatDialogContent, i13.MatProgressSpinner, i6.DatePipe],\n      styles: [\".assignment-dialog[_ngcontent-%COMP%] {\\n  min-width: 400px;\\n  max-width: 500px;\\n}\\n\\n.dialog-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 16px;\\n}\\n\\n.dialog-header[_ngcontent-%COMP%]   h2[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 8px;\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n\\n.scan-type[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.assignment-form[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n}\\n\\n.full-width[_ngcontent-%COMP%] {\\n  width: 100%;\\n}\\n\\n.points-option[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  padding: 4px 0;\\n}\\n\\n.points-value[_ngcontent-%COMP%] {\\n  font-weight: 500;\\n  color: #333;\\n}\\n\\n.points-description[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #666;\\n  margin-top: 2px;\\n}\\n\\n.scan-details[_ngcontent-%COMP%] {\\n  background: #f5f5f5;\\n  border-radius: 8px;\\n  padding: 16px;\\n  margin: 16px 0;\\n}\\n\\n.scan-details[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  color: #333;\\n  font-size: 1rem;\\n  font-weight: 500;\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 8px;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n.detail-item[_ngcontent-%COMP%]:last-child {\\n  margin-bottom: 0;\\n}\\n\\n.detail-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n  color: #999;\\n}\\n\\n.points-preview[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  border-radius: 12px;\\n  padding: 16px;\\n  color: white;\\n  margin: 16px 0;\\n}\\n\\n.preview-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n}\\n\\n.preview-icon[_ngcontent-%COMP%] {\\n  font-size: 2.5rem;\\n  color: #ffd700;\\n}\\n\\n.preview-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 1.5rem;\\n  font-weight: 600;\\n}\\n\\n.preview-text[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  opacity: 0.9;\\n  font-size: 0.9rem;\\n}\\n\\nmat-dialog-actions[_ngcontent-%COMP%] {\\n  padding: 16px 0 0 0;\\n  margin: 0;\\n}\\n\\nmat-dialog-actions[_ngcontent-%COMP%]   button[_ngcontent-%COMP%] {\\n  margin-left: 8px;\\n}\\n\\n\\n\\n@media (max-width: 480px) {\\n  .assignment-dialog[_ngcontent-%COMP%] {\\n    min-width: 300px;\\n    max-width: 350px;\\n  }\\n  \\n  .preview-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    text-align: center;\\n    gap: 8px;\\n  }\\n  \\n  .preview-icon[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  \\n  .preview-text[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n    font-size: 1.3rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["Validators", "MAT_DIALOG_DATA", "TransactionType", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "option_r1", "value", "ɵɵadvance", "ɵɵtextInterpolate", "label", "description", "ɵɵelement", "ɵɵtextInterpolate1", "ɵɵpipeBind2", "ctx_r1", "data", "qrData", "timestamp", "action", "PointsAssignmentComponent", "constructor", "fb", "userService", "authService", "snackBar", "dialogRef", "isLoading", "pointsOptions", "assignmentForm", "group", "points", "getDefaultPoints", "required", "min", "getDefaultDescription", "targetUserId", "getTargetUserId", "ngOnInit", "prefillForm", "scanResult", "type", "message", "userId", "currentUser", "uid", "patchValue", "onSubmit", "_this", "_asyncToGenerator", "invalid", "markFormGroupTouched", "formValue", "getCurrentUser", "Error", "transactionType", "getTransactionType", "addPointsToUser", "validatorId", "open", "duration", "close", "error", "console", "VALIDATED", "EARNED", "BONUS", "TRANSFERRED", "Object", "keys", "controls", "for<PERSON>ach", "key", "control", "get", "<PERSON><PERSON><PERSON><PERSON>ched", "getFieldError", "fieldName", "field", "errors", "touched", "onCancel", "getScanTypeLabel", "typeLabels", "getScanTypeIcon", "typeIcons", "ɵɵdirectiveInject", "i1", "FormBuilder", "i2", "UserService", "i3", "AuthService", "i4", "MatSnackBar", "i5", "MatDialogRef", "selectors", "decls", "vars", "consts", "template", "PointsAssignmentComponent_Template", "rf", "ctx", "ɵɵtemplate", "PointsAssignmentComponent_mat_option_14_Template", "PointsAssignmentComponent_mat_form_field_24_Template", "PointsAssignmentComponent_div_33_Template", "PointsAssignmentComponent_div_34_Template", "ɵɵlistener", "PointsAssignmentComponent_Template_button_click_45_listener", "PointsAssignmentComponent_Template_button_click_47_listener", "PointsAssignmentComponent_mat_spinner_48_Template", "PointsAssignmentComponent_span_49_Template", "tmp_11_0", "name"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\qr-scanner\\components\\points-assignment\\points-assignment.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\qr-scanner\\components\\points-assignment\\points-assignment.component.html"], "sourcesContent": ["import { Component, Inject, OnInit } from '@angular/core';\nimport { <PERSON><PERSON><PERSON>er, FormGroup, Validators } from '@angular/forms';\nimport { MatDialogRef, MAT_DIALOG_DATA } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { UserService } from '../../../../core/services/user.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { QrCodeData, User, TransactionType } from '../../../../core/models';\n\ninterface DialogData {\n  scanResult: any;\n  qrData: QrCodeData;\n  currentUser: User;\n}\n\n@Component({\n  selector: 'app-points-assignment',\n  templateUrl: './points-assignment.component.html',\n  styleUrls: ['./points-assignment.component.css']\n})\nexport class PointsAssignmentComponent implements OnInit {\n  assignmentForm: FormGroup;\n  isLoading = false;\n  \n  pointsOptions = [\n    { value: 1, label: '1 point', description: 'Action simple' },\n    { value: 3, label: '3 points', description: 'Action modérée' },\n    { value: 5, label: '5 points', description: 'Action importante' },\n    { value: 10, label: '10 points', description: 'Action majeure' }\n  ];\n\n  constructor(\n    private fb: FormBuilder,\n    private userService: UserService,\n    private authService: AuthService,\n    private snackBar: MatSnackBar,\n    public dialogRef: MatDialogRef<PointsAssignmentComponent>,\n    @Inject(MAT_DIALOG_DATA) public data: DialogData\n  ) {\n    this.assignmentForm = this.fb.group({\n      points: [this.getDefaultPoints(), [Validators.required, Validators.min(1)]],\n      description: [this.getDefaultDescription(), Validators.required],\n      targetUserId: [this.getTargetUserId()]\n    });\n  }\n\n  ngOnInit(): void {\n    // Auto-fill form based on QR scan result\n    this.prefillForm();\n  }\n\n  private getDefaultPoints(): number {\n    if (this.data.scanResult?.points) {\n      return this.data.scanResult.points;\n    }\n    \n    switch (this.data.qrData?.type) {\n      case 'user_transfer':\n        return 1;\n      case 'validator_action':\n        return 10;\n      case 'partner_reward':\n        return 5;\n      case 'system_bonus':\n        return 3;\n      default:\n        return 1;\n    }\n  }\n\n  private getDefaultDescription(): string {\n    if (this.data.scanResult?.message) {\n      return this.data.scanResult.message;\n    }\n    \n    switch (this.data.qrData?.type) {\n      case 'user_transfer':\n        return 'Transfert de points entre utilisateurs';\n      case 'validator_action':\n        return `Action validée: ${this.data.qrData.action || 'activité communautaire'}`;\n      case 'partner_reward':\n        return 'Récompense partenaire scannée';\n      case 'system_bonus':\n        return 'Bonus système activé';\n      default:\n        return 'Points gagnés via QR code';\n    }\n  }\n\n  private getTargetUserId(): string {\n    return this.data.qrData?.userId || this.data.currentUser?.uid || '';\n  }\n\n  private prefillForm(): void {\n    // Additional logic to prefill form based on scan type\n    if (this.data.qrData?.type === 'user_transfer' && this.data.qrData.userId) {\n      // For user transfers, we might want to show the target user info\n      this.assignmentForm.patchValue({\n        description: `Transfert vers utilisateur ${this.data.qrData.userId}`\n      });\n    }\n  }\n\n  async onSubmit(): Promise<void> {\n    if (this.assignmentForm.invalid) {\n      this.markFormGroupTouched();\n      return;\n    }\n\n    this.isLoading = true;\n    const formValue = this.assignmentForm.value;\n\n    try {\n      const currentUser = this.authService.getCurrentUser();\n      if (!currentUser) {\n        throw new Error('Utilisateur non connecté');\n      }\n\n      // Determine transaction type based on QR code type\n      const transactionType = this.getTransactionType();\n      \n      // Add points to the target user (usually current user)\n      const targetUserId = formValue.targetUserId || currentUser.uid;\n      \n      await this.userService.addPointsToUser(\n        targetUserId,\n        formValue.points,\n        transactionType,\n        formValue.description,\n        this.data.qrData?.validatorId || currentUser.uid\n      );\n\n      this.snackBar.open(\n        `${formValue.points} point(s) attribué(s) avec succès!`, \n        'Fermer', \n        { duration: 3000 }\n      );\n\n      this.dialogRef.close(true);\n\n    } catch (error: any) {\n      console.error('Error assigning points:', error);\n      this.snackBar.open(\n        'Erreur lors de l\\'attribution des points', \n        'Fermer', \n        { duration: 5000 }\n      );\n    } finally {\n      this.isLoading = false;\n    }\n  }\n\n  private getTransactionType(): TransactionType {\n    switch (this.data.qrData?.type) {\n      case 'validator_action':\n        return TransactionType.VALIDATED;\n      case 'partner_reward':\n        return TransactionType.EARNED;\n      case 'system_bonus':\n        return TransactionType.BONUS;\n      case 'user_transfer':\n        return TransactionType.TRANSFERRED;\n      default:\n        return TransactionType.EARNED;\n    }\n  }\n\n  private markFormGroupTouched(): void {\n    Object.keys(this.assignmentForm.controls).forEach(key => {\n      const control = this.assignmentForm.get(key);\n      control?.markAsTouched();\n    });\n  }\n\n  getFieldError(fieldName: string): string {\n    const field = this.assignmentForm.get(fieldName);\n    if (field?.errors && field.touched) {\n      if (field.errors['required']) {\n        return 'Champ requis';\n      }\n      if (field.errors['min']) {\n        return 'Minimum 1 point';\n      }\n    }\n    return '';\n  }\n\n  onCancel(): void {\n    this.dialogRef.close(false);\n  }\n\n  getScanTypeLabel(): string {\n    const typeLabels: { [key: string]: string } = {\n      'user_transfer': 'Transfert utilisateur',\n      'validator_action': 'Action validateur',\n      'partner_reward': 'Récompense partenaire',\n      'system_bonus': 'Bonus système'\n    };\n    return typeLabels[this.data.qrData?.type || ''] || 'Scan QR';\n  }\n\n  getScanTypeIcon(): string {\n    const typeIcons: { [key: string]: string } = {\n      'user_transfer': 'swap_horiz',\n      'validator_action': 'verified',\n      'partner_reward': 'card_giftcard',\n      'system_bonus': 'star'\n    };\n    return typeIcons[this.data.qrData?.type || ''] || 'qr_code';\n  }\n}\n", "<div class=\"assignment-dialog\">\n  <div class=\"dialog-header\">\n    <h2 mat-dialog-title>\n      <mat-icon [color]=\"'primary'\">{{ getScanTypeIcon() }}</mat-icon>\n      Attribution de points\n    </h2>\n    <p class=\"scan-type\">{{ getScanTypeLabel() }}</p>\n  </div>\n\n  <mat-dialog-content>\n    <form [formGroup]=\"assignmentForm\" class=\"assignment-form\">\n      <!-- Points selection -->\n      <mat-form-field appearance=\"outline\" class=\"full-width\">\n        <mat-label>Nombre de points</mat-label>\n        <mat-select formControlName=\"points\">\n          <mat-option *ngFor=\"let option of pointsOptions\" [value]=\"option.value\">\n            <div class=\"points-option\">\n              <span class=\"points-value\">{{ option.label }}</span>\n              <span class=\"points-description\">{{ option.description }}</span>\n            </div>\n          </mat-option>\n        </mat-select>\n        <mat-error>{{ getFieldError('points') }}</mat-error>\n      </mat-form-field>\n\n      <!-- Description -->\n      <mat-form-field appearance=\"outline\" class=\"full-width\">\n        <mat-label>Description</mat-label>\n        <textarea matInput \n                  formControlName=\"description\"\n                  rows=\"3\"\n                  placeholder=\"Décrivez l'action ou la raison de l'attribution\">\n        </textarea>\n        <mat-error>{{ getFieldError('description') }}</mat-error>\n      </mat-form-field>\n\n      <!-- Target user (if applicable) -->\n      <mat-form-field appearance=\"outline\" class=\"full-width\" \n                      *ngIf=\"data.qrData?.type === 'user_transfer'\">\n        <mat-label>Utilisateur cible</mat-label>\n        <input matInput \n               formControlName=\"targetUserId\"\n               readonly\n               placeholder=\"ID de l'utilisateur\">\n        <mat-icon matSuffix>person</mat-icon>\n      </mat-form-field>\n\n      <!-- Scan details -->\n      <div class=\"scan-details\">\n        <h4>Détails du scan</h4>\n        <div class=\"detail-item\">\n          <mat-icon>qr_code</mat-icon>\n          <span>Type: {{ getScanTypeLabel() }}</span>\n        </div>\n        <div class=\"detail-item\" *ngIf=\"data.qrData?.timestamp\">\n          <mat-icon>schedule</mat-icon>\n          <span>Scanné: {{ data.qrData.timestamp | date:'short' }}</span>\n        </div>\n        <div class=\"detail-item\" *ngIf=\"data.qrData?.action\">\n          <mat-icon>task_alt</mat-icon>\n          <span>Action: {{ data.qrData.action }}</span>\n        </div>\n      </div>\n\n      <!-- Points preview -->\n      <div class=\"points-preview\">\n        <div class=\"preview-content\">\n          <mat-icon class=\"preview-icon\">stars</mat-icon>\n          <div class=\"preview-text\">\n            <h3>{{ assignmentForm.get('points')?.value || 0 }} point(s)</h3>\n            <p>seront attribués à {{ data.currentUser?.name }}</p>\n          </div>\n        </div>\n      </div>\n    </form>\n  </mat-dialog-content>\n\n  <mat-dialog-actions align=\"end\">\n    <button mat-button (click)=\"onCancel()\" [disabled]=\"isLoading\">\n      Annuler\n    </button>\n    <button mat-raised-button \n            color=\"primary\" \n            (click)=\"onSubmit()\"\n            [disabled]=\"isLoading || assignmentForm.invalid\">\n      <mat-spinner diameter=\"20\" *ngIf=\"isLoading\"></mat-spinner>\n      <span *ngIf=\"!isLoading\">Attribuer les points</span>\n    </button>\n  </mat-dialog-actions>\n</div>\n"], "mappings": ";AACA,SAAiCA,UAAU,QAAQ,gBAAgB;AACnE,SAAuBC,eAAe,QAAQ,0BAA0B;AAIxE,SAA2BC,eAAe,QAAQ,yBAAyB;;;;;;;;;;;;;;;;;ICW7DC,EAFJ,CAAAC,cAAA,qBAAwE,cAC3C,eACE;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACpDH,EAAA,CAAAC,cAAA,eAAiC;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAE7DF,EAF6D,CAAAG,YAAA,EAAO,EAC5D,EACK;;;;IALoCH,EAAA,CAAAI,UAAA,UAAAC,SAAA,CAAAC,KAAA,CAAsB;IAExCN,EAAA,CAAAO,SAAA,GAAkB;IAAlBP,EAAA,CAAAQ,iBAAA,CAAAH,SAAA,CAAAI,KAAA,CAAkB;IACZT,EAAA,CAAAO,SAAA,GAAwB;IAAxBP,EAAA,CAAAQ,iBAAA,CAAAH,SAAA,CAAAK,WAAA,CAAwB;;;;;IAqB/DV,EAFF,CAAAC,cAAA,wBAC8D,gBACjD;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAY;IACxCH,EAAA,CAAAW,SAAA,gBAGyC;IACzCX,EAAA,CAAAC,cAAA,mBAAoB;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAC5BF,EAD4B,CAAAG,YAAA,EAAW,EACtB;;;;;IAUbH,EADF,CAAAC,cAAA,cAAwD,eAC5C;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkD;;IAC1DF,EAD0D,CAAAG,YAAA,EAAO,EAC3D;;;;IADEH,EAAA,CAAAO,SAAA,GAAkD;IAAlDP,EAAA,CAAAY,kBAAA,kBAAAZ,EAAA,CAAAa,WAAA,OAAAC,MAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAC,SAAA,eAAkD;;;;;IAGxDjB,EADF,CAAAC,cAAA,cAAqD,eACzC;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgC;IACxCF,EADwC,CAAAG,YAAA,EAAO,EACzC;;;;IADEH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAAY,kBAAA,aAAAE,MAAA,CAAAC,IAAA,CAAAC,MAAA,CAAAE,MAAA,KAAgC;;;;;IAyB1ClB,EAAA,CAAAW,SAAA,sBAA2D;;;;;IAC3DX,EAAA,CAAAC,cAAA,WAAyB;IAAAD,EAAA,CAAAE,MAAA,2BAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;ADnE1D,OAAM,MAAOgB,yBAAyB;EAWpCC,YACUC,EAAe,EACfC,WAAwB,EACxBC,WAAwB,EACxBC,QAAqB,EACtBC,SAAkD,EACzBV,IAAgB;IALxC,KAAAM,EAAE,GAAFA,EAAE;IACF,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,QAAQ,GAARA,QAAQ;IACT,KAAAC,SAAS,GAATA,SAAS;IACgB,KAAAV,IAAI,GAAJA,IAAI;IAftC,KAAAW,SAAS,GAAG,KAAK;IAEjB,KAAAC,aAAa,GAAG,CACd;MAAErB,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE,SAAS;MAAEC,WAAW,EAAE;IAAe,CAAE,EAC5D;MAAEJ,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE,UAAU;MAAEC,WAAW,EAAE;IAAgB,CAAE,EAC9D;MAAEJ,KAAK,EAAE,CAAC;MAAEG,KAAK,EAAE,UAAU;MAAEC,WAAW,EAAE;IAAmB,CAAE,EACjE;MAAEJ,KAAK,EAAE,EAAE;MAAEG,KAAK,EAAE,WAAW;MAAEC,WAAW,EAAE;IAAgB,CAAE,CACjE;IAUC,IAAI,CAACkB,cAAc,GAAG,IAAI,CAACP,EAAE,CAACQ,KAAK,CAAC;MAClCC,MAAM,EAAE,CAAC,IAAI,CAACC,gBAAgB,EAAE,EAAE,CAAClC,UAAU,CAACmC,QAAQ,EAAEnC,UAAU,CAACoC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;MAC3EvB,WAAW,EAAE,CAAC,IAAI,CAACwB,qBAAqB,EAAE,EAAErC,UAAU,CAACmC,QAAQ,CAAC;MAChEG,YAAY,EAAE,CAAC,IAAI,CAACC,eAAe,EAAE;KACtC,CAAC;EACJ;EAEAC,QAAQA,CAAA;IACN;IACA,IAAI,CAACC,WAAW,EAAE;EACpB;EAEQP,gBAAgBA,CAAA;IACtB,IAAI,IAAI,CAAChB,IAAI,CAACwB,UAAU,EAAET,MAAM,EAAE;MAChC,OAAO,IAAI,CAACf,IAAI,CAACwB,UAAU,CAACT,MAAM;;IAGpC,QAAQ,IAAI,CAACf,IAAI,CAACC,MAAM,EAAEwB,IAAI;MAC5B,KAAK,eAAe;QAClB,OAAO,CAAC;MACV,KAAK,kBAAkB;QACrB,OAAO,EAAE;MACX,KAAK,gBAAgB;QACnB,OAAO,CAAC;MACV,KAAK,cAAc;QACjB,OAAO,CAAC;MACV;QACE,OAAO,CAAC;;EAEd;EAEQN,qBAAqBA,CAAA;IAC3B,IAAI,IAAI,CAACnB,IAAI,CAACwB,UAAU,EAAEE,OAAO,EAAE;MACjC,OAAO,IAAI,CAAC1B,IAAI,CAACwB,UAAU,CAACE,OAAO;;IAGrC,QAAQ,IAAI,CAAC1B,IAAI,CAACC,MAAM,EAAEwB,IAAI;MAC5B,KAAK,eAAe;QAClB,OAAO,wCAAwC;MACjD,KAAK,kBAAkB;QACrB,OAAO,mBAAmB,IAAI,CAACzB,IAAI,CAACC,MAAM,CAACE,MAAM,IAAI,wBAAwB,EAAE;MACjF,KAAK,gBAAgB;QACnB,OAAO,+BAA+B;MACxC,KAAK,cAAc;QACjB,OAAO,sBAAsB;MAC/B;QACE,OAAO,2BAA2B;;EAExC;EAEQkB,eAAeA,CAAA;IACrB,OAAO,IAAI,CAACrB,IAAI,CAACC,MAAM,EAAE0B,MAAM,IAAI,IAAI,CAAC3B,IAAI,CAAC4B,WAAW,EAAEC,GAAG,IAAI,EAAE;EACrE;EAEQN,WAAWA,CAAA;IACjB;IACA,IAAI,IAAI,CAACvB,IAAI,CAACC,MAAM,EAAEwB,IAAI,KAAK,eAAe,IAAI,IAAI,CAACzB,IAAI,CAACC,MAAM,CAAC0B,MAAM,EAAE;MACzE;MACA,IAAI,CAACd,cAAc,CAACiB,UAAU,CAAC;QAC7BnC,WAAW,EAAE,8BAA8B,IAAI,CAACK,IAAI,CAACC,MAAM,CAAC0B,MAAM;OACnE,CAAC;;EAEN;EAEMI,QAAQA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACZ,IAAID,KAAI,CAACnB,cAAc,CAACqB,OAAO,EAAE;QAC/BF,KAAI,CAACG,oBAAoB,EAAE;QAC3B;;MAGFH,KAAI,CAACrB,SAAS,GAAG,IAAI;MACrB,MAAMyB,SAAS,GAAGJ,KAAI,CAACnB,cAAc,CAACtB,KAAK;MAE3C,IAAI;QACF,MAAMqC,WAAW,GAAGI,KAAI,CAACxB,WAAW,CAAC6B,cAAc,EAAE;QACrD,IAAI,CAACT,WAAW,EAAE;UAChB,MAAM,IAAIU,KAAK,CAAC,0BAA0B,CAAC;;QAG7C;QACA,MAAMC,eAAe,GAAGP,KAAI,CAACQ,kBAAkB,EAAE;QAEjD;QACA,MAAMpB,YAAY,GAAGgB,SAAS,CAAChB,YAAY,IAAIQ,WAAW,CAACC,GAAG;QAE9D,MAAMG,KAAI,CAACzB,WAAW,CAACkC,eAAe,CACpCrB,YAAY,EACZgB,SAAS,CAACrB,MAAM,EAChBwB,eAAe,EACfH,SAAS,CAACzC,WAAW,EACrBqC,KAAI,CAAChC,IAAI,CAACC,MAAM,EAAEyC,WAAW,IAAId,WAAW,CAACC,GAAG,CACjD;QAEDG,KAAI,CAACvB,QAAQ,CAACkC,IAAI,CAChB,GAAGP,SAAS,CAACrB,MAAM,oCAAoC,EACvD,QAAQ,EACR;UAAE6B,QAAQ,EAAE;QAAI,CAAE,CACnB;QAEDZ,KAAI,CAACtB,SAAS,CAACmC,KAAK,CAAC,IAAI,CAAC;OAE3B,CAAC,OAAOC,KAAU,EAAE;QACnBC,OAAO,CAACD,KAAK,CAAC,yBAAyB,EAAEA,KAAK,CAAC;QAC/Cd,KAAI,CAACvB,QAAQ,CAACkC,IAAI,CAChB,0CAA0C,EAC1C,QAAQ,EACR;UAAEC,QAAQ,EAAE;QAAI,CAAE,CACnB;OACF,SAAS;QACRZ,KAAI,CAACrB,SAAS,GAAG,KAAK;;IACvB;EACH;EAEQ6B,kBAAkBA,CAAA;IACxB,QAAQ,IAAI,CAACxC,IAAI,CAACC,MAAM,EAAEwB,IAAI;MAC5B,KAAK,kBAAkB;QACrB,OAAOzC,eAAe,CAACgE,SAAS;MAClC,KAAK,gBAAgB;QACnB,OAAOhE,eAAe,CAACiE,MAAM;MAC/B,KAAK,cAAc;QACjB,OAAOjE,eAAe,CAACkE,KAAK;MAC9B,KAAK,eAAe;QAClB,OAAOlE,eAAe,CAACmE,WAAW;MACpC;QACE,OAAOnE,eAAe,CAACiE,MAAM;;EAEnC;EAEQd,oBAAoBA,CAAA;IAC1BiB,MAAM,CAACC,IAAI,CAAC,IAAI,CAACxC,cAAc,CAACyC,QAAQ,CAAC,CAACC,OAAO,CAACC,GAAG,IAAG;MACtD,MAAMC,OAAO,GAAG,IAAI,CAAC5C,cAAc,CAAC6C,GAAG,CAACF,GAAG,CAAC;MAC5CC,OAAO,EAAEE,aAAa,EAAE;IAC1B,CAAC,CAAC;EACJ;EAEAC,aAAaA,CAACC,SAAiB;IAC7B,MAAMC,KAAK,GAAG,IAAI,CAACjD,cAAc,CAAC6C,GAAG,CAACG,SAAS,CAAC;IAChD,IAAIC,KAAK,EAAEC,MAAM,IAAID,KAAK,CAACE,OAAO,EAAE;MAClC,IAAIF,KAAK,CAACC,MAAM,CAAC,UAAU,CAAC,EAAE;QAC5B,OAAO,cAAc;;MAEvB,IAAID,KAAK,CAACC,MAAM,CAAC,KAAK,CAAC,EAAE;QACvB,OAAO,iBAAiB;;;IAG5B,OAAO,EAAE;EACX;EAEAE,QAAQA,CAAA;IACN,IAAI,CAACvD,SAAS,CAACmC,KAAK,CAAC,KAAK,CAAC;EAC7B;EAEAqB,gBAAgBA,CAAA;IACd,MAAMC,UAAU,GAA8B;MAC5C,eAAe,EAAE,uBAAuB;MACxC,kBAAkB,EAAE,mBAAmB;MACvC,gBAAgB,EAAE,uBAAuB;MACzC,cAAc,EAAE;KACjB;IACD,OAAOA,UAAU,CAAC,IAAI,CAACnE,IAAI,CAACC,MAAM,EAAEwB,IAAI,IAAI,EAAE,CAAC,IAAI,SAAS;EAC9D;EAEA2C,eAAeA,CAAA;IACb,MAAMC,SAAS,GAA8B;MAC3C,eAAe,EAAE,YAAY;MAC7B,kBAAkB,EAAE,UAAU;MAC9B,gBAAgB,EAAE,eAAe;MACjC,cAAc,EAAE;KACjB;IACD,OAAOA,SAAS,CAAC,IAAI,CAACrE,IAAI,CAACC,MAAM,EAAEwB,IAAI,IAAI,EAAE,CAAC,IAAI,SAAS;EAC7D;;;uBA7LWrB,yBAAyB,EAAAnB,EAAA,CAAAqF,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAvF,EAAA,CAAAqF,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAzF,EAAA,CAAAqF,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAA3F,EAAA,CAAAqF,iBAAA,CAAAO,EAAA,CAAAC,WAAA,GAAA7F,EAAA,CAAAqF,iBAAA,CAAAS,EAAA,CAAAC,YAAA,GAAA/F,EAAA,CAAAqF,iBAAA,CAiB1BvF,eAAe;IAAA;EAAA;;;YAjBdqB,yBAAyB;MAAA6E,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,mCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UChBhCtG,EAHN,CAAAC,cAAA,aAA+B,aACF,YACJ,kBACW;UAAAD,EAAA,CAAAE,MAAA,GAAuB;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAChEH,EAAA,CAAAE,MAAA,8BACF;UAAAF,EAAA,CAAAG,YAAA,EAAK;UACLH,EAAA,CAAAC,cAAA,WAAqB;UAAAD,EAAA,CAAAE,MAAA,GAAwB;UAC/CF,EAD+C,CAAAG,YAAA,EAAI,EAC7C;UAMAH,EAJN,CAAAC,cAAA,yBAAoB,cACyC,yBAED,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,wBAAgB;UAAAF,EAAA,CAAAG,YAAA,EAAY;UACvCH,EAAA,CAAAC,cAAA,qBAAqC;UACnCD,EAAA,CAAAwG,UAAA,KAAAC,gDAAA,wBAAwE;UAM1EzG,EAAA,CAAAG,YAAA,EAAa;UACbH,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,IAA6B;UAC1CF,EAD0C,CAAAG,YAAA,EAAY,EACrC;UAIfH,EADF,CAAAC,cAAA,yBAAwD,iBAC3C;UAAAD,EAAA,CAAAE,MAAA,mBAAW;UAAAF,EAAA,CAAAG,YAAA,EAAY;UAClCH,EAAA,CAAAC,cAAA,mBAGwE;UACxED,EAAA,CAAAE,MAAA;UAAAF,EAAA,CAAAG,YAAA,EAAW;UACXH,EAAA,CAAAC,cAAA,iBAAW;UAAAD,EAAA,CAAAE,MAAA,IAAkC;UAC/CF,EAD+C,CAAAG,YAAA,EAAY,EAC1C;UAGjBH,EAAA,CAAAwG,UAAA,KAAAE,oDAAA,6BAC8D;UAW5D1G,EADF,CAAAC,cAAA,eAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,4BAAe;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAEtBH,EADF,CAAAC,cAAA,eAAyB,gBACb;UAAAD,EAAA,CAAAE,MAAA,eAAO;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAC,cAAA,YAAM;UAAAD,EAAA,CAAAE,MAAA,IAA8B;UACtCF,EADsC,CAAAG,YAAA,EAAO,EACvC;UAKNH,EAJA,CAAAwG,UAAA,KAAAG,yCAAA,kBAAwD,KAAAC,yCAAA,kBAIH;UAIvD5G,EAAA,CAAAG,YAAA,EAAM;UAKFH,EAFJ,CAAAC,cAAA,eAA4B,eACG,oBACI;UAAAD,EAAA,CAAAE,MAAA,aAAK;UAAAF,EAAA,CAAAG,YAAA,EAAW;UAE7CH,EADF,CAAAC,cAAA,eAA0B,UACpB;UAAAD,EAAA,CAAAE,MAAA,IAAuD;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAChEH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAE,MAAA,IAA+C;UAK5DF,EAL4D,CAAAG,YAAA,EAAI,EAClD,EACF,EACF,EACD,EACY;UAGnBH,EADF,CAAAC,cAAA,8BAAgC,kBACiC;UAA5CD,EAAA,CAAA6G,UAAA,mBAAAC,4DAAA;YAAA,OAASP,GAAA,CAAAvB,QAAA,EAAU;UAAA,EAAC;UACrChF,EAAA,CAAAE,MAAA,iBACF;UAAAF,EAAA,CAAAG,YAAA,EAAS;UACTH,EAAA,CAAAC,cAAA,kBAGyD;UADjDD,EAAA,CAAA6G,UAAA,mBAAAE,4DAAA;YAAA,OAASR,GAAA,CAAAzD,QAAA,EAAU;UAAA,EAAC;UAG1B9C,EADA,CAAAwG,UAAA,KAAAQ,iDAAA,0BAA6C,KAAAC,0CAAA,mBACpB;UAG/BjH,EAFI,CAAAG,YAAA,EAAS,EACU,EACjB;;;;UAtFUH,EAAA,CAAAO,SAAA,GAAmB;UAAnBP,EAAA,CAAAI,UAAA,oBAAmB;UAACJ,EAAA,CAAAO,SAAA,EAAuB;UAAvBP,EAAA,CAAAQ,iBAAA,CAAA+F,GAAA,CAAApB,eAAA,GAAuB;UAGlCnF,EAAA,CAAAO,SAAA,GAAwB;UAAxBP,EAAA,CAAAQ,iBAAA,CAAA+F,GAAA,CAAAtB,gBAAA,GAAwB;UAIvCjF,EAAA,CAAAO,SAAA,GAA4B;UAA5BP,EAAA,CAAAI,UAAA,cAAAmG,GAAA,CAAA3E,cAAA,CAA4B;UAKG5B,EAAA,CAAAO,SAAA,GAAgB;UAAhBP,EAAA,CAAAI,UAAA,YAAAmG,GAAA,CAAA5E,aAAA,CAAgB;UAOtC3B,EAAA,CAAAO,SAAA,GAA6B;UAA7BP,EAAA,CAAAQ,iBAAA,CAAA+F,GAAA,CAAA5B,aAAA,WAA6B;UAW7B3E,EAAA,CAAAO,SAAA,GAAkC;UAAlCP,EAAA,CAAAQ,iBAAA,CAAA+F,GAAA,CAAA5B,aAAA,gBAAkC;UAK9B3E,EAAA,CAAAO,SAAA,EAA2C;UAA3CP,EAAA,CAAAI,UAAA,UAAAmG,GAAA,CAAAxF,IAAA,CAAAC,MAAA,kBAAAuF,GAAA,CAAAxF,IAAA,CAAAC,MAAA,CAAAwB,IAAA,sBAA2C;UAclDxC,EAAA,CAAAO,SAAA,GAA8B;UAA9BP,EAAA,CAAAY,kBAAA,WAAA2F,GAAA,CAAAtB,gBAAA,OAA8B;UAEZjF,EAAA,CAAAO,SAAA,EAA4B;UAA5BP,EAAA,CAAAI,UAAA,SAAAmG,GAAA,CAAAxF,IAAA,CAAAC,MAAA,kBAAAuF,GAAA,CAAAxF,IAAA,CAAAC,MAAA,CAAAC,SAAA,CAA4B;UAI5BjB,EAAA,CAAAO,SAAA,EAAyB;UAAzBP,EAAA,CAAAI,UAAA,SAAAmG,GAAA,CAAAxF,IAAA,CAAAC,MAAA,kBAAAuF,GAAA,CAAAxF,IAAA,CAAAC,MAAA,CAAAE,MAAA,CAAyB;UAW3ClB,EAAA,CAAAO,SAAA,GAAuD;UAAvDP,EAAA,CAAAY,kBAAA,OAAAsG,QAAA,GAAAX,GAAA,CAAA3E,cAAA,CAAA6C,GAAA,6BAAAyC,QAAA,CAAA5G,KAAA,oBAAuD;UACxDN,EAAA,CAAAO,SAAA,GAA+C;UAA/CP,EAAA,CAAAY,kBAAA,kCAAA2F,GAAA,CAAAxF,IAAA,CAAA4B,WAAA,kBAAA4D,GAAA,CAAAxF,IAAA,CAAA4B,WAAA,CAAAwE,IAAA,KAA+C;UAQlBnH,EAAA,CAAAO,SAAA,GAAsB;UAAtBP,EAAA,CAAAI,UAAA,aAAAmG,GAAA,CAAA7E,SAAA,CAAsB;UAMtD1B,EAAA,CAAAO,SAAA,GAAgD;UAAhDP,EAAA,CAAAI,UAAA,aAAAmG,GAAA,CAAA7E,SAAA,IAAA6E,GAAA,CAAA3E,cAAA,CAAAqB,OAAA,CAAgD;UAC1BjD,EAAA,CAAAO,SAAA,EAAe;UAAfP,EAAA,CAAAI,UAAA,SAAAmG,GAAA,CAAA7E,SAAA,CAAe;UACpC1B,EAAA,CAAAO,SAAA,EAAgB;UAAhBP,EAAA,CAAAI,UAAA,UAAAmG,GAAA,CAAA7E,SAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}