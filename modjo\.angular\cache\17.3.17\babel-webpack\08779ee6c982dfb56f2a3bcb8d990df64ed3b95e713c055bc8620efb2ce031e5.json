{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Optional, NgModule } from '@angular/core';\nimport { asyncScheduler, Observable, from, of } from 'rxjs';\nimport * as i1 from '@angular/fire';\nimport { keepUnstableUntilFirst, VERSION } from '@angular/fire';\nimport { startWith, pairwise, map, scan, distinctUntilChanged, filter } from 'rxjs/operators';\nimport { ɵfirebaseAppFactory, ɵcacheInstance, FIREBASE_OPTIONS, FIREBASE_APP_NAME } from '@angular/fire/compat';\nimport { isPlatformServer } from '@angular/common';\nimport 'firebase/compat/auth';\nimport 'firebase/compat/firestore';\nimport * as i2 from '@angular/fire/compat/auth';\nimport { ɵauthFactory, USE_EMULATOR as USE_EMULATOR$1, SETTINGS as SETTINGS$1, TENANT_ID, LANGUAGE_CODE, USE_DEVICE_LANGUAGE, PERSISTENCE } from '@angular/fire/compat/auth';\nimport * as i3 from '@angular/fire/app-check';\nimport firebase from 'firebase/compat/app';\nfunction _fromRef(ref, scheduler = asyncScheduler) {\n  return new Observable(subscriber => {\n    let unsubscribe;\n    if (scheduler != null) {\n      scheduler.schedule(() => {\n        unsubscribe = ref.onSnapshot({\n          includeMetadataChanges: true\n        }, subscriber);\n      });\n    } else {\n      unsubscribe = ref.onSnapshot({\n        includeMetadataChanges: true\n      }, subscriber);\n    }\n    return () => {\n      if (unsubscribe != null) {\n        unsubscribe();\n      }\n    };\n  });\n}\nfunction fromRef(ref, scheduler) {\n  return _fromRef(ref, scheduler);\n}\nfunction fromDocRef(ref, scheduler) {\n  return fromRef(ref, scheduler).pipe(startWith(undefined), pairwise(), map(([priorPayload, payload]) => {\n    if (!payload.exists) {\n      return {\n        payload,\n        type: 'removed'\n      };\n    }\n    if (!(priorPayload === null || priorPayload === void 0 ? void 0 : priorPayload.exists)) {\n      return {\n        payload,\n        type: 'added'\n      };\n    }\n    return {\n      payload,\n      type: 'modified'\n    };\n  }));\n}\nfunction fromCollectionRef(ref, scheduler) {\n  return fromRef(ref, scheduler).pipe(map(payload => ({\n    payload,\n    type: 'query'\n  })));\n}\n\n/**\n * Return a stream of document changes on a query. These results are not in sort order but in\n * order of occurence.\n */\nfunction docChanges(query, scheduler) {\n  return fromCollectionRef(query, scheduler).pipe(startWith(undefined), pairwise(), map(([priorAction, action]) => {\n    const docChanges = action.payload.docChanges();\n    const actions = docChanges.map(change => ({\n      type: change.type,\n      payload: change\n    }));\n    // the metadata has changed from the prior emission\n    if (priorAction && JSON.stringify(priorAction.payload.metadata) !== JSON.stringify(action.payload.metadata)) {\n      // go through all the docs in payload and figure out which ones changed\n      action.payload.docs.forEach((currentDoc, currentIndex) => {\n        const docChange = docChanges.find(d => d.doc.ref.isEqual(currentDoc.ref));\n        const priorDoc = priorAction === null || priorAction === void 0 ? void 0 : priorAction.payload.docs.find(d => d.ref.isEqual(currentDoc.ref));\n        if (docChange && JSON.stringify(docChange.doc.metadata) === JSON.stringify(currentDoc.metadata) || !docChange && priorDoc && JSON.stringify(priorDoc.metadata) === JSON.stringify(currentDoc.metadata)) {\n          // document doesn't appear to have changed, don't log another action\n        } else {\n          // since the actions are processed in order just push onto the array\n          actions.push({\n            type: 'modified',\n            payload: {\n              oldIndex: currentIndex,\n              newIndex: currentIndex,\n              type: 'modified',\n              doc: currentDoc\n            }\n          });\n        }\n      });\n    }\n    return actions;\n  }));\n}\n/**\n * Return a stream of document changes on a query. These results are in sort order.\n */\nfunction sortedChanges(query, events, scheduler) {\n  return docChanges(query, scheduler).pipe(scan((current, changes) => combineChanges(current, changes.map(it => it.payload), events), []), distinctUntilChanged(),\n  // cut down on unneed change cycles\n  map(changes => changes.map(c => ({\n    type: c.type,\n    payload: c\n  }))));\n}\n/**\n * Combines the total result set from the current set of changes from an incoming set\n * of changes.\n */\nfunction combineChanges(current, changes, events) {\n  changes.forEach(change => {\n    // skip unwanted change types\n    if (events.indexOf(change.type) > -1) {\n      current = combineChange(current, change);\n    }\n  });\n  return current;\n}\n/**\n * Splice arguments on top of a sliced array, to break top-level ===\n * this is useful for change-detection\n */\nfunction sliceAndSplice(original, start, deleteCount, ...args) {\n  const returnArray = original.slice();\n  returnArray.splice(start, deleteCount, ...args);\n  return returnArray;\n}\n/**\n * Creates a new sorted array from a new change.\n * Build our own because we allow filtering of action types ('added', 'removed', 'modified') before scanning\n * and so we have greater control over change detection (by breaking ===)\n */\nfunction combineChange(combined, change) {\n  switch (change.type) {\n    case 'added':\n      if (combined[change.newIndex] && combined[change.newIndex].doc.ref.isEqual(change.doc.ref)) {\n        // Not sure why the duplicates are getting fired\n      } else {\n        return sliceAndSplice(combined, change.newIndex, 0, change);\n      }\n      break;\n    case 'modified':\n      if (combined[change.oldIndex] == null || combined[change.oldIndex].doc.ref.isEqual(change.doc.ref)) {\n        // When an item changes position we first remove it\n        // and then add it's new position\n        if (change.oldIndex !== change.newIndex) {\n          const copiedArray = combined.slice();\n          copiedArray.splice(change.oldIndex, 1);\n          copiedArray.splice(change.newIndex, 0, change);\n          return copiedArray;\n        } else {\n          return sliceAndSplice(combined, change.newIndex, 1, change);\n        }\n      }\n      break;\n    case 'removed':\n      if (combined[change.oldIndex] && combined[change.oldIndex].doc.ref.isEqual(change.doc.ref)) {\n        return sliceAndSplice(combined, change.oldIndex, 1);\n      }\n      break;\n  }\n  return combined;\n}\nfunction validateEventsArray(events) {\n  if (!events || events.length === 0) {\n    events = ['added', 'removed', 'modified'];\n  }\n  return events;\n}\n/**\n * AngularFirestoreCollection service\n *\n * This class creates a reference to a Firestore Collection. A reference and a query are provided in\n * in the constructor. The query can be the unqueried reference if no query is desired.The class\n * is generic which gives you type safety for data update methods and data streaming.\n *\n * This class uses Symbol.observable to transform into Observable using Observable.from().\n *\n * This class is rarely used directly and should be created from the AngularFirestore service.\n *\n * Example:\n *\n * const collectionRef = firebase.firestore.collection('stocks');\n * const query = collectionRef.where('price', '>', '0.01');\n * const fakeStock = new AngularFirestoreCollection<Stock>(collectionRef, query);\n *\n * // NOTE!: the updates are performed on the reference not the query\n * await fakeStock.add({ name: 'FAKE', price: 0.01 });\n *\n * // Subscribe to changes as snapshots. This provides you data updates as well as delta updates.\n * fakeStock.valueChanges().subscribe(value => console.log(value));\n */\nclass AngularFirestoreCollection {\n  /**\n   * The constructor takes in a CollectionReference and Query to provide wrapper methods\n   * for data operations and data streaming.\n   *\n   * Note: Data operation methods are done on the reference not the query. This means\n   * when you update data it is not updating data to the window of your query unless\n   * the data fits the criteria of the query. See the AssociatedRefence type for details\n   * on this implication.\n   */\n  constructor(ref, query, afs) {\n    this.ref = ref;\n    this.query = query;\n    this.afs = afs;\n  }\n  /**\n   * Listen to the latest change in the stream. This method returns changes\n   * as they occur and they are not sorted by query order. This allows you to construct\n   * your own data structure.\n   */\n  stateChanges(events) {\n    let source = docChanges(this.query, this.afs.schedulers.outsideAngular);\n    if (events && events.length > 0) {\n      source = source.pipe(map(actions => actions.filter(change => events.indexOf(change.type) > -1)));\n    }\n    return source.pipe(\n    // We want to filter out empty arrays, but always emit at first, so the developer knows\n    // that the collection has been resolve; even if it's empty\n    startWith(undefined), pairwise(), filter(([prior, current]) => current.length > 0 || !prior), map(([prior, current]) => current), keepUnstableUntilFirst);\n  }\n  /**\n   * Create a stream of changes as they occur it time. This method is similar to stateChanges()\n   * but it collects each event in an array over time.\n   */\n  auditTrail(events) {\n    return this.stateChanges(events).pipe(scan((current, action) => [...current, ...action], []));\n  }\n  /**\n   * Create a stream of synchronized changes. This method keeps the local array in sorted\n   * query order.\n   */\n  snapshotChanges(events) {\n    const validatedEvents = validateEventsArray(events);\n    const scheduledSortedChanges$ = sortedChanges(this.query, validatedEvents, this.afs.schedulers.outsideAngular);\n    return scheduledSortedChanges$.pipe(keepUnstableUntilFirst);\n  }\n  valueChanges(options = {}) {\n    return fromCollectionRef(this.query, this.afs.schedulers.outsideAngular).pipe(map(actions => actions.payload.docs.map(a => {\n      if (options.idField) {\n        return Object.assign(Object.assign({}, a.data()), {\n          [options.idField]: a.id\n        });\n      } else {\n        return a.data();\n      }\n    })), keepUnstableUntilFirst);\n  }\n  /**\n   * Retrieve the results of the query once.\n   */\n  get(options) {\n    return from(this.query.get(options)).pipe(keepUnstableUntilFirst);\n  }\n  /**\n   * Add data to a collection reference.\n   *\n   * Note: Data operation methods are done on the reference not the query. This means\n   * when you update data it is not updating data to the window of your query unless\n   * the data fits the criteria of the query.\n   */\n  add(data) {\n    return this.ref.add(data);\n  }\n  /**\n   * Create a reference to a single document in a collection.\n   */\n  doc(path) {\n    // TODO is there a better way to solve this type issue\n    return new AngularFirestoreDocument(this.ref.doc(path), this.afs);\n  }\n}\n\n/**\n * AngularFirestoreDocument service\n *\n * This class creates a reference to a Firestore Document. A reference is provided in\n * in the constructor. The class is generic which gives you type safety for data update\n * methods and data streaming.\n *\n * This class uses Symbol.observable to transform into Observable using Observable.from().\n *\n * This class is rarely used directly and should be created from the AngularFirestore service.\n *\n * Example:\n *\n * const fakeStock = new AngularFirestoreDocument<Stock>(doc('stocks/FAKE'));\n * await fakeStock.set({ name: 'FAKE', price: 0.01 });\n * fakeStock.valueChanges().map(snap => {\n *   if(snap.exists) return snap.data();\n *   return null;\n * }).subscribe(value => console.log(value));\n * // OR! Transform using Observable.from() and the data is unwrapped for you\n * Observable.from(fakeStock).subscribe(value => console.log(value));\n */\nclass AngularFirestoreDocument {\n  /**\n   * The constructor takes in a DocumentReference to provide wrapper methods\n   * for data operations, data streaming, and Symbol.observable.\n   */\n  constructor(ref, afs) {\n    this.ref = ref;\n    this.afs = afs;\n  }\n  /**\n   * Create or overwrite a single document.\n   */\n  set(data, options) {\n    return this.ref.set(data, options);\n  }\n  /**\n   * Update some fields of a document without overwriting the entire document.\n   */\n  update(data) {\n    return this.ref.update(data);\n  }\n  /**\n   * Delete a document.\n   */\n  delete() {\n    return this.ref.delete();\n  }\n  /**\n   * Create a reference to a sub-collection given a path and an optional query\n   * function.\n   */\n  collection(path, queryFn) {\n    const collectionRef = this.ref.collection(path);\n    const {\n      ref,\n      query\n    } = associateQuery(collectionRef, queryFn);\n    return new AngularFirestoreCollection(ref, query, this.afs);\n  }\n  /**\n   * Listen to snapshot updates from the document.\n   */\n  snapshotChanges() {\n    const scheduledFromDocRef$ = fromDocRef(this.ref, this.afs.schedulers.outsideAngular);\n    return scheduledFromDocRef$.pipe(keepUnstableUntilFirst);\n  }\n  valueChanges(options = {}) {\n    return this.snapshotChanges().pipe(map(({\n      payload\n    }) => options.idField ? Object.assign(Object.assign({}, payload.data()), {\n      [options.idField]: payload.id\n    }) : payload.data()));\n  }\n  /**\n   * Retrieve the document once.\n   */\n  get(options) {\n    return from(this.ref.get(options)).pipe(keepUnstableUntilFirst);\n  }\n}\n\n/**\n * AngularFirestoreCollectionGroup service\n *\n * This class holds a reference to a Firestore Collection Group Query.\n *\n * This class uses Symbol.observable to transform into Observable using Observable.from().\n *\n * This class is rarely used directly and should be created from the AngularFirestore service.\n *\n * Example:\n *\n * const collectionGroup = firebase.firestore.collectionGroup('stocks');\n * const query = collectionRef.where('price', '>', '0.01');\n * const fakeStock = new AngularFirestoreCollectionGroup<Stock>(query, afs);\n *\n * // Subscribe to changes as snapshots. This provides you data updates as well as delta updates.\n * fakeStock.valueChanges().subscribe(value => console.log(value));\n */\nclass AngularFirestoreCollectionGroup {\n  /**\n   * The constructor takes in a CollectionGroupQuery to provide wrapper methods\n   * for data operations and data streaming.\n   */\n  constructor(query, afs) {\n    this.query = query;\n    this.afs = afs;\n  }\n  /**\n   * Listen to the latest change in the stream. This method returns changes\n   * as they occur and they are not sorted by query order. This allows you to construct\n   * your own data structure.\n   */\n  stateChanges(events) {\n    if (!events || events.length === 0) {\n      return docChanges(this.query, this.afs.schedulers.outsideAngular).pipe(keepUnstableUntilFirst);\n    }\n    return docChanges(this.query, this.afs.schedulers.outsideAngular).pipe(map(actions => actions.filter(change => events.indexOf(change.type) > -1)), filter(changes => changes.length > 0), keepUnstableUntilFirst);\n  }\n  /**\n   * Create a stream of changes as they occur it time. This method is similar to stateChanges()\n   * but it collects each event in an array over time.\n   */\n  auditTrail(events) {\n    return this.stateChanges(events).pipe(scan((current, action) => [...current, ...action], []));\n  }\n  /**\n   * Create a stream of synchronized changes. This method keeps the local array in sorted\n   * query order.\n   */\n  snapshotChanges(events) {\n    const validatedEvents = validateEventsArray(events);\n    const scheduledSortedChanges$ = sortedChanges(this.query, validatedEvents, this.afs.schedulers.outsideAngular);\n    return scheduledSortedChanges$.pipe(keepUnstableUntilFirst);\n  }\n  valueChanges(options = {}) {\n    const fromCollectionRefScheduled$ = fromCollectionRef(this.query, this.afs.schedulers.outsideAngular);\n    return fromCollectionRefScheduled$.pipe(map(actions => actions.payload.docs.map(a => {\n      if (options.idField) {\n        return Object.assign({\n          [options.idField]: a.id\n        }, a.data());\n      } else {\n        return a.data();\n      }\n    })), keepUnstableUntilFirst);\n  }\n  /**\n   * Retrieve the results of the query once.\n   */\n  get(options) {\n    return from(this.query.get(options)).pipe(keepUnstableUntilFirst);\n  }\n}\n\n/**\n * The value of this token determines whether or not the firestore will have persistance enabled\n */\nconst ENABLE_PERSISTENCE = new InjectionToken('angularfire2.enableFirestorePersistence');\nconst PERSISTENCE_SETTINGS = new InjectionToken('angularfire2.firestore.persistenceSettings');\nconst SETTINGS = new InjectionToken('angularfire2.firestore.settings');\nconst USE_EMULATOR = new InjectionToken('angularfire2.firestore.use-emulator');\n/**\n * A utility methods for associating a collection reference with\n * a query.\n *\n * @param collectionRef - A collection reference to query\n * @param queryFn - The callback to create a query\n *\n * Example:\n * const { query, ref } = associateQuery(docRef.collection('items'), ref => {\n *  return ref.where('age', '<', 200);\n * });\n */\nfunction associateQuery(collectionRef, queryFn = ref => ref) {\n  const query = queryFn(collectionRef);\n  const ref = collectionRef;\n  return {\n    query,\n    ref\n  };\n}\n/**\n * AngularFirestore Service\n *\n * This service is the main entry point for this feature module. It provides\n * an API for creating Collection and Reference services. These services can\n * then be used to do data updates and observable streams of the data.\n *\n * Example:\n *\n * import { Component } from '@angular/core';\n * import { AngularFirestore, AngularFirestoreCollection, AngularFirestoreDocument } from '@angular/fire/firestore';\n * import { Observable } from 'rxjs/Observable';\n * import { from } from 'rxjs/observable';\n *\n * @Component({\n *   selector: 'app-my-component',\n *   template: `\n *    <h2>Items for {{ (profile | async)?.name }}\n *    <ul>\n *       <li *ngFor=\"let item of items | async\">{{ item.name }}</li>\n *    </ul>\n *    <div class=\"control-input\">\n *       <input type=\"text\" #itemname />\n *       <button (click)=\"addItem(itemname.value)\">Add Item</button>\n *    </div>\n *   `\n * })\n * export class MyComponent implements OnInit {\n *\n *   // services for data operations and data streaming\n *   private readonly itemsRef: AngularFirestoreCollection<Item>;\n *   private readonly profileRef: AngularFirestoreDocument<Profile>;\n *\n *   // observables for template\n *   items: Observable<Item[]>;\n *   profile: Observable<Profile>;\n *\n *   // inject main service\n *   constructor(private readonly afs: AngularFirestore) {}\n *\n *   ngOnInit() {\n *     this.itemsRef = afs.collection('items', ref => ref.where('user', '==', 'davideast').limit(10));\n *     this.items = this.itemsRef.valueChanges().map(snap => snap.docs.map(data => doc.data()));\n *     // this.items = from(this.itemsRef); // you can also do this with no mapping\n *\n *     this.profileRef = afs.doc('users/davideast');\n *     this.profile = this.profileRef.valueChanges();\n *   }\n *\n *   addItem(name: string) {\n *     const user = 'davideast';\n *     this.itemsRef.add({ name, user });\n *   }\n * }\n */\nclass AngularFirestore {\n  /**\n   * Each Feature of AngularFire has a FirebaseApp injected. This way we\n   * don't rely on the main Firebase App instance and we can create named\n   * apps and use multiple apps.\n   */\n  constructor(options, name, shouldEnablePersistence, settings,\n  // tslint:disable-next-line:ban-types\n  platformId, zone, schedulers, persistenceSettings, _useEmulator, auth, useAuthEmulator, authSettings,\n  // can't use firebase.auth.AuthSettings here\n  tenantId, languageCode, useDeviceLanguage, persistence, _appCheckInstances) {\n    this.schedulers = schedulers;\n    const app = ɵfirebaseAppFactory(options, zone, name);\n    const useEmulator = _useEmulator;\n    if (auth) {\n      ɵauthFactory(app, zone, useAuthEmulator, tenantId, languageCode, useDeviceLanguage, authSettings, persistence);\n    }\n    [this.firestore, this.persistenceEnabled$] = ɵcacheInstance(`${app.name}.firestore`, 'AngularFirestore', app.name, () => {\n      const firestore = zone.runOutsideAngular(() => app.firestore());\n      if (settings) {\n        firestore.settings(settings);\n      }\n      if (useEmulator) {\n        firestore.useEmulator(...useEmulator);\n      }\n      if (shouldEnablePersistence && !isPlatformServer(platformId)) {\n        // We need to try/catch here because not all enablePersistence() failures are caught\n        // https://github.com/firebase/firebase-js-sdk/issues/608\n        const enablePersistence = () => {\n          try {\n            return from(firestore.enablePersistence(persistenceSettings || undefined).then(() => true, () => false));\n          } catch (e) {\n            if (typeof console !== 'undefined') {\n              console.warn(e);\n            }\n            return of(false);\n          }\n        };\n        return [firestore, zone.runOutsideAngular(enablePersistence)];\n      } else {\n        return [firestore, of(false)];\n      }\n    }, [settings, useEmulator, shouldEnablePersistence]);\n  }\n  collection(pathOrRef, queryFn) {\n    let collectionRef;\n    if (typeof pathOrRef === 'string') {\n      collectionRef = this.firestore.collection(pathOrRef);\n    } else {\n      collectionRef = pathOrRef;\n    }\n    const {\n      ref,\n      query\n    } = associateQuery(collectionRef, queryFn);\n    const refInZone = this.schedulers.ngZone.run(() => ref);\n    return new AngularFirestoreCollection(refInZone, query, this);\n  }\n  /**\n   * Create a reference to a Firestore Collection Group based on a collectionId\n   * and an optional query function to narrow the result\n   * set.\n   */\n  collectionGroup(collectionId, queryGroupFn) {\n    const queryFn = queryGroupFn || (ref => ref);\n    const collectionGroup = this.firestore.collectionGroup(collectionId);\n    return new AngularFirestoreCollectionGroup(queryFn(collectionGroup), this);\n  }\n  doc(pathOrRef) {\n    let ref;\n    if (typeof pathOrRef === 'string') {\n      ref = this.firestore.doc(pathOrRef);\n    } else {\n      ref = pathOrRef;\n    }\n    const refInZone = this.schedulers.ngZone.run(() => ref);\n    return new AngularFirestoreDocument(refInZone, this);\n  }\n  /**\n   * Returns a generated Firestore Document Id.\n   */\n  createId() {\n    return this.firestore.collection('_').doc().id;\n  }\n}\nAngularFirestore.ɵfac = function AngularFirestore_Factory(t) {\n  return new (t || AngularFirestore)(i0.ɵɵinject(FIREBASE_OPTIONS), i0.ɵɵinject(FIREBASE_APP_NAME, 8), i0.ɵɵinject(ENABLE_PERSISTENCE, 8), i0.ɵɵinject(SETTINGS, 8), i0.ɵɵinject(PLATFORM_ID), i0.ɵɵinject(i0.NgZone), i0.ɵɵinject(i1.ɵAngularFireSchedulers), i0.ɵɵinject(PERSISTENCE_SETTINGS, 8), i0.ɵɵinject(USE_EMULATOR, 8), i0.ɵɵinject(i2.AngularFireAuth, 8), i0.ɵɵinject(USE_EMULATOR$1, 8), i0.ɵɵinject(SETTINGS$1, 8), i0.ɵɵinject(TENANT_ID, 8), i0.ɵɵinject(LANGUAGE_CODE, 8), i0.ɵɵinject(USE_DEVICE_LANGUAGE, 8), i0.ɵɵinject(PERSISTENCE, 8), i0.ɵɵinject(i3.AppCheckInstances, 8));\n};\nAngularFirestore.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n  token: AngularFirestore,\n  factory: AngularFirestore.ɵfac,\n  providedIn: 'any'\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularFirestore, [{\n    type: Injectable,\n    args: [{\n      providedIn: 'any'\n    }]\n  }], function () {\n    return [{\n      type: undefined,\n      decorators: [{\n        type: Inject,\n        args: [FIREBASE_OPTIONS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [FIREBASE_APP_NAME]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [ENABLE_PERSISTENCE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [SETTINGS]\n      }]\n    }, {\n      type: Object,\n      decorators: [{\n        type: Inject,\n        args: [PLATFORM_ID]\n      }]\n    }, {\n      type: i0.NgZone\n    }, {\n      type: i1.ɵAngularFireSchedulers\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [PERSISTENCE_SETTINGS]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [USE_EMULATOR]\n      }]\n    }, {\n      type: i2.AngularFireAuth,\n      decorators: [{\n        type: Optional\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [USE_EMULATOR$1]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [SETTINGS$1]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [TENANT_ID]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [LANGUAGE_CODE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [USE_DEVICE_LANGUAGE]\n      }]\n    }, {\n      type: undefined,\n      decorators: [{\n        type: Optional\n      }, {\n        type: Inject,\n        args: [PERSISTENCE]\n      }]\n    }, {\n      type: i3.AppCheckInstances,\n      decorators: [{\n        type: Optional\n      }]\n    }];\n  }, null);\n})();\nclass AngularFirestoreModule {\n  constructor() {\n    firebase.registerVersion('angularfire', VERSION.full, 'fst-compat');\n  }\n  /**\n   * Attempt to enable persistent storage, if possible\n   */\n  static enablePersistence(persistenceSettings) {\n    return {\n      ngModule: AngularFirestoreModule,\n      providers: [{\n        provide: ENABLE_PERSISTENCE,\n        useValue: true\n      }, {\n        provide: PERSISTENCE_SETTINGS,\n        useValue: persistenceSettings\n      }]\n    };\n  }\n}\nAngularFirestoreModule.ɵfac = function AngularFirestoreModule_Factory(t) {\n  return new (t || AngularFirestoreModule)();\n};\nAngularFirestoreModule.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n  type: AngularFirestoreModule\n});\nAngularFirestoreModule.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n  providers: [AngularFirestore]\n});\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(AngularFirestoreModule, [{\n    type: NgModule,\n    args: [{\n      providers: [AngularFirestore]\n    }]\n  }], function () {\n    return [];\n  }, null);\n})();\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularFirestore, AngularFirestoreCollection, AngularFirestoreCollectionGroup, AngularFirestoreDocument, AngularFirestoreModule, ENABLE_PERSISTENCE, PERSISTENCE_SETTINGS, SETTINGS, USE_EMULATOR, associateQuery, combineChange, combineChanges, docChanges, fromCollectionRef, fromDocRef, fromRef, sortedChanges, validateEventsArray };", "map": {"version": 3, "names": ["i0", "InjectionToken", "PLATFORM_ID", "Injectable", "Inject", "Optional", "NgModule", "asyncScheduler", "Observable", "from", "of", "i1", "keepUnstableUntilFirst", "VERSION", "startWith", "pairwise", "map", "scan", "distinctUntilChanged", "filter", "ɵfirebaseAppFactory", "ɵcacheInstance", "FIREBASE_OPTIONS", "FIREBASE_APP_NAME", "isPlatformServer", "i2", "ɵauthFactory", "USE_EMULATOR", "USE_EMULATOR$1", "SETTINGS", "SETTINGS$1", "TENANT_ID", "LANGUAGE_CODE", "USE_DEVICE_LANGUAGE", "PERSISTENCE", "i3", "firebase", "_fromRef", "ref", "scheduler", "subscriber", "unsubscribe", "schedule", "onSnapshot", "includeMetadataChanges", "fromRef", "fromDocRef", "pipe", "undefined", "priorPayload", "payload", "exists", "type", "fromCollectionRef", "do<PERSON><PERSON><PERSON><PERSON>", "query", "priorAction", "action", "actions", "change", "JSON", "stringify", "metadata", "docs", "for<PERSON>ach", "currentDoc", "currentIndex", "doc<PERSON><PERSON><PERSON>", "find", "d", "doc", "isEqual", "priorDoc", "push", "oldIndex", "newIndex", "sortedChanges", "events", "current", "changes", "combineChanges", "it", "c", "indexOf", "combineChange", "sliceAndSplice", "original", "start", "deleteCount", "args", "returnArray", "slice", "splice", "combined", "copiedArray", "validateEventsArray", "length", "AngularFirestoreCollection", "constructor", "afs", "stateChanges", "source", "schedulers", "outsideAngular", "prior", "auditTrail", "snapshotChanges", "validatedEvents", "scheduledSortedChanges$", "valueChanges", "options", "a", "idField", "Object", "assign", "data", "id", "get", "add", "path", "AngularFirestoreDocument", "set", "update", "delete", "collection", "queryFn", "collectionRef", "<PERSON><PERSON><PERSON><PERSON>", "scheduledFromDocRef$", "AngularFirestoreCollectionGroup", "fromCollectionRefScheduled$", "ENABLE_PERSISTENCE", "PERSISTENCE_SETTINGS", "AngularFirestore", "name", "shouldEnablePersistence", "settings", "platformId", "zone", "persistenceSettings", "_useEmulator", "auth", "useAuthEmulator", "authSettings", "tenantId", "languageCode", "useDeviceLanguage", "persistence", "_appCheckInstances", "app", "useEmulator", "firestore", "persistenceEnabled$", "runOutsideAngular", "enablePersistence", "then", "e", "console", "warn", "pathOrRef", "refInZone", "ngZone", "run", "collectionGroup", "collectionId", "queryGroupFn", "createId", "ɵfac", "AngularFirestore_Factory", "t", "ɵɵinject", "NgZone", "ɵAngularFireSchedulers", "AngularFireAuth", "AppCheckInstances", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "providedIn", "ngDevMode", "ɵsetClassMetadata", "decorators", "AngularFirestoreModule", "registerVersion", "full", "ngModule", "providers", "provide", "useValue", "AngularFirestoreModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/fire/fesm2015/angular-fire-compat-firestore.js"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, PLATFORM_ID, Injectable, Inject, Optional, NgModule } from '@angular/core';\nimport { asyncScheduler, Observable, from, of } from 'rxjs';\nimport * as i1 from '@angular/fire';\nimport { keepUnstableUntilFirst, VERSION } from '@angular/fire';\nimport { startWith, pairwise, map, scan, distinctUntilChanged, filter } from 'rxjs/operators';\nimport { ɵfirebaseAppFactory, ɵcacheInstance, FIREBASE_OPTIONS, FIREBASE_APP_NAME } from '@angular/fire/compat';\nimport { isPlatformServer } from '@angular/common';\nimport 'firebase/compat/auth';\nimport 'firebase/compat/firestore';\nimport * as i2 from '@angular/fire/compat/auth';\nimport { ɵauthFactory, USE_EMULATOR as USE_EMULATOR$1, SETTINGS as SETTINGS$1, TENANT_ID, LANGUAGE_CODE, USE_DEVICE_LANGUAGE, PERSISTENCE } from '@angular/fire/compat/auth';\nimport * as i3 from '@angular/fire/app-check';\nimport firebase from 'firebase/compat/app';\n\nfunction _fromRef(ref, scheduler = asyncScheduler) {\n    return new Observable(subscriber => {\n        let unsubscribe;\n        if (scheduler != null) {\n            scheduler.schedule(() => {\n                unsubscribe = ref.onSnapshot({ includeMetadataChanges: true }, subscriber);\n            });\n        }\n        else {\n            unsubscribe = ref.onSnapshot({ includeMetadataChanges: true }, subscriber);\n        }\n        return () => {\n            if (unsubscribe != null) {\n                unsubscribe();\n            }\n        };\n    });\n}\nfunction fromRef(ref, scheduler) {\n    return _fromRef(ref, scheduler);\n}\nfunction fromDocRef(ref, scheduler) {\n    return fromRef(ref, scheduler)\n        .pipe(startWith(undefined), pairwise(), map(([priorPayload, payload]) => {\n        if (!payload.exists) {\n            return { payload, type: 'removed' };\n        }\n        if (!(priorPayload === null || priorPayload === void 0 ? void 0 : priorPayload.exists)) {\n            return { payload, type: 'added' };\n        }\n        return { payload, type: 'modified' };\n    }));\n}\nfunction fromCollectionRef(ref, scheduler) {\n    return fromRef(ref, scheduler).pipe(map(payload => ({ payload, type: 'query' })));\n}\n\n/**\n * Return a stream of document changes on a query. These results are not in sort order but in\n * order of occurence.\n */\nfunction docChanges(query, scheduler) {\n    return fromCollectionRef(query, scheduler)\n        .pipe(startWith(undefined), pairwise(), map(([priorAction, action]) => {\n        const docChanges = action.payload.docChanges();\n        const actions = docChanges.map(change => ({ type: change.type, payload: change }));\n        // the metadata has changed from the prior emission\n        if (priorAction && JSON.stringify(priorAction.payload.metadata) !== JSON.stringify(action.payload.metadata)) {\n            // go through all the docs in payload and figure out which ones changed\n            action.payload.docs.forEach((currentDoc, currentIndex) => {\n                const docChange = docChanges.find(d => d.doc.ref.isEqual(currentDoc.ref));\n                const priorDoc = priorAction === null || priorAction === void 0 ? void 0 : priorAction.payload.docs.find(d => d.ref.isEqual(currentDoc.ref));\n                if (docChange && JSON.stringify(docChange.doc.metadata) === JSON.stringify(currentDoc.metadata) ||\n                    !docChange && priorDoc && JSON.stringify(priorDoc.metadata) === JSON.stringify(currentDoc.metadata)) {\n                    // document doesn't appear to have changed, don't log another action\n                }\n                else {\n                    // since the actions are processed in order just push onto the array\n                    actions.push({\n                        type: 'modified',\n                        payload: {\n                            oldIndex: currentIndex,\n                            newIndex: currentIndex,\n                            type: 'modified',\n                            doc: currentDoc\n                        }\n                    });\n                }\n            });\n        }\n        return actions;\n    }));\n}\n/**\n * Return a stream of document changes on a query. These results are in sort order.\n */\nfunction sortedChanges(query, events, scheduler) {\n    return docChanges(query, scheduler)\n        .pipe(scan((current, changes) => combineChanges(current, changes.map(it => it.payload), events), []), distinctUntilChanged(), // cut down on unneed change cycles\n    map(changes => changes.map(c => ({ type: c.type, payload: c }))));\n}\n/**\n * Combines the total result set from the current set of changes from an incoming set\n * of changes.\n */\nfunction combineChanges(current, changes, events) {\n    changes.forEach(change => {\n        // skip unwanted change types\n        if (events.indexOf(change.type) > -1) {\n            current = combineChange(current, change);\n        }\n    });\n    return current;\n}\n/**\n * Splice arguments on top of a sliced array, to break top-level ===\n * this is useful for change-detection\n */\nfunction sliceAndSplice(original, start, deleteCount, ...args) {\n    const returnArray = original.slice();\n    returnArray.splice(start, deleteCount, ...args);\n    return returnArray;\n}\n/**\n * Creates a new sorted array from a new change.\n * Build our own because we allow filtering of action types ('added', 'removed', 'modified') before scanning\n * and so we have greater control over change detection (by breaking ===)\n */\nfunction combineChange(combined, change) {\n    switch (change.type) {\n        case 'added':\n            if (combined[change.newIndex] && combined[change.newIndex].doc.ref.isEqual(change.doc.ref)) {\n                // Not sure why the duplicates are getting fired\n            }\n            else {\n                return sliceAndSplice(combined, change.newIndex, 0, change);\n            }\n            break;\n        case 'modified':\n            if (combined[change.oldIndex] == null || combined[change.oldIndex].doc.ref.isEqual(change.doc.ref)) {\n                // When an item changes position we first remove it\n                // and then add it's new position\n                if (change.oldIndex !== change.newIndex) {\n                    const copiedArray = combined.slice();\n                    copiedArray.splice(change.oldIndex, 1);\n                    copiedArray.splice(change.newIndex, 0, change);\n                    return copiedArray;\n                }\n                else {\n                    return sliceAndSplice(combined, change.newIndex, 1, change);\n                }\n            }\n            break;\n        case 'removed':\n            if (combined[change.oldIndex] && combined[change.oldIndex].doc.ref.isEqual(change.doc.ref)) {\n                return sliceAndSplice(combined, change.oldIndex, 1);\n            }\n            break;\n    }\n    return combined;\n}\n\nfunction validateEventsArray(events) {\n    if (!events || events.length === 0) {\n        events = ['added', 'removed', 'modified'];\n    }\n    return events;\n}\n/**\n * AngularFirestoreCollection service\n *\n * This class creates a reference to a Firestore Collection. A reference and a query are provided in\n * in the constructor. The query can be the unqueried reference if no query is desired.The class\n * is generic which gives you type safety for data update methods and data streaming.\n *\n * This class uses Symbol.observable to transform into Observable using Observable.from().\n *\n * This class is rarely used directly and should be created from the AngularFirestore service.\n *\n * Example:\n *\n * const collectionRef = firebase.firestore.collection('stocks');\n * const query = collectionRef.where('price', '>', '0.01');\n * const fakeStock = new AngularFirestoreCollection<Stock>(collectionRef, query);\n *\n * // NOTE!: the updates are performed on the reference not the query\n * await fakeStock.add({ name: 'FAKE', price: 0.01 });\n *\n * // Subscribe to changes as snapshots. This provides you data updates as well as delta updates.\n * fakeStock.valueChanges().subscribe(value => console.log(value));\n */\nclass AngularFirestoreCollection {\n    /**\n     * The constructor takes in a CollectionReference and Query to provide wrapper methods\n     * for data operations and data streaming.\n     *\n     * Note: Data operation methods are done on the reference not the query. This means\n     * when you update data it is not updating data to the window of your query unless\n     * the data fits the criteria of the query. See the AssociatedRefence type for details\n     * on this implication.\n     */\n    constructor(ref, query, afs) {\n        this.ref = ref;\n        this.query = query;\n        this.afs = afs;\n    }\n    /**\n     * Listen to the latest change in the stream. This method returns changes\n     * as they occur and they are not sorted by query order. This allows you to construct\n     * your own data structure.\n     */\n    stateChanges(events) {\n        let source = docChanges(this.query, this.afs.schedulers.outsideAngular);\n        if (events && events.length > 0) {\n            source = source.pipe(map(actions => actions.filter(change => events.indexOf(change.type) > -1)));\n        }\n        return source.pipe(\n        // We want to filter out empty arrays, but always emit at first, so the developer knows\n        // that the collection has been resolve; even if it's empty\n        startWith(undefined), pairwise(), filter(([prior, current]) => current.length > 0 || !prior), map(([prior, current]) => current), keepUnstableUntilFirst);\n    }\n    /**\n     * Create a stream of changes as they occur it time. This method is similar to stateChanges()\n     * but it collects each event in an array over time.\n     */\n    auditTrail(events) {\n        return this.stateChanges(events).pipe(scan((current, action) => [...current, ...action], []));\n    }\n    /**\n     * Create a stream of synchronized changes. This method keeps the local array in sorted\n     * query order.\n     */\n    snapshotChanges(events) {\n        const validatedEvents = validateEventsArray(events);\n        const scheduledSortedChanges$ = sortedChanges(this.query, validatedEvents, this.afs.schedulers.outsideAngular);\n        return scheduledSortedChanges$.pipe(keepUnstableUntilFirst);\n    }\n    valueChanges(options = {}) {\n        return fromCollectionRef(this.query, this.afs.schedulers.outsideAngular)\n            .pipe(map(actions => actions.payload.docs.map(a => {\n            if (options.idField) {\n                return Object.assign(Object.assign({}, a.data()), { [options.idField]: a.id });\n            }\n            else {\n                return a.data();\n            }\n        })), keepUnstableUntilFirst);\n    }\n    /**\n     * Retrieve the results of the query once.\n     */\n    get(options) {\n        return from(this.query.get(options)).pipe(keepUnstableUntilFirst);\n    }\n    /**\n     * Add data to a collection reference.\n     *\n     * Note: Data operation methods are done on the reference not the query. This means\n     * when you update data it is not updating data to the window of your query unless\n     * the data fits the criteria of the query.\n     */\n    add(data) {\n        return this.ref.add(data);\n    }\n    /**\n     * Create a reference to a single document in a collection.\n     */\n    doc(path) {\n        // TODO is there a better way to solve this type issue\n        return new AngularFirestoreDocument(this.ref.doc(path), this.afs);\n    }\n}\n\n/**\n * AngularFirestoreDocument service\n *\n * This class creates a reference to a Firestore Document. A reference is provided in\n * in the constructor. The class is generic which gives you type safety for data update\n * methods and data streaming.\n *\n * This class uses Symbol.observable to transform into Observable using Observable.from().\n *\n * This class is rarely used directly and should be created from the AngularFirestore service.\n *\n * Example:\n *\n * const fakeStock = new AngularFirestoreDocument<Stock>(doc('stocks/FAKE'));\n * await fakeStock.set({ name: 'FAKE', price: 0.01 });\n * fakeStock.valueChanges().map(snap => {\n *   if(snap.exists) return snap.data();\n *   return null;\n * }).subscribe(value => console.log(value));\n * // OR! Transform using Observable.from() and the data is unwrapped for you\n * Observable.from(fakeStock).subscribe(value => console.log(value));\n */\nclass AngularFirestoreDocument {\n    /**\n     * The constructor takes in a DocumentReference to provide wrapper methods\n     * for data operations, data streaming, and Symbol.observable.\n     */\n    constructor(ref, afs) {\n        this.ref = ref;\n        this.afs = afs;\n    }\n    /**\n     * Create or overwrite a single document.\n     */\n    set(data, options) {\n        return this.ref.set(data, options);\n    }\n    /**\n     * Update some fields of a document without overwriting the entire document.\n     */\n    update(data) {\n        return this.ref.update(data);\n    }\n    /**\n     * Delete a document.\n     */\n    delete() {\n        return this.ref.delete();\n    }\n    /**\n     * Create a reference to a sub-collection given a path and an optional query\n     * function.\n     */\n    collection(path, queryFn) {\n        const collectionRef = this.ref.collection(path);\n        const { ref, query } = associateQuery(collectionRef, queryFn);\n        return new AngularFirestoreCollection(ref, query, this.afs);\n    }\n    /**\n     * Listen to snapshot updates from the document.\n     */\n    snapshotChanges() {\n        const scheduledFromDocRef$ = fromDocRef(this.ref, this.afs.schedulers.outsideAngular);\n        return scheduledFromDocRef$.pipe(keepUnstableUntilFirst);\n    }\n    valueChanges(options = {}) {\n        return this.snapshotChanges().pipe(map(({ payload }) => options.idField ? Object.assign(Object.assign({}, payload.data()), { [options.idField]: payload.id }) : payload.data()));\n    }\n    /**\n     * Retrieve the document once.\n     */\n    get(options) {\n        return from(this.ref.get(options)).pipe(keepUnstableUntilFirst);\n    }\n}\n\n/**\n * AngularFirestoreCollectionGroup service\n *\n * This class holds a reference to a Firestore Collection Group Query.\n *\n * This class uses Symbol.observable to transform into Observable using Observable.from().\n *\n * This class is rarely used directly and should be created from the AngularFirestore service.\n *\n * Example:\n *\n * const collectionGroup = firebase.firestore.collectionGroup('stocks');\n * const query = collectionRef.where('price', '>', '0.01');\n * const fakeStock = new AngularFirestoreCollectionGroup<Stock>(query, afs);\n *\n * // Subscribe to changes as snapshots. This provides you data updates as well as delta updates.\n * fakeStock.valueChanges().subscribe(value => console.log(value));\n */\nclass AngularFirestoreCollectionGroup {\n    /**\n     * The constructor takes in a CollectionGroupQuery to provide wrapper methods\n     * for data operations and data streaming.\n     */\n    constructor(query, afs) {\n        this.query = query;\n        this.afs = afs;\n    }\n    /**\n     * Listen to the latest change in the stream. This method returns changes\n     * as they occur and they are not sorted by query order. This allows you to construct\n     * your own data structure.\n     */\n    stateChanges(events) {\n        if (!events || events.length === 0) {\n            return docChanges(this.query, this.afs.schedulers.outsideAngular).pipe(keepUnstableUntilFirst);\n        }\n        return docChanges(this.query, this.afs.schedulers.outsideAngular)\n            .pipe(map(actions => actions.filter(change => events.indexOf(change.type) > -1)), filter(changes => changes.length > 0), keepUnstableUntilFirst);\n    }\n    /**\n     * Create a stream of changes as they occur it time. This method is similar to stateChanges()\n     * but it collects each event in an array over time.\n     */\n    auditTrail(events) {\n        return this.stateChanges(events).pipe(scan((current, action) => [...current, ...action], []));\n    }\n    /**\n     * Create a stream of synchronized changes. This method keeps the local array in sorted\n     * query order.\n     */\n    snapshotChanges(events) {\n        const validatedEvents = validateEventsArray(events);\n        const scheduledSortedChanges$ = sortedChanges(this.query, validatedEvents, this.afs.schedulers.outsideAngular);\n        return scheduledSortedChanges$.pipe(keepUnstableUntilFirst);\n    }\n    valueChanges(options = {}) {\n        const fromCollectionRefScheduled$ = fromCollectionRef(this.query, this.afs.schedulers.outsideAngular);\n        return fromCollectionRefScheduled$\n            .pipe(map(actions => actions.payload.docs.map(a => {\n            if (options.idField) {\n                return Object.assign({ [options.idField]: a.id }, a.data());\n            }\n            else {\n                return a.data();\n            }\n        })), keepUnstableUntilFirst);\n    }\n    /**\n     * Retrieve the results of the query once.\n     */\n    get(options) {\n        return from(this.query.get(options)).pipe(keepUnstableUntilFirst);\n    }\n}\n\n/**\n * The value of this token determines whether or not the firestore will have persistance enabled\n */\nconst ENABLE_PERSISTENCE = new InjectionToken('angularfire2.enableFirestorePersistence');\nconst PERSISTENCE_SETTINGS = new InjectionToken('angularfire2.firestore.persistenceSettings');\nconst SETTINGS = new InjectionToken('angularfire2.firestore.settings');\nconst USE_EMULATOR = new InjectionToken('angularfire2.firestore.use-emulator');\n/**\n * A utility methods for associating a collection reference with\n * a query.\n *\n * @param collectionRef - A collection reference to query\n * @param queryFn - The callback to create a query\n *\n * Example:\n * const { query, ref } = associateQuery(docRef.collection('items'), ref => {\n *  return ref.where('age', '<', 200);\n * });\n */\nfunction associateQuery(collectionRef, queryFn = ref => ref) {\n    const query = queryFn(collectionRef);\n    const ref = collectionRef;\n    return { query, ref };\n}\n/**\n * AngularFirestore Service\n *\n * This service is the main entry point for this feature module. It provides\n * an API for creating Collection and Reference services. These services can\n * then be used to do data updates and observable streams of the data.\n *\n * Example:\n *\n * import { Component } from '@angular/core';\n * import { AngularFirestore, AngularFirestoreCollection, AngularFirestoreDocument } from '@angular/fire/firestore';\n * import { Observable } from 'rxjs/Observable';\n * import { from } from 'rxjs/observable';\n *\n * @Component({\n *   selector: 'app-my-component',\n *   template: `\n *    <h2>Items for {{ (profile | async)?.name }}\n *    <ul>\n *       <li *ngFor=\"let item of items | async\">{{ item.name }}</li>\n *    </ul>\n *    <div class=\"control-input\">\n *       <input type=\"text\" #itemname />\n *       <button (click)=\"addItem(itemname.value)\">Add Item</button>\n *    </div>\n *   `\n * })\n * export class MyComponent implements OnInit {\n *\n *   // services for data operations and data streaming\n *   private readonly itemsRef: AngularFirestoreCollection<Item>;\n *   private readonly profileRef: AngularFirestoreDocument<Profile>;\n *\n *   // observables for template\n *   items: Observable<Item[]>;\n *   profile: Observable<Profile>;\n *\n *   // inject main service\n *   constructor(private readonly afs: AngularFirestore) {}\n *\n *   ngOnInit() {\n *     this.itemsRef = afs.collection('items', ref => ref.where('user', '==', 'davideast').limit(10));\n *     this.items = this.itemsRef.valueChanges().map(snap => snap.docs.map(data => doc.data()));\n *     // this.items = from(this.itemsRef); // you can also do this with no mapping\n *\n *     this.profileRef = afs.doc('users/davideast');\n *     this.profile = this.profileRef.valueChanges();\n *   }\n *\n *   addItem(name: string) {\n *     const user = 'davideast';\n *     this.itemsRef.add({ name, user });\n *   }\n * }\n */\nclass AngularFirestore {\n    /**\n     * Each Feature of AngularFire has a FirebaseApp injected. This way we\n     * don't rely on the main Firebase App instance and we can create named\n     * apps and use multiple apps.\n     */\n    constructor(options, name, shouldEnablePersistence, settings, \n    // tslint:disable-next-line:ban-types\n    platformId, zone, schedulers, persistenceSettings, _useEmulator, auth, useAuthEmulator, authSettings, // can't use firebase.auth.AuthSettings here\n    tenantId, languageCode, useDeviceLanguage, persistence, _appCheckInstances) {\n        this.schedulers = schedulers;\n        const app = ɵfirebaseAppFactory(options, zone, name);\n        const useEmulator = _useEmulator;\n        if (auth) {\n            ɵauthFactory(app, zone, useAuthEmulator, tenantId, languageCode, useDeviceLanguage, authSettings, persistence);\n        }\n        [this.firestore, this.persistenceEnabled$] = ɵcacheInstance(`${app.name}.firestore`, 'AngularFirestore', app.name, () => {\n            const firestore = zone.runOutsideAngular(() => app.firestore());\n            if (settings) {\n                firestore.settings(settings);\n            }\n            if (useEmulator) {\n                firestore.useEmulator(...useEmulator);\n            }\n            if (shouldEnablePersistence && !isPlatformServer(platformId)) {\n                // We need to try/catch here because not all enablePersistence() failures are caught\n                // https://github.com/firebase/firebase-js-sdk/issues/608\n                const enablePersistence = () => {\n                    try {\n                        return from(firestore.enablePersistence(persistenceSettings || undefined).then(() => true, () => false));\n                    }\n                    catch (e) {\n                        if (typeof console !== 'undefined') {\n                            console.warn(e);\n                        }\n                        return of(false);\n                    }\n                };\n                return [firestore, zone.runOutsideAngular(enablePersistence)];\n            }\n            else {\n                return [firestore, of(false)];\n            }\n        }, [settings, useEmulator, shouldEnablePersistence]);\n    }\n    collection(pathOrRef, queryFn) {\n        let collectionRef;\n        if (typeof pathOrRef === 'string') {\n            collectionRef = this.firestore.collection(pathOrRef);\n        }\n        else {\n            collectionRef = pathOrRef;\n        }\n        const { ref, query } = associateQuery(collectionRef, queryFn);\n        const refInZone = this.schedulers.ngZone.run(() => ref);\n        return new AngularFirestoreCollection(refInZone, query, this);\n    }\n    /**\n     * Create a reference to a Firestore Collection Group based on a collectionId\n     * and an optional query function to narrow the result\n     * set.\n     */\n    collectionGroup(collectionId, queryGroupFn) {\n        const queryFn = queryGroupFn || (ref => ref);\n        const collectionGroup = this.firestore.collectionGroup(collectionId);\n        return new AngularFirestoreCollectionGroup(queryFn(collectionGroup), this);\n    }\n    doc(pathOrRef) {\n        let ref;\n        if (typeof pathOrRef === 'string') {\n            ref = this.firestore.doc(pathOrRef);\n        }\n        else {\n            ref = pathOrRef;\n        }\n        const refInZone = this.schedulers.ngZone.run(() => ref);\n        return new AngularFirestoreDocument(refInZone, this);\n    }\n    /**\n     * Returns a generated Firestore Document Id.\n     */\n    createId() {\n        return this.firestore.collection('_').doc().id;\n    }\n}\nAngularFirestore.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestore, deps: [{ token: FIREBASE_OPTIONS }, { token: FIREBASE_APP_NAME, optional: true }, { token: ENABLE_PERSISTENCE, optional: true }, { token: SETTINGS, optional: true }, { token: PLATFORM_ID }, { token: i0.NgZone }, { token: i1.ɵAngularFireSchedulers }, { token: PERSISTENCE_SETTINGS, optional: true }, { token: USE_EMULATOR, optional: true }, { token: i2.AngularFireAuth, optional: true }, { token: USE_EMULATOR$1, optional: true }, { token: SETTINGS$1, optional: true }, { token: TENANT_ID, optional: true }, { token: LANGUAGE_CODE, optional: true }, { token: USE_DEVICE_LANGUAGE, optional: true }, { token: PERSISTENCE, optional: true }, { token: i3.AppCheckInstances, optional: true }], target: i0.ɵɵFactoryTarget.Injectable });\nAngularFirestore.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestore, providedIn: 'any' });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestore, decorators: [{\n            type: Injectable,\n            args: [{\n                    providedIn: 'any'\n                }]\n        }], ctorParameters: function () { return [{ type: undefined, decorators: [{\n                    type: Inject,\n                    args: [FIREBASE_OPTIONS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [FIREBASE_APP_NAME]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [ENABLE_PERSISTENCE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [SETTINGS]\n                }] }, { type: Object, decorators: [{\n                    type: Inject,\n                    args: [PLATFORM_ID]\n                }] }, { type: i0.NgZone }, { type: i1.ɵAngularFireSchedulers }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [PERSISTENCE_SETTINGS]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [USE_EMULATOR]\n                }] }, { type: i2.AngularFireAuth, decorators: [{\n                    type: Optional\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [USE_EMULATOR$1]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [SETTINGS$1]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [TENANT_ID]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [LANGUAGE_CODE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [USE_DEVICE_LANGUAGE]\n                }] }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [PERSISTENCE]\n                }] }, { type: i3.AppCheckInstances, decorators: [{\n                    type: Optional\n                }] }]; } });\n\nclass AngularFirestoreModule {\n    constructor() {\n        firebase.registerVersion('angularfire', VERSION.full, 'fst-compat');\n    }\n    /**\n     * Attempt to enable persistent storage, if possible\n     */\n    static enablePersistence(persistenceSettings) {\n        return {\n            ngModule: AngularFirestoreModule,\n            providers: [\n                { provide: ENABLE_PERSISTENCE, useValue: true },\n                { provide: PERSISTENCE_SETTINGS, useValue: persistenceSettings },\n            ]\n        };\n    }\n}\nAngularFirestoreModule.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestoreModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule });\nAngularFirestoreModule.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestoreModule });\nAngularFirestoreModule.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestoreModule, providers: [AngularFirestore] });\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"12.1.3\", ngImport: i0, type: AngularFirestoreModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    providers: [AngularFirestore]\n                }]\n        }], ctorParameters: function () { return []; } });\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { AngularFirestore, AngularFirestoreCollection, AngularFirestoreCollectionGroup, AngularFirestoreDocument, AngularFirestoreModule, ENABLE_PERSISTENCE, PERSISTENCE_SETTINGS, SETTINGS, USE_EMULATOR, associateQuery, combineChange, combineChanges, docChanges, fromCollectionRef, fromDocRef, fromRef, sortedChanges, validateEventsArray };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,WAAW,EAAEC,UAAU,EAAEC,MAAM,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AACnG,SAASC,cAAc,EAAEC,UAAU,EAAEC,IAAI,EAAEC,EAAE,QAAQ,MAAM;AAC3D,OAAO,KAAKC,EAAE,MAAM,eAAe;AACnC,SAASC,sBAAsB,EAAEC,OAAO,QAAQ,eAAe;AAC/D,SAASC,SAAS,EAAEC,QAAQ,EAAEC,GAAG,EAAEC,IAAI,EAAEC,oBAAoB,EAAEC,MAAM,QAAQ,gBAAgB;AAC7F,SAASC,mBAAmB,EAAEC,cAAc,EAAEC,gBAAgB,EAAEC,iBAAiB,QAAQ,sBAAsB;AAC/G,SAASC,gBAAgB,QAAQ,iBAAiB;AAClD,OAAO,sBAAsB;AAC7B,OAAO,2BAA2B;AAClC,OAAO,KAAKC,EAAE,MAAM,2BAA2B;AAC/C,SAASC,YAAY,EAAEC,YAAY,IAAIC,cAAc,EAAEC,QAAQ,IAAIC,UAAU,EAAEC,SAAS,EAAEC,aAAa,EAAEC,mBAAmB,EAAEC,WAAW,QAAQ,2BAA2B;AAC5K,OAAO,KAAKC,EAAE,MAAM,yBAAyB;AAC7C,OAAOC,QAAQ,MAAM,qBAAqB;AAE1C,SAASC,QAAQA,CAACC,GAAG,EAAEC,SAAS,GAAGhC,cAAc,EAAE;EAC/C,OAAO,IAAIC,UAAU,CAACgC,UAAU,IAAI;IAChC,IAAIC,WAAW;IACf,IAAIF,SAAS,IAAI,IAAI,EAAE;MACnBA,SAAS,CAACG,QAAQ,CAAC,MAAM;QACrBD,WAAW,GAAGH,GAAG,CAACK,UAAU,CAAC;UAAEC,sBAAsB,EAAE;QAAK,CAAC,EAAEJ,UAAU,CAAC;MAC9E,CAAC,CAAC;IACN,CAAC,MACI;MACDC,WAAW,GAAGH,GAAG,CAACK,UAAU,CAAC;QAAEC,sBAAsB,EAAE;MAAK,CAAC,EAAEJ,UAAU,CAAC;IAC9E;IACA,OAAO,MAAM;MACT,IAAIC,WAAW,IAAI,IAAI,EAAE;QACrBA,WAAW,CAAC,CAAC;MACjB;IACJ,CAAC;EACL,CAAC,CAAC;AACN;AACA,SAASI,OAAOA,CAACP,GAAG,EAAEC,SAAS,EAAE;EAC7B,OAAOF,QAAQ,CAACC,GAAG,EAAEC,SAAS,CAAC;AACnC;AACA,SAASO,UAAUA,CAACR,GAAG,EAAEC,SAAS,EAAE;EAChC,OAAOM,OAAO,CAACP,GAAG,EAAEC,SAAS,CAAC,CACzBQ,IAAI,CAACjC,SAAS,CAACkC,SAAS,CAAC,EAAEjC,QAAQ,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAACiC,YAAY,EAAEC,OAAO,CAAC,KAAK;IACzE,IAAI,CAACA,OAAO,CAACC,MAAM,EAAE;MACjB,OAAO;QAAED,OAAO;QAAEE,IAAI,EAAE;MAAU,CAAC;IACvC;IACA,IAAI,EAAEH,YAAY,KAAK,IAAI,IAAIA,YAAY,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,YAAY,CAACE,MAAM,CAAC,EAAE;MACpF,OAAO;QAAED,OAAO;QAAEE,IAAI,EAAE;MAAQ,CAAC;IACrC;IACA,OAAO;MAAEF,OAAO;MAAEE,IAAI,EAAE;IAAW,CAAC;EACxC,CAAC,CAAC,CAAC;AACP;AACA,SAASC,iBAAiBA,CAACf,GAAG,EAAEC,SAAS,EAAE;EACvC,OAAOM,OAAO,CAACP,GAAG,EAAEC,SAAS,CAAC,CAACQ,IAAI,CAAC/B,GAAG,CAACkC,OAAO,KAAK;IAAEA,OAAO;IAAEE,IAAI,EAAE;EAAQ,CAAC,CAAC,CAAC,CAAC;AACrF;;AAEA;AACA;AACA;AACA;AACA,SAASE,UAAUA,CAACC,KAAK,EAAEhB,SAAS,EAAE;EAClC,OAAOc,iBAAiB,CAACE,KAAK,EAAEhB,SAAS,CAAC,CACrCQ,IAAI,CAACjC,SAAS,CAACkC,SAAS,CAAC,EAAEjC,QAAQ,CAAC,CAAC,EAAEC,GAAG,CAAC,CAAC,CAACwC,WAAW,EAAEC,MAAM,CAAC,KAAK;IACvE,MAAMH,UAAU,GAAGG,MAAM,CAACP,OAAO,CAACI,UAAU,CAAC,CAAC;IAC9C,MAAMI,OAAO,GAAGJ,UAAU,CAACtC,GAAG,CAAC2C,MAAM,KAAK;MAAEP,IAAI,EAAEO,MAAM,CAACP,IAAI;MAAEF,OAAO,EAAES;IAAO,CAAC,CAAC,CAAC;IAClF;IACA,IAAIH,WAAW,IAAII,IAAI,CAACC,SAAS,CAACL,WAAW,CAACN,OAAO,CAACY,QAAQ,CAAC,KAAKF,IAAI,CAACC,SAAS,CAACJ,MAAM,CAACP,OAAO,CAACY,QAAQ,CAAC,EAAE;MACzG;MACAL,MAAM,CAACP,OAAO,CAACa,IAAI,CAACC,OAAO,CAAC,CAACC,UAAU,EAAEC,YAAY,KAAK;QACtD,MAAMC,SAAS,GAAGb,UAAU,CAACc,IAAI,CAACC,CAAC,IAAIA,CAAC,CAACC,GAAG,CAAChC,GAAG,CAACiC,OAAO,CAACN,UAAU,CAAC3B,GAAG,CAAC,CAAC;QACzE,MAAMkC,QAAQ,GAAGhB,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAACN,OAAO,CAACa,IAAI,CAACK,IAAI,CAACC,CAAC,IAAIA,CAAC,CAAC/B,GAAG,CAACiC,OAAO,CAACN,UAAU,CAAC3B,GAAG,CAAC,CAAC;QAC5I,IAAI6B,SAAS,IAAIP,IAAI,CAACC,SAAS,CAACM,SAAS,CAACG,GAAG,CAACR,QAAQ,CAAC,KAAKF,IAAI,CAACC,SAAS,CAACI,UAAU,CAACH,QAAQ,CAAC,IAC3F,CAACK,SAAS,IAAIK,QAAQ,IAAIZ,IAAI,CAACC,SAAS,CAACW,QAAQ,CAACV,QAAQ,CAAC,KAAKF,IAAI,CAACC,SAAS,CAACI,UAAU,CAACH,QAAQ,CAAC,EAAE;UACrG;QAAA,CACH,MACI;UACD;UACAJ,OAAO,CAACe,IAAI,CAAC;YACTrB,IAAI,EAAE,UAAU;YAChBF,OAAO,EAAE;cACLwB,QAAQ,EAAER,YAAY;cACtBS,QAAQ,EAAET,YAAY;cACtBd,IAAI,EAAE,UAAU;cAChBkB,GAAG,EAAEL;YACT;UACJ,CAAC,CAAC;QACN;MACJ,CAAC,CAAC;IACN;IACA,OAAOP,OAAO;EAClB,CAAC,CAAC,CAAC;AACP;AACA;AACA;AACA;AACA,SAASkB,aAAaA,CAACrB,KAAK,EAAEsB,MAAM,EAAEtC,SAAS,EAAE;EAC7C,OAAOe,UAAU,CAACC,KAAK,EAAEhB,SAAS,CAAC,CAC9BQ,IAAI,CAAC9B,IAAI,CAAC,CAAC6D,OAAO,EAAEC,OAAO,KAAKC,cAAc,CAACF,OAAO,EAAEC,OAAO,CAAC/D,GAAG,CAACiE,EAAE,IAAIA,EAAE,CAAC/B,OAAO,CAAC,EAAE2B,MAAM,CAAC,EAAE,EAAE,CAAC,EAAE3D,oBAAoB,CAAC,CAAC;EAAE;EAClIF,GAAG,CAAC+D,OAAO,IAAIA,OAAO,CAAC/D,GAAG,CAACkE,CAAC,KAAK;IAAE9B,IAAI,EAAE8B,CAAC,CAAC9B,IAAI;IAAEF,OAAO,EAAEgC;EAAE,CAAC,CAAC,CAAC,CAAC,CAAC;AACrE;AACA;AACA;AACA;AACA;AACA,SAASF,cAAcA,CAACF,OAAO,EAAEC,OAAO,EAAEF,MAAM,EAAE;EAC9CE,OAAO,CAACf,OAAO,CAACL,MAAM,IAAI;IACtB;IACA,IAAIkB,MAAM,CAACM,OAAO,CAACxB,MAAM,CAACP,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE;MAClC0B,OAAO,GAAGM,aAAa,CAACN,OAAO,EAAEnB,MAAM,CAAC;IAC5C;EACJ,CAAC,CAAC;EACF,OAAOmB,OAAO;AAClB;AACA;AACA;AACA;AACA;AACA,SAASO,cAAcA,CAACC,QAAQ,EAAEC,KAAK,EAAEC,WAAW,EAAE,GAAGC,IAAI,EAAE;EAC3D,MAAMC,WAAW,GAAGJ,QAAQ,CAACK,KAAK,CAAC,CAAC;EACpCD,WAAW,CAACE,MAAM,CAACL,KAAK,EAAEC,WAAW,EAAE,GAAGC,IAAI,CAAC;EAC/C,OAAOC,WAAW;AACtB;AACA;AACA;AACA;AACA;AACA;AACA,SAASN,aAAaA,CAACS,QAAQ,EAAElC,MAAM,EAAE;EACrC,QAAQA,MAAM,CAACP,IAAI;IACf,KAAK,OAAO;MACR,IAAIyC,QAAQ,CAAClC,MAAM,CAACgB,QAAQ,CAAC,IAAIkB,QAAQ,CAAClC,MAAM,CAACgB,QAAQ,CAAC,CAACL,GAAG,CAAChC,GAAG,CAACiC,OAAO,CAACZ,MAAM,CAACW,GAAG,CAAChC,GAAG,CAAC,EAAE;QACxF;MAAA,CACH,MACI;QACD,OAAO+C,cAAc,CAACQ,QAAQ,EAAElC,MAAM,CAACgB,QAAQ,EAAE,CAAC,EAAEhB,MAAM,CAAC;MAC/D;MACA;IACJ,KAAK,UAAU;MACX,IAAIkC,QAAQ,CAAClC,MAAM,CAACe,QAAQ,CAAC,IAAI,IAAI,IAAImB,QAAQ,CAAClC,MAAM,CAACe,QAAQ,CAAC,CAACJ,GAAG,CAAChC,GAAG,CAACiC,OAAO,CAACZ,MAAM,CAACW,GAAG,CAAChC,GAAG,CAAC,EAAE;QAChG;QACA;QACA,IAAIqB,MAAM,CAACe,QAAQ,KAAKf,MAAM,CAACgB,QAAQ,EAAE;UACrC,MAAMmB,WAAW,GAAGD,QAAQ,CAACF,KAAK,CAAC,CAAC;UACpCG,WAAW,CAACF,MAAM,CAACjC,MAAM,CAACe,QAAQ,EAAE,CAAC,CAAC;UACtCoB,WAAW,CAACF,MAAM,CAACjC,MAAM,CAACgB,QAAQ,EAAE,CAAC,EAAEhB,MAAM,CAAC;UAC9C,OAAOmC,WAAW;QACtB,CAAC,MACI;UACD,OAAOT,cAAc,CAACQ,QAAQ,EAAElC,MAAM,CAACgB,QAAQ,EAAE,CAAC,EAAEhB,MAAM,CAAC;QAC/D;MACJ;MACA;IACJ,KAAK,SAAS;MACV,IAAIkC,QAAQ,CAAClC,MAAM,CAACe,QAAQ,CAAC,IAAImB,QAAQ,CAAClC,MAAM,CAACe,QAAQ,CAAC,CAACJ,GAAG,CAAChC,GAAG,CAACiC,OAAO,CAACZ,MAAM,CAACW,GAAG,CAAChC,GAAG,CAAC,EAAE;QACxF,OAAO+C,cAAc,CAACQ,QAAQ,EAAElC,MAAM,CAACe,QAAQ,EAAE,CAAC,CAAC;MACvD;MACA;EACR;EACA,OAAOmB,QAAQ;AACnB;AAEA,SAASE,mBAAmBA,CAAClB,MAAM,EAAE;EACjC,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACmB,MAAM,KAAK,CAAC,EAAE;IAChCnB,MAAM,GAAG,CAAC,OAAO,EAAE,SAAS,EAAE,UAAU,CAAC;EAC7C;EACA,OAAOA,MAAM;AACjB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMoB,0BAA0B,CAAC;EAC7B;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIC,WAAWA,CAAC5D,GAAG,EAAEiB,KAAK,EAAE4C,GAAG,EAAE;IACzB,IAAI,CAAC7D,GAAG,GAAGA,GAAG;IACd,IAAI,CAACiB,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC4C,GAAG,GAAGA,GAAG;EAClB;EACA;AACJ;AACA;AACA;AACA;EACIC,YAAYA,CAACvB,MAAM,EAAE;IACjB,IAAIwB,MAAM,GAAG/C,UAAU,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAAC4C,GAAG,CAACG,UAAU,CAACC,cAAc,CAAC;IACvE,IAAI1B,MAAM,IAAIA,MAAM,CAACmB,MAAM,GAAG,CAAC,EAAE;MAC7BK,MAAM,GAAGA,MAAM,CAACtD,IAAI,CAAC/B,GAAG,CAAC0C,OAAO,IAAIA,OAAO,CAACvC,MAAM,CAACwC,MAAM,IAAIkB,MAAM,CAACM,OAAO,CAACxB,MAAM,CAACP,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC;IACpG;IACA,OAAOiD,MAAM,CAACtD,IAAI;IAClB;IACA;IACAjC,SAAS,CAACkC,SAAS,CAAC,EAAEjC,QAAQ,CAAC,CAAC,EAAEI,MAAM,CAAC,CAAC,CAACqF,KAAK,EAAE1B,OAAO,CAAC,KAAKA,OAAO,CAACkB,MAAM,GAAG,CAAC,IAAI,CAACQ,KAAK,CAAC,EAAExF,GAAG,CAAC,CAAC,CAACwF,KAAK,EAAE1B,OAAO,CAAC,KAAKA,OAAO,CAAC,EAAElE,sBAAsB,CAAC;EAC7J;EACA;AACJ;AACA;AACA;EACI6F,UAAUA,CAAC5B,MAAM,EAAE;IACf,OAAO,IAAI,CAACuB,YAAY,CAACvB,MAAM,CAAC,CAAC9B,IAAI,CAAC9B,IAAI,CAAC,CAAC6D,OAAO,EAAErB,MAAM,KAAK,CAAC,GAAGqB,OAAO,EAAE,GAAGrB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;EACjG;EACA;AACJ;AACA;AACA;EACIiD,eAAeA,CAAC7B,MAAM,EAAE;IACpB,MAAM8B,eAAe,GAAGZ,mBAAmB,CAAClB,MAAM,CAAC;IACnD,MAAM+B,uBAAuB,GAAGhC,aAAa,CAAC,IAAI,CAACrB,KAAK,EAAEoD,eAAe,EAAE,IAAI,CAACR,GAAG,CAACG,UAAU,CAACC,cAAc,CAAC;IAC9G,OAAOK,uBAAuB,CAAC7D,IAAI,CAACnC,sBAAsB,CAAC;EAC/D;EACAiG,YAAYA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACvB,OAAOzD,iBAAiB,CAAC,IAAI,CAACE,KAAK,EAAE,IAAI,CAAC4C,GAAG,CAACG,UAAU,CAACC,cAAc,CAAC,CACnExD,IAAI,CAAC/B,GAAG,CAAC0C,OAAO,IAAIA,OAAO,CAACR,OAAO,CAACa,IAAI,CAAC/C,GAAG,CAAC+F,CAAC,IAAI;MACnD,IAAID,OAAO,CAACE,OAAO,EAAE;QACjB,OAAOC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,CAAC,CAACI,IAAI,CAAC,CAAC,CAAC,EAAE;UAAE,CAACL,OAAO,CAACE,OAAO,GAAGD,CAAC,CAACK;QAAG,CAAC,CAAC;MAClF,CAAC,MACI;QACD,OAAOL,CAAC,CAACI,IAAI,CAAC,CAAC;MACnB;IACJ,CAAC,CAAC,CAAC,EAAEvG,sBAAsB,CAAC;EAChC;EACA;AACJ;AACA;EACIyG,GAAGA,CAACP,OAAO,EAAE;IACT,OAAOrG,IAAI,CAAC,IAAI,CAAC8C,KAAK,CAAC8D,GAAG,CAACP,OAAO,CAAC,CAAC,CAAC/D,IAAI,CAACnC,sBAAsB,CAAC;EACrE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACI0G,GAAGA,CAACH,IAAI,EAAE;IACN,OAAO,IAAI,CAAC7E,GAAG,CAACgF,GAAG,CAACH,IAAI,CAAC;EAC7B;EACA;AACJ;AACA;EACI7C,GAAGA,CAACiD,IAAI,EAAE;IACN;IACA,OAAO,IAAIC,wBAAwB,CAAC,IAAI,CAAClF,GAAG,CAACgC,GAAG,CAACiD,IAAI,CAAC,EAAE,IAAI,CAACpB,GAAG,CAAC;EACrE;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqB,wBAAwB,CAAC;EAC3B;AACJ;AACA;AACA;EACItB,WAAWA,CAAC5D,GAAG,EAAE6D,GAAG,EAAE;IAClB,IAAI,CAAC7D,GAAG,GAAGA,GAAG;IACd,IAAI,CAAC6D,GAAG,GAAGA,GAAG;EAClB;EACA;AACJ;AACA;EACIsB,GAAGA,CAACN,IAAI,EAAEL,OAAO,EAAE;IACf,OAAO,IAAI,CAACxE,GAAG,CAACmF,GAAG,CAACN,IAAI,EAAEL,OAAO,CAAC;EACtC;EACA;AACJ;AACA;EACIY,MAAMA,CAACP,IAAI,EAAE;IACT,OAAO,IAAI,CAAC7E,GAAG,CAACoF,MAAM,CAACP,IAAI,CAAC;EAChC;EACA;AACJ;AACA;EACIQ,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACrF,GAAG,CAACqF,MAAM,CAAC,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACIC,UAAUA,CAACL,IAAI,EAAEM,OAAO,EAAE;IACtB,MAAMC,aAAa,GAAG,IAAI,CAACxF,GAAG,CAACsF,UAAU,CAACL,IAAI,CAAC;IAC/C,MAAM;MAAEjF,GAAG;MAAEiB;IAAM,CAAC,GAAGwE,cAAc,CAACD,aAAa,EAAED,OAAO,CAAC;IAC7D,OAAO,IAAI5B,0BAA0B,CAAC3D,GAAG,EAAEiB,KAAK,EAAE,IAAI,CAAC4C,GAAG,CAAC;EAC/D;EACA;AACJ;AACA;EACIO,eAAeA,CAAA,EAAG;IACd,MAAMsB,oBAAoB,GAAGlF,UAAU,CAAC,IAAI,CAACR,GAAG,EAAE,IAAI,CAAC6D,GAAG,CAACG,UAAU,CAACC,cAAc,CAAC;IACrF,OAAOyB,oBAAoB,CAACjF,IAAI,CAACnC,sBAAsB,CAAC;EAC5D;EACAiG,YAAYA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACvB,OAAO,IAAI,CAACJ,eAAe,CAAC,CAAC,CAAC3D,IAAI,CAAC/B,GAAG,CAAC,CAAC;MAAEkC;IAAQ,CAAC,KAAK4D,OAAO,CAACE,OAAO,GAAGC,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEhE,OAAO,CAACiE,IAAI,CAAC,CAAC,CAAC,EAAE;MAAE,CAACL,OAAO,CAACE,OAAO,GAAG9D,OAAO,CAACkE;IAAG,CAAC,CAAC,GAAGlE,OAAO,CAACiE,IAAI,CAAC,CAAC,CAAC,CAAC;EACpL;EACA;AACJ;AACA;EACIE,GAAGA,CAACP,OAAO,EAAE;IACT,OAAOrG,IAAI,CAAC,IAAI,CAAC6B,GAAG,CAAC+E,GAAG,CAACP,OAAO,CAAC,CAAC,CAAC/D,IAAI,CAACnC,sBAAsB,CAAC;EACnE;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMqH,+BAA+B,CAAC;EAClC;AACJ;AACA;AACA;EACI/B,WAAWA,CAAC3C,KAAK,EAAE4C,GAAG,EAAE;IACpB,IAAI,CAAC5C,KAAK,GAAGA,KAAK;IAClB,IAAI,CAAC4C,GAAG,GAAGA,GAAG;EAClB;EACA;AACJ;AACA;AACA;AACA;EACIC,YAAYA,CAACvB,MAAM,EAAE;IACjB,IAAI,CAACA,MAAM,IAAIA,MAAM,CAACmB,MAAM,KAAK,CAAC,EAAE;MAChC,OAAO1C,UAAU,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAAC4C,GAAG,CAACG,UAAU,CAACC,cAAc,CAAC,CAACxD,IAAI,CAACnC,sBAAsB,CAAC;IAClG;IACA,OAAO0C,UAAU,CAAC,IAAI,CAACC,KAAK,EAAE,IAAI,CAAC4C,GAAG,CAACG,UAAU,CAACC,cAAc,CAAC,CAC5DxD,IAAI,CAAC/B,GAAG,CAAC0C,OAAO,IAAIA,OAAO,CAACvC,MAAM,CAACwC,MAAM,IAAIkB,MAAM,CAACM,OAAO,CAACxB,MAAM,CAACP,IAAI,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,EAAEjC,MAAM,CAAC4D,OAAO,IAAIA,OAAO,CAACiB,MAAM,GAAG,CAAC,CAAC,EAAEpF,sBAAsB,CAAC;EACxJ;EACA;AACJ;AACA;AACA;EACI6F,UAAUA,CAAC5B,MAAM,EAAE;IACf,OAAO,IAAI,CAACuB,YAAY,CAACvB,MAAM,CAAC,CAAC9B,IAAI,CAAC9B,IAAI,CAAC,CAAC6D,OAAO,EAAErB,MAAM,KAAK,CAAC,GAAGqB,OAAO,EAAE,GAAGrB,MAAM,CAAC,EAAE,EAAE,CAAC,CAAC;EACjG;EACA;AACJ;AACA;AACA;EACIiD,eAAeA,CAAC7B,MAAM,EAAE;IACpB,MAAM8B,eAAe,GAAGZ,mBAAmB,CAAClB,MAAM,CAAC;IACnD,MAAM+B,uBAAuB,GAAGhC,aAAa,CAAC,IAAI,CAACrB,KAAK,EAAEoD,eAAe,EAAE,IAAI,CAACR,GAAG,CAACG,UAAU,CAACC,cAAc,CAAC;IAC9G,OAAOK,uBAAuB,CAAC7D,IAAI,CAACnC,sBAAsB,CAAC;EAC/D;EACAiG,YAAYA,CAACC,OAAO,GAAG,CAAC,CAAC,EAAE;IACvB,MAAMoB,2BAA2B,GAAG7E,iBAAiB,CAAC,IAAI,CAACE,KAAK,EAAE,IAAI,CAAC4C,GAAG,CAACG,UAAU,CAACC,cAAc,CAAC;IACrG,OAAO2B,2BAA2B,CAC7BnF,IAAI,CAAC/B,GAAG,CAAC0C,OAAO,IAAIA,OAAO,CAACR,OAAO,CAACa,IAAI,CAAC/C,GAAG,CAAC+F,CAAC,IAAI;MACnD,IAAID,OAAO,CAACE,OAAO,EAAE;QACjB,OAAOC,MAAM,CAACC,MAAM,CAAC;UAAE,CAACJ,OAAO,CAACE,OAAO,GAAGD,CAAC,CAACK;QAAG,CAAC,EAAEL,CAAC,CAACI,IAAI,CAAC,CAAC,CAAC;MAC/D,CAAC,MACI;QACD,OAAOJ,CAAC,CAACI,IAAI,CAAC,CAAC;MACnB;IACJ,CAAC,CAAC,CAAC,EAAEvG,sBAAsB,CAAC;EAChC;EACA;AACJ;AACA;EACIyG,GAAGA,CAACP,OAAO,EAAE;IACT,OAAOrG,IAAI,CAAC,IAAI,CAAC8C,KAAK,CAAC8D,GAAG,CAACP,OAAO,CAAC,CAAC,CAAC/D,IAAI,CAACnC,sBAAsB,CAAC;EACrE;AACJ;;AAEA;AACA;AACA;AACA,MAAMuH,kBAAkB,GAAG,IAAIlI,cAAc,CAAC,yCAAyC,CAAC;AACxF,MAAMmI,oBAAoB,GAAG,IAAInI,cAAc,CAAC,4CAA4C,CAAC;AAC7F,MAAM4B,QAAQ,GAAG,IAAI5B,cAAc,CAAC,iCAAiC,CAAC;AACtE,MAAM0B,YAAY,GAAG,IAAI1B,cAAc,CAAC,qCAAqC,CAAC;AAC9E;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS8H,cAAcA,CAACD,aAAa,EAAED,OAAO,GAAGvF,GAAG,IAAIA,GAAG,EAAE;EACzD,MAAMiB,KAAK,GAAGsE,OAAO,CAACC,aAAa,CAAC;EACpC,MAAMxF,GAAG,GAAGwF,aAAa;EACzB,OAAO;IAAEvE,KAAK;IAAEjB;EAAI,CAAC;AACzB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+F,gBAAgB,CAAC;EACnB;AACJ;AACA;AACA;AACA;EACInC,WAAWA,CAACY,OAAO,EAAEwB,IAAI,EAAEC,uBAAuB,EAAEC,QAAQ;EAC5D;EACAC,UAAU,EAAEC,IAAI,EAAEpC,UAAU,EAAEqC,mBAAmB,EAAEC,YAAY,EAAEC,IAAI,EAAEC,eAAe,EAAEC,YAAY;EAAE;EACtGC,QAAQ,EAAEC,YAAY,EAAEC,iBAAiB,EAAEC,WAAW,EAAEC,kBAAkB,EAAE;IACxE,IAAI,CAAC9C,UAAU,GAAGA,UAAU;IAC5B,MAAM+C,GAAG,GAAGjI,mBAAmB,CAAC0F,OAAO,EAAE4B,IAAI,EAAEJ,IAAI,CAAC;IACpD,MAAMgB,WAAW,GAAGV,YAAY;IAChC,IAAIC,IAAI,EAAE;MACNnH,YAAY,CAAC2H,GAAG,EAAEX,IAAI,EAAEI,eAAe,EAAEE,QAAQ,EAAEC,YAAY,EAAEC,iBAAiB,EAAEH,YAAY,EAAEI,WAAW,CAAC;IAClH;IACA,CAAC,IAAI,CAACI,SAAS,EAAE,IAAI,CAACC,mBAAmB,CAAC,GAAGnI,cAAc,CAAC,GAAGgI,GAAG,CAACf,IAAI,YAAY,EAAE,kBAAkB,EAAEe,GAAG,CAACf,IAAI,EAAE,MAAM;MACrH,MAAMiB,SAAS,GAAGb,IAAI,CAACe,iBAAiB,CAAC,MAAMJ,GAAG,CAACE,SAAS,CAAC,CAAC,CAAC;MAC/D,IAAIf,QAAQ,EAAE;QACVe,SAAS,CAACf,QAAQ,CAACA,QAAQ,CAAC;MAChC;MACA,IAAIc,WAAW,EAAE;QACbC,SAAS,CAACD,WAAW,CAAC,GAAGA,WAAW,CAAC;MACzC;MACA,IAAIf,uBAAuB,IAAI,CAAC/G,gBAAgB,CAACiH,UAAU,CAAC,EAAE;QAC1D;QACA;QACA,MAAMiB,iBAAiB,GAAGA,CAAA,KAAM;UAC5B,IAAI;YACA,OAAOjJ,IAAI,CAAC8I,SAAS,CAACG,iBAAiB,CAACf,mBAAmB,IAAI3F,SAAS,CAAC,CAAC2G,IAAI,CAAC,MAAM,IAAI,EAAE,MAAM,KAAK,CAAC,CAAC;UAC5G,CAAC,CACD,OAAOC,CAAC,EAAE;YACN,IAAI,OAAOC,OAAO,KAAK,WAAW,EAAE;cAChCA,OAAO,CAACC,IAAI,CAACF,CAAC,CAAC;YACnB;YACA,OAAOlJ,EAAE,CAAC,KAAK,CAAC;UACpB;QACJ,CAAC;QACD,OAAO,CAAC6I,SAAS,EAAEb,IAAI,CAACe,iBAAiB,CAACC,iBAAiB,CAAC,CAAC;MACjE,CAAC,MACI;QACD,OAAO,CAACH,SAAS,EAAE7I,EAAE,CAAC,KAAK,CAAC,CAAC;MACjC;IACJ,CAAC,EAAE,CAAC8H,QAAQ,EAAEc,WAAW,EAAEf,uBAAuB,CAAC,CAAC;EACxD;EACAX,UAAUA,CAACmC,SAAS,EAAElC,OAAO,EAAE;IAC3B,IAAIC,aAAa;IACjB,IAAI,OAAOiC,SAAS,KAAK,QAAQ,EAAE;MAC/BjC,aAAa,GAAG,IAAI,CAACyB,SAAS,CAAC3B,UAAU,CAACmC,SAAS,CAAC;IACxD,CAAC,MACI;MACDjC,aAAa,GAAGiC,SAAS;IAC7B;IACA,MAAM;MAAEzH,GAAG;MAAEiB;IAAM,CAAC,GAAGwE,cAAc,CAACD,aAAa,EAAED,OAAO,CAAC;IAC7D,MAAMmC,SAAS,GAAG,IAAI,CAAC1D,UAAU,CAAC2D,MAAM,CAACC,GAAG,CAAC,MAAM5H,GAAG,CAAC;IACvD,OAAO,IAAI2D,0BAA0B,CAAC+D,SAAS,EAAEzG,KAAK,EAAE,IAAI,CAAC;EACjE;EACA;AACJ;AACA;AACA;AACA;EACI4G,eAAeA,CAACC,YAAY,EAAEC,YAAY,EAAE;IACxC,MAAMxC,OAAO,GAAGwC,YAAY,KAAK/H,GAAG,IAAIA,GAAG,CAAC;IAC5C,MAAM6H,eAAe,GAAG,IAAI,CAACZ,SAAS,CAACY,eAAe,CAACC,YAAY,CAAC;IACpE,OAAO,IAAInC,+BAA+B,CAACJ,OAAO,CAACsC,eAAe,CAAC,EAAE,IAAI,CAAC;EAC9E;EACA7F,GAAGA,CAACyF,SAAS,EAAE;IACX,IAAIzH,GAAG;IACP,IAAI,OAAOyH,SAAS,KAAK,QAAQ,EAAE;MAC/BzH,GAAG,GAAG,IAAI,CAACiH,SAAS,CAACjF,GAAG,CAACyF,SAAS,CAAC;IACvC,CAAC,MACI;MACDzH,GAAG,GAAGyH,SAAS;IACnB;IACA,MAAMC,SAAS,GAAG,IAAI,CAAC1D,UAAU,CAAC2D,MAAM,CAACC,GAAG,CAAC,MAAM5H,GAAG,CAAC;IACvD,OAAO,IAAIkF,wBAAwB,CAACwC,SAAS,EAAE,IAAI,CAAC;EACxD;EACA;AACJ;AACA;EACIM,QAAQA,CAAA,EAAG;IACP,OAAO,IAAI,CAACf,SAAS,CAAC3B,UAAU,CAAC,GAAG,CAAC,CAACtD,GAAG,CAAC,CAAC,CAAC8C,EAAE;EAClD;AACJ;AACAiB,gBAAgB,CAACkC,IAAI,YAAAC,yBAAAC,CAAA;EAAA,YAAAA,CAAA,IAAwFpC,gBAAgB,EAA1BrI,EAAE,CAAA0K,QAAA,CAA0CpJ,gBAAgB,GAA5DtB,EAAE,CAAA0K,QAAA,CAAuEnJ,iBAAiB,MAA1FvB,EAAE,CAAA0K,QAAA,CAAqHvC,kBAAkB,MAAzInI,EAAE,CAAA0K,QAAA,CAAoK7I,QAAQ,MAA9K7B,EAAE,CAAA0K,QAAA,CAAyMxK,WAAW,GAAtNF,EAAE,CAAA0K,QAAA,CAAiO1K,EAAE,CAAC2K,MAAM,GAA5O3K,EAAE,CAAA0K,QAAA,CAAuP/J,EAAE,CAACiK,sBAAsB,GAAlR5K,EAAE,CAAA0K,QAAA,CAA6RtC,oBAAoB,MAAnTpI,EAAE,CAAA0K,QAAA,CAA8U/I,YAAY,MAA5V3B,EAAE,CAAA0K,QAAA,CAAuXjJ,EAAE,CAACoJ,eAAe,MAA3Y7K,EAAE,CAAA0K,QAAA,CAAsa9I,cAAc,MAAtb5B,EAAE,CAAA0K,QAAA,CAAid5I,UAAU,MAA7d9B,EAAE,CAAA0K,QAAA,CAAwf3I,SAAS,MAAngB/B,EAAE,CAAA0K,QAAA,CAA8hB1I,aAAa,MAA7iBhC,EAAE,CAAA0K,QAAA,CAAwkBzI,mBAAmB,MAA7lBjC,EAAE,CAAA0K,QAAA,CAAwnBxI,WAAW,MAAroBlC,EAAE,CAAA0K,QAAA,CAAgqBvI,EAAE,CAAC2I,iBAAiB;AAAA,CAA6D;AACt1BzC,gBAAgB,CAAC0C,KAAK,kBAD6E/K,EAAE,CAAAgL,kBAAA;EAAAC,KAAA,EACY5C,gBAAgB;EAAA6C,OAAA,EAAhB7C,gBAAgB,CAAAkC,IAAA;EAAAY,UAAA,EAAc;AAAK,EAAG;AACvJ;EAAA,QAAAC,SAAA,oBAAAA,SAAA,KAFmGpL,EAAE,CAAAqL,iBAAA,CAEVhD,gBAAgB,EAAc,CAAC;IAC9GjF,IAAI,EAAEjD,UAAU;IAChBsF,IAAI,EAAE,CAAC;MACC0F,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,CAAC;MAAE/H,IAAI,EAAEJ,SAAS;MAAEsI,UAAU,EAAE,CAAC;QAC9DlI,IAAI,EAAEhD,MAAM;QACZqF,IAAI,EAAE,CAACnE,gBAAgB;MAC3B,CAAC;IAAE,CAAC,EAAE;MAAE8B,IAAI,EAAEJ,SAAS;MAAEsI,UAAU,EAAE,CAAC;QAClClI,IAAI,EAAE/C;MACV,CAAC,EAAE;QACC+C,IAAI,EAAEhD,MAAM;QACZqF,IAAI,EAAE,CAAClE,iBAAiB;MAC5B,CAAC;IAAE,CAAC,EAAE;MAAE6B,IAAI,EAAEJ,SAAS;MAAEsI,UAAU,EAAE,CAAC;QAClClI,IAAI,EAAE/C;MACV,CAAC,EAAE;QACC+C,IAAI,EAAEhD,MAAM;QACZqF,IAAI,EAAE,CAAC0C,kBAAkB;MAC7B,CAAC;IAAE,CAAC,EAAE;MAAE/E,IAAI,EAAEJ,SAAS;MAAEsI,UAAU,EAAE,CAAC;QAClClI,IAAI,EAAE/C;MACV,CAAC,EAAE;QACC+C,IAAI,EAAEhD,MAAM;QACZqF,IAAI,EAAE,CAAC5D,QAAQ;MACnB,CAAC;IAAE,CAAC,EAAE;MAAEuB,IAAI,EAAE6D,MAAM;MAAEqE,UAAU,EAAE,CAAC;QAC/BlI,IAAI,EAAEhD,MAAM;QACZqF,IAAI,EAAE,CAACvF,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAEkD,IAAI,EAAEpD,EAAE,CAAC2K;IAAO,CAAC,EAAE;MAAEvH,IAAI,EAAEzC,EAAE,CAACiK;IAAuB,CAAC,EAAE;MAAExH,IAAI,EAAEJ,SAAS;MAAEsI,UAAU,EAAE,CAAC;QAC5FlI,IAAI,EAAE/C;MACV,CAAC,EAAE;QACC+C,IAAI,EAAEhD,MAAM;QACZqF,IAAI,EAAE,CAAC2C,oBAAoB;MAC/B,CAAC;IAAE,CAAC,EAAE;MAAEhF,IAAI,EAAEJ,SAAS;MAAEsI,UAAU,EAAE,CAAC;QAClClI,IAAI,EAAE/C;MACV,CAAC,EAAE;QACC+C,IAAI,EAAEhD,MAAM;QACZqF,IAAI,EAAE,CAAC9D,YAAY;MACvB,CAAC;IAAE,CAAC,EAAE;MAAEyB,IAAI,EAAE3B,EAAE,CAACoJ,eAAe;MAAES,UAAU,EAAE,CAAC;QAC3ClI,IAAI,EAAE/C;MACV,CAAC;IAAE,CAAC,EAAE;MAAE+C,IAAI,EAAEJ,SAAS;MAAEsI,UAAU,EAAE,CAAC;QAClClI,IAAI,EAAE/C;MACV,CAAC,EAAE;QACC+C,IAAI,EAAEhD,MAAM;QACZqF,IAAI,EAAE,CAAC7D,cAAc;MACzB,CAAC;IAAE,CAAC,EAAE;MAAEwB,IAAI,EAAEJ,SAAS;MAAEsI,UAAU,EAAE,CAAC;QAClClI,IAAI,EAAE/C;MACV,CAAC,EAAE;QACC+C,IAAI,EAAEhD,MAAM;QACZqF,IAAI,EAAE,CAAC3D,UAAU;MACrB,CAAC;IAAE,CAAC,EAAE;MAAEsB,IAAI,EAAEJ,SAAS;MAAEsI,UAAU,EAAE,CAAC;QAClClI,IAAI,EAAE/C;MACV,CAAC,EAAE;QACC+C,IAAI,EAAEhD,MAAM;QACZqF,IAAI,EAAE,CAAC1D,SAAS;MACpB,CAAC;IAAE,CAAC,EAAE;MAAEqB,IAAI,EAAEJ,SAAS;MAAEsI,UAAU,EAAE,CAAC;QAClClI,IAAI,EAAE/C;MACV,CAAC,EAAE;QACC+C,IAAI,EAAEhD,MAAM;QACZqF,IAAI,EAAE,CAACzD,aAAa;MACxB,CAAC;IAAE,CAAC,EAAE;MAAEoB,IAAI,EAAEJ,SAAS;MAAEsI,UAAU,EAAE,CAAC;QAClClI,IAAI,EAAE/C;MACV,CAAC,EAAE;QACC+C,IAAI,EAAEhD,MAAM;QACZqF,IAAI,EAAE,CAACxD,mBAAmB;MAC9B,CAAC;IAAE,CAAC,EAAE;MAAEmB,IAAI,EAAEJ,SAAS;MAAEsI,UAAU,EAAE,CAAC;QAClClI,IAAI,EAAE/C;MACV,CAAC,EAAE;QACC+C,IAAI,EAAEhD,MAAM;QACZqF,IAAI,EAAE,CAACvD,WAAW;MACtB,CAAC;IAAE,CAAC,EAAE;MAAEkB,IAAI,EAAEjB,EAAE,CAAC2I,iBAAiB;MAAEQ,UAAU,EAAE,CAAC;QAC7ClI,IAAI,EAAE/C;MACV,CAAC;IAAE,CAAC,CAAC;EAAE,CAAC;AAAA;AAExB,MAAMkL,sBAAsB,CAAC;EACzBrF,WAAWA,CAAA,EAAG;IACV9D,QAAQ,CAACoJ,eAAe,CAAC,aAAa,EAAE3K,OAAO,CAAC4K,IAAI,EAAE,YAAY,CAAC;EACvE;EACA;AACJ;AACA;EACI,OAAO/B,iBAAiBA,CAACf,mBAAmB,EAAE;IAC1C,OAAO;MACH+C,QAAQ,EAAEH,sBAAsB;MAChCI,SAAS,EAAE,CACP;QAAEC,OAAO,EAAEzD,kBAAkB;QAAE0D,QAAQ,EAAE;MAAK,CAAC,EAC/C;QAAED,OAAO,EAAExD,oBAAoB;QAAEyD,QAAQ,EAAElD;MAAoB,CAAC;IAExE,CAAC;EACL;AACJ;AACA4C,sBAAsB,CAAChB,IAAI,YAAAuB,+BAAArB,CAAA;EAAA,YAAAA,CAAA,IAAwFc,sBAAsB;AAAA,CAAkD;AAC3LA,sBAAsB,CAACQ,IAAI,kBA5FwE/L,EAAE,CAAAgM,gBAAA;EAAA5I,IAAA,EA4FemI;AAAsB,EAAG;AAC7IA,sBAAsB,CAACU,IAAI,kBA7FwEjM,EAAE,CAAAkM,gBAAA;EAAAP,SAAA,EA6FkD,CAACtD,gBAAgB;AAAC,EAAG;AAC5K;EAAA,QAAA+C,SAAA,oBAAAA,SAAA,KA9FmGpL,EAAE,CAAAqL,iBAAA,CA8FVE,sBAAsB,EAAc,CAAC;IACpHnI,IAAI,EAAE9C,QAAQ;IACdmF,IAAI,EAAE,CAAC;MACCkG,SAAS,EAAE,CAACtD,gBAAgB;IAChC,CAAC;EACT,CAAC,CAAC,EAAkB,YAAY;IAAE,OAAO,EAAE;EAAE,CAAC;AAAA;;AAEtD;AACA;AACA;;AAEA,SAASA,gBAAgB,EAAEpC,0BAA0B,EAAEgC,+BAA+B,EAAET,wBAAwB,EAAE+D,sBAAsB,EAAEpD,kBAAkB,EAAEC,oBAAoB,EAAEvG,QAAQ,EAAEF,YAAY,EAAEoG,cAAc,EAAE3C,aAAa,EAAEJ,cAAc,EAAE1B,UAAU,EAAED,iBAAiB,EAAEP,UAAU,EAAED,OAAO,EAAE+B,aAAa,EAAEmB,mBAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}