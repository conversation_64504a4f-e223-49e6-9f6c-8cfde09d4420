import { Injectable } from '@angular/core';
import { Router } from '@angular/router';
import { UserRole, User } from '../models';

@Injectable({
  providedIn: 'root'
})
export class DashboardRouterService {

  constructor(private router: Router) {}

  /**
   * Redirige l'utilisateur vers le dashboard approprié selon son rôle
   */
  navigateToUserDashboard(user: User): void {
    const route = this.getDashboardRoute(user.role);
    this.router.navigate([route]);
  }

  /**
   * Retourne la route du dashboard selon le rôle utilisateur
   */
  getDashboardRoute(role: UserRole): string {
    const dashboardRoutes = {
      [UserRole.USER]: '/dashboard',           // 🙋‍♂️ Utilisateur standard
      [UserRole.ADMIN]: '/admin',              // 🧑‍💼 Administrateur
      [UserRole.VALIDATOR]: '/validator',      // 🧑‍🏫 Validateur
      [UserRole.PARTNER]: '/partner',          // 🧑‍🍳 Partenaire
      [UserRole.PROVIDER]: '/provider'         // 🧑‍🔧 Prestataire
    };

    return dashboardRoutes[role] || '/dashboard';
  }

  /**
   * Vérifie si l'utilisateur a accès à une route spécifique
   */
  canAccessRoute(userRole: UserRole, route: string): boolean {
    const roleRoutes = {
      [UserRole.USER]: ['/dashboard', '/profile', '/qr-scanner', '/rewards'],
      [UserRole.ADMIN]: ['/admin', '/profile'],
      [UserRole.VALIDATOR]: ['/validator', '/profile'],
      [UserRole.PARTNER]: ['/partner', '/profile'],
      [UserRole.PROVIDER]: ['/provider', '/profile', '/qr-scanner']
    };

    const allowedRoutes = roleRoutes[userRole] || [];
    return allowedRoutes.some(allowedRoute => route.startsWith(allowedRoute));
  }

  /**
   * Retourne le nom d'affichage du dashboard selon le rôle
   */
  getDashboardDisplayName(role: UserRole): string {
    const dashboardNames = {
      [UserRole.USER]: 'Dashboard Utilisateur',
      [UserRole.ADMIN]: 'Dashboard Administrateur',
      [UserRole.VALIDATOR]: 'Dashboard Validateur',
      [UserRole.PARTNER]: 'Dashboard Partenaire',
      [UserRole.PROVIDER]: 'Dashboard Prestataire'
    };

    return dashboardNames[role] || 'Dashboard';
  }

  /**
   * Retourne l'icône du dashboard selon le rôle
   */
  getDashboardIcon(role: UserRole): string {
    const dashboardIcons = {
      [UserRole.USER]: 'person',
      [UserRole.ADMIN]: 'admin_panel_settings',
      [UserRole.VALIDATOR]: 'verified_user',
      [UserRole.PARTNER]: 'store',
      [UserRole.PROVIDER]: 'business'
    };

    return dashboardIcons[role] || 'dashboard';
  }

  /**
   * Retourne la description du dashboard selon le rôle
   */
  getDashboardDescription(role: UserRole): string {
    const dashboardDescriptions = {
      [UserRole.USER]: 'Gérez vos points, scannez des QR codes et échangez des récompenses',
      [UserRole.ADMIN]: 'Supervision complète de l\'application et gestion des utilisateurs',
      [UserRole.VALIDATOR]: 'Validez les actions communautaires et attribuez des points',
      [UserRole.PARTNER]: 'Gérez vos récompenses et suivez vos performances commerciales',
      [UserRole.PROVIDER]: 'Générez des QR codes et suivez l\'engagement de vos activités'
    };

    return dashboardDescriptions[role] || 'Tableau de bord';
  }

  /**
   * Retourne les fonctionnalités principales selon le rôle
   */
  getDashboardFeatures(role: UserRole): string[] {
    const dashboardFeatures = {
      [UserRole.USER]: [
        'Scanner des QR codes',
        'Voir ses points disponibles',
        'Échanger des récompenses',
        'Consulter l\'historique',
        'Modifier son profil'
      ],
      [UserRole.ADMIN]: [
        'Gestion des utilisateurs',
        'Suivi des points globaux',
        'Gestion des partenaires',
        'Configuration système',
        'Statistiques globales'
      ],
      [UserRole.VALIDATOR]: [
        'Valider les actions',
        'Attribuer des points',
        'Consulter l\'historique',
        'Voir les statistiques'
      ],
      [UserRole.PARTNER]: [
        'Ajouter des récompenses',
        'Suivre les échanges',
        'Modifier les offres',
        'Voir les statistiques'
      ],
      [UserRole.PROVIDER]: [
        'Générer des QR codes',
        'Voir les statistiques d\'usage',
        'Consulter l\'historique',
        'Gérer son profil'
      ]
    };

    return dashboardFeatures[role] || [];
  }
}
