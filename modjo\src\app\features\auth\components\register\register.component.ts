import { Component, OnInit } from '@angular/core';
import { FormBuilder, FormGroup, Validators } from '@angular/forms';
import { Router } from '@angular/router';
import { MatSnackBar } from '@angular/material/snack-bar';
import { AuthService } from '../../../../core/services/auth.service';
import { UserRole } from '../../../../core/models';

@Component({
  selector: 'app-register',
  templateUrl: './register.component.html',
  styleUrls: ['./register.component.css']
})
export class RegisterComponent implements OnInit {
  registerForm: FormGroup;
  isLoading = false;
  hidePassword = true;
  hideConfirmPassword = true;

  cities = [
    { value: 'Monastir', label: 'Monastir' },
    { value: 'Sousse', label: 'Sousse' }
  ];

  roles = [
    { value: UserRole.USER, label: 'Utilisateur', description: 'Participer aux activités communautaires' },
    { value: UserRole.PROVIDER, label: 'Partenaire', description: 'Offrir des récompenses et services' },
    { value: UserRole.VALIDATOR, label: 'Validateur', description: 'Valider les actions communautaires' }
  ];

  constructor(
    private fb: FormBuilder,
    private authService: AuthService,
    private router: Router,
    private snackBar: MatSnackBar
  ) {
    this.registerForm = this.fb.group({
      name: ['', [Validators.required, Validators.minLength(2)]],
      email: ['', [Validators.required, Validators.email]],
      phone: ['', [Validators.pattern(/^[0-9]{8}$/)]],
      city: ['', Validators.required],
      role: [UserRole.USER, Validators.required],
      password: ['', [Validators.required, Validators.minLength(6)]],
      confirmPassword: ['', Validators.required],
      acceptTerms: [false, Validators.requiredTrue]
    }, { validators: this.passwordMatchValidator });
  }

  ngOnInit(): void {
    // Redirect if already authenticated
    if (this.authService.isAuthenticated()) {
      this.router.navigate(['/dashboard']);
    }
  }

  passwordMatchValidator(form: FormGroup) {
    const password = form.get('password');
    const confirmPassword = form.get('confirmPassword');
    
    if (password && confirmPassword && password.value !== confirmPassword.value) {
      confirmPassword.setErrors({ passwordMismatch: true });
    } else if (confirmPassword?.errors?.['passwordMismatch']) {
      delete confirmPassword.errors['passwordMismatch'];
      if (Object.keys(confirmPassword.errors).length === 0) {
        confirmPassword.setErrors(null);
      }
    }
    return null;
  }

  async onSubmit(): Promise<void> {
    if (this.registerForm.invalid) {
      this.markFormGroupTouched();
      return;
    }

    this.isLoading = true;
    const formValue = this.registerForm.value;

    try {
      await this.authService.register({
        email: formValue.email,
        password: formValue.password,
        name: formValue.name,
        phone: formValue.phone,
        city: formValue.city,
        role: formValue.role
      });

      this.snackBar.open('Compte créé avec succès!', 'Fermer', { duration: 3000 });
      this.router.navigate(['/dashboard']);
    } catch (error: any) {
      console.error('Registration error:', error);
      this.snackBar.open(
        this.getErrorMessage(error), 
        'Fermer', 
        { duration: 5000 }
      );
    } finally {
      this.isLoading = false;
    }
  }

  private getErrorMessage(error: any): string {
    if (error.code) {
      switch (error.code) {
        case 'auth/email-already-in-use':
          return 'Cette adresse email est déjà utilisée.';
        case 'auth/invalid-email':
          return 'Adresse email invalide.';
        case 'auth/weak-password':
          return 'Le mot de passe est trop faible.';
        case 'auth/operation-not-allowed':
          return 'Création de compte désactivée.';
        default:
          return 'Erreur lors de la création du compte.';
      }
    }
    return 'Erreur lors de la création du compte.';
  }

  private markFormGroupTouched(): void {
    Object.keys(this.registerForm.controls).forEach(key => {
      const control = this.registerForm.get(key);
      control?.markAsTouched();
    });
  }

  getFieldError(fieldName: string): string {
    const field = this.registerForm.get(fieldName);
    if (field?.errors && field.touched) {
      if (field.errors['required']) {
        return this.getRequiredMessage(fieldName);
      }
      if (field.errors['email']) {
        return 'Format email invalide';
      }
      if (field.errors['minlength']) {
        return fieldName === 'password' ? 
          'Mot de passe trop court (min. 6 caractères)' : 
          'Nom trop court (min. 2 caractères)';
      }
      if (field.errors['pattern']) {
        return 'Numéro de téléphone invalide (8 chiffres)';
      }
      if (field.errors['passwordMismatch']) {
        return 'Les mots de passe ne correspondent pas';
      }
      if (field.errors['requiredTrue']) {
        return 'Vous devez accepter les conditions';
      }
    }
    return '';
  }

  private getRequiredMessage(fieldName: string): string {
    const messages: { [key: string]: string } = {
      name: 'Nom requis',
      email: 'Email requis',
      city: 'Ville requise',
      role: 'Rôle requis',
      password: 'Mot de passe requis',
      confirmPassword: 'Confirmation requise',
      acceptTerms: 'Acceptation requise'
    };
    return messages[fieldName] || 'Champ requis';
  }
}
