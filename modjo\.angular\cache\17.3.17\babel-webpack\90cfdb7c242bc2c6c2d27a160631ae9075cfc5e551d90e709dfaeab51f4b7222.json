{"ast": null, "code": "export var ProviderType;\n(function (ProviderType) {\n  ProviderType[\"SCHOOL\"] = \"school\";\n  ProviderType[\"UNIVERSITY\"] = \"university\";\n  ProviderType[\"ASSOCIATION\"] = \"association\";\n  ProviderType[\"NGO\"] = \"ngo\";\n  ProviderType[\"SPORTS_CLUB\"] = \"sports_club\";\n  ProviderType[\"CULTURAL_CENTER\"] = \"cultural_center\";\n  ProviderType[\"LIBRARY\"] = \"library\";\n  ProviderType[\"COMMUNITY_CENTER\"] = \"community_center\";\n  ProviderType[\"TRAINING_CENTER\"] = \"training_center\";\n  ProviderType[\"COACHING\"] = \"coaching\";\n  ProviderType[\"OTHER\"] = \"other\";\n})(ProviderType || (ProviderType = {}));\nexport var VerificationStatus;\n(function (VerificationStatus) {\n  VerificationStatus[\"PENDING\"] = \"pending\";\n  VerificationStatus[\"VERIFIED\"] = \"verified\";\n  VerificationStatus[\"REJECTED\"] = \"rejected\";\n  VerificationStatus[\"SUSPENDED\"] = \"suspended\";\n})(VerificationStatus || (VerificationStatus = {}));\nexport var ActivityType;\n(function (ActivityType) {\n  ActivityType[\"LESSON\"] = \"lesson\";\n  ActivityType[\"WORKSHOP\"] = \"workshop\";\n  ActivityType[\"TRAINING\"] = \"training\";\n  ActivityType[\"CONFERENCE\"] = \"conference\";\n  ActivityType[\"SPORTS_SESSION\"] = \"sports_session\";\n  ActivityType[\"CULTURAL_EVENT\"] = \"cultural_event\";\n  ActivityType[\"VOLUNTEER_WORK\"] = \"volunteer_work\";\n  ActivityType[\"TUTORING\"] = \"tutoring\";\n  ActivityType[\"LIBRARY_VISIT\"] = \"library_visit\";\n  ActivityType[\"COMMUNITY_SERVICE\"] = \"community_service\";\n  ActivityType[\"OTHER\"] = \"other\";\n})(ActivityType || (ActivityType = {}));", "map": {"version": 3, "names": ["ProviderType", "VerificationStatus", "ActivityType"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\models\\provider.model.ts"], "sourcesContent": ["export interface Provider {\n  id: string;\n  userId: string; // Lié au compte utilisateur\n  organizationName: string;\n  organizationType: ProviderType;\n  description: string;\n  address: string;\n  city: 'Monastir' | 'Sousse';\n  phone: string;\n  email: string;\n  logo?: string;\n  website?: string;\n  contactPerson: string;\n  isActive: boolean;\n  verificationStatus: VerificationStatus;\n  joinedAt: Date;\n  lastActiveAt: Date;\n}\n\nexport enum ProviderType {\n  SCHOOL = 'school',\n  UNIVERSITY = 'university',\n  ASSOCIATION = 'association',\n  NGO = 'ngo',\n  SPORTS_CLUB = 'sports_club',\n  CULTURAL_CENTER = 'cultural_center',\n  LIBRARY = 'library',\n  COMMUNITY_CENTER = 'community_center',\n  TRAINING_CENTER = 'training_center',\n  COACHING = 'coaching',\n  OTHER = 'other'\n}\n\nexport enum VerificationStatus {\n  PENDING = 'pending',\n  VERIFIED = 'verified',\n  REJECTED = 'rejected',\n  SUSPENDED = 'suspended'\n}\n\nexport interface QrCodeGeneration {\n  id: string;\n  providerId: string;\n  providerName: string;\n  qrCodeData: string;\n  pointsValue: number; // 1, 3, ou 5 points\n  activityType: ActivityType;\n  description: string;\n  maxScansPerUser: number;\n  maxScansPerDay: number;\n  validFrom: Date;\n  validUntil: Date;\n  isActive: boolean;\n  createdAt: Date;\n  totalScans: number;\n  uniqueUsers: number;\n}\n\nexport enum ActivityType {\n  LESSON = 'lesson',\n  WORKSHOP = 'workshop',\n  TRAINING = 'training',\n  CONFERENCE = 'conference',\n  SPORTS_SESSION = 'sports_session',\n  CULTURAL_EVENT = 'cultural_event',\n  VOLUNTEER_WORK = 'volunteer_work',\n  TUTORING = 'tutoring',\n  LIBRARY_VISIT = 'library_visit',\n  COMMUNITY_SERVICE = 'community_service',\n  OTHER = 'other'\n}\n\nexport interface QrCodeScan {\n  id: string;\n  qrCodeId: string;\n  userId: string;\n  userName: string;\n  providerId: string;\n  providerName: string;\n  pointsAwarded: number;\n  scannedAt: Date;\n  location?: string;\n  isValid: boolean;\n  dailyLimitReached: boolean;\n}\n\nexport interface ProviderStats {\n  providerId: string;\n  totalQrCodes: number;\n  activeQrCodes: number;\n  totalScans: number;\n  totalPointsDistributed: number;\n  uniqueUsers: number;\n  averageScansPerQr: number;\n  popularActivities: PopularActivity[];\n  monthlyStats: MonthlyProviderStats[];\n  userEngagement: number; // pourcentage de retour\n}\n\nexport interface PopularActivity {\n  activityType: ActivityType;\n  description: string;\n  scanCount: number;\n  pointsDistributed: number;\n}\n\nexport interface MonthlyProviderStats {\n  month: string; // YYYY-MM\n  scans: number;\n  pointsDistributed: number;\n  uniqueUsers: number;\n  newUsers: number;\n}\n\nexport interface CreateQrCodeRequest {\n  pointsValue: number; // 1, 3, ou 5\n  activityType: ActivityType;\n  description: string;\n  maxScansPerUser: number;\n  maxScansPerDay: number;\n  validUntil: Date;\n}\n\nexport interface UpdateQrCodeRequest {\n  description?: string;\n  maxScansPerUser?: number;\n  maxScansPerDay?: number;\n  validUntil?: Date;\n  isActive?: boolean;\n}\n\nexport interface DailyLimit {\n  userId: string;\n  providerId: string;\n  date: string; // YYYY-MM-DD\n  scansCount: number;\n  pointsEarned: number;\n  lastScanAt: Date;\n}\n\nexport interface QrCodeValidationRule {\n  maxPointsPerDay: number;\n  maxScansPerUserPerProvider: number;\n  maxScansPerUserPerDay: number;\n  cooldownPeriod: number; // en minutes\n  requireLocation: boolean;\n}\n"], "mappings": "AAmBA,WAAYA,YAYX;AAZD,WAAYA,YAAY;EACtBA,YAAA,qBAAiB;EACjBA,YAAA,6BAAyB;EACzBA,YAAA,+BAA2B;EAC3BA,YAAA,eAAW;EACXA,YAAA,+BAA2B;EAC3BA,YAAA,uCAAmC;EACnCA,YAAA,uBAAmB;EACnBA,YAAA,yCAAqC;EACrCA,YAAA,uCAAmC;EACnCA,YAAA,yBAAqB;EACrBA,YAAA,mBAAe;AACjB,CAAC,EAZWA,YAAY,KAAZA,YAAY;AAcxB,WAAYC,kBAKX;AALD,WAAYA,kBAAkB;EAC5BA,kBAAA,uBAAmB;EACnBA,kBAAA,yBAAqB;EACrBA,kBAAA,yBAAqB;EACrBA,kBAAA,2BAAuB;AACzB,CAAC,EALWA,kBAAkB,KAAlBA,kBAAkB;AAyB9B,WAAYC,YAYX;AAZD,WAAYA,YAAY;EACtBA,YAAA,qBAAiB;EACjBA,YAAA,yBAAqB;EACrBA,YAAA,yBAAqB;EACrBA,YAAA,6BAAyB;EACzBA,YAAA,qCAAiC;EACjCA,YAAA,qCAAiC;EACjCA,YAAA,qCAAiC;EACjCA,YAAA,yBAAqB;EACrBA,YAAA,mCAA+B;EAC/BA,YAAA,2CAAuC;EACvCA,YAAA,mBAAe;AACjB,CAAC,EAZWA,YAAY,KAAZA,YAAY", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}