{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { as as debugAssert, at as _isIOS, au as _isAndroid, av as _fail, aw as _getRedirectUrl, ax as _getProjectConfig, ay as _isIOS7Or8, az as _createError, aA as _assert, aB as AuthEventManager, aC as _getInstance, b as browserLocalPersistence, aD as _persistenceKeyName, a as browserSessionPersistence, aE as _getRedirectResult, aF as _overrideRedirectResult, aG as _clearRedirectOutcomes, aH as _castAuth } from './index-e3d5d3f4.js';\nexport { A as ActionCodeOperation, ag as ActionCodeURL, J as AuthCredential, G as AuthErrorCodes, aJ as AuthImpl, aM as AuthPopup, K as EmailAuthCredential, Q as EmailAuthProvider, U as FacebookAuthProvider, F as FactorId, aN as FetchProvider, W as GithubAuthProvider, V as GoogleAuthProvider, L as OAuthCredential, X as OAuthProvider, O as OperationType, M as PhoneAuthCredential, P as PhoneAuthProvider, m as PhoneMultiFactorGenerator, p as ProviderId, R as RecaptchaVerifier, aO as SAMLAuthCredential, Y as SAMLAuthProvider, S as SignInMethod, T as TotpMultiFactorGenerator, n as TotpSecret, Z as TwitterAuthProvider, aI as UserImpl, aA as _assert, aH as _castAuth, av as _fail, aL as _generateEventId, aK as _getClientVersion, aC as _getInstance, aE as _getRedirectResult, aF as _overrideRedirectResult, aD as _persistenceKeyName, a5 as applyActionCode, w as beforeAuthStateChanged, b as browserLocalPersistence, k as browserPopupRedirectResolver, a as browserSessionPersistence, a6 as checkActionCode, a4 as confirmPasswordReset, I as connectAuthEmulator, a8 as createUserWithEmailAndPassword, D as debugErrorMap, C as deleteUser, ad as fetchSignInMethodsForEmail, ao as getAdditionalUserInfo, o as getAuth, al as getIdToken, am as getIdTokenResult, aq as getMultiFactorResolver, j as getRedirectResult, N as inMemoryPersistence, i as indexedDBLocalPersistence, H as initializeAuth, t as initializeRecaptchaConfig, ab as isSignInWithEmailLink, a0 as linkWithCredential, l as linkWithPhoneNumber, d as linkWithPopup, g as linkWithRedirect, ar as multiFactor, x as onAuthStateChanged, v as onIdTokenChanged, ah as parseActionCodeURL, E as prodErrorMap, a1 as reauthenticateWithCredential, r as reauthenticateWithPhoneNumber, e as reauthenticateWithPopup, h as reauthenticateWithRedirect, ap as reload, ae as sendEmailVerification, a3 as sendPasswordResetEmail, aa as sendSignInLinkToEmail, q as setPersistence, _ as signInAnonymously, $ as signInWithCredential, a2 as signInWithCustomToken, a9 as signInWithEmailAndPassword, ac as signInWithEmailLink, s as signInWithPhoneNumber, c as signInWithPopup, f as signInWithRedirect, B as signOut, an as unlink, z as updateCurrentUser, aj as updateEmail, ak as updatePassword, u as updatePhoneNumber, ai as updateProfile, y as useDeviceLanguage, af as verifyBeforeUpdateEmail, a7 as verifyPasswordResetCode } from './index-e3d5d3f4.js';\nimport { querystringDecode } from '@firebase/util';\nimport '@firebase/app';\nimport 'tslib';\nimport '@firebase/logger';\nimport '@firebase/component';\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction _cordovaWindow() {\n  return window;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * How long to wait after the app comes back into focus before concluding that\r\n * the user closed the sign in tab.\r\n */\nconst REDIRECT_TIMEOUT_MS = 2000;\n/**\r\n * Generates the URL for the OAuth handler.\r\n */\nfunction _generateHandlerUrl(_x, _x2, _x3) {\n  return _generateHandlerUrl2.apply(this, arguments);\n}\n/**\r\n * Validates that this app is valid for this project configuration\r\n */\nfunction _generateHandlerUrl2() {\n  _generateHandlerUrl2 = _asyncToGenerator(function* (auth, event, provider) {\n    var _a;\n    // Get the cordova plugins\n    const {\n      BuildInfo\n    } = _cordovaWindow();\n    debugAssert(event.sessionId, 'AuthEvent did not contain a session ID');\n    const sessionDigest = yield computeSha256(event.sessionId);\n    const additionalParams = {};\n    if (_isIOS()) {\n      // iOS app identifier\n      additionalParams['ibi'] = BuildInfo.packageName;\n    } else if (_isAndroid()) {\n      // Android app identifier\n      additionalParams['apn'] = BuildInfo.packageName;\n    } else {\n      _fail(auth, \"operation-not-supported-in-this-environment\" /* AuthErrorCode.OPERATION_NOT_SUPPORTED */);\n    }\n    // Add the display name if available\n    if (BuildInfo.displayName) {\n      additionalParams['appDisplayName'] = BuildInfo.displayName;\n    }\n    // Attached the hashed session ID\n    additionalParams['sessionId'] = sessionDigest;\n    return _getRedirectUrl(auth, provider, event.type, undefined, (_a = event.eventId) !== null && _a !== void 0 ? _a : undefined, additionalParams);\n  });\n  return _generateHandlerUrl2.apply(this, arguments);\n}\nfunction _validateOrigin(_x4) {\n  return _validateOrigin2.apply(this, arguments);\n}\nfunction _validateOrigin2() {\n  _validateOrigin2 = _asyncToGenerator(function* (auth) {\n    const {\n      BuildInfo\n    } = _cordovaWindow();\n    const request = {};\n    if (_isIOS()) {\n      request.iosBundleId = BuildInfo.packageName;\n    } else if (_isAndroid()) {\n      request.androidPackageName = BuildInfo.packageName;\n    } else {\n      _fail(auth, \"operation-not-supported-in-this-environment\" /* AuthErrorCode.OPERATION_NOT_SUPPORTED */);\n    }\n    // Will fail automatically if package name is not authorized\n    yield _getProjectConfig(auth, request);\n  });\n  return _validateOrigin2.apply(this, arguments);\n}\nfunction _performRedirect(handlerUrl) {\n  // Get the cordova plugins\n  const {\n    cordova\n  } = _cordovaWindow();\n  return new Promise(resolve => {\n    cordova.plugins.browsertab.isAvailable(browserTabIsAvailable => {\n      let iabRef = null;\n      if (browserTabIsAvailable) {\n        cordova.plugins.browsertab.openUrl(handlerUrl);\n      } else {\n        // TODO: Return the inappbrowser ref that's returned from the open call\n        iabRef = cordova.InAppBrowser.open(handlerUrl, _isIOS7Or8() ? '_blank' : '_system', 'location=yes');\n      }\n      resolve(iabRef);\n    });\n  });\n}\n/**\r\n * This function waits for app activity to be seen before resolving. It does\r\n * this by attaching listeners to various dom events. Once the app is determined\r\n * to be visible, this promise resolves. AFTER that resolution, the listeners\r\n * are detached and any browser tabs left open will be closed.\r\n */\nfunction _waitForAppResume(_x5, _x6, _x7) {\n  return _waitForAppResume2.apply(this, arguments);\n}\n/**\r\n * Checks the configuration of the Cordova environment. This has no side effect\r\n * if the configuration is correct; otherwise it throws an error with the\r\n * missing plugin.\r\n */\nfunction _waitForAppResume2() {\n  _waitForAppResume2 = _asyncToGenerator(function* (auth, eventListener, iabRef) {\n    // Get the cordova plugins\n    const {\n      cordova\n    } = _cordovaWindow();\n    let cleanup = () => {};\n    try {\n      yield new Promise((resolve, reject) => {\n        let onCloseTimer = null;\n        // DEFINE ALL THE CALLBACKS =====\n        function authEventSeen() {\n          var _a;\n          // Auth event was detected. Resolve this promise and close the extra\n          // window if it's still open.\n          resolve();\n          const closeBrowserTab = (_a = cordova.plugins.browsertab) === null || _a === void 0 ? void 0 : _a.close;\n          if (typeof closeBrowserTab === 'function') {\n            closeBrowserTab();\n          }\n          // Close inappbrowser emebedded webview in iOS7 and 8 case if still\n          // open.\n          if (typeof (iabRef === null || iabRef === void 0 ? void 0 : iabRef.close) === 'function') {\n            iabRef.close();\n          }\n        }\n        function resumed() {\n          if (onCloseTimer) {\n            // This code already ran; do not rerun.\n            return;\n          }\n          onCloseTimer = window.setTimeout(() => {\n            // Wait two seeconds after resume then reject.\n            reject(_createError(auth, \"redirect-cancelled-by-user\" /* AuthErrorCode.REDIRECT_CANCELLED_BY_USER */));\n          }, REDIRECT_TIMEOUT_MS);\n        }\n        function visibilityChanged() {\n          if ((document === null || document === void 0 ? void 0 : document.visibilityState) === 'visible') {\n            resumed();\n          }\n        }\n        // ATTACH ALL THE LISTENERS =====\n        // Listen for the auth event\n        eventListener.addPassiveListener(authEventSeen);\n        // Listen for resume and visibility events\n        document.addEventListener('resume', resumed, false);\n        if (_isAndroid()) {\n          document.addEventListener('visibilitychange', visibilityChanged, false);\n        }\n        // SETUP THE CLEANUP FUNCTION =====\n        cleanup = () => {\n          eventListener.removePassiveListener(authEventSeen);\n          document.removeEventListener('resume', resumed, false);\n          document.removeEventListener('visibilitychange', visibilityChanged, false);\n          if (onCloseTimer) {\n            window.clearTimeout(onCloseTimer);\n          }\n        };\n      });\n    } finally {\n      cleanup();\n    }\n  });\n  return _waitForAppResume2.apply(this, arguments);\n}\nfunction _checkCordovaConfiguration(auth) {\n  var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\n  const win = _cordovaWindow();\n  // Check all dependencies installed.\n  // https://github.com/nordnet/cordova-universal-links-plugin\n  // Note that cordova-universal-links-plugin has been abandoned.\n  // A fork with latest fixes is available at:\n  // https://www.npmjs.com/package/cordova-universal-links-plugin-fix\n  _assert(typeof ((_a = win === null || win === void 0 ? void 0 : win.universalLinks) === null || _a === void 0 ? void 0 : _a.subscribe) === 'function', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\n    missingPlugin: 'cordova-universal-links-plugin-fix'\n  });\n  // https://www.npmjs.com/package/cordova-plugin-buildinfo\n  _assert(typeof ((_b = win === null || win === void 0 ? void 0 : win.BuildInfo) === null || _b === void 0 ? void 0 : _b.packageName) !== 'undefined', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\n    missingPlugin: 'cordova-plugin-buildInfo'\n  });\n  // https://github.com/google/cordova-plugin-browsertab\n  _assert(typeof ((_e = (_d = (_c = win === null || win === void 0 ? void 0 : win.cordova) === null || _c === void 0 ? void 0 : _c.plugins) === null || _d === void 0 ? void 0 : _d.browsertab) === null || _e === void 0 ? void 0 : _e.openUrl) === 'function', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\n    missingPlugin: 'cordova-plugin-browsertab'\n  });\n  _assert(typeof ((_h = (_g = (_f = win === null || win === void 0 ? void 0 : win.cordova) === null || _f === void 0 ? void 0 : _f.plugins) === null || _g === void 0 ? void 0 : _g.browsertab) === null || _h === void 0 ? void 0 : _h.isAvailable) === 'function', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\n    missingPlugin: 'cordova-plugin-browsertab'\n  });\n  // https://cordova.apache.org/docs/en/latest/reference/cordova-plugin-inappbrowser/\n  _assert(typeof ((_k = (_j = win === null || win === void 0 ? void 0 : win.cordova) === null || _j === void 0 ? void 0 : _j.InAppBrowser) === null || _k === void 0 ? void 0 : _k.open) === 'function', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\n    missingPlugin: 'cordova-plugin-inappbrowser'\n  });\n}\n/**\r\n * Computes the SHA-256 of a session ID. The SubtleCrypto interface is only\r\n * available in \"secure\" contexts, which covers Cordova (which is served on a file\r\n * protocol).\r\n */\nfunction computeSha256(_x8) {\n  return _computeSha.apply(this, arguments);\n}\nfunction _computeSha() {\n  _computeSha = _asyncToGenerator(function* (sessionId) {\n    const bytes = stringToArrayBuffer(sessionId);\n    // TODO: For IE11 crypto has a different name and this operation comes back\n    //       as an object, not a promise. This is the old proposed standard that\n    //       is used by IE11:\n    // https://www.w3.org/TR/2013/WD-WebCryptoAPI-20130108/#cryptooperation-interface\n    const buf = yield crypto.subtle.digest('SHA-256', bytes);\n    const arr = Array.from(new Uint8Array(buf));\n    return arr.map(num => num.toString(16).padStart(2, '0')).join('');\n  });\n  return _computeSha.apply(this, arguments);\n}\nfunction stringToArrayBuffer(str) {\n  // This function is only meant to deal with an ASCII charset and makes\n  // certain simplifying assumptions.\n  debugAssert(/[0-9a-zA-Z]+/.test(str), 'Can only convert alpha-numeric strings');\n  if (typeof TextEncoder !== 'undefined') {\n    return new TextEncoder().encode(str);\n  }\n  const buff = new ArrayBuffer(str.length);\n  const view = new Uint8Array(buff);\n  for (let i = 0; i < str.length; i++) {\n    view[i] = str.charCodeAt(i);\n  }\n  return view;\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst SESSION_ID_LENGTH = 20;\n/** Custom AuthEventManager that adds passive listeners to events */\nclass CordovaAuthEventManager extends AuthEventManager {\n  constructor() {\n    super(...arguments);\n    this.passiveListeners = new Set();\n    this.initPromise = new Promise(resolve => {\n      this.resolveInialized = resolve;\n    });\n  }\n  addPassiveListener(cb) {\n    this.passiveListeners.add(cb);\n  }\n  removePassiveListener(cb) {\n    this.passiveListeners.delete(cb);\n  }\n  // In a Cordova environment, this manager can live through multiple redirect\n  // operations\n  resetRedirect() {\n    this.queuedRedirectEvent = null;\n    this.hasHandledPotentialRedirect = false;\n  }\n  /** Override the onEvent method */\n  onEvent(event) {\n    this.resolveInialized();\n    this.passiveListeners.forEach(cb => cb(event));\n    return super.onEvent(event);\n  }\n  initialized() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      yield _this.initPromise;\n    })();\n  }\n}\n/**\r\n * Generates a (partial) {@link AuthEvent}.\r\n */\nfunction _generateNewEvent(auth, type, eventId = null) {\n  return {\n    type,\n    eventId,\n    urlResponse: null,\n    sessionId: generateSessionId(),\n    postBody: null,\n    tenantId: auth.tenantId,\n    error: _createError(auth, \"no-auth-event\" /* AuthErrorCode.NO_AUTH_EVENT */)\n  };\n}\nfunction _savePartialEvent(auth, event) {\n  return storage()._set(persistenceKey(auth), event);\n}\nfunction _getAndRemoveEvent(_x9) {\n  return _getAndRemoveEvent2.apply(this, arguments);\n}\nfunction _getAndRemoveEvent2() {\n  _getAndRemoveEvent2 = _asyncToGenerator(function* (auth) {\n    const event = yield storage()._get(persistenceKey(auth));\n    if (event) {\n      yield storage()._remove(persistenceKey(auth));\n    }\n    return event;\n  });\n  return _getAndRemoveEvent2.apply(this, arguments);\n}\nfunction _eventFromPartialAndUrl(partialEvent, url) {\n  var _a, _b;\n  // Parse the deep link within the dynamic link URL.\n  const callbackUrl = _getDeepLinkFromCallback(url);\n  // Confirm it is actually a callback URL.\n  // Currently the universal link will be of this format:\n  // https://<AUTH_DOMAIN>/__/auth/callback<OAUTH_RESPONSE>\n  // This is a fake URL but is not intended to take the user anywhere\n  // and just redirect to the app.\n  if (callbackUrl.includes('/__/auth/callback')) {\n    // Check if there is an error in the URL.\n    // This mechanism is also used to pass errors back to the app:\n    // https://<AUTH_DOMAIN>/__/auth/callback?firebaseError=<STRINGIFIED_ERROR>\n    const params = searchParamsOrEmpty(callbackUrl);\n    // Get the error object corresponding to the stringified error if found.\n    const errorObject = params['firebaseError'] ? parseJsonOrNull(decodeURIComponent(params['firebaseError'])) : null;\n    const code = (_b = (_a = errorObject === null || errorObject === void 0 ? void 0 : errorObject['code']) === null || _a === void 0 ? void 0 : _a.split('auth/')) === null || _b === void 0 ? void 0 : _b[1];\n    const error = code ? _createError(code) : null;\n    if (error) {\n      return {\n        type: partialEvent.type,\n        eventId: partialEvent.eventId,\n        tenantId: partialEvent.tenantId,\n        error,\n        urlResponse: null,\n        sessionId: null,\n        postBody: null\n      };\n    } else {\n      return {\n        type: partialEvent.type,\n        eventId: partialEvent.eventId,\n        tenantId: partialEvent.tenantId,\n        sessionId: partialEvent.sessionId,\n        urlResponse: callbackUrl,\n        postBody: null\n      };\n    }\n  }\n  return null;\n}\nfunction generateSessionId() {\n  const chars = [];\n  const allowedChars = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\n  for (let i = 0; i < SESSION_ID_LENGTH; i++) {\n    const idx = Math.floor(Math.random() * allowedChars.length);\n    chars.push(allowedChars.charAt(idx));\n  }\n  return chars.join('');\n}\nfunction storage() {\n  return _getInstance(browserLocalPersistence);\n}\nfunction persistenceKey(auth) {\n  return _persistenceKeyName(\"authEvent\" /* KeyName.AUTH_EVENT */, auth.config.apiKey, auth.name);\n}\nfunction parseJsonOrNull(json) {\n  try {\n    return JSON.parse(json);\n  } catch (e) {\n    return null;\n  }\n}\n// Exported for testing\nfunction _getDeepLinkFromCallback(url) {\n  const params = searchParamsOrEmpty(url);\n  const link = params['link'] ? decodeURIComponent(params['link']) : undefined;\n  // Double link case (automatic redirect)\n  const doubleDeepLink = searchParamsOrEmpty(link)['link'];\n  // iOS custom scheme links.\n  const iOSDeepLink = params['deep_link_id'] ? decodeURIComponent(params['deep_link_id']) : undefined;\n  const iOSDoubleDeepLink = searchParamsOrEmpty(iOSDeepLink)['link'];\n  return iOSDoubleDeepLink || iOSDeepLink || doubleDeepLink || link || url;\n}\n/**\r\n * Optimistically tries to get search params from a string, or else returns an\r\n * empty search params object.\r\n */\nfunction searchParamsOrEmpty(url) {\n  if (!(url === null || url === void 0 ? void 0 : url.includes('?'))) {\n    return {};\n  }\n  const [_, ...rest] = url.split('?');\n  return querystringDecode(rest.join('?'));\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * How long to wait for the initial auth event before concluding no\r\n * redirect pending\r\n */\nconst INITIAL_EVENT_TIMEOUT_MS = 500;\nclass CordovaPopupRedirectResolver {\n  constructor() {\n    this._redirectPersistence = browserSessionPersistence;\n    this._shouldInitProactively = true; // This is lightweight for Cordova\n    this.eventManagers = new Map();\n    this.originValidationPromises = {};\n    this._completeRedirectFn = _getRedirectResult;\n    this._overrideRedirectResult = _overrideRedirectResult;\n  }\n  _initialize(auth) {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      const key = auth._key();\n      let manager = _this2.eventManagers.get(key);\n      if (!manager) {\n        manager = new CordovaAuthEventManager(auth);\n        _this2.eventManagers.set(key, manager);\n        _this2.attachCallbackListeners(auth, manager);\n      }\n      return manager;\n    })();\n  }\n  _openPopup(auth) {\n    _fail(auth, \"operation-not-supported-in-this-environment\" /* AuthErrorCode.OPERATION_NOT_SUPPORTED */);\n  }\n  _openRedirect(auth, provider, authType, eventId) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _checkCordovaConfiguration(auth);\n      const manager = yield _this3._initialize(auth);\n      yield manager.initialized();\n      // Reset the persisted redirect states. This does not matter on Web where\n      // the redirect always blows away application state entirely. On Cordova,\n      // the app maintains control flow through the redirect.\n      manager.resetRedirect();\n      _clearRedirectOutcomes();\n      yield _this3._originValidation(auth);\n      const event = _generateNewEvent(auth, authType, eventId);\n      yield _savePartialEvent(auth, event);\n      const url = yield _generateHandlerUrl(auth, event, provider);\n      const iabRef = yield _performRedirect(url);\n      return _waitForAppResume(auth, manager, iabRef);\n    })();\n  }\n  _isIframeWebStorageSupported(_auth, _cb) {\n    throw new Error('Method not implemented.');\n  }\n  _originValidation(auth) {\n    const key = auth._key();\n    if (!this.originValidationPromises[key]) {\n      this.originValidationPromises[key] = _validateOrigin(auth);\n    }\n    return this.originValidationPromises[key];\n  }\n  attachCallbackListeners(auth, manager) {\n    // Get the global plugins\n    const {\n      universalLinks,\n      handleOpenURL,\n      BuildInfo\n    } = _cordovaWindow();\n    const noEventTimeout = setTimeout(/*#__PURE__*/_asyncToGenerator(function* () {\n      // We didn't see that initial event. Clear any pending object and\n      // dispatch no event\n      yield _getAndRemoveEvent(auth);\n      manager.onEvent(generateNoEvent());\n    }), INITIAL_EVENT_TIMEOUT_MS);\n    const universalLinksCb = /*#__PURE__*/function () {\n      var _ref2 = _asyncToGenerator(function* (eventData) {\n        // We have an event so we can clear the no event timeout\n        clearTimeout(noEventTimeout);\n        const partialEvent = yield _getAndRemoveEvent(auth);\n        let finalEvent = null;\n        if (partialEvent && (eventData === null || eventData === void 0 ? void 0 : eventData['url'])) {\n          finalEvent = _eventFromPartialAndUrl(partialEvent, eventData['url']);\n        }\n        // If finalEvent is never filled, trigger with no event\n        manager.onEvent(finalEvent || generateNoEvent());\n      });\n      return function universalLinksCb(_x0) {\n        return _ref2.apply(this, arguments);\n      };\n    }();\n    // Universal links subscriber doesn't exist for iOS, so we need to check\n    if (typeof universalLinks !== 'undefined' && typeof universalLinks.subscribe === 'function') {\n      universalLinks.subscribe(null, universalLinksCb);\n    }\n    // iOS 7 or 8 custom URL schemes.\n    // This is also the current default behavior for iOS 9+.\n    // For this to work, cordova-plugin-customurlscheme needs to be installed.\n    // https://github.com/EddyVerbruggen/Custom-URL-scheme\n    // Do not overwrite the existing developer's URL handler.\n    const existingHandleOpenURL = handleOpenURL;\n    const packagePrefix = `${BuildInfo.packageName.toLowerCase()}://`;\n    _cordovaWindow().handleOpenURL = /*#__PURE__*/function () {\n      var _ref3 = _asyncToGenerator(function* (url) {\n        if (url.toLowerCase().startsWith(packagePrefix)) {\n          // We want this intentionally to float\n          // eslint-disable-next-line @typescript-eslint/no-floating-promises\n          universalLinksCb({\n            url\n          });\n        }\n        // Call the developer's handler if it is present.\n        if (typeof existingHandleOpenURL === 'function') {\n          try {\n            existingHandleOpenURL(url);\n          } catch (e) {\n            // This is a developer error. Don't stop the flow of the SDK.\n            console.error(e);\n          }\n        }\n      });\n      return function (_x1) {\n        return _ref3.apply(this, arguments);\n      };\n    }();\n  }\n}\n/**\r\n * An implementation of {@link PopupRedirectResolver} suitable for Cordova\r\n * based applications.\r\n *\r\n * @public\r\n */\nconst cordovaPopupRedirectResolver = CordovaPopupRedirectResolver;\nfunction generateNoEvent() {\n  return {\n    type: \"unknown\" /* AuthEventType.UNKNOWN */,\n    eventId: null,\n    sessionId: null,\n    urlResponse: null,\n    postBody: null,\n    tenantId: null,\n    error: _createError(\"no-auth-event\" /* AuthErrorCode.NO_AUTH_EVENT */)\n  };\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// This function should only be called by frameworks (e.g. FirebaseUI-web) to log their usage.\n// It is not intended for direct use by developer apps. NO jsdoc here to intentionally leave it out\n// of autogenerated documentation pages to reduce accidental misuse.\nfunction addFrameworkForLogging(auth, framework) {\n  _castAuth(auth)._logFramework(framework);\n}\nexport { addFrameworkForLogging, cordovaPopupRedirectResolver };", "map": {"version": 3, "names": ["as", "debugAssert", "at", "_isIOS", "au", "_isAndroid", "av", "_fail", "aw", "_getRedirectUrl", "ax", "_getProjectConfig", "ay", "_isIOS7Or8", "az", "_createError", "aA", "_assert", "aB", "AuthEventManager", "aC", "_getInstance", "b", "browserLocalPersistence", "aD", "_persistenceKeyName", "a", "browserSessionPersistence", "aE", "_getRedirectResult", "aF", "_overrideRedirectResult", "aG", "_clearRedirectOutcomes", "aH", "_castAuth", "A", "ActionCodeOperation", "ag", "ActionCodeURL", "J", "AuthCredential", "G", "AuthErrorCodes", "aJ", "AuthImpl", "aM", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "K", "EmailAuthCredential", "Q", "EmailAuthProvider", "U", "FacebookAuthProvider", "F", "FactorId", "aN", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "W", "GithubAuth<PERSON>rovider", "V", "GoogleAuthProvider", "L", "OAuthCredential", "X", "OAuth<PERSON><PERSON><PERSON>", "O", "OperationType", "M", "PhoneAuthCredential", "P", "PhoneAuthProvider", "m", "PhoneMultiFactorGenerator", "p", "ProviderId", "R", "RecaptchaVerifier", "aO", "SAMLAuthCredential", "Y", "SAMLAuthProvider", "S", "SignInMethod", "T", "TotpMultiFactorGenerator", "n", "TotpSecret", "Z", "TwitterAuthProvider", "aI", "UserImpl", "aL", "_generateEventId", "aK", "_getClientVersion", "a5", "applyActionCode", "w", "beforeAuthStateChanged", "k", "browserPopupRedirectResolver", "a6", "checkActionCode", "a4", "confirmPasswordReset", "I", "connectAuthEmulator", "a8", "createUserWithEmailAndPassword", "D", "debugErrorMap", "C", "deleteUser", "ad", "fetchSignInMethodsForEmail", "ao", "getAdditionalUserInfo", "o", "getAuth", "al", "getIdToken", "am", "getIdTokenResult", "aq", "getMultiFactorResolver", "j", "getRedirectResult", "N", "inMemoryPersistence", "i", "indexedDBLocalPersistence", "H", "initializeAuth", "t", "initializeRecaptchaConfig", "ab", "isSignInWithEmailLink", "a0", "linkWithCredential", "l", "linkWithPhoneNumber", "d", "linkWithPopup", "g", "linkWithRedirect", "ar", "multiFactor", "x", "onAuthStateChanged", "v", "onIdTokenChanged", "ah", "parseActionCodeURL", "E", "prodErrorMap", "a1", "reauthenticateWithCredential", "r", "reauthenticateWithPhoneNumber", "e", "reauthenticateWithPopup", "h", "reauthenticateWithRedirect", "ap", "reload", "ae", "sendEmailVerification", "a3", "sendPasswordResetEmail", "aa", "sendSignInLinkToEmail", "q", "setPersistence", "_", "signInAnonymously", "$", "signInWithCredential", "a2", "signInWithCustomToken", "a9", "signInWithEmailAndPassword", "ac", "signInWithEmailLink", "s", "signInWithPhoneNumber", "c", "signInWithPopup", "f", "signInWithRedirect", "B", "signOut", "an", "unlink", "z", "updateCurrentUser", "aj", "updateEmail", "ak", "updatePassword", "u", "updatePhoneNumber", "ai", "updateProfile", "y", "useDeviceLanguage", "af", "verifyBeforeUpdateEmail", "a7", "verifyPasswordResetCode", "querystringDecode", "_cordovaWindow", "window", "REDIRECT_TIMEOUT_MS", "_generateHandlerUrl", "_x", "_x2", "_x3", "_generateHandlerUrl2", "apply", "arguments", "_asyncToGenerator", "auth", "event", "provider", "_a", "BuildInfo", "sessionId", "sessionDigest", "computeSha256", "additionalParams", "packageName", "displayName", "type", "undefined", "eventId", "_validate<PERSON><PERSON><PERSON>", "_x4", "_validateOrigin2", "request", "iosBundleId", "androidPackageName", "_performRedirect", "handlerUrl", "<PERSON><PERSON>", "Promise", "resolve", "plugins", "browsertab", "isAvailable", "browserTabIsAvailable", "iabRef", "openUrl", "InAppBrowser", "open", "_waitForAppResume", "_x5", "_x6", "_x7", "_waitForAppResume2", "eventListener", "cleanup", "reject", "onCloseTimer", "authEventSeen", "closeBrowserTab", "close", "resumed", "setTimeout", "visibilityChanged", "document", "visibilityState", "addPassiveListener", "addEventListener", "removePassiveListener", "removeEventListener", "clearTimeout", "_checkCordovaConfiguration", "_b", "_c", "_d", "_e", "_f", "_g", "_h", "_j", "_k", "win", "universalLinks", "subscribe", "missingPlugin", "_x8", "_computeSha", "bytes", "stringToArrayBuffer", "buf", "crypto", "subtle", "digest", "arr", "Array", "from", "Uint8Array", "map", "num", "toString", "padStart", "join", "str", "test", "TextEncoder", "encode", "buff", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "view", "charCodeAt", "SESSION_ID_LENGTH", "CordovaAuthEventManager", "constructor", "passiveListeners", "Set", "initPromise", "resolveInialized", "cb", "add", "delete", "resetRedirect", "queuedRedirectEvent", "hasHandledPotentialRedirect", "onEvent", "for<PERSON>ach", "initialized", "_this", "_generateNewEvent", "urlResponse", "generateSessionId", "postBody", "tenantId", "error", "_savePartialEvent", "storage", "_set", "persistenceKey", "_getAndRemoveEvent", "_x9", "_getAndRemoveEvent2", "_get", "_remove", "_eventFromPartialAndUrl", "partialEvent", "url", "callbackUrl", "_getDeepLinkFromCallback", "includes", "params", "searchParamsOrEmpty", "errorObject", "parseJsonOrNull", "decodeURIComponent", "code", "split", "chars", "<PERSON><PERSON><PERSON><PERSON>", "idx", "Math", "floor", "random", "push", "char<PERSON>t", "config", "<PERSON><PERSON><PERSON><PERSON>", "name", "json", "JSON", "parse", "link", "doubleDeepLink", "iOSDeepLink", "iOSDoubleDeepLink", "rest", "INITIAL_EVENT_TIMEOUT_MS", "CordovaPopupRedirectResolver", "_redirectPersistence", "_shouldInitProactively", "eventManagers", "Map", "originValidationPromises", "_completeRedirectFn", "_initialize", "_this2", "key", "_key", "manager", "get", "set", "attachCallbackListeners", "_openPopup", "_openRedirect", "authType", "_this3", "_originValidation", "_isIframeWebStorageSupported", "_auth", "_cb", "Error", "handleOpenURL", "noEventTimeout", "generateNoEvent", "universalLinksCb", "_ref2", "eventData", "finalEvent", "_x0", "existingHandleOpenURL", "packagePrefix", "toLowerCase", "_ref3", "startsWith", "console", "_x1", "cordovaPopupRedirectResolver", "addFrameworkForLogging", "framework", "_logFramework"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/fire/node_modules/@firebase/auth/dist/esm2017/internal.js"], "sourcesContent": ["import { as as debugAssert, at as _isIOS, au as _isAndroid, av as _fail, aw as _getRedirectUrl, ax as _getProjectConfig, ay as _isIOS7Or8, az as _createError, aA as _assert, aB as AuthEventManager, aC as _getInstance, b as browserLocalPersistence, aD as _persistenceKeyName, a as browserSessionPersistence, aE as _getRedirectResult, aF as _overrideRedirectResult, aG as _clearRedirectOutcomes, aH as _castAuth } from './index-e3d5d3f4.js';\nexport { A as ActionCodeOperation, ag as ActionCodeURL, J as AuthCredential, G as AuthErrorCodes, aJ as AuthImpl, aM as AuthPopup, K as EmailAuthCredential, Q as EmailAuthProvider, U as FacebookAuthProvider, F as FactorId, aN as FetchProvider, W as GithubAuthProvider, V as GoogleAuthProvider, L as OAuthCredential, X as OAuthProvider, O as OperationType, M as PhoneAuthCredential, P as PhoneAuthProvider, m as PhoneMultiFactorGenerator, p as ProviderId, R as RecaptchaVerifier, aO as SAMLAuthCredential, Y as SAMLAuthProvider, S as SignInMethod, T as TotpMultiFactorGenerator, n as TotpSecret, Z as TwitterAuthProvider, aI as UserImpl, aA as _assert, aH as _castAuth, av as _fail, aL as _generateEventId, aK as _getClientVersion, aC as _getInstance, aE as _getRedirectResult, aF as _overrideRedirectResult, aD as _persistenceKeyName, a5 as applyActionCode, w as beforeAuthStateChanged, b as browserLocalPersistence, k as browserPopupRedirectResolver, a as browserSessionPersistence, a6 as checkActionCode, a4 as confirmPasswordReset, I as connectAuthEmulator, a8 as createUserWithEmailAndPassword, D as debugErrorMap, C as deleteUser, ad as fetchSignInMethodsForEmail, ao as getAdditionalUserInfo, o as getAuth, al as getIdToken, am as getIdTokenResult, aq as getMultiFactorResolver, j as getRedirectResult, N as inMemoryPersistence, i as indexedDBLocalPersistence, H as initializeAuth, t as initializeRecaptchaConfig, ab as isSignInWithEmailLink, a0 as linkWithCredential, l as linkWithPhoneNumber, d as linkWithPopup, g as linkWithRedirect, ar as multiFactor, x as onAuthStateChanged, v as onIdTokenChanged, ah as parseActionCodeURL, E as prodErrorMap, a1 as reauthenticateWithCredential, r as reauthenticateWithPhoneNumber, e as reauthenticateWithPopup, h as reauthenticateWithRedirect, ap as reload, ae as sendEmailVerification, a3 as sendPasswordResetEmail, aa as sendSignInLinkToEmail, q as setPersistence, _ as signInAnonymously, $ as signInWithCredential, a2 as signInWithCustomToken, a9 as signInWithEmailAndPassword, ac as signInWithEmailLink, s as signInWithPhoneNumber, c as signInWithPopup, f as signInWithRedirect, B as signOut, an as unlink, z as updateCurrentUser, aj as updateEmail, ak as updatePassword, u as updatePhoneNumber, ai as updateProfile, y as useDeviceLanguage, af as verifyBeforeUpdateEmail, a7 as verifyPasswordResetCode } from './index-e3d5d3f4.js';\nimport { querystringDecode } from '@firebase/util';\nimport '@firebase/app';\nimport 'tslib';\nimport '@firebase/logger';\nimport '@firebase/component';\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction _cordovaWindow() {\r\n    return window;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * How long to wait after the app comes back into focus before concluding that\r\n * the user closed the sign in tab.\r\n */\r\nconst REDIRECT_TIMEOUT_MS = 2000;\r\n/**\r\n * Generates the URL for the OAuth handler.\r\n */\r\nasync function _generateHandlerUrl(auth, event, provider) {\r\n    var _a;\r\n    // Get the cordova plugins\r\n    const { BuildInfo } = _cordovaWindow();\r\n    debugAssert(event.sessionId, 'AuthEvent did not contain a session ID');\r\n    const sessionDigest = await computeSha256(event.sessionId);\r\n    const additionalParams = {};\r\n    if (_isIOS()) {\r\n        // iOS app identifier\r\n        additionalParams['ibi'] = BuildInfo.packageName;\r\n    }\r\n    else if (_isAndroid()) {\r\n        // Android app identifier\r\n        additionalParams['apn'] = BuildInfo.packageName;\r\n    }\r\n    else {\r\n        _fail(auth, \"operation-not-supported-in-this-environment\" /* AuthErrorCode.OPERATION_NOT_SUPPORTED */);\r\n    }\r\n    // Add the display name if available\r\n    if (BuildInfo.displayName) {\r\n        additionalParams['appDisplayName'] = BuildInfo.displayName;\r\n    }\r\n    // Attached the hashed session ID\r\n    additionalParams['sessionId'] = sessionDigest;\r\n    return _getRedirectUrl(auth, provider, event.type, undefined, (_a = event.eventId) !== null && _a !== void 0 ? _a : undefined, additionalParams);\r\n}\r\n/**\r\n * Validates that this app is valid for this project configuration\r\n */\r\nasync function _validateOrigin(auth) {\r\n    const { BuildInfo } = _cordovaWindow();\r\n    const request = {};\r\n    if (_isIOS()) {\r\n        request.iosBundleId = BuildInfo.packageName;\r\n    }\r\n    else if (_isAndroid()) {\r\n        request.androidPackageName = BuildInfo.packageName;\r\n    }\r\n    else {\r\n        _fail(auth, \"operation-not-supported-in-this-environment\" /* AuthErrorCode.OPERATION_NOT_SUPPORTED */);\r\n    }\r\n    // Will fail automatically if package name is not authorized\r\n    await _getProjectConfig(auth, request);\r\n}\r\nfunction _performRedirect(handlerUrl) {\r\n    // Get the cordova plugins\r\n    const { cordova } = _cordovaWindow();\r\n    return new Promise(resolve => {\r\n        cordova.plugins.browsertab.isAvailable(browserTabIsAvailable => {\r\n            let iabRef = null;\r\n            if (browserTabIsAvailable) {\r\n                cordova.plugins.browsertab.openUrl(handlerUrl);\r\n            }\r\n            else {\r\n                // TODO: Return the inappbrowser ref that's returned from the open call\r\n                iabRef = cordova.InAppBrowser.open(handlerUrl, _isIOS7Or8() ? '_blank' : '_system', 'location=yes');\r\n            }\r\n            resolve(iabRef);\r\n        });\r\n    });\r\n}\r\n/**\r\n * This function waits for app activity to be seen before resolving. It does\r\n * this by attaching listeners to various dom events. Once the app is determined\r\n * to be visible, this promise resolves. AFTER that resolution, the listeners\r\n * are detached and any browser tabs left open will be closed.\r\n */\r\nasync function _waitForAppResume(auth, eventListener, iabRef) {\r\n    // Get the cordova plugins\r\n    const { cordova } = _cordovaWindow();\r\n    let cleanup = () => { };\r\n    try {\r\n        await new Promise((resolve, reject) => {\r\n            let onCloseTimer = null;\r\n            // DEFINE ALL THE CALLBACKS =====\r\n            function authEventSeen() {\r\n                var _a;\r\n                // Auth event was detected. Resolve this promise and close the extra\r\n                // window if it's still open.\r\n                resolve();\r\n                const closeBrowserTab = (_a = cordova.plugins.browsertab) === null || _a === void 0 ? void 0 : _a.close;\r\n                if (typeof closeBrowserTab === 'function') {\r\n                    closeBrowserTab();\r\n                }\r\n                // Close inappbrowser emebedded webview in iOS7 and 8 case if still\r\n                // open.\r\n                if (typeof (iabRef === null || iabRef === void 0 ? void 0 : iabRef.close) === 'function') {\r\n                    iabRef.close();\r\n                }\r\n            }\r\n            function resumed() {\r\n                if (onCloseTimer) {\r\n                    // This code already ran; do not rerun.\r\n                    return;\r\n                }\r\n                onCloseTimer = window.setTimeout(() => {\r\n                    // Wait two seeconds after resume then reject.\r\n                    reject(_createError(auth, \"redirect-cancelled-by-user\" /* AuthErrorCode.REDIRECT_CANCELLED_BY_USER */));\r\n                }, REDIRECT_TIMEOUT_MS);\r\n            }\r\n            function visibilityChanged() {\r\n                if ((document === null || document === void 0 ? void 0 : document.visibilityState) === 'visible') {\r\n                    resumed();\r\n                }\r\n            }\r\n            // ATTACH ALL THE LISTENERS =====\r\n            // Listen for the auth event\r\n            eventListener.addPassiveListener(authEventSeen);\r\n            // Listen for resume and visibility events\r\n            document.addEventListener('resume', resumed, false);\r\n            if (_isAndroid()) {\r\n                document.addEventListener('visibilitychange', visibilityChanged, false);\r\n            }\r\n            // SETUP THE CLEANUP FUNCTION =====\r\n            cleanup = () => {\r\n                eventListener.removePassiveListener(authEventSeen);\r\n                document.removeEventListener('resume', resumed, false);\r\n                document.removeEventListener('visibilitychange', visibilityChanged, false);\r\n                if (onCloseTimer) {\r\n                    window.clearTimeout(onCloseTimer);\r\n                }\r\n            };\r\n        });\r\n    }\r\n    finally {\r\n        cleanup();\r\n    }\r\n}\r\n/**\r\n * Checks the configuration of the Cordova environment. This has no side effect\r\n * if the configuration is correct; otherwise it throws an error with the\r\n * missing plugin.\r\n */\r\nfunction _checkCordovaConfiguration(auth) {\r\n    var _a, _b, _c, _d, _e, _f, _g, _h, _j, _k;\r\n    const win = _cordovaWindow();\r\n    // Check all dependencies installed.\r\n    // https://github.com/nordnet/cordova-universal-links-plugin\r\n    // Note that cordova-universal-links-plugin has been abandoned.\r\n    // A fork with latest fixes is available at:\r\n    // https://www.npmjs.com/package/cordova-universal-links-plugin-fix\r\n    _assert(typeof ((_a = win === null || win === void 0 ? void 0 : win.universalLinks) === null || _a === void 0 ? void 0 : _a.subscribe) === 'function', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\r\n        missingPlugin: 'cordova-universal-links-plugin-fix'\r\n    });\r\n    // https://www.npmjs.com/package/cordova-plugin-buildinfo\r\n    _assert(typeof ((_b = win === null || win === void 0 ? void 0 : win.BuildInfo) === null || _b === void 0 ? void 0 : _b.packageName) !== 'undefined', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\r\n        missingPlugin: 'cordova-plugin-buildInfo'\r\n    });\r\n    // https://github.com/google/cordova-plugin-browsertab\r\n    _assert(typeof ((_e = (_d = (_c = win === null || win === void 0 ? void 0 : win.cordova) === null || _c === void 0 ? void 0 : _c.plugins) === null || _d === void 0 ? void 0 : _d.browsertab) === null || _e === void 0 ? void 0 : _e.openUrl) === 'function', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\r\n        missingPlugin: 'cordova-plugin-browsertab'\r\n    });\r\n    _assert(typeof ((_h = (_g = (_f = win === null || win === void 0 ? void 0 : win.cordova) === null || _f === void 0 ? void 0 : _f.plugins) === null || _g === void 0 ? void 0 : _g.browsertab) === null || _h === void 0 ? void 0 : _h.isAvailable) === 'function', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\r\n        missingPlugin: 'cordova-plugin-browsertab'\r\n    });\r\n    // https://cordova.apache.org/docs/en/latest/reference/cordova-plugin-inappbrowser/\r\n    _assert(typeof ((_k = (_j = win === null || win === void 0 ? void 0 : win.cordova) === null || _j === void 0 ? void 0 : _j.InAppBrowser) === null || _k === void 0 ? void 0 : _k.open) === 'function', auth, \"invalid-cordova-configuration\" /* AuthErrorCode.INVALID_CORDOVA_CONFIGURATION */, {\r\n        missingPlugin: 'cordova-plugin-inappbrowser'\r\n    });\r\n}\r\n/**\r\n * Computes the SHA-256 of a session ID. The SubtleCrypto interface is only\r\n * available in \"secure\" contexts, which covers Cordova (which is served on a file\r\n * protocol).\r\n */\r\nasync function computeSha256(sessionId) {\r\n    const bytes = stringToArrayBuffer(sessionId);\r\n    // TODO: For IE11 crypto has a different name and this operation comes back\r\n    //       as an object, not a promise. This is the old proposed standard that\r\n    //       is used by IE11:\r\n    // https://www.w3.org/TR/2013/WD-WebCryptoAPI-20130108/#cryptooperation-interface\r\n    const buf = await crypto.subtle.digest('SHA-256', bytes);\r\n    const arr = Array.from(new Uint8Array(buf));\r\n    return arr.map(num => num.toString(16).padStart(2, '0')).join('');\r\n}\r\nfunction stringToArrayBuffer(str) {\r\n    // This function is only meant to deal with an ASCII charset and makes\r\n    // certain simplifying assumptions.\r\n    debugAssert(/[0-9a-zA-Z]+/.test(str), 'Can only convert alpha-numeric strings');\r\n    if (typeof TextEncoder !== 'undefined') {\r\n        return new TextEncoder().encode(str);\r\n    }\r\n    const buff = new ArrayBuffer(str.length);\r\n    const view = new Uint8Array(buff);\r\n    for (let i = 0; i < str.length; i++) {\r\n        view[i] = str.charCodeAt(i);\r\n    }\r\n    return view;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst SESSION_ID_LENGTH = 20;\r\n/** Custom AuthEventManager that adds passive listeners to events */\r\nclass CordovaAuthEventManager extends AuthEventManager {\r\n    constructor() {\r\n        super(...arguments);\r\n        this.passiveListeners = new Set();\r\n        this.initPromise = new Promise(resolve => {\r\n            this.resolveInialized = resolve;\r\n        });\r\n    }\r\n    addPassiveListener(cb) {\r\n        this.passiveListeners.add(cb);\r\n    }\r\n    removePassiveListener(cb) {\r\n        this.passiveListeners.delete(cb);\r\n    }\r\n    // In a Cordova environment, this manager can live through multiple redirect\r\n    // operations\r\n    resetRedirect() {\r\n        this.queuedRedirectEvent = null;\r\n        this.hasHandledPotentialRedirect = false;\r\n    }\r\n    /** Override the onEvent method */\r\n    onEvent(event) {\r\n        this.resolveInialized();\r\n        this.passiveListeners.forEach(cb => cb(event));\r\n        return super.onEvent(event);\r\n    }\r\n    async initialized() {\r\n        await this.initPromise;\r\n    }\r\n}\r\n/**\r\n * Generates a (partial) {@link AuthEvent}.\r\n */\r\nfunction _generateNewEvent(auth, type, eventId = null) {\r\n    return {\r\n        type,\r\n        eventId,\r\n        urlResponse: null,\r\n        sessionId: generateSessionId(),\r\n        postBody: null,\r\n        tenantId: auth.tenantId,\r\n        error: _createError(auth, \"no-auth-event\" /* AuthErrorCode.NO_AUTH_EVENT */)\r\n    };\r\n}\r\nfunction _savePartialEvent(auth, event) {\r\n    return storage()._set(persistenceKey(auth), event);\r\n}\r\nasync function _getAndRemoveEvent(auth) {\r\n    const event = (await storage()._get(persistenceKey(auth)));\r\n    if (event) {\r\n        await storage()._remove(persistenceKey(auth));\r\n    }\r\n    return event;\r\n}\r\nfunction _eventFromPartialAndUrl(partialEvent, url) {\r\n    var _a, _b;\r\n    // Parse the deep link within the dynamic link URL.\r\n    const callbackUrl = _getDeepLinkFromCallback(url);\r\n    // Confirm it is actually a callback URL.\r\n    // Currently the universal link will be of this format:\r\n    // https://<AUTH_DOMAIN>/__/auth/callback<OAUTH_RESPONSE>\r\n    // This is a fake URL but is not intended to take the user anywhere\r\n    // and just redirect to the app.\r\n    if (callbackUrl.includes('/__/auth/callback')) {\r\n        // Check if there is an error in the URL.\r\n        // This mechanism is also used to pass errors back to the app:\r\n        // https://<AUTH_DOMAIN>/__/auth/callback?firebaseError=<STRINGIFIED_ERROR>\r\n        const params = searchParamsOrEmpty(callbackUrl);\r\n        // Get the error object corresponding to the stringified error if found.\r\n        const errorObject = params['firebaseError']\r\n            ? parseJsonOrNull(decodeURIComponent(params['firebaseError']))\r\n            : null;\r\n        const code = (_b = (_a = errorObject === null || errorObject === void 0 ? void 0 : errorObject['code']) === null || _a === void 0 ? void 0 : _a.split('auth/')) === null || _b === void 0 ? void 0 : _b[1];\r\n        const error = code ? _createError(code) : null;\r\n        if (error) {\r\n            return {\r\n                type: partialEvent.type,\r\n                eventId: partialEvent.eventId,\r\n                tenantId: partialEvent.tenantId,\r\n                error,\r\n                urlResponse: null,\r\n                sessionId: null,\r\n                postBody: null\r\n            };\r\n        }\r\n        else {\r\n            return {\r\n                type: partialEvent.type,\r\n                eventId: partialEvent.eventId,\r\n                tenantId: partialEvent.tenantId,\r\n                sessionId: partialEvent.sessionId,\r\n                urlResponse: callbackUrl,\r\n                postBody: null\r\n            };\r\n        }\r\n    }\r\n    return null;\r\n}\r\nfunction generateSessionId() {\r\n    const chars = [];\r\n    const allowedChars = '1234567890abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';\r\n    for (let i = 0; i < SESSION_ID_LENGTH; i++) {\r\n        const idx = Math.floor(Math.random() * allowedChars.length);\r\n        chars.push(allowedChars.charAt(idx));\r\n    }\r\n    return chars.join('');\r\n}\r\nfunction storage() {\r\n    return _getInstance(browserLocalPersistence);\r\n}\r\nfunction persistenceKey(auth) {\r\n    return _persistenceKeyName(\"authEvent\" /* KeyName.AUTH_EVENT */, auth.config.apiKey, auth.name);\r\n}\r\nfunction parseJsonOrNull(json) {\r\n    try {\r\n        return JSON.parse(json);\r\n    }\r\n    catch (e) {\r\n        return null;\r\n    }\r\n}\r\n// Exported for testing\r\nfunction _getDeepLinkFromCallback(url) {\r\n    const params = searchParamsOrEmpty(url);\r\n    const link = params['link'] ? decodeURIComponent(params['link']) : undefined;\r\n    // Double link case (automatic redirect)\r\n    const doubleDeepLink = searchParamsOrEmpty(link)['link'];\r\n    // iOS custom scheme links.\r\n    const iOSDeepLink = params['deep_link_id']\r\n        ? decodeURIComponent(params['deep_link_id'])\r\n        : undefined;\r\n    const iOSDoubleDeepLink = searchParamsOrEmpty(iOSDeepLink)['link'];\r\n    return iOSDoubleDeepLink || iOSDeepLink || doubleDeepLink || link || url;\r\n}\r\n/**\r\n * Optimistically tries to get search params from a string, or else returns an\r\n * empty search params object.\r\n */\r\nfunction searchParamsOrEmpty(url) {\r\n    if (!(url === null || url === void 0 ? void 0 : url.includes('?'))) {\r\n        return {};\r\n    }\r\n    const [_, ...rest] = url.split('?');\r\n    return querystringDecode(rest.join('?'));\r\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * How long to wait for the initial auth event before concluding no\r\n * redirect pending\r\n */\r\nconst INITIAL_EVENT_TIMEOUT_MS = 500;\r\nclass CordovaPopupRedirectResolver {\r\n    constructor() {\r\n        this._redirectPersistence = browserSessionPersistence;\r\n        this._shouldInitProactively = true; // This is lightweight for Cordova\r\n        this.eventManagers = new Map();\r\n        this.originValidationPromises = {};\r\n        this._completeRedirectFn = _getRedirectResult;\r\n        this._overrideRedirectResult = _overrideRedirectResult;\r\n    }\r\n    async _initialize(auth) {\r\n        const key = auth._key();\r\n        let manager = this.eventManagers.get(key);\r\n        if (!manager) {\r\n            manager = new CordovaAuthEventManager(auth);\r\n            this.eventManagers.set(key, manager);\r\n            this.attachCallbackListeners(auth, manager);\r\n        }\r\n        return manager;\r\n    }\r\n    _openPopup(auth) {\r\n        _fail(auth, \"operation-not-supported-in-this-environment\" /* AuthErrorCode.OPERATION_NOT_SUPPORTED */);\r\n    }\r\n    async _openRedirect(auth, provider, authType, eventId) {\r\n        _checkCordovaConfiguration(auth);\r\n        const manager = await this._initialize(auth);\r\n        await manager.initialized();\r\n        // Reset the persisted redirect states. This does not matter on Web where\r\n        // the redirect always blows away application state entirely. On Cordova,\r\n        // the app maintains control flow through the redirect.\r\n        manager.resetRedirect();\r\n        _clearRedirectOutcomes();\r\n        await this._originValidation(auth);\r\n        const event = _generateNewEvent(auth, authType, eventId);\r\n        await _savePartialEvent(auth, event);\r\n        const url = await _generateHandlerUrl(auth, event, provider);\r\n        const iabRef = await _performRedirect(url);\r\n        return _waitForAppResume(auth, manager, iabRef);\r\n    }\r\n    _isIframeWebStorageSupported(_auth, _cb) {\r\n        throw new Error('Method not implemented.');\r\n    }\r\n    _originValidation(auth) {\r\n        const key = auth._key();\r\n        if (!this.originValidationPromises[key]) {\r\n            this.originValidationPromises[key] = _validateOrigin(auth);\r\n        }\r\n        return this.originValidationPromises[key];\r\n    }\r\n    attachCallbackListeners(auth, manager) {\r\n        // Get the global plugins\r\n        const { universalLinks, handleOpenURL, BuildInfo } = _cordovaWindow();\r\n        const noEventTimeout = setTimeout(async () => {\r\n            // We didn't see that initial event. Clear any pending object and\r\n            // dispatch no event\r\n            await _getAndRemoveEvent(auth);\r\n            manager.onEvent(generateNoEvent());\r\n        }, INITIAL_EVENT_TIMEOUT_MS);\r\n        const universalLinksCb = async (eventData) => {\r\n            // We have an event so we can clear the no event timeout\r\n            clearTimeout(noEventTimeout);\r\n            const partialEvent = await _getAndRemoveEvent(auth);\r\n            let finalEvent = null;\r\n            if (partialEvent && (eventData === null || eventData === void 0 ? void 0 : eventData['url'])) {\r\n                finalEvent = _eventFromPartialAndUrl(partialEvent, eventData['url']);\r\n            }\r\n            // If finalEvent is never filled, trigger with no event\r\n            manager.onEvent(finalEvent || generateNoEvent());\r\n        };\r\n        // Universal links subscriber doesn't exist for iOS, so we need to check\r\n        if (typeof universalLinks !== 'undefined' &&\r\n            typeof universalLinks.subscribe === 'function') {\r\n            universalLinks.subscribe(null, universalLinksCb);\r\n        }\r\n        // iOS 7 or 8 custom URL schemes.\r\n        // This is also the current default behavior for iOS 9+.\r\n        // For this to work, cordova-plugin-customurlscheme needs to be installed.\r\n        // https://github.com/EddyVerbruggen/Custom-URL-scheme\r\n        // Do not overwrite the existing developer's URL handler.\r\n        const existingHandleOpenURL = handleOpenURL;\r\n        const packagePrefix = `${BuildInfo.packageName.toLowerCase()}://`;\r\n        _cordovaWindow().handleOpenURL = async (url) => {\r\n            if (url.toLowerCase().startsWith(packagePrefix)) {\r\n                // We want this intentionally to float\r\n                // eslint-disable-next-line @typescript-eslint/no-floating-promises\r\n                universalLinksCb({ url });\r\n            }\r\n            // Call the developer's handler if it is present.\r\n            if (typeof existingHandleOpenURL === 'function') {\r\n                try {\r\n                    existingHandleOpenURL(url);\r\n                }\r\n                catch (e) {\r\n                    // This is a developer error. Don't stop the flow of the SDK.\r\n                    console.error(e);\r\n                }\r\n            }\r\n        };\r\n    }\r\n}\r\n/**\r\n * An implementation of {@link PopupRedirectResolver} suitable for Cordova\r\n * based applications.\r\n *\r\n * @public\r\n */\r\nconst cordovaPopupRedirectResolver = CordovaPopupRedirectResolver;\r\nfunction generateNoEvent() {\r\n    return {\r\n        type: \"unknown\" /* AuthEventType.UNKNOWN */,\r\n        eventId: null,\r\n        sessionId: null,\r\n        urlResponse: null,\r\n        postBody: null,\r\n        tenantId: null,\r\n        error: _createError(\"no-auth-event\" /* AuthErrorCode.NO_AUTH_EVENT */)\r\n    };\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n// This function should only be called by frameworks (e.g. FirebaseUI-web) to log their usage.\r\n// It is not intended for direct use by developer apps. NO jsdoc here to intentionally leave it out\r\n// of autogenerated documentation pages to reduce accidental misuse.\r\nfunction addFrameworkForLogging(auth, framework) {\r\n    _castAuth(auth)._logFramework(framework);\r\n}\n\nexport { addFrameworkForLogging, cordovaPopupRedirectResolver };\n"], "mappings": ";AAAA,SAASA,EAAE,IAAIC,WAAW,EAAEC,EAAE,IAAIC,MAAM,EAAEC,EAAE,IAAIC,UAAU,EAAEC,EAAE,IAAIC,KAAK,EAAEC,EAAE,IAAIC,eAAe,EAAEC,EAAE,IAAIC,iBAAiB,EAAEC,EAAE,IAAIC,UAAU,EAAEC,EAAE,IAAIC,YAAY,EAAEC,EAAE,IAAIC,OAAO,EAAEC,EAAE,IAAIC,gBAAgB,EAAEC,EAAE,IAAIC,YAAY,EAAEC,CAAC,IAAIC,uBAAuB,EAAEC,EAAE,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,EAAE,IAAIC,kBAAkB,EAAEC,EAAE,IAAIC,uBAAuB,EAAEC,EAAE,IAAIC,sBAAsB,EAAEC,EAAE,IAAIC,SAAS,QAAQ,qBAAqB;AACtb,SAASC,CAAC,IAAIC,mBAAmB,EAAEC,EAAE,IAAIC,aAAa,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,cAAc,EAAEC,EAAE,IAAIC,QAAQ,EAAEC,EAAE,IAAIC,SAAS,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,QAAQ,EAAEC,EAAE,IAAIC,aAAa,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,EAAE,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,CAAC,IAAIC,YAAY,EAAEC,CAAC,IAAIC,wBAAwB,EAAEC,CAAC,IAAIC,UAAU,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,EAAE,IAAIC,QAAQ,EAAE3E,EAAE,IAAIC,OAAO,EAAEiB,EAAE,IAAIC,SAAS,EAAE7B,EAAE,IAAIC,KAAK,EAAEqF,EAAE,IAAIC,gBAAgB,EAAEC,EAAE,IAAIC,iBAAiB,EAAE3E,EAAE,IAAIC,YAAY,EAAEO,EAAE,IAAIC,kBAAkB,EAAEC,EAAE,IAAIC,uBAAuB,EAAEP,EAAE,IAAIC,mBAAmB,EAAEuE,EAAE,IAAIC,eAAe,EAAEC,CAAC,IAAIC,sBAAsB,EAAE7E,CAAC,IAAIC,uBAAuB,EAAE6E,CAAC,IAAIC,4BAA4B,EAAE3E,CAAC,IAAIC,yBAAyB,EAAE2E,EAAE,IAAIC,eAAe,EAAEC,EAAE,IAAIC,oBAAoB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,EAAE,IAAIC,8BAA8B,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,UAAU,EAAEC,EAAE,IAAIC,0BAA0B,EAAEC,EAAE,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,OAAO,EAAEC,EAAE,IAAIC,UAAU,EAAEC,EAAE,IAAIC,gBAAgB,EAAEC,EAAE,IAAIC,sBAAsB,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,yBAAyB,EAAEC,EAAE,IAAIC,qBAAqB,EAAEC,EAAE,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,aAAa,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,EAAE,IAAIC,WAAW,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,gBAAgB,EAAEC,EAAE,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,YAAY,EAAEC,EAAE,IAAIC,4BAA4B,EAAEC,CAAC,IAAIC,6BAA6B,EAAEC,CAAC,IAAIC,uBAAuB,EAAEC,CAAC,IAAIC,0BAA0B,EAAEC,EAAE,IAAIC,MAAM,EAAEC,EAAE,IAAIC,qBAAqB,EAAEC,EAAE,IAAIC,sBAAsB,EAAEC,EAAE,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,cAAc,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,CAAC,IAAIC,oBAAoB,EAAEC,EAAE,IAAIC,qBAAqB,EAAEC,EAAE,IAAIC,0BAA0B,EAAEC,EAAE,IAAIC,mBAAmB,EAAEC,CAAC,IAAIC,qBAAqB,EAAEC,CAAC,IAAIC,eAAe,EAAEC,CAAC,IAAIC,kBAAkB,EAAEC,CAAC,IAAIC,OAAO,EAAEC,EAAE,IAAIC,MAAM,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,EAAE,IAAIC,WAAW,EAAEC,EAAE,IAAIC,cAAc,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,EAAE,IAAIC,aAAa,EAAEC,CAAC,IAAIC,iBAAiB,EAAEC,EAAE,IAAIC,uBAAuB,EAAEC,EAAE,IAAIC,uBAAuB,QAAQ,qBAAqB;AAC70E,SAASC,iBAAiB,QAAQ,gBAAgB;AAClD,OAAO,eAAe;AACtB,OAAO,OAAO;AACd,OAAO,kBAAkB;AACzB,OAAO,qBAAqB;;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,cAAcA,CAAA,EAAG;EACtB,OAAOC,MAAM;AACjB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,mBAAmB,GAAG,IAAI;AAChC;AACA;AACA;AAFA,SAGeC,mBAAmBA,CAAAC,EAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,oBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AA0BlC;AACA;AACA;AAFA,SAAAF,qBAAA;EAAAA,oBAAA,GAAAG,iBAAA,CA1BA,WAAmCC,IAAI,EAAEC,KAAK,EAAEC,QAAQ,EAAE;IACtD,IAAIC,EAAE;IACN;IACA,MAAM;MAAEC;IAAU,CAAC,GAAGf,cAAc,CAAC,CAAC;IACtClN,WAAW,CAAC8N,KAAK,CAACI,SAAS,EAAE,wCAAwC,CAAC;IACtE,MAAMC,aAAa,SAASC,aAAa,CAACN,KAAK,CAACI,SAAS,CAAC;IAC1D,MAAMG,gBAAgB,GAAG,CAAC,CAAC;IAC3B,IAAInO,MAAM,CAAC,CAAC,EAAE;MACV;MACAmO,gBAAgB,CAAC,KAAK,CAAC,GAAGJ,SAAS,CAACK,WAAW;IACnD,CAAC,MACI,IAAIlO,UAAU,CAAC,CAAC,EAAE;MACnB;MACAiO,gBAAgB,CAAC,KAAK,CAAC,GAAGJ,SAAS,CAACK,WAAW;IACnD,CAAC,MACI;MACDhO,KAAK,CAACuN,IAAI,EAAE,6CAA6C,CAAC,2CAA2C,CAAC;IAC1G;IACA;IACA,IAAII,SAAS,CAACM,WAAW,EAAE;MACvBF,gBAAgB,CAAC,gBAAgB,CAAC,GAAGJ,SAAS,CAACM,WAAW;IAC9D;IACA;IACAF,gBAAgB,CAAC,WAAW,CAAC,GAAGF,aAAa;IAC7C,OAAO3N,eAAe,CAACqN,IAAI,EAAEE,QAAQ,EAAED,KAAK,CAACU,IAAI,EAAEC,SAAS,EAAE,CAACT,EAAE,GAAGF,KAAK,CAACY,OAAO,MAAM,IAAI,IAAIV,EAAE,KAAK,KAAK,CAAC,GAAGA,EAAE,GAAGS,SAAS,EAAEJ,gBAAgB,CAAC;EACpJ,CAAC;EAAA,OAAAZ,oBAAA,CAAAC,KAAA,OAAAC,SAAA;AAAA;AAAA,SAIcgB,eAAeA,CAAAC,GAAA;EAAA,OAAAC,gBAAA,CAAAnB,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAkB,iBAAA;EAAAA,gBAAA,GAAAjB,iBAAA,CAA9B,WAA+BC,IAAI,EAAE;IACjC,MAAM;MAAEI;IAAU,CAAC,GAAGf,cAAc,CAAC,CAAC;IACtC,MAAM4B,OAAO,GAAG,CAAC,CAAC;IAClB,IAAI5O,MAAM,CAAC,CAAC,EAAE;MACV4O,OAAO,CAACC,WAAW,GAAGd,SAAS,CAACK,WAAW;IAC/C,CAAC,MACI,IAAIlO,UAAU,CAAC,CAAC,EAAE;MACnB0O,OAAO,CAACE,kBAAkB,GAAGf,SAAS,CAACK,WAAW;IACtD,CAAC,MACI;MACDhO,KAAK,CAACuN,IAAI,EAAE,6CAA6C,CAAC,2CAA2C,CAAC;IAC1G;IACA;IACA,MAAMnN,iBAAiB,CAACmN,IAAI,EAAEiB,OAAO,CAAC;EAC1C,CAAC;EAAA,OAAAD,gBAAA,CAAAnB,KAAA,OAAAC,SAAA;AAAA;AACD,SAASsB,gBAAgBA,CAACC,UAAU,EAAE;EAClC;EACA,MAAM;IAAEC;EAAQ,CAAC,GAAGjC,cAAc,CAAC,CAAC;EACpC,OAAO,IAAIkC,OAAO,CAACC,OAAO,IAAI;IAC1BF,OAAO,CAACG,OAAO,CAACC,UAAU,CAACC,WAAW,CAACC,qBAAqB,IAAI;MAC5D,IAAIC,MAAM,GAAG,IAAI;MACjB,IAAID,qBAAqB,EAAE;QACvBN,OAAO,CAACG,OAAO,CAACC,UAAU,CAACI,OAAO,CAACT,UAAU,CAAC;MAClD,CAAC,MACI;QACD;QACAQ,MAAM,GAAGP,OAAO,CAACS,YAAY,CAACC,IAAI,CAACX,UAAU,EAAEtO,UAAU,CAAC,CAAC,GAAG,QAAQ,GAAG,SAAS,EAAE,cAAc,CAAC;MACvG;MACAyO,OAAO,CAACK,MAAM,CAAC;IACnB,CAAC,CAAC;EACN,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AACA;AALA,SAMeI,iBAAiBA,CAAAC,GAAA,EAAAC,GAAA,EAAAC,GAAA;EAAA,OAAAC,kBAAA,CAAAxC,KAAA,OAAAC,SAAA;AAAA;AA6DhC;AACA;AACA;AACA;AACA;AAJA,SAAAuC,mBAAA;EAAAA,kBAAA,GAAAtC,iBAAA,CA7DA,WAAiCC,IAAI,EAAEsC,aAAa,EAAET,MAAM,EAAE;IAC1D;IACA,MAAM;MAAEP;IAAQ,CAAC,GAAGjC,cAAc,CAAC,CAAC;IACpC,IAAIkD,OAAO,GAAGA,CAAA,KAAM,CAAE,CAAC;IACvB,IAAI;MACA,MAAM,IAAIhB,OAAO,CAAC,CAACC,OAAO,EAAEgB,MAAM,KAAK;QACnC,IAAIC,YAAY,GAAG,IAAI;QACvB;QACA,SAASC,aAAaA,CAAA,EAAG;UACrB,IAAIvC,EAAE;UACN;UACA;UACAqB,OAAO,CAAC,CAAC;UACT,MAAMmB,eAAe,GAAG,CAACxC,EAAE,GAAGmB,OAAO,CAACG,OAAO,CAACC,UAAU,MAAM,IAAI,IAAIvB,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACyC,KAAK;UACvG,IAAI,OAAOD,eAAe,KAAK,UAAU,EAAE;YACvCA,eAAe,CAAC,CAAC;UACrB;UACA;UACA;UACA,IAAI,QAAQd,MAAM,KAAK,IAAI,IAAIA,MAAM,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,MAAM,CAACe,KAAK,CAAC,KAAK,UAAU,EAAE;YACtFf,MAAM,CAACe,KAAK,CAAC,CAAC;UAClB;QACJ;QACA,SAASC,OAAOA,CAAA,EAAG;UACf,IAAIJ,YAAY,EAAE;YACd;YACA;UACJ;UACAA,YAAY,GAAGnD,MAAM,CAACwD,UAAU,CAAC,MAAM;YACnC;YACAN,MAAM,CAACvP,YAAY,CAAC+M,IAAI,EAAE,4BAA4B,CAAC,8CAA8C,CAAC,CAAC;UAC3G,CAAC,EAAET,mBAAmB,CAAC;QAC3B;QACA,SAASwD,iBAAiBA,CAAA,EAAG;UACzB,IAAI,CAACC,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,QAAQ,CAACC,eAAe,MAAM,SAAS,EAAE;YAC9FJ,OAAO,CAAC,CAAC;UACb;QACJ;QACA;QACA;QACAP,aAAa,CAACY,kBAAkB,CAACR,aAAa,CAAC;QAC/C;QACAM,QAAQ,CAACG,gBAAgB,CAAC,QAAQ,EAAEN,OAAO,EAAE,KAAK,CAAC;QACnD,IAAItQ,UAAU,CAAC,CAAC,EAAE;UACdyQ,QAAQ,CAACG,gBAAgB,CAAC,kBAAkB,EAAEJ,iBAAiB,EAAE,KAAK,CAAC;QAC3E;QACA;QACAR,OAAO,GAAGA,CAAA,KAAM;UACZD,aAAa,CAACc,qBAAqB,CAACV,aAAa,CAAC;UAClDM,QAAQ,CAACK,mBAAmB,CAAC,QAAQ,EAAER,OAAO,EAAE,KAAK,CAAC;UACtDG,QAAQ,CAACK,mBAAmB,CAAC,kBAAkB,EAAEN,iBAAiB,EAAE,KAAK,CAAC;UAC1E,IAAIN,YAAY,EAAE;YACdnD,MAAM,CAACgE,YAAY,CAACb,YAAY,CAAC;UACrC;QACJ,CAAC;MACL,CAAC,CAAC;IACN,CAAC,SACO;MACJF,OAAO,CAAC,CAAC;IACb;EACJ,CAAC;EAAA,OAAAF,kBAAA,CAAAxC,KAAA,OAAAC,SAAA;AAAA;AAMD,SAASyD,0BAA0BA,CAACvD,IAAI,EAAE;EACtC,IAAIG,EAAE,EAAEqD,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE,EAAEC,EAAE;EAC1C,MAAMC,GAAG,GAAG5E,cAAc,CAAC,CAAC;EAC5B;EACA;EACA;EACA;EACA;EACAlM,OAAO,CAAC,QAAQ,CAACgN,EAAE,GAAG8D,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACC,cAAc,MAAM,IAAI,IAAI/D,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACgE,SAAS,CAAC,KAAK,UAAU,EAAEnE,IAAI,EAAE,+BAA+B,CAAC,mDAAmD;IAC5OoE,aAAa,EAAE;EACnB,CAAC,CAAC;EACF;EACAjR,OAAO,CAAC,QAAQ,CAACqQ,EAAE,GAAGS,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC7D,SAAS,MAAM,IAAI,IAAIoD,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC/C,WAAW,CAAC,KAAK,WAAW,EAAET,IAAI,EAAE,+BAA+B,CAAC,mDAAmD;IAC1OoE,aAAa,EAAE;EACnB,CAAC,CAAC;EACF;EACAjR,OAAO,CAAC,QAAQ,CAACwQ,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGQ,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC3C,OAAO,MAAM,IAAI,IAAImC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChC,OAAO,MAAM,IAAI,IAAIiC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChC,UAAU,MAAM,IAAI,IAAIiC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC7B,OAAO,CAAC,KAAK,UAAU,EAAE9B,IAAI,EAAE,+BAA+B,CAAC,mDAAmD;IACpVoE,aAAa,EAAE;EACnB,CAAC,CAAC;EACFjR,OAAO,CAAC,QAAQ,CAAC2Q,EAAE,GAAG,CAACD,EAAE,GAAG,CAACD,EAAE,GAAGK,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC3C,OAAO,MAAM,IAAI,IAAIsC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACnC,OAAO,MAAM,IAAI,IAAIoC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACnC,UAAU,MAAM,IAAI,IAAIoC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACnC,WAAW,CAAC,KAAK,UAAU,EAAE3B,IAAI,EAAE,+BAA+B,CAAC,mDAAmD;IACxVoE,aAAa,EAAE;EACnB,CAAC,CAAC;EACF;EACAjR,OAAO,CAAC,QAAQ,CAAC6Q,EAAE,GAAG,CAACD,EAAE,GAAGE,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAAC3C,OAAO,MAAM,IAAI,IAAIyC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChC,YAAY,MAAM,IAAI,IAAIiC,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAChC,IAAI,CAAC,KAAK,UAAU,EAAEhC,IAAI,EAAE,+BAA+B,CAAC,mDAAmD;IAC5RoE,aAAa,EAAE;EACnB,CAAC,CAAC;AACN;AACA;AACA;AACA;AACA;AACA;AAJA,SAKe7D,aAAaA,CAAA8D,GAAA;EAAA,OAAAC,WAAA,CAAAzE,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAAwE,YAAA;EAAAA,WAAA,GAAAvE,iBAAA,CAA5B,WAA6BM,SAAS,EAAE;IACpC,MAAMkE,KAAK,GAAGC,mBAAmB,CAACnE,SAAS,CAAC;IAC5C;IACA;IACA;IACA;IACA,MAAMoE,GAAG,SAASC,MAAM,CAACC,MAAM,CAACC,MAAM,CAAC,SAAS,EAAEL,KAAK,CAAC;IACxD,MAAMM,GAAG,GAAGC,KAAK,CAACC,IAAI,CAAC,IAAIC,UAAU,CAACP,GAAG,CAAC,CAAC;IAC3C,OAAOI,GAAG,CAACI,GAAG,CAACC,GAAG,IAAIA,GAAG,CAACC,QAAQ,CAAC,EAAE,CAAC,CAACC,QAAQ,CAAC,CAAC,EAAE,GAAG,CAAC,CAAC,CAACC,IAAI,CAAC,EAAE,CAAC;EACrE,CAAC;EAAA,OAAAf,WAAA,CAAAzE,KAAA,OAAAC,SAAA;AAAA;AACD,SAAS0E,mBAAmBA,CAACc,GAAG,EAAE;EAC9B;EACA;EACAnT,WAAW,CAAC,cAAc,CAACoT,IAAI,CAACD,GAAG,CAAC,EAAE,wCAAwC,CAAC;EAC/E,IAAI,OAAOE,WAAW,KAAK,WAAW,EAAE;IACpC,OAAO,IAAIA,WAAW,CAAC,CAAC,CAACC,MAAM,CAACH,GAAG,CAAC;EACxC;EACA,MAAMI,IAAI,GAAG,IAAIC,WAAW,CAACL,GAAG,CAACM,MAAM,CAAC;EACxC,MAAMC,IAAI,GAAG,IAAIb,UAAU,CAACU,IAAI,CAAC;EACjC,KAAK,IAAItL,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGkL,GAAG,CAACM,MAAM,EAAExL,CAAC,EAAE,EAAE;IACjCyL,IAAI,CAACzL,CAAC,CAAC,GAAGkL,GAAG,CAACQ,UAAU,CAAC1L,CAAC,CAAC;EAC/B;EACA,OAAOyL,IAAI;AACf;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAME,iBAAiB,GAAG,EAAE;AAC5B;AACA,MAAMC,uBAAuB,SAAS3S,gBAAgB,CAAC;EACnD4S,WAAWA,CAAA,EAAG;IACV,KAAK,CAAC,GAAGnG,SAAS,CAAC;IACnB,IAAI,CAACoG,gBAAgB,GAAG,IAAIC,GAAG,CAAC,CAAC;IACjC,IAAI,CAACC,WAAW,GAAG,IAAI7E,OAAO,CAACC,OAAO,IAAI;MACtC,IAAI,CAAC6E,gBAAgB,GAAG7E,OAAO;IACnC,CAAC,CAAC;EACN;EACA0B,kBAAkBA,CAACoD,EAAE,EAAE;IACnB,IAAI,CAACJ,gBAAgB,CAACK,GAAG,CAACD,EAAE,CAAC;EACjC;EACAlD,qBAAqBA,CAACkD,EAAE,EAAE;IACtB,IAAI,CAACJ,gBAAgB,CAACM,MAAM,CAACF,EAAE,CAAC;EACpC;EACA;EACA;EACAG,aAAaA,CAAA,EAAG;IACZ,IAAI,CAACC,mBAAmB,GAAG,IAAI;IAC/B,IAAI,CAACC,2BAA2B,GAAG,KAAK;EAC5C;EACA;EACAC,OAAOA,CAAC3G,KAAK,EAAE;IACX,IAAI,CAACoG,gBAAgB,CAAC,CAAC;IACvB,IAAI,CAACH,gBAAgB,CAACW,OAAO,CAACP,EAAE,IAAIA,EAAE,CAACrG,KAAK,CAAC,CAAC;IAC9C,OAAO,KAAK,CAAC2G,OAAO,CAAC3G,KAAK,CAAC;EAC/B;EACM6G,WAAWA,CAAA,EAAG;IAAA,IAAAC,KAAA;IAAA,OAAAhH,iBAAA;MAChB,MAAMgH,KAAI,CAACX,WAAW;IAAC;EAC3B;AACJ;AACA;AACA;AACA;AACA,SAASY,iBAAiBA,CAAChH,IAAI,EAAEW,IAAI,EAAEE,OAAO,GAAG,IAAI,EAAE;EACnD,OAAO;IACHF,IAAI;IACJE,OAAO;IACPoG,WAAW,EAAE,IAAI;IACjB5G,SAAS,EAAE6G,iBAAiB,CAAC,CAAC;IAC9BC,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAEpH,IAAI,CAACoH,QAAQ;IACvBC,KAAK,EAAEpU,YAAY,CAAC+M,IAAI,EAAE,eAAe,CAAC,iCAAiC;EAC/E,CAAC;AACL;AACA,SAASsH,iBAAiBA,CAACtH,IAAI,EAAEC,KAAK,EAAE;EACpC,OAAOsH,OAAO,CAAC,CAAC,CAACC,IAAI,CAACC,cAAc,CAACzH,IAAI,CAAC,EAAEC,KAAK,CAAC;AACtD;AAAC,SACcyH,kBAAkBA,CAAAC,GAAA;EAAA,OAAAC,mBAAA,CAAA/H,KAAA,OAAAC,SAAA;AAAA;AAAA,SAAA8H,oBAAA;EAAAA,mBAAA,GAAA7H,iBAAA,CAAjC,WAAkCC,IAAI,EAAE;IACpC,MAAMC,KAAK,SAAUsH,OAAO,CAAC,CAAC,CAACM,IAAI,CAACJ,cAAc,CAACzH,IAAI,CAAC,CAAE;IAC1D,IAAIC,KAAK,EAAE;MACP,MAAMsH,OAAO,CAAC,CAAC,CAACO,OAAO,CAACL,cAAc,CAACzH,IAAI,CAAC,CAAC;IACjD;IACA,OAAOC,KAAK;EAChB,CAAC;EAAA,OAAA2H,mBAAA,CAAA/H,KAAA,OAAAC,SAAA;AAAA;AACD,SAASiI,uBAAuBA,CAACC,YAAY,EAAEC,GAAG,EAAE;EAChD,IAAI9H,EAAE,EAAEqD,EAAE;EACV;EACA,MAAM0E,WAAW,GAAGC,wBAAwB,CAACF,GAAG,CAAC;EACjD;EACA;EACA;EACA;EACA;EACA,IAAIC,WAAW,CAACE,QAAQ,CAAC,mBAAmB,CAAC,EAAE;IAC3C;IACA;IACA;IACA,MAAMC,MAAM,GAAGC,mBAAmB,CAACJ,WAAW,CAAC;IAC/C;IACA,MAAMK,WAAW,GAAGF,MAAM,CAAC,eAAe,CAAC,GACrCG,eAAe,CAACC,kBAAkB,CAACJ,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,GAC5D,IAAI;IACV,MAAMK,IAAI,GAAG,CAAClF,EAAE,GAAG,CAACrD,EAAE,GAAGoI,WAAW,KAAK,IAAI,IAAIA,WAAW,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,WAAW,CAAC,MAAM,CAAC,MAAM,IAAI,IAAIpI,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACwI,KAAK,CAAC,OAAO,CAAC,MAAM,IAAI,IAAInF,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAAC,CAAC,CAAC;IAC1M,MAAM6D,KAAK,GAAGqB,IAAI,GAAGzV,YAAY,CAACyV,IAAI,CAAC,GAAG,IAAI;IAC9C,IAAIrB,KAAK,EAAE;MACP,OAAO;QACH1G,IAAI,EAAEqH,YAAY,CAACrH,IAAI;QACvBE,OAAO,EAAEmH,YAAY,CAACnH,OAAO;QAC7BuG,QAAQ,EAAEY,YAAY,CAACZ,QAAQ;QAC/BC,KAAK;QACLJ,WAAW,EAAE,IAAI;QACjB5G,SAAS,EAAE,IAAI;QACf8G,QAAQ,EAAE;MACd,CAAC;IACL,CAAC,MACI;MACD,OAAO;QACHxG,IAAI,EAAEqH,YAAY,CAACrH,IAAI;QACvBE,OAAO,EAAEmH,YAAY,CAACnH,OAAO;QAC7BuG,QAAQ,EAAEY,YAAY,CAACZ,QAAQ;QAC/B/G,SAAS,EAAE2H,YAAY,CAAC3H,SAAS;QACjC4G,WAAW,EAAEiB,WAAW;QACxBf,QAAQ,EAAE;MACd,CAAC;IACL;EACJ;EACA,OAAO,IAAI;AACf;AACA,SAASD,iBAAiBA,CAAA,EAAG;EACzB,MAAM0B,KAAK,GAAG,EAAE;EAChB,MAAMC,YAAY,GAAG,gEAAgE;EACrF,KAAK,IAAIzO,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG2L,iBAAiB,EAAE3L,CAAC,EAAE,EAAE;IACxC,MAAM0O,GAAG,GAAGC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAGJ,YAAY,CAACjD,MAAM,CAAC;IAC3DgD,KAAK,CAACM,IAAI,CAACL,YAAY,CAACM,MAAM,CAACL,GAAG,CAAC,CAAC;EACxC;EACA,OAAOF,KAAK,CAACvD,IAAI,CAAC,EAAE,CAAC;AACzB;AACA,SAASkC,OAAOA,CAAA,EAAG;EACf,OAAOhU,YAAY,CAACE,uBAAuB,CAAC;AAChD;AACA,SAASgU,cAAcA,CAACzH,IAAI,EAAE;EAC1B,OAAOrM,mBAAmB,CAAC,WAAW,CAAC,0BAA0BqM,IAAI,CAACoJ,MAAM,CAACC,MAAM,EAAErJ,IAAI,CAACsJ,IAAI,CAAC;AACnG;AACA,SAASd,eAAeA,CAACe,IAAI,EAAE;EAC3B,IAAI;IACA,OAAOC,IAAI,CAACC,KAAK,CAACF,IAAI,CAAC;EAC3B,CAAC,CACD,OAAOrN,CAAC,EAAE;IACN,OAAO,IAAI;EACf;AACJ;AACA;AACA,SAASiM,wBAAwBA,CAACF,GAAG,EAAE;EACnC,MAAMI,MAAM,GAAGC,mBAAmB,CAACL,GAAG,CAAC;EACvC,MAAMyB,IAAI,GAAGrB,MAAM,CAAC,MAAM,CAAC,GAAGI,kBAAkB,CAACJ,MAAM,CAAC,MAAM,CAAC,CAAC,GAAGzH,SAAS;EAC5E;EACA,MAAM+I,cAAc,GAAGrB,mBAAmB,CAACoB,IAAI,CAAC,CAAC,MAAM,CAAC;EACxD;EACA,MAAME,WAAW,GAAGvB,MAAM,CAAC,cAAc,CAAC,GACpCI,kBAAkB,CAACJ,MAAM,CAAC,cAAc,CAAC,CAAC,GAC1CzH,SAAS;EACf,MAAMiJ,iBAAiB,GAAGvB,mBAAmB,CAACsB,WAAW,CAAC,CAAC,MAAM,CAAC;EAClE,OAAOC,iBAAiB,IAAID,WAAW,IAAID,cAAc,IAAID,IAAI,IAAIzB,GAAG;AAC5E;AACA;AACA;AACA;AACA;AACA,SAASK,mBAAmBA,CAACL,GAAG,EAAE;EAC9B,IAAI,EAAEA,GAAG,KAAK,IAAI,IAAIA,GAAG,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,GAAG,CAACG,QAAQ,CAAC,GAAG,CAAC,CAAC,EAAE;IAChE,OAAO,CAAC,CAAC;EACb;EACA,MAAM,CAACpL,CAAC,EAAE,GAAG8M,IAAI,CAAC,GAAG7B,GAAG,CAACU,KAAK,CAAC,GAAG,CAAC;EACnC,OAAOvJ,iBAAiB,CAAC0K,IAAI,CAACzE,IAAI,CAAC,GAAG,CAAC,CAAC;AAC5C;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM0E,wBAAwB,GAAG,GAAG;AACpC,MAAMC,4BAA4B,CAAC;EAC/B/D,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgE,oBAAoB,GAAGpW,yBAAyB;IACrD,IAAI,CAACqW,sBAAsB,GAAG,IAAI,CAAC,CAAC;IACpC,IAAI,CAACC,aAAa,GAAG,IAAIC,GAAG,CAAC,CAAC;IAC9B,IAAI,CAACC,wBAAwB,GAAG,CAAC,CAAC;IAClC,IAAI,CAACC,mBAAmB,GAAGvW,kBAAkB;IAC7C,IAAI,CAACE,uBAAuB,GAAGA,uBAAuB;EAC1D;EACMsW,WAAWA,CAACvK,IAAI,EAAE;IAAA,IAAAwK,MAAA;IAAA,OAAAzK,iBAAA;MACpB,MAAM0K,GAAG,GAAGzK,IAAI,CAAC0K,IAAI,CAAC,CAAC;MACvB,IAAIC,OAAO,GAAGH,MAAI,CAACL,aAAa,CAACS,GAAG,CAACH,GAAG,CAAC;MACzC,IAAI,CAACE,OAAO,EAAE;QACVA,OAAO,GAAG,IAAI3E,uBAAuB,CAAChG,IAAI,CAAC;QAC3CwK,MAAI,CAACL,aAAa,CAACU,GAAG,CAACJ,GAAG,EAAEE,OAAO,CAAC;QACpCH,MAAI,CAACM,uBAAuB,CAAC9K,IAAI,EAAE2K,OAAO,CAAC;MAC/C;MACA,OAAOA,OAAO;IAAC;EACnB;EACAI,UAAUA,CAAC/K,IAAI,EAAE;IACbvN,KAAK,CAACuN,IAAI,EAAE,6CAA6C,CAAC,2CAA2C,CAAC;EAC1G;EACMgL,aAAaA,CAAChL,IAAI,EAAEE,QAAQ,EAAE+K,QAAQ,EAAEpK,OAAO,EAAE;IAAA,IAAAqK,MAAA;IAAA,OAAAnL,iBAAA;MACnDwD,0BAA0B,CAACvD,IAAI,CAAC;MAChC,MAAM2K,OAAO,SAASO,MAAI,CAACX,WAAW,CAACvK,IAAI,CAAC;MAC5C,MAAM2K,OAAO,CAAC7D,WAAW,CAAC,CAAC;MAC3B;MACA;MACA;MACA6D,OAAO,CAAClE,aAAa,CAAC,CAAC;MACvBtS,sBAAsB,CAAC,CAAC;MACxB,MAAM+W,MAAI,CAACC,iBAAiB,CAACnL,IAAI,CAAC;MAClC,MAAMC,KAAK,GAAG+G,iBAAiB,CAAChH,IAAI,EAAEiL,QAAQ,EAAEpK,OAAO,CAAC;MACxD,MAAMyG,iBAAiB,CAACtH,IAAI,EAAEC,KAAK,CAAC;MACpC,MAAMgI,GAAG,SAASzI,mBAAmB,CAACQ,IAAI,EAAEC,KAAK,EAAEC,QAAQ,CAAC;MAC5D,MAAM2B,MAAM,SAAST,gBAAgB,CAAC6G,GAAG,CAAC;MAC1C,OAAOhG,iBAAiB,CAACjC,IAAI,EAAE2K,OAAO,EAAE9I,MAAM,CAAC;IAAC;EACpD;EACAuJ,4BAA4BA,CAACC,KAAK,EAAEC,GAAG,EAAE;IACrC,MAAM,IAAIC,KAAK,CAAC,yBAAyB,CAAC;EAC9C;EACAJ,iBAAiBA,CAACnL,IAAI,EAAE;IACpB,MAAMyK,GAAG,GAAGzK,IAAI,CAAC0K,IAAI,CAAC,CAAC;IACvB,IAAI,CAAC,IAAI,CAACL,wBAAwB,CAACI,GAAG,CAAC,EAAE;MACrC,IAAI,CAACJ,wBAAwB,CAACI,GAAG,CAAC,GAAG3J,eAAe,CAACd,IAAI,CAAC;IAC9D;IACA,OAAO,IAAI,CAACqK,wBAAwB,CAACI,GAAG,CAAC;EAC7C;EACAK,uBAAuBA,CAAC9K,IAAI,EAAE2K,OAAO,EAAE;IACnC;IACA,MAAM;MAAEzG,cAAc;MAAEsH,aAAa;MAAEpL;IAAU,CAAC,GAAGf,cAAc,CAAC,CAAC;IACrE,MAAMoM,cAAc,GAAG3I,UAAU,cAAA/C,iBAAA,CAAC,aAAY;MAC1C;MACA;MACA,MAAM2H,kBAAkB,CAAC1H,IAAI,CAAC;MAC9B2K,OAAO,CAAC/D,OAAO,CAAC8E,eAAe,CAAC,CAAC,CAAC;IACtC,CAAC,GAAE3B,wBAAwB,CAAC;IAC5B,MAAM4B,gBAAgB;MAAA,IAAAC,KAAA,GAAA7L,iBAAA,CAAG,WAAO8L,SAAS,EAAK;QAC1C;QACAvI,YAAY,CAACmI,cAAc,CAAC;QAC5B,MAAMzD,YAAY,SAASN,kBAAkB,CAAC1H,IAAI,CAAC;QACnD,IAAI8L,UAAU,GAAG,IAAI;QACrB,IAAI9D,YAAY,KAAK6D,SAAS,KAAK,IAAI,IAAIA,SAAS,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,SAAS,CAAC,KAAK,CAAC,CAAC,EAAE;UAC1FC,UAAU,GAAG/D,uBAAuB,CAACC,YAAY,EAAE6D,SAAS,CAAC,KAAK,CAAC,CAAC;QACxE;QACA;QACAlB,OAAO,CAAC/D,OAAO,CAACkF,UAAU,IAAIJ,eAAe,CAAC,CAAC,CAAC;MACpD,CAAC;MAAA,gBAVKC,gBAAgBA,CAAAI,GAAA;QAAA,OAAAH,KAAA,CAAA/L,KAAA,OAAAC,SAAA;MAAA;IAAA,GAUrB;IACD;IACA,IAAI,OAAOoE,cAAc,KAAK,WAAW,IACrC,OAAOA,cAAc,CAACC,SAAS,KAAK,UAAU,EAAE;MAChDD,cAAc,CAACC,SAAS,CAAC,IAAI,EAAEwH,gBAAgB,CAAC;IACpD;IACA;IACA;IACA;IACA;IACA;IACA,MAAMK,qBAAqB,GAAGR,aAAa;IAC3C,MAAMS,aAAa,GAAG,GAAG7L,SAAS,CAACK,WAAW,CAACyL,WAAW,CAAC,CAAC,KAAK;IACjE7M,cAAc,CAAC,CAAC,CAACmM,aAAa;MAAA,IAAAW,KAAA,GAAApM,iBAAA,CAAG,WAAOkI,GAAG,EAAK;QAC5C,IAAIA,GAAG,CAACiE,WAAW,CAAC,CAAC,CAACE,UAAU,CAACH,aAAa,CAAC,EAAE;UAC7C;UACA;UACAN,gBAAgB,CAAC;YAAE1D;UAAI,CAAC,CAAC;QAC7B;QACA;QACA,IAAI,OAAO+D,qBAAqB,KAAK,UAAU,EAAE;UAC7C,IAAI;YACAA,qBAAqB,CAAC/D,GAAG,CAAC;UAC9B,CAAC,CACD,OAAO/L,CAAC,EAAE;YACN;YACAmQ,OAAO,CAAChF,KAAK,CAACnL,CAAC,CAAC;UACpB;QACJ;MACJ,CAAC;MAAA,iBAAAoQ,GAAA;QAAA,OAAAH,KAAA,CAAAtM,KAAA,OAAAC,SAAA;MAAA;IAAA;EACL;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMyM,4BAA4B,GAAGvC,4BAA4B;AACjE,SAAS0B,eAAeA,CAAA,EAAG;EACvB,OAAO;IACH/K,IAAI,EAAE,SAAS,CAAC;IAChBE,OAAO,EAAE,IAAI;IACbR,SAAS,EAAE,IAAI;IACf4G,WAAW,EAAE,IAAI;IACjBE,QAAQ,EAAE,IAAI;IACdC,QAAQ,EAAE,IAAI;IACdC,KAAK,EAAEpU,YAAY,CAAC,eAAe,CAAC,iCAAiC;EACzE,CAAC;AACL;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASuZ,sBAAsBA,CAACxM,IAAI,EAAEyM,SAAS,EAAE;EAC7CpY,SAAS,CAAC2L,IAAI,CAAC,CAAC0M,aAAa,CAACD,SAAS,CAAC;AAC5C;AAEA,SAASD,sBAAsB,EAAED,4BAA4B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}