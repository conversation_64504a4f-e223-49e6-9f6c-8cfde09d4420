<div class="rewards-container">
  <h1>Récompenses</h1>
  
  <div *ngIf="isLoading" class="loading">
    <p>Chargement des récompenses...</p>
  </div>

  <div *ngIf="error" class="error">
    <p>{{ error }}</p>
  </div>

  <div *ngIf="!isLoading && !error">
    <div class="points-summary">
      <div class="points-badge">
        <span class="points-count">{{ getUserPoints() }}</span>
        <span class="points-label">points disponibles</span>
      </div>
    </div>
    
    <div class="success-message" *ngIf="successMessage">
      {{ successMessage }}
    </div>
    
    <div class="error-message" *ngIf="errorMessage">
      {{ errorMessage }}
    </div>
    
    <div class="rewards-section">
      <h2>Récompenses disponibles</h2>
      
      <div *ngIf="rewards.length === 0" class="no-data">
        <p>Aucune récompense disponible pour le moment</p>
      </div>
      
      <div *ngIf="rewards.length > 0" class="rewards-grid">
        <div *ngFor="let reward of rewards" class="reward-card" [class.disabled]="!canRedeemReward(reward)">
          <div class="reward-image">
            <img [src]="reward.imageUrl || 'assets/default-reward.png'" alt="{{ reward.title }}">
          </div>
          <div class="reward-content">
            <h3>{{ reward.title }}</h3>
            <p class="reward-description">{{ reward.description }}</p>
            <p class="reward-partner">Partenaire: {{ reward.partnerName }}</p>
            <div class="reward-cost">
              <span class="cost-value">{{ reward.pointsCost }}</span>
              <span class="cost-label">points</span>
            </div>
          </div>
          <button 
            class="redeem-button" 
            [disabled]="!canRedeemReward(reward) || redeemingRewardId === reward.id" 
            (click)="redeemReward(reward.id)"
          >
            <span *ngIf="redeemingRewardId === reward.id">Échange en cours...</span>
            <span *ngIf="redeemingRewardId !== reward.id">Échanger</span>
          </button>
        </div>
      </div>
    </div>
    
    <div class="redemptions-section" *ngIf="userRedemptions.length > 0">
      <h2>Mes récompenses échangées</h2>
      
      <div class="redemptions-list">
        <div *ngFor="let redemption of userRedemptions" class="redemption-item">
          <div class="redemption-info">
            <h3>{{ redemption.reward.title }}</h3>
            <p class="redemption-date">Échangé le {{ redemption.redeemedAt | date:'dd/MM/yyyy' }}</p>
            <p class="redemption-partner">Partenaire: {{ redemption.reward.partnerName }}</p>
          </div>
          <div class="redemption-status" [ngClass]="{
            'pending': redemption.status === 'pending',
            'completed': redemption.status === 'completed',
            'cancelled': redemption.status === 'cancelled'
          }">
            {{ redemption.status === 'pending' ? 'En attente' : 
               redemption.status === 'completed' ? 'Complété' : 'Annulé' }}
          </div>
        </div>
      </div>
    </div>
  </div>
</div>
