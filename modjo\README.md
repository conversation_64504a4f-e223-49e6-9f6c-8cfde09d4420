# Modjo

Ce projet a été créé manuellement pour simuler un projet Angular.

## Prérequis

Pour utiliser ce projet, vous devez installer :

1. [Node.js et npm](https://nodejs.org/)
2. Angular CLI : `npm install -g @angular/cli`

## Installation

Une fois Node.js et Angular CLI installés, vous pouvez installer les dépendances du projet :

```bash
cd modjo
npm install
```

## Démarrage du serveur de développement

Pour démarrer le serveur de développement, exécutez :

```bash
ng serve
```

Naviguez ensuite vers `http://localhost:4200/`. L'application se rechargera automatiquement si vous modifiez l'un des fichiers source.

## Génération de code

Vous pouvez utiliser Angular CLI pour générer des composants, des services, etc. :

```bash
ng generate component component-name
ng generate service service-name
```

## Construction

Pour construire le projet, exécutez :

```bash
ng build
```

Les artefacts de construction seront stockés dans le répertoire `dist/`.

## Exécution des tests unitaires

Pour exécuter les tests unitaires via [Karma](https://karma-runner.github.io), exécutez :

```bash
ng test
```

## Aide supplémentaire

Pour obtenir plus d'aide sur Angular CLI, utilisez `ng help` ou consultez la [page de documentation Angular CLI](https://angular.io/cli).
