<div class="login-container">
  <mat-card class="login-card">
    <mat-card-header>
      <div class="login-header">
        <img src="assets/logo.png" alt="Modjo" class="logo">
        <h1>Modjo</h1>
        <p>Connectez-vous à votre compte</p>
      </div>
    </mat-card-header>

    <mat-card-content>
      <form [formGroup]="loginForm" (ngSubmit)="onSubmit()" class="login-form">
        <!-- Email field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Adresse email</mat-label>
          <input matInput 
                 type="email" 
                 formControlName="email"
                 placeholder="<EMAIL>">
          <mat-icon matSuffix>email</mat-icon>
          <mat-error>{{ getFieldError('email') }}</mat-error>
        </mat-form-field>

        <!-- Password field -->
        <mat-form-field appearance="outline" class="full-width">
          <mat-label>Mot de passe</mat-label>
          <input matInput 
                 [type]="hidePassword ? 'password' : 'text'"
                 formControlName="password"
                 placeholder="Votre mot de passe">
          <button mat-icon-button 
                  matSuffix 
                  type="button"
                  (click)="hidePassword = !hidePassword"
                  [attr.aria-label]="'Hide password'"
                  [attr.aria-pressed]="hidePassword">
            <mat-icon>{{ hidePassword ? 'visibility_off' : 'visibility' }}</mat-icon>
          </button>
          <mat-error>{{ getFieldError('password') }}</mat-error>
        </mat-form-field>

        <!-- Submit button -->
        <button mat-raised-button 
                color="primary" 
                type="submit"
                class="full-width login-button"
                [disabled]="isLoading">
          <mat-spinner diameter="20" *ngIf="isLoading"></mat-spinner>
          <span *ngIf="!isLoading">Se connecter</span>
        </button>

        <!-- Register link -->
        <div class="register-link">
          <p>Pas encore de compte? 
            <a routerLink="/auth/register" class="link">Créer un compte</a>
          </p>
        </div>
      </form>

      <!-- Demo login buttons -->
      <div class="demo-section">
        <mat-divider></mat-divider>
        <h3>Connexion démo</h3>
        <div class="demo-buttons">
          <button mat-stroked-button 
                  color="primary" 
                  (click)="loginAsUser()"
                  class="demo-button">
            <mat-icon>person</mat-icon>
            Utilisateur
          </button>
          
          <button mat-stroked-button 
                  color="accent" 
                  (click)="loginAsValidator()"
                  class="demo-button">
            <mat-icon>verified</mat-icon>
            Validateur
          </button>
          
          <button mat-stroked-button 
                  color="warn" 
                  (click)="loginAsAdmin()"
                  class="demo-button">
            <mat-icon>admin_panel_settings</mat-icon>
            Admin
          </button>
        </div>
      </div>
    </mat-card-content>
  </mat-card>
</div>
