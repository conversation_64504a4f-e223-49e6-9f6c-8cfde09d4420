{"ast": null, "code": "import { UserRole, ValidationStatus } from '../../../../core/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nfunction ValidatorDashboardComponent_div_0_mat_card_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 16)(1, \"mat-card-content\")(2, \"div\", 17)(3, \"div\", 18)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 19)(7, \"h3\", 20);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 21);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 22);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const stat_r1 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-left-color\", stat_r1.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", stat_r1.color + \"20\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", stat_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(stat_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r1.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.description);\n  }\n}\nfunction ValidatorDashboardComponent_div_0_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.pendingValidations.length);\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"mat-card\", 25)(2, \"mat-card-content\")(3, \"mat-icon\", 26);\n    i0.ɵɵtext(4, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6, \"Aucune validation en attente\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Toutes les actions ont \\u00E9t\\u00E9 trait\\u00E9es. Excellent travail !\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const validation_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(validation_r4.location);\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_button_4_Template_button_click_0_listener() {\n      const evidence_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.viewEvidence(evidence_r6));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"photo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Voir la preuve \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"h5\");\n    i0.ɵɵtext(2, \"Preuves fournies:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 47);\n    i0.ɵɵtemplate(4, ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_button_4_Template, 4, 0, \"button\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const validation_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", validation_r4.evidence);\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 29)(1, \"mat-card-header\")(2, \"div\", 30)(3, \"div\", 31)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 32);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 33)(9, \"span\", 34);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"mat-card-content\")(12, \"div\", 35)(13, \"p\", 36);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 37);\n    i0.ɵɵtemplate(16, ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_16_Template, 5, 1, \"div\", 38);\n    i0.ɵɵelementStart(17, \"div\", 39)(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"timeAgo\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 40)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(28, ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_Template, 5, 1, \"div\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"mat-card-actions\", 42)(30, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template_button_click_30_listener() {\n      const validation_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.validateAction(validation_r4, true));\n    });\n    i0.ɵɵelementStart(31, \"mat-icon\");\n    i0.ɵɵtext(32, \"check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template_button_click_34_listener() {\n      const validation_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.validateAction(validation_r4, false));\n    });\n    i0.ɵɵelementStart(35, \"mat-icon\");\n    i0.ɵɵtext(36, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37, \" Rejeter \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template_button_click_38_listener() {\n      const validation_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewValidationDetails(validation_r4));\n    });\n    i0.ɵɵelementStart(39, \"mat-icon\");\n    i0.ɵɵtext(40, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const validation_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(validation_r4.userName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(validation_r4.userEmail);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getActionTypeDisplayName(validation_r4.actionType));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(validation_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", validation_r4.location);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 9, validation_r4.submittedAt));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", validation_r4.points, \" points\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", validation_r4.evidence && validation_r4.evidence.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" Approuver (+\", validation_r4.points, \" pts) \");\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template, 42, 11, \"mat-card\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.pendingValidations);\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_28_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 57)(5, \"p\", 58)(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 59)(10, \"span\", 60);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"timeAgo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 61);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 62);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const validation_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.getStatusColor(validation_r8.status) + \"20\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getStatusColor(validation_r8.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", validation_r8.status === \"approved\" ? \"check_circle\" : \"cancel\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(validation_r8.userName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", validation_r8.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 12, validation_r8.validatedAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getStatusColor(validation_r8.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusDisplayName(validation_r8.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", validation_r8.points, \" pts\");\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"h2\", 8)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Validations R\\u00E9centes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card\", 51)(6, \"mat-card-content\")(7, \"div\", 52);\n    i0.ɵɵtemplate(8, ValidatorDashboardComponent_div_0_div_28_div_8_Template, 17, 14, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function ValidatorDashboardComponent_div_0_div_28_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"/validator/history\"));\n    });\n    i0.ɵɵtext(10, \" Voir tout l'historique \");\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"arrow_forward\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.recentValidations.slice(0, 5));\n  }\n}\nfunction ValidatorDashboardComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"h1\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 5);\n    i0.ɵɵtext(7, \"\\uD83E\\uDDD1\\u200D\\uD83C\\uDFEB Tableau de bord validateur - Validez les actions communautaires\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 6)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Validateur\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 7)(14, \"h2\", 8)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Statistiques de Validation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 9);\n    i0.ɵɵtemplate(19, ValidatorDashboardComponent_div_0_mat_card_19_Template, 13, 10, \"mat-card\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 11)(21, \"h2\", 8)(22, \"mat-icon\");\n    i0.ɵɵtext(23, \"pending_actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24, \" Actions \\u00E0 Valider \");\n    i0.ɵɵtemplate(25, ValidatorDashboardComponent_div_0_span_25_Template, 2, 1, \"span\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, ValidatorDashboardComponent_div_0_div_26_Template, 9, 0, \"div\", 13)(27, ValidatorDashboardComponent_div_0_div_27_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, ValidatorDashboardComponent_div_0_div_28_Template, 13, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.getGreeting(), \", \", ctx_r1.user.name, \"\");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.quickStats);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pendingValidations.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pendingValidations.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pendingValidations.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.recentValidations.length > 0);\n  }\n}\nexport class ValidatorDashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.user = null;\n    this.validatorStats = null;\n    this.pendingValidations = [];\n    this.recentValidations = [];\n    // Quick stats\n    this.quickStats = [{\n      title: 'Validations en Attente',\n      value: '0',\n      icon: 'pending_actions',\n      color: '#FF9800',\n      description: 'Actions à valider'\n    }, {\n      title: 'Validations Aujourd\\'hui',\n      value: '0',\n      icon: 'today',\n      color: '#4CAF50',\n      description: 'Validées aujourd\\'hui'\n    }, {\n      title: 'Total Validations',\n      value: '0',\n      icon: 'verified',\n      color: '#2196F3',\n      description: 'Toutes validations'\n    }, {\n      title: 'Taux d\\'Approbation',\n      value: '0%',\n      icon: 'thumb_up',\n      color: '#9C27B0',\n      description: 'Pourcentage approuvé'\n    }];\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user && user.role === UserRole.VALIDATOR) {\n        this.loadValidatorData();\n      } else {\n        this.router.navigate(['/dashboard']);\n      }\n    });\n  }\n  loadValidatorData() {\n    this.loadValidatorStats();\n    this.loadPendingValidations();\n    this.loadRecentValidations();\n  }\n  loadValidatorStats() {\n    // Simulate validator stats\n    this.validatorStats = {\n      validatorId: this.user.uid,\n      totalValidations: 156,\n      validationsToday: 8,\n      averageValidationTime: 2.5,\n      approvalRate: 94,\n      lastValidationAt: new Date(Date.now() - 1000 * 60 * 30)\n    };\n    // Update quick stats\n    this.quickStats[0].value = this.pendingValidations.length.toString();\n    this.quickStats[1].value = this.validatorStats.validationsToday.toString();\n    this.quickStats[2].value = this.validatorStats.totalValidations.toString();\n    this.quickStats[3].value = this.validatorStats.approvalRate + '%';\n  }\n  loadPendingValidations() {\n    // Simulate pending validations\n    this.pendingValidations = [{\n      id: '1',\n      userId: 'user1',\n      userName: 'Ahmed Ben Ali',\n      userEmail: '<EMAIL>',\n      actionType: 'LIBRARY_VOLUNTEER',\n      description: 'Aide à la bibliothèque municipale - organisation des livres',\n      location: 'Bibliothèque Municipale Monastir',\n      submittedAt: new Date(Date.now() - 1000 * 60 * 30),\n      points: 10,\n      status: ValidationStatus.PENDING,\n      evidence: ['https://example.com/photo1.jpg']\n    }, {\n      id: '2',\n      userId: 'user2',\n      userName: 'Fatma Trabelsi',\n      userEmail: '<EMAIL>',\n      actionType: 'SCHOOL_HELP',\n      description: 'Aide aux devoirs pour les élèves de primaire',\n      location: 'École Primaire Sousse',\n      submittedAt: new Date(Date.now() - 1000 * 60 * 60),\n      points: 15,\n      status: ValidationStatus.PENDING,\n      evidence: ['https://example.com/photo2.jpg', 'https://example.com/photo3.jpg']\n    }, {\n      id: '3',\n      userId: 'user3',\n      userName: 'Mohamed Gharbi',\n      userEmail: '<EMAIL>',\n      actionType: 'COMMUNITY_SERVICE',\n      description: 'Nettoyage du parc municipal',\n      location: 'Parc Central Monastir',\n      submittedAt: new Date(Date.now() - 1000 * 60 * 90),\n      points: 12,\n      status: ValidationStatus.PENDING\n    }];\n    this.quickStats[0].value = this.pendingValidations.length.toString();\n  }\n  loadRecentValidations() {\n    // Simulate recent validations\n    this.recentValidations = [{\n      id: '4',\n      userId: 'user4',\n      userName: 'Leila Mansouri',\n      userEmail: '<EMAIL>',\n      actionType: 'TUTORING',\n      description: 'Cours de mathématiques pour lycéens',\n      submittedAt: new Date(Date.now() - 1000 * 60 * 60 * 2),\n      validatedAt: new Date(Date.now() - 1000 * 60 * 60),\n      validatedBy: this.user.uid,\n      validatorName: this.user.name,\n      points: 20,\n      status: ValidationStatus.APPROVED\n    }, {\n      id: '5',\n      userId: 'user5',\n      userName: 'Karim Bouazizi',\n      userEmail: '<EMAIL>',\n      actionType: 'ENVIRONMENTAL_ACTION',\n      description: 'Plantation d\\'arbres dans le quartier',\n      submittedAt: new Date(Date.now() - 1000 * 60 * 60 * 3),\n      validatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2),\n      validatedBy: this.user.uid,\n      validatorName: this.user.name,\n      points: 15,\n      status: ValidationStatus.APPROVED\n    }];\n  }\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n  getActionTypeDisplayName(actionType) {\n    const actionNames = {\n      'SCHOOL_HELP': 'Aide scolaire',\n      'LIBRARY_VOLUNTEER': 'Bénévolat bibliothèque',\n      'ASSOCIATION_WORK': 'Travail associatif',\n      'COMMUNITY_SERVICE': 'Service communautaire',\n      'ENVIRONMENTAL_ACTION': 'Action environnementale',\n      'CULTURAL_EVENT': 'Événement culturel',\n      'SPORTS_COACHING': 'Coaching sportif',\n      'TUTORING': 'Tutorat',\n      'ELDERLY_CARE': 'Aide aux personnes âgées',\n      'OTHER': 'Autre'\n    };\n    return actionNames[actionType] || actionType;\n  }\n  getStatusColor(status) {\n    const colors = {\n      [ValidationStatus.PENDING]: '#FF9800',\n      [ValidationStatus.APPROVED]: '#4CAF50',\n      [ValidationStatus.REJECTED]: '#F44336'\n    };\n    return colors[status];\n  }\n  getStatusDisplayName(status) {\n    const statusNames = {\n      [ValidationStatus.PENDING]: 'En attente',\n      [ValidationStatus.APPROVED]: 'Approuvée',\n      [ValidationStatus.REJECTED]: 'Rejetée'\n    };\n    return statusNames[status];\n  }\n  validateAction(validation, approved) {\n    validation.status = approved ? ValidationStatus.APPROVED : ValidationStatus.REJECTED;\n    validation.validatedAt = new Date();\n    validation.validatedBy = this.user.uid;\n    validation.validatorName = this.user.name;\n    // Remove from pending list\n    this.pendingValidations = this.pendingValidations.filter(v => v.id !== validation.id);\n    // Add to recent validations\n    this.recentValidations.unshift(validation);\n    // Update stats\n    this.validatorStats.validationsToday++;\n    this.validatorStats.totalValidations++;\n    this.quickStats[0].value = this.pendingValidations.length.toString();\n    this.quickStats[1].value = this.validatorStats.validationsToday.toString();\n    this.quickStats[2].value = this.validatorStats.totalValidations.toString();\n    console.log(`Validation ${approved ? 'approved' : 'rejected'}:`, validation);\n  }\n  viewValidationDetails(validation) {\n    console.log('View validation details:', validation);\n    // TODO: Open validation details modal\n  }\n  navigateTo(route) {\n    this.router.navigate([route]);\n  }\n  static {\n    this.ɵfac = function ValidatorDashboardComponent_Factory(t) {\n      return new (t || ValidatorDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ValidatorDashboardComponent,\n      selectors: [[\"app-validator-dashboard\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"validator-dashboard-container\", 4, \"ngIf\"], [1, \"validator-dashboard-container\"], [1, \"validator-header\"], [1, \"header-content\"], [1, \"validator-welcome\"], [1, \"validator-subtitle\"], [1, \"validator-badge\"], [1, \"quick-stats-section\"], [1, \"section-title\"], [1, \"stats-grid\"], [\"class\", \"stat-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"pending-section\"], [\"class\", \"pending-count\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"validations-grid\", 4, \"ngIf\"], [\"class\", \"recent-section\", 4, \"ngIf\"], [1, \"stat-card\"], [1, \"stat-header\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"stat-value\"], [1, \"stat-title\"], [1, \"stat-description\"], [1, \"pending-count\"], [1, \"empty-state\"], [1, \"empty-card\"], [1, \"empty-icon\"], [1, \"validations-grid\"], [\"class\", \"validation-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"validation-card\"], [1, \"validation-header\"], [1, \"user-info\"], [1, \"user-email\"], [1, \"action-type\"], [1, \"type-badge\"], [1, \"validation-details\"], [1, \"description\"], [1, \"validation-meta\"], [\"class\", \"meta-item\", 4, \"ngIf\"], [1, \"meta-item\"], [1, \"meta-item\", \"points\"], [\"class\", \"evidence-section\", 4, \"ngIf\"], [1, \"validation-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"approve-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 1, \"reject-btn\", 3, \"click\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"evidence-section\"], [1, \"evidence-list\"], [\"mat-stroked-button\", \"\", \"class\", \"evidence-btn\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", 1, \"evidence-btn\", 3, \"click\"], [1, \"recent-section\"], [1, \"recent-card\"], [1, \"recent-list\"], [\"class\", \"recent-item\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", 1, \"view-all-btn\", 3, \"click\"], [1, \"recent-item\"], [1, \"recent-icon\"], [1, \"recent-content\"], [1, \"recent-description\"], [1, \"recent-meta\"], [1, \"recent-time\"], [1, \"recent-status\"], [1, \"recent-points\"]],\n      template: function ValidatorDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ValidatorDashboardComponent_div_0_Template, 29, 7, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n        }\n      },\n      styles: [\"\\n (()[_ngcontent-%COMP%]   =[_ngcontent-%COMP%] >  { // webpackBootstrap\\n\\n \\t\\\"use strict\\\";\\n\\n \\t\\n\\n \\t\\n\\n })()[_ngcontent-%COMP%]\\n;\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["UserRole", "ValidationStatus", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵstyleProp", "stat_r1", "color", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "value", "title", "description", "ctx_r1", "pendingValidations", "length", "validation_r4", "location", "ɵɵlistener", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_button_4_Template_button_click_0_listener", "evidence_r6", "ɵɵrestoreView", "_r5", "$implicit", "ɵɵnextContext", "ɵɵresetView", "viewEvidence", "ɵɵtemplate", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_button_4_Template", "ɵɵproperty", "evidence", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_16_Template", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_Template", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template_button_click_30_listener", "_r3", "validateAction", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template_button_click_34_listener", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template_button_click_38_listener", "viewValidationDetails", "userName", "userEmail", "getActionTypeDisplayName", "actionType", "ɵɵpipeBind1", "submittedAt", "ɵɵtextInterpolate1", "points", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template", "getStatusColor", "validation_r8", "status", "validatedAt", "getStatusDisplayName", "ValidatorDashboardComponent_div_0_div_28_div_8_Template", "ValidatorDashboardComponent_div_0_div_28_Template_button_click_9_listener", "_r7", "navigateTo", "recentValidations", "slice", "ValidatorDashboardComponent_div_0_mat_card_19_Template", "ValidatorDashboardComponent_div_0_span_25_Template", "ValidatorDashboardComponent_div_0_div_26_Template", "ValidatorDashboardComponent_div_0_div_27_Template", "ValidatorDashboardComponent_div_0_div_28_Template", "ɵɵtextInterpolate2", "getGreeting", "user", "name", "quickStats", "ValidatorDashboardComponent", "constructor", "authService", "router", "validatorStats", "ngOnInit", "currentUser$", "subscribe", "role", "VALIDATOR", "loadValidatorData", "navigate", "loadValidatorStats", "loadPendingValidations", "loadRecentValidations", "validatorId", "uid", "totalValidations", "validationsToday", "averageValidationTime", "approvalRate", "lastValidationAt", "Date", "now", "toString", "id", "userId", "PENDING", "validatedBy", "validatorName", "APPROVED", "hour", "getHours", "actionNames", "colors", "REJECTED", "statusNames", "validation", "approved", "filter", "v", "unshift", "console", "log", "route", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "ValidatorDashboardComponent_Template", "rf", "ctx", "ValidatorDashboardComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\validator-dashboard\\components\\validator-dashboard\\validator-dashboard.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\validator-dashboard\\components\\validator-dashboard\\validator-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { User, UserRole, ValidationAction, ValidationStatus, ValidatorStats } from '../../../../core/models';\n\n@Component({\n  selector: 'app-validator-dashboard',\n  templateUrl: './validator-dashboard.component.html',\n  styleUrls: ['./validator-dashboard.component.css']\n})\nexport class ValidatorDashboardComponent implements OnInit {\n  user: User | null = null;\n  validatorStats: ValidatorStats | null = null;\n  pendingValidations: ValidationAction[] = [];\n  recentValidations: ValidationAction[] = [];\n\n  // Quick stats\n  quickStats = [\n    {\n      title: 'Validations en Attente',\n      value: '0',\n      icon: 'pending_actions',\n      color: '#FF9800',\n      description: 'Actions à valider'\n    },\n    {\n      title: 'Validations Aujourd\\'hui',\n      value: '0',\n      icon: 'today',\n      color: '#4CAF50',\n      description: 'Validées aujourd\\'hui'\n    },\n    {\n      title: 'Total Validations',\n      value: '0',\n      icon: 'verified',\n      color: '#2196F3',\n      description: 'Toutes validations'\n    },\n    {\n      title: 'Taux d\\'Approbation',\n      value: '0%',\n      icon: 'thumb_up',\n      color: '#9C27B0',\n      description: 'Pourcentage approuvé'\n    }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user && user.role === UserRole.VALIDATOR) {\n        this.loadValidatorData();\n      } else {\n        this.router.navigate(['/dashboard']);\n      }\n    });\n  }\n\n  private loadValidatorData(): void {\n    this.loadValidatorStats();\n    this.loadPendingValidations();\n    this.loadRecentValidations();\n  }\n\n  private loadValidatorStats(): void {\n    // Simulate validator stats\n    this.validatorStats = {\n      validatorId: this.user!.uid,\n      totalValidations: 156,\n      validationsToday: 8,\n      averageValidationTime: 2.5,\n      approvalRate: 94,\n      lastValidationAt: new Date(Date.now() - 1000 * 60 * 30)\n    };\n\n    // Update quick stats\n    this.quickStats[0].value = this.pendingValidations.length.toString();\n    this.quickStats[1].value = this.validatorStats.validationsToday.toString();\n    this.quickStats[2].value = this.validatorStats.totalValidations.toString();\n    this.quickStats[3].value = this.validatorStats.approvalRate + '%';\n  }\n\n  private loadPendingValidations(): void {\n    // Simulate pending validations\n    this.pendingValidations = [\n      {\n        id: '1',\n        userId: 'user1',\n        userName: 'Ahmed Ben Ali',\n        userEmail: '<EMAIL>',\n        actionType: 'LIBRARY_VOLUNTEER' as any,\n        description: 'Aide à la bibliothèque municipale - organisation des livres',\n        location: 'Bibliothèque Municipale Monastir',\n        submittedAt: new Date(Date.now() - 1000 * 60 * 30),\n        points: 10,\n        status: ValidationStatus.PENDING,\n        evidence: ['https://example.com/photo1.jpg']\n      },\n      {\n        id: '2',\n        userId: 'user2',\n        userName: 'Fatma Trabelsi',\n        userEmail: '<EMAIL>',\n        actionType: 'SCHOOL_HELP' as any,\n        description: 'Aide aux devoirs pour les élèves de primaire',\n        location: 'École Primaire Sousse',\n        submittedAt: new Date(Date.now() - 1000 * 60 * 60),\n        points: 15,\n        status: ValidationStatus.PENDING,\n        evidence: ['https://example.com/photo2.jpg', 'https://example.com/photo3.jpg']\n      },\n      {\n        id: '3',\n        userId: 'user3',\n        userName: 'Mohamed Gharbi',\n        userEmail: '<EMAIL>',\n        actionType: 'COMMUNITY_SERVICE' as any,\n        description: 'Nettoyage du parc municipal',\n        location: 'Parc Central Monastir',\n        submittedAt: new Date(Date.now() - 1000 * 60 * 90),\n        points: 12,\n        status: ValidationStatus.PENDING\n      }\n    ];\n\n    this.quickStats[0].value = this.pendingValidations.length.toString();\n  }\n\n  private loadRecentValidations(): void {\n    // Simulate recent validations\n    this.recentValidations = [\n      {\n        id: '4',\n        userId: 'user4',\n        userName: 'Leila Mansouri',\n        userEmail: '<EMAIL>',\n        actionType: 'TUTORING' as any,\n        description: 'Cours de mathématiques pour lycéens',\n        submittedAt: new Date(Date.now() - 1000 * 60 * 60 * 2),\n        validatedAt: new Date(Date.now() - 1000 * 60 * 60),\n        validatedBy: this.user!.uid,\n        validatorName: this.user!.name,\n        points: 20,\n        status: ValidationStatus.APPROVED\n      },\n      {\n        id: '5',\n        userId: 'user5',\n        userName: 'Karim Bouazizi',\n        userEmail: '<EMAIL>',\n        actionType: 'ENVIRONMENTAL_ACTION' as any,\n        description: 'Plantation d\\'arbres dans le quartier',\n        submittedAt: new Date(Date.now() - 1000 * 60 * 60 * 3),\n        validatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2),\n        validatedBy: this.user!.uid,\n        validatorName: this.user!.name,\n        points: 15,\n        status: ValidationStatus.APPROVED\n      }\n    ];\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n\n  getActionTypeDisplayName(actionType: string): string {\n    const actionNames: { [key: string]: string } = {\n      'SCHOOL_HELP': 'Aide scolaire',\n      'LIBRARY_VOLUNTEER': 'Bénévolat bibliothèque',\n      'ASSOCIATION_WORK': 'Travail associatif',\n      'COMMUNITY_SERVICE': 'Service communautaire',\n      'ENVIRONMENTAL_ACTION': 'Action environnementale',\n      'CULTURAL_EVENT': 'Événement culturel',\n      'SPORTS_COACHING': 'Coaching sportif',\n      'TUTORING': 'Tutorat',\n      'ELDERLY_CARE': 'Aide aux personnes âgées',\n      'OTHER': 'Autre'\n    };\n    return actionNames[actionType] || actionType;\n  }\n\n  getStatusColor(status: ValidationStatus): string {\n    const colors = {\n      [ValidationStatus.PENDING]: '#FF9800',\n      [ValidationStatus.APPROVED]: '#4CAF50',\n      [ValidationStatus.REJECTED]: '#F44336'\n    };\n    return colors[status];\n  }\n\n  getStatusDisplayName(status: ValidationStatus): string {\n    const statusNames = {\n      [ValidationStatus.PENDING]: 'En attente',\n      [ValidationStatus.APPROVED]: 'Approuvée',\n      [ValidationStatus.REJECTED]: 'Rejetée'\n    };\n    return statusNames[status];\n  }\n\n  validateAction(validation: ValidationAction, approved: boolean): void {\n    validation.status = approved ? ValidationStatus.APPROVED : ValidationStatus.REJECTED;\n    validation.validatedAt = new Date();\n    validation.validatedBy = this.user!.uid;\n    validation.validatorName = this.user!.name;\n\n    // Remove from pending list\n    this.pendingValidations = this.pendingValidations.filter(v => v.id !== validation.id);\n    \n    // Add to recent validations\n    this.recentValidations.unshift(validation);\n    \n    // Update stats\n    this.validatorStats!.validationsToday++;\n    this.validatorStats!.totalValidations++;\n    this.quickStats[0].value = this.pendingValidations.length.toString();\n    this.quickStats[1].value = this.validatorStats!.validationsToday.toString();\n    this.quickStats[2].value = this.validatorStats!.totalValidations.toString();\n\n    console.log(`Validation ${approved ? 'approved' : 'rejected'}:`, validation);\n  }\n\n  viewValidationDetails(validation: ValidationAction): void {\n    console.log('View validation details:', validation);\n    // TODO: Open validation details modal\n  }\n\n  navigateTo(route: string): void {\n    this.router.navigate([route]);\n  }\n}\n", "<div class=\"validator-dashboard-container\" *ngIf=\"user\">\n  <!-- Header Section -->\n  <div class=\"validator-header\">\n    <div class=\"header-content\">\n      <div class=\"validator-welcome\">\n        <h1>{{ getGreeting() }}, {{ user.name }}</h1>\n        <p class=\"validator-subtitle\">🧑‍🏫 Tableau de bord validateur - Validez les actions communautaires</p>\n      </div>\n      <div class=\"validator-badge\">\n        <mat-icon>verified_user</mat-icon>\n        <span>Validateur</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Quick Stats -->\n  <div class=\"quick-stats-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>analytics</mat-icon>\n      Statistiques de Validation\n    </h2>\n    <div class=\"stats-grid\">\n      <mat-card *ngFor=\"let stat of quickStats\" class=\"stat-card\" [style.border-left-color]=\"stat.color\">\n        <mat-card-content>\n          <div class=\"stat-header\">\n            <div class=\"stat-icon\" [style.background-color]=\"stat.color + '20'\">\n              <mat-icon [style.color]=\"stat.color\">{{ stat.icon }}</mat-icon>\n            </div>\n          </div>\n          <div class=\"stat-content\">\n            <h3 class=\"stat-value\">{{ stat.value }}</h3>\n            <p class=\"stat-title\">{{ stat.title }}</p>\n            <span class=\"stat-description\">{{ stat.description }}</span>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Pending Validations -->\n  <div class=\"pending-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>pending_actions</mat-icon>\n      Actions à Valider\n      <span class=\"pending-count\" *ngIf=\"pendingValidations.length > 0\">{{ pendingValidations.length }}</span>\n    </h2>\n    \n    <div *ngIf=\"pendingValidations.length === 0\" class=\"empty-state\">\n      <mat-card class=\"empty-card\">\n        <mat-card-content>\n          <mat-icon class=\"empty-icon\">check_circle</mat-icon>\n          <h3>Aucune validation en attente</h3>\n          <p>Toutes les actions ont été traitées. Excellent travail !</p>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <div class=\"validations-grid\" *ngIf=\"pendingValidations.length > 0\">\n      <mat-card *ngFor=\"let validation of pendingValidations\" class=\"validation-card\">\n        <mat-card-header>\n          <div class=\"validation-header\">\n            <div class=\"user-info\">\n              <h4>{{ validation.userName }}</h4>\n              <span class=\"user-email\">{{ validation.userEmail }}</span>\n            </div>\n            <div class=\"action-type\">\n              <span class=\"type-badge\">{{ getActionTypeDisplayName(validation.actionType) }}</span>\n            </div>\n          </div>\n        </mat-card-header>\n        \n        <mat-card-content>\n          <div class=\"validation-details\">\n            <p class=\"description\">{{ validation.description }}</p>\n            <div class=\"validation-meta\">\n              <div class=\"meta-item\" *ngIf=\"validation.location\">\n                <mat-icon>location_on</mat-icon>\n                <span>{{ validation.location }}</span>\n              </div>\n              <div class=\"meta-item\">\n                <mat-icon>schedule</mat-icon>\n                <span>{{ validation.submittedAt | timeAgo }}</span>\n              </div>\n              <div class=\"meta-item points\">\n                <mat-icon>stars</mat-icon>\n                <span>{{ validation.points }} points</span>\n              </div>\n            </div>\n            \n            <div class=\"evidence-section\" *ngIf=\"validation.evidence && validation.evidence.length > 0\">\n              <h5>Preuves fournies:</h5>\n              <div class=\"evidence-list\">\n                <button *ngFor=\"let evidence of validation.evidence\" \n                        mat-stroked-button \n                        class=\"evidence-btn\"\n                        (click)=\"viewEvidence(evidence)\">\n                  <mat-icon>photo</mat-icon>\n                  Voir la preuve\n                </button>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n        \n        <mat-card-actions class=\"validation-actions\">\n          <button mat-raised-button \n                  color=\"primary\" \n                  (click)=\"validateAction(validation, true)\"\n                  class=\"approve-btn\">\n            <mat-icon>check</mat-icon>\n            Approuver (+{{ validation.points }} pts)\n          </button>\n          <button mat-raised-button \n                  color=\"warn\" \n                  (click)=\"validateAction(validation, false)\"\n                  class=\"reject-btn\">\n            <mat-icon>close</mat-icon>\n            Rejeter\n          </button>\n          <button mat-button (click)=\"viewValidationDetails(validation)\">\n            <mat-icon>info</mat-icon>\n            Détails\n          </button>\n        </mat-card-actions>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Recent Validations -->\n  <div class=\"recent-section\" *ngIf=\"recentValidations.length > 0\">\n    <h2 class=\"section-title\">\n      <mat-icon>history</mat-icon>\n      Validations Récentes\n    </h2>\n    <mat-card class=\"recent-card\">\n      <mat-card-content>\n        <div class=\"recent-list\">\n          <div *ngFor=\"let validation of recentValidations.slice(0, 5)\" class=\"recent-item\">\n            <div class=\"recent-icon\" [style.background-color]=\"getStatusColor(validation.status) + '20'\">\n              <mat-icon [style.color]=\"getStatusColor(validation.status)\">\n                {{ validation.status === 'approved' ? 'check_circle' : 'cancel' }}\n              </mat-icon>\n            </div>\n            <div class=\"recent-content\">\n              <p class=\"recent-description\">\n                <strong>{{ validation.userName }}</strong> - {{ validation.description }}\n              </p>\n              <div class=\"recent-meta\">\n                <span class=\"recent-time\">{{ validation.validatedAt | timeAgo }}</span>\n                <span class=\"recent-status\" [style.color]=\"getStatusColor(validation.status)\">\n                  {{ getStatusDisplayName(validation.status) }}\n                </span>\n                <span class=\"recent-points\">{{ validation.points }} pts</span>\n              </div>\n            </div>\n          </div>\n        </div>\n        <button mat-stroked-button class=\"view-all-btn\" (click)=\"navigateTo('/validator/history')\">\n          Voir tout l'historique\n          <mat-icon>arrow_forward</mat-icon>\n        </button>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AAGA,SAAeA,QAAQ,EAAoBC,gBAAgB,QAAwB,yBAAyB;;;;;;ICuB9FC,EAJR,CAAAC,cAAA,mBAAmG,uBAC/E,cACS,cAC6C,eAC7B;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAExDF,EAFwD,CAAAG,YAAA,EAAW,EAC3D,EACF;IAEJH,EADF,CAAAC,cAAA,cAA0B,aACD;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC1CH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAG3DF,EAH2D,CAAAG,YAAA,EAAO,EACxD,EACW,EACV;;;;IAbiDH,EAAA,CAAAI,WAAA,sBAAAC,OAAA,CAAAC,KAAA,CAAsC;IAGrEN,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,WAAA,qBAAAC,OAAA,CAAAC,KAAA,QAA4C;IACvDN,EAAA,CAAAO,SAAA,EAA0B;IAA1BP,EAAA,CAAAI,WAAA,UAAAC,OAAA,CAAAC,KAAA,CAA0B;IAACN,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAI,IAAA,CAAe;IAI/BT,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAK,KAAA,CAAgB;IACjBV,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAM,KAAA,CAAgB;IACPX,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAO,WAAA,CAAsB;;;;;IAY3DZ,EAAA,CAAAC,cAAA,eAAkE;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtCH,EAAA,CAAAO,SAAA,EAA+B;IAA/BP,EAAA,CAAAQ,iBAAA,CAAAK,MAAA,CAAAC,kBAAA,CAAAC,MAAA,CAA+B;;;;;IAM7Ff,EAHN,CAAAC,cAAA,cAAiE,mBAClC,uBACT,mBACa;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8EAAwD;IAGjEF,EAHiE,CAAAG,YAAA,EAAI,EAC9C,EACV,EACP;;;;;IAqBMH,EADF,CAAAC,cAAA,cAAmD,eACvC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IACjCF,EADiC,CAAAG,YAAA,EAAO,EAClC;;;;IADEH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,iBAAA,CAAAQ,aAAA,CAAAC,QAAA,CAAyB;;;;;;IAe/BjB,EAAA,CAAAC,cAAA,iBAGyC;IAAjCD,EAAA,CAAAkB,UAAA,mBAAAC,qGAAA;MAAA,MAAAC,WAAA,GAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASZ,MAAA,CAAAa,YAAA,CAAAN,WAAA,CAAsB;IAAA,EAAC;IACtCpB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IARXH,EADF,CAAAC,cAAA,cAA4F,SACtF;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAA2B,UAAA,IAAAC,4EAAA,qBAGyC;IAK7C5B,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAR2BH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAA6B,UAAA,YAAAb,aAAA,CAAAc,QAAA,CAAsB;;;;;;IA9BrD9B,EAJR,CAAAC,cAAA,mBAAgF,sBAC7D,cACgB,cACN,SACjB;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IACrDF,EADqD,CAAAG,YAAA,EAAO,EACtD;IAEJH,EADF,CAAAC,cAAA,cAAyB,eACE;IAAAD,EAAA,CAAAE,MAAA,IAAqD;IAGpFF,EAHoF,CAAAG,YAAA,EAAO,EACjF,EACF,EACU;IAIdH,EAFJ,CAAAC,cAAA,wBAAkB,eACgB,aACP;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvDH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAA2B,UAAA,KAAAI,mEAAA,kBAAmD;IAKjD/B,EADF,CAAAC,cAAA,eAAuB,gBACX;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAsC;;IAC9CF,EAD8C,CAAAG,YAAA,EAAO,EAC/C;IAEJH,EADF,CAAAC,cAAA,eAA8B,gBAClB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA8B;IAExCF,EAFwC,CAAAG,YAAA,EAAO,EACvC,EACF;IAENH,EAAA,CAAA2B,UAAA,KAAAK,mEAAA,kBAA4F;IAahGhC,EADE,CAAAG,YAAA,EAAM,EACW;IAGjBH,EADF,CAAAC,cAAA,4BAA6C,kBAIf;IADpBD,EAAA,CAAAkB,UAAA,mBAAAe,sFAAA;MAAA,MAAAjB,aAAA,GAAAhB,EAAA,CAAAqB,aAAA,CAAAa,GAAA,EAAAX,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASZ,MAAA,CAAAsB,cAAA,CAAAnB,aAAA,EAA2B,IAAI,CAAC;IAAA,EAAC;IAEhDhB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAG2B;IADnBD,EAAA,CAAAkB,UAAA,mBAAAkB,sFAAA;MAAA,MAAApB,aAAA,GAAAhB,EAAA,CAAAqB,aAAA,CAAAa,GAAA,EAAAX,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASZ,MAAA,CAAAsB,cAAA,CAAAnB,aAAA,EAA2B,KAAK,CAAC;IAAA,EAAC;IAEjDhB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA+D;IAA5CD,EAAA,CAAAkB,UAAA,mBAAAmB,sFAAA;MAAA,MAAArB,aAAA,GAAAhB,EAAA,CAAAqB,aAAA,CAAAa,GAAA,EAAAX,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASZ,MAAA,CAAAyB,qBAAA,CAAAtB,aAAA,CAAiC;IAAA,EAAC;IAC5DhB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,sBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;;;;;IA9DCH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,iBAAA,CAAAQ,aAAA,CAAAuB,QAAA,CAAyB;IACJvC,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAQ,iBAAA,CAAAQ,aAAA,CAAAwB,SAAA,CAA0B;IAG1BxC,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAQ,iBAAA,CAAAK,MAAA,CAAA4B,wBAAA,CAAAzB,aAAA,CAAA0B,UAAA,EAAqD;IAOzD1C,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAQ,iBAAA,CAAAQ,aAAA,CAAAJ,WAAA,CAA4B;IAEzBZ,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAA6B,UAAA,SAAAb,aAAA,CAAAC,QAAA,CAAyB;IAMzCjB,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAA2C,WAAA,QAAA3B,aAAA,CAAA4B,WAAA,EAAsC;IAItC5C,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAA6C,kBAAA,KAAA7B,aAAA,CAAA8B,MAAA,YAA8B;IAIT9C,EAAA,CAAAO,SAAA,EAA2D;IAA3DP,EAAA,CAAA6B,UAAA,SAAAb,aAAA,CAAAc,QAAA,IAAAd,aAAA,CAAAc,QAAA,CAAAf,MAAA,KAA2D;IAqB1Ff,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA6C,kBAAA,kBAAA7B,aAAA,CAAA8B,MAAA,WACF;;;;;IAtDN9C,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAA2B,UAAA,IAAAoB,4DAAA,yBAAgF;IAmElF/C,EAAA,CAAAG,YAAA,EAAM;;;;IAnE6BH,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAA6B,UAAA,YAAAhB,MAAA,CAAAC,kBAAA,CAAqB;;;;;IAiF9Cd,EAFJ,CAAAC,cAAA,cAAkF,cACa,eAC/B;IAC1DD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAGFH,EAFJ,CAAAC,cAAA,cAA4B,YACI,aACpB;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAC7C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,cAAyB,gBACG;IAAAD,EAAA,CAAAE,MAAA,IAAsC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvEH,EAAA,CAAAC,cAAA,gBAA8E;IAC5ED,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAG7DF,EAH6D,CAAAG,YAAA,EAAO,EAC1D,EACF,EACF;;;;;IAjBqBH,EAAA,CAAAO,SAAA,EAAmE;IAAnEP,EAAA,CAAAI,WAAA,qBAAAS,MAAA,CAAAmC,cAAA,CAAAC,aAAA,CAAAC,MAAA,SAAmE;IAChFlD,EAAA,CAAAO,SAAA,EAAiD;IAAjDP,EAAA,CAAAI,WAAA,UAAAS,MAAA,CAAAmC,cAAA,CAAAC,aAAA,CAAAC,MAAA,EAAiD;IACzDlD,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAA6C,kBAAA,MAAAI,aAAA,CAAAC,MAAA,iDACF;IAIUlD,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,iBAAA,CAAAyC,aAAA,CAAAV,QAAA,CAAyB;IAAUvC,EAAA,CAAAO,SAAA,EAC7C;IAD6CP,EAAA,CAAA6C,kBAAA,QAAAI,aAAA,CAAArC,WAAA,MAC7C;IAE4BZ,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAA2C,WAAA,SAAAM,aAAA,CAAAE,WAAA,EAAsC;IACpCnD,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAAI,WAAA,UAAAS,MAAA,CAAAmC,cAAA,CAAAC,aAAA,CAAAC,MAAA,EAAiD;IAC3ElD,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAA6C,kBAAA,MAAAhC,MAAA,CAAAuC,oBAAA,CAAAH,aAAA,CAAAC,MAAA,OACF;IAC4BlD,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAA6C,kBAAA,KAAAI,aAAA,CAAAH,MAAA,SAA2B;;;;;;IArBjE9C,EAFJ,CAAAC,cAAA,cAAiE,YACrC,eACd;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGDH,EAFJ,CAAAC,cAAA,mBAA8B,uBACV,cACS;IACvBD,EAAA,CAAA2B,UAAA,IAAA0B,uDAAA,oBAAkF;IAmBpFrD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAA2F;IAA3CD,EAAA,CAAAkB,UAAA,mBAAAoC,0EAAA;MAAAtD,EAAA,CAAAqB,aAAA,CAAAkC,GAAA;MAAA,MAAA1C,MAAA,GAAAb,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASZ,MAAA,CAAA2C,UAAA,CAAW,oBAAoB,CAAC;IAAA,EAAC;IACxFxD,EAAA,CAAAE,MAAA,gCACA;IAAAF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAI/BF,EAJ+B,CAAAG,YAAA,EAAW,EAC3B,EACQ,EACV,EACP;;;;IA1B8BH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAA6B,UAAA,YAAAhB,MAAA,CAAA4C,iBAAA,CAAAC,KAAA,OAAgC;;;;;IApI9D1D,EALR,CAAAC,cAAA,aAAwD,aAExB,aACA,aACK,SACzB;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7CH,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAE,MAAA,qGAAqE;IACrGF,EADqG,CAAAG,YAAA,EAAI,EACnG;IAEJH,EADF,CAAAC,cAAA,aAA6B,eACjB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAGtBF,EAHsB,CAAAG,YAAA,EAAO,EACnB,EACF,EACF;IAKFH,EAFJ,CAAAC,cAAA,cAAiC,aACL,gBACd;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAA2B,UAAA,KAAAgC,sDAAA,yBAAmG;IAevG3D,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAA6B,aACD,gBACd;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAE,MAAA,gCACA;IAAAF,EAAA,CAAA2B,UAAA,KAAAiC,kDAAA,mBAAkE;IACpE5D,EAAA,CAAAG,YAAA,EAAK;IAYLH,EAVA,CAAA2B,UAAA,KAAAkC,iDAAA,kBAAiE,KAAAC,iDAAA,kBAUG;IAqEtE9D,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA2B,UAAA,KAAAoC,iDAAA,mBAAiE;IAmCnE/D,EAAA,CAAAG,YAAA,EAAM;;;;IA/JMH,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAgE,kBAAA,KAAAnD,MAAA,CAAAoD,WAAA,UAAApD,MAAA,CAAAqD,IAAA,CAAAC,IAAA,KAAoC;IAiBfnE,EAAA,CAAAO,SAAA,IAAa;IAAbP,EAAA,CAAA6B,UAAA,YAAAhB,MAAA,CAAAuD,UAAA,CAAa;IAsBXpE,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAA6B,UAAA,SAAAhB,MAAA,CAAAC,kBAAA,CAAAC,MAAA,KAAmC;IAG5Df,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAA6B,UAAA,SAAAhB,MAAA,CAAAC,kBAAA,CAAAC,MAAA,OAAqC;IAUZf,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAA6B,UAAA,SAAAhB,MAAA,CAAAC,kBAAA,CAAAC,MAAA,KAAmC;IAwEvCf,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAA6B,UAAA,SAAAhB,MAAA,CAAA4C,iBAAA,CAAA1C,MAAA,KAAkC;;;ADvHjE,OAAM,MAAOsD,2BAA2B;EAsCtCC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAvChB,KAAAN,IAAI,GAAgB,IAAI;IACxB,KAAAO,cAAc,GAA0B,IAAI;IAC5C,KAAA3D,kBAAkB,GAAuB,EAAE;IAC3C,KAAA2C,iBAAiB,GAAuB,EAAE;IAE1C;IACA,KAAAW,UAAU,GAAG,CACX;MACEzD,KAAK,EAAE,wBAAwB;MAC/BD,KAAK,EAAE,GAAG;MACVD,IAAI,EAAE,iBAAiB;MACvBH,KAAK,EAAE,SAAS;MAChBM,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,0BAA0B;MACjCD,KAAK,EAAE,GAAG;MACVD,IAAI,EAAE,OAAO;MACbH,KAAK,EAAE,SAAS;MAChBM,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,mBAAmB;MAC1BD,KAAK,EAAE,GAAG;MACVD,IAAI,EAAE,UAAU;MAChBH,KAAK,EAAE,SAAS;MAChBM,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,qBAAqB;MAC5BD,KAAK,EAAE,IAAI;MACXD,IAAI,EAAE,UAAU;MAChBH,KAAK,EAAE,SAAS;MAChBM,WAAW,EAAE;KACd,CACF;EAKE;EAEH8D,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,CAACI,YAAY,CAACC,SAAS,CAACV,IAAI,IAAG;MAC7C,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAIA,IAAI,IAAIA,IAAI,CAACW,IAAI,KAAK/E,QAAQ,CAACgF,SAAS,EAAE;QAC5C,IAAI,CAACC,iBAAiB,EAAE;OACzB,MAAM;QACL,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;IAExC,CAAC,CAAC;EACJ;EAEQD,iBAAiBA,CAAA;IACvB,IAAI,CAACE,kBAAkB,EAAE;IACzB,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEQF,kBAAkBA,CAAA;IACxB;IACA,IAAI,CAACR,cAAc,GAAG;MACpBW,WAAW,EAAE,IAAI,CAAClB,IAAK,CAACmB,GAAG;MAC3BC,gBAAgB,EAAE,GAAG;MACrBC,gBAAgB,EAAE,CAAC;MACnBC,qBAAqB,EAAE,GAAG;MAC1BC,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;KACvD;IAED;IACA,IAAI,CAACxB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAACI,kBAAkB,CAACC,MAAM,CAAC8E,QAAQ,EAAE;IACpE,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAAC+D,cAAc,CAACc,gBAAgB,CAACM,QAAQ,EAAE;IAC1E,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAAC+D,cAAc,CAACa,gBAAgB,CAACO,QAAQ,EAAE;IAC1E,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAAC+D,cAAc,CAACgB,YAAY,GAAG,GAAG;EACnE;EAEQP,sBAAsBA,CAAA;IAC5B;IACA,IAAI,CAACpE,kBAAkB,GAAG,CACxB;MACEgF,EAAE,EAAE,GAAG;MACPC,MAAM,EAAE,OAAO;MACfxD,QAAQ,EAAE,eAAe;MACzBC,SAAS,EAAE,iBAAiB;MAC5BE,UAAU,EAAE,mBAA0B;MACtC9B,WAAW,EAAE,6DAA6D;MAC1EK,QAAQ,EAAE,kCAAkC;MAC5C2B,WAAW,EAAE,IAAI+C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAClD9C,MAAM,EAAE,EAAE;MACVI,MAAM,EAAEnD,gBAAgB,CAACiG,OAAO;MAChClE,QAAQ,EAAE,CAAC,gCAAgC;KAC5C,EACD;MACEgE,EAAE,EAAE,GAAG;MACPC,MAAM,EAAE,OAAO;MACfxD,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,iBAAiB;MAC5BE,UAAU,EAAE,aAAoB;MAChC9B,WAAW,EAAE,8CAA8C;MAC3DK,QAAQ,EAAE,uBAAuB;MACjC2B,WAAW,EAAE,IAAI+C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAClD9C,MAAM,EAAE,EAAE;MACVI,MAAM,EAAEnD,gBAAgB,CAACiG,OAAO;MAChClE,QAAQ,EAAE,CAAC,gCAAgC,EAAE,gCAAgC;KAC9E,EACD;MACEgE,EAAE,EAAE,GAAG;MACPC,MAAM,EAAE,OAAO;MACfxD,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,mBAAmB;MAC9BE,UAAU,EAAE,mBAA0B;MACtC9B,WAAW,EAAE,6BAA6B;MAC1CK,QAAQ,EAAE,uBAAuB;MACjC2B,WAAW,EAAE,IAAI+C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAClD9C,MAAM,EAAE,EAAE;MACVI,MAAM,EAAEnD,gBAAgB,CAACiG;KAC1B,CACF;IAED,IAAI,CAAC5B,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAACI,kBAAkB,CAACC,MAAM,CAAC8E,QAAQ,EAAE;EACtE;EAEQV,qBAAqBA,CAAA;IAC3B;IACA,IAAI,CAAC1B,iBAAiB,GAAG,CACvB;MACEqC,EAAE,EAAE,GAAG;MACPC,MAAM,EAAE,OAAO;MACfxD,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,iBAAiB;MAC5BE,UAAU,EAAE,UAAiB;MAC7B9B,WAAW,EAAE,qCAAqC;MAClDgC,WAAW,EAAE,IAAI+C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACtDzC,WAAW,EAAE,IAAIwC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAClDK,WAAW,EAAE,IAAI,CAAC/B,IAAK,CAACmB,GAAG;MAC3Ba,aAAa,EAAE,IAAI,CAAChC,IAAK,CAACC,IAAI;MAC9BrB,MAAM,EAAE,EAAE;MACVI,MAAM,EAAEnD,gBAAgB,CAACoG;KAC1B,EACD;MACEL,EAAE,EAAE,GAAG;MACPC,MAAM,EAAE,OAAO;MACfxD,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,iBAAiB;MAC5BE,UAAU,EAAE,sBAA6B;MACzC9B,WAAW,EAAE,uCAAuC;MACpDgC,WAAW,EAAE,IAAI+C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACtDzC,WAAW,EAAE,IAAIwC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACtDK,WAAW,EAAE,IAAI,CAAC/B,IAAK,CAACmB,GAAG;MAC3Ba,aAAa,EAAE,IAAI,CAAChC,IAAK,CAACC,IAAI;MAC9BrB,MAAM,EAAE,EAAE;MACVI,MAAM,EAAEnD,gBAAgB,CAACoG;KAC1B,CACF;EACH;EAEAlC,WAAWA,CAAA;IACT,MAAMmC,IAAI,GAAG,IAAIT,IAAI,EAAE,CAACU,QAAQ,EAAE;IAClC,IAAID,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,SAAS;EAClB;EAEA3D,wBAAwBA,CAACC,UAAkB;IACzC,MAAM4D,WAAW,GAA8B;MAC7C,aAAa,EAAE,eAAe;MAC9B,mBAAmB,EAAE,wBAAwB;MAC7C,kBAAkB,EAAE,oBAAoB;MACxC,mBAAmB,EAAE,uBAAuB;MAC5C,sBAAsB,EAAE,yBAAyB;MACjD,gBAAgB,EAAE,oBAAoB;MACtC,iBAAiB,EAAE,kBAAkB;MACrC,UAAU,EAAE,SAAS;MACrB,cAAc,EAAE,0BAA0B;MAC1C,OAAO,EAAE;KACV;IACD,OAAOA,WAAW,CAAC5D,UAAU,CAAC,IAAIA,UAAU;EAC9C;EAEAM,cAAcA,CAACE,MAAwB;IACrC,MAAMqD,MAAM,GAAG;MACb,CAACxG,gBAAgB,CAACiG,OAAO,GAAG,SAAS;MACrC,CAACjG,gBAAgB,CAACoG,QAAQ,GAAG,SAAS;MACtC,CAACpG,gBAAgB,CAACyG,QAAQ,GAAG;KAC9B;IACD,OAAOD,MAAM,CAACrD,MAAM,CAAC;EACvB;EAEAE,oBAAoBA,CAACF,MAAwB;IAC3C,MAAMuD,WAAW,GAAG;MAClB,CAAC1G,gBAAgB,CAACiG,OAAO,GAAG,YAAY;MACxC,CAACjG,gBAAgB,CAACoG,QAAQ,GAAG,WAAW;MACxC,CAACpG,gBAAgB,CAACyG,QAAQ,GAAG;KAC9B;IACD,OAAOC,WAAW,CAACvD,MAAM,CAAC;EAC5B;EAEAf,cAAcA,CAACuE,UAA4B,EAAEC,QAAiB;IAC5DD,UAAU,CAACxD,MAAM,GAAGyD,QAAQ,GAAG5G,gBAAgB,CAACoG,QAAQ,GAAGpG,gBAAgB,CAACyG,QAAQ;IACpFE,UAAU,CAACvD,WAAW,GAAG,IAAIwC,IAAI,EAAE;IACnCe,UAAU,CAACT,WAAW,GAAG,IAAI,CAAC/B,IAAK,CAACmB,GAAG;IACvCqB,UAAU,CAACR,aAAa,GAAG,IAAI,CAAChC,IAAK,CAACC,IAAI;IAE1C;IACA,IAAI,CAACrD,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC8F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACf,EAAE,KAAKY,UAAU,CAACZ,EAAE,CAAC;IAErF;IACA,IAAI,CAACrC,iBAAiB,CAACqD,OAAO,CAACJ,UAAU,CAAC;IAE1C;IACA,IAAI,CAACjC,cAAe,CAACc,gBAAgB,EAAE;IACvC,IAAI,CAACd,cAAe,CAACa,gBAAgB,EAAE;IACvC,IAAI,CAAClB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAACI,kBAAkB,CAACC,MAAM,CAAC8E,QAAQ,EAAE;IACpE,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAAC+D,cAAe,CAACc,gBAAgB,CAACM,QAAQ,EAAE;IAC3E,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAAC+D,cAAe,CAACa,gBAAgB,CAACO,QAAQ,EAAE;IAE3EkB,OAAO,CAACC,GAAG,CAAC,cAAcL,QAAQ,GAAG,UAAU,GAAG,UAAU,GAAG,EAAED,UAAU,CAAC;EAC9E;EAEApE,qBAAqBA,CAACoE,UAA4B;IAChDK,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEN,UAAU,CAAC;IACnD;EACF;EAEAlD,UAAUA,CAACyD,KAAa;IACtB,IAAI,CAACzC,MAAM,CAACQ,QAAQ,CAAC,CAACiC,KAAK,CAAC,CAAC;EAC/B;;;uBApOW5C,2BAA2B,EAAArE,EAAA,CAAAkH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApH,EAAA,CAAAkH,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA3BjD,2BAA2B;MAAAkD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UCVxC7H,EAAA,CAAA2B,UAAA,IAAAoG,0CAAA,kBAAwD;;;UAAZ/H,EAAA,CAAA6B,UAAA,SAAAiG,GAAA,CAAA5D,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}