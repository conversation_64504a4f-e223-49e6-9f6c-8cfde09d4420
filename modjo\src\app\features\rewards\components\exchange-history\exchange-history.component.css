.exchange-history-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 0;
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

.history-header {
  text-align: center;
  margin-bottom: 40px;
  padding: 40px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 24px;
  color: white;
  position: relative;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(103, 126, 234, 0.3);
}

.history-header::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: 
    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);
  pointer-events: none;
}

.history-header h1 {
  margin: 0 0 12px 0;
  font-size: 2.5rem;
  font-weight: 700;
  font-family: 'Poppins', sans-serif;
  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.history-header p {
  margin: 0;
  font-size: 1.2rem;
  opacity: 0.9;
}

.stats-section {
  margin-bottom: 40px;
}

.stats-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  gap: 24px;
}

.stat-card {
  border-radius: 20px;
  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  position: relative;
}

.stat-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
  opacity: 0.8;
}

.stat-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.stat-content {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 24px;
  position: relative;
  z-index: 1;
}

.stat-icon {
  font-size: 2.5rem;
  width: 2.5rem;
  height: 2.5rem;
  background: linear-gradient(135deg, rgba(103, 126, 234, 0.1), rgba(118, 75, 162, 0.1));
  border-radius: 16px;
  padding: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1);
}

.stat-info {
  flex: 1;
}

.stat-info h3 {
  margin: 0 0 8px 0;
  font-size: 2rem;
  font-weight: 700;
  color: #2d3748;
  font-family: 'Poppins', sans-serif;
}

.stat-info p {
  margin: 0;
  color: #718096;
  font-size: 0.9rem;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.exchanges-section {
  margin-bottom: 40px;
}

.exchanges-list {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.exchange-card {
  border-radius: 20px;
  overflow: hidden;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.exchange-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
}

.exchange-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  width: 100%;
}

.exchange-title {
  flex: 1;
}

.exchange-title h3 {
  margin: 0 0 8px 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: #2d3748;
}

.exchange-points {
  display: flex;
  align-items: center;
  gap: 8px;
  background: linear-gradient(135deg, #ffd700, #ffed4e);
  color: #2d3748;
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: 700;
  font-size: 1rem;
  box-shadow: 0 4px 12px rgba(255, 215, 0, 0.3);
}

.exchange-points mat-icon {
  font-size: 1.1rem;
  width: 1.1rem;
  height: 1.1rem;
}

.exchange-details {
  padding: 0 24px 24px 24px;
}

.detail-row {
  display: flex;
  align-items: flex-start;
  gap: 16px;
  padding: 12px 0;
  border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.detail-row:last-child {
  border-bottom: none;
}

.detail-row mat-icon {
  color: #667eea;
  flex-shrink: 0;
  margin-top: 2px;
}

.detail-row > div {
  flex: 1;
}

.detail-row label {
  display: block;
  font-size: 0.8rem;
  font-weight: 500;
  color: #a0aec0;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin-bottom: 4px;
}

.detail-row span {
  color: #4a5568;
  font-weight: 500;
}

.detail-row span.expired {
  color: #f56565;
  font-weight: 600;
}

.exchange-code {
  display: flex;
  align-items: center;
  gap: 16px;
  background: linear-gradient(135deg, rgba(103, 126, 234, 0.05), rgba(118, 75, 162, 0.05));
  padding: 16px;
  border-radius: 16px;
  border: 2px dashed rgba(103, 126, 234, 0.3);
}

.code-info {
  flex: 1;
}

.code-value {
  display: flex;
  align-items: center;
  gap: 12px;
}

.code {
  font-family: 'Courier New', monospace;
  font-size: 1.1rem;
  font-weight: 700;
  color: #2d3748;
  background: rgba(255, 255, 255, 0.8);
  padding: 8px 12px;
  border-radius: 8px;
  letter-spacing: 1px;
}

.exchange-actions {
  padding: 16px 24px;
  background: rgba(103, 126, 234, 0.05);
  border-top: 1px solid rgba(103, 126, 234, 0.1);
}

.usage-instructions {
  display: flex;
  align-items: center;
  gap: 12px;
  color: #667eea;
  font-weight: 500;
  font-size: 0.9rem;
}

.usage-instructions mat-icon {
  color: #667eea;
}

.empty-state {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.empty-card {
  max-width: 500px;
  text-align: center;
}

.empty-content {
  padding: 40px;
}

.empty-icon {
  font-size: 4rem;
  color: #cbd5e0;
  margin-bottom: 24px;
}

.empty-content h3 {
  margin: 0 0 12px 0;
  color: #2d3748;
  font-size: 1.5rem;
  font-weight: 600;
}

.empty-content p {
  margin: 0 0 24px 0;
  color: #718096;
  line-height: 1.6;
}

.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 400px;
}

.loading-card {
  max-width: 400px;
  text-align: center;
}

.loading-content {
  padding: 40px;
}

.loading-content h3 {
  margin: 24px 0 0 0;
  color: #2d3748;
  font-size: 1.3rem;
}

/* Mobile responsive */
@media (max-width: 768px) {
  .exchange-history-container {
    padding: 16px;
  }
  
  .history-header {
    padding: 24px;
    margin-bottom: 24px;
  }
  
  .history-header h1 {
    font-size: 2rem;
  }
  
  .stats-grid {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 16px;
  }
  
  .stat-content {
    padding: 16px;
    gap: 16px;
  }
  
  .stat-icon {
    font-size: 2rem;
    width: 2rem;
    height: 2rem;
    padding: 12px;
  }
  
  .stat-info h3 {
    font-size: 1.5rem;
  }
  
  .exchange-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .exchange-points {
    align-self: flex-end;
  }
  
  .exchange-details {
    padding: 0 16px 16px 16px;
  }
  
  .detail-row {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .exchange-code {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .code-value {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
}
