{"ast": null, "code": "import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Input, ContentChildren, Directive, NgModule } from '@angular/core';\nimport { setLines, MatLine, MatLineModule, MatCommonModule } from '@angular/material/core';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/bidi';\n\n/**\n * Class for determining, from a list of tiles, the (row, col) position of each of those tiles\n * in the grid. This is necessary (rather than just rendering the tiles in normal document flow)\n * because the tiles can have a rowspan.\n *\n * The positioning algorithm greedily places each tile as soon as it encounters a gap in the grid\n * large enough to accommodate it so that the tiles still render in the same order in which they\n * are given.\n *\n * The basis of the algorithm is the use of an array to track the already placed tiles. Each\n * element of the array corresponds to a column, and the value indicates how many cells in that\n * column are already occupied; zero indicates an empty cell. Moving \"down\" to the next row\n * decrements each value in the tracking array (indicating that the column is one cell closer to\n * being free).\n *\n * @docs-private\n */\nconst _c0 = [\"*\"];\nconst _c1 = [[[\"\", \"mat-grid-avatar\", \"\"], [\"\", \"matGridAvatar\", \"\"]], [[\"\", \"mat-line\", \"\"], [\"\", \"matLine\", \"\"]], \"*\"];\nconst _c2 = [\"[mat-grid-avatar], [matGridAvatar]\", \"[mat-line], [matLine]\", \"*\"];\nconst _c3 = \".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size)}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size)}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size)}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size)}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\";\nclass TileCoordinator {\n  constructor() {\n    /** Index at which the search for the next gap will start. */\n    this.columnIndex = 0;\n    /** The current row index. */\n    this.rowIndex = 0;\n  }\n  /** Gets the total number of rows occupied by tiles */\n  get rowCount() {\n    return this.rowIndex + 1;\n  }\n  /**\n   * Gets the total span of rows occupied by tiles.\n   * Ex: A list with 1 row that contains a tile with rowspan 2 will have a total rowspan of 2.\n   */\n  get rowspan() {\n    const lastRowMax = Math.max(...this.tracker);\n    // if any of the tiles has a rowspan that pushes it beyond the total row count,\n    // add the difference to the rowcount\n    return lastRowMax > 1 ? this.rowCount + lastRowMax - 1 : this.rowCount;\n  }\n  /**\n   * Updates the tile positions.\n   * @param numColumns Amount of columns in the grid.\n   * @param tiles Tiles to be positioned.\n   */\n  update(numColumns, tiles) {\n    this.columnIndex = 0;\n    this.rowIndex = 0;\n    this.tracker = new Array(numColumns);\n    this.tracker.fill(0, 0, this.tracker.length);\n    this.positions = tiles.map(tile => this._trackTile(tile));\n  }\n  /** Calculates the row and col position of a tile. */\n  _trackTile(tile) {\n    // Find a gap large enough for this tile.\n    const gapStartIndex = this._findMatchingGap(tile.colspan);\n    // Place tile in the resulting gap.\n    this._markTilePosition(gapStartIndex, tile);\n    // The next time we look for a gap, the search will start at columnIndex, which should be\n    // immediately after the tile that has just been placed.\n    this.columnIndex = gapStartIndex + tile.colspan;\n    return new TilePosition(this.rowIndex, gapStartIndex);\n  }\n  /** Finds the next available space large enough to fit the tile. */\n  _findMatchingGap(tileCols) {\n    if (tileCols > this.tracker.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`mat-grid-list: tile with colspan ${tileCols} is wider than ` + `grid with cols=\"${this.tracker.length}\".`);\n    }\n    // Start index is inclusive, end index is exclusive.\n    let gapStartIndex = -1;\n    let gapEndIndex = -1;\n    // Look for a gap large enough to fit the given tile. Empty spaces are marked with a zero.\n    do {\n      // If we've reached the end of the row, go to the next row.\n      if (this.columnIndex + tileCols > this.tracker.length) {\n        this._nextRow();\n        gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n        gapEndIndex = this._findGapEndIndex(gapStartIndex);\n        continue;\n      }\n      gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n      // If there are no more empty spaces in this row at all, move on to the next row.\n      if (gapStartIndex == -1) {\n        this._nextRow();\n        gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n        gapEndIndex = this._findGapEndIndex(gapStartIndex);\n        continue;\n      }\n      gapEndIndex = this._findGapEndIndex(gapStartIndex);\n      // If a gap large enough isn't found, we want to start looking immediately after the current\n      // gap on the next iteration.\n      this.columnIndex = gapStartIndex + 1;\n      // Continue iterating until we find a gap wide enough for this tile. Since gapEndIndex is\n      // exclusive, gapEndIndex is 0 means we didn't find a gap and should continue.\n    } while (gapEndIndex - gapStartIndex < tileCols || gapEndIndex == 0);\n    // If we still didn't manage to find a gap, ensure that the index is\n    // at least zero so the tile doesn't get pulled out of the grid.\n    return Math.max(gapStartIndex, 0);\n  }\n  /** Move \"down\" to the next row. */\n  _nextRow() {\n    this.columnIndex = 0;\n    this.rowIndex++;\n    // Decrement all spaces by one to reflect moving down one row.\n    for (let i = 0; i < this.tracker.length; i++) {\n      this.tracker[i] = Math.max(0, this.tracker[i] - 1);\n    }\n  }\n  /**\n   * Finds the end index (exclusive) of a gap given the index from which to start looking.\n   * The gap ends when a non-zero value is found.\n   */\n  _findGapEndIndex(gapStartIndex) {\n    for (let i = gapStartIndex + 1; i < this.tracker.length; i++) {\n      if (this.tracker[i] != 0) {\n        return i;\n      }\n    }\n    // The gap ends with the end of the row.\n    return this.tracker.length;\n  }\n  /** Update the tile tracker to account for the given tile in the given space. */\n  _markTilePosition(start, tile) {\n    for (let i = 0; i < tile.colspan; i++) {\n      this.tracker[start + i] = tile.rowspan;\n    }\n  }\n}\n/**\n * Simple data structure for tile position (row, col).\n * @docs-private\n */\nclass TilePosition {\n  constructor(row, col) {\n    this.row = row;\n    this.col = col;\n  }\n}\n\n/**\n * Injection token used to provide a grid list to a tile and to avoid circular imports.\n * @docs-private\n */\nconst MAT_GRID_LIST = new InjectionToken('MAT_GRID_LIST');\nclass MatGridTile {\n  constructor(_element, _gridList) {\n    this._element = _element;\n    this._gridList = _gridList;\n    this._rowspan = 1;\n    this._colspan = 1;\n  }\n  /** Amount of rows that the grid tile takes up. */\n  get rowspan() {\n    return this._rowspan;\n  }\n  set rowspan(value) {\n    this._rowspan = Math.round(coerceNumberProperty(value));\n  }\n  /** Amount of columns that the grid tile takes up. */\n  get colspan() {\n    return this._colspan;\n  }\n  set colspan(value) {\n    this._colspan = Math.round(coerceNumberProperty(value));\n  }\n  /**\n   * Sets the style of the grid-tile element.  Needs to be set manually to avoid\n   * \"Changed after checked\" errors that would occur with HostBinding.\n   */\n  _setStyle(property, value) {\n    this._element.nativeElement.style[property] = value;\n  }\n  static {\n    this.ɵfac = function MatGridTile_Factory(t) {\n      return new (t || MatGridTile)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(MAT_GRID_LIST, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatGridTile,\n      selectors: [[\"mat-grid-tile\"]],\n      hostAttrs: [1, \"mat-grid-tile\"],\n      hostVars: 2,\n      hostBindings: function MatGridTile_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"rowspan\", ctx.rowspan)(\"colspan\", ctx.colspan);\n        }\n      },\n      inputs: {\n        rowspan: \"rowspan\",\n        colspan: \"colspan\"\n      },\n      exportAs: [\"matGridTile\"],\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 0,\n      consts: [[1, \"mat-grid-tile-content\"]],\n      template: function MatGridTile_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\", 0);\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size)}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size)}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size)}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size)}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\"],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridTile, [{\n    type: Component,\n    args: [{\n      selector: 'mat-grid-tile',\n      exportAs: 'matGridTile',\n      host: {\n        'class': 'mat-grid-tile',\n        // Ensures that the \"rowspan\" and \"colspan\" input value is reflected in\n        // the DOM. This is needed for the grid-tile harness.\n        '[attr.rowspan]': 'rowspan',\n        '[attr.colspan]': 'colspan'\n      },\n      encapsulation: ViewEncapsulation.None,\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      standalone: true,\n      template: \"<div class=\\\"mat-grid-tile-content\\\">\\n  <ng-content></ng-content>\\n</div>\\n\",\n      styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size)}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size)}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size)}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size)}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: undefined,\n    decorators: [{\n      type: Optional\n    }, {\n      type: Inject,\n      args: [MAT_GRID_LIST]\n    }]\n  }], {\n    rowspan: [{\n      type: Input\n    }],\n    colspan: [{\n      type: Input\n    }]\n  });\n})();\nclass MatGridTileText {\n  constructor(_element) {\n    this._element = _element;\n  }\n  ngAfterContentInit() {\n    setLines(this._lines, this._element);\n  }\n  static {\n    this.ɵfac = function MatGridTileText_Factory(t) {\n      return new (t || MatGridTileText)(i0.ɵɵdirectiveInject(i0.ElementRef));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatGridTileText,\n      selectors: [[\"mat-grid-tile-header\"], [\"mat-grid-tile-footer\"]],\n      contentQueries: function MatGridTileText_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatLine, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._lines = _t);\n        }\n      },\n      standalone: true,\n      features: [i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c2,\n      decls: 4,\n      vars: 0,\n      consts: [[1, \"mat-grid-list-text\"]],\n      template: function MatGridTileText_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef(_c1);\n          i0.ɵɵprojection(0);\n          i0.ɵɵelementStart(1, \"div\", 0);\n          i0.ɵɵprojection(2, 1);\n          i0.ɵɵelementEnd();\n          i0.ɵɵprojection(3, 2);\n        }\n      },\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridTileText, [{\n    type: Component,\n    args: [{\n      selector: 'mat-grid-tile-header, mat-grid-tile-footer',\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      template: \"<ng-content select=\\\"[mat-grid-avatar], [matGridAvatar]\\\"></ng-content>\\n<div class=\\\"mat-grid-list-text\\\"><ng-content select=\\\"[mat-line], [matLine]\\\"></ng-content></div>\\n<ng-content></ng-content>\\n\"\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }], {\n    _lines: [{\n      type: ContentChildren,\n      args: [MatLine, {\n        descendants: true\n      }]\n    }]\n  });\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridAvatarCssMatStyler {\n  static {\n    this.ɵfac = function MatGridAvatarCssMatStyler_Factory(t) {\n      return new (t || MatGridAvatarCssMatStyler)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatGridAvatarCssMatStyler,\n      selectors: [[\"\", \"mat-grid-avatar\", \"\"], [\"\", \"matGridAvatar\", \"\"]],\n      hostAttrs: [1, \"mat-grid-avatar\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridAvatarCssMatStyler, [{\n    type: Directive,\n    args: [{\n      selector: '[mat-grid-avatar], [matGridAvatar]',\n      host: {\n        'class': 'mat-grid-avatar'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridTileHeaderCssMatStyler {\n  static {\n    this.ɵfac = function MatGridTileHeaderCssMatStyler_Factory(t) {\n      return new (t || MatGridTileHeaderCssMatStyler)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatGridTileHeaderCssMatStyler,\n      selectors: [[\"mat-grid-tile-header\"]],\n      hostAttrs: [1, \"mat-grid-tile-header\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridTileHeaderCssMatStyler, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-grid-tile-header',\n      host: {\n        'class': 'mat-grid-tile-header'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridTileFooterCssMatStyler {\n  static {\n    this.ɵfac = function MatGridTileFooterCssMatStyler_Factory(t) {\n      return new (t || MatGridTileFooterCssMatStyler)();\n    };\n  }\n  static {\n    this.ɵdir = /* @__PURE__ */i0.ɵɵdefineDirective({\n      type: MatGridTileFooterCssMatStyler,\n      selectors: [[\"mat-grid-tile-footer\"]],\n      hostAttrs: [1, \"mat-grid-tile-footer\"],\n      standalone: true\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridTileFooterCssMatStyler, [{\n    type: Directive,\n    args: [{\n      selector: 'mat-grid-tile-footer',\n      host: {\n        'class': 'mat-grid-tile-footer'\n      },\n      standalone: true\n    }]\n  }], null, null);\n})();\n\n/**\n * RegExp that can be used to check whether a value will\n * be allowed inside a CSS `calc()` expression.\n */\nconst cssCalcAllowedValue = /^-?\\d+((\\.\\d+)?[A-Za-z%$]?)+$/;\n/**\n * Sets the style properties for an individual tile, given the position calculated by the\n * Tile Coordinator.\n * @docs-private\n */\nclass TileStyler {\n  constructor() {\n    this._rows = 0;\n    this._rowspan = 0;\n  }\n  /**\n   * Adds grid-list layout info once it is available. Cannot be processed in the constructor\n   * because these properties haven't been calculated by that point.\n   *\n   * @param gutterSize Size of the grid's gutter.\n   * @param tracker Instance of the TileCoordinator.\n   * @param cols Amount of columns in the grid.\n   * @param direction Layout direction of the grid.\n   */\n  init(gutterSize, tracker, cols, direction) {\n    this._gutterSize = normalizeUnits(gutterSize);\n    this._rows = tracker.rowCount;\n    this._rowspan = tracker.rowspan;\n    this._cols = cols;\n    this._direction = direction;\n  }\n  /**\n   * Computes the amount of space a single 1x1 tile would take up (width or height).\n   * Used as a basis for other calculations.\n   * @param sizePercent Percent of the total grid-list space that one 1x1 tile would take up.\n   * @param gutterFraction Fraction of the gutter size taken up by one 1x1 tile.\n   * @return The size of a 1x1 tile as an expression that can be evaluated via CSS calc().\n   */\n  getBaseTileSize(sizePercent, gutterFraction) {\n    // Take the base size percent (as would be if evenly dividing the size between cells),\n    // and then subtracting the size of one gutter. However, since there are no gutters on the\n    // edges, each tile only uses a fraction (gutterShare = numGutters / numCells) of the gutter\n    // size. (Imagine having one gutter per tile, and then breaking up the extra gutter on the\n    // edge evenly among the cells).\n    return `(${sizePercent}% - (${this._gutterSize} * ${gutterFraction}))`;\n  }\n  /**\n   * Gets The horizontal or vertical position of a tile, e.g., the 'top' or 'left' property value.\n   * @param offset Number of tiles that have already been rendered in the row/column.\n   * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n   * @return Position of the tile as a CSS calc() expression.\n   */\n  getTilePosition(baseSize, offset) {\n    // The position comes the size of a 1x1 tile plus gutter for each previous tile in the\n    // row/column (offset).\n    return offset === 0 ? '0' : calc(`(${baseSize} + ${this._gutterSize}) * ${offset}`);\n  }\n  /**\n   * Gets the actual size of a tile, e.g., width or height, taking rowspan or colspan into account.\n   * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n   * @param span The tile's rowspan or colspan.\n   * @return Size of the tile as a CSS calc() expression.\n   */\n  getTileSize(baseSize, span) {\n    return `(${baseSize} * ${span}) + (${span - 1} * ${this._gutterSize})`;\n  }\n  /**\n   * Sets the style properties to be applied to a tile for the given row and column index.\n   * @param tile Tile to which to apply the styling.\n   * @param rowIndex Index of the tile's row.\n   * @param colIndex Index of the tile's column.\n   */\n  setStyle(tile, rowIndex, colIndex) {\n    // Percent of the available horizontal space that one column takes up.\n    let percentWidthPerTile = 100 / this._cols;\n    // Fraction of the vertical gutter size that each column takes up.\n    // For example, if there are 5 columns, each column uses 4/5 = 0.8 times the gutter width.\n    let gutterWidthFractionPerTile = (this._cols - 1) / this._cols;\n    this.setColStyles(tile, colIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n    this.setRowStyles(tile, rowIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n  }\n  /** Sets the horizontal placement of the tile in the list. */\n  setColStyles(tile, colIndex, percentWidth, gutterWidth) {\n    // Base horizontal size of a column.\n    let baseTileWidth = this.getBaseTileSize(percentWidth, gutterWidth);\n    // The width and horizontal position of each tile is always calculated the same way, but the\n    // height and vertical position depends on the rowMode.\n    let side = this._direction === 'rtl' ? 'right' : 'left';\n    tile._setStyle(side, this.getTilePosition(baseTileWidth, colIndex));\n    tile._setStyle('width', calc(this.getTileSize(baseTileWidth, tile.colspan)));\n  }\n  /**\n   * Calculates the total size taken up by gutters across one axis of a list.\n   */\n  getGutterSpan() {\n    return `${this._gutterSize} * (${this._rowspan} - 1)`;\n  }\n  /**\n   * Calculates the total size taken up by tiles across one axis of a list.\n   * @param tileHeight Height of the tile.\n   */\n  getTileSpan(tileHeight) {\n    return `${this._rowspan} * ${this.getTileSize(tileHeight, 1)}`;\n  }\n  /**\n   * Calculates the computed height and returns the correct style property to set.\n   * This method can be implemented by each type of TileStyler.\n   * @docs-private\n   */\n  getComputedHeight() {\n    return null;\n  }\n}\n/**\n * This type of styler is instantiated when the user passes in a fixed row height.\n * Example `<mat-grid-list cols=\"3\" rowHeight=\"100px\">`\n * @docs-private\n */\nclass FixedTileStyler extends TileStyler {\n  constructor(fixedRowHeight) {\n    super();\n    this.fixedRowHeight = fixedRowHeight;\n  }\n  init(gutterSize, tracker, cols, direction) {\n    super.init(gutterSize, tracker, cols, direction);\n    this.fixedRowHeight = normalizeUnits(this.fixedRowHeight);\n    if (!cssCalcAllowedValue.test(this.fixedRowHeight) && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`Invalid value \"${this.fixedRowHeight}\" set as rowHeight.`);\n    }\n  }\n  setRowStyles(tile, rowIndex) {\n    tile._setStyle('top', this.getTilePosition(this.fixedRowHeight, rowIndex));\n    tile._setStyle('height', calc(this.getTileSize(this.fixedRowHeight, tile.rowspan)));\n  }\n  getComputedHeight() {\n    return ['height', calc(`${this.getTileSpan(this.fixedRowHeight)} + ${this.getGutterSpan()}`)];\n  }\n  reset(list) {\n    list._setListStyle(['height', null]);\n    if (list._tiles) {\n      list._tiles.forEach(tile => {\n        tile._setStyle('top', null);\n        tile._setStyle('height', null);\n      });\n    }\n  }\n}\n/**\n * This type of styler is instantiated when the user passes in a width:height ratio\n * for the row height.  Example `<mat-grid-list cols=\"3\" rowHeight=\"3:1\">`\n * @docs-private\n */\nclass RatioTileStyler extends TileStyler {\n  constructor(value) {\n    super();\n    this._parseRatio(value);\n  }\n  setRowStyles(tile, rowIndex, percentWidth, gutterWidth) {\n    let percentHeightPerTile = percentWidth / this.rowHeightRatio;\n    this.baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterWidth);\n    // Use padding-top and margin-top to maintain the given aspect ratio, as\n    // a percentage-based value for these properties is applied versus the *width* of the\n    // containing block. See http://www.w3.org/TR/CSS2/box.html#margin-properties\n    tile._setStyle('marginTop', this.getTilePosition(this.baseTileHeight, rowIndex));\n    tile._setStyle('paddingTop', calc(this.getTileSize(this.baseTileHeight, tile.rowspan)));\n  }\n  getComputedHeight() {\n    return ['paddingBottom', calc(`${this.getTileSpan(this.baseTileHeight)} + ${this.getGutterSpan()}`)];\n  }\n  reset(list) {\n    list._setListStyle(['paddingBottom', null]);\n    list._tiles.forEach(tile => {\n      tile._setStyle('marginTop', null);\n      tile._setStyle('paddingTop', null);\n    });\n  }\n  _parseRatio(value) {\n    const ratioParts = value.split(':');\n    if (ratioParts.length !== 2 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`mat-grid-list: invalid ratio given for row-height: \"${value}\"`);\n    }\n    this.rowHeightRatio = parseFloat(ratioParts[0]) / parseFloat(ratioParts[1]);\n  }\n}\n/**\n * This type of styler is instantiated when the user selects a \"fit\" row height mode.\n * In other words, the row height will reflect the total height of the container divided\n * by the number of rows.  Example `<mat-grid-list cols=\"3\" rowHeight=\"fit\">`\n *\n * @docs-private\n */\nclass FitTileStyler extends TileStyler {\n  setRowStyles(tile, rowIndex) {\n    // Percent of the available vertical space that one row takes up.\n    let percentHeightPerTile = 100 / this._rowspan;\n    // Fraction of the horizontal gutter size that each column takes up.\n    let gutterHeightPerTile = (this._rows - 1) / this._rows;\n    // Base vertical size of a column.\n    let baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterHeightPerTile);\n    tile._setStyle('top', this.getTilePosition(baseTileHeight, rowIndex));\n    tile._setStyle('height', calc(this.getTileSize(baseTileHeight, tile.rowspan)));\n  }\n  reset(list) {\n    if (list._tiles) {\n      list._tiles.forEach(tile => {\n        tile._setStyle('top', null);\n        tile._setStyle('height', null);\n      });\n    }\n  }\n}\n/** Wraps a CSS string in a calc function */\nfunction calc(exp) {\n  return `calc(${exp})`;\n}\n/** Appends pixels to a CSS string if no units are given. */\nfunction normalizeUnits(value) {\n  return value.match(/([A-Za-z%]+)$/) ? value : `${value}px`;\n}\n\n// TODO(kara): Conditional (responsive) column count / row size.\n// TODO(kara): Re-layout on window resize / media change (debounced).\n// TODO(kara): gridTileHeader and gridTileFooter.\nconst MAT_FIT_MODE = 'fit';\nclass MatGridList {\n  constructor(_element, _dir) {\n    this._element = _element;\n    this._dir = _dir;\n    /** The amount of space between tiles. This will be something like '5px' or '2em'. */\n    this._gutter = '1px';\n  }\n  /** Amount of columns in the grid list. */\n  get cols() {\n    return this._cols;\n  }\n  set cols(value) {\n    this._cols = Math.max(1, Math.round(coerceNumberProperty(value)));\n  }\n  /** Size of the grid list's gutter in pixels. */\n  get gutterSize() {\n    return this._gutter;\n  }\n  set gutterSize(value) {\n    this._gutter = `${value == null ? '' : value}`;\n  }\n  /** Set internal representation of row height from the user-provided value. */\n  get rowHeight() {\n    return this._rowHeight;\n  }\n  set rowHeight(value) {\n    const newValue = `${value == null ? '' : value}`;\n    if (newValue !== this._rowHeight) {\n      this._rowHeight = newValue;\n      this._setTileStyler(this._rowHeight);\n    }\n  }\n  ngOnInit() {\n    this._checkCols();\n    this._checkRowHeight();\n  }\n  /**\n   * The layout calculation is fairly cheap if nothing changes, so there's little cost\n   * to run it frequently.\n   */\n  ngAfterContentChecked() {\n    this._layoutTiles();\n  }\n  /** Throw a friendly error if cols property is missing */\n  _checkCols() {\n    if (!this.cols && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n      throw Error(`mat-grid-list: must pass in number of columns. ` + `Example: <mat-grid-list cols=\"3\">`);\n    }\n  }\n  /** Default to equal width:height if rowHeight property is missing */\n  _checkRowHeight() {\n    if (!this._rowHeight) {\n      this._setTileStyler('1:1');\n    }\n  }\n  /** Creates correct Tile Styler subtype based on rowHeight passed in by user */\n  _setTileStyler(rowHeight) {\n    if (this._tileStyler) {\n      this._tileStyler.reset(this);\n    }\n    if (rowHeight === MAT_FIT_MODE) {\n      this._tileStyler = new FitTileStyler();\n    } else if (rowHeight && rowHeight.indexOf(':') > -1) {\n      this._tileStyler = new RatioTileStyler(rowHeight);\n    } else {\n      this._tileStyler = new FixedTileStyler(rowHeight);\n    }\n  }\n  /** Computes and applies the size and position for all children grid tiles. */\n  _layoutTiles() {\n    if (!this._tileCoordinator) {\n      this._tileCoordinator = new TileCoordinator();\n    }\n    const tracker = this._tileCoordinator;\n    const tiles = this._tiles.filter(tile => !tile._gridList || tile._gridList === this);\n    const direction = this._dir ? this._dir.value : 'ltr';\n    this._tileCoordinator.update(this.cols, tiles);\n    this._tileStyler.init(this.gutterSize, tracker, this.cols, direction);\n    tiles.forEach((tile, index) => {\n      const pos = tracker.positions[index];\n      this._tileStyler.setStyle(tile, pos.row, pos.col);\n    });\n    this._setListStyle(this._tileStyler.getComputedHeight());\n  }\n  /** Sets style on the main grid-list element, given the style name and value. */\n  _setListStyle(style) {\n    if (style) {\n      this._element.nativeElement.style[style[0]] = style[1];\n    }\n  }\n  static {\n    this.ɵfac = function MatGridList_Factory(t) {\n      return new (t || MatGridList)(i0.ɵɵdirectiveInject(i0.ElementRef), i0.ɵɵdirectiveInject(i1.Directionality, 8));\n    };\n  }\n  static {\n    this.ɵcmp = /* @__PURE__ */i0.ɵɵdefineComponent({\n      type: MatGridList,\n      selectors: [[\"mat-grid-list\"]],\n      contentQueries: function MatGridList_ContentQueries(rf, ctx, dirIndex) {\n        if (rf & 1) {\n          i0.ɵɵcontentQuery(dirIndex, MatGridTile, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx._tiles = _t);\n        }\n      },\n      hostAttrs: [1, \"mat-grid-list\"],\n      hostVars: 1,\n      hostBindings: function MatGridList_HostBindings(rf, ctx) {\n        if (rf & 2) {\n          i0.ɵɵattribute(\"cols\", ctx.cols);\n        }\n      },\n      inputs: {\n        cols: \"cols\",\n        gutterSize: \"gutterSize\",\n        rowHeight: \"rowHeight\"\n      },\n      exportAs: [\"matGridList\"],\n      standalone: true,\n      features: [i0.ɵɵProvidersFeature([{\n        provide: MAT_GRID_LIST,\n        useExisting: MatGridList\n      }]), i0.ɵɵStandaloneFeature],\n      ngContentSelectors: _c0,\n      decls: 2,\n      vars: 0,\n      template: function MatGridList_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵprojectionDef();\n          i0.ɵɵelementStart(0, \"div\");\n          i0.ɵɵprojection(1);\n          i0.ɵɵelementEnd();\n        }\n      },\n      styles: [_c3],\n      encapsulation: 2,\n      changeDetection: 0\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridList, [{\n    type: Component,\n    args: [{\n      selector: 'mat-grid-list',\n      exportAs: 'matGridList',\n      host: {\n        'class': 'mat-grid-list',\n        // Ensures that the \"cols\" input value is reflected in the DOM. This is\n        // needed for the grid-list harness.\n        '[attr.cols]': 'cols'\n      },\n      providers: [{\n        provide: MAT_GRID_LIST,\n        useExisting: MatGridList\n      }],\n      changeDetection: ChangeDetectionStrategy.OnPush,\n      encapsulation: ViewEncapsulation.None,\n      standalone: true,\n      template: \"<div>\\n  <ng-content></ng-content>\\n</div>\",\n      styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size)}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size)}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size)}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size)}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\"]\n    }]\n  }], () => [{\n    type: i0.ElementRef\n  }, {\n    type: i1.Directionality,\n    decorators: [{\n      type: Optional\n    }]\n  }], {\n    _tiles: [{\n      type: ContentChildren,\n      args: [MatGridTile, {\n        descendants: true\n      }]\n    }],\n    cols: [{\n      type: Input\n    }],\n    gutterSize: [{\n      type: Input\n    }],\n    rowHeight: [{\n      type: Input\n    }]\n  });\n})();\nclass MatGridListModule {\n  static {\n    this.ɵfac = function MatGridListModule_Factory(t) {\n      return new (t || MatGridListModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: MatGridListModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      imports: [MatLineModule, MatCommonModule, MatLineModule, MatCommonModule]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(MatGridListModule, [{\n    type: NgModule,\n    args: [{\n      imports: [MatLineModule, MatCommonModule, MatGridList, MatGridTile, MatGridTileText, MatGridTileHeaderCssMatStyler, MatGridTileFooterCssMatStyler, MatGridAvatarCssMatStyler],\n      exports: [MatGridList, MatGridTile, MatGridTileText, MatLineModule, MatCommonModule, MatGridTileHeaderCssMatStyler, MatGridTileFooterCssMatStyler, MatGridAvatarCssMatStyler]\n    }]\n  }], null, null);\n})();\n\n// Privately exported for the grid-list harness.\nconst ɵTileCoordinator = TileCoordinator;\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatGridAvatarCssMatStyler, MatGridList, MatGridListModule, MatGridTile, MatGridTileFooterCssMatStyler, MatGridTileHeaderCssMatStyler, MatGridTileText, ɵTileCoordinator };", "map": {"version": 3, "names": ["i0", "InjectionToken", "Component", "ViewEncapsulation", "ChangeDetectionStrategy", "Optional", "Inject", "Input", "ContentChildren", "Directive", "NgModule", "setLines", "MatLine", "MatLineModule", "MatCommonModule", "coerceNumberProperty", "i1", "_c0", "_c1", "_c2", "_c3", "TileCoordinator", "constructor", "columnIndex", "rowIndex", "rowCount", "rowspan", "lastRowMax", "Math", "max", "tracker", "update", "numColumns", "tiles", "Array", "fill", "length", "positions", "map", "tile", "_trackTile", "gapStartIndex", "_findMatchingGap", "colspan", "_markTilePosition", "TilePosition", "tileCols", "ngDevMode", "Error", "gapEndIndex", "_nextRow", "indexOf", "_findGapEndIndex", "i", "start", "row", "col", "MAT_GRID_LIST", "MatGridTile", "_element", "_gridList", "_rowspan", "_colspan", "value", "round", "_setStyle", "property", "nativeElement", "style", "ɵfac", "MatGridTile_Factory", "t", "ɵɵdirectiveInject", "ElementRef", "ɵcmp", "ɵɵdefineComponent", "type", "selectors", "hostAttrs", "hostVars", "hostBindings", "MatGridTile_HostBindings", "rf", "ctx", "ɵɵattribute", "inputs", "exportAs", "standalone", "features", "ɵɵStandaloneFeature", "ngContentSelectors", "decls", "vars", "consts", "template", "MatGridTile_Template", "ɵɵprojectionDef", "ɵɵelementStart", "ɵɵprojection", "ɵɵelementEnd", "styles", "encapsulation", "changeDetection", "ɵsetClassMetadata", "args", "selector", "host", "None", "OnPush", "undefined", "decorators", "MatGridTileText", "ngAfterContentInit", "_lines", "MatGridTileText_Factory", "contentQueries", "MatGridTileText_ContentQueries", "dirIndex", "ɵɵcontentQuery", "_t", "ɵɵqueryRefresh", "ɵɵloadQuery", "MatGridTileText_Template", "descendants", "MatGridAvatarCssMatStyler", "MatGridAvatarCssMatStyler_Factory", "ɵdir", "ɵɵdefineDirective", "MatGridTileHeaderCssMatStyler", "MatGridTileHeaderCssMatStyler_Factory", "MatGridTileFooterCssMatStyler", "MatGridTileFooterCssMatStyler_Factory", "cssCalcAllowedValue", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "_rows", "init", "gutterSize", "cols", "direction", "_gutterSize", "normalizeUnits", "_cols", "_direction", "getBaseTileSize", "sizePercent", "gutterFraction", "getTilePosition", "baseSize", "offset", "calc", "getTileSize", "span", "setStyle", "colIndex", "percentWidthPerTile", "gutterWidthFractionPerTile", "setColStyles", "setRowStyles", "percentWidth", "gutterWidth", "baseTileWidth", "side", "getGutterSpan", "getTileSpan", "tileHeight", "getComputedHeight", "FixedTileStyler", "fixedRowHeight", "test", "reset", "list", "_setListStyle", "_tiles", "for<PERSON>ach", "RatioTileStyler", "_parseRatio", "percentHeightPerTile", "rowHeightRatio", "baseTileHeight", "ratioParts", "split", "parseFloat", "FitTileStyler", "gutterHeightPerTile", "exp", "match", "MAT_FIT_MODE", "MatGridList", "_dir", "_gutter", "rowHeight", "_rowHeight", "newValue", "_setTileStyler", "ngOnInit", "_checkCols", "_checkRowHeight", "ngAfterContentChecked", "_layoutTiles", "_tileStyler", "_tileCoordinator", "filter", "index", "pos", "MatGridList_Factory", "Directionality", "MatGridList_ContentQueries", "MatGridList_HostBindings", "ɵɵProvidersFeature", "provide", "useExisting", "MatGridList_Template", "providers", "MatGridListModule", "MatGridListModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector", "imports", "exports", "ɵTileCoordinator"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/material/fesm2022/grid-list.mjs"], "sourcesContent": ["import * as i0 from '@angular/core';\nimport { InjectionToken, Component, ViewEncapsulation, ChangeDetectionStrategy, Optional, Inject, Input, ContentChildren, Directive, NgModule } from '@angular/core';\nimport { setLines, MatLine, MatLineModule, MatCommonModule } from '@angular/material/core';\nimport { coerceNumberProperty } from '@angular/cdk/coercion';\nimport * as i1 from '@angular/cdk/bidi';\n\n/**\n * Class for determining, from a list of tiles, the (row, col) position of each of those tiles\n * in the grid. This is necessary (rather than just rendering the tiles in normal document flow)\n * because the tiles can have a rowspan.\n *\n * The positioning algorithm greedily places each tile as soon as it encounters a gap in the grid\n * large enough to accommodate it so that the tiles still render in the same order in which they\n * are given.\n *\n * The basis of the algorithm is the use of an array to track the already placed tiles. Each\n * element of the array corresponds to a column, and the value indicates how many cells in that\n * column are already occupied; zero indicates an empty cell. Moving \"down\" to the next row\n * decrements each value in the tracking array (indicating that the column is one cell closer to\n * being free).\n *\n * @docs-private\n */\nclass TileCoordinator {\n    constructor() {\n        /** Index at which the search for the next gap will start. */\n        this.columnIndex = 0;\n        /** The current row index. */\n        this.rowIndex = 0;\n    }\n    /** Gets the total number of rows occupied by tiles */\n    get rowCount() {\n        return this.rowIndex + 1;\n    }\n    /**\n     * Gets the total span of rows occupied by tiles.\n     * Ex: A list with 1 row that contains a tile with rowspan 2 will have a total rowspan of 2.\n     */\n    get rowspan() {\n        const lastRowMax = Math.max(...this.tracker);\n        // if any of the tiles has a rowspan that pushes it beyond the total row count,\n        // add the difference to the rowcount\n        return lastRowMax > 1 ? this.rowCount + lastRowMax - 1 : this.rowCount;\n    }\n    /**\n     * Updates the tile positions.\n     * @param numColumns Amount of columns in the grid.\n     * @param tiles Tiles to be positioned.\n     */\n    update(numColumns, tiles) {\n        this.columnIndex = 0;\n        this.rowIndex = 0;\n        this.tracker = new Array(numColumns);\n        this.tracker.fill(0, 0, this.tracker.length);\n        this.positions = tiles.map(tile => this._trackTile(tile));\n    }\n    /** Calculates the row and col position of a tile. */\n    _trackTile(tile) {\n        // Find a gap large enough for this tile.\n        const gapStartIndex = this._findMatchingGap(tile.colspan);\n        // Place tile in the resulting gap.\n        this._markTilePosition(gapStartIndex, tile);\n        // The next time we look for a gap, the search will start at columnIndex, which should be\n        // immediately after the tile that has just been placed.\n        this.columnIndex = gapStartIndex + tile.colspan;\n        return new TilePosition(this.rowIndex, gapStartIndex);\n    }\n    /** Finds the next available space large enough to fit the tile. */\n    _findMatchingGap(tileCols) {\n        if (tileCols > this.tracker.length && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`mat-grid-list: tile with colspan ${tileCols} is wider than ` +\n                `grid with cols=\"${this.tracker.length}\".`);\n        }\n        // Start index is inclusive, end index is exclusive.\n        let gapStartIndex = -1;\n        let gapEndIndex = -1;\n        // Look for a gap large enough to fit the given tile. Empty spaces are marked with a zero.\n        do {\n            // If we've reached the end of the row, go to the next row.\n            if (this.columnIndex + tileCols > this.tracker.length) {\n                this._nextRow();\n                gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n                gapEndIndex = this._findGapEndIndex(gapStartIndex);\n                continue;\n            }\n            gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n            // If there are no more empty spaces in this row at all, move on to the next row.\n            if (gapStartIndex == -1) {\n                this._nextRow();\n                gapStartIndex = this.tracker.indexOf(0, this.columnIndex);\n                gapEndIndex = this._findGapEndIndex(gapStartIndex);\n                continue;\n            }\n            gapEndIndex = this._findGapEndIndex(gapStartIndex);\n            // If a gap large enough isn't found, we want to start looking immediately after the current\n            // gap on the next iteration.\n            this.columnIndex = gapStartIndex + 1;\n            // Continue iterating until we find a gap wide enough for this tile. Since gapEndIndex is\n            // exclusive, gapEndIndex is 0 means we didn't find a gap and should continue.\n        } while (gapEndIndex - gapStartIndex < tileCols || gapEndIndex == 0);\n        // If we still didn't manage to find a gap, ensure that the index is\n        // at least zero so the tile doesn't get pulled out of the grid.\n        return Math.max(gapStartIndex, 0);\n    }\n    /** Move \"down\" to the next row. */\n    _nextRow() {\n        this.columnIndex = 0;\n        this.rowIndex++;\n        // Decrement all spaces by one to reflect moving down one row.\n        for (let i = 0; i < this.tracker.length; i++) {\n            this.tracker[i] = Math.max(0, this.tracker[i] - 1);\n        }\n    }\n    /**\n     * Finds the end index (exclusive) of a gap given the index from which to start looking.\n     * The gap ends when a non-zero value is found.\n     */\n    _findGapEndIndex(gapStartIndex) {\n        for (let i = gapStartIndex + 1; i < this.tracker.length; i++) {\n            if (this.tracker[i] != 0) {\n                return i;\n            }\n        }\n        // The gap ends with the end of the row.\n        return this.tracker.length;\n    }\n    /** Update the tile tracker to account for the given tile in the given space. */\n    _markTilePosition(start, tile) {\n        for (let i = 0; i < tile.colspan; i++) {\n            this.tracker[start + i] = tile.rowspan;\n        }\n    }\n}\n/**\n * Simple data structure for tile position (row, col).\n * @docs-private\n */\nclass TilePosition {\n    constructor(row, col) {\n        this.row = row;\n        this.col = col;\n    }\n}\n\n/**\n * Injection token used to provide a grid list to a tile and to avoid circular imports.\n * @docs-private\n */\nconst MAT_GRID_LIST = new InjectionToken('MAT_GRID_LIST');\n\nclass MatGridTile {\n    constructor(_element, _gridList) {\n        this._element = _element;\n        this._gridList = _gridList;\n        this._rowspan = 1;\n        this._colspan = 1;\n    }\n    /** Amount of rows that the grid tile takes up. */\n    get rowspan() {\n        return this._rowspan;\n    }\n    set rowspan(value) {\n        this._rowspan = Math.round(coerceNumberProperty(value));\n    }\n    /** Amount of columns that the grid tile takes up. */\n    get colspan() {\n        return this._colspan;\n    }\n    set colspan(value) {\n        this._colspan = Math.round(coerceNumberProperty(value));\n    }\n    /**\n     * Sets the style of the grid-tile element.  Needs to be set manually to avoid\n     * \"Changed after checked\" errors that would occur with HostBinding.\n     */\n    _setStyle(property, value) {\n        this._element.nativeElement.style[property] = value;\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTile, deps: [{ token: i0.ElementRef }, { token: MAT_GRID_LIST, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatGridTile, isStandalone: true, selector: \"mat-grid-tile\", inputs: { rowspan: \"rowspan\", colspan: \"colspan\" }, host: { properties: { \"attr.rowspan\": \"rowspan\", \"attr.colspan\": \"colspan\" }, classAttribute: \"mat-grid-tile\" }, exportAs: [\"matGridTile\"], ngImport: i0, template: \"<div class=\\\"mat-grid-tile-content\\\">\\n  <ng-content></ng-content>\\n</div>\\n\", styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size)}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size)}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size)}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size)}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTile, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-grid-tile', exportAs: 'matGridTile', host: {\n                        'class': 'mat-grid-tile',\n                        // Ensures that the \"rowspan\" and \"colspan\" input value is reflected in\n                        // the DOM. This is needed for the grid-tile harness.\n                        '[attr.rowspan]': 'rowspan',\n                        '[attr.colspan]': 'colspan',\n                    }, encapsulation: ViewEncapsulation.None, changeDetection: ChangeDetectionStrategy.OnPush, standalone: true, template: \"<div class=\\\"mat-grid-tile-content\\\">\\n  <ng-content></ng-content>\\n</div>\\n\", styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size)}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size)}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size)}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size)}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: undefined, decorators: [{\n                    type: Optional\n                }, {\n                    type: Inject,\n                    args: [MAT_GRID_LIST]\n                }] }], propDecorators: { rowspan: [{\n                type: Input\n            }], colspan: [{\n                type: Input\n            }] } });\nclass MatGridTileText {\n    constructor(_element) {\n        this._element = _element;\n    }\n    ngAfterContentInit() {\n        setLines(this._lines, this._element);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTileText, deps: [{ token: i0.ElementRef }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatGridTileText, isStandalone: true, selector: \"mat-grid-tile-header, mat-grid-tile-footer\", queries: [{ propertyName: \"_lines\", predicate: MatLine, descendants: true }], ngImport: i0, template: \"<ng-content select=\\\"[mat-grid-avatar], [matGridAvatar]\\\"></ng-content>\\n<div class=\\\"mat-grid-list-text\\\"><ng-content select=\\\"[mat-line], [matLine]\\\"></ng-content></div>\\n<ng-content></ng-content>\\n\", changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTileText, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-grid-tile-header, mat-grid-tile-footer', changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, standalone: true, template: \"<ng-content select=\\\"[mat-grid-avatar], [matGridAvatar]\\\"></ng-content>\\n<div class=\\\"mat-grid-list-text\\\"><ng-content select=\\\"[mat-line], [matLine]\\\"></ng-content></div>\\n<ng-content></ng-content>\\n\" }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }], propDecorators: { _lines: [{\n                type: ContentChildren,\n                args: [MatLine, { descendants: true }]\n            }] } });\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridAvatarCssMatStyler {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridAvatarCssMatStyler, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatGridAvatarCssMatStyler, isStandalone: true, selector: \"[mat-grid-avatar], [matGridAvatar]\", host: { classAttribute: \"mat-grid-avatar\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridAvatarCssMatStyler, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: '[mat-grid-avatar], [matGridAvatar]',\n                    host: { 'class': 'mat-grid-avatar' },\n                    standalone: true,\n                }]\n        }] });\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridTileHeaderCssMatStyler {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTileHeaderCssMatStyler, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatGridTileHeaderCssMatStyler, isStandalone: true, selector: \"mat-grid-tile-header\", host: { classAttribute: \"mat-grid-tile-header\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTileHeaderCssMatStyler, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-grid-tile-header',\n                    host: { 'class': 'mat-grid-tile-header' },\n                    standalone: true,\n                }]\n        }] });\n/**\n * Directive whose purpose is to add the mat- CSS styling to this selector.\n * @docs-private\n */\nclass MatGridTileFooterCssMatStyler {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTileFooterCssMatStyler, deps: [], target: i0.ɵɵFactoryTarget.Directive }); }\n    static { this.ɵdir = i0.ɵɵngDeclareDirective({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatGridTileFooterCssMatStyler, isStandalone: true, selector: \"mat-grid-tile-footer\", host: { classAttribute: \"mat-grid-tile-footer\" }, ngImport: i0 }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridTileFooterCssMatStyler, decorators: [{\n            type: Directive,\n            args: [{\n                    selector: 'mat-grid-tile-footer',\n                    host: { 'class': 'mat-grid-tile-footer' },\n                    standalone: true,\n                }]\n        }] });\n\n/**\n * RegExp that can be used to check whether a value will\n * be allowed inside a CSS `calc()` expression.\n */\nconst cssCalcAllowedValue = /^-?\\d+((\\.\\d+)?[A-Za-z%$]?)+$/;\n/**\n * Sets the style properties for an individual tile, given the position calculated by the\n * Tile Coordinator.\n * @docs-private\n */\nclass TileStyler {\n    constructor() {\n        this._rows = 0;\n        this._rowspan = 0;\n    }\n    /**\n     * Adds grid-list layout info once it is available. Cannot be processed in the constructor\n     * because these properties haven't been calculated by that point.\n     *\n     * @param gutterSize Size of the grid's gutter.\n     * @param tracker Instance of the TileCoordinator.\n     * @param cols Amount of columns in the grid.\n     * @param direction Layout direction of the grid.\n     */\n    init(gutterSize, tracker, cols, direction) {\n        this._gutterSize = normalizeUnits(gutterSize);\n        this._rows = tracker.rowCount;\n        this._rowspan = tracker.rowspan;\n        this._cols = cols;\n        this._direction = direction;\n    }\n    /**\n     * Computes the amount of space a single 1x1 tile would take up (width or height).\n     * Used as a basis for other calculations.\n     * @param sizePercent Percent of the total grid-list space that one 1x1 tile would take up.\n     * @param gutterFraction Fraction of the gutter size taken up by one 1x1 tile.\n     * @return The size of a 1x1 tile as an expression that can be evaluated via CSS calc().\n     */\n    getBaseTileSize(sizePercent, gutterFraction) {\n        // Take the base size percent (as would be if evenly dividing the size between cells),\n        // and then subtracting the size of one gutter. However, since there are no gutters on the\n        // edges, each tile only uses a fraction (gutterShare = numGutters / numCells) of the gutter\n        // size. (Imagine having one gutter per tile, and then breaking up the extra gutter on the\n        // edge evenly among the cells).\n        return `(${sizePercent}% - (${this._gutterSize} * ${gutterFraction}))`;\n    }\n    /**\n     * Gets The horizontal or vertical position of a tile, e.g., the 'top' or 'left' property value.\n     * @param offset Number of tiles that have already been rendered in the row/column.\n     * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n     * @return Position of the tile as a CSS calc() expression.\n     */\n    getTilePosition(baseSize, offset) {\n        // The position comes the size of a 1x1 tile plus gutter for each previous tile in the\n        // row/column (offset).\n        return offset === 0 ? '0' : calc(`(${baseSize} + ${this._gutterSize}) * ${offset}`);\n    }\n    /**\n     * Gets the actual size of a tile, e.g., width or height, taking rowspan or colspan into account.\n     * @param baseSize Base size of a 1x1 tile (as computed in getBaseTileSize).\n     * @param span The tile's rowspan or colspan.\n     * @return Size of the tile as a CSS calc() expression.\n     */\n    getTileSize(baseSize, span) {\n        return `(${baseSize} * ${span}) + (${span - 1} * ${this._gutterSize})`;\n    }\n    /**\n     * Sets the style properties to be applied to a tile for the given row and column index.\n     * @param tile Tile to which to apply the styling.\n     * @param rowIndex Index of the tile's row.\n     * @param colIndex Index of the tile's column.\n     */\n    setStyle(tile, rowIndex, colIndex) {\n        // Percent of the available horizontal space that one column takes up.\n        let percentWidthPerTile = 100 / this._cols;\n        // Fraction of the vertical gutter size that each column takes up.\n        // For example, if there are 5 columns, each column uses 4/5 = 0.8 times the gutter width.\n        let gutterWidthFractionPerTile = (this._cols - 1) / this._cols;\n        this.setColStyles(tile, colIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n        this.setRowStyles(tile, rowIndex, percentWidthPerTile, gutterWidthFractionPerTile);\n    }\n    /** Sets the horizontal placement of the tile in the list. */\n    setColStyles(tile, colIndex, percentWidth, gutterWidth) {\n        // Base horizontal size of a column.\n        let baseTileWidth = this.getBaseTileSize(percentWidth, gutterWidth);\n        // The width and horizontal position of each tile is always calculated the same way, but the\n        // height and vertical position depends on the rowMode.\n        let side = this._direction === 'rtl' ? 'right' : 'left';\n        tile._setStyle(side, this.getTilePosition(baseTileWidth, colIndex));\n        tile._setStyle('width', calc(this.getTileSize(baseTileWidth, tile.colspan)));\n    }\n    /**\n     * Calculates the total size taken up by gutters across one axis of a list.\n     */\n    getGutterSpan() {\n        return `${this._gutterSize} * (${this._rowspan} - 1)`;\n    }\n    /**\n     * Calculates the total size taken up by tiles across one axis of a list.\n     * @param tileHeight Height of the tile.\n     */\n    getTileSpan(tileHeight) {\n        return `${this._rowspan} * ${this.getTileSize(tileHeight, 1)}`;\n    }\n    /**\n     * Calculates the computed height and returns the correct style property to set.\n     * This method can be implemented by each type of TileStyler.\n     * @docs-private\n     */\n    getComputedHeight() {\n        return null;\n    }\n}\n/**\n * This type of styler is instantiated when the user passes in a fixed row height.\n * Example `<mat-grid-list cols=\"3\" rowHeight=\"100px\">`\n * @docs-private\n */\nclass FixedTileStyler extends TileStyler {\n    constructor(fixedRowHeight) {\n        super();\n        this.fixedRowHeight = fixedRowHeight;\n    }\n    init(gutterSize, tracker, cols, direction) {\n        super.init(gutterSize, tracker, cols, direction);\n        this.fixedRowHeight = normalizeUnits(this.fixedRowHeight);\n        if (!cssCalcAllowedValue.test(this.fixedRowHeight) &&\n            (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`Invalid value \"${this.fixedRowHeight}\" set as rowHeight.`);\n        }\n    }\n    setRowStyles(tile, rowIndex) {\n        tile._setStyle('top', this.getTilePosition(this.fixedRowHeight, rowIndex));\n        tile._setStyle('height', calc(this.getTileSize(this.fixedRowHeight, tile.rowspan)));\n    }\n    getComputedHeight() {\n        return ['height', calc(`${this.getTileSpan(this.fixedRowHeight)} + ${this.getGutterSpan()}`)];\n    }\n    reset(list) {\n        list._setListStyle(['height', null]);\n        if (list._tiles) {\n            list._tiles.forEach(tile => {\n                tile._setStyle('top', null);\n                tile._setStyle('height', null);\n            });\n        }\n    }\n}\n/**\n * This type of styler is instantiated when the user passes in a width:height ratio\n * for the row height.  Example `<mat-grid-list cols=\"3\" rowHeight=\"3:1\">`\n * @docs-private\n */\nclass RatioTileStyler extends TileStyler {\n    constructor(value) {\n        super();\n        this._parseRatio(value);\n    }\n    setRowStyles(tile, rowIndex, percentWidth, gutterWidth) {\n        let percentHeightPerTile = percentWidth / this.rowHeightRatio;\n        this.baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterWidth);\n        // Use padding-top and margin-top to maintain the given aspect ratio, as\n        // a percentage-based value for these properties is applied versus the *width* of the\n        // containing block. See http://www.w3.org/TR/CSS2/box.html#margin-properties\n        tile._setStyle('marginTop', this.getTilePosition(this.baseTileHeight, rowIndex));\n        tile._setStyle('paddingTop', calc(this.getTileSize(this.baseTileHeight, tile.rowspan)));\n    }\n    getComputedHeight() {\n        return [\n            'paddingBottom',\n            calc(`${this.getTileSpan(this.baseTileHeight)} + ${this.getGutterSpan()}`),\n        ];\n    }\n    reset(list) {\n        list._setListStyle(['paddingBottom', null]);\n        list._tiles.forEach(tile => {\n            tile._setStyle('marginTop', null);\n            tile._setStyle('paddingTop', null);\n        });\n    }\n    _parseRatio(value) {\n        const ratioParts = value.split(':');\n        if (ratioParts.length !== 2 && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`mat-grid-list: invalid ratio given for row-height: \"${value}\"`);\n        }\n        this.rowHeightRatio = parseFloat(ratioParts[0]) / parseFloat(ratioParts[1]);\n    }\n}\n/**\n * This type of styler is instantiated when the user selects a \"fit\" row height mode.\n * In other words, the row height will reflect the total height of the container divided\n * by the number of rows.  Example `<mat-grid-list cols=\"3\" rowHeight=\"fit\">`\n *\n * @docs-private\n */\nclass FitTileStyler extends TileStyler {\n    setRowStyles(tile, rowIndex) {\n        // Percent of the available vertical space that one row takes up.\n        let percentHeightPerTile = 100 / this._rowspan;\n        // Fraction of the horizontal gutter size that each column takes up.\n        let gutterHeightPerTile = (this._rows - 1) / this._rows;\n        // Base vertical size of a column.\n        let baseTileHeight = this.getBaseTileSize(percentHeightPerTile, gutterHeightPerTile);\n        tile._setStyle('top', this.getTilePosition(baseTileHeight, rowIndex));\n        tile._setStyle('height', calc(this.getTileSize(baseTileHeight, tile.rowspan)));\n    }\n    reset(list) {\n        if (list._tiles) {\n            list._tiles.forEach(tile => {\n                tile._setStyle('top', null);\n                tile._setStyle('height', null);\n            });\n        }\n    }\n}\n/** Wraps a CSS string in a calc function */\nfunction calc(exp) {\n    return `calc(${exp})`;\n}\n/** Appends pixels to a CSS string if no units are given. */\nfunction normalizeUnits(value) {\n    return value.match(/([A-Za-z%]+)$/) ? value : `${value}px`;\n}\n\n// TODO(kara): Conditional (responsive) column count / row size.\n// TODO(kara): Re-layout on window resize / media change (debounced).\n// TODO(kara): gridTileHeader and gridTileFooter.\nconst MAT_FIT_MODE = 'fit';\nclass MatGridList {\n    constructor(_element, _dir) {\n        this._element = _element;\n        this._dir = _dir;\n        /** The amount of space between tiles. This will be something like '5px' or '2em'. */\n        this._gutter = '1px';\n    }\n    /** Amount of columns in the grid list. */\n    get cols() {\n        return this._cols;\n    }\n    set cols(value) {\n        this._cols = Math.max(1, Math.round(coerceNumberProperty(value)));\n    }\n    /** Size of the grid list's gutter in pixels. */\n    get gutterSize() {\n        return this._gutter;\n    }\n    set gutterSize(value) {\n        this._gutter = `${value == null ? '' : value}`;\n    }\n    /** Set internal representation of row height from the user-provided value. */\n    get rowHeight() {\n        return this._rowHeight;\n    }\n    set rowHeight(value) {\n        const newValue = `${value == null ? '' : value}`;\n        if (newValue !== this._rowHeight) {\n            this._rowHeight = newValue;\n            this._setTileStyler(this._rowHeight);\n        }\n    }\n    ngOnInit() {\n        this._checkCols();\n        this._checkRowHeight();\n    }\n    /**\n     * The layout calculation is fairly cheap if nothing changes, so there's little cost\n     * to run it frequently.\n     */\n    ngAfterContentChecked() {\n        this._layoutTiles();\n    }\n    /** Throw a friendly error if cols property is missing */\n    _checkCols() {\n        if (!this.cols && (typeof ngDevMode === 'undefined' || ngDevMode)) {\n            throw Error(`mat-grid-list: must pass in number of columns. ` + `Example: <mat-grid-list cols=\"3\">`);\n        }\n    }\n    /** Default to equal width:height if rowHeight property is missing */\n    _checkRowHeight() {\n        if (!this._rowHeight) {\n            this._setTileStyler('1:1');\n        }\n    }\n    /** Creates correct Tile Styler subtype based on rowHeight passed in by user */\n    _setTileStyler(rowHeight) {\n        if (this._tileStyler) {\n            this._tileStyler.reset(this);\n        }\n        if (rowHeight === MAT_FIT_MODE) {\n            this._tileStyler = new FitTileStyler();\n        }\n        else if (rowHeight && rowHeight.indexOf(':') > -1) {\n            this._tileStyler = new RatioTileStyler(rowHeight);\n        }\n        else {\n            this._tileStyler = new FixedTileStyler(rowHeight);\n        }\n    }\n    /** Computes and applies the size and position for all children grid tiles. */\n    _layoutTiles() {\n        if (!this._tileCoordinator) {\n            this._tileCoordinator = new TileCoordinator();\n        }\n        const tracker = this._tileCoordinator;\n        const tiles = this._tiles.filter(tile => !tile._gridList || tile._gridList === this);\n        const direction = this._dir ? this._dir.value : 'ltr';\n        this._tileCoordinator.update(this.cols, tiles);\n        this._tileStyler.init(this.gutterSize, tracker, this.cols, direction);\n        tiles.forEach((tile, index) => {\n            const pos = tracker.positions[index];\n            this._tileStyler.setStyle(tile, pos.row, pos.col);\n        });\n        this._setListStyle(this._tileStyler.getComputedHeight());\n    }\n    /** Sets style on the main grid-list element, given the style name and value. */\n    _setListStyle(style) {\n        if (style) {\n            this._element.nativeElement.style[style[0]] = style[1];\n        }\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridList, deps: [{ token: i0.ElementRef }, { token: i1.Directionality, optional: true }], target: i0.ɵɵFactoryTarget.Component }); }\n    static { this.ɵcmp = i0.ɵɵngDeclareComponent({ minVersion: \"14.0.0\", version: \"17.2.0\", type: MatGridList, isStandalone: true, selector: \"mat-grid-list\", inputs: { cols: \"cols\", gutterSize: \"gutterSize\", rowHeight: \"rowHeight\" }, host: { properties: { \"attr.cols\": \"cols\" }, classAttribute: \"mat-grid-list\" }, providers: [\n            {\n                provide: MAT_GRID_LIST,\n                useExisting: MatGridList,\n            },\n        ], queries: [{ propertyName: \"_tiles\", predicate: MatGridTile, descendants: true }], exportAs: [\"matGridList\"], ngImport: i0, template: \"<div>\\n  <ng-content></ng-content>\\n</div>\", styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size)}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size)}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size)}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size)}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\"], changeDetection: i0.ChangeDetectionStrategy.OnPush, encapsulation: i0.ViewEncapsulation.None }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridList, decorators: [{\n            type: Component,\n            args: [{ selector: 'mat-grid-list', exportAs: 'matGridList', host: {\n                        'class': 'mat-grid-list',\n                        // Ensures that the \"cols\" input value is reflected in the DOM. This is\n                        // needed for the grid-list harness.\n                        '[attr.cols]': 'cols',\n                    }, providers: [\n                        {\n                            provide: MAT_GRID_LIST,\n                            useExisting: MatGridList,\n                        },\n                    ], changeDetection: ChangeDetectionStrategy.OnPush, encapsulation: ViewEncapsulation.None, standalone: true, template: \"<div>\\n  <ng-content></ng-content>\\n</div>\", styles: [\".mat-grid-list{display:block;position:relative}.mat-grid-tile{display:block;position:absolute;overflow:hidden}.mat-grid-tile .mat-grid-tile-header,.mat-grid-tile .mat-grid-tile-footer{display:flex;align-items:center;height:48px;color:#fff;background:rgba(0,0,0,.38);overflow:hidden;padding:0 16px;position:absolute;left:0;right:0}.mat-grid-tile .mat-grid-tile-header>*,.mat-grid-tile .mat-grid-tile-footer>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-tile-header.mat-2-line,.mat-grid-tile .mat-grid-tile-footer.mat-2-line{height:68px}.mat-grid-tile .mat-grid-list-text{display:flex;flex-direction:column;flex:auto;box-sizing:border-box;overflow:hidden}.mat-grid-tile .mat-grid-list-text>*{margin:0;padding:0;font-weight:normal;font-size:inherit}.mat-grid-tile .mat-grid-list-text:empty{display:none}.mat-grid-tile .mat-grid-tile-header{top:0}.mat-grid-tile .mat-grid-tile-footer{bottom:0}.mat-grid-tile .mat-grid-avatar{padding-right:16px}[dir=rtl] .mat-grid-tile .mat-grid-avatar{padding-right:0;padding-left:16px}.mat-grid-tile .mat-grid-avatar:empty{display:none}.mat-grid-tile-header{font-size:var(--mat-grid-list-tile-header-primary-text-size)}.mat-grid-tile-header .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-header .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-header-secondary-text-size)}.mat-grid-tile-footer{font-size:var(--mat-grid-list-tile-footer-primary-text-size)}.mat-grid-tile-footer .mat-line{white-space:nowrap;overflow:hidden;text-overflow:ellipsis;display:block;box-sizing:border-box}.mat-grid-tile-footer .mat-line:nth-child(n+2){font-size:var(--mat-grid-list-tile-footer-secondary-text-size)}.mat-grid-tile-content{top:0;left:0;right:0;bottom:0;position:absolute;display:flex;align-items:center;justify-content:center;height:100%;padding:0;margin:0}\"] }]\n        }], ctorParameters: () => [{ type: i0.ElementRef }, { type: i1.Directionality, decorators: [{\n                    type: Optional\n                }] }], propDecorators: { _tiles: [{\n                type: ContentChildren,\n                args: [MatGridTile, { descendants: true }]\n            }], cols: [{\n                type: Input\n            }], gutterSize: [{\n                type: Input\n            }], rowHeight: [{\n                type: Input\n            }] } });\n\nclass MatGridListModule {\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridListModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridListModule, imports: [MatLineModule,\n            MatCommonModule,\n            MatGridList,\n            MatGridTile,\n            MatGridTileText,\n            MatGridTileHeaderCssMatStyler,\n            MatGridTileFooterCssMatStyler,\n            MatGridAvatarCssMatStyler], exports: [MatGridList,\n            MatGridTile,\n            MatGridTileText,\n            MatLineModule,\n            MatCommonModule,\n            MatGridTileHeaderCssMatStyler,\n            MatGridTileFooterCssMatStyler,\n            MatGridAvatarCssMatStyler] }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridListModule, imports: [MatLineModule,\n            MatCommonModule, MatLineModule,\n            MatCommonModule] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.2.0\", ngImport: i0, type: MatGridListModule, decorators: [{\n            type: NgModule,\n            args: [{\n                    imports: [\n                        MatLineModule,\n                        MatCommonModule,\n                        MatGridList,\n                        MatGridTile,\n                        MatGridTileText,\n                        MatGridTileHeaderCssMatStyler,\n                        MatGridTileFooterCssMatStyler,\n                        MatGridAvatarCssMatStyler,\n                    ],\n                    exports: [\n                        MatGridList,\n                        MatGridTile,\n                        MatGridTileText,\n                        MatLineModule,\n                        MatCommonModule,\n                        MatGridTileHeaderCssMatStyler,\n                        MatGridTileFooterCssMatStyler,\n                        MatGridAvatarCssMatStyler,\n                    ],\n                }]\n        }] });\n\n// Privately exported for the grid-list harness.\nconst ɵTileCoordinator = TileCoordinator;\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { MatGridAvatarCssMatStyler, MatGridList, MatGridListModule, MatGridTile, MatGridTileFooterCssMatStyler, MatGridTileHeaderCssMatStyler, MatGridTileText, ɵTileCoordinator };\n"], "mappings": "AAAA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,cAAc,EAAEC,SAAS,EAAEC,iBAAiB,EAAEC,uBAAuB,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,KAAK,EAAEC,eAAe,EAAEC,SAAS,EAAEC,QAAQ,QAAQ,eAAe;AACpK,SAASC,QAAQ,EAAEC,OAAO,EAAEC,aAAa,EAAEC,eAAe,QAAQ,wBAAwB;AAC1F,SAASC,oBAAoB,QAAQ,uBAAuB;AAC5D,OAAO,KAAKC,EAAE,MAAM,mBAAmB;;AAEvC;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AAhBA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAAA,MAAAC,GAAA;AAiBA,MAAMC,eAAe,CAAC;EAClBC,WAAWA,CAAA,EAAG;IACV;IACA,IAAI,CAACC,WAAW,GAAG,CAAC;IACpB;IACA,IAAI,CAACC,QAAQ,GAAG,CAAC;EACrB;EACA;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACD,QAAQ,GAAG,CAAC;EAC5B;EACA;AACJ;AACA;AACA;EACI,IAAIE,OAAOA,CAAA,EAAG;IACV,MAAMC,UAAU,GAAGC,IAAI,CAACC,GAAG,CAAC,GAAG,IAAI,CAACC,OAAO,CAAC;IAC5C;IACA;IACA,OAAOH,UAAU,GAAG,CAAC,GAAG,IAAI,CAACF,QAAQ,GAAGE,UAAU,GAAG,CAAC,GAAG,IAAI,CAACF,QAAQ;EAC1E;EACA;AACJ;AACA;AACA;AACA;EACIM,MAAMA,CAACC,UAAU,EAAEC,KAAK,EAAE;IACtB,IAAI,CAACV,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACM,OAAO,GAAG,IAAII,KAAK,CAACF,UAAU,CAAC;IACpC,IAAI,CAACF,OAAO,CAACK,IAAI,CAAC,CAAC,EAAE,CAAC,EAAE,IAAI,CAACL,OAAO,CAACM,MAAM,CAAC;IAC5C,IAAI,CAACC,SAAS,GAAGJ,KAAK,CAACK,GAAG,CAACC,IAAI,IAAI,IAAI,CAACC,UAAU,CAACD,IAAI,CAAC,CAAC;EAC7D;EACA;EACAC,UAAUA,CAACD,IAAI,EAAE;IACb;IACA,MAAME,aAAa,GAAG,IAAI,CAACC,gBAAgB,CAACH,IAAI,CAACI,OAAO,CAAC;IACzD;IACA,IAAI,CAACC,iBAAiB,CAACH,aAAa,EAAEF,IAAI,CAAC;IAC3C;IACA;IACA,IAAI,CAAChB,WAAW,GAAGkB,aAAa,GAAGF,IAAI,CAACI,OAAO;IAC/C,OAAO,IAAIE,YAAY,CAAC,IAAI,CAACrB,QAAQ,EAAEiB,aAAa,CAAC;EACzD;EACA;EACAC,gBAAgBA,CAACI,QAAQ,EAAE;IACvB,IAAIA,QAAQ,GAAG,IAAI,CAAChB,OAAO,CAACM,MAAM,KAAK,OAAOW,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACnF,MAAMC,KAAK,CAAC,oCAAoCF,QAAQ,iBAAiB,GACrE,mBAAmB,IAAI,CAAChB,OAAO,CAACM,MAAM,IAAI,CAAC;IACnD;IACA;IACA,IAAIK,aAAa,GAAG,CAAC,CAAC;IACtB,IAAIQ,WAAW,GAAG,CAAC,CAAC;IACpB;IACA,GAAG;MACC;MACA,IAAI,IAAI,CAAC1B,WAAW,GAAGuB,QAAQ,GAAG,IAAI,CAAChB,OAAO,CAACM,MAAM,EAAE;QACnD,IAAI,CAACc,QAAQ,CAAC,CAAC;QACfT,aAAa,GAAG,IAAI,CAACX,OAAO,CAACqB,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC5B,WAAW,CAAC;QACzD0B,WAAW,GAAG,IAAI,CAACG,gBAAgB,CAACX,aAAa,CAAC;QAClD;MACJ;MACAA,aAAa,GAAG,IAAI,CAACX,OAAO,CAACqB,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC5B,WAAW,CAAC;MACzD;MACA,IAAIkB,aAAa,IAAI,CAAC,CAAC,EAAE;QACrB,IAAI,CAACS,QAAQ,CAAC,CAAC;QACfT,aAAa,GAAG,IAAI,CAACX,OAAO,CAACqB,OAAO,CAAC,CAAC,EAAE,IAAI,CAAC5B,WAAW,CAAC;QACzD0B,WAAW,GAAG,IAAI,CAACG,gBAAgB,CAACX,aAAa,CAAC;QAClD;MACJ;MACAQ,WAAW,GAAG,IAAI,CAACG,gBAAgB,CAACX,aAAa,CAAC;MAClD;MACA;MACA,IAAI,CAAClB,WAAW,GAAGkB,aAAa,GAAG,CAAC;MACpC;MACA;IACJ,CAAC,QAAQQ,WAAW,GAAGR,aAAa,GAAGK,QAAQ,IAAIG,WAAW,IAAI,CAAC;IACnE;IACA;IACA,OAAOrB,IAAI,CAACC,GAAG,CAACY,aAAa,EAAE,CAAC,CAAC;EACrC;EACA;EACAS,QAAQA,CAAA,EAAG;IACP,IAAI,CAAC3B,WAAW,GAAG,CAAC;IACpB,IAAI,CAACC,QAAQ,EAAE;IACf;IACA,KAAK,IAAI6B,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACvB,OAAO,CAACM,MAAM,EAAEiB,CAAC,EAAE,EAAE;MAC1C,IAAI,CAACvB,OAAO,CAACuB,CAAC,CAAC,GAAGzB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,IAAI,CAACC,OAAO,CAACuB,CAAC,CAAC,GAAG,CAAC,CAAC;IACtD;EACJ;EACA;AACJ;AACA;AACA;EACID,gBAAgBA,CAACX,aAAa,EAAE;IAC5B,KAAK,IAAIY,CAAC,GAAGZ,aAAa,GAAG,CAAC,EAAEY,CAAC,GAAG,IAAI,CAACvB,OAAO,CAACM,MAAM,EAAEiB,CAAC,EAAE,EAAE;MAC1D,IAAI,IAAI,CAACvB,OAAO,CAACuB,CAAC,CAAC,IAAI,CAAC,EAAE;QACtB,OAAOA,CAAC;MACZ;IACJ;IACA;IACA,OAAO,IAAI,CAACvB,OAAO,CAACM,MAAM;EAC9B;EACA;EACAQ,iBAAiBA,CAACU,KAAK,EAAEf,IAAI,EAAE;IAC3B,KAAK,IAAIc,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGd,IAAI,CAACI,OAAO,EAAEU,CAAC,EAAE,EAAE;MACnC,IAAI,CAACvB,OAAO,CAACwB,KAAK,GAAGD,CAAC,CAAC,GAAGd,IAAI,CAACb,OAAO;IAC1C;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMmB,YAAY,CAAC;EACfvB,WAAWA,CAACiC,GAAG,EAAEC,GAAG,EAAE;IAClB,IAAI,CAACD,GAAG,GAAGA,GAAG;IACd,IAAI,CAACC,GAAG,GAAGA,GAAG;EAClB;AACJ;;AAEA;AACA;AACA;AACA;AACA,MAAMC,aAAa,GAAG,IAAIxD,cAAc,CAAC,eAAe,CAAC;AAEzD,MAAMyD,WAAW,CAAC;EACdpC,WAAWA,CAACqC,QAAQ,EAAEC,SAAS,EAAE;IAC7B,IAAI,CAACD,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACC,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACC,QAAQ,GAAG,CAAC;IACjB,IAAI,CAACC,QAAQ,GAAG,CAAC;EACrB;EACA;EACA,IAAIpC,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACmC,QAAQ;EACxB;EACA,IAAInC,OAAOA,CAACqC,KAAK,EAAE;IACf,IAAI,CAACF,QAAQ,GAAGjC,IAAI,CAACoC,KAAK,CAACjD,oBAAoB,CAACgD,KAAK,CAAC,CAAC;EAC3D;EACA;EACA,IAAIpB,OAAOA,CAAA,EAAG;IACV,OAAO,IAAI,CAACmB,QAAQ;EACxB;EACA,IAAInB,OAAOA,CAACoB,KAAK,EAAE;IACf,IAAI,CAACD,QAAQ,GAAGlC,IAAI,CAACoC,KAAK,CAACjD,oBAAoB,CAACgD,KAAK,CAAC,CAAC;EAC3D;EACA;AACJ;AACA;AACA;EACIE,SAASA,CAACC,QAAQ,EAAEH,KAAK,EAAE;IACvB,IAAI,CAACJ,QAAQ,CAACQ,aAAa,CAACC,KAAK,CAACF,QAAQ,CAAC,GAAGH,KAAK;EACvD;EACA;IAAS,IAAI,CAACM,IAAI,YAAAC,oBAAAC,CAAA;MAAA,YAAAA,CAAA,IAAwFb,WAAW,EAArB1D,EAAE,CAAAwE,iBAAA,CAAqCxE,EAAE,CAACyE,UAAU,GAApDzE,EAAE,CAAAwE,iBAAA,CAA+Df,aAAa;IAAA,CAA4D;EAAE;EAC5O;IAAS,IAAI,CAACiB,IAAI,kBAD8E1E,EAAE,CAAA2E,iBAAA;MAAAC,IAAA,EACJlB,WAAW;MAAAmB,SAAA;MAAAC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UADTlF,EAAE,CAAAoF,WAAA,YAAAD,GAAA,CAAAzD,OAAA,aAAAyD,GAAA,CAAAxC,OAAA;QAAA;MAAA;MAAA0C,MAAA;QAAA3D,OAAA;QAAAiB,OAAA;MAAA;MAAA2C,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFxF,EAAE,CAAAyF,mBAAA;MAAAC,kBAAA,EAAAzE,GAAA;MAAA0E,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qBAAAb,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlF,EAAE,CAAAgG,eAAA;UAAFhG,EAAE,CAAAiG,cAAA,YACqT,CAAC;UADxTjG,EAAE,CAAAkG,YAAA,EACkV,CAAC;UADrVlG,EAAE,CAAAmG,YAAA,CAC0V,CAAC;QAAA;MAAA;MAAAC,MAAA;MAAAC,aAAA;MAAAC,eAAA;IAAA,EAA69D;EAAE;AACh6E;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KAHoG/C,EAAE,CAAAuG,iBAAA,CAGX7C,WAAW,EAAc,CAAC;IACzGkB,IAAI,EAAE1E,SAAS;IACfsG,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEnB,QAAQ,EAAE,aAAa;MAAEoB,IAAI,EAAE;QACvD,OAAO,EAAE,eAAe;QACxB;QACA;QACA,gBAAgB,EAAE,SAAS;QAC3B,gBAAgB,EAAE;MACtB,CAAC;MAAEL,aAAa,EAAElG,iBAAiB,CAACwG,IAAI;MAAEL,eAAe,EAAElG,uBAAuB,CAACwG,MAAM;MAAErB,UAAU,EAAE,IAAI;MAAEO,QAAQ,EAAE,8EAA8E;MAAEM,MAAM,EAAE,CAAC,62DAA62D;IAAE,CAAC;EAC5kE,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAExB,IAAI,EAAE5E,EAAE,CAACyE;EAAW,CAAC,EAAE;IAAEG,IAAI,EAAEiC,SAAS;IAAEC,UAAU,EAAE,CAAC;MACxElC,IAAI,EAAEvE;IACV,CAAC,EAAE;MACCuE,IAAI,EAAEtE,MAAM;MACZkG,IAAI,EAAE,CAAC/C,aAAa;IACxB,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAE/B,OAAO,EAAE,CAAC;MACnCkD,IAAI,EAAErE;IACV,CAAC,CAAC;IAAEoC,OAAO,EAAE,CAAC;MACViC,IAAI,EAAErE;IACV,CAAC;EAAE,CAAC;AAAA;AAChB,MAAMwG,eAAe,CAAC;EAClBzF,WAAWA,CAACqC,QAAQ,EAAE;IAClB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC5B;EACAqD,kBAAkBA,CAAA,EAAG;IACjBrG,QAAQ,CAAC,IAAI,CAACsG,MAAM,EAAE,IAAI,CAACtD,QAAQ,CAAC;EACxC;EACA;IAAS,IAAI,CAACU,IAAI,YAAA6C,wBAAA3C,CAAA;MAAA,YAAAA,CAAA,IAAwFwC,eAAe,EA7BzB/G,EAAE,CAAAwE,iBAAA,CA6ByCxE,EAAE,CAACyE,UAAU;IAAA,CAA4C;EAAE;EACtM;IAAS,IAAI,CAACC,IAAI,kBA9B8E1E,EAAE,CAAA2E,iBAAA;MAAAC,IAAA,EA8BJmC,eAAe;MAAAlC,SAAA;MAAAsC,cAAA,WAAAC,+BAAAlC,EAAA,EAAAC,GAAA,EAAAkC,QAAA;QAAA,IAAAnC,EAAA;UA9BblF,EAAE,CAAAsH,cAAA,CAAAD,QAAA,EA8BwIzG,OAAO;QAAA;QAAA,IAAAsE,EAAA;UAAA,IAAAqC,EAAA;UA9BjJvH,EAAE,CAAAwH,cAAA,CAAAD,EAAA,GAAFvH,EAAE,CAAAyH,WAAA,QAAAtC,GAAA,CAAA8B,MAAA,GAAAM,EAAA;QAAA;MAAA;MAAAhC,UAAA;MAAAC,QAAA,GAAFxF,EAAE,CAAAyF,mBAAA;MAAAC,kBAAA,EAAAvE,GAAA;MAAAwE,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAA4B,yBAAAxC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlF,EAAE,CAAAgG,eAAA,CAAA9E,GAAA;UAAFlB,EAAE,CAAAkG,YAAA,EA8BsQ,CAAC;UA9BzQlG,EAAE,CAAAiG,cAAA,YA8B0S,CAAC;UA9B7SjG,EAAE,CAAAkG,YAAA,KA8BoW,CAAC;UA9BvWlG,EAAE,CAAAmG,YAAA,CA8B0W,CAAC;UA9B7WnG,EAAE,CAAAkG,YAAA,KA8BqY,CAAC;QAAA;MAAA;MAAAG,aAAA;MAAAC,eAAA;IAAA,EAAoG;EAAE;AACllB;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KAhCoG/C,EAAE,CAAAuG,iBAAA,CAgCXQ,eAAe,EAAc,CAAC;IAC7GnC,IAAI,EAAE1E,SAAS;IACfsG,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,4CAA4C;MAAEH,eAAe,EAAElG,uBAAuB,CAACwG,MAAM;MAAEP,aAAa,EAAElG,iBAAiB,CAACwG,IAAI;MAAEpB,UAAU,EAAE,IAAI;MAAEO,QAAQ,EAAE;IAA2M,CAAC;EACrY,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAElB,IAAI,EAAE5E,EAAE,CAACyE;EAAW,CAAC,CAAC,EAAkB;IAAEwC,MAAM,EAAE,CAAC;MACxErC,IAAI,EAAEpE,eAAe;MACrBgG,IAAI,EAAE,CAAC5F,OAAO,EAAE;QAAE+G,WAAW,EAAE;MAAK,CAAC;IACzC,CAAC;EAAE,CAAC;AAAA;AAChB;AACA;AACA;AACA;AACA,MAAMC,yBAAyB,CAAC;EAC5B;IAAS,IAAI,CAACvD,IAAI,YAAAwD,kCAAAtD,CAAA;MAAA,YAAAA,CAAA,IAAwFqD,yBAAyB;IAAA,CAAmD;EAAE;EACxL;IAAS,IAAI,CAACE,IAAI,kBA7C8E9H,EAAE,CAAA+H,iBAAA;MAAAnD,IAAA,EA6CJgD,yBAAyB;MAAA/C,SAAA;MAAAC,SAAA;MAAAS,UAAA;IAAA,EAAkI;EAAE;AAC/P;AACA;EAAA,QAAAxC,SAAA,oBAAAA,SAAA,KA/CoG/C,EAAE,CAAAuG,iBAAA,CA+CXqB,yBAAyB,EAAc,CAAC;IACvHhD,IAAI,EAAEnE,SAAS;IACf+F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,oCAAoC;MAC9CC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAkB,CAAC;MACpCnB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAMyC,6BAA6B,CAAC;EAChC;IAAS,IAAI,CAAC3D,IAAI,YAAA4D,sCAAA1D,CAAA;MAAA,YAAAA,CAAA,IAAwFyD,6BAA6B;IAAA,CAAmD;EAAE;EAC5L;IAAS,IAAI,CAACF,IAAI,kBA7D8E9H,EAAE,CAAA+H,iBAAA;MAAAnD,IAAA,EA6DJoD,6BAA6B;MAAAnD,SAAA;MAAAC,SAAA;MAAAS,UAAA;IAAA,EAAyH;EAAE;AAC1P;AACA;EAAA,QAAAxC,SAAA,oBAAAA,SAAA,KA/DoG/C,EAAE,CAAAuG,iBAAA,CA+DXyB,6BAA6B,EAAc,CAAC;IAC3HpD,IAAI,EAAEnE,SAAS;IACf+F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAuB,CAAC;MACzCnB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;AACV;AACA;AACA;AACA;AACA,MAAM2C,6BAA6B,CAAC;EAChC;IAAS,IAAI,CAAC7D,IAAI,YAAA8D,sCAAA5D,CAAA;MAAA,YAAAA,CAAA,IAAwF2D,6BAA6B;IAAA,CAAmD;EAAE;EAC5L;IAAS,IAAI,CAACJ,IAAI,kBA7E8E9H,EAAE,CAAA+H,iBAAA;MAAAnD,IAAA,EA6EJsD,6BAA6B;MAAArD,SAAA;MAAAC,SAAA;MAAAS,UAAA;IAAA,EAAyH;EAAE;AAC1P;AACA;EAAA,QAAAxC,SAAA,oBAAAA,SAAA,KA/EoG/C,EAAE,CAAAuG,iBAAA,CA+EX2B,6BAA6B,EAAc,CAAC;IAC3HtD,IAAI,EAAEnE,SAAS;IACf+F,IAAI,EAAE,CAAC;MACCC,QAAQ,EAAE,sBAAsB;MAChCC,IAAI,EAAE;QAAE,OAAO,EAAE;MAAuB,CAAC;MACzCnB,UAAU,EAAE;IAChB,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA,MAAM6C,mBAAmB,GAAG,+BAA+B;AAC3D;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACb/G,WAAWA,CAAA,EAAG;IACV,IAAI,CAACgH,KAAK,GAAG,CAAC;IACd,IAAI,CAACzE,QAAQ,GAAG,CAAC;EACrB;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACI0E,IAAIA,CAACC,UAAU,EAAE1G,OAAO,EAAE2G,IAAI,EAAEC,SAAS,EAAE;IACvC,IAAI,CAACC,WAAW,GAAGC,cAAc,CAACJ,UAAU,CAAC;IAC7C,IAAI,CAACF,KAAK,GAAGxG,OAAO,CAACL,QAAQ;IAC7B,IAAI,CAACoC,QAAQ,GAAG/B,OAAO,CAACJ,OAAO;IAC/B,IAAI,CAACmH,KAAK,GAAGJ,IAAI;IACjB,IAAI,CAACK,UAAU,GAAGJ,SAAS;EAC/B;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIK,eAAeA,CAACC,WAAW,EAAEC,cAAc,EAAE;IACzC;IACA;IACA;IACA;IACA;IACA,OAAO,IAAID,WAAW,QAAQ,IAAI,CAACL,WAAW,MAAMM,cAAc,IAAI;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIC,eAAeA,CAACC,QAAQ,EAAEC,MAAM,EAAE;IAC9B;IACA;IACA,OAAOA,MAAM,KAAK,CAAC,GAAG,GAAG,GAAGC,IAAI,CAAC,IAAIF,QAAQ,MAAM,IAAI,CAACR,WAAW,OAAOS,MAAM,EAAE,CAAC;EACvF;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,WAAWA,CAACH,QAAQ,EAAEI,IAAI,EAAE;IACxB,OAAO,IAAIJ,QAAQ,MAAMI,IAAI,QAAQA,IAAI,GAAG,CAAC,MAAM,IAAI,CAACZ,WAAW,GAAG;EAC1E;EACA;AACJ;AACA;AACA;AACA;AACA;EACIa,QAAQA,CAACjH,IAAI,EAAEf,QAAQ,EAAEiI,QAAQ,EAAE;IAC/B;IACA,IAAIC,mBAAmB,GAAG,GAAG,GAAG,IAAI,CAACb,KAAK;IAC1C;IACA;IACA,IAAIc,0BAA0B,GAAG,CAAC,IAAI,CAACd,KAAK,GAAG,CAAC,IAAI,IAAI,CAACA,KAAK;IAC9D,IAAI,CAACe,YAAY,CAACrH,IAAI,EAAEkH,QAAQ,EAAEC,mBAAmB,EAAEC,0BAA0B,CAAC;IAClF,IAAI,CAACE,YAAY,CAACtH,IAAI,EAAEf,QAAQ,EAAEkI,mBAAmB,EAAEC,0BAA0B,CAAC;EACtF;EACA;EACAC,YAAYA,CAACrH,IAAI,EAAEkH,QAAQ,EAAEK,YAAY,EAAEC,WAAW,EAAE;IACpD;IACA,IAAIC,aAAa,GAAG,IAAI,CAACjB,eAAe,CAACe,YAAY,EAAEC,WAAW,CAAC;IACnE;IACA;IACA,IAAIE,IAAI,GAAG,IAAI,CAACnB,UAAU,KAAK,KAAK,GAAG,OAAO,GAAG,MAAM;IACvDvG,IAAI,CAAC0B,SAAS,CAACgG,IAAI,EAAE,IAAI,CAACf,eAAe,CAACc,aAAa,EAAEP,QAAQ,CAAC,CAAC;IACnElH,IAAI,CAAC0B,SAAS,CAAC,OAAO,EAAEoF,IAAI,CAAC,IAAI,CAACC,WAAW,CAACU,aAAa,EAAEzH,IAAI,CAACI,OAAO,CAAC,CAAC,CAAC;EAChF;EACA;AACJ;AACA;EACIuH,aAAaA,CAAA,EAAG;IACZ,OAAO,GAAG,IAAI,CAACvB,WAAW,OAAO,IAAI,CAAC9E,QAAQ,OAAO;EACzD;EACA;AACJ;AACA;AACA;EACIsG,WAAWA,CAACC,UAAU,EAAE;IACpB,OAAO,GAAG,IAAI,CAACvG,QAAQ,MAAM,IAAI,CAACyF,WAAW,CAACc,UAAU,EAAE,CAAC,CAAC,EAAE;EAClE;EACA;AACJ;AACA;AACA;AACA;EACIC,iBAAiBA,CAAA,EAAG;IAChB,OAAO,IAAI;EACf;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,eAAe,SAASjC,UAAU,CAAC;EACrC/G,WAAWA,CAACiJ,cAAc,EAAE;IACxB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,cAAc,GAAGA,cAAc;EACxC;EACAhC,IAAIA,CAACC,UAAU,EAAE1G,OAAO,EAAE2G,IAAI,EAAEC,SAAS,EAAE;IACvC,KAAK,CAACH,IAAI,CAACC,UAAU,EAAE1G,OAAO,EAAE2G,IAAI,EAAEC,SAAS,CAAC;IAChD,IAAI,CAAC6B,cAAc,GAAG3B,cAAc,CAAC,IAAI,CAAC2B,cAAc,CAAC;IACzD,IAAI,CAACnC,mBAAmB,CAACoC,IAAI,CAAC,IAAI,CAACD,cAAc,CAAC,KAC7C,OAAOxH,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MACjD,MAAMC,KAAK,CAAC,kBAAkB,IAAI,CAACuH,cAAc,qBAAqB,CAAC;IAC3E;EACJ;EACAV,YAAYA,CAACtH,IAAI,EAAEf,QAAQ,EAAE;IACzBe,IAAI,CAAC0B,SAAS,CAAC,KAAK,EAAE,IAAI,CAACiF,eAAe,CAAC,IAAI,CAACqB,cAAc,EAAE/I,QAAQ,CAAC,CAAC;IAC1Ee,IAAI,CAAC0B,SAAS,CAAC,QAAQ,EAAEoF,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAACiB,cAAc,EAAEhI,IAAI,CAACb,OAAO,CAAC,CAAC,CAAC;EACvF;EACA2I,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CAAC,QAAQ,EAAEhB,IAAI,CAAC,GAAG,IAAI,CAACc,WAAW,CAAC,IAAI,CAACI,cAAc,CAAC,MAAM,IAAI,CAACL,aAAa,CAAC,CAAC,EAAE,CAAC,CAAC;EACjG;EACAO,KAAKA,CAACC,IAAI,EAAE;IACRA,IAAI,CAACC,aAAa,CAAC,CAAC,QAAQ,EAAE,IAAI,CAAC,CAAC;IACpC,IAAID,IAAI,CAACE,MAAM,EAAE;MACbF,IAAI,CAACE,MAAM,CAACC,OAAO,CAACtI,IAAI,IAAI;QACxBA,IAAI,CAAC0B,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;QAC3B1B,IAAI,CAAC0B,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC;MAClC,CAAC,CAAC;IACN;EACJ;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6G,eAAe,SAASzC,UAAU,CAAC;EACrC/G,WAAWA,CAACyC,KAAK,EAAE;IACf,KAAK,CAAC,CAAC;IACP,IAAI,CAACgH,WAAW,CAAChH,KAAK,CAAC;EAC3B;EACA8F,YAAYA,CAACtH,IAAI,EAAEf,QAAQ,EAAEsI,YAAY,EAAEC,WAAW,EAAE;IACpD,IAAIiB,oBAAoB,GAAGlB,YAAY,GAAG,IAAI,CAACmB,cAAc;IAC7D,IAAI,CAACC,cAAc,GAAG,IAAI,CAACnC,eAAe,CAACiC,oBAAoB,EAAEjB,WAAW,CAAC;IAC7E;IACA;IACA;IACAxH,IAAI,CAAC0B,SAAS,CAAC,WAAW,EAAE,IAAI,CAACiF,eAAe,CAAC,IAAI,CAACgC,cAAc,EAAE1J,QAAQ,CAAC,CAAC;IAChFe,IAAI,CAAC0B,SAAS,CAAC,YAAY,EAAEoF,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC,IAAI,CAAC4B,cAAc,EAAE3I,IAAI,CAACb,OAAO,CAAC,CAAC,CAAC;EAC3F;EACA2I,iBAAiBA,CAAA,EAAG;IAChB,OAAO,CACH,eAAe,EACfhB,IAAI,CAAC,GAAG,IAAI,CAACc,WAAW,CAAC,IAAI,CAACe,cAAc,CAAC,MAAM,IAAI,CAAChB,aAAa,CAAC,CAAC,EAAE,CAAC,CAC7E;EACL;EACAO,KAAKA,CAACC,IAAI,EAAE;IACRA,IAAI,CAACC,aAAa,CAAC,CAAC,eAAe,EAAE,IAAI,CAAC,CAAC;IAC3CD,IAAI,CAACE,MAAM,CAACC,OAAO,CAACtI,IAAI,IAAI;MACxBA,IAAI,CAAC0B,SAAS,CAAC,WAAW,EAAE,IAAI,CAAC;MACjC1B,IAAI,CAAC0B,SAAS,CAAC,YAAY,EAAE,IAAI,CAAC;IACtC,CAAC,CAAC;EACN;EACA8G,WAAWA,CAAChH,KAAK,EAAE;IACf,MAAMoH,UAAU,GAAGpH,KAAK,CAACqH,KAAK,CAAC,GAAG,CAAC;IACnC,IAAID,UAAU,CAAC/I,MAAM,KAAK,CAAC,KAAK,OAAOW,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC5E,MAAMC,KAAK,CAAC,uDAAuDe,KAAK,GAAG,CAAC;IAChF;IACA,IAAI,CAACkH,cAAc,GAAGI,UAAU,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC,GAAGE,UAAU,CAACF,UAAU,CAAC,CAAC,CAAC,CAAC;EAC/E;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMG,aAAa,SAASjD,UAAU,CAAC;EACnCwB,YAAYA,CAACtH,IAAI,EAAEf,QAAQ,EAAE;IACzB;IACA,IAAIwJ,oBAAoB,GAAG,GAAG,GAAG,IAAI,CAACnH,QAAQ;IAC9C;IACA,IAAI0H,mBAAmB,GAAG,CAAC,IAAI,CAACjD,KAAK,GAAG,CAAC,IAAI,IAAI,CAACA,KAAK;IACvD;IACA,IAAI4C,cAAc,GAAG,IAAI,CAACnC,eAAe,CAACiC,oBAAoB,EAAEO,mBAAmB,CAAC;IACpFhJ,IAAI,CAAC0B,SAAS,CAAC,KAAK,EAAE,IAAI,CAACiF,eAAe,CAACgC,cAAc,EAAE1J,QAAQ,CAAC,CAAC;IACrEe,IAAI,CAAC0B,SAAS,CAAC,QAAQ,EAAEoF,IAAI,CAAC,IAAI,CAACC,WAAW,CAAC4B,cAAc,EAAE3I,IAAI,CAACb,OAAO,CAAC,CAAC,CAAC;EAClF;EACA+I,KAAKA,CAACC,IAAI,EAAE;IACR,IAAIA,IAAI,CAACE,MAAM,EAAE;MACbF,IAAI,CAACE,MAAM,CAACC,OAAO,CAACtI,IAAI,IAAI;QACxBA,IAAI,CAAC0B,SAAS,CAAC,KAAK,EAAE,IAAI,CAAC;QAC3B1B,IAAI,CAAC0B,SAAS,CAAC,QAAQ,EAAE,IAAI,CAAC;MAClC,CAAC,CAAC;IACN;EACJ;AACJ;AACA;AACA,SAASoF,IAAIA,CAACmC,GAAG,EAAE;EACf,OAAO,QAAQA,GAAG,GAAG;AACzB;AACA;AACA,SAAS5C,cAAcA,CAAC7E,KAAK,EAAE;EAC3B,OAAOA,KAAK,CAAC0H,KAAK,CAAC,eAAe,CAAC,GAAG1H,KAAK,GAAG,GAAGA,KAAK,IAAI;AAC9D;;AAEA;AACA;AACA;AACA,MAAM2H,YAAY,GAAG,KAAK;AAC1B,MAAMC,WAAW,CAAC;EACdrK,WAAWA,CAACqC,QAAQ,EAAEiI,IAAI,EAAE;IACxB,IAAI,CAACjI,QAAQ,GAAGA,QAAQ;IACxB,IAAI,CAACiI,IAAI,GAAGA,IAAI;IAChB;IACA,IAAI,CAACC,OAAO,GAAG,KAAK;EACxB;EACA;EACA,IAAIpD,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACI,KAAK;EACrB;EACA,IAAIJ,IAAIA,CAAC1E,KAAK,EAAE;IACZ,IAAI,CAAC8E,KAAK,GAAGjH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAED,IAAI,CAACoC,KAAK,CAACjD,oBAAoB,CAACgD,KAAK,CAAC,CAAC,CAAC;EACrE;EACA;EACA,IAAIyE,UAAUA,CAAA,EAAG;IACb,OAAO,IAAI,CAACqD,OAAO;EACvB;EACA,IAAIrD,UAAUA,CAACzE,KAAK,EAAE;IAClB,IAAI,CAAC8H,OAAO,GAAG,GAAG9H,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,EAAE;EAClD;EACA;EACA,IAAI+H,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACC,UAAU;EAC1B;EACA,IAAID,SAASA,CAAC/H,KAAK,EAAE;IACjB,MAAMiI,QAAQ,GAAG,GAAGjI,KAAK,IAAI,IAAI,GAAG,EAAE,GAAGA,KAAK,EAAE;IAChD,IAAIiI,QAAQ,KAAK,IAAI,CAACD,UAAU,EAAE;MAC9B,IAAI,CAACA,UAAU,GAAGC,QAAQ;MAC1B,IAAI,CAACC,cAAc,CAAC,IAAI,CAACF,UAAU,CAAC;IACxC;EACJ;EACAG,QAAQA,CAAA,EAAG;IACP,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACC,eAAe,CAAC,CAAC;EAC1B;EACA;AACJ;AACA;AACA;EACIC,qBAAqBA,CAAA,EAAG;IACpB,IAAI,CAACC,YAAY,CAAC,CAAC;EACvB;EACA;EACAH,UAAUA,CAAA,EAAG;IACT,IAAI,CAAC,IAAI,CAAC1D,IAAI,KAAK,OAAO1F,SAAS,KAAK,WAAW,IAAIA,SAAS,CAAC,EAAE;MAC/D,MAAMC,KAAK,CAAC,iDAAiD,GAAG,mCAAmC,CAAC;IACxG;EACJ;EACA;EACAoJ,eAAeA,CAAA,EAAG;IACd,IAAI,CAAC,IAAI,CAACL,UAAU,EAAE;MAClB,IAAI,CAACE,cAAc,CAAC,KAAK,CAAC;IAC9B;EACJ;EACA;EACAA,cAAcA,CAACH,SAAS,EAAE;IACtB,IAAI,IAAI,CAACS,WAAW,EAAE;MAClB,IAAI,CAACA,WAAW,CAAC9B,KAAK,CAAC,IAAI,CAAC;IAChC;IACA,IAAIqB,SAAS,KAAKJ,YAAY,EAAE;MAC5B,IAAI,CAACa,WAAW,GAAG,IAAIjB,aAAa,CAAC,CAAC;IAC1C,CAAC,MACI,IAAIQ,SAAS,IAAIA,SAAS,CAAC3I,OAAO,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,EAAE;MAC/C,IAAI,CAACoJ,WAAW,GAAG,IAAIzB,eAAe,CAACgB,SAAS,CAAC;IACrD,CAAC,MACI;MACD,IAAI,CAACS,WAAW,GAAG,IAAIjC,eAAe,CAACwB,SAAS,CAAC;IACrD;EACJ;EACA;EACAQ,YAAYA,CAAA,EAAG;IACX,IAAI,CAAC,IAAI,CAACE,gBAAgB,EAAE;MACxB,IAAI,CAACA,gBAAgB,GAAG,IAAInL,eAAe,CAAC,CAAC;IACjD;IACA,MAAMS,OAAO,GAAG,IAAI,CAAC0K,gBAAgB;IACrC,MAAMvK,KAAK,GAAG,IAAI,CAAC2I,MAAM,CAAC6B,MAAM,CAAClK,IAAI,IAAI,CAACA,IAAI,CAACqB,SAAS,IAAIrB,IAAI,CAACqB,SAAS,KAAK,IAAI,CAAC;IACpF,MAAM8E,SAAS,GAAG,IAAI,CAACkD,IAAI,GAAG,IAAI,CAACA,IAAI,CAAC7H,KAAK,GAAG,KAAK;IACrD,IAAI,CAACyI,gBAAgB,CAACzK,MAAM,CAAC,IAAI,CAAC0G,IAAI,EAAExG,KAAK,CAAC;IAC9C,IAAI,CAACsK,WAAW,CAAChE,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE1G,OAAO,EAAE,IAAI,CAAC2G,IAAI,EAAEC,SAAS,CAAC;IACrEzG,KAAK,CAAC4I,OAAO,CAAC,CAACtI,IAAI,EAAEmK,KAAK,KAAK;MAC3B,MAAMC,GAAG,GAAG7K,OAAO,CAACO,SAAS,CAACqK,KAAK,CAAC;MACpC,IAAI,CAACH,WAAW,CAAC/C,QAAQ,CAACjH,IAAI,EAAEoK,GAAG,CAACpJ,GAAG,EAAEoJ,GAAG,CAACnJ,GAAG,CAAC;IACrD,CAAC,CAAC;IACF,IAAI,CAACmH,aAAa,CAAC,IAAI,CAAC4B,WAAW,CAAClC,iBAAiB,CAAC,CAAC,CAAC;EAC5D;EACA;EACAM,aAAaA,CAACvG,KAAK,EAAE;IACjB,IAAIA,KAAK,EAAE;MACP,IAAI,CAACT,QAAQ,CAACQ,aAAa,CAACC,KAAK,CAACA,KAAK,CAAC,CAAC,CAAC,CAAC,GAAGA,KAAK,CAAC,CAAC,CAAC;IAC1D;EACJ;EACA;IAAS,IAAI,CAACC,IAAI,YAAAuI,oBAAArI,CAAA;MAAA,YAAAA,CAAA,IAAwFoH,WAAW,EAxZrB3L,EAAE,CAAAwE,iBAAA,CAwZqCxE,EAAE,CAACyE,UAAU,GAxZpDzE,EAAE,CAAAwE,iBAAA,CAwZ+DxD,EAAE,CAAC6L,cAAc;IAAA,CAA4D;EAAE;EAChP;IAAS,IAAI,CAACnI,IAAI,kBAzZ8E1E,EAAE,CAAA2E,iBAAA;MAAAC,IAAA,EAyZJ+G,WAAW;MAAA9G,SAAA;MAAAsC,cAAA,WAAA2F,2BAAA5H,EAAA,EAAAC,GAAA,EAAAkC,QAAA;QAAA,IAAAnC,EAAA;UAzZTlF,EAAE,CAAAsH,cAAA,CAAAD,QAAA,EA8Z5C3D,WAAW;QAAA;QAAA,IAAAwB,EAAA;UAAA,IAAAqC,EAAA;UA9Z+BvH,EAAE,CAAAwH,cAAA,CAAAD,EAAA,GAAFvH,EAAE,CAAAyH,WAAA,QAAAtC,GAAA,CAAAyF,MAAA,GAAArD,EAAA;QAAA;MAAA;MAAAzC,SAAA;MAAAC,QAAA;MAAAC,YAAA,WAAA+H,yBAAA7H,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlF,EAAE,CAAAoF,WAAA,SAAAD,GAAA,CAAAsD,IAAA;QAAA;MAAA;MAAApD,MAAA;QAAAoD,IAAA;QAAAD,UAAA;QAAAsD,SAAA;MAAA;MAAAxG,QAAA;MAAAC,UAAA;MAAAC,QAAA,GAAFxF,EAAE,CAAAgN,kBAAA,CAyZ+N,CACzT;QACIC,OAAO,EAAExJ,aAAa;QACtByJ,WAAW,EAAEvB;MACjB,CAAC,CACJ,GA9Z2F3L,EAAE,CAAAyF,mBAAA;MAAAC,kBAAA,EAAAzE,GAAA;MAAA0E,KAAA;MAAAC,IAAA;MAAAE,QAAA,WAAAqH,qBAAAjI,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAAFlF,EAAE,CAAAgG,eAAA;UAAFhG,EAAE,CAAAiG,cAAA,SA8Z+C,CAAC;UA9ZlDjG,EAAE,CAAAkG,YAAA,EA8Z4E,CAAC;UA9Z/ElG,EAAE,CAAAmG,YAAA,CA8ZoF,CAAC;QAAA;MAAA;MAAAC,MAAA,GAAAhF,GAAA;MAAAiF,aAAA;MAAAC,eAAA;IAAA,EAA29D;EAAE;AACxpE;AACA;EAAA,QAAAvD,SAAA,oBAAAA,SAAA,KAhaoG/C,EAAE,CAAAuG,iBAAA,CAgaXoF,WAAW,EAAc,CAAC;IACzG/G,IAAI,EAAE1E,SAAS;IACfsG,IAAI,EAAE,CAAC;MAAEC,QAAQ,EAAE,eAAe;MAAEnB,QAAQ,EAAE,aAAa;MAAEoB,IAAI,EAAE;QACvD,OAAO,EAAE,eAAe;QACxB;QACA;QACA,aAAa,EAAE;MACnB,CAAC;MAAE0G,SAAS,EAAE,CACV;QACIH,OAAO,EAAExJ,aAAa;QACtByJ,WAAW,EAAEvB;MACjB,CAAC,CACJ;MAAErF,eAAe,EAAElG,uBAAuB,CAACwG,MAAM;MAAEP,aAAa,EAAElG,iBAAiB,CAACwG,IAAI;MAAEpB,UAAU,EAAE,IAAI;MAAEO,QAAQ,EAAE,4CAA4C;MAAEM,MAAM,EAAE,CAAC,62DAA62D;IAAE,CAAC;EAC1iE,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAExB,IAAI,EAAE5E,EAAE,CAACyE;EAAW,CAAC,EAAE;IAAEG,IAAI,EAAE5D,EAAE,CAAC6L,cAAc;IAAE/F,UAAU,EAAE,CAAC;MAChFlC,IAAI,EAAEvE;IACV,CAAC;EAAE,CAAC,CAAC,EAAkB;IAAEuK,MAAM,EAAE,CAAC;MAClChG,IAAI,EAAEpE,eAAe;MACrBgG,IAAI,EAAE,CAAC9C,WAAW,EAAE;QAAEiE,WAAW,EAAE;MAAK,CAAC;IAC7C,CAAC,CAAC;IAAEc,IAAI,EAAE,CAAC;MACP7D,IAAI,EAAErE;IACV,CAAC,CAAC;IAAEiI,UAAU,EAAE,CAAC;MACb5D,IAAI,EAAErE;IACV,CAAC,CAAC;IAAEuL,SAAS,EAAE,CAAC;MACZlH,IAAI,EAAErE;IACV,CAAC;EAAE,CAAC;AAAA;AAEhB,MAAM8M,iBAAiB,CAAC;EACpB;IAAS,IAAI,CAAChJ,IAAI,YAAAiJ,0BAAA/I,CAAA;MAAA,YAAAA,CAAA,IAAwF8I,iBAAiB;IAAA,CAAkD;EAAE;EAC/K;IAAS,IAAI,CAACE,IAAI,kBA5b8EvN,EAAE,CAAAwN,gBAAA;MAAA5I,IAAA,EA4bSyI;IAAiB,EAcvF;EAAE;EACvC;IAAS,IAAI,CAACI,IAAI,kBA3c8EzN,EAAE,CAAA0N,gBAAA;MAAAC,OAAA,GA2csC9M,aAAa,EAC7IC,eAAe,EAAED,aAAa,EAC9BC,eAAe;IAAA,EAAI;EAAE;AACjC;AACA;EAAA,QAAAiC,SAAA,oBAAAA,SAAA,KA/coG/C,EAAE,CAAAuG,iBAAA,CA+cX8G,iBAAiB,EAAc,CAAC;IAC/GzI,IAAI,EAAElE,QAAQ;IACd8F,IAAI,EAAE,CAAC;MACCmH,OAAO,EAAE,CACL9M,aAAa,EACbC,eAAe,EACf6K,WAAW,EACXjI,WAAW,EACXqD,eAAe,EACfiB,6BAA6B,EAC7BE,6BAA6B,EAC7BN,yBAAyB,CAC5B;MACDgG,OAAO,EAAE,CACLjC,WAAW,EACXjI,WAAW,EACXqD,eAAe,EACflG,aAAa,EACbC,eAAe,EACfkH,6BAA6B,EAC7BE,6BAA6B,EAC7BN,yBAAyB;IAEjC,CAAC;EACT,CAAC,CAAC;AAAA;;AAEV;AACA,MAAMiG,gBAAgB,GAAGxM,eAAe;;AAExC;AACA;AACA;;AAEA,SAASuG,yBAAyB,EAAE+D,WAAW,EAAE0B,iBAAiB,EAAE3J,WAAW,EAAEwE,6BAA6B,EAAEF,6BAA6B,EAAEjB,eAAe,EAAE8G,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}