{"ast": null, "code": "// User models\nexport * from './user.model';\n// Reward models\nexport * from './reward.model';\n// Validation models\nexport * from './validation.model';\n// Partner models\nexport * from './partner.model';\n// Provider models\nexport * from './provider.model';\n// Admin models\nexport * from './admin.model';\nexport var NotificationType;\n(function (NotificationType) {\n  NotificationType[\"POINTS_EARNED\"] = \"points_earned\";\n  NotificationType[\"POINTS_SPENT\"] = \"points_spent\";\n  NotificationType[\"VALIDATION_APPROVED\"] = \"validation_approved\";\n  NotificationType[\"VALIDATION_REJECTED\"] = \"validation_rejected\";\n  NotificationType[\"REWARD_AVAILABLE\"] = \"reward_available\";\n  NotificationType[\"SYSTEM_ANNOUNCEMENT\"] = \"system_announcement\";\n})(NotificationType || (NotificationType = {}));", "map": {"version": 3, "names": ["NotificationType"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\models\\index.ts"], "sourcesContent": ["// User models\nexport * from './user.model';\n\n// Reward models\nexport * from './reward.model';\n\n// Validation models\nexport * from './validation.model';\n\n// Partner models\nexport * from './partner.model';\n\n// Provider models\nexport * from './provider.model';\n\n// Admin models\nexport * from './admin.model';\n\n// Common interfaces\nexport interface ApiResponse<T> {\n  success: boolean;\n  data?: T;\n  error?: string;\n  message?: string;\n}\n\nexport interface PaginatedResponse<T> {\n  items: T[];\n  total: number;\n  page: number;\n  limit: number;\n  hasNext: boolean;\n  hasPrevious: boolean;\n}\n\nexport interface FilterOptions {\n  search?: string;\n  category?: string;\n  city?: 'Monastir' | 'Sousse';\n  dateFrom?: Date;\n  dateTo?: Date;\n  status?: string;\n  sortBy?: string;\n  sortOrder?: 'asc' | 'desc';\n}\n\nexport interface NotificationData {\n  id: string;\n  userId: string;\n  title: string;\n  message: string;\n  type: NotificationType;\n  isRead: boolean;\n  createdAt: Date;\n  data?: any;\n}\n\nexport enum NotificationType {\n  POINTS_EARNED = 'points_earned',\n  POINTS_SPENT = 'points_spent',\n  VALIDATION_APPROVED = 'validation_approved',\n  VALIDATION_REJECTED = 'validation_rejected',\n  REWARD_AVAILABLE = 'reward_available',\n  SYSTEM_ANNOUNCEMENT = 'system_announcement'\n}\n\nexport interface AppConfig {\n  firebase: {\n    apiKey: string;\n    authDomain: string;\n    projectId: string;\n    storageBucket: string;\n    messagingSenderId: string;\n    appId: string;\n  };\n  app: {\n    name: string;\n    version: string;\n    environment: 'development' | 'staging' | 'production';\n  };\n  features: {\n    pushNotifications: boolean;\n    geolocation: boolean;\n    darkMode: boolean;\n    multiLanguage: boolean;\n  };\n}\n"], "mappings": "AAAA;AACA,cAAc,cAAc;AAE5B;AACA,cAAc,gBAAgB;AAE9B;AACA,cAAc,oBAAoB;AAElC;AACA,cAAc,iBAAiB;AAE/B;AACA,cAAc,kBAAkB;AAEhC;AACA,cAAc,eAAe;AAyC7B,WAAYA,gBAOX;AAPD,WAAYA,gBAAgB;EAC1BA,gBAAA,mCAA+B;EAC/BA,gBAAA,iCAA6B;EAC7BA,gBAAA,+CAA2C;EAC3CA,gBAAA,+CAA2C;EAC3CA,gBAAA,yCAAqC;EACrCA,gBAAA,+CAA2C;AAC7C,CAAC,EAPWA,gBAAgB,KAAhBA,gBAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}