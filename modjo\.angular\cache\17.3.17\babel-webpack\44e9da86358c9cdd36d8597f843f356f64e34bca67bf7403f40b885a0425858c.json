{"ast": null, "code": "/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, NgZone, ApplicationRef, makeEnvironmentProviders, PLATFORM_ID, APP_INITIALIZER, Injector, NgModule } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { defer, throwError, fromEvent, of, concat, Subject, NEVER, merge } from 'rxjs';\nimport { map, filter, switchMap, publish, take, tap, delay } from 'rxjs/operators';\nconst ERR_SW_NOT_SUPPORTED = 'Service workers are disabled or not supported by this browser';\nfunction errorObservable(message) {\n  return defer(() => throwError(new Error(message)));\n}\n/**\n * @publicApi\n */\nclass NgswCommChannel {\n  constructor(serviceWorker) {\n    this.serviceWorker = serviceWorker;\n    if (!serviceWorker) {\n      this.worker = this.events = this.registration = errorObservable(ERR_SW_NOT_SUPPORTED);\n    } else {\n      const controllerChangeEvents = fromEvent(serviceWorker, 'controllerchange');\n      const controllerChanges = controllerChangeEvents.pipe(map(() => serviceWorker.controller));\n      const currentController = defer(() => of(serviceWorker.controller));\n      const controllerWithChanges = concat(currentController, controllerChanges);\n      this.worker = controllerWithChanges.pipe(filter(c => !!c));\n      this.registration = this.worker.pipe(switchMap(() => serviceWorker.getRegistration()));\n      const rawEvents = fromEvent(serviceWorker, 'message');\n      const rawEventPayload = rawEvents.pipe(map(event => event.data));\n      const eventsUnconnected = rawEventPayload.pipe(filter(event => event && event.type));\n      const events = eventsUnconnected.pipe(publish());\n      events.connect();\n      this.events = events;\n    }\n  }\n  postMessage(action, payload) {\n    return this.worker.pipe(take(1), tap(sw => {\n      sw.postMessage({\n        action,\n        ...payload\n      });\n    })).toPromise().then(() => undefined);\n  }\n  postMessageWithOperation(type, payload, operationNonce) {\n    const waitForOperationCompleted = this.waitForOperationCompleted(operationNonce);\n    const postMessage = this.postMessage(type, payload);\n    return Promise.all([postMessage, waitForOperationCompleted]).then(([, result]) => result);\n  }\n  generateNonce() {\n    return Math.round(Math.random() * 10000000);\n  }\n  eventsOfType(type) {\n    let filterFn;\n    if (typeof type === 'string') {\n      filterFn = event => event.type === type;\n    } else {\n      filterFn = event => type.includes(event.type);\n    }\n    return this.events.pipe(filter(filterFn));\n  }\n  nextEventOfType(type) {\n    return this.eventsOfType(type).pipe(take(1));\n  }\n  waitForOperationCompleted(nonce) {\n    return this.eventsOfType('OPERATION_COMPLETED').pipe(filter(event => event.nonce === nonce), take(1), map(event => {\n      if (event.result !== undefined) {\n        return event.result;\n      }\n      throw new Error(event.error);\n    })).toPromise();\n  }\n  get isEnabled() {\n    return !!this.serviceWorker;\n  }\n}\n\n/**\n * Subscribe and listen to\n * [Web Push\n * Notifications](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices) through\n * Angular Service Worker.\n *\n * @usageNotes\n *\n * You can inject a `SwPush` instance into any component or service\n * as a dependency.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"inject-sw-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * To subscribe, call `SwPush.requestSubscription()`, which asks the user for permission.\n * The call returns a `Promise` with a new\n * [`PushSubscription`](https://developer.mozilla.org/en-US/docs/Web/API/PushSubscription)\n * instance.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * A request is rejected if the user denies permission, or if the browser\n * blocks or does not support the Push API or ServiceWorkers.\n * Check `SwPush.isEnabled` to confirm status.\n *\n * Invoke Push Notifications by pushing a message with the following payload.\n *\n * ```ts\n * {\n *   \"notification\": {\n *     \"actions\": NotificationAction[],\n *     \"badge\": USVString,\n *     \"body\": DOMString,\n *     \"data\": any,\n *     \"dir\": \"auto\"|\"ltr\"|\"rtl\",\n *     \"icon\": USVString,\n *     \"image\": USVString,\n *     \"lang\": DOMString,\n *     \"renotify\": boolean,\n *     \"requireInteraction\": boolean,\n *     \"silent\": boolean,\n *     \"tag\": DOMString,\n *     \"timestamp\": DOMTimeStamp,\n *     \"title\": DOMString,\n *     \"vibrate\": number[]\n *   }\n * }\n * ```\n *\n * Only `title` is required. See `Notification`\n * [instance\n * properties](https://developer.mozilla.org/en-US/docs/Web/API/Notification#Instance_properties).\n *\n * While the subscription is active, Service Worker listens for\n * [PushEvent](https://developer.mozilla.org/en-US/docs/Web/API/PushEvent)\n * occurrences and creates\n * [Notification](https://developer.mozilla.org/en-US/docs/Web/API/Notification)\n * instances in response.\n *\n * Unsubscribe using `SwPush.unsubscribe()`.\n *\n * An application can subscribe to `SwPush.notificationClicks` observable to be notified when a user\n * clicks on a notification. For example:\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-notification-clicks\"\n * header=\"app.component.ts\"></code-example>\n *\n * You can read more on handling notification clicks in the [Service worker notifications\n * guide](guide/service-worker-notifications).\n *\n * @see [Push Notifications](https://developers.google.com/web/fundamentals/codelabs/push-notifications/)\n * @see [Angular Push Notifications](https://blog.angular-university.io/angular-push-notifications/)\n * @see [MDN: Push API](https://developer.mozilla.org/en-US/docs/Web/API/Push_API)\n * @see [MDN: Notifications API](https://developer.mozilla.org/en-US/docs/Web/API/Notifications_API)\n * @see [MDN: Web Push API Notifications best practices](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices)\n *\n * @publicApi\n */\nclass SwPush {\n  /**\n   * True if the Service Worker is enabled (supported by the browser and enabled via\n   * `ServiceWorkerModule`).\n   */\n  get isEnabled() {\n    return this.sw.isEnabled;\n  }\n  constructor(sw) {\n    this.sw = sw;\n    this.pushManager = null;\n    this.subscriptionChanges = new Subject();\n    if (!sw.isEnabled) {\n      this.messages = NEVER;\n      this.notificationClicks = NEVER;\n      this.subscription = NEVER;\n      return;\n    }\n    this.messages = this.sw.eventsOfType('PUSH').pipe(map(message => message.data));\n    this.notificationClicks = this.sw.eventsOfType('NOTIFICATION_CLICK').pipe(map(message => message.data));\n    this.pushManager = this.sw.registration.pipe(map(registration => registration.pushManager));\n    const workerDrivenSubscriptions = this.pushManager.pipe(switchMap(pm => pm.getSubscription()));\n    this.subscription = merge(workerDrivenSubscriptions, this.subscriptionChanges);\n  }\n  /**\n   * Subscribes to Web Push Notifications,\n   * after requesting and receiving user permission.\n   *\n   * @param options An object containing the `serverPublicKey` string.\n   * @returns A Promise that resolves to the new subscription object.\n   */\n  requestSubscription(options) {\n    if (!this.sw.isEnabled || this.pushManager === null) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const pushOptions = {\n      userVisibleOnly: true\n    };\n    let key = this.decodeBase64(options.serverPublicKey.replace(/_/g, '/').replace(/-/g, '+'));\n    let applicationServerKey = new Uint8Array(new ArrayBuffer(key.length));\n    for (let i = 0; i < key.length; i++) {\n      applicationServerKey[i] = key.charCodeAt(i);\n    }\n    pushOptions.applicationServerKey = applicationServerKey;\n    return this.pushManager.pipe(switchMap(pm => pm.subscribe(pushOptions)), take(1)).toPromise().then(sub => {\n      this.subscriptionChanges.next(sub);\n      return sub;\n    });\n  }\n  /**\n   * Unsubscribes from Service Worker push notifications.\n   *\n   * @returns A Promise that is resolved when the operation succeeds, or is rejected if there is no\n   *          active subscription or the unsubscribe operation fails.\n   */\n  unsubscribe() {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const doUnsubscribe = sub => {\n      if (sub === null) {\n        throw new Error('Not subscribed to push notifications.');\n      }\n      return sub.unsubscribe().then(success => {\n        if (!success) {\n          throw new Error('Unsubscribe failed!');\n        }\n        this.subscriptionChanges.next(null);\n      });\n    };\n    return this.subscription.pipe(take(1), switchMap(doUnsubscribe)).toPromise();\n  }\n  decodeBase64(input) {\n    return atob(input);\n  }\n  static {\n    this.ɵfac = function SwPush_Factory(t) {\n      return new (t || SwPush)(i0.ɵɵinject(NgswCommChannel));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SwPush,\n      factory: SwPush.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SwPush, [{\n    type: Injectable\n  }], () => [{\n    type: NgswCommChannel\n  }], null);\n})();\n\n/**\n * Subscribe to update notifications from the Service Worker, trigger update\n * checks, and forcibly activate updates.\n *\n * @see {@link guide/service-worker-communications Service worker communication guide}\n *\n * @publicApi\n */\nclass SwUpdate {\n  /**\n   * True if the Service Worker is enabled (supported by the browser and enabled via\n   * `ServiceWorkerModule`).\n   */\n  get isEnabled() {\n    return this.sw.isEnabled;\n  }\n  constructor(sw) {\n    this.sw = sw;\n    if (!sw.isEnabled) {\n      this.versionUpdates = NEVER;\n      this.unrecoverable = NEVER;\n      return;\n    }\n    this.versionUpdates = this.sw.eventsOfType(['VERSION_DETECTED', 'VERSION_INSTALLATION_FAILED', 'VERSION_READY', 'NO_NEW_VERSION_DETECTED']);\n    this.unrecoverable = this.sw.eventsOfType('UNRECOVERABLE_STATE');\n  }\n  /**\n   * Checks for an update and waits until the new version is downloaded from the server and ready\n   * for activation.\n   *\n   * @returns a promise that\n   * - resolves to `true` if a new version was found and is ready to be activated.\n   * - resolves to `false` if no new version was found\n   * - rejects if any error occurs\n   */\n  checkForUpdate() {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const nonce = this.sw.generateNonce();\n    return this.sw.postMessageWithOperation('CHECK_FOR_UPDATES', {\n      nonce\n    }, nonce);\n  }\n  /**\n   * Updates the current client (i.e. browser tab) to the latest version that is ready for\n   * activation.\n   *\n   * In most cases, you should not use this method and instead should update a client by reloading\n   * the page.\n   *\n   * <div class=\"alert is-important\">\n   *\n   * Updating a client without reloading can easily result in a broken application due to a version\n   * mismatch between the [application shell](guide/glossary#app-shell) and other page resources,\n   * such as [lazy-loaded chunks](guide/glossary#lazy-loading), whose filenames may change between\n   * versions.\n   *\n   * Only use this method, if you are certain it is safe for your specific use case.\n   *\n   * </div>\n   *\n   * @returns a promise that\n   *  - resolves to `true` if an update was activated successfully\n   *  - resolves to `false` if no update was available (for example, the client was already on the\n   *    latest version).\n   *  - rejects if any error occurs\n   */\n  activateUpdate() {\n    if (!this.sw.isEnabled) {\n      return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n    }\n    const nonce = this.sw.generateNonce();\n    return this.sw.postMessageWithOperation('ACTIVATE_UPDATE', {\n      nonce\n    }, nonce);\n  }\n  static {\n    this.ɵfac = function SwUpdate_Factory(t) {\n      return new (t || SwUpdate)(i0.ɵɵinject(NgswCommChannel));\n    };\n  }\n  static {\n    this.ɵprov = /* @__PURE__ */i0.ɵɵdefineInjectable({\n      token: SwUpdate,\n      factory: SwUpdate.ɵfac\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(SwUpdate, [{\n    type: Injectable\n  }], () => [{\n    type: NgswCommChannel\n  }], null);\n})();\n\n/*!\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst SCRIPT = new InjectionToken(ngDevMode ? 'NGSW_REGISTER_SCRIPT' : '');\nfunction ngswAppInitializer(injector, script, options, platformId) {\n  return () => {\n    if (!(isPlatformBrowser(platformId) && 'serviceWorker' in navigator && options.enabled !== false)) {\n      return;\n    }\n    // Wait for service worker controller changes, and fire an INITIALIZE action when a new SW\n    // becomes active. This allows the SW to initialize itself even if there is no application\n    // traffic.\n    navigator.serviceWorker.addEventListener('controllerchange', () => {\n      if (navigator.serviceWorker.controller !== null) {\n        navigator.serviceWorker.controller.postMessage({\n          action: 'INITIALIZE'\n        });\n      }\n    });\n    let readyToRegister$;\n    if (typeof options.registrationStrategy === 'function') {\n      readyToRegister$ = options.registrationStrategy();\n    } else {\n      const [strategy, ...args] = (options.registrationStrategy || 'registerWhenStable:30000').split(':');\n      switch (strategy) {\n        case 'registerImmediately':\n          readyToRegister$ = of(null);\n          break;\n        case 'registerWithDelay':\n          readyToRegister$ = delayWithTimeout(+args[0] || 0);\n          break;\n        case 'registerWhenStable':\n          readyToRegister$ = !args[0] ? whenStable(injector) : merge(whenStable(injector), delayWithTimeout(+args[0]));\n          break;\n        default:\n          // Unknown strategy.\n          throw new Error(`Unknown ServiceWorker registration strategy: ${options.registrationStrategy}`);\n      }\n    }\n    // Don't return anything to avoid blocking the application until the SW is registered.\n    // Also, run outside the Angular zone to avoid preventing the app from stabilizing (especially\n    // given that some registration strategies wait for the app to stabilize).\n    // Catch and log the error if SW registration fails to avoid uncaught rejection warning.\n    const ngZone = injector.get(NgZone);\n    ngZone.runOutsideAngular(() => readyToRegister$.pipe(take(1)).subscribe(() => navigator.serviceWorker.register(script, {\n      scope: options.scope\n    }).catch(err => console.error('Service worker registration failed with:', err))));\n  };\n}\nfunction delayWithTimeout(timeout) {\n  return of(null).pipe(delay(timeout));\n}\nfunction whenStable(injector) {\n  const appRef = injector.get(ApplicationRef);\n  return appRef.isStable.pipe(filter(stable => stable));\n}\nfunction ngswCommChannelFactory(opts, platformId) {\n  return new NgswCommChannel(isPlatformBrowser(platformId) && opts.enabled !== false ? navigator.serviceWorker : undefined);\n}\n/**\n * Token that can be used to provide options for `ServiceWorkerModule` outside of\n * `ServiceWorkerModule.register()`.\n *\n * You can use this token to define a provider that generates the registration options at runtime,\n * for example via a function call:\n *\n * {@example service-worker/registration-options/module.ts region=\"registration-options\"\n *     header=\"app.module.ts\"}\n *\n * @publicApi\n */\nclass SwRegistrationOptions {}\n/**\n * @publicApi\n *\n * Sets up providers to register the given Angular Service Worker script.\n *\n * If `enabled` is set to `false` in the given options, the module will behave as if service\n * workers are not supported by the browser, and the service worker will not be registered.\n *\n * Example usage:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [\n *     provideServiceWorker('ngsw-worker.js')\n *   ],\n * });\n * ```\n */\nfunction provideServiceWorker(script, options = {}) {\n  return makeEnvironmentProviders([SwPush, SwUpdate, {\n    provide: SCRIPT,\n    useValue: script\n  }, {\n    provide: SwRegistrationOptions,\n    useValue: options\n  }, {\n    provide: NgswCommChannel,\n    useFactory: ngswCommChannelFactory,\n    deps: [SwRegistrationOptions, PLATFORM_ID]\n  }, {\n    provide: APP_INITIALIZER,\n    useFactory: ngswAppInitializer,\n    deps: [Injector, SCRIPT, SwRegistrationOptions, PLATFORM_ID],\n    multi: true\n  }]);\n}\n\n/**\n * @publicApi\n */\nclass ServiceWorkerModule {\n  /**\n   * Register the given Angular Service Worker script.\n   *\n   * If `enabled` is set to `false` in the given options, the module will behave as if service\n   * workers are not supported by the browser, and the service worker will not be registered.\n   */\n  static register(script, options = {}) {\n    return {\n      ngModule: ServiceWorkerModule,\n      providers: [provideServiceWorker(script, options)]\n    };\n  }\n  static {\n    this.ɵfac = function ServiceWorkerModule_Factory(t) {\n      return new (t || ServiceWorkerModule)();\n    };\n  }\n  static {\n    this.ɵmod = /* @__PURE__ */i0.ɵɵdefineNgModule({\n      type: ServiceWorkerModule\n    });\n  }\n  static {\n    this.ɵinj = /* @__PURE__ */i0.ɵɵdefineInjector({\n      providers: [SwPush, SwUpdate]\n    });\n  }\n}\n(() => {\n  (typeof ngDevMode === \"undefined\" || ngDevMode) && i0.ɵsetClassMetadata(ServiceWorkerModule, [{\n    type: NgModule,\n    args: [{\n      providers: [SwPush, SwUpdate]\n    }]\n  }], null, null);\n})();\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ServiceWorkerModule, SwPush, SwRegistrationOptions, SwUpdate, provideServiceWorker };", "map": {"version": 3, "names": ["i0", "Injectable", "InjectionToken", "NgZone", "ApplicationRef", "makeEnvironmentProviders", "PLATFORM_ID", "APP_INITIALIZER", "Injector", "NgModule", "isPlatformBrowser", "defer", "throwError", "fromEvent", "of", "concat", "Subject", "NEVER", "merge", "map", "filter", "switchMap", "publish", "take", "tap", "delay", "ERR_SW_NOT_SUPPORTED", "errorObservable", "message", "Error", "NgswCommChannel", "constructor", "serviceWorker", "worker", "events", "registration", "controllerChangeEvents", "controllerChanges", "pipe", "controller", "currentController", "controllerWithChanges", "c", "getRegistration", "rawEvents", "rawEventPayload", "event", "data", "eventsUnconnected", "type", "connect", "postMessage", "action", "payload", "sw", "to<PERSON>romise", "then", "undefined", "postMessageWithOperation", "operationNonce", "waitForOperationCompleted", "Promise", "all", "result", "generateNonce", "Math", "round", "random", "eventsOfType", "filterFn", "includes", "nextEventOfType", "nonce", "error", "isEnabled", "SwPush", "pushManager", "subscriptionChanges", "messages", "notificationClicks", "subscription", "workerDrivenSubscriptions", "pm", "getSubscription", "requestSubscription", "options", "reject", "pushOptions", "userVisibleOnly", "key", "decodeBase64", "serverPublicKey", "replace", "applicationServerKey", "Uint8Array", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "length", "i", "charCodeAt", "subscribe", "sub", "next", "unsubscribe", "doUnsubscribe", "success", "input", "atob", "ɵfac", "SwPush_Factory", "t", "ɵɵinject", "ɵprov", "ɵɵdefineInjectable", "token", "factory", "ngDevMode", "ɵsetClassMetadata", "SwUpdate", "versionUpdates", "unrecoverable", "checkForUpdate", "activateUpdate", "SwUpdate_Factory", "SCRIPT", "ngswAppInitializer", "injector", "script", "platformId", "navigator", "enabled", "addEventListener", "readyToRegister$", "registrationStrategy", "strategy", "args", "split", "delayWithTimeout", "whenStable", "ngZone", "get", "runOutsideAngular", "register", "scope", "catch", "err", "console", "timeout", "appRef", "isStable", "stable", "ngswCommChannelFactory", "opts", "SwRegistrationOptions", "provideServiceWorker", "provide", "useValue", "useFactory", "deps", "multi", "ServiceWorkerModule", "ngModule", "providers", "ServiceWorkerModule_Factory", "ɵmod", "ɵɵdefineNgModule", "ɵinj", "ɵɵdefineInjector"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/service-worker/fesm2022/service-worker.mjs"], "sourcesContent": ["/**\n * @license Angular v17.3.12\n * (c) 2010-2024 Google LLC. https://angular.io/\n * License: MIT\n */\n\nimport * as i0 from '@angular/core';\nimport { Injectable, InjectionToken, NgZone, ApplicationRef, makeEnvironmentProviders, PLATFORM_ID, APP_INITIALIZER, Injector, NgModule } from '@angular/core';\nimport { isPlatformBrowser } from '@angular/common';\nimport { defer, throwError, fromEvent, of, concat, Subject, NEVER, merge } from 'rxjs';\nimport { map, filter, switchMap, publish, take, tap, delay } from 'rxjs/operators';\n\nconst ERR_SW_NOT_SUPPORTED = 'Service workers are disabled or not supported by this browser';\nfunction errorObservable(message) {\n    return defer(() => throwError(new Error(message)));\n}\n/**\n * @publicApi\n */\nclass NgswCommChannel {\n    constructor(serviceWorker) {\n        this.serviceWorker = serviceWorker;\n        if (!serviceWorker) {\n            this.worker = this.events = this.registration = errorObservable(ERR_SW_NOT_SUPPORTED);\n        }\n        else {\n            const controllerChangeEvents = fromEvent(serviceWorker, 'controllerchange');\n            const controllerChanges = controllerChangeEvents.pipe(map(() => serviceWorker.controller));\n            const currentController = defer(() => of(serviceWorker.controller));\n            const controllerWithChanges = concat(currentController, controllerChanges);\n            this.worker = controllerWithChanges.pipe(filter((c) => !!c));\n            this.registration = (this.worker.pipe(switchMap(() => serviceWorker.getRegistration())));\n            const rawEvents = fromEvent(serviceWorker, 'message');\n            const rawEventPayload = rawEvents.pipe(map((event) => event.data));\n            const eventsUnconnected = rawEventPayload.pipe(filter((event) => event && event.type));\n            const events = eventsUnconnected.pipe(publish());\n            events.connect();\n            this.events = events;\n        }\n    }\n    postMessage(action, payload) {\n        return this.worker\n            .pipe(take(1), tap((sw) => {\n            sw.postMessage({\n                action,\n                ...payload,\n            });\n        }))\n            .toPromise()\n            .then(() => undefined);\n    }\n    postMessageWithOperation(type, payload, operationNonce) {\n        const waitForOperationCompleted = this.waitForOperationCompleted(operationNonce);\n        const postMessage = this.postMessage(type, payload);\n        return Promise.all([postMessage, waitForOperationCompleted]).then(([, result]) => result);\n    }\n    generateNonce() {\n        return Math.round(Math.random() * 10000000);\n    }\n    eventsOfType(type) {\n        let filterFn;\n        if (typeof type === 'string') {\n            filterFn = (event) => event.type === type;\n        }\n        else {\n            filterFn = (event) => type.includes(event.type);\n        }\n        return this.events.pipe(filter(filterFn));\n    }\n    nextEventOfType(type) {\n        return this.eventsOfType(type).pipe(take(1));\n    }\n    waitForOperationCompleted(nonce) {\n        return this.eventsOfType('OPERATION_COMPLETED')\n            .pipe(filter((event) => event.nonce === nonce), take(1), map((event) => {\n            if (event.result !== undefined) {\n                return event.result;\n            }\n            throw new Error(event.error);\n        }))\n            .toPromise();\n    }\n    get isEnabled() {\n        return !!this.serviceWorker;\n    }\n}\n\n/**\n * Subscribe and listen to\n * [Web Push\n * Notifications](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices) through\n * Angular Service Worker.\n *\n * @usageNotes\n *\n * You can inject a `SwPush` instance into any component or service\n * as a dependency.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"inject-sw-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * To subscribe, call `SwPush.requestSubscription()`, which asks the user for permission.\n * The call returns a `Promise` with a new\n * [`PushSubscription`](https://developer.mozilla.org/en-US/docs/Web/API/PushSubscription)\n * instance.\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-push\"\n * header=\"app.component.ts\"></code-example>\n *\n * A request is rejected if the user denies permission, or if the browser\n * blocks or does not support the Push API or ServiceWorkers.\n * Check `SwPush.isEnabled` to confirm status.\n *\n * Invoke Push Notifications by pushing a message with the following payload.\n *\n * ```ts\n * {\n *   \"notification\": {\n *     \"actions\": NotificationAction[],\n *     \"badge\": USVString,\n *     \"body\": DOMString,\n *     \"data\": any,\n *     \"dir\": \"auto\"|\"ltr\"|\"rtl\",\n *     \"icon\": USVString,\n *     \"image\": USVString,\n *     \"lang\": DOMString,\n *     \"renotify\": boolean,\n *     \"requireInteraction\": boolean,\n *     \"silent\": boolean,\n *     \"tag\": DOMString,\n *     \"timestamp\": DOMTimeStamp,\n *     \"title\": DOMString,\n *     \"vibrate\": number[]\n *   }\n * }\n * ```\n *\n * Only `title` is required. See `Notification`\n * [instance\n * properties](https://developer.mozilla.org/en-US/docs/Web/API/Notification#Instance_properties).\n *\n * While the subscription is active, Service Worker listens for\n * [PushEvent](https://developer.mozilla.org/en-US/docs/Web/API/PushEvent)\n * occurrences and creates\n * [Notification](https://developer.mozilla.org/en-US/docs/Web/API/Notification)\n * instances in response.\n *\n * Unsubscribe using `SwPush.unsubscribe()`.\n *\n * An application can subscribe to `SwPush.notificationClicks` observable to be notified when a user\n * clicks on a notification. For example:\n *\n * <code-example path=\"service-worker/push/module.ts\" region=\"subscribe-to-notification-clicks\"\n * header=\"app.component.ts\"></code-example>\n *\n * You can read more on handling notification clicks in the [Service worker notifications\n * guide](guide/service-worker-notifications).\n *\n * @see [Push Notifications](https://developers.google.com/web/fundamentals/codelabs/push-notifications/)\n * @see [Angular Push Notifications](https://blog.angular-university.io/angular-push-notifications/)\n * @see [MDN: Push API](https://developer.mozilla.org/en-US/docs/Web/API/Push_API)\n * @see [MDN: Notifications API](https://developer.mozilla.org/en-US/docs/Web/API/Notifications_API)\n * @see [MDN: Web Push API Notifications best practices](https://developer.mozilla.org/en-US/docs/Web/API/Push_API/Best_Practices)\n *\n * @publicApi\n */\nclass SwPush {\n    /**\n     * True if the Service Worker is enabled (supported by the browser and enabled via\n     * `ServiceWorkerModule`).\n     */\n    get isEnabled() {\n        return this.sw.isEnabled;\n    }\n    constructor(sw) {\n        this.sw = sw;\n        this.pushManager = null;\n        this.subscriptionChanges = new Subject();\n        if (!sw.isEnabled) {\n            this.messages = NEVER;\n            this.notificationClicks = NEVER;\n            this.subscription = NEVER;\n            return;\n        }\n        this.messages = this.sw.eventsOfType('PUSH').pipe(map((message) => message.data));\n        this.notificationClicks = this.sw\n            .eventsOfType('NOTIFICATION_CLICK')\n            .pipe(map((message) => message.data));\n        this.pushManager = this.sw.registration.pipe(map((registration) => registration.pushManager));\n        const workerDrivenSubscriptions = this.pushManager.pipe(switchMap((pm) => pm.getSubscription()));\n        this.subscription = merge(workerDrivenSubscriptions, this.subscriptionChanges);\n    }\n    /**\n     * Subscribes to Web Push Notifications,\n     * after requesting and receiving user permission.\n     *\n     * @param options An object containing the `serverPublicKey` string.\n     * @returns A Promise that resolves to the new subscription object.\n     */\n    requestSubscription(options) {\n        if (!this.sw.isEnabled || this.pushManager === null) {\n            return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n        }\n        const pushOptions = { userVisibleOnly: true };\n        let key = this.decodeBase64(options.serverPublicKey.replace(/_/g, '/').replace(/-/g, '+'));\n        let applicationServerKey = new Uint8Array(new ArrayBuffer(key.length));\n        for (let i = 0; i < key.length; i++) {\n            applicationServerKey[i] = key.charCodeAt(i);\n        }\n        pushOptions.applicationServerKey = applicationServerKey;\n        return this.pushManager\n            .pipe(switchMap((pm) => pm.subscribe(pushOptions)), take(1))\n            .toPromise()\n            .then((sub) => {\n            this.subscriptionChanges.next(sub);\n            return sub;\n        });\n    }\n    /**\n     * Unsubscribes from Service Worker push notifications.\n     *\n     * @returns A Promise that is resolved when the operation succeeds, or is rejected if there is no\n     *          active subscription or the unsubscribe operation fails.\n     */\n    unsubscribe() {\n        if (!this.sw.isEnabled) {\n            return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n        }\n        const doUnsubscribe = (sub) => {\n            if (sub === null) {\n                throw new Error('Not subscribed to push notifications.');\n            }\n            return sub.unsubscribe().then((success) => {\n                if (!success) {\n                    throw new Error('Unsubscribe failed!');\n                }\n                this.subscriptionChanges.next(null);\n            });\n        };\n        return this.subscription.pipe(take(1), switchMap(doUnsubscribe)).toPromise();\n    }\n    decodeBase64(input) {\n        return atob(input);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: SwPush, deps: [{ token: NgswCommChannel }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: SwPush }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: SwPush, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: NgswCommChannel }] });\n\n/**\n * Subscribe to update notifications from the Service Worker, trigger update\n * checks, and forcibly activate updates.\n *\n * @see {@link guide/service-worker-communications Service worker communication guide}\n *\n * @publicApi\n */\nclass SwUpdate {\n    /**\n     * True if the Service Worker is enabled (supported by the browser and enabled via\n     * `ServiceWorkerModule`).\n     */\n    get isEnabled() {\n        return this.sw.isEnabled;\n    }\n    constructor(sw) {\n        this.sw = sw;\n        if (!sw.isEnabled) {\n            this.versionUpdates = NEVER;\n            this.unrecoverable = NEVER;\n            return;\n        }\n        this.versionUpdates = this.sw.eventsOfType([\n            'VERSION_DETECTED',\n            'VERSION_INSTALLATION_FAILED',\n            'VERSION_READY',\n            'NO_NEW_VERSION_DETECTED',\n        ]);\n        this.unrecoverable = this.sw.eventsOfType('UNRECOVERABLE_STATE');\n    }\n    /**\n     * Checks for an update and waits until the new version is downloaded from the server and ready\n     * for activation.\n     *\n     * @returns a promise that\n     * - resolves to `true` if a new version was found and is ready to be activated.\n     * - resolves to `false` if no new version was found\n     * - rejects if any error occurs\n     */\n    checkForUpdate() {\n        if (!this.sw.isEnabled) {\n            return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n        }\n        const nonce = this.sw.generateNonce();\n        return this.sw.postMessageWithOperation('CHECK_FOR_UPDATES', { nonce }, nonce);\n    }\n    /**\n     * Updates the current client (i.e. browser tab) to the latest version that is ready for\n     * activation.\n     *\n     * In most cases, you should not use this method and instead should update a client by reloading\n     * the page.\n     *\n     * <div class=\"alert is-important\">\n     *\n     * Updating a client without reloading can easily result in a broken application due to a version\n     * mismatch between the [application shell](guide/glossary#app-shell) and other page resources,\n     * such as [lazy-loaded chunks](guide/glossary#lazy-loading), whose filenames may change between\n     * versions.\n     *\n     * Only use this method, if you are certain it is safe for your specific use case.\n     *\n     * </div>\n     *\n     * @returns a promise that\n     *  - resolves to `true` if an update was activated successfully\n     *  - resolves to `false` if no update was available (for example, the client was already on the\n     *    latest version).\n     *  - rejects if any error occurs\n     */\n    activateUpdate() {\n        if (!this.sw.isEnabled) {\n            return Promise.reject(new Error(ERR_SW_NOT_SUPPORTED));\n        }\n        const nonce = this.sw.generateNonce();\n        return this.sw.postMessageWithOperation('ACTIVATE_UPDATE', { nonce }, nonce);\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: SwUpdate, deps: [{ token: NgswCommChannel }], target: i0.ɵɵFactoryTarget.Injectable }); }\n    static { this.ɵprov = i0.ɵɵngDeclareInjectable({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: SwUpdate }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: SwUpdate, decorators: [{\n            type: Injectable\n        }], ctorParameters: () => [{ type: NgswCommChannel }] });\n\n/*!\n * @license\n * Copyright Google LLC All Rights Reserved.\n *\n * Use of this source code is governed by an MIT-style license that can be\n * found in the LICENSE file at https://angular.io/license\n */\nconst SCRIPT = new InjectionToken(ngDevMode ? 'NGSW_REGISTER_SCRIPT' : '');\nfunction ngswAppInitializer(injector, script, options, platformId) {\n    return () => {\n        if (!(isPlatformBrowser(platformId) && 'serviceWorker' in navigator && options.enabled !== false)) {\n            return;\n        }\n        // Wait for service worker controller changes, and fire an INITIALIZE action when a new SW\n        // becomes active. This allows the SW to initialize itself even if there is no application\n        // traffic.\n        navigator.serviceWorker.addEventListener('controllerchange', () => {\n            if (navigator.serviceWorker.controller !== null) {\n                navigator.serviceWorker.controller.postMessage({ action: 'INITIALIZE' });\n            }\n        });\n        let readyToRegister$;\n        if (typeof options.registrationStrategy === 'function') {\n            readyToRegister$ = options.registrationStrategy();\n        }\n        else {\n            const [strategy, ...args] = (options.registrationStrategy || 'registerWhenStable:30000').split(':');\n            switch (strategy) {\n                case 'registerImmediately':\n                    readyToRegister$ = of(null);\n                    break;\n                case 'registerWithDelay':\n                    readyToRegister$ = delayWithTimeout(+args[0] || 0);\n                    break;\n                case 'registerWhenStable':\n                    readyToRegister$ = !args[0]\n                        ? whenStable(injector)\n                        : merge(whenStable(injector), delayWithTimeout(+args[0]));\n                    break;\n                default:\n                    // Unknown strategy.\n                    throw new Error(`Unknown ServiceWorker registration strategy: ${options.registrationStrategy}`);\n            }\n        }\n        // Don't return anything to avoid blocking the application until the SW is registered.\n        // Also, run outside the Angular zone to avoid preventing the app from stabilizing (especially\n        // given that some registration strategies wait for the app to stabilize).\n        // Catch and log the error if SW registration fails to avoid uncaught rejection warning.\n        const ngZone = injector.get(NgZone);\n        ngZone.runOutsideAngular(() => readyToRegister$\n            .pipe(take(1))\n            .subscribe(() => navigator.serviceWorker\n            .register(script, { scope: options.scope })\n            .catch((err) => console.error('Service worker registration failed with:', err))));\n    };\n}\nfunction delayWithTimeout(timeout) {\n    return of(null).pipe(delay(timeout));\n}\nfunction whenStable(injector) {\n    const appRef = injector.get(ApplicationRef);\n    return appRef.isStable.pipe(filter((stable) => stable));\n}\nfunction ngswCommChannelFactory(opts, platformId) {\n    return new NgswCommChannel(isPlatformBrowser(platformId) && opts.enabled !== false ? navigator.serviceWorker : undefined);\n}\n/**\n * Token that can be used to provide options for `ServiceWorkerModule` outside of\n * `ServiceWorkerModule.register()`.\n *\n * You can use this token to define a provider that generates the registration options at runtime,\n * for example via a function call:\n *\n * {@example service-worker/registration-options/module.ts region=\"registration-options\"\n *     header=\"app.module.ts\"}\n *\n * @publicApi\n */\nclass SwRegistrationOptions {\n}\n/**\n * @publicApi\n *\n * Sets up providers to register the given Angular Service Worker script.\n *\n * If `enabled` is set to `false` in the given options, the module will behave as if service\n * workers are not supported by the browser, and the service worker will not be registered.\n *\n * Example usage:\n * ```ts\n * bootstrapApplication(AppComponent, {\n *   providers: [\n *     provideServiceWorker('ngsw-worker.js')\n *   ],\n * });\n * ```\n */\nfunction provideServiceWorker(script, options = {}) {\n    return makeEnvironmentProviders([\n        SwPush,\n        SwUpdate,\n        { provide: SCRIPT, useValue: script },\n        { provide: SwRegistrationOptions, useValue: options },\n        {\n            provide: NgswCommChannel,\n            useFactory: ngswCommChannelFactory,\n            deps: [SwRegistrationOptions, PLATFORM_ID],\n        },\n        {\n            provide: APP_INITIALIZER,\n            useFactory: ngswAppInitializer,\n            deps: [Injector, SCRIPT, SwRegistrationOptions, PLATFORM_ID],\n            multi: true,\n        },\n    ]);\n}\n\n/**\n * @publicApi\n */\nclass ServiceWorkerModule {\n    /**\n     * Register the given Angular Service Worker script.\n     *\n     * If `enabled` is set to `false` in the given options, the module will behave as if service\n     * workers are not supported by the browser, and the service worker will not be registered.\n     */\n    static register(script, options = {}) {\n        return {\n            ngModule: ServiceWorkerModule,\n            providers: [provideServiceWorker(script, options)],\n        };\n    }\n    static { this.ɵfac = i0.ɵɵngDeclareFactory({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: ServiceWorkerModule, deps: [], target: i0.ɵɵFactoryTarget.NgModule }); }\n    static { this.ɵmod = i0.ɵɵngDeclareNgModule({ minVersion: \"14.0.0\", version: \"17.3.12\", ngImport: i0, type: ServiceWorkerModule }); }\n    static { this.ɵinj = i0.ɵɵngDeclareInjector({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: ServiceWorkerModule, providers: [SwPush, SwUpdate] }); }\n}\ni0.ɵɵngDeclareClassMetadata({ minVersion: \"12.0.0\", version: \"17.3.12\", ngImport: i0, type: ServiceWorkerModule, decorators: [{\n            type: NgModule,\n            args: [{ providers: [SwPush, SwUpdate] }]\n        }] });\n\n/**\n * @module\n * @description\n * Entry point for all public APIs of this package.\n */\n// This file only reexports content of the `src` folder. Keep it that way.\n\n// This file is not used to build this module. It is only used during editing\n\n/**\n * Generated bundle index. Do not edit.\n */\n\nexport { ServiceWorkerModule, SwPush, SwRegistrationOptions, SwUpdate, provideServiceWorker };\n"], "mappings": "AAAA;AACA;AACA;AACA;AACA;;AAEA,OAAO,KAAKA,EAAE,MAAM,eAAe;AACnC,SAASC,UAAU,EAAEC,cAAc,EAAEC,MAAM,EAAEC,cAAc,EAAEC,wBAAwB,EAAEC,WAAW,EAAEC,eAAe,EAAEC,QAAQ,EAAEC,QAAQ,QAAQ,eAAe;AAC9J,SAASC,iBAAiB,QAAQ,iBAAiB;AACnD,SAASC,KAAK,EAAEC,UAAU,EAAEC,SAAS,EAAEC,EAAE,EAAEC,MAAM,EAAEC,OAAO,EAAEC,KAAK,EAAEC,KAAK,QAAQ,MAAM;AACtF,SAASC,GAAG,EAAEC,MAAM,EAAEC,SAAS,EAAEC,OAAO,EAAEC,IAAI,EAAEC,GAAG,EAAEC,KAAK,QAAQ,gBAAgB;AAElF,MAAMC,oBAAoB,GAAG,+DAA+D;AAC5F,SAASC,eAAeA,CAACC,OAAO,EAAE;EAC9B,OAAOjB,KAAK,CAAC,MAAMC,UAAU,CAAC,IAAIiB,KAAK,CAACD,OAAO,CAAC,CAAC,CAAC;AACtD;AACA;AACA;AACA;AACA,MAAME,eAAe,CAAC;EAClBC,WAAWA,CAACC,aAAa,EAAE;IACvB,IAAI,CAACA,aAAa,GAAGA,aAAa;IAClC,IAAI,CAACA,aAAa,EAAE;MAChB,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,MAAM,GAAG,IAAI,CAACC,YAAY,GAAGR,eAAe,CAACD,oBAAoB,CAAC;IACzF,CAAC,MACI;MACD,MAAMU,sBAAsB,GAAGvB,SAAS,CAACmB,aAAa,EAAE,kBAAkB,CAAC;MAC3E,MAAMK,iBAAiB,GAAGD,sBAAsB,CAACE,IAAI,CAACnB,GAAG,CAAC,MAAMa,aAAa,CAACO,UAAU,CAAC,CAAC;MAC1F,MAAMC,iBAAiB,GAAG7B,KAAK,CAAC,MAAMG,EAAE,CAACkB,aAAa,CAACO,UAAU,CAAC,CAAC;MACnE,MAAME,qBAAqB,GAAG1B,MAAM,CAACyB,iBAAiB,EAAEH,iBAAiB,CAAC;MAC1E,IAAI,CAACJ,MAAM,GAAGQ,qBAAqB,CAACH,IAAI,CAAClB,MAAM,CAAEsB,CAAC,IAAK,CAAC,CAACA,CAAC,CAAC,CAAC;MAC5D,IAAI,CAACP,YAAY,GAAI,IAAI,CAACF,MAAM,CAACK,IAAI,CAACjB,SAAS,CAAC,MAAMW,aAAa,CAACW,eAAe,CAAC,CAAC,CAAC,CAAE;MACxF,MAAMC,SAAS,GAAG/B,SAAS,CAACmB,aAAa,EAAE,SAAS,CAAC;MACrD,MAAMa,eAAe,GAAGD,SAAS,CAACN,IAAI,CAACnB,GAAG,CAAE2B,KAAK,IAAKA,KAAK,CAACC,IAAI,CAAC,CAAC;MAClE,MAAMC,iBAAiB,GAAGH,eAAe,CAACP,IAAI,CAAClB,MAAM,CAAE0B,KAAK,IAAKA,KAAK,IAAIA,KAAK,CAACG,IAAI,CAAC,CAAC;MACtF,MAAMf,MAAM,GAAGc,iBAAiB,CAACV,IAAI,CAAChB,OAAO,CAAC,CAAC,CAAC;MAChDY,MAAM,CAACgB,OAAO,CAAC,CAAC;MAChB,IAAI,CAAChB,MAAM,GAAGA,MAAM;IACxB;EACJ;EACAiB,WAAWA,CAACC,MAAM,EAAEC,OAAO,EAAE;IACzB,OAAO,IAAI,CAACpB,MAAM,CACbK,IAAI,CAACf,IAAI,CAAC,CAAC,CAAC,EAAEC,GAAG,CAAE8B,EAAE,IAAK;MAC3BA,EAAE,CAACH,WAAW,CAAC;QACXC,MAAM;QACN,GAAGC;MACP,CAAC,CAAC;IACN,CAAC,CAAC,CAAC,CACEE,SAAS,CAAC,CAAC,CACXC,IAAI,CAAC,MAAMC,SAAS,CAAC;EAC9B;EACAC,wBAAwBA,CAACT,IAAI,EAAEI,OAAO,EAAEM,cAAc,EAAE;IACpD,MAAMC,yBAAyB,GAAG,IAAI,CAACA,yBAAyB,CAACD,cAAc,CAAC;IAChF,MAAMR,WAAW,GAAG,IAAI,CAACA,WAAW,CAACF,IAAI,EAAEI,OAAO,CAAC;IACnD,OAAOQ,OAAO,CAACC,GAAG,CAAC,CAACX,WAAW,EAAES,yBAAyB,CAAC,CAAC,CAACJ,IAAI,CAAC,CAAC,GAAGO,MAAM,CAAC,KAAKA,MAAM,CAAC;EAC7F;EACAC,aAAaA,CAAA,EAAG;IACZ,OAAOC,IAAI,CAACC,KAAK,CAACD,IAAI,CAACE,MAAM,CAAC,CAAC,GAAG,QAAQ,CAAC;EAC/C;EACAC,YAAYA,CAACnB,IAAI,EAAE;IACf,IAAIoB,QAAQ;IACZ,IAAI,OAAOpB,IAAI,KAAK,QAAQ,EAAE;MAC1BoB,QAAQ,GAAIvB,KAAK,IAAKA,KAAK,CAACG,IAAI,KAAKA,IAAI;IAC7C,CAAC,MACI;MACDoB,QAAQ,GAAIvB,KAAK,IAAKG,IAAI,CAACqB,QAAQ,CAACxB,KAAK,CAACG,IAAI,CAAC;IACnD;IACA,OAAO,IAAI,CAACf,MAAM,CAACI,IAAI,CAAClB,MAAM,CAACiD,QAAQ,CAAC,CAAC;EAC7C;EACAE,eAAeA,CAACtB,IAAI,EAAE;IAClB,OAAO,IAAI,CAACmB,YAAY,CAACnB,IAAI,CAAC,CAACX,IAAI,CAACf,IAAI,CAAC,CAAC,CAAC,CAAC;EAChD;EACAqC,yBAAyBA,CAACY,KAAK,EAAE;IAC7B,OAAO,IAAI,CAACJ,YAAY,CAAC,qBAAqB,CAAC,CAC1C9B,IAAI,CAAClB,MAAM,CAAE0B,KAAK,IAAKA,KAAK,CAAC0B,KAAK,KAAKA,KAAK,CAAC,EAAEjD,IAAI,CAAC,CAAC,CAAC,EAAEJ,GAAG,CAAE2B,KAAK,IAAK;MACxE,IAAIA,KAAK,CAACiB,MAAM,KAAKN,SAAS,EAAE;QAC5B,OAAOX,KAAK,CAACiB,MAAM;MACvB;MACA,MAAM,IAAIlC,KAAK,CAACiB,KAAK,CAAC2B,KAAK,CAAC;IAChC,CAAC,CAAC,CAAC,CACElB,SAAS,CAAC,CAAC;EACpB;EACA,IAAImB,SAASA,CAAA,EAAG;IACZ,OAAO,CAAC,CAAC,IAAI,CAAC1C,aAAa;EAC/B;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM2C,MAAM,CAAC;EACT;AACJ;AACA;AACA;EACI,IAAID,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACpB,EAAE,CAACoB,SAAS;EAC5B;EACA3C,WAAWA,CAACuB,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACsB,WAAW,GAAG,IAAI;IACvB,IAAI,CAACC,mBAAmB,GAAG,IAAI7D,OAAO,CAAC,CAAC;IACxC,IAAI,CAACsC,EAAE,CAACoB,SAAS,EAAE;MACf,IAAI,CAACI,QAAQ,GAAG7D,KAAK;MACrB,IAAI,CAAC8D,kBAAkB,GAAG9D,KAAK;MAC/B,IAAI,CAAC+D,YAAY,GAAG/D,KAAK;MACzB;IACJ;IACA,IAAI,CAAC6D,QAAQ,GAAG,IAAI,CAACxB,EAAE,CAACc,YAAY,CAAC,MAAM,CAAC,CAAC9B,IAAI,CAACnB,GAAG,CAAES,OAAO,IAAKA,OAAO,CAACmB,IAAI,CAAC,CAAC;IACjF,IAAI,CAACgC,kBAAkB,GAAG,IAAI,CAACzB,EAAE,CAC5Bc,YAAY,CAAC,oBAAoB,CAAC,CAClC9B,IAAI,CAACnB,GAAG,CAAES,OAAO,IAAKA,OAAO,CAACmB,IAAI,CAAC,CAAC;IACzC,IAAI,CAAC6B,WAAW,GAAG,IAAI,CAACtB,EAAE,CAACnB,YAAY,CAACG,IAAI,CAACnB,GAAG,CAAEgB,YAAY,IAAKA,YAAY,CAACyC,WAAW,CAAC,CAAC;IAC7F,MAAMK,yBAAyB,GAAG,IAAI,CAACL,WAAW,CAACtC,IAAI,CAACjB,SAAS,CAAE6D,EAAE,IAAKA,EAAE,CAACC,eAAe,CAAC,CAAC,CAAC,CAAC;IAChG,IAAI,CAACH,YAAY,GAAG9D,KAAK,CAAC+D,yBAAyB,EAAE,IAAI,CAACJ,mBAAmB,CAAC;EAClF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;EACIO,mBAAmBA,CAACC,OAAO,EAAE;IACzB,IAAI,CAAC,IAAI,CAAC/B,EAAE,CAACoB,SAAS,IAAI,IAAI,CAACE,WAAW,KAAK,IAAI,EAAE;MACjD,OAAOf,OAAO,CAACyB,MAAM,CAAC,IAAIzD,KAAK,CAACH,oBAAoB,CAAC,CAAC;IAC1D;IACA,MAAM6D,WAAW,GAAG;MAAEC,eAAe,EAAE;IAAK,CAAC;IAC7C,IAAIC,GAAG,GAAG,IAAI,CAACC,YAAY,CAACL,OAAO,CAACM,eAAe,CAACC,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAACA,OAAO,CAAC,IAAI,EAAE,GAAG,CAAC,CAAC;IAC1F,IAAIC,oBAAoB,GAAG,IAAIC,UAAU,CAAC,IAAIC,WAAW,CAACN,GAAG,CAACO,MAAM,CAAC,CAAC;IACtE,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGR,GAAG,CAACO,MAAM,EAAEC,CAAC,EAAE,EAAE;MACjCJ,oBAAoB,CAACI,CAAC,CAAC,GAAGR,GAAG,CAACS,UAAU,CAACD,CAAC,CAAC;IAC/C;IACAV,WAAW,CAACM,oBAAoB,GAAGA,oBAAoB;IACvD,OAAO,IAAI,CAACjB,WAAW,CAClBtC,IAAI,CAACjB,SAAS,CAAE6D,EAAE,IAAKA,EAAE,CAACiB,SAAS,CAACZ,WAAW,CAAC,CAAC,EAAEhE,IAAI,CAAC,CAAC,CAAC,CAAC,CAC3DgC,SAAS,CAAC,CAAC,CACXC,IAAI,CAAE4C,GAAG,IAAK;MACf,IAAI,CAACvB,mBAAmB,CAACwB,IAAI,CAACD,GAAG,CAAC;MAClC,OAAOA,GAAG;IACd,CAAC,CAAC;EACN;EACA;AACJ;AACA;AACA;AACA;AACA;EACIE,WAAWA,CAAA,EAAG;IACV,IAAI,CAAC,IAAI,CAAChD,EAAE,CAACoB,SAAS,EAAE;MACpB,OAAOb,OAAO,CAACyB,MAAM,CAAC,IAAIzD,KAAK,CAACH,oBAAoB,CAAC,CAAC;IAC1D;IACA,MAAM6E,aAAa,GAAIH,GAAG,IAAK;MAC3B,IAAIA,GAAG,KAAK,IAAI,EAAE;QACd,MAAM,IAAIvE,KAAK,CAAC,uCAAuC,CAAC;MAC5D;MACA,OAAOuE,GAAG,CAACE,WAAW,CAAC,CAAC,CAAC9C,IAAI,CAAEgD,OAAO,IAAK;QACvC,IAAI,CAACA,OAAO,EAAE;UACV,MAAM,IAAI3E,KAAK,CAAC,qBAAqB,CAAC;QAC1C;QACA,IAAI,CAACgD,mBAAmB,CAACwB,IAAI,CAAC,IAAI,CAAC;MACvC,CAAC,CAAC;IACN,CAAC;IACD,OAAO,IAAI,CAACrB,YAAY,CAAC1C,IAAI,CAACf,IAAI,CAAC,CAAC,CAAC,EAAEF,SAAS,CAACkF,aAAa,CAAC,CAAC,CAAChD,SAAS,CAAC,CAAC;EAChF;EACAmC,YAAYA,CAACe,KAAK,EAAE;IAChB,OAAOC,IAAI,CAACD,KAAK,CAAC;EACtB;EACA;IAAS,IAAI,CAACE,IAAI,YAAAC,eAAAC,CAAA;MAAA,YAAAA,CAAA,IAAyFlC,MAAM,EAAhB3E,EAAE,CAAA8G,QAAA,CAAgChF,eAAe;IAAA,CAA6C;EAAE;EACjM;IAAS,IAAI,CAACiF,KAAK,kBAD8E/G,EAAE,CAAAgH,kBAAA;MAAAC,KAAA,EACYtC,MAAM;MAAAuC,OAAA,EAANvC,MAAM,CAAAgC;IAAA,EAAG;EAAE;AAC9H;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAHqGnH,EAAE,CAAAoH,iBAAA,CAGXzC,MAAM,EAAc,CAAC;IACrG1B,IAAI,EAAEhD;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEgD,IAAI,EAAEnB;EAAgB,CAAC,CAAC;AAAA;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMuF,QAAQ,CAAC;EACX;AACJ;AACA;AACA;EACI,IAAI3C,SAASA,CAAA,EAAG;IACZ,OAAO,IAAI,CAACpB,EAAE,CAACoB,SAAS;EAC5B;EACA3C,WAAWA,CAACuB,EAAE,EAAE;IACZ,IAAI,CAACA,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACA,EAAE,CAACoB,SAAS,EAAE;MACf,IAAI,CAAC4C,cAAc,GAAGrG,KAAK;MAC3B,IAAI,CAACsG,aAAa,GAAGtG,KAAK;MAC1B;IACJ;IACA,IAAI,CAACqG,cAAc,GAAG,IAAI,CAAChE,EAAE,CAACc,YAAY,CAAC,CACvC,kBAAkB,EAClB,6BAA6B,EAC7B,eAAe,EACf,yBAAyB,CAC5B,CAAC;IACF,IAAI,CAACmD,aAAa,GAAG,IAAI,CAACjE,EAAE,CAACc,YAAY,CAAC,qBAAqB,CAAC;EACpE;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIoD,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAAClE,EAAE,CAACoB,SAAS,EAAE;MACpB,OAAOb,OAAO,CAACyB,MAAM,CAAC,IAAIzD,KAAK,CAACH,oBAAoB,CAAC,CAAC;IAC1D;IACA,MAAM8C,KAAK,GAAG,IAAI,CAAClB,EAAE,CAACU,aAAa,CAAC,CAAC;IACrC,OAAO,IAAI,CAACV,EAAE,CAACI,wBAAwB,CAAC,mBAAmB,EAAE;MAAEc;IAAM,CAAC,EAAEA,KAAK,CAAC;EAClF;EACA;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;EACIiD,cAAcA,CAAA,EAAG;IACb,IAAI,CAAC,IAAI,CAACnE,EAAE,CAACoB,SAAS,EAAE;MACpB,OAAOb,OAAO,CAACyB,MAAM,CAAC,IAAIzD,KAAK,CAACH,oBAAoB,CAAC,CAAC;IAC1D;IACA,MAAM8C,KAAK,GAAG,IAAI,CAAClB,EAAE,CAACU,aAAa,CAAC,CAAC;IACrC,OAAO,IAAI,CAACV,EAAE,CAACI,wBAAwB,CAAC,iBAAiB,EAAE;MAAEc;IAAM,CAAC,EAAEA,KAAK,CAAC;EAChF;EACA;IAAS,IAAI,CAACmC,IAAI,YAAAe,iBAAAb,CAAA;MAAA,YAAAA,CAAA,IAAyFQ,QAAQ,EArFlBrH,EAAE,CAAA8G,QAAA,CAqFkChF,eAAe;IAAA,CAA6C;EAAE;EACnM;IAAS,IAAI,CAACiF,KAAK,kBAtF8E/G,EAAE,CAAAgH,kBAAA;MAAAC,KAAA,EAsFYI,QAAQ;MAAAH,OAAA,EAARG,QAAQ,CAAAV;IAAA,EAAG;EAAE;AAChI;AACA;EAAA,QAAAQ,SAAA,oBAAAA,SAAA,KAxFqGnH,EAAE,CAAAoH,iBAAA,CAwFXC,QAAQ,EAAc,CAAC;IACvGpE,IAAI,EAAEhD;EACV,CAAC,CAAC,EAAkB,MAAM,CAAC;IAAEgD,IAAI,EAAEnB;EAAgB,CAAC,CAAC;AAAA;;AAE7D;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM6F,MAAM,GAAG,IAAIzH,cAAc,CAACiH,SAAS,GAAG,sBAAsB,GAAG,EAAE,CAAC;AAC1E,SAASS,kBAAkBA,CAACC,QAAQ,EAAEC,MAAM,EAAEzC,OAAO,EAAE0C,UAAU,EAAE;EAC/D,OAAO,MAAM;IACT,IAAI,EAAErH,iBAAiB,CAACqH,UAAU,CAAC,IAAI,eAAe,IAAIC,SAAS,IAAI3C,OAAO,CAAC4C,OAAO,KAAK,KAAK,CAAC,EAAE;MAC/F;IACJ;IACA;IACA;IACA;IACAD,SAAS,CAAChG,aAAa,CAACkG,gBAAgB,CAAC,kBAAkB,EAAE,MAAM;MAC/D,IAAIF,SAAS,CAAChG,aAAa,CAACO,UAAU,KAAK,IAAI,EAAE;QAC7CyF,SAAS,CAAChG,aAAa,CAACO,UAAU,CAACY,WAAW,CAAC;UAAEC,MAAM,EAAE;QAAa,CAAC,CAAC;MAC5E;IACJ,CAAC,CAAC;IACF,IAAI+E,gBAAgB;IACpB,IAAI,OAAO9C,OAAO,CAAC+C,oBAAoB,KAAK,UAAU,EAAE;MACpDD,gBAAgB,GAAG9C,OAAO,CAAC+C,oBAAoB,CAAC,CAAC;IACrD,CAAC,MACI;MACD,MAAM,CAACC,QAAQ,EAAE,GAAGC,IAAI,CAAC,GAAG,CAACjD,OAAO,CAAC+C,oBAAoB,IAAI,0BAA0B,EAAEG,KAAK,CAAC,GAAG,CAAC;MACnG,QAAQF,QAAQ;QACZ,KAAK,qBAAqB;UACtBF,gBAAgB,GAAGrH,EAAE,CAAC,IAAI,CAAC;UAC3B;QACJ,KAAK,mBAAmB;UACpBqH,gBAAgB,GAAGK,gBAAgB,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;UAClD;QACJ,KAAK,oBAAoB;UACrBH,gBAAgB,GAAG,CAACG,IAAI,CAAC,CAAC,CAAC,GACrBG,UAAU,CAACZ,QAAQ,CAAC,GACpB3G,KAAK,CAACuH,UAAU,CAACZ,QAAQ,CAAC,EAAEW,gBAAgB,CAAC,CAACF,IAAI,CAAC,CAAC,CAAC,CAAC,CAAC;UAC7D;QACJ;UACI;UACA,MAAM,IAAIzG,KAAK,CAAC,gDAAgDwD,OAAO,CAAC+C,oBAAoB,EAAE,CAAC;MACvG;IACJ;IACA;IACA;IACA;IACA;IACA,MAAMM,MAAM,GAAGb,QAAQ,CAACc,GAAG,CAACxI,MAAM,CAAC;IACnCuI,MAAM,CAACE,iBAAiB,CAAC,MAAMT,gBAAgB,CAC1C7F,IAAI,CAACf,IAAI,CAAC,CAAC,CAAC,CAAC,CACb4E,SAAS,CAAC,MAAM6B,SAAS,CAAChG,aAAa,CACvC6G,QAAQ,CAACf,MAAM,EAAE;MAAEgB,KAAK,EAAEzD,OAAO,CAACyD;IAAM,CAAC,CAAC,CAC1CC,KAAK,CAAEC,GAAG,IAAKC,OAAO,CAACxE,KAAK,CAAC,0CAA0C,EAAEuE,GAAG,CAAC,CAAC,CAAC,CAAC;EACzF,CAAC;AACL;AACA,SAASR,gBAAgBA,CAACU,OAAO,EAAE;EAC/B,OAAOpI,EAAE,CAAC,IAAI,CAAC,CAACwB,IAAI,CAACb,KAAK,CAACyH,OAAO,CAAC,CAAC;AACxC;AACA,SAAST,UAAUA,CAACZ,QAAQ,EAAE;EAC1B,MAAMsB,MAAM,GAAGtB,QAAQ,CAACc,GAAG,CAACvI,cAAc,CAAC;EAC3C,OAAO+I,MAAM,CAACC,QAAQ,CAAC9G,IAAI,CAAClB,MAAM,CAAEiI,MAAM,IAAKA,MAAM,CAAC,CAAC;AAC3D;AACA,SAASC,sBAAsBA,CAACC,IAAI,EAAExB,UAAU,EAAE;EAC9C,OAAO,IAAIjG,eAAe,CAACpB,iBAAiB,CAACqH,UAAU,CAAC,IAAIwB,IAAI,CAACtB,OAAO,KAAK,KAAK,GAAGD,SAAS,CAAChG,aAAa,GAAGyB,SAAS,CAAC;AAC7H;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM+F,qBAAqB,CAAC;AAE5B;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAAC3B,MAAM,EAAEzC,OAAO,GAAG,CAAC,CAAC,EAAE;EAChD,OAAOhF,wBAAwB,CAAC,CAC5BsE,MAAM,EACN0C,QAAQ,EACR;IAAEqC,OAAO,EAAE/B,MAAM;IAAEgC,QAAQ,EAAE7B;EAAO,CAAC,EACrC;IAAE4B,OAAO,EAAEF,qBAAqB;IAAEG,QAAQ,EAAEtE;EAAQ,CAAC,EACrD;IACIqE,OAAO,EAAE5H,eAAe;IACxB8H,UAAU,EAAEN,sBAAsB;IAClCO,IAAI,EAAE,CAACL,qBAAqB,EAAElJ,WAAW;EAC7C,CAAC,EACD;IACIoJ,OAAO,EAAEnJ,eAAe;IACxBqJ,UAAU,EAAEhC,kBAAkB;IAC9BiC,IAAI,EAAE,CAACrJ,QAAQ,EAAEmH,MAAM,EAAE6B,qBAAqB,EAAElJ,WAAW,CAAC;IAC5DwJ,KAAK,EAAE;EACX,CAAC,CACJ,CAAC;AACN;;AAEA;AACA;AACA;AACA,MAAMC,mBAAmB,CAAC;EACtB;AACJ;AACA;AACA;AACA;AACA;EACI,OAAOlB,QAAQA,CAACf,MAAM,EAAEzC,OAAO,GAAG,CAAC,CAAC,EAAE;IAClC,OAAO;MACH2E,QAAQ,EAAED,mBAAmB;MAC7BE,SAAS,EAAE,CAACR,oBAAoB,CAAC3B,MAAM,EAAEzC,OAAO,CAAC;IACrD,CAAC;EACL;EACA;IAAS,IAAI,CAACsB,IAAI,YAAAuD,4BAAArD,CAAA;MAAA,YAAAA,CAAA,IAAyFkD,mBAAmB;IAAA,CAAkD;EAAE;EAClL;IAAS,IAAI,CAACI,IAAI,kBAlO+EnK,EAAE,CAAAoK,gBAAA;MAAAnH,IAAA,EAkOS8G;IAAmB,EAAG;EAAE;EACpI;IAAS,IAAI,CAACM,IAAI,kBAnO+ErK,EAAE,CAAAsK,gBAAA;MAAAL,SAAA,EAmOyC,CAACtF,MAAM,EAAE0C,QAAQ;IAAC,EAAG;EAAE;AACvK;AACA;EAAA,QAAAF,SAAA,oBAAAA,SAAA,KArOqGnH,EAAE,CAAAoH,iBAAA,CAqOX2C,mBAAmB,EAAc,CAAC;IAClH9G,IAAI,EAAExC,QAAQ;IACd6H,IAAI,EAAE,CAAC;MAAE2B,SAAS,EAAE,CAACtF,MAAM,EAAE0C,QAAQ;IAAE,CAAC;EAC5C,CAAC,CAAC;AAAA;;AAEV;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;;AAEA,SAAS0C,mBAAmB,EAAEpF,MAAM,EAAE6E,qBAAqB,EAAEnC,QAAQ,EAAEoC,oBAAoB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}