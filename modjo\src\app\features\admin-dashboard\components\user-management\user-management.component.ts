import { Component, OnInit } from '@angular/core';
import { UserRole, UserManagement, CreateUserRequest } from '../../../../core/models';

@Component({
  selector: 'app-user-management',
  templateUrl: './user-management.component.html',
  styleUrls: ['./user-management.component.css']
})
export class UserManagementComponent implements OnInit {
  users: UserManagement[] = [];
  filteredUsers: UserManagement[] = [];
  searchTerm = '';
  selectedRole: UserRole | 'all' = 'all';
  selectedCity: 'Monastir' | 'Sousse' | 'all' = 'all';
  
  userRoles = Object.values(UserRole);
  cities = ['Monastir', 'Sousse'];
  
  // Pagination
  pageSize = 10;
  currentPage = 0;
  totalUsers = 0;

  constructor() {}

  ngOnInit(): void {
    this.loadUsers();
  }

  private loadUsers(): void {
    // Simulate user data
    this.users = [
      {
        id: '1',
        email: '<EMAIL>',
        name: '<PERSON>',
        role: UserRole.USER,
        city: 'Monastir',
        points: 245,
        isActive: true,
        lastLoginAt: new Date(Date.now() - 1000 * 60 * 30),
        createdAt: new Date('2024-01-15'),
        totalRedemptions: 5
      },
      {
        id: '2',
        email: '<EMAIL>',
        name: 'Fatma Trabelsi',
        role: UserRole.VALIDATOR,
        city: 'Sousse',
        points: 0,
        isActive: true,
        lastLoginAt: new Date(Date.now() - 1000 * 60 * 60 * 2),
        createdAt: new Date('2024-01-10'),
        totalValidations: 89,
        organizationName: 'École Primaire Sousse'
      },
      {
        id: '3',
        email: '<EMAIL>',
        name: 'Mohamed Gharbi',
        role: UserRole.PARTNER,
        city: 'Monastir',
        points: 0,
        isActive: true,
        lastLoginAt: new Date(Date.now() - 1000 * 60 * 60 * 5),
        createdAt: new Date('2024-01-08'),
        organizationName: 'Café des Nattes'
      },
      {
        id: '4',
        email: '<EMAIL>',
        name: 'Leila Mansouri',
        role: UserRole.PROVIDER,
        city: 'Sousse',
        points: 0,
        isActive: true,
        lastLoginAt: new Date(Date.now() - 1000 * 60 * 60 * 24),
        createdAt: new Date('2024-01-05'),
        organizationName: 'Club de Football Sousse'
      },
      {
        id: '5',
        email: '<EMAIL>',
        name: 'Karim Bouazizi',
        role: UserRole.USER,
        city: 'Monastir',
        points: 156,
        isActive: false,
        lastLoginAt: new Date(Date.now() - 1000 * 60 * 60 * 24 * 7),
        createdAt: new Date('2024-01-03'),
        totalRedemptions: 2
      }
    ];
    
    this.totalUsers = this.users.length;
    this.applyFilters();
  }

  applyFilters(): void {
    this.filteredUsers = this.users.filter(user => {
      const matchesSearch = user.name.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                           user.email.toLowerCase().includes(this.searchTerm.toLowerCase()) ||
                           (user.organizationName && user.organizationName.toLowerCase().includes(this.searchTerm.toLowerCase()));
      
      const matchesRole = this.selectedRole === 'all' || user.role === this.selectedRole;
      const matchesCity = this.selectedCity === 'all' || user.city === this.selectedCity;
      
      return matchesSearch && matchesRole && matchesCity;
    });
  }

  onSearchChange(): void {
    this.currentPage = 0;
    this.applyFilters();
  }

  onRoleChange(): void {
    this.currentPage = 0;
    this.applyFilters();
  }

  onCityChange(): void {
    this.currentPage = 0;
    this.applyFilters();
  }

  getRoleDisplayName(role: UserRole): string {
    const roleNames = {
      [UserRole.USER]: 'Utilisateur',
      [UserRole.VALIDATOR]: 'Validateur',
      [UserRole.PARTNER]: 'Partenaire',
      [UserRole.PROVIDER]: 'Prestataire',
      [UserRole.ADMIN]: 'Administrateur'
    };
    return roleNames[role];
  }

  getRoleColor(role: UserRole): string {
    const colors = {
      [UserRole.USER]: '#4CAF50',
      [UserRole.VALIDATOR]: '#2196F3',
      [UserRole.PARTNER]: '#FF9800',
      [UserRole.PROVIDER]: '#9C27B0',
      [UserRole.ADMIN]: '#F44336'
    };
    return colors[role];
  }

  getStatusColor(isActive: boolean): string {
    return isActive ? '#4CAF50' : '#F44336';
  }

  getStatusText(isActive: boolean): string {
    return isActive ? 'Actif' : 'Inactif';
  }

  editUser(user: UserManagement): void {
    console.log('Edit user:', user);
    // TODO: Open edit dialog
  }

  toggleUserStatus(user: UserManagement): void {
    user.isActive = !user.isActive;
    console.log('Toggle user status:', user);
    // TODO: Update user status in backend
  }

  deleteUser(user: UserManagement): void {
    console.log('Delete user:', user);
    // TODO: Show confirmation dialog and delete user
  }

  addUser(): void {
    console.log('Add new user');
    // TODO: Open add user dialog
  }

  exportUsers(): void {
    console.log('Export users');
    // TODO: Export users to CSV/Excel
  }

  getPaginatedUsers(): UserManagement[] {
    const startIndex = this.currentPage * this.pageSize;
    return this.filteredUsers.slice(startIndex, startIndex + this.pageSize);
  }

  getTotalPages(): number {
    return Math.ceil(this.filteredUsers.length / this.pageSize);
  }

  nextPage(): void {
    if (this.currentPage < this.getTotalPages() - 1) {
      this.currentPage++;
    }
  }

  previousPage(): void {
    if (this.currentPage > 0) {
      this.currentPage--;
    }
  }

  goToPage(page: number): void {
    this.currentPage = page;
  }
}
