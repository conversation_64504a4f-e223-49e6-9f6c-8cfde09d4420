{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\nimport { ValidatorDashboardComponent } from './components/validator-dashboard/validator-dashboard.component';\nimport { ValidationListComponent } from './components/validation-list/validation-list.component';\nimport { ValidationHistoryComponent } from './components/validation-history/validation-history.component';\nimport { validatorDashboardRoutes } from './validator-dashboard.routes';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nexport class ValidatorDashboardModule {\n  static {\n    this.ɵfac = function ValidatorDashboardModule_Factory(t) {\n      return new (t || ValidatorDashboardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: ValidatorDashboardModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, RouterModule.forChild(validatorDashboardRoutes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(ValidatorDashboardModule, {\n    declarations: [ValidatorDashboardComponent, ValidationListComponent, ValidationHistoryComponent],\n    imports: [SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SharedModule", "ValidatorDashboardComponent", "ValidationListComponent", "ValidationHistoryComponent", "validatorDashboardRoutes", "ValidatorDashboardModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\validator-dashboard\\validator-dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\nimport { SharedModule } from '../../shared/shared.module';\nimport { ValidatorDashboardComponent } from './components/validator-dashboard/validator-dashboard.component';\nimport { ValidationListComponent } from './components/validation-list/validation-list.component';\nimport { ValidationHistoryComponent } from './components/validation-history/validation-history.component';\nimport { validatorDashboardRoutes } from './validator-dashboard.routes';\n\n@NgModule({\n  declarations: [\n    ValidatorDashboardComponent,\n    ValidationListComponent,\n    ValidationHistoryComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(validatorDashboardRoutes)\n  ]\n})\nexport class ValidatorDashboardModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,2BAA2B,QAAQ,gEAAgE;AAC5G,SAASC,uBAAuB,QAAQ,wDAAwD;AAChG,SAASC,0BAA0B,QAAQ,8DAA8D;AACzG,SAASC,wBAAwB,QAAQ,8BAA8B;;;AAavE,OAAM,MAAOC,wBAAwB;;;uBAAxBA,wBAAwB;IAAA;EAAA;;;YAAxBA;IAAwB;EAAA;;;gBAJjCL,YAAY,EACZD,YAAY,CAACO,QAAQ,CAACF,wBAAwB,CAAC;IAAA;EAAA;;;2EAGtCC,wBAAwB;IAAAE,YAAA,GATjCN,2BAA2B,EAC3BC,uBAAuB,EACvBC,0BAA0B;IAAAK,OAAA,GAG1BR,YAAY,EAAAS,EAAA,CAAAV,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}