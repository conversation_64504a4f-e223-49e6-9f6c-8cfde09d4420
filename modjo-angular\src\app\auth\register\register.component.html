<div class="register-container">
  <div class="register-card">
    <h2>Inscription</h2>
    <p>Créez votre compte AccrédiQR</p>

    <form (ngSubmit)="onSubmit()">
      <div class="form-row">
        <div class="form-group">
          <label for="firstName">Prénom *</label>
          <input 
            type="text" 
            id="firstName" 
            name="firstName" 
            [(ngModel)]="firstName" 
            required 
            placeholder="Votre prénom"
          >
        </div>

        <div class="form-group">
          <label for="lastName">Nom *</label>
          <input 
            type="text" 
            id="lastName" 
            name="lastName" 
            [(ngModel)]="lastName" 
            required 
            placeholder="Votre nom"
          >
        </div>
      </div>

      <div class="form-group">
        <label for="email">Email *</label>
        <input 
          type="email" 
          id="email" 
          name="email" 
          [(ngModel)]="email" 
          required 
          placeholder="Votre adresse email"
        >
      </div>

      <div class="form-row">
        <div class="form-group">
          <label for="password">Mot de passe *</label>
          <input 
            type="password" 
            id="password" 
            name="password" 
            [(ngModel)]="password" 
            required 
            placeholder="Créez un mot de passe"
          >
        </div>

        <div class="form-group">
          <label for="confirmPassword">Confirmer *</label>
          <input 
            type="password" 
            id="confirmPassword" 
            name="confirmPassword" 
            [(ngModel)]="confirmPassword" 
            required 
            placeholder="Confirmez votre mot de passe"
          >
        </div>
      </div>

      <div class="form-group">
        <label for="city">Ville *</label>
        <input 
          type="text" 
          id="city" 
          name="city" 
          [(ngModel)]="city" 
          required 
          placeholder="Votre ville (ex: Monastir, Sousse)"
        >
      </div>

      <div class="form-group">
        <label for="role">Type de compte *</label>
        <select 
          id="role" 
          name="role" 
          [(ngModel)]="role" 
          required
        >
          <option *ngFor="let roleOption of roles" [value]="roleOption.value">
            {{ roleOption.label }}
          </option>
        </select>
      </div>

      <div class="form-group" *ngIf="role === 'provider'">
        <label for="activity">Activité *</label>
        <input 
          type="text" 
          id="activity" 
          name="activity" 
          [(ngModel)]="activity" 
          [required]="role === 'provider'" 
          placeholder="Votre activité (ex: Coiffeur, Étudiant en médecine)"
        >
      </div>

      <div class="error-message" *ngIf="errorMessage">
        {{ errorMessage }}
      </div>

      <button type="submit" [disabled]="isLoading">
        <span *ngIf="isLoading">Chargement...</span>
        <span *ngIf="!isLoading">S'inscrire</span>
      </button>
    </form>

    <div class="register-footer">
      <p>Vous avez déjà un compte ? <a routerLink="/login">Connectez-vous</a></p>
    </div>
  </div>
</div>
