{"ast": null, "code": "import firebase from '@firebase/app-compat';\nimport { FirestoreError, Bytes, _isBase64Available, enableIndexedDbPersistence, enableMultiTabIndexedDbPersistence, clearIndexedDbPersistence, _DatabaseId, _logWarn, connectFirestoreEmulator, enableNetwork, disableNetwork, _validateIsNotUsedTogether, waitForPendingWrites, onSnapshotsInSync, collection, doc, collectionGroup, runTransaction, ensureFirestoreConfigured, WriteBatch as WriteBatch$1, executeWrite, loadBundle, namedQuery, DocumentSnapshot as DocumentSnapshot$1, DocumentReference as DocumentReference$1, _DocumentKey, refEqual, setDoc, updateDoc, deleteDoc, onSnapshot, getDocFromCache, getDocFromServer, getDoc, snapshotEqual, query, where, orderBy, limit, limitToLast, startAt, startAfter, endBefore, endAt, queryEqual, getDocsFromCache, getDocsFromServer, getDocs, QuerySnapshot as QuerySnapshot$1, addDoc, _cast, AbstractUserDataWriter, setLogLevel as setLogLevel$1, QueryDocumentSnapshot as QueryDocumentSnapshot$1, _debugAssert, FieldPath as FieldPath$1, _FieldPath, serverTimestamp, deleteField, arrayUnion, arrayRemove, increment, GeoPoint, Timestamp, CACHE_SIZE_UNLIMITED } from '@firebase/firestore';\nimport { getModularInstance } from '@firebase/util';\nimport { Component } from '@firebase/component';\nconst name = \"@firebase/firestore-compat\";\nconst version = \"0.3.12\";\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction validateSetOptions(methodName, options) {\n  if (options === undefined) {\n    return {\n      merge: false\n    };\n  }\n  if (options.mergeFields !== undefined && options.merge !== undefined) {\n    throw new FirestoreError('invalid-argument', `Invalid options passed to function ${methodName}(): You cannot ` + 'specify both \"merge\" and \"mergeFields\".');\n  }\n  return options;\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/** Helper function to assert Uint8Array is available at runtime. */\nfunction assertUint8ArrayAvailable() {\n  if (typeof Uint8Array === 'undefined') {\n    throw new FirestoreError('unimplemented', 'Uint8Arrays are not available in this environment.');\n  }\n}\n/** Helper function to assert Base64 functions are available at runtime. */\nfunction assertBase64Available() {\n  if (!_isBase64Available()) {\n    throw new FirestoreError('unimplemented', 'Blobs are unavailable in Firestore in this environment.');\n  }\n}\n/** Immutable class holding a blob (binary data) */\nclass Blob {\n  constructor(_delegate) {\n    this._delegate = _delegate;\n  }\n  static fromBase64String(base64) {\n    assertBase64Available();\n    return new Blob(Bytes.fromBase64String(base64));\n  }\n  static fromUint8Array(array) {\n    assertUint8ArrayAvailable();\n    return new Blob(Bytes.fromUint8Array(array));\n  }\n  toBase64() {\n    assertBase64Available();\n    return this._delegate.toBase64();\n  }\n  toUint8Array() {\n    assertUint8ArrayAvailable();\n    return this._delegate.toUint8Array();\n  }\n  isEqual(other) {\n    return this._delegate.isEqual(other._delegate);\n  }\n  toString() {\n    return 'Blob(base64: ' + this.toBase64() + ')';\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nfunction isPartialObserver(obj) {\n  return implementsAnyMethods(obj, ['next', 'error', 'complete']);\n}\n/**\r\n * Returns true if obj is an object and contains at least one of the specified\r\n * methods.\r\n */\nfunction implementsAnyMethods(obj, methods) {\n  if (typeof obj !== 'object' || obj === null) {\n    return false;\n  }\n  const object = obj;\n  for (const method of methods) {\n    if (method in object && typeof object[method] === 'function') {\n      return true;\n    }\n  }\n  return false;\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * The persistence provider included with the full Firestore SDK.\r\n */\nclass IndexedDbPersistenceProvider {\n  enableIndexedDbPersistence(firestore, forceOwnership) {\n    return enableIndexedDbPersistence(firestore._delegate, {\n      forceOwnership\n    });\n  }\n  enableMultiTabIndexedDbPersistence(firestore) {\n    return enableMultiTabIndexedDbPersistence(firestore._delegate);\n  }\n  clearIndexedDbPersistence(firestore) {\n    return clearIndexedDbPersistence(firestore._delegate);\n  }\n}\n/**\r\n * Compat class for Firestore. Exposes Firestore Legacy API, but delegates\r\n * to the functional API of firestore-exp.\r\n */\nclass Firestore {\n  constructor(databaseIdOrApp, _delegate, _persistenceProvider) {\n    this._delegate = _delegate;\n    this._persistenceProvider = _persistenceProvider;\n    this.INTERNAL = {\n      delete: () => this.terminate()\n    };\n    if (!(databaseIdOrApp instanceof _DatabaseId)) {\n      this._appCompat = databaseIdOrApp;\n    }\n  }\n  get _databaseId() {\n    return this._delegate._databaseId;\n  }\n  settings(settingsLiteral) {\n    const currentSettings = this._delegate._getSettings();\n    if (!settingsLiteral.merge && currentSettings.host !== settingsLiteral.host) {\n      _logWarn('You are overriding the original host. If you did not intend ' + 'to override your settings, use {merge: true}.');\n    }\n    if (settingsLiteral.merge) {\n      settingsLiteral = Object.assign(Object.assign({}, currentSettings), settingsLiteral);\n      // Remove the property from the settings once the merge is completed\n      delete settingsLiteral.merge;\n    }\n    this._delegate._setSettings(settingsLiteral);\n  }\n  useEmulator(host, port, options = {}) {\n    connectFirestoreEmulator(this._delegate, host, port, options);\n  }\n  enableNetwork() {\n    return enableNetwork(this._delegate);\n  }\n  disableNetwork() {\n    return disableNetwork(this._delegate);\n  }\n  enablePersistence(settings) {\n    let synchronizeTabs = false;\n    let experimentalForceOwningTab = false;\n    if (settings) {\n      synchronizeTabs = !!settings.synchronizeTabs;\n      experimentalForceOwningTab = !!settings.experimentalForceOwningTab;\n      _validateIsNotUsedTogether('synchronizeTabs', synchronizeTabs, 'experimentalForceOwningTab', experimentalForceOwningTab);\n    }\n    return synchronizeTabs ? this._persistenceProvider.enableMultiTabIndexedDbPersistence(this) : this._persistenceProvider.enableIndexedDbPersistence(this, experimentalForceOwningTab);\n  }\n  clearPersistence() {\n    return this._persistenceProvider.clearIndexedDbPersistence(this);\n  }\n  terminate() {\n    if (this._appCompat) {\n      this._appCompat._removeServiceInstance('firestore-compat');\n      this._appCompat._removeServiceInstance('firestore');\n    }\n    return this._delegate._delete();\n  }\n  waitForPendingWrites() {\n    return waitForPendingWrites(this._delegate);\n  }\n  onSnapshotsInSync(arg) {\n    return onSnapshotsInSync(this._delegate, arg);\n  }\n  get app() {\n    if (!this._appCompat) {\n      throw new FirestoreError('failed-precondition', \"Firestore was not initialized using the Firebase SDK. 'app' is \" + 'not available');\n    }\n    return this._appCompat;\n  }\n  collection(pathString) {\n    try {\n      return new CollectionReference(this, collection(this._delegate, pathString));\n    } catch (e) {\n      throw replaceFunctionName(e, 'collection()', 'Firestore.collection()');\n    }\n  }\n  doc(pathString) {\n    try {\n      return new DocumentReference(this, doc(this._delegate, pathString));\n    } catch (e) {\n      throw replaceFunctionName(e, 'doc()', 'Firestore.doc()');\n    }\n  }\n  collectionGroup(collectionId) {\n    try {\n      return new Query(this, collectionGroup(this._delegate, collectionId));\n    } catch (e) {\n      throw replaceFunctionName(e, 'collectionGroup()', 'Firestore.collectionGroup()');\n    }\n  }\n  runTransaction(updateFunction) {\n    return runTransaction(this._delegate, transaction => updateFunction(new Transaction(this, transaction)));\n  }\n  batch() {\n    ensureFirestoreConfigured(this._delegate);\n    return new WriteBatch(new WriteBatch$1(this._delegate, mutations => executeWrite(this._delegate, mutations)));\n  }\n  loadBundle(bundleData) {\n    return loadBundle(this._delegate, bundleData);\n  }\n  namedQuery(name) {\n    return namedQuery(this._delegate, name).then(expQuery => {\n      if (!expQuery) {\n        return null;\n      }\n      return new Query(this,\n      // We can pass `expQuery` here directly since named queries don't have a UserDataConverter.\n      // Otherwise, we would have to create a new ExpQuery and pass the old UserDataConverter.\n      expQuery);\n    });\n  }\n}\nclass UserDataWriter extends AbstractUserDataWriter {\n  constructor(firestore) {\n    super();\n    this.firestore = firestore;\n  }\n  convertBytes(bytes) {\n    return new Blob(new Bytes(bytes));\n  }\n  convertReference(name) {\n    const key = this.convertDocumentKey(name, this.firestore._databaseId);\n    return DocumentReference.forKey(key, this.firestore, /* converter= */null);\n  }\n}\nfunction setLogLevel(level) {\n  setLogLevel$1(level);\n}\n/**\r\n * A reference to a transaction.\r\n */\nclass Transaction {\n  constructor(_firestore, _delegate) {\n    this._firestore = _firestore;\n    this._delegate = _delegate;\n    this._userDataWriter = new UserDataWriter(_firestore);\n  }\n  get(documentRef) {\n    const ref = castReference(documentRef);\n    return this._delegate.get(ref).then(result => new DocumentSnapshot(this._firestore, new DocumentSnapshot$1(this._firestore._delegate, this._userDataWriter, result._key, result._document, result.metadata, ref.converter)));\n  }\n  set(documentRef, data, options) {\n    const ref = castReference(documentRef);\n    if (options) {\n      validateSetOptions('Transaction.set', options);\n      this._delegate.set(ref, data, options);\n    } else {\n      this._delegate.set(ref, data);\n    }\n    return this;\n  }\n  update(documentRef, dataOrField, value, ...moreFieldsAndValues) {\n    const ref = castReference(documentRef);\n    if (arguments.length === 2) {\n      this._delegate.update(ref, dataOrField);\n    } else {\n      this._delegate.update(ref, dataOrField, value, ...moreFieldsAndValues);\n    }\n    return this;\n  }\n  delete(documentRef) {\n    const ref = castReference(documentRef);\n    this._delegate.delete(ref);\n    return this;\n  }\n}\nclass WriteBatch {\n  constructor(_delegate) {\n    this._delegate = _delegate;\n  }\n  set(documentRef, data, options) {\n    const ref = castReference(documentRef);\n    if (options) {\n      validateSetOptions('WriteBatch.set', options);\n      this._delegate.set(ref, data, options);\n    } else {\n      this._delegate.set(ref, data);\n    }\n    return this;\n  }\n  update(documentRef, dataOrField, value, ...moreFieldsAndValues) {\n    const ref = castReference(documentRef);\n    if (arguments.length === 2) {\n      this._delegate.update(ref, dataOrField);\n    } else {\n      this._delegate.update(ref, dataOrField, value, ...moreFieldsAndValues);\n    }\n    return this;\n  }\n  delete(documentRef) {\n    const ref = castReference(documentRef);\n    this._delegate.delete(ref);\n    return this;\n  }\n  commit() {\n    return this._delegate.commit();\n  }\n}\n/**\r\n * Wraps a `PublicFirestoreDataConverter` translating the types from the\r\n * experimental SDK into corresponding types from the Classic SDK before passing\r\n * them to the wrapped converter.\r\n */\nclass FirestoreDataConverter {\n  constructor(_firestore, _userDataWriter, _delegate) {\n    this._firestore = _firestore;\n    this._userDataWriter = _userDataWriter;\n    this._delegate = _delegate;\n  }\n  fromFirestore(snapshot, options) {\n    const expSnapshot = new QueryDocumentSnapshot$1(this._firestore._delegate, this._userDataWriter, snapshot._key, snapshot._document, snapshot.metadata, /* converter= */null);\n    return this._delegate.fromFirestore(new QueryDocumentSnapshot(this._firestore, expSnapshot), options !== null && options !== void 0 ? options : {});\n  }\n  toFirestore(modelObject, options) {\n    if (!options) {\n      return this._delegate.toFirestore(modelObject);\n    } else {\n      return this._delegate.toFirestore(modelObject, options);\n    }\n  }\n  // Use the same instance of `FirestoreDataConverter` for the given instances\n  // of `Firestore` and `PublicFirestoreDataConverter` so that isEqual() will\n  // compare equal for two objects created with the same converter instance.\n  static getInstance(firestore, converter) {\n    const converterMapByFirestore = FirestoreDataConverter.INSTANCES;\n    let untypedConverterByConverter = converterMapByFirestore.get(firestore);\n    if (!untypedConverterByConverter) {\n      untypedConverterByConverter = new WeakMap();\n      converterMapByFirestore.set(firestore, untypedConverterByConverter);\n    }\n    let instance = untypedConverterByConverter.get(converter);\n    if (!instance) {\n      instance = new FirestoreDataConverter(firestore, new UserDataWriter(firestore), converter);\n      untypedConverterByConverter.set(converter, instance);\n    }\n    return instance;\n  }\n}\nFirestoreDataConverter.INSTANCES = new WeakMap();\n/**\r\n * A reference to a particular document in a collection in the database.\r\n */\nclass DocumentReference {\n  constructor(firestore, _delegate) {\n    this.firestore = firestore;\n    this._delegate = _delegate;\n    this._userDataWriter = new UserDataWriter(firestore);\n  }\n  static forPath(path, firestore, converter) {\n    if (path.length % 2 !== 0) {\n      throw new FirestoreError('invalid-argument', 'Invalid document reference. Document ' + 'references must have an even number of segments, but ' + `${path.canonicalString()} has ${path.length}`);\n    }\n    return new DocumentReference(firestore, new DocumentReference$1(firestore._delegate, converter, new _DocumentKey(path)));\n  }\n  static forKey(key, firestore, converter) {\n    return new DocumentReference(firestore, new DocumentReference$1(firestore._delegate, converter, key));\n  }\n  get id() {\n    return this._delegate.id;\n  }\n  get parent() {\n    return new CollectionReference(this.firestore, this._delegate.parent);\n  }\n  get path() {\n    return this._delegate.path;\n  }\n  collection(pathString) {\n    try {\n      return new CollectionReference(this.firestore, collection(this._delegate, pathString));\n    } catch (e) {\n      throw replaceFunctionName(e, 'collection()', 'DocumentReference.collection()');\n    }\n  }\n  isEqual(other) {\n    other = getModularInstance(other);\n    if (!(other instanceof DocumentReference$1)) {\n      return false;\n    }\n    return refEqual(this._delegate, other);\n  }\n  set(value, options) {\n    options = validateSetOptions('DocumentReference.set', options);\n    try {\n      if (options) {\n        return setDoc(this._delegate, value, options);\n      } else {\n        return setDoc(this._delegate, value);\n      }\n    } catch (e) {\n      throw replaceFunctionName(e, 'setDoc()', 'DocumentReference.set()');\n    }\n  }\n  update(fieldOrUpdateData, value, ...moreFieldsAndValues) {\n    try {\n      if (arguments.length === 1) {\n        return updateDoc(this._delegate, fieldOrUpdateData);\n      } else {\n        return updateDoc(this._delegate, fieldOrUpdateData, value, ...moreFieldsAndValues);\n      }\n    } catch (e) {\n      throw replaceFunctionName(e, 'updateDoc()', 'DocumentReference.update()');\n    }\n  }\n  delete() {\n    return deleteDoc(this._delegate);\n  }\n  onSnapshot(...args) {\n    const options = extractSnapshotOptions(args);\n    const observer = wrapObserver(args, result => new DocumentSnapshot(this.firestore, new DocumentSnapshot$1(this.firestore._delegate, this._userDataWriter, result._key, result._document, result.metadata, this._delegate.converter)));\n    return onSnapshot(this._delegate, options, observer);\n  }\n  get(options) {\n    let snap;\n    if ((options === null || options === void 0 ? void 0 : options.source) === 'cache') {\n      snap = getDocFromCache(this._delegate);\n    } else if ((options === null || options === void 0 ? void 0 : options.source) === 'server') {\n      snap = getDocFromServer(this._delegate);\n    } else {\n      snap = getDoc(this._delegate);\n    }\n    return snap.then(result => new DocumentSnapshot(this.firestore, new DocumentSnapshot$1(this.firestore._delegate, this._userDataWriter, result._key, result._document, result.metadata, this._delegate.converter)));\n  }\n  withConverter(converter) {\n    return new DocumentReference(this.firestore, converter ? this._delegate.withConverter(FirestoreDataConverter.getInstance(this.firestore, converter)) : this._delegate.withConverter(null));\n  }\n}\n/**\r\n * Replaces the function name in an error thrown by the firestore-exp API\r\n * with the function names used in the classic API.\r\n */\nfunction replaceFunctionName(e, original, updated) {\n  e.message = e.message.replace(original, updated);\n  return e;\n}\n/**\r\n * Iterates the list of arguments from an `onSnapshot` call and returns the\r\n * first argument that may be an `SnapshotListenOptions` object. Returns an\r\n * empty object if none is found.\r\n */\nfunction extractSnapshotOptions(args) {\n  for (const arg of args) {\n    if (typeof arg === 'object' && !isPartialObserver(arg)) {\n      return arg;\n    }\n  }\n  return {};\n}\n/**\r\n * Creates an observer that can be passed to the firestore-exp SDK. The\r\n * observer converts all observed values into the format expected by the classic\r\n * SDK.\r\n *\r\n * @param args - The list of arguments from an `onSnapshot` call.\r\n * @param wrapper - The function that converts the firestore-exp type into the\r\n * type used by this shim.\r\n */\nfunction wrapObserver(args, wrapper) {\n  var _a, _b;\n  let userObserver;\n  if (isPartialObserver(args[0])) {\n    userObserver = args[0];\n  } else if (isPartialObserver(args[1])) {\n    userObserver = args[1];\n  } else if (typeof args[0] === 'function') {\n    userObserver = {\n      next: args[0],\n      error: args[1],\n      complete: args[2]\n    };\n  } else {\n    userObserver = {\n      next: args[1],\n      error: args[2],\n      complete: args[3]\n    };\n  }\n  return {\n    next: val => {\n      if (userObserver.next) {\n        userObserver.next(wrapper(val));\n      }\n    },\n    error: (_a = userObserver.error) === null || _a === void 0 ? void 0 : _a.bind(userObserver),\n    complete: (_b = userObserver.complete) === null || _b === void 0 ? void 0 : _b.bind(userObserver)\n  };\n}\nclass DocumentSnapshot {\n  constructor(_firestore, _delegate) {\n    this._firestore = _firestore;\n    this._delegate = _delegate;\n  }\n  get ref() {\n    return new DocumentReference(this._firestore, this._delegate.ref);\n  }\n  get id() {\n    return this._delegate.id;\n  }\n  get metadata() {\n    return this._delegate.metadata;\n  }\n  get exists() {\n    return this._delegate.exists();\n  }\n  data(options) {\n    return this._delegate.data(options);\n  }\n  get(fieldPath, options\n  // We are using `any` here to avoid an explicit cast by our users.\n  // eslint-disable-next-line @typescript-eslint/no-explicit-any\n  ) {\n    return this._delegate.get(fieldPath, options);\n  }\n  isEqual(other) {\n    return snapshotEqual(this._delegate, other._delegate);\n  }\n}\nclass QueryDocumentSnapshot extends DocumentSnapshot {\n  data(options) {\n    const data = this._delegate.data(options);\n    _debugAssert(data !== undefined, 'Document in a QueryDocumentSnapshot should exist');\n    return data;\n  }\n}\nclass Query {\n  constructor(firestore, _delegate) {\n    this.firestore = firestore;\n    this._delegate = _delegate;\n    this._userDataWriter = new UserDataWriter(firestore);\n  }\n  where(fieldPath, opStr, value) {\n    try {\n      // The \"as string\" cast is a little bit of a hack. `where` accepts the\n      // FieldPath Compat type as input, but is not typed as such in order to\n      // not expose this via our public typings file.\n      return new Query(this.firestore, query(this._delegate, where(fieldPath, opStr, value)));\n    } catch (e) {\n      throw replaceFunctionName(e, /(orderBy|where)\\(\\)/, 'Query.$1()');\n    }\n  }\n  orderBy(fieldPath, directionStr) {\n    try {\n      // The \"as string\" cast is a little bit of a hack. `orderBy` accepts the\n      // FieldPath Compat type as input, but is not typed as such in order to\n      // not expose this via our public typings file.\n      return new Query(this.firestore, query(this._delegate, orderBy(fieldPath, directionStr)));\n    } catch (e) {\n      throw replaceFunctionName(e, /(orderBy|where)\\(\\)/, 'Query.$1()');\n    }\n  }\n  limit(n) {\n    try {\n      return new Query(this.firestore, query(this._delegate, limit(n)));\n    } catch (e) {\n      throw replaceFunctionName(e, 'limit()', 'Query.limit()');\n    }\n  }\n  limitToLast(n) {\n    try {\n      return new Query(this.firestore, query(this._delegate, limitToLast(n)));\n    } catch (e) {\n      throw replaceFunctionName(e, 'limitToLast()', 'Query.limitToLast()');\n    }\n  }\n  startAt(...args) {\n    try {\n      return new Query(this.firestore, query(this._delegate, startAt(...args)));\n    } catch (e) {\n      throw replaceFunctionName(e, 'startAt()', 'Query.startAt()');\n    }\n  }\n  startAfter(...args) {\n    try {\n      return new Query(this.firestore, query(this._delegate, startAfter(...args)));\n    } catch (e) {\n      throw replaceFunctionName(e, 'startAfter()', 'Query.startAfter()');\n    }\n  }\n  endBefore(...args) {\n    try {\n      return new Query(this.firestore, query(this._delegate, endBefore(...args)));\n    } catch (e) {\n      throw replaceFunctionName(e, 'endBefore()', 'Query.endBefore()');\n    }\n  }\n  endAt(...args) {\n    try {\n      return new Query(this.firestore, query(this._delegate, endAt(...args)));\n    } catch (e) {\n      throw replaceFunctionName(e, 'endAt()', 'Query.endAt()');\n    }\n  }\n  isEqual(other) {\n    return queryEqual(this._delegate, other._delegate);\n  }\n  get(options) {\n    let query;\n    if ((options === null || options === void 0 ? void 0 : options.source) === 'cache') {\n      query = getDocsFromCache(this._delegate);\n    } else if ((options === null || options === void 0 ? void 0 : options.source) === 'server') {\n      query = getDocsFromServer(this._delegate);\n    } else {\n      query = getDocs(this._delegate);\n    }\n    return query.then(result => new QuerySnapshot(this.firestore, new QuerySnapshot$1(this.firestore._delegate, this._userDataWriter, this._delegate, result._snapshot)));\n  }\n  onSnapshot(...args) {\n    const options = extractSnapshotOptions(args);\n    const observer = wrapObserver(args, snap => new QuerySnapshot(this.firestore, new QuerySnapshot$1(this.firestore._delegate, this._userDataWriter, this._delegate, snap._snapshot)));\n    return onSnapshot(this._delegate, options, observer);\n  }\n  withConverter(converter) {\n    return new Query(this.firestore, converter ? this._delegate.withConverter(FirestoreDataConverter.getInstance(this.firestore, converter)) : this._delegate.withConverter(null));\n  }\n}\nclass DocumentChange {\n  constructor(_firestore, _delegate) {\n    this._firestore = _firestore;\n    this._delegate = _delegate;\n  }\n  get type() {\n    return this._delegate.type;\n  }\n  get doc() {\n    return new QueryDocumentSnapshot(this._firestore, this._delegate.doc);\n  }\n  get oldIndex() {\n    return this._delegate.oldIndex;\n  }\n  get newIndex() {\n    return this._delegate.newIndex;\n  }\n}\nclass QuerySnapshot {\n  constructor(_firestore, _delegate) {\n    this._firestore = _firestore;\n    this._delegate = _delegate;\n  }\n  get query() {\n    return new Query(this._firestore, this._delegate.query);\n  }\n  get metadata() {\n    return this._delegate.metadata;\n  }\n  get size() {\n    return this._delegate.size;\n  }\n  get empty() {\n    return this._delegate.empty;\n  }\n  get docs() {\n    return this._delegate.docs.map(doc => new QueryDocumentSnapshot(this._firestore, doc));\n  }\n  docChanges(options) {\n    return this._delegate.docChanges(options).map(docChange => new DocumentChange(this._firestore, docChange));\n  }\n  forEach(callback, thisArg) {\n    this._delegate.forEach(snapshot => {\n      callback.call(thisArg, new QueryDocumentSnapshot(this._firestore, snapshot));\n    });\n  }\n  isEqual(other) {\n    return snapshotEqual(this._delegate, other._delegate);\n  }\n}\nclass CollectionReference extends Query {\n  constructor(firestore, _delegate) {\n    super(firestore, _delegate);\n    this.firestore = firestore;\n    this._delegate = _delegate;\n  }\n  get id() {\n    return this._delegate.id;\n  }\n  get path() {\n    return this._delegate.path;\n  }\n  get parent() {\n    const docRef = this._delegate.parent;\n    return docRef ? new DocumentReference(this.firestore, docRef) : null;\n  }\n  doc(documentPath) {\n    try {\n      if (documentPath === undefined) {\n        // Call `doc` without `documentPath` if `documentPath` is `undefined`\n        // as `doc` validates the number of arguments to prevent users from\n        // accidentally passing `undefined`.\n        return new DocumentReference(this.firestore, doc(this._delegate));\n      } else {\n        return new DocumentReference(this.firestore, doc(this._delegate, documentPath));\n      }\n    } catch (e) {\n      throw replaceFunctionName(e, 'doc()', 'CollectionReference.doc()');\n    }\n  }\n  add(data) {\n    return addDoc(this._delegate, data).then(docRef => new DocumentReference(this.firestore, docRef));\n  }\n  isEqual(other) {\n    return refEqual(this._delegate, other._delegate);\n  }\n  withConverter(converter) {\n    return new CollectionReference(this.firestore, converter ? this._delegate.withConverter(FirestoreDataConverter.getInstance(this.firestore, converter)) : this._delegate.withConverter(null));\n  }\n}\nfunction castReference(documentRef) {\n  return _cast(documentRef, DocumentReference$1);\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n// The objects that are a part of this API are exposed to third-parties as\n// compiled javascript so we want to flag our private members with a leading\n// underscore to discourage their use.\n/**\r\n * A `FieldPath` refers to a field in a document. The path may consist of a\r\n * single field name (referring to a top-level field in the document), or a list\r\n * of field names (referring to a nested field in the document).\r\n */\nclass FieldPath {\n  /**\r\n   * Creates a FieldPath from the provided field names. If more than one field\r\n   * name is provided, the path will point to a nested field in a document.\r\n   *\r\n   * @param fieldNames - A list of field names.\r\n   */\n  constructor(...fieldNames) {\n    this._delegate = new FieldPath$1(...fieldNames);\n  }\n  static documentId() {\n    /**\r\n     * Internal Note: The backend doesn't technically support querying by\r\n     * document ID. Instead it queries by the entire document name (full path\r\n     * included), but in the cases we currently support documentId(), the net\r\n     * effect is the same.\r\n     */\n    return new FieldPath(_FieldPath.keyField().canonicalString());\n  }\n  isEqual(other) {\n    other = getModularInstance(other);\n    if (!(other instanceof FieldPath$1)) {\n      return false;\n    }\n    return this._delegate._internalPath.isEqual(other._internalPath);\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nclass FieldValue {\n  constructor(_delegate) {\n    this._delegate = _delegate;\n  }\n  static serverTimestamp() {\n    const delegate = serverTimestamp();\n    delegate._methodName = 'FieldValue.serverTimestamp';\n    return new FieldValue(delegate);\n  }\n  static delete() {\n    const delegate = deleteField();\n    delegate._methodName = 'FieldValue.delete';\n    return new FieldValue(delegate);\n  }\n  static arrayUnion(...elements) {\n    const delegate = arrayUnion(...elements);\n    delegate._methodName = 'FieldValue.arrayUnion';\n    return new FieldValue(delegate);\n  }\n  static arrayRemove(...elements) {\n    const delegate = arrayRemove(...elements);\n    delegate._methodName = 'FieldValue.arrayRemove';\n    return new FieldValue(delegate);\n  }\n  static increment(n) {\n    const delegate = increment(n);\n    delegate._methodName = 'FieldValue.increment';\n    return new FieldValue(delegate);\n  }\n  isEqual(other) {\n    return this._delegate.isEqual(other._delegate);\n  }\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\nconst firestoreNamespace = {\n  Firestore,\n  GeoPoint,\n  Timestamp,\n  Blob,\n  Transaction,\n  WriteBatch,\n  DocumentReference,\n  DocumentSnapshot,\n  Query,\n  QueryDocumentSnapshot,\n  QuerySnapshot,\n  CollectionReference,\n  FieldPath,\n  FieldValue,\n  setLogLevel,\n  CACHE_SIZE_UNLIMITED\n};\n/**\r\n * Configures Firestore as part of the Firebase SDK by calling registerComponent.\r\n *\r\n * @param firebase - The FirebaseNamespace to register Firestore with\r\n * @param firestoreFactory - A factory function that returns a new Firestore\r\n *    instance.\r\n */\nfunction configureForFirebase(firebase, firestoreFactory) {\n  firebase.INTERNAL.registerComponent(new Component('firestore-compat', container => {\n    const app = container.getProvider('app-compat').getImmediate();\n    const firestoreExp = container.getProvider('firestore').getImmediate();\n    return firestoreFactory(app, firestoreExp);\n  }, 'PUBLIC').setServiceProps(Object.assign({}, firestoreNamespace)));\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\n/**\r\n * Registers the main Firestore build with the components framework.\r\n * Persistence can be enabled via `firebase.firestore().enablePersistence()`.\r\n */\nfunction registerFirestore(instance) {\n  configureForFirebase(instance, (app, firestoreExp) => new Firestore(app, firestoreExp, new IndexedDbPersistenceProvider()));\n  instance.registerVersion(name, version);\n}\nregisterFirestore(firebase);\nexport { registerFirestore };", "map": {"version": 3, "names": ["firebase", "FirestoreError", "Bytes", "_isBase64Available", "enableIndexedDbPersistence", "enableMultiTabIndexedDbPersistence", "clearIndexedDbPersistence", "_DatabaseId", "_logWarn", "connectFirestoreEmulator", "enableNetwork", "disableNetwork", "_validateIsNotUsedTogether", "waitForPendingWrites", "onSnapshotsInSync", "collection", "doc", "collectionGroup", "runTransaction", "ensureFirestoreConfigured", "WriteBatch", "WriteBatch$1", "executeWrite", "loadBundle", "<PERSON><PERSON><PERSON><PERSON>", "DocumentSnapshot", "DocumentSnapshot$1", "DocumentReference", "DocumentReference$1", "_DocumentKey", "refEqual", "setDoc", "updateDoc", "deleteDoc", "onSnapshot", "getDocFromCache", "getDocFromServer", "getDoc", "snapshotEqual", "query", "where", "orderBy", "limit", "limitToLast", "startAt", "startAfter", "endBefore", "endAt", "queryEqual", "getDocsFromCache", "getDocsFromServer", "getDocs", "QuerySnapshot", "QuerySnapshot$1", "addDoc", "_cast", "AbstractUserDataWriter", "setLogLevel", "setLogLevel$1", "QueryDocumentSnapshot", "QueryDocumentSnapshot$1", "_debugAssert", "FieldPath", "FieldPath$1", "_FieldPath", "serverTimestamp", "deleteField", "arrayUnion", "arrayRemove", "increment", "GeoPoint", "Timestamp", "CACHE_SIZE_UNLIMITED", "getModularInstance", "Component", "name", "version", "validateSetOptions", "methodName", "options", "undefined", "merge", "mergeFields", "assertUint8ArrayAvailable", "Uint8Array", "assertBase64Available", "Blob", "constructor", "_delegate", "fromBase64String", "base64", "fromUint8Array", "array", "toBase64", "toUint8Array", "isEqual", "other", "toString", "isPartialObserver", "obj", "implementsAnyMethods", "methods", "object", "method", "IndexedDbPersistenceProvider", "firestore", "forceOwnership", "Firestore", "databaseIdOrApp", "_persistenceProvider", "INTERNAL", "delete", "terminate", "_appCompat", "_databaseId", "settings", "settingsLiteral", "currentSettings", "_getSettings", "host", "Object", "assign", "_setSettings", "useEmulator", "port", "enablePersistence", "synchronizeTabs", "experimentalForceOwningTab", "clearPersistence", "_removeServiceInstance", "_delete", "arg", "app", "pathString", "CollectionReference", "e", "replaceFunctionName", "collectionId", "Query", "updateFunction", "transaction", "Transaction", "batch", "mutations", "bundleData", "then", "expQuery", "UserDataWriter", "convertBytes", "bytes", "convertReference", "key", "convertDocumentKey", "for<PERSON><PERSON>", "level", "_firestore", "_userDataWriter", "get", "documentRef", "ref", "castReference", "result", "_key", "_document", "metadata", "converter", "set", "data", "update", "dataOrField", "value", "more<PERSON><PERSON>s<PERSON>nd<PERSON><PERSON><PERSON>", "arguments", "length", "commit", "FirestoreDataConverter", "fromFirestore", "snapshot", "expSnapshot", "toFirestore", "modelObject", "getInstance", "converterMapByFirestore", "INSTANCES", "untypedConverterByConverter", "WeakMap", "instance", "for<PERSON><PERSON>", "path", "canonicalString", "id", "parent", "fieldOrUpdateData", "args", "extractSnapshotOptions", "observer", "wrapObserver", "snap", "source", "withConverter", "original", "updated", "message", "replace", "wrapper", "_a", "_b", "userObserver", "next", "error", "complete", "val", "bind", "exists", "fieldPath", "opStr", "directionStr", "n", "_snapshot", "DocumentChange", "type", "oldIndex", "newIndex", "size", "empty", "docs", "map", "do<PERSON><PERSON><PERSON><PERSON>", "doc<PERSON><PERSON><PERSON>", "for<PERSON>ach", "callback", "thisArg", "call", "doc<PERSON>ef", "documentPath", "add", "fieldNames", "documentId", "keyField", "_internalPath", "FieldValue", "delegate", "_methodName", "elements", "firestoreNamespace", "configureForFirebase", "firestoreFactory", "registerComponent", "container", "get<PERSON><PERSON><PERSON>", "getImmediate", "firestoreExp", "setServiceProps", "registerFirestore", "registerVersion"], "sources": ["C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@angular/fire/node_modules/@firebase/firestore-compat/dist/index.esm2017.js"], "sourcesContent": ["import firebase from '@firebase/app-compat';\nimport { FirestoreError, Bytes, _isBase64Available, enableIndexedDbPersistence, enableMultiTabIndexedDbPersistence, clearIndexedDbPersistence, _DatabaseId, _logWarn, connectFirestoreEmulator, enableNetwork, disableNetwork, _validateIsNotUsedTogether, waitForPendingWrites, onSnapshotsInSync, collection, doc, collectionGroup, runTransaction, ensureFirestoreConfigured, WriteBatch as WriteBatch$1, executeWrite, loadBundle, namedQuery, DocumentSnapshot as DocumentSnapshot$1, DocumentReference as DocumentReference$1, _DocumentKey, refEqual, setDoc, updateDoc, deleteDoc, onSnapshot, getDocFromCache, getDocFromServer, getDoc, snapshotEqual, query, where, orderBy, limit, limitToLast, startAt, startAfter, endBefore, endAt, queryEqual, getDocsFromCache, getDocsFromServer, getDocs, QuerySnapshot as QuerySnapshot$1, addDoc, _cast, AbstractUserDataWriter, setLogLevel as setLogLevel$1, QueryDocumentSnapshot as QueryDocumentSnapshot$1, _debugAssert, FieldPath as FieldPath$1, _FieldPath, serverTimestamp, deleteField, arrayUnion, arrayRemove, increment, GeoPoint, Timestamp, CACHE_SIZE_UNLIMITED } from '@firebase/firestore';\nimport { getModularInstance } from '@firebase/util';\nimport { Component } from '@firebase/component';\n\nconst name = \"@firebase/firestore-compat\";\nconst version = \"0.3.12\";\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction validateSetOptions(methodName, options) {\r\n    if (options === undefined) {\r\n        return {\r\n            merge: false\r\n        };\r\n    }\r\n    if (options.mergeFields !== undefined && options.merge !== undefined) {\r\n        throw new FirestoreError('invalid-argument', `Invalid options passed to function ${methodName}(): You cannot ` +\r\n            'specify both \"merge\" and \"mergeFields\".');\r\n    }\r\n    return options;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/** Helper function to assert Uint8Array is available at runtime. */\r\nfunction assertUint8ArrayAvailable() {\r\n    if (typeof Uint8Array === 'undefined') {\r\n        throw new FirestoreError('unimplemented', 'Uint8Arrays are not available in this environment.');\r\n    }\r\n}\r\n/** Helper function to assert Base64 functions are available at runtime. */\r\nfunction assertBase64Available() {\r\n    if (!_isBase64Available()) {\r\n        throw new FirestoreError('unimplemented', 'Blobs are unavailable in Firestore in this environment.');\r\n    }\r\n}\r\n/** Immutable class holding a blob (binary data) */\r\nclass Blob {\r\n    constructor(_delegate) {\r\n        this._delegate = _delegate;\r\n    }\r\n    static fromBase64String(base64) {\r\n        assertBase64Available();\r\n        return new Blob(Bytes.fromBase64String(base64));\r\n    }\r\n    static fromUint8Array(array) {\r\n        assertUint8ArrayAvailable();\r\n        return new Blob(Bytes.fromUint8Array(array));\r\n    }\r\n    toBase64() {\r\n        assertBase64Available();\r\n        return this._delegate.toBase64();\r\n    }\r\n    toUint8Array() {\r\n        assertUint8ArrayAvailable();\r\n        return this._delegate.toUint8Array();\r\n    }\r\n    isEqual(other) {\r\n        return this._delegate.isEqual(other._delegate);\r\n    }\r\n    toString() {\r\n        return 'Blob(base64: ' + this.toBase64() + ')';\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nfunction isPartialObserver(obj) {\r\n    return implementsAnyMethods(obj, ['next', 'error', 'complete']);\r\n}\r\n/**\r\n * Returns true if obj is an object and contains at least one of the specified\r\n * methods.\r\n */\r\nfunction implementsAnyMethods(obj, methods) {\r\n    if (typeof obj !== 'object' || obj === null) {\r\n        return false;\r\n    }\r\n    const object = obj;\r\n    for (const method of methods) {\r\n        if (method in object && typeof object[method] === 'function') {\r\n            return true;\r\n        }\r\n    }\r\n    return false;\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * The persistence provider included with the full Firestore SDK.\r\n */\r\nclass IndexedDbPersistenceProvider {\r\n    enableIndexedDbPersistence(firestore, forceOwnership) {\r\n        return enableIndexedDbPersistence(firestore._delegate, { forceOwnership });\r\n    }\r\n    enableMultiTabIndexedDbPersistence(firestore) {\r\n        return enableMultiTabIndexedDbPersistence(firestore._delegate);\r\n    }\r\n    clearIndexedDbPersistence(firestore) {\r\n        return clearIndexedDbPersistence(firestore._delegate);\r\n    }\r\n}\r\n/**\r\n * Compat class for Firestore. Exposes Firestore Legacy API, but delegates\r\n * to the functional API of firestore-exp.\r\n */\r\nclass Firestore {\r\n    constructor(databaseIdOrApp, _delegate, _persistenceProvider) {\r\n        this._delegate = _delegate;\r\n        this._persistenceProvider = _persistenceProvider;\r\n        this.INTERNAL = {\r\n            delete: () => this.terminate()\r\n        };\r\n        if (!(databaseIdOrApp instanceof _DatabaseId)) {\r\n            this._appCompat = databaseIdOrApp;\r\n        }\r\n    }\r\n    get _databaseId() {\r\n        return this._delegate._databaseId;\r\n    }\r\n    settings(settingsLiteral) {\r\n        const currentSettings = this._delegate._getSettings();\r\n        if (!settingsLiteral.merge &&\r\n            currentSettings.host !== settingsLiteral.host) {\r\n            _logWarn('You are overriding the original host. If you did not intend ' +\r\n                'to override your settings, use {merge: true}.');\r\n        }\r\n        if (settingsLiteral.merge) {\r\n            settingsLiteral = Object.assign(Object.assign({}, currentSettings), settingsLiteral);\r\n            // Remove the property from the settings once the merge is completed\r\n            delete settingsLiteral.merge;\r\n        }\r\n        this._delegate._setSettings(settingsLiteral);\r\n    }\r\n    useEmulator(host, port, options = {}) {\r\n        connectFirestoreEmulator(this._delegate, host, port, options);\r\n    }\r\n    enableNetwork() {\r\n        return enableNetwork(this._delegate);\r\n    }\r\n    disableNetwork() {\r\n        return disableNetwork(this._delegate);\r\n    }\r\n    enablePersistence(settings) {\r\n        let synchronizeTabs = false;\r\n        let experimentalForceOwningTab = false;\r\n        if (settings) {\r\n            synchronizeTabs = !!settings.synchronizeTabs;\r\n            experimentalForceOwningTab = !!settings.experimentalForceOwningTab;\r\n            _validateIsNotUsedTogether('synchronizeTabs', synchronizeTabs, 'experimentalForceOwningTab', experimentalForceOwningTab);\r\n        }\r\n        return synchronizeTabs\r\n            ? this._persistenceProvider.enableMultiTabIndexedDbPersistence(this)\r\n            : this._persistenceProvider.enableIndexedDbPersistence(this, experimentalForceOwningTab);\r\n    }\r\n    clearPersistence() {\r\n        return this._persistenceProvider.clearIndexedDbPersistence(this);\r\n    }\r\n    terminate() {\r\n        if (this._appCompat) {\r\n            this._appCompat._removeServiceInstance('firestore-compat');\r\n            this._appCompat._removeServiceInstance('firestore');\r\n        }\r\n        return this._delegate._delete();\r\n    }\r\n    waitForPendingWrites() {\r\n        return waitForPendingWrites(this._delegate);\r\n    }\r\n    onSnapshotsInSync(arg) {\r\n        return onSnapshotsInSync(this._delegate, arg);\r\n    }\r\n    get app() {\r\n        if (!this._appCompat) {\r\n            throw new FirestoreError('failed-precondition', \"Firestore was not initialized using the Firebase SDK. 'app' is \" +\r\n                'not available');\r\n        }\r\n        return this._appCompat;\r\n    }\r\n    collection(pathString) {\r\n        try {\r\n            return new CollectionReference(this, collection(this._delegate, pathString));\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, 'collection()', 'Firestore.collection()');\r\n        }\r\n    }\r\n    doc(pathString) {\r\n        try {\r\n            return new DocumentReference(this, doc(this._delegate, pathString));\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, 'doc()', 'Firestore.doc()');\r\n        }\r\n    }\r\n    collectionGroup(collectionId) {\r\n        try {\r\n            return new Query(this, collectionGroup(this._delegate, collectionId));\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, 'collectionGroup()', 'Firestore.collectionGroup()');\r\n        }\r\n    }\r\n    runTransaction(updateFunction) {\r\n        return runTransaction(this._delegate, transaction => updateFunction(new Transaction(this, transaction)));\r\n    }\r\n    batch() {\r\n        ensureFirestoreConfigured(this._delegate);\r\n        return new WriteBatch(new WriteBatch$1(this._delegate, mutations => executeWrite(this._delegate, mutations)));\r\n    }\r\n    loadBundle(bundleData) {\r\n        return loadBundle(this._delegate, bundleData);\r\n    }\r\n    namedQuery(name) {\r\n        return namedQuery(this._delegate, name).then(expQuery => {\r\n            if (!expQuery) {\r\n                return null;\r\n            }\r\n            return new Query(this, \r\n            // We can pass `expQuery` here directly since named queries don't have a UserDataConverter.\r\n            // Otherwise, we would have to create a new ExpQuery and pass the old UserDataConverter.\r\n            expQuery);\r\n        });\r\n    }\r\n}\r\nclass UserDataWriter extends AbstractUserDataWriter {\r\n    constructor(firestore) {\r\n        super();\r\n        this.firestore = firestore;\r\n    }\r\n    convertBytes(bytes) {\r\n        return new Blob(new Bytes(bytes));\r\n    }\r\n    convertReference(name) {\r\n        const key = this.convertDocumentKey(name, this.firestore._databaseId);\r\n        return DocumentReference.forKey(key, this.firestore, /* converter= */ null);\r\n    }\r\n}\r\nfunction setLogLevel(level) {\r\n    setLogLevel$1(level);\r\n}\r\n/**\r\n * A reference to a transaction.\r\n */\r\nclass Transaction {\r\n    constructor(_firestore, _delegate) {\r\n        this._firestore = _firestore;\r\n        this._delegate = _delegate;\r\n        this._userDataWriter = new UserDataWriter(_firestore);\r\n    }\r\n    get(documentRef) {\r\n        const ref = castReference(documentRef);\r\n        return this._delegate\r\n            .get(ref)\r\n            .then(result => new DocumentSnapshot(this._firestore, new DocumentSnapshot$1(this._firestore._delegate, this._userDataWriter, result._key, result._document, result.metadata, ref.converter)));\r\n    }\r\n    set(documentRef, data, options) {\r\n        const ref = castReference(documentRef);\r\n        if (options) {\r\n            validateSetOptions('Transaction.set', options);\r\n            this._delegate.set(ref, data, options);\r\n        }\r\n        else {\r\n            this._delegate.set(ref, data);\r\n        }\r\n        return this;\r\n    }\r\n    update(documentRef, dataOrField, value, ...moreFieldsAndValues) {\r\n        const ref = castReference(documentRef);\r\n        if (arguments.length === 2) {\r\n            this._delegate.update(ref, dataOrField);\r\n        }\r\n        else {\r\n            this._delegate.update(ref, dataOrField, value, ...moreFieldsAndValues);\r\n        }\r\n        return this;\r\n    }\r\n    delete(documentRef) {\r\n        const ref = castReference(documentRef);\r\n        this._delegate.delete(ref);\r\n        return this;\r\n    }\r\n}\r\nclass WriteBatch {\r\n    constructor(_delegate) {\r\n        this._delegate = _delegate;\r\n    }\r\n    set(documentRef, data, options) {\r\n        const ref = castReference(documentRef);\r\n        if (options) {\r\n            validateSetOptions('WriteBatch.set', options);\r\n            this._delegate.set(ref, data, options);\r\n        }\r\n        else {\r\n            this._delegate.set(ref, data);\r\n        }\r\n        return this;\r\n    }\r\n    update(documentRef, dataOrField, value, ...moreFieldsAndValues) {\r\n        const ref = castReference(documentRef);\r\n        if (arguments.length === 2) {\r\n            this._delegate.update(ref, dataOrField);\r\n        }\r\n        else {\r\n            this._delegate.update(ref, dataOrField, value, ...moreFieldsAndValues);\r\n        }\r\n        return this;\r\n    }\r\n    delete(documentRef) {\r\n        const ref = castReference(documentRef);\r\n        this._delegate.delete(ref);\r\n        return this;\r\n    }\r\n    commit() {\r\n        return this._delegate.commit();\r\n    }\r\n}\r\n/**\r\n * Wraps a `PublicFirestoreDataConverter` translating the types from the\r\n * experimental SDK into corresponding types from the Classic SDK before passing\r\n * them to the wrapped converter.\r\n */\r\nclass FirestoreDataConverter {\r\n    constructor(_firestore, _userDataWriter, _delegate) {\r\n        this._firestore = _firestore;\r\n        this._userDataWriter = _userDataWriter;\r\n        this._delegate = _delegate;\r\n    }\r\n    fromFirestore(snapshot, options) {\r\n        const expSnapshot = new QueryDocumentSnapshot$1(this._firestore._delegate, this._userDataWriter, snapshot._key, snapshot._document, snapshot.metadata, \r\n        /* converter= */ null);\r\n        return this._delegate.fromFirestore(new QueryDocumentSnapshot(this._firestore, expSnapshot), options !== null && options !== void 0 ? options : {});\r\n    }\r\n    toFirestore(modelObject, options) {\r\n        if (!options) {\r\n            return this._delegate.toFirestore(modelObject);\r\n        }\r\n        else {\r\n            return this._delegate.toFirestore(modelObject, options);\r\n        }\r\n    }\r\n    // Use the same instance of `FirestoreDataConverter` for the given instances\r\n    // of `Firestore` and `PublicFirestoreDataConverter` so that isEqual() will\r\n    // compare equal for two objects created with the same converter instance.\r\n    static getInstance(firestore, converter) {\r\n        const converterMapByFirestore = FirestoreDataConverter.INSTANCES;\r\n        let untypedConverterByConverter = converterMapByFirestore.get(firestore);\r\n        if (!untypedConverterByConverter) {\r\n            untypedConverterByConverter = new WeakMap();\r\n            converterMapByFirestore.set(firestore, untypedConverterByConverter);\r\n        }\r\n        let instance = untypedConverterByConverter.get(converter);\r\n        if (!instance) {\r\n            instance = new FirestoreDataConverter(firestore, new UserDataWriter(firestore), converter);\r\n            untypedConverterByConverter.set(converter, instance);\r\n        }\r\n        return instance;\r\n    }\r\n}\r\nFirestoreDataConverter.INSTANCES = new WeakMap();\r\n/**\r\n * A reference to a particular document in a collection in the database.\r\n */\r\nclass DocumentReference {\r\n    constructor(firestore, _delegate) {\r\n        this.firestore = firestore;\r\n        this._delegate = _delegate;\r\n        this._userDataWriter = new UserDataWriter(firestore);\r\n    }\r\n    static forPath(path, firestore, converter) {\r\n        if (path.length % 2 !== 0) {\r\n            throw new FirestoreError('invalid-argument', 'Invalid document reference. Document ' +\r\n                'references must have an even number of segments, but ' +\r\n                `${path.canonicalString()} has ${path.length}`);\r\n        }\r\n        return new DocumentReference(firestore, new DocumentReference$1(firestore._delegate, converter, new _DocumentKey(path)));\r\n    }\r\n    static forKey(key, firestore, converter) {\r\n        return new DocumentReference(firestore, new DocumentReference$1(firestore._delegate, converter, key));\r\n    }\r\n    get id() {\r\n        return this._delegate.id;\r\n    }\r\n    get parent() {\r\n        return new CollectionReference(this.firestore, this._delegate.parent);\r\n    }\r\n    get path() {\r\n        return this._delegate.path;\r\n    }\r\n    collection(pathString) {\r\n        try {\r\n            return new CollectionReference(this.firestore, collection(this._delegate, pathString));\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, 'collection()', 'DocumentReference.collection()');\r\n        }\r\n    }\r\n    isEqual(other) {\r\n        other = getModularInstance(other);\r\n        if (!(other instanceof DocumentReference$1)) {\r\n            return false;\r\n        }\r\n        return refEqual(this._delegate, other);\r\n    }\r\n    set(value, options) {\r\n        options = validateSetOptions('DocumentReference.set', options);\r\n        try {\r\n            if (options) {\r\n                return setDoc(this._delegate, value, options);\r\n            }\r\n            else {\r\n                return setDoc(this._delegate, value);\r\n            }\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, 'setDoc()', 'DocumentReference.set()');\r\n        }\r\n    }\r\n    update(fieldOrUpdateData, value, ...moreFieldsAndValues) {\r\n        try {\r\n            if (arguments.length === 1) {\r\n                return updateDoc(this._delegate, fieldOrUpdateData);\r\n            }\r\n            else {\r\n                return updateDoc(this._delegate, fieldOrUpdateData, value, ...moreFieldsAndValues);\r\n            }\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, 'updateDoc()', 'DocumentReference.update()');\r\n        }\r\n    }\r\n    delete() {\r\n        return deleteDoc(this._delegate);\r\n    }\r\n    onSnapshot(...args) {\r\n        const options = extractSnapshotOptions(args);\r\n        const observer = wrapObserver(args, result => new DocumentSnapshot(this.firestore, new DocumentSnapshot$1(this.firestore._delegate, this._userDataWriter, result._key, result._document, result.metadata, this._delegate.converter)));\r\n        return onSnapshot(this._delegate, options, observer);\r\n    }\r\n    get(options) {\r\n        let snap;\r\n        if ((options === null || options === void 0 ? void 0 : options.source) === 'cache') {\r\n            snap = getDocFromCache(this._delegate);\r\n        }\r\n        else if ((options === null || options === void 0 ? void 0 : options.source) === 'server') {\r\n            snap = getDocFromServer(this._delegate);\r\n        }\r\n        else {\r\n            snap = getDoc(this._delegate);\r\n        }\r\n        return snap.then(result => new DocumentSnapshot(this.firestore, new DocumentSnapshot$1(this.firestore._delegate, this._userDataWriter, result._key, result._document, result.metadata, this._delegate.converter)));\r\n    }\r\n    withConverter(converter) {\r\n        return new DocumentReference(this.firestore, converter\r\n            ? this._delegate.withConverter(FirestoreDataConverter.getInstance(this.firestore, converter))\r\n            : this._delegate.withConverter(null));\r\n    }\r\n}\r\n/**\r\n * Replaces the function name in an error thrown by the firestore-exp API\r\n * with the function names used in the classic API.\r\n */\r\nfunction replaceFunctionName(e, original, updated) {\r\n    e.message = e.message.replace(original, updated);\r\n    return e;\r\n}\r\n/**\r\n * Iterates the list of arguments from an `onSnapshot` call and returns the\r\n * first argument that may be an `SnapshotListenOptions` object. Returns an\r\n * empty object if none is found.\r\n */\r\nfunction extractSnapshotOptions(args) {\r\n    for (const arg of args) {\r\n        if (typeof arg === 'object' && !isPartialObserver(arg)) {\r\n            return arg;\r\n        }\r\n    }\r\n    return {};\r\n}\r\n/**\r\n * Creates an observer that can be passed to the firestore-exp SDK. The\r\n * observer converts all observed values into the format expected by the classic\r\n * SDK.\r\n *\r\n * @param args - The list of arguments from an `onSnapshot` call.\r\n * @param wrapper - The function that converts the firestore-exp type into the\r\n * type used by this shim.\r\n */\r\nfunction wrapObserver(args, wrapper) {\r\n    var _a, _b;\r\n    let userObserver;\r\n    if (isPartialObserver(args[0])) {\r\n        userObserver = args[0];\r\n    }\r\n    else if (isPartialObserver(args[1])) {\r\n        userObserver = args[1];\r\n    }\r\n    else if (typeof args[0] === 'function') {\r\n        userObserver = {\r\n            next: args[0],\r\n            error: args[1],\r\n            complete: args[2]\r\n        };\r\n    }\r\n    else {\r\n        userObserver = {\r\n            next: args[1],\r\n            error: args[2],\r\n            complete: args[3]\r\n        };\r\n    }\r\n    return {\r\n        next: val => {\r\n            if (userObserver.next) {\r\n                userObserver.next(wrapper(val));\r\n            }\r\n        },\r\n        error: (_a = userObserver.error) === null || _a === void 0 ? void 0 : _a.bind(userObserver),\r\n        complete: (_b = userObserver.complete) === null || _b === void 0 ? void 0 : _b.bind(userObserver)\r\n    };\r\n}\r\nclass DocumentSnapshot {\r\n    constructor(_firestore, _delegate) {\r\n        this._firestore = _firestore;\r\n        this._delegate = _delegate;\r\n    }\r\n    get ref() {\r\n        return new DocumentReference(this._firestore, this._delegate.ref);\r\n    }\r\n    get id() {\r\n        return this._delegate.id;\r\n    }\r\n    get metadata() {\r\n        return this._delegate.metadata;\r\n    }\r\n    get exists() {\r\n        return this._delegate.exists();\r\n    }\r\n    data(options) {\r\n        return this._delegate.data(options);\r\n    }\r\n    get(fieldPath, options\r\n    // We are using `any` here to avoid an explicit cast by our users.\r\n    // eslint-disable-next-line @typescript-eslint/no-explicit-any\r\n    ) {\r\n        return this._delegate.get(fieldPath, options);\r\n    }\r\n    isEqual(other) {\r\n        return snapshotEqual(this._delegate, other._delegate);\r\n    }\r\n}\r\nclass QueryDocumentSnapshot extends DocumentSnapshot {\r\n    data(options) {\r\n        const data = this._delegate.data(options);\r\n        _debugAssert(data !== undefined, 'Document in a QueryDocumentSnapshot should exist');\r\n        return data;\r\n    }\r\n}\r\nclass Query {\r\n    constructor(firestore, _delegate) {\r\n        this.firestore = firestore;\r\n        this._delegate = _delegate;\r\n        this._userDataWriter = new UserDataWriter(firestore);\r\n    }\r\n    where(fieldPath, opStr, value) {\r\n        try {\r\n            // The \"as string\" cast is a little bit of a hack. `where` accepts the\r\n            // FieldPath Compat type as input, but is not typed as such in order to\r\n            // not expose this via our public typings file.\r\n            return new Query(this.firestore, query(this._delegate, where(fieldPath, opStr, value)));\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, /(orderBy|where)\\(\\)/, 'Query.$1()');\r\n        }\r\n    }\r\n    orderBy(fieldPath, directionStr) {\r\n        try {\r\n            // The \"as string\" cast is a little bit of a hack. `orderBy` accepts the\r\n            // FieldPath Compat type as input, but is not typed as such in order to\r\n            // not expose this via our public typings file.\r\n            return new Query(this.firestore, query(this._delegate, orderBy(fieldPath, directionStr)));\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, /(orderBy|where)\\(\\)/, 'Query.$1()');\r\n        }\r\n    }\r\n    limit(n) {\r\n        try {\r\n            return new Query(this.firestore, query(this._delegate, limit(n)));\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, 'limit()', 'Query.limit()');\r\n        }\r\n    }\r\n    limitToLast(n) {\r\n        try {\r\n            return new Query(this.firestore, query(this._delegate, limitToLast(n)));\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, 'limitToLast()', 'Query.limitToLast()');\r\n        }\r\n    }\r\n    startAt(...args) {\r\n        try {\r\n            return new Query(this.firestore, query(this._delegate, startAt(...args)));\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, 'startAt()', 'Query.startAt()');\r\n        }\r\n    }\r\n    startAfter(...args) {\r\n        try {\r\n            return new Query(this.firestore, query(this._delegate, startAfter(...args)));\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, 'startAfter()', 'Query.startAfter()');\r\n        }\r\n    }\r\n    endBefore(...args) {\r\n        try {\r\n            return new Query(this.firestore, query(this._delegate, endBefore(...args)));\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, 'endBefore()', 'Query.endBefore()');\r\n        }\r\n    }\r\n    endAt(...args) {\r\n        try {\r\n            return new Query(this.firestore, query(this._delegate, endAt(...args)));\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, 'endAt()', 'Query.endAt()');\r\n        }\r\n    }\r\n    isEqual(other) {\r\n        return queryEqual(this._delegate, other._delegate);\r\n    }\r\n    get(options) {\r\n        let query;\r\n        if ((options === null || options === void 0 ? void 0 : options.source) === 'cache') {\r\n            query = getDocsFromCache(this._delegate);\r\n        }\r\n        else if ((options === null || options === void 0 ? void 0 : options.source) === 'server') {\r\n            query = getDocsFromServer(this._delegate);\r\n        }\r\n        else {\r\n            query = getDocs(this._delegate);\r\n        }\r\n        return query.then(result => new QuerySnapshot(this.firestore, new QuerySnapshot$1(this.firestore._delegate, this._userDataWriter, this._delegate, result._snapshot)));\r\n    }\r\n    onSnapshot(...args) {\r\n        const options = extractSnapshotOptions(args);\r\n        const observer = wrapObserver(args, snap => new QuerySnapshot(this.firestore, new QuerySnapshot$1(this.firestore._delegate, this._userDataWriter, this._delegate, snap._snapshot)));\r\n        return onSnapshot(this._delegate, options, observer);\r\n    }\r\n    withConverter(converter) {\r\n        return new Query(this.firestore, converter\r\n            ? this._delegate.withConverter(FirestoreDataConverter.getInstance(this.firestore, converter))\r\n            : this._delegate.withConverter(null));\r\n    }\r\n}\r\nclass DocumentChange {\r\n    constructor(_firestore, _delegate) {\r\n        this._firestore = _firestore;\r\n        this._delegate = _delegate;\r\n    }\r\n    get type() {\r\n        return this._delegate.type;\r\n    }\r\n    get doc() {\r\n        return new QueryDocumentSnapshot(this._firestore, this._delegate.doc);\r\n    }\r\n    get oldIndex() {\r\n        return this._delegate.oldIndex;\r\n    }\r\n    get newIndex() {\r\n        return this._delegate.newIndex;\r\n    }\r\n}\r\nclass QuerySnapshot {\r\n    constructor(_firestore, _delegate) {\r\n        this._firestore = _firestore;\r\n        this._delegate = _delegate;\r\n    }\r\n    get query() {\r\n        return new Query(this._firestore, this._delegate.query);\r\n    }\r\n    get metadata() {\r\n        return this._delegate.metadata;\r\n    }\r\n    get size() {\r\n        return this._delegate.size;\r\n    }\r\n    get empty() {\r\n        return this._delegate.empty;\r\n    }\r\n    get docs() {\r\n        return this._delegate.docs.map(doc => new QueryDocumentSnapshot(this._firestore, doc));\r\n    }\r\n    docChanges(options) {\r\n        return this._delegate\r\n            .docChanges(options)\r\n            .map(docChange => new DocumentChange(this._firestore, docChange));\r\n    }\r\n    forEach(callback, thisArg) {\r\n        this._delegate.forEach(snapshot => {\r\n            callback.call(thisArg, new QueryDocumentSnapshot(this._firestore, snapshot));\r\n        });\r\n    }\r\n    isEqual(other) {\r\n        return snapshotEqual(this._delegate, other._delegate);\r\n    }\r\n}\r\nclass CollectionReference extends Query {\r\n    constructor(firestore, _delegate) {\r\n        super(firestore, _delegate);\r\n        this.firestore = firestore;\r\n        this._delegate = _delegate;\r\n    }\r\n    get id() {\r\n        return this._delegate.id;\r\n    }\r\n    get path() {\r\n        return this._delegate.path;\r\n    }\r\n    get parent() {\r\n        const docRef = this._delegate.parent;\r\n        return docRef ? new DocumentReference(this.firestore, docRef) : null;\r\n    }\r\n    doc(documentPath) {\r\n        try {\r\n            if (documentPath === undefined) {\r\n                // Call `doc` without `documentPath` if `documentPath` is `undefined`\r\n                // as `doc` validates the number of arguments to prevent users from\r\n                // accidentally passing `undefined`.\r\n                return new DocumentReference(this.firestore, doc(this._delegate));\r\n            }\r\n            else {\r\n                return new DocumentReference(this.firestore, doc(this._delegate, documentPath));\r\n            }\r\n        }\r\n        catch (e) {\r\n            throw replaceFunctionName(e, 'doc()', 'CollectionReference.doc()');\r\n        }\r\n    }\r\n    add(data) {\r\n        return addDoc(this._delegate, data).then(docRef => new DocumentReference(this.firestore, docRef));\r\n    }\r\n    isEqual(other) {\r\n        return refEqual(this._delegate, other._delegate);\r\n    }\r\n    withConverter(converter) {\r\n        return new CollectionReference(this.firestore, converter\r\n            ? this._delegate.withConverter(FirestoreDataConverter.getInstance(this.firestore, converter))\r\n            : this._delegate.withConverter(null));\r\n    }\r\n}\r\nfunction castReference(documentRef) {\r\n    return _cast(documentRef, DocumentReference$1);\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n// The objects that are a part of this API are exposed to third-parties as\r\n// compiled javascript so we want to flag our private members with a leading\r\n// underscore to discourage their use.\r\n/**\r\n * A `FieldPath` refers to a field in a document. The path may consist of a\r\n * single field name (referring to a top-level field in the document), or a list\r\n * of field names (referring to a nested field in the document).\r\n */\r\nclass FieldPath {\r\n    /**\r\n     * Creates a FieldPath from the provided field names. If more than one field\r\n     * name is provided, the path will point to a nested field in a document.\r\n     *\r\n     * @param fieldNames - A list of field names.\r\n     */\r\n    constructor(...fieldNames) {\r\n        this._delegate = new FieldPath$1(...fieldNames);\r\n    }\r\n    static documentId() {\r\n        /**\r\n         * Internal Note: The backend doesn't technically support querying by\r\n         * document ID. Instead it queries by the entire document name (full path\r\n         * included), but in the cases we currently support documentId(), the net\r\n         * effect is the same.\r\n         */\r\n        return new FieldPath(_FieldPath.keyField().canonicalString());\r\n    }\r\n    isEqual(other) {\r\n        other = getModularInstance(other);\r\n        if (!(other instanceof FieldPath$1)) {\r\n            return false;\r\n        }\r\n        return this._delegate._internalPath.isEqual(other._internalPath);\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2017 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nclass FieldValue {\r\n    constructor(_delegate) {\r\n        this._delegate = _delegate;\r\n    }\r\n    static serverTimestamp() {\r\n        const delegate = serverTimestamp();\r\n        delegate._methodName = 'FieldValue.serverTimestamp';\r\n        return new FieldValue(delegate);\r\n    }\r\n    static delete() {\r\n        const delegate = deleteField();\r\n        delegate._methodName = 'FieldValue.delete';\r\n        return new FieldValue(delegate);\r\n    }\r\n    static arrayUnion(...elements) {\r\n        const delegate = arrayUnion(...elements);\r\n        delegate._methodName = 'FieldValue.arrayUnion';\r\n        return new FieldValue(delegate);\r\n    }\r\n    static arrayRemove(...elements) {\r\n        const delegate = arrayRemove(...elements);\r\n        delegate._methodName = 'FieldValue.arrayRemove';\r\n        return new FieldValue(delegate);\r\n    }\r\n    static increment(n) {\r\n        const delegate = increment(n);\r\n        delegate._methodName = 'FieldValue.increment';\r\n        return new FieldValue(delegate);\r\n    }\r\n    isEqual(other) {\r\n        return this._delegate.isEqual(other._delegate);\r\n    }\r\n}\n\n/**\r\n * @license\r\n * Copyright 2021 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\nconst firestoreNamespace = {\r\n    Firestore,\r\n    GeoPoint,\r\n    Timestamp,\r\n    Blob,\r\n    Transaction,\r\n    WriteBatch,\r\n    DocumentReference,\r\n    DocumentSnapshot,\r\n    Query,\r\n    QueryDocumentSnapshot,\r\n    QuerySnapshot,\r\n    CollectionReference,\r\n    FieldPath,\r\n    FieldValue,\r\n    setLogLevel,\r\n    CACHE_SIZE_UNLIMITED\r\n};\r\n/**\r\n * Configures Firestore as part of the Firebase SDK by calling registerComponent.\r\n *\r\n * @param firebase - The FirebaseNamespace to register Firestore with\r\n * @param firestoreFactory - A factory function that returns a new Firestore\r\n *    instance.\r\n */\r\nfunction configureForFirebase(firebase, firestoreFactory) {\r\n    firebase.INTERNAL.registerComponent(new Component('firestore-compat', container => {\r\n        const app = container.getProvider('app-compat').getImmediate();\r\n        const firestoreExp = container.getProvider('firestore').getImmediate();\r\n        return firestoreFactory(app, firestoreExp);\r\n    }, 'PUBLIC').setServiceProps(Object.assign({}, firestoreNamespace)));\r\n}\n\n/**\r\n * @license\r\n * Copyright 2020 Google LLC\r\n *\r\n * Licensed under the Apache License, Version 2.0 (the \"License\");\r\n * you may not use this file except in compliance with the License.\r\n * You may obtain a copy of the License at\r\n *\r\n *   http://www.apache.org/licenses/LICENSE-2.0\r\n *\r\n * Unless required by applicable law or agreed to in writing, software\r\n * distributed under the License is distributed on an \"AS IS\" BASIS,\r\n * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.\r\n * See the License for the specific language governing permissions and\r\n * limitations under the License.\r\n */\r\n/**\r\n * Registers the main Firestore build with the components framework.\r\n * Persistence can be enabled via `firebase.firestore().enablePersistence()`.\r\n */\r\nfunction registerFirestore(instance) {\r\n    configureForFirebase(instance, (app, firestoreExp) => new Firestore(app, firestoreExp, new IndexedDbPersistenceProvider()));\r\n    instance.registerVersion(name, version);\r\n}\r\nregisterFirestore(firebase);\n\nexport { registerFirestore };\n"], "mappings": "AAAA,OAAOA,QAAQ,MAAM,sBAAsB;AAC3C,SAASC,cAAc,EAAEC,KAAK,EAAEC,kBAAkB,EAAEC,0BAA0B,EAAEC,kCAAkC,EAAEC,yBAAyB,EAAEC,WAAW,EAAEC,QAAQ,EAAEC,wBAAwB,EAAEC,aAAa,EAAEC,cAAc,EAAEC,0BAA0B,EAAEC,oBAAoB,EAAEC,iBAAiB,EAAEC,UAAU,EAAEC,GAAG,EAAEC,eAAe,EAAEC,cAAc,EAAEC,yBAAyB,EAAEC,UAAU,IAAIC,YAAY,EAAEC,YAAY,EAAEC,UAAU,EAAEC,UAAU,EAAEC,gBAAgB,IAAIC,kBAAkB,EAAEC,iBAAiB,IAAIC,mBAAmB,EAAEC,YAAY,EAAEC,QAAQ,EAAEC,MAAM,EAAEC,SAAS,EAAEC,SAAS,EAAEC,UAAU,EAAEC,eAAe,EAAEC,gBAAgB,EAAEC,MAAM,EAAEC,aAAa,EAAEC,KAAK,EAAEC,KAAK,EAAEC,OAAO,EAAEC,KAAK,EAAEC,WAAW,EAAEC,OAAO,EAAEC,UAAU,EAAEC,SAAS,EAAEC,KAAK,EAAEC,UAAU,EAAEC,gBAAgB,EAAEC,iBAAiB,EAAEC,OAAO,EAAEC,aAAa,IAAIC,eAAe,EAAEC,MAAM,EAAEC,KAAK,EAAEC,sBAAsB,EAAEC,WAAW,IAAIC,aAAa,EAAEC,qBAAqB,IAAIC,uBAAuB,EAAEC,YAAY,EAAEC,SAAS,IAAIC,WAAW,EAAEC,UAAU,EAAEC,eAAe,EAAEC,WAAW,EAAEC,UAAU,EAAEC,WAAW,EAAEC,SAAS,EAAEC,QAAQ,EAAEC,SAAS,EAAEC,oBAAoB,QAAQ,qBAAqB;AAClmC,SAASC,kBAAkB,QAAQ,gBAAgB;AACnD,SAASC,SAAS,QAAQ,qBAAqB;AAE/C,MAAMC,IAAI,GAAG,4BAA4B;AACzC,MAAMC,OAAO,GAAG,QAAQ;;AAExB;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASC,kBAAkBA,CAACC,UAAU,EAAEC,OAAO,EAAE;EAC7C,IAAIA,OAAO,KAAKC,SAAS,EAAE;IACvB,OAAO;MACHC,KAAK,EAAE;IACX,CAAC;EACL;EACA,IAAIF,OAAO,CAACG,WAAW,KAAKF,SAAS,IAAID,OAAO,CAACE,KAAK,KAAKD,SAAS,EAAE;IAClE,MAAM,IAAI/E,cAAc,CAAC,kBAAkB,EAAE,sCAAsC6E,UAAU,iBAAiB,GAC1G,yCAAyC,CAAC;EAClD;EACA,OAAOC,OAAO;AAClB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,yBAAyBA,CAAA,EAAG;EACjC,IAAI,OAAOC,UAAU,KAAK,WAAW,EAAE;IACnC,MAAM,IAAInF,cAAc,CAAC,eAAe,EAAE,oDAAoD,CAAC;EACnG;AACJ;AACA;AACA,SAASoF,qBAAqBA,CAAA,EAAG;EAC7B,IAAI,CAAClF,kBAAkB,CAAC,CAAC,EAAE;IACvB,MAAM,IAAIF,cAAc,CAAC,eAAe,EAAE,yDAAyD,CAAC;EACxG;AACJ;AACA;AACA,MAAMqF,IAAI,CAAC;EACPC,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA,OAAOC,gBAAgBA,CAACC,MAAM,EAAE;IAC5BL,qBAAqB,CAAC,CAAC;IACvB,OAAO,IAAIC,IAAI,CAACpF,KAAK,CAACuF,gBAAgB,CAACC,MAAM,CAAC,CAAC;EACnD;EACA,OAAOC,cAAcA,CAACC,KAAK,EAAE;IACzBT,yBAAyB,CAAC,CAAC;IAC3B,OAAO,IAAIG,IAAI,CAACpF,KAAK,CAACyF,cAAc,CAACC,KAAK,CAAC,CAAC;EAChD;EACAC,QAAQA,CAAA,EAAG;IACPR,qBAAqB,CAAC,CAAC;IACvB,OAAO,IAAI,CAACG,SAAS,CAACK,QAAQ,CAAC,CAAC;EACpC;EACAC,YAAYA,CAAA,EAAG;IACXX,yBAAyB,CAAC,CAAC;IAC3B,OAAO,IAAI,CAACK,SAAS,CAACM,YAAY,CAAC,CAAC;EACxC;EACAC,OAAOA,CAACC,KAAK,EAAE;IACX,OAAO,IAAI,CAACR,SAAS,CAACO,OAAO,CAACC,KAAK,CAACR,SAAS,CAAC;EAClD;EACAS,QAAQA,CAAA,EAAG;IACP,OAAO,eAAe,GAAG,IAAI,CAACJ,QAAQ,CAAC,CAAC,GAAG,GAAG;EAClD;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASK,iBAAiBA,CAACC,GAAG,EAAE;EAC5B,OAAOC,oBAAoB,CAACD,GAAG,EAAE,CAAC,MAAM,EAAE,OAAO,EAAE,UAAU,CAAC,CAAC;AACnE;AACA;AACA;AACA;AACA;AACA,SAASC,oBAAoBA,CAACD,GAAG,EAAEE,OAAO,EAAE;EACxC,IAAI,OAAOF,GAAG,KAAK,QAAQ,IAAIA,GAAG,KAAK,IAAI,EAAE;IACzC,OAAO,KAAK;EAChB;EACA,MAAMG,MAAM,GAAGH,GAAG;EAClB,KAAK,MAAMI,MAAM,IAAIF,OAAO,EAAE;IAC1B,IAAIE,MAAM,IAAID,MAAM,IAAI,OAAOA,MAAM,CAACC,MAAM,CAAC,KAAK,UAAU,EAAE;MAC1D,OAAO,IAAI;IACf;EACJ;EACA,OAAO,KAAK;AAChB;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,4BAA4B,CAAC;EAC/BpG,0BAA0BA,CAACqG,SAAS,EAAEC,cAAc,EAAE;IAClD,OAAOtG,0BAA0B,CAACqG,SAAS,CAACjB,SAAS,EAAE;MAAEkB;IAAe,CAAC,CAAC;EAC9E;EACArG,kCAAkCA,CAACoG,SAAS,EAAE;IAC1C,OAAOpG,kCAAkC,CAACoG,SAAS,CAACjB,SAAS,CAAC;EAClE;EACAlF,yBAAyBA,CAACmG,SAAS,EAAE;IACjC,OAAOnG,yBAAyB,CAACmG,SAAS,CAACjB,SAAS,CAAC;EACzD;AACJ;AACA;AACA;AACA;AACA;AACA,MAAMmB,SAAS,CAAC;EACZpB,WAAWA,CAACqB,eAAe,EAAEpB,SAAS,EAAEqB,oBAAoB,EAAE;IAC1D,IAAI,CAACrB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACqB,oBAAoB,GAAGA,oBAAoB;IAChD,IAAI,CAACC,QAAQ,GAAG;MACZC,MAAM,EAAEA,CAAA,KAAM,IAAI,CAACC,SAAS,CAAC;IACjC,CAAC;IACD,IAAI,EAAEJ,eAAe,YAAYrG,WAAW,CAAC,EAAE;MAC3C,IAAI,CAAC0G,UAAU,GAAGL,eAAe;IACrC;EACJ;EACA,IAAIM,WAAWA,CAAA,EAAG;IACd,OAAO,IAAI,CAAC1B,SAAS,CAAC0B,WAAW;EACrC;EACAC,QAAQA,CAACC,eAAe,EAAE;IACtB,MAAMC,eAAe,GAAG,IAAI,CAAC7B,SAAS,CAAC8B,YAAY,CAAC,CAAC;IACrD,IAAI,CAACF,eAAe,CAACnC,KAAK,IACtBoC,eAAe,CAACE,IAAI,KAAKH,eAAe,CAACG,IAAI,EAAE;MAC/C/G,QAAQ,CAAC,8DAA8D,GACnE,+CAA+C,CAAC;IACxD;IACA,IAAI4G,eAAe,CAACnC,KAAK,EAAE;MACvBmC,eAAe,GAAGI,MAAM,CAACC,MAAM,CAACD,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEJ,eAAe,CAAC,EAAED,eAAe,CAAC;MACpF;MACA,OAAOA,eAAe,CAACnC,KAAK;IAChC;IACA,IAAI,CAACO,SAAS,CAACkC,YAAY,CAACN,eAAe,CAAC;EAChD;EACAO,WAAWA,CAACJ,IAAI,EAAEK,IAAI,EAAE7C,OAAO,GAAG,CAAC,CAAC,EAAE;IAClCtE,wBAAwB,CAAC,IAAI,CAAC+E,SAAS,EAAE+B,IAAI,EAAEK,IAAI,EAAE7C,OAAO,CAAC;EACjE;EACArE,aAAaA,CAAA,EAAG;IACZ,OAAOA,aAAa,CAAC,IAAI,CAAC8E,SAAS,CAAC;EACxC;EACA7E,cAAcA,CAAA,EAAG;IACb,OAAOA,cAAc,CAAC,IAAI,CAAC6E,SAAS,CAAC;EACzC;EACAqC,iBAAiBA,CAACV,QAAQ,EAAE;IACxB,IAAIW,eAAe,GAAG,KAAK;IAC3B,IAAIC,0BAA0B,GAAG,KAAK;IACtC,IAAIZ,QAAQ,EAAE;MACVW,eAAe,GAAG,CAAC,CAACX,QAAQ,CAACW,eAAe;MAC5CC,0BAA0B,GAAG,CAAC,CAACZ,QAAQ,CAACY,0BAA0B;MAClEnH,0BAA0B,CAAC,iBAAiB,EAAEkH,eAAe,EAAE,4BAA4B,EAAEC,0BAA0B,CAAC;IAC5H;IACA,OAAOD,eAAe,GAChB,IAAI,CAACjB,oBAAoB,CAACxG,kCAAkC,CAAC,IAAI,CAAC,GAClE,IAAI,CAACwG,oBAAoB,CAACzG,0BAA0B,CAAC,IAAI,EAAE2H,0BAA0B,CAAC;EAChG;EACAC,gBAAgBA,CAAA,EAAG;IACf,OAAO,IAAI,CAACnB,oBAAoB,CAACvG,yBAAyB,CAAC,IAAI,CAAC;EACpE;EACA0G,SAASA,CAAA,EAAG;IACR,IAAI,IAAI,CAACC,UAAU,EAAE;MACjB,IAAI,CAACA,UAAU,CAACgB,sBAAsB,CAAC,kBAAkB,CAAC;MAC1D,IAAI,CAAChB,UAAU,CAACgB,sBAAsB,CAAC,WAAW,CAAC;IACvD;IACA,OAAO,IAAI,CAACzC,SAAS,CAAC0C,OAAO,CAAC,CAAC;EACnC;EACArH,oBAAoBA,CAAA,EAAG;IACnB,OAAOA,oBAAoB,CAAC,IAAI,CAAC2E,SAAS,CAAC;EAC/C;EACA1E,iBAAiBA,CAACqH,GAAG,EAAE;IACnB,OAAOrH,iBAAiB,CAAC,IAAI,CAAC0E,SAAS,EAAE2C,GAAG,CAAC;EACjD;EACA,IAAIC,GAAGA,CAAA,EAAG;IACN,IAAI,CAAC,IAAI,CAACnB,UAAU,EAAE;MAClB,MAAM,IAAIhH,cAAc,CAAC,qBAAqB,EAAE,iEAAiE,GAC7G,eAAe,CAAC;IACxB;IACA,OAAO,IAAI,CAACgH,UAAU;EAC1B;EACAlG,UAAUA,CAACsH,UAAU,EAAE;IACnB,IAAI;MACA,OAAO,IAAIC,mBAAmB,CAAC,IAAI,EAAEvH,UAAU,CAAC,IAAI,CAACyE,SAAS,EAAE6C,UAAU,CAAC,CAAC;IAChF,CAAC,CACD,OAAOE,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,cAAc,EAAE,wBAAwB,CAAC;IAC1E;EACJ;EACAvH,GAAGA,CAACqH,UAAU,EAAE;IACZ,IAAI;MACA,OAAO,IAAI1G,iBAAiB,CAAC,IAAI,EAAEX,GAAG,CAAC,IAAI,CAACwE,SAAS,EAAE6C,UAAU,CAAC,CAAC;IACvE,CAAC,CACD,OAAOE,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,OAAO,EAAE,iBAAiB,CAAC;IAC5D;EACJ;EACAtH,eAAeA,CAACwH,YAAY,EAAE;IAC1B,IAAI;MACA,OAAO,IAAIC,KAAK,CAAC,IAAI,EAAEzH,eAAe,CAAC,IAAI,CAACuE,SAAS,EAAEiD,YAAY,CAAC,CAAC;IACzE,CAAC,CACD,OAAOF,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,mBAAmB,EAAE,6BAA6B,CAAC;IACpF;EACJ;EACArH,cAAcA,CAACyH,cAAc,EAAE;IAC3B,OAAOzH,cAAc,CAAC,IAAI,CAACsE,SAAS,EAAEoD,WAAW,IAAID,cAAc,CAAC,IAAIE,WAAW,CAAC,IAAI,EAAED,WAAW,CAAC,CAAC,CAAC;EAC5G;EACAE,KAAKA,CAAA,EAAG;IACJ3H,yBAAyB,CAAC,IAAI,CAACqE,SAAS,CAAC;IACzC,OAAO,IAAIpE,UAAU,CAAC,IAAIC,YAAY,CAAC,IAAI,CAACmE,SAAS,EAAEuD,SAAS,IAAIzH,YAAY,CAAC,IAAI,CAACkE,SAAS,EAAEuD,SAAS,CAAC,CAAC,CAAC;EACjH;EACAxH,UAAUA,CAACyH,UAAU,EAAE;IACnB,OAAOzH,UAAU,CAAC,IAAI,CAACiE,SAAS,EAAEwD,UAAU,CAAC;EACjD;EACAxH,UAAUA,CAACmD,IAAI,EAAE;IACb,OAAOnD,UAAU,CAAC,IAAI,CAACgE,SAAS,EAAEb,IAAI,CAAC,CAACsE,IAAI,CAACC,QAAQ,IAAI;MACrD,IAAI,CAACA,QAAQ,EAAE;QACX,OAAO,IAAI;MACf;MACA,OAAO,IAAIR,KAAK,CAAC,IAAI;MACrB;MACA;MACAQ,QAAQ,CAAC;IACb,CAAC,CAAC;EACN;AACJ;AACA,MAAMC,cAAc,SAAS3F,sBAAsB,CAAC;EAChD+B,WAAWA,CAACkB,SAAS,EAAE;IACnB,KAAK,CAAC,CAAC;IACP,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA2C,YAAYA,CAACC,KAAK,EAAE;IAChB,OAAO,IAAI/D,IAAI,CAAC,IAAIpF,KAAK,CAACmJ,KAAK,CAAC,CAAC;EACrC;EACAC,gBAAgBA,CAAC3E,IAAI,EAAE;IACnB,MAAM4E,GAAG,GAAG,IAAI,CAACC,kBAAkB,CAAC7E,IAAI,EAAE,IAAI,CAAC8B,SAAS,CAACS,WAAW,CAAC;IACrE,OAAOvF,iBAAiB,CAAC8H,MAAM,CAACF,GAAG,EAAE,IAAI,CAAC9C,SAAS,EAAE,gBAAiB,IAAI,CAAC;EAC/E;AACJ;AACA,SAAShD,WAAWA,CAACiG,KAAK,EAAE;EACxBhG,aAAa,CAACgG,KAAK,CAAC;AACxB;AACA;AACA;AACA;AACA,MAAMb,WAAW,CAAC;EACdtD,WAAWA,CAACoE,UAAU,EAAEnE,SAAS,EAAE;IAC/B,IAAI,CAACmE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACnE,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACoE,eAAe,GAAG,IAAIT,cAAc,CAACQ,UAAU,CAAC;EACzD;EACAE,GAAGA,CAACC,WAAW,EAAE;IACb,MAAMC,GAAG,GAAGC,aAAa,CAACF,WAAW,CAAC;IACtC,OAAO,IAAI,CAACtE,SAAS,CAChBqE,GAAG,CAACE,GAAG,CAAC,CACRd,IAAI,CAACgB,MAAM,IAAI,IAAIxI,gBAAgB,CAAC,IAAI,CAACkI,UAAU,EAAE,IAAIjI,kBAAkB,CAAC,IAAI,CAACiI,UAAU,CAACnE,SAAS,EAAE,IAAI,CAACoE,eAAe,EAAEK,MAAM,CAACC,IAAI,EAAED,MAAM,CAACE,SAAS,EAAEF,MAAM,CAACG,QAAQ,EAAEL,GAAG,CAACM,SAAS,CAAC,CAAC,CAAC;EACtM;EACAC,GAAGA,CAACR,WAAW,EAAES,IAAI,EAAExF,OAAO,EAAE;IAC5B,MAAMgF,GAAG,GAAGC,aAAa,CAACF,WAAW,CAAC;IACtC,IAAI/E,OAAO,EAAE;MACTF,kBAAkB,CAAC,iBAAiB,EAAEE,OAAO,CAAC;MAC9C,IAAI,CAACS,SAAS,CAAC8E,GAAG,CAACP,GAAG,EAAEQ,IAAI,EAAExF,OAAO,CAAC;IAC1C,CAAC,MACI;MACD,IAAI,CAACS,SAAS,CAAC8E,GAAG,CAACP,GAAG,EAAEQ,IAAI,CAAC;IACjC;IACA,OAAO,IAAI;EACf;EACAC,MAAMA,CAACV,WAAW,EAAEW,WAAW,EAAEC,KAAK,EAAE,GAAGC,mBAAmB,EAAE;IAC5D,MAAMZ,GAAG,GAAGC,aAAa,CAACF,WAAW,CAAC;IACtC,IAAIc,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MACxB,IAAI,CAACrF,SAAS,CAACgF,MAAM,CAACT,GAAG,EAAEU,WAAW,CAAC;IAC3C,CAAC,MACI;MACD,IAAI,CAACjF,SAAS,CAACgF,MAAM,CAACT,GAAG,EAAEU,WAAW,EAAEC,KAAK,EAAE,GAAGC,mBAAmB,CAAC;IAC1E;IACA,OAAO,IAAI;EACf;EACA5D,MAAMA,CAAC+C,WAAW,EAAE;IAChB,MAAMC,GAAG,GAAGC,aAAa,CAACF,WAAW,CAAC;IACtC,IAAI,CAACtE,SAAS,CAACuB,MAAM,CAACgD,GAAG,CAAC;IAC1B,OAAO,IAAI;EACf;AACJ;AACA,MAAM3I,UAAU,CAAC;EACbmE,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA8E,GAAGA,CAACR,WAAW,EAAES,IAAI,EAAExF,OAAO,EAAE;IAC5B,MAAMgF,GAAG,GAAGC,aAAa,CAACF,WAAW,CAAC;IACtC,IAAI/E,OAAO,EAAE;MACTF,kBAAkB,CAAC,gBAAgB,EAAEE,OAAO,CAAC;MAC7C,IAAI,CAACS,SAAS,CAAC8E,GAAG,CAACP,GAAG,EAAEQ,IAAI,EAAExF,OAAO,CAAC;IAC1C,CAAC,MACI;MACD,IAAI,CAACS,SAAS,CAAC8E,GAAG,CAACP,GAAG,EAAEQ,IAAI,CAAC;IACjC;IACA,OAAO,IAAI;EACf;EACAC,MAAMA,CAACV,WAAW,EAAEW,WAAW,EAAEC,KAAK,EAAE,GAAGC,mBAAmB,EAAE;IAC5D,MAAMZ,GAAG,GAAGC,aAAa,CAACF,WAAW,CAAC;IACtC,IAAIc,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;MACxB,IAAI,CAACrF,SAAS,CAACgF,MAAM,CAACT,GAAG,EAAEU,WAAW,CAAC;IAC3C,CAAC,MACI;MACD,IAAI,CAACjF,SAAS,CAACgF,MAAM,CAACT,GAAG,EAAEU,WAAW,EAAEC,KAAK,EAAE,GAAGC,mBAAmB,CAAC;IAC1E;IACA,OAAO,IAAI;EACf;EACA5D,MAAMA,CAAC+C,WAAW,EAAE;IAChB,MAAMC,GAAG,GAAGC,aAAa,CAACF,WAAW,CAAC;IACtC,IAAI,CAACtE,SAAS,CAACuB,MAAM,CAACgD,GAAG,CAAC;IAC1B,OAAO,IAAI;EACf;EACAe,MAAMA,CAAA,EAAG;IACL,OAAO,IAAI,CAACtF,SAAS,CAACsF,MAAM,CAAC,CAAC;EAClC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,sBAAsB,CAAC;EACzBxF,WAAWA,CAACoE,UAAU,EAAEC,eAAe,EAAEpE,SAAS,EAAE;IAChD,IAAI,CAACmE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,eAAe,GAAGA,eAAe;IACtC,IAAI,CAACpE,SAAS,GAAGA,SAAS;EAC9B;EACAwF,aAAaA,CAACC,QAAQ,EAAElG,OAAO,EAAE;IAC7B,MAAMmG,WAAW,GAAG,IAAItH,uBAAuB,CAAC,IAAI,CAAC+F,UAAU,CAACnE,SAAS,EAAE,IAAI,CAACoE,eAAe,EAAEqB,QAAQ,CAACf,IAAI,EAAEe,QAAQ,CAACd,SAAS,EAAEc,QAAQ,CAACb,QAAQ,EACrJ,gBAAiB,IAAI,CAAC;IACtB,OAAO,IAAI,CAAC5E,SAAS,CAACwF,aAAa,CAAC,IAAIrH,qBAAqB,CAAC,IAAI,CAACgG,UAAU,EAAEuB,WAAW,CAAC,EAAEnG,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAGA,OAAO,GAAG,CAAC,CAAC,CAAC;EACvJ;EACAoG,WAAWA,CAACC,WAAW,EAAErG,OAAO,EAAE;IAC9B,IAAI,CAACA,OAAO,EAAE;MACV,OAAO,IAAI,CAACS,SAAS,CAAC2F,WAAW,CAACC,WAAW,CAAC;IAClD,CAAC,MACI;MACD,OAAO,IAAI,CAAC5F,SAAS,CAAC2F,WAAW,CAACC,WAAW,EAAErG,OAAO,CAAC;IAC3D;EACJ;EACA;EACA;EACA;EACA,OAAOsG,WAAWA,CAAC5E,SAAS,EAAE4D,SAAS,EAAE;IACrC,MAAMiB,uBAAuB,GAAGP,sBAAsB,CAACQ,SAAS;IAChE,IAAIC,2BAA2B,GAAGF,uBAAuB,CAACzB,GAAG,CAACpD,SAAS,CAAC;IACxE,IAAI,CAAC+E,2BAA2B,EAAE;MAC9BA,2BAA2B,GAAG,IAAIC,OAAO,CAAC,CAAC;MAC3CH,uBAAuB,CAAChB,GAAG,CAAC7D,SAAS,EAAE+E,2BAA2B,CAAC;IACvE;IACA,IAAIE,QAAQ,GAAGF,2BAA2B,CAAC3B,GAAG,CAACQ,SAAS,CAAC;IACzD,IAAI,CAACqB,QAAQ,EAAE;MACXA,QAAQ,GAAG,IAAIX,sBAAsB,CAACtE,SAAS,EAAE,IAAI0C,cAAc,CAAC1C,SAAS,CAAC,EAAE4D,SAAS,CAAC;MAC1FmB,2BAA2B,CAAClB,GAAG,CAACD,SAAS,EAAEqB,QAAQ,CAAC;IACxD;IACA,OAAOA,QAAQ;EACnB;AACJ;AACAX,sBAAsB,CAACQ,SAAS,GAAG,IAAIE,OAAO,CAAC,CAAC;AAChD;AACA;AACA;AACA,MAAM9J,iBAAiB,CAAC;EACpB4D,WAAWA,CAACkB,SAAS,EAAEjB,SAAS,EAAE;IAC9B,IAAI,CAACiB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACjB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACoE,eAAe,GAAG,IAAIT,cAAc,CAAC1C,SAAS,CAAC;EACxD;EACA,OAAOkF,OAAOA,CAACC,IAAI,EAAEnF,SAAS,EAAE4D,SAAS,EAAE;IACvC,IAAIuB,IAAI,CAACf,MAAM,GAAG,CAAC,KAAK,CAAC,EAAE;MACvB,MAAM,IAAI5K,cAAc,CAAC,kBAAkB,EAAE,uCAAuC,GAChF,uDAAuD,GACvD,GAAG2L,IAAI,CAACC,eAAe,CAAC,CAAC,QAAQD,IAAI,CAACf,MAAM,EAAE,CAAC;IACvD;IACA,OAAO,IAAIlJ,iBAAiB,CAAC8E,SAAS,EAAE,IAAI7E,mBAAmB,CAAC6E,SAAS,CAACjB,SAAS,EAAE6E,SAAS,EAAE,IAAIxI,YAAY,CAAC+J,IAAI,CAAC,CAAC,CAAC;EAC5H;EACA,OAAOnC,MAAMA,CAACF,GAAG,EAAE9C,SAAS,EAAE4D,SAAS,EAAE;IACrC,OAAO,IAAI1I,iBAAiB,CAAC8E,SAAS,EAAE,IAAI7E,mBAAmB,CAAC6E,SAAS,CAACjB,SAAS,EAAE6E,SAAS,EAAEd,GAAG,CAAC,CAAC;EACzG;EACA,IAAIuC,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACtG,SAAS,CAACsG,EAAE;EAC5B;EACA,IAAIC,MAAMA,CAAA,EAAG;IACT,OAAO,IAAIzD,mBAAmB,CAAC,IAAI,CAAC7B,SAAS,EAAE,IAAI,CAACjB,SAAS,CAACuG,MAAM,CAAC;EACzE;EACA,IAAIH,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACpG,SAAS,CAACoG,IAAI;EAC9B;EACA7K,UAAUA,CAACsH,UAAU,EAAE;IACnB,IAAI;MACA,OAAO,IAAIC,mBAAmB,CAAC,IAAI,CAAC7B,SAAS,EAAE1F,UAAU,CAAC,IAAI,CAACyE,SAAS,EAAE6C,UAAU,CAAC,CAAC;IAC1F,CAAC,CACD,OAAOE,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,cAAc,EAAE,gCAAgC,CAAC;IAClF;EACJ;EACAxC,OAAOA,CAACC,KAAK,EAAE;IACXA,KAAK,GAAGvB,kBAAkB,CAACuB,KAAK,CAAC;IACjC,IAAI,EAAEA,KAAK,YAAYpE,mBAAmB,CAAC,EAAE;MACzC,OAAO,KAAK;IAChB;IACA,OAAOE,QAAQ,CAAC,IAAI,CAAC0D,SAAS,EAAEQ,KAAK,CAAC;EAC1C;EACAsE,GAAGA,CAACI,KAAK,EAAE3F,OAAO,EAAE;IAChBA,OAAO,GAAGF,kBAAkB,CAAC,uBAAuB,EAAEE,OAAO,CAAC;IAC9D,IAAI;MACA,IAAIA,OAAO,EAAE;QACT,OAAOhD,MAAM,CAAC,IAAI,CAACyD,SAAS,EAAEkF,KAAK,EAAE3F,OAAO,CAAC;MACjD,CAAC,MACI;QACD,OAAOhD,MAAM,CAAC,IAAI,CAACyD,SAAS,EAAEkF,KAAK,CAAC;MACxC;IACJ,CAAC,CACD,OAAOnC,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,UAAU,EAAE,yBAAyB,CAAC;IACvE;EACJ;EACAiC,MAAMA,CAACwB,iBAAiB,EAAEtB,KAAK,EAAE,GAAGC,mBAAmB,EAAE;IACrD,IAAI;MACA,IAAIC,SAAS,CAACC,MAAM,KAAK,CAAC,EAAE;QACxB,OAAO7I,SAAS,CAAC,IAAI,CAACwD,SAAS,EAAEwG,iBAAiB,CAAC;MACvD,CAAC,MACI;QACD,OAAOhK,SAAS,CAAC,IAAI,CAACwD,SAAS,EAAEwG,iBAAiB,EAAEtB,KAAK,EAAE,GAAGC,mBAAmB,CAAC;MACtF;IACJ,CAAC,CACD,OAAOpC,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,aAAa,EAAE,4BAA4B,CAAC;IAC7E;EACJ;EACAxB,MAAMA,CAAA,EAAG;IACL,OAAO9E,SAAS,CAAC,IAAI,CAACuD,SAAS,CAAC;EACpC;EACAtD,UAAUA,CAAC,GAAG+J,IAAI,EAAE;IAChB,MAAMlH,OAAO,GAAGmH,sBAAsB,CAACD,IAAI,CAAC;IAC5C,MAAME,QAAQ,GAAGC,YAAY,CAACH,IAAI,EAAEhC,MAAM,IAAI,IAAIxI,gBAAgB,CAAC,IAAI,CAACgF,SAAS,EAAE,IAAI/E,kBAAkB,CAAC,IAAI,CAAC+E,SAAS,CAACjB,SAAS,EAAE,IAAI,CAACoE,eAAe,EAAEK,MAAM,CAACC,IAAI,EAAED,MAAM,CAACE,SAAS,EAAEF,MAAM,CAACG,QAAQ,EAAE,IAAI,CAAC5E,SAAS,CAAC6E,SAAS,CAAC,CAAC,CAAC;IACrO,OAAOnI,UAAU,CAAC,IAAI,CAACsD,SAAS,EAAET,OAAO,EAAEoH,QAAQ,CAAC;EACxD;EACAtC,GAAGA,CAAC9E,OAAO,EAAE;IACT,IAAIsH,IAAI;IACR,IAAI,CAACtH,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACuH,MAAM,MAAM,OAAO,EAAE;MAChFD,IAAI,GAAGlK,eAAe,CAAC,IAAI,CAACqD,SAAS,CAAC;IAC1C,CAAC,MACI,IAAI,CAACT,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACuH,MAAM,MAAM,QAAQ,EAAE;MACtFD,IAAI,GAAGjK,gBAAgB,CAAC,IAAI,CAACoD,SAAS,CAAC;IAC3C,CAAC,MACI;MACD6G,IAAI,GAAGhK,MAAM,CAAC,IAAI,CAACmD,SAAS,CAAC;IACjC;IACA,OAAO6G,IAAI,CAACpD,IAAI,CAACgB,MAAM,IAAI,IAAIxI,gBAAgB,CAAC,IAAI,CAACgF,SAAS,EAAE,IAAI/E,kBAAkB,CAAC,IAAI,CAAC+E,SAAS,CAACjB,SAAS,EAAE,IAAI,CAACoE,eAAe,EAAEK,MAAM,CAACC,IAAI,EAAED,MAAM,CAACE,SAAS,EAAEF,MAAM,CAACG,QAAQ,EAAE,IAAI,CAAC5E,SAAS,CAAC6E,SAAS,CAAC,CAAC,CAAC;EACtN;EACAkC,aAAaA,CAAClC,SAAS,EAAE;IACrB,OAAO,IAAI1I,iBAAiB,CAAC,IAAI,CAAC8E,SAAS,EAAE4D,SAAS,GAChD,IAAI,CAAC7E,SAAS,CAAC+G,aAAa,CAACxB,sBAAsB,CAACM,WAAW,CAAC,IAAI,CAAC5E,SAAS,EAAE4D,SAAS,CAAC,CAAC,GAC3F,IAAI,CAAC7E,SAAS,CAAC+G,aAAa,CAAC,IAAI,CAAC,CAAC;EAC7C;AACJ;AACA;AACA;AACA;AACA;AACA,SAAS/D,mBAAmBA,CAACD,CAAC,EAAEiE,QAAQ,EAAEC,OAAO,EAAE;EAC/ClE,CAAC,CAACmE,OAAO,GAAGnE,CAAC,CAACmE,OAAO,CAACC,OAAO,CAACH,QAAQ,EAAEC,OAAO,CAAC;EAChD,OAAOlE,CAAC;AACZ;AACA;AACA;AACA;AACA;AACA;AACA,SAAS2D,sBAAsBA,CAACD,IAAI,EAAE;EAClC,KAAK,MAAM9D,GAAG,IAAI8D,IAAI,EAAE;IACpB,IAAI,OAAO9D,GAAG,KAAK,QAAQ,IAAI,CAACjC,iBAAiB,CAACiC,GAAG,CAAC,EAAE;MACpD,OAAOA,GAAG;IACd;EACJ;EACA,OAAO,CAAC,CAAC;AACb;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASiE,YAAYA,CAACH,IAAI,EAAEW,OAAO,EAAE;EACjC,IAAIC,EAAE,EAAEC,EAAE;EACV,IAAIC,YAAY;EAChB,IAAI7G,iBAAiB,CAAC+F,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;IAC5Bc,YAAY,GAAGd,IAAI,CAAC,CAAC,CAAC;EAC1B,CAAC,MACI,IAAI/F,iBAAiB,CAAC+F,IAAI,CAAC,CAAC,CAAC,CAAC,EAAE;IACjCc,YAAY,GAAGd,IAAI,CAAC,CAAC,CAAC;EAC1B,CAAC,MACI,IAAI,OAAOA,IAAI,CAAC,CAAC,CAAC,KAAK,UAAU,EAAE;IACpCc,YAAY,GAAG;MACXC,IAAI,EAAEf,IAAI,CAAC,CAAC,CAAC;MACbgB,KAAK,EAAEhB,IAAI,CAAC,CAAC,CAAC;MACdiB,QAAQ,EAAEjB,IAAI,CAAC,CAAC;IACpB,CAAC;EACL,CAAC,MACI;IACDc,YAAY,GAAG;MACXC,IAAI,EAAEf,IAAI,CAAC,CAAC,CAAC;MACbgB,KAAK,EAAEhB,IAAI,CAAC,CAAC,CAAC;MACdiB,QAAQ,EAAEjB,IAAI,CAAC,CAAC;IACpB,CAAC;EACL;EACA,OAAO;IACHe,IAAI,EAAEG,GAAG,IAAI;MACT,IAAIJ,YAAY,CAACC,IAAI,EAAE;QACnBD,YAAY,CAACC,IAAI,CAACJ,OAAO,CAACO,GAAG,CAAC,CAAC;MACnC;IACJ,CAAC;IACDF,KAAK,EAAE,CAACJ,EAAE,GAAGE,YAAY,CAACE,KAAK,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACO,IAAI,CAACL,YAAY,CAAC;IAC3FG,QAAQ,EAAE,CAACJ,EAAE,GAAGC,YAAY,CAACG,QAAQ,MAAM,IAAI,IAAIJ,EAAE,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,EAAE,CAACM,IAAI,CAACL,YAAY;EACpG,CAAC;AACL;AACA,MAAMtL,gBAAgB,CAAC;EACnB8D,WAAWA,CAACoE,UAAU,EAAEnE,SAAS,EAAE;IAC/B,IAAI,CAACmE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACnE,SAAS,GAAGA,SAAS;EAC9B;EACA,IAAIuE,GAAGA,CAAA,EAAG;IACN,OAAO,IAAIpI,iBAAiB,CAAC,IAAI,CAACgI,UAAU,EAAE,IAAI,CAACnE,SAAS,CAACuE,GAAG,CAAC;EACrE;EACA,IAAI+B,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACtG,SAAS,CAACsG,EAAE;EAC5B;EACA,IAAI1B,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC5E,SAAS,CAAC4E,QAAQ;EAClC;EACA,IAAIiD,MAAMA,CAAA,EAAG;IACT,OAAO,IAAI,CAAC7H,SAAS,CAAC6H,MAAM,CAAC,CAAC;EAClC;EACA9C,IAAIA,CAACxF,OAAO,EAAE;IACV,OAAO,IAAI,CAACS,SAAS,CAAC+E,IAAI,CAACxF,OAAO,CAAC;EACvC;EACA8E,GAAGA,CAACyD,SAAS,EAAEvI;EACf;EACA;EAAA,EACE;IACE,OAAO,IAAI,CAACS,SAAS,CAACqE,GAAG,CAACyD,SAAS,EAAEvI,OAAO,CAAC;EACjD;EACAgB,OAAOA,CAACC,KAAK,EAAE;IACX,OAAO1D,aAAa,CAAC,IAAI,CAACkD,SAAS,EAAEQ,KAAK,CAACR,SAAS,CAAC;EACzD;AACJ;AACA,MAAM7B,qBAAqB,SAASlC,gBAAgB,CAAC;EACjD8I,IAAIA,CAACxF,OAAO,EAAE;IACV,MAAMwF,IAAI,GAAG,IAAI,CAAC/E,SAAS,CAAC+E,IAAI,CAACxF,OAAO,CAAC;IACzClB,YAAY,CAAC0G,IAAI,KAAKvF,SAAS,EAAE,kDAAkD,CAAC;IACpF,OAAOuF,IAAI;EACf;AACJ;AACA,MAAM7B,KAAK,CAAC;EACRnD,WAAWA,CAACkB,SAAS,EAAEjB,SAAS,EAAE;IAC9B,IAAI,CAACiB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACjB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACoE,eAAe,GAAG,IAAIT,cAAc,CAAC1C,SAAS,CAAC;EACxD;EACAjE,KAAKA,CAAC8K,SAAS,EAAEC,KAAK,EAAE7C,KAAK,EAAE;IAC3B,IAAI;MACA;MACA;MACA;MACA,OAAO,IAAIhC,KAAK,CAAC,IAAI,CAACjC,SAAS,EAAElE,KAAK,CAAC,IAAI,CAACiD,SAAS,EAAEhD,KAAK,CAAC8K,SAAS,EAAEC,KAAK,EAAE7C,KAAK,CAAC,CAAC,CAAC;IAC3F,CAAC,CACD,OAAOnC,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,qBAAqB,EAAE,YAAY,CAAC;IACrE;EACJ;EACA9F,OAAOA,CAAC6K,SAAS,EAAEE,YAAY,EAAE;IAC7B,IAAI;MACA;MACA;MACA;MACA,OAAO,IAAI9E,KAAK,CAAC,IAAI,CAACjC,SAAS,EAAElE,KAAK,CAAC,IAAI,CAACiD,SAAS,EAAE/C,OAAO,CAAC6K,SAAS,EAAEE,YAAY,CAAC,CAAC,CAAC;IAC7F,CAAC,CACD,OAAOjF,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,qBAAqB,EAAE,YAAY,CAAC;IACrE;EACJ;EACA7F,KAAKA,CAAC+K,CAAC,EAAE;IACL,IAAI;MACA,OAAO,IAAI/E,KAAK,CAAC,IAAI,CAACjC,SAAS,EAAElE,KAAK,CAAC,IAAI,CAACiD,SAAS,EAAE9C,KAAK,CAAC+K,CAAC,CAAC,CAAC,CAAC;IACrE,CAAC,CACD,OAAOlF,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,SAAS,EAAE,eAAe,CAAC;IAC5D;EACJ;EACA5F,WAAWA,CAAC8K,CAAC,EAAE;IACX,IAAI;MACA,OAAO,IAAI/E,KAAK,CAAC,IAAI,CAACjC,SAAS,EAAElE,KAAK,CAAC,IAAI,CAACiD,SAAS,EAAE7C,WAAW,CAAC8K,CAAC,CAAC,CAAC,CAAC;IAC3E,CAAC,CACD,OAAOlF,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,eAAe,EAAE,qBAAqB,CAAC;IACxE;EACJ;EACA3F,OAAOA,CAAC,GAAGqJ,IAAI,EAAE;IACb,IAAI;MACA,OAAO,IAAIvD,KAAK,CAAC,IAAI,CAACjC,SAAS,EAAElE,KAAK,CAAC,IAAI,CAACiD,SAAS,EAAE5C,OAAO,CAAC,GAAGqJ,IAAI,CAAC,CAAC,CAAC;IAC7E,CAAC,CACD,OAAO1D,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,WAAW,EAAE,iBAAiB,CAAC;IAChE;EACJ;EACA1F,UAAUA,CAAC,GAAGoJ,IAAI,EAAE;IAChB,IAAI;MACA,OAAO,IAAIvD,KAAK,CAAC,IAAI,CAACjC,SAAS,EAAElE,KAAK,CAAC,IAAI,CAACiD,SAAS,EAAE3C,UAAU,CAAC,GAAGoJ,IAAI,CAAC,CAAC,CAAC;IAChF,CAAC,CACD,OAAO1D,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,cAAc,EAAE,oBAAoB,CAAC;IACtE;EACJ;EACAzF,SAASA,CAAC,GAAGmJ,IAAI,EAAE;IACf,IAAI;MACA,OAAO,IAAIvD,KAAK,CAAC,IAAI,CAACjC,SAAS,EAAElE,KAAK,CAAC,IAAI,CAACiD,SAAS,EAAE1C,SAAS,CAAC,GAAGmJ,IAAI,CAAC,CAAC,CAAC;IAC/E,CAAC,CACD,OAAO1D,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,aAAa,EAAE,mBAAmB,CAAC;IACpE;EACJ;EACAxF,KAAKA,CAAC,GAAGkJ,IAAI,EAAE;IACX,IAAI;MACA,OAAO,IAAIvD,KAAK,CAAC,IAAI,CAACjC,SAAS,EAAElE,KAAK,CAAC,IAAI,CAACiD,SAAS,EAAEzC,KAAK,CAAC,GAAGkJ,IAAI,CAAC,CAAC,CAAC;IAC3E,CAAC,CACD,OAAO1D,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,SAAS,EAAE,eAAe,CAAC;IAC5D;EACJ;EACAxC,OAAOA,CAACC,KAAK,EAAE;IACX,OAAOhD,UAAU,CAAC,IAAI,CAACwC,SAAS,EAAEQ,KAAK,CAACR,SAAS,CAAC;EACtD;EACAqE,GAAGA,CAAC9E,OAAO,EAAE;IACT,IAAIxC,KAAK;IACT,IAAI,CAACwC,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACuH,MAAM,MAAM,OAAO,EAAE;MAChF/J,KAAK,GAAGU,gBAAgB,CAAC,IAAI,CAACuC,SAAS,CAAC;IAC5C,CAAC,MACI,IAAI,CAACT,OAAO,KAAK,IAAI,IAAIA,OAAO,KAAK,KAAK,CAAC,GAAG,KAAK,CAAC,GAAGA,OAAO,CAACuH,MAAM,MAAM,QAAQ,EAAE;MACtF/J,KAAK,GAAGW,iBAAiB,CAAC,IAAI,CAACsC,SAAS,CAAC;IAC7C,CAAC,MACI;MACDjD,KAAK,GAAGY,OAAO,CAAC,IAAI,CAACqC,SAAS,CAAC;IACnC;IACA,OAAOjD,KAAK,CAAC0G,IAAI,CAACgB,MAAM,IAAI,IAAI7G,aAAa,CAAC,IAAI,CAACqD,SAAS,EAAE,IAAIpD,eAAe,CAAC,IAAI,CAACoD,SAAS,CAACjB,SAAS,EAAE,IAAI,CAACoE,eAAe,EAAE,IAAI,CAACpE,SAAS,EAAEyE,MAAM,CAACyD,SAAS,CAAC,CAAC,CAAC;EACzK;EACAxL,UAAUA,CAAC,GAAG+J,IAAI,EAAE;IAChB,MAAMlH,OAAO,GAAGmH,sBAAsB,CAACD,IAAI,CAAC;IAC5C,MAAME,QAAQ,GAAGC,YAAY,CAACH,IAAI,EAAEI,IAAI,IAAI,IAAIjJ,aAAa,CAAC,IAAI,CAACqD,SAAS,EAAE,IAAIpD,eAAe,CAAC,IAAI,CAACoD,SAAS,CAACjB,SAAS,EAAE,IAAI,CAACoE,eAAe,EAAE,IAAI,CAACpE,SAAS,EAAE6G,IAAI,CAACqB,SAAS,CAAC,CAAC,CAAC;IACnL,OAAOxL,UAAU,CAAC,IAAI,CAACsD,SAAS,EAAET,OAAO,EAAEoH,QAAQ,CAAC;EACxD;EACAI,aAAaA,CAAClC,SAAS,EAAE;IACrB,OAAO,IAAI3B,KAAK,CAAC,IAAI,CAACjC,SAAS,EAAE4D,SAAS,GACpC,IAAI,CAAC7E,SAAS,CAAC+G,aAAa,CAACxB,sBAAsB,CAACM,WAAW,CAAC,IAAI,CAAC5E,SAAS,EAAE4D,SAAS,CAAC,CAAC,GAC3F,IAAI,CAAC7E,SAAS,CAAC+G,aAAa,CAAC,IAAI,CAAC,CAAC;EAC7C;AACJ;AACA,MAAMoB,cAAc,CAAC;EACjBpI,WAAWA,CAACoE,UAAU,EAAEnE,SAAS,EAAE;IAC/B,IAAI,CAACmE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACnE,SAAS,GAAGA,SAAS;EAC9B;EACA,IAAIoI,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACpI,SAAS,CAACoI,IAAI;EAC9B;EACA,IAAI5M,GAAGA,CAAA,EAAG;IACN,OAAO,IAAI2C,qBAAqB,CAAC,IAAI,CAACgG,UAAU,EAAE,IAAI,CAACnE,SAAS,CAACxE,GAAG,CAAC;EACzE;EACA,IAAI6M,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACrI,SAAS,CAACqI,QAAQ;EAClC;EACA,IAAIC,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAACtI,SAAS,CAACsI,QAAQ;EAClC;AACJ;AACA,MAAM1K,aAAa,CAAC;EAChBmC,WAAWA,CAACoE,UAAU,EAAEnE,SAAS,EAAE;IAC/B,IAAI,CAACmE,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACnE,SAAS,GAAGA,SAAS;EAC9B;EACA,IAAIjD,KAAKA,CAAA,EAAG;IACR,OAAO,IAAImG,KAAK,CAAC,IAAI,CAACiB,UAAU,EAAE,IAAI,CAACnE,SAAS,CAACjD,KAAK,CAAC;EAC3D;EACA,IAAI6H,QAAQA,CAAA,EAAG;IACX,OAAO,IAAI,CAAC5E,SAAS,CAAC4E,QAAQ;EAClC;EACA,IAAI2D,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACvI,SAAS,CAACuI,IAAI;EAC9B;EACA,IAAIC,KAAKA,CAAA,EAAG;IACR,OAAO,IAAI,CAACxI,SAAS,CAACwI,KAAK;EAC/B;EACA,IAAIC,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACzI,SAAS,CAACyI,IAAI,CAACC,GAAG,CAAClN,GAAG,IAAI,IAAI2C,qBAAqB,CAAC,IAAI,CAACgG,UAAU,EAAE3I,GAAG,CAAC,CAAC;EAC1F;EACAmN,UAAUA,CAACpJ,OAAO,EAAE;IAChB,OAAO,IAAI,CAACS,SAAS,CAChB2I,UAAU,CAACpJ,OAAO,CAAC,CACnBmJ,GAAG,CAACE,SAAS,IAAI,IAAIT,cAAc,CAAC,IAAI,CAAChE,UAAU,EAAEyE,SAAS,CAAC,CAAC;EACzE;EACAC,OAAOA,CAACC,QAAQ,EAAEC,OAAO,EAAE;IACvB,IAAI,CAAC/I,SAAS,CAAC6I,OAAO,CAACpD,QAAQ,IAAI;MAC/BqD,QAAQ,CAACE,IAAI,CAACD,OAAO,EAAE,IAAI5K,qBAAqB,CAAC,IAAI,CAACgG,UAAU,EAAEsB,QAAQ,CAAC,CAAC;IAChF,CAAC,CAAC;EACN;EACAlF,OAAOA,CAACC,KAAK,EAAE;IACX,OAAO1D,aAAa,CAAC,IAAI,CAACkD,SAAS,EAAEQ,KAAK,CAACR,SAAS,CAAC;EACzD;AACJ;AACA,MAAM8C,mBAAmB,SAASI,KAAK,CAAC;EACpCnD,WAAWA,CAACkB,SAAS,EAAEjB,SAAS,EAAE;IAC9B,KAAK,CAACiB,SAAS,EAAEjB,SAAS,CAAC;IAC3B,IAAI,CAACiB,SAAS,GAAGA,SAAS;IAC1B,IAAI,CAACjB,SAAS,GAAGA,SAAS;EAC9B;EACA,IAAIsG,EAAEA,CAAA,EAAG;IACL,OAAO,IAAI,CAACtG,SAAS,CAACsG,EAAE;EAC5B;EACA,IAAIF,IAAIA,CAAA,EAAG;IACP,OAAO,IAAI,CAACpG,SAAS,CAACoG,IAAI;EAC9B;EACA,IAAIG,MAAMA,CAAA,EAAG;IACT,MAAM0C,MAAM,GAAG,IAAI,CAACjJ,SAAS,CAACuG,MAAM;IACpC,OAAO0C,MAAM,GAAG,IAAI9M,iBAAiB,CAAC,IAAI,CAAC8E,SAAS,EAAEgI,MAAM,CAAC,GAAG,IAAI;EACxE;EACAzN,GAAGA,CAAC0N,YAAY,EAAE;IACd,IAAI;MACA,IAAIA,YAAY,KAAK1J,SAAS,EAAE;QAC5B;QACA;QACA;QACA,OAAO,IAAIrD,iBAAiB,CAAC,IAAI,CAAC8E,SAAS,EAAEzF,GAAG,CAAC,IAAI,CAACwE,SAAS,CAAC,CAAC;MACrE,CAAC,MACI;QACD,OAAO,IAAI7D,iBAAiB,CAAC,IAAI,CAAC8E,SAAS,EAAEzF,GAAG,CAAC,IAAI,CAACwE,SAAS,EAAEkJ,YAAY,CAAC,CAAC;MACnF;IACJ,CAAC,CACD,OAAOnG,CAAC,EAAE;MACN,MAAMC,mBAAmB,CAACD,CAAC,EAAE,OAAO,EAAE,2BAA2B,CAAC;IACtE;EACJ;EACAoG,GAAGA,CAACpE,IAAI,EAAE;IACN,OAAOjH,MAAM,CAAC,IAAI,CAACkC,SAAS,EAAE+E,IAAI,CAAC,CAACtB,IAAI,CAACwF,MAAM,IAAI,IAAI9M,iBAAiB,CAAC,IAAI,CAAC8E,SAAS,EAAEgI,MAAM,CAAC,CAAC;EACrG;EACA1I,OAAOA,CAACC,KAAK,EAAE;IACX,OAAOlE,QAAQ,CAAC,IAAI,CAAC0D,SAAS,EAAEQ,KAAK,CAACR,SAAS,CAAC;EACpD;EACA+G,aAAaA,CAAClC,SAAS,EAAE;IACrB,OAAO,IAAI/B,mBAAmB,CAAC,IAAI,CAAC7B,SAAS,EAAE4D,SAAS,GAClD,IAAI,CAAC7E,SAAS,CAAC+G,aAAa,CAACxB,sBAAsB,CAACM,WAAW,CAAC,IAAI,CAAC5E,SAAS,EAAE4D,SAAS,CAAC,CAAC,GAC3F,IAAI,CAAC7E,SAAS,CAAC+G,aAAa,CAAC,IAAI,CAAC,CAAC;EAC7C;AACJ;AACA,SAASvC,aAAaA,CAACF,WAAW,EAAE;EAChC,OAAOvG,KAAK,CAACuG,WAAW,EAAElI,mBAAmB,CAAC;AAClD;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMkC,SAAS,CAAC;EACZ;AACJ;AACA;AACA;AACA;AACA;EACIyB,WAAWA,CAAC,GAAGqJ,UAAU,EAAE;IACvB,IAAI,CAACpJ,SAAS,GAAG,IAAIzB,WAAW,CAAC,GAAG6K,UAAU,CAAC;EACnD;EACA,OAAOC,UAAUA,CAAA,EAAG;IAChB;AACR;AACA;AACA;AACA;AACA;IACQ,OAAO,IAAI/K,SAAS,CAACE,UAAU,CAAC8K,QAAQ,CAAC,CAAC,CAACjD,eAAe,CAAC,CAAC,CAAC;EACjE;EACA9F,OAAOA,CAACC,KAAK,EAAE;IACXA,KAAK,GAAGvB,kBAAkB,CAACuB,KAAK,CAAC;IACjC,IAAI,EAAEA,KAAK,YAAYjC,WAAW,CAAC,EAAE;MACjC,OAAO,KAAK;IAChB;IACA,OAAO,IAAI,CAACyB,SAAS,CAACuJ,aAAa,CAAChJ,OAAO,CAACC,KAAK,CAAC+I,aAAa,CAAC;EACpE;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAMC,UAAU,CAAC;EACbzJ,WAAWA,CAACC,SAAS,EAAE;IACnB,IAAI,CAACA,SAAS,GAAGA,SAAS;EAC9B;EACA,OAAOvB,eAAeA,CAAA,EAAG;IACrB,MAAMgL,QAAQ,GAAGhL,eAAe,CAAC,CAAC;IAClCgL,QAAQ,CAACC,WAAW,GAAG,4BAA4B;IACnD,OAAO,IAAIF,UAAU,CAACC,QAAQ,CAAC;EACnC;EACA,OAAOlI,MAAMA,CAAA,EAAG;IACZ,MAAMkI,QAAQ,GAAG/K,WAAW,CAAC,CAAC;IAC9B+K,QAAQ,CAACC,WAAW,GAAG,mBAAmB;IAC1C,OAAO,IAAIF,UAAU,CAACC,QAAQ,CAAC;EACnC;EACA,OAAO9K,UAAUA,CAAC,GAAGgL,QAAQ,EAAE;IAC3B,MAAMF,QAAQ,GAAG9K,UAAU,CAAC,GAAGgL,QAAQ,CAAC;IACxCF,QAAQ,CAACC,WAAW,GAAG,uBAAuB;IAC9C,OAAO,IAAIF,UAAU,CAACC,QAAQ,CAAC;EACnC;EACA,OAAO7K,WAAWA,CAAC,GAAG+K,QAAQ,EAAE;IAC5B,MAAMF,QAAQ,GAAG7K,WAAW,CAAC,GAAG+K,QAAQ,CAAC;IACzCF,QAAQ,CAACC,WAAW,GAAG,wBAAwB;IAC/C,OAAO,IAAIF,UAAU,CAACC,QAAQ,CAAC;EACnC;EACA,OAAO5K,SAASA,CAACoJ,CAAC,EAAE;IAChB,MAAMwB,QAAQ,GAAG5K,SAAS,CAACoJ,CAAC,CAAC;IAC7BwB,QAAQ,CAACC,WAAW,GAAG,sBAAsB;IAC7C,OAAO,IAAIF,UAAU,CAACC,QAAQ,CAAC;EACnC;EACAlJ,OAAOA,CAACC,KAAK,EAAE;IACX,OAAO,IAAI,CAACR,SAAS,CAACO,OAAO,CAACC,KAAK,CAACR,SAAS,CAAC;EAClD;AACJ;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,MAAM4J,kBAAkB,GAAG;EACvBzI,SAAS;EACTrC,QAAQ;EACRC,SAAS;EACTe,IAAI;EACJuD,WAAW;EACXzH,UAAU;EACVO,iBAAiB;EACjBF,gBAAgB;EAChBiH,KAAK;EACL/E,qBAAqB;EACrBP,aAAa;EACbkF,mBAAmB;EACnBxE,SAAS;EACTkL,UAAU;EACVvL,WAAW;EACXe;AACJ,CAAC;AACD;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAAS6K,oBAAoBA,CAACrP,QAAQ,EAAEsP,gBAAgB,EAAE;EACtDtP,QAAQ,CAAC8G,QAAQ,CAACyI,iBAAiB,CAAC,IAAI7K,SAAS,CAAC,kBAAkB,EAAE8K,SAAS,IAAI;IAC/E,MAAMpH,GAAG,GAAGoH,SAAS,CAACC,WAAW,CAAC,YAAY,CAAC,CAACC,YAAY,CAAC,CAAC;IAC9D,MAAMC,YAAY,GAAGH,SAAS,CAACC,WAAW,CAAC,WAAW,CAAC,CAACC,YAAY,CAAC,CAAC;IACtE,OAAOJ,gBAAgB,CAAClH,GAAG,EAAEuH,YAAY,CAAC;EAC9C,CAAC,EAAE,QAAQ,CAAC,CAACC,eAAe,CAACpI,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAE2H,kBAAkB,CAAC,CAAC,CAAC;AACxE;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASS,iBAAiBA,CAACnE,QAAQ,EAAE;EACjC2D,oBAAoB,CAAC3D,QAAQ,EAAE,CAACtD,GAAG,EAAEuH,YAAY,KAAK,IAAIhJ,SAAS,CAACyB,GAAG,EAAEuH,YAAY,EAAE,IAAInJ,4BAA4B,CAAC,CAAC,CAAC,CAAC;EAC3HkF,QAAQ,CAACoE,eAAe,CAACnL,IAAI,EAAEC,OAAO,CAAC;AAC3C;AACAiL,iBAAiB,CAAC7P,QAAQ,CAAC;AAE3B,SAAS6P,iBAAiB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}