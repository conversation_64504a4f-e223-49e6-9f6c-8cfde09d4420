import { GeoLocation } from './user.model';

export interface Reward {
  id: string;
  title: string;
  description: string;
  pointsRequired: number;
  partnerId: string;
  partnerName: string;
  category: RewardCategory;
  imageUrl?: string;
  isActive: boolean;
  availableQuantity?: number;
  validUntil?: Date;
  terms?: string;
  createdAt: Date;
  updatedAt: Date;
  city: 'Monastir' | 'Sousse' | 'Both';
}

export enum RewardCategory {
  FOOD = 'food',
  SHOPPING = 'shopping',
  ENTERTAINMENT = 'entertainment',
  SERVICES = 'services',
  HEALTH = 'health',
  EDUCATION = 'education',
  TRANSPORT = 'transport',
  OTHER = 'other'
}

export interface Partner {
  id: string;
  name: string;
  description: string;
  logo?: string;
  category: PartnerCategory;
  address: string;
  city: 'Monastir' | 'Sousse';
  phone?: string;
  email?: string;
  website?: string;
  location: GeoLocation;
  isActive: boolean;
  rewards: string[]; // Array of reward IDs
  createdAt: Date;
  updatedAt: Date;
}

export enum PartnerCategory {
  RESTAURANT = 'restaurant',
  CAFE = 'cafe',
  RETAIL = 'retail',
  SERVICE = 'service',
  ENTERTAINMENT = 'entertainment',
  HEALTH = 'health',
  EDUCATION = 'education',
  OTHER = 'other'
}

export interface RewardExchange {
  id: string;
  userId: string;
  rewardId: string;
  pointsSpent: number;
  status: ExchangeStatus;
  exchangeCode: string;
  exchangedAt: Date;
  usedAt?: Date;
  validUntil: Date;
  partnerConfirmation?: string;
}

export enum ExchangeStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  USED = 'used',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled'
}

export interface CreateRewardRequest {
  title: string;
  description: string;
  pointsRequired: number;
  partnerId: string;
  category: RewardCategory;
  imageUrl?: string;
  availableQuantity?: number;
  validUntil?: Date;
  terms?: string;
  city: 'Monastir' | 'Sousse' | 'Both';
}

export interface UpdateRewardRequest {
  title?: string;
  description?: string;
  pointsRequired?: number;
  category?: RewardCategory;
  imageUrl?: string;
  availableQuantity?: number;
  validUntil?: Date;
  terms?: string;
  isActive?: boolean;
}

// GeoLocation interface is defined in user.model.ts
