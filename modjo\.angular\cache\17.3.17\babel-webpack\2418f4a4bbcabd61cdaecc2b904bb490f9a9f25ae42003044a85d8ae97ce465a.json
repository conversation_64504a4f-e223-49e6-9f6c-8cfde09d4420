{"ast": null, "code": "import { UserRole } from '../../../core/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/auth.service\";\nimport * as i2 from \"../../../core/services/dashboard-router.service\";\nimport * as i3 from \"@angular/router\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nfunction RoleNavigationComponent_div_0_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 10)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.user.points, \" points\");\n  }\n}\nfunction RoleNavigationComponent_div_0_a_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"a\", 11)(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const item_r3 = ctx.$implicit;\n    i0.ɵɵproperty(\"routerLink\", item_r3.route);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(item_r3.title);\n  }\n}\nfunction RoleNavigationComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"h3\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"span\", 4);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵtemplate(7, RoleNavigationComponent_div_0_div_7_Template, 5, 1, \"div\", 5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"nav\", 6);\n    i0.ɵɵtemplate(9, RoleNavigationComponent_div_0_a_9_Template, 5, 3, \"a\", 7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"div\", 8)(11, \"button\", 9);\n    i0.ɵɵlistener(\"click\", function RoleNavigationComponent_div_0_Template_button_click_11_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelementStart(12, \"mat-icon\");\n    i0.ɵɵtext(13, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(14, \" D\\u00E9connexion \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(ctx_r1.user.name);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.getRoleColor(ctx_r1.user.role) + \"20\")(\"color\", ctx_r1.getRoleColor(ctx_r1.user.role));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getRoleDisplayName(ctx_r1.user.role), \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.user.role === \"user\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.navigationItems);\n  }\n}\nexport class RoleNavigationComponent {\n  constructor(authService, dashboardRouter, router) {\n    this.authService = authService;\n    this.dashboardRouter = dashboardRouter;\n    this.router = router;\n    this.user = null;\n    this.navigationItems = [];\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        this.loadNavigationItems(user.role);\n      }\n    });\n  }\n  loadNavigationItems(role) {\n    const navigationMap = {\n      [UserRole.USER]: [{\n        title: 'Dashboard',\n        icon: 'dashboard',\n        route: '/dashboard'\n      }, {\n        title: 'Scanner QR',\n        icon: 'qr_code_scanner',\n        route: '/qr-scanner'\n      }, {\n        title: 'Récompenses',\n        icon: 'card_giftcard',\n        route: '/rewards'\n      }, {\n        title: 'Mon Profil',\n        icon: 'person',\n        route: '/profile'\n      }],\n      [UserRole.ADMIN]: [{\n        title: 'Dashboard Admin',\n        icon: 'admin_panel_settings',\n        route: '/admin'\n      }, {\n        title: 'Gestion Utilisateurs',\n        icon: 'people',\n        route: '/admin/users'\n      }, {\n        title: 'Gestion Partenaires',\n        icon: 'store',\n        route: '/admin/partners'\n      }, {\n        title: 'Configuration',\n        icon: 'settings',\n        route: '/admin/config'\n      }, {\n        title: 'Mon Profil',\n        icon: 'person',\n        route: '/profile'\n      }],\n      [UserRole.VALIDATOR]: [{\n        title: 'Dashboard Validateur',\n        icon: 'verified_user',\n        route: '/validator'\n      }, {\n        title: 'Actions à Valider',\n        icon: 'pending_actions',\n        route: '/validator/pending'\n      }, {\n        title: 'Historique',\n        icon: 'history',\n        route: '/validator/history'\n      }, {\n        title: 'Mon Profil',\n        icon: 'person',\n        route: '/profile'\n      }],\n      [UserRole.PARTNER]: [{\n        title: 'Dashboard Partenaire',\n        icon: 'store',\n        route: '/partner'\n      }, {\n        title: 'Mes Récompenses',\n        icon: 'card_giftcard',\n        route: '/partner/rewards'\n      }, {\n        title: 'Statistiques',\n        icon: 'analytics',\n        route: '/partner/stats'\n      }, {\n        title: 'Mon Profil',\n        icon: 'person',\n        route: '/profile'\n      }],\n      [UserRole.PROVIDER]: [{\n        title: 'Dashboard Prestataire',\n        icon: 'business',\n        route: '/provider'\n      }, {\n        title: 'Générer QR',\n        icon: 'qr_code',\n        route: '/provider/qr-generator'\n      }, {\n        title: 'Statistiques',\n        icon: 'analytics',\n        route: '/provider/stats'\n      }, {\n        title: 'Mon Profil',\n        icon: 'person',\n        route: '/profile'\n      }]\n    };\n    this.navigationItems = navigationMap[role] || [];\n  }\n  getRoleDisplayName(role) {\n    const roleNames = {\n      [UserRole.USER]: 'Utilisateur',\n      [UserRole.ADMIN]: 'Administrateur',\n      [UserRole.VALIDATOR]: 'Validateur',\n      [UserRole.PARTNER]: 'Partenaire',\n      [UserRole.PROVIDER]: 'Prestataire'\n    };\n    return roleNames[role];\n  }\n  getRoleColor(role) {\n    const colors = {\n      [UserRole.USER]: '#4CAF50',\n      [UserRole.ADMIN]: '#F44336',\n      [UserRole.VALIDATOR]: '#2196F3',\n      [UserRole.PARTNER]: '#FF9800',\n      [UserRole.PROVIDER]: '#9C27B0'\n    };\n    return colors[role];\n  }\n  logout() {\n    this.authService.logout();\n    this.router.navigate(['/auth/login']);\n  }\n  static {\n    this.ɵfac = function RoleNavigationComponent_Factory(t) {\n      return new (t || RoleNavigationComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.DashboardRouterService), i0.ɵɵdirectiveInject(i3.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RoleNavigationComponent,\n      selectors: [[\"app-role-navigation\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"role-navigation\", 4, \"ngIf\"], [1, \"role-navigation\"], [1, \"nav-header\"], [1, \"user-info\"], [1, \"role-badge\"], [\"class\", \"user-points\", 4, \"ngIf\"], [1, \"nav-menu\"], [\"routerLinkActive\", \"active\", \"class\", \"nav-item\", 3, \"routerLink\", 4, \"ngFor\", \"ngForOf\"], [1, \"nav-footer\"], [\"mat-button\", \"\", 1, \"logout-btn\", 3, \"click\"], [1, \"user-points\"], [\"routerLinkActive\", \"active\", 1, \"nav-item\", 3, \"routerLink\"]],\n      template: function RoleNavigationComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, RoleNavigationComponent_div_0_Template, 15, 8, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i3.RouterLink, i3.RouterLinkActive, i5.MatButton, i6.MatIcon],\n      styles: [\".role-navigation[_ngcontent-%COMP%] {\\n      background: white;\\n      border-radius: 16px;\\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n      padding: 24px;\\n      margin-bottom: 24px;\\n    }\\n    \\n    .nav-header[_ngcontent-%COMP%] {\\n      display: flex;\\n      justify-content: space-between;\\n      align-items: center;\\n      margin-bottom: 24px;\\n      padding-bottom: 16px;\\n      border-bottom: 1px solid #e2e8f0;\\n    }\\n    \\n    .user-info[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n      margin: 0 0 8px 0;\\n      color: #2d3748;\\n      font-weight: 600;\\n    }\\n    \\n    .role-badge[_ngcontent-%COMP%] {\\n      padding: 4px 12px;\\n      border-radius: 12px;\\n      font-size: 0.8rem;\\n      font-weight: 600;\\n      text-transform: uppercase;\\n    }\\n    \\n    .user-points[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 8px;\\n      background: linear-gradient(135deg, #FFD700, #FFA500);\\n      color: white;\\n      padding: 8px 16px;\\n      border-radius: 20px;\\n      font-weight: 600;\\n    }\\n    \\n    .nav-menu[_ngcontent-%COMP%] {\\n      display: flex;\\n      flex-direction: column;\\n      gap: 8px;\\n      margin-bottom: 24px;\\n    }\\n    \\n    .nav-item[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 12px;\\n      padding: 12px 16px;\\n      border-radius: 12px;\\n      color: #4a5568;\\n      text-decoration: none;\\n      transition: all 0.2s ease;\\n    }\\n    \\n    .nav-item[_ngcontent-%COMP%]:hover {\\n      background: #f7fafc;\\n      color: #2d3748;\\n    }\\n    \\n    .nav-item.active[_ngcontent-%COMP%] {\\n      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\\n      color: #667eea;\\n      font-weight: 600;\\n    }\\n    \\n    .nav-footer[_ngcontent-%COMP%] {\\n      padding-top: 16px;\\n      border-top: 1px solid #e2e8f0;\\n    }\\n    \\n    .logout-btn[_ngcontent-%COMP%] {\\n      width: 100%;\\n      color: #F44336;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvc2hhcmVkL2NvbXBvbmVudHMvcm9sZS1uYXZpZ2F0aW9uL3JvbGUtbmF2aWdhdGlvbi5jb21wb25lbnQudHMiXSwibmFtZXMiOltdLCJtYXBwaW5ncyI6IjtJQUNJO01BQ0UsaUJBQWlCO01BQ2pCLG1CQUFtQjtNQUNuQix5Q0FBeUM7TUFDekMsYUFBYTtNQUNiLG1CQUFtQjtJQUNyQjs7SUFFQTtNQUNFLGFBQWE7TUFDYiw4QkFBOEI7TUFDOUIsbUJBQW1CO01BQ25CLG1CQUFtQjtNQUNuQixvQkFBb0I7TUFDcEIsZ0NBQWdDO0lBQ2xDOztJQUVBO01BQ0UsaUJBQWlCO01BQ2pCLGNBQWM7TUFDZCxnQkFBZ0I7SUFDbEI7O0lBRUE7TUFDRSxpQkFBaUI7TUFDakIsbUJBQW1CO01BQ25CLGlCQUFpQjtNQUNqQixnQkFBZ0I7TUFDaEIseUJBQXlCO0lBQzNCOztJQUVBO01BQ0UsYUFBYTtNQUNiLG1CQUFtQjtNQUNuQixRQUFRO01BQ1IscURBQXFEO01BQ3JELFlBQVk7TUFDWixpQkFBaUI7TUFDakIsbUJBQW1CO01BQ25CLGdCQUFnQjtJQUNsQjs7SUFFQTtNQUNFLGFBQWE7TUFDYixzQkFBc0I7TUFDdEIsUUFBUTtNQUNSLG1CQUFtQjtJQUNyQjs7SUFFQTtNQUNFLGFBQWE7TUFDYixtQkFBbUI7TUFDbkIsU0FBUztNQUNULGtCQUFrQjtNQUNsQixtQkFBbUI7TUFDbkIsY0FBYztNQUNkLHFCQUFxQjtNQUNyQix5QkFBeUI7SUFDM0I7O0lBRUE7TUFDRSxtQkFBbUI7TUFDbkIsY0FBYztJQUNoQjs7SUFFQTtNQUNFLHNGQUFzRjtNQUN0RixjQUFjO01BQ2QsZ0JBQWdCO0lBQ2xCOztJQUVBO01BQ0UsaUJBQWlCO01BQ2pCLDZCQUE2QjtJQUMvQjs7SUFFQTtNQUNFLFdBQVc7TUFDWCxjQUFjO0lBQ2hCOztBQUVKLDQ3R0FBNDdHIiwic291cmNlc0NvbnRlbnQiOlsiXG4gICAgLnJvbGUtbmF2aWdhdGlvbiB7XG4gICAgICBiYWNrZ3JvdW5kOiB3aGl0ZTtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDE2cHg7XG4gICAgICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgICAgIHBhZGRpbmc6IDI0cHg7XG4gICAgICBtYXJnaW4tYm90dG9tOiAyNHB4O1xuICAgIH1cbiAgICBcbiAgICAubmF2LWhlYWRlciB7XG4gICAgICBkaXNwbGF5OiBmbGV4O1xuICAgICAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICAgICAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgICAgIG1hcmdpbi1ib3R0b206IDI0cHg7XG4gICAgICBwYWRkaW5nLWJvdHRvbTogMTZweDtcbiAgICAgIGJvcmRlci1ib3R0b206IDFweCBzb2xpZCAjZTJlOGYwO1xuICAgIH1cbiAgICBcbiAgICAudXNlci1pbmZvIGgzIHtcbiAgICAgIG1hcmdpbjogMCAwIDhweCAwO1xuICAgICAgY29sb3I6ICMyZDM3NDg7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgIH1cbiAgICBcbiAgICAucm9sZS1iYWRnZSB7XG4gICAgICBwYWRkaW5nOiA0cHggMTJweDtcbiAgICAgIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gICAgICBmb250LXNpemU6IDAuOHJlbTtcbiAgICAgIGZvbnQtd2VpZ2h0OiA2MDA7XG4gICAgICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xuICAgIH1cbiAgICBcbiAgICAudXNlci1wb2ludHMge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDhweDtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNGRkQ3MDAsICNGRkE1MDApO1xuICAgICAgY29sb3I6IHdoaXRlO1xuICAgICAgcGFkZGluZzogOHB4IDE2cHg7XG4gICAgICBib3JkZXItcmFkaXVzOiAyMHB4O1xuICAgICAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgICB9XG4gICAgXG4gICAgLm5hdi1tZW51IHtcbiAgICAgIGRpc3BsYXk6IGZsZXg7XG4gICAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgICAgZ2FwOiA4cHg7XG4gICAgICBtYXJnaW4tYm90dG9tOiAyNHB4O1xuICAgIH1cbiAgICBcbiAgICAubmF2LWl0ZW0ge1xuICAgICAgZGlzcGxheTogZmxleDtcbiAgICAgIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gICAgICBnYXA6IDEycHg7XG4gICAgICBwYWRkaW5nOiAxMnB4IDE2cHg7XG4gICAgICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICAgICAgY29sb3I6ICM0YTU1Njg7XG4gICAgICB0ZXh0LWRlY29yYXRpb246IG5vbmU7XG4gICAgICB0cmFuc2l0aW9uOiBhbGwgMC4ycyBlYXNlO1xuICAgIH1cbiAgICBcbiAgICAubmF2LWl0ZW06aG92ZXIge1xuICAgICAgYmFja2dyb3VuZDogI2Y3ZmFmYztcbiAgICAgIGNvbG9yOiAjMmQzNzQ4O1xuICAgIH1cbiAgICBcbiAgICAubmF2LWl0ZW0uYWN0aXZlIHtcbiAgICAgIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4xKSwgcmdiYSgxMTgsIDc1LCAxNjIsIDAuMSkpO1xuICAgICAgY29sb3I6ICM2NjdlZWE7XG4gICAgICBmb250LXdlaWdodDogNjAwO1xuICAgIH1cbiAgICBcbiAgICAubmF2LWZvb3RlciB7XG4gICAgICBwYWRkaW5nLXRvcDogMTZweDtcbiAgICAgIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTJlOGYwO1xuICAgIH1cbiAgICBcbiAgICAubG9nb3V0LWJ0biB7XG4gICAgICB3aWR0aDogMTAwJTtcbiAgICAgIGNvbG9yOiAjRjQ0MzM2O1xuICAgIH1cbiAgIl0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["UserRole", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵtextInterpolate1", "ctx_r1", "user", "points", "ɵɵproperty", "item_r3", "route", "ɵɵtextInterpolate", "icon", "title", "ɵɵtemplate", "RoleNavigationComponent_div_0_div_7_Template", "RoleNavigationComponent_div_0_a_9_Template", "ɵɵlistener", "RoleNavigationComponent_div_0_Template_button_click_11_listener", "ɵɵrestoreView", "_r1", "ɵɵnextContext", "ɵɵresetView", "logout", "name", "ɵɵstyleProp", "getRoleColor", "role", "getRoleDisplayName", "navigationItems", "RoleNavigationComponent", "constructor", "authService", "dashboardRouter", "router", "ngOnInit", "currentUser$", "subscribe", "loadNavigationItems", "navigationMap", "USER", "ADMIN", "VALIDATOR", "PARTNER", "PROVIDER", "roleNames", "colors", "navigate", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "DashboardRouterService", "i3", "Router", "selectors", "decls", "vars", "consts", "template", "RoleNavigationComponent_Template", "rf", "ctx", "RoleNavigationComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\shared\\components\\role-navigation\\role-navigation.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../core/services/auth.service';\nimport { DashboardRouterService } from '../../../core/services/dashboard-router.service';\nimport { User, UserRole } from '../../../core/models';\n\n@Component({\n  selector: 'app-role-navigation',\n  template: `\n    <div class=\"role-navigation\" *ngIf=\"user\">\n      <div class=\"nav-header\">\n        <div class=\"user-info\">\n          <h3>{{ user.name }}</h3>\n          <span class=\"role-badge\" [style.background-color]=\"getRoleColor(user.role) + '20'\" \n                [style.color]=\"getRoleColor(user.role)\">\n            {{ getRoleDisplayName(user.role) }}\n          </span>\n        </div>\n        <div class=\"user-points\" *ngIf=\"user.role === 'user'\">\n          <mat-icon>stars</mat-icon>\n          <span>{{ user.points }} points</span>\n        </div>\n      </div>\n      \n      <nav class=\"nav-menu\">\n        <a *ngFor=\"let item of navigationItems\" \n           [routerLink]=\"item.route\" \n           routerLinkActive=\"active\"\n           class=\"nav-item\">\n          <mat-icon>{{ item.icon }}</mat-icon>\n          <span>{{ item.title }}</span>\n        </a>\n      </nav>\n      \n      <div class=\"nav-footer\">\n        <button mat-button (click)=\"logout()\" class=\"logout-btn\">\n          <mat-icon>logout</mat-icon>\n          Déconnexion\n        </button>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .role-navigation {\n      background: white;\n      border-radius: 16px;\n      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\n      padding: 24px;\n      margin-bottom: 24px;\n    }\n    \n    .nav-header {\n      display: flex;\n      justify-content: space-between;\n      align-items: center;\n      margin-bottom: 24px;\n      padding-bottom: 16px;\n      border-bottom: 1px solid #e2e8f0;\n    }\n    \n    .user-info h3 {\n      margin: 0 0 8px 0;\n      color: #2d3748;\n      font-weight: 600;\n    }\n    \n    .role-badge {\n      padding: 4px 12px;\n      border-radius: 12px;\n      font-size: 0.8rem;\n      font-weight: 600;\n      text-transform: uppercase;\n    }\n    \n    .user-points {\n      display: flex;\n      align-items: center;\n      gap: 8px;\n      background: linear-gradient(135deg, #FFD700, #FFA500);\n      color: white;\n      padding: 8px 16px;\n      border-radius: 20px;\n      font-weight: 600;\n    }\n    \n    .nav-menu {\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n      margin-bottom: 24px;\n    }\n    \n    .nav-item {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n      padding: 12px 16px;\n      border-radius: 12px;\n      color: #4a5568;\n      text-decoration: none;\n      transition: all 0.2s ease;\n    }\n    \n    .nav-item:hover {\n      background: #f7fafc;\n      color: #2d3748;\n    }\n    \n    .nav-item.active {\n      background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\n      color: #667eea;\n      font-weight: 600;\n    }\n    \n    .nav-footer {\n      padding-top: 16px;\n      border-top: 1px solid #e2e8f0;\n    }\n    \n    .logout-btn {\n      width: 100%;\n      color: #F44336;\n    }\n  `]\n})\nexport class RoleNavigationComponent implements OnInit {\n  user: User | null = null;\n  navigationItems: any[] = [];\n\n  constructor(\n    private authService: AuthService,\n    private dashboardRouter: DashboardRouterService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        this.loadNavigationItems(user.role);\n      }\n    });\n  }\n\n  private loadNavigationItems(role: UserRole): void {\n    const navigationMap = {\n      [UserRole.USER]: [\n        { title: 'Dashboard', icon: 'dashboard', route: '/dashboard' },\n        { title: 'Scanner QR', icon: 'qr_code_scanner', route: '/qr-scanner' },\n        { title: 'Récompenses', icon: 'card_giftcard', route: '/rewards' },\n        { title: 'Mon Profil', icon: 'person', route: '/profile' }\n      ],\n      [UserRole.ADMIN]: [\n        { title: 'Dashboard Admin', icon: 'admin_panel_settings', route: '/admin' },\n        { title: 'Gestion Utilisateurs', icon: 'people', route: '/admin/users' },\n        { title: 'Gestion Partenaires', icon: 'store', route: '/admin/partners' },\n        { title: 'Configuration', icon: 'settings', route: '/admin/config' },\n        { title: 'Mon Profil', icon: 'person', route: '/profile' }\n      ],\n      [UserRole.VALIDATOR]: [\n        { title: 'Dashboard Validateur', icon: 'verified_user', route: '/validator' },\n        { title: 'Actions à Valider', icon: 'pending_actions', route: '/validator/pending' },\n        { title: 'Historique', icon: 'history', route: '/validator/history' },\n        { title: 'Mon Profil', icon: 'person', route: '/profile' }\n      ],\n      [UserRole.PARTNER]: [\n        { title: 'Dashboard Partenaire', icon: 'store', route: '/partner' },\n        { title: 'Mes Récompenses', icon: 'card_giftcard', route: '/partner/rewards' },\n        { title: 'Statistiques', icon: 'analytics', route: '/partner/stats' },\n        { title: 'Mon Profil', icon: 'person', route: '/profile' }\n      ],\n      [UserRole.PROVIDER]: [\n        { title: 'Dashboard Prestataire', icon: 'business', route: '/provider' },\n        { title: 'Générer QR', icon: 'qr_code', route: '/provider/qr-generator' },\n        { title: 'Statistiques', icon: 'analytics', route: '/provider/stats' },\n        { title: 'Mon Profil', icon: 'person', route: '/profile' }\n      ]\n    };\n\n    this.navigationItems = navigationMap[role] || [];\n  }\n\n  getRoleDisplayName(role: UserRole): string {\n    const roleNames = {\n      [UserRole.USER]: 'Utilisateur',\n      [UserRole.ADMIN]: 'Administrateur',\n      [UserRole.VALIDATOR]: 'Validateur',\n      [UserRole.PARTNER]: 'Partenaire',\n      [UserRole.PROVIDER]: 'Prestataire'\n    };\n    return roleNames[role];\n  }\n\n  getRoleColor(role: UserRole): string {\n    const colors = {\n      [UserRole.USER]: '#4CAF50',\n      [UserRole.ADMIN]: '#F44336',\n      [UserRole.VALIDATOR]: '#2196F3',\n      [UserRole.PARTNER]: '#FF9800',\n      [UserRole.PROVIDER]: '#9C27B0'\n    };\n    return colors[role];\n  }\n\n  logout(): void {\n    this.authService.logout();\n    this.router.navigate(['/auth/login']);\n  }\n}\n"], "mappings": "AAIA,SAAeA,QAAQ,QAAQ,sBAAsB;;;;;;;;;;IAe3CC,EADF,CAAAC,cAAA,cAAsD,eAC1C;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAChCF,EADgC,CAAAG,YAAA,EAAO,EACjC;;;;IADEH,EAAA,CAAAI,SAAA,GAAwB;IAAxBJ,EAAA,CAAAK,kBAAA,KAAAC,MAAA,CAAAC,IAAA,CAAAC,MAAA,YAAwB;;;;;IAS9BR,EAJF,CAAAC,cAAA,YAGoB,eACR;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IACxBF,EADwB,CAAAG,YAAA,EAAO,EAC3B;;;;IALDH,EAAA,CAAAS,UAAA,eAAAC,OAAA,CAAAC,KAAA,CAAyB;IAGhBX,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAY,iBAAA,CAAAF,OAAA,CAAAG,IAAA,CAAe;IACnBb,EAAA,CAAAI,SAAA,GAAgB;IAAhBJ,EAAA,CAAAY,iBAAA,CAAAF,OAAA,CAAAI,KAAA,CAAgB;;;;;;IAlBtBd,EAHN,CAAAC,cAAA,aAA0C,aAChB,aACC,SACjB;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACxBH,EAAA,CAAAC,cAAA,cAC8C;IAC5CD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAO,EACH;IACNH,EAAA,CAAAe,UAAA,IAAAC,4CAAA,iBAAsD;IAIxDhB,EAAA,CAAAG,YAAA,EAAM;IAENH,EAAA,CAAAC,cAAA,aAAsB;IACpBD,EAAA,CAAAe,UAAA,IAAAE,0CAAA,eAGoB;IAItBjB,EAAA,CAAAG,YAAA,EAAM;IAGJH,EADF,CAAAC,cAAA,cAAwB,iBACmC;IAAtCD,EAAA,CAAAkB,UAAA,mBAAAC,gEAAA;MAAAnB,EAAA,CAAAoB,aAAA,CAAAC,GAAA;MAAA,MAAAf,MAAA,GAAAN,EAAA,CAAAsB,aAAA;MAAA,OAAAtB,EAAA,CAAAuB,WAAA,CAASjB,MAAA,CAAAkB,MAAA,EAAQ;IAAA,EAAC;IACnCxB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAE,MAAA,0BACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACL,EACF;;;;IA5BIH,EAAA,CAAAI,SAAA,GAAe;IAAfJ,EAAA,CAAAY,iBAAA,CAAAN,MAAA,CAAAC,IAAA,CAAAkB,IAAA,CAAe;IACMzB,EAAA,CAAAI,SAAA,EAAyD;IAC5EJ,EADmB,CAAA0B,WAAA,qBAAApB,MAAA,CAAAqB,YAAA,CAAArB,MAAA,CAAAC,IAAA,CAAAqB,IAAA,SAAyD,UAAAtB,MAAA,CAAAqB,YAAA,CAAArB,MAAA,CAAAC,IAAA,CAAAqB,IAAA,EACrC;IAC3C5B,EAAA,CAAAI,SAAA,EACF;IADEJ,EAAA,CAAAK,kBAAA,MAAAC,MAAA,CAAAuB,kBAAA,CAAAvB,MAAA,CAAAC,IAAA,CAAAqB,IAAA,OACF;IAEwB5B,EAAA,CAAAI,SAAA,EAA0B;IAA1BJ,EAAA,CAAAS,UAAA,SAAAH,MAAA,CAAAC,IAAA,CAAAqB,IAAA,YAA0B;IAOhC5B,EAAA,CAAAI,SAAA,GAAkB;IAAlBJ,EAAA,CAAAS,UAAA,YAAAH,MAAA,CAAAwB,eAAA,CAAkB;;;AAoG9C,OAAM,MAAOC,uBAAuB;EAIlCC,YACUC,WAAwB,EACxBC,eAAuC,EACvCC,MAAc;IAFd,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IACf,KAAAC,MAAM,GAANA,MAAM;IANhB,KAAA5B,IAAI,GAAgB,IAAI;IACxB,KAAAuB,eAAe,GAAU,EAAE;EAMxB;EAEHM,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,CAACI,YAAY,CAACC,SAAS,CAAC/B,IAAI,IAAG;MAC7C,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAIA,IAAI,EAAE;QACR,IAAI,CAACgC,mBAAmB,CAAChC,IAAI,CAACqB,IAAI,CAAC;;IAEvC,CAAC,CAAC;EACJ;EAEQW,mBAAmBA,CAACX,IAAc;IACxC,MAAMY,aAAa,GAAG;MACpB,CAACzC,QAAQ,CAAC0C,IAAI,GAAG,CACf;QAAE3B,KAAK,EAAE,WAAW;QAAED,IAAI,EAAE,WAAW;QAAEF,KAAK,EAAE;MAAY,CAAE,EAC9D;QAAEG,KAAK,EAAE,YAAY;QAAED,IAAI,EAAE,iBAAiB;QAAEF,KAAK,EAAE;MAAa,CAAE,EACtE;QAAEG,KAAK,EAAE,aAAa;QAAED,IAAI,EAAE,eAAe;QAAEF,KAAK,EAAE;MAAU,CAAE,EAClE;QAAEG,KAAK,EAAE,YAAY;QAAED,IAAI,EAAE,QAAQ;QAAEF,KAAK,EAAE;MAAU,CAAE,CAC3D;MACD,CAACZ,QAAQ,CAAC2C,KAAK,GAAG,CAChB;QAAE5B,KAAK,EAAE,iBAAiB;QAAED,IAAI,EAAE,sBAAsB;QAAEF,KAAK,EAAE;MAAQ,CAAE,EAC3E;QAAEG,KAAK,EAAE,sBAAsB;QAAED,IAAI,EAAE,QAAQ;QAAEF,KAAK,EAAE;MAAc,CAAE,EACxE;QAAEG,KAAK,EAAE,qBAAqB;QAAED,IAAI,EAAE,OAAO;QAAEF,KAAK,EAAE;MAAiB,CAAE,EACzE;QAAEG,KAAK,EAAE,eAAe;QAAED,IAAI,EAAE,UAAU;QAAEF,KAAK,EAAE;MAAe,CAAE,EACpE;QAAEG,KAAK,EAAE,YAAY;QAAED,IAAI,EAAE,QAAQ;QAAEF,KAAK,EAAE;MAAU,CAAE,CAC3D;MACD,CAACZ,QAAQ,CAAC4C,SAAS,GAAG,CACpB;QAAE7B,KAAK,EAAE,sBAAsB;QAAED,IAAI,EAAE,eAAe;QAAEF,KAAK,EAAE;MAAY,CAAE,EAC7E;QAAEG,KAAK,EAAE,mBAAmB;QAAED,IAAI,EAAE,iBAAiB;QAAEF,KAAK,EAAE;MAAoB,CAAE,EACpF;QAAEG,KAAK,EAAE,YAAY;QAAED,IAAI,EAAE,SAAS;QAAEF,KAAK,EAAE;MAAoB,CAAE,EACrE;QAAEG,KAAK,EAAE,YAAY;QAAED,IAAI,EAAE,QAAQ;QAAEF,KAAK,EAAE;MAAU,CAAE,CAC3D;MACD,CAACZ,QAAQ,CAAC6C,OAAO,GAAG,CAClB;QAAE9B,KAAK,EAAE,sBAAsB;QAAED,IAAI,EAAE,OAAO;QAAEF,KAAK,EAAE;MAAU,CAAE,EACnE;QAAEG,KAAK,EAAE,iBAAiB;QAAED,IAAI,EAAE,eAAe;QAAEF,KAAK,EAAE;MAAkB,CAAE,EAC9E;QAAEG,KAAK,EAAE,cAAc;QAAED,IAAI,EAAE,WAAW;QAAEF,KAAK,EAAE;MAAgB,CAAE,EACrE;QAAEG,KAAK,EAAE,YAAY;QAAED,IAAI,EAAE,QAAQ;QAAEF,KAAK,EAAE;MAAU,CAAE,CAC3D;MACD,CAACZ,QAAQ,CAAC8C,QAAQ,GAAG,CACnB;QAAE/B,KAAK,EAAE,uBAAuB;QAAED,IAAI,EAAE,UAAU;QAAEF,KAAK,EAAE;MAAW,CAAE,EACxE;QAAEG,KAAK,EAAE,YAAY;QAAED,IAAI,EAAE,SAAS;QAAEF,KAAK,EAAE;MAAwB,CAAE,EACzE;QAAEG,KAAK,EAAE,cAAc;QAAED,IAAI,EAAE,WAAW;QAAEF,KAAK,EAAE;MAAiB,CAAE,EACtE;QAAEG,KAAK,EAAE,YAAY;QAAED,IAAI,EAAE,QAAQ;QAAEF,KAAK,EAAE;MAAU,CAAE;KAE7D;IAED,IAAI,CAACmB,eAAe,GAAGU,aAAa,CAACZ,IAAI,CAAC,IAAI,EAAE;EAClD;EAEAC,kBAAkBA,CAACD,IAAc;IAC/B,MAAMkB,SAAS,GAAG;MAChB,CAAC/C,QAAQ,CAAC0C,IAAI,GAAG,aAAa;MAC9B,CAAC1C,QAAQ,CAAC2C,KAAK,GAAG,gBAAgB;MAClC,CAAC3C,QAAQ,CAAC4C,SAAS,GAAG,YAAY;MAClC,CAAC5C,QAAQ,CAAC6C,OAAO,GAAG,YAAY;MAChC,CAAC7C,QAAQ,CAAC8C,QAAQ,GAAG;KACtB;IACD,OAAOC,SAAS,CAAClB,IAAI,CAAC;EACxB;EAEAD,YAAYA,CAACC,IAAc;IACzB,MAAMmB,MAAM,GAAG;MACb,CAAChD,QAAQ,CAAC0C,IAAI,GAAG,SAAS;MAC1B,CAAC1C,QAAQ,CAAC2C,KAAK,GAAG,SAAS;MAC3B,CAAC3C,QAAQ,CAAC4C,SAAS,GAAG,SAAS;MAC/B,CAAC5C,QAAQ,CAAC6C,OAAO,GAAG,SAAS;MAC7B,CAAC7C,QAAQ,CAAC8C,QAAQ,GAAG;KACtB;IACD,OAAOE,MAAM,CAACnB,IAAI,CAAC;EACrB;EAEAJ,MAAMA,CAAA;IACJ,IAAI,CAACS,WAAW,CAACT,MAAM,EAAE;IACzB,IAAI,CAACW,MAAM,CAACa,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;EACvC;;;uBAlFWjB,uBAAuB,EAAA/B,EAAA,CAAAiD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAnD,EAAA,CAAAiD,iBAAA,CAAAG,EAAA,CAAAC,sBAAA,GAAArD,EAAA,CAAAiD,iBAAA,CAAAK,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAvBxB,uBAAuB;MAAAyB,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,iCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UApHhC9D,EAAA,CAAAe,UAAA,IAAAiD,sCAAA,kBAA0C;;;UAAZhE,EAAA,CAAAS,UAAA,SAAAsD,GAAA,CAAAxD,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}