{"ast": null, "code": "import { UserRole, ValidationStatus } from '../../../../core/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/button\";\nimport * as i5 from \"@angular/material/card\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"../../../../shared/pipes/time-ago.pipe\";\nfunction ValidatorDashboardComponent_div_0_mat_card_19_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 16)(1, \"mat-card-content\")(2, \"div\", 17)(3, \"div\", 18)(4, \"mat-icon\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(6, \"div\", 19)(7, \"h3\", 20);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 21);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\", 22);\n    i0.ɵɵtext(12);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const stat_r1 = ctx.$implicit;\n    i0.ɵɵstyleProp(\"border-left-color\", stat_r1.color);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"background-color\", stat_r1.color + \"20\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", stat_r1.color);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(stat_r1.icon);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(stat_r1.value);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r1.description);\n  }\n}\nfunction ValidatorDashboardComponent_div_0_span_25_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 23);\n    i0.ɵɵtext(1);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate(ctx_r1.pendingValidations.length);\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_26_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 24)(1, \"mat-card\", 25)(2, \"mat-card-content\")(3, \"mat-icon\", 26);\n    i0.ɵɵtext(4, \"check_circle\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"h3\");\n    i0.ɵɵtext(6, \"Aucune validation en attente\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\");\n    i0.ɵɵtext(8, \"Toutes les actions ont \\u00E9t\\u00E9 trait\\u00E9es. Excellent travail !\");\n    i0.ɵɵelementEnd()()()();\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_16_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 39)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const validation_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(validation_r4.location);\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 49);\n    i0.ɵɵlistener(\"click\", function ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_button_4_Template_button_click_0_listener() {\n      const evidence_r6 = i0.ɵɵrestoreView(_r5).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(5);\n      return i0.ɵɵresetView(ctx_r1.viewEvidence(evidence_r6));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"photo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Voir la preuve \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 46)(1, \"h5\");\n    i0.ɵɵtext(2, \"Preuves fournies:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 47);\n    i0.ɵɵtemplate(4, ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_button_4_Template, 4, 0, \"button\", 48);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const validation_r4 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", validation_r4.evidence);\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 29)(1, \"mat-card-header\")(2, \"div\", 30)(3, \"div\", 31)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"span\", 32);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 33)(9, \"span\", 34);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(11, \"mat-card-content\")(12, \"div\", 35)(13, \"p\", 36);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 37);\n    i0.ɵɵtemplate(16, ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_16_Template, 5, 1, \"div\", 38);\n    i0.ɵɵelementStart(17, \"div\", 39)(18, \"mat-icon\");\n    i0.ɵɵtext(19, \"schedule\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"span\");\n    i0.ɵɵtext(21);\n    i0.ɵɵpipe(22, \"timeAgo\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(23, \"div\", 40)(24, \"mat-icon\");\n    i0.ɵɵtext(25, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(26, \"span\");\n    i0.ɵɵtext(27);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵtemplate(28, ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_Template, 5, 1, \"div\", 41);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(29, \"mat-card-actions\", 42)(30, \"button\", 43);\n    i0.ɵɵlistener(\"click\", function ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template_button_click_30_listener() {\n      const validation_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.validateAction(validation_r4, true));\n    });\n    i0.ɵɵelementStart(31, \"mat-icon\");\n    i0.ɵɵtext(32, \"check\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(33);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(34, \"button\", 44);\n    i0.ɵɵlistener(\"click\", function ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template_button_click_34_listener() {\n      const validation_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.validateAction(validation_r4, false));\n    });\n    i0.ɵɵelementStart(35, \"mat-icon\");\n    i0.ɵɵtext(36, \"close\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(37, \" Rejeter \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(38, \"button\", 45);\n    i0.ɵɵlistener(\"click\", function ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template_button_click_38_listener() {\n      const validation_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.viewValidationDetails(validation_r4));\n    });\n    i0.ɵɵelementStart(39, \"mat-icon\");\n    i0.ɵɵtext(40, \"info\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(41, \" D\\u00E9tails \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const validation_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(validation_r4.userName);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(validation_r4.userEmail);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getActionTypeDisplayName(validation_r4.actionType));\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(validation_r4.description);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", validation_r4.location);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(22, 9, validation_r4.submittedAt));\n    i0.ɵɵadvance(6);\n    i0.ɵɵtextInterpolate1(\"\", validation_r4.points, \" points\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", validation_r4.evidence && validation_r4.evidence.length > 0);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\" Approuver (+\", validation_r4.points, \" pts) \");\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_27_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 27);\n    i0.ɵɵtemplate(1, ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template, 42, 11, \"mat-card\", 28);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.pendingValidations);\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_28_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 55)(1, \"div\", 56)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 57)(5, \"p\", 58)(6, \"strong\");\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 59)(10, \"span\", 60);\n    i0.ɵɵtext(11);\n    i0.ɵɵpipe(12, \"timeAgo\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"span\", 61);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"span\", 62);\n    i0.ɵɵtext(16);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const validation_r8 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background-color\", ctx_r1.getStatusColor(validation_r8.status) + \"20\");\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getStatusColor(validation_r8.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", validation_r8.status === \"approved\" ? \"check_circle\" : \"cancel\", \" \");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(validation_r8.userName);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" - \", validation_r8.description, \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind1(12, 12, validation_r8.validatedAt));\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getStatusColor(validation_r8.status));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getStatusDisplayName(validation_r8.status), \" \");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", validation_r8.points, \" pts\");\n  }\n}\nfunction ValidatorDashboardComponent_div_0_div_28_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r7 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 50)(1, \"h2\", 8)(2, \"mat-icon\");\n    i0.ɵɵtext(3, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4, \" Validations R\\u00E9centes \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"mat-card\", 51)(6, \"mat-card-content\")(7, \"div\", 52);\n    i0.ɵɵtemplate(8, ValidatorDashboardComponent_div_0_div_28_div_8_Template, 17, 14, \"div\", 53);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"button\", 54);\n    i0.ɵɵlistener(\"click\", function ValidatorDashboardComponent_div_0_div_28_Template_button_click_9_listener() {\n      i0.ɵɵrestoreView(_r7);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.navigateTo(\"/validator/history\"));\n    });\n    i0.ɵɵtext(10, \" Voir tout l'historique \");\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"arrow_forward\");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.recentValidations.slice(0, 5));\n  }\n}\nfunction ValidatorDashboardComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"div\", 2)(2, \"div\", 3)(3, \"div\", 4)(4, \"h1\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 5);\n    i0.ɵɵtext(7, \"\\uD83E\\uDDD1\\u200D\\uD83C\\uDFEB Tableau de bord validateur - Validez les actions communautaires\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(8, \"div\", 6)(9, \"mat-icon\");\n    i0.ɵɵtext(10, \"verified_user\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(11, \"span\");\n    i0.ɵɵtext(12, \"Validateur\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(13, \"div\", 7)(14, \"h2\", 8)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(17, \" Statistiques de Validation \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(18, \"div\", 9);\n    i0.ɵɵtemplate(19, ValidatorDashboardComponent_div_0_mat_card_19_Template, 13, 10, \"mat-card\", 10);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(20, \"div\", 11)(21, \"h2\", 8)(22, \"mat-icon\");\n    i0.ɵɵtext(23, \"pending_actions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(24, \" Actions \\u00E0 Valider \");\n    i0.ɵɵtemplate(25, ValidatorDashboardComponent_div_0_span_25_Template, 2, 1, \"span\", 12);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(26, ValidatorDashboardComponent_div_0_div_26_Template, 9, 0, \"div\", 13)(27, ValidatorDashboardComponent_div_0_div_27_Template, 2, 1, \"div\", 14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(28, ValidatorDashboardComponent_div_0_div_28_Template, 13, 1, \"div\", 15);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.getGreeting(), \", \", ctx_r1.user.name, \"\");\n    i0.ɵɵadvance(14);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.quickStats);\n    i0.ɵɵadvance(6);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pendingValidations.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pendingValidations.length === 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.pendingValidations.length > 0);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.recentValidations.length > 0);\n  }\n}\nexport class ValidatorDashboardComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.user = null;\n    this.validatorStats = null;\n    this.pendingValidations = [];\n    this.recentValidations = [];\n    // Quick stats\n    this.quickStats = [{\n      title: 'Validations en Attente',\n      value: '0',\n      icon: 'pending_actions',\n      color: '#FF9800',\n      description: 'Actions à valider'\n    }, {\n      title: 'Validations Aujourd\\'hui',\n      value: '0',\n      icon: 'today',\n      color: '#4CAF50',\n      description: 'Validées aujourd\\'hui'\n    }, {\n      title: 'Total Validations',\n      value: '0',\n      icon: 'verified',\n      color: '#2196F3',\n      description: 'Toutes validations'\n    }, {\n      title: 'Taux d\\'Approbation',\n      value: '0%',\n      icon: 'thumb_up',\n      color: '#9C27B0',\n      description: 'Pourcentage approuvé'\n    }];\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user && user.role === UserRole.VALIDATOR) {\n        this.loadValidatorData();\n      } else {\n        this.router.navigate(['/dashboard']);\n      }\n    });\n  }\n  loadValidatorData() {\n    this.loadValidatorStats();\n    this.loadPendingValidations();\n    this.loadRecentValidations();\n  }\n  loadValidatorStats() {\n    // Simulate validator stats\n    this.validatorStats = {\n      validatorId: this.user.uid,\n      totalValidations: 156,\n      validationsToday: 8,\n      averageValidationTime: 2.5,\n      approvalRate: 94,\n      lastValidationAt: new Date(Date.now() - 1000 * 60 * 30)\n    };\n    // Update quick stats\n    this.quickStats[0].value = this.pendingValidations.length.toString();\n    this.quickStats[1].value = this.validatorStats.validationsToday.toString();\n    this.quickStats[2].value = this.validatorStats.totalValidations.toString();\n    this.quickStats[3].value = this.validatorStats.approvalRate + '%';\n  }\n  loadPendingValidations() {\n    // Simulate pending validations\n    this.pendingValidations = [{\n      id: '1',\n      userId: 'user1',\n      userName: 'Ahmed Ben Ali',\n      userEmail: '<EMAIL>',\n      actionType: 'LIBRARY_VOLUNTEER',\n      description: 'Aide à la bibliothèque municipale - organisation des livres',\n      location: 'Bibliothèque Municipale Monastir',\n      submittedAt: new Date(Date.now() - 1000 * 60 * 30),\n      points: 10,\n      status: ValidationStatus.PENDING,\n      evidence: ['https://example.com/photo1.jpg']\n    }, {\n      id: '2',\n      userId: 'user2',\n      userName: 'Fatma Trabelsi',\n      userEmail: '<EMAIL>',\n      actionType: 'SCHOOL_HELP',\n      description: 'Aide aux devoirs pour les élèves de primaire',\n      location: 'École Primaire Sousse',\n      submittedAt: new Date(Date.now() - 1000 * 60 * 60),\n      points: 15,\n      status: ValidationStatus.PENDING,\n      evidence: ['https://example.com/photo2.jpg', 'https://example.com/photo3.jpg']\n    }, {\n      id: '3',\n      userId: 'user3',\n      userName: 'Mohamed Gharbi',\n      userEmail: '<EMAIL>',\n      actionType: 'COMMUNITY_SERVICE',\n      description: 'Nettoyage du parc municipal',\n      location: 'Parc Central Monastir',\n      submittedAt: new Date(Date.now() - 1000 * 60 * 90),\n      points: 12,\n      status: ValidationStatus.PENDING\n    }];\n    this.quickStats[0].value = this.pendingValidations.length.toString();\n  }\n  loadRecentValidations() {\n    // Simulate recent validations\n    this.recentValidations = [{\n      id: '4',\n      userId: 'user4',\n      userName: 'Leila Mansouri',\n      userEmail: '<EMAIL>',\n      actionType: 'TUTORING',\n      description: 'Cours de mathématiques pour lycéens',\n      submittedAt: new Date(Date.now() - 1000 * 60 * 60 * 2),\n      validatedAt: new Date(Date.now() - 1000 * 60 * 60),\n      validatedBy: this.user.uid,\n      validatorName: this.user.name,\n      points: 20,\n      status: ValidationStatus.APPROVED\n    }, {\n      id: '5',\n      userId: 'user5',\n      userName: 'Karim Bouazizi',\n      userEmail: '<EMAIL>',\n      actionType: 'ENVIRONMENTAL_ACTION',\n      description: 'Plantation d\\'arbres dans le quartier',\n      submittedAt: new Date(Date.now() - 1000 * 60 * 60 * 3),\n      validatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2),\n      validatedBy: this.user.uid,\n      validatorName: this.user.name,\n      points: 15,\n      status: ValidationStatus.APPROVED\n    }];\n  }\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n  getActionTypeDisplayName(actionType) {\n    const actionNames = {\n      'SCHOOL_HELP': 'Aide scolaire',\n      'LIBRARY_VOLUNTEER': 'Bénévolat bibliothèque',\n      'ASSOCIATION_WORK': 'Travail associatif',\n      'COMMUNITY_SERVICE': 'Service communautaire',\n      'ENVIRONMENTAL_ACTION': 'Action environnementale',\n      'CULTURAL_EVENT': 'Événement culturel',\n      'SPORTS_COACHING': 'Coaching sportif',\n      'TUTORING': 'Tutorat',\n      'ELDERLY_CARE': 'Aide aux personnes âgées',\n      'OTHER': 'Autre'\n    };\n    return actionNames[actionType] || actionType;\n  }\n  getStatusColor(status) {\n    const colors = {\n      [ValidationStatus.PENDING]: '#FF9800',\n      [ValidationStatus.APPROVED]: '#4CAF50',\n      [ValidationStatus.REJECTED]: '#F44336'\n    };\n    return colors[status];\n  }\n  getStatusDisplayName(status) {\n    const statusNames = {\n      [ValidationStatus.PENDING]: 'En attente',\n      [ValidationStatus.APPROVED]: 'Approuvée',\n      [ValidationStatus.REJECTED]: 'Rejetée'\n    };\n    return statusNames[status];\n  }\n  validateAction(validation, approved) {\n    validation.status = approved ? ValidationStatus.APPROVED : ValidationStatus.REJECTED;\n    validation.validatedAt = new Date();\n    validation.validatedBy = this.user.uid;\n    validation.validatorName = this.user.name;\n    // Remove from pending list\n    this.pendingValidations = this.pendingValidations.filter(v => v.id !== validation.id);\n    // Add to recent validations\n    this.recentValidations.unshift(validation);\n    // Update stats\n    this.validatorStats.validationsToday++;\n    this.validatorStats.totalValidations++;\n    this.quickStats[0].value = this.pendingValidations.length.toString();\n    this.quickStats[1].value = this.validatorStats.validationsToday.toString();\n    this.quickStats[2].value = this.validatorStats.totalValidations.toString();\n    console.log(`Validation ${approved ? 'approved' : 'rejected'}:`, validation);\n  }\n  viewValidationDetails(validation) {\n    console.log('View validation details:', validation);\n    // TODO: Open validation details modal\n  }\n  navigateTo(route) {\n    this.router.navigate([route]);\n  }\n  static {\n    this.ɵfac = function ValidatorDashboardComponent_Factory(t) {\n      return new (t || ValidatorDashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ValidatorDashboardComponent,\n      selectors: [[\"app-validator-dashboard\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"validator-dashboard-container\", 4, \"ngIf\"], [1, \"validator-dashboard-container\"], [1, \"validator-header\"], [1, \"header-content\"], [1, \"validator-welcome\"], [1, \"validator-subtitle\"], [1, \"validator-badge\"], [1, \"quick-stats-section\"], [1, \"section-title\"], [1, \"stats-grid\"], [\"class\", \"stat-card\", 3, \"border-left-color\", 4, \"ngFor\", \"ngForOf\"], [1, \"pending-section\"], [\"class\", \"pending-count\", 4, \"ngIf\"], [\"class\", \"empty-state\", 4, \"ngIf\"], [\"class\", \"validations-grid\", 4, \"ngIf\"], [\"class\", \"recent-section\", 4, \"ngIf\"], [1, \"stat-card\"], [1, \"stat-header\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"stat-value\"], [1, \"stat-title\"], [1, \"stat-description\"], [1, \"pending-count\"], [1, \"empty-state\"], [1, \"empty-card\"], [1, \"empty-icon\"], [1, \"validations-grid\"], [\"class\", \"validation-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"validation-card\"], [1, \"validation-header\"], [1, \"user-info\"], [1, \"user-email\"], [1, \"action-type\"], [1, \"type-badge\"], [1, \"validation-details\"], [1, \"description\"], [1, \"validation-meta\"], [\"class\", \"meta-item\", 4, \"ngIf\"], [1, \"meta-item\"], [1, \"meta-item\", \"points\"], [\"class\", \"evidence-section\", 4, \"ngIf\"], [1, \"validation-actions\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"approve-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 1, \"reject-btn\", 3, \"click\"], [\"mat-button\", \"\", 3, \"click\"], [1, \"evidence-section\"], [1, \"evidence-list\"], [\"mat-stroked-button\", \"\", \"class\", \"evidence-btn\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", 1, \"evidence-btn\", 3, \"click\"], [1, \"recent-section\"], [1, \"recent-card\"], [1, \"recent-list\"], [\"class\", \"recent-item\", 4, \"ngFor\", \"ngForOf\"], [\"mat-stroked-button\", \"\", 1, \"view-all-btn\", 3, \"click\"], [1, \"recent-item\"], [1, \"recent-icon\"], [1, \"recent-content\"], [1, \"recent-description\"], [1, \"recent-meta\"], [1, \"recent-time\"], [1, \"recent-status\"], [1, \"recent-points\"]],\n      template: function ValidatorDashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, ValidatorDashboardComponent_div_0_Template, 29, 7, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n        }\n      },\n      dependencies: [i3.NgForOf, i3.NgIf, i4.MatButton, i5.MatCard, i5.MatCardActions, i5.MatCardContent, i5.MatCardHeader, i6.MatIcon, i7.TimeAgoPipe],\n      styles: [\"\\n\\n.validator-dashboard-container[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  min-height: 100vh;\\n}\\n\\n\\n\\n.validator-header[_ngcontent-%COMP%] {\\n  margin-bottom: 40px;\\n  padding: 40px;\\n  background: linear-gradient(135deg, #2196F3 0%, #1976D2 100%);\\n  border-radius: 24px;\\n  color: white;\\n  box-shadow: 0 20px 40px rgba(33, 150, 243, 0.3);\\n}\\n\\n.header-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n}\\n\\n.validator-welcome[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  font-family: 'Poppins', sans-serif;\\n}\\n\\n.validator-subtitle[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.1rem;\\n  opacity: 0.9;\\n}\\n\\n.validator-badge[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 16px 24px;\\n  border-radius: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.3);\\n}\\n\\n.validator-badge[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.validator-badge[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  font-size: 1.1rem;\\n}\\n\\n\\n\\n.section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  margin: 0 0 24px 0;\\n  color: #2d3748;\\n  font-size: 1.8rem;\\n  font-weight: 700;\\n  font-family: 'Poppins', sans-serif;\\n  position: relative;\\n}\\n\\n.pending-count[_ngcontent-%COMP%] {\\n  background: #FF9800;\\n  color: white;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  margin-left: 12px;\\n}\\n\\n\\n\\n.quick-stats-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.stats-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 24px;\\n}\\n\\n.stat-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  border-left: 4px solid;\\n  transition: all 0.3s ease;\\n}\\n\\n.stat-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.stat-header[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%] {\\n  width: 48px;\\n  height: 48px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.stat-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  width: 1.5rem;\\n  height: 1.5rem;\\n}\\n\\n.stat-value[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 2rem;\\n  font-weight: 700;\\n  color: #2d3748;\\n}\\n\\n.stat-title[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #4a5568;\\n  font-size: 1rem;\\n  font-weight: 600;\\n}\\n\\n.stat-description[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: 0.8rem;\\n}\\n\\n\\n\\n.pending-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.empty-state[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.empty-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  padding: 40px;\\n}\\n\\n.empty-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem !important;\\n  width: 4rem !important;\\n  height: 4rem !important;\\n  color: #4CAF50;\\n  margin-bottom: 16px;\\n}\\n\\n.empty-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #2d3748;\\n  font-size: 1.5rem;\\n}\\n\\n.empty-card[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #718096;\\n}\\n\\n.validations-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));\\n  gap: 24px;\\n}\\n\\n.validation-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n}\\n\\n.validation-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.validation-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: flex-start;\\n  width: 100%;\\n}\\n\\n.user-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #2d3748;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.user-email[_ngcontent-%COMP%] {\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n\\n.type-badge[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(33, 150, 243, 0.1), rgba(25, 118, 210, 0.1));\\n  color: #1976D2;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  text-transform: uppercase;\\n}\\n\\n.description[_ngcontent-%COMP%] {\\n  margin: 16px 0;\\n  color: #2d3748;\\n  font-size: 1rem;\\n  line-height: 1.5;\\n}\\n\\n.validation-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-wrap: wrap;\\n  gap: 16px;\\n  margin-bottom: 16px;\\n}\\n\\n.meta-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  color: #4a5568;\\n  font-size: 0.9rem;\\n}\\n\\n.meta-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  width: 1rem !important;\\n  height: 1rem !important;\\n  color: #718096;\\n}\\n\\n.meta-item.points[_ngcontent-%COMP%] {\\n  color: #FF9800;\\n  font-weight: 600;\\n}\\n\\n.meta-item.points[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #FF9800;\\n}\\n\\n.evidence-section[_ngcontent-%COMP%] {\\n  margin-top: 16px;\\n  padding-top: 16px;\\n  border-top: 1px solid #e2e8f0;\\n}\\n\\n.evidence-section[_ngcontent-%COMP%]   h5[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  color: #4a5568;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n}\\n\\n.evidence-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  flex-wrap: wrap;\\n}\\n\\n.evidence-btn[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n}\\n\\n.validation-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  flex-wrap: wrap;\\n  padding: 16px;\\n  border-top: 1px solid #e2e8f0;\\n}\\n\\n.approve-btn[_ngcontent-%COMP%], .reject-btn[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n\\n\\n\\n.recent-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.recent-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.recent-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  margin-bottom: 24px;\\n}\\n\\n.recent-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 16px;\\n  background: #f7fafc;\\n  border-radius: 12px;\\n  transition: all 0.2s ease;\\n}\\n\\n.recent-item[_ngcontent-%COMP%]:hover {\\n  background: #edf2f7;\\n  transform: translateX(4px);\\n}\\n\\n.recent-icon[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n}\\n\\n.recent-icon[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1.2rem;\\n  width: 1.2rem;\\n  height: 1.2rem;\\n}\\n\\n.recent-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.recent-description[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #2d3748;\\n  font-size: 0.9rem;\\n}\\n\\n.recent-meta[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 16px;\\n  align-items: center;\\n  font-size: 0.8rem;\\n}\\n\\n.recent-time[_ngcontent-%COMP%] {\\n  color: #718096;\\n}\\n\\n.recent-status[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n}\\n\\n.recent-points[_ngcontent-%COMP%] {\\n  color: #FF9800;\\n  font-weight: 600;\\n}\\n\\n.view-all-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 44px;\\n  font-weight: 600;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .validator-dashboard-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n  \\n  .header-content[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n    text-align: center;\\n  }\\n  \\n  .validator-welcome[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n  \\n  .stats-grid[_ngcontent-%COMP%], .validations-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n  \\n  .validation-actions[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n  }\\n  \\n  .recent-meta[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    align-items: flex-start;\\n    gap: 4px;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInZhbGlkYXRvci1kYXNoYm9hcmQuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxrQ0FBa0M7QUFDbEM7RUFDRSxpQkFBaUI7RUFDakIsY0FBYztFQUNkLGFBQWE7RUFDYiw2REFBNkQ7RUFDN0QsaUJBQWlCO0FBQ25COztBQUVBLG1CQUFtQjtBQUNuQjtFQUNFLG1CQUFtQjtFQUNuQixhQUFhO0VBQ2IsNkRBQTZEO0VBQzdELG1CQUFtQjtFQUNuQixZQUFZO0VBQ1osK0NBQStDO0FBQ2pEOztBQUVBO0VBQ0UsYUFBYTtFQUNiLDhCQUE4QjtFQUM5QixtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsaUJBQWlCO0VBQ2pCLGdCQUFnQjtFQUNoQixrQ0FBa0M7QUFDcEM7O0FBRUE7RUFDRSxTQUFTO0VBQ1QsaUJBQWlCO0VBQ2pCLFlBQVk7QUFDZDs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsU0FBUztFQUNULG9DQUFvQztFQUNwQyxrQkFBa0I7RUFDbEIsbUJBQW1CO0VBQ25CLG1DQUEyQjtVQUEzQiwyQkFBMkI7RUFDM0IsMENBQTBDO0FBQzVDOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLGFBQWE7RUFDYixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsZ0JBQWdCO0VBQ2hCLGlCQUFpQjtBQUNuQjs7QUFFQSxtQkFBbUI7QUFDbkI7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFNBQVM7RUFDVCxrQkFBa0I7RUFDbEIsY0FBYztFQUNkLGlCQUFpQjtFQUNqQixnQkFBZ0I7RUFDaEIsa0NBQWtDO0VBQ2xDLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixZQUFZO0VBQ1osaUJBQWlCO0VBQ2pCLG1CQUFtQjtFQUNuQixpQkFBaUI7RUFDakIsZ0JBQWdCO0VBQ2hCLGlCQUFpQjtBQUNuQjs7QUFFQSxnQkFBZ0I7QUFDaEI7RUFDRSxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsMkRBQTJEO0VBQzNELFNBQVM7QUFDWDs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQix5Q0FBeUM7RUFDekMsc0JBQXNCO0VBQ3RCLHlCQUF5QjtBQUMzQjs7QUFFQTtFQUNFLDJCQUEyQjtFQUMzQiwwQ0FBMEM7QUFDNUM7O0FBRUE7RUFDRSxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLG1CQUFtQjtFQUNuQixhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLHVCQUF1QjtBQUN6Qjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixhQUFhO0VBQ2IsY0FBYztBQUNoQjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixlQUFlO0VBQ2YsZ0JBQWdCO0VBQ2hCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsY0FBYztFQUNkLGVBQWU7RUFDZixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxjQUFjO0VBQ2QsaUJBQWlCO0FBQ25COztBQUVBLHdCQUF3QjtBQUN4QjtFQUNFLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQix5Q0FBeUM7RUFDekMsYUFBYTtBQUNmOztBQUVBO0VBQ0UsMEJBQTBCO0VBQzFCLHNCQUFzQjtFQUN0Qix1QkFBdUI7RUFDdkIsY0FBYztFQUNkLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixjQUFjO0VBQ2QsaUJBQWlCO0FBQ25COztBQUVBO0VBQ0UsU0FBUztFQUNULGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsMkRBQTJEO0VBQzNELFNBQVM7QUFDWDs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQix5Q0FBeUM7RUFDekMseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UsMkJBQTJCO0VBQzNCLDBDQUEwQztBQUM1Qzs7QUFFQTtFQUNFLGFBQWE7RUFDYiw4QkFBOEI7RUFDOUIsdUJBQXVCO0VBQ3ZCLFdBQVc7QUFDYjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixjQUFjO0VBQ2QsaUJBQWlCO0VBQ2pCLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGNBQWM7RUFDZCxpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxxRkFBcUY7RUFDckYsY0FBYztFQUNkLGlCQUFpQjtFQUNqQixtQkFBbUI7RUFDbkIsaUJBQWlCO0VBQ2pCLGdCQUFnQjtFQUNoQix5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxjQUFjO0VBQ2QsY0FBYztFQUNkLGVBQWU7RUFDZixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsZUFBZTtFQUNmLFNBQVM7RUFDVCxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFFBQVE7RUFDUixjQUFjO0VBQ2QsaUJBQWlCO0FBQ25COztBQUVBO0VBQ0UsMEJBQTBCO0VBQzFCLHNCQUFzQjtFQUN0Qix1QkFBdUI7RUFDdkIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLGNBQWM7RUFDZCxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsZ0JBQWdCO0VBQ2hCLGlCQUFpQjtFQUNqQiw2QkFBNkI7QUFDL0I7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsY0FBYztFQUNkLGlCQUFpQjtFQUNqQixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsUUFBUTtFQUNSLGVBQWU7QUFDakI7O0FBRUE7RUFDRSxpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsUUFBUTtFQUNSLGVBQWU7RUFDZixhQUFhO0VBQ2IsNkJBQTZCO0FBQy9COztBQUVBO0VBQ0UsZ0JBQWdCO0FBQ2xCOztBQUVBLHVCQUF1QjtBQUN2QjtFQUNFLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQix5Q0FBeUM7QUFDM0M7O0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLFNBQVM7RUFDVCxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFNBQVM7RUFDVCxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLG1CQUFtQjtFQUNuQix5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsMEJBQTBCO0FBQzVCOztBQUVBO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWixrQkFBa0I7RUFDbEIsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7QUFDekI7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsYUFBYTtFQUNiLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxPQUFPO0FBQ1Q7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsY0FBYztFQUNkLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixTQUFTO0VBQ1QsbUJBQW1CO0VBQ25CLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxjQUFjO0VBQ2QsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWixnQkFBZ0I7QUFDbEI7O0FBRUEsc0JBQXNCO0FBQ3RCO0VBQ0U7SUFDRSxhQUFhO0VBQ2Y7O0VBRUE7SUFDRSxzQkFBc0I7SUFDdEIsU0FBUztJQUNULGtCQUFrQjtFQUNwQjs7RUFFQTtJQUNFLGVBQWU7RUFDakI7O0VBRUE7O0lBRUUsMEJBQTBCO0VBQzVCOztFQUVBO0lBQ0Usc0JBQXNCO0VBQ3hCOztFQUVBO0lBQ0Usc0JBQXNCO0lBQ3RCLHVCQUF1QjtJQUN2QixRQUFRO0VBQ1Y7QUFDRiIsImZpbGUiOiJ2YWxpZGF0b3ItZGFzaGJvYXJkLmNvbXBvbmVudC5jc3MiLCJzb3VyY2VzQ29udGVudCI6WyIvKiBWYWxpZGF0b3IgRGFzaGJvYXJkIENvbnRhaW5lciAqL1xuLnZhbGlkYXRvci1kYXNoYm9hcmQtY29udGFpbmVyIHtcbiAgbWF4LXdpZHRoOiAxNDAwcHg7XG4gIG1hcmdpbjogMCBhdXRvO1xuICBwYWRkaW5nOiAyMHB4O1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjVmN2ZhIDAlLCAjYzNjZmUyIDEwMCUpO1xuICBtaW4taGVpZ2h0OiAxMDB2aDtcbn1cblxuLyogSGVhZGVyIFNlY3Rpb24gKi9cbi52YWxpZGF0b3ItaGVhZGVyIHtcbiAgbWFyZ2luLWJvdHRvbTogNDBweDtcbiAgcGFkZGluZzogNDBweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzIxOTZGMyAwJSwgIzE5NzZEMiAxMDAlKTtcbiAgYm9yZGVyLXJhZGl1czogMjRweDtcbiAgY29sb3I6IHdoaXRlO1xuICBib3gtc2hhZG93OiAwIDIwcHggNDBweCByZ2JhKDMzLCAxNTAsIDI0MywgMC4zKTtcbn1cblxuLmhlYWRlci1jb250ZW50IHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xufVxuXG4udmFsaWRhdG9yLXdlbGNvbWUgaDEge1xuICBtYXJnaW46IDAgMCA4cHggMDtcbiAgZm9udC1zaXplOiAyLjVyZW07XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGZvbnQtZmFtaWx5OiAnUG9wcGlucycsIHNhbnMtc2VyaWY7XG59XG5cbi52YWxpZGF0b3Itc3VidGl0bGUge1xuICBtYXJnaW46IDA7XG4gIGZvbnQtc2l6ZTogMS4xcmVtO1xuICBvcGFjaXR5OiAwLjk7XG59XG5cbi52YWxpZGF0b3ItYmFkZ2Uge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDEycHg7XG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4yKTtcbiAgcGFkZGluZzogMTZweCAyNHB4O1xuICBib3JkZXItcmFkaXVzOiAyMHB4O1xuICBiYWNrZHJvcC1maWx0ZXI6IGJsdXIoMTBweCk7XG4gIGJvcmRlcjogMXB4IHNvbGlkIHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4zKTtcbn1cblxuLnZhbGlkYXRvci1iYWRnZSBtYXQtaWNvbiB7XG4gIGZvbnQtc2l6ZTogMS41cmVtO1xuICB3aWR0aDogMS41cmVtO1xuICBoZWlnaHQ6IDEuNXJlbTtcbn1cblxuLnZhbGlkYXRvci1iYWRnZSBzcGFuIHtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgZm9udC1zaXplOiAxLjFyZW07XG59XG5cbi8qIFNlY3Rpb24gVGl0bGVzICovXG4uc2VjdGlvbi10aXRsZSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMTJweDtcbiAgbWFyZ2luOiAwIDAgMjRweCAwO1xuICBjb2xvcjogIzJkMzc0ODtcbiAgZm9udC1zaXplOiAxLjhyZW07XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGZvbnQtZmFtaWx5OiAnUG9wcGlucycsIHNhbnMtc2VyaWY7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cblxuLnBlbmRpbmctY291bnQge1xuICBiYWNrZ3JvdW5kOiAjRkY5ODAwO1xuICBjb2xvcjogd2hpdGU7XG4gIHBhZGRpbmc6IDRweCAxMnB4O1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBmb250LXNpemU6IDAuOXJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgbWFyZ2luLWxlZnQ6IDEycHg7XG59XG5cbi8qIFF1aWNrIFN0YXRzICovXG4ucXVpY2stc3RhdHMtc2VjdGlvbiB7XG4gIG1hcmdpbi1ib3R0b206IDQ4cHg7XG59XG5cbi5zdGF0cy1ncmlkIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgyNTBweCwgMWZyKSk7XG4gIGdhcDogMjRweDtcbn1cblxuLnN0YXQtY2FyZCB7XG4gIGJvcmRlci1yYWRpdXM6IDE2cHg7XG4gIGJveC1zaGFkb3c6IDAgNHB4IDEycHggcmdiYSgwLCAwLCAwLCAwLjEpO1xuICBib3JkZXItbGVmdDogNHB4IHNvbGlkO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xufVxuXG4uc3RhdC1jYXJkOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC00cHgpO1xuICBib3gtc2hhZG93OiAwIDhweCAyNXB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XG59XG5cbi5zdGF0LWhlYWRlciB7XG4gIG1hcmdpbi1ib3R0b206IDE2cHg7XG59XG5cbi5zdGF0LWljb24ge1xuICB3aWR0aDogNDhweDtcbiAgaGVpZ2h0OiA0OHB4O1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbn1cblxuLnN0YXQtaWNvbiBtYXQtaWNvbiB7XG4gIGZvbnQtc2l6ZTogMS41cmVtO1xuICB3aWR0aDogMS41cmVtO1xuICBoZWlnaHQ6IDEuNXJlbTtcbn1cblxuLnN0YXQtdmFsdWUge1xuICBtYXJnaW46IDAgMCA0cHggMDtcbiAgZm9udC1zaXplOiAycmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBjb2xvcjogIzJkMzc0ODtcbn1cblxuLnN0YXQtdGl0bGUge1xuICBtYXJnaW46IDAgMCA0cHggMDtcbiAgY29sb3I6ICM0YTU1Njg7XG4gIGZvbnQtc2l6ZTogMXJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbn1cblxuLnN0YXQtZGVzY3JpcHRpb24ge1xuICBjb2xvcjogIzcxODA5NjtcbiAgZm9udC1zaXplOiAwLjhyZW07XG59XG5cbi8qIFBlbmRpbmcgVmFsaWRhdGlvbnMgKi9cbi5wZW5kaW5nLXNlY3Rpb24ge1xuICBtYXJnaW4tYm90dG9tOiA0OHB4O1xufVxuXG4uZW1wdHktc3RhdGUge1xuICB0ZXh0LWFsaWduOiBjZW50ZXI7XG59XG5cbi5lbXB0eS1jYXJkIHtcbiAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIHBhZGRpbmc6IDQwcHg7XG59XG5cbi5lbXB0eS1pY29uIHtcbiAgZm9udC1zaXplOiA0cmVtICFpbXBvcnRhbnQ7XG4gIHdpZHRoOiA0cmVtICFpbXBvcnRhbnQ7XG4gIGhlaWdodDogNHJlbSAhaW1wb3J0YW50O1xuICBjb2xvcjogIzRDQUY1MDtcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcbn1cblxuLmVtcHR5LWNhcmQgaDMge1xuICBtYXJnaW46IDAgMCA4cHggMDtcbiAgY29sb3I6ICMyZDM3NDg7XG4gIGZvbnQtc2l6ZTogMS41cmVtO1xufVxuXG4uZW1wdHktY2FyZCBwIHtcbiAgbWFyZ2luOiAwO1xuICBjb2xvcjogIzcxODA5Njtcbn1cblxuLnZhbGlkYXRpb25zLWdyaWQge1xuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDQwMHB4LCAxZnIpKTtcbiAgZ2FwOiAyNHB4O1xufVxuXG4udmFsaWRhdGlvbi1jYXJkIHtcbiAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG59XG5cbi52YWxpZGF0aW9uLWNhcmQ6aG92ZXIge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG4gIGJveC1zaGFkb3c6IDAgOHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcbn1cblxuLnZhbGlkYXRpb24taGVhZGVyIHtcbiAgZGlzcGxheTogZmxleDtcbiAganVzdGlmeS1jb250ZW50OiBzcGFjZS1iZXR3ZWVuO1xuICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgd2lkdGg6IDEwMCU7XG59XG5cbi51c2VyLWluZm8gaDQge1xuICBtYXJnaW46IDAgMCA0cHggMDtcbiAgY29sb3I6ICMyZDM3NDg7XG4gIGZvbnQtc2l6ZTogMS4xcmVtO1xuICBmb250LXdlaWdodDogNjAwO1xufVxuXG4udXNlci1lbWFpbCB7XG4gIGNvbG9yOiAjNzE4MDk2O1xuICBmb250LXNpemU6IDAuOXJlbTtcbn1cblxuLnR5cGUtYmFkZ2Uge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDMzLCAxNTAsIDI0MywgMC4xKSwgcmdiYSgyNSwgMTE4LCAyMTAsIDAuMSkpO1xuICBjb2xvcjogIzE5NzZEMjtcbiAgcGFkZGluZzogNHB4IDEycHg7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICB0ZXh0LXRyYW5zZm9ybTogdXBwZXJjYXNlO1xufVxuXG4uZGVzY3JpcHRpb24ge1xuICBtYXJnaW46IDE2cHggMDtcbiAgY29sb3I6ICMyZDM3NDg7XG4gIGZvbnQtc2l6ZTogMXJlbTtcbiAgbGluZS1oZWlnaHQ6IDEuNTtcbn1cblxuLnZhbGlkYXRpb24tbWV0YSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtd3JhcDogd3JhcDtcbiAgZ2FwOiAxNnB4O1xuICBtYXJnaW4tYm90dG9tOiAxNnB4O1xufVxuXG4ubWV0YS1pdGVtIHtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgZ2FwOiA0cHg7XG4gIGNvbG9yOiAjNGE1NTY4O1xuICBmb250LXNpemU6IDAuOXJlbTtcbn1cblxuLm1ldGEtaXRlbSBtYXQtaWNvbiB7XG4gIGZvbnQtc2l6ZTogMXJlbSAhaW1wb3J0YW50O1xuICB3aWR0aDogMXJlbSAhaW1wb3J0YW50O1xuICBoZWlnaHQ6IDFyZW0gIWltcG9ydGFudDtcbiAgY29sb3I6ICM3MTgwOTY7XG59XG5cbi5tZXRhLWl0ZW0ucG9pbnRzIHtcbiAgY29sb3I6ICNGRjk4MDA7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG59XG5cbi5tZXRhLWl0ZW0ucG9pbnRzIG1hdC1pY29uIHtcbiAgY29sb3I6ICNGRjk4MDA7XG59XG5cbi5ldmlkZW5jZS1zZWN0aW9uIHtcbiAgbWFyZ2luLXRvcDogMTZweDtcbiAgcGFkZGluZy10b3A6IDE2cHg7XG4gIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTJlOGYwO1xufVxuXG4uZXZpZGVuY2Utc2VjdGlvbiBoNSB7XG4gIG1hcmdpbjogMCAwIDEycHggMDtcbiAgY29sb3I6ICM0YTU1Njg7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xufVxuXG4uZXZpZGVuY2UtbGlzdCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogOHB4O1xuICBmbGV4LXdyYXA6IHdyYXA7XG59XG5cbi5ldmlkZW5jZS1idG4ge1xuICBmb250LXNpemU6IDAuOHJlbTtcbn1cblxuLnZhbGlkYXRpb24tYWN0aW9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogOHB4O1xuICBmbGV4LXdyYXA6IHdyYXA7XG4gIHBhZGRpbmc6IDE2cHg7XG4gIGJvcmRlci10b3A6IDFweCBzb2xpZCAjZTJlOGYwO1xufVxuXG4uYXBwcm92ZS1idG4sIC5yZWplY3QtYnRuIHtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbn1cblxuLyogUmVjZW50IFZhbGlkYXRpb25zICovXG4ucmVjZW50LXNlY3Rpb24ge1xuICBtYXJnaW4tYm90dG9tOiA0OHB4O1xufVxuXG4ucmVjZW50LWNhcmQge1xuICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbn1cblxuLnJlY2VudC1saXN0IHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAxNnB4O1xuICBtYXJnaW4tYm90dG9tOiAyNHB4O1xufVxuXG4ucmVjZW50LWl0ZW0ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDE2cHg7XG4gIHBhZGRpbmc6IDE2cHg7XG4gIGJhY2tncm91bmQ6ICNmN2ZhZmM7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG59XG5cbi5yZWNlbnQtaXRlbTpob3ZlciB7XG4gIGJhY2tncm91bmQ6ICNlZGYyZjc7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWCg0cHgpO1xufVxuXG4ucmVjZW50LWljb24ge1xuICB3aWR0aDogNDBweDtcbiAgaGVpZ2h0OiA0MHB4O1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xufVxuXG4ucmVjZW50LWljb24gbWF0LWljb24ge1xuICBmb250LXNpemU6IDEuMnJlbTtcbiAgd2lkdGg6IDEuMnJlbTtcbiAgaGVpZ2h0OiAxLjJyZW07XG59XG5cbi5yZWNlbnQtY29udGVudCB7XG4gIGZsZXg6IDE7XG59XG5cbi5yZWNlbnQtZGVzY3JpcHRpb24ge1xuICBtYXJnaW46IDAgMCA0cHggMDtcbiAgY29sb3I6ICMyZDM3NDg7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xufVxuXG4ucmVjZW50LW1ldGEge1xuICBkaXNwbGF5OiBmbGV4O1xuICBnYXA6IDE2cHg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xufVxuXG4ucmVjZW50LXRpbWUge1xuICBjb2xvcjogIzcxODA5Njtcbn1cblxuLnJlY2VudC1zdGF0dXMge1xuICBmb250LXdlaWdodDogNjAwO1xufVxuXG4ucmVjZW50LXBvaW50cyB7XG4gIGNvbG9yOiAjRkY5ODAwO1xuICBmb250LXdlaWdodDogNjAwO1xufVxuXG4udmlldy1hbGwtYnRuIHtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogNDRweDtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbn1cblxuLyogTW9iaWxlIFJlc3BvbnNpdmUgKi9cbkBtZWRpYSAobWF4LXdpZHRoOiA3NjhweCkge1xuICAudmFsaWRhdG9yLWRhc2hib2FyZC1jb250YWluZXIge1xuICAgIHBhZGRpbmc6IDE2cHg7XG4gIH1cbiAgXG4gIC5oZWFkZXItY29udGVudCB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBnYXA6IDIwcHg7XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICB9XG4gIFxuICAudmFsaWRhdG9yLXdlbGNvbWUgaDEge1xuICAgIGZvbnQtc2l6ZTogMnJlbTtcbiAgfVxuICBcbiAgLnN0YXRzLWdyaWQsXG4gIC52YWxpZGF0aW9ucy1ncmlkIHtcbiAgICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IDFmcjtcbiAgfVxuICBcbiAgLnZhbGlkYXRpb24tYWN0aW9ucyB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgfVxuICBcbiAgLnJlY2VudC1tZXRhIHtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGFsaWduLWl0ZW1zOiBmbGV4LXN0YXJ0O1xuICAgIGdhcDogNHB4O1xuICB9XG59XG4iXX0= */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["UserRole", "ValidationStatus", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵstyleProp", "stat_r1", "color", "ɵɵadvance", "ɵɵtextInterpolate", "icon", "value", "title", "description", "ctx_r1", "pendingValidations", "length", "validation_r4", "location", "ɵɵlistener", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_button_4_Template_button_click_0_listener", "evidence_r6", "ɵɵrestoreView", "_r5", "$implicit", "ɵɵnextContext", "ɵɵresetView", "viewEvidence", "ɵɵtemplate", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_button_4_Template", "ɵɵproperty", "evidence", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_16_Template", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_div_28_Template", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template_button_click_30_listener", "_r3", "validateAction", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template_button_click_34_listener", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template_button_click_38_listener", "viewValidationDetails", "userName", "userEmail", "getActionTypeDisplayName", "actionType", "ɵɵpipeBind1", "submittedAt", "ɵɵtextInterpolate1", "points", "ValidatorDashboardComponent_div_0_div_27_mat_card_1_Template", "getStatusColor", "validation_r8", "status", "validatedAt", "getStatusDisplayName", "ValidatorDashboardComponent_div_0_div_28_div_8_Template", "ValidatorDashboardComponent_div_0_div_28_Template_button_click_9_listener", "_r7", "navigateTo", "recentValidations", "slice", "ValidatorDashboardComponent_div_0_mat_card_19_Template", "ValidatorDashboardComponent_div_0_span_25_Template", "ValidatorDashboardComponent_div_0_div_26_Template", "ValidatorDashboardComponent_div_0_div_27_Template", "ValidatorDashboardComponent_div_0_div_28_Template", "ɵɵtextInterpolate2", "getGreeting", "user", "name", "quickStats", "ValidatorDashboardComponent", "constructor", "authService", "router", "validatorStats", "ngOnInit", "currentUser$", "subscribe", "role", "VALIDATOR", "loadValidatorData", "navigate", "loadValidatorStats", "loadPendingValidations", "loadRecentValidations", "validatorId", "uid", "totalValidations", "validationsToday", "averageValidationTime", "approvalRate", "lastValidationAt", "Date", "now", "toString", "id", "userId", "PENDING", "validatedBy", "validatorName", "APPROVED", "hour", "getHours", "actionNames", "colors", "REJECTED", "statusNames", "validation", "approved", "filter", "v", "unshift", "console", "log", "route", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "ValidatorDashboardComponent_Template", "rf", "ctx", "ValidatorDashboardComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\validator-dashboard\\components\\validator-dashboard\\validator-dashboard.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\validator-dashboard\\components\\validator-dashboard\\validator-dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { User, UserRole, ValidationStatus, ValidatorStats } from '../../../../core/models';\n\n// Interface locale pour les validations\ninterface ValidationRequest {\n  id: string;\n  userId: string;\n  userName: string;\n  userEmail: string;\n  actionType: string;\n  description: string;\n  location?: string;\n  submittedAt: Date;\n  validatedAt?: Date;\n  validatedBy?: string;\n  validatorName?: string;\n  points: number;\n  status: ValidationStatus;\n  evidence?: string[];\n  comments?: string;\n}\n\n@Component({\n  selector: 'app-validator-dashboard',\n  templateUrl: './validator-dashboard.component.html',\n  styleUrls: ['./validator-dashboard.component.css']\n})\nexport class ValidatorDashboardComponent implements OnInit {\n  user: User | null = null;\n  validatorStats: ValidatorStats | null = null;\n  pendingValidations: ValidationRequest[] = [];\n  recentValidations: ValidationRequest[] = [];\n\n  // Quick stats\n  quickStats = [\n    {\n      title: 'Validations en Attente',\n      value: '0',\n      icon: 'pending_actions',\n      color: '#FF9800',\n      description: 'Actions à valider'\n    },\n    {\n      title: 'Validations Aujourd\\'hui',\n      value: '0',\n      icon: 'today',\n      color: '#4CAF50',\n      description: 'Validées aujourd\\'hui'\n    },\n    {\n      title: 'Total Validations',\n      value: '0',\n      icon: 'verified',\n      color: '#2196F3',\n      description: 'Toutes validations'\n    },\n    {\n      title: 'Taux d\\'Approbation',\n      value: '0%',\n      icon: 'thumb_up',\n      color: '#9C27B0',\n      description: 'Pourcentage approuvé'\n    }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user && user.role === UserRole.VALIDATOR) {\n        this.loadValidatorData();\n      } else {\n        this.router.navigate(['/dashboard']);\n      }\n    });\n  }\n\n  private loadValidatorData(): void {\n    this.loadValidatorStats();\n    this.loadPendingValidations();\n    this.loadRecentValidations();\n  }\n\n  private loadValidatorStats(): void {\n    // Simulate validator stats\n    this.validatorStats = {\n      validatorId: this.user!.uid,\n      totalValidations: 156,\n      validationsToday: 8,\n      averageValidationTime: 2.5,\n      approvalRate: 94,\n      lastValidationAt: new Date(Date.now() - 1000 * 60 * 30)\n    };\n\n    // Update quick stats\n    this.quickStats[0].value = this.pendingValidations.length.toString();\n    this.quickStats[1].value = this.validatorStats.validationsToday.toString();\n    this.quickStats[2].value = this.validatorStats.totalValidations.toString();\n    this.quickStats[3].value = this.validatorStats.approvalRate + '%';\n  }\n\n  private loadPendingValidations(): void {\n    // Simulate pending validations\n    this.pendingValidations = [\n      {\n        id: '1',\n        userId: 'user1',\n        userName: 'Ahmed Ben Ali',\n        userEmail: '<EMAIL>',\n        actionType: 'LIBRARY_VOLUNTEER',\n        description: 'Aide à la bibliothèque municipale - organisation des livres',\n        location: 'Bibliothèque Municipale Monastir',\n        submittedAt: new Date(Date.now() - 1000 * 60 * 30),\n        points: 10,\n        status: ValidationStatus.PENDING,\n        evidence: ['https://example.com/photo1.jpg']\n      },\n      {\n        id: '2',\n        userId: 'user2',\n        userName: 'Fatma Trabelsi',\n        userEmail: '<EMAIL>',\n        actionType: 'SCHOOL_HELP' as any,\n        description: 'Aide aux devoirs pour les élèves de primaire',\n        location: 'École Primaire Sousse',\n        submittedAt: new Date(Date.now() - 1000 * 60 * 60),\n        points: 15,\n        status: ValidationStatus.PENDING,\n        evidence: ['https://example.com/photo2.jpg', 'https://example.com/photo3.jpg']\n      },\n      {\n        id: '3',\n        userId: 'user3',\n        userName: 'Mohamed Gharbi',\n        userEmail: '<EMAIL>',\n        actionType: 'COMMUNITY_SERVICE' as any,\n        description: 'Nettoyage du parc municipal',\n        location: 'Parc Central Monastir',\n        submittedAt: new Date(Date.now() - 1000 * 60 * 90),\n        points: 12,\n        status: ValidationStatus.PENDING\n      }\n    ];\n\n    this.quickStats[0].value = this.pendingValidations.length.toString();\n  }\n\n  private loadRecentValidations(): void {\n    // Simulate recent validations\n    this.recentValidations = [\n      {\n        id: '4',\n        userId: 'user4',\n        userName: 'Leila Mansouri',\n        userEmail: '<EMAIL>',\n        actionType: 'TUTORING' as any,\n        description: 'Cours de mathématiques pour lycéens',\n        submittedAt: new Date(Date.now() - 1000 * 60 * 60 * 2),\n        validatedAt: new Date(Date.now() - 1000 * 60 * 60),\n        validatedBy: this.user!.uid,\n        validatorName: this.user!.name,\n        points: 20,\n        status: ValidationStatus.APPROVED\n      },\n      {\n        id: '5',\n        userId: 'user5',\n        userName: 'Karim Bouazizi',\n        userEmail: '<EMAIL>',\n        actionType: 'ENVIRONMENTAL_ACTION' as any,\n        description: 'Plantation d\\'arbres dans le quartier',\n        submittedAt: new Date(Date.now() - 1000 * 60 * 60 * 3),\n        validatedAt: new Date(Date.now() - 1000 * 60 * 60 * 2),\n        validatedBy: this.user!.uid,\n        validatorName: this.user!.name,\n        points: 15,\n        status: ValidationStatus.APPROVED\n      }\n    ];\n  }\n\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n\n  getActionTypeDisplayName(actionType: string): string {\n    const actionNames: { [key: string]: string } = {\n      'SCHOOL_HELP': 'Aide scolaire',\n      'LIBRARY_VOLUNTEER': 'Bénévolat bibliothèque',\n      'ASSOCIATION_WORK': 'Travail associatif',\n      'COMMUNITY_SERVICE': 'Service communautaire',\n      'ENVIRONMENTAL_ACTION': 'Action environnementale',\n      'CULTURAL_EVENT': 'Événement culturel',\n      'SPORTS_COACHING': 'Coaching sportif',\n      'TUTORING': 'Tutorat',\n      'ELDERLY_CARE': 'Aide aux personnes âgées',\n      'OTHER': 'Autre'\n    };\n    return actionNames[actionType] || actionType;\n  }\n\n  getStatusColor(status: ValidationStatus): string {\n    const colors = {\n      [ValidationStatus.PENDING]: '#FF9800',\n      [ValidationStatus.APPROVED]: '#4CAF50',\n      [ValidationStatus.REJECTED]: '#F44336'\n    };\n    return colors[status];\n  }\n\n  getStatusDisplayName(status: ValidationStatus): string {\n    const statusNames = {\n      [ValidationStatus.PENDING]: 'En attente',\n      [ValidationStatus.APPROVED]: 'Approuvée',\n      [ValidationStatus.REJECTED]: 'Rejetée'\n    };\n    return statusNames[status];\n  }\n\n  validateAction(validation: ValidationAction, approved: boolean): void {\n    validation.status = approved ? ValidationStatus.APPROVED : ValidationStatus.REJECTED;\n    validation.validatedAt = new Date();\n    validation.validatedBy = this.user!.uid;\n    validation.validatorName = this.user!.name;\n\n    // Remove from pending list\n    this.pendingValidations = this.pendingValidations.filter(v => v.id !== validation.id);\n    \n    // Add to recent validations\n    this.recentValidations.unshift(validation);\n    \n    // Update stats\n    this.validatorStats!.validationsToday++;\n    this.validatorStats!.totalValidations++;\n    this.quickStats[0].value = this.pendingValidations.length.toString();\n    this.quickStats[1].value = this.validatorStats!.validationsToday.toString();\n    this.quickStats[2].value = this.validatorStats!.totalValidations.toString();\n\n    console.log(`Validation ${approved ? 'approved' : 'rejected'}:`, validation);\n  }\n\n  viewValidationDetails(validation: ValidationAction): void {\n    console.log('View validation details:', validation);\n    // TODO: Open validation details modal\n  }\n\n  navigateTo(route: string): void {\n    this.router.navigate([route]);\n  }\n}\n", "<div class=\"validator-dashboard-container\" *ngIf=\"user\">\n  <!-- Header Section -->\n  <div class=\"validator-header\">\n    <div class=\"header-content\">\n      <div class=\"validator-welcome\">\n        <h1>{{ getGreeting() }}, {{ user.name }}</h1>\n        <p class=\"validator-subtitle\">🧑‍🏫 Tableau de bord validateur - Validez les actions communautaires</p>\n      </div>\n      <div class=\"validator-badge\">\n        <mat-icon>verified_user</mat-icon>\n        <span>Validateur</span>\n      </div>\n    </div>\n  </div>\n\n  <!-- Quick Stats -->\n  <div class=\"quick-stats-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>analytics</mat-icon>\n      Statistiques de Validation\n    </h2>\n    <div class=\"stats-grid\">\n      <mat-card *ngFor=\"let stat of quickStats\" class=\"stat-card\" [style.border-left-color]=\"stat.color\">\n        <mat-card-content>\n          <div class=\"stat-header\">\n            <div class=\"stat-icon\" [style.background-color]=\"stat.color + '20'\">\n              <mat-icon [style.color]=\"stat.color\">{{ stat.icon }}</mat-icon>\n            </div>\n          </div>\n          <div class=\"stat-content\">\n            <h3 class=\"stat-value\">{{ stat.value }}</h3>\n            <p class=\"stat-title\">{{ stat.title }}</p>\n            <span class=\"stat-description\">{{ stat.description }}</span>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Pending Validations -->\n  <div class=\"pending-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>pending_actions</mat-icon>\n      Actions à Valider\n      <span class=\"pending-count\" *ngIf=\"pendingValidations.length > 0\">{{ pendingValidations.length }}</span>\n    </h2>\n    \n    <div *ngIf=\"pendingValidations.length === 0\" class=\"empty-state\">\n      <mat-card class=\"empty-card\">\n        <mat-card-content>\n          <mat-icon class=\"empty-icon\">check_circle</mat-icon>\n          <h3>Aucune validation en attente</h3>\n          <p>Toutes les actions ont été traitées. Excellent travail !</p>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <div class=\"validations-grid\" *ngIf=\"pendingValidations.length > 0\">\n      <mat-card *ngFor=\"let validation of pendingValidations\" class=\"validation-card\">\n        <mat-card-header>\n          <div class=\"validation-header\">\n            <div class=\"user-info\">\n              <h4>{{ validation.userName }}</h4>\n              <span class=\"user-email\">{{ validation.userEmail }}</span>\n            </div>\n            <div class=\"action-type\">\n              <span class=\"type-badge\">{{ getActionTypeDisplayName(validation.actionType) }}</span>\n            </div>\n          </div>\n        </mat-card-header>\n        \n        <mat-card-content>\n          <div class=\"validation-details\">\n            <p class=\"description\">{{ validation.description }}</p>\n            <div class=\"validation-meta\">\n              <div class=\"meta-item\" *ngIf=\"validation.location\">\n                <mat-icon>location_on</mat-icon>\n                <span>{{ validation.location }}</span>\n              </div>\n              <div class=\"meta-item\">\n                <mat-icon>schedule</mat-icon>\n                <span>{{ validation.submittedAt | timeAgo }}</span>\n              </div>\n              <div class=\"meta-item points\">\n                <mat-icon>stars</mat-icon>\n                <span>{{ validation.points }} points</span>\n              </div>\n            </div>\n            \n            <div class=\"evidence-section\" *ngIf=\"validation.evidence && validation.evidence.length > 0\">\n              <h5>Preuves fournies:</h5>\n              <div class=\"evidence-list\">\n                <button *ngFor=\"let evidence of validation.evidence\" \n                        mat-stroked-button \n                        class=\"evidence-btn\"\n                        (click)=\"viewEvidence(evidence)\">\n                  <mat-icon>photo</mat-icon>\n                  Voir la preuve\n                </button>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n        \n        <mat-card-actions class=\"validation-actions\">\n          <button mat-raised-button \n                  color=\"primary\" \n                  (click)=\"validateAction(validation, true)\"\n                  class=\"approve-btn\">\n            <mat-icon>check</mat-icon>\n            Approuver (+{{ validation.points }} pts)\n          </button>\n          <button mat-raised-button \n                  color=\"warn\" \n                  (click)=\"validateAction(validation, false)\"\n                  class=\"reject-btn\">\n            <mat-icon>close</mat-icon>\n            Rejeter\n          </button>\n          <button mat-button (click)=\"viewValidationDetails(validation)\">\n            <mat-icon>info</mat-icon>\n            Détails\n          </button>\n        </mat-card-actions>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Recent Validations -->\n  <div class=\"recent-section\" *ngIf=\"recentValidations.length > 0\">\n    <h2 class=\"section-title\">\n      <mat-icon>history</mat-icon>\n      Validations Récentes\n    </h2>\n    <mat-card class=\"recent-card\">\n      <mat-card-content>\n        <div class=\"recent-list\">\n          <div *ngFor=\"let validation of recentValidations.slice(0, 5)\" class=\"recent-item\">\n            <div class=\"recent-icon\" [style.background-color]=\"getStatusColor(validation.status) + '20'\">\n              <mat-icon [style.color]=\"getStatusColor(validation.status)\">\n                {{ validation.status === 'approved' ? 'check_circle' : 'cancel' }}\n              </mat-icon>\n            </div>\n            <div class=\"recent-content\">\n              <p class=\"recent-description\">\n                <strong>{{ validation.userName }}</strong> - {{ validation.description }}\n              </p>\n              <div class=\"recent-meta\">\n                <span class=\"recent-time\">{{ validation.validatedAt | timeAgo }}</span>\n                <span class=\"recent-status\" [style.color]=\"getStatusColor(validation.status)\">\n                  {{ getStatusDisplayName(validation.status) }}\n                </span>\n                <span class=\"recent-points\">{{ validation.points }} pts</span>\n              </div>\n            </div>\n          </div>\n        </div>\n        <button mat-stroked-button class=\"view-all-btn\" (click)=\"navigateTo('/validator/history')\">\n          Voir tout l'historique\n          <mat-icon>arrow_forward</mat-icon>\n        </button>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AAGA,SAAeA,QAAQ,EAAEC,gBAAgB,QAAwB,yBAAyB;;;;;;;;;;;ICuB5EC,EAJR,CAAAC,cAAA,mBAAmG,uBAC/E,cACS,cAC6C,eAC7B;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAExDF,EAFwD,CAAAG,YAAA,EAAW,EAC3D,EACF;IAEJH,EADF,CAAAC,cAAA,cAA0B,aACD;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC5CH,EAAA,CAAAC,cAAA,YAAsB;IAAAD,EAAA,CAAAE,MAAA,IAAgB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAC1CH,EAAA,CAAAC,cAAA,gBAA+B;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAG3DF,EAH2D,CAAAG,YAAA,EAAO,EACxD,EACW,EACV;;;;IAbiDH,EAAA,CAAAI,WAAA,sBAAAC,OAAA,CAAAC,KAAA,CAAsC;IAGrEN,EAAA,CAAAO,SAAA,GAA4C;IAA5CP,EAAA,CAAAI,WAAA,qBAAAC,OAAA,CAAAC,KAAA,QAA4C;IACvDN,EAAA,CAAAO,SAAA,EAA0B;IAA1BP,EAAA,CAAAI,WAAA,UAAAC,OAAA,CAAAC,KAAA,CAA0B;IAACN,EAAA,CAAAO,SAAA,EAAe;IAAfP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAI,IAAA,CAAe;IAI/BT,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAK,KAAA,CAAgB;IACjBV,EAAA,CAAAO,SAAA,GAAgB;IAAhBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAM,KAAA,CAAgB;IACPX,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAAQ,iBAAA,CAAAH,OAAA,CAAAO,WAAA,CAAsB;;;;;IAY3DZ,EAAA,CAAAC,cAAA,eAAkE;IAAAD,EAAA,CAAAE,MAAA,GAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IAAtCH,EAAA,CAAAO,SAAA,EAA+B;IAA/BP,EAAA,CAAAQ,iBAAA,CAAAK,MAAA,CAAAC,kBAAA,CAAAC,MAAA,CAA+B;;;;;IAM7Ff,EAHN,CAAAC,cAAA,cAAiE,mBAClC,uBACT,mBACa;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,mCAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,8EAAwD;IAGjEF,EAHiE,CAAAG,YAAA,EAAI,EAC9C,EACV,EACP;;;;;IAqBMH,EADF,CAAAC,cAAA,cAAmD,eACvC;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IACjCF,EADiC,CAAAG,YAAA,EAAO,EAClC;;;;IADEH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,iBAAA,CAAAQ,aAAA,CAAAC,QAAA,CAAyB;;;;;;IAe/BjB,EAAA,CAAAC,cAAA,iBAGyC;IAAjCD,EAAA,CAAAkB,UAAA,mBAAAC,qGAAA;MAAA,MAAAC,WAAA,GAAApB,EAAA,CAAAqB,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASZ,MAAA,CAAAa,YAAA,CAAAN,WAAA,CAAsB;IAAA,EAAC;IACtCpB,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,YAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,uBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;;;;;IARXH,EADF,CAAAC,cAAA,cAA4F,SACtF;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC1BH,EAAA,CAAAC,cAAA,cAA2B;IACzBD,EAAA,CAAA2B,UAAA,IAAAC,4EAAA,qBAGyC;IAK7C5B,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAR2BH,EAAA,CAAAO,SAAA,GAAsB;IAAtBP,EAAA,CAAA6B,UAAA,YAAAb,aAAA,CAAAc,QAAA,CAAsB;;;;;;IA9BrD9B,EAJR,CAAAC,cAAA,mBAAgF,sBAC7D,cACgB,cACN,SACjB;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,eAAyB;IAAAD,EAAA,CAAAE,MAAA,GAA0B;IACrDF,EADqD,CAAAG,YAAA,EAAO,EACtD;IAEJH,EADF,CAAAC,cAAA,cAAyB,eACE;IAAAD,EAAA,CAAAE,MAAA,IAAqD;IAGpFF,EAHoF,CAAAG,YAAA,EAAO,EACjF,EACF,EACU;IAIdH,EAFJ,CAAAC,cAAA,wBAAkB,eACgB,aACP;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvDH,EAAA,CAAAC,cAAA,eAA6B;IAC3BD,EAAA,CAAA2B,UAAA,KAAAI,mEAAA,kBAAmD;IAKjD/B,EADF,CAAAC,cAAA,eAAuB,gBACX;IAAAD,EAAA,CAAAE,MAAA,gBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAsC;;IAC9CF,EAD8C,CAAAG,YAAA,EAAO,EAC/C;IAEJH,EADF,CAAAC,cAAA,eAA8B,gBAClB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAA8B;IAExCF,EAFwC,CAAAG,YAAA,EAAO,EACvC,EACF;IAENH,EAAA,CAAA2B,UAAA,KAAAK,mEAAA,kBAA4F;IAahGhC,EADE,CAAAG,YAAA,EAAM,EACW;IAGjBH,EADF,CAAAC,cAAA,4BAA6C,kBAIf;IADpBD,EAAA,CAAAkB,UAAA,mBAAAe,sFAAA;MAAA,MAAAjB,aAAA,GAAAhB,EAAA,CAAAqB,aAAA,CAAAa,GAAA,EAAAX,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASZ,MAAA,CAAAsB,cAAA,CAAAnB,aAAA,EAA2B,IAAI,CAAC;IAAA,EAAC;IAEhDhB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAG2B;IADnBD,EAAA,CAAAkB,UAAA,mBAAAkB,sFAAA;MAAA,MAAApB,aAAA,GAAAhB,EAAA,CAAAqB,aAAA,CAAAa,GAAA,EAAAX,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASZ,MAAA,CAAAsB,cAAA,CAAAnB,aAAA,EAA2B,KAAK,CAAC;IAAA,EAAC;IAEjDhB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,iBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IACTH,EAAA,CAAAC,cAAA,kBAA+D;IAA5CD,EAAA,CAAAkB,UAAA,mBAAAmB,sFAAA;MAAA,MAAArB,aAAA,GAAAhB,EAAA,CAAAqB,aAAA,CAAAa,GAAA,EAAAX,SAAA;MAAA,MAAAV,MAAA,GAAAb,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASZ,MAAA,CAAAyB,qBAAA,CAAAtB,aAAA,CAAiC;IAAA,EAAC;IAC5DhB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,YAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAE,MAAA,sBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;;;;;IA9DCH,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,iBAAA,CAAAQ,aAAA,CAAAuB,QAAA,CAAyB;IACJvC,EAAA,CAAAO,SAAA,GAA0B;IAA1BP,EAAA,CAAAQ,iBAAA,CAAAQ,aAAA,CAAAwB,SAAA,CAA0B;IAG1BxC,EAAA,CAAAO,SAAA,GAAqD;IAArDP,EAAA,CAAAQ,iBAAA,CAAAK,MAAA,CAAA4B,wBAAA,CAAAzB,aAAA,CAAA0B,UAAA,EAAqD;IAOzD1C,EAAA,CAAAO,SAAA,GAA4B;IAA5BP,EAAA,CAAAQ,iBAAA,CAAAQ,aAAA,CAAAJ,WAAA,CAA4B;IAEzBZ,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAA6B,UAAA,SAAAb,aAAA,CAAAC,QAAA,CAAyB;IAMzCjB,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAA2C,WAAA,QAAA3B,aAAA,CAAA4B,WAAA,EAAsC;IAItC5C,EAAA,CAAAO,SAAA,GAA8B;IAA9BP,EAAA,CAAA6C,kBAAA,KAAA7B,aAAA,CAAA8B,MAAA,YAA8B;IAIT9C,EAAA,CAAAO,SAAA,EAA2D;IAA3DP,EAAA,CAAA6B,UAAA,SAAAb,aAAA,CAAAc,QAAA,IAAAd,aAAA,CAAAc,QAAA,CAAAf,MAAA,KAA2D;IAqB1Ff,EAAA,CAAAO,SAAA,GACF;IADEP,EAAA,CAAA6C,kBAAA,kBAAA7B,aAAA,CAAA8B,MAAA,WACF;;;;;IAtDN9C,EAAA,CAAAC,cAAA,cAAoE;IAClED,EAAA,CAAA2B,UAAA,IAAAoB,4DAAA,yBAAgF;IAmElF/C,EAAA,CAAAG,YAAA,EAAM;;;;IAnE6BH,EAAA,CAAAO,SAAA,EAAqB;IAArBP,EAAA,CAAA6B,UAAA,YAAAhB,MAAA,CAAAC,kBAAA,CAAqB;;;;;IAiF9Cd,EAFJ,CAAAC,cAAA,cAAkF,cACa,eAC/B;IAC1DD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAGFH,EAFJ,CAAAC,cAAA,cAA4B,YACI,aACpB;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAACH,EAAA,CAAAE,MAAA,GAC7C;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAEFH,EADF,CAAAC,cAAA,cAAyB,gBACG;IAAAD,EAAA,CAAAE,MAAA,IAAsC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvEH,EAAA,CAAAC,cAAA,gBAA8E;IAC5ED,EAAA,CAAAE,MAAA,IACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACPH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAA2B;IAG7DF,EAH6D,CAAAG,YAAA,EAAO,EAC1D,EACF,EACF;;;;;IAjBqBH,EAAA,CAAAO,SAAA,EAAmE;IAAnEP,EAAA,CAAAI,WAAA,qBAAAS,MAAA,CAAAmC,cAAA,CAAAC,aAAA,CAAAC,MAAA,SAAmE;IAChFlD,EAAA,CAAAO,SAAA,EAAiD;IAAjDP,EAAA,CAAAI,WAAA,UAAAS,MAAA,CAAAmC,cAAA,CAAAC,aAAA,CAAAC,MAAA,EAAiD;IACzDlD,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAA6C,kBAAA,MAAAI,aAAA,CAAAC,MAAA,iDACF;IAIUlD,EAAA,CAAAO,SAAA,GAAyB;IAAzBP,EAAA,CAAAQ,iBAAA,CAAAyC,aAAA,CAAAV,QAAA,CAAyB;IAAUvC,EAAA,CAAAO,SAAA,EAC7C;IAD6CP,EAAA,CAAA6C,kBAAA,QAAAI,aAAA,CAAArC,WAAA,MAC7C;IAE4BZ,EAAA,CAAAO,SAAA,GAAsC;IAAtCP,EAAA,CAAAQ,iBAAA,CAAAR,EAAA,CAAA2C,WAAA,SAAAM,aAAA,CAAAE,WAAA,EAAsC;IACpCnD,EAAA,CAAAO,SAAA,GAAiD;IAAjDP,EAAA,CAAAI,WAAA,UAAAS,MAAA,CAAAmC,cAAA,CAAAC,aAAA,CAAAC,MAAA,EAAiD;IAC3ElD,EAAA,CAAAO,SAAA,EACF;IADEP,EAAA,CAAA6C,kBAAA,MAAAhC,MAAA,CAAAuC,oBAAA,CAAAH,aAAA,CAAAC,MAAA,OACF;IAC4BlD,EAAA,CAAAO,SAAA,GAA2B;IAA3BP,EAAA,CAAA6C,kBAAA,KAAAI,aAAA,CAAAH,MAAA,SAA2B;;;;;;IArBjE9C,EAFJ,CAAAC,cAAA,cAAiE,YACrC,eACd;IAAAD,EAAA,CAAAE,MAAA,cAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,kCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAGDH,EAFJ,CAAAC,cAAA,mBAA8B,uBACV,cACS;IACvBD,EAAA,CAAA2B,UAAA,IAAA0B,uDAAA,oBAAkF;IAmBpFrD,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,iBAA2F;IAA3CD,EAAA,CAAAkB,UAAA,mBAAAoC,0EAAA;MAAAtD,EAAA,CAAAqB,aAAA,CAAAkC,GAAA;MAAA,MAAA1C,MAAA,GAAAb,EAAA,CAAAwB,aAAA;MAAA,OAAAxB,EAAA,CAAAyB,WAAA,CAASZ,MAAA,CAAA2C,UAAA,CAAW,oBAAoB,CAAC;IAAA,EAAC;IACxFxD,EAAA,CAAAE,MAAA,gCACA;IAAAF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAI/BF,EAJ+B,CAAAG,YAAA,EAAW,EAC3B,EACQ,EACV,EACP;;;;IA1B8BH,EAAA,CAAAO,SAAA,GAAgC;IAAhCP,EAAA,CAAA6B,UAAA,YAAAhB,MAAA,CAAA4C,iBAAA,CAAAC,KAAA,OAAgC;;;;;IApI9D1D,EALR,CAAAC,cAAA,aAAwD,aAExB,aACA,aACK,SACzB;IAAAD,EAAA,CAAAE,MAAA,GAAoC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC7CH,EAAA,CAAAC,cAAA,WAA8B;IAAAD,EAAA,CAAAE,MAAA,qGAAqE;IACrGF,EADqG,CAAAG,YAAA,EAAI,EACnG;IAEJH,EADF,CAAAC,cAAA,aAA6B,eACjB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAGtBF,EAHsB,CAAAG,YAAA,EAAO,EACnB,EACF,EACF;IAKFH,EAFJ,CAAAC,cAAA,cAAiC,aACL,gBACd;IAAAD,EAAA,CAAAE,MAAA,iBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,cAAwB;IACtBD,EAAA,CAAA2B,UAAA,KAAAgC,sDAAA,yBAAmG;IAevG3D,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,eAA6B,aACD,gBACd;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAE,MAAA,gCACA;IAAAF,EAAA,CAAA2B,UAAA,KAAAiC,kDAAA,mBAAkE;IACpE5D,EAAA,CAAAG,YAAA,EAAK;IAYLH,EAVA,CAAA2B,UAAA,KAAAkC,iDAAA,kBAAiE,KAAAC,iDAAA,kBAUG;IAqEtE9D,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAA2B,UAAA,KAAAoC,iDAAA,mBAAiE;IAmCnE/D,EAAA,CAAAG,YAAA,EAAM;;;;IA/JMH,EAAA,CAAAO,SAAA,GAAoC;IAApCP,EAAA,CAAAgE,kBAAA,KAAAnD,MAAA,CAAAoD,WAAA,UAAApD,MAAA,CAAAqD,IAAA,CAAAC,IAAA,KAAoC;IAiBfnE,EAAA,CAAAO,SAAA,IAAa;IAAbP,EAAA,CAAA6B,UAAA,YAAAhB,MAAA,CAAAuD,UAAA,CAAa;IAsBXpE,EAAA,CAAAO,SAAA,GAAmC;IAAnCP,EAAA,CAAA6B,UAAA,SAAAhB,MAAA,CAAAC,kBAAA,CAAAC,MAAA,KAAmC;IAG5Df,EAAA,CAAAO,SAAA,EAAqC;IAArCP,EAAA,CAAA6B,UAAA,SAAAhB,MAAA,CAAAC,kBAAA,CAAAC,MAAA,OAAqC;IAUZf,EAAA,CAAAO,SAAA,EAAmC;IAAnCP,EAAA,CAAA6B,UAAA,SAAAhB,MAAA,CAAAC,kBAAA,CAAAC,MAAA,KAAmC;IAwEvCf,EAAA,CAAAO,SAAA,EAAkC;IAAlCP,EAAA,CAAA6B,UAAA,SAAAhB,MAAA,CAAA4C,iBAAA,CAAA1C,MAAA,KAAkC;;;ADpGjE,OAAM,MAAOsD,2BAA2B;EAsCtCC,YACUC,WAAwB,EACxBC,MAAc;IADd,KAAAD,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IAvChB,KAAAN,IAAI,GAAgB,IAAI;IACxB,KAAAO,cAAc,GAA0B,IAAI;IAC5C,KAAA3D,kBAAkB,GAAwB,EAAE;IAC5C,KAAA2C,iBAAiB,GAAwB,EAAE;IAE3C;IACA,KAAAW,UAAU,GAAG,CACX;MACEzD,KAAK,EAAE,wBAAwB;MAC/BD,KAAK,EAAE,GAAG;MACVD,IAAI,EAAE,iBAAiB;MACvBH,KAAK,EAAE,SAAS;MAChBM,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,0BAA0B;MACjCD,KAAK,EAAE,GAAG;MACVD,IAAI,EAAE,OAAO;MACbH,KAAK,EAAE,SAAS;MAChBM,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,mBAAmB;MAC1BD,KAAK,EAAE,GAAG;MACVD,IAAI,EAAE,UAAU;MAChBH,KAAK,EAAE,SAAS;MAChBM,WAAW,EAAE;KACd,EACD;MACED,KAAK,EAAE,qBAAqB;MAC5BD,KAAK,EAAE,IAAI;MACXD,IAAI,EAAE,UAAU;MAChBH,KAAK,EAAE,SAAS;MAChBM,WAAW,EAAE;KACd,CACF;EAKE;EAEH8D,QAAQA,CAAA;IACN,IAAI,CAACH,WAAW,CAACI,YAAY,CAACC,SAAS,CAACV,IAAI,IAAG;MAC7C,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAIA,IAAI,IAAIA,IAAI,CAACW,IAAI,KAAK/E,QAAQ,CAACgF,SAAS,EAAE;QAC5C,IAAI,CAACC,iBAAiB,EAAE;OACzB,MAAM;QACL,IAAI,CAACP,MAAM,CAACQ,QAAQ,CAAC,CAAC,YAAY,CAAC,CAAC;;IAExC,CAAC,CAAC;EACJ;EAEQD,iBAAiBA,CAAA;IACvB,IAAI,CAACE,kBAAkB,EAAE;IACzB,IAAI,CAACC,sBAAsB,EAAE;IAC7B,IAAI,CAACC,qBAAqB,EAAE;EAC9B;EAEQF,kBAAkBA,CAAA;IACxB;IACA,IAAI,CAACR,cAAc,GAAG;MACpBW,WAAW,EAAE,IAAI,CAAClB,IAAK,CAACmB,GAAG;MAC3BC,gBAAgB,EAAE,GAAG;MACrBC,gBAAgB,EAAE,CAAC;MACnBC,qBAAqB,EAAE,GAAG;MAC1BC,YAAY,EAAE,EAAE;MAChBC,gBAAgB,EAAE,IAAIC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE;KACvD;IAED;IACA,IAAI,CAACxB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAACI,kBAAkB,CAACC,MAAM,CAAC8E,QAAQ,EAAE;IACpE,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAAC+D,cAAc,CAACc,gBAAgB,CAACM,QAAQ,EAAE;IAC1E,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAAC+D,cAAc,CAACa,gBAAgB,CAACO,QAAQ,EAAE;IAC1E,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAAC+D,cAAc,CAACgB,YAAY,GAAG,GAAG;EACnE;EAEQP,sBAAsBA,CAAA;IAC5B;IACA,IAAI,CAACpE,kBAAkB,GAAG,CACxB;MACEgF,EAAE,EAAE,GAAG;MACPC,MAAM,EAAE,OAAO;MACfxD,QAAQ,EAAE,eAAe;MACzBC,SAAS,EAAE,iBAAiB;MAC5BE,UAAU,EAAE,mBAAmB;MAC/B9B,WAAW,EAAE,6DAA6D;MAC1EK,QAAQ,EAAE,kCAAkC;MAC5C2B,WAAW,EAAE,IAAI+C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAClD9C,MAAM,EAAE,EAAE;MACVI,MAAM,EAAEnD,gBAAgB,CAACiG,OAAO;MAChClE,QAAQ,EAAE,CAAC,gCAAgC;KAC5C,EACD;MACEgE,EAAE,EAAE,GAAG;MACPC,MAAM,EAAE,OAAO;MACfxD,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,iBAAiB;MAC5BE,UAAU,EAAE,aAAoB;MAChC9B,WAAW,EAAE,8CAA8C;MAC3DK,QAAQ,EAAE,uBAAuB;MACjC2B,WAAW,EAAE,IAAI+C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAClD9C,MAAM,EAAE,EAAE;MACVI,MAAM,EAAEnD,gBAAgB,CAACiG,OAAO;MAChClE,QAAQ,EAAE,CAAC,gCAAgC,EAAE,gCAAgC;KAC9E,EACD;MACEgE,EAAE,EAAE,GAAG;MACPC,MAAM,EAAE,OAAO;MACfxD,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,mBAAmB;MAC9BE,UAAU,EAAE,mBAA0B;MACtC9B,WAAW,EAAE,6BAA6B;MAC1CK,QAAQ,EAAE,uBAAuB;MACjC2B,WAAW,EAAE,IAAI+C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAClD9C,MAAM,EAAE,EAAE;MACVI,MAAM,EAAEnD,gBAAgB,CAACiG;KAC1B,CACF;IAED,IAAI,CAAC5B,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAACI,kBAAkB,CAACC,MAAM,CAAC8E,QAAQ,EAAE;EACtE;EAEQV,qBAAqBA,CAAA;IAC3B;IACA,IAAI,CAAC1B,iBAAiB,GAAG,CACvB;MACEqC,EAAE,EAAE,GAAG;MACPC,MAAM,EAAE,OAAO;MACfxD,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,iBAAiB;MAC5BE,UAAU,EAAE,UAAiB;MAC7B9B,WAAW,EAAE,qCAAqC;MAClDgC,WAAW,EAAE,IAAI+C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACtDzC,WAAW,EAAE,IAAIwC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAClDK,WAAW,EAAE,IAAI,CAAC/B,IAAK,CAACmB,GAAG;MAC3Ba,aAAa,EAAE,IAAI,CAAChC,IAAK,CAACC,IAAI;MAC9BrB,MAAM,EAAE,EAAE;MACVI,MAAM,EAAEnD,gBAAgB,CAACoG;KAC1B,EACD;MACEL,EAAE,EAAE,GAAG;MACPC,MAAM,EAAE,OAAO;MACfxD,QAAQ,EAAE,gBAAgB;MAC1BC,SAAS,EAAE,iBAAiB;MAC5BE,UAAU,EAAE,sBAA6B;MACzC9B,WAAW,EAAE,uCAAuC;MACpDgC,WAAW,EAAE,IAAI+C,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACtDzC,WAAW,EAAE,IAAIwC,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACtDK,WAAW,EAAE,IAAI,CAAC/B,IAAK,CAACmB,GAAG;MAC3Ba,aAAa,EAAE,IAAI,CAAChC,IAAK,CAACC,IAAI;MAC9BrB,MAAM,EAAE,EAAE;MACVI,MAAM,EAAEnD,gBAAgB,CAACoG;KAC1B,CACF;EACH;EAEAlC,WAAWA,CAAA;IACT,MAAMmC,IAAI,GAAG,IAAIT,IAAI,EAAE,CAACU,QAAQ,EAAE;IAClC,IAAID,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,SAAS;EAClB;EAEA3D,wBAAwBA,CAACC,UAAkB;IACzC,MAAM4D,WAAW,GAA8B;MAC7C,aAAa,EAAE,eAAe;MAC9B,mBAAmB,EAAE,wBAAwB;MAC7C,kBAAkB,EAAE,oBAAoB;MACxC,mBAAmB,EAAE,uBAAuB;MAC5C,sBAAsB,EAAE,yBAAyB;MACjD,gBAAgB,EAAE,oBAAoB;MACtC,iBAAiB,EAAE,kBAAkB;MACrC,UAAU,EAAE,SAAS;MACrB,cAAc,EAAE,0BAA0B;MAC1C,OAAO,EAAE;KACV;IACD,OAAOA,WAAW,CAAC5D,UAAU,CAAC,IAAIA,UAAU;EAC9C;EAEAM,cAAcA,CAACE,MAAwB;IACrC,MAAMqD,MAAM,GAAG;MACb,CAACxG,gBAAgB,CAACiG,OAAO,GAAG,SAAS;MACrC,CAACjG,gBAAgB,CAACoG,QAAQ,GAAG,SAAS;MACtC,CAACpG,gBAAgB,CAACyG,QAAQ,GAAG;KAC9B;IACD,OAAOD,MAAM,CAACrD,MAAM,CAAC;EACvB;EAEAE,oBAAoBA,CAACF,MAAwB;IAC3C,MAAMuD,WAAW,GAAG;MAClB,CAAC1G,gBAAgB,CAACiG,OAAO,GAAG,YAAY;MACxC,CAACjG,gBAAgB,CAACoG,QAAQ,GAAG,WAAW;MACxC,CAACpG,gBAAgB,CAACyG,QAAQ,GAAG;KAC9B;IACD,OAAOC,WAAW,CAACvD,MAAM,CAAC;EAC5B;EAEAf,cAAcA,CAACuE,UAA4B,EAAEC,QAAiB;IAC5DD,UAAU,CAACxD,MAAM,GAAGyD,QAAQ,GAAG5G,gBAAgB,CAACoG,QAAQ,GAAGpG,gBAAgB,CAACyG,QAAQ;IACpFE,UAAU,CAACvD,WAAW,GAAG,IAAIwC,IAAI,EAAE;IACnCe,UAAU,CAACT,WAAW,GAAG,IAAI,CAAC/B,IAAK,CAACmB,GAAG;IACvCqB,UAAU,CAACR,aAAa,GAAG,IAAI,CAAChC,IAAK,CAACC,IAAI;IAE1C;IACA,IAAI,CAACrD,kBAAkB,GAAG,IAAI,CAACA,kBAAkB,CAAC8F,MAAM,CAACC,CAAC,IAAIA,CAAC,CAACf,EAAE,KAAKY,UAAU,CAACZ,EAAE,CAAC;IAErF;IACA,IAAI,CAACrC,iBAAiB,CAACqD,OAAO,CAACJ,UAAU,CAAC;IAE1C;IACA,IAAI,CAACjC,cAAe,CAACc,gBAAgB,EAAE;IACvC,IAAI,CAACd,cAAe,CAACa,gBAAgB,EAAE;IACvC,IAAI,CAAClB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAACI,kBAAkB,CAACC,MAAM,CAAC8E,QAAQ,EAAE;IACpE,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAAC+D,cAAe,CAACc,gBAAgB,CAACM,QAAQ,EAAE;IAC3E,IAAI,CAACzB,UAAU,CAAC,CAAC,CAAC,CAAC1D,KAAK,GAAG,IAAI,CAAC+D,cAAe,CAACa,gBAAgB,CAACO,QAAQ,EAAE;IAE3EkB,OAAO,CAACC,GAAG,CAAC,cAAcL,QAAQ,GAAG,UAAU,GAAG,UAAU,GAAG,EAAED,UAAU,CAAC;EAC9E;EAEApE,qBAAqBA,CAACoE,UAA4B;IAChDK,OAAO,CAACC,GAAG,CAAC,0BAA0B,EAAEN,UAAU,CAAC;IACnD;EACF;EAEAlD,UAAUA,CAACyD,KAAa;IACtB,IAAI,CAACzC,MAAM,CAACQ,QAAQ,CAAC,CAACiC,KAAK,CAAC,CAAC;EAC/B;;;uBApOW5C,2BAA2B,EAAArE,EAAA,CAAAkH,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApH,EAAA,CAAAkH,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAA3BjD,2BAA2B;MAAAkD,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC7BxC7H,EAAA,CAAA2B,UAAA,IAAAoG,0CAAA,kBAAwD;;;UAAZ/H,EAAA,CAAA6B,UAAA,SAAAiG,GAAA,CAAA5D,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}