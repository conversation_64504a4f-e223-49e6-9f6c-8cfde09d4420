{"ast": null, "code": "// import { RewardsListComponent } from './components/rewards-list/rewards-list.component';\n// import { RewardDetailComponent } from './components/reward-detail/reward-detail.component';\nexport const rewardsRoutes = [\n  // {\n  //   path: '',\n  //   component: RewardsListComponent\n  // },\n  // {\n  //   path: ':id',\n  //   component: RewardDetailComponent\n  // }\n];", "map": {"version": 3, "names": ["rewardsRoutes"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\rewards\\rewards.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\n// import { RewardsListComponent } from './components/rewards-list/rewards-list.component';\n// import { RewardDetailComponent } from './components/reward-detail/reward-detail.component';\n\nexport const rewardsRoutes: Routes = [\n  // {\n  //   path: '',\n  //   component: RewardsListComponent\n  // },\n  // {\n  //   path: ':id',\n  //   component: RewardDetailComponent\n  // }\n];\n"], "mappings": "AACA;AACA;AAEA,OAAO,MAAMA,aAAa,GAAW;EACnC;EACA;EACA;EACA;EACA;EACA;EACA;EACA;AAAA,CACD", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}