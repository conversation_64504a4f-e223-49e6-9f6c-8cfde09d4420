import { NgModule } from '@angular/core';
import { RouterModule } from '@angular/router';

import { SharedModule } from '../../shared/shared.module';
import { ValidatorDashboardComponent } from './components/validator-dashboard/validator-dashboard.component';
import { ValidationListComponent } from './components/validation-list/validation-list.component';
import { ValidationHistoryComponent } from './components/validation-history/validation-history.component';
import { validatorDashboardRoutes } from './validator-dashboard.routes';

@NgModule({
  declarations: [
    ValidatorDashboardComponent,
    ValidationListComponent,
    ValidationHistoryComponent
  ],
  imports: [
    SharedModule,
    RouterModule.forChild(validatorDashboardRoutes)
  ]
})
export class ValidatorDashboardModule { }
