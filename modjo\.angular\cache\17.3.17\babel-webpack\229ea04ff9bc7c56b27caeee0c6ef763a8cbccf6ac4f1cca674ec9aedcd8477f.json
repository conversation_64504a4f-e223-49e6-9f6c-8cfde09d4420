{"ast": null, "code": "import { UserRole } from '../../../../core/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"../../../../core/services/dashboard-router.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/card\";\nimport * as i7 from \"@angular/material/icon\";\nimport * as i8 from \"@angular/material/form-field\";\nimport * as i9 from \"@angular/material/menu\";\nimport * as i10 from \"@angular/material/select\";\nimport * as i11 from \"@angular/material/core\";\nimport * as i12 from \"@angular/material/badge\";\nfunction DashboardComponent_div_0_mat_card_89_div_3_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 93)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"check_circle\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_0_mat_card_89_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r3 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 85)(1, \"div\", 86);\n    i0.ɵɵelement(2, \"img\", 87);\n    i0.ɵɵtemplate(3, DashboardComponent_div_0_mat_card_89_div_3_Template, 3, 0, \"div\", 88);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"mat-card-content\")(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"p\", 89);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 90)(10, \"mat-icon\");\n    i0.ɵɵtext(11, \"store\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\");\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 91)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"button\", 92);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_mat_card_89_Template_button_click_19_listener() {\n      const reward_r4 = i0.ɵɵrestoreView(_r3).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.exchangeReward(reward_r4));\n    });\n    i0.ɵɵtext(20);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const reward_r4 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵclassProp(\"affordable\", ctx_r1.user.points >= reward_r4.pointsRequired);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", reward_r4.imageUrl, i0.ɵɵsanitizeUrl)(\"alt\", reward_r4.title);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.user.points >= reward_r4.pointsRequired);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(reward_r4.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(reward_r4.description);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(reward_r4.partnerName);\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate1(\"\", reward_r4.pointsRequired, \" points\");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"color\", ctx_r1.user.points >= reward_r4.pointsRequired ? \"primary\" : \"\")(\"disabled\", ctx_r1.user.points < reward_r4.pointsRequired);\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.user.points >= reward_r4.pointsRequired ? \"\\u00C9changer\" : \"Pas assez de points\", \" \");\n  }\n}\nfunction DashboardComponent_div_0_div_112_div_1_span_11_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"span\", 105)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"location_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const action_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\" \", action_r5.location, \" \");\n  }\n}\nfunction DashboardComponent_div_0_div_112_div_1_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 106)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"business\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const action_r5 = i0.ɵɵnextContext().$implicit;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate(action_r5.providerName);\n  }\n}\nfunction DashboardComponent_div_0_div_112_div_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 96)(1, \"div\", 97)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(4, \"div\", 98)(5, \"h4\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"div\", 99)(8, \"span\", 100);\n    i0.ɵɵtext(9);\n    i0.ɵɵpipe(10, \"date\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(11, DashboardComponent_div_0_div_112_div_1_span_11_Template, 4, 1, \"span\", 101);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(12, DashboardComponent_div_0_div_112_div_1_div_12_Template, 5, 1, \"div\", 102);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(13, \"div\", 103)(14, \"span\", 104);\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"span\", 15);\n    i0.ɵɵtext(17, \"pts\");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const action_r5 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(3);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"color\", ctx_r1.getActionColor(action_r5.type));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r1.getActionIcon(action_r5.type), \" \");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(action_r5.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(i0.ɵɵpipeBind2(10, 8, action_r5.timestamp, \"short\"));\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", action_r5.location);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", action_r5.providerName);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate1(\"+\", action_r5.points, \"\");\n  }\n}\nfunction DashboardComponent_div_0_div_112_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 94);\n    i0.ɵɵtemplate(1, DashboardComponent_div_0_div_112_div_1_Template, 18, 11, \"div\", 95);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.filteredHistory);\n  }\n}\nfunction DashboardComponent_div_0_ng_template_113_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 107)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"sentiment_neutral\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"p\");\n    i0.ɵɵtext(4, \"Aucune action pour cette p\\u00E9riode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"button\", 108);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_ng_template_113_Template_button_click_5_listener() {\n      i0.ɵɵrestoreView(_r6);\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.openQRScanner());\n    });\n    i0.ɵɵtext(6, \" Commencer maintenant \");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction DashboardComponent_div_0_div_165_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 109);\n    i0.ɵɵelement(1, \"div\", 110);\n    i0.ɵɵelementStart(2, \"span\", 111);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const week_r7 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"height\", week_r7.points / ctx_r1.maxWeeklyPoints * 100 + \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"S\", week_r7.week, \"\");\n  }\n}\nfunction DashboardComponent_div_0_mat_card_172_mat_icon_9_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-icon\", 122);\n    i0.ɵɵtext(1, \"star\");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction DashboardComponent_div_0_mat_card_172_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r8 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"mat-card\", 112);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_mat_card_172_Template_mat_card_click_0_listener() {\n      const partner_r9 = i0.ɵɵrestoreView(_r8).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r1.viewPartner(partner_r9));\n    });\n    i0.ɵɵelementStart(1, \"div\", 113);\n    i0.ɵɵelement(2, \"img\", 114);\n    i0.ɵɵelementStart(3, \"div\", 115)(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"p\", 116);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"div\", 117);\n    i0.ɵɵtemplate(9, DashboardComponent_div_0_mat_card_172_mat_icon_9_Template, 2, 0, \"mat-icon\", 118);\n    i0.ɵɵelementStart(10, \"span\");\n    i0.ɵɵtext(11);\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(12, \"mat-card-content\")(13, \"p\", 119);\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(15, \"div\", 120)(16, \"button\", 121)(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"qr_code\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19, \" Scanner QR \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(20, \"button\", 121)(21, \"mat-icon\");\n    i0.ɵɵtext(22, \"directions\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(23, \" Itin\\u00E9raire \");\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const partner_r9 = ctx.$implicit;\n    const ctx_r1 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"src\", partner_r9.logo, i0.ɵɵsanitizeUrl)(\"alt\", partner_r9.name);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(partner_r9.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(partner_r9.category);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.getStars(partner_r9.rating));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"(\", partner_r9.reviews, \")\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(partner_r9.currentOffer);\n  }\n}\nfunction DashboardComponent_div_0_div_179_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r10 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 127);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_div_179_button_4_Template_button_click_0_listener() {\n      const action_r11 = i0.ɵɵrestoreView(_r10).$implicit;\n      const ctx_r1 = i0.ɵɵnextContext(3);\n      return i0.ɵɵresetView(ctx_r1.executeAction(action_r11));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"div\", 103);\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const action_r11 = ctx.$implicit;\n    i0.ɵɵproperty(\"color\", action_r11.color);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r11.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(action_r11.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"+\", action_r11.points, \"\");\n  }\n}\nfunction DashboardComponent_div_0_div_179_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 123)(1, \"h3\", 124);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 125);\n    i0.ɵɵtemplate(4, DashboardComponent_div_0_div_179_button_4_Template, 7, 4, \"button\", 126);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const category_r12 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(category_r12.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", category_r12.actions);\n  }\n}\nfunction DashboardComponent_div_0_mat_card_186_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"mat-card\", 128)(1, \"div\", 129)(2, \"mat-icon\", 130);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"h4\");\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"mat-card-content\")(7, \"p\", 131);\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 132)(10, \"div\", 35);\n    i0.ɵɵelement(11, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(12, \"span\", 37);\n    i0.ɵɵtext(13);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(14, \"div\", 133)(15, \"mat-icon\");\n    i0.ɵɵtext(16, \"card_giftcard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(17, \"span\");\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(19, \"button\", 134);\n    i0.ɵɵtext(20, \" Participer \");\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const challenge_r13 = ctx.$implicit;\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"background\", challenge_r13.gradient);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(challenge_r13.icon);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(challenge_r13.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(challenge_r13.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵstyleProp(\"width\", challenge_r13.progress + \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate2(\"\", challenge_r13.current, \"/\", challenge_r13.target, \"\");\n    i0.ɵɵadvance(5);\n    i0.ɵɵtextInterpolate(challenge_r13.reward);\n  }\n}\nfunction DashboardComponent_div_0_div_215_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 135)(1, \"span\", 136);\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 137)(4, \"div\", 138)(5, \"span\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"div\", 139)(8, \"span\");\n    i0.ɵɵtext(9);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const stat_r14 = ctx.$implicit;\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r14.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵstyleProp(\"width\", stat_r14.monastir + \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r14.monastirValue);\n    i0.ɵɵadvance();\n    i0.ɵɵstyleProp(\"width\", stat_r14.sousse + \"%\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(stat_r14.sousseValue);\n  }\n}\nfunction DashboardComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 3)(1, \"div\", 4)(2, \"div\", 5)(3, \"div\", 6)(4, \"mat-icon\");\n    i0.ɵɵtext(5, \"school\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(6, \"div\", 7)(7, \"h2\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"p\", 8);\n    i0.ɵɵtext(10);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(11, \"div\", 9)(12, \"div\", 10)(13, \"div\", 11)(14, \"mat-icon\", 12);\n    i0.ɵɵtext(15, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"div\", 13)(17, \"span\", 14);\n    i0.ɵɵtext(18);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(19, \"span\", 15);\n    i0.ɵɵtext(20, \"Points\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(21, \"button\", 16);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_Template_button_click_21_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.toggleNotifications());\n    });\n    i0.ɵɵelementStart(22, \"mat-icon\");\n    i0.ɵɵtext(23, \"notifications\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(24, \"button\", 17)(25, \"mat-icon\");\n    i0.ɵɵtext(26, \"account_circle\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(27, \"mat-menu\", null, 0)(29, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_Template_button_click_29_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openProfile());\n    });\n    i0.ɵɵelementStart(30, \"mat-icon\");\n    i0.ɵɵtext(31, \"person\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(32, \"span\");\n    i0.ɵɵtext(33, \"Mon Profil\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(34, \"button\", 18);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_Template_button_click_34_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.logout());\n    });\n    i0.ɵɵelementStart(35, \"mat-icon\");\n    i0.ɵɵtext(36, \"logout\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(37, \"span\");\n    i0.ɵɵtext(38, \"D\\u00E9connexion\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(39, \"div\", 19)(40, \"div\", 20)(41, \"mat-card\", 21)(42, \"mat-card-content\")(43, \"div\", 22)(44, \"div\", 23)(45, \"mat-icon\");\n    i0.ɵɵtext(46, \"qr_code_scanner\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(47, \"div\", 24)(48, \"h3\");\n    i0.ɵɵtext(49, \"Scanner un QR Code KnowMe\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(50, \"p\");\n    i0.ɵɵtext(51, \"D\\u00E9couvre un prestataire et gagne des points !\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(52, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function DashboardComponent_div_0_Template_button_click_52_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.openQRScanner());\n    });\n    i0.ɵɵelementStart(53, \"mat-icon\");\n    i0.ɵɵtext(54, \"camera_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(55, \" Scanner \");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(56, \"div\", 26)(57, \"mat-card\", 27)(58, \"mat-card-content\")(59, \"h3\");\n    i0.ɵɵtext(60, \"Progression vers ta prochaine r\\u00E9compense\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(61, \"div\", 28)(62, \"div\", 29)(63, \"div\", 30);\n    i0.ɵɵelement(64, \"img\", 31);\n    i0.ɵɵelementStart(65, \"div\", 32)(66, \"h4\");\n    i0.ɵɵtext(67);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(68, \"p\");\n    i0.ɵɵtext(69);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(70, \"div\", 33)(71, \"span\", 34);\n    i0.ɵɵtext(72);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(73, \"span\", 15);\n    i0.ɵɵtext(74, \"points restants\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(75, \"div\", 35);\n    i0.ɵɵelement(76, \"div\", 36);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(77, \"div\", 37)(78, \"span\");\n    i0.ɵɵtext(79);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(80, \"div\", 38)(81, \"p\");\n    i0.ɵɵtext(82);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(83, \"div\", 39)(84, \"h3\", 40)(85, \"mat-icon\");\n    i0.ɵɵtext(86, \"card_giftcard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(87, \" R\\u00E9compenses disponibles \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(88, \"div\", 41);\n    i0.ɵɵtemplate(89, DashboardComponent_div_0_mat_card_89_Template, 21, 12, \"mat-card\", 42);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(90, \"div\", 43)(91, \"div\", 44)(92, \"mat-card\", 45)(93, \"mat-card-header\")(94, \"mat-card-title\")(95, \"mat-icon\");\n    i0.ɵɵtext(96, \"history\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(97, \" Historique de mes actions \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(98, \"div\", 46)(99, \"mat-form-field\", 47)(100, \"mat-label\");\n    i0.ɵɵtext(101, \"P\\u00E9riode\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(102, \"mat-select\", 48);\n    i0.ɵɵtwoWayListener(\"valueChange\", function DashboardComponent_div_0_Template_mat_select_valueChange_102_listener($event) {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      i0.ɵɵtwoWayBindingSet(ctx_r1.selectedPeriod, $event) || (ctx_r1.selectedPeriod = $event);\n      return i0.ɵɵresetView($event);\n    });\n    i0.ɵɵlistener(\"selectionChange\", function DashboardComponent_div_0_Template_mat_select_selectionChange_102_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r1 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r1.filterHistory());\n    });\n    i0.ɵɵelementStart(103, \"mat-option\", 49);\n    i0.ɵɵtext(104, \"Cette semaine\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(105, \"mat-option\", 50);\n    i0.ɵɵtext(106, \"Ce mois\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(107, \"mat-option\", 51);\n    i0.ɵɵtext(108, \"Cette ann\\u00E9e\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(109, \"mat-option\", 52);\n    i0.ɵɵtext(110, \"Tout\");\n    i0.ɵɵelementEnd()()()()();\n    i0.ɵɵelementStart(111, \"mat-card-content\");\n    i0.ɵɵtemplate(112, DashboardComponent_div_0_div_112_Template, 2, 1, \"div\", 53)(113, DashboardComponent_div_0_ng_template_113_Template, 7, 0, \"ng-template\", null, 1, i0.ɵɵtemplateRefExtractor);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(115, \"div\", 54)(116, \"mat-card\", 55)(117, \"mat-card-header\")(118, \"mat-card-title\")(119, \"mat-icon\");\n    i0.ɵɵtext(120, \"analytics\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(121, \" Mes statistiques ce mois \");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(122, \"mat-card-content\")(123, \"div\", 56)(124, \"div\", 57)(125, \"div\", 58)(126, \"mat-icon\");\n    i0.ɵɵtext(127, \"trending_up\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(128, \"div\", 59)(129, \"span\", 60);\n    i0.ɵɵtext(130);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(131, \"span\", 61);\n    i0.ɵɵtext(132, \"Points gagn\\u00E9s\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(133, \"div\", 57)(134, \"div\", 58)(135, \"mat-icon\");\n    i0.ɵɵtext(136, \"trending_down\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(137, \"div\", 59)(138, \"span\", 60);\n    i0.ɵɵtext(139);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(140, \"span\", 61);\n    i0.ɵɵtext(141, \"Points d\\u00E9pens\\u00E9s\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(142, \"div\", 57)(143, \"div\", 58)(144, \"mat-icon\");\n    i0.ɵɵtext(145, \"qr_code_scanner\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(146, \"div\", 59)(147, \"span\", 60);\n    i0.ɵɵtext(148);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(149, \"span\", 61);\n    i0.ɵɵtext(150, \"QR scann\\u00E9s\");\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(151, \"div\", 57)(152, \"div\", 58)(153, \"mat-icon\");\n    i0.ɵɵtext(154, \"verified\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(155, \"div\", 59)(156, \"span\", 60);\n    i0.ɵɵtext(157);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(158, \"span\", 61);\n    i0.ɵɵtext(159, \"Actions valid\\u00E9es\");\n    i0.ɵɵelementEnd()()()();\n    i0.ɵɵelementStart(160, \"div\", 62)(161, \"h4\");\n    i0.ɵɵtext(162, \"\\u00C9volution des points\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(163, \"div\", 63)(164, \"div\", 64);\n    i0.ɵɵtemplate(165, DashboardComponent_div_0_div_165_Template, 4, 3, \"div\", 65);\n    i0.ɵɵelementEnd()()()()()();\n    i0.ɵɵelementStart(166, \"div\", 66)(167, \"h2\", 67)(168, \"mat-icon\");\n    i0.ɵɵtext(169, \"store\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(170, \" Partenaires Locaux \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(171, \"div\", 68);\n    i0.ɵɵtemplate(172, DashboardComponent_div_0_mat_card_172_Template, 24, 7, \"mat-card\", 69);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(173, \"div\", 70)(174, \"h2\", 67)(175, \"mat-icon\");\n    i0.ɵɵtext(176, \"flash_on\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(177, \" Actions Rapides \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(178, \"div\", 71);\n    i0.ɵɵtemplate(179, DashboardComponent_div_0_div_179_Template, 5, 2, \"div\", 72);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(180, \"div\", 73)(181, \"h2\", 67)(182, \"mat-icon\");\n    i0.ɵɵtext(183, \"emoji_events\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(184, \" D\\u00E9fis Communautaires \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(185, \"div\", 74);\n    i0.ɵɵtemplate(186, DashboardComponent_div_0_mat_card_186_Template, 21, 10, \"mat-card\", 75);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(187, \"div\", 76)(188, \"h2\", 67)(189, \"mat-icon\");\n    i0.ɵɵtext(190, \"compare_arrows\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(191, \" Monastir vs Sousse \");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(192, \"mat-card\", 77)(193, \"mat-card-content\")(194, \"div\", 78)(195, \"div\", 79)(196, \"h3\");\n    i0.ɵɵtext(197, \"Monastir\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(198, \"div\", 80);\n    i0.ɵɵtext(199);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(200, \"p\");\n    i0.ɵɵtext(201);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(202, \"div\", 81)(203, \"mat-icon\");\n    i0.ɵɵtext(204, \"sports\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(205, \"span\");\n    i0.ɵɵtext(206, \"VS\");\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(207, \"div\", 82)(208, \"h3\");\n    i0.ɵɵtext(209, \"Sousse\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(210, \"div\", 80);\n    i0.ɵɵtext(211);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(212, \"p\");\n    i0.ɵɵtext(213);\n    i0.ɵɵelementEnd()()();\n    i0.ɵɵelementStart(214, \"div\", 83);\n    i0.ɵɵtemplate(215, DashboardComponent_div_0_div_215_Template, 10, 7, \"div\", 84);\n    i0.ɵɵelementEnd()()()()()();\n  }\n  if (rf & 2) {\n    const profileMenu_r15 = i0.ɵɵreference(28);\n    const noHistory_r16 = i0.ɵɵreference(114);\n    const ctx_r1 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate1(\"Bonjour \", ctx_r1.user.name, \" ! \\uD83D\\uDC4B\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\\u00C9l\\u00E8ve \\u2022 \", ctx_r1.user.city, \"\");\n    i0.ɵɵadvance(8);\n    i0.ɵɵtextInterpolate(ctx_r1.user.points);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matBadge\", ctx_r1.unreadNotifications)(\"matBadgeHidden\", ctx_r1.unreadNotifications === 0);\n    i0.ɵɵadvance(3);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", profileMenu_r15);\n    i0.ɵɵadvance(40);\n    i0.ɵɵproperty(\"src\", ctx_r1.nextReward.image, i0.ɵɵsanitizeUrl)(\"alt\", ctx_r1.nextReward.title);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.nextReward.title);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r1.nextReward.description);\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getPointsToNextReward());\n    i0.ɵɵadvance(4);\n    i0.ɵɵstyleProp(\"width\", ctx_r1.getProgressPercentage() + \"%\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate2(\"\", ctx_r1.user.points, \" / \", ctx_r1.nextReward.pointsRequired, \" points\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r1.getMotivationMessage());\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.availableRewards);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtwoWayProperty(\"value\", ctx_r1.selectedPeriod);\n    i0.ɵɵadvance(10);\n    i0.ɵɵproperty(\"ngIf\", ctx_r1.filteredHistory.length > 0)(\"ngIfElse\", noHistory_r16);\n    i0.ɵɵadvance(18);\n    i0.ɵɵtextInterpolate(ctx_r1.monthlyStats.pointsEarned);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.monthlyStats.pointsSpent);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.monthlyStats.scansCount);\n    i0.ɵɵadvance(9);\n    i0.ɵɵtextInterpolate(ctx_r1.monthlyStats.validationsCount);\n    i0.ɵɵadvance(8);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.weeklyPointsData);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.featuredPartners);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.actionCategories);\n    i0.ɵɵadvance(7);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.activeChallenges);\n    i0.ɵɵadvance(13);\n    i0.ɵɵtextInterpolate(ctx_r1.cityComparison.monastir.score);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.cityComparison.monastir.participants, \" participants\");\n    i0.ɵɵadvance(10);\n    i0.ɵɵtextInterpolate(ctx_r1.cityComparison.sousse.score);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate1(\"\", ctx_r1.cityComparison.sousse.participants, \" participants\");\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r1.comparisonStats);\n  }\n}\nexport class DashboardComponent {\n  constructor(authService, router, dashboardRouter) {\n    this.authService = authService;\n    this.router = router;\n    this.dashboardRouter = dashboardRouter;\n    this.user = null;\n    // Community data\n    this.communityFeed = [];\n    this.topContributors = [];\n    this.localEvents = [];\n    this.featuredPartners = [];\n    this.activeChallenges = [];\n    // City comparison data\n    this.cityComparison = {\n      monastir: {\n        score: 0,\n        participants: 0\n      },\n      sousse: {\n        score: 0,\n        participants: 0\n      }\n    };\n    this.comparisonStats = [{\n      name: 'Actions validées',\n      monastir: 0,\n      sousse: 0,\n      monastirValue: 0,\n      sousseValue: 0\n    }, {\n      name: 'Événements organisés',\n      monastir: 0,\n      sousse: 0,\n      monastirValue: 0,\n      sousseValue: 0\n    }, {\n      name: 'Partenaires actifs',\n      monastir: 0,\n      sousse: 0,\n      monastirValue: 0,\n      sousseValue: 0\n    }];\n    this.actionCategories = [{\n      title: 'Environnement',\n      actions: [{\n        title: 'Nettoyage plage',\n        icon: 'waves',\n        color: 'primary',\n        points: 15\n      }, {\n        title: 'Plantation arbres',\n        icon: 'park',\n        color: 'primary',\n        points: 20\n      }, {\n        title: 'Recyclage',\n        icon: 'recycling',\n        color: 'primary',\n        points: 10\n      }]\n    }, {\n      title: 'Social',\n      actions: [{\n        title: 'Aide personnes âgées',\n        icon: 'elderly',\n        color: 'accent',\n        points: 25\n      }, {\n        title: 'Cours bénévoles',\n        icon: 'school',\n        color: 'accent',\n        points: 30\n      }, {\n        title: 'Distribution repas',\n        icon: 'restaurant',\n        color: 'accent',\n        points: 20\n      }]\n    }, {\n      title: 'Culture',\n      actions: [{\n        title: 'Guide touristique',\n        icon: 'tour',\n        color: 'warn',\n        points: 15\n      }, {\n        title: 'Animation enfants',\n        icon: 'child_care',\n        color: 'warn',\n        points: 18\n      }, {\n        title: 'Événement culturel',\n        icon: 'theater_comedy',\n        color: 'warn',\n        points: 25\n      }]\n    }];\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        // Rediriger vers le dashboard approprié selon le rôle\n        if (user.role !== UserRole.USER) {\n          this.dashboardRouter.navigateToUserDashboard(user);\n          return;\n        }\n        this.loadDashboardData();\n      }\n    });\n  }\n  loadDashboardData() {\n    this.loadCommunityFeed();\n    this.loadTopContributors();\n    this.loadLocalEvents();\n    this.loadFeaturedPartners();\n    this.loadActiveChallenges();\n    this.loadCityComparison();\n  }\n  loadCommunityFeed() {\n    // Simulate real community activity feed\n    this.communityFeed = [{\n      id: '1',\n      description: 'Ahmed a nettoyé la plage de Monastir',\n      points: 15,\n      type: 'environment',\n      timestamp: new Date(Date.now() - 1000 * 60 * 30),\n      userId: 'user1',\n      userName: 'Ahmed'\n    }, {\n      id: '2',\n      description: 'Fatma a aidé des personnes âgées à Sousse',\n      points: 25,\n      type: 'social',\n      timestamp: new Date(Date.now() - 1000 * 60 * 60),\n      userId: 'user2',\n      userName: 'Fatma'\n    }, {\n      id: '3',\n      description: 'Mohamed a organisé un cours de français',\n      points: 30,\n      type: 'education',\n      timestamp: new Date(Date.now() - 1000 * 60 * 90),\n      userId: 'user3',\n      userName: 'Mohamed'\n    }, {\n      id: '4',\n      description: 'Leila a guidé des touristes au Ribat',\n      points: 15,\n      type: 'culture',\n      timestamp: new Date(Date.now() - 1000 * 60 * 120),\n      userId: 'user4',\n      userName: 'Leila'\n    }];\n  }\n  loadTopContributors() {\n    this.topContributors = [{\n      uid: 'top1',\n      name: 'Ahmed Ben Ali',\n      city: 'Monastir',\n      points: 1250,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }, {\n      uid: 'top2',\n      name: 'Fatma Trabelsi',\n      city: 'Sousse',\n      points: 1180,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }, {\n      uid: 'top3',\n      name: 'Mohamed Gharbi',\n      city: 'Monastir',\n      points: 1050,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }, {\n      uid: 'top4',\n      name: 'Leila Mansouri',\n      city: 'Sousse',\n      points: 980,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }, {\n      uid: 'top5',\n      name: 'Karim Bouazizi',\n      city: 'Monastir',\n      points: 920,\n      email: '',\n      role: UserRole.USER,\n      createdAt: new Date(),\n      updatedAt: new Date(),\n      isActive: true,\n      history: []\n    }];\n  }\n  loadLocalEvents() {\n    this.localEvents = [{\n      id: 'event1',\n      title: 'Nettoyage de la Plage de Monastir',\n      description: 'Rejoignez-nous pour nettoyer notre belle plage',\n      date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2),\n      location: 'Plage de Monastir',\n      category: 'Environnement',\n      points: 20,\n      image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=200&fit=crop',\n      participants: 45,\n      maxParticipants: 100\n    }, {\n      id: 'event2',\n      title: 'Festival Culturel de Sousse',\n      description: 'Célébrons notre patrimoine culturel ensemble',\n      date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5),\n      location: 'Médina de Sousse',\n      category: 'Culture',\n      points: 15,\n      image: 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=400&h=200&fit=crop',\n      participants: 120,\n      maxParticipants: 200\n    }, {\n      id: 'event3',\n      title: 'Cours de Français pour Réfugiés',\n      description: 'Aidez à enseigner le français aux nouveaux arrivants',\n      date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7),\n      location: 'Centre Communautaire',\n      category: 'Éducation',\n      points: 30,\n      image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=200&fit=crop',\n      participants: 15,\n      maxParticipants: 30\n    }];\n  }\n  loadFeaturedPartners() {\n    this.featuredPartners = [{\n      id: 'partner1',\n      name: 'Café des Nattes',\n      category: 'Restaurant',\n      logo: 'https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=100&h=100&fit=crop',\n      rating: 4.5,\n      reviews: 127,\n      currentOffer: '10% de réduction avec 50 points',\n      location: 'Médina de Monastir',\n      qrCode: 'PARTNER_CAFE_NATTES'\n    }, {\n      id: 'partner2',\n      name: 'Boutique Artisanat Sousse',\n      category: 'Artisanat',\n      logo: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop',\n      rating: 4.8,\n      reviews: 89,\n      currentOffer: 'Produit gratuit avec 100 points',\n      location: 'Médina de Sousse',\n      qrCode: 'PARTNER_ARTISANAT_SOUSSE'\n    }, {\n      id: 'partner3',\n      name: 'Hammam Traditionnel',\n      category: 'Bien-être',\n      logo: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=100&h=100&fit=crop',\n      rating: 4.3,\n      reviews: 156,\n      currentOffer: 'Séance gratuite avec 75 points',\n      location: 'Centre-ville Monastir',\n      qrCode: 'PARTNER_HAMMAM'\n    }];\n  }\n  loadActiveChallenges() {\n    this.activeChallenges = [{\n      id: 'challenge1',\n      title: 'Éco-Warrior',\n      description: 'Participez à 5 actions environnementales ce mois-ci',\n      icon: 'eco',\n      gradient: 'linear-gradient(135deg, #4CAF50, #8BC34A)',\n      progress: 60,\n      current: 3,\n      target: 5,\n      reward: 'Badge Éco-Warrior + 100 points bonus',\n      endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 15) // 15 days\n    }, {\n      id: 'challenge2',\n      title: 'Ambassadeur Culturel',\n      description: 'Guidez 10 touristes dans votre ville',\n      icon: 'tour',\n      gradient: 'linear-gradient(135deg, #FF9800, #FFC107)',\n      progress: 30,\n      current: 3,\n      target: 10,\n      reward: 'Badge Ambassadeur + Visite gratuite musée',\n      endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 20) // 20 days\n    }, {\n      id: 'challenge3',\n      title: 'Solidarité Communautaire',\n      description: 'Aidez 15 personnes dans le besoin',\n      icon: 'volunteer_activism',\n      gradient: 'linear-gradient(135deg, #E91E63, #F06292)',\n      progress: 80,\n      current: 12,\n      target: 15,\n      reward: 'Badge Solidarité + 200 points bonus',\n      endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 10) // 10 days\n    }];\n  }\n  loadCityComparison() {\n    // Simulate city competition data\n    this.cityComparison = {\n      monastir: {\n        score: 15420,\n        participants: 342\n      },\n      sousse: {\n        score: 16180,\n        participants: 389\n      }\n    };\n    this.comparisonStats = [{\n      name: 'Actions validées',\n      monastir: 65,\n      sousse: 78,\n      monastirValue: 1250,\n      sousseValue: 1520\n    }, {\n      name: 'Événements organisés',\n      monastir: 45,\n      sousse: 52,\n      monastirValue: 23,\n      sousseValue: 27\n    }, {\n      name: 'Partenaires actifs',\n      monastir: 80,\n      sousse: 70,\n      monastirValue: 16,\n      sousseValue: 14\n    }];\n  }\n  // UI Helper Methods\n  getGreeting() {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n  getCityIcon() {\n    if (!this.user) return 'location_city';\n    return this.user.city === 'Monastir' ? 'account_balance' : 'museum';\n  }\n  getCommunityRank() {\n    if (!this.user) return 0;\n    const userIndex = this.topContributors.findIndex(u => u.uid === this.user.uid);\n    return userIndex >= 0 ? userIndex + 1 : this.topContributors.length + 1;\n  }\n  getImpactScore() {\n    if (!this.user) return 0;\n    // Calculate impact based on points and community actions\n    return Math.floor(this.user.points * 1.2 + (this.user.history?.length || 0) * 5);\n  }\n  getCityProgress() {\n    if (!this.user) return 0;\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    const monthlyTarget = 20000;\n    return Math.min(cityData.score / monthlyTarget * 314, 314); // 314 = 2π * 50 (circle circumference)\n  }\n  getCityProgressPercent() {\n    if (!this.user) return 0;\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    const monthlyTarget = 20000;\n    return Math.min(Math.round(cityData.score / monthlyTarget * 100), 100);\n  }\n  getCityStats() {\n    if (!this.user) return {\n      validatedActions: 0,\n      activeUsers: 0\n    };\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    return {\n      validatedActions: Math.floor(cityData.score / 15),\n      activeUsers: cityData.participants\n    };\n  }\n  getActivityColor(type) {\n    const colorMap = {\n      'environment': '#4CAF50',\n      'social': '#E91E63',\n      'education': '#2196F3',\n      'culture': '#FF9800',\n      'health': '#9C27B0'\n    };\n    return colorMap[type] || '#667eea';\n  }\n  getActivityIcon(type) {\n    const iconMap = {\n      'environment': 'eco',\n      'social': 'volunteer_activism',\n      'education': 'school',\n      'culture': 'theater_comedy',\n      'health': 'health_and_safety'\n    };\n    return iconMap[type] || 'circle';\n  }\n  getTrophyColor(index) {\n    const colors = ['#FFD700', '#C0C0C0', '#CD7F32']; // Gold, Silver, Bronze\n    return colors[index] || '#667eea';\n  }\n  getTrophyIcon(index) {\n    return index < 3 ? 'emoji_events' : 'star';\n  }\n  getStars(rating) {\n    return Array(Math.floor(rating)).fill(0);\n  }\n  // Action Methods\n  viewEvent(event) {\n    // Navigate to event details or open modal\n    console.log('Viewing event:', event);\n  }\n  viewPartner(partner) {\n    // Navigate to partner details or open modal\n    console.log('Viewing partner:', partner);\n  }\n  executeAction(action) {\n    // Execute the selected action\n    console.log('Executing action:', action);\n    // This would typically open a form or navigate to action details\n  }\n  viewAllActivity() {\n    this.router.navigate(['/community/activity']);\n  }\n  static {\n    this.ɵfac = function DashboardComponent_Factory(t) {\n      return new (t || DashboardComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router), i0.ɵɵdirectiveInject(i3.DashboardRouterService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: DashboardComponent,\n      selectors: [[\"app-dashboard\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"profileMenu\", \"matMenu\"], [\"noHistory\", \"\"], [\"class\", \"dashboard-container\", 4, \"ngIf\"], [1, \"dashboard-container\"], [1, \"dashboard-header\", \"fade-in\"], [1, \"user-welcome\"], [1, \"user-avatar\"], [1, \"user-info\"], [1, \"user-role\"], [1, \"header-actions\"], [1, \"points-display\"], [1, \"points-card\"], [1, \"points-icon\"], [1, \"points-info\"], [1, \"points-value\"], [1, \"points-label\"], [\"mat-icon-button\", \"\", \"matBadgeColor\", \"warn\", 1, \"notifications-btn\", 3, \"click\", \"matBadge\", \"matBadgeHidden\"], [\"mat-icon-button\", \"\", 3, \"matMenuTriggerFor\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"main-section\"], [1, \"qr-scan-section\"], [1, \"qr-scan-card\"], [1, \"qr-scan-content\"], [1, \"qr-scan-icon\"], [1, \"qr-scan-text\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"qr-scan-btn\", 3, \"click\"], [1, \"progress-section\"], [1, \"progress-card\"], [1, \"reward-progress\"], [1, \"progress-info\"], [1, \"next-reward\"], [1, \"reward-icon\", 3, \"src\", \"alt\"], [1, \"reward-details\"], [1, \"points-needed\"], [1, \"points-remaining\"], [1, \"progress-bar\"], [1, \"progress-fill\"], [1, \"progress-text\"], [1, \"motivation-message\"], [1, \"rewards-section\"], [1, \"section-subtitle\"], [1, \"rewards-grid\"], [\"class\", \"reward-card\", 3, \"affordable\", 4, \"ngFor\", \"ngForOf\"], [1, \"secondary-section\"], [1, \"history-section\"], [1, \"history-card\"], [1, \"history-filters\"], [\"appearance\", \"outline\", 1, \"filter-field\"], [3, \"valueChange\", \"selectionChange\", \"value\"], [\"value\", \"week\"], [\"value\", \"month\"], [\"value\", \"year\"], [\"value\", \"all\"], [\"class\", \"history-list\", 4, \"ngIf\", \"ngIfElse\"], [1, \"stats-section\"], [1, \"stats-card\"], [1, \"stats-grid\"], [1, \"stat-item\"], [1, \"stat-icon\"], [1, \"stat-content\"], [1, \"stat-value\"], [1, \"stat-label\"], [1, \"points-chart\"], [1, \"chart-container\"], [1, \"chart-bars\"], [\"class\", \"chart-bar\", 4, \"ngFor\", \"ngForOf\"], [1, \"partners-section\"], [1, \"section-title\"], [1, \"partners-grid\"], [\"class\", \"partner-card\", 3, \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"actions-hub\"], [1, \"actions-grid\"], [\"class\", \"action-category\", 4, \"ngFor\", \"ngForOf\"], [1, \"challenges-section\"], [1, \"challenges-grid\"], [\"class\", \"challenge-card\", 4, \"ngFor\", \"ngForOf\"], [1, \"comparison-section\"], [1, \"comparison-card\"], [1, \"comparison-header\"], [1, \"city-score\", \"monastir\"], [1, \"score\"], [1, \"vs-divider\"], [1, \"city-score\", \"sousse\"], [1, \"comparison-stats\"], [\"class\", \"stat-comparison\", 4, \"ngFor\", \"ngForOf\"], [1, \"reward-card\"], [1, \"reward-image\"], [3, \"src\", \"alt\"], [\"class\", \"reward-badge\", 4, \"ngIf\"], [1, \"reward-description\"], [1, \"reward-partner\"], [1, \"reward-points\"], [\"mat-raised-button\", \"\", 1, \"exchange-btn\", 3, \"click\", \"color\", \"disabled\"], [1, \"reward-badge\"], [1, \"history-list\"], [\"class\", \"history-item\", 4, \"ngFor\", \"ngForOf\"], [1, \"history-item\"], [1, \"action-icon\"], [1, \"action-details\"], [1, \"action-meta\"], [1, \"action-date\"], [\"class\", \"action-location\", 4, \"ngIf\"], [\"class\", \"action-provider\", 4, \"ngIf\"], [1, \"action-points\"], [1, \"points-earned\"], [1, \"action-location\"], [1, \"action-provider\"], [1, \"no-history\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [1, \"chart-bar\"], [1, \"bar\"], [1, \"bar-label\"], [1, \"partner-card\", 3, \"click\"], [1, \"partner-header\"], [1, \"partner-logo\", 3, \"src\", \"alt\"], [1, \"partner-info\"], [1, \"partner-category\"], [1, \"partner-rating\"], [\"class\", \"star-icon\", 4, \"ngFor\", \"ngForOf\"], [1, \"partner-offer\"], [1, \"partner-actions\"], [\"mat-stroked-button\", \"\", 1, \"partner-btn\"], [1, \"star-icon\"], [1, \"action-category\"], [1, \"category-title\"], [1, \"category-actions\"], [\"mat-raised-button\", \"\", \"class\", \"action-btn\", 3, \"color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"mat-raised-button\", \"\", 1, \"action-btn\", 3, \"click\", \"color\"], [1, \"challenge-card\"], [1, \"challenge-header\"], [1, \"challenge-icon\"], [1, \"challenge-description\"], [1, \"challenge-progress\"], [1, \"challenge-reward\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 1, \"challenge-btn\"], [1, \"stat-comparison\"], [1, \"stat-name\"], [1, \"stat-bars\"], [1, \"stat-bar\", \"monastir\"], [1, \"stat-bar\", \"sousse\"]],\n      template: function DashboardComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, DashboardComponent_div_0_Template, 216, 33, \"div\", 2);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", ctx.user);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.MatButton, i5.MatIconButton, i6.MatCard, i6.MatCardContent, i6.MatCardHeader, i6.MatCardTitle, i7.MatIcon, i8.MatFormField, i8.MatLabel, i9.MatMenu, i9.MatMenuItem, i9.MatMenuTrigger, i10.MatSelect, i11.MatOption, i12.MatBadge, i4.DatePipe],\n      styles: [\"\\n\\n.dashboard-container[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  padding: 20px;\\n  animation: _ngcontent-%COMP%_fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  min-height: 100vh;\\n}\\n\\n\\n\\n.hero-section[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  align-items: center;\\n  margin-bottom: 40px;\\n  padding: 60px 40px;\\n  border-radius: 32px;\\n  color: white;\\n  position: relative;\\n  overflow: hidden;\\n  box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.1);\\n}\\n\\n.hero-section.city-monastir[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n}\\n\\n.hero-section.city-sousse[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);\\n}\\n\\n.hero-section[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n.city-badge[_ngcontent-%COMP%] {\\n  display: inline-flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: rgba(255, 255, 255, 0.2);\\n  padding: 8px 16px;\\n  border-radius: 20px;\\n  font-size: 0.9rem;\\n  font-weight: 600;\\n  margin-bottom: 16px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.hero-title[_ngcontent-%COMP%] {\\n  margin: 0 0 12px 0;\\n  font-size: 3rem;\\n  font-weight: 800;\\n  font-family: 'Poppins', sans-serif;\\n  text-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);\\n  background: linear-gradient(45deg, #ffffff, #f0f0f0);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.hero-subtitle[_ngcontent-%COMP%] {\\n  margin: 0 0 32px 0;\\n  font-size: 1.2rem;\\n  opacity: 0.9;\\n  font-weight: 400;\\n}\\n\\n.hero-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 32px;\\n}\\n\\n.hero-stat[_ngcontent-%COMP%] {\\n  text-align: center;\\n}\\n\\n.stat-number[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 2.5rem;\\n  font-weight: 700;\\n  line-height: 1;\\n  margin-bottom: 4px;\\n}\\n\\n.stat-label[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n  opacity: 0.8;\\n  text-transform: uppercase;\\n  letter-spacing: 1px;\\n}\\n\\n.hero-visual[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n}\\n\\n.city-illustration[_ngcontent-%COMP%] {\\n  width: 120px;\\n  height: 120px;\\n  background: rgba(255, 255, 255, 0.15);\\n  border-radius: 50%;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 2px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.city-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem !important;\\n  width: 4rem !important;\\n  height: 4rem !important;\\n  color: rgba(255, 255, 255, 0.9);\\n}\\n\\n\\n\\n.impact-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.section-title[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  gap: 12px;\\n  margin: 0 0 32px 0;\\n  color: #2d3748;\\n  font-size: 2rem;\\n  font-weight: 700;\\n  font-family: 'Poppins', sans-serif;\\n  text-align: center;\\n  position: relative;\\n  padding-bottom: 16px;\\n}\\n\\n.section-title[_ngcontent-%COMP%]::after {\\n  content: '';\\n  position: absolute;\\n  bottom: 0;\\n  left: 50%;\\n  transform: translateX(-50%);\\n  width: 80px;\\n  height: 4px;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  border-radius: 2px;\\n}\\n\\n.impact-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 24px;\\n}\\n\\n.impact-card[_ngcontent-%COMP%] {\\n  border-radius: 20px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1);\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  overflow: hidden;\\n  position: relative;\\n}\\n\\n.impact-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-8px);\\n  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);\\n}\\n\\n.impact-card[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #2d3748;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  text-align: center;\\n}\\n\\n\\n\\n.city-progress[_ngcontent-%COMP%]   .progress-ring[_ngcontent-%COMP%] {\\n  position: relative;\\n  margin: 0 auto 24px;\\n  width: 120px;\\n  height: 120px;\\n}\\n\\n.progress-svg[_ngcontent-%COMP%] {\\n  transform: rotate(-90deg);\\n}\\n\\n.progress-bg[_ngcontent-%COMP%] {\\n  fill: none;\\n  stroke: #e2e8f0;\\n  stroke-width: 8;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  fill: none;\\n  stroke: #667eea;\\n  stroke-width: 8;\\n  stroke-linecap: round;\\n  transition: stroke-dasharray 1s ease-in-out;\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 50%;\\n  left: 50%;\\n  transform: translate(-50%, -50%);\\n  text-align: center;\\n}\\n\\n.progress-value[_ngcontent-%COMP%] {\\n  display: block;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  color: #2d3748;\\n}\\n\\n.progress-label[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.progress-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.detail-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: space-between;\\n  padding: 8px 0;\\n  border-bottom: 1px solid #e2e8f0;\\n}\\n\\n.detail-item[_ngcontent-%COMP%]:last-child {\\n  border-bottom: none;\\n}\\n\\n\\n\\n.community-feed[_ngcontent-%COMP%]   .feed-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n  margin-bottom: 16px;\\n}\\n\\n.feed-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  background: #f7fafc;\\n  border-radius: 12px;\\n  transition: all 0.2s ease;\\n}\\n\\n.feed-item[_ngcontent-%COMP%]:hover {\\n  background: #edf2f7;\\n  transform: translateX(4px);\\n}\\n\\n.feed-avatar[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 50%;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  flex-shrink: 0;\\n}\\n\\n.feed-content[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.feed-text[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  font-size: 0.9rem;\\n  color: #2d3748;\\n  font-weight: 500;\\n}\\n\\n.feed-time[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.feed-points[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78, #38a169);\\n  color: white;\\n  padding: 4px 8px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n}\\n\\n.view-all-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  margin-top: 8px;\\n}\\n\\n\\n\\n.leaderboard[_ngcontent-%COMP%]   .leaderboard-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.leader-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 12px;\\n  padding: 12px;\\n  border-radius: 12px;\\n  transition: all 0.2s ease;\\n}\\n\\n.leader-item[_ngcontent-%COMP%]:hover {\\n  background: #f7fafc;\\n}\\n\\n.leader-item.current-user[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\\n  border: 2px solid rgba(102, 126, 234, 0.3);\\n}\\n\\n.leader-rank[_ngcontent-%COMP%] {\\n  width: 32px;\\n  height: 32px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-weight: 700;\\n  border-radius: 50%;\\n  background: #e2e8f0;\\n  color: #4a5568;\\n}\\n\\n.trophy-icon[_ngcontent-%COMP%] {\\n  font-size: 1.5rem !important;\\n  width: 1.5rem !important;\\n  height: 1.5rem !important;\\n}\\n\\n.rank-number[_ngcontent-%COMP%] {\\n  font-size: 0.9rem;\\n}\\n\\n.leader-info[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.leader-name[_ngcontent-%COMP%] {\\n  display: block;\\n  font-weight: 600;\\n  color: #2d3748;\\n  font-size: 0.9rem;\\n}\\n\\n.leader-city[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.leader-points[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #667eea;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.events-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.events-carousel[_ngcontent-%COMP%] {\\n  overflow: hidden;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 24px;\\n  overflow-x: auto;\\n  padding-bottom: 16px;\\n  scroll-behavior: smooth;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%]::-webkit-scrollbar {\\n  height: 8px;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-track {\\n  background: #f1f1f1;\\n  border-radius: 4px;\\n}\\n\\n.events-scroll[_ngcontent-%COMP%]::-webkit-scrollbar-thumb {\\n  background: #667eea;\\n  border-radius: 4px;\\n}\\n\\n.event-card[_ngcontent-%COMP%] {\\n  min-width: 320px;\\n  border-radius: 16px;\\n  overflow: hidden;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.event-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.event-image[_ngcontent-%COMP%] {\\n  height: 160px;\\n  background-size: cover;\\n  background-position: center;\\n  position: relative;\\n  background-color: #e2e8f0;\\n}\\n\\n.event-badge[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 12px;\\n  right: 12px;\\n  padding: 4px 12px;\\n  border-radius: 12px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  color: white;\\n  text-transform: uppercase;\\n}\\n\\n.badge-environnement[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #48bb78, #38a169);\\n}\\n\\n.badge-culture[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #ed8936, #dd6b20);\\n}\\n\\n.badge-\\u00E9ducation[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #4299e1, #3182ce);\\n}\\n\\n.event-card[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #2d3748;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.event-description[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n  line-height: 1.4;\\n}\\n\\n.event-details[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n  margin-bottom: 16px;\\n}\\n\\n.event-detail[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  font-size: 0.8rem;\\n  color: #4a5568;\\n}\\n\\n.event-detail[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  width: 1rem !important;\\n  height: 1rem !important;\\n  color: #667eea;\\n}\\n\\n.event-join-btn[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 36px;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n.partners-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.partners-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));\\n  gap: 24px;\\n}\\n\\n.partner-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  cursor: pointer;\\n  transition: all 0.3s ease;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.partner-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.partner-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 20px 20px 0;\\n}\\n\\n.partner-logo[_ngcontent-%COMP%] {\\n  width: 60px;\\n  height: 60px;\\n  border-radius: 12px;\\n  object-fit: cover;\\n  background: #f7fafc;\\n}\\n\\n.partner-info[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #2d3748;\\n  font-size: 1.1rem;\\n  font-weight: 600;\\n}\\n\\n.partner-category[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n\\n.partner-rating[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 4px;\\n  font-size: 0.8rem;\\n  color: #718096;\\n}\\n\\n.star-icon[_ngcontent-%COMP%] {\\n  font-size: 1rem !important;\\n  width: 1rem !important;\\n  height: 1rem !important;\\n  color: #ffd700;\\n}\\n\\n.partner-offer[_ngcontent-%COMP%] {\\n  margin: 0 0 16px 0;\\n  color: #2d3748;\\n  font-weight: 500;\\n  background: linear-gradient(135deg, rgba(102, 126, 234, 0.1), rgba(118, 75, 162, 0.1));\\n  padding: 12px;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n}\\n\\n.partner-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n}\\n\\n.partner-btn[_ngcontent-%COMP%] {\\n  flex: 1;\\n  height: 36px;\\n  font-size: 0.8rem;\\n}\\n\\n\\n\\n.actions-hub[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.actions-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));\\n  gap: 32px;\\n}\\n\\n.action-category[_ngcontent-%COMP%] {\\n  background: white;\\n  border-radius: 16px;\\n  padding: 24px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.category-title[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #2d3748;\\n  font-size: 1.2rem;\\n  font-weight: 600;\\n  text-align: center;\\n  padding-bottom: 12px;\\n  border-bottom: 2px solid #e2e8f0;\\n}\\n\\n.category-actions[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 12px;\\n}\\n\\n.action-btn[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  padding: 16px;\\n  border-radius: 12px;\\n  transition: all 0.3s ease;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.action-btn[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  flex: 1;\\n  text-align: left;\\n  margin-left: 12px;\\n  font-weight: 500;\\n}\\n\\n.action-points[_ngcontent-%COMP%] {\\n  background: rgba(255, 255, 255, 0.9);\\n  color: #2d3748;\\n  padding: 4px 8px;\\n  border-radius: 8px;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n}\\n\\n\\n\\n.challenges-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.challenges-grid[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));\\n  gap: 24px;\\n}\\n\\n.challenge-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  overflow: hidden;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n  transition: all 0.3s ease;\\n}\\n\\n.challenge-card[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-4px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.challenge-header[_ngcontent-%COMP%] {\\n  padding: 24px;\\n  text-align: center;\\n  color: white;\\n  position: relative;\\n}\\n\\n.challenge-icon[_ngcontent-%COMP%] {\\n  font-size: 3rem !important;\\n  width: 3rem !important;\\n  height: 3rem !important;\\n  margin-bottom: 12px;\\n}\\n\\n.challenge-header[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0;\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n}\\n\\n.challenge-description[_ngcontent-%COMP%] {\\n  margin: 0 0 20px 0;\\n  color: #4a5568;\\n  line-height: 1.5;\\n}\\n\\n.challenge-progress[_ngcontent-%COMP%] {\\n  margin-bottom: 16px;\\n}\\n\\n.progress-bar[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 8px;\\n  background: #e2e8f0;\\n  border-radius: 4px;\\n  overflow: hidden;\\n  margin-bottom: 8px;\\n}\\n\\n.progress-fill[_ngcontent-%COMP%] {\\n  height: 100%;\\n  background: linear-gradient(90deg, #667eea, #764ba2);\\n  border-radius: 4px;\\n  transition: width 1s ease-in-out;\\n}\\n\\n.progress-text[_ngcontent-%COMP%] {\\n  text-align: center;\\n  font-size: 0.9rem;\\n  color: #4a5568;\\n  font-weight: 600;\\n}\\n\\n.challenge-reward[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  margin-bottom: 20px;\\n  padding: 12px;\\n  background: #f7fafc;\\n  border-radius: 8px;\\n  font-size: 0.9rem;\\n  color: #2d3748;\\n}\\n\\n\\n\\n.comparison-section[_ngcontent-%COMP%] {\\n  margin-bottom: 48px;\\n}\\n\\n.comparison-card[_ngcontent-%COMP%] {\\n  border-radius: 16px;\\n  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);\\n}\\n\\n.comparison-header[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  justify-content: space-between;\\n  margin-bottom: 32px;\\n  padding: 0 20px;\\n}\\n\\n.city-score[_ngcontent-%COMP%] {\\n  text-align: center;\\n  flex: 1;\\n}\\n\\n.city-score.monastir[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n\\n.city-score.sousse[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  color: #f093fb;\\n}\\n\\n.city-score[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n}\\n\\n.city-score[_ngcontent-%COMP%]   .score[_ngcontent-%COMP%] {\\n  font-size: 3rem;\\n  font-weight: 800;\\n  margin-bottom: 8px;\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.city-score.sousse[_ngcontent-%COMP%]   .score[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n  -webkit-background-clip: text;\\n  -webkit-text-fill-color: transparent;\\n  background-clip: text;\\n}\\n\\n.city-score[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #718096;\\n  font-size: 0.9rem;\\n}\\n\\n.vs-divider[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 0 20px;\\n}\\n\\n.vs-divider[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  font-size: 2rem !important;\\n  width: 2rem !important;\\n  height: 2rem !important;\\n  color: #667eea;\\n}\\n\\n.vs-divider[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n  font-weight: 700;\\n  color: #2d3748;\\n  font-size: 1.2rem;\\n}\\n\\n.comparison-stats[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 20px;\\n}\\n\\n.stat-comparison[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 8px;\\n}\\n\\n.stat-name[_ngcontent-%COMP%] {\\n  font-weight: 600;\\n  color: #2d3748;\\n  font-size: 0.9rem;\\n  text-align: center;\\n}\\n\\n.stat-bars[_ngcontent-%COMP%] {\\n  display: flex;\\n  gap: 8px;\\n  align-items: center;\\n}\\n\\n.stat-bar[_ngcontent-%COMP%] {\\n  height: 24px;\\n  border-radius: 12px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  color: white;\\n  font-size: 0.8rem;\\n  font-weight: 600;\\n  min-width: 60px;\\n  transition: all 0.3s ease;\\n}\\n\\n.stat-bar.monastir[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea, #764ba2);\\n}\\n\\n.stat-bar.sousse[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #f093fb, #f5576c);\\n}\\n\\n\\n\\n@keyframes _ngcontent-%COMP%_fadeIn {\\n  from {\\n    opacity: 0;\\n    transform: translateY(20px);\\n  }\\n  to {\\n    opacity: 1;\\n    transform: translateY(0);\\n  }\\n}\\n\\n@keyframes _ngcontent-%COMP%_pulse {\\n  0%, 100% {\\n    transform: scale(1);\\n  }\\n  50% {\\n    transform: scale(1.1);\\n  }\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .dashboard-container[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .hero-section[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 24px;\\n    text-align: center;\\n    padding: 40px 24px;\\n  }\\n\\n  .hero-title[_ngcontent-%COMP%] {\\n    font-size: 2rem;\\n  }\\n\\n  .hero-stats[_ngcontent-%COMP%] {\\n    justify-content: center;\\n    gap: 20px;\\n  }\\n\\n  .impact-grid[_ngcontent-%COMP%], .partners-grid[_ngcontent-%COMP%], .actions-grid[_ngcontent-%COMP%], .challenges-grid[_ngcontent-%COMP%] {\\n    grid-template-columns: 1fr;\\n  }\\n\\n  .events-scroll[_ngcontent-%COMP%] {\\n    padding-left: 16px;\\n  }\\n\\n  .comparison-header[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 20px;\\n  }\\n\\n  .vs-divider[_ngcontent-%COMP%] {\\n    order: -1;\\n  }\\n\\n  .stat-bars[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 4px;\\n  }\\n\\n  .stat-bar[_ngcontent-%COMP%] {\\n    width: 100%;\\n    justify-content: space-between;\\n    padding: 0 12px;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .hero-stats[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 16px;\\n  }\\n\\n  .section-title[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n\\n  .action-btn[_ngcontent-%COMP%] {\\n    flex-direction: column;\\n    gap: 8px;\\n    text-align: center;\\n  }\\n\\n  .action-btn[_ngcontent-%COMP%]   span[_ngcontent-%COMP%] {\\n    margin-left: 0;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbImRhc2hib2FyZC5jb21wb25lbnQuY3NzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLHdCQUF3QjtBQUN4QjtFQUNFLGlCQUFpQjtFQUNqQixjQUFjO0VBQ2QsYUFBYTtFQUNiLG1EQUFtRDtFQUNuRCw2REFBNkQ7RUFDN0QsaUJBQWlCO0FBQ25COztBQUVBLGlCQUFpQjtBQUNqQjtFQUNFLGFBQWE7RUFDYiw4QkFBOEI7RUFDOUIsbUJBQW1CO0VBQ25CLG1CQUFtQjtFQUNuQixrQkFBa0I7RUFDbEIsbUJBQW1CO0VBQ25CLFlBQVk7RUFDWixrQkFBa0I7RUFDbEIsZ0JBQWdCO0VBQ2hCLDJDQUEyQztFQUMzQyxtQ0FBMkI7VUFBM0IsMkJBQTJCO0VBQzNCLDBDQUEwQztBQUM1Qzs7QUFFQTtFQUNFLDZEQUE2RDtBQUMvRDs7QUFFQTtFQUNFLDZEQUE2RDtBQUMvRDs7QUFFQTtFQUNFLFdBQVc7RUFDWCxrQkFBa0I7RUFDbEIsTUFBTTtFQUNOLE9BQU87RUFDUCxRQUFRO0VBQ1IsU0FBUztFQUNUOztvRkFFa0Y7RUFDbEYsb0JBQW9CO0FBQ3RCOztBQUVBO0VBQ0Usb0JBQW9CO0VBQ3BCLG1CQUFtQjtFQUNuQixRQUFRO0VBQ1Isb0NBQW9DO0VBQ3BDLGlCQUFpQjtFQUNqQixtQkFBbUI7RUFDbkIsaUJBQWlCO0VBQ2pCLGdCQUFnQjtFQUNoQixtQkFBbUI7RUFDbkIsbUNBQTJCO1VBQTNCLDJCQUEyQjtBQUM3Qjs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixlQUFlO0VBQ2YsZ0JBQWdCO0VBQ2hCLGtDQUFrQztFQUNsQyx5Q0FBeUM7RUFDekMsb0RBQW9EO0VBQ3BELDZCQUE2QjtFQUM3QixvQ0FBb0M7RUFDcEMscUJBQXFCO0FBQ3ZCOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLGlCQUFpQjtFQUNqQixZQUFZO0VBQ1osZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLFNBQVM7QUFDWDs7QUFFQTtFQUNFLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLGNBQWM7RUFDZCxpQkFBaUI7RUFDakIsZ0JBQWdCO0VBQ2hCLGNBQWM7RUFDZCxrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsWUFBWTtFQUNaLHlCQUF5QjtFQUN6QixtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsVUFBVTtBQUNaOztBQUVBO0VBQ0UsWUFBWTtFQUNaLGFBQWE7RUFDYixxQ0FBcUM7RUFDckMsa0JBQWtCO0VBQ2xCLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLG1DQUEyQjtVQUEzQiwyQkFBMkI7RUFDM0IsMENBQTBDO0FBQzVDOztBQUVBO0VBQ0UsMEJBQTBCO0VBQzFCLHNCQUFzQjtFQUN0Qix1QkFBdUI7RUFDdkIsK0JBQStCO0FBQ2pDOztBQUVBLG1CQUFtQjtBQUNuQjtFQUNFLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLFNBQVM7RUFDVCxrQkFBa0I7RUFDbEIsY0FBYztFQUNkLGVBQWU7RUFDZixnQkFBZ0I7RUFDaEIsa0NBQWtDO0VBQ2xDLGtCQUFrQjtFQUNsQixrQkFBa0I7RUFDbEIsb0JBQW9CO0FBQ3RCOztBQUVBO0VBQ0UsV0FBVztFQUNYLGtCQUFrQjtFQUNsQixTQUFTO0VBQ1QsU0FBUztFQUNULDJCQUEyQjtFQUMzQixXQUFXO0VBQ1gsV0FBVztFQUNYLG9EQUFvRDtFQUNwRCxrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsMkRBQTJEO0VBQzNELFNBQVM7QUFDWDs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQiwwQ0FBMEM7RUFDMUMscUNBQXFDO0VBQ3JDLG1DQUEyQjtVQUEzQiwyQkFBMkI7RUFDM0IsMENBQTBDO0VBQzFDLGlEQUFpRDtFQUNqRCxnQkFBZ0I7RUFDaEIsa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsMkJBQTJCO0VBQzNCLDJDQUEyQztBQUM3Qzs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixjQUFjO0VBQ2QsaUJBQWlCO0VBQ2pCLGdCQUFnQjtFQUNoQixrQkFBa0I7QUFDcEI7O0FBRUEsdUJBQXVCO0FBQ3ZCO0VBQ0Usa0JBQWtCO0VBQ2xCLG1CQUFtQjtFQUNuQixZQUFZO0VBQ1osYUFBYTtBQUNmOztBQUVBO0VBQ0UseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UsVUFBVTtFQUNWLGVBQWU7RUFDZixlQUFlO0FBQ2pCOztBQUVBO0VBQ0UsVUFBVTtFQUNWLGVBQWU7RUFDZixlQUFlO0VBQ2YscUJBQXFCO0VBQ3JCLDJDQUEyQztBQUM3Qzs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixRQUFRO0VBQ1IsU0FBUztFQUNULGdDQUFnQztFQUNoQyxrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxjQUFjO0VBQ2QsaUJBQWlCO0VBQ2pCLGdCQUFnQjtFQUNoQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLFFBQVE7QUFDVjs7QUFFQTtFQUNFLGFBQWE7RUFDYiw4QkFBOEI7RUFDOUIsY0FBYztFQUNkLGdDQUFnQztBQUNsQzs7QUFFQTtFQUNFLG1CQUFtQjtBQUNyQjs7QUFFQSx3QkFBd0I7QUFDeEI7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLFNBQVM7RUFDVCxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFNBQVM7RUFDVCxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLG1CQUFtQjtFQUNuQix5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsMEJBQTBCO0FBQzVCOztBQUVBO0VBQ0UsV0FBVztFQUNYLFlBQVk7RUFDWixrQkFBa0I7RUFDbEIscURBQXFEO0VBQ3JELGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsdUJBQXVCO0VBQ3ZCLFlBQVk7RUFDWixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsT0FBTztBQUNUOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLGlCQUFpQjtFQUNqQixjQUFjO0VBQ2QsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxxREFBcUQ7RUFDckQsWUFBWTtFQUNaLGdCQUFnQjtFQUNoQixtQkFBbUI7RUFDbkIsaUJBQWlCO0VBQ2pCLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLFdBQVc7RUFDWCxlQUFlO0FBQ2pCOztBQUVBLHFCQUFxQjtBQUNyQjtFQUNFLGFBQWE7RUFDYixzQkFBc0I7RUFDdEIsUUFBUTtFQUNSLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsU0FBUztFQUNULGFBQWE7RUFDYixtQkFBbUI7RUFDbkIseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0Usc0ZBQXNGO0VBQ3RGLDBDQUEwQztBQUM1Qzs7QUFFQTtFQUNFLFdBQVc7RUFDWCxZQUFZO0VBQ1osYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsZ0JBQWdCO0VBQ2hCLGtCQUFrQjtFQUNsQixtQkFBbUI7RUFDbkIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLDRCQUE0QjtFQUM1Qix3QkFBd0I7RUFDeEIseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0UsaUJBQWlCO0FBQ25COztBQUVBO0VBQ0UsT0FBTztBQUNUOztBQUVBO0VBQ0UsY0FBYztFQUNkLGdCQUFnQjtFQUNoQixjQUFjO0VBQ2QsaUJBQWlCO0FBQ25COztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxnQkFBZ0I7RUFDaEIsY0FBYztFQUNkLGlCQUFpQjtBQUNuQjs7QUFFQSxtQkFBbUI7QUFDbkI7RUFDRSxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsU0FBUztFQUNULGdCQUFnQjtFQUNoQixvQkFBb0I7RUFDcEIsdUJBQXVCO0FBQ3pCOztBQUVBO0VBQ0UsV0FBVztBQUNiOztBQUVBO0VBQ0UsbUJBQW1CO0VBQ25CLGtCQUFrQjtBQUNwQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxnQkFBZ0I7RUFDaEIsbUJBQW1CO0VBQ25CLGdCQUFnQjtFQUNoQixlQUFlO0VBQ2YseUJBQXlCO0VBQ3pCLHlDQUF5QztBQUMzQzs7QUFFQTtFQUNFLDJCQUEyQjtFQUMzQiwwQ0FBMEM7QUFDNUM7O0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLDJCQUEyQjtFQUMzQixrQkFBa0I7RUFDbEIseUJBQXlCO0FBQzNCOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLFNBQVM7RUFDVCxXQUFXO0VBQ1gsaUJBQWlCO0VBQ2pCLG1CQUFtQjtFQUNuQixpQkFBaUI7RUFDakIsZ0JBQWdCO0VBQ2hCLFlBQVk7RUFDWix5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSxxREFBcUQ7QUFDdkQ7O0FBRUE7RUFDRSxxREFBcUQ7QUFDdkQ7O0FBRUE7RUFDRSxxREFBcUQ7QUFDdkQ7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsY0FBYztFQUNkLGlCQUFpQjtFQUNqQixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsY0FBYztFQUNkLGlCQUFpQjtFQUNqQixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLFFBQVE7RUFDUixtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFFBQVE7RUFDUixpQkFBaUI7RUFDakIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLDBCQUEwQjtFQUMxQixzQkFBc0I7RUFDdEIsdUJBQXVCO0VBQ3ZCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLGlCQUFpQjtBQUNuQjs7QUFFQSxxQkFBcUI7QUFDckI7RUFDRSxtQkFBbUI7QUFDckI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsMkRBQTJEO0VBQzNELFNBQVM7QUFDWDs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQixlQUFlO0VBQ2YseUJBQXlCO0VBQ3pCLHlDQUF5QztBQUMzQzs7QUFFQTtFQUNFLDJCQUEyQjtFQUMzQiwwQ0FBMEM7QUFDNUM7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFNBQVM7RUFDVCxvQkFBb0I7QUFDdEI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsWUFBWTtFQUNaLG1CQUFtQjtFQUNuQixpQkFBaUI7RUFDakIsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLGNBQWM7RUFDZCxpQkFBaUI7RUFDakIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLGNBQWM7RUFDZCxpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLFFBQVE7RUFDUixpQkFBaUI7RUFDakIsY0FBYztBQUNoQjs7QUFFQTtFQUNFLDBCQUEwQjtFQUMxQixzQkFBc0I7RUFDdEIsdUJBQXVCO0VBQ3ZCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsY0FBYztFQUNkLGdCQUFnQjtFQUNoQixzRkFBc0Y7RUFDdEYsYUFBYTtFQUNiLGtCQUFrQjtFQUNsQixpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsUUFBUTtBQUNWOztBQUVBO0VBQ0UsT0FBTztFQUNQLFlBQVk7RUFDWixpQkFBaUI7QUFDbkI7O0FBRUEsZ0JBQWdCO0FBQ2hCO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLDJEQUEyRDtFQUMzRCxTQUFTO0FBQ1g7O0FBRUE7RUFDRSxpQkFBaUI7RUFDakIsbUJBQW1CO0VBQ25CLGFBQWE7RUFDYix5Q0FBeUM7QUFDM0M7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsY0FBYztFQUNkLGlCQUFpQjtFQUNqQixnQkFBZ0I7RUFDaEIsa0JBQWtCO0VBQ2xCLG9CQUFvQjtFQUNwQixnQ0FBZ0M7QUFDbEM7O0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLFNBQVM7QUFDWDs7QUFFQTtFQUNFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsOEJBQThCO0VBQzlCLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIseUJBQXlCO0VBQ3pCLGtCQUFrQjtFQUNsQixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSwyQkFBMkI7RUFDM0IsMENBQTBDO0FBQzVDOztBQUVBO0VBQ0UsT0FBTztFQUNQLGdCQUFnQjtFQUNoQixpQkFBaUI7RUFDakIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0Usb0NBQW9DO0VBQ3BDLGNBQWM7RUFDZCxnQkFBZ0I7RUFDaEIsa0JBQWtCO0VBQ2xCLGlCQUFpQjtFQUNqQixnQkFBZ0I7QUFDbEI7O0FBRUEsdUJBQXVCO0FBQ3ZCO0VBQ0UsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLDJEQUEyRDtFQUMzRCxTQUFTO0FBQ1g7O0FBRUE7RUFDRSxtQkFBbUI7RUFDbkIsZ0JBQWdCO0VBQ2hCLHlDQUF5QztFQUN6Qyx5QkFBeUI7QUFDM0I7O0FBRUE7RUFDRSwyQkFBMkI7RUFDM0IsMENBQTBDO0FBQzVDOztBQUVBO0VBQ0UsYUFBYTtFQUNiLGtCQUFrQjtFQUNsQixZQUFZO0VBQ1osa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsMEJBQTBCO0VBQzFCLHNCQUFzQjtFQUN0Qix1QkFBdUI7RUFDdkIsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsU0FBUztFQUNULGlCQUFpQjtFQUNqQixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsY0FBYztFQUNkLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLFdBQVc7RUFDWCxXQUFXO0VBQ1gsbUJBQW1CO0VBQ25CLGtCQUFrQjtFQUNsQixnQkFBZ0I7RUFDaEIsa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsWUFBWTtFQUNaLG9EQUFvRDtFQUNwRCxrQkFBa0I7RUFDbEIsZ0NBQWdDO0FBQ2xDOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLGlCQUFpQjtFQUNqQixjQUFjO0VBQ2QsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQixRQUFRO0VBQ1IsbUJBQW1CO0VBQ25CLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsa0JBQWtCO0VBQ2xCLGlCQUFpQjtFQUNqQixjQUFjO0FBQ2hCOztBQUVBLDRCQUE0QjtBQUM1QjtFQUNFLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLG1CQUFtQjtFQUNuQix5Q0FBeUM7QUFDM0M7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsbUJBQW1CO0VBQ25CLDhCQUE4QjtFQUM5QixtQkFBbUI7RUFDbkIsZUFBZTtBQUNqQjs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixPQUFPO0FBQ1Q7O0FBRUE7RUFDRSxjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsY0FBYztBQUNoQjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixpQkFBaUI7RUFDakIsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsZUFBZTtFQUNmLGdCQUFnQjtFQUNoQixrQkFBa0I7RUFDbEIscURBQXFEO0VBQ3JELDZCQUE2QjtFQUM3QixvQ0FBb0M7RUFDcEMscUJBQXFCO0FBQ3ZCOztBQUVBO0VBQ0UscURBQXFEO0VBQ3JELDZCQUE2QjtFQUM3QixvQ0FBb0M7RUFDcEMscUJBQXFCO0FBQ3ZCOztBQUVBO0VBQ0UsU0FBUztFQUNULGNBQWM7RUFDZCxpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLG1CQUFtQjtFQUNuQixRQUFRO0VBQ1IsZUFBZTtBQUNqQjs7QUFFQTtFQUNFLDBCQUEwQjtFQUMxQixzQkFBc0I7RUFDdEIsdUJBQXVCO0VBQ3ZCLGNBQWM7QUFDaEI7O0FBRUE7RUFDRSxnQkFBZ0I7RUFDaEIsY0FBYztFQUNkLGlCQUFpQjtBQUNuQjs7QUFFQTtFQUNFLGFBQWE7RUFDYixzQkFBc0I7RUFDdEIsU0FBUztBQUNYOztBQUVBO0VBQ0UsYUFBYTtFQUNiLHNCQUFzQjtFQUN0QixRQUFRO0FBQ1Y7O0FBRUE7RUFDRSxnQkFBZ0I7RUFDaEIsY0FBYztFQUNkLGlCQUFpQjtFQUNqQixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsUUFBUTtFQUNSLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLFlBQVk7RUFDWixtQkFBbUI7RUFDbkIsYUFBYTtFQUNiLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsWUFBWTtFQUNaLGlCQUFpQjtFQUNqQixnQkFBZ0I7RUFDaEIsZUFBZTtFQUNmLHlCQUF5QjtBQUMzQjs7QUFFQTtFQUNFLHFEQUFxRDtBQUN2RDs7QUFFQTtFQUNFLHFEQUFxRDtBQUN2RDs7QUFFQSxlQUFlO0FBQ2Y7RUFDRTtJQUNFLFVBQVU7SUFDViwyQkFBMkI7RUFDN0I7RUFDQTtJQUNFLFVBQVU7SUFDVix3QkFBd0I7RUFDMUI7QUFDRjs7QUFFQTtFQUNFO0lBQ0UsbUJBQW1CO0VBQ3JCO0VBQ0E7SUFDRSxxQkFBcUI7RUFDdkI7QUFDRjs7QUFFQSxzQkFBc0I7QUFDdEI7RUFDRTtJQUNFLGFBQWE7RUFDZjs7RUFFQTtJQUNFLHNCQUFzQjtJQUN0QixTQUFTO0lBQ1Qsa0JBQWtCO0lBQ2xCLGtCQUFrQjtFQUNwQjs7RUFFQTtJQUNFLGVBQWU7RUFDakI7O0VBRUE7SUFDRSx1QkFBdUI7SUFDdkIsU0FBUztFQUNYOztFQUVBOzs7O0lBSUUsMEJBQTBCO0VBQzVCOztFQUVBO0lBQ0Usa0JBQWtCO0VBQ3BCOztFQUVBO0lBQ0Usc0JBQXNCO0lBQ3RCLFNBQVM7RUFDWDs7RUFFQTtJQUNFLFNBQVM7RUFDWDs7RUFFQTtJQUNFLHNCQUFzQjtJQUN0QixRQUFRO0VBQ1Y7O0VBRUE7SUFDRSxXQUFXO0lBQ1gsOEJBQThCO0lBQzlCLGVBQWU7RUFDakI7QUFDRjs7QUFFQTtFQUNFO0lBQ0Usc0JBQXNCO0lBQ3RCLFNBQVM7RUFDWDs7RUFFQTtJQUNFLGlCQUFpQjtFQUNuQjs7RUFFQTtJQUNFLHNCQUFzQjtJQUN0QixRQUFRO0lBQ1Isa0JBQWtCO0VBQ3BCOztFQUVBO0lBQ0UsY0FBYztFQUNoQjtBQUNGIiwiZmlsZSI6ImRhc2hib2FyZC5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLyogRGFzaGJvYXJkIENvbnRhaW5lciAqL1xuLmRhc2hib2FyZC1jb250YWluZXIge1xuICBtYXgtd2lkdGg6IDE0MDBweDtcbiAgbWFyZ2luOiAwIGF1dG87XG4gIHBhZGRpbmc6IDIwcHg7XG4gIGFuaW1hdGlvbjogZmFkZUluIDAuNnMgY3ViaWMtYmV6aWVyKDAuNCwgMCwgMC4yLCAxKTtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2Y1ZjdmYSAwJSwgI2MzY2ZlMiAxMDAlKTtcbiAgbWluLWhlaWdodDogMTAwdmg7XG59XG5cbi8qIEhlcm8gU2VjdGlvbiAqL1xuLmhlcm8tc2VjdGlvbiB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogNDBweDtcbiAgcGFkZGluZzogNjBweCA0MHB4O1xuICBib3JkZXItcmFkaXVzOiAzMnB4O1xuICBjb2xvcjogd2hpdGU7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgYm94LXNoYWRvdzogMCAyNXB4IDUwcHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSk7XG59XG5cbi5oZXJvLXNlY3Rpb24uY2l0eS1tb25hc3RpciB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEgMCUsICM3NjRiYTIgMTAwJSk7XG59XG5cbi5oZXJvLXNlY3Rpb24uY2l0eS1zb3Vzc2Uge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjZjA5M2ZiIDAlLCAjZjU1NzZjIDEwMCUpO1xufVxuXG4uaGVyby1zZWN0aW9uOjpiZWZvcmUge1xuICBjb250ZW50OiAnJztcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB0b3A6IDA7XG4gIGxlZnQ6IDA7XG4gIHJpZ2h0OiAwO1xuICBib3R0b206IDA7XG4gIGJhY2tncm91bmQ6XG4gICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCAyMCUgODAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgMCUsIHRyYW5zcGFyZW50IDUwJSksXG4gICAgcmFkaWFsLWdyYWRpZW50KGNpcmNsZSBhdCA4MCUgMjAlLCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMSkgMCUsIHRyYW5zcGFyZW50IDUwJSk7XG4gIHBvaW50ZXItZXZlbnRzOiBub25lO1xufVxuXG4uY2l0eS1iYWRnZSB7XG4gIGRpc3BsYXk6IGlubGluZS1mbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDhweDtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xuICBwYWRkaW5nOiA4cHggMTZweDtcbiAgYm9yZGVyLXJhZGl1czogMjBweDtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIG1hcmdpbi1ib3R0b206IDE2cHg7XG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcbn1cblxuLmhlcm8tdGl0bGUge1xuICBtYXJnaW46IDAgMCAxMnB4IDA7XG4gIGZvbnQtc2l6ZTogM3JlbTtcbiAgZm9udC13ZWlnaHQ6IDgwMDtcbiAgZm9udC1mYW1pbHk6ICdQb3BwaW5zJywgc2Fucy1zZXJpZjtcbiAgdGV4dC1zaGFkb3c6IDAgNHB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMik7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCg0NWRlZywgI2ZmZmZmZiwgI2YwZjBmMCk7XG4gIC13ZWJraXQtYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xuICAtd2Via2l0LXRleHQtZmlsbC1jb2xvcjogdHJhbnNwYXJlbnQ7XG4gIGJhY2tncm91bmQtY2xpcDogdGV4dDtcbn1cblxuLmhlcm8tc3VidGl0bGUge1xuICBtYXJnaW46IDAgMCAzMnB4IDA7XG4gIGZvbnQtc2l6ZTogMS4ycmVtO1xuICBvcGFjaXR5OiAwLjk7XG4gIGZvbnQtd2VpZ2h0OiA0MDA7XG59XG5cbi5oZXJvLXN0YXRzIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZ2FwOiAzMnB4O1xufVxuXG4uaGVyby1zdGF0IHtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xufVxuXG4uc3RhdC1udW1iZXIge1xuICBkaXNwbGF5OiBibG9jaztcbiAgZm9udC1zaXplOiAyLjVyZW07XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGxpbmUtaGVpZ2h0OiAxO1xuICBtYXJnaW4tYm90dG9tOiA0cHg7XG59XG5cbi5zdGF0LWxhYmVsIHtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIG9wYWNpdHk6IDAuODtcbiAgdGV4dC10cmFuc2Zvcm06IHVwcGVyY2FzZTtcbiAgbGV0dGVyLXNwYWNpbmc6IDFweDtcbn1cblxuLmhlcm8tdmlzdWFsIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB6LWluZGV4OiAxO1xufVxuXG4uY2l0eS1pbGx1c3RyYXRpb24ge1xuICB3aWR0aDogMTIwcHg7XG4gIGhlaWdodDogMTIwcHg7XG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC4xNSk7XG4gIGJvcmRlci1yYWRpdXM6IDUwJTtcbiAgZGlzcGxheTogZmxleDtcbiAgYWxpZ24taXRlbXM6IGNlbnRlcjtcbiAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gIGJhY2tkcm9wLWZpbHRlcjogYmx1cigxMHB4KTtcbiAgYm9yZGVyOiAycHggc29saWQgcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjIpO1xufVxuXG4uY2l0eS1pY29uIHtcbiAgZm9udC1zaXplOiA0cmVtICFpbXBvcnRhbnQ7XG4gIHdpZHRoOiA0cmVtICFpbXBvcnRhbnQ7XG4gIGhlaWdodDogNHJlbSAhaW1wb3J0YW50O1xuICBjb2xvcjogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjkpO1xufVxuXG4vKiBJbXBhY3QgU2VjdGlvbiAqL1xuLmltcGFjdC1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogNDhweDtcbn1cblxuLnNlY3Rpb24tdGl0bGUge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgZ2FwOiAxMnB4O1xuICBtYXJnaW46IDAgMCAzMnB4IDA7XG4gIGNvbG9yOiAjMmQzNzQ4O1xuICBmb250LXNpemU6IDJyZW07XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGZvbnQtZmFtaWx5OiAnUG9wcGlucycsIHNhbnMtc2VyaWY7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBwYWRkaW5nLWJvdHRvbTogMTZweDtcbn1cblxuLnNlY3Rpb24tdGl0bGU6OmFmdGVyIHtcbiAgY29udGVudDogJyc7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgYm90dG9tOiAwO1xuICBsZWZ0OiA1MCU7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWCgtNTAlKTtcbiAgd2lkdGg6IDgwcHg7XG4gIGhlaWdodDogNHB4O1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICM2NjdlZWEsICM3NjRiYTIpO1xuICBib3JkZXItcmFkaXVzOiAycHg7XG59XG5cbi5pbXBhY3QtZ3JpZCB7XG4gIGRpc3BsYXk6IGdyaWQ7XG4gIGdyaWQtdGVtcGxhdGUtY29sdW1uczogcmVwZWF0KGF1dG8tZml0LCBtaW5tYXgoMzUwcHgsIDFmcikpO1xuICBnYXA6IDI0cHg7XG59XG5cbi5pbXBhY3QtY2FyZCB7XG4gIGJvcmRlci1yYWRpdXM6IDIwcHg7XG4gIGJveC1zaGFkb3c6IDAgMTBweCAzMHB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgYmFja2dyb3VuZDogcmdiYSgyNTUsIDI1NSwgMjU1LCAwLjk1KTtcbiAgYmFja2Ryb3AtZmlsdGVyOiBibHVyKDIwcHgpO1xuICBib3JkZXI6IDFweCBzb2xpZCByZ2JhKDI1NSwgMjU1LCAyNTUsIDAuMik7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XG4gIG92ZXJmbG93OiBoaWRkZW47XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cblxuLmltcGFjdC1jYXJkOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC04cHgpO1xuICBib3gtc2hhZG93OiAwIDIwcHggNDBweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xufVxuXG4uaW1wYWN0LWNhcmQgaDMge1xuICBtYXJnaW46IDAgMCAyMHB4IDA7XG4gIGNvbG9yOiAjMmQzNzQ4O1xuICBmb250LXNpemU6IDEuM3JlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xufVxuXG4vKiBDaXR5IFByb2dyZXNzIENhcmQgKi9cbi5jaXR5LXByb2dyZXNzIC5wcm9ncmVzcy1yaW5nIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICBtYXJnaW46IDAgYXV0byAyNHB4O1xuICB3aWR0aDogMTIwcHg7XG4gIGhlaWdodDogMTIwcHg7XG59XG5cbi5wcm9ncmVzcy1zdmcge1xuICB0cmFuc2Zvcm06IHJvdGF0ZSgtOTBkZWcpO1xufVxuXG4ucHJvZ3Jlc3MtYmcge1xuICBmaWxsOiBub25lO1xuICBzdHJva2U6ICNlMmU4ZjA7XG4gIHN0cm9rZS13aWR0aDogODtcbn1cblxuLnByb2dyZXNzLWZpbGwge1xuICBmaWxsOiBub25lO1xuICBzdHJva2U6ICM2NjdlZWE7XG4gIHN0cm9rZS13aWR0aDogODtcbiAgc3Ryb2tlLWxpbmVjYXA6IHJvdW5kO1xuICB0cmFuc2l0aW9uOiBzdHJva2UtZGFzaGFycmF5IDFzIGVhc2UtaW4tb3V0O1xufVxuXG4ucHJvZ3Jlc3MtdGV4dCB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiA1MCU7XG4gIGxlZnQ6IDUwJTtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGUoLTUwJSwgLTUwJSk7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbn1cblxuLnByb2dyZXNzLXZhbHVlIHtcbiAgZGlzcGxheTogYmxvY2s7XG4gIGZvbnQtc2l6ZTogMS41cmVtO1xuICBmb250LXdlaWdodDogNzAwO1xuICBjb2xvcjogIzJkMzc0ODtcbn1cblxuLnByb2dyZXNzLWxhYmVsIHtcbiAgZm9udC1zaXplOiAwLjhyZW07XG4gIGNvbG9yOiAjNzE4MDk2O1xufVxuXG4ucHJvZ3Jlc3MtZGV0YWlscyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogOHB4O1xufVxuXG4uZGV0YWlsLWl0ZW0ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIHBhZGRpbmc6IDhweCAwO1xuICBib3JkZXItYm90dG9tOiAxcHggc29saWQgI2UyZThmMDtcbn1cblxuLmRldGFpbC1pdGVtOmxhc3QtY2hpbGQge1xuICBib3JkZXItYm90dG9tOiBub25lO1xufVxuXG4vKiBDb21tdW5pdHkgRmVlZCBDYXJkICovXG4uY29tbXVuaXR5LWZlZWQgLmZlZWQtbGlzdCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogMTJweDtcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcbn1cblxuLmZlZWQtaXRlbSB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogMTJweDtcbiAgcGFkZGluZzogMTJweDtcbiAgYmFja2dyb3VuZDogI2Y3ZmFmYztcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgdHJhbnNpdGlvbjogYWxsIDAuMnMgZWFzZTtcbn1cblxuLmZlZWQtaXRlbTpob3ZlciB7XG4gIGJhY2tncm91bmQ6ICNlZGYyZjc7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWCg0cHgpO1xufVxuXG4uZmVlZC1hdmF0YXIge1xuICB3aWR0aDogNDBweDtcbiAgaGVpZ2h0OiA0MHB4O1xuICBib3JkZXItcmFkaXVzOiA1MCU7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEsICM3NjRiYTIpO1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgY29sb3I6IHdoaXRlO1xuICBmbGV4LXNocmluazogMDtcbn1cblxuLmZlZWQtY29udGVudCB7XG4gIGZsZXg6IDE7XG59XG5cbi5mZWVkLXRleHQge1xuICBtYXJnaW46IDAgMCA0cHggMDtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIGNvbG9yOiAjMmQzNzQ4O1xuICBmb250LXdlaWdodDogNTAwO1xufVxuXG4uZmVlZC10aW1lIHtcbiAgZm9udC1zaXplOiAwLjhyZW07XG4gIGNvbG9yOiAjNzE4MDk2O1xufVxuXG4uZmVlZC1wb2ludHMge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCAjNDhiYjc4LCAjMzhhMTY5KTtcbiAgY29sb3I6IHdoaXRlO1xuICBwYWRkaW5nOiA0cHggOHB4O1xuICBib3JkZXItcmFkaXVzOiAxMnB4O1xuICBmb250LXNpemU6IDAuOHJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbn1cblxuLnZpZXctYWxsLWJ0biB7XG4gIHdpZHRoOiAxMDAlO1xuICBtYXJnaW4tdG9wOiA4cHg7XG59XG5cbi8qIExlYWRlcmJvYXJkIENhcmQgKi9cbi5sZWFkZXJib2FyZCAubGVhZGVyYm9hcmQtbGlzdCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogOHB4O1xuICBtYXJnaW4tYm90dG9tOiAxNnB4O1xufVxuXG4ubGVhZGVyLWl0ZW0ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDEycHg7XG4gIHBhZGRpbmc6IDEycHg7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIHRyYW5zaXRpb246IGFsbCAwLjJzIGVhc2U7XG59XG5cbi5sZWFkZXItaXRlbTpob3ZlciB7XG4gIGJhY2tncm91bmQ6ICNmN2ZhZmM7XG59XG5cbi5sZWFkZXItaXRlbS5jdXJyZW50LXVzZXIge1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoMTM1ZGVnLCByZ2JhKDEwMiwgMTI2LCAyMzQsIDAuMSksIHJnYmEoMTE4LCA3NSwgMTYyLCAwLjEpKTtcbiAgYm9yZGVyOiAycHggc29saWQgcmdiYSgxMDIsIDEyNiwgMjM0LCAwLjMpO1xufVxuXG4ubGVhZGVyLXJhbmsge1xuICB3aWR0aDogMzJweDtcbiAgaGVpZ2h0OiAzMnB4O1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgZm9udC13ZWlnaHQ6IDcwMDtcbiAgYm9yZGVyLXJhZGl1czogNTAlO1xuICBiYWNrZ3JvdW5kOiAjZTJlOGYwO1xuICBjb2xvcjogIzRhNTU2ODtcbn1cblxuLnRyb3BoeS1pY29uIHtcbiAgZm9udC1zaXplOiAxLjVyZW0gIWltcG9ydGFudDtcbiAgd2lkdGg6IDEuNXJlbSAhaW1wb3J0YW50O1xuICBoZWlnaHQ6IDEuNXJlbSAhaW1wb3J0YW50O1xufVxuXG4ucmFuay1udW1iZXIge1xuICBmb250LXNpemU6IDAuOXJlbTtcbn1cblxuLmxlYWRlci1pbmZvIHtcbiAgZmxleDogMTtcbn1cblxuLmxlYWRlci1uYW1lIHtcbiAgZGlzcGxheTogYmxvY2s7XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIGNvbG9yOiAjMmQzNzQ4O1xuICBmb250LXNpemU6IDAuOXJlbTtcbn1cblxuLmxlYWRlci1jaXR5IHtcbiAgZm9udC1zaXplOiAwLjhyZW07XG4gIGNvbG9yOiAjNzE4MDk2O1xufVxuXG4ubGVhZGVyLXBvaW50cyB7XG4gIGZvbnQtd2VpZ2h0OiA3MDA7XG4gIGNvbG9yOiAjNjY3ZWVhO1xuICBmb250LXNpemU6IDAuOXJlbTtcbn1cblxuLyogRXZlbnRzIFNlY3Rpb24gKi9cbi5ldmVudHMtc2VjdGlvbiB7XG4gIG1hcmdpbi1ib3R0b206IDQ4cHg7XG59XG5cbi5ldmVudHMtY2Fyb3VzZWwge1xuICBvdmVyZmxvdzogaGlkZGVuO1xufVxuXG4uZXZlbnRzLXNjcm9sbCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogMjRweDtcbiAgb3ZlcmZsb3cteDogYXV0bztcbiAgcGFkZGluZy1ib3R0b206IDE2cHg7XG4gIHNjcm9sbC1iZWhhdmlvcjogc21vb3RoO1xufVxuXG4uZXZlbnRzLXNjcm9sbDo6LXdlYmtpdC1zY3JvbGxiYXIge1xuICBoZWlnaHQ6IDhweDtcbn1cblxuLmV2ZW50cy1zY3JvbGw6Oi13ZWJraXQtc2Nyb2xsYmFyLXRyYWNrIHtcbiAgYmFja2dyb3VuZDogI2YxZjFmMTtcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xufVxuXG4uZXZlbnRzLXNjcm9sbDo6LXdlYmtpdC1zY3JvbGxiYXItdGh1bWIge1xuICBiYWNrZ3JvdW5kOiAjNjY3ZWVhO1xuICBib3JkZXItcmFkaXVzOiA0cHg7XG59XG5cbi5ldmVudC1jYXJkIHtcbiAgbWluLXdpZHRoOiAzMjBweDtcbiAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbn1cblxuLmV2ZW50LWNhcmQ6aG92ZXIge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTRweCk7XG4gIGJveC1zaGFkb3c6IDAgOHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcbn1cblxuLmV2ZW50LWltYWdlIHtcbiAgaGVpZ2h0OiAxNjBweDtcbiAgYmFja2dyb3VuZC1zaXplOiBjb3ZlcjtcbiAgYmFja2dyb3VuZC1wb3NpdGlvbjogY2VudGVyO1xuICBwb3NpdGlvbjogcmVsYXRpdmU7XG4gIGJhY2tncm91bmQtY29sb3I6ICNlMmU4ZjA7XG59XG5cbi5ldmVudC1iYWRnZSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAxMnB4O1xuICByaWdodDogMTJweDtcbiAgcGFkZGluZzogNHB4IDEycHg7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICBjb2xvcjogd2hpdGU7XG4gIHRleHQtdHJhbnNmb3JtOiB1cHBlcmNhc2U7XG59XG5cbi5iYWRnZS1lbnZpcm9ubmVtZW50IHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzQ4YmI3OCwgIzM4YTE2OSk7XG59XG5cbi5iYWRnZS1jdWx0dXJlIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2VkODkzNiwgI2RkNmIyMCk7XG59XG5cbi5iYWRnZS3DqWR1Y2F0aW9uIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzQyOTllMSwgIzMxODJjZSk7XG59XG5cbi5ldmVudC1jYXJkIGg0IHtcbiAgbWFyZ2luOiAwIDAgOHB4IDA7XG4gIGNvbG9yOiAjMmQzNzQ4O1xuICBmb250LXNpemU6IDEuMXJlbTtcbiAgZm9udC13ZWlnaHQ6IDYwMDtcbn1cblxuLmV2ZW50LWRlc2NyaXB0aW9uIHtcbiAgbWFyZ2luOiAwIDAgMTZweCAwO1xuICBjb2xvcjogIzcxODA5NjtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIGxpbmUtaGVpZ2h0OiAxLjQ7XG59XG5cbi5ldmVudC1kZXRhaWxzIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiA4cHg7XG4gIG1hcmdpbi1ib3R0b206IDE2cHg7XG59XG5cbi5ldmVudC1kZXRhaWwge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDhweDtcbiAgZm9udC1zaXplOiAwLjhyZW07XG4gIGNvbG9yOiAjNGE1NTY4O1xufVxuXG4uZXZlbnQtZGV0YWlsIG1hdC1pY29uIHtcbiAgZm9udC1zaXplOiAxcmVtICFpbXBvcnRhbnQ7XG4gIHdpZHRoOiAxcmVtICFpbXBvcnRhbnQ7XG4gIGhlaWdodDogMXJlbSAhaW1wb3J0YW50O1xuICBjb2xvcjogIzY2N2VlYTtcbn1cblxuLmV2ZW50LWpvaW4tYnRuIHtcbiAgd2lkdGg6IDEwMCU7XG4gIGhlaWdodDogMzZweDtcbiAgZm9udC1zaXplOiAwLjlyZW07XG59XG5cbi8qIFBhcnRuZXJzIFNlY3Rpb24gKi9cbi5wYXJ0bmVycy1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogNDhweDtcbn1cblxuLnBhcnRuZXJzLWdyaWQge1xuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDMwMHB4LCAxZnIpKTtcbiAgZ2FwOiAyNHB4O1xufVxuXG4ucGFydG5lci1jYXJkIHtcbiAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgY3Vyc29yOiBwb2ludGVyO1xuICB0cmFuc2l0aW9uOiBhbGwgMC4zcyBlYXNlO1xuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbn1cblxuLnBhcnRuZXItY2FyZDpob3ZlciB7XG4gIHRyYW5zZm9ybTogdHJhbnNsYXRlWSgtNHB4KTtcbiAgYm94LXNoYWRvdzogMCA4cHggMjVweCByZ2JhKDAsIDAsIDAsIDAuMTUpO1xufVxuXG4ucGFydG5lci1oZWFkZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDE2cHg7XG4gIHBhZGRpbmc6IDIwcHggMjBweCAwO1xufVxuXG4ucGFydG5lci1sb2dvIHtcbiAgd2lkdGg6IDYwcHg7XG4gIGhlaWdodDogNjBweDtcbiAgYm9yZGVyLXJhZGl1czogMTJweDtcbiAgb2JqZWN0LWZpdDogY292ZXI7XG4gIGJhY2tncm91bmQ6ICNmN2ZhZmM7XG59XG5cbi5wYXJ0bmVyLWluZm8gaDQge1xuICBtYXJnaW46IDAgMCA0cHggMDtcbiAgY29sb3I6ICMyZDM3NDg7XG4gIGZvbnQtc2l6ZTogMS4xcmVtO1xuICBmb250LXdlaWdodDogNjAwO1xufVxuXG4ucGFydG5lci1jYXRlZ29yeSB7XG4gIG1hcmdpbjogMCAwIDhweCAwO1xuICBjb2xvcjogIzcxODA5NjtcbiAgZm9udC1zaXplOiAwLjlyZW07XG59XG5cbi5wYXJ0bmVyLXJhdGluZyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogNHB4O1xuICBmb250LXNpemU6IDAuOHJlbTtcbiAgY29sb3I6ICM3MTgwOTY7XG59XG5cbi5zdGFyLWljb24ge1xuICBmb250LXNpemU6IDFyZW0gIWltcG9ydGFudDtcbiAgd2lkdGg6IDFyZW0gIWltcG9ydGFudDtcbiAgaGVpZ2h0OiAxcmVtICFpbXBvcnRhbnQ7XG4gIGNvbG9yOiAjZmZkNzAwO1xufVxuXG4ucGFydG5lci1vZmZlciB7XG4gIG1hcmdpbjogMCAwIDE2cHggMDtcbiAgY29sb3I6ICMyZDM3NDg7XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsIHJnYmEoMTAyLCAxMjYsIDIzNCwgMC4xKSwgcmdiYSgxMTgsIDc1LCAxNjIsIDAuMSkpO1xuICBwYWRkaW5nOiAxMnB4O1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xufVxuXG4ucGFydG5lci1hY3Rpb25zIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZ2FwOiA4cHg7XG59XG5cbi5wYXJ0bmVyLWJ0biB7XG4gIGZsZXg6IDE7XG4gIGhlaWdodDogMzZweDtcbiAgZm9udC1zaXplOiAwLjhyZW07XG59XG5cbi8qIEFjdGlvbnMgSHViICovXG4uYWN0aW9ucy1odWIge1xuICBtYXJnaW4tYm90dG9tOiA0OHB4O1xufVxuXG4uYWN0aW9ucy1ncmlkIHtcbiAgZGlzcGxheTogZ3JpZDtcbiAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiByZXBlYXQoYXV0by1maXQsIG1pbm1heCgzNTBweCwgMWZyKSk7XG4gIGdhcDogMzJweDtcbn1cblxuLmFjdGlvbi1jYXRlZ29yeSB7XG4gIGJhY2tncm91bmQ6IHdoaXRlO1xuICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICBwYWRkaW5nOiAyNHB4O1xuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbn1cblxuLmNhdGVnb3J5LXRpdGxlIHtcbiAgbWFyZ2luOiAwIDAgMjBweCAwO1xuICBjb2xvcjogIzJkMzc0ODtcbiAgZm9udC1zaXplOiAxLjJyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgcGFkZGluZy1ib3R0b206IDEycHg7XG4gIGJvcmRlci1ib3R0b206IDJweCBzb2xpZCAjZTJlOGYwO1xufVxuXG4uY2F0ZWdvcnktYWN0aW9ucyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogMTJweDtcbn1cblxuLmFjdGlvbi1idG4ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gIHBhZGRpbmc6IDE2cHg7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cblxuLmFjdGlvbi1idG46aG92ZXIge1xuICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoLTJweCk7XG4gIGJveC1zaGFkb3c6IDAgOHB4IDI1cHggcmdiYSgwLCAwLCAwLCAwLjE1KTtcbn1cblxuLmFjdGlvbi1idG4gc3BhbiB7XG4gIGZsZXg6IDE7XG4gIHRleHQtYWxpZ246IGxlZnQ7XG4gIG1hcmdpbi1sZWZ0OiAxMnB4O1xuICBmb250LXdlaWdodDogNTAwO1xufVxuXG4uYWN0aW9uLXBvaW50cyB7XG4gIGJhY2tncm91bmQ6IHJnYmEoMjU1LCAyNTUsIDI1NSwgMC45KTtcbiAgY29sb3I6ICMyZDM3NDg7XG4gIHBhZGRpbmc6IDRweCA4cHg7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgZm9udC1zaXplOiAwLjhyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG59XG5cbi8qIENoYWxsZW5nZXMgU2VjdGlvbiAqL1xuLmNoYWxsZW5nZXMtc2VjdGlvbiB7XG4gIG1hcmdpbi1ib3R0b206IDQ4cHg7XG59XG5cbi5jaGFsbGVuZ2VzLWdyaWQge1xuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDMyMHB4LCAxZnIpKTtcbiAgZ2FwOiAyNHB4O1xufVxuXG4uY2hhbGxlbmdlLWNhcmQge1xuICBib3JkZXItcmFkaXVzOiAxNnB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBib3gtc2hhZG93OiAwIDRweCAxMnB4IHJnYmEoMCwgMCwgMCwgMC4xKTtcbiAgdHJhbnNpdGlvbjogYWxsIDAuM3MgZWFzZTtcbn1cblxuLmNoYWxsZW5nZS1jYXJkOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC00cHgpO1xuICBib3gtc2hhZG93OiAwIDhweCAyNXB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XG59XG5cbi5jaGFsbGVuZ2UtaGVhZGVyIHtcbiAgcGFkZGluZzogMjRweDtcbiAgdGV4dC1hbGlnbjogY2VudGVyO1xuICBjb2xvcjogd2hpdGU7XG4gIHBvc2l0aW9uOiByZWxhdGl2ZTtcbn1cblxuLmNoYWxsZW5nZS1pY29uIHtcbiAgZm9udC1zaXplOiAzcmVtICFpbXBvcnRhbnQ7XG4gIHdpZHRoOiAzcmVtICFpbXBvcnRhbnQ7XG4gIGhlaWdodDogM3JlbSAhaW1wb3J0YW50O1xuICBtYXJnaW4tYm90dG9tOiAxMnB4O1xufVxuXG4uY2hhbGxlbmdlLWhlYWRlciBoNCB7XG4gIG1hcmdpbjogMDtcbiAgZm9udC1zaXplOiAxLjNyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG59XG5cbi5jaGFsbGVuZ2UtZGVzY3JpcHRpb24ge1xuICBtYXJnaW46IDAgMCAyMHB4IDA7XG4gIGNvbG9yOiAjNGE1NTY4O1xuICBsaW5lLWhlaWdodDogMS41O1xufVxuXG4uY2hhbGxlbmdlLXByb2dyZXNzIHtcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcbn1cblxuLnByb2dyZXNzLWJhciB7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDhweDtcbiAgYmFja2dyb3VuZDogI2UyZThmMDtcbiAgYm9yZGVyLXJhZGl1czogNHB4O1xuICBvdmVyZmxvdzogaGlkZGVuO1xuICBtYXJnaW4tYm90dG9tOiA4cHg7XG59XG5cbi5wcm9ncmVzcy1maWxsIHtcbiAgaGVpZ2h0OiAxMDAlO1xuICBiYWNrZ3JvdW5kOiBsaW5lYXItZ3JhZGllbnQoOTBkZWcsICM2NjdlZWEsICM3NjRiYTIpO1xuICBib3JkZXItcmFkaXVzOiA0cHg7XG4gIHRyYW5zaXRpb246IHdpZHRoIDFzIGVhc2UtaW4tb3V0O1xufVxuXG4ucHJvZ3Jlc3MtdGV4dCB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIGNvbG9yOiAjNGE1NTY4O1xuICBmb250LXdlaWdodDogNjAwO1xufVxuXG4uY2hhbGxlbmdlLXJld2FyZCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGdhcDogOHB4O1xuICBtYXJnaW4tYm90dG9tOiAyMHB4O1xuICBwYWRkaW5nOiAxMnB4O1xuICBiYWNrZ3JvdW5kOiAjZjdmYWZjO1xuICBib3JkZXItcmFkaXVzOiA4cHg7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xuICBjb2xvcjogIzJkMzc0ODtcbn1cblxuLyogQ2l0eSBDb21wYXJpc29uIFNlY3Rpb24gKi9cbi5jb21wYXJpc29uLXNlY3Rpb24ge1xuICBtYXJnaW4tYm90dG9tOiA0OHB4O1xufVxuXG4uY29tcGFyaXNvbi1jYXJkIHtcbiAgYm9yZGVyLXJhZGl1czogMTZweDtcbiAgYm94LXNoYWRvdzogMCA0cHggMTJweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG59XG5cbi5jb21wYXJpc29uLWhlYWRlciB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogc3BhY2UtYmV0d2VlbjtcbiAgbWFyZ2luLWJvdHRvbTogMzJweDtcbiAgcGFkZGluZzogMCAyMHB4O1xufVxuXG4uY2l0eS1zY29yZSB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgZmxleDogMTtcbn1cblxuLmNpdHktc2NvcmUubW9uYXN0aXIgaDMge1xuICBjb2xvcjogIzY2N2VlYTtcbn1cblxuLmNpdHktc2NvcmUuc291c3NlIGgzIHtcbiAgY29sb3I6ICNmMDkzZmI7XG59XG5cbi5jaXR5LXNjb3JlIGgzIHtcbiAgbWFyZ2luOiAwIDAgOHB4IDA7XG4gIGZvbnQtc2l6ZTogMS41cmVtO1xuICBmb250LXdlaWdodDogNzAwO1xufVxuXG4uY2l0eS1zY29yZSAuc2NvcmUge1xuICBmb250LXNpemU6IDNyZW07XG4gIGZvbnQtd2VpZ2h0OiA4MDA7XG4gIG1hcmdpbi1ib3R0b206IDhweDtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgIzY2N2VlYSwgIzc2NGJhMik7XG4gIC13ZWJraXQtYmFja2dyb3VuZC1jbGlwOiB0ZXh0O1xuICAtd2Via2l0LXRleHQtZmlsbC1jb2xvcjogdHJhbnNwYXJlbnQ7XG4gIGJhY2tncm91bmQtY2xpcDogdGV4dDtcbn1cblxuLmNpdHktc2NvcmUuc291c3NlIC5zY29yZSB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICNmMDkzZmIsICNmNTU3NmMpO1xuICAtd2Via2l0LWJhY2tncm91bmQtY2xpcDogdGV4dDtcbiAgLXdlYmtpdC10ZXh0LWZpbGwtY29sb3I6IHRyYW5zcGFyZW50O1xuICBiYWNrZ3JvdW5kLWNsaXA6IHRleHQ7XG59XG5cbi5jaXR5LXNjb3JlIHAge1xuICBtYXJnaW46IDA7XG4gIGNvbG9yOiAjNzE4MDk2O1xuICBmb250LXNpemU6IDAuOXJlbTtcbn1cblxuLnZzLWRpdmlkZXIge1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDhweDtcbiAgcGFkZGluZzogMCAyMHB4O1xufVxuXG4udnMtZGl2aWRlciBtYXQtaWNvbiB7XG4gIGZvbnQtc2l6ZTogMnJlbSAhaW1wb3J0YW50O1xuICB3aWR0aDogMnJlbSAhaW1wb3J0YW50O1xuICBoZWlnaHQ6IDJyZW0gIWltcG9ydGFudDtcbiAgY29sb3I6ICM2NjdlZWE7XG59XG5cbi52cy1kaXZpZGVyIHNwYW4ge1xuICBmb250LXdlaWdodDogNzAwO1xuICBjb2xvcjogIzJkMzc0ODtcbiAgZm9udC1zaXplOiAxLjJyZW07XG59XG5cbi5jb21wYXJpc29uLXN0YXRzIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiAyMHB4O1xufVxuXG4uc3RhdC1jb21wYXJpc29uIHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgZ2FwOiA4cHg7XG59XG5cbi5zdGF0LW5hbWUge1xuICBmb250LXdlaWdodDogNjAwO1xuICBjb2xvcjogIzJkMzc0ODtcbiAgZm9udC1zaXplOiAwLjlyZW07XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbn1cblxuLnN0YXQtYmFycyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGdhcDogOHB4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xufVxuXG4uc3RhdC1iYXIge1xuICBoZWlnaHQ6IDI0cHg7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBjb2xvcjogd2hpdGU7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICBtaW4td2lkdGg6IDYwcHg7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGVhc2U7XG59XG5cbi5zdGF0LWJhci5tb25hc3RpciB7XG4gIGJhY2tncm91bmQ6IGxpbmVhci1ncmFkaWVudCgxMzVkZWcsICM2NjdlZWEsICM3NjRiYTIpO1xufVxuXG4uc3RhdC1iYXIuc291c3NlIHtcbiAgYmFja2dyb3VuZDogbGluZWFyLWdyYWRpZW50KDEzNWRlZywgI2YwOTNmYiwgI2Y1NTc2Yyk7XG59XG5cbi8qIEFuaW1hdGlvbnMgKi9cbkBrZXlmcmFtZXMgZmFkZUluIHtcbiAgZnJvbSB7XG4gICAgb3BhY2l0eTogMDtcbiAgICB0cmFuc2Zvcm06IHRyYW5zbGF0ZVkoMjBweCk7XG4gIH1cbiAgdG8ge1xuICAgIG9wYWNpdHk6IDE7XG4gICAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKDApO1xuICB9XG59XG5cbkBrZXlmcmFtZXMgcHVsc2Uge1xuICAwJSwgMTAwJSB7XG4gICAgdHJhbnNmb3JtOiBzY2FsZSgxKTtcbiAgfVxuICA1MCUge1xuICAgIHRyYW5zZm9ybTogc2NhbGUoMS4xKTtcbiAgfVxufVxuXG4vKiBNb2JpbGUgUmVzcG9uc2l2ZSAqL1xuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5kYXNoYm9hcmQtY29udGFpbmVyIHtcbiAgICBwYWRkaW5nOiAxNnB4O1xuICB9XG5cbiAgLmhlcm8tc2VjdGlvbiB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBnYXA6IDI0cHg7XG4gICAgdGV4dC1hbGlnbjogY2VudGVyO1xuICAgIHBhZGRpbmc6IDQwcHggMjRweDtcbiAgfVxuXG4gIC5oZXJvLXRpdGxlIHtcbiAgICBmb250LXNpemU6IDJyZW07XG4gIH1cblxuICAuaGVyby1zdGF0cyB7XG4gICAganVzdGlmeS1jb250ZW50OiBjZW50ZXI7XG4gICAgZ2FwOiAyMHB4O1xuICB9XG5cbiAgLmltcGFjdC1ncmlkLFxuICAucGFydG5lcnMtZ3JpZCxcbiAgLmFjdGlvbnMtZ3JpZCxcbiAgLmNoYWxsZW5nZXMtZ3JpZCB7XG4gICAgZ3JpZC10ZW1wbGF0ZS1jb2x1bW5zOiAxZnI7XG4gIH1cblxuICAuZXZlbnRzLXNjcm9sbCB7XG4gICAgcGFkZGluZy1sZWZ0OiAxNnB4O1xuICB9XG5cbiAgLmNvbXBhcmlzb24taGVhZGVyIHtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGdhcDogMjBweDtcbiAgfVxuXG4gIC52cy1kaXZpZGVyIHtcbiAgICBvcmRlcjogLTE7XG4gIH1cblxuICAuc3RhdC1iYXJzIHtcbiAgICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICAgIGdhcDogNHB4O1xuICB9XG5cbiAgLnN0YXQtYmFyIHtcbiAgICB3aWR0aDogMTAwJTtcbiAgICBqdXN0aWZ5LWNvbnRlbnQ6IHNwYWNlLWJldHdlZW47XG4gICAgcGFkZGluZzogMCAxMnB4O1xuICB9XG59XG5cbkBtZWRpYSAobWF4LXdpZHRoOiA0ODBweCkge1xuICAuaGVyby1zdGF0cyB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBnYXA6IDE2cHg7XG4gIH1cblxuICAuc2VjdGlvbi10aXRsZSB7XG4gICAgZm9udC1zaXplOiAxLjVyZW07XG4gIH1cblxuICAuYWN0aW9uLWJ0biB7XG4gICAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgICBnYXA6IDhweDtcbiAgICB0ZXh0LWFsaWduOiBjZW50ZXI7XG4gIH1cblxuICAuYWN0aW9uLWJ0biBzcGFuIHtcbiAgICBtYXJnaW4tbGVmdDogMDtcbiAgfVxufVxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["UserRole", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵelement", "ɵɵtemplate", "DashboardComponent_div_0_mat_card_89_div_3_Template", "ɵɵlistener", "DashboardComponent_div_0_mat_card_89_Template_button_click_19_listener", "reward_r4", "ɵɵrestoreView", "_r3", "$implicit", "ctx_r1", "ɵɵnextContext", "ɵɵresetView", "exchangeReward", "ɵɵclassProp", "user", "points", "pointsRequired", "ɵɵadvance", "ɵɵproperty", "imageUrl", "ɵɵsanitizeUrl", "title", "ɵɵtextInterpolate", "description", "partner<PERSON>ame", "ɵɵtextInterpolate1", "action_r5", "location", "providerName", "DashboardComponent_div_0_div_112_div_1_span_11_Template", "DashboardComponent_div_0_div_112_div_1_div_12_Template", "ɵɵstyleProp", "getActionColor", "type", "getActionIcon", "ɵɵpipeBind2", "timestamp", "DashboardComponent_div_0_div_112_div_1_Template", "filteredHistory", "DashboardComponent_div_0_ng_template_113_Template_button_click_5_listener", "_r6", "openQRScanner", "week_r7", "maxWeeklyPoints", "week", "DashboardComponent_div_0_mat_card_172_Template_mat_card_click_0_listener", "partner_r9", "_r8", "viewP<PERSON>ner", "DashboardComponent_div_0_mat_card_172_mat_icon_9_Template", "logo", "name", "category", "getStars", "rating", "reviews", "currentOffer", "DashboardComponent_div_0_div_179_button_4_Template_button_click_0_listener", "action_r11", "_r10", "executeAction", "color", "icon", "DashboardComponent_div_0_div_179_button_4_Template", "category_r12", "actions", "challenge_r13", "gradient", "progress", "ɵɵtextInterpolate2", "current", "target", "reward", "stat_r14", "monastir", "monastirValue", "sousse", "sousseValue", "DashboardComponent_div_0_Template_button_click_21_listener", "_r1", "toggleNotifications", "DashboardComponent_div_0_Template_button_click_29_listener", "openProfile", "DashboardComponent_div_0_Template_button_click_34_listener", "logout", "DashboardComponent_div_0_Template_button_click_52_listener", "DashboardComponent_div_0_mat_card_89_Template", "ɵɵtwoWayListener", "DashboardComponent_div_0_Template_mat_select_valueChange_102_listener", "$event", "ɵɵtwoWayBindingSet", "<PERSON><PERSON><PERSON><PERSON>", "DashboardComponent_div_0_Template_mat_select_selectionChange_102_listener", "filterHistory", "DashboardComponent_div_0_div_112_Template", "DashboardComponent_div_0_ng_template_113_Template", "ɵɵtemplateRefExtractor", "DashboardComponent_div_0_div_165_Template", "DashboardComponent_div_0_mat_card_172_Template", "DashboardComponent_div_0_div_179_Template", "DashboardComponent_div_0_mat_card_186_Template", "DashboardComponent_div_0_div_215_Template", "city", "unreadNotifications", "profileMenu_r15", "nextReward", "image", "getPointsToNextReward", "getProgressPercentage", "getMotivationMessage", "availableRewards", "ɵɵtwoWayProperty", "length", "noHistory_r16", "monthlyStats", "pointsEarned", "pointsSpent", "scansCount", "validationsCount", "weeklyPointsData", "featuredPartners", "actionCategories", "activeChallenges", "cityComparison", "score", "participants", "comparisonStats", "DashboardComponent", "constructor", "authService", "router", "dashboardRouter", "communityFeed", "topContributors", "localEvents", "ngOnInit", "currentUser$", "subscribe", "role", "USER", "navigateToUserDashboard", "loadDashboardData", "loadCommunityFeed", "loadTopContributors", "loadLocalEvents", "loadFeaturedPartners", "loadActiveChallenges", "loadCityComparison", "id", "Date", "now", "userId", "userName", "uid", "email", "createdAt", "updatedAt", "isActive", "history", "date", "maxParticipants", "qrCode", "endDate", "getGreeting", "hour", "getHours", "getCityIcon", "getCommunityRank", "userIndex", "findIndex", "u", "getImpactScore", "Math", "floor", "getCityProgress", "cityData", "monthlyTarget", "min", "getCityProgressPercent", "round", "getCityStats", "validatedActions", "activeUsers", "getActivityColor", "colorMap", "getActivityIcon", "iconMap", "getTrophyColor", "index", "colors", "getTrophyIcon", "Array", "fill", "viewEvent", "event", "console", "log", "partner", "action", "viewAllActivity", "navigate", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "i3", "DashboardRouterService", "selectors", "decls", "vars", "consts", "template", "DashboardComponent_Template", "rf", "ctx", "DashboardComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\components\\dashboard\\dashboard.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\dashboard\\components\\dashboard\\dashboard.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router } from '@angular/router';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { DashboardRouterService } from '../../../../core/services/dashboard-router.service';\nimport { User, UserRole } from '../../../../core/models';\n\ninterface CommunityActivity {\n  id: string;\n  description: string;\n  points: number;\n  type: string;\n  timestamp: Date;\n  userId: string;\n  userName: string;\n}\n\ninterface LocalEvent {\n  id: string;\n  title: string;\n  description: string;\n  date: Date;\n  location: string;\n  category: string;\n  points: number;\n  image: string;\n  participants: number;\n  maxParticipants: number;\n}\n\ninterface Partner {\n  id: string;\n  name: string;\n  category: string;\n  logo: string;\n  rating: number;\n  reviews: number;\n  currentOffer: string;\n  location: string;\n  qrCode: string;\n}\n\ninterface Challenge {\n  id: string;\n  title: string;\n  description: string;\n  icon: string;\n  gradient: string;\n  progress: number;\n  current: number;\n  target: number;\n  reward: string;\n  endDate: Date;\n}\n\n@Component({\n  selector: 'app-dashboard',\n  templateUrl: './dashboard.component.html',\n  styleUrls: ['./dashboard.component.css']\n})\nexport class DashboardComponent implements OnInit {\n  user: User | null = null;\n\n  // Community data\n  communityFeed: CommunityActivity[] = [];\n  topContributors: User[] = [];\n  localEvents: LocalEvent[] = [];\n  featuredPartners: Partner[] = [];\n  activeChallenges: Challenge[] = [];\n\n  // City comparison data\n  cityComparison = {\n    monastir: { score: 0, participants: 0 },\n    sousse: { score: 0, participants: 0 }\n  };\n\n  comparisonStats = [\n    { name: 'Actions validées', monastir: 0, sousse: 0, monastirValue: 0, sousseValue: 0 },\n    { name: 'Événements organisés', monastir: 0, sousse: 0, monastirValue: 0, sousseValue: 0 },\n    { name: 'Partenaires actifs', monastir: 0, sousse: 0, monastirValue: 0, sousseValue: 0 }\n  ];\n\n  actionCategories = [\n    {\n      title: 'Environnement',\n      actions: [\n        { title: 'Nettoyage plage', icon: 'waves', color: 'primary', points: 15 },\n        { title: 'Plantation arbres', icon: 'park', color: 'primary', points: 20 },\n        { title: 'Recyclage', icon: 'recycling', color: 'primary', points: 10 }\n      ]\n    },\n    {\n      title: 'Social',\n      actions: [\n        { title: 'Aide personnes âgées', icon: 'elderly', color: 'accent', points: 25 },\n        { title: 'Cours bénévoles', icon: 'school', color: 'accent', points: 30 },\n        { title: 'Distribution repas', icon: 'restaurant', color: 'accent', points: 20 }\n      ]\n    },\n    {\n      title: 'Culture',\n      actions: [\n        { title: 'Guide touristique', icon: 'tour', color: 'warn', points: 15 },\n        { title: 'Animation enfants', icon: 'child_care', color: 'warn', points: 18 },\n        { title: 'Événement culturel', icon: 'theater_comedy', color: 'warn', points: 25 }\n      ]\n    }\n  ];\n\n  constructor(\n    private authService: AuthService,\n    private router: Router,\n    private dashboardRouter: DashboardRouterService\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.user = user;\n      if (user) {\n        // Rediriger vers le dashboard approprié selon le rôle\n        if (user.role !== UserRole.USER) {\n          this.dashboardRouter.navigateToUserDashboard(user);\n          return;\n        }\n        this.loadDashboardData();\n      }\n    });\n  }\n\n  private loadDashboardData(): void {\n    this.loadCommunityFeed();\n    this.loadTopContributors();\n    this.loadLocalEvents();\n    this.loadFeaturedPartners();\n    this.loadActiveChallenges();\n    this.loadCityComparison();\n  }\n\n  private loadCommunityFeed(): void {\n    // Simulate real community activity feed\n    this.communityFeed = [\n      {\n        id: '1',\n        description: 'Ahmed a nettoyé la plage de Monastir',\n        points: 15,\n        type: 'environment',\n        timestamp: new Date(Date.now() - 1000 * 60 * 30), // 30 min ago\n        userId: 'user1',\n        userName: 'Ahmed'\n      },\n      {\n        id: '2',\n        description: 'Fatma a aidé des personnes âgées à Sousse',\n        points: 25,\n        type: 'social',\n        timestamp: new Date(Date.now() - 1000 * 60 * 60), // 1 hour ago\n        userId: 'user2',\n        userName: 'Fatma'\n      },\n      {\n        id: '3',\n        description: 'Mohamed a organisé un cours de français',\n        points: 30,\n        type: 'education',\n        timestamp: new Date(Date.now() - 1000 * 60 * 90), // 1.5 hours ago\n        userId: 'user3',\n        userName: 'Mohamed'\n      },\n      {\n        id: '4',\n        description: 'Leila a guidé des touristes au Ribat',\n        points: 15,\n        type: 'culture',\n        timestamp: new Date(Date.now() - 1000 * 60 * 120), // 2 hours ago\n        userId: 'user4',\n        userName: 'Leila'\n      }\n    ];\n  }\n\n  private loadTopContributors(): void {\n    this.topContributors = [\n      { uid: 'top1', name: 'Ahmed Ben Ali', city: 'Monastir', points: 1250, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },\n      { uid: 'top2', name: 'Fatma Trabelsi', city: 'Sousse', points: 1180, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },\n      { uid: 'top3', name: 'Mohamed Gharbi', city: 'Monastir', points: 1050, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },\n      { uid: 'top4', name: 'Leila Mansouri', city: 'Sousse', points: 980, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] },\n      { uid: 'top5', name: 'Karim Bouazizi', city: 'Monastir', points: 920, email: '', role: UserRole.USER, createdAt: new Date(), updatedAt: new Date(), isActive: true, history: [] }\n    ];\n  }\n\n  private loadLocalEvents(): void {\n    this.localEvents = [\n      {\n        id: 'event1',\n        title: 'Nettoyage de la Plage de Monastir',\n        description: 'Rejoignez-nous pour nettoyer notre belle plage',\n        date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 2), // In 2 days\n        location: 'Plage de Monastir',\n        category: 'Environnement',\n        points: 20,\n        image: 'https://images.unsplash.com/photo-1558618666-fcd25c85cd64?w=400&h=200&fit=crop',\n        participants: 45,\n        maxParticipants: 100\n      },\n      {\n        id: 'event2',\n        title: 'Festival Culturel de Sousse',\n        description: 'Célébrons notre patrimoine culturel ensemble',\n        date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 5), // In 5 days\n        location: 'Médina de Sousse',\n        category: 'Culture',\n        points: 15,\n        image: 'https://images.unsplash.com/photo-1533174072545-7a4b6ad7a6c3?w=400&h=200&fit=crop',\n        participants: 120,\n        maxParticipants: 200\n      },\n      {\n        id: 'event3',\n        title: 'Cours de Français pour Réfugiés',\n        description: 'Aidez à enseigner le français aux nouveaux arrivants',\n        date: new Date(Date.now() + 1000 * 60 * 60 * 24 * 7), // In 1 week\n        location: 'Centre Communautaire',\n        category: 'Éducation',\n        points: 30,\n        image: 'https://images.unsplash.com/photo-1522202176988-66273c2fd55f?w=400&h=200&fit=crop',\n        participants: 15,\n        maxParticipants: 30\n      }\n    ];\n  }\n\n  private loadFeaturedPartners(): void {\n    this.featuredPartners = [\n      {\n        id: 'partner1',\n        name: 'Café des Nattes',\n        category: 'Restaurant',\n        logo: 'https://images.unsplash.com/photo-1554118811-1e0d58224f24?w=100&h=100&fit=crop',\n        rating: 4.5,\n        reviews: 127,\n        currentOffer: '10% de réduction avec 50 points',\n        location: 'Médina de Monastir',\n        qrCode: 'PARTNER_CAFE_NATTES'\n      },\n      {\n        id: 'partner2',\n        name: 'Boutique Artisanat Sousse',\n        category: 'Artisanat',\n        logo: 'https://images.unsplash.com/photo-1578662996442-48f60103fc96?w=100&h=100&fit=crop',\n        rating: 4.8,\n        reviews: 89,\n        currentOffer: 'Produit gratuit avec 100 points',\n        location: 'Médina de Sousse',\n        qrCode: 'PARTNER_ARTISANAT_SOUSSE'\n      },\n      {\n        id: 'partner3',\n        name: 'Hammam Traditionnel',\n        category: 'Bien-être',\n        logo: 'https://images.unsplash.com/photo-1571019613454-1cb2f99b2d8b?w=100&h=100&fit=crop',\n        rating: 4.3,\n        reviews: 156,\n        currentOffer: 'Séance gratuite avec 75 points',\n        location: 'Centre-ville Monastir',\n        qrCode: 'PARTNER_HAMMAM'\n      }\n    ];\n  }\n\n  private loadActiveChallenges(): void {\n    this.activeChallenges = [\n      {\n        id: 'challenge1',\n        title: 'Éco-Warrior',\n        description: 'Participez à 5 actions environnementales ce mois-ci',\n        icon: 'eco',\n        gradient: 'linear-gradient(135deg, #4CAF50, #8BC34A)',\n        progress: 60,\n        current: 3,\n        target: 5,\n        reward: 'Badge Éco-Warrior + 100 points bonus',\n        endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 15) // 15 days\n      },\n      {\n        id: 'challenge2',\n        title: 'Ambassadeur Culturel',\n        description: 'Guidez 10 touristes dans votre ville',\n        icon: 'tour',\n        gradient: 'linear-gradient(135deg, #FF9800, #FFC107)',\n        progress: 30,\n        current: 3,\n        target: 10,\n        reward: 'Badge Ambassadeur + Visite gratuite musée',\n        endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 20) // 20 days\n      },\n      {\n        id: 'challenge3',\n        title: 'Solidarité Communautaire',\n        description: 'Aidez 15 personnes dans le besoin',\n        icon: 'volunteer_activism',\n        gradient: 'linear-gradient(135deg, #E91E63, #F06292)',\n        progress: 80,\n        current: 12,\n        target: 15,\n        reward: 'Badge Solidarité + 200 points bonus',\n        endDate: new Date(Date.now() + 1000 * 60 * 60 * 24 * 10) // 10 days\n      }\n    ];\n  }\n\n  private loadCityComparison(): void {\n    // Simulate city competition data\n    this.cityComparison = {\n      monastir: { score: 15420, participants: 342 },\n      sousse: { score: 16180, participants: 389 }\n    };\n\n    this.comparisonStats = [\n      {\n        name: 'Actions validées',\n        monastir: 65,\n        sousse: 78,\n        monastirValue: 1250,\n        sousseValue: 1520\n      },\n      {\n        name: 'Événements organisés',\n        monastir: 45,\n        sousse: 52,\n        monastirValue: 23,\n        sousseValue: 27\n      },\n      {\n        name: 'Partenaires actifs',\n        monastir: 80,\n        sousse: 70,\n        monastirValue: 16,\n        sousseValue: 14\n      }\n    ];\n  }\n\n  // UI Helper Methods\n  getGreeting(): string {\n    const hour = new Date().getHours();\n    if (hour < 12) return 'Bonjour';\n    if (hour < 18) return 'Bon après-midi';\n    return 'Bonsoir';\n  }\n\n  getCityIcon(): string {\n    if (!this.user) return 'location_city';\n    return this.user.city === 'Monastir' ? 'account_balance' : 'museum';\n  }\n\n  getCommunityRank(): number {\n    if (!this.user) return 0;\n    const userIndex = this.topContributors.findIndex(u => u.uid === this.user!.uid);\n    return userIndex >= 0 ? userIndex + 1 : this.topContributors.length + 1;\n  }\n\n  getImpactScore(): number {\n    if (!this.user) return 0;\n    // Calculate impact based on points and community actions\n    return Math.floor(this.user.points * 1.2 + (this.user.history?.length || 0) * 5);\n  }\n\n  getCityProgress(): number {\n    if (!this.user) return 0;\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    const monthlyTarget = 20000;\n    return Math.min((cityData.score / monthlyTarget) * 314, 314); // 314 = 2π * 50 (circle circumference)\n  }\n\n  getCityProgressPercent(): number {\n    if (!this.user) return 0;\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    const monthlyTarget = 20000;\n    return Math.min(Math.round((cityData.score / monthlyTarget) * 100), 100);\n  }\n\n  getCityStats() {\n    if (!this.user) return { validatedActions: 0, activeUsers: 0 };\n    const cityData = this.user.city === 'Monastir' ? this.cityComparison.monastir : this.cityComparison.sousse;\n    return {\n      validatedActions: Math.floor(cityData.score / 15), // Estimate based on average points per action\n      activeUsers: cityData.participants\n    };\n  }\n\n  getActivityColor(type: string): string {\n    const colorMap: { [key: string]: string } = {\n      'environment': '#4CAF50',\n      'social': '#E91E63',\n      'education': '#2196F3',\n      'culture': '#FF9800',\n      'health': '#9C27B0'\n    };\n    return colorMap[type] || '#667eea';\n  }\n\n  getActivityIcon(type: string): string {\n    const iconMap: { [key: string]: string } = {\n      'environment': 'eco',\n      'social': 'volunteer_activism',\n      'education': 'school',\n      'culture': 'theater_comedy',\n      'health': 'health_and_safety'\n    };\n    return iconMap[type] || 'circle';\n  }\n\n  getTrophyColor(index: number): string {\n    const colors = ['#FFD700', '#C0C0C0', '#CD7F32']; // Gold, Silver, Bronze\n    return colors[index] || '#667eea';\n  }\n\n  getTrophyIcon(index: number): string {\n    return index < 3 ? 'emoji_events' : 'star';\n  }\n\n  getStars(rating: number): number[] {\n    return Array(Math.floor(rating)).fill(0);\n  }\n\n  // Action Methods\n  viewEvent(event: LocalEvent): void {\n    // Navigate to event details or open modal\n    console.log('Viewing event:', event);\n  }\n\n  viewPartner(partner: Partner): void {\n    // Navigate to partner details or open modal\n    console.log('Viewing partner:', partner);\n  }\n\n  executeAction(action: any): void {\n    // Execute the selected action\n    console.log('Executing action:', action);\n    // This would typically open a form or navigate to action details\n  }\n\n  viewAllActivity(): void {\n    this.router.navigate(['/community/activity']);\n  }\n}\n", "<div class=\"dashboard-container\" *ngIf=\"user\">\n  <!-- En-tête avec informations utilisateur -->\n  <div class=\"dashboard-header fade-in\">\n    <div class=\"user-welcome\">\n      <div class=\"user-avatar\">\n        <mat-icon>school</mat-icon>\n      </div>\n      <div class=\"user-info\">\n        <h2>Bonjour {{ user.name }} ! 👋</h2>\n        <p class=\"user-role\">Élève • {{ user.city }}</p>\n      </div>\n    </div>\n\n    <div class=\"header-actions\">\n      <div class=\"points-display\">\n        <div class=\"points-card\">\n          <mat-icon class=\"points-icon\">stars</mat-icon>\n          <div class=\"points-info\">\n            <span class=\"points-value\">{{ user.points }}</span>\n            <span class=\"points-label\">Points</span>\n          </div>\n        </div>\n      </div>\n\n      <!-- Notifications -->\n      <button mat-icon-button class=\"notifications-btn\"\n              [matBadge]=\"unreadNotifications\"\n              matBadgeColor=\"warn\"\n              [matBadgeHidden]=\"unreadNotifications === 0\"\n              (click)=\"toggleNotifications()\">\n        <mat-icon>notifications</mat-icon>\n      </button>\n\n      <!-- Profil -->\n      <button mat-icon-button [matMenuTriggerFor]=\"profileMenu\">\n        <mat-icon>account_circle</mat-icon>\n      </button>\n      <mat-menu #profileMenu=\"matMenu\">\n        <button mat-menu-item (click)=\"openProfile()\">\n          <mat-icon>person</mat-icon>\n          <span>Mon Profil</span>\n        </button>\n        <button mat-menu-item (click)=\"logout()\">\n          <mat-icon>logout</mat-icon>\n          <span>Déconnexion</span>\n        </button>\n      </mat-menu>\n    </div>\n  </div>\n\n  <!-- Section principale -->\n  <div class=\"main-section\">\n    <!-- Bouton Scanner QR - Bien visible -->\n    <div class=\"qr-scan-section\">\n      <mat-card class=\"qr-scan-card\">\n        <mat-card-content>\n          <div class=\"qr-scan-content\">\n            <div class=\"qr-scan-icon\">\n              <mat-icon>qr_code_scanner</mat-icon>\n            </div>\n            <div class=\"qr-scan-text\">\n              <h3>Scanner un QR Code KnowMe</h3>\n              <p>Découvre un prestataire et gagne des points !</p>\n            </div>\n            <button mat-raised-button color=\"primary\" class=\"qr-scan-btn\" (click)=\"openQRScanner()\">\n              <mat-icon>camera_alt</mat-icon>\n              Scanner\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Jauge d'avancement vers la prochaine récompense -->\n    <div class=\"progress-section\">\n      <mat-card class=\"progress-card\">\n        <mat-card-content>\n          <h3>Progression vers ta prochaine récompense</h3>\n          <div class=\"reward-progress\">\n            <div class=\"progress-info\">\n              <div class=\"next-reward\">\n                <img [src]=\"nextReward.image\" [alt]=\"nextReward.title\" class=\"reward-icon\">\n                <div class=\"reward-details\">\n                  <h4>{{ nextReward.title }}</h4>\n                  <p>{{ nextReward.description }}</p>\n                </div>\n              </div>\n              <div class=\"points-needed\">\n                <span class=\"points-remaining\">{{ getPointsToNextReward() }}</span>\n                <span class=\"points-label\">points restants</span>\n              </div>\n            </div>\n            <div class=\"progress-bar\">\n              <div class=\"progress-fill\" [style.width]=\"getProgressPercentage() + '%'\"></div>\n            </div>\n            <div class=\"progress-text\">\n              <span>{{ user.points }} / {{ nextReward.pointsRequired }} points</span>\n            </div>\n            <div class=\"motivation-message\">\n              <p>{{ getMotivationMessage() }}</p>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Offres/Récompenses disponibles -->\n    <div class=\"rewards-section\">\n      <h3 class=\"section-subtitle\">\n        <mat-icon>card_giftcard</mat-icon>\n        Récompenses disponibles\n      </h3>\n      <div class=\"rewards-grid\">\n        <mat-card *ngFor=\"let reward of availableRewards\" class=\"reward-card\"\n                  [class.affordable]=\"user.points >= reward.pointsRequired\">\n          <div class=\"reward-image\">\n            <img [src]=\"reward.imageUrl\" [alt]=\"reward.title\">\n            <div class=\"reward-badge\" *ngIf=\"user.points >= reward.pointsRequired\">\n              <mat-icon>check_circle</mat-icon>\n            </div>\n          </div>\n          <mat-card-content>\n            <h4>{{ reward.title }}</h4>\n            <p class=\"reward-description\">{{ reward.description }}</p>\n            <div class=\"reward-partner\">\n              <mat-icon>store</mat-icon>\n              <span>{{ reward.partnerName }}</span>\n            </div>\n            <div class=\"reward-points\">\n              <mat-icon>stars</mat-icon>\n              <span>{{ reward.pointsRequired }} points</span>\n            </div>\n            <button mat-raised-button\n                    [color]=\"user.points >= reward.pointsRequired ? 'primary' : ''\"\n                    [disabled]=\"user.points < reward.pointsRequired\"\n                    class=\"exchange-btn\"\n                    (click)=\"exchangeReward(reward)\">\n              {{ user.points >= reward.pointsRequired ? 'Échanger' : 'Pas assez de points' }}\n            </button>\n          </mat-card-content>\n        </mat-card>\n      </div>\n    </div>\n  </div>\n\n  <!-- Section secondaire -->\n  <div class=\"secondary-section\">\n    <!-- Historique des bonnes actions -->\n    <div class=\"history-section\">\n      <mat-card class=\"history-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>history</mat-icon>\n            Historique de mes actions\n          </mat-card-title>\n          <div class=\"history-filters\">\n            <mat-form-field appearance=\"outline\" class=\"filter-field\">\n              <mat-label>Période</mat-label>\n              <mat-select [(value)]=\"selectedPeriod\" (selectionChange)=\"filterHistory()\">\n                <mat-option value=\"week\">Cette semaine</mat-option>\n                <mat-option value=\"month\">Ce mois</mat-option>\n                <mat-option value=\"year\">Cette année</mat-option>\n                <mat-option value=\"all\">Tout</mat-option>\n              </mat-select>\n            </mat-form-field>\n          </div>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"history-list\" *ngIf=\"filteredHistory.length > 0; else noHistory\">\n            <div *ngFor=\"let action of filteredHistory\" class=\"history-item\">\n              <div class=\"action-icon\">\n                <mat-icon [style.color]=\"getActionColor(action.type)\">\n                  {{ getActionIcon(action.type) }}\n                </mat-icon>\n              </div>\n              <div class=\"action-details\">\n                <h4>{{ action.description }}</h4>\n                <div class=\"action-meta\">\n                  <span class=\"action-date\">{{ action.timestamp | date:'short' }}</span>\n                  <span class=\"action-location\" *ngIf=\"action.location\">\n                    <mat-icon>location_on</mat-icon>\n                    {{ action.location }}\n                  </span>\n                </div>\n                <div class=\"action-provider\" *ngIf=\"action.providerName\">\n                  <mat-icon>business</mat-icon>\n                  <span>{{ action.providerName }}</span>\n                </div>\n              </div>\n              <div class=\"action-points\">\n                <span class=\"points-earned\">+{{ action.points }}</span>\n                <span class=\"points-label\">pts</span>\n              </div>\n            </div>\n          </div>\n          <ng-template #noHistory>\n            <div class=\"no-history\">\n              <mat-icon>sentiment_neutral</mat-icon>\n              <p>Aucune action pour cette période</p>\n              <button mat-raised-button color=\"primary\" (click)=\"openQRScanner()\">\n                Commencer maintenant\n              </button>\n            </div>\n          </ng-template>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n    <!-- Statistiques mensuelles -->\n    <div class=\"stats-section\">\n      <mat-card class=\"stats-card\">\n        <mat-card-header>\n          <mat-card-title>\n            <mat-icon>analytics</mat-icon>\n            Mes statistiques ce mois\n          </mat-card-title>\n        </mat-card-header>\n        <mat-card-content>\n          <div class=\"stats-grid\">\n            <div class=\"stat-item\">\n              <div class=\"stat-icon\">\n                <mat-icon>trending_up</mat-icon>\n              </div>\n              <div class=\"stat-content\">\n                <span class=\"stat-value\">{{ monthlyStats.pointsEarned }}</span>\n                <span class=\"stat-label\">Points gagnés</span>\n              </div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-icon\">\n                <mat-icon>trending_down</mat-icon>\n              </div>\n              <div class=\"stat-content\">\n                <span class=\"stat-value\">{{ monthlyStats.pointsSpent }}</span>\n                <span class=\"stat-label\">Points dépensés</span>\n              </div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-icon\">\n                <mat-icon>qr_code_scanner</mat-icon>\n              </div>\n              <div class=\"stat-content\">\n                <span class=\"stat-value\">{{ monthlyStats.scansCount }}</span>\n                <span class=\"stat-label\">QR scannés</span>\n              </div>\n            </div>\n            <div class=\"stat-item\">\n              <div class=\"stat-icon\">\n                <mat-icon>verified</mat-icon>\n              </div>\n              <div class=\"stat-content\">\n                <span class=\"stat-value\">{{ monthlyStats.validationsCount }}</span>\n                <span class=\"stat-label\">Actions validées</span>\n              </div>\n            </div>\n          </div>\n\n          <!-- Graphique simple des points -->\n          <div class=\"points-chart\">\n            <h4>Évolution des points</h4>\n            <div class=\"chart-container\">\n              <div class=\"chart-bars\">\n                <div *ngFor=\"let week of weeklyPointsData\" class=\"chart-bar\">\n                  <div class=\"bar\" [style.height]=\"(week.points / maxWeeklyPoints * 100) + '%'\"></div>\n                  <span class=\"bar-label\">S{{ week.week }}</span>\n                </div>\n              </div>\n            </div>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n\n  <!-- Partner Spotlight -->\n  <div class=\"partners-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>store</mat-icon>\n      Partenaires Locaux\n    </h2>\n    <div class=\"partners-grid\">\n      <mat-card *ngFor=\"let partner of featuredPartners\" class=\"partner-card\" (click)=\"viewPartner(partner)\">\n        <div class=\"partner-header\">\n          <img [src]=\"partner.logo\" [alt]=\"partner.name\" class=\"partner-logo\">\n          <div class=\"partner-info\">\n            <h4>{{ partner.name }}</h4>\n            <p class=\"partner-category\">{{ partner.category }}</p>\n            <div class=\"partner-rating\">\n              <mat-icon *ngFor=\"let star of getStars(partner.rating)\" class=\"star-icon\">star</mat-icon>\n              <span>({{ partner.reviews }})</span>\n            </div>\n          </div>\n        </div>\n        <mat-card-content>\n          <p class=\"partner-offer\">{{ partner.currentOffer }}</p>\n          <div class=\"partner-actions\">\n            <button mat-stroked-button class=\"partner-btn\">\n              <mat-icon>qr_code</mat-icon>\n              Scanner QR\n            </button>\n            <button mat-stroked-button class=\"partner-btn\">\n              <mat-icon>directions</mat-icon>\n              Itinéraire\n            </button>\n          </div>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- Quick Actions Hub -->\n  <div class=\"actions-hub\">\n    <h2 class=\"section-title\">\n      <mat-icon>flash_on</mat-icon>\n      Actions Rapides\n    </h2>\n    <div class=\"actions-grid\">\n      <div class=\"action-category\" *ngFor=\"let category of actionCategories\">\n        <h3 class=\"category-title\">{{ category.title }}</h3>\n        <div class=\"category-actions\">\n          <button *ngFor=\"let action of category.actions\"\n                  mat-raised-button\n                  [color]=\"action.color\"\n                  class=\"action-btn\"\n                  (click)=\"executeAction(action)\">\n            <mat-icon>{{ action.icon }}</mat-icon>\n            <span>{{ action.title }}</span>\n            <div class=\"action-points\">+{{ action.points }}</div>\n          </button>\n        </div>\n      </div>\n    </div>\n  </div>\n\n  <!-- Community Challenges -->\n  <div class=\"challenges-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>emoji_events</mat-icon>\n      Défis Communautaires\n    </h2>\n    <div class=\"challenges-grid\">\n      <mat-card *ngFor=\"let challenge of activeChallenges\" class=\"challenge-card\">\n        <div class=\"challenge-header\" [style.background]=\"challenge.gradient\">\n          <mat-icon class=\"challenge-icon\">{{ challenge.icon }}</mat-icon>\n          <h4>{{ challenge.title }}</h4>\n        </div>\n        <mat-card-content>\n          <p class=\"challenge-description\">{{ challenge.description }}</p>\n          <div class=\"challenge-progress\">\n            <div class=\"progress-bar\">\n              <div class=\"progress-fill\" [style.width]=\"challenge.progress + '%'\"></div>\n            </div>\n            <span class=\"progress-text\">{{ challenge.current }}/{{ challenge.target }}</span>\n          </div>\n          <div class=\"challenge-reward\">\n            <mat-icon>card_giftcard</mat-icon>\n            <span>{{ challenge.reward }}</span>\n          </div>\n          <button mat-raised-button color=\"accent\" class=\"challenge-btn\">\n            Participer\n          </button>\n        </mat-card-content>\n      </mat-card>\n    </div>\n  </div>\n\n  <!-- City Comparison -->\n  <div class=\"comparison-section\">\n    <h2 class=\"section-title\">\n      <mat-icon>compare_arrows</mat-icon>\n      Monastir vs Sousse\n    </h2>\n    <mat-card class=\"comparison-card\">\n      <mat-card-content>\n        <div class=\"comparison-header\">\n          <div class=\"city-score monastir\">\n            <h3>Monastir</h3>\n            <div class=\"score\">{{ cityComparison.monastir.score }}</div>\n            <p>{{ cityComparison.monastir.participants }} participants</p>\n          </div>\n          <div class=\"vs-divider\">\n            <mat-icon>sports</mat-icon>\n            <span>VS</span>\n          </div>\n          <div class=\"city-score sousse\">\n            <h3>Sousse</h3>\n            <div class=\"score\">{{ cityComparison.sousse.score }}</div>\n            <p>{{ cityComparison.sousse.participants }} participants</p>\n          </div>\n        </div>\n        <div class=\"comparison-stats\">\n          <div class=\"stat-comparison\" *ngFor=\"let stat of comparisonStats\">\n            <span class=\"stat-name\">{{ stat.name }}</span>\n            <div class=\"stat-bars\">\n              <div class=\"stat-bar monastir\" [style.width]=\"stat.monastir + '%'\">\n                <span>{{ stat.monastirValue }}</span>\n              </div>\n              <div class=\"stat-bar sousse\" [style.width]=\"stat.sousse + '%'\">\n                <span>{{ stat.sousseValue }}</span>\n              </div>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": "AAIA,SAAeA,QAAQ,QAAQ,yBAAyB;;;;;;;;;;;;;;;;ICkH1CC,EADF,CAAAC,cAAA,cAAuE,eAC3D;IAAAD,EAAA,CAAAE,MAAA,mBAAY;IACxBF,EADwB,CAAAG,YAAA,EAAW,EAC7B;;;;;;IAJRH,EAFF,CAAAC,cAAA,mBACoE,cACxC;IACxBD,EAAA,CAAAI,SAAA,cAAkD;IAClDJ,EAAA,CAAAK,UAAA,IAAAC,mDAAA,kBAAuE;IAGzEN,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,uBAAkB,SACZ;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,YAA8B;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAExDH,EADF,CAAAC,cAAA,cAA4B,gBAChB;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAwB;IAChCF,EADgC,CAAAG,YAAA,EAAO,EACjC;IAEJH,EADF,CAAAC,cAAA,eAA2B,gBACf;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAkC;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;IACNH,EAAA,CAAAC,cAAA,kBAIyC;IAAjCD,EAAA,CAAAO,UAAA,mBAAAC,uEAAA;MAAA,MAAAC,SAAA,GAAAT,EAAA,CAAAU,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAG,cAAA,CAAAP,SAAA,CAAsB;IAAA,EAAC;IACtCT,EAAA,CAAAE,MAAA,IACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;;;;;IA1BDH,EAAA,CAAAiB,WAAA,eAAAJ,MAAA,CAAAK,IAAA,CAAAC,MAAA,IAAAV,SAAA,CAAAW,cAAA,CAAyD;IAE1DpB,EAAA,CAAAqB,SAAA,GAAuB;IAACrB,EAAxB,CAAAsB,UAAA,QAAAb,SAAA,CAAAc,QAAA,EAAAvB,EAAA,CAAAwB,aAAA,CAAuB,QAAAf,SAAA,CAAAgB,KAAA,CAAqB;IACtBzB,EAAA,CAAAqB,SAAA,EAA0C;IAA1CrB,EAAA,CAAAsB,UAAA,SAAAT,MAAA,CAAAK,IAAA,CAAAC,MAAA,IAAAV,SAAA,CAAAW,cAAA,CAA0C;IAKjEpB,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAA0B,iBAAA,CAAAjB,SAAA,CAAAgB,KAAA,CAAkB;IACQzB,EAAA,CAAAqB,SAAA,GAAwB;IAAxBrB,EAAA,CAAA0B,iBAAA,CAAAjB,SAAA,CAAAkB,WAAA,CAAwB;IAG9C3B,EAAA,CAAAqB,SAAA,GAAwB;IAAxBrB,EAAA,CAAA0B,iBAAA,CAAAjB,SAAA,CAAAmB,WAAA,CAAwB;IAIxB5B,EAAA,CAAAqB,SAAA,GAAkC;IAAlCrB,EAAA,CAAA6B,kBAAA,KAAApB,SAAA,CAAAW,cAAA,YAAkC;IAGlCpB,EAAA,CAAAqB,SAAA,EAA+D;IAC/DrB,EADA,CAAAsB,UAAA,UAAAT,MAAA,CAAAK,IAAA,CAAAC,MAAA,IAAAV,SAAA,CAAAW,cAAA,kBAA+D,aAAAP,MAAA,CAAAK,IAAA,CAAAC,MAAA,GAAAV,SAAA,CAAAW,cAAA,CACf;IAGtDpB,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAA6B,kBAAA,MAAAhB,MAAA,CAAAK,IAAA,CAAAC,MAAA,IAAAV,SAAA,CAAAW,cAAA,gDACF;;;;;IA0CQpB,EADF,CAAAC,cAAA,gBAAsD,eAC1C;IAAAD,EAAA,CAAAE,MAAA,kBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChCH,EAAA,CAAAE,MAAA,GACF;IAAAF,EAAA,CAAAG,YAAA,EAAO;;;;IADLH,EAAA,CAAAqB,SAAA,GACF;IADErB,EAAA,CAAA6B,kBAAA,MAAAC,SAAA,CAAAC,QAAA,MACF;;;;;IAGA/B,EADF,CAAAC,cAAA,eAAyD,eAC7C;IAAAD,EAAA,CAAAE,MAAA,eAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAyB;IACjCF,EADiC,CAAAG,YAAA,EAAO,EAClC;;;;IADEH,EAAA,CAAAqB,SAAA,GAAyB;IAAzBrB,EAAA,CAAA0B,iBAAA,CAAAI,SAAA,CAAAE,YAAA,CAAyB;;;;;IAfjChC,EAFJ,CAAAC,cAAA,cAAiE,cACtC,eAC+B;IACpDD,EAAA,CAAAE,MAAA,GACF;IACFF,EADE,CAAAG,YAAA,EAAW,EACP;IAEJH,EADF,CAAAC,cAAA,cAA4B,SACtB;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE/BH,EADF,CAAAC,cAAA,cAAyB,gBACG;IAAAD,EAAA,CAAAE,MAAA,GAAqC;;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACtEH,EAAA,CAAAK,UAAA,KAAA4B,uDAAA,oBAAsD;IAIxDjC,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAK,UAAA,KAAA6B,sDAAA,mBAAyD;IAI3DlC,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,gBAA2B,iBACG;IAAAD,EAAA,CAAAE,MAAA,IAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACvDH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,WAAG;IAElCF,EAFkC,CAAAG,YAAA,EAAO,EACjC,EACF;;;;;IAtBQH,EAAA,CAAAqB,SAAA,GAA2C;IAA3CrB,EAAA,CAAAmC,WAAA,UAAAtB,MAAA,CAAAuB,cAAA,CAAAN,SAAA,CAAAO,IAAA,EAA2C;IACnDrC,EAAA,CAAAqB,SAAA,EACF;IADErB,EAAA,CAAA6B,kBAAA,MAAAhB,MAAA,CAAAyB,aAAA,CAAAR,SAAA,CAAAO,IAAA,OACF;IAGIrC,EAAA,CAAAqB,SAAA,GAAwB;IAAxBrB,EAAA,CAAA0B,iBAAA,CAAAI,SAAA,CAAAH,WAAA,CAAwB;IAEA3B,EAAA,CAAAqB,SAAA,GAAqC;IAArCrB,EAAA,CAAA0B,iBAAA,CAAA1B,EAAA,CAAAuC,WAAA,QAAAT,SAAA,CAAAU,SAAA,WAAqC;IAChCxC,EAAA,CAAAqB,SAAA,GAAqB;IAArBrB,EAAA,CAAAsB,UAAA,SAAAQ,SAAA,CAAAC,QAAA,CAAqB;IAKxB/B,EAAA,CAAAqB,SAAA,EAAyB;IAAzBrB,EAAA,CAAAsB,UAAA,SAAAQ,SAAA,CAAAE,YAAA,CAAyB;IAM3BhC,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAA6B,kBAAA,MAAAC,SAAA,CAAAX,MAAA,KAAoB;;;;;IAtBtDnB,EAAA,CAAAC,cAAA,cAA6E;IAC3ED,EAAA,CAAAK,UAAA,IAAAoC,+CAAA,oBAAiE;IAyBnEzC,EAAA,CAAAG,YAAA,EAAM;;;;IAzBoBH,EAAA,CAAAqB,SAAA,EAAkB;IAAlBrB,EAAA,CAAAsB,UAAA,YAAAT,MAAA,CAAA6B,eAAA,CAAkB;;;;;;IA4BxC1C,EADF,CAAAC,cAAA,eAAwB,eACZ;IAAAD,EAAA,CAAAE,MAAA,wBAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAE,MAAA,4CAAgC;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACvCH,EAAA,CAAAC,cAAA,kBAAoE;IAA1BD,EAAA,CAAAO,UAAA,mBAAAoC,0EAAA;MAAA3C,EAAA,CAAAU,aAAA,CAAAkC,GAAA;MAAA,MAAA/B,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAgC,aAAA,EAAe;IAAA,EAAC;IACjE7C,EAAA,CAAAE,MAAA,6BACF;IACFF,EADE,CAAAG,YAAA,EAAS,EACL;;;;;IA4DFH,EAAA,CAAAC,cAAA,eAA6D;IAC3DD,EAAA,CAAAI,SAAA,eAAoF;IACpFJ,EAAA,CAAAC,cAAA,gBAAwB;IAAAD,EAAA,CAAAE,MAAA,GAAgB;IAC1CF,EAD0C,CAAAG,YAAA,EAAO,EAC3C;;;;;IAFaH,EAAA,CAAAqB,SAAA,EAA4D;IAA5DrB,EAAA,CAAAmC,WAAA,WAAAW,OAAA,CAAA3B,MAAA,GAAAN,MAAA,CAAAkC,eAAA,aAA4D;IACrD/C,EAAA,CAAAqB,SAAA,GAAgB;IAAhBrB,EAAA,CAAA6B,kBAAA,MAAAiB,OAAA,CAAAE,IAAA,KAAgB;;;;;IAuB5ChD,EAAA,CAAAC,cAAA,oBAA0E;IAAAD,EAAA,CAAAE,MAAA,WAAI;IAAAF,EAAA,CAAAG,YAAA,EAAW;;;;;;IAPjGH,EAAA,CAAAC,cAAA,oBAAuG;IAA/BD,EAAA,CAAAO,UAAA,mBAAA0C,yEAAA;MAAA,MAAAC,UAAA,GAAAlD,EAAA,CAAAU,aAAA,CAAAyC,GAAA,EAAAvC,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAuC,WAAA,CAAAF,UAAA,CAAoB;IAAA,EAAC;IACpGlD,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAI,SAAA,eAAoE;IAElEJ,EADF,CAAAC,cAAA,eAA0B,SACpB;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC3BH,EAAA,CAAAC,cAAA,aAA4B;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAI;IACtDH,EAAA,CAAAC,cAAA,eAA4B;IAC1BD,EAAA,CAAAK,UAAA,IAAAgD,yDAAA,wBAA0E;IAC1ErD,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAGnCF,EAHmC,CAAAG,YAAA,EAAO,EAChC,EACF,EACF;IAEJH,EADF,CAAAC,cAAA,wBAAkB,cACS;IAAAD,EAAA,CAAAE,MAAA,IAA0B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAGnDH,EAFJ,CAAAC,cAAA,gBAA6B,mBACoB,gBACnC;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,oBACF;IAAAF,EAAA,CAAAG,YAAA,EAAS;IAEPH,EADF,CAAAC,cAAA,mBAA+C,gBACnC;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,yBACF;IAGNF,EAHM,CAAAG,YAAA,EAAS,EACL,EACW,EACV;;;;;IAvBFH,EAAA,CAAAqB,SAAA,GAAoB;IAACrB,EAArB,CAAAsB,UAAA,QAAA4B,UAAA,CAAAI,IAAA,EAAAtD,EAAA,CAAAwB,aAAA,CAAoB,QAAA0B,UAAA,CAAAK,IAAA,CAAqB;IAExCvD,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAA0B,iBAAA,CAAAwB,UAAA,CAAAK,IAAA,CAAkB;IACMvD,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAA0B,iBAAA,CAAAwB,UAAA,CAAAM,QAAA,CAAsB;IAErBxD,EAAA,CAAAqB,SAAA,GAA2B;IAA3BrB,EAAA,CAAAsB,UAAA,YAAAT,MAAA,CAAA4C,QAAA,CAAAP,UAAA,CAAAQ,MAAA,EAA2B;IAChD1D,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAA6B,kBAAA,MAAAqB,UAAA,CAAAS,OAAA,MAAuB;IAKR3D,EAAA,CAAAqB,SAAA,GAA0B;IAA1BrB,EAAA,CAAA0B,iBAAA,CAAAwB,UAAA,CAAAU,YAAA,CAA0B;;;;;;IA0BnD5D,EAAA,CAAAC,cAAA,kBAIwC;IAAhCD,EAAA,CAAAO,UAAA,mBAAAsD,2EAAA;MAAA,MAAAC,UAAA,GAAA9D,EAAA,CAAAU,aAAA,CAAAqD,IAAA,EAAAnD,SAAA;MAAA,MAAAC,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAmD,aAAA,CAAAF,UAAA,CAAqB;IAAA,EAAC;IACrC9D,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAE,MAAA,GAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACtCH,EAAA,CAAAC,cAAA,WAAM;IAAAD,EAAA,CAAAE,MAAA,GAAkB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/BH,EAAA,CAAAC,cAAA,eAA2B;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IACjDF,EADiD,CAAAG,YAAA,EAAM,EAC9C;;;;IANDH,EAAA,CAAAsB,UAAA,UAAAwC,UAAA,CAAAG,KAAA,CAAsB;IAGlBjE,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAA0B,iBAAA,CAAAoC,UAAA,CAAAI,IAAA,CAAiB;IACrBlE,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAA0B,iBAAA,CAAAoC,UAAA,CAAArC,KAAA,CAAkB;IACGzB,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAA6B,kBAAA,MAAAiC,UAAA,CAAA3C,MAAA,KAAoB;;;;;IATnDnB,EADF,CAAAC,cAAA,eAAuE,cAC1C;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACpDH,EAAA,CAAAC,cAAA,eAA8B;IAC5BD,EAAA,CAAAK,UAAA,IAAA8D,kDAAA,sBAIwC;IAM5CnE,EADE,CAAAG,YAAA,EAAM,EACF;;;;IAZuBH,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAA0B,iBAAA,CAAA0C,YAAA,CAAA3C,KAAA,CAAoB;IAElBzB,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAsB,UAAA,YAAA8C,YAAA,CAAAC,OAAA,CAAmB;;;;;IAuB9CrE,EAFJ,CAAAC,cAAA,oBAA4E,eACJ,oBACnC;IAAAD,EAAA,CAAAE,MAAA,GAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAChEH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAE,MAAA,GAAqB;IAC3BF,EAD2B,CAAAG,YAAA,EAAK,EAC1B;IAEJH,EADF,CAAAC,cAAA,uBAAkB,aACiB;IAAAD,EAAA,CAAAE,MAAA,GAA2B;IAAAF,EAAA,CAAAG,YAAA,EAAI;IAE9DH,EADF,CAAAC,cAAA,eAAgC,eACJ;IACxBD,EAAA,CAAAI,SAAA,eAA0E;IAC5EJ,EAAA,CAAAG,YAAA,EAAM;IACNH,EAAA,CAAAC,cAAA,gBAA4B;IAAAD,EAAA,CAAAE,MAAA,IAA8C;IAC5EF,EAD4E,CAAAG,YAAA,EAAO,EAC7E;IAEJH,EADF,CAAAC,cAAA,gBAA8B,gBAClB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAC9BF,EAD8B,CAAAG,YAAA,EAAO,EAC/B;IACNH,EAAA,CAAAC,cAAA,mBAA+D;IAC7DD,EAAA,CAAAE,MAAA,oBACF;IAEJF,EAFI,CAAAG,YAAA,EAAS,EACQ,EACV;;;;IApBqBH,EAAA,CAAAqB,SAAA,EAAuC;IAAvCrB,EAAA,CAAAmC,WAAA,eAAAmC,aAAA,CAAAC,QAAA,CAAuC;IAClCvE,EAAA,CAAAqB,SAAA,GAAoB;IAApBrB,EAAA,CAAA0B,iBAAA,CAAA4C,aAAA,CAAAJ,IAAA,CAAoB;IACjDlE,EAAA,CAAAqB,SAAA,GAAqB;IAArBrB,EAAA,CAAA0B,iBAAA,CAAA4C,aAAA,CAAA7C,KAAA,CAAqB;IAGQzB,EAAA,CAAAqB,SAAA,GAA2B;IAA3BrB,EAAA,CAAA0B,iBAAA,CAAA4C,aAAA,CAAA3C,WAAA,CAA2B;IAG7B3B,EAAA,CAAAqB,SAAA,GAAwC;IAAxCrB,EAAA,CAAAmC,WAAA,UAAAmC,aAAA,CAAAE,QAAA,OAAwC;IAEzCxE,EAAA,CAAAqB,SAAA,GAA8C;IAA9CrB,EAAA,CAAAyE,kBAAA,KAAAH,aAAA,CAAAI,OAAA,OAAAJ,aAAA,CAAAK,MAAA,KAA8C;IAIpE3E,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAA0B,iBAAA,CAAA4C,aAAA,CAAAM,MAAA,CAAsB;;;;;IAoC5B5E,EADF,CAAAC,cAAA,eAAkE,gBACxC;IAAAD,EAAA,CAAAE,MAAA,GAAe;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAG1CH,EAFJ,CAAAC,cAAA,eAAuB,eAC8C,WAC3D;IAAAD,EAAA,CAAAE,MAAA,GAAwB;IAChCF,EADgC,CAAAG,YAAA,EAAO,EACjC;IAEJH,EADF,CAAAC,cAAA,eAA+D,WACvD;IAAAD,EAAA,CAAAE,MAAA,GAAsB;IAGlCF,EAHkC,CAAAG,YAAA,EAAO,EAC/B,EACF,EACF;;;;IAToBH,EAAA,CAAAqB,SAAA,GAAe;IAAfrB,EAAA,CAAA0B,iBAAA,CAAAmD,QAAA,CAAAtB,IAAA,CAAe;IAENvD,EAAA,CAAAqB,SAAA,GAAmC;IAAnCrB,EAAA,CAAAmC,WAAA,UAAA0C,QAAA,CAAAC,QAAA,OAAmC;IAC1D9E,EAAA,CAAAqB,SAAA,GAAwB;IAAxBrB,EAAA,CAAA0B,iBAAA,CAAAmD,QAAA,CAAAE,aAAA,CAAwB;IAEH/E,EAAA,CAAAqB,SAAA,EAAiC;IAAjCrB,EAAA,CAAAmC,WAAA,UAAA0C,QAAA,CAAAG,MAAA,OAAiC;IACtDhF,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAA0B,iBAAA,CAAAmD,QAAA,CAAAI,WAAA,CAAsB;;;;;;IAxYpCjF,EALR,CAAAC,cAAA,aAA8C,aAEN,aACV,aACC,eACb;IAAAD,EAAA,CAAAE,MAAA,aAAM;IAClBF,EADkB,CAAAG,YAAA,EAAW,EACvB;IAEJH,EADF,CAAAC,cAAA,aAAuB,SACjB;IAAAD,EAAA,CAAAE,MAAA,GAA4B;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACrCH,EAAA,CAAAC,cAAA,WAAqB;IAAAD,EAAA,CAAAE,MAAA,IAAuB;IAEhDF,EAFgD,CAAAG,YAAA,EAAI,EAC5C,EACF;IAKAH,EAHN,CAAAC,cAAA,cAA4B,eACE,eACD,oBACO;IAAAD,EAAA,CAAAE,MAAA,aAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAE5CH,EADF,CAAAC,cAAA,eAAyB,gBACI;IAAAD,EAAA,CAAAE,MAAA,IAAiB;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnDH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAGvCF,EAHuC,CAAAG,YAAA,EAAO,EACpC,EACF,EACF;IAGNH,EAAA,CAAAC,cAAA,kBAIwC;IAAhCD,EAAA,CAAAO,UAAA,mBAAA2E,2DAAA;MAAAlF,EAAA,CAAAU,aAAA,CAAAyE,GAAA;MAAA,MAAAtE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAuE,mBAAA,EAAqB;IAAA,EAAC;IACrCpF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IACzBF,EADyB,CAAAG,YAAA,EAAW,EAC3B;IAIPH,EADF,CAAAC,cAAA,kBAA0D,gBAC9C;IAAAD,EAAA,CAAAE,MAAA,sBAAc;IAC1BF,EAD0B,CAAAG,YAAA,EAAW,EAC5B;IAEPH,EADF,CAAAC,cAAA,yBAAiC,kBACe;IAAxBD,EAAA,CAAAO,UAAA,mBAAA8E,2DAAA;MAAArF,EAAA,CAAAU,aAAA,CAAAyE,GAAA;MAAA,MAAAtE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAyE,WAAA,EAAa;IAAA,EAAC;IAC3CtF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAClBF,EADkB,CAAAG,YAAA,EAAO,EAChB;IACTH,EAAA,CAAAC,cAAA,kBAAyC;IAAnBD,EAAA,CAAAO,UAAA,mBAAAgF,2DAAA;MAAAvF,EAAA,CAAAU,aAAA,CAAAyE,GAAA;MAAA,MAAAtE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAA2E,MAAA,EAAQ;IAAA,EAAC;IACtCxF,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,cAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,YAAM;IAAAD,EAAA,CAAAE,MAAA,wBAAW;IAIzBF,EAJyB,CAAAG,YAAA,EAAO,EACjB,EACA,EACP,EACF;IAUMH,EAPZ,CAAAC,cAAA,eAA0B,eAEK,oBACI,wBACX,eACa,eACD,gBACd;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAChC;IAEJH,EADF,CAAAC,cAAA,eAA0B,UACpB;IAAAD,EAAA,CAAAE,MAAA,iCAAyB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAClCH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,0DAA6C;IAClDF,EADkD,CAAAG,YAAA,EAAI,EAChD;IACNH,EAAA,CAAAC,cAAA,kBAAwF;IAA1BD,EAAA,CAAAO,UAAA,mBAAAkF,2DAAA;MAAAzF,EAAA,CAAAU,aAAA,CAAAyE,GAAA;MAAA,MAAAtE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAASF,MAAA,CAAAgC,aAAA,EAAe;IAAA,EAAC;IACrF7C,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAE,MAAA,kBAAU;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC/BH,EAAA,CAAAE,MAAA,iBACF;IAIRF,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;IAMAH,EAHN,CAAAC,cAAA,eAA8B,oBACI,wBACZ,UACZ;IAAAD,EAAA,CAAAE,MAAA,qDAAwC;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAG7CH,EAFJ,CAAAC,cAAA,eAA6B,eACA,eACA;IACvBD,EAAA,CAAAI,SAAA,eAA2E;IAEzEJ,EADF,CAAAC,cAAA,eAA4B,UACtB;IAAAD,EAAA,CAAAE,MAAA,IAAsB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAC/BH,EAAA,CAAAC,cAAA,SAAG;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAEnCF,EAFmC,CAAAG,YAAA,EAAI,EAC/B,EACF;IAEJH,EADF,CAAAC,cAAA,eAA2B,gBACM;IAAAD,EAAA,CAAAE,MAAA,IAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAE,MAAA,uBAAe;IAE9CF,EAF8C,CAAAG,YAAA,EAAO,EAC7C,EACF;IACNH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAI,SAAA,eAA+E;IACjFJ,EAAA,CAAAG,YAAA,EAAM;IAEJH,EADF,CAAAC,cAAA,eAA2B,YACnB;IAAAD,EAAA,CAAAE,MAAA,IAA0D;IAClEF,EADkE,CAAAG,YAAA,EAAO,EACnE;IAEJH,EADF,CAAAC,cAAA,eAAgC,SAC3B;IAAAD,EAAA,CAAAE,MAAA,IAA4B;IAKzCF,EALyC,CAAAG,YAAA,EAAI,EAC/B,EACF,EACW,EACV,EACP;IAKFH,EAFJ,CAAAC,cAAA,eAA6B,cACE,gBACjB;IAAAD,EAAA,CAAAE,MAAA,qBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAClCH,EAAA,CAAAE,MAAA,sCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,eAA0B;IACxBD,EAAA,CAAAK,UAAA,KAAAqF,6CAAA,yBACoE;IA6B1E1F,EAFI,CAAAG,YAAA,EAAM,EACF,EACF;IASIH,EANV,CAAAC,cAAA,eAA+B,eAEA,oBACI,uBACZ,sBACC,gBACJ;IAAAD,EAAA,CAAAE,MAAA,eAAO;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC5BH,EAAA,CAAAE,MAAA,mCACF;IAAAF,EAAA,CAAAG,YAAA,EAAiB;IAGbH,EAFJ,CAAAC,cAAA,eAA6B,0BAC+B,kBAC7C;IAAAD,EAAA,CAAAE,MAAA,qBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAY;IAC9BH,EAAA,CAAAC,cAAA,uBAA2E;IAA/DD,EAAA,CAAA2F,gBAAA,yBAAAC,sEAAAC,MAAA;MAAA7F,EAAA,CAAAU,aAAA,CAAAyE,GAAA;MAAA,MAAAtE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAAd,EAAA,CAAA8F,kBAAA,CAAAjF,MAAA,CAAAkF,cAAA,EAAAF,MAAA,MAAAhF,MAAA,CAAAkF,cAAA,GAAAF,MAAA;MAAA,OAAA7F,EAAA,CAAAe,WAAA,CAAA8E,MAAA;IAAA,EAA0B;IAAC7F,EAAA,CAAAO,UAAA,6BAAAyF,0EAAA;MAAAhG,EAAA,CAAAU,aAAA,CAAAyE,GAAA;MAAA,MAAAtE,MAAA,GAAAb,EAAA,CAAAc,aAAA;MAAA,OAAAd,EAAA,CAAAe,WAAA,CAAmBF,MAAA,CAAAoF,aAAA,EAAe;IAAA,EAAC;IACxEjG,EAAA,CAAAC,cAAA,uBAAyB;IAAAD,EAAA,CAAAE,MAAA,sBAAa;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACnDH,EAAA,CAAAC,cAAA,uBAA0B;IAAAD,EAAA,CAAAE,MAAA,gBAAO;IAAAF,EAAA,CAAAG,YAAA,EAAa;IAC9CH,EAAA,CAAAC,cAAA,uBAAyB;IAAAD,EAAA,CAAAE,MAAA,yBAAW;IAAAF,EAAA,CAAAG,YAAA,EAAa;IACjDH,EAAA,CAAAC,cAAA,uBAAwB;IAAAD,EAAA,CAAAE,MAAA,aAAI;IAIpCF,EAJoC,CAAAG,YAAA,EAAa,EAC9B,EACE,EACb,EACU;IAClBH,EAAA,CAAAC,cAAA,yBAAkB;IA4BhBD,EA3BA,CAAAK,UAAA,MAAA6F,yCAAA,kBAA6E,MAAAC,iDAAA,gCAAAnG,EAAA,CAAAoG,sBAAA,CA2BrD;IAW9BpG,EAFI,CAAAG,YAAA,EAAmB,EACV,EACP;IAOEH,EAJR,CAAAC,cAAA,gBAA2B,qBACI,wBACV,uBACC,iBACJ;IAAAD,EAAA,CAAAE,MAAA,kBAAS;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC9BH,EAAA,CAAAE,MAAA,mCACF;IACFF,EADE,CAAAG,YAAA,EAAiB,EACD;IAKVH,EAJR,CAAAC,cAAA,yBAAkB,gBACQ,gBACC,gBACE,iBACX;IAAAD,EAAA,CAAAE,MAAA,oBAAW;IACvBF,EADuB,CAAAG,YAAA,EAAW,EAC5B;IAEJH,EADF,CAAAC,cAAA,gBAA0B,iBACC;IAAAD,EAAA,CAAAE,MAAA,KAA+B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC/DH,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,2BAAa;IAE1CF,EAF0C,CAAAG,YAAA,EAAO,EACzC,EACF;IAGFH,EAFJ,CAAAC,cAAA,gBAAuB,gBACE,iBACX;IAAAD,EAAA,CAAAE,MAAA,sBAAa;IACzBF,EADyB,CAAAG,YAAA,EAAW,EAC9B;IAEJH,EADF,CAAAC,cAAA,gBAA0B,iBACC;IAAAD,EAAA,CAAAE,MAAA,KAA8B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC9DH,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,kCAAe;IAE5CF,EAF4C,CAAAG,YAAA,EAAO,EAC3C,EACF;IAGFH,EAFJ,CAAAC,cAAA,gBAAuB,gBACE,iBACX;IAAAD,EAAA,CAAAE,MAAA,wBAAe;IAC3BF,EAD2B,CAAAG,YAAA,EAAW,EAChC;IAEJH,EADF,CAAAC,cAAA,gBAA0B,iBACC;IAAAD,EAAA,CAAAE,MAAA,KAA6B;IAAAF,EAAA,CAAAG,YAAA,EAAO;IAC7DH,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,wBAAU;IAEvCF,EAFuC,CAAAG,YAAA,EAAO,EACtC,EACF;IAGFH,EAFJ,CAAAC,cAAA,gBAAuB,gBACE,iBACX;IAAAD,EAAA,CAAAE,MAAA,iBAAQ;IACpBF,EADoB,CAAAG,YAAA,EAAW,EACzB;IAEJH,EADF,CAAAC,cAAA,gBAA0B,iBACC;IAAAD,EAAA,CAAAE,MAAA,KAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAO;IACnEH,EAAA,CAAAC,cAAA,iBAAyB;IAAAD,EAAA,CAAAE,MAAA,8BAAgB;IAG/CF,EAH+C,CAAAG,YAAA,EAAO,EAC5C,EACF,EACF;IAIJH,EADF,CAAAC,cAAA,gBAA0B,WACpB;IAAAD,EAAA,CAAAE,MAAA,kCAAoB;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAE3BH,EADF,CAAAC,cAAA,gBAA6B,gBACH;IACtBD,EAAA,CAAAK,UAAA,MAAAgG,yCAAA,kBAA6D;IASzErG,EALU,CAAAG,YAAA,EAAM,EACF,EACF,EACW,EACV,EACP;IAKJH,EAFJ,CAAAC,cAAA,gBAA8B,eACF,iBACd;IAAAD,EAAA,CAAAE,MAAA,cAAK;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC1BH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,gBAA2B;IACzBD,EAAA,CAAAK,UAAA,MAAAiG,8CAAA,wBAAuG;IA2B3GtG,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,gBAAyB,eACG,iBACd;IAAAD,EAAA,CAAAE,MAAA,iBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAE,MAAA,0BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,gBAA0B;IACxBD,EAAA,CAAAK,UAAA,MAAAkG,yCAAA,kBAAuE;IAe3EvG,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,gBAAgC,eACJ,iBACd;IAAAD,EAAA,CAAAE,MAAA,qBAAY;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACjCH,EAAA,CAAAE,MAAA,oCACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACLH,EAAA,CAAAC,cAAA,gBAA6B;IAC3BD,EAAA,CAAAK,UAAA,MAAAmG,8CAAA,yBAA4E;IAuBhFxG,EADE,CAAAG,YAAA,EAAM,EACF;IAKFH,EAFJ,CAAAC,cAAA,gBAAgC,eACJ,iBACd;IAAAD,EAAA,CAAAE,MAAA,uBAAc;IAAAF,EAAA,CAAAG,YAAA,EAAW;IACnCH,EAAA,CAAAE,MAAA,6BACF;IAAAF,EAAA,CAAAG,YAAA,EAAK;IAKGH,EAJR,CAAAC,cAAA,qBAAkC,yBACd,gBACe,gBACI,WAC3B;IAAAD,EAAA,CAAAE,MAAA,iBAAQ;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACjBH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,KAAmC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC5DH,EAAA,CAAAC,cAAA,UAAG;IAAAD,EAAA,CAAAE,MAAA,KAAuD;IAC5DF,EAD4D,CAAAG,YAAA,EAAI,EAC1D;IAEJH,EADF,CAAAC,cAAA,gBAAwB,iBACZ;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAW;IAC3BH,EAAA,CAAAC,cAAA,aAAM;IAAAD,EAAA,CAAAE,MAAA,WAAE;IACVF,EADU,CAAAG,YAAA,EAAO,EACX;IAEJH,EADF,CAAAC,cAAA,gBAA+B,WACzB;IAAAD,EAAA,CAAAE,MAAA,eAAM;IAAAF,EAAA,CAAAG,YAAA,EAAK;IACfH,EAAA,CAAAC,cAAA,gBAAmB;IAAAD,EAAA,CAAAE,MAAA,KAAiC;IAAAF,EAAA,CAAAG,YAAA,EAAM;IAC1DH,EAAA,CAAAC,cAAA,UAAG;IAAAD,EAAA,CAAAE,MAAA,KAAqD;IAE5DF,EAF4D,CAAAG,YAAA,EAAI,EACxD,EACF;IACNH,EAAA,CAAAC,cAAA,gBAA8B;IAC5BD,EAAA,CAAAK,UAAA,MAAAoG,yCAAA,mBAAkE;IAe5EzG,EAJQ,CAAAG,YAAA,EAAM,EACW,EACV,EACP,EACF,EArZwC;;;;;;IAQlCH,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAA6B,kBAAA,aAAAhB,MAAA,CAAAK,IAAA,CAAAqC,IAAA,oBAA4B;IACXvD,EAAA,CAAAqB,SAAA,GAAuB;IAAvBrB,EAAA,CAAA6B,kBAAA,4BAAAhB,MAAA,CAAAK,IAAA,CAAAwF,IAAA,KAAuB;IASb1G,EAAA,CAAAqB,SAAA,GAAiB;IAAjBrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAK,IAAA,CAAAC,MAAA,CAAiB;IAQ1CnB,EAAA,CAAAqB,SAAA,GAAgC;IAEhCrB,EAFA,CAAAsB,UAAA,aAAAT,MAAA,CAAA8F,mBAAA,CAAgC,mBAAA9F,MAAA,CAAA8F,mBAAA,OAEY;IAM5B3G,EAAA,CAAAqB,SAAA,GAAiC;IAAjCrB,EAAA,CAAAsB,UAAA,sBAAAsF,eAAA,CAAiC;IA+C1C5G,EAAA,CAAAqB,SAAA,IAAwB;IAACrB,EAAzB,CAAAsB,UAAA,QAAAT,MAAA,CAAAgG,UAAA,CAAAC,KAAA,EAAA9G,EAAA,CAAAwB,aAAA,CAAwB,QAAAX,MAAA,CAAAgG,UAAA,CAAApF,KAAA,CAAyB;IAEhDzB,EAAA,CAAAqB,SAAA,GAAsB;IAAtBrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAgG,UAAA,CAAApF,KAAA,CAAsB;IACvBzB,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAgG,UAAA,CAAAlF,WAAA,CAA4B;IAIF3B,EAAA,CAAAqB,SAAA,GAA6B;IAA7BrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAkG,qBAAA,GAA6B;IAKnC/G,EAAA,CAAAqB,SAAA,GAA6C;IAA7CrB,EAAA,CAAAmC,WAAA,UAAAtB,MAAA,CAAAmG,qBAAA,SAA6C;IAGlEhH,EAAA,CAAAqB,SAAA,GAA0D;IAA1DrB,EAAA,CAAAyE,kBAAA,KAAA5D,MAAA,CAAAK,IAAA,CAAAC,MAAA,SAAAN,MAAA,CAAAgG,UAAA,CAAAzF,cAAA,YAA0D;IAG7DpB,EAAA,CAAAqB,SAAA,GAA4B;IAA5BrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAoG,oBAAA,GAA4B;IAcRjH,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAsB,UAAA,YAAAT,MAAA,CAAAqG,gBAAA,CAAmB;IA6C9BlH,EAAA,CAAAqB,SAAA,IAA0B;IAA1BrB,EAAA,CAAAmH,gBAAA,UAAAtG,MAAA,CAAAkF,cAAA,CAA0B;IAUf/F,EAAA,CAAAqB,SAAA,IAAkC;IAAArB,EAAlC,CAAAsB,UAAA,SAAAT,MAAA,CAAA6B,eAAA,CAAA0E,MAAA,KAAkC,aAAAC,aAAA,CAAc;IAwD5CrH,EAAA,CAAAqB,SAAA,IAA+B;IAA/BrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAyG,YAAA,CAAAC,YAAA,CAA+B;IAS/BvH,EAAA,CAAAqB,SAAA,GAA8B;IAA9BrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAyG,YAAA,CAAAE,WAAA,CAA8B;IAS9BxH,EAAA,CAAAqB,SAAA,GAA6B;IAA7BrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAyG,YAAA,CAAAG,UAAA,CAA6B;IAS7BzH,EAAA,CAAAqB,SAAA,GAAmC;IAAnCrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAyG,YAAA,CAAAI,gBAAA,CAAmC;IAWtC1H,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAsB,UAAA,YAAAT,MAAA,CAAA8G,gBAAA,CAAmB;IAkBrB3H,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAsB,UAAA,YAAAT,MAAA,CAAA+G,gBAAA,CAAmB;IAoCC5H,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAsB,UAAA,YAAAT,MAAA,CAAAgH,gBAAA,CAAmB;IAwBrC7H,EAAA,CAAAqB,SAAA,GAAmB;IAAnBrB,EAAA,CAAAsB,UAAA,YAAAT,MAAA,CAAAiH,gBAAA,CAAmB;IAoC1B9H,EAAA,CAAAqB,SAAA,IAAmC;IAAnCrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAkH,cAAA,CAAAjD,QAAA,CAAAkD,KAAA,CAAmC;IACnDhI,EAAA,CAAAqB,SAAA,GAAuD;IAAvDrB,EAAA,CAAA6B,kBAAA,KAAAhB,MAAA,CAAAkH,cAAA,CAAAjD,QAAA,CAAAmD,YAAA,kBAAuD;IAQvCjI,EAAA,CAAAqB,SAAA,IAAiC;IAAjCrB,EAAA,CAAA0B,iBAAA,CAAAb,MAAA,CAAAkH,cAAA,CAAA/C,MAAA,CAAAgD,KAAA,CAAiC;IACjDhI,EAAA,CAAAqB,SAAA,GAAqD;IAArDrB,EAAA,CAAA6B,kBAAA,KAAAhB,MAAA,CAAAkH,cAAA,CAAA/C,MAAA,CAAAiD,YAAA,kBAAqD;IAIZjI,EAAA,CAAAqB,SAAA,GAAkB;IAAlBrB,EAAA,CAAAsB,UAAA,YAAAT,MAAA,CAAAqH,eAAA,CAAkB;;;AD3U1E,OAAM,MAAOC,kBAAkB;EAiD7BC,YACUC,WAAwB,EACxBC,MAAc,EACdC,eAAuC;IAFvC,KAAAF,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,eAAe,GAAfA,eAAe;IAnDzB,KAAArH,IAAI,GAAgB,IAAI;IAExB;IACA,KAAAsH,aAAa,GAAwB,EAAE;IACvC,KAAAC,eAAe,GAAW,EAAE;IAC5B,KAAAC,WAAW,GAAiB,EAAE;IAC9B,KAAAd,gBAAgB,GAAc,EAAE;IAChC,KAAAE,gBAAgB,GAAgB,EAAE;IAElC;IACA,KAAAC,cAAc,GAAG;MACfjD,QAAQ,EAAE;QAAEkD,KAAK,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAC,CAAE;MACvCjD,MAAM,EAAE;QAAEgD,KAAK,EAAE,CAAC;QAAEC,YAAY,EAAE;MAAC;KACpC;IAED,KAAAC,eAAe,GAAG,CAChB;MAAE3E,IAAI,EAAE,kBAAkB;MAAEuB,QAAQ,EAAE,CAAC;MAAEE,MAAM,EAAE,CAAC;MAAED,aAAa,EAAE,CAAC;MAAEE,WAAW,EAAE;IAAC,CAAE,EACtF;MAAE1B,IAAI,EAAE,sBAAsB;MAAEuB,QAAQ,EAAE,CAAC;MAAEE,MAAM,EAAE,CAAC;MAAED,aAAa,EAAE,CAAC;MAAEE,WAAW,EAAE;IAAC,CAAE,EAC1F;MAAE1B,IAAI,EAAE,oBAAoB;MAAEuB,QAAQ,EAAE,CAAC;MAAEE,MAAM,EAAE,CAAC;MAAED,aAAa,EAAE,CAAC;MAAEE,WAAW,EAAE;IAAC,CAAE,CACzF;IAED,KAAA4C,gBAAgB,GAAG,CACjB;MACEpG,KAAK,EAAE,eAAe;MACtB4C,OAAO,EAAE,CACP;QAAE5C,KAAK,EAAE,iBAAiB;QAAEyC,IAAI,EAAE,OAAO;QAAED,KAAK,EAAE,SAAS;QAAE9C,MAAM,EAAE;MAAE,CAAE,EACzE;QAAEM,KAAK,EAAE,mBAAmB;QAAEyC,IAAI,EAAE,MAAM;QAAED,KAAK,EAAE,SAAS;QAAE9C,MAAM,EAAE;MAAE,CAAE,EAC1E;QAAEM,KAAK,EAAE,WAAW;QAAEyC,IAAI,EAAE,WAAW;QAAED,KAAK,EAAE,SAAS;QAAE9C,MAAM,EAAE;MAAE,CAAE;KAE1E,EACD;MACEM,KAAK,EAAE,QAAQ;MACf4C,OAAO,EAAE,CACP;QAAE5C,KAAK,EAAE,sBAAsB;QAAEyC,IAAI,EAAE,SAAS;QAAED,KAAK,EAAE,QAAQ;QAAE9C,MAAM,EAAE;MAAE,CAAE,EAC/E;QAAEM,KAAK,EAAE,iBAAiB;QAAEyC,IAAI,EAAE,QAAQ;QAAED,KAAK,EAAE,QAAQ;QAAE9C,MAAM,EAAE;MAAE,CAAE,EACzE;QAAEM,KAAK,EAAE,oBAAoB;QAAEyC,IAAI,EAAE,YAAY;QAAED,KAAK,EAAE,QAAQ;QAAE9C,MAAM,EAAE;MAAE,CAAE;KAEnF,EACD;MACEM,KAAK,EAAE,SAAS;MAChB4C,OAAO,EAAE,CACP;QAAE5C,KAAK,EAAE,mBAAmB;QAAEyC,IAAI,EAAE,MAAM;QAAED,KAAK,EAAE,MAAM;QAAE9C,MAAM,EAAE;MAAE,CAAE,EACvE;QAAEM,KAAK,EAAE,mBAAmB;QAAEyC,IAAI,EAAE,YAAY;QAAED,KAAK,EAAE,MAAM;QAAE9C,MAAM,EAAE;MAAE,CAAE,EAC7E;QAAEM,KAAK,EAAE,oBAAoB;QAAEyC,IAAI,EAAE,gBAAgB;QAAED,KAAK,EAAE,MAAM;QAAE9C,MAAM,EAAE;MAAE,CAAE;KAErF,CACF;EAME;EAEHwH,QAAQA,CAAA;IACN,IAAI,CAACN,WAAW,CAACO,YAAY,CAACC,SAAS,CAAC3H,IAAI,IAAG;MAC7C,IAAI,CAACA,IAAI,GAAGA,IAAI;MAChB,IAAIA,IAAI,EAAE;QACR;QACA,IAAIA,IAAI,CAAC4H,IAAI,KAAK/I,QAAQ,CAACgJ,IAAI,EAAE;UAC/B,IAAI,CAACR,eAAe,CAACS,uBAAuB,CAAC9H,IAAI,CAAC;UAClD;;QAEF,IAAI,CAAC+H,iBAAiB,EAAE;;IAE5B,CAAC,CAAC;EACJ;EAEQA,iBAAiBA,CAAA;IACvB,IAAI,CAACC,iBAAiB,EAAE;IACxB,IAAI,CAACC,mBAAmB,EAAE;IAC1B,IAAI,CAACC,eAAe,EAAE;IACtB,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,oBAAoB,EAAE;IAC3B,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEQL,iBAAiBA,CAAA;IACvB;IACA,IAAI,CAACV,aAAa,GAAG,CACnB;MACEgB,EAAE,EAAE,GAAG;MACP7H,WAAW,EAAE,sCAAsC;MACnDR,MAAM,EAAE,EAAE;MACVkB,IAAI,EAAE,aAAa;MACnBG,SAAS,EAAE,IAAIiH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE;KACX,EACD;MACEJ,EAAE,EAAE,GAAG;MACP7H,WAAW,EAAE,2CAA2C;MACxDR,MAAM,EAAE,EAAE;MACVkB,IAAI,EAAE,QAAQ;MACdG,SAAS,EAAE,IAAIiH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE;KACX,EACD;MACEJ,EAAE,EAAE,GAAG;MACP7H,WAAW,EAAE,yCAAyC;MACtDR,MAAM,EAAE,EAAE;MACVkB,IAAI,EAAE,WAAW;MACjBG,SAAS,EAAE,IAAIiH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC;MAChDC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE;KACX,EACD;MACEJ,EAAE,EAAE,GAAG;MACP7H,WAAW,EAAE,sCAAsC;MACnDR,MAAM,EAAE,EAAE;MACVkB,IAAI,EAAE,SAAS;MACfG,SAAS,EAAE,IAAIiH,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,GAAG,CAAC;MACjDC,MAAM,EAAE,OAAO;MACfC,QAAQ,EAAE;KACX,CACF;EACH;EAEQT,mBAAmBA,CAAA;IACzB,IAAI,CAACV,eAAe,GAAG,CACrB;MAAEoB,GAAG,EAAE,MAAM;MAAEtG,IAAI,EAAE,eAAe;MAAEmD,IAAI,EAAE,UAAU;MAAEvF,MAAM,EAAE,IAAI;MAAE2I,KAAK,EAAE,EAAE;MAAEhB,IAAI,EAAE/I,QAAQ,CAACgJ,IAAI;MAAEgB,SAAS,EAAE,IAAIN,IAAI,EAAE;MAAEO,SAAS,EAAE,IAAIP,IAAI,EAAE;MAAEQ,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,EACjL;MAAEL,GAAG,EAAE,MAAM;MAAEtG,IAAI,EAAE,gBAAgB;MAAEmD,IAAI,EAAE,QAAQ;MAAEvF,MAAM,EAAE,IAAI;MAAE2I,KAAK,EAAE,EAAE;MAAEhB,IAAI,EAAE/I,QAAQ,CAACgJ,IAAI;MAAEgB,SAAS,EAAE,IAAIN,IAAI,EAAE;MAAEO,SAAS,EAAE,IAAIP,IAAI,EAAE;MAAEQ,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,EAChL;MAAEL,GAAG,EAAE,MAAM;MAAEtG,IAAI,EAAE,gBAAgB;MAAEmD,IAAI,EAAE,UAAU;MAAEvF,MAAM,EAAE,IAAI;MAAE2I,KAAK,EAAE,EAAE;MAAEhB,IAAI,EAAE/I,QAAQ,CAACgJ,IAAI;MAAEgB,SAAS,EAAE,IAAIN,IAAI,EAAE;MAAEO,SAAS,EAAE,IAAIP,IAAI,EAAE;MAAEQ,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,EAClL;MAAEL,GAAG,EAAE,MAAM;MAAEtG,IAAI,EAAE,gBAAgB;MAAEmD,IAAI,EAAE,QAAQ;MAAEvF,MAAM,EAAE,GAAG;MAAE2I,KAAK,EAAE,EAAE;MAAEhB,IAAI,EAAE/I,QAAQ,CAACgJ,IAAI;MAAEgB,SAAS,EAAE,IAAIN,IAAI,EAAE;MAAEO,SAAS,EAAE,IAAIP,IAAI,EAAE;MAAEQ,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,EAC/K;MAAEL,GAAG,EAAE,MAAM;MAAEtG,IAAI,EAAE,gBAAgB;MAAEmD,IAAI,EAAE,UAAU;MAAEvF,MAAM,EAAE,GAAG;MAAE2I,KAAK,EAAE,EAAE;MAAEhB,IAAI,EAAE/I,QAAQ,CAACgJ,IAAI;MAAEgB,SAAS,EAAE,IAAIN,IAAI,EAAE;MAAEO,SAAS,EAAE,IAAIP,IAAI,EAAE;MAAEQ,QAAQ,EAAE,IAAI;MAAEC,OAAO,EAAE;IAAE,CAAE,CAClL;EACH;EAEQd,eAAeA,CAAA;IACrB,IAAI,CAACV,WAAW,GAAG,CACjB;MACEc,EAAE,EAAE,QAAQ;MACZ/H,KAAK,EAAE,mCAAmC;MAC1CE,WAAW,EAAE,gDAAgD;MAC7DwI,IAAI,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACpD3H,QAAQ,EAAE,mBAAmB;MAC7ByB,QAAQ,EAAE,eAAe;MACzBrC,MAAM,EAAE,EAAE;MACV2F,KAAK,EAAE,gFAAgF;MACvFmB,YAAY,EAAE,EAAE;MAChBmC,eAAe,EAAE;KAClB,EACD;MACEZ,EAAE,EAAE,QAAQ;MACZ/H,KAAK,EAAE,6BAA6B;MACpCE,WAAW,EAAE,8CAA8C;MAC3DwI,IAAI,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACpD3H,QAAQ,EAAE,kBAAkB;MAC5ByB,QAAQ,EAAE,SAAS;MACnBrC,MAAM,EAAE,EAAE;MACV2F,KAAK,EAAE,mFAAmF;MAC1FmB,YAAY,EAAE,GAAG;MACjBmC,eAAe,EAAE;KAClB,EACD;MACEZ,EAAE,EAAE,QAAQ;MACZ/H,KAAK,EAAE,iCAAiC;MACxCE,WAAW,EAAE,sDAAsD;MACnEwI,IAAI,EAAE,IAAIV,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC;MACpD3H,QAAQ,EAAE,sBAAsB;MAChCyB,QAAQ,EAAE,WAAW;MACrBrC,MAAM,EAAE,EAAE;MACV2F,KAAK,EAAE,mFAAmF;MAC1FmB,YAAY,EAAE,EAAE;MAChBmC,eAAe,EAAE;KAClB,CACF;EACH;EAEQf,oBAAoBA,CAAA;IAC1B,IAAI,CAACzB,gBAAgB,GAAG,CACtB;MACE4B,EAAE,EAAE,UAAU;MACdjG,IAAI,EAAE,iBAAiB;MACvBC,QAAQ,EAAE,YAAY;MACtBF,IAAI,EAAE,gFAAgF;MACtFI,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,GAAG;MACZC,YAAY,EAAE,iCAAiC;MAC/C7B,QAAQ,EAAE,oBAAoB;MAC9BsI,MAAM,EAAE;KACT,EACD;MACEb,EAAE,EAAE,UAAU;MACdjG,IAAI,EAAE,2BAA2B;MACjCC,QAAQ,EAAE,WAAW;MACrBF,IAAI,EAAE,mFAAmF;MACzFI,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,EAAE;MACXC,YAAY,EAAE,iCAAiC;MAC/C7B,QAAQ,EAAE,kBAAkB;MAC5BsI,MAAM,EAAE;KACT,EACD;MACEb,EAAE,EAAE,UAAU;MACdjG,IAAI,EAAE,qBAAqB;MAC3BC,QAAQ,EAAE,WAAW;MACrBF,IAAI,EAAE,mFAAmF;MACzFI,MAAM,EAAE,GAAG;MACXC,OAAO,EAAE,GAAG;MACZC,YAAY,EAAE,gCAAgC;MAC9C7B,QAAQ,EAAE,uBAAuB;MACjCsI,MAAM,EAAE;KACT,CACF;EACH;EAEQf,oBAAoBA,CAAA;IAC1B,IAAI,CAACxB,gBAAgB,GAAG,CACtB;MACE0B,EAAE,EAAE,YAAY;MAChB/H,KAAK,EAAE,aAAa;MACpBE,WAAW,EAAE,qDAAqD;MAClEuC,IAAI,EAAE,KAAK;MACXK,QAAQ,EAAE,2CAA2C;MACrDC,QAAQ,EAAE,EAAE;MACZE,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,CAAC;MACTC,MAAM,EAAE,sCAAsC;MAC9C0F,OAAO,EAAE,IAAIb,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;KAC1D,EACD;MACEF,EAAE,EAAE,YAAY;MAChB/H,KAAK,EAAE,sBAAsB;MAC7BE,WAAW,EAAE,sCAAsC;MACnDuC,IAAI,EAAE,MAAM;MACZK,QAAQ,EAAE,2CAA2C;MACrDC,QAAQ,EAAE,EAAE;MACZE,OAAO,EAAE,CAAC;MACVC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,2CAA2C;MACnD0F,OAAO,EAAE,IAAIb,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;KAC1D,EACD;MACEF,EAAE,EAAE,YAAY;MAChB/H,KAAK,EAAE,0BAA0B;MACjCE,WAAW,EAAE,mCAAmC;MAChDuC,IAAI,EAAE,oBAAoB;MAC1BK,QAAQ,EAAE,2CAA2C;MACrDC,QAAQ,EAAE,EAAE;MACZE,OAAO,EAAE,EAAE;MACXC,MAAM,EAAE,EAAE;MACVC,MAAM,EAAE,qCAAqC;MAC7C0F,OAAO,EAAE,IAAIb,IAAI,CAACA,IAAI,CAACC,GAAG,EAAE,GAAG,IAAI,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;KAC1D,CACF;EACH;EAEQH,kBAAkBA,CAAA;IACxB;IACA,IAAI,CAACxB,cAAc,GAAG;MACpBjD,QAAQ,EAAE;QAAEkD,KAAK,EAAE,KAAK;QAAEC,YAAY,EAAE;MAAG,CAAE;MAC7CjD,MAAM,EAAE;QAAEgD,KAAK,EAAE,KAAK;QAAEC,YAAY,EAAE;MAAG;KAC1C;IAED,IAAI,CAACC,eAAe,GAAG,CACrB;MACE3E,IAAI,EAAE,kBAAkB;MACxBuB,QAAQ,EAAE,EAAE;MACZE,MAAM,EAAE,EAAE;MACVD,aAAa,EAAE,IAAI;MACnBE,WAAW,EAAE;KACd,EACD;MACE1B,IAAI,EAAE,sBAAsB;MAC5BuB,QAAQ,EAAE,EAAE;MACZE,MAAM,EAAE,EAAE;MACVD,aAAa,EAAE,EAAE;MACjBE,WAAW,EAAE;KACd,EACD;MACE1B,IAAI,EAAE,oBAAoB;MAC1BuB,QAAQ,EAAE,EAAE;MACZE,MAAM,EAAE,EAAE;MACVD,aAAa,EAAE,EAAE;MACjBE,WAAW,EAAE;KACd,CACF;EACH;EAEA;EACAsF,WAAWA,CAAA;IACT,MAAMC,IAAI,GAAG,IAAIf,IAAI,EAAE,CAACgB,QAAQ,EAAE;IAClC,IAAID,IAAI,GAAG,EAAE,EAAE,OAAO,SAAS;IAC/B,IAAIA,IAAI,GAAG,EAAE,EAAE,OAAO,gBAAgB;IACtC,OAAO,SAAS;EAClB;EAEAE,WAAWA,CAAA;IACT,IAAI,CAAC,IAAI,CAACxJ,IAAI,EAAE,OAAO,eAAe;IACtC,OAAO,IAAI,CAACA,IAAI,CAACwF,IAAI,KAAK,UAAU,GAAG,iBAAiB,GAAG,QAAQ;EACrE;EAEAiE,gBAAgBA,CAAA;IACd,IAAI,CAAC,IAAI,CAACzJ,IAAI,EAAE,OAAO,CAAC;IACxB,MAAM0J,SAAS,GAAG,IAAI,CAACnC,eAAe,CAACoC,SAAS,CAACC,CAAC,IAAIA,CAAC,CAACjB,GAAG,KAAK,IAAI,CAAC3I,IAAK,CAAC2I,GAAG,CAAC;IAC/E,OAAOe,SAAS,IAAI,CAAC,GAAGA,SAAS,GAAG,CAAC,GAAG,IAAI,CAACnC,eAAe,CAACrB,MAAM,GAAG,CAAC;EACzE;EAEA2D,cAAcA,CAAA;IACZ,IAAI,CAAC,IAAI,CAAC7J,IAAI,EAAE,OAAO,CAAC;IACxB;IACA,OAAO8J,IAAI,CAACC,KAAK,CAAC,IAAI,CAAC/J,IAAI,CAACC,MAAM,GAAG,GAAG,GAAG,CAAC,IAAI,CAACD,IAAI,CAACgJ,OAAO,EAAE9C,MAAM,IAAI,CAAC,IAAI,CAAC,CAAC;EAClF;EAEA8D,eAAeA,CAAA;IACb,IAAI,CAAC,IAAI,CAAChK,IAAI,EAAE,OAAO,CAAC;IACxB,MAAMiK,QAAQ,GAAG,IAAI,CAACjK,IAAI,CAACwF,IAAI,KAAK,UAAU,GAAG,IAAI,CAACqB,cAAc,CAACjD,QAAQ,GAAG,IAAI,CAACiD,cAAc,CAAC/C,MAAM;IAC1G,MAAMoG,aAAa,GAAG,KAAK;IAC3B,OAAOJ,IAAI,CAACK,GAAG,CAAEF,QAAQ,CAACnD,KAAK,GAAGoD,aAAa,GAAI,GAAG,EAAE,GAAG,CAAC,CAAC,CAAC;EAChE;EAEAE,sBAAsBA,CAAA;IACpB,IAAI,CAAC,IAAI,CAACpK,IAAI,EAAE,OAAO,CAAC;IACxB,MAAMiK,QAAQ,GAAG,IAAI,CAACjK,IAAI,CAACwF,IAAI,KAAK,UAAU,GAAG,IAAI,CAACqB,cAAc,CAACjD,QAAQ,GAAG,IAAI,CAACiD,cAAc,CAAC/C,MAAM;IAC1G,MAAMoG,aAAa,GAAG,KAAK;IAC3B,OAAOJ,IAAI,CAACK,GAAG,CAACL,IAAI,CAACO,KAAK,CAAEJ,QAAQ,CAACnD,KAAK,GAAGoD,aAAa,GAAI,GAAG,CAAC,EAAE,GAAG,CAAC;EAC1E;EAEAI,YAAYA,CAAA;IACV,IAAI,CAAC,IAAI,CAACtK,IAAI,EAAE,OAAO;MAAEuK,gBAAgB,EAAE,CAAC;MAAEC,WAAW,EAAE;IAAC,CAAE;IAC9D,MAAMP,QAAQ,GAAG,IAAI,CAACjK,IAAI,CAACwF,IAAI,KAAK,UAAU,GAAG,IAAI,CAACqB,cAAc,CAACjD,QAAQ,GAAG,IAAI,CAACiD,cAAc,CAAC/C,MAAM;IAC1G,OAAO;MACLyG,gBAAgB,EAAET,IAAI,CAACC,KAAK,CAACE,QAAQ,CAACnD,KAAK,GAAG,EAAE,CAAC;MACjD0D,WAAW,EAAEP,QAAQ,CAAClD;KACvB;EACH;EAEA0D,gBAAgBA,CAACtJ,IAAY;IAC3B,MAAMuJ,QAAQ,GAA8B;MAC1C,aAAa,EAAE,SAAS;MACxB,QAAQ,EAAE,SAAS;MACnB,WAAW,EAAE,SAAS;MACtB,SAAS,EAAE,SAAS;MACpB,QAAQ,EAAE;KACX;IACD,OAAOA,QAAQ,CAACvJ,IAAI,CAAC,IAAI,SAAS;EACpC;EAEAwJ,eAAeA,CAACxJ,IAAY;IAC1B,MAAMyJ,OAAO,GAA8B;MACzC,aAAa,EAAE,KAAK;MACpB,QAAQ,EAAE,oBAAoB;MAC9B,WAAW,EAAE,QAAQ;MACrB,SAAS,EAAE,gBAAgB;MAC3B,QAAQ,EAAE;KACX;IACD,OAAOA,OAAO,CAACzJ,IAAI,CAAC,IAAI,QAAQ;EAClC;EAEA0J,cAAcA,CAACC,KAAa;IAC1B,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,SAAS,EAAE,SAAS,CAAC,CAAC,CAAC;IAClD,OAAOA,MAAM,CAACD,KAAK,CAAC,IAAI,SAAS;EACnC;EAEAE,aAAaA,CAACF,KAAa;IACzB,OAAOA,KAAK,GAAG,CAAC,GAAG,cAAc,GAAG,MAAM;EAC5C;EAEAvI,QAAQA,CAACC,MAAc;IACrB,OAAOyI,KAAK,CAACnB,IAAI,CAACC,KAAK,CAACvH,MAAM,CAAC,CAAC,CAAC0I,IAAI,CAAC,CAAC,CAAC;EAC1C;EAEA;EACAC,SAASA,CAACC,KAAiB;IACzB;IACAC,OAAO,CAACC,GAAG,CAAC,gBAAgB,EAAEF,KAAK,CAAC;EACtC;EAEAlJ,WAAWA,CAACqJ,OAAgB;IAC1B;IACAF,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAEC,OAAO,CAAC;EAC1C;EAEAzI,aAAaA,CAAC0I,MAAW;IACvB;IACAH,OAAO,CAACC,GAAG,CAAC,mBAAmB,EAAEE,MAAM,CAAC;IACxC;EACF;EAEAC,eAAeA,CAAA;IACb,IAAI,CAACrE,MAAM,CAACsE,QAAQ,CAAC,CAAC,qBAAqB,CAAC,CAAC;EAC/C;;;uBAhYWzE,kBAAkB,EAAAnI,EAAA,CAAA6M,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAA/M,EAAA,CAAA6M,iBAAA,CAAAG,EAAA,CAAAC,MAAA,GAAAjN,EAAA,CAAA6M,iBAAA,CAAAK,EAAA,CAAAC,sBAAA;IAAA;EAAA;;;YAAlBhF,kBAAkB;MAAAiF,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,4BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UC3D/B1N,EAAA,CAAAK,UAAA,IAAAuN,iCAAA,oBAA8C;;;UAAZ5N,EAAA,CAAAsB,UAAA,SAAAqM,GAAA,CAAAzM,IAAA,CAAU", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}