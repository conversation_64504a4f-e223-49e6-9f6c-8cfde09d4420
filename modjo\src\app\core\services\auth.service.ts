import { Injectable } from '@angular/core';
import { Auth, signInWithEmailAndPassword, createUserWithEmailAndPassword, signOut, user, User as FirebaseUser } from '@angular/fire/auth';
import { Firestore, doc, setDoc, getDoc, updateDoc } from '@angular/fire/firestore';
import { BehaviorSubject, Observable, from, map, switchMap, of } from 'rxjs';
import { User, CreateUserRequest, UserRole } from '../models';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    private auth: Auth,
    private firestore: Firestore
  ) {
    // Listen to auth state changes
    user(this.auth).subscribe(async (firebaseUser) => {
      if (firebaseUser) {
        const userData = await this.getUserData(firebaseUser.uid);
        this.currentUserSubject.next(userData);
      } else {
        this.currentUserSubject.next(null);
      }
    });
  }

  async register(userData: CreateUserRequest): Promise<User> {
    try {
      // Create Firebase Auth user
      const credential = await createUserWithEmailAndPassword(
        this.auth,
        userData.email,
        userData.password
      );

      // Create user document in Firestore
      const user: User = {
        uid: credential.user.uid,
        email: userData.email,
        name: userData.name,
        phone: userData.phone,
        city: userData.city,
        role: userData.role,
        points: 0,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        history: []
      };

      await setDoc(doc(this.firestore, 'users', user.uid), user);
      this.currentUserSubject.next(user);
      
      return user;
    } catch (error) {
      console.error('Registration error:', error);
      throw error;
    }
  }

  async login(email: string, password: string): Promise<User> {
    try {
      const credential = await signInWithEmailAndPassword(this.auth, email, password);
      const userData = await this.getUserData(credential.user.uid);
      this.currentUserSubject.next(userData);
      return userData;
    } catch (error) {
      console.error('Login error:', error);
      throw error;
    }
  }

  async logout(): Promise<void> {
    try {
      await signOut(this.auth);
      this.currentUserSubject.next(null);
    } catch (error) {
      console.error('Logout error:', error);
      throw error;
    }
  }

  private async getUserData(uid: string): Promise<User> {
    const userDoc = await getDoc(doc(this.firestore, 'users', uid));
    if (userDoc.exists()) {
      return userDoc.data() as User;
    }
    throw new Error('User data not found');
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  isAuthenticated(): boolean {
    return this.currentUserSubject.value !== null;
  }

  hasRole(role: UserRole): boolean {
    const user = this.getCurrentUser();
    return user?.role === role;
  }

  hasAnyRole(roles: UserRole[]): boolean {
    const user = this.getCurrentUser();
    return user ? roles.includes(user.role) : false;
  }

  async updateUserProfile(updates: Partial<User>): Promise<void> {
    const user = this.getCurrentUser();
    if (!user) throw new Error('No authenticated user');

    const updatedUser = { ...user, ...updates, updatedAt: new Date() };
    await updateDoc(doc(this.firestore, 'users', user.uid), updatedUser);
    this.currentUserSubject.next(updatedUser);
  }

  // Password reset
  async resetPassword(email: string): Promise<void> {
    // Implementation would use Firebase Auth sendPasswordResetEmail
    // For now, just a placeholder
    console.log('Password reset requested for:', email);
  }
}
