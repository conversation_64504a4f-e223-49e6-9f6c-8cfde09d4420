import { Injectable } from '@angular/core';
// import { AngularFireAuth } from '@angular/fire/compat/auth';
// import { AngularFirestore } from '@angular/fire/compat/firestore';
import { BehaviorSubject, Observable, from, map, switchMap, of } from 'rxjs';
import { User, CreateUserRequest, UserRole } from '../models';

@Injectable({
  providedIn: 'root'
})
export class AuthService {
  private currentUserSubject = new BehaviorSubject<User | null>(null);
  public currentUser$ = this.currentUserSubject.asObservable();

  constructor(
    // private auth: AngularFireAuth,
    // private firestore: AngularFirestore
  ) {
    // Temporarily disabled for initial setup
    // Create a mock user for testing
    const mockUser: User = {
      uid: 'mock-user-123',
      email: '<EMAIL>',
      name: 'Utilisateur Test',
      city: 'Monastir',
      role: UserRole.USER,
      points: 150,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      history: []
    };
    this.currentUserSubject.next(mockUser);
  }

  async register(userData: CreateUserRequest): Promise<User> {
    // Mock registration for initial setup
    const user: User = {
      uid: 'mock-user-' + Date.now(),
      email: userData.email,
      name: userData.name,
      phone: userData.phone,
      city: userData.city,
      role: userData.role,
      points: 0,
      createdAt: new Date(),
      updatedAt: new Date(),
      isActive: true,
      history: []
    };

    this.currentUserSubject.next(user);
    return Promise.resolve(user);
  }

  async login(email: string, password: string): Promise<User> {
    // Mock login for initial setup
    const mockUsers: { [key: string]: User } = {
      '<EMAIL>': {
        uid: 'user-123',
        email: '<EMAIL>',
        name: 'Utilisateur Test',
        city: 'Monastir',
        role: UserRole.USER,
        points: 150,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        history: []
      },
      '<EMAIL>': {
        uid: 'admin-123',
        email: '<EMAIL>',
        name: 'Admin Test',
        city: 'Sousse',
        role: UserRole.ADMIN,
        points: 500,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        history: []
      },
      '<EMAIL>': {
        uid: 'validator-123',
        email: '<EMAIL>',
        name: 'Validateur Test',
        city: 'Monastir',
        role: UserRole.VALIDATOR,
        points: 300,
        createdAt: new Date(),
        updatedAt: new Date(),
        isActive: true,
        history: []
      }
    };

    const user = mockUsers[email];
    if (user && password === 'password123') {
      this.currentUserSubject.next(user);
      return Promise.resolve(user);
    }

    throw new Error('Invalid credentials');
  }

  async logout(): Promise<void> {
    this.currentUserSubject.next(null);
    return Promise.resolve();
  }

  private async getUserData(uid: string): Promise<User> {
    // Mock implementation
    throw new Error('User data not found');
  }

  getCurrentUser(): User | null {
    return this.currentUserSubject.value;
  }

  isAuthenticated(): boolean {
    return this.currentUserSubject.value !== null;
  }

  hasRole(role: UserRole): boolean {
    const user = this.getCurrentUser();
    return user?.role === role;
  }

  hasAnyRole(roles: UserRole[]): boolean {
    const user = this.getCurrentUser();
    return user ? roles.includes(user.role) : false;
  }

  async updateUserProfile(updates: Partial<User>): Promise<void> {
    const user = this.getCurrentUser();
    if (!user) throw new Error('No authenticated user');

    const updatedUser = { ...user, ...updates, updatedAt: new Date() };
    this.currentUserSubject.next(updatedUser);
    return Promise.resolve();
  }

  // Password reset
  async resetPassword(email: string): Promise<void> {
    // Implementation would use Firebase Auth sendPasswordResetEmail
    // For now, just a placeholder
    console.log('Password reset requested for:', email);
  }

  // Method for testing - set current user directly
  setCurrentUser(user: User | null): void {
    this.currentUserSubject.next(user);
  }
}
