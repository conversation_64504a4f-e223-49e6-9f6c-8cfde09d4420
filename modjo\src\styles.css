/* You can add global styles to this file, and also import other style files */

/* Tailwind CSS imports */
@import 'tailwindcss/base';
@import 'tailwindcss/components';
@import 'tailwindcss/utilities';

/* Google Fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Poppins:wght@300;400;500;600;700;800&display=swap');

/* Angular Material theme */
@import '@angular/material/prebuilt-themes/indigo-pink.css';

/* CSS Custom Properties for theming */
:root {
  --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
  --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
  --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
  --dark-gradient: linear-gradient(135deg, #434343 0%, #000000 100%);

  --primary-color: #667eea;
  --secondary-color: #764ba2;
  --accent-color: #f093fb;
  --success-color: #4facfe;
  --warning-color: #43e97b;
  --error-color: #ff6b6b;

  --text-primary: #2d3748;
  --text-secondary: #718096;
  --text-muted: #a0aec0;

  --bg-primary: #ffffff;
  --bg-secondary: #f7fafc;
  --bg-tertiary: #edf2f7;

  --shadow-sm: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);

  --border-radius-sm: 0.375rem;
  --border-radius-md: 0.5rem;
  --border-radius-lg: 0.75rem;
  --border-radius-xl: 1rem;
  --border-radius-2xl: 1.5rem;
}

/* Global styles */
* {
  box-sizing: border-box;
}

html, body {
  height: 100%;
  margin: 0;
  padding: 0;
  font-family: 'Inter', 'Poppins', 'Roboto', sans-serif;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  color: var(--text-primary);
  line-height: 1.6;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Material Design overrides */
.mat-mdc-card {
  border-radius: var(--border-radius-xl) !important;
  box-shadow: var(--shadow-lg) !important;
  border: 1px solid rgba(255, 255, 255, 0.2) !important;
  backdrop-filter: blur(10px) !important;
  background: rgba(255, 255, 255, 0.95) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.mat-mdc-card:hover {
  transform: translateY(-2px) !important;
  box-shadow: var(--shadow-xl) !important;
}

.mat-mdc-button {
  border-radius: var(--border-radius-lg) !important;
  font-weight: 500 !important;
  text-transform: none !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.mat-mdc-raised-button {
  border-radius: var(--border-radius-lg) !important;
  font-weight: 600 !important;
  text-transform: none !important;
  box-shadow: var(--shadow-md) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.mat-mdc-raised-button:hover {
  transform: translateY(-1px) !important;
  box-shadow: var(--shadow-lg) !important;
}

.mat-mdc-form-field {
  width: 100%;
}

.mat-mdc-form-field .mat-mdc-form-field-outline {
  border-radius: var(--border-radius-lg) !important;
}

.mat-toolbar {
  background: var(--primary-gradient) !important;
  color: white !important;
  box-shadow: var(--shadow-lg) !important;
}

.mat-mdc-fab {
  box-shadow: var(--shadow-xl) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.mat-mdc-fab:hover {
  transform: scale(1.05) !important;
}

/* Utility classes */
.full-width {
  width: 100% !important;
}

.text-center {
  text-align: center !important;
}

.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.gradient-bg {
  background: var(--primary-gradient);
}

.gradient-bg-secondary {
  background: var(--secondary-gradient);
}

.gradient-bg-success {
  background: var(--success-gradient);
}

.gradient-bg-warning {
  background: var(--warning-gradient);
}

.glass-effect {
  background: rgba(255, 255, 255, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.18);
  box-shadow: var(--shadow-xl);
}

.glass-effect-dark {
  background: rgba(0, 0, 0, 0.25);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: var(--shadow-xl);
}

.floating-card {
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(20px);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: var(--border-radius-2xl);
  box-shadow: var(--shadow-xl);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.floating-card:hover {
  transform: translateY(-4px);
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
}

/* Animation classes */
.fade-in {
  animation: fadeIn 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.slide-up {
  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideUp {
  from {
    transform: translateY(30px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-in-left {
  animation: slideInLeft 0.6s cubic-bezier(0.4, 0, 0.2, 1);
}

@keyframes slideInLeft {
  from {
    transform: translateX(-30px);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

.bounce-in {
  animation: bounceIn 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes bounceIn {
  0% {
    transform: scale(0.3);
    opacity: 0;
  }
  50% {
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
}

.glow {
  animation: glow 2s ease-in-out infinite alternate;
}

@keyframes glow {
  from {
    box-shadow: 0 0 10px var(--primary-color), 0 0 20px var(--primary-color), 0 0 30px var(--primary-color);
  }
  to {
    box-shadow: 0 0 20px var(--primary-color), 0 0 30px var(--primary-color), 0 0 40px var(--primary-color);
  }
}

/* PWA specific styles */
.pwa-install-prompt {
  position: fixed;
  bottom: 16px;
  left: 16px;
  right: 16px;
  z-index: 1000;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);
  padding: 16px;
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Floating particles animation */
.floating-particles {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  overflow: hidden;
  pointer-events: none;
  z-index: 0;
}

.particle {
  position: absolute;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  animation: float 6s ease-in-out infinite;
}

.particle:nth-child(1) {
  width: 8px;
  height: 8px;
  left: 10%;
  animation-delay: 0s;
  animation-duration: 6s;
}

.particle:nth-child(2) {
  width: 12px;
  height: 12px;
  left: 20%;
  animation-delay: 1s;
  animation-duration: 8s;
}

.particle:nth-child(3) {
  width: 6px;
  height: 6px;
  left: 30%;
  animation-delay: 2s;
  animation-duration: 7s;
}

.particle:nth-child(4) {
  width: 10px;
  height: 10px;
  left: 40%;
  animation-delay: 3s;
  animation-duration: 9s;
}

.particle:nth-child(5) {
  width: 8px;
  height: 8px;
  left: 50%;
  animation-delay: 4s;
  animation-duration: 6s;
}

.particle:nth-child(6) {
  width: 14px;
  height: 14px;
  left: 60%;
  animation-delay: 5s;
  animation-duration: 8s;
}

.particle:nth-child(7) {
  width: 6px;
  height: 6px;
  left: 70%;
  animation-delay: 6s;
  animation-duration: 7s;
}

.particle:nth-child(8) {
  width: 10px;
  height: 10px;
  left: 80%;
  animation-delay: 7s;
  animation-duration: 9s;
}

.particle:nth-child(9) {
  width: 8px;
  height: 8px;
  left: 90%;
  animation-delay: 8s;
  animation-duration: 6s;
}

@keyframes float {
  0%, 100% {
    transform: translateY(100vh) rotate(0deg);
    opacity: 0;
  }
  10% {
    opacity: 1;
  }
  90% {
    opacity: 1;
  }
  100% {
    transform: translateY(-100px) rotate(360deg);
    opacity: 0;
  }
}

/* Enhanced scrollbar */
::-webkit-scrollbar {
  width: 12px;
}

::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
  border-radius: 6px;
}

::-webkit-scrollbar-thumb {
  background: linear-gradient(135deg, #667eea, #764ba2);
  border-radius: 6px;
  border: 2px solid rgba(255, 255, 255, 0.1);
}

::-webkit-scrollbar-thumb:hover {
  background: linear-gradient(135deg, #5a6fd8, #6a4190);
}

/* Mobile-first responsive design */
@media (max-width: 768px) {
  .mat-mdc-card {
    margin: 8px;
  }

  .mat-toolbar {
    padding: 0 8px;
  }

  .particle {
    display: none; /* Hide particles on mobile for performance */
  }
}
body {
  font-family: Arial, Helvetica, sans-serif;
  margin: 0;
  padding: 0;
}
