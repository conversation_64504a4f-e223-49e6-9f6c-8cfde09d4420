<div class="validator-dashboard-container" *ngIf="user">
  <!-- Header Section -->
  <div class="validator-header">
    <div class="header-content">
      <div class="validator-welcome">
        <h1>{{ getGreeting() }}, {{ user.name }}</h1>
        <p class="validator-subtitle">🧑‍🏫 Tableau de bord validateur - Validez les actions communautaires</p>
      </div>
      <div class="validator-badge">
        <mat-icon>verified_user</mat-icon>
        <span>Validateur</span>
      </div>
    </div>
  </div>

  <!-- Quick Stats -->
  <div class="quick-stats-section">
    <h2 class="section-title">
      <mat-icon>analytics</mat-icon>
      Statistiques de Validation
    </h2>
    <div class="stats-grid">
      <mat-card *ngFor="let stat of quickStats" class="stat-card" [style.border-left-color]="stat.color">
        <mat-card-content>
          <div class="stat-header">
            <div class="stat-icon" [style.background-color]="stat.color + '20'">
              <mat-icon [style.color]="stat.color">{{ stat.icon }}</mat-icon>
            </div>
          </div>
          <div class="stat-content">
            <h3 class="stat-value">{{ stat.value }}</h3>
            <p class="stat-title">{{ stat.title }}</p>
            <span class="stat-description">{{ stat.description }}</span>
          </div>
        </mat-card-content>
      </mat-card>
    </div>
  </div>

  <!-- Pending Validations -->
  <div class="pending-section">
    <h2 class="section-title">
      <mat-icon>pending_actions</mat-icon>
      Actions à Valider
      <span class="pending-count" *ngIf="pendingValidations.length > 0">{{ pendingValidations.length }}</span>
    </h2>
    
    <div *ngIf="pendingValidations.length === 0" class="empty-state">
      <mat-card class="empty-card">
        <mat-card-content>
          <mat-icon class="empty-icon">check_circle</mat-icon>
          <h3>Aucune validation en attente</h3>
          <p>Toutes les actions ont été traitées. Excellent travail !</p>
        </mat-card-content>
      </mat-card>
    </div>

    <div class="validations-grid" *ngIf="pendingValidations.length > 0">
      <mat-card *ngFor="let validation of pendingValidations" class="validation-card">
        <mat-card-header>
          <div class="validation-header">
            <div class="user-info">
              <h4>{{ validation.userName }}</h4>
              <span class="user-email">{{ validation.userEmail }}</span>
            </div>
            <div class="action-type">
              <span class="type-badge">{{ getActionTypeDisplayName(validation.actionType) }}</span>
            </div>
          </div>
        </mat-card-header>
        
        <mat-card-content>
          <div class="validation-details">
            <p class="description">{{ validation.description }}</p>
            <div class="validation-meta">
              <div class="meta-item" *ngIf="validation.location">
                <mat-icon>location_on</mat-icon>
                <span>{{ validation.location }}</span>
              </div>
              <div class="meta-item">
                <mat-icon>schedule</mat-icon>
                <span>{{ validation.submittedAt | timeAgo }}</span>
              </div>
              <div class="meta-item points">
                <mat-icon>stars</mat-icon>
                <span>{{ validation.points }} points</span>
              </div>
            </div>
            
            <div class="evidence-section" *ngIf="validation.evidence && validation.evidence.length > 0">
              <h5>Preuves fournies:</h5>
              <div class="evidence-list">
                <button *ngFor="let evidence of validation.evidence" 
                        mat-stroked-button 
                        class="evidence-btn"
                        (click)="viewEvidence(evidence)">
                  <mat-icon>photo</mat-icon>
                  Voir la preuve
                </button>
              </div>
            </div>
          </div>
        </mat-card-content>
        
        <mat-card-actions class="validation-actions">
          <button mat-raised-button 
                  color="primary" 
                  (click)="validateAction(validation, true)"
                  class="approve-btn">
            <mat-icon>check</mat-icon>
            Approuver (+{{ validation.points }} pts)
          </button>
          <button mat-raised-button 
                  color="warn" 
                  (click)="validateAction(validation, false)"
                  class="reject-btn">
            <mat-icon>close</mat-icon>
            Rejeter
          </button>
          <button mat-button (click)="viewValidationDetails(validation)">
            <mat-icon>info</mat-icon>
            Détails
          </button>
        </mat-card-actions>
      </mat-card>
    </div>
  </div>

  <!-- Recent Validations -->
  <div class="recent-section" *ngIf="recentValidations.length > 0">
    <h2 class="section-title">
      <mat-icon>history</mat-icon>
      Validations Récentes
    </h2>
    <mat-card class="recent-card">
      <mat-card-content>
        <div class="recent-list">
          <div *ngFor="let validation of recentValidations.slice(0, 5)" class="recent-item">
            <div class="recent-icon" [style.background-color]="getStatusColor(validation.status) + '20'">
              <mat-icon [style.color]="getStatusColor(validation.status)">
                {{ validation.status === 'approved' ? 'check_circle' : 'cancel' }}
              </mat-icon>
            </div>
            <div class="recent-content">
              <p class="recent-description">
                <strong>{{ validation.userName }}</strong> - {{ validation.description }}
              </p>
              <div class="recent-meta">
                <span class="recent-time">{{ validation.validatedAt | timeAgo }}</span>
                <span class="recent-status" [style.color]="getStatusColor(validation.status)">
                  {{ getStatusDisplayName(validation.status) }}
                </span>
                <span class="recent-points">{{ validation.points }} pts</span>
              </div>
            </div>
          </div>
        </div>
        <button mat-stroked-button class="view-all-btn" (click)="navigateTo('/validator/history')">
          Voir tout l'historique
          <mat-icon>arrow_forward</mat-icon>
        </button>
      </mat-card-content>
    </mat-card>
  </div>
</div>
