{"compileOnSave": false, "compilerOptions": {"baseUrl": "./", "outDir": "./dist/out-tsc", "forceConsistentCasingInFileNames": true, "strict": true, "noImplicitOverride": true, "noPropertyAccessFromIndexSignature": true, "noImplicitReturns": true, "noFallthroughCasesInSwitch": true, "sourceMap": true, "declaration": false, "downlevelIteration": true, "experimentalDecorators": true, "moduleResolution": "node", "importHelpers": true, "target": "ES2022", "module": "ES2022", "useDefineForClassFields": false, "skipLibCheck": true, "allowSyntheticDefaultImports": true, "resolveJsonModule": true, "esModuleInterop": true, "types": ["jasmine"], "typeRoots": ["node_modules/@types"], "lib": ["ES2022", "dom", "dom.iterable"]}, "angularCompilerOptions": {"enableI18nLegacyMessageIdFormat": false, "strictInjectionParameters": true, "strictInputAccessModifiers": true, "strictTemplates": true}, "exclude": ["node_modules", "dist", "cypress", "**/*.spec.ts", "**/*.cy.ts"]}