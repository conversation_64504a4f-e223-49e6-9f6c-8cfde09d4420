{"ast": null, "code": "export var RewardCategory;\n(function (RewardCategory) {\n  RewardCategory[\"FOOD\"] = \"food\";\n  RewardCategory[\"SHOPPING\"] = \"shopping\";\n  RewardCategory[\"ENTERTAINMENT\"] = \"entertainment\";\n  RewardCategory[\"SERVICES\"] = \"services\";\n  RewardCategory[\"HEALTH\"] = \"health\";\n  RewardCategory[\"EDUCATION\"] = \"education\";\n  RewardCategory[\"TRANSPORT\"] = \"transport\";\n  RewardCategory[\"OTHER\"] = \"other\";\n})(RewardCategory || (RewardCategory = {}));\nexport var PartnerCategory;\n(function (PartnerCategory) {\n  PartnerCategory[\"RESTAURANT\"] = \"restaurant\";\n  PartnerCategory[\"CAFE\"] = \"cafe\";\n  PartnerCategory[\"RETAIL\"] = \"retail\";\n  PartnerCategory[\"SERVICE\"] = \"service\";\n  PartnerCategory[\"ENTERTAINMENT\"] = \"entertainment\";\n  PartnerCategory[\"HEALTH\"] = \"health\";\n  PartnerCategory[\"EDUCATION\"] = \"education\";\n  PartnerCategory[\"OTHER\"] = \"other\";\n})(PartnerCategory || (PartnerCategory = {}));\nexport var ExchangeStatus;\n(function (ExchangeStatus) {\n  ExchangeStatus[\"PENDING\"] = \"pending\";\n  ExchangeStatus[\"CONFIRMED\"] = \"confirmed\";\n  ExchangeStatus[\"USED\"] = \"used\";\n  ExchangeStatus[\"EXPIRED\"] = \"expired\";\n  ExchangeStatus[\"CANCELLED\"] = \"cancelled\";\n})(ExchangeStatus || (ExchangeStatus = {}));\n// GeoLocation interface is defined in user.model.ts", "map": {"version": 3, "names": ["RewardCategory", "PartnerCategory", "ExchangeStatus"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\models\\reward.model.ts"], "sourcesContent": ["import { GeoLocation } from './user.model';\n\nexport interface Reward {\n  id: string;\n  title: string;\n  description: string;\n  pointsRequired: number;\n  partnerId: string;\n  partnerName: string;\n  category: RewardCategory;\n  imageUrl?: string;\n  isActive: boolean;\n  availableQuantity?: number;\n  validUntil?: Date;\n  terms?: string;\n  createdAt: Date;\n  updatedAt: Date;\n  city: 'Monastir' | 'Sousse' | 'Both';\n}\n\nexport enum RewardCategory {\n  FOOD = 'food',\n  SHOPPING = 'shopping',\n  ENTERTAINMENT = 'entertainment',\n  SERVICES = 'services',\n  HEALTH = 'health',\n  EDUCATION = 'education',\n  TRANSPORT = 'transport',\n  OTHER = 'other'\n}\n\nexport interface Partner {\n  id: string;\n  name: string;\n  description: string;\n  logo?: string;\n  category: PartnerCategory;\n  address: string;\n  city: 'Monastir' | 'Sousse';\n  phone?: string;\n  email?: string;\n  website?: string;\n  location: GeoLocation;\n  isActive: boolean;\n  rewards: string[]; // Array of reward IDs\n  createdAt: Date;\n  updatedAt: Date;\n}\n\nexport enum PartnerCategory {\n  RESTAURANT = 'restaurant',\n  CAFE = 'cafe',\n  RETAIL = 'retail',\n  SERVICE = 'service',\n  ENTERTAINMENT = 'entertainment',\n  HEALTH = 'health',\n  EDUCATION = 'education',\n  OTHER = 'other'\n}\n\nexport interface RewardExchange {\n  id: string;\n  userId: string;\n  rewardId: string;\n  pointsSpent: number;\n  status: ExchangeStatus;\n  exchangeCode: string;\n  exchangedAt: Date;\n  usedAt?: Date;\n  validUntil: Date;\n  partnerConfirmation?: string;\n}\n\nexport enum ExchangeStatus {\n  PENDING = 'pending',\n  CONFIRMED = 'confirmed',\n  USED = 'used',\n  EXPIRED = 'expired',\n  CANCELLED = 'cancelled'\n}\n\nexport interface CreateRewardRequest {\n  title: string;\n  description: string;\n  pointsRequired: number;\n  partnerId: string;\n  category: RewardCategory;\n  imageUrl?: string;\n  availableQuantity?: number;\n  validUntil?: Date;\n  terms?: string;\n  city: 'Monastir' | 'Sousse' | 'Both';\n}\n\nexport interface UpdateRewardRequest {\n  title?: string;\n  description?: string;\n  pointsRequired?: number;\n  category?: RewardCategory;\n  imageUrl?: string;\n  availableQuantity?: number;\n  validUntil?: Date;\n  terms?: string;\n  isActive?: boolean;\n}\n\n// GeoLocation interface is defined in user.model.ts\n"], "mappings": "AAoBA,WAAYA,cASX;AATD,WAAYA,cAAc;EACxBA,cAAA,iBAAa;EACbA,cAAA,yBAAqB;EACrBA,cAAA,mCAA+B;EAC/BA,cAAA,yBAAqB;EACrBA,cAAA,qBAAiB;EACjBA,cAAA,2BAAuB;EACvBA,cAAA,2BAAuB;EACvBA,cAAA,mBAAe;AACjB,CAAC,EATWA,cAAc,KAAdA,cAAc;AA6B1B,WAAYC,eASX;AATD,WAAYA,eAAe;EACzBA,eAAA,6BAAyB;EACzBA,eAAA,iBAAa;EACbA,eAAA,qBAAiB;EACjBA,eAAA,uBAAmB;EACnBA,eAAA,mCAA+B;EAC/BA,eAAA,qBAAiB;EACjBA,eAAA,2BAAuB;EACvBA,eAAA,mBAAe;AACjB,CAAC,EATWA,eAAe,KAAfA,eAAe;AAwB3B,WAAYC,cAMX;AAND,WAAYA,cAAc;EACxBA,cAAA,uBAAmB;EACnBA,cAAA,2BAAuB;EACvBA,cAAA,iBAAa;EACbA,cAAA,uBAAmB;EACnBA,cAAA,2BAAuB;AACzB,CAAC,EANWA,cAAc,KAAdA,cAAc;AAiC1B", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}