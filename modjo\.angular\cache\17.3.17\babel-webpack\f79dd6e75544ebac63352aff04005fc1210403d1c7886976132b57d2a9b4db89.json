{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { __decorate } from \"tslib\";\nimport { Component } from '@angular/core';\nimport { NavigationEnd } from '@angular/router';\nimport { filter, map } from 'rxjs/operators';\nlet AppComponent = class AppComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.title = 'Modjo';\n    this.currentPageTitle = 'Tableau de bord';\n  }\n  ngOnInit() {\n    // Listen to route changes to update page title\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd), map(() => this.router.url)).subscribe(url => {\n      this.updatePageTitle(url);\n    });\n  }\n  getPageTitle() {\n    return this.currentPageTitle;\n  }\n  updatePageTitle(url) {\n    const titleMap = {\n      '/dashboard': 'Tableau de bord',\n      '/profile': 'Profil',\n      '/qr-scanner': 'Scanner QR',\n      '/rewards': 'Récompenses',\n      '/validation': 'Validation',\n      '/admin': 'Administration'\n    };\n    // Find matching route\n    const matchedRoute = Object.keys(titleMap).find(route => url.startsWith(route));\n    this.currentPageTitle = matchedRoute ? titleMap[matchedRoute] : 'Modjo';\n  }\n  logout() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this.authService.logout();\n        _this.router.navigate(['/auth/login']);\n      } catch (error) {\n        console.error('Logout error:', error);\n      }\n    })();\n  }\n};\nAppComponent = __decorate([Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})], AppComponent);\nexport { AppComponent };", "map": {"version": 3, "names": ["Component", "NavigationEnd", "filter", "map", "AppComponent", "constructor", "authService", "router", "title", "currentPageTitle", "ngOnInit", "events", "pipe", "event", "url", "subscribe", "updatePageTitle", "getPageTitle", "titleMap", "matchedRoute", "Object", "keys", "find", "route", "startsWith", "logout", "_this", "_asyncToGenerator", "navigate", "error", "console", "__decorate", "selector", "templateUrl", "styleUrls"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\app.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { filter, map } from 'rxjs/operators';\nimport { AuthService } from './core/services/auth.service';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent implements OnInit {\n  title = 'Modjo';\n  currentPageTitle = 'Tableau de bord';\n\n  constructor(\n    public authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    // Listen to route changes to update page title\n    this.router.events.pipe(\n      filter(event => event instanceof NavigationEnd),\n      map(() => this.router.url)\n    ).subscribe(url => {\n      this.updatePageTitle(url);\n    });\n  }\n\n  getPageTitle(): string {\n    return this.currentPageTitle;\n  }\n\n  private updatePageTitle(url: string): void {\n    const titleMap: { [key: string]: string } = {\n      '/dashboard': 'Tableau de bord',\n      '/profile': 'Profil',\n      '/qr-scanner': 'Scanner QR',\n      '/rewards': 'Récompenses',\n      '/validation': 'Validation',\n      '/admin': 'Administration'\n    };\n\n    // Find matching route\n    const matchedRoute = Object.keys(titleMap).find(route => url.startsWith(route));\n    this.currentPageTitle = matchedRoute ? titleMap[matchedRoute] : 'Modjo';\n  }\n\n  async logout(): Promise<void> {\n    try {\n      await this.authService.logout();\n      this.router.navigate(['/auth/login']);\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  }\n}\n"], "mappings": ";;AAAA,SAASA,SAAS,QAAgB,eAAe;AACjD,SAAiBC,aAAa,QAAQ,iBAAiB;AACvD,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;AAQrC,IAAMC,YAAY,GAAlB,MAAMA,YAAY;EAIvBC,YACSC,WAAwB,EACvBC,MAAc;IADf,KAAAD,WAAW,GAAXA,WAAW;IACV,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAC,KAAK,GAAG,OAAO;IACf,KAAAC,gBAAgB,GAAG,iBAAiB;EAKjC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,MAAM,CAACI,MAAM,CAACC,IAAI,CACrBV,MAAM,CAACW,KAAK,IAAIA,KAAK,YAAYZ,aAAa,CAAC,EAC/CE,GAAG,CAAC,MAAM,IAAI,CAACI,MAAM,CAACO,GAAG,CAAC,CAC3B,CAACC,SAAS,CAACD,GAAG,IAAG;MAChB,IAAI,CAACE,eAAe,CAACF,GAAG,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEAG,YAAYA,CAAA;IACV,OAAO,IAAI,CAACR,gBAAgB;EAC9B;EAEQO,eAAeA,CAACF,GAAW;IACjC,MAAMI,QAAQ,GAA8B;MAC1C,YAAY,EAAE,iBAAiB;MAC/B,UAAU,EAAE,QAAQ;MACpB,aAAa,EAAE,YAAY;MAC3B,UAAU,EAAE,aAAa;MACzB,aAAa,EAAE,YAAY;MAC3B,QAAQ,EAAE;KACX;IAED;IACA,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,IAAI,CAACC,KAAK,IAAIT,GAAG,CAACU,UAAU,CAACD,KAAK,CAAC,CAAC;IAC/E,IAAI,CAACd,gBAAgB,GAAGU,YAAY,GAAGD,QAAQ,CAACC,YAAY,CAAC,GAAG,OAAO;EACzE;EAEMM,MAAMA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACV,IAAI;QACF,MAAMD,KAAI,CAACpB,WAAW,CAACmB,MAAM,EAAE;QAC/BC,KAAI,CAACnB,MAAM,CAACqB,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;OACtC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;;IACtC;EACH;CACD;AA9CYzB,YAAY,GAAA2B,UAAA,EALxB/B,SAAS,CAAC;EACTgC,QAAQ,EAAE,UAAU;EACpBC,WAAW,EAAE,sBAAsB;EACnCC,SAAS,EAAE,CAAC,qBAAqB;CAClC,CAAC,C,EACW9B,YAAY,CA8CxB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}