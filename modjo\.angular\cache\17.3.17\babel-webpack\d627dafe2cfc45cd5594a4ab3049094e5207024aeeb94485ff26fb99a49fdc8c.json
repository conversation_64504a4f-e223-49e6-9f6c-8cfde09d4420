{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { NavigationEnd } from '@angular/router';\nimport { filter, map } from 'rxjs/operators';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"./core/services/auth.service\";\nimport * as i2 from \"@angular/router\";\nimport * as i3 from \"@angular/common\";\nimport * as i4 from \"@angular/material/toolbar\";\nimport * as i5 from \"@angular/material/button\";\nimport * as i6 from \"@angular/material/icon\";\nimport * as i7 from \"@angular/material/menu\";\nfunction AppComponent_ng_container_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"button\", 17)(2, \"mat-icon\");\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const user_r2 = ctx.ngIf;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"routerLink\", ctx_r2.getDashboardRoute(user_r2.role));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getDashboardIcon(user_r2.role));\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.getDashboardDisplayName(user_r2.role), \" \");\n  }\n}\nfunction AppComponent_ng_template_10_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 18)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"login\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Connexion \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction AppComponent_div_12_ng_container_1_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementContainerStart(0);\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"stars\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"span\");\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementContainerEnd();\n  }\n  if (rf & 2) {\n    const user_r4 = i0.ɵɵnextContext().ngIf;\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\"\", user_r4.points, \" pts\");\n  }\n}\nfunction AppComponent_div_12_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 19);\n    i0.ɵɵtemplate(1, AppComponent_div_12_ng_container_1_Template, 5, 1, \"ng-container\", 20);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const user_r4 = ctx.ngIf;\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", user_r4.role === \"user\");\n  }\n}\nfunction AppComponent_button_14_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 21)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"account_circle\");\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    i0.ɵɵnextContext();\n    const userMenu_r5 = i0.ɵɵreference(17);\n    i0.ɵɵproperty(\"matMenuTriggerFor\", userMenu_r5);\n  }\n}\nfunction AppComponent_button_31_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"button\", 22)(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"qr_code_scanner\");\n    i0.ɵɵelementEnd()();\n  }\n}\nexport class AppComponent {\n  constructor(authService, router) {\n    this.authService = authService;\n    this.router = router;\n    this.title = 'Modjo';\n    this.currentPageTitle = 'Tableau de bord';\n  }\n  ngOnInit() {\n    // Listen to route changes to update page title\n    this.router.events.pipe(filter(event => event instanceof NavigationEnd), map(() => this.router.url)).subscribe(url => {\n      this.updatePageTitle(url);\n    });\n  }\n  getPageTitle() {\n    return this.currentPageTitle;\n  }\n  updatePageTitle(url) {\n    const titleMap = {\n      '/dashboard': 'Tableau de bord',\n      '/profile': 'Profil',\n      '/qr-scanner': 'Scanner QR',\n      '/rewards': 'Récompenses',\n      '/validation': 'Validation',\n      '/admin': 'Administration'\n    };\n    // Find matching route\n    const matchedRoute = Object.keys(titleMap).find(route => url.startsWith(route));\n    this.currentPageTitle = matchedRoute ? titleMap[matchedRoute] : 'Modjo';\n  }\n  logout() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        yield _this.authService.logout();\n        _this.router.navigate(['/auth/login']);\n      } catch (error) {\n        console.error('Logout error:', error);\n      }\n    })();\n  }\n  static {\n    this.ɵfac = function AppComponent_Factory(t) {\n      return new (t || AppComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.Router));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: AppComponent,\n      selectors: [[\"app-root\"]],\n      decls: 34,\n      vars: 13,\n      consts: [[\"authButtons\", \"\"], [\"userMenu\", \"matMenu\"], [1, \"app-container\"], [1, \"welcome-container\", \"fade-in\"], [\"color\", \"primary\", 1, \"main-toolbar\"], [1, \"logo\"], [1, \"toolbar-title\"], [1, \"spacer\"], [4, \"ngIf\", \"ngIfElse\"], [\"class\", \"points-display\", 4, \"ngIf\"], [\"mat-icon-button\", \"\", \"class\", \"user-avatar\", 3, \"matMenuTriggerFor\", 4, \"ngIf\"], [1, \"user-menu\"], [\"mat-menu-item\", \"\", \"routerLink\", \"/profile\"], [\"mat-menu-item\", \"\", 3, \"click\"], [1, \"main-content\"], [1, \"content-wrapper\", \"slide-up\"], [\"mat-fab\", \"\", \"class\", \"floating-fab\", \"color\", \"primary\", \"routerLink\", \"/qr-scanner\", \"matTooltip\", \"Scanner QR Code\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"accent\", 1, \"nav-button\", 3, \"routerLink\"], [\"mat-stroked-button\", \"\", \"routerLink\", \"/auth\", 1, \"nav-button\"], [1, \"points-display\"], [4, \"ngIf\"], [\"mat-icon-button\", \"\", 1, \"user-avatar\", 3, \"matMenuTriggerFor\"], [\"mat-fab\", \"\", \"color\", \"primary\", \"routerLink\", \"/qr-scanner\", \"matTooltip\", \"Scanner QR Code\", 1, \"floating-fab\"]],\n      template: function AppComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          const _r1 = i0.ɵɵgetCurrentView();\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"mat-toolbar\", 4)(3, \"div\", 5);\n          i0.ɵɵtext(4, \"\\uD83C\\uDF1F\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(5, \"span\", 6);\n          i0.ɵɵtext(6, \"Modjo PWA\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelement(7, \"span\", 7);\n          i0.ɵɵtemplate(8, AppComponent_ng_container_8_Template, 5, 3, \"ng-container\", 8);\n          i0.ɵɵpipe(9, \"async\");\n          i0.ɵɵtemplate(10, AppComponent_ng_template_10_Template, 4, 0, \"ng-template\", null, 0, i0.ɵɵtemplateRefExtractor)(12, AppComponent_div_12_Template, 2, 1, \"div\", 9);\n          i0.ɵɵpipe(13, \"async\");\n          i0.ɵɵtemplate(14, AppComponent_button_14_Template, 3, 1, \"button\", 10);\n          i0.ɵɵpipe(15, \"async\");\n          i0.ɵɵelementStart(16, \"mat-menu\", 11, 1)(18, \"button\", 12)(19, \"mat-icon\");\n          i0.ɵɵtext(20, \"person\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(21, \"span\");\n          i0.ɵɵtext(22, \"Profil\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(23, \"button\", 13);\n          i0.ɵɵlistener(\"click\", function AppComponent_Template_button_click_23_listener() {\n            i0.ɵɵrestoreView(_r1);\n            return i0.ɵɵresetView(ctx.logout());\n          });\n          i0.ɵɵelementStart(24, \"mat-icon\");\n          i0.ɵɵtext(25, \"logout\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(26, \"span\");\n          i0.ɵɵtext(27, \"D\\u00E9connexion\");\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(28, \"main\", 14)(29, \"div\", 15);\n          i0.ɵɵelement(30, \"router-outlet\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(31, AppComponent_button_31_Template, 3, 0, \"button\", 16);\n          i0.ɵɵpipe(32, \"async\");\n          i0.ɵɵelement(33, \"app-role-switcher\");\n          i0.ɵɵelementEnd()();\n        }\n        if (rf & 2) {\n          const authButtons_r6 = i0.ɵɵreference(11);\n          i0.ɵɵadvance(8);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(9, 5, ctx.authService.currentUser$))(\"ngIfElse\", authButtons_r6);\n          i0.ɵɵadvance(4);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(13, 7, ctx.authService.currentUser$));\n          i0.ɵɵadvance(2);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(15, 9, ctx.authService.currentUser$));\n          i0.ɵɵadvance(17);\n          i0.ɵɵproperty(\"ngIf\", i0.ɵɵpipeBind1(32, 11, ctx.authService.currentUser$));\n        }\n      },\n      dependencies: [i3.NgIf, i2.RouterOutlet, i2.RouterLink, i4.MatToolbar, i5.MatButton, i5.MatIconButton, i5.MatFabButton, i6.MatIcon, i7.MatMenu, i7.MatMenuItem, i7.MatMenuTrigger, i3.AsyncPipe],\n      styles: [\".app-container[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.app-container[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),\\n    radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.2) 0%, transparent 50%);\\n  pointer-events: none;\\n  z-index: 0;\\n}\\n\\n.welcome-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  z-index: 1;\\n  height: 100vh;\\n  display: flex;\\n  flex-direction: column;\\n}\\n\\n.sidenav-container[_ngcontent-%COMP%] {\\n  flex: 1;\\n}\\n\\n.sidenav[_ngcontent-%COMP%] {\\n  width: 320px;\\n  background: rgba(255, 255, 255, 0.95);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-right: 1px solid rgba(255, 255, 255, 0.2);\\n}\\n\\n.sidenav-header[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  color: white;\\n  display: flex;\\n  align-items: center;\\n  gap: 16px;\\n  padding: 0 24px;\\n  height: 80px;\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.sidenav-header[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background: linear-gradient(45deg, rgba(255, 255, 255, 0.1) 0%, transparent 100%);\\n  pointer-events: none;\\n}\\n\\n.logo[_ngcontent-%COMP%] {\\n  width: 40px;\\n  height: 40px;\\n  border-radius: 12px;\\n  background: rgba(255, 255, 255, 0.2);\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  font-size: 1.5rem;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n}\\n\\n.app-name[_ngcontent-%COMP%] {\\n  font-size: 1.5rem;\\n  font-weight: 700;\\n  font-family: 'Poppins', sans-serif;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.main-toolbar[_ngcontent-%COMP%] {\\n  position: sticky;\\n  top: 0;\\n  z-index: 1000;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.15);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  border-bottom: 1px solid rgba(255, 255, 255, 0.1);\\n  height: 80px;\\n  padding: 0 24px;\\n}\\n\\n.menu-button[_ngcontent-%COMP%] {\\n  margin-right: 20px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 12px;\\n  width: 48px;\\n  height: 48px;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.menu-button[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: scale(1.05);\\n}\\n\\n.toolbar-title[_ngcontent-%COMP%] {\\n  font-size: 1.3rem;\\n  font-weight: 600;\\n  font-family: 'Poppins', sans-serif;\\n  text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);\\n}\\n\\n.spacer[_ngcontent-%COMP%] {\\n  flex: 1 1 auto;\\n}\\n\\n.points-display[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  background: rgba(255, 255, 255, 0.15);\\n  padding: 8px 16px;\\n  border-radius: 24px;\\n  margin-right: 20px;\\n  -webkit-backdrop-filter: blur(10px);\\n          backdrop-filter: blur(10px);\\n  border: 1px solid rgba(255, 255, 255, 0.2);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  font-weight: 600;\\n}\\n\\n.points-display[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.25);\\n  transform: scale(1.05);\\n}\\n\\n.points-display[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #ffd700;\\n  font-size: 20px;\\n  width: 20px;\\n  height: 20px;\\n  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.2));\\n}\\n\\n.user-avatar[_ngcontent-%COMP%] {\\n  margin-left: 12px;\\n  background: rgba(255, 255, 255, 0.1);\\n  border-radius: 50%;\\n  width: 48px;\\n  height: 48px;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.user-avatar[_ngcontent-%COMP%]:hover {\\n  background: rgba(255, 255, 255, 0.2);\\n  transform: scale(1.1);\\n}\\n\\n.main-content[_ngcontent-%COMP%] {\\n  padding: 32px;\\n  min-height: calc(100vh - 80px);\\n  background: transparent;\\n  position: relative;\\n  z-index: 1;\\n  overflow-y: auto;\\n}\\n\\n.auth-layout[_ngcontent-%COMP%] {\\n  height: 100vh;\\n  display: flex;\\n  align-items: center;\\n  justify-content: center;\\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\\n  position: relative;\\n  overflow: hidden;\\n}\\n\\n.auth-layout[_ngcontent-%COMP%]::before {\\n  content: '';\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  background:\\n    radial-gradient(circle at 20% 80%, rgba(255, 255, 255, 0.1) 0%, transparent 50%),\\n    radial-gradient(circle at 80% 20%, rgba(255, 255, 255, 0.1) 0%, transparent 50%);\\n  pointer-events: none;\\n}\\n\\n\\n\\n.mat-mdc-list-item.active[_ngcontent-%COMP%] {\\n  background-color: rgba(103, 126, 234, 0.1);\\n  color: #667eea;\\n}\\n\\n.mat-mdc-list-item.active[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  color: #667eea;\\n}\\n\\n.nav-button[_ngcontent-%COMP%] {\\n  margin: 0 8px;\\n  border-radius: 24px;\\n  padding: 0 20px;\\n  height: 40px;\\n  font-weight: 500;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.nav-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.content-wrapper[_ngcontent-%COMP%] {\\n  max-width: 1400px;\\n  margin: 0 auto;\\n  width: 100%;\\n}\\n\\n.user-menu[_ngcontent-%COMP%] {\\n  margin-top: 8px;\\n  border-radius: 12px;\\n  box-shadow: 0 10px 30px rgba(0, 0, 0, 0.15);\\n  -webkit-backdrop-filter: blur(20px);\\n          backdrop-filter: blur(20px);\\n  background: rgba(255, 255, 255, 0.95);\\n}\\n\\n\\n\\n.mat-mdc-nav-list[_ngcontent-%COMP%]   .mat-mdc-list-item[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  margin: 4px 12px;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.mat-mdc-nav-list[_ngcontent-%COMP%]   .mat-mdc-list-item[_ngcontent-%COMP%]:hover {\\n  background: rgba(103, 126, 234, 0.1);\\n  transform: translateX(8px);\\n}\\n\\n.mat-mdc-nav-list[_ngcontent-%COMP%]   .mat-mdc-list-item.active[_ngcontent-%COMP%] {\\n  background: linear-gradient(135deg, rgba(103, 126, 234, 0.15) 0%, rgba(118, 75, 162, 0.15) 100%);\\n  color: var(--primary-color);\\n  font-weight: 600;\\n}\\n\\n\\n\\n.floating-fab[_ngcontent-%COMP%] {\\n  position: fixed;\\n  bottom: 24px;\\n  right: 24px;\\n  z-index: 1000;\\n  background: var(--primary-gradient);\\n  color: white;\\n  box-shadow: 0 8px 32px rgba(103, 126, 234, 0.4);\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n}\\n\\n.floating-fab[_ngcontent-%COMP%]:hover {\\n  transform: scale(1.1) rotate(5deg);\\n  box-shadow: 0 12px 40px rgba(103, 126, 234, 0.6);\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .main-content[_ngcontent-%COMP%] {\\n    padding: 16px;\\n  }\\n\\n  .main-toolbar[_ngcontent-%COMP%] {\\n    height: 64px;\\n    padding: 0 16px;\\n  }\\n\\n  .toolbar-title[_ngcontent-%COMP%] {\\n    font-size: 1.1rem;\\n  }\\n\\n  .nav-button[_ngcontent-%COMP%] {\\n    margin: 0 4px;\\n    padding: 0 12px;\\n    height: 36px;\\n    font-size: 0.9rem;\\n  }\\n\\n  .nav-button[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n    display: none;\\n  }\\n\\n  .points-display[_ngcontent-%COMP%] {\\n    padding: 6px 12px;\\n    font-size: 0.9rem;\\n    margin-right: 12px;\\n  }\\n\\n  .user-avatar[_ngcontent-%COMP%] {\\n    width: 40px;\\n    height: 40px;\\n    margin-left: 8px;\\n  }\\n\\n  .content-wrapper[_ngcontent-%COMP%] {\\n    padding: 0;\\n  }\\n}\\n\\n@media (max-width: 480px) {\\n  .main-toolbar[_ngcontent-%COMP%] {\\n    padding: 0 12px;\\n  }\\n\\n  .toolbar-title[_ngcontent-%COMP%] {\\n    font-size: 1rem;\\n  }\\n\\n  .nav-button[_ngcontent-%COMP%] {\\n    padding: 0 8px;\\n    font-size: 0.8rem;\\n  }\\n\\n  .points-display[_ngcontent-%COMP%] {\\n    padding: 4px 8px;\\n    font-size: 0.8rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["NavigationEnd", "filter", "map", "i0", "ɵɵelementContainerStart", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd", "ɵɵadvance", "ɵɵproperty", "ctx_r2", "getDashboardRoute", "user_r2", "role", "ɵɵtextInterpolate", "getDashboardIcon", "ɵɵtextInterpolate1", "getDashboardDisplayName", "user_r4", "points", "ɵɵtemplate", "AppComponent_div_12_ng_container_1_Template", "userMenu_r5", "AppComponent", "constructor", "authService", "router", "title", "currentPageTitle", "ngOnInit", "events", "pipe", "event", "url", "subscribe", "updatePageTitle", "getPageTitle", "titleMap", "matchedRoute", "Object", "keys", "find", "route", "startsWith", "logout", "_this", "_asyncToGenerator", "navigate", "error", "console", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "Router", "selectors", "decls", "vars", "consts", "template", "AppComponent_Template", "rf", "ctx", "ɵɵelement", "AppComponent_ng_container_8_Template", "AppComponent_ng_template_10_Template", "ɵɵtemplateRefExtractor", "AppComponent_div_12_Template", "AppComponent_button_14_Template", "ɵɵlistener", "AppComponent_Template_button_click_23_listener", "ɵɵrestoreView", "_r1", "ɵɵresetView", "AppComponent_button_31_Template", "ɵɵpipeBind1", "currentUser$", "authButtons_r6"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\app.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\app.component.html"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { Router, NavigationEnd } from '@angular/router';\nimport { filter, map } from 'rxjs/operators';\nimport { AuthService } from './core/services/auth.service';\n\n@Component({\n  selector: 'app-root',\n  templateUrl: './app.component.html',\n  styleUrls: ['./app.component.css']\n})\nexport class AppComponent implements OnInit {\n  title = 'Modjo';\n  currentPageTitle = 'Tableau de bord';\n\n  constructor(\n    public authService: AuthService,\n    private router: Router\n  ) {}\n\n  ngOnInit() {\n    // Listen to route changes to update page title\n    this.router.events.pipe(\n      filter(event => event instanceof NavigationEnd),\n      map(() => this.router.url)\n    ).subscribe(url => {\n      this.updatePageTitle(url);\n    });\n  }\n\n  getPageTitle(): string {\n    return this.currentPageTitle;\n  }\n\n  private updatePageTitle(url: string): void {\n    const titleMap: { [key: string]: string } = {\n      '/dashboard': 'Tableau de bord',\n      '/profile': 'Profil',\n      '/qr-scanner': 'Scanner QR',\n      '/rewards': 'Récompenses',\n      '/validation': 'Validation',\n      '/admin': 'Administration'\n    };\n\n    // Find matching route\n    const matchedRoute = Object.keys(titleMap).find(route => url.startsWith(route));\n    this.currentPageTitle = matchedRoute ? titleMap[matchedRoute] : 'Modjo';\n  }\n\n  async logout(): Promise<void> {\n    try {\n      await this.authService.logout();\n      this.router.navigate(['/auth/login']);\n    } catch (error) {\n      console.error('Logout error:', error);\n    }\n  }\n}\n", "<div class=\"app-container\">\n  <!-- Enhanced main layout -->\n  <div class=\"welcome-container fade-in\">\n    <mat-toolbar color=\"primary\" class=\"main-toolbar\">\n      <div class=\"logo\">🌟</div>\n      <span class=\"toolbar-title\">Mo<PERSON><PERSON></span>\n      <span class=\"spacer\"></span>\n\n      <!-- Navigation buttons -->\n      <ng-container *ngIf=\"authService.currentUser$ | async as user; else authButtons\">\n        <button mat-raised-button\n                color=\"accent\"\n                [routerLink]=\"getDashboardRoute(user.role)\"\n                class=\"nav-button\">\n          <mat-icon>{{ getDashboardIcon(user.role) }}</mat-icon>\n          {{ getDashboardDisplayName(user.role) }}\n        </button>\n      </ng-container>\n\n      <ng-template #authButtons>\n        <button mat-stroked-button\n                routerLink=\"/auth\"\n                class=\"nav-button\">\n          <mat-icon>login</mat-icon>\n          Connexion\n        </button>\n      </ng-template>\n\n      <!-- Points display (only for users) -->\n      <div class=\"points-display\" *ngIf=\"authService.currentUser$ | async as user\">\n        <ng-container *ngIf=\"user.role === 'user'\">\n          <mat-icon>stars</mat-icon>\n          <span>{{ user.points }} pts</span>\n        </ng-container>\n      </div>\n\n      <!-- User menu -->\n      <button mat-icon-button\n              [matMenuTriggerFor]=\"userMenu\"\n              class=\"user-avatar\"\n              *ngIf=\"authService.currentUser$ | async\">\n        <mat-icon>account_circle</mat-icon>\n      </button>\n\n      <mat-menu #userMenu=\"matMenu\" class=\"user-menu\">\n        <button mat-menu-item routerLink=\"/profile\">\n          <mat-icon>person</mat-icon>\n          <span>Profil</span>\n        </button>\n        <button mat-menu-item (click)=\"logout()\">\n          <mat-icon>logout</mat-icon>\n          <span>Déconnexion</span>\n        </button>\n      </mat-menu>\n    </mat-toolbar>\n\n    <main class=\"main-content\">\n      <div class=\"content-wrapper slide-up\">\n        <router-outlet></router-outlet>\n      </div>\n    </main>\n\n    <!-- Floating Action Button for Quick QR Scan -->\n    <button mat-fab\n            class=\"floating-fab\"\n            color=\"primary\"\n            routerLink=\"/qr-scanner\"\n            matTooltip=\"Scanner QR Code\"\n            *ngIf=\"authService.currentUser$ | async\">\n      <mat-icon>qr_code_scanner</mat-icon>\n    </button>\n\n    <!-- Role Switcher for Testing -->\n    <app-role-switcher></app-role-switcher>\n  </div>\n</div>\n"], "mappings": ";AACA,SAAiBA,aAAa,QAAQ,iBAAiB;AACvD,SAASC,MAAM,EAAEC,GAAG,QAAQ,gBAAgB;;;;;;;;;;;ICOtCC,EAAA,CAAAC,uBAAA,GAAiF;IAK7ED,EAJF,CAAAE,cAAA,iBAG2B,eACf;IAAAF,EAAA,CAAAG,MAAA,GAAiC;IAAAH,EAAA,CAAAI,YAAA,EAAW;IACtDJ,EAAA,CAAAG,MAAA,GACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;;IAJDJ,EAAA,CAAAK,SAAA,EAA2C;IAA3CL,EAAA,CAAAM,UAAA,eAAAC,MAAA,CAAAC,iBAAA,CAAAC,OAAA,CAAAC,IAAA,EAA2C;IAEvCV,EAAA,CAAAK,SAAA,GAAiC;IAAjCL,EAAA,CAAAW,iBAAA,CAAAJ,MAAA,CAAAK,gBAAA,CAAAH,OAAA,CAAAC,IAAA,EAAiC;IAC3CV,EAAA,CAAAK,SAAA,EACF;IADEL,EAAA,CAAAa,kBAAA,MAAAN,MAAA,CAAAO,uBAAA,CAAAL,OAAA,CAAAC,IAAA,OACF;;;;;IAOEV,EAHF,CAAAE,cAAA,iBAE2B,eACf;IAAAF,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAG,MAAA,kBACF;IAAAH,EAAA,CAAAI,YAAA,EAAS;;;;;IAKTJ,EAAA,CAAAC,uBAAA,GAA2C;IACzCD,EAAA,CAAAE,cAAA,eAAU;IAAAF,EAAA,CAAAG,MAAA,YAAK;IAAAH,EAAA,CAAAI,YAAA,EAAW;IAC1BJ,EAAA,CAAAE,cAAA,WAAM;IAAAF,EAAA,CAAAG,MAAA,GAAqB;IAAAH,EAAA,CAAAI,YAAA,EAAO;;;;;IAA5BJ,EAAA,CAAAK,SAAA,GAAqB;IAArBL,EAAA,CAAAa,kBAAA,KAAAE,OAAA,CAAAC,MAAA,SAAqB;;;;;IAH/BhB,EAAA,CAAAE,cAAA,cAA6E;IAC3EF,EAAA,CAAAiB,UAAA,IAAAC,2CAAA,2BAA2C;IAI7ClB,EAAA,CAAAI,YAAA,EAAM;;;;IAJWJ,EAAA,CAAAK,SAAA,EAA0B;IAA1BL,EAAA,CAAAM,UAAA,SAAAS,OAAA,CAAAL,IAAA,YAA0B;;;;;IAWzCV,EAJF,CAAAE,cAAA,iBAGiD,eACrC;IAAAF,EAAA,CAAAG,MAAA,qBAAc;IAC1BH,EAD0B,CAAAI,YAAA,EAAW,EAC5B;;;;;IAJDJ,EAAA,CAAAM,UAAA,sBAAAa,WAAA,CAA8B;;;;;IA+BtCnB,EANF,CAAAE,cAAA,iBAKiD,eACrC;IAAAF,EAAA,CAAAG,MAAA,sBAAe;IAC3BH,EAD2B,CAAAI,YAAA,EAAW,EAC7B;;;AD5Db,OAAM,MAAOgB,YAAY;EAIvBC,YACSC,WAAwB,EACvBC,MAAc;IADf,KAAAD,WAAW,GAAXA,WAAW;IACV,KAAAC,MAAM,GAANA,MAAM;IALhB,KAAAC,KAAK,GAAG,OAAO;IACf,KAAAC,gBAAgB,GAAG,iBAAiB;EAKjC;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACH,MAAM,CAACI,MAAM,CAACC,IAAI,CACrB9B,MAAM,CAAC+B,KAAK,IAAIA,KAAK,YAAYhC,aAAa,CAAC,EAC/CE,GAAG,CAAC,MAAM,IAAI,CAACwB,MAAM,CAACO,GAAG,CAAC,CAC3B,CAACC,SAAS,CAACD,GAAG,IAAG;MAChB,IAAI,CAACE,eAAe,CAACF,GAAG,CAAC;IAC3B,CAAC,CAAC;EACJ;EAEAG,YAAYA,CAAA;IACV,OAAO,IAAI,CAACR,gBAAgB;EAC9B;EAEQO,eAAeA,CAACF,GAAW;IACjC,MAAMI,QAAQ,GAA8B;MAC1C,YAAY,EAAE,iBAAiB;MAC/B,UAAU,EAAE,QAAQ;MACpB,aAAa,EAAE,YAAY;MAC3B,UAAU,EAAE,aAAa;MACzB,aAAa,EAAE,YAAY;MAC3B,QAAQ,EAAE;KACX;IAED;IACA,MAAMC,YAAY,GAAGC,MAAM,CAACC,IAAI,CAACH,QAAQ,CAAC,CAACI,IAAI,CAACC,KAAK,IAAIT,GAAG,CAACU,UAAU,CAACD,KAAK,CAAC,CAAC;IAC/E,IAAI,CAACd,gBAAgB,GAAGU,YAAY,GAAGD,QAAQ,CAACC,YAAY,CAAC,GAAG,OAAO;EACzE;EAEMM,MAAMA,CAAA;IAAA,IAAAC,KAAA;IAAA,OAAAC,iBAAA;MACV,IAAI;QACF,MAAMD,KAAI,CAACpB,WAAW,CAACmB,MAAM,EAAE;QAC/BC,KAAI,CAACnB,MAAM,CAACqB,QAAQ,CAAC,CAAC,aAAa,CAAC,CAAC;OACtC,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,eAAe,EAAEA,KAAK,CAAC;;IACtC;EACH;;;uBA7CWzB,YAAY,EAAApB,EAAA,CAAA+C,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAAjD,EAAA,CAAA+C,iBAAA,CAAAG,EAAA,CAAAC,MAAA;IAAA;EAAA;;;YAAZ/B,YAAY;MAAAgC,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,sBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;UCNnB1D,EAJN,CAAAE,cAAA,aAA2B,aAEc,qBACa,aAC9B;UAAAF,EAAA,CAAAG,MAAA,mBAAE;UAAAH,EAAA,CAAAI,YAAA,EAAM;UAC1BJ,EAAA,CAAAE,cAAA,cAA4B;UAAAF,EAAA,CAAAG,MAAA,gBAAS;UAAAH,EAAA,CAAAI,YAAA,EAAO;UAC5CJ,EAAA,CAAA4D,SAAA,cAA4B;UAG5B5D,EAAA,CAAAiB,UAAA,IAAA4C,oCAAA,0BAAiF;;UAoBjF7D,EAVA,CAAAiB,UAAA,KAAA6C,oCAAA,gCAAA9D,EAAA,CAAA+D,sBAAA,CAA0B,KAAAC,4BAAA,iBAUmD;;UAQ7EhE,EAAA,CAAAiB,UAAA,KAAAgD,+BAAA,qBAGiD;;UAM7CjE,EAFJ,CAAAE,cAAA,uBAAgD,kBACF,gBAChC;UAAAF,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAC3BJ,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,cAAM;UACdH,EADc,CAAAI,YAAA,EAAO,EACZ;UACTJ,EAAA,CAAAE,cAAA,kBAAyC;UAAnBF,EAAA,CAAAkE,UAAA,mBAAAC,+CAAA;YAAAnE,EAAA,CAAAoE,aAAA,CAAAC,GAAA;YAAA,OAAArE,EAAA,CAAAsE,WAAA,CAASX,GAAA,CAAAlB,MAAA,EAAQ;UAAA,EAAC;UACtCzC,EAAA,CAAAE,cAAA,gBAAU;UAAAF,EAAA,CAAAG,MAAA,cAAM;UAAAH,EAAA,CAAAI,YAAA,EAAW;UAC3BJ,EAAA,CAAAE,cAAA,YAAM;UAAAF,EAAA,CAAAG,MAAA,wBAAW;UAGvBH,EAHuB,CAAAI,YAAA,EAAO,EACjB,EACA,EACC;UAGZJ,EADF,CAAAE,cAAA,gBAA2B,eACa;UACpCF,EAAA,CAAA4D,SAAA,qBAA+B;UAEnC5D,EADE,CAAAI,YAAA,EAAM,EACD;UAGPJ,EAAA,CAAAiB,UAAA,KAAAsD,+BAAA,qBAKiD;;UAKjDvE,EAAA,CAAA4D,SAAA,yBAAuC;UAE3C5D,EADE,CAAAI,YAAA,EAAM,EACF;;;;UAlEeJ,EAAA,CAAAK,SAAA,GAAuC;UAASL,EAAhD,CAAAM,UAAA,SAAAN,EAAA,CAAAwE,WAAA,OAAAb,GAAA,CAAArC,WAAA,CAAAmD,YAAA,EAAuC,aAAAC,cAAA,CAAyB;UAoBlD1E,EAAA,CAAAK,SAAA,GAAuC;UAAvCL,EAAA,CAAAM,UAAA,SAAAN,EAAA,CAAAwE,WAAA,QAAAb,GAAA,CAAArC,WAAA,CAAAmD,YAAA,EAAuC;UAW3DzE,EAAA,CAAAK,SAAA,GAAsC;UAAtCL,EAAA,CAAAM,UAAA,SAAAN,EAAA,CAAAwE,WAAA,QAAAb,GAAA,CAAArC,WAAA,CAAAmD,YAAA,EAAsC;UA4BxCzE,EAAA,CAAAK,SAAA,IAAsC;UAAtCL,EAAA,CAAAM,UAAA,SAAAN,EAAA,CAAAwE,WAAA,SAAAb,GAAA,CAAArC,WAAA,CAAAmD,YAAA,EAAsC", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}