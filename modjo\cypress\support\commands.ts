/// <reference types="cypress" />
// ***********************************************
// This example commands.ts shows you how to
// create various custom commands and overwrite
// existing commands.
//
// For more comprehensive examples of custom
// commands please read more here:
// https://on.cypress.io/custom-commands
// ***********************************************

// Custom command to mock Firebase authentication
Cypress.Commands.add('mockFirebaseAuth', () => {
  cy.window().then((win) => {
    // Mock Firebase auth for testing
    (win as any).firebase = {
      auth: () => ({
        signInWithEmailAndPassword: cy.stub().resolves({
          user: { uid: 'test-user-id', email: '<EMAIL>' }
        }),
        createUserWithEmailAndPassword: cy.stub().resolves({
          user: { uid: 'test-user-id', email: '<EMAIL>' }
        }),
        signOut: cy.stub().resolves()
      })
    };
  });
});

// Custom command to wait for Angular
Cypress.Commands.add('waitForAngular', () => {
  cy.window().then((win) => {
    return new Cypress.Promise((resolve) => {
      if ((win as any).getAllAngularTestabilities) {
        const testabilities = (win as any).getAllAngularTestabilities();
        if (testabilities.length === 0) {
          resolve(undefined);
          return;
        }
        
        let count = 0;
        testabilities.forEach((testability: any) => {
          testability.whenStable(() => {
            count++;
            if (count === testabilities.length) {
              resolve(undefined);
            }
          });
        });
      } else {
        resolve(undefined);
      }
    });
  });
});

// Custom command to select Material select option
Cypress.Commands.add('selectMatOption', (selector: string, optionText: string) => {
  cy.get(selector).click();
  cy.get('.mat-option').contains(optionText).click();
});

// Custom command to check if element is in viewport
Cypress.Commands.add('isInViewport', { prevSubject: true }, (subject) => {
  const bottom = Cypress.$(cy.state('window')).height();
  const rect = subject[0].getBoundingClientRect();

  expect(rect.top).to.be.lessThan(bottom);
  expect(rect.bottom).to.be.greaterThan(0);
  
  return subject;
});

declare global {
  namespace Cypress {
    interface Chainable {
      mockFirebaseAuth(): Chainable<void>;
      waitForAngular(): Chainable<void>;
      selectMatOption(selector: string, optionText: string): Chainable<void>;
      isInViewport(): Chainable<Element>;
    }
  }
}
