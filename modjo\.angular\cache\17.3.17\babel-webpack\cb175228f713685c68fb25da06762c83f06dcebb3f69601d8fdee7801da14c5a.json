{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class TimeAgoPipe {\n  transform(value) {\n    if (!value) return '';\n    const now = new Date();\n    const date = new Date(value);\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    if (diffInSeconds < 60) {\n      return 'À l\\'instant';\n    }\n    const diffInMinutes = Math.floor(diffInSeconds / 60);\n    if (diffInMinutes < 60) {\n      return `Il y a ${diffInMinutes} min`;\n    }\n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) {\n      return `Il y a ${diffInHours}h`;\n    }\n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) {\n      return `Il y a ${diffInDays}j`;\n    }\n    const diffInWeeks = Math.floor(diffInDays / 7);\n    if (diffInWeeks < 4) {\n      return `Il y a ${diffInWeeks}sem`;\n    }\n    const diffInMonths = Math.floor(diffInDays / 30);\n    return `Il y a ${diffInMonths}mois`;\n  }\n  static {\n    this.ɵfac = function TimeAgoPipe_Factory(t) {\n      return new (t || TimeAgoPipe)();\n    };\n  }\n  static {\n    this.ɵpipe = /*@__PURE__*/i0.ɵɵdefinePipe({\n      name: \"timeAgo\",\n      type: TimeAgoPipe,\n      pure: true\n    });\n  }\n}", "map": {"version": 3, "names": ["TimeAgoPipe", "transform", "value", "now", "Date", "date", "diffInSeconds", "Math", "floor", "getTime", "diffInMinutes", "diffInHours", "diffInDays", "diffInWeeks", "diffInMonths", "pure"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\shared\\pipes\\time-ago.pipe.ts"], "sourcesContent": ["import { Pipe, PipeTransform } from '@angular/core';\n\n@Pipe({\n  name: 'timeAgo'\n})\nexport class TimeAgoPipe implements PipeTransform {\n  transform(value: Date | string): string {\n    if (!value) return '';\n    \n    const now = new Date();\n    const date = new Date(value);\n    const diffInSeconds = Math.floor((now.getTime() - date.getTime()) / 1000);\n    \n    if (diffInSeconds < 60) {\n      return 'À l\\'instant';\n    }\n    \n    const diffInMinutes = Math.floor(diffInSeconds / 60);\n    if (diffInMinutes < 60) {\n      return `Il y a ${diffInMinutes} min`;\n    }\n    \n    const diffInHours = Math.floor(diffInMinutes / 60);\n    if (diffInHours < 24) {\n      return `Il y a ${diffInHours}h`;\n    }\n    \n    const diffInDays = Math.floor(diffInHours / 24);\n    if (diffInDays < 7) {\n      return `Il y a ${diffInDays}j`;\n    }\n    \n    const diffInWeeks = Math.floor(diffInDays / 7);\n    if (diffInWeeks < 4) {\n      return `Il y a ${diffInWeeks}sem`;\n    }\n    \n    const diffInMonths = Math.floor(diffInDays / 30);\n    return `Il y a ${diffInMonths}mois`;\n  }\n}\n"], "mappings": ";AAKA,OAAM,MAAOA,WAAW;EACtBC,SAASA,CAACC,KAAoB;IAC5B,IAAI,CAACA,KAAK,EAAE,OAAO,EAAE;IAErB,MAAMC,GAAG,GAAG,IAAIC,IAAI,EAAE;IACtB,MAAMC,IAAI,GAAG,IAAID,IAAI,CAACF,KAAK,CAAC;IAC5B,MAAMI,aAAa,GAAGC,IAAI,CAACC,KAAK,CAAC,CAACL,GAAG,CAACM,OAAO,EAAE,GAAGJ,IAAI,CAACI,OAAO,EAAE,IAAI,IAAI,CAAC;IAEzE,IAAIH,aAAa,GAAG,EAAE,EAAE;MACtB,OAAO,cAAc;;IAGvB,MAAMI,aAAa,GAAGH,IAAI,CAACC,KAAK,CAACF,aAAa,GAAG,EAAE,CAAC;IACpD,IAAII,aAAa,GAAG,EAAE,EAAE;MACtB,OAAO,UAAUA,aAAa,MAAM;;IAGtC,MAAMC,WAAW,GAAGJ,IAAI,CAACC,KAAK,CAACE,aAAa,GAAG,EAAE,CAAC;IAClD,IAAIC,WAAW,GAAG,EAAE,EAAE;MACpB,OAAO,UAAUA,WAAW,GAAG;;IAGjC,MAAMC,UAAU,GAAGL,IAAI,CAACC,KAAK,CAACG,WAAW,GAAG,EAAE,CAAC;IAC/C,IAAIC,UAAU,GAAG,CAAC,EAAE;MAClB,OAAO,UAAUA,UAAU,GAAG;;IAGhC,MAAMC,WAAW,GAAGN,IAAI,CAACC,KAAK,CAACI,UAAU,GAAG,CAAC,CAAC;IAC9C,IAAIC,WAAW,GAAG,CAAC,EAAE;MACnB,OAAO,UAAUA,WAAW,KAAK;;IAGnC,MAAMC,YAAY,GAAGP,IAAI,CAACC,KAAK,CAACI,UAAU,GAAG,EAAE,CAAC;IAChD,OAAO,UAAUE,YAAY,MAAM;EACrC;;;uBAlCWd,WAAW;IAAA;EAAA;;;;YAAXA,WAAW;MAAAe,IAAA;IAAA;EAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}