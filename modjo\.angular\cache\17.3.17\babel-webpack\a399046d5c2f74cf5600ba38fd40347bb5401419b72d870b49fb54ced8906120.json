{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class SystemConfigComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function SystemConfigComponent_Factory(t) {\n      return new (t || SystemConfigComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: SystemConfigComponent,\n      selectors: [[\"app-system-config\"]],\n      decls: 5,\n      vars: 0,\n      template: function SystemConfigComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"h2\");\n          i0.ɵɵtext(2, \"\\u2699\\uFE0F Configuration Syst\\u00E8me\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"Param\\u00E8tres syst\\u00E8me et r\\u00E8gles de fonctionnement\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\"div[_ngcontent-%COMP%] { padding: 20px; }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInN5c3RlbS1jb25maWcuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLE1BQU0sYUFBYSxFQUFFIiwiZmlsZSI6InN5c3RlbS1jb25maWcuY29tcG9uZW50LnRzIiwic291cmNlc0NvbnRlbnQiOlsiZGl2IHsgcGFkZGluZzogMjBweDsgfSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvYWRtaW4tZGFzaGJvYXJkL2NvbXBvbmVudHMvc3lzdGVtLWNvbmZpZy9zeXN0ZW0tY29uZmlnLmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxNQUFNLGFBQWEsRUFBRTtBQUNyQixvU0FBb1MiLCJzb3VyY2VzQ29udGVudCI6WyJkaXYgeyBwYWRkaW5nOiAyMHB4OyB9Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["SystemConfigComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "template", "SystemConfigComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\admin-dashboard\\components\\system-config\\system-config.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-system-config',\n  template: '<div><h2>⚙️ Configuration Système</h2><p>Paramètres système et règles de fonctionnement</p></div>',\n  styles: ['div { padding: 20px; }']\n})\nexport class SystemConfigComponent implements OnInit {\n  constructor() { }\n  ngOnInit(): void { }\n}\n"], "mappings": ";AAOA,OAAM,MAAOA,qBAAqB;EAChCC,YAAA,GAAgB;EAChBC,QAAQA,CAAA,GAAW;;;uBAFRF,qBAAqB;IAAA;EAAA;;;YAArBA,qBAAqB;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAHhBE,EAAL,CAAAC,cAAA,UAAK,SAAI;UAAAD,EAAA,CAAAE,MAAA,8CAAwB;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAAAH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,oEAA8C;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}