<header class="header">
  <div class="header-container">
    <div class="logo">
      <a routerLink="/"><PERSON><PERSON><PERSON><PERSON></a>
    </div>
    
    <button class="menu-toggle" (click)="toggleMenu()">
      <span></span>
      <span></span>
      <span></span>
    </button>
    
    <nav class="nav" [class.open]="isMenuOpen">
      <ul class="nav-list">
        <!-- Common links for all authenticated users -->
        <li *ngIf="currentUser">
          <a routerLink="/profile" routerLinkActive="active">Mon Profil</a>
        </li>
        
        <!-- User-specific links -->
        <li *ngIf="currentUser && isUser">
          <a routerLink="/scanner" routerLinkActive="active">Scanner</a>
        </li>
        <li *ngIf="currentUser && isUser">
          <a routerLink="/rewards" routerLinkActive="active">Récompenses</a>
        </li>
        
        <!-- Provider-specific links -->
        <li *ngIf="currentUser && isProvider">
          <a routerLink="/provider-dashboard" routerLinkActive="active">Tableau de bord</a>
        </li>
        
        <!-- Validator-specific links -->
        <li *ngIf="currentUser && isValidator">
          <a routerLink="/validator-dashboard" routerLinkActive="active">Validation</a>
        </li>
        
        <!-- Admin-specific links -->
        <li *ngIf="currentUser && isAdmin">
          <a routerLink="/admin" routerLinkActive="active">Administration</a>
        </li>
        
        <!-- Authentication links -->
        <li *ngIf="!currentUser">
          <a routerLink="/login" routerLinkActive="active">Connexion</a>
        </li>
        <li *ngIf="!currentUser">
          <a routerLink="/register" routerLinkActive="active">Inscription</a>
        </li>
        <li *ngIf="currentUser">
          <a href="javascript:void(0)" (click)="logout()">Déconnexion</a>
        </li>
      </ul>
    </nav>
    
    <div class="user-info" *ngIf="currentUser">
      <span class="user-name">{{ currentUser.displayName }}</span>
      <span class="user-points">{{ currentUser.points }} points</span>
    </div>
  </div>
</header>
