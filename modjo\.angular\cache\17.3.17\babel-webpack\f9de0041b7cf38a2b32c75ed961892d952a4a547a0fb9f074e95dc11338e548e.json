{"ast": null, "code": "import { UserRole } from '../../../core/models';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../core/services/auth.service\";\nimport * as i2 from \"../../../core/services/mock-data.service\";\nimport * as i3 from \"../../../core/services/dashboard-router.service\";\nimport * as i4 from \"@angular/common\";\nimport * as i5 from \"@angular/material/button\";\nfunction RoleSwitcherComponent_div_0_button_4_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 5);\n    i0.ɵɵlistener(\"click\", function RoleSwitcherComponent_div_0_button_4_Template_button_click_0_listener() {\n      const user_r2 = i0.ɵɵrestoreView(_r1).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.switchToUser(user_r2));\n    });\n    i0.ɵɵelementStart(1, \"div\", 6)(2, \"span\", 7);\n    i0.ɵɵtext(3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(4, \"div\", 8)(5, \"strong\");\n    i0.ɵɵtext(6);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(7, \"small\");\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()()()();\n  }\n  if (rf & 2) {\n    const user_r2 = ctx.$implicit;\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵproperty(\"color\", (ctx_r2.currentUser == null ? null : ctx_r2.currentUser.uid) === user_r2.uid ? \"primary\" : \"basic\");\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(ctx_r2.getRoleEmoji(user_r2.role));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(user_r2.name);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getRoleDisplayName(user_r2.role));\n  }\n}\nfunction RoleSwitcherComponent_div_0_div_5_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 9)(1, \"p\")(2, \"strong\");\n    i0.ɵɵtext(3, \"Utilisateur actuel:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(4);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(5, \"p\")(6, \"strong\");\n    i0.ɵɵtext(7, \"Dashboard:\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(8);\n    i0.ɵɵelementEnd()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext(2);\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate2(\" \", ctx_r2.currentUser.name, \" (\", ctx_r2.getRoleDisplayName(ctx_r2.currentUser.role), \")\");\n    i0.ɵɵadvance(4);\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.dashboardRouter.getDashboardDisplayName(ctx_r2.currentUser.role), \"\");\n  }\n}\nfunction RoleSwitcherComponent_div_0_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 1)(1, \"h4\");\n    i0.ɵɵtext(2, \"\\uD83E\\uDDEA Mode Test - Changer de R\\u00F4le\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 2);\n    i0.ɵɵtemplate(4, RoleSwitcherComponent_div_0_button_4_Template, 9, 4, \"button\", 3);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtemplate(5, RoleSwitcherComponent_div_0_div_5_Template, 9, 3, \"div\", 4);\n    i0.ɵɵelementEnd();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngForOf\", ctx_r2.testUsers);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.currentUser);\n  }\n}\nexport class RoleSwitcherComponent {\n  constructor(authService, mockDataService, dashboardRouter) {\n    this.authService = authService;\n    this.mockDataService = mockDataService;\n    this.dashboardRouter = dashboardRouter;\n    this.testUsers = [];\n    this.currentUser = null;\n    this.isProduction = false; // Set to true in production\n  }\n  ngOnInit() {\n    // Only show in development mode\n    this.isProduction = false; // Change to true for production\n    if (!this.isProduction) {\n      this.testUsers = this.mockDataService.getMockUsers();\n      this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n      });\n    }\n  }\n  switchToUser(user) {\n    // Simulate login with the selected user\n    this.authService.setCurrentUser(user);\n    // Navigate to appropriate dashboard\n    this.dashboardRouter.navigateToUserDashboard(user);\n  }\n  getRoleDisplayName(role) {\n    const roleNames = {\n      [UserRole.USER]: 'Utilisateur',\n      [UserRole.ADMIN]: 'Administrateur',\n      [UserRole.VALIDATOR]: 'Validateur',\n      [UserRole.PARTNER]: 'Partenaire',\n      [UserRole.PROVIDER]: 'Prestataire'\n    };\n    return roleNames[role];\n  }\n  getRoleEmoji(role) {\n    const roleEmojis = {\n      [UserRole.USER]: '🙋‍♂️',\n      [UserRole.ADMIN]: '🧑‍💼',\n      [UserRole.VALIDATOR]: '🧑‍🏫',\n      [UserRole.PARTNER]: '🧑‍🍳',\n      [UserRole.PROVIDER]: '🧑‍🔧'\n    };\n    return roleEmojis[role];\n  }\n  static {\n    this.ɵfac = function RoleSwitcherComponent_Factory(t) {\n      return new (t || RoleSwitcherComponent)(i0.ɵɵdirectiveInject(i1.AuthService), i0.ɵɵdirectiveInject(i2.MockDataService), i0.ɵɵdirectiveInject(i3.DashboardRouterService));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: RoleSwitcherComponent,\n      selectors: [[\"app-role-switcher\"]],\n      decls: 1,\n      vars: 1,\n      consts: [[\"class\", \"role-switcher\", 4, \"ngIf\"], [1, \"role-switcher\"], [1, \"role-buttons\"], [\"mat-raised-button\", \"\", \"class\", \"role-btn\", 3, \"color\", \"click\", 4, \"ngFor\", \"ngForOf\"], [\"class\", \"current-user\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", 1, \"role-btn\", 3, \"click\", \"color\"], [1, \"role-info\"], [1, \"role-emoji\"], [1, \"role-details\"], [1, \"current-user\"]],\n      template: function RoleSwitcherComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵtemplate(0, RoleSwitcherComponent_div_0_Template, 6, 2, \"div\", 0);\n        }\n        if (rf & 2) {\n          i0.ɵɵproperty(\"ngIf\", !ctx.isProduction);\n        }\n      },\n      dependencies: [i4.NgForOf, i4.NgIf, i5.MatButton],\n      styles: [\".role-switcher[_ngcontent-%COMP%] {\\n      position: fixed;\\n      top: 20px;\\n      right: 20px;\\n      background: white;\\n      border: 2px solid #667eea;\\n      border-radius: 16px;\\n      padding: 16px;\\n      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n      z-index: 1000;\\n      max-width: 300px;\\n    }\\n    \\n    .role-switcher[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n      margin: 0 0 16px 0;\\n      color: #667eea;\\n      font-size: 0.9rem;\\n      text-align: center;\\n    }\\n    \\n    .role-buttons[_ngcontent-%COMP%] {\\n      display: flex;\\n      flex-direction: column;\\n      gap: 8px;\\n      margin-bottom: 16px;\\n    }\\n    \\n    .role-btn[_ngcontent-%COMP%] {\\n      padding: 8px 12px;\\n      text-align: left;\\n      height: auto;\\n    }\\n    \\n    .role-info[_ngcontent-%COMP%] {\\n      display: flex;\\n      align-items: center;\\n      gap: 12px;\\n    }\\n    \\n    .role-emoji[_ngcontent-%COMP%] {\\n      font-size: 1.5rem;\\n    }\\n    \\n    .role-details[_ngcontent-%COMP%] {\\n      display: flex;\\n      flex-direction: column;\\n      align-items: flex-start;\\n    }\\n    \\n    .role-details[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n      font-size: 0.9rem;\\n      color: #2d3748;\\n    }\\n    \\n    .role-details[_ngcontent-%COMP%]   small[_ngcontent-%COMP%] {\\n      font-size: 0.7rem;\\n      color: #718096;\\n      text-transform: uppercase;\\n      font-weight: 600;\\n    }\\n    \\n    .current-user[_ngcontent-%COMP%] {\\n      padding-top: 12px;\\n      border-top: 1px solid #e2e8f0;\\n      font-size: 0.8rem;\\n      color: #4a5568;\\n    }\\n    \\n    .current-user[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n      margin: 4px 0;\\n    }\\n    \\n    .current-user[_ngcontent-%COMP%]   strong[_ngcontent-%COMP%] {\\n      color: #2d3748;\\n    }\\n  \\n/*# sourceMappingURL=data:application/json;base64,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 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["UserRole", "i0", "ɵɵelementStart", "ɵɵlistener", "RoleSwitcherComponent_div_0_button_4_Template_button_click_0_listener", "user_r2", "ɵɵrestoreView", "_r1", "$implicit", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "switchToUser", "ɵɵtext", "ɵɵelementEnd", "ɵɵproperty", "currentUser", "uid", "ɵɵadvance", "ɵɵtextInterpolate", "getRoleEmoji", "role", "name", "getRoleDisplayName", "ɵɵtextInterpolate2", "ɵɵtextInterpolate1", "dashboardRouter", "getDashboardDisplayName", "ɵɵtemplate", "RoleSwitcherComponent_div_0_button_4_Template", "RoleSwitcherComponent_div_0_div_5_Template", "testUsers", "RoleSwitcherComponent", "constructor", "authService", "mockDataService", "isProduction", "ngOnInit", "getMockUsers", "currentUser$", "subscribe", "user", "setCurrentUser", "navigateToUserDashboard", "roleNames", "USER", "ADMIN", "VALIDATOR", "PARTNER", "PROVIDER", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "ɵɵdirectiveInject", "i1", "AuthService", "i2", "MockDataService", "i3", "DashboardRouterService", "selectors", "decls", "vars", "consts", "template", "RoleSwitcherComponent_Template", "rf", "ctx", "RoleSwitcherComponent_div_0_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\shared\\components\\role-switcher\\role-switcher.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\nimport { AuthService } from '../../../core/services/auth.service';\nimport { MockDataService } from '../../../core/services/mock-data.service';\nimport { DashboardRouterService } from '../../../core/services/dashboard-router.service';\nimport { User, UserRole } from '../../../core/models';\n\n@Component({\n  selector: 'app-role-switcher',\n  template: `\n    <div class=\"role-switcher\" *ngIf=\"!isProduction\">\n      <h4>🧪 Mode Test - Changer de Rôle</h4>\n      <div class=\"role-buttons\">\n        <button *ngFor=\"let user of testUsers\" \n                mat-raised-button \n                [color]=\"currentUser?.uid === user.uid ? 'primary' : 'basic'\"\n                (click)=\"switchToUser(user)\"\n                class=\"role-btn\">\n          <div class=\"role-info\">\n            <span class=\"role-emoji\">{{ getRoleEmoji(user.role) }}</span>\n            <div class=\"role-details\">\n              <strong>{{ user.name }}</strong>\n              <small>{{ getRoleDisplayName(user.role) }}</small>\n            </div>\n          </div>\n        </button>\n      </div>\n      <div class=\"current-user\" *ngIf=\"currentUser\">\n        <p><strong>Utilisateur actuel:</strong> {{ currentUser.name }} ({{ getRoleDisplayName(currentUser.role) }})</p>\n        <p><strong>Dashboard:</strong> {{ dashboardRouter.getDashboardDisplayName(currentUser.role) }}</p>\n      </div>\n    </div>\n  `,\n  styles: [`\n    .role-switcher {\n      position: fixed;\n      top: 20px;\n      right: 20px;\n      background: white;\n      border: 2px solid #667eea;\n      border-radius: 16px;\n      padding: 16px;\n      box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\n      z-index: 1000;\n      max-width: 300px;\n    }\n    \n    .role-switcher h4 {\n      margin: 0 0 16px 0;\n      color: #667eea;\n      font-size: 0.9rem;\n      text-align: center;\n    }\n    \n    .role-buttons {\n      display: flex;\n      flex-direction: column;\n      gap: 8px;\n      margin-bottom: 16px;\n    }\n    \n    .role-btn {\n      padding: 8px 12px;\n      text-align: left;\n      height: auto;\n    }\n    \n    .role-info {\n      display: flex;\n      align-items: center;\n      gap: 12px;\n    }\n    \n    .role-emoji {\n      font-size: 1.5rem;\n    }\n    \n    .role-details {\n      display: flex;\n      flex-direction: column;\n      align-items: flex-start;\n    }\n    \n    .role-details strong {\n      font-size: 0.9rem;\n      color: #2d3748;\n    }\n    \n    .role-details small {\n      font-size: 0.7rem;\n      color: #718096;\n      text-transform: uppercase;\n      font-weight: 600;\n    }\n    \n    .current-user {\n      padding-top: 12px;\n      border-top: 1px solid #e2e8f0;\n      font-size: 0.8rem;\n      color: #4a5568;\n    }\n    \n    .current-user p {\n      margin: 4px 0;\n    }\n    \n    .current-user strong {\n      color: #2d3748;\n    }\n  `]\n})\nexport class RoleSwitcherComponent implements OnInit {\n  testUsers: User[] = [];\n  currentUser: User | null = null;\n  isProduction = false; // Set to true in production\n\n  constructor(\n    private authService: AuthService,\n    private mockDataService: MockDataService,\n    public dashboardRouter: DashboardRouterService\n  ) {}\n\n  ngOnInit(): void {\n    // Only show in development mode\n    this.isProduction = false; // Change to true for production\n    \n    if (!this.isProduction) {\n      this.testUsers = this.mockDataService.getMockUsers();\n      \n      this.authService.currentUser$.subscribe(user => {\n        this.currentUser = user;\n      });\n    }\n  }\n\n  switchToUser(user: User): void {\n    // Simulate login with the selected user\n    this.authService.setCurrentUser(user);\n    \n    // Navigate to appropriate dashboard\n    this.dashboardRouter.navigateToUserDashboard(user);\n  }\n\n  getRoleDisplayName(role: UserRole): string {\n    const roleNames = {\n      [UserRole.USER]: 'Utilisateur',\n      [UserRole.ADMIN]: 'Administrateur',\n      [UserRole.VALIDATOR]: 'Validateur',\n      [UserRole.PARTNER]: 'Partenaire',\n      [UserRole.PROVIDER]: 'Prestataire'\n    };\n    return roleNames[role];\n  }\n\n  getRoleEmoji(role: UserRole): string {\n    const roleEmojis = {\n      [UserRole.USER]: '🙋‍♂️',\n      [UserRole.ADMIN]: '🧑‍💼',\n      [UserRole.VALIDATOR]: '🧑‍🏫',\n      [UserRole.PARTNER]: '🧑‍🍳',\n      [UserRole.PROVIDER]: '🧑‍🔧'\n    };\n    return roleEmojis[role];\n  }\n}\n"], "mappings": "AAIA,SAAeA,QAAQ,QAAQ,sBAAsB;;;;;;;;;;IAQ7CC,EAAA,CAAAC,cAAA,gBAIyB;IADjBD,EAAA,CAAAE,UAAA,mBAAAC,sEAAA;MAAA,MAAAC,OAAA,GAAAJ,EAAA,CAAAK,aAAA,CAAAC,GAAA,EAAAC,SAAA;MAAA,MAAAC,MAAA,GAAAR,EAAA,CAAAS,aAAA;MAAA,OAAAT,EAAA,CAAAU,WAAA,CAASF,MAAA,CAAAG,YAAA,CAAAP,OAAA,CAAkB;IAAA,EAAC;IAGhCJ,EADF,CAAAC,cAAA,aAAuB,cACI;IAAAD,EAAA,CAAAY,MAAA,GAA6B;IAAAZ,EAAA,CAAAa,YAAA,EAAO;IAE3Db,EADF,CAAAC,cAAA,aAA0B,aAChB;IAAAD,EAAA,CAAAY,MAAA,GAAe;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IAChCb,EAAA,CAAAC,cAAA,YAAO;IAAAD,EAAA,CAAAY,MAAA,GAAmC;IAGhDZ,EAHgD,CAAAa,YAAA,EAAQ,EAC9C,EACF,EACC;;;;;IAVDb,EAAA,CAAAc,UAAA,WAAAN,MAAA,CAAAO,WAAA,kBAAAP,MAAA,CAAAO,WAAA,CAAAC,GAAA,MAAAZ,OAAA,CAAAY,GAAA,uBAA6D;IAIxChB,EAAA,CAAAiB,SAAA,GAA6B;IAA7BjB,EAAA,CAAAkB,iBAAA,CAAAV,MAAA,CAAAW,YAAA,CAAAf,OAAA,CAAAgB,IAAA,EAA6B;IAE5CpB,EAAA,CAAAiB,SAAA,GAAe;IAAfjB,EAAA,CAAAkB,iBAAA,CAAAd,OAAA,CAAAiB,IAAA,CAAe;IAChBrB,EAAA,CAAAiB,SAAA,GAAmC;IAAnCjB,EAAA,CAAAkB,iBAAA,CAAAV,MAAA,CAAAc,kBAAA,CAAAlB,OAAA,CAAAgB,IAAA,EAAmC;;;;;IAM7CpB,EADL,CAAAC,cAAA,aAA8C,QACzC,aAAQ;IAAAD,EAAA,CAAAY,MAAA,0BAAmB;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IAACb,EAAA,CAAAY,MAAA,GAAmE;IAAAZ,EAAA,CAAAa,YAAA,EAAI;IAC5Gb,EAAH,CAAAC,cAAA,QAAG,aAAQ;IAAAD,EAAA,CAAAY,MAAA,iBAAU;IAAAZ,EAAA,CAAAa,YAAA,EAAS;IAACb,EAAA,CAAAY,MAAA,GAA+D;IAChGZ,EADgG,CAAAa,YAAA,EAAI,EAC9F;;;;IAFoCb,EAAA,CAAAiB,SAAA,GAAmE;IAAnEjB,EAAA,CAAAuB,kBAAA,MAAAf,MAAA,CAAAO,WAAA,CAAAM,IAAA,QAAAb,MAAA,CAAAc,kBAAA,CAAAd,MAAA,CAAAO,WAAA,CAAAK,IAAA,OAAmE;IAC5EpB,EAAA,CAAAiB,SAAA,GAA+D;IAA/DjB,EAAA,CAAAwB,kBAAA,MAAAhB,MAAA,CAAAiB,eAAA,CAAAC,uBAAA,CAAAlB,MAAA,CAAAO,WAAA,CAAAK,IAAA,MAA+D;;;;;IAlBhGpB,EADF,CAAAC,cAAA,aAAiD,SAC3C;IAAAD,EAAA,CAAAY,MAAA,oDAA8B;IAAAZ,EAAA,CAAAa,YAAA,EAAK;IACvCb,EAAA,CAAAC,cAAA,aAA0B;IACxBD,EAAA,CAAA2B,UAAA,IAAAC,6CAAA,oBAIyB;IAS3B5B,EAAA,CAAAa,YAAA,EAAM;IACNb,EAAA,CAAA2B,UAAA,IAAAE,0CAAA,iBAA8C;IAIhD7B,EAAA,CAAAa,YAAA,EAAM;;;;IAlBuBb,EAAA,CAAAiB,SAAA,GAAY;IAAZjB,EAAA,CAAAc,UAAA,YAAAN,MAAA,CAAAsB,SAAA,CAAY;IAcZ9B,EAAA,CAAAiB,SAAA,EAAiB;IAAjBjB,EAAA,CAAAc,UAAA,SAAAN,MAAA,CAAAO,WAAA,CAAiB;;;AAoFlD,OAAM,MAAOgB,qBAAqB;EAKhCC,YACUC,WAAwB,EACxBC,eAAgC,EACjCT,eAAuC;IAFtC,KAAAQ,WAAW,GAAXA,WAAW;IACX,KAAAC,eAAe,GAAfA,eAAe;IAChB,KAAAT,eAAe,GAAfA,eAAe;IAPxB,KAAAK,SAAS,GAAW,EAAE;IACtB,KAAAf,WAAW,GAAgB,IAAI;IAC/B,KAAAoB,YAAY,GAAG,KAAK,CAAC,CAAC;EAMnB;EAEHC,QAAQA,CAAA;IACN;IACA,IAAI,CAACD,YAAY,GAAG,KAAK,CAAC,CAAC;IAE3B,IAAI,CAAC,IAAI,CAACA,YAAY,EAAE;MACtB,IAAI,CAACL,SAAS,GAAG,IAAI,CAACI,eAAe,CAACG,YAAY,EAAE;MAEpD,IAAI,CAACJ,WAAW,CAACK,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;QAC7C,IAAI,CAACzB,WAAW,GAAGyB,IAAI;MACzB,CAAC,CAAC;;EAEN;EAEA7B,YAAYA,CAAC6B,IAAU;IACrB;IACA,IAAI,CAACP,WAAW,CAACQ,cAAc,CAACD,IAAI,CAAC;IAErC;IACA,IAAI,CAACf,eAAe,CAACiB,uBAAuB,CAACF,IAAI,CAAC;EACpD;EAEAlB,kBAAkBA,CAACF,IAAc;IAC/B,MAAMuB,SAAS,GAAG;MAChB,CAAC5C,QAAQ,CAAC6C,IAAI,GAAG,aAAa;MAC9B,CAAC7C,QAAQ,CAAC8C,KAAK,GAAG,gBAAgB;MAClC,CAAC9C,QAAQ,CAAC+C,SAAS,GAAG,YAAY;MAClC,CAAC/C,QAAQ,CAACgD,OAAO,GAAG,YAAY;MAChC,CAAChD,QAAQ,CAACiD,QAAQ,GAAG;KACtB;IACD,OAAOL,SAAS,CAACvB,IAAI,CAAC;EACxB;EAEAD,YAAYA,CAACC,IAAc;IACzB,MAAM6B,UAAU,GAAG;MACjB,CAAClD,QAAQ,CAAC6C,IAAI,GAAG,OAAO;MACxB,CAAC7C,QAAQ,CAAC8C,KAAK,GAAG,OAAO;MACzB,CAAC9C,QAAQ,CAAC+C,SAAS,GAAG,OAAO;MAC7B,CAAC/C,QAAQ,CAACgD,OAAO,GAAG,OAAO;MAC3B,CAAChD,QAAQ,CAACiD,QAAQ,GAAG;KACtB;IACD,OAAOC,UAAU,CAAC7B,IAAI,CAAC;EACzB;;;uBApDWW,qBAAqB,EAAA/B,EAAA,CAAAkD,iBAAA,CAAAC,EAAA,CAAAC,WAAA,GAAApD,EAAA,CAAAkD,iBAAA,CAAAG,EAAA,CAAAC,eAAA,GAAAtD,EAAA,CAAAkD,iBAAA,CAAAK,EAAA,CAAAC,sBAAA;IAAA;EAAA;;;YAArBzB,qBAAqB;MAAA0B,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,MAAA;MAAAC,QAAA,WAAAC,+BAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UArG9B/D,EAAA,CAAA2B,UAAA,IAAAsC,oCAAA,iBAAiD;;;UAArBjE,EAAA,CAAAc,UAAA,UAAAkD,GAAA,CAAA7B,YAAA,CAAmB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}