{"name": "modjo", "version": "1.0.0", "description": "Modjo - Social and community engagement PWA for Monastir and Sousse", "scripts": {"ng": "ng", "start": "ng serve", "dev": "ng serve --host 0.0.0.0 --port 4200", "build": "ng build --configuration production", "build:pwa": "ng build --configuration production", "watch": "ng build --watch --configuration development", "test": "ng test", "test:coverage": "ng test --code-coverage", "e2e": "cypress open", "e2e:headless": "cypress run", "lint": "ng lint", "deploy": "firebase deploy", "cap:add:android": "npx cap add android", "cap:add:ios": "npx cap add ios", "cap:sync": "npx cap sync", "cap:open:android": "npx cap open android", "cap:open:ios": "npx cap open ios", "cap:build:android": "npm run build:pwa && npx cap sync android && npx cap open android", "cap:build:ios": "npm run build:pwa && npx cap sync ios && npx cap open ios"}, "private": true, "dependencies": {"@angular/animations": "^17.3.0", "@angular/cdk": "^17.3.0", "@angular/common": "^17.3.0", "@angular/compiler": "^17.3.0", "@angular/core": "^17.3.0", "@angular/fire": "^17.0.1", "@angular/forms": "^17.3.0", "@angular/material": "^17.3.0", "@angular/platform-browser": "^17.3.0", "@angular/platform-browser-dynamic": "^17.3.0", "@angular/pwa": "^17.3.0", "@angular/router": "^17.3.0", "@angular/service-worker": "^17.3.0", "@capacitor/android": "^5.7.0", "@capacitor/app": "^5.0.6", "@capacitor/camera": "^5.0.7", "@capacitor/core": "^5.7.0", "@capacitor/haptics": "^5.0.6", "@capacitor/ios": "^5.7.0", "@capacitor/keyboard": "^5.0.6", "@capacitor/push-notifications": "^5.1.0", "@capacitor/splash-screen": "^5.0.6", "@capacitor/status-bar": "^5.0.6", "@zxing/library": "^0.20.0", "firebase": "^10.8.0", "rxjs": "~7.8.0", "tslib": "^2.3.0", "zone.js": "~0.14.2"}, "devDependencies": {"@angular-devkit/build-angular": "^17.3.0", "@angular-eslint/builder": "^17.2.1", "@angular-eslint/eslint-plugin": "^17.2.1", "@angular-eslint/eslint-plugin-template": "^17.2.1", "@angular-eslint/schematics": "^17.2.1", "@angular-eslint/template-parser": "^17.2.1", "@angular/cli": "^17.3.0", "@angular/compiler-cli": "^17.3.0", "@capacitor/cli": "^5.7.0", "@types/jasmine": "~5.1.0", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "autoprefixer": "^10.4.17", "cypress": "^13.6.6", "eslint": "^8.56.0", "jasmine-core": "~5.1.0", "karma": "~6.4.0", "karma-chrome-launcher": "~3.2.0", "karma-coverage": "~2.2.0", "karma-jasmine": "~5.1.0", "karma-jasmine-html-reporter": "~2.1.0", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "typescript": "~5.2.2"}}