{"ast": null, "code": "import * as i0 from \"@angular/core\";\nexport class ProviderManagementComponent {\n  constructor() {}\n  ngOnInit() {}\n  static {\n    this.ɵfac = function ProviderManagementComponent_Factory(t) {\n      return new (t || ProviderManagementComponent)();\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: ProviderManagementComponent,\n      selectors: [[\"app-provider-management\"]],\n      decls: 5,\n      vars: 0,\n      template: function ProviderManagementComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\")(1, \"h2\");\n          i0.ɵɵtext(2, \"\\uD83E\\uDDD1\\u200D\\uD83D\\uDD27 Gestion Prestataires\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(3, \"p\");\n          i0.ɵɵtext(4, \"Validation et suivi des prestataires (\\u00E9coles, associations, etc.)\");\n          i0.ɵɵelementEnd()();\n        }\n      },\n      styles: [\"div[_ngcontent-%COMP%] { padding: 20px; }\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInByb3ZpZGVyLW1hbmFnZW1lbnQuY29tcG9uZW50LnRzIl0sIm5hbWVzIjpbXSwibWFwcGluZ3MiOiJBQUFBLE1BQU0sYUFBYSxFQUFFIiwiZmlsZSI6InByb3ZpZGVyLW1hbmFnZW1lbnQuY29tcG9uZW50LnRzIiwic291cmNlc0NvbnRlbnQiOlsiZGl2IHsgcGFkZGluZzogMjBweDsgfSJdfQ== */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly8uL3NyYy9hcHAvZmVhdHVyZXMvYWRtaW4tZGFzaGJvYXJkL2NvbXBvbmVudHMvcHJvdmlkZXItbWFuYWdlbWVudC9wcm92aWRlci1tYW5hZ2VtZW50LmNvbXBvbmVudC50cyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQSxNQUFNLGFBQWEsRUFBRTtBQUNyQixvVEFBb1QiLCJzb3VyY2VzQ29udGVudCI6WyJkaXYgeyBwYWRkaW5nOiAyMHB4OyB9Il0sInNvdXJjZVJvb3QiOiIifQ== */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["ProviderManagementComponent", "constructor", "ngOnInit", "selectors", "decls", "vars", "template", "ProviderManagementComponent_Template", "rf", "ctx", "i0", "ɵɵelementStart", "ɵɵtext", "ɵɵelementEnd"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\admin-dashboard\\components\\provider-management\\provider-management.component.ts"], "sourcesContent": ["import { Component, OnInit } from '@angular/core';\n\n@Component({\n  selector: 'app-provider-management',\n  template: '<div><h2>🧑‍🔧 Gestion Prestataires</h2><p>Validation et suivi des prestataires (écoles, associations, etc.)</p></div>',\n  styles: ['div { padding: 20px; }']\n})\nexport class ProviderManagementComponent implements OnInit {\n  constructor() { }\n  ngOnInit(): void { }\n}\n"], "mappings": ";AAOA,OAAM,MAAOA,2BAA2B;EACtCC,YAAA,GAAgB;EAChBC,QAAQA,CAAA,GAAW;;;uBAFRF,2BAA2B;IAAA;EAAA;;;YAA3BA,2BAA2B;MAAAG,SAAA;MAAAC,KAAA;MAAAC,IAAA;MAAAC,QAAA,WAAAC,qCAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;UAHtBE,EAAL,CAAAC,cAAA,UAAK,SAAI;UAAAD,EAAA,CAAAE,MAAA,0DAA0B;UAAAF,EAAA,CAAAG,YAAA,EAAK;UAAAH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAE,MAAA,6EAAiE;UAAIF,EAAJ,CAAAG,YAAA,EAAI,EAAM", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}