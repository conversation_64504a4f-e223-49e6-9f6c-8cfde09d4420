{"ast": null, "code": "import { RouterModule } from '@angular/router';\nimport { SharedModule } from '../../shared/shared.module';\nimport { PartnerDashboardComponent } from './components/partner-dashboard/partner-dashboard.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"@angular/router\";\nconst routes = [{\n  path: '',\n  component: PartnerDashboardComponent\n}];\nexport class PartnerDashboardModule {\n  static {\n    this.ɵfac = function PartnerDashboardModule_Factory(t) {\n      return new (t || PartnerDashboardModule)();\n    };\n  }\n  static {\n    this.ɵmod = /*@__PURE__*/i0.ɵɵdefineNgModule({\n      type: PartnerDashboardModule\n    });\n  }\n  static {\n    this.ɵinj = /*@__PURE__*/i0.ɵɵdefineInjector({\n      imports: [SharedModule, RouterModule.forChild(routes)]\n    });\n  }\n}\n(function () {\n  (typeof ngJitMode === \"undefined\" || ngJitMode) && i0.ɵɵsetNgModuleScope(PartnerDashboardModule, {\n    declarations: [PartnerDashboardComponent],\n    imports: [SharedModule, i1.RouterModule]\n  });\n})();", "map": {"version": 3, "names": ["RouterModule", "SharedModule", "PartnerDashboardComponent", "routes", "path", "component", "PartnerDashboardModule", "<PERSON><PERSON><PERSON><PERSON>", "declarations", "imports", "i1"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\partner-dashboard\\partner-dashboard.module.ts"], "sourcesContent": ["import { NgModule } from '@angular/core';\nimport { RouterModule } from '@angular/router';\n\nimport { SharedModule } from '../../shared/shared.module';\nimport { PartnerDashboardComponent } from './components/partner-dashboard/partner-dashboard.component';\n\nconst routes = [\n  { path: '', component: PartnerDashboardComponent }\n];\n\n@NgModule({\n  declarations: [\n    PartnerDashboardComponent\n  ],\n  imports: [\n    SharedModule,\n    RouterModule.forChild(routes)\n  ]\n})\nexport class PartnerDashboardModule { }\n"], "mappings": "AACA,SAASA,YAAY,QAAQ,iBAAiB;AAE9C,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,yBAAyB,QAAQ,4DAA4D;;;AAEtG,MAAMC,MAAM,GAAG,CACb;EAAEC,IAAI,EAAE,EAAE;EAAEC,SAAS,EAAEH;AAAyB,CAAE,CACnD;AAWD,OAAM,MAAOI,sBAAsB;;;uBAAtBA,sBAAsB;IAAA;EAAA;;;YAAtBA;IAAsB;EAAA;;;gBAJ/BL,YAAY,EACZD,YAAY,CAACO,QAAQ,CAACJ,MAAM,CAAC;IAAA;EAAA;;;2EAGpBG,sBAAsB;IAAAE,YAAA,GAP/BN,yBAAyB;IAAAO,OAAA,GAGzBR,YAAY,EAAAS,EAAA,CAAAV,YAAA;EAAA;AAAA", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}