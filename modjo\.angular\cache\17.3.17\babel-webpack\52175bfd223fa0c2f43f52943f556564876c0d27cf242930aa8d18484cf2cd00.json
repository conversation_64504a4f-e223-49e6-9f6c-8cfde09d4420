{"ast": null, "code": "import { RewardsListComponent } from './components/rewards-list/rewards-list.component';\nimport { RewardDetailComponent } from './components/reward-detail/reward-detail.component';\nexport const rewardsRoutes = [{\n  path: '',\n  component: RewardsListComponent\n}, {\n  path: ':id',\n  component: RewardDetailComponent\n}];", "map": {"version": 3, "names": ["RewardsListComponent", "RewardDetailComponent", "rewardsRoutes", "path", "component"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\rewards\\rewards.routes.ts"], "sourcesContent": ["import { Routes } from '@angular/router';\nimport { RewardsListComponent } from './components/rewards-list/rewards-list.component';\nimport { RewardDetailComponent } from './components/reward-detail/reward-detail.component';\n\nexport const rewardsRoutes: Routes = [\n  {\n    path: '',\n    component: RewardsListComponent\n  },\n  {\n    path: ':id',\n    component: RewardDetailComponent\n  }\n];\n"], "mappings": "AACA,SAASA,oBAAoB,QAAQ,kDAAkD;AACvF,SAASC,qBAAqB,QAAQ,oDAAoD;AAE1F,OAAO,MAAMC,aAAa,GAAW,CACnC;EACEC,IAAI,EAAE,EAAE;EACRC,SAAS,EAAEJ;CACZ,EACD;EACEG,IAAI,EAAE,KAAK;EACXC,SAAS,EAAEH;CACZ,CACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}