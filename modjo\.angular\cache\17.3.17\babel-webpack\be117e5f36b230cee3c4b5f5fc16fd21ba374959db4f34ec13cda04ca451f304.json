{"ast": null, "code": "export var UserRole;\n(function (UserRole) {\n  UserRole[\"USER\"] = \"user\";\n  UserRole[\"PROVIDER\"] = \"provider\";\n  UserRole[\"VALIDATOR\"] = \"validator\";\n  UserRole[\"PARTNER\"] = \"partner\";\n  UserRole[\"ADMIN\"] = \"admin\"; // 🧑‍💼 Administrateur\n})(UserRole || (UserRole = {}));\nexport var TransactionType;\n(function (TransactionType) {\n  TransactionType[\"EARNED\"] = \"earned\";\n  TransactionType[\"SPENT\"] = \"spent\";\n  TransactionType[\"TRANSFERRED\"] = \"transferred\";\n  TransactionType[\"VALIDATED\"] = \"validated\";\n  TransactionType[\"BONUS\"] = \"bonus\";\n})(TransactionType || (TransactionType = {}));", "map": {"version": 3, "names": ["UserRole", "TransactionType"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\core\\models\\user.model.ts"], "sourcesContent": ["export interface User {\n  uid: string;\n  email: string;\n  name: string;\n  phone?: string;\n  city: '<PERSON><PERSON>ir' | 'Sousse';\n  role: UserR<PERSON>;\n  points: number;\n  avatar?: string;\n  createdAt: Date;\n  updatedAt: Date;\n  isActive: boolean;\n  history: PointsTransaction[];\n}\n\nexport interface UserProfile {\n  uid: string;\n  name: string;\n  email: string;\n  phone?: string;\n  city: 'Monastir' | 'Sousse';\n  avatar?: string;\n  bio?: string;\n  dateOfBirth?: Date;\n  preferences: UserPreferences;\n}\n\nexport interface UserPreferences {\n  notifications: boolean;\n  darkMode: boolean;\n  language: 'fr' | 'ar' | 'en';\n  location: boolean;\n}\n\nexport enum UserRole {\n  USER = 'user',           // 🙋‍♂️ Utilisateur standard\n  PROVIDER = 'provider',   // 🧑‍🔧 Prestataire (prof, coach, association)\n  VALIDATOR = 'validator', // 🧑‍🏫 Validateur (école, bibliothèque)\n  PARTNER = 'partner',     // 🧑‍🍳 Partenaire (commerçant, entreprise)\n  ADMIN = 'admin'          // 🧑‍💼 Administrateur\n}\n\nexport interface PointsTransaction {\n  id: string;\n  fromUserId?: string;\n  toUserId: string;\n  points: number;\n  type: TransactionType;\n  description: string;\n  timestamp: Date;\n  validatedBy?: string;\n  qrCode?: string;\n  location?: GeoLocation;\n}\n\nexport enum TransactionType {\n  EARNED = 'earned',\n  SPENT = 'spent',\n  TRANSFERRED = 'transferred',\n  VALIDATED = 'validated',\n  BONUS = 'bonus'\n}\n\nexport interface GeoLocation {\n  latitude: number;\n  longitude: number;\n  address?: string;\n}\n\nexport interface CreateUserRequest {\n  email: string;\n  password: string;\n  name: string;\n  phone?: string;\n  city: 'Monastir' | 'Sousse';\n  role: UserRole;\n}\n\nexport interface UpdateUserRequest {\n  name?: string;\n  phone?: string;\n  city?: 'Monastir' | 'Sousse';\n  avatar?: string;\n  bio?: string;\n  preferences?: Partial<UserPreferences>;\n}\n"], "mappings": "AAkCA,WAAYA,QAMX;AAND,WAAYA,QAAQ;EAClBA,QAAA,iBAAa;EACbA,QAAA,yBAAqB;EACrBA,QAAA,2BAAuB;EACvBA,QAAA,uBAAmB;EACnBA,QAAA,mBAAe,EAAU;AAC3B,CAAC,EANWA,QAAQ,KAARA,QAAQ;AAqBpB,WAAYC,eAMX;AAND,WAAYA,eAAe;EACzBA,eAAA,qBAAiB;EACjBA,eAAA,mBAAe;EACfA,eAAA,+BAA2B;EAC3BA,eAAA,2BAAuB;EACvBA,eAAA,mBAAe;AACjB,CAAC,EANWA,eAAe,KAAfA,eAAe", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}