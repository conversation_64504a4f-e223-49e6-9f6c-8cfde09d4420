<div class="login-container">
  <div class="login-card">
    <h2>Connexion</h2>
    <p>Connectez-vous à votre compte ModJob</p>

    <form (ngSubmit)="onSubmit()">
      <div class="form-group">
        <label for="email">Email</label>
        <input
          type="email"
          id="email"
          name="email"
          [(ngModel)]="email"
          required
          placeholder="Entrez votre email"
        >
      </div>

      <div class="form-group">
        <label for="password">Mot de passe</label>
        <input
          type="password"
          id="password"
          name="password"
          [(ngModel)]="password"
          required
          placeholder="Entrez votre mot de passe"
        >
      </div>

      <div class="error-message" *ngIf="errorMessage">
        {{ errorMessage }}
      </div>

      <button type="submit" [disabled]="isLoading">
        <span *ngIf="isLoading">Chargement...</span>
        <span *ngIf="!isLoading">Se connecter</span>
      </button>
    </form>

    <div class="login-footer">
      <p>Vous n'avez pas de compte ? <a routerLink="/register">Inscrivez-vous</a></p>
    </div>

    <div class="demo-accounts">
      <h3>Comptes de démonstration</h3>
      <p>Utilisateur: user&#64;example.com / password</p>
      <p>Prestataire: provider&#64;example.com / password</p>
      <p>Validateur: validator&#64;example.com / password</p>
    </div>
  </div>
</div>
