import { Component, OnInit, On<PERSON><PERSON>roy, ViewChild, ElementRef } from '@angular/core';
import { CommonModule } from '@angular/common';
import { Router } from '@angular/router';
import { ScannerService } from '../scanner.service';
import { AuthService } from '../../auth/auth.service';

// This would normally be imported from the html5-qrcode package
// import { Html5Qrcode } from 'html5-qrcode';

@Component({
  selector: 'app-scanner',
  standalone: true,
  imports: [CommonModule],
  templateUrl: './scanner.component.html',
  styleUrls: ['./scanner.component.css']
})
export class ScannerComponent implements OnInit, OnDestroy {
  @ViewChild('reader', { static: false }) readerElement!: ElementRef;
  
  isScanning: boolean = false;
  scanError: string = '';
  scanResult: string = '';
  
  // This would be initialized with the Html5Qrcode library
  private scanner: any = null;

  constructor(
    private scannerService: ScannerService,
    private authService: AuthService,
    private router: Router
  ) {}

  ngOnInit(): void {
    // Check if user is authenticated
    if (!this.authService.isAuthenticated()) {
      this.router.navigate(['/login'], { queryParams: { returnUrl: '/scanner' } });
      return;
    }
  }

  ngAfterViewInit(): void {
    // In a real implementation, we would initialize the scanner here
    // this.initScanner();
    
    // For demo purposes, we'll simulate the scanner
    this.simulateScanner();
  }

  ngOnDestroy(): void {
    // In a real implementation, we would stop the scanner here
    // this.stopScanner();
  }

  // Initialize the QR code scanner
  private initScanner(): void {
    // This would be the actual implementation with the Html5Qrcode library
    /*
    this.scanner = new Html5Qrcode('reader');
    
    this.isScanning = true;
    this.scanner.start(
      { facingMode: 'environment' }, // Use the back camera
      {
        fps: 10,
        qrbox: 250
      },
      this.onScanSuccess.bind(this),
      this.onScanError.bind(this)
    ).catch((err: any) => {
      this.scanError = 'Erreur d\'initialisation de la caméra: ' + err;
      this.isScanning = false;
    });
    */
  }

  // Stop the QR code scanner
  private stopScanner(): void {
    // This would be the actual implementation with the Html5Qrcode library
    /*
    if (this.scanner && this.isScanning) {
      this.scanner.stop().then(() => {
        this.isScanning = false;
      }).catch((err: any) => {
        console.error('Error stopping scanner:', err);
      });
    }
    */
  }

  // Handle successful scan
  private onScanSuccess(decodedText: string): void {
    this.scanResult = decodedText;
    this.stopScanner();
    
    // Process the scanned QR code
    const knowMeId = this.scannerService.parseKnowMeUrl(decodedText);
    if (knowMeId) {
      // Navigate to the profile view with the KnowMe ID
      this.router.navigate(['/profile-view', knowMeId]);
    } else {
      this.scanError = 'QR code invalide. Veuillez scanner un code QR KnowMe.';
    }
  }

  // Handle scan error
  private onScanError(error: any): void {
    console.error('QR scan error:', error);
    // We don't set scanError here to avoid flooding the UI with errors
  }

  // Restart scanning
  restartScan(): void {
    this.scanResult = '';
    this.scanError = '';
    this.initScanner();
  }

  // For demo purposes only - simulate scanning a KnowMe QR code
  private simulateScanner(): void {
    // Add buttons to simulate scanning different QR codes
    setTimeout(() => {
      const demoContainer = document.createElement('div');
      demoContainer.className = 'demo-container';
      
      const title = document.createElement('h3');
      title.textContent = 'Simulation de scan (pour démo)';
      demoContainer.appendChild(title);
      
      const button1 = document.createElement('button');
      button1.textContent = 'Simuler scan coiffeur';
      button1.onclick = () => this.onScanSuccess('https://knowme.com/profile/coiffeur123');
      demoContainer.appendChild(button1);
      
      const button2 = document.createElement('button');
      button2.textContent = 'Simuler scan étudiant';
      button2.onclick = () => this.onScanSuccess('https://knowme.com/profile/etudiant456');
      demoContainer.appendChild(button2);
      
      const button3 = document.createElement('button');
      button3.textContent = 'Simuler scan invalide';
      button3.onclick = () => {
        this.scanError = 'QR code invalide. Veuillez scanner un code QR KnowMe.';
      };
      demoContainer.appendChild(button3);
      
      this.readerElement.nativeElement.appendChild(demoContainer);
    }, 500);
  }
}
