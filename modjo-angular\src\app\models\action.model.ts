export enum ActionType {
  TASK_COMPLETION = 'task_completion',
  VOLUNTEER_WORK = 'volunteer_work',
  EDUCATIONAL_ACHIEVEMENT = 'educational_achievement',
  OTHER = 'other'
}

export interface Action {
  id: string;
  userId: string;
  validatorId?: string;
  type: ActionType;
  description: string;
  pointsAwarded: number;
  status: 'pending' | 'validated' | 'rejected';
  createdAt: Date;
  validatedAt?: Date;
}
