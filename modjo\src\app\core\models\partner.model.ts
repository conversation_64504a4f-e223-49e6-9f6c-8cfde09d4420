export interface PartnerBusiness {
  id: string;
  userId: string; // Lié au compte utilisateur
  businessName: string;
  businessType: PartnerBusinessType;
  description: string;
  address: string;
  city: 'Monastir' | 'Sousse';
  phone: string;
  email: string;
  logo?: string;
  website?: string;
  socialMedia?: SocialMediaLinks;
  isActive: boolean;
  verificationStatus: PartnerVerificationStatus;
  joinedAt: Date;
  lastActiveAt: Date;
}

export enum PartnerBusinessType {
  RESTAURANT = 'restaurant',
  RETAIL = 'retail',
  SERVICES = 'services',
  ENTERTAINMENT = 'entertainment',
  HEALTH = 'health',
  EDUCATION = 'education',
  SPORTS = 'sports',
  CULTURE = 'culture',
  OTHER = 'other'
}

export enum PartnerVerificationStatus {
  PENDING = 'pending',
  VERIFIED = 'verified',
  REJECTED = 'rejected',
  SUSPENDED = 'suspended'
}

export interface SocialMediaLinks {
  facebook?: string;
  instagram?: string;
  twitter?: string;
  linkedin?: string;
}

export interface PartnerReward {
  id: string;
  partnerId: string;
  partnerName: string;
  title: string;
  description: string;
  pointsCost: number;
  originalPrice?: number;
  discountPercentage?: number;
  category: PartnerRewardCategory;
  image?: string;
  terms: string;
  validFrom: Date;
  validUntil: Date;
  maxRedemptions?: number;
  currentRedemptions: number;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export enum PartnerRewardCategory {
  FOOD = 'food',
  SHOPPING = 'shopping',
  SERVICES = 'services',
  ENTERTAINMENT = 'entertainment',
  HEALTH = 'health',
  EDUCATION = 'education',
  SPORTS = 'sports',
  CULTURE = 'culture',
  DISCOUNT = 'discount',
  FREE_ITEM = 'free_item',
  OTHER = 'other'
}

export interface RewardRedemption {
  id: string;
  rewardId: string;
  userId: string;
  userName: string;
  partnerId: string;
  partnerName: string;
  pointsUsed: number;
  redemptionCode: string;
  status: RedemptionStatus;
  redeemedAt: Date;
  usedAt?: Date;
  expiresAt: Date;
  qrCode?: string;
}

export enum RedemptionStatus {
  PENDING = 'pending',
  CONFIRMED = 'confirmed',
  USED = 'used',
  EXPIRED = 'expired',
  CANCELLED = 'cancelled'
}

export interface PartnerStats {
  partnerId: string;
  totalRewards: number;
  activeRewards: number;
  totalRedemptions: number;
  totalPointsGenerated: number;
  averageRedemptionValue: number;
  popularRewards: PopularReward[];
  monthlyStats: MonthlyPartnerStats[];
  customerRetention: number; // pourcentage
  rating: number;
  reviewCount: number;
}

export interface PopularReward {
  rewardId: string;
  title: string;
  redemptionCount: number;
  pointsCost: number;
}

export interface MonthlyPartnerStats {
  month: string; // YYYY-MM
  redemptions: number;
  pointsGenerated: number;
  newCustomers: number;
  revenue: number;
}

export interface CreatePartnerRewardRequest {
  title: string;
  description: string;
  pointsCost: number;
  originalPrice?: number;
  category: PartnerRewardCategory;
  image?: string;
  terms: string;
  validUntil: Date;
  maxRedemptions?: number;
}

export interface UpdatePartnerRewardRequest {
  title?: string;
  description?: string;
  pointsCost?: number;
  originalPrice?: number;
  terms?: string;
  validUntil?: Date;
  maxRedemptions?: number;
  isActive?: boolean;
}
