# 🚀 Guide de Configuration Modjo PWA

Ce guide vous accompagne dans la configuration complète de l'application Modjo.

## ✅ Checklist de Configuration

### 1. Prérequis Système
- [ ] Node.js 18+ installé
- [ ] npm ou yarn installé
- [ ] Angular CLI 17+ installé (`npm install -g @angular/cli`)
- [ ] Git installé
- [ ] Compte Firebase créé

### 2. Installation des Dépendances
```bash
cd modjo
npm install
```

### 3. Configuration Firebase

#### 3.1 Créer un Projet Firebase
1. Aller sur https://console.firebase.google.com
2. Cliquer sur "Créer un projet"
3. Nommer le projet "modjo-app" (ou autre nom)
4. Activer Google Analytics (optionnel)

#### 3.2 Configurer Authentication
1. Dans la console Firebase, aller dans "Authentication"
2. Cliquer sur "Commencer"
3. Onglet "Sign-in method"
4. Activer "E-mail/Mot de passe"
5. Optionnel: Activer "Lien e-mail (connexion sans mot de passe)"

#### 3.3 Configurer Firestore
1. Aller dans "Firestore Database"
2. Cliquer sur "Créer une base de données"
3. Choisir "Commencer en mode test" (pour le développement)
4. Sélectionner une région proche (europe-west1 pour l'Europe)

#### 3.4 Configurer Storage
1. Aller dans "Storage"
2. Cliquer sur "Commencer"
3. Accepter les règles par défaut

#### 3.5 Obtenir la Configuration
1. Aller dans "Paramètres du projet" (icône engrenage)
2. Faire défiler jusqu'à "Vos applications"
3. Cliquer sur l'icône web "</>"
4. Nommer l'app "Modjo Web"
5. Cocher "Configurer Firebase Hosting"
6. Copier la configuration

#### 3.6 Mettre à Jour les Environnements
Remplacer la configuration dans `src/environments/environment.ts` et `src/environments/environment.prod.ts`:

```typescript
export const environment = {
  production: false, // true pour environment.prod.ts
  firebase: {
    apiKey: "VOTRE_API_KEY",
    authDomain: "VOTRE_PROJECT_ID.firebaseapp.com",
    projectId: "VOTRE_PROJECT_ID",
    storageBucket: "VOTRE_PROJECT_ID.appspot.com",
    messagingSenderId: "VOTRE_SENDER_ID",
    appId: "VOTRE_APP_ID"
  },
  // ... reste de la configuration
};
```

### 4. Configuration PWA

#### 4.1 Installer Angular PWA
```bash
ng add @angular/pwa
```

#### 4.2 Personnaliser le Manifest
Le fichier `src/manifest.json` est déjà configuré. Vous pouvez personnaliser:
- `name` et `short_name`
- `theme_color` et `background_color`
- Les icônes (placez vos icônes dans `src/assets/icons/`)

### 5. Configuration Mobile (Capacitor)

#### 5.1 Installer Capacitor
```bash
npm install @capacitor/core @capacitor/cli
npx cap init modjo tn.modjo.app
```

#### 5.2 Ajouter les Plateformes
```bash
# Android
npx cap add android

# iOS (sur macOS uniquement)
npx cap add ios
```

#### 5.3 Configurer les Permissions
Le fichier `capacitor.config.ts` est déjà configuré avec les permissions nécessaires.

### 6. Configuration des Tests

#### 6.1 Tests E2E avec Cypress
```bash
npm install cypress --save-dev
```

#### 6.2 Configuration ESLint
```bash
ng add @angular-eslint/schematics
```

### 7. Déploiement Firebase

#### 7.1 Installer Firebase CLI
```bash
npm install -g firebase-tools
```

#### 7.2 Se Connecter à Firebase
```bash
firebase login
```

#### 7.3 Initialiser Firebase dans le Projet
```bash
firebase init
```
Sélectionner:
- Hosting
- Firestore
- Storage
- Functions (optionnel)

#### 7.4 Déployer
```bash
npm run build
firebase deploy
```

## 🧪 Tests et Validation

### Tester l'Application Localement
```bash
# Serveur de développement
npm run dev

# Build de production
npm run build

# Tests unitaires
npm test

# Tests E2E
npm run e2e
```

### Tester la PWA
1. Ouvrir Chrome DevTools
2. Aller dans l'onglet "Application"
3. Vérifier "Service Workers" et "Manifest"
4. Tester l'installation PWA

### Tester sur Mobile
```bash
# Build et sync
npm run build
npx cap sync

# Ouvrir dans l'IDE natif
npx cap open android
npx cap open ios
```

## 🔧 Configuration Avancée

### Variables d'Environnement
Créer un fichier `.env` à la racine:
```
FIREBASE_API_KEY=your-api-key
FIREBASE_PROJECT_ID=your-project-id
GOOGLE_MAPS_API_KEY=your-maps-key
```

### Notifications Push
1. Dans Firebase Console, aller dans "Cloud Messaging"
2. Générer une paire de clés VAPID
3. Ajouter la clé publique dans `environment.ts`

### Analytics
1. Activer Google Analytics dans Firebase
2. Installer `@angular/fire/analytics`
3. Configurer dans `app.module.ts`

## 🚨 Dépannage

### Erreurs Communes

#### "Firebase not initialized"
- Vérifier la configuration dans `environment.ts`
- S'assurer que Firebase est importé dans `app.module.ts`

#### "Permission denied" Firestore
- Vérifier les règles Firestore
- S'assurer que l'utilisateur est authentifié

#### PWA ne s'installe pas
- Vérifier que l'app est servie en HTTPS
- Vérifier le manifest.json
- Vérifier le service worker

#### Build mobile échoue
- Vérifier que les SDKs Android/iOS sont installés
- Mettre à jour Capacitor: `npm update @capacitor/core @capacitor/cli`

### Logs et Debug
```bash
# Logs Firebase
firebase functions:log

# Debug Capacitor
npx cap doctor

# Analyser le bundle
npm run build -- --stats-json
npx webpack-bundle-analyzer dist/modjo/stats.json
```

## 📞 Support

- Documentation Firebase: https://firebase.google.com/docs
- Documentation Angular: https://angular.io/docs
- Documentation Capacitor: https://capacitorjs.com/docs
- Issues GitHub: [Créer une issue]

---

**Configuration terminée! 🎉 Votre application Modjo est prête pour le développement.**
