{"ast": null, "code": "import _asyncToGenerator from \"C:/Users/<USER>/Documents/augment-projects/modjo/modjo/node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\";\nimport { PointsAssignmentComponent } from '../points-assignment/points-assignment.component';\nimport * as i0 from \"@angular/core\";\nimport * as i1 from \"../../../../core/services/qr.service\";\nimport * as i2 from \"../../../../core/services/user.service\";\nimport * as i3 from \"../../../../core/services/auth.service\";\nimport * as i4 from \"@angular/material/dialog\";\nimport * as i5 from \"@angular/material/snack-bar\";\nimport * as i6 from \"@angular/common\";\nimport * as i7 from \"@angular/material/card\";\nimport * as i8 from \"@angular/material/button\";\nimport * as i9 from \"@angular/material/icon\";\nconst _c0 = [\"video\"];\nconst _c1 = [\"canvas\"];\nfunction QrScannerComponent_div_6_div_8_Template(rf, ctx) {\n  if (rf & 1) {\n    i0.ɵɵelementStart(0, \"div\", 26)(1, \"div\", 27)(2, \"div\", 28);\n    i0.ɵɵelement(3, \"div\", 29)(4, \"div\", 30)(5, \"div\", 31)(6, \"div\", 32);\n    i0.ɵɵelementEnd()();\n    i0.ɵɵelementStart(7, \"p\", 33);\n    i0.ɵɵtext(8, \"Placez le QR code dans le cadre\");\n    i0.ɵɵelementEnd()();\n  }\n}\nfunction QrScannerComponent_div_6_button_10_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r2 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 34);\n    i0.ɵɵlistener(\"click\", function QrScannerComponent_div_6_button_10_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r2);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.startScanning());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"qr_code_scanner\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Commencer le scan \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QrScannerComponent_div_6_button_11_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r4 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 35);\n    i0.ɵɵlistener(\"click\", function QrScannerComponent_div_6_button_11_Template_button_click_0_listener() {\n      i0.ɵɵrestoreView(_r4);\n      const ctx_r2 = i0.ɵɵnextContext(2);\n      return i0.ɵɵresetView(ctx_r2.stopScanning());\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2, \"stop\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(3, \" Arr\\u00EAter le scan \");\n    i0.ɵɵelementEnd();\n  }\n}\nfunction QrScannerComponent_div_6_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r1 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 15)(1, \"mat-card\", 16)(2, \"mat-card-content\")(3, \"div\", 17);\n    i0.ɵɵelement(4, \"video\", 18, 0)(6, \"canvas\", 19, 1);\n    i0.ɵɵtemplate(8, QrScannerComponent_div_6_div_8_Template, 9, 0, \"div\", 20);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(9, \"div\", 21);\n    i0.ɵɵtemplate(10, QrScannerComponent_div_6_button_10_Template, 4, 0, \"button\", 22)(11, QrScannerComponent_div_6_button_11_Template, 4, 0, \"button\", 23);\n    i0.ɵɵelementStart(12, \"button\", 24);\n    i0.ɵɵlistener(\"click\", function QrScannerComponent_div_6_Template_button_click_12_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.toggleFlashlight());\n    });\n    i0.ɵɵelementStart(13, \"mat-icon\");\n    i0.ɵɵtext(14);\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(15);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(16, \"button\", 25);\n    i0.ɵɵlistener(\"click\", function QrScannerComponent_div_6_Template_button_click_16_listener() {\n      i0.ɵɵrestoreView(_r1);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.switchCamera());\n    });\n    i0.ɵɵelementStart(17, \"mat-icon\");\n    i0.ɵɵtext(18, \"flip_camera_android\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(19, \" Changer cam\\u00E9ra \");\n    i0.ɵɵelementEnd()()()()();\n  }\n  if (rf & 2) {\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵadvance(4);\n    i0.ɵɵclassProp(\"active\", ctx_r2.isScanning);\n    i0.ɵɵadvance(4);\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isScanning);\n    i0.ɵɵadvance(2);\n    i0.ɵɵproperty(\"ngIf\", !ctx_r2.isScanning);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"ngIf\", ctx_r2.isScanning);\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.isScanning);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.flashlightOn ? \"flash_off\" : \"flash_on\");\n    i0.ɵɵadvance();\n    i0.ɵɵtextInterpolate1(\" \", ctx_r2.flashlightOn ? \"Flash Off\" : \"Flash On\", \" \");\n    i0.ɵɵadvance();\n    i0.ɵɵproperty(\"disabled\", !ctx_r2.isScanning);\n  }\n}\nfunction QrScannerComponent_div_7_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r5 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"div\", 36)(1, \"mat-card\", 37)(2, \"mat-card-content\")(3, \"div\", 38)(4, \"mat-icon\", 39);\n    i0.ɵɵtext(5, \"camera_alt\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"h3\");\n    i0.ɵɵtext(7, \"Cam\\u00E9ra non disponible\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(8, \"p\");\n    i0.ɵɵtext(9, \"Votre appareil ne dispose pas de cam\\u00E9ra ou l'acc\\u00E8s a \\u00E9t\\u00E9 refus\\u00E9.\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(10, \"button\", 40);\n    i0.ɵɵlistener(\"click\", function QrScannerComponent_div_7_Template_button_click_10_listener() {\n      i0.ɵɵrestoreView(_r5);\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.manualQrInput());\n    });\n    i0.ɵɵelementStart(11, \"mat-icon\");\n    i0.ɵɵtext(12, \"keyboard\");\n    i0.ɵɵelementEnd();\n    i0.ɵɵtext(13, \" Saisie manuelle \");\n    i0.ɵɵelementEnd()()()()();\n  }\n}\nfunction QrScannerComponent_button_19_Template(rf, ctx) {\n  if (rf & 1) {\n    const _r6 = i0.ɵɵgetCurrentView();\n    i0.ɵɵelementStart(0, \"button\", 41);\n    i0.ɵɵlistener(\"click\", function QrScannerComponent_button_19_Template_button_click_0_listener() {\n      const demo_r7 = i0.ɵɵrestoreView(_r6).$implicit;\n      const ctx_r2 = i0.ɵɵnextContext();\n      return i0.ɵɵresetView(ctx_r2.testQrCode(demo_r7.data));\n    });\n    i0.ɵɵelementStart(1, \"mat-icon\");\n    i0.ɵɵtext(2);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(3, \"div\", 42)(4, \"span\", 43);\n    i0.ɵɵtext(5);\n    i0.ɵɵelementEnd();\n    i0.ɵɵelementStart(6, \"small\", 44);\n    i0.ɵɵtext(7);\n    i0.ɵɵelementEnd()()();\n  }\n  if (rf & 2) {\n    const demo_r7 = ctx.$implicit;\n    const i_r8 = ctx.index;\n    const ctx_r2 = i0.ɵɵnextContext();\n    i0.ɵɵstyleProp(\"animation-delay\", i_r8 * 0.1 + \"s\");\n    i0.ɵɵproperty(\"color\", ctx_r2.getDemoButtonColor(i_r8));\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getDemoButtonIcon(demo_r7.label));\n    i0.ɵɵadvance(3);\n    i0.ɵɵtextInterpolate(demo_r7.label);\n    i0.ɵɵadvance(2);\n    i0.ɵɵtextInterpolate(ctx_r2.getDemoPoints(demo_r7.label));\n  }\n}\nexport class QrScannerComponent {\n  constructor(qrService, userService, authService, dialog, snackBar) {\n    this.qrService = qrService;\n    this.userService = userService;\n    this.authService = authService;\n    this.dialog = dialog;\n    this.snackBar = snackBar;\n    this.isScanning = false;\n    this.hasCamera = false;\n    this.currentUser = null;\n    this.stream = null;\n    // Demo QR codes for testing\n    this.demoQrCodes = [{\n      label: 'Transfert utilisateur (1 pt)',\n      data: 'user:demo123'\n    }, {\n      label: 'Action validateur (10 pts)',\n      data: 'validator:val456:community_service:10'\n    }, {\n      label: 'Récompense partenaire (5 pts)',\n      data: '{\"type\":\"partner_reward\",\"points\":5,\"timestamp\":' + Date.now() + '}'\n    }, {\n      label: 'Bonus système (3 pts)',\n      data: '{\"type\":\"system_bonus\",\"points\":3,\"timestamp\":' + Date.now() + '}'\n    }];\n  }\n  ngOnInit() {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.checkCameraSupport();\n  }\n  ngOnDestroy() {\n    this.stopScanning();\n  }\n  checkCameraSupport() {\n    var _this = this;\n    return _asyncToGenerator(function* () {\n      try {\n        const devices = yield navigator.mediaDevices.enumerateDevices();\n        _this.hasCamera = devices.some(device => device.kind === 'videoinput');\n      } catch (error) {\n        console.error('Error checking camera support:', error);\n        _this.hasCamera = false;\n      }\n    })();\n  }\n  startScanning() {\n    var _this2 = this;\n    return _asyncToGenerator(function* () {\n      if (!_this2.hasCamera) {\n        _this2.snackBar.open('Caméra non disponible', 'Fermer', {\n          duration: 3000\n        });\n        return;\n      }\n      try {\n        _this2.stream = yield navigator.mediaDevices.getUserMedia({\n          video: {\n            facingMode: 'environment'\n          }\n        });\n        _this2.video.nativeElement.srcObject = _this2.stream;\n        _this2.video.nativeElement.play();\n        _this2.isScanning = true;\n        // Start scanning for QR codes\n        _this2.scanInterval = setInterval(() => {\n          _this2.scanForQrCode();\n        }, 500);\n      } catch (error) {\n        console.error('Error starting camera:', error);\n        _this2.snackBar.open('Erreur d\\'accès à la caméra', 'Fermer', {\n          duration: 3000\n        });\n      }\n    })();\n  }\n  stopScanning() {\n    if (this.stream) {\n      this.stream.getTracks().forEach(track => track.stop());\n      this.stream = null;\n    }\n    if (this.scanInterval) {\n      clearInterval(this.scanInterval);\n      this.scanInterval = null;\n    }\n    this.isScanning = false;\n  }\n  scanForQrCode() {\n    if (!this.video.nativeElement || !this.canvas.nativeElement) return;\n    const video = this.video.nativeElement;\n    const canvas = this.canvas.nativeElement;\n    const context = canvas.getContext('2d');\n    if (!context || video.readyState !== video.HAVE_ENOUGH_DATA) return;\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    context.drawImage(video, 0, 0, canvas.width, canvas.height);\n    // In a real implementation, you would use a QR code library like @zxing/library\n    // For demo purposes, we'll simulate QR detection\n    this.simulateQrDetection();\n  }\n  simulateQrDetection() {\n    // Simulate random QR code detection for demo\n    if (Math.random() < 0.1) {\n      // 10% chance per scan\n      const randomQr = this.demoQrCodes[Math.floor(Math.random() * this.demoQrCodes.length)];\n      this.processQrCode(randomQr.data);\n    }\n  }\n  processQrCode(qrData) {\n    var _this3 = this;\n    return _asyncToGenerator(function* () {\n      _this3.stopScanning();\n      try {\n        const parsedData = _this3.qrService.parseQrData(qrData);\n        if (!parsedData) {\n          _this3.snackBar.open('Code QR invalide', 'Fermer', {\n            duration: 3000\n          });\n          return;\n        }\n        const result = yield _this3.qrService.handleQrScan(parsedData);\n        _this3.openPointsAssignmentDialog(result, parsedData);\n      } catch (error) {\n        console.error('Error processing QR code:', error);\n        _this3.snackBar.open('Erreur lors du traitement du QR code', 'Fermer', {\n          duration: 3000\n        });\n      }\n    })();\n  }\n  openPointsAssignmentDialog(scanResult, qrData) {\n    const dialogRef = this.dialog.open(PointsAssignmentComponent, {\n      width: '400px',\n      data: {\n        scanResult,\n        qrData,\n        currentUser: this.currentUser\n      }\n    });\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.snackBar.open('Points attribués avec succès!', 'Fermer', {\n          duration: 3000\n        });\n      }\n    });\n  }\n  // Demo methods for testing\n  testQrCode(qrData) {\n    this.processQrCode(qrData);\n  }\n  getDemoButtonColor(index) {\n    const colors = ['primary', 'accent', 'warn', 'primary'];\n    return colors[index % colors.length];\n  }\n  getDemoButtonIcon(label) {\n    if (label.includes('utilisateur')) return 'person';\n    if (label.includes('validateur')) return 'verified';\n    if (label.includes('partenaire')) return 'store';\n    if (label.includes('système')) return 'auto_awesome';\n    return 'qr_code';\n  }\n  getDemoPoints(label) {\n    if (label.includes('1 pt')) return '+1 point';\n    if (label.includes('10 pts')) return '+10 points';\n    if (label.includes('5 pts')) return '+5 points';\n    if (label.includes('3 pts')) return '+3 points';\n    return 'Points variables';\n  }\n  manualQrInput() {\n    var _this4 = this;\n    return _asyncToGenerator(function* () {\n      // In a real app, you might open a dialog for manual input\n      const qrData = prompt('Entrez le code QR manuellement:');\n      if (qrData) {\n        yield _this4.processQrCode(qrData);\n      }\n    })();\n  }\n  static {\n    this.ɵfac = function QrScannerComponent_Factory(t) {\n      return new (t || QrScannerComponent)(i0.ɵɵdirectiveInject(i1.QrService), i0.ɵɵdirectiveInject(i2.UserService), i0.ɵɵdirectiveInject(i3.AuthService), i0.ɵɵdirectiveInject(i4.MatDialog), i0.ɵɵdirectiveInject(i5.MatSnackBar));\n    };\n  }\n  static {\n    this.ɵcmp = /*@__PURE__*/i0.ɵɵdefineComponent({\n      type: QrScannerComponent,\n      selectors: [[\"app-qr-scanner\"]],\n      viewQuery: function QrScannerComponent_Query(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵviewQuery(_c0, 5);\n          i0.ɵɵviewQuery(_c1, 5);\n        }\n        if (rf & 2) {\n          let _t;\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.video = _t.first);\n          i0.ɵɵqueryRefresh(_t = i0.ɵɵloadQuery()) && (ctx.canvas = _t.first);\n        }\n      },\n      decls: 53,\n      vars: 3,\n      consts: [[\"video\", \"\"], [\"canvas\", \"\"], [1, \"scanner-container\"], [1, \"scanner-header\"], [\"class\", \"camera-section\", 4, \"ngIf\"], [\"class\", \"no-camera-section\", 4, \"ngIf\"], [1, \"demo-section\", \"fade-in\"], [1, \"demo-card\", \"floating-card\"], [1, \"demo-buttons\"], [\"mat-raised-button\", \"\", \"class\", \"demo-qr-button\", 3, \"color\", \"animation-delay\", \"click\", 4, \"ngFor\", \"ngForOf\"], [1, \"instructions-section\"], [1, \"instructions-card\"], [1, \"instructions-list\"], [1, \"instruction-item\"], [\"color\", \"primary\"], [1, \"camera-section\"], [1, \"camera-card\"], [1, \"camera-container\"], [\"autoplay\", \"\", \"playsinline\", \"\", 1, \"camera-video\"], [1, \"camera-canvas\", 2, \"display\", \"none\"], [\"class\", \"scanner-overlay\", 4, \"ngIf\"], [1, \"camera-controls\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", \"class\", \"scan-button enhanced-btn\", 3, \"click\", 4, \"ngIf\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", \"class\", \"stop-button enhanced-btn\", 3, \"click\", 4, \"ngIf\"], [\"mat-stroked-button\", \"\", \"color\", \"accent\", 1, \"flash-button\", \"enhanced-btn\", 3, \"click\", \"disabled\"], [\"mat-stroked-button\", \"\", \"color\", \"primary\", 1, \"camera-button\", \"enhanced-btn\", 3, \"click\", \"disabled\"], [1, \"scanner-overlay\"], [1, \"scanner-frame\"], [1, \"scanner-corners\"], [1, \"corner\", \"top-left\"], [1, \"corner\", \"top-right\"], [1, \"corner\", \"bottom-left\"], [1, \"corner\", \"bottom-right\"], [1, \"scanner-instruction\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 1, \"scan-button\", \"enhanced-btn\", 3, \"click\"], [\"mat-raised-button\", \"\", \"color\", \"warn\", 1, \"stop-button\", \"enhanced-btn\", 3, \"click\"], [1, \"no-camera-section\"], [1, \"no-camera-card\"], [1, \"no-camera-content\"], [1, \"no-camera-icon\"], [\"mat-raised-button\", \"\", \"color\", \"primary\", 3, \"click\"], [\"mat-raised-button\", \"\", 1, \"demo-qr-button\", 3, \"click\", \"color\"], [1, \"demo-button-content\"], [1, \"demo-title\"], [1, \"demo-points\"]],\n      template: function QrScannerComponent_Template(rf, ctx) {\n        if (rf & 1) {\n          i0.ɵɵelementStart(0, \"div\", 2)(1, \"div\", 3)(2, \"h1\");\n          i0.ɵɵtext(3, \"Scanner QR Code\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(4, \"p\");\n          i0.ɵɵtext(5, \"Pointez votre cam\\u00E9ra vers un code QR pour gagner des points\");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵtemplate(6, QrScannerComponent_div_6_Template, 20, 9, \"div\", 4)(7, QrScannerComponent_div_7_Template, 14, 0, \"div\", 5);\n          i0.ɵɵelementStart(8, \"div\", 6)(9, \"mat-card\", 7)(10, \"mat-card-header\")(11, \"mat-card-title\")(12, \"mat-icon\");\n          i0.ɵɵtext(13, \"science\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(14, \" \\uD83C\\uDFAF Codes QR de d\\u00E9monstration \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(15, \"mat-card-content\")(16, \"p\");\n          i0.ɵɵtext(17, \"Testez l'application avec ces codes QR de d\\u00E9monstration :\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(18, \"div\", 8);\n          i0.ɵɵtemplate(19, QrScannerComponent_button_19_Template, 8, 6, \"button\", 9);\n          i0.ɵɵelementEnd()()()();\n          i0.ɵɵelementStart(20, \"div\", 10)(21, \"mat-card\", 11)(22, \"mat-card-header\")(23, \"mat-card-title\")(24, \"mat-icon\");\n          i0.ɵɵtext(25, \"help_outline\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵtext(26, \" Comment \\u00E7a marche ? \");\n          i0.ɵɵelementEnd()();\n          i0.ɵɵelementStart(27, \"mat-card-content\")(28, \"div\", 12)(29, \"div\", 13)(30, \"mat-icon\", 14);\n          i0.ɵɵtext(31, \"qr_code_scanner\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(32, \"div\")(33, \"h4\");\n          i0.ɵɵtext(34, \"1. Scanner\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(35, \"p\");\n          i0.ɵɵtext(36, \"Pointez votre cam\\u00E9ra vers un code QR valide\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(37, \"div\", 13)(38, \"mat-icon\", 14);\n          i0.ɵɵtext(39, \"verified\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(40, \"div\")(41, \"h4\");\n          i0.ɵɵtext(42, \"2. Validation\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(43, \"p\");\n          i0.ɵɵtext(44, \"Le code est automatiquement v\\u00E9rifi\\u00E9 et trait\\u00E9\");\n          i0.ɵɵelementEnd()()();\n          i0.ɵɵelementStart(45, \"div\", 13)(46, \"mat-icon\", 14);\n          i0.ɵɵtext(47, \"stars\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(48, \"div\")(49, \"h4\");\n          i0.ɵɵtext(50, \"3. Points\");\n          i0.ɵɵelementEnd();\n          i0.ɵɵelementStart(51, \"p\");\n          i0.ɵɵtext(52, \"Gagnez des points selon l'action effectu\\u00E9e\");\n          i0.ɵɵelementEnd()()()()()()()();\n        }\n        if (rf & 2) {\n          i0.ɵɵadvance(6);\n          i0.ɵɵproperty(\"ngIf\", ctx.hasCamera);\n          i0.ɵɵadvance();\n          i0.ɵɵproperty(\"ngIf\", !ctx.hasCamera);\n          i0.ɵɵadvance(12);\n          i0.ɵɵproperty(\"ngForOf\", ctx.demoQrCodes);\n        }\n      },\n      dependencies: [i6.NgForOf, i6.NgIf, i7.MatCard, i7.MatCardContent, i7.MatCardHeader, i7.MatCardTitle, i8.MatButton, i9.MatIcon],\n      styles: [\".scanner-container[_ngcontent-%COMP%] {\\n  max-width: 800px;\\n  margin: 0 auto;\\n  padding: 16px;\\n}\\n\\n.scanner-header[_ngcontent-%COMP%] {\\n  text-align: center;\\n  margin-bottom: 24px;\\n}\\n\\n.scanner-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n  font-size: 2rem;\\n  font-weight: 600;\\n}\\n\\n.scanner-header[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 1rem;\\n}\\n\\n.camera-section[_ngcontent-%COMP%], .no-camera-section[_ngcontent-%COMP%], .demo-section[_ngcontent-%COMP%], .instructions-section[_ngcontent-%COMP%] {\\n  margin-bottom: 24px;\\n}\\n\\n.camera-card[_ngcontent-%COMP%], .no-camera-card[_ngcontent-%COMP%], .demo-card[_ngcontent-%COMP%], .instructions-card[_ngcontent-%COMP%] {\\n  border-radius: 12px;\\n  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);\\n}\\n\\n.camera-container[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  max-width: 400px;\\n  margin: 0 auto;\\n  background: #000;\\n  border-radius: 8px;\\n  overflow: hidden;\\n}\\n\\n.camera-video[_ngcontent-%COMP%] {\\n  width: 100%;\\n  height: 300px;\\n  object-fit: cover;\\n  display: block;\\n}\\n\\n.camera-video.active[_ngcontent-%COMP%] {\\n  height: 400px;\\n}\\n\\n.scanner-overlay[_ngcontent-%COMP%] {\\n  position: absolute;\\n  top: 0;\\n  left: 0;\\n  right: 0;\\n  bottom: 0;\\n  display: flex;\\n  flex-direction: column;\\n  align-items: center;\\n  justify-content: center;\\n  background: rgba(0, 0, 0, 0.3);\\n}\\n\\n.scanner-frame[_ngcontent-%COMP%] {\\n  width: 200px;\\n  height: 200px;\\n  position: relative;\\n}\\n\\n.scanner-corners[_ngcontent-%COMP%] {\\n  position: relative;\\n  width: 100%;\\n  height: 100%;\\n}\\n\\n.corner[_ngcontent-%COMP%] {\\n  position: absolute;\\n  width: 20px;\\n  height: 20px;\\n  border: 3px solid #fff;\\n}\\n\\n.corner.top-left[_ngcontent-%COMP%] {\\n  top: 0;\\n  left: 0;\\n  border-right: none;\\n  border-bottom: none;\\n}\\n\\n.corner.top-right[_ngcontent-%COMP%] {\\n  top: 0;\\n  right: 0;\\n  border-left: none;\\n  border-bottom: none;\\n}\\n\\n.corner.bottom-left[_ngcontent-%COMP%] {\\n  bottom: 0;\\n  left: 0;\\n  border-right: none;\\n  border-top: none;\\n}\\n\\n.corner.bottom-right[_ngcontent-%COMP%] {\\n  bottom: 0;\\n  right: 0;\\n  border-left: none;\\n  border-top: none;\\n}\\n\\n.scanner-instruction[_ngcontent-%COMP%] {\\n  color: white;\\n  margin-top: 16px;\\n  text-align: center;\\n  font-weight: 500;\\n}\\n\\n.camera-controls[_ngcontent-%COMP%] {\\n  display: flex;\\n  justify-content: center;\\n  margin-top: 16px;\\n}\\n\\n.scan-button[_ngcontent-%COMP%], .stop-button[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: center;\\n  gap: 8px;\\n  padding: 12px 24px;\\n  font-size: 1rem;\\n}\\n\\n.no-camera-content[_ngcontent-%COMP%] {\\n  text-align: center;\\n  padding: 32px;\\n}\\n\\n.no-camera-icon[_ngcontent-%COMP%] {\\n  font-size: 4rem;\\n  color: #ccc;\\n  margin-bottom: 16px;\\n}\\n\\n.no-camera-content[_ngcontent-%COMP%]   h3[_ngcontent-%COMP%] {\\n  margin: 0 0 8px 0;\\n  color: #333;\\n}\\n\\n.no-camera-content[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0 0 24px 0;\\n  color: #666;\\n}\\n\\n.demo-buttons[_ngcontent-%COMP%] {\\n  display: grid;\\n  grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));\\n  gap: 16px;\\n  margin-top: 24px;\\n}\\n\\n.demo-qr-button[_ngcontent-%COMP%] {\\n  height: 64px;\\n  display: flex;\\n  align-items: center;\\n  justify-content: flex-start;\\n  gap: 12px;\\n  padding: 0 20px;\\n  border-radius: 16px;\\n  font-weight: 500;\\n  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);\\n  animation: slideUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;\\n  text-align: left;\\n}\\n\\n.demo-qr-button[_ngcontent-%COMP%]:hover {\\n  transform: translateY(-2px);\\n  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);\\n}\\n\\n.demo-button-content[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  align-items: flex-start;\\n  text-align: left;\\n}\\n\\n.demo-title[_ngcontent-%COMP%] {\\n  font-size: 1rem;\\n  font-weight: 600;\\n  margin-bottom: 2px;\\n}\\n\\n.demo-points[_ngcontent-%COMP%] {\\n  font-size: 0.8rem;\\n  opacity: 0.8;\\n  font-weight: 400;\\n}\\n\\n.instructions-list[_ngcontent-%COMP%] {\\n  display: flex;\\n  flex-direction: column;\\n  gap: 16px;\\n  margin-top: 16px;\\n}\\n\\n.instruction-item[_ngcontent-%COMP%] {\\n  display: flex;\\n  align-items: flex-start;\\n  gap: 16px;\\n}\\n\\n.instruction-item[_ngcontent-%COMP%]   mat-icon[_ngcontent-%COMP%] {\\n  margin-top: 4px;\\n  flex-shrink: 0;\\n}\\n\\n.instruction-item[_ngcontent-%COMP%]   h4[_ngcontent-%COMP%] {\\n  margin: 0 0 4px 0;\\n  color: #333;\\n  font-size: 1.1rem;\\n}\\n\\n.instruction-item[_ngcontent-%COMP%]   p[_ngcontent-%COMP%] {\\n  margin: 0;\\n  color: #666;\\n  font-size: 0.9rem;\\n}\\n\\n\\n\\n@media (max-width: 768px) {\\n  .scanner-container[_ngcontent-%COMP%] {\\n    padding: 8px;\\n  }\\n  \\n  .scanner-header[_ngcontent-%COMP%]   h1[_ngcontent-%COMP%] {\\n    font-size: 1.5rem;\\n  }\\n  \\n  .camera-video[_ngcontent-%COMP%] {\\n    height: 250px;\\n  }\\n  \\n  .camera-video.active[_ngcontent-%COMP%] {\\n    height: 300px;\\n  }\\n  \\n  .scanner-frame[_ngcontent-%COMP%] {\\n    width: 150px;\\n    height: 150px;\\n  }\\n  \\n  .demo-buttons[_ngcontent-%COMP%] {\\n    gap: 6px;\\n  }\\n  \\n  .demo-qr-button[_ngcontent-%COMP%] {\\n    padding: 8px 12px;\\n    font-size: 0.9rem;\\n  }\\n}\\n\\n/*# sourceMappingURL=data:application/json;base64,eyJ2ZXJzaW9uIjozLCJzb3VyY2VzIjpbInFyLXNjYW5uZXIuY29tcG9uZW50LmNzcyJdLCJuYW1lcyI6W10sIm1hcHBpbmdzIjoiQUFBQTtFQUNFLGdCQUFnQjtFQUNoQixjQUFjO0VBQ2QsYUFBYTtBQUNmOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLGlCQUFpQjtFQUNqQixXQUFXO0VBQ1gsZUFBZTtFQUNmLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLFNBQVM7RUFDVCxXQUFXO0VBQ1gsZUFBZTtBQUNqQjs7QUFFQTs7OztFQUlFLG1CQUFtQjtBQUNyQjs7QUFFQTs7OztFQUlFLG1CQUFtQjtFQUNuQix3Q0FBd0M7QUFDMUM7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsV0FBVztFQUNYLGdCQUFnQjtFQUNoQixjQUFjO0VBQ2QsZ0JBQWdCO0VBQ2hCLGtCQUFrQjtFQUNsQixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxXQUFXO0VBQ1gsYUFBYTtFQUNiLGlCQUFpQjtFQUNqQixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsYUFBYTtBQUNmOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLE1BQU07RUFDTixPQUFPO0VBQ1AsUUFBUTtFQUNSLFNBQVM7RUFDVCxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLG1CQUFtQjtFQUNuQix1QkFBdUI7RUFDdkIsOEJBQThCO0FBQ2hDOztBQUVBO0VBQ0UsWUFBWTtFQUNaLGFBQWE7RUFDYixrQkFBa0I7QUFDcEI7O0FBRUE7RUFDRSxrQkFBa0I7RUFDbEIsV0FBVztFQUNYLFlBQVk7QUFDZDs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixXQUFXO0VBQ1gsWUFBWTtFQUNaLHNCQUFzQjtBQUN4Qjs7QUFFQTtFQUNFLE1BQU07RUFDTixPQUFPO0VBQ1Asa0JBQWtCO0VBQ2xCLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLE1BQU07RUFDTixRQUFRO0VBQ1IsaUJBQWlCO0VBQ2pCLG1CQUFtQjtBQUNyQjs7QUFFQTtFQUNFLFNBQVM7RUFDVCxPQUFPO0VBQ1Asa0JBQWtCO0VBQ2xCLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLFNBQVM7RUFDVCxRQUFRO0VBQ1IsaUJBQWlCO0VBQ2pCLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLFlBQVk7RUFDWixnQkFBZ0I7RUFDaEIsa0JBQWtCO0VBQ2xCLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGFBQWE7RUFDYix1QkFBdUI7RUFDdkIsZ0JBQWdCO0FBQ2xCOztBQUVBOztFQUVFLGFBQWE7RUFDYixtQkFBbUI7RUFDbkIsUUFBUTtFQUNSLGtCQUFrQjtFQUNsQixlQUFlO0FBQ2pCOztBQUVBO0VBQ0Usa0JBQWtCO0VBQ2xCLGFBQWE7QUFDZjs7QUFFQTtFQUNFLGVBQWU7RUFDZixXQUFXO0VBQ1gsbUJBQW1CO0FBQ3JCOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLFdBQVc7QUFDYjs7QUFFQTtFQUNFLGtCQUFrQjtFQUNsQixXQUFXO0FBQ2I7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsMkRBQTJEO0VBQzNELFNBQVM7RUFDVCxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxZQUFZO0VBQ1osYUFBYTtFQUNiLG1CQUFtQjtFQUNuQiwyQkFBMkI7RUFDM0IsU0FBUztFQUNULGVBQWU7RUFDZixtQkFBbUI7RUFDbkIsZ0JBQWdCO0VBQ2hCLGlEQUFpRDtFQUNqRCx5REFBeUQ7RUFDekQsZ0JBQWdCO0FBQ2xCOztBQUVBO0VBQ0UsMkJBQTJCO0VBQzNCLDBDQUEwQztBQUM1Qzs7QUFFQTtFQUNFLGFBQWE7RUFDYixzQkFBc0I7RUFDdEIsdUJBQXVCO0VBQ3ZCLGdCQUFnQjtBQUNsQjs7QUFFQTtFQUNFLGVBQWU7RUFDZixnQkFBZ0I7RUFDaEIsa0JBQWtCO0FBQ3BCOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLFlBQVk7RUFDWixnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2Isc0JBQXNCO0VBQ3RCLFNBQVM7RUFDVCxnQkFBZ0I7QUFDbEI7O0FBRUE7RUFDRSxhQUFhO0VBQ2IsdUJBQXVCO0VBQ3ZCLFNBQVM7QUFDWDs7QUFFQTtFQUNFLGVBQWU7RUFDZixjQUFjO0FBQ2hCOztBQUVBO0VBQ0UsaUJBQWlCO0VBQ2pCLFdBQVc7RUFDWCxpQkFBaUI7QUFDbkI7O0FBRUE7RUFDRSxTQUFTO0VBQ1QsV0FBVztFQUNYLGlCQUFpQjtBQUNuQjs7QUFFQSxzQkFBc0I7QUFDdEI7RUFDRTtJQUNFLFlBQVk7RUFDZDs7RUFFQTtJQUNFLGlCQUFpQjtFQUNuQjs7RUFFQTtJQUNFLGFBQWE7RUFDZjs7RUFFQTtJQUNFLGFBQWE7RUFDZjs7RUFFQTtJQUNFLFlBQVk7SUFDWixhQUFhO0VBQ2Y7O0VBRUE7SUFDRSxRQUFRO0VBQ1Y7O0VBRUE7SUFDRSxpQkFBaUI7SUFDakIsaUJBQWlCO0VBQ25CO0FBQ0YiLCJmaWxlIjoicXItc2Nhbm5lci5jb21wb25lbnQuY3NzIiwic291cmNlc0NvbnRlbnQiOlsiLnNjYW5uZXItY29udGFpbmVyIHtcbiAgbWF4LXdpZHRoOiA4MDBweDtcbiAgbWFyZ2luOiAwIGF1dG87XG4gIHBhZGRpbmc6IDE2cHg7XG59XG5cbi5zY2FubmVyLWhlYWRlciB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgbWFyZ2luLWJvdHRvbTogMjRweDtcbn1cblxuLnNjYW5uZXItaGVhZGVyIGgxIHtcbiAgbWFyZ2luOiAwIDAgOHB4IDA7XG4gIGNvbG9yOiAjMzMzO1xuICBmb250LXNpemU6IDJyZW07XG4gIGZvbnQtd2VpZ2h0OiA2MDA7XG59XG5cbi5zY2FubmVyLWhlYWRlciBwIHtcbiAgbWFyZ2luOiAwO1xuICBjb2xvcjogIzY2NjtcbiAgZm9udC1zaXplOiAxcmVtO1xufVxuXG4uY2FtZXJhLXNlY3Rpb24sXG4ubm8tY2FtZXJhLXNlY3Rpb24sXG4uZGVtby1zZWN0aW9uLFxuLmluc3RydWN0aW9ucy1zZWN0aW9uIHtcbiAgbWFyZ2luLWJvdHRvbTogMjRweDtcbn1cblxuLmNhbWVyYS1jYXJkLFxuLm5vLWNhbWVyYS1jYXJkLFxuLmRlbW8tY2FyZCxcbi5pbnN0cnVjdGlvbnMtY2FyZCB7XG4gIGJvcmRlci1yYWRpdXM6IDEycHg7XG4gIGJveC1zaGFkb3c6IDAgMnB4IDhweCByZ2JhKDAsIDAsIDAsIDAuMSk7XG59XG5cbi5jYW1lcmEtY29udGFpbmVyIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB3aWR0aDogMTAwJTtcbiAgbWF4LXdpZHRoOiA0MDBweDtcbiAgbWFyZ2luOiAwIGF1dG87XG4gIGJhY2tncm91bmQ6ICMwMDA7XG4gIGJvcmRlci1yYWRpdXM6IDhweDtcbiAgb3ZlcmZsb3c6IGhpZGRlbjtcbn1cblxuLmNhbWVyYS12aWRlbyB7XG4gIHdpZHRoOiAxMDAlO1xuICBoZWlnaHQ6IDMwMHB4O1xuICBvYmplY3QtZml0OiBjb3ZlcjtcbiAgZGlzcGxheTogYmxvY2s7XG59XG5cbi5jYW1lcmEtdmlkZW8uYWN0aXZlIHtcbiAgaGVpZ2h0OiA0MDBweDtcbn1cblxuLnNjYW5uZXItb3ZlcmxheSB7XG4gIHBvc2l0aW9uOiBhYnNvbHV0ZTtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICByaWdodDogMDtcbiAgYm90dG9tOiAwO1xuICBkaXNwbGF5OiBmbGV4O1xuICBmbGV4LWRpcmVjdGlvbjogY29sdW1uO1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBqdXN0aWZ5LWNvbnRlbnQ6IGNlbnRlcjtcbiAgYmFja2dyb3VuZDogcmdiYSgwLCAwLCAwLCAwLjMpO1xufVxuXG4uc2Nhbm5lci1mcmFtZSB7XG4gIHdpZHRoOiAyMDBweDtcbiAgaGVpZ2h0OiAyMDBweDtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xufVxuXG4uc2Nhbm5lci1jb3JuZXJzIHtcbiAgcG9zaXRpb246IHJlbGF0aXZlO1xuICB3aWR0aDogMTAwJTtcbiAgaGVpZ2h0OiAxMDAlO1xufVxuXG4uY29ybmVyIHtcbiAgcG9zaXRpb246IGFic29sdXRlO1xuICB3aWR0aDogMjBweDtcbiAgaGVpZ2h0OiAyMHB4O1xuICBib3JkZXI6IDNweCBzb2xpZCAjZmZmO1xufVxuXG4uY29ybmVyLnRvcC1sZWZ0IHtcbiAgdG9wOiAwO1xuICBsZWZ0OiAwO1xuICBib3JkZXItcmlnaHQ6IG5vbmU7XG4gIGJvcmRlci1ib3R0b206IG5vbmU7XG59XG5cbi5jb3JuZXIudG9wLXJpZ2h0IHtcbiAgdG9wOiAwO1xuICByaWdodDogMDtcbiAgYm9yZGVyLWxlZnQ6IG5vbmU7XG4gIGJvcmRlci1ib3R0b206IG5vbmU7XG59XG5cbi5jb3JuZXIuYm90dG9tLWxlZnQge1xuICBib3R0b206IDA7XG4gIGxlZnQ6IDA7XG4gIGJvcmRlci1yaWdodDogbm9uZTtcbiAgYm9yZGVyLXRvcDogbm9uZTtcbn1cblxuLmNvcm5lci5ib3R0b20tcmlnaHQge1xuICBib3R0b206IDA7XG4gIHJpZ2h0OiAwO1xuICBib3JkZXItbGVmdDogbm9uZTtcbiAgYm9yZGVyLXRvcDogbm9uZTtcbn1cblxuLnNjYW5uZXItaW5zdHJ1Y3Rpb24ge1xuICBjb2xvcjogd2hpdGU7XG4gIG1hcmdpbi10b3A6IDE2cHg7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgZm9udC13ZWlnaHQ6IDUwMDtcbn1cblxuLmNhbWVyYS1jb250cm9scyB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGp1c3RpZnktY29udGVudDogY2VudGVyO1xuICBtYXJnaW4tdG9wOiAxNnB4O1xufVxuXG4uc2Nhbi1idXR0b24sXG4uc3RvcC1idXR0b24ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogY2VudGVyO1xuICBnYXA6IDhweDtcbiAgcGFkZGluZzogMTJweCAyNHB4O1xuICBmb250LXNpemU6IDFyZW07XG59XG5cbi5uby1jYW1lcmEtY29udGVudCB7XG4gIHRleHQtYWxpZ246IGNlbnRlcjtcbiAgcGFkZGluZzogMzJweDtcbn1cblxuLm5vLWNhbWVyYS1pY29uIHtcbiAgZm9udC1zaXplOiA0cmVtO1xuICBjb2xvcjogI2NjYztcbiAgbWFyZ2luLWJvdHRvbTogMTZweDtcbn1cblxuLm5vLWNhbWVyYS1jb250ZW50IGgzIHtcbiAgbWFyZ2luOiAwIDAgOHB4IDA7XG4gIGNvbG9yOiAjMzMzO1xufVxuXG4ubm8tY2FtZXJhLWNvbnRlbnQgcCB7XG4gIG1hcmdpbjogMCAwIDI0cHggMDtcbiAgY29sb3I6ICM2NjY7XG59XG5cbi5kZW1vLWJ1dHRvbnMge1xuICBkaXNwbGF5OiBncmlkO1xuICBncmlkLXRlbXBsYXRlLWNvbHVtbnM6IHJlcGVhdChhdXRvLWZpdCwgbWlubWF4KDI1MHB4LCAxZnIpKTtcbiAgZ2FwOiAxNnB4O1xuICBtYXJnaW4tdG9wOiAyNHB4O1xufVxuXG4uZGVtby1xci1idXR0b24ge1xuICBoZWlnaHQ6IDY0cHg7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGFsaWduLWl0ZW1zOiBjZW50ZXI7XG4gIGp1c3RpZnktY29udGVudDogZmxleC1zdGFydDtcbiAgZ2FwOiAxMnB4O1xuICBwYWRkaW5nOiAwIDIwcHg7XG4gIGJvcmRlci1yYWRpdXM6IDE2cHg7XG4gIGZvbnQtd2VpZ2h0OiA1MDA7XG4gIHRyYW5zaXRpb246IGFsbCAwLjNzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSk7XG4gIGFuaW1hdGlvbjogc2xpZGVVcCAwLjZzIGN1YmljLWJlemllcigwLjQsIDAsIDAuMiwgMSkgYm90aDtcbiAgdGV4dC1hbGlnbjogbGVmdDtcbn1cblxuLmRlbW8tcXItYnV0dG9uOmhvdmVyIHtcbiAgdHJhbnNmb3JtOiB0cmFuc2xhdGVZKC0ycHgpO1xuICBib3gtc2hhZG93OiAwIDhweCAyNXB4IHJnYmEoMCwgMCwgMCwgMC4xNSk7XG59XG5cbi5kZW1vLWJ1dHRvbi1jb250ZW50IHtcbiAgZGlzcGxheTogZmxleDtcbiAgZmxleC1kaXJlY3Rpb246IGNvbHVtbjtcbiAgYWxpZ24taXRlbXM6IGZsZXgtc3RhcnQ7XG4gIHRleHQtYWxpZ246IGxlZnQ7XG59XG5cbi5kZW1vLXRpdGxlIHtcbiAgZm9udC1zaXplOiAxcmVtO1xuICBmb250LXdlaWdodDogNjAwO1xuICBtYXJnaW4tYm90dG9tOiAycHg7XG59XG5cbi5kZW1vLXBvaW50cyB7XG4gIGZvbnQtc2l6ZTogMC44cmVtO1xuICBvcGFjaXR5OiAwLjg7XG4gIGZvbnQtd2VpZ2h0OiA0MDA7XG59XG5cbi5pbnN0cnVjdGlvbnMtbGlzdCB7XG4gIGRpc3BsYXk6IGZsZXg7XG4gIGZsZXgtZGlyZWN0aW9uOiBjb2x1bW47XG4gIGdhcDogMTZweDtcbiAgbWFyZ2luLXRvcDogMTZweDtcbn1cblxuLmluc3RydWN0aW9uLWl0ZW0ge1xuICBkaXNwbGF5OiBmbGV4O1xuICBhbGlnbi1pdGVtczogZmxleC1zdGFydDtcbiAgZ2FwOiAxNnB4O1xufVxuXG4uaW5zdHJ1Y3Rpb24taXRlbSBtYXQtaWNvbiB7XG4gIG1hcmdpbi10b3A6IDRweDtcbiAgZmxleC1zaHJpbms6IDA7XG59XG5cbi5pbnN0cnVjdGlvbi1pdGVtIGg0IHtcbiAgbWFyZ2luOiAwIDAgNHB4IDA7XG4gIGNvbG9yOiAjMzMzO1xuICBmb250LXNpemU6IDEuMXJlbTtcbn1cblxuLmluc3RydWN0aW9uLWl0ZW0gcCB7XG4gIG1hcmdpbjogMDtcbiAgY29sb3I6ICM2NjY7XG4gIGZvbnQtc2l6ZTogMC45cmVtO1xufVxuXG4vKiBNb2JpbGUgcmVzcG9uc2l2ZSAqL1xuQG1lZGlhIChtYXgtd2lkdGg6IDc2OHB4KSB7XG4gIC5zY2FubmVyLWNvbnRhaW5lciB7XG4gICAgcGFkZGluZzogOHB4O1xuICB9XG4gIFxuICAuc2Nhbm5lci1oZWFkZXIgaDEge1xuICAgIGZvbnQtc2l6ZTogMS41cmVtO1xuICB9XG4gIFxuICAuY2FtZXJhLXZpZGVvIHtcbiAgICBoZWlnaHQ6IDI1MHB4O1xuICB9XG4gIFxuICAuY2FtZXJhLXZpZGVvLmFjdGl2ZSB7XG4gICAgaGVpZ2h0OiAzMDBweDtcbiAgfVxuICBcbiAgLnNjYW5uZXItZnJhbWUge1xuICAgIHdpZHRoOiAxNTBweDtcbiAgICBoZWlnaHQ6IDE1MHB4O1xuICB9XG4gIFxuICAuZGVtby1idXR0b25zIHtcbiAgICBnYXA6IDZweDtcbiAgfVxuICBcbiAgLmRlbW8tcXItYnV0dG9uIHtcbiAgICBwYWRkaW5nOiA4cHggMTJweDtcbiAgICBmb250LXNpemU6IDAuOXJlbTtcbiAgfVxufVxuIl19 */\\n/*# sourceMappingURL=data:application/json;charset=utf-8;base64,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 */\"]\n    });\n  }\n}", "map": {"version": 3, "names": ["PointsAssignmentComponent", "i0", "ɵɵelementStart", "ɵɵelement", "ɵɵelementEnd", "ɵɵtext", "ɵɵlistener", "QrScannerComponent_div_6_button_10_Template_button_click_0_listener", "ɵɵrestoreView", "_r2", "ctx_r2", "ɵɵnextContext", "ɵɵresetView", "startScanning", "QrScannerComponent_div_6_button_11_Template_button_click_0_listener", "_r4", "stopScanning", "ɵɵtemplate", "QrScannerComponent_div_6_div_8_Template", "QrScannerComponent_div_6_button_10_Template", "QrScannerComponent_div_6_button_11_Template", "QrScannerComponent_div_6_Template_button_click_12_listener", "_r1", "toggleFlashlight", "QrScannerComponent_div_6_Template_button_click_16_listener", "switchCamera", "ɵɵadvance", "ɵɵclassProp", "isScanning", "ɵɵproperty", "ɵɵtextInterpolate", "flashlightOn", "ɵɵtextInterpolate1", "QrScannerComponent_div_7_Template_button_click_10_listener", "_r5", "manualQrInput", "QrScannerComponent_button_19_Template_button_click_0_listener", "demo_r7", "_r6", "$implicit", "testQrCode", "data", "ɵɵstyleProp", "i_r8", "getDemoButtonColor", "getDemoButtonIcon", "label", "getDemoPoints", "QrScannerComponent", "constructor", "qrService", "userService", "authService", "dialog", "snackBar", "hasCamera", "currentUser", "stream", "demoQrCodes", "Date", "now", "ngOnInit", "currentUser$", "subscribe", "user", "checkCameraSupport", "ngOnDestroy", "_this", "_asyncToGenerator", "devices", "navigator", "mediaDevices", "enumerateDevices", "some", "device", "kind", "error", "console", "_this2", "open", "duration", "getUserMedia", "video", "facingMode", "nativeElement", "srcObject", "play", "scanInterval", "setInterval", "scanForQrCode", "getTracks", "for<PERSON>ach", "track", "stop", "clearInterval", "canvas", "context", "getContext", "readyState", "HAVE_ENOUGH_DATA", "width", "videoWidth", "height", "videoHeight", "drawImage", "simulateQrDetection", "Math", "random", "randomQr", "floor", "length", "processQrCode", "qrData", "_this3", "parsedData", "parseQrData", "result", "handleQrScan", "openPointsAssignmentDialog", "scanResult", "dialogRef", "afterClosed", "index", "colors", "includes", "_this4", "prompt", "ɵɵdirectiveInject", "i1", "QrService", "i2", "UserService", "i3", "AuthService", "i4", "MatDialog", "i5", "MatSnackBar", "selectors", "viewQuery", "QrScannerComponent_Query", "rf", "ctx", "QrScannerComponent_div_6_Template", "QrScannerComponent_div_7_Template", "QrScannerComponent_button_19_Template"], "sources": ["C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\qr-scanner\\components\\qr-scanner\\qr-scanner.component.ts", "C:\\Users\\<USER>\\Documents\\augment-projects\\modjo\\modjo\\src\\app\\features\\qr-scanner\\components\\qr-scanner\\qr-scanner.component.html"], "sourcesContent": ["import { <PERSON>mpo<PERSON>, <PERSON><PERSON>nit, <PERSON><PERSON><PERSON>roy, ViewChild, ElementRef } from '@angular/core';\nimport { MatDialog } from '@angular/material/dialog';\nimport { MatSnackBar } from '@angular/material/snack-bar';\nimport { QrService } from '../../../../core/services/qr.service';\nimport { UserService } from '../../../../core/services/user.service';\nimport { AuthService } from '../../../../core/services/auth.service';\nimport { PointsAssignmentComponent } from '../points-assignment/points-assignment.component';\nimport { QrCodeData, User } from '../../../../core/models';\n\n@Component({\n  selector: 'app-qr-scanner',\n  templateUrl: './qr-scanner.component.html',\n  styleUrls: ['./qr-scanner.component.css']\n})\nexport class QrScannerComponent implements OnInit, On<PERSON><PERSON>roy {\n  @ViewChild('video', { static: false }) video!: ElementRef<HTMLVideoElement>;\n  @ViewChild('canvas', { static: false }) canvas!: ElementRef<HTMLCanvasElement>;\n\n  isScanning = false;\n  hasCamera = false;\n  currentUser: User | null = null;\n  stream: MediaStream | null = null;\n  scanInterval: any;\n\n  // Demo QR codes for testing\n  demoQrCodes = [\n    { label: 'Transfert utilisateur (1 pt)', data: 'user:demo123' },\n    { label: 'Action validateur (10 pts)', data: 'validator:val456:community_service:10' },\n    { label: 'Récompense partenaire (5 pts)', data: '{\"type\":\"partner_reward\",\"points\":5,\"timestamp\":' + Date.now() + '}' },\n    { label: 'Bonus système (3 pts)', data: '{\"type\":\"system_bonus\",\"points\":3,\"timestamp\":' + Date.now() + '}' }\n  ];\n\n  constructor(\n    private qrService: QrService,\n    private userService: UserService,\n    private authService: AuthService,\n    private dialog: MatDialog,\n    private snackBar: MatSnackBar\n  ) {}\n\n  ngOnInit(): void {\n    this.authService.currentUser$.subscribe(user => {\n      this.currentUser = user;\n    });\n    this.checkCameraSupport();\n  }\n\n  ngOnDestroy(): void {\n    this.stopScanning();\n  }\n\n  async checkCameraSupport(): Promise<void> {\n    try {\n      const devices = await navigator.mediaDevices.enumerateDevices();\n      this.hasCamera = devices.some(device => device.kind === 'videoinput');\n    } catch (error) {\n      console.error('Error checking camera support:', error);\n      this.hasCamera = false;\n    }\n  }\n\n  async startScanning(): Promise<void> {\n    if (!this.hasCamera) {\n      this.snackBar.open('Caméra non disponible', 'Fermer', { duration: 3000 });\n      return;\n    }\n\n    try {\n      this.stream = await navigator.mediaDevices.getUserMedia({\n        video: { facingMode: 'environment' }\n      });\n      \n      this.video.nativeElement.srcObject = this.stream;\n      this.video.nativeElement.play();\n      this.isScanning = true;\n\n      // Start scanning for QR codes\n      this.scanInterval = setInterval(() => {\n        this.scanForQrCode();\n      }, 500);\n\n    } catch (error) {\n      console.error('Error starting camera:', error);\n      this.snackBar.open('Erreur d\\'accès à la caméra', 'Fermer', { duration: 3000 });\n    }\n  }\n\n  stopScanning(): void {\n    if (this.stream) {\n      this.stream.getTracks().forEach(track => track.stop());\n      this.stream = null;\n    }\n    \n    if (this.scanInterval) {\n      clearInterval(this.scanInterval);\n      this.scanInterval = null;\n    }\n    \n    this.isScanning = false;\n  }\n\n  private scanForQrCode(): void {\n    if (!this.video.nativeElement || !this.canvas.nativeElement) return;\n\n    const video = this.video.nativeElement;\n    const canvas = this.canvas.nativeElement;\n    const context = canvas.getContext('2d');\n\n    if (!context || video.readyState !== video.HAVE_ENOUGH_DATA) return;\n\n    canvas.width = video.videoWidth;\n    canvas.height = video.videoHeight;\n    context.drawImage(video, 0, 0, canvas.width, canvas.height);\n\n    // In a real implementation, you would use a QR code library like @zxing/library\n    // For demo purposes, we'll simulate QR detection\n    this.simulateQrDetection();\n  }\n\n  private simulateQrDetection(): void {\n    // Simulate random QR code detection for demo\n    if (Math.random() < 0.1) { // 10% chance per scan\n      const randomQr = this.demoQrCodes[Math.floor(Math.random() * this.demoQrCodes.length)];\n      this.processQrCode(randomQr.data);\n    }\n  }\n\n  async processQrCode(qrData: string): Promise<void> {\n    this.stopScanning();\n\n    try {\n      const parsedData = this.qrService.parseQrData(qrData);\n      if (!parsedData) {\n        this.snackBar.open('Code QR invalide', 'Fermer', { duration: 3000 });\n        return;\n      }\n\n      const result = await this.qrService.handleQrScan(parsedData);\n      this.openPointsAssignmentDialog(result, parsedData);\n\n    } catch (error) {\n      console.error('Error processing QR code:', error);\n      this.snackBar.open('Erreur lors du traitement du QR code', 'Fermer', { duration: 3000 });\n    }\n  }\n\n  private openPointsAssignmentDialog(scanResult: any, qrData: QrCodeData): void {\n    const dialogRef = this.dialog.open(PointsAssignmentComponent, {\n      width: '400px',\n      data: {\n        scanResult,\n        qrData,\n        currentUser: this.currentUser\n      }\n    });\n\n    dialogRef.afterClosed().subscribe(result => {\n      if (result) {\n        this.snackBar.open('Points attribués avec succès!', 'Fermer', { duration: 3000 });\n      }\n    });\n  }\n\n  // Demo methods for testing\n  testQrCode(qrData: string): void {\n    this.processQrCode(qrData);\n  }\n\n  getDemoButtonColor(index: number): string {\n    const colors = ['primary', 'accent', 'warn', 'primary'];\n    return colors[index % colors.length];\n  }\n\n  getDemoButtonIcon(label: string): string {\n    if (label.includes('utilisateur')) return 'person';\n    if (label.includes('validateur')) return 'verified';\n    if (label.includes('partenaire')) return 'store';\n    if (label.includes('système')) return 'auto_awesome';\n    return 'qr_code';\n  }\n\n  getDemoPoints(label: string): string {\n    if (label.includes('1 pt')) return '+1 point';\n    if (label.includes('10 pts')) return '+10 points';\n    if (label.includes('5 pts')) return '+5 points';\n    if (label.includes('3 pts')) return '+3 points';\n    return 'Points variables';\n  }\n\n  async manualQrInput(): Promise<void> {\n    // In a real app, you might open a dialog for manual input\n    const qrData = prompt('Entrez le code QR manuellement:');\n    if (qrData) {\n      await this.processQrCode(qrData);\n    }\n  }\n}\n", "<div class=\"scanner-container\">\n  <!-- Scanner header -->\n  <div class=\"scanner-header\">\n    <h1>Scanner QR Code</h1>\n    <p>Pointez votre caméra vers un code QR pour gagner des points</p>\n  </div>\n\n  <!-- Camera section -->\n  <div class=\"camera-section\" *ngIf=\"hasCamera\">\n    <mat-card class=\"camera-card\">\n      <mat-card-content>\n        <div class=\"camera-container\">\n          <video #video \n                 class=\"camera-video\" \n                 [class.active]=\"isScanning\"\n                 autoplay \n                 playsinline>\n          </video>\n          <canvas #canvas class=\"camera-canvas\" style=\"display: none;\"></canvas>\n          \n          <!-- Scanner overlay -->\n          <div class=\"scanner-overlay\" *ngIf=\"isScanning\">\n            <div class=\"scanner-frame\">\n              <div class=\"scanner-corners\">\n                <div class=\"corner top-left\"></div>\n                <div class=\"corner top-right\"></div>\n                <div class=\"corner bottom-left\"></div>\n                <div class=\"corner bottom-right\"></div>\n              </div>\n            </div>\n            <p class=\"scanner-instruction\">Placez le QR code dans le cadre</p>\n          </div>\n        </div>\n\n        <!-- Camera controls -->\n        <div class=\"camera-controls\">\n          <button mat-raised-button\n                  color=\"primary\"\n                  (click)=\"startScanning()\"\n                  *ngIf=\"!isScanning\"\n                  class=\"scan-button enhanced-btn\">\n            <mat-icon>qr_code_scanner</mat-icon>\n            Commencer le scan\n          </button>\n\n          <button mat-raised-button\n                  color=\"warn\"\n                  (click)=\"stopScanning()\"\n                  *ngIf=\"isScanning\"\n                  class=\"stop-button enhanced-btn\">\n            <mat-icon>stop</mat-icon>\n            Arrêter le scan\n          </button>\n\n          <button mat-stroked-button\n                  color=\"accent\"\n                  (click)=\"toggleFlashlight()\"\n                  [disabled]=\"!isScanning\"\n                  class=\"flash-button enhanced-btn\">\n            <mat-icon>{{ flashlightOn ? 'flash_off' : 'flash_on' }}</mat-icon>\n            {{ flashlightOn ? 'Flash Off' : 'Flash On' }}\n          </button>\n\n          <button mat-stroked-button\n                  color=\"primary\"\n                  (click)=\"switchCamera()\"\n                  [disabled]=\"!isScanning\"\n                  class=\"camera-button enhanced-btn\">\n            <mat-icon>flip_camera_android</mat-icon>\n            Changer caméra\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- No camera fallback -->\n  <div class=\"no-camera-section\" *ngIf=\"!hasCamera\">\n    <mat-card class=\"no-camera-card\">\n      <mat-card-content>\n        <div class=\"no-camera-content\">\n          <mat-icon class=\"no-camera-icon\">camera_alt</mat-icon>\n          <h3>Caméra non disponible</h3>\n          <p>Votre appareil ne dispose pas de caméra ou l'accès a été refusé.</p>\n          <button mat-raised-button color=\"primary\" (click)=\"manualQrInput()\">\n            <mat-icon>keyboard</mat-icon>\n            Saisie manuelle\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Demo section -->\n  <div class=\"demo-section fade-in\">\n    <mat-card class=\"demo-card floating-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>science</mat-icon>\n          🎯 Codes QR de démonstration\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <p>Testez l'application avec ces codes QR de démonstration :</p>\n        <div class=\"demo-buttons\">\n          <button mat-raised-button\n                  *ngFor=\"let demo of demoQrCodes; let i = index\"\n                  (click)=\"testQrCode(demo.data)\"\n                  [color]=\"getDemoButtonColor(i)\"\n                  class=\"demo-qr-button\"\n                  [style.animation-delay]=\"(i * 0.1) + 's'\">\n            <mat-icon>{{ getDemoButtonIcon(demo.label) }}</mat-icon>\n            <div class=\"demo-button-content\">\n              <span class=\"demo-title\">{{ demo.label }}</span>\n              <small class=\"demo-points\">{{ getDemoPoints(demo.label) }}</small>\n            </div>\n          </button>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n\n  <!-- Instructions -->\n  <div class=\"instructions-section\">\n    <mat-card class=\"instructions-card\">\n      <mat-card-header>\n        <mat-card-title>\n          <mat-icon>help_outline</mat-icon>\n          Comment ça marche ?\n        </mat-card-title>\n      </mat-card-header>\n      <mat-card-content>\n        <div class=\"instructions-list\">\n          <div class=\"instruction-item\">\n            <mat-icon color=\"primary\">qr_code_scanner</mat-icon>\n            <div>\n              <h4>1. Scanner</h4>\n              <p>Pointez votre caméra vers un code QR valide</p>\n            </div>\n          </div>\n          \n          <div class=\"instruction-item\">\n            <mat-icon color=\"primary\">verified</mat-icon>\n            <div>\n              <h4>2. Validation</h4>\n              <p>Le code est automatiquement vérifié et traité</p>\n            </div>\n          </div>\n          \n          <div class=\"instruction-item\">\n            <mat-icon color=\"primary\">stars</mat-icon>\n            <div>\n              <h4>3. Points</h4>\n              <p>Gagnez des points selon l'action effectuée</p>\n            </div>\n          </div>\n        </div>\n      </mat-card-content>\n    </mat-card>\n  </div>\n</div>\n"], "mappings": ";AAMA,SAASA,yBAAyB,QAAQ,kDAAkD;;;;;;;;;;;;;;;ICiB9EC,EAFJ,CAAAC,cAAA,cAAgD,cACnB,cACI;IAI3BD,EAHA,CAAAE,SAAA,cAAmC,cACC,cACE,cACC;IAE3CF,EADE,CAAAG,YAAA,EAAM,EACF;IACNH,EAAA,CAAAC,cAAA,YAA+B;IAAAD,EAAA,CAAAI,MAAA,sCAA+B;IAChEJ,EADgE,CAAAG,YAAA,EAAI,EAC9D;;;;;;IAKNH,EAAA,CAAAC,cAAA,iBAIyC;IAFjCD,EAAA,CAAAK,UAAA,mBAAAC,oEAAA;MAAAN,EAAA,CAAAO,aAAA,CAAAC,GAAA;MAAA,MAAAC,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAG,aAAA,EAAe;IAAA,EAAC;IAG/BZ,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,sBAAe;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACpCH,EAAA,CAAAI,MAAA,0BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAETH,EAAA,CAAAC,cAAA,iBAIyC;IAFjCD,EAAA,CAAAK,UAAA,mBAAAQ,oEAAA;MAAAb,EAAA,CAAAO,aAAA,CAAAO,GAAA;MAAA,MAAAL,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAM,YAAA,EAAc;IAAA,EAAC;IAG9Bf,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,WAAI;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACzBH,EAAA,CAAAI,MAAA,6BACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;;;;;;IAzCXH,EAHN,CAAAC,cAAA,cAA8C,mBACd,uBACV,cACc;IAO5BD,EANA,CAAAE,SAAA,mBAKQ,oBAC8D;IAGtEF,EAAA,CAAAgB,UAAA,IAAAC,uCAAA,kBAAgD;IAWlDjB,EAAA,CAAAG,YAAA,EAAM;IAGNH,EAAA,CAAAC,cAAA,cAA6B;IAU3BD,EATA,CAAAgB,UAAA,KAAAE,2CAAA,qBAIyC,KAAAC,2CAAA,qBASA;IAKzCnB,EAAA,CAAAC,cAAA,kBAI0C;IAFlCD,EAAA,CAAAK,UAAA,mBAAAe,2DAAA;MAAApB,EAAA,CAAAO,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAa,gBAAA,EAAkB;IAAA,EAAC;IAGlCtB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAI,MAAA,IAA6C;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAClEH,EAAA,CAAAI,MAAA,IACF;IAAAJ,EAAA,CAAAG,YAAA,EAAS;IAETH,EAAA,CAAAC,cAAA,kBAI2C;IAFnCD,EAAA,CAAAK,UAAA,mBAAAkB,2DAAA;MAAAvB,EAAA,CAAAO,aAAA,CAAAc,GAAA;MAAA,MAAAZ,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAe,YAAA,EAAc;IAAA,EAAC;IAG9BxB,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAI,MAAA,2BAAmB;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACxCH,EAAA,CAAAI,MAAA,6BACF;IAIRJ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;;IA5DSH,EAAA,CAAAyB,SAAA,GAA2B;IAA3BzB,EAAA,CAAA0B,WAAA,WAAAjB,MAAA,CAAAkB,UAAA,CAA2B;IAOJ3B,EAAA,CAAAyB,SAAA,GAAgB;IAAhBzB,EAAA,CAAA4B,UAAA,SAAAnB,MAAA,CAAAkB,UAAA,CAAgB;IAkBrC3B,EAAA,CAAAyB,SAAA,GAAiB;IAAjBzB,EAAA,CAAA4B,UAAA,UAAAnB,MAAA,CAAAkB,UAAA,CAAiB;IASjB3B,EAAA,CAAAyB,SAAA,EAAgB;IAAhBzB,EAAA,CAAA4B,UAAA,SAAAnB,MAAA,CAAAkB,UAAA,CAAgB;IASjB3B,EAAA,CAAAyB,SAAA,EAAwB;IAAxBzB,EAAA,CAAA4B,UAAA,cAAAnB,MAAA,CAAAkB,UAAA,CAAwB;IAEpB3B,EAAA,CAAAyB,SAAA,GAA6C;IAA7CzB,EAAA,CAAA6B,iBAAA,CAAApB,MAAA,CAAAqB,YAAA,4BAA6C;IACvD9B,EAAA,CAAAyB,SAAA,EACF;IADEzB,EAAA,CAAA+B,kBAAA,MAAAtB,MAAA,CAAAqB,YAAA,iCACF;IAKQ9B,EAAA,CAAAyB,SAAA,EAAwB;IAAxBzB,EAAA,CAAA4B,UAAA,cAAAnB,MAAA,CAAAkB,UAAA,CAAwB;;;;;;IAehC3B,EAJR,CAAAC,cAAA,cAAkD,mBACf,uBACb,cACe,mBACI;IAAAD,EAAA,CAAAI,MAAA,iBAAU;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IACtDH,EAAA,CAAAC,cAAA,SAAI;IAAAD,EAAA,CAAAI,MAAA,iCAAqB;IAAAJ,EAAA,CAAAG,YAAA,EAAK;IAC9BH,EAAA,CAAAC,cAAA,QAAG;IAAAD,EAAA,CAAAI,MAAA,gGAAgE;IAAAJ,EAAA,CAAAG,YAAA,EAAI;IACvEH,EAAA,CAAAC,cAAA,kBAAoE;IAA1BD,EAAA,CAAAK,UAAA,mBAAA2B,2DAAA;MAAAhC,EAAA,CAAAO,aAAA,CAAA0B,GAAA;MAAA,MAAAxB,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAAyB,aAAA,EAAe;IAAA,EAAC;IACjElC,EAAA,CAAAC,cAAA,gBAAU;IAAAD,EAAA,CAAAI,MAAA,gBAAQ;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAC7BH,EAAA,CAAAI,MAAA,yBACF;IAIRJ,EAJQ,CAAAG,YAAA,EAAS,EACL,EACW,EACV,EACP;;;;;;IAcEH,EAAA,CAAAC,cAAA,iBAKkD;IAH1CD,EAAA,CAAAK,UAAA,mBAAA8B,8DAAA;MAAA,MAAAC,OAAA,GAAApC,EAAA,CAAAO,aAAA,CAAA8B,GAAA,EAAAC,SAAA;MAAA,MAAA7B,MAAA,GAAAT,EAAA,CAAAU,aAAA;MAAA,OAAAV,EAAA,CAAAW,WAAA,CAASF,MAAA,CAAA8B,UAAA,CAAAH,OAAA,CAAAI,IAAA,CAAqB;IAAA,EAAC;IAIrCxC,EAAA,CAAAC,cAAA,eAAU;IAAAD,EAAA,CAAAI,MAAA,GAAmC;IAAAJ,EAAA,CAAAG,YAAA,EAAW;IAEtDH,EADF,CAAAC,cAAA,cAAiC,eACN;IAAAD,EAAA,CAAAI,MAAA,GAAgB;IAAAJ,EAAA,CAAAG,YAAA,EAAO;IAChDH,EAAA,CAAAC,cAAA,gBAA2B;IAAAD,EAAA,CAAAI,MAAA,GAA+B;IAE9DJ,EAF8D,CAAAG,YAAA,EAAQ,EAC9D,EACC;;;;;;IANDH,EAAA,CAAAyC,WAAA,oBAAAC,IAAA,aAAyC;IAFzC1C,EAAA,CAAA4B,UAAA,UAAAnB,MAAA,CAAAkC,kBAAA,CAAAD,IAAA,EAA+B;IAG3B1C,EAAA,CAAAyB,SAAA,GAAmC;IAAnCzB,EAAA,CAAA6B,iBAAA,CAAApB,MAAA,CAAAmC,iBAAA,CAAAR,OAAA,CAAAS,KAAA,EAAmC;IAElB7C,EAAA,CAAAyB,SAAA,GAAgB;IAAhBzB,EAAA,CAAA6B,iBAAA,CAAAO,OAAA,CAAAS,KAAA,CAAgB;IACd7C,EAAA,CAAAyB,SAAA,GAA+B;IAA/BzB,EAAA,CAAA6B,iBAAA,CAAApB,MAAA,CAAAqC,aAAA,CAAAV,OAAA,CAAAS,KAAA,EAA+B;;;ADpGxE,OAAM,MAAOE,kBAAkB;EAkB7BC,YACUC,SAAoB,EACpBC,WAAwB,EACxBC,WAAwB,EACxBC,MAAiB,EACjBC,QAAqB;IAJrB,KAAAJ,SAAS,GAATA,SAAS;IACT,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,WAAW,GAAXA,WAAW;IACX,KAAAC,MAAM,GAANA,MAAM;IACN,KAAAC,QAAQ,GAARA,QAAQ;IAnBlB,KAAA1B,UAAU,GAAG,KAAK;IAClB,KAAA2B,SAAS,GAAG,KAAK;IACjB,KAAAC,WAAW,GAAgB,IAAI;IAC/B,KAAAC,MAAM,GAAuB,IAAI;IAGjC;IACA,KAAAC,WAAW,GAAG,CACZ;MAAEZ,KAAK,EAAE,8BAA8B;MAAEL,IAAI,EAAE;IAAc,CAAE,EAC/D;MAAEK,KAAK,EAAE,4BAA4B;MAAEL,IAAI,EAAE;IAAuC,CAAE,EACtF;MAAEK,KAAK,EAAE,+BAA+B;MAAEL,IAAI,EAAE,kDAAkD,GAAGkB,IAAI,CAACC,GAAG,EAAE,GAAG;IAAG,CAAE,EACvH;MAAEd,KAAK,EAAE,uBAAuB;MAAEL,IAAI,EAAE,gDAAgD,GAAGkB,IAAI,CAACC,GAAG,EAAE,GAAG;IAAG,CAAE,CAC9G;EAQE;EAEHC,QAAQA,CAAA;IACN,IAAI,CAACT,WAAW,CAACU,YAAY,CAACC,SAAS,CAACC,IAAI,IAAG;MAC7C,IAAI,CAACR,WAAW,GAAGQ,IAAI;IACzB,CAAC,CAAC;IACF,IAAI,CAACC,kBAAkB,EAAE;EAC3B;EAEAC,WAAWA,CAAA;IACT,IAAI,CAAClD,YAAY,EAAE;EACrB;EAEMiD,kBAAkBA,CAAA;IAAA,IAAAE,KAAA;IAAA,OAAAC,iBAAA;MACtB,IAAI;QACF,MAAMC,OAAO,SAASC,SAAS,CAACC,YAAY,CAACC,gBAAgB,EAAE;QAC/DL,KAAI,CAACZ,SAAS,GAAGc,OAAO,CAACI,IAAI,CAACC,MAAM,IAAIA,MAAM,CAACC,IAAI,KAAK,YAAY,CAAC;OACtE,CAAC,OAAOC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,gCAAgC,EAAEA,KAAK,CAAC;QACtDT,KAAI,CAACZ,SAAS,GAAG,KAAK;;IACvB;EACH;EAEM1C,aAAaA,CAAA;IAAA,IAAAiE,MAAA;IAAA,OAAAV,iBAAA;MACjB,IAAI,CAACU,MAAI,CAACvB,SAAS,EAAE;QACnBuB,MAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,uBAAuB,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;QACzE;;MAGF,IAAI;QACFF,MAAI,CAACrB,MAAM,SAASa,SAAS,CAACC,YAAY,CAACU,YAAY,CAAC;UACtDC,KAAK,EAAE;YAAEC,UAAU,EAAE;UAAa;SACnC,CAAC;QAEFL,MAAI,CAACI,KAAK,CAACE,aAAa,CAACC,SAAS,GAAGP,MAAI,CAACrB,MAAM;QAChDqB,MAAI,CAACI,KAAK,CAACE,aAAa,CAACE,IAAI,EAAE;QAC/BR,MAAI,CAAClD,UAAU,GAAG,IAAI;QAEtB;QACAkD,MAAI,CAACS,YAAY,GAAGC,WAAW,CAAC,MAAK;UACnCV,MAAI,CAACW,aAAa,EAAE;QACtB,CAAC,EAAE,GAAG,CAAC;OAER,CAAC,OAAOb,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,wBAAwB,EAAEA,KAAK,CAAC;QAC9CE,MAAI,CAACxB,QAAQ,CAACyB,IAAI,CAAC,6BAA6B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;;IAChF;EACH;EAEAhE,YAAYA,CAAA;IACV,IAAI,IAAI,CAACyC,MAAM,EAAE;MACf,IAAI,CAACA,MAAM,CAACiC,SAAS,EAAE,CAACC,OAAO,CAACC,KAAK,IAAIA,KAAK,CAACC,IAAI,EAAE,CAAC;MACtD,IAAI,CAACpC,MAAM,GAAG,IAAI;;IAGpB,IAAI,IAAI,CAAC8B,YAAY,EAAE;MACrBO,aAAa,CAAC,IAAI,CAACP,YAAY,CAAC;MAChC,IAAI,CAACA,YAAY,GAAG,IAAI;;IAG1B,IAAI,CAAC3D,UAAU,GAAG,KAAK;EACzB;EAEQ6D,aAAaA,CAAA;IACnB,IAAI,CAAC,IAAI,CAACP,KAAK,CAACE,aAAa,IAAI,CAAC,IAAI,CAACW,MAAM,CAACX,aAAa,EAAE;IAE7D,MAAMF,KAAK,GAAG,IAAI,CAACA,KAAK,CAACE,aAAa;IACtC,MAAMW,MAAM,GAAG,IAAI,CAACA,MAAM,CAACX,aAAa;IACxC,MAAMY,OAAO,GAAGD,MAAM,CAACE,UAAU,CAAC,IAAI,CAAC;IAEvC,IAAI,CAACD,OAAO,IAAId,KAAK,CAACgB,UAAU,KAAKhB,KAAK,CAACiB,gBAAgB,EAAE;IAE7DJ,MAAM,CAACK,KAAK,GAAGlB,KAAK,CAACmB,UAAU;IAC/BN,MAAM,CAACO,MAAM,GAAGpB,KAAK,CAACqB,WAAW;IACjCP,OAAO,CAACQ,SAAS,CAACtB,KAAK,EAAE,CAAC,EAAE,CAAC,EAAEa,MAAM,CAACK,KAAK,EAAEL,MAAM,CAACO,MAAM,CAAC;IAE3D;IACA;IACA,IAAI,CAACG,mBAAmB,EAAE;EAC5B;EAEQA,mBAAmBA,CAAA;IACzB;IACA,IAAIC,IAAI,CAACC,MAAM,EAAE,GAAG,GAAG,EAAE;MAAE;MACzB,MAAMC,QAAQ,GAAG,IAAI,CAAClD,WAAW,CAACgD,IAAI,CAACG,KAAK,CAACH,IAAI,CAACC,MAAM,EAAE,GAAG,IAAI,CAACjD,WAAW,CAACoD,MAAM,CAAC,CAAC;MACtF,IAAI,CAACC,aAAa,CAACH,QAAQ,CAACnE,IAAI,CAAC;;EAErC;EAEMsE,aAAaA,CAACC,MAAc;IAAA,IAAAC,MAAA;IAAA,OAAA7C,iBAAA;MAChC6C,MAAI,CAACjG,YAAY,EAAE;MAEnB,IAAI;QACF,MAAMkG,UAAU,GAAGD,MAAI,CAAC/D,SAAS,CAACiE,WAAW,CAACH,MAAM,CAAC;QACrD,IAAI,CAACE,UAAU,EAAE;UACfD,MAAI,CAAC3D,QAAQ,CAACyB,IAAI,CAAC,kBAAkB,EAAE,QAAQ,EAAE;YAAEC,QAAQ,EAAE;UAAI,CAAE,CAAC;UACpE;;QAGF,MAAMoC,MAAM,SAASH,MAAI,CAAC/D,SAAS,CAACmE,YAAY,CAACH,UAAU,CAAC;QAC5DD,MAAI,CAACK,0BAA0B,CAACF,MAAM,EAAEF,UAAU,CAAC;OAEpD,CAAC,OAAOtC,KAAK,EAAE;QACdC,OAAO,CAACD,KAAK,CAAC,2BAA2B,EAAEA,KAAK,CAAC;QACjDqC,MAAI,CAAC3D,QAAQ,CAACyB,IAAI,CAAC,sCAAsC,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;;IACzF;EACH;EAEQsC,0BAA0BA,CAACC,UAAe,EAAEP,MAAkB;IACpE,MAAMQ,SAAS,GAAG,IAAI,CAACnE,MAAM,CAAC0B,IAAI,CAAC/E,yBAAyB,EAAE;MAC5DoG,KAAK,EAAE,OAAO;MACd3D,IAAI,EAAE;QACJ8E,UAAU;QACVP,MAAM;QACNxD,WAAW,EAAE,IAAI,CAACA;;KAErB,CAAC;IAEFgE,SAAS,CAACC,WAAW,EAAE,CAAC1D,SAAS,CAACqD,MAAM,IAAG;MACzC,IAAIA,MAAM,EAAE;QACV,IAAI,CAAC9D,QAAQ,CAACyB,IAAI,CAAC,+BAA+B,EAAE,QAAQ,EAAE;UAAEC,QAAQ,EAAE;QAAI,CAAE,CAAC;;IAErF,CAAC,CAAC;EACJ;EAEA;EACAxC,UAAUA,CAACwE,MAAc;IACvB,IAAI,CAACD,aAAa,CAACC,MAAM,CAAC;EAC5B;EAEApE,kBAAkBA,CAAC8E,KAAa;IAC9B,MAAMC,MAAM,GAAG,CAAC,SAAS,EAAE,QAAQ,EAAE,MAAM,EAAE,SAAS,CAAC;IACvD,OAAOA,MAAM,CAACD,KAAK,GAAGC,MAAM,CAACb,MAAM,CAAC;EACtC;EAEAjE,iBAAiBA,CAACC,KAAa;IAC7B,IAAIA,KAAK,CAAC8E,QAAQ,CAAC,aAAa,CAAC,EAAE,OAAO,QAAQ;IAClD,IAAI9E,KAAK,CAAC8E,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,UAAU;IACnD,IAAI9E,KAAK,CAAC8E,QAAQ,CAAC,YAAY,CAAC,EAAE,OAAO,OAAO;IAChD,IAAI9E,KAAK,CAAC8E,QAAQ,CAAC,SAAS,CAAC,EAAE,OAAO,cAAc;IACpD,OAAO,SAAS;EAClB;EAEA7E,aAAaA,CAACD,KAAa;IACzB,IAAIA,KAAK,CAAC8E,QAAQ,CAAC,MAAM,CAAC,EAAE,OAAO,UAAU;IAC7C,IAAI9E,KAAK,CAAC8E,QAAQ,CAAC,QAAQ,CAAC,EAAE,OAAO,YAAY;IACjD,IAAI9E,KAAK,CAAC8E,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,WAAW;IAC/C,IAAI9E,KAAK,CAAC8E,QAAQ,CAAC,OAAO,CAAC,EAAE,OAAO,WAAW;IAC/C,OAAO,kBAAkB;EAC3B;EAEMzF,aAAaA,CAAA;IAAA,IAAA0F,MAAA;IAAA,OAAAzD,iBAAA;MACjB;MACA,MAAM4C,MAAM,GAAGc,MAAM,CAAC,iCAAiC,CAAC;MACxD,IAAId,MAAM,EAAE;QACV,MAAMa,MAAI,CAACd,aAAa,CAACC,MAAM,CAAC;;IACjC;EACH;;;uBArLWhE,kBAAkB,EAAA/C,EAAA,CAAA8H,iBAAA,CAAAC,EAAA,CAAAC,SAAA,GAAAhI,EAAA,CAAA8H,iBAAA,CAAAG,EAAA,CAAAC,WAAA,GAAAlI,EAAA,CAAA8H,iBAAA,CAAAK,EAAA,CAAAC,WAAA,GAAApI,EAAA,CAAA8H,iBAAA,CAAAO,EAAA,CAAAC,SAAA,GAAAtI,EAAA,CAAA8H,iBAAA,CAAAS,EAAA,CAAAC,WAAA;IAAA;EAAA;;;YAAlBzF,kBAAkB;MAAA0F,SAAA;MAAAC,SAAA,WAAAC,yBAAAC,EAAA,EAAAC,GAAA;QAAA,IAAAD,EAAA;;;;;;;;;;;;;;;UCX3B5I,EAHJ,CAAAC,cAAA,aAA+B,aAED,SACtB;UAAAD,EAAA,CAAAI,MAAA,sBAAe;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACxBH,EAAA,CAAAC,cAAA,QAAG;UAAAD,EAAA,CAAAI,MAAA,uEAA2D;UAChEJ,EADgE,CAAAG,YAAA,EAAI,EAC9D;UAwENH,EArEA,CAAAgB,UAAA,IAAA8H,iCAAA,kBAA8C,IAAAC,iCAAA,kBAqEI;UAqB1C/I,EAJR,CAAAC,cAAA,aAAkC,kBACU,uBACvB,sBACC,gBACJ;UAAAD,EAAA,CAAAI,MAAA,eAAO;UAAAJ,EAAA,CAAAG,YAAA,EAAW;UAC5BH,EAAA,CAAAI,MAAA,qDACF;UACFJ,EADE,CAAAG,YAAA,EAAiB,EACD;UAEhBH,EADF,CAAAC,cAAA,wBAAkB,SACb;UAAAD,EAAA,CAAAI,MAAA,sEAAyD;UAAAJ,EAAA,CAAAG,YAAA,EAAI;UAChEH,EAAA,CAAAC,cAAA,cAA0B;UACxBD,EAAA,CAAAgB,UAAA,KAAAgI,qCAAA,oBAKkD;UAU1DhJ,EAHM,CAAAG,YAAA,EAAM,EACW,EACV,EACP;UAOEH,EAJR,CAAAC,cAAA,eAAkC,oBACI,uBACjB,sBACC,gBACJ;UAAAD,EAAA,CAAAI,MAAA,oBAAY;UAAAJ,EAAA,CAAAG,YAAA,EAAW;UACjCH,EAAA,CAAAI,MAAA,kCACF;UACFJ,EADE,CAAAG,YAAA,EAAiB,EACD;UAIZH,EAHN,CAAAC,cAAA,wBAAkB,eACe,eACC,oBACF;UAAAD,EAAA,CAAAI,MAAA,uBAAe;UAAAJ,EAAA,CAAAG,YAAA,EAAW;UAElDH,EADF,CAAAC,cAAA,WAAK,UACC;UAAAD,EAAA,CAAAI,MAAA,kBAAU;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACnBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,wDAA2C;UAElDJ,EAFkD,CAAAG,YAAA,EAAI,EAC9C,EACF;UAGJH,EADF,CAAAC,cAAA,eAA8B,oBACF;UAAAD,EAAA,CAAAI,MAAA,gBAAQ;UAAAJ,EAAA,CAAAG,YAAA,EAAW;UAE3CH,EADF,CAAAC,cAAA,WAAK,UACC;UAAAD,EAAA,CAAAI,MAAA,qBAAa;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UACtBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,oEAA6C;UAEpDJ,EAFoD,CAAAG,YAAA,EAAI,EAChD,EACF;UAGJH,EADF,CAAAC,cAAA,eAA8B,oBACF;UAAAD,EAAA,CAAAI,MAAA,aAAK;UAAAJ,EAAA,CAAAG,YAAA,EAAW;UAExCH,EADF,CAAAC,cAAA,WAAK,UACC;UAAAD,EAAA,CAAAI,MAAA,iBAAS;UAAAJ,EAAA,CAAAG,YAAA,EAAK;UAClBH,EAAA,CAAAC,cAAA,SAAG;UAAAD,EAAA,CAAAI,MAAA,uDAA0C;UAO3DJ,EAP2D,CAAAG,YAAA,EAAI,EAC7C,EACF,EACF,EACW,EACV,EACP,EACF;;;UAxJyBH,EAAA,CAAAyB,SAAA,GAAe;UAAfzB,EAAA,CAAA4B,UAAA,SAAAiH,GAAA,CAAAvF,SAAA,CAAe;UAqEZtD,EAAA,CAAAyB,SAAA,EAAgB;UAAhBzB,EAAA,CAAA4B,UAAA,UAAAiH,GAAA,CAAAvF,SAAA,CAAgB;UA6BftD,EAAA,CAAAyB,SAAA,IAAgB;UAAhBzB,EAAA,CAAA4B,UAAA,YAAAiH,GAAA,CAAApF,WAAA,CAAgB", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}